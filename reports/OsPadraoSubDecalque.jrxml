<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsPadraoSubTermo" pageWidth="595" pageHeight="842" whenNoDataType="NoPages" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="0efcef53-ba2f-4102-8b70-a807a9128687">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="344"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="646"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[select 
     OS_DECALQUE.DECALQUE 
from OS_DECALQUE
where OS_DECALQUE.COD_EMPRESA = $P{COD_EMPRESA}
			 AND OS_DECALQUE.NUMERO_OS = $P{NUMERO_OS}]]>
	</queryString>
	<field name="DECALQUE" class="java.io.InputStream"/>
	<title>
		<band height="19" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="1" width="554" height="18" backcolor="#D9D9D9" uuid="8e72b655-22fe-4155-91d5-649e08d38a2b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="6" y="3" width="100" height="14" uuid="9d2bb599-f3d7-426e-9070-05b1a6d31f06">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Decalque]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="100" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="554" height="100" uuid="2b509a05-c795-4e29-bd8f-641319d9de64"/>
				<image scaleImage="RealHeight" hAlign="Center" vAlign="Middle">
					<reportElement x="190" y="10" width="174" height="80" uuid="5be14aea-2c9f-4686-91c7-8376b5ba55bc">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{DECALQUE}]]></imageExpression>
				</image>
			</frame>
		</band>
	</detail>
</jasperReport>
