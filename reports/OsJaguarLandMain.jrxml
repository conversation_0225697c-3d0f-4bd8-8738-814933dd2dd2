<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsJaguarLandSubServicos" pageWidth="595" pageHeight="1200" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isTitleNewPage="true" isSummaryNewPage="true" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="FIELD_NULL" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232335.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\29881\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH Q_OS AS
 (SELECT OS.COD_EMPRESA,
         OS_AGENDA.DATA_AGENDADA,
         TO_CHAR(OS_AGENDA.DATA_AGENDADA, 'DD/MM/YYYY HH24:MI') AS D_AG_STG,
         OS.STATUS_OS,
         OS.NUMERO_OS,
         OS.COD_OS_AGENDA,
         ABS(OS.NUMERO_OS) AS ABS_OSNUM,
         OS.COD_CLIENTE,
         OS.CLIENTE_RAPIDO,
         OS.TIPO_ENDERECO,
         OS.OBSERVACAO,
         OS.EXTENDIDA,
         OS.SEGURADORA,
         OS.DATA_EMISSAO,
         (TO_DATE(OS.DATA_EMISSAO, 'DD/MM/YYYY') || ' AS ' ||
         OS.HORA_EMISSAO) AS DH_EMISSAO,
         DECODE(OS.DATA_LIBERADO,
                NULL,
                '',
                (TO_DATE(OS.DATA_LIBERADO, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_LIBERADO)) AS DH_LIBERADO,
         DECODE(OS.DATA_ENCERRADA,
                NULL,
                '',
                (TO_DATE(OS.DATA_ENCERRADA, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_ENCERRADA)) AS DH_ENCERRADO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2) AS DIA_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2) AS MES_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4) AS ANO_EMISSAO,
         OS.HORA_EMISSAO,
         OS.HORA_ENCERRADA,
         OS.DATA_ENCERRADA,
         OS.HORA_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 1, 2) AS HORA24_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 4, 2) AS MINUTO_PROMETIDA,
         
         OS.DATA_PROMETIDA,
         OS.DATA_PROMETIDA_REVISADA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2) AS DIA_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2) AS MES_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4) AS ANO_PROMETIDA,
         
         OS.VALOR_SERVICOS_BRUTO,
         OS.VALOR_ITENS_BRUTO,
         OS.DESCONTOS_SERVICOS,
         OS.DESCONTOS_ITENS,
         (OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS TOTAL_OS_SERVICOS,
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS) AS TOTAL_OS_ITENS,
         (OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO) AS TOTAL_OS_BRUTO,
         (OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS) AS TOTAL_OS_DESCONTO,
         ((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) +
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS)) AS TOTAL_OS,
         OS.COD_SEGURADORA,
         
         OS_DADOS_VEICULOS.ANO,
         OS_DADOS_VEICULOS.HORIMETRO,
         OS_DADOS_VEICULOS.PRISMA,
         OS_DADOS_VEICULOS.DATA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2) AS DIA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2) AS MES_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4) AS ANO_VENDA,
         OS_DADOS_VEICULOS.COMBUSTIVEL,
         OS_DADOS_VEICULOS.COR_EXTERNA,
         OS_DADOS_VEICULOS.PLACA,
         OS_DADOS_VEICULOS.KM,
         OS_DADOS_VEICULOS.CHASSI,
         OS_DADOS_VEICULOS.NUMERO_MOTOR,
         OS_DADOS_VEICULOS.NUMERO_RENAVAM,
         OS_DADOS_VEICULOS.SERIE,
         OS_DADOS_VEICULOS.COD_CONCESSIONARIA,
         OS_DADOS_VEICULOS.ESTADO_PINTURA,
         OS_DADOS_VEICULOS.JOGO_FERRAMENTAS,
         OS_DADOS_VEICULOS.ELASTICOS,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_D,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_E,
         OS_DADOS_VEICULOS.FLANELA,
         OS.TIPO,
         OS_TIPOS.DESCRICAO AS TIPO_DESCRICAO,
         OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO AS TIPO_COM_DESCRICAO,
         OS_TIPOS.GARANTIA,
         OS_TIPOS.REVISAO_GRATUITA,
         OS_TIPOS.INTERNO,
         OS_TIPOS.COD_CLIENTE AS CLIENTE_DO_TIPO,
         OS_TIPOS.OUTRO_CONCESSIONARIA,
         OS.NOME AS CONSULTOR,
         EMPRESAS_USUARIOS.NOME_COMPLETO AS CONSULTOR_COMPLETO,
         PRODUTOS.DESCRICAO_PRODUTO,
         PRODUTOS_MODELOS.DESCRICAO_MODELO,
         (PRODUTOS.DESCRICAO_PRODUTO || ' - ' ||
         PRODUTOS_MODELOS.DESCRICAO_MODELO) DESC_PROD_MOD,
         PRODUTOS_MODELOS.LINHA,
         MARCAS.DESCRICAO_MARCA,
         CONCESSIONARIAS.NOME CONCESSIONARIA_NOME,
         CONCESSIONARIAS.UF CONCESSIONARIA_UF,
         CONCESSIONARIAS.CIDADE CONCESSIONARIA_CIDADE,
         CONCESSIONARIAS.BAIRRO CONCESSIONARIA_BAIRRO,
         CONCESSIONARIAS.ENDERECO CONCESSIONARIA_RUA,
         CONCESSIONARIAS.CEP CONCESSIONARIA_CEP,
         CONCESSIONARIAS.CODIGO_PADRAO CONCESSIONARIA_CODIGO,
         UF_CONCESSIONARIA.DESCRICAO CONCESSIONARIA_ESTADO,
         
         (CASE
           WHEN NVL(OS.OS_ORIGEM_RETORNO, 0) > 0 THEN
            'S'
           ELSE
            'N'
         END) AS AG_RETORNO,
         NVL(OS_AGENDA.CLIENTE_AGUARDA, 'N') AS AG_CLIENTE_AGUARDA,
         NVL(OS_AGENDA.VEICULO_PLATAFORMA, 'N') AS AG_VEICULO_PLATAFORMA,
         NVL(OS_AGENDA.TAXI, 'N') AS AG_TAXI,
         NVL(OS_AGENDA.BLINDADO, 'N') AS AG_BLINDADO,
         NVL(OS_AGENDA.TESTE_RODAGEM, 'N') AS AG_TESTE_RODAGEM,
         NVL(OS_AGENDA.LEVAR_PECAS_SUBSTITUIDAS, 'N') AS AG_LEVAR_PECAS_SUBSTITUIDAS,
         NVL(OS_AGENDA.LAVAR_VEICULO, 'N') AS AG_LAVAR_VEICULO,
         NVL(OS_AGENDA.VEICULO_MODIFICADO, 'N') AS VEICULO_MODIFICADO,
         NVL(OS_AGENDA.DDW_GARANTIA, 'NÃO EXISTENTE') AS DDW_GARANTIA,
         OS.HORA_PROMETIDA_REVISADA,
         OS_TIPOS.TIPO_FABRICA,
         DECODE(OS_AGENDA.REC_INTERATIVA,
                'S',
                'VEÍCULO COM RECEPÇÃO INTERATIVA',
                'N',
                '') REC_INTERATIVA,
         OS_AGENDA.COD_TIPO_IMOBILIZADO,
         DECODE(OS_AGENDA.COD_TIPO_IMOBILIZADO,
                NULL,
                '',
                'VEICULO IMOBILIZADO: ' || TIPO_IMOBILIZADO.DESCRICAO) AS IMOBILIZADO,
         OS_AGENDA.REC_INTERATIVA AS RECEPCAO_INTERATIVA,
         DECODE(OS_AGENDA.COD_TIPO_MOBILIDADE,
                NULL,
                'SEM MOBILIDADE',
                TIPO_MOBILIDADE.DESCRICAO) MOBILIDADE_DESCRICAO,
         NVL((SELECT 'S'
               FROM JLR_CHASSI_BLINDADO J
              WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
                AND ROWNUM < 2),
             'N') AS BLINDADO,
         (SELECT J.NOME_BLINDADORA
            FROM JLR_CHASSI_BLINDADO J
           WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
             AND ROWNUM < 2) AS BLINDADORA
  
    FROM OS,
         OS_DADOS_VEICULOS,
         OS_AGENDA,
         EMPRESAS_USUARIOS,
         VW_OS_TIPOS       OS_TIPOS,
         CONCESSIONARIAS,
         PRODUTOS,
         PRODUTOS_MODELOS,
         MARCAS,
         UF                UF_CONCESSIONARIA,
         TIPO_IMOBILIZADO,
         TIPO_MOBILIDADE
   WHERE OS.TIPO = OS_TIPOS.TIPO
     AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA(+)
     AND OS.NOME = EMPRESAS_USUARIOS.NOME
     AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS(+)
     AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA(+)
     AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+)
     AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO
        
     AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA(+)
     AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS(+)
        
     AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
     AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
     AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
     AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF(+)
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_TIPO_IMOBILIZADO =
         TIPO_IMOBILIZADO.COD_TIPO_IMOBILIZADO(+)
     AND OS_AGENDA.COD_TIPO_MOBILIDADE = TIPO_MOBILIDADE.COD_TIPO_MOBIL(+)),
Q_EMPRESA AS
 (SELECT EMPRESAS.COD_EMPRESA,
         EMPRESAS.NOME NOME_EMPRESA,
         EMPRESAS.CGC,
         EMPRESAS.FACHADA,
         EMPRESAS.ESTADO AS UF,
         (TRIM(EMPRESAS.CIDADE) || ' - ' || TRIM(EMPRESAS.ESTADO)) AS CIDADE,
         EMPRESAS.BAIRRO,
         EMPRESAS.COMPLEMENTO,
         (EMPRESAS.RUA || ', ' || EMPRESAS.FACHADA) AS RUA,
         EMPRESAS.FONE,
         EMPRESAS.FAX,
         EMPRESAS.CEP,
         EMPRESAS.INSCRICAO_MUNICIPAL,
         EMPRESAS.INSCRICAO_SUBSTITUICAO,
         UF.DESCRICAO ESTADO,
         EMPRESAS.INSCRICAO_ESTADUAL,
         TRUNC(SYSDATE) AS DATA_ATUAL,
         SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'), 1, 5) AS HORA_ATUAL_STR,
         CLIENTES.ENDERECO_ELETRONICO AS EMAIL,
         EMPRESA_LOGO.LOGO
    FROM EMPRESAS, EMPRESA_LOGO, UF, CLIENTES
   WHERE EMPRESAS.COD_EMPRESA = $P{COD_EMPRESA}
     AND EMPRESAS.COD_EMPRESA = EMPRESA_LOGO.COD_EMPRESA(+)
     AND EMPRESAS.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND UF.UF = EMPRESAS.ESTADO),
Q_CLIENTE AS
 (SELECT CLIENTE_DIVERSO.COD_CLIENTE,
         
         CLIENTE_DIVERSO.NOME AS NOME,
         CLIENTE_DIVERSO.RG   AS RG,
         
         ('(' || CLIENTES.PREFIXO_RES || ') ' || CLIENTES.TELEFONE_RES) AS TELEFONE_RES,
         CLIENTES.PREFIXO_RES,
         ('(' || CLIENTES.PREFIXO_COM || ') ' || CLIENTES.TELEFONE_COM) AS TELEFONE_COM,
         CLIENTES.PREFIXO_COM,
         ('(' || CLIENTES.PREFIXO_FAX || ') ' || CLIENTES.TELEFONE_FAX) AS TELEFONE_FAX,
         CLIENTES.PREFIXO_FAX,
         ('(' || CLIENTES.PREFIXO_CEL || ') ' || CLIENTES.TELEFONE_CEL) AS TELEFONE_CEL,
         CLIENTES.PREFIXO_CEL,
         
         NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL) AS INSC_ESTAD,
         
         CLIENTE_DIVERSO.CGC,
         CLIENTE_DIVERSO.CPF,
         CLIENTES.COD_CLASSE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.UF,
                2,
                CLIENTES.UF_RES,
                3,
                CLIENTES.UF_COM,
                4,
                CLIENTES.UF_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.UF,
                NULL) UF,
         DECODE(OS.TIPO_ENDERECO,
                1,
                UF_DIVERSO.DESCRICAO,
                2,
                UF_RES.DESCRICAO,
                3,
                UF_COM.DESCRICAO,
                4,
                UF_COBRANCA.DESCRICAO,
                5,
                UF_INSCRICAO.DESCRICAO,
                NULL) ESTADO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CIDADES_DIV.DESCRICAO,
                2,
                CIDADES_RES.DESCRICAO,
                3,
                CIDADES_COM.DESCRICAO,
                4,
                CIDADES_COBRANCA.DESCRICAO,
                5,
                ENDERECO_POR_INSCRICAO.CIDADE,
                NULL) CIDADE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.BAIRRO,
                2,
                CLIENTES.BAIRRO_RES,
                3,
                CLIENTES.BAIRRO_COM,
                4,
                CLIENTES.BAIRRO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.BAIRRO,
                NULL) BAIRRO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                TRANSLATE(TO_CHAR(CLIENTE_DIVERSO.CEP / 1000, '00000.000'),
                          ',.',
                          '.-'),
                2,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_RES / 1000, '00000.000'),
                          ',.',
                          '.-'),
                3,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_COM / 1000, '00000.000'),
                          ',.',
                          '.-'),
                4,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_COBRANCA / 1000, '00000.000'),
                          ',.',
                          '.-'),
                5,
                TRANSLATE(TO_CHAR(ENDERECO_POR_INSCRICAO.CEP / 1000,
                                  '00000.000'),
                          ',.',
                          '.-'),
                NULL) CEP,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.ENDERECO,
                2,
                CLIENTES.RUA_RES,
                3,
                CLIENTES.RUA_COM,
                4,
                CLIENTES.RUA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.RUA,
                NULL) RUA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.COMPLEMENTO,
                2,
                CLIENTES.COMPLEMENTO_RES,
                3,
                CLIENTES.COMPLEMENTO_COM,
                4,
                CLIENTES.COMPLEMENTO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.COMPLEMENTO,
                NULL) COMPLEMENTO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                NULL,
                2,
                CLIENTES.FACHADA_RES,
                3,
                CLIENTES.FACHADA_COM,
                4,
                CLIENTES.FACHADA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.FACHADA,
                NULL) FACHADA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.FONE_CONTATO,
                2,
                CLIENTES.TELEFONE_RES,
                3,
                CLIENTES.TELEFONE_COM,
                4,
                CLIENTES.TELEFONE_CEL,
                5,
                ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
                NULL) FONE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
                2,
                CLIENTES.PREFIXO_RES,
                3,
                CLIENTES.PREFIXO_COM,
                4,
                CLIENTES.PREFIXO_CEL,
                5,
                ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
                NULL) PREFIXO,
         
         CLIENTES.ENDERECO_ELETRONICO,
         CLIENTES.EMAIL_NFE,
         NVL(CLIENTES.EMAIL_TRABALHO, CLIENTES.EMAIL2) AS EMAIL2
  
    FROM OS,
         CLIENTE_DIVERSO,
         CLIENTES,
         ENDERECO_POR_INSCRICAO,
         CIDADES                CIDADES_RES,
         CIDADES                CIDADES_COM,
         CIDADES                CIDADES_COBRANCA,
         CIDADES                CIDADES_DIV,
         UF                     UF_DIVERSO,
         UF                     UF_RES,
         UF                     UF_COM,
         UF                     UF_COBRANCA,
         UF                     UF_INSCRICAO
   WHERE OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE
     AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES(+)
     AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES(+)
     AND OS.INSCRICAO_ESTADUAL =
         ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL(+)
     AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF(+)
     AND CLIENTES.UF_RES = UF_RES.UF(+)
     AND CLIENTES.UF_COM = UF_COM.UF(+)
     AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF(+)
     AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF(+)
        
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}),
Q_HIST_ORC AS
 (SELECT ROW_NUMBER() OVER(ORDER BY ORC.NUMERO_OS) AS NUMERO_LINHA,
         ORC.NUMERO_OS,
         ORC.COD_EMPRESA,
         COUNT(ORC.NUMERO_OS) NUMERO_ORCAMENTOS,
         LISTAGG(ORC.NUMERO_ORCAMENTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS ORCAMENTOS,
         LISTAGG(ORCF.VALOR_BRUTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS VALOR_ORCAMENTO_BRUTO,
         SUM(ORCF.VALOR_BRUTO) TOTAL
    FROM OS_ORCAMENTOS ORC
    LEFT JOIN OS_ORC_FECHAMENTO ORCF
      ON ORCF.NUMERO_OS = ORC.NUMERO_ORCAMENTO
     AND ORCF.COD_EMPRESA = ORC.COD_EMPRESA
   WHERE ORC.NUMERO_OS = $P{NUMERO_OS}
     AND ORC.COD_EMPRESA = $P{COD_EMPRESA}
   GROUP BY ORC.NUMERO_OS, ORC.COD_EMPRESA),

Q_HISTORICO AS
 (SELECT LISTAGG(HISTORICO_OS.N_OS, ', ') WITHIN GROUP(ORDER BY HISTORICO_OS.N_OS) AS HISTORICO_OS,
         HISTORICO_OS.CHASSI,
         HISTORICO_OS.COD_EMPRESA
    FROM (SELECT DISTINCT TO_CHAR(OSD.NUMERO_OS) AS N_OS,
                          OSD.CHASSI,
                          OSD.COD_EMPRESA
            FROM OS_DADOS_VEICULOS OSD, OS, Q_OS
           WHERE OSD.COD_EMPRESA = Q_OS.COD_EMPRESA
             AND OS.NUMERO_OS = OSD.NUMERO_OS
             AND OS.STATUS_OS = 1
             AND OS.NUMERO_OS > 0
             AND OSD.CHASSI = Q_OS.CHASSI
                
             AND ROWNUM < 11
           ORDER BY OSD.NUMERO_OS DESC) HISTORICO_OS
   GROUP BY HISTORICO_OS.CHASSI, HISTORICO_OS.COD_EMPRESA),

QRYDADOSVEICULOS AS
 (SELECT CF.ANO, CF.DATA_COMPRA, CF.CHASSI, CF.PLACA
    FROM CLIENTES_FROTA CF, Q_OS
   WHERE CF.VENDIDO = 'N'
     AND ROWNUM <= 1
     AND ((Q_OS.CHASSI IS NOT NULL AND CF.CHASSI = Q_OS.CHASSI) OR
         (Q_OS.PLACA IS NOT NULL AND CF.PLACA = Q_OS.PLACA))
   ORDER BY CF.DATA_COMPRA DESC),

Q_PARM_SYS as
 (SELECT PARM_SYS.COD_EMPRESA,
         TIPO_TEMPO,
         TIPO_VALOR_OS,
         cod_cliente_balcao,
         tipo_concessionaria,
         CONCESSIONARIAS.codigo_padrao,
         versao2,
         cod_cliente_fabrica_garantia,
         PARM_SYS2.HONDA_IMP_NUMERO_REQUISICAO,
         PARM_SYS3.TERMO_OS_JLR
    FROM PARM_SYS, PARM_SYS2, PARM_SYS3, CONCESSIONARIAS
   WHERE (PARM_SYS.CONCESSIONARIA_NUMERO =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+))
     AND (PARM_SYS2.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS3.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS.COD_EMPRESA = $P{COD_EMPRESA})),

Q_ASSINATURA as
 (select OS_AGENDA.NUMERO_OS,
         OS_AGENDA.COD_EMPRESA,
         OS_AGENDA.SIGNATURE As assintaura
    from OS_AGENDA
   where OS_AGENDA.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_EMPRESA = $P{COD_EMPRESA})

SELECT Q_EMPRESA.NOME_EMPRESA           AS Q_EMPRESA_NOME_EMPRESA,
       Q_EMPRESA.RUA                    AS Q_EMPRESA_RUA,
       Q_EMPRESA.CIDADE                 AS Q_EMPRESA_CIDADE,
       Q_EMPRESA.CEP                    AS Q_EMPRESA_CEP,
       Q_EMPRESA.FONE                   AS Q_EMPRESA_FONE,
       Q_EMPRESA.INSCRICAO_ESTADUAL     AS Q_EMPRESA_INSCRICAO_ESTADUAL,
       Q_EMPRESA.CGC                    AS Q_EMPRESA_CGC,
       Q_OS.TIPO                        AS Q_OS_TIPO,
       Q_OS.D_AG_STG                    AS Q_OS_D_AG_STG,
       Q_OS.CONSULTOR                   AS Q_OS_CONSULTOR,
       Q_OS.PRISMA                      AS Q_OS_PRISMA,
       Q_OS.DATA_PROMETIDA              AS Q_OS_DATA_PROMETIDA,
       Q_OS.DATA_PROMETIDA_REVISADA     AS Q_OS_DATA_PROMETIDA_REVISADA,
       Q_OS.AG_RETORNO                  AS Q_OS_AG_RETORNO,
       Q_OS.AG_CLIENTE_AGUARDA          AS Q_OS_AG_CLIENTE_AGUARDA,
       Q_OS.RECEPCAO_INTERATIVA         AS Q_OS_RECEPCAO_INTERATIVA,
       Q_OS.AG_LAVAR_VEICULO            AS Q_OS_AG_LAVAR_VEICULO,
       Q_OS.AG_BLINDADO                 AS Q_OS_AG_BLINDADO,
       Q_OS.AG_TESTE_RODAGEM            AS Q_OS_AG_TESTE_RODAGEM,
       Q_OS.AG_LEVAR_PECAS_SUBSTITUIDAS AS Q_OS_AG_LEVAR_PECAS_SUBSTITUID,
       Q_OS.VEICULO_MODIFICADO          AS Q_OS_VEICULO_MODIFICADO,
       Q_OS.NUMERO_OS                   AS Q_OS_NUMERO_OS,
       Q_OS.HORA_PROMETIDA              AS Q_OS_HORA_PROMETIDA,
       Q_OS.PLACA                       AS Q_OS_PLACA,
       Q_OS.HORA_PROMETIDA_REVISADA     AS Q_OS_HORA_PROMETIDA_REVISADA,
       Q_OS.MOBILIDADE_DESCRICAO        AS Q_OS_MOBILIDADE_DESCRICAO,
       Q_CLIENTE.TELEFONE_CEL           AS Q_CLIENTE_TELEFONE_CEL,
       Q_CLIENTE.TELEFONE_COM           AS Q_CLIENTE_TELEFONE_COM,
       Q_CLIENTE.TELEFONE_RES           AS Q_CLIENTE_TELEFONE_RES,
       Q_CLIENTE.EMAIL_NFE              AS Q_CLIENTE_EMAIL_NFE,
       Q_CLIENTE.EMAIL2                 AS Q_CLIENTE_EMAIL2,
       Q_CLIENTE.ENDERECO_ELETRONICO    AS Q_CLIENTE_ENDERECO_ELETRONICO,
       Q_CLIENTE.RUA                    AS Q_CLIENTE_RUA,
       Q_CLIENTE.BAIRRO                 AS Q_CLIENTE_BAIRRO,
       Q_CLIENTE.NOME                   AS Q_CLIENTE_NOME,
       Q_CLIENTE.CIDADE                 AS Q_CLIENTE_CIDADE,
       Q_CLIENTE.CEP                    AS Q_CLIENTE_CEP,
       Q_CLIENTE.CPF                    AS Q_CLIENTE_CPF,
       Q_CLIENTE.RG                     AS Q_CLIENTE_RG,
       Q_OS.DESC_PROD_MOD               AS Q_OS_DESC_PROD_MOD,
       Q_OS.KM                          AS Q_OS_KM,
       Q_OS.CHASSI                      AS Q_OS_CHASSI,
       Q_OS.COR_EXTERNA                 AS Q_OS_COR_EXTERNA,
       Q_OS.ANO                         AS Q_OS_ANO,
       Q_OS.NUMERO_MOTOR                AS Q_OS_NUMERO_MOTOR,
       Q_OS.DATA_VENDA                  AS Q_OS_DATA_VENDA,
       Q_OS.CONCESSIONARIA_NOME         AS Q_OS_CONCESSIONARIA_NOME,
       Q_OS.TOTAL_OS                    AS Q_OS_TOTAL_OS,
       Q_OS.BLINDADORA                  AS Q_OS_BLINDADORA,
       Q_OS.BLINDADO                    AS Q_OS_BLINDADO,
       Q_OS.DDW_GARANTIA                AS Q_OS_DDW_GARANTIA,
       Q_OS.REC_INTERATIVA              AS Q_OS_REC_INTERATIVA,
       Q_OS.IMOBILIZADO                 AS Q_OS_IMOBILIZADO,
       Q_OS.DH_EMISSAO                  AS Q_OS_DH_EMISSAO,
       Q_OS.DH_ENCERRADO                AS Q_OS_DH_ENCERRADO,
       Q_OS.DH_LIBERADO                 AS Q_OS_DH_LIBERADO,
       Q_OS.TIPO_FABRICA                AS Q_OS_TIPO_FABRICA,
       Q_OS.OBSERVACAO                  AS Q_OS_OBSERVACAO,
       Q_HIST_ORC.ORCAMENTOS            AS Q_HIST_ORC_LISTA_ORCAMENTOS,
       Q_HIST_ORC.TOTAL                 AS Q_HIST_ORC_TOTAL_ORCAMENTOS,
       Q_HISTORICO.HISTORICO_OS         AS Q_HIST_ORC_HISTORICO_OS,
       Q_PARM_SYS.TERMO_OS_JLR          AS Q_PARM_SYS_TERMO_OS_JLR,
       Q_ASSINATURA.assintaura          AS Q_ASSINATURA_ASSINATURA
  FROM Q_OS,
       Q_EMPRESA,
       Q_CLIENTE,
       Q_HIST_ORC,
       Q_HISTORICO,
       QRYDADOSVEICULOS,
       Q_ASSINATURA,
       Q_PARM_SYS
 WHERE Q_OS.COD_CLIENTE = Q_CLIENTE.COD_CLIENTE(+)
   AND Q_OS.COD_EMPRESA = Q_EMPRESA.COD_EMPRESA(+)
   AND Q_OS.COD_EMPRESA = Q_HIST_ORC.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_HIST_ORC.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_HISTORICO.COD_EMPRESA(+)
   AND Q_OS.CHASSI = Q_HISTORICO.CHASSI(+)
   AND Q_OS.CHASSI = QRYDADOSVEICULOS.CHASSI(+)
   AND Q_OS.PLACA = QRYDADOSVEICULOS.PLACA(+)
   AND Q_OS.COD_EMPRESA = Q_PARM_SYS.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_ASSINATURA.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_ASSINATURA.COD_EMPRESA(+)]]>
	</queryString>
	<field name="Q_EMPRESA_NOME_EMPRESA" class="java.lang.String"/>
	<field name="Q_EMPRESA_RUA" class="java.lang.String"/>
	<field name="Q_EMPRESA_CIDADE" class="java.lang.String"/>
	<field name="Q_EMPRESA_CEP" class="java.lang.String"/>
	<field name="Q_EMPRESA_FONE" class="java.lang.String"/>
	<field name="Q_EMPRESA_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="Q_EMPRESA_CGC" class="java.lang.String"/>
	<field name="Q_OS_TIPO" class="java.lang.String"/>
	<field name="Q_OS_D_AG_STG" class="java.lang.String"/>
	<field name="Q_OS_CONSULTOR" class="java.lang.String"/>
	<field name="Q_OS_PRISMA" class="java.lang.String"/>
	<field name="Q_OS_DATA_PROMETIDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_DATA_PROMETIDA_REVISADA" class="java.sql.Timestamp"/>
	<field name="Q_OS_AG_RETORNO" class="java.lang.String"/>
	<field name="Q_OS_AG_CLIENTE_AGUARDA" class="java.lang.String"/>
	<field name="Q_OS_RECEPCAO_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_AG_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="Q_OS_AG_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_AG_TESTE_RODAGEM" class="java.lang.String"/>
	<field name="Q_OS_AG_LEVAR_PECAS_SUBSTITUID" class="java.lang.String"/>
	<field name="Q_OS_VEICULO_MODIFICADO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="Q_OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="Q_OS_PLACA" class="java.lang.String"/>
	<field name="Q_OS_HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="Q_OS_MOBILIDADE_DESCRICAO" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_CEL" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_COM" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_RES" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL_NFE" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL2" class="java.lang.String"/>
	<field name="Q_CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="Q_CLIENTE_RUA" class="java.lang.String"/>
	<field name="Q_CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="Q_CLIENTE_NOME" class="java.lang.String"/>
	<field name="Q_CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="Q_CLIENTE_CEP" class="java.lang.String"/>
	<field name="Q_CLIENTE_CPF" class="java.lang.String"/>
	<field name="Q_CLIENTE_RG" class="java.lang.String"/>
	<field name="Q_OS_DESC_PROD_MOD" class="java.lang.String"/>
	<field name="Q_OS_KM" class="java.lang.Double"/>
	<field name="Q_OS_CHASSI" class="java.lang.String"/>
	<field name="Q_OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="Q_OS_ANO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="Q_OS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="Q_OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="Q_OS_BLINDADORA" class="java.lang.String"/>
	<field name="Q_OS_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_DDW_GARANTIA" class="java.lang.String"/>
	<field name="Q_OS_REC_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_IMOBILIZADO" class="java.lang.String"/>
	<field name="Q_OS_DH_EMISSAO" class="java.lang.String"/>
	<field name="Q_OS_DH_ENCERRADO" class="java.lang.String"/>
	<field name="Q_OS_DH_LIBERADO" class="java.lang.String"/>
	<field name="Q_OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="Q_OS_OBSERVACAO" class="java.lang.String"/>
	<field name="Q_HIST_ORC_LISTA_ORCAMENTOS" class="java.lang.String"/>
	<field name="Q_HIST_ORC_TOTAL_ORCAMENTOS" class="java.lang.Double"/>
	<field name="Q_HIST_ORC_HISTORICO_OS" class="java.lang.String"/>
	<field name="Q_PARM_SYS_TERMO_OS_JLR" class="java.lang.String"/>
	<field name="Q_ASSINATURA_ASSINATURA" class="java.awt.Image"/>
	<detail>
		<band height="926">
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="595" height="842" isRemoveLineWhenBlank="true" uuid="a3c54d1a-3c5b-42db-bac1-139193a403ba">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsJaguarLand.jasper"]]></subreportExpression>
			</subreport>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement positionType="Float" stretchType="ContainerBottom" x="0" y="850" width="595" height="76" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="*************-45d0-8514-8c3d40336cf5">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsJaguarLandSubServicos.jasper"]]></subreportExpression>
			</subreport>
			<break>
				<reportElement x="0" y="846" width="100" height="1" uuid="6af0e7ab-1fc6-4728-bba0-f642acf33147"/>
			</break>
		</band>
	</detail>
</jasperReport>
