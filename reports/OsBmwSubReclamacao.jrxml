<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsFCASubServicos" pageWidth="555" pageHeight="842" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="campoNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT SUBSTR(TO_CHAR(O.ITEM + 100), 2, 2) AS NITEM
      , INITCAP(O.DESCRICAO) as DESCRICAO, R.*
FROM (SELECT T.ITEM,
             SUM(T.PRECO_VENDA) AS PRECO_VENDA,
             SUM(T.TEMPO_PADRAO) AS TEMPO_PADRAO
        FROM OS_SERVICOS T
       WHERE T.NUMERO_OS = $P{NUMERO_OS}
         AND T.COD_EMPRESA = $P{COD_EMPRESA}
       GROUP BY T.ITEM) R,
     OS_ORIGINAL O
 WHERE O.NUMERO_OS = $P{NUMERO_OS}
   AND O.COD_EMPRESA = $P{COD_EMPRESA}
   AND R.ITEM(+) = O.ITEM
 ORDER BY O.ITEM
]]>
	</queryString>
	<field name="NITEM" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="TEMPO_PADRAO" class="java.lang.Double"/>
	<columnHeader>
		<band height="16">
			<frame>
				<reportElement x="0" y="0" width="555" height="16" uuid="a4479b45-49cb-49d8-b631-36ff7cf89872">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="0" y="1" width="1" height="14" uuid="9db040c3-0bb1-409d-b270-8b86b5f2d66a"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="503" y="0" width="52" height="16" uuid="3e113abe-1068-4057-8b03-c30f41df9937"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tot. Serviço]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="444" y="0" width="59" height="16" uuid="41dba8c5-d97a-4f01-ad5a-7cbdfe6fc5ae"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tempo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="17" y="0" width="427" height="16" uuid="a66b7f7a-2c63-4af6-9421-d12e61f4f781"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Reclamação]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="15">
			<frame>
				<reportElement x="0" y="0" width="555" height="15" uuid="dc9730f4-55e7-46af-93d4-2ace73c7157d">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="17" y="0" width="427" height="15" uuid="0f68fb56-018a-4081-9f4b-6a80eecca3dc"/>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="503" y="0" width="52" height="15" uuid="87c62d83-1ff7-49f8-ac5f-1c8b26822ada"/>
					<box leftPadding="0" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="444" y="0" width="59" height="15" uuid="295d46fa-6b24-4871-9c28-ed59d3ff41ec"/>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="17" height="15" uuid="399b3678-dbb7-4bae-b84c-9236bd2eec6c"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NITEM}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
