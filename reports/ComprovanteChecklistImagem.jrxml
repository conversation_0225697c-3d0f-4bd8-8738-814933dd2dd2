<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklistImagem" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3cf726fd-45e7-489a-8330-9383d3f47ed0">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="SUP39"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="APLICACAO" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT IM.IMAGEM
FROM MOB_OS_IMAGEM IM
WHERE IM.COD_EMPRESA = $P{COD_EMPRESA} 
 AND IM.NUMERO_OS = $P{NUMERO_OS}
 AND IM.APLICACAO =  $P{APLICACAO}]]>
	</queryString>
	<field name="IMAGEM" class="java.io.InputStream"/>
	<detail>
		<band height="383" splitType="Stretch">
			<image>
				<reportElement x="0" y="0" width="300" height="360" uuid="a88ae016-1d64-410c-ad43-2a247c480cdd">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<imageExpression><![CDATA[$F{IMAGEM}]]></imageExpression>
			</image>
			<subreport>
				<reportElement x="310" y="0" width="200" height="300" uuid="b2a432be-acae-463a-b93b-feead17db55c"/>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "ComprovanteChecklistLegenda.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="13">
			<subreport>
				<reportElement x="0" y="0" width="595" height="13" uuid="692bb543-d44d-40f2-96cb-3334cd28aeb7"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}  + "ComprovanteChecklistImagemItem.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
