<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsPadraoSubFechamento" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="0efcef53-ba2f-4102-8b70-a807a9128687">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="344"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="646"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH Q_OS_FABRICA_RELACIONADAS AS
 (SELECT *
    FROM TABLE(PKG_CRM_SERVICE_UTIL.Get_Table_os_Relac_Num_Fabrica($P{NUMERO_OS},
                                                                   $P{COD_EMPRESA}))),

Q_OS_DESCONTO_FISCAIS as
 (SELECT (CASE
           WHEN NVL(OS_TIPOS_EMPRESAS.IMPRIMIR_IMP_RT_ORC_OS, 'N') = 'S' THEN
            (DESCONTOS_FISCAIS.VALOR_PIS_COFINS_CSLL +
            DESCONTOS_FISCAIS.VALOR_INSS_RETIDO +
            DESCONTOS_FISCAIS.VALOR_IRRF + DESCONTOS_FISCAIS.VALOR_ISS)
           ELSE
            0
         END) AS DESCONTO_FISCAIS_SERVICOS,
         (DESCONTOS_FISCAIS.VALOR_IMPOSTO_IPI +
         DESCONTOS_FISCAIS.VALOR_SUBSTITUICAO_TRIBUTARIA +
         DESCONTOS_FISCAIS.VALOR_FCP_ST) AS DESCONTO_FISCAIS_PECAS
    FROM OS,
         OS_TIPOS_EMPRESAS,
         TABLE(PKG_CRM_SERVICE_UTIL.GET_TABLE_DADOS_FISCAIS_OS($P{NUMERO_OS},
                                                               $P{COD_EMPRESA})) DESCONTOS_FISCAIS
   WHERE 1 = 1
     AND OS_TIPOS_EMPRESAS.TIPO(+) = OS.TIPO
     AND OS_TIPOS_EMPRESAS.COD_EMPRESA = OS.COD_EMPRESA
     AND DESCONTOS_FISCAIS.NUMERO_OS = OS.NUMERO_OS
     AND DESCONTOS_FISCAIS.COD_EMPRESA = OS.COD_EMPRESA
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}),

Q_OS_FECHAMENTO AS
 (SELECT NVL(T.SRV_VALOR_TOTAL_BRUTO, 0) SRV_VALOR_TOTAL_BRUTO,
         NVL(T.SRV_DESCONTO, 0) SRV_DESCONTO,
         NVL(T.SRV_DESC_PERCENT, 0) SRV_DESC_PERCENT,
         NVL(T.SRV_OS_LIQUIDO, 0) SRV_OS_LIQUIDO,
         NVL(T.PC_VALOR_TOTAL_BRUTO, 0) PC_VALOR_TOTAL_BRUTO,
         NVL(T.PC_DESCONTO, 0) PC_DESCONTO,
         NVL(T.PC_DESC_PERCENT, 0) PC_DESC_PERCENT,
         NVL(T.PC_OS_LIQUIDO, 0) PC_OS_LIQUIDO,
         NVL(T.SRV_VALOR_TOTAL_BRUTO, 0) + NVL(T.PC_VALOR_TOTAL_BRUTO, 0) TOTAL_BRUTO_GERAL,
         NVL(T.SRV_DESCONTO, 0) + NVL(T.PC_DESCONTO, 0) TOTAL_DESC_GERAL,
         NVL(T.PKG_TOT_IMPOSTOS, 0) PKG_TOT_IMPOSTOS,
         (NVL(T.SRV_VALOR_TOTAL_BRUTO, 0) + NVL(T.PC_VALOR_TOTAL_BRUTO, 0) +
         NVL(T.PKG_TOT_IMPOSTOS, 0)) -
         (NVL(T.SRV_DESCONTO, 0) + NVL(T.PC_DESCONTO, 0)) TOTAL_LIQ_GERAL,
         T.COD_EMPRESA,
         T.NUMERO_OS
    FROM (SELECT CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.VALOR_SERVICOS_BRUTO
                   ELSE
                    OS.ORC_SERV_BRUTO
                 END SRV_VALOR_TOTAL_BRUTO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.DESCONTOS_SERVICOS
                   ELSE
                    OS.ORC_SERV_DESCONTO
                 END SRV_DESCONTO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    CASE
                      WHEN NVL(ROUND(OS.VALOR_SERVICOS_BRUTO, 2), 0) = 0 THEN
                       0
                      ELSE
                       ROUND(100 * NVL(OS.DESCONTOS_SERVICOS, 0) /
                             NVL(OS.VALOR_SERVICOS_BRUTO, 0),
                             2)
                    END
                   ELSE
                    CASE
                      WHEN NVL(ROUND(OS.ORC_SERV_BRUTO, 2), 0) = 0 THEN
                       0
                      ELSE
                       ROUND(100 * NVL(OS.ORC_SERV_DESCONTO, 0) /
                             NVL(OS.ORC_SERV_BRUTO, 0),
                             2)
                    END
                 END SRV_DESC_PERCENT,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS
                   ELSE
                    OS.ORC_SERV_BRUTO - OS.ORC_SERV_DESCONTO
                 END SRV_OS_LIQUIDO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.VALOR_ITENS_BRUTO
                   ELSE
                    OS.ORC_ITEM_BRUTO
                 END PC_VALOR_TOTAL_BRUTO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.DESCONTOS_ITENS
                   ELSE
                    OS.ORC_ITEM_DESCONTO
                 END PC_DESCONTO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    CASE
                      WHEN NVL(ROUND(OS.VALOR_ITENS_BRUTO, 2), 0) = 0 THEN
                       0
                      ELSE
                       ROUND(100 * NVL(ROUND(OS.DESCONTOS_ITENS, 2), 0) /
                             NVL(ROUND(OS.VALOR_ITENS_BRUTO, 2), 0),
                             2)
                    END
                   ELSE
                    CASE
                      WHEN NVL(ROUND(OS.ORC_ITEM_BRUTO, 2), 0) = 0 THEN
                       0
                      ELSE
                       ROUND(100 * NVL(ROUND(OS.ORC_ITEM_DESCONTO, 2), 0) /
                             NVL(ROUND(OS.ORC_ITEM_BRUTO, 2), 0),
                             2)
                    END
                 END PC_DESC_PERCENT,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS
                   ELSE
                    OS.ORC_ITEM_BRUTO - OS.ORC_ITEM_DESCONTO
                 END PC_OS_LIQUIDO,
                 OS.PKG_TOT_IMPOSTOS,
                 OS.NUMERO_OS,
                 OS.COD_EMPRESA
            FROM OS, PARM_SYS2 PS2
           WHERE 1 = 1
             AND PS2.COD_EMPRESA = OS.COD_EMPRESA) T
   WHERE (T.COD_EMPRESA, T.NUMERO_OS) IN
         (SELECT * FROM Q_OS_FABRICA_RELACIONADAS))
SELECT SRV_VALOR_TOTAL_BRUTO,
       SRV_DESCONTO,
       SRV_DESC_PERCENT,
       SRV_OS_LIQUIDO,
       PC_VALOR_TOTAL_BRUTO,
       PC_DESCONTO,
       PC_DESC_PERCENT,
       PC_OS_LIQUIDO,
       TOTAL_BRUTO_GERAL,
       TOTAL_DESC_GERAL,
       PKG_TOT_IMPOSTOS,
       TOTAL_LIQ_GERAL,
       DESCONTO_FISCAIS_PECAS,
       DESCONTO_FISCAIS_SERVICOS
      
  FROM (SELECT SUM(SRV_VALOR_TOTAL_BRUTO) AS SRV_VALOR_TOTAL_BRUTO,
               SUM(SRV_DESCONTO)          AS SRV_DESCONTO,
               SUM(SRV_DESC_PERCENT)      AS SRV_DESC_PERCENT,
               SUM(SRV_OS_LIQUIDO)        AS SRV_OS_LIQUIDO,
               SUM(PC_VALOR_TOTAL_BRUTO)  AS PC_VALOR_TOTAL_BRUTO,
               SUM(PC_DESCONTO)           AS PC_DESCONTO,
               SUM(PC_DESC_PERCENT)       AS PC_DESC_PERCENT,
               SUM(PC_OS_LIQUIDO)         AS PC_OS_LIQUIDO,
               SUM(TOTAL_BRUTO_GERAL)     AS TOTAL_BRUTO_GERAL,
               SUM(TOTAL_DESC_GERAL)      AS TOTAL_DESC_GERAL,
               SUM(PKG_TOT_IMPOSTOS)      AS PKG_TOT_IMPOSTOS,
               SUM(TOTAL_LIQ_GERAL)       AS TOTAL_LIQ_GERAL
          FROM Q_OS_FECHAMENTO) SOMA_SERVICOS,
         Q_OS_DESCONTO_FISCAIS]]>
	</queryString>
	<field name="SRV_VALOR_TOTAL_BRUTO" class="java.lang.Double"/>
	<field name="SRV_DESCONTO" class="java.lang.Double"/>
	<field name="SRV_DESC_PERCENT" class="java.lang.Double"/>
	<field name="SRV_OS_LIQUIDO" class="java.lang.Double"/>
	<field name="PC_VALOR_TOTAL_BRUTO" class="java.lang.Double"/>
	<field name="PC_DESCONTO" class="java.lang.Double"/>
	<field name="PC_DESC_PERCENT" class="java.lang.Double"/>
	<field name="PC_OS_LIQUIDO" class="java.lang.Double"/>
	<field name="TOTAL_BRUTO_GERAL" class="java.lang.Double"/>
	<field name="TOTAL_DESC_GERAL" class="java.lang.Double"/>
	<field name="PKG_TOT_IMPOSTOS" class="java.lang.Double"/>
	<field name="TOTAL_LIQ_GERAL" class="java.lang.Double"/>
	<field name="DESCONTO_FISCAIS_PECAS" class="java.lang.Double"/>
	<field name="DESCONTO_FISCAIS_SERVICOS" class="java.lang.Double"/>
	<title>
		<band height="19" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="1" width="554" height="18" backcolor="#D9D9D9" uuid="8e72b655-22fe-4155-91d5-649e08d38a2b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
		</band>
	</title>
	<detail>
		<band height="60" splitType="Stretch">
			<staticText>
				<reportElement x="6" y="-16" width="100" height="13" uuid="883e6159-f612-47dc-ad0d-7a4898d29287">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Fechamento]]></text>
			</staticText>
			<staticText>
				<reportElement x="6" y="4" width="58" height="14" uuid="aa79422f-d200-408a-9d8c-fc4765572c99">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[Serviços:]]></text>
			</staticText>
			<staticText>
				<reportElement x="6" y="18" width="58" height="14" uuid="75716480-86de-4bf7-95bd-692d95cc1be9">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[Descontos:]]></text>
			</staticText>
			<frame>
				<reportElement mode="Transparent" x="6" y="32" width="124" height="1" uuid="30cb3195-a77d-42bd-a1ca-4449e12683c3"/>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<staticText>
				<reportElement x="6" y="32" width="58" height="14" uuid="6051cbd8-fdec-4547-9b75-30947dc6c1b9">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<textField pattern="#,##0.00">
				<reportElement x="65" y="4" width="65" height="14" uuid="121cbf88-824f-48ee-b289-0c27ae13d07c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SRV_VALOR_TOTAL_BRUTO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="65" y="18" width="65" height="14" uuid="d1f8ca77-02db-40aa-a9d0-3bc6c8de3c80">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SRV_DESCONTO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="65" y="32" width="65" height="14" uuid="13a840f2-41d2-4e83-8153-014483c37ad4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SRV_VALOR_TOTAL_BRUTO} - ($F{SRV_DESCONTO})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="186" y="4" width="58" height="14" uuid="49add40d-0e92-4047-9ff5-54fa1e8cf01e">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[Itens:]]></text>
			</staticText>
			<staticText>
				<reportElement x="186" y="18" width="58" height="14" uuid="058bd477-53ce-4638-96fa-229cc5bb57b1">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[Descontos:]]></text>
			</staticText>
			<frame>
				<reportElement mode="Transparent" x="186" y="32" width="124" height="1" uuid="48e97194-e7db-4618-b4d2-086343fadd28"/>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<staticText>
				<reportElement x="186" y="32" width="58" height="14" uuid="bf323a2e-05d2-4460-b3f9-ee9840df6ab7">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<textField pattern="#,##0.00">
				<reportElement x="245" y="4" width="65" height="14" uuid="06f7babf-c11d-444e-9c1b-e90d70406153">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PC_VALOR_TOTAL_BRUTO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="245" y="18" width="65" height="14" uuid="9fac3bb3-c115-4978-a59b-109502ffeb29">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PC_DESCONTO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="245" y="32" width="65" height="14" uuid="16b0bdd5-5092-4ce0-ba94-9cda9d14a716">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PC_VALOR_TOTAL_BRUTO} - ($F{PC_DESCONTO})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="380" y="4" width="69" height="14" uuid="4a5a984c-72a5-4610-8058-8d09dde8f6ed">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[Serviços+Itens:]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="32" width="69" height="14" uuid="51698cf7-1fea-45be-8213-efdd788bd3cb">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[Descontos:]]></text>
			</staticText>
			<frame>
				<reportElement mode="Transparent" x="380" y="46" width="135" height="1" uuid="c260e892-29ab-4db7-ade7-17d551401395"/>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<staticText>
				<reportElement x="380" y="46" width="69" height="14" uuid="7b6be133-74a1-418f-9757-b5ba0e9cc6d8">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<textField pattern="#,##0.00">
				<reportElement x="450" y="4" width="65" height="14" uuid="a8155f41-666e-4c5a-9adf-97375b34a54a">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SRV_VALOR_TOTAL_BRUTO} + $F{PC_VALOR_TOTAL_BRUTO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="450" y="32" width="65" height="14" uuid="5bd1ec10-f677-4c4b-a270-d6e581fd570c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SRV_DESCONTO} + $F{PC_DESCONTO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="450" y="46" width="65" height="14" uuid="9d6c6c20-57cb-4ba5-897e-fa96def2674e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SRV_VALOR_TOTAL_BRUTO} + $F{PC_VALOR_TOTAL_BRUTO} - ($F{SRV_DESCONTO} + $F{DESCONTO_FISCAIS_SERVICOS} + $F{PC_DESCONTO} + $F{DESCONTO_FISCAIS_PECAS})]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="450" y="18" width="65" height="14" uuid="da00f071-129f-4635-971b-79c55be5607a">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCONTO_FISCAIS_SERVICOS} + $F{DESCONTO_FISCAIS_PECAS}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="380" y="18" width="69" height="14" uuid="9a4bb911-a11c-4c80-bf9e-5c95a46c8b3d">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[(+)Impostos:]]></text>
			</staticText>
		</band>
	</detail>
	<summary>
		<band>
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</summary>
</jasperReport>
