<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListGwmPdsSubItemOpcaoNaoAplicavel" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[115582.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="OBRIGATORIO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<parameter name="LISTA_GRUPOS" class="java.lang.String">
		<defaultValueExpression><![CDATA["30050"]]></defaultValueExpression>
	</parameter>
	<parameter name="ID_GRUPO" class="java.lang.Double">
		<defaultValueExpression><![CDATA[31550.0]]></defaultValueExpression>
	</parameter>
	<parameter name="ID_CHECKLIST" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH TODOS_ITENS as (
SELECT A.ID_GRUPO,
       A.COD_ITEM,
       TRIM(
            CASE 
                WHEN INSTR(A.ITEM_DESCRICAO, '-') > 0 
                THEN SUBSTR(A.ITEM_DESCRICAO, 1, INSTR(A.ITEM_DESCRICAO, '-') - 1)
                ELSE ''
            END
        ) AS ACAO_ITEM,
       TRIM(SUBSTR(A.ITEM_DESCRICAO, INSTR(A.ITEM_DESCRICAO, '-') + 1)) AS DESCRICAO_ITEM,
       A.ITEM_DESCRICAO,
       NVL(A.ITEM_OBSERVACAO,' ') AS OBSERVACAO_ITEM,
       A.ITEM_RESPOSTA_EH_OBSERVACAO AS RESPOSTA_EH_OBSERVACAO,
       UPPER(A.SELECIONADO_OPCAO_DESCRICAO) AS DESCRICAO_OPCAO,
       A.ITEM_DESCRICAO DESCRICAO_GRUPO,
       A.ITEM_ORDEM AS ORDEM_GRUPO ,
       NVL(A.SELECIONADO_OBSERVACAO,' ') AS OBSERVACAO,
       ROW_NUMBER() OVER (ORDER BY A.ID_GRUPO, A.GRUPO_ORDEM, A.ITEM_ORDEM) AS LINHA_RELATORIO
FROM TABLE(PKG_CRM_SERVICE_CHECKLIST.GET_TABLE_CHECKLIST_ITEM($P{COD_EMPRESA}, $P{NUMERO_OS}, 0, '')) A
WHERE A.ID_CHECKLIST = $P{ID_CHECKLIST}
      AND A.ATIVO = 'S'
)
select ID_GRUPO,
       COD_ITEM,
       ACAO_ITEM,
       CASE WHEN LENGTH(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM) > 300 then LPAD(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM,297) || '...' ELSE DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM END AS DESCRICAO_ITEM_ABREVIADA,
       DESCRICAO_ITEM,
       OBSERVACAO_ITEM,
       RESPOSTA_EH_OBSERVACAO,
       DESCRICAO_OPCAO,
       DESCRICAO_GRUPO,
       OBSERVACAO,
       ORDEM_GRUPO,
       LINHA_RELATORIO
FROM TODOS_ITENS
WHERE ID_GRUPO = $P{ID_GRUPO}
ORDER BY ID_GRUPO]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="ACAO_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM_ABREVIADA" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="OBSERVACAO_ITEM" class="java.lang.String"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="ORDEM_GRUPO" class="java.lang.Double"/>
	<field name="LINHA_RELATORIO" class="java.lang.Double"/>
	<variable name="returnValue" class="java.lang.Double">
		<variableExpression><![CDATA[$V{returnValue}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="14">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean(($F{DESCRICAO_ITEM_ABREVIADA}.length()) < 100)]]></printWhenExpression>
			<frame borderSplitType="DrawBorders">
				<reportElement x="0" y="0" width="555" height="14" isPrintWhenDetailOverflows="true" uuid="0703988c-4b5b-44b6-9da3-5329b13d2953">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="95" y="0" width="31" height="14" isPrintWhenDetailOverflows="true" uuid="6c2564fc-7f4a-47c6-9d82-b6cd1f5d7c04">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ACAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement key="" stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="14" isPrintWhenDetailOverflows="true" uuid="89784aff-f7ba-4acb-a8d1-b0ea1d13fed4">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA_RELATORIO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="505" y="0" width="25" height="14" isPrintWhenDetailOverflows="true" uuid="55c86164-8332-4873-84c3-93d898108fb1">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NG") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="480" y="0" width="25" height="14" isPrintWhenDetailOverflows="true" uuid="7abe8338-30fd-4f30-84ec-f9051edc08c3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="126" y="0" width="354" height="14" backcolor="#A3FFCE" uuid="370d2936-66dc-477b-95b4-73d938dd6346">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM_ABREVIADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="530" y="0" width="25" height="14" isPrintWhenDetailOverflows="true" uuid="ed2f2fa5-1077-4b6a-afe9-2322f10dd9f7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NÃO APLICÁVEL") ? "X" : ""]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="28">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{DESCRICAO_ITEM_ABREVIADA}.length() > 100 && $F{DESCRICAO_ITEM_ABREVIADA}.length() <= 200)]]></printWhenExpression>
			<frame borderSplitType="DrawBorders">
				<reportElement x="0" y="0" width="555" height="28" isPrintWhenDetailOverflows="true" uuid="2a81b7ca-9fd5-4d52-9db2-a1ba3feb9319">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="95" y="0" width="31" height="28" isPrintWhenDetailOverflows="true" uuid="7a12e8df-6419-48eb-8422-ce6ee6d2616b">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ACAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement key="" stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="28" isPrintWhenDetailOverflows="true" uuid="6ed7f37e-650a-4251-93ca-c9f13db6e73b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA_RELATORIO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="505" y="0" width="25" height="28" isPrintWhenDetailOverflows="true" uuid="5029f178-43b2-40be-9e19-1d53daebb1c0">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NG") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="480" y="0" width="25" height="28" isPrintWhenDetailOverflows="true" uuid="b7d0c64e-1b8f-4cd3-80fd-0a46815280a3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="126" y="0" width="354" height="28" backcolor="#FFCDCC" uuid="50bada55-fec2-460c-aa8d-51d8660a2fd0">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM_ABREVIADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="530" y="0" width="25" height="28" isPrintWhenDetailOverflows="true" uuid="a9045729-b486-4d35-81c8-fa2bb1e2747e">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NÃO APLICÁVEL") ? "X" : ""]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="42">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{DESCRICAO_ITEM_ABREVIADA}.length() > 200)]]></printWhenExpression>
			<frame borderSplitType="DrawBorders">
				<reportElement x="0" y="0" width="555" height="42" isPrintWhenDetailOverflows="true" uuid="66b11279-d5c4-4d1a-a448-0f0a68adacec">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="95" y="0" width="31" height="42" isPrintWhenDetailOverflows="true" uuid="3d66a84a-2745-430e-a9a7-b91afd4b8c06">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ACAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement key="" stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="42" isPrintWhenDetailOverflows="true" uuid="ad1ddb37-94a5-4bf7-8f08-4bcc2067b021">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA_RELATORIO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="505" y="0" width="25" height="42" isPrintWhenDetailOverflows="true" uuid="fe98beee-59a6-4cff-8c5e-eb1a22a1ec35">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NG") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="480" y="0" width="25" height="42" isPrintWhenDetailOverflows="true" uuid="7886a8f6-3b11-4f77-a9a3-fcf456192859">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="126" y="0" width="354" height="42" backcolor="#A4AAFC" uuid="0dcbf5e9-7b46-403a-85d9-e149fcb8da5f">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="0" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM_ABREVIADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="530" y="0" width="25" height="42" isPrintWhenDetailOverflows="true" uuid="fc3045fe-0f87-47e5-8fed-d572972c553b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NÃO APLICÁVEL") ? "X" : ""]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
