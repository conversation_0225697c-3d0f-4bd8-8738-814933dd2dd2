<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaVinteSeteItensFormularioInspecaoFinalSubQualidadeFinal" columnCount="3" pageWidth="575" pageHeight="119" columnWidth="175" columnSpacing="10" leftMargin="10" rightMargin="10" topMargin="0" bottomMargin="0" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<style name="Outros" style="Style1">
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{ITEM_DESCRICAO}.equals("Outros")]]></conditionExpression>
			<style>
				<box>
					<bottomPen lineWidth="0.75"/>
				</box>
			</style>
		</conditionalStyle>
	</style>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select 
  initcap(a.item_descricao) as item_descricao,
  a.SELECIONADO_OBSERVACAO as observacao,
  UPPER(a.SELECIONADO_OPCAO_DESCRICAO) as descricao_opcao,
  a.ITEM_RESPOSTA_EH_OBSERVACAO as resposta_eh_observacao
from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
where a.id_checklist = 20000
      and a.id_grupo in (20010,  20011) -- honda checklist(estático,  dinâmico)]]>
	</queryString>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<pageHeader>
		<band height="29">
			<staticText>
				<reportElement x="0" y="16" width="175" height="9" uuid="7ebe6642-70fe-4288-ba07-c55e0b1a1e6a">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[ESTÁTICO]]></text>
			</staticText>
			<staticText>
				<reportElement x="185" y="16" width="370" height="9" uuid="b4ead4e2-07d7-4d15-9680-ede0effa0155">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[DINÂMICO]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="3" width="555" height="9" uuid="3e287a61-89e4-4a57-88e1-74519b962782">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[INSPEÇÃO FINAL - PILOTO]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="15">
			<textField>
				<reportElement x="0" y="2" width="19" height="11" uuid="9775c997-ebb0-45e5-add4-6415689c751a">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
?$F{OBSERVACAO}.equals("")  ? "Y" :"X"
:$F{DESCRICAO_OPCAO}.equals("SIM") ?"X":""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="Outros" x="27" y="3" width="142" height="9" uuid="16721e60-9106-43bc-8642-dac536016def">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}.equals("Outros") ? $F{OBSERVACAO} : $F{ITEM_DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
