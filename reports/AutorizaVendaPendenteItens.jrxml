<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RequisicoesItens" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="5" bottomMargin="5" uuid="b210b23a-3a04-4bce-bdc3-5829df4bd199">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="no_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT VSE.COD_EMPRESA
     , EMPRESAS.NOME AS NOME_EMPRESA
     , VSE.DOCUMENTO    
     , VSE.COD_ITEM
     , ITENS.DESCRICAO
     , vse.OBSERVACAO
     , VSE.COD_FORNECEDOR
     , FE.NOME_FORNECEDOR
     , VSE.QTDE_ORIGINAL
     , nvl(Nvl(vse.preco_venda,vse.preco_unitario),0)  AS PRECO_VENDA
     , nvl(NVL(VSE.preco_venda,vse.preco_unitario),0) * NVL(VSE.QTDE_ORIGINAL,0) AS PRECO_TOTAL
    ,  to_char(sysdate,'DD/MM/YYYY HH24:MI:SS') DATAATUAL_SERV
  FROM VENDAS_SEM_ESTOQUE VSE,
       ITENS,
       FORNECEDOR_ESTOQUE_EMPRESA FEE,
       FORNECEDOR_ESTOQUE FE,
        EMPRESAS
  WHERE VSE.COD_ITEM = ITENS.COD_ITEM
   AND VSE.COD_EMPRESA = FEE.COD_EMPRESA
   AND VSE.COD_FORNECEDOR = FEE.COD_FORNECEDOR
   AND FEE.COD_FORNECEDOR = FE.COD_FORNECEDOR(+)
   AND FEE.COD_EMPRESA = FE.COD_EMPRESA(+)
   AND VSE.COD_EMPRESA = EMPRESAS.COD_EMPRESA   
   AND VSE.DOCUMENTO = $P{NUMERO_OS}
   AND VSE.COD_EMPRESA = $P{COD_EMPRESA}
 ORDER BY ITENS.DESCRICAO]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.math.BigDecimal"/>
	<field name="NOME_EMPRESA" class="java.lang.String"/>
	<field name="DOCUMENTO" class="java.math.BigDecimal"/>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="COD_FORNECEDOR" class="java.math.BigDecimal"/>
	<field name="NOME_FORNECEDOR" class="java.lang.String"/>
	<field name="QTDE_ORIGINAL" class="java.math.BigDecimal"/>
	<field name="PRECO_VENDA" class="java.math.BigDecimal"/>
	<field name="PRECO_TOTAL" class="java.math.BigDecimal"/>
	<field name="DATAATUAL_SERV" class="java.lang.String"/>
	<variable name="totalItens" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{COD_ITEM}]]></variableExpression>
	</variable>
	<variable name="qtdeTotal" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{QTDE_ORIGINAL}]]></variableExpression>
	</variable>
	<variable name="valorTotal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{PRECO_TOTAL}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="21" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#E6E6E6" uuid="9c26aca8-03ed-48b6-b7b3-b6a4b58c5ae2">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="124" y="2" width="46" height="14" uuid="17cf11c6-6eb7-459c-835e-16cd45da144b">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement x="279" y="2" width="46" height="14" uuid="fc3269ad-5df1-4958-ab4d-c89deecf0e55">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Fornec]]></text>
			</staticText>
			<staticText>
				<reportElement x="422" y="2" width="24" height="14" uuid="7502c13f-5f7c-4b1f-bf2b-3d9b55be743e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement x="463" y="2" width="45" height="14" uuid="0a4ca2b0-da88-44eb-8fd9-eb35ca75eee1">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Vlr Unitário]]></text>
			</staticText>
			<staticText>
				<reportElement x="512" y="2" width="39" height="14" uuid="19a565f1-a0e3-4883-9917-44df47322fd8">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Vlr Total]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="4" width="46" height="14" uuid="b53dac2d-f45c-45c9-89c6-9a0dd7b9a88a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Código]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="35" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField>
				<reportElement x="1" y="3" width="123" height="14" uuid="81c8b626-138f-4a7e-9dcf-89c00047de7a"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="124" y="3" width="155" height="14" uuid="ada07fed-3c39-4d62-bbd3-200a83549e5b"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="279" y="3" width="121" height="14" uuid="8fff3a6b-eca5-4024-83da-408e9f1ac790"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME_FORNECEDOR}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="422" y="3" width="32" height="14" uuid="b05fcef1-7740-4614-8b70-136792690622"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QTDE_ORIGINAL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="463" y="3" width="45" height="14" uuid="ab8952cf-989f-4f76-b9d0-3b7b5b103e9a"/>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="512" y="3" width="39" height="14" uuid="0d2df4ec-b5df-46d3-9a66-88d340b5e4fe"/>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_TOTAL}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="51" y="17" width="490" height="14" uuid="dadf4477-5b03-464f-8eb5-a7cb3da924d9"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="17" width="47" height="14" uuid="0412aa22-7dd8-415f-827c-436857d0fc8c">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Observação:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="0" width="554" height="1" forecolor="#D6D6D6" uuid="38e687dd-4cd4-4422-a01d-4de9dc83b6f5">
					<printWhenExpression><![CDATA[new Boolean($V{COLUMN_COUNT} != 1.0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band height="171" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#E6E6E6" uuid="3e712a3f-c9c7-4c77-8b33-1d037ea65838">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="1" y="2" width="46" height="14" uuid="9b7c2d6a-9636-48f8-97ed-aecfea43244f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Total de Itens:]]></text>
			</staticText>
			<textField>
				<reportElement x="48" y="2" width="52" height="14" uuid="316fe163-c224-4d7b-99f4-5c8299de5c4a"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalItens}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="483" y="4" width="68" height="12" uuid="85482b0e-a18f-4260-9fb6-2501cd4a43e1"/>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{valorTotal}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement mode="Transparent" x="191" y="142" width="210" height="1" uuid="d4e9a8b3-2031-4a4c-b28b-d31d3a08394f"/>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<staticText>
				<reportElement x="191" y="143" width="209" height="14" uuid="2aabbb2b-c80c-4e6d-a960-04f8443da21f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Cliente ou Responsável]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="25" width="551" height="75" isPrintWhenDetailOverflows="true" uuid="4dd1d706-0a4a-4f22-86df-48d1569e8d7f"/>
				<staticText>
					<reportElement x="0" y="0" width="100" height="14" uuid="b0a4252b-38bc-4b39-bdee-462e9dab159c">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Cliente ou Responsável:]]></text>
				</staticText>
				<textField>
					<reportElement x="0" y="15" width="551" height="60" uuid="aa5f847f-628a-4255-aff2-3dcb9f137b56"/>
					<textElement textAlignment="Justified"/>
					<textFieldExpression><![CDATA["Eu assumo o compromisso em trazer o veiculo para aplicação das peças solicitadas, após a comunicação da chegada das mesmas no prazo maximo de 05 dias úteis. Deixo autorizado o faturamento das mesmas em meu nome, caso não venha substituí-las na "+$F{NOME_EMPRESA}+" , no prazo acima estipulado."]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="60" width="30" height="14" uuid="2939f326-70a3-4bc5-8fe2-3dbeb382d235">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="25" y="60" width="86" height="14" uuid="a29deb24-031d-4723-b126-a80f858da1d6"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATAATUAL_SERV}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnFooter>
</jasperReport>
