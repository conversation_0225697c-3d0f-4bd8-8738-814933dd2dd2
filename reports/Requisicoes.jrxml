<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Requisicoes" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a5d195c3-46e2-4e2e-9575-573e21d1a687">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Desenvolvimento"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="false"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="570"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="416"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Endereco" uuid="b5cc2c7c-1709-4206-b75b-66bfdebecbb1">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Desenvolvimento"/>
		<queryString>
			<![CDATA[select  cli.cod_cliente,
        clid.CPF,
        CLI.NOME,
        cli.prefixo_res,
        cli.telefone_res,
        cli.prefixo_com,
        cli.telefone_com,
        CLI.RUA_RES,
        CLI.BAIRRO_RES,
        CLI.CEP_RES,
        CLI.COMPLEMENTO_RES,
        CID.DESCRICAO,
        CID.UF,
        CLI.FACHADA_RES
  from clientes cli, cliente_diverso clid, CIDADES CID
 where cli.cod_cliente = clid.cod_cliente
   AND CLI.COD_CID_RES = CID.COD_CIDADES  
   and cli.cod_cliente = 98374869615]]>
		</queryString>
		<field name="COD_CLIENTE" class="java.math.BigDecimal"/>
		<field name="CPF" class="java.lang.String"/>
		<field name="NOME" class="java.lang.String"/>
		<field name="PREFIXO_RES" class="java.lang.String"/>
		<field name="TELEFONE_RES" class="java.lang.String"/>
		<field name="PREFIXO_COM" class="java.lang.String"/>
		<field name="TELEFONE_COM" class="java.lang.String"/>
		<field name="RUA_RES" class="java.lang.String"/>
		<field name="BAIRRO_RES" class="java.lang.String"/>
		<field name="CEP_RES" class="java.lang.String"/>
		<field name="COMPLEMENTO_RES" class="java.lang.String"/>
		<field name="DESCRICAO" class="java.lang.String"/>
		<field name="UF" class="java.lang.String"/>
		<field name="FACHADA_RES" class="java.lang.String"/>
	</subDataset>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="REQUISICAO" class="java.lang.Double"/>
	<parameter name="IMPENDERECO" class="java.lang.String"/>
	<parameter name="IMPLOCACAO" class="java.lang.String"/>
	<parameter name="IMPDEFEITO" class="java.lang.String"/>
	<parameter name="IMPPLACA" class="java.lang.String"/>
	<parameter name="ORDENAR" class="java.lang.String"/>
	<parameter name="MOSTRAR" class="java.lang.String"/>
	<parameter name="RECADO" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT os.cod_empresa,
       os.numero_os,
        CASE
          WHEN OS.NUMERO_OS < 0 THEN
            'ORC.: ' || TO_CHAR(ABS(OS.NUMERO_OS))
          ELSE
            'O.S: ' || TO_CHAR(OS.NUMERO_OS)
          END TITULO,
        (PKG_CRM_SERVICE_UTIL.GET_TIPO_OS(OS.COD_EMPRESA, OS.TIPO, 'TP')) TIPO,
        (PKG_CRM_SERVICE_UTIL.GET_TIPO_OS(OS.COD_EMPRESA, OS.TIPO, 'XX')) TIPO_DESC,
        TO_CHAR(TO_DATE(SYSDATE,'DD/MM/RRRR'), 'DD/MM/RRRR') DATA_ATUAL,
        GET_FIRST_SECOND_NAME(P.DESCRICAO_PRODUTO, 2) || ' / ' || INITCAP(PRODUTOMODELO.DESCRICAO_MODELO) AS VEICULO,  
        ODV.CHASSI,
        ODV.KM,
        NVL(ODV.ANO, PRODUTOMODELO.ANO_MODELO) ANO_MODELO,
        ODV.PLACA,
        CLI.COD_CLIENTE,
        CLI_DIV.CPF,
        CLI.NOME,
        CLI.RUA_RES,
        CLI.BAIRRO_RES,
        CLI.CEP_RES,
        CLI.COMPLEMENTO_RES,
        CID.DESCRICAO,
        CID.UF,
        CLI.FACHADA_RES,
        cli.prefixo_res,
        cli.telefone_res,
        cli.prefixo_com,
        cli.telefone_com,
         OREQ.DATA,
        OREQ.DATA_REQ,
        OREQ.HORA_REQ,
        OREQ.REQUISICAO,
        OREQ.QTDE_REIMPRESSAO,
         REQASS.ASSINATURA_PRODUTIVO,
        CASE
            WHEN REQASS.ASSINATURA_PRODUTIVO IS NULL THEN
              'N'
            ELSE
              'S'
        END TEM_ASSINATURA_PRODUTIVO,
       get_first_second_name(initcap( OREQ.NOME_PRODUTIVO),2) AS NOME_PRODUTIVO,
        get_first_second_name(initcap(OS.NOME),2) AS CONSULTOR,
        get_first_second_name(initcap(OREQ.VENDEDOR),2) AS VENDEDOR
   FROM OS,
        OS_DADOS_VEICULOS ODV,
        PRODUTOS_MODELOS  PRODUTOMODELO,
        PRODUTOS          P,
        CLIENTES          CLI,
        CLIENTE_DIVERSO   CLI_DIV,
        CIDADES           CID ,
        (SELECT O.DATA,
                 TRUNC(O.DATA) DATA_REQ,
                 TO_CHAR(O.DATA, 'HH:MM:ss') HORA_REQ, 
                 O.REQUISICAO,
                 O.QTDE_REIMPRESSAO,
                 ST.NOME AS NOME_PRODUTIVO,
                 EU.NOME_COMPLETO AS VENDEDOR       
          FROM OS_REQUISICOES O, 
                      SERVICOS_TECNICOS ST,
                      EMPRESAS_USUARIOS EU
         WHERE O.COD_EMPRESA = ST.COD_EMPRESA(+)
           AND O.COD_TECNICO = ST.COD_TECNICO(+)
           and O.NOME = EU.NOME(+)
           AND O.COD_EMPRESA =  $P{COD_EMPRESA}
           AND O.NUMERO_OS  = $P{NUMERO_OS}
           AND O.REQUISICAO = $P{REQUISICAO}
           AND ROWNUM = 1) OREQ ,
            (SELECT REQA.COD_EMPRESA, 
                 REQA.NUMERO_OS,
           REQA.ASSINATURA_PRODUTIVO
          FROM OS_REQ_ASSINATURA REQA
         WHERE REQA.COD_EMPRESA =   $P{COD_EMPRESA}
           AND REQA.NUMERO_OS  = $P{NUMERO_OS}
           AND REQA.REQUISICAO =  $P{REQUISICAO}
           AND ROWNUM = 1) REQASS
 WHERE OS.COD_EMPRESA = ODV.COD_EMPRESA
   AND OS.NUMERO_OS = ODV.NUMERO_OS
   AND OS.COD_PRODUTO = PRODUTOMODELO.COD_PRODUTO
   AND OS.COD_MODELO = PRODUTOMODELO.COD_MODELO
   AND OS.COD_CLIENTE = CLI.COD_CLIENTE
   AND OS.COD_PRODUTO = P.COD_PRODUTO
   AND CLI.COD_CLIENTE = CLI_DIV.COD_CLIENTE
   AND CLI.COD_CID_RES = CID.COD_CIDADES    
    AND OS.COD_EMPRESA = REQASS.COD_EMPRESA(+)
   AND OS.NUMERO_OS = REQASS.NUMERO_OS(+)
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND OS.NUMERO_OS = $P{NUMERO_OS}]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="TITULO" class="java.lang.String"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="TIPO_DESC" class="java.lang.String"/>
	<field name="DATA_ATUAL" class="java.lang.String"/>
	<field name="VEICULO" class="java.lang.String"/>
	<field name="CHASSI" class="java.lang.String"/>
	<field name="KM" class="java.lang.Double"/>
	<field name="ANO_MODELO" class="java.lang.String"/>
	<field name="PLACA" class="java.lang.String"/>
	<field name="COD_CLIENTE" class="java.lang.Double"/>
	<field name="CPF" class="java.lang.String"/>
	<field name="NOME" class="java.lang.String"/>
	<field name="RUA_RES" class="java.lang.String"/>
	<field name="BAIRRO_RES" class="java.lang.String"/>
	<field name="CEP_RES" class="java.lang.String"/>
	<field name="COMPLEMENTO_RES" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="UF" class="java.lang.String"/>
	<field name="FACHADA_RES" class="java.lang.String"/>
	<field name="PREFIXO_RES" class="java.lang.String"/>
	<field name="TELEFONE_RES" class="java.lang.String"/>
	<field name="PREFIXO_COM" class="java.lang.String"/>
	<field name="TELEFONE_COM" class="java.lang.String"/>
	<field name="DATA" class="java.sql.Timestamp"/>
	<field name="DATA_REQ" class="java.sql.Timestamp"/>
	<field name="HORA_REQ" class="java.lang.String"/>
	<field name="REQUISICAO" class="java.lang.Double"/>
	<field name="QTDE_REIMPRESSAO" class="java.lang.Integer"/>
	<field name="ASSINATURA_PRODUTIVO" class="java.io.InputStream"/>
	<field name="TEM_ASSINATURA_PRODUTIVO" class="java.lang.String"/>
	<field name="NOME_PRODUTIVO" class="java.lang.String"/>
	<field name="CONSULTOR" class="java.lang.String"/>
	<field name="VENDEDOR" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="71" splitType="Stretch">
			<subreport>
				<reportElement positionType="Float" x="0" y="5" width="554" height="66" isRemoveLineWhenBlank="true" uuid="e6b46a5a-93d8-41cc-beee-41c77135cf38">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"CabecalhoPadraoRetrato.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</title>
	<pageHeader>
		<band height="77">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="41" width="554" height="18" backcolor="#E6E6E6" uuid="f044ce1b-c6b0-40ca-b473-27331128e380">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="1" y="43" width="60" height="14" uuid="a66bf5c6-78d9-4ca9-a46a-2263872138e5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Requisição:]]></text>
			</staticText>
			<textField>
				<reportElement x="256" y="43" width="42" height="14" uuid="cc7e89cf-f441-46bd-9cce-0637e9e9a757"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIPO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="501" y="43" width="44" height="14" uuid="920e8209-084e-451d-9453-f0971c80d3a9"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HORA_REQ}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="409" y="43" width="42" height="14" uuid="d43ce4de-3380-4d4e-9092-6237bdd71695"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DATA_REQ}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="53" y="43" width="42" height="14" uuid="7d85f495-5510-4882-9d3e-5b96a429108f"/>
				<textElement verticalAlignment="Middle">
					<font size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new Integer($F{REQUISICAO}.intValue())]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="149" y="43" width="66" height="14" uuid="35007780-be87-407c-881d-d943472c6669"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new Integer($F{NUMERO_OS}.intValue())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="471" y="43" width="30" height="14" uuid="3eae6f4a-224a-4d4a-881e-479b3a98356f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<staticText>
				<reportElement x="230" y="43" width="26" height="14" uuid="d2f9aff0-f937-4093-8f3c-0b76c99bba92">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo:]]></text>
			</staticText>
			<staticText>
				<reportElement x="379" y="43" width="32" height="14" uuid="f7759447-cec2-4c0d-b39a-11a5ae492e54">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="129" y="43" width="20" height="14" uuid="4156bb02-3b82-4a89-96f3-2cd63f7c44ee">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[OS:]]></text>
			</staticText>
			<textField>
				<reportElement x="1" y="0" width="553" height="40" uuid="1beb5197-b699-4e85-b45e-e0e56f4d68df"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="18" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QTDE_REIMPRESSAO} == null?"REQUISIÇÃO":"REIMPRESSÃO DA REQUISIÇÃO"]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="59" width="554" height="18" backcolor="#E6E6E6" uuid="6bc087b2-b98a-491e-a9a3-469e8ccf5fd0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="1" y="60" width="45" height="14" uuid="9af2a6cc-825f-486d-becd-48791264aaee">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Consultor:]]></text>
			</staticText>
			<staticText>
				<reportElement x="230" y="60" width="42" height="14" uuid="c315fb3f-16f1-4a02-9002-d5945c9f64bc">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Vendedor:]]></text>
			</staticText>
			<staticText>
				<reportElement x="379" y="60" width="42" height="14" uuid="2bc749c1-ab3a-4d70-b62e-5a8c026bd062">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Produtivo:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="421" y="60" width="133" height="14" uuid="e4707751-1d50-4638-a475-4b86d64e65c4"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME_PRODUTIVO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="46" y="60" width="154" height="14" uuid="2c83d75c-8dde-45b2-acbc-de991e7623f5"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CONSULTOR}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="272" y="60" width="107" height="14" uuid="d0df14b4-dc1a-4a1e-b26d-22dd8cf48d9a"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{VENDEDOR}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="14">
			<printWhenExpression><![CDATA[$P{IMPPLACA} == "S"]]></printWhenExpression>
			<staticText>
				<reportElement x="0" y="0" width="46" height="14" uuid="59dcb895-0cf7-4826-8a06-3151a1c07fcd">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Produto:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="450" y="0" width="78" height="14" uuid="f19708ab-8c80-4a67-ac4c-4cf8ecd56ea3"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PLACA}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="262" y="0" width="129" height="14" uuid="51c37f1c-5826-4380-a4ef-dda3724b9a4b"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHASSI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="230" y="0" width="31" height="14" uuid="27a534e5-dd1c-420f-9594-04ba2e0fef12">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Chassi:]]></text>
			</staticText>
			<staticText>
				<reportElement x="421" y="0" width="26" height="14" uuid="6109fe18-a564-416f-b390-814a01b037ac">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Placa:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="46" y="0" width="154" height="14" uuid="5275ceb7-b539-47f0-b23e-5315a0658e4c"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{VEICULO}]]></textFieldExpression>
			</textField>
		</band>
		<band height="16">
			<staticText>
				<reportElement x="0" y="2" width="46" height="14" uuid="7d1d33e8-572c-45d1-a46c-973eff6f2be1">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Cliente:]]></text>
			</staticText>
			<staticText>
				<reportElement x="379" y="2" width="54" height="14" uuid="79a2dab3-35c5-41ee-a723-a26ee9c63c9a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CPF / CNPJ:]]></text>
			</staticText>
			<textField>
				<reportElement x="433" y="2" width="121" height="14" uuid="446aacd0-bb94-48fd-ab92-cbc6ebd383ef"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CPF}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="46" y="2" width="323" height="14" uuid="8b8e143b-1c60-47b7-afec-67ab21246ec4"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME}]]></textFieldExpression>
			</textField>
		</band>
		<band height="61">
			<printWhenExpression><![CDATA[$P{IMPENDERECO} == "S"]]></printWhenExpression>
			<staticText>
				<reportElement x="0" y="0" width="46" height="14" uuid="d0aaedf2-c057-4145-8afe-5f677c540eff">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Telefone:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="16" width="46" height="14" uuid="4fda6549-ab21-40e5-b1eb-d7585c3de95b">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Endereço:]]></text>
			</staticText>
			<textField>
				<reportElement x="46" y="16" width="323" height="14" uuid="1a5512cb-8694-422a-b92f-b12305685ae1"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RUA_RES}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="46" y="30" width="323" height="14" uuid="c06ba9a0-d652-464f-ae80-a662aa4864e6"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BAIRRO_RES}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="379" y="16" width="166" height="14" uuid="1f930240-689a-4854-9f67-b2341ff336eb"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{FACHADA_RES}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="46" y="44" width="193" height="14" uuid="886f1845-9021-4068-be16-5e43783499b7"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="240" y="44" width="129" height="14" uuid="f4178c18-**************-0bc18ee19192"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{UF}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="379" y="44" width="166" height="14" uuid="c94b38ca-3700-4ec2-b2cc-be8f07f15fb6"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CEP_RES}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="46" y="0" width="113" height="14" uuid="07238b36-5d40-42ff-aa7d-a1fb696a5f4f"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PREFIXO_RES}+"-"+$F{TELEFONE_RES}]]></textFieldExpression>
			</textField>
		</band>
		<band height="31">
			<subreport>
				<reportElement x="0" y="0" width="554" height="31" uuid="7b09fd7f-a899-43d0-aa8c-51253b90f71f"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REQUISICAO">
					<subreportParameterExpression><![CDATA[$F{REQUISICAO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="RECADO">
					<subreportParameterExpression><![CDATA[$P{RECADO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="IMPDEFEITO">
					<subreportParameterExpression><![CDATA[$P{IMPDEFEITO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="MOSTRAR">
					<subreportParameterExpression><![CDATA[$P{MOSTRAR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="IMPLOCACAO">
					<subreportParameterExpression><![CDATA[$P{IMPLOCACAO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ORDENAR">
					<subreportParameterExpression><![CDATA[$P{ORDENAR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"RequisicoesItens.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
