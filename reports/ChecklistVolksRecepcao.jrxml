<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ChecklistVolksRecepcao" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="TESTE_FREEDOM.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="padrao_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232561.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\34396\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\temp\\image_zk\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH qryOS AS
 (SELECT ROWNUM AS NUMERO_LINHA,
  OS.COD_EMPRESA,
         OS.NUMERO_OS,
         OS.TIPO,
         TO_DATE(TO_CHAR(OS.DATA_EMISSAO, 'DD-MM-YYYY') || ' ' || OS.HORA_EMISSAO, 'DD-MM-YYYY HH24:MI:SS') AS DATA_EMISSAO,
         OS.DATA_ENTREGA,
         V.PRISMA,
         C.NOME NOME_CLIENTE,
         NVL(NULLIF(CD.CGC, ''), CD.CPF) CNPJ_CPF,
         CD.Inscricao_Estadual  Inscricao_Estadual,
         CD.RG,
         NVL(C.RUA_RES, C.RUA_COM) RUA_RES,
         NVL(C.BAIRRO_RES, C.BAIRRO_COM) BAIRRO_RES,
         (SELECT DESCRICAO
            FROM CIDADES
           WHERE COD_CIDADES = NVL(C.COD_CID_RES, C.COD_CID_COM)
             AND ROWNUM = 1) AS CIDADE_RES,
         NVL(C.UF_RES, C.UF_COM) UF_RES,
         NVL(C.CEP_RES, C.CEP_COM) CEP_RES,
         C.ENDERECO_ELETRONICO,
         (NVL(C.PREFIXO_RES, C.PREFIXO_COM) || ' - ' ||
         NVL(C.TELEFONE_RES, C.TELEFONE_COM)) AS TELEFONE_RES,
         (C.PREFIXO_CEL || ' - ' || C.TELEFONE_CEL) AS TELEFONE_CEL,
         (C.PREFIXO_COM || ' - ' || C.TELEFONE_COM) AS TELEFONE_COM,
         NVL(NULLIF(C.RUA_COBRANCA, ''), C.RUA_RES) RUA_FAT,
         NVL(NULLIF(C.CEP_COBRANCA, ''), C.CEP_RES) CEP_FAT,
         NVL(NULLIF(C.BAIRRO_COBRANCA, ''), C.BAIRRO_RES) BAIRRO_FAT,
         NVL(CIDF.DESCRICAO, CIDR.DESCRICAO) CIDADE_FAT,
         NVL(CIDF.UF, CIDR.UF) UF_FAT,
         OS.OBSERVACAO,
         CASE
           WHEN OT.REVISAO_GRATUITA = 'S' THEN
            1 
           WHEN OT.INTERNO = 'S' THEN
            2 
           WHEN OT.GARANTIA = 'S' THEN
            3 
           WHEN OTE.FUNILARIA = 'S' THEN
            4 
           ELSE
            0 
         END TIPO_OS,
         CASE UPPER(FPG.TIPO_PGTO)
           WHEN 'Z' THEN
            1 
           WHEN 'H' THEN
            2 
           WHEN 'V' THEN
            3 
           WHEN 'M' THEN
            4 
           WHEN 'P' THEN
            4 
           ELSE
            0 
         END FORMA_PAGAMENTO,
         PM.DESCRICAO_MODELO,
         V.CHASSI,
         V.COR_EXTERNA,
         V.KM,
         V.DATA_VENDA,
         V.ANO,
         V.PLACA,
         CON.CODIGO_PADRAO COD_REVENDEDOR,
         CON.NOME NOME_REVENDEDOR,
         V.COMBUSTIVEL,
         NVL(OS.NOME, OS.CONSULTOR_RECEPCAO) AS CONSULTOR,
     OS_AGENDA.SIGNATURE AS OS_ASSINATURA,
     PRODUTOS.COD_SEGMENTO
    FROM OS,
     OS_AGENDA,
         OS_DADOS_VEICULOS V,
         OS_TIPOS          OT,
         OS_TIPOS_EMPRESAS OTE,
         OS_PAGAMENTO      OPG,
         FORMA_PGTO        FPG,
         CLIENTES          C,
         CLIENTE_DIVERSO   CD,
         CIDADES           CIDR,
         CIDADES           CIDF,
         PRODUTOS_MODELOS  PM,
         PRODUTOS,
         CONCESSIONARIAS   CON
   WHERE (OS.NUMERO_OS = V.NUMERO_OS AND OS.COD_EMPRESA = V. COD_EMPRESA)
   AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
     AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS (+)
     AND (OS.TIPO = OT.TIPO)
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND (OT.TIPO = OTE.TIPO AND OTE.COD_EMPRESA = OS.COD_EMPRESA)
     AND (OS.NUMERO_OS = OPG.NUMERO_OS(+) AND
         OS.COD_EMPRESA = OPG.COD_EMPRESA(+))
     AND (OPG.COD_FORMA_PGTO = FPG.COD_FORMA_PGTO(+))
     AND (OS.COD_CLIENTE = C.COD_CLIENTE)
     AND (C.COD_CLIENTE = CD.COD_CLIENTE)
     AND C.COD_CID_RES = CIDR.COD_CIDADES(+)
     AND C.COD_CID_COBRANCA = CIDF.COD_CIDADES(+)
        
     AND V.COD_PRODUTO = PM.COD_PRODUTO (+)
     AND V.COD_MODELO = PM.COD_MODELO (+)
     AND PM.COD_PRODUTO = PRODUTOS.COD_PRODUTO (+)
     AND V.COD_CONCESSIONARIA = CON.COD_CONCESSIONARIA
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}),

qryEmpresa AS
 (SELECT ROWNUM AS NUMERO_LINHA, E.NOME,
         E.RUA,
         E.BAIRRO,
         E.CIDADE,
         E.ESTADO,
         E.CEP,
         E.FONE,
         E.FAX,
         C.CODIGO_PADRAO CODIGO_REVENDEDOR
    FROM EMPRESAS E, PARM_SYS P, CONCESSIONARIAS C
   WHERE E.COD_EMPRESA = P.COD_EMPRESA
     AND P.CONCESSIONARIA_NUMERO = C.COD_CONCESSIONARIA(+)
     AND E.COD_EMPRESA = $P{COD_EMPRESA}),

Q_LOGO AS 
(
select OTE.logo as logo_por_tipo, E.logo as logo_empresa
FROM  EMPRESA_LOGO E, OS_TIPOS_EMPRESAS OTE, OS
 WHERE E.COD_EMPRESA (+) = OS.COD_EMPRESA
 AND OTE.TIPO (+) = OS.TIPO
 AND OTE.COD_EMPRESA(+) = OS.COD_EMPRESA
 AND OS.NUMERO_OS = $P{NUMERO_OS}
 AND OS.COD_EMPRESA = $P{COD_EMPRESA}
),
Q_ASSINATURA AS (
select 
ROWNUM AS NUMERO_LINHA,
ASSINATURA AS ASSINATURA_CONSULTOR,
ASSINATURA_CLIENTE
from mob_os_assinatura moa
where moa.aplicacao = 'R'
      and moa.numero_os = $P{NUMERO_OS}
      and moa.cod_empresa = $P{COD_EMPRESA}
)
SELECT 
qryempresa.NOME AS qryempresa_NOME,
qryempresa.RUA AS qryempresa_RUA,
qryos.NUMERO_OS AS qryos_NUMERO_OS,
qryempresa.CEP AS qryempresa_CEP,
qryempresa.CODIGO_REVENDEDOR AS qryempresa_CODIGO_REVENDEDOR,
qryempresa.FONE AS qryempresa_FONE,
qryempresa.BAIRRO AS qryempresa_BAIRRO,
qryos.PRISMA AS qryos_PRISMA,
qryos.DATA_EMISSAO AS qryos_DATA_EMISSAO,
qryos.DATA_ENTREGA as qryos_DATA_ENTREGA,
qryempresa.CIDADE AS qryempresa_CIDADE,
qryempresa.FAX AS qryempresa_FAX,
qryos.COR_EXTERNA AS qryos_COR_EXTERNA,
qryos.CHASSI AS qryos_CHASSI,
qryos.DESCRICAO_MODELO AS qryos_DESCRICAO_MODELO,
qryos.DATA_VENDA AS qryos_DATA_VENDA,
qryos.KM AS qryos_KM,
qryos.OBSERVACAO AS qryos_OBSERVACAO,
qryos.ANO AS qryos_ANO,
qryos.PLACA AS qryos_PLACA,
qryos.COD_REVENDEDOR AS qryos_COD_REVENDEDOR,
qryos.NOME_REVENDEDOR AS qryos_NOME_REVENDEDOR,
qryos.NOME_CLIENTE AS qryos_NOME_CLIENTE,
qryos.RUA_RES AS qryos_RUA_RES,
qryos.BAIRRO_RES AS qryos_BAIRRO_RES,
qryos.RG AS qryos_RG,
qryos.CNPJ_CPF AS qryos_CNPJ_CPF,
qryos.CIDADE_RES AS qryos_CIDADE_RES,
qryos.TELEFONE_COM AS qryos_TELEFONE_COM,
qryos.ENDERECO_ELETRONICO AS qryos_ENDERECO_ELETRONICO,
qryos.CEP_RES AS qryos_CEP_RES,
qryos.UF_RES AS qryos_UF_RES,
qryos.TELEFONE_CEL AS qryos_TELEFONE_CEL,
qryos.TELEFONE_RES AS qryos_TELEFONE_RES,
qryos.BAIRRO_FAT AS qryos_BAIRRO_FAT,
COALESCE (qryos.TELEFONE_COM,qryos.TELEFONE_RES,qryos.TELEFONE_CEL) AS qryos_TELEFONE_CLIENTE,
qryos.CEP_FAT AS qryos_CEP_FAT,
qryos.UF_FAT AS qryos_UF_FAT,
qryos.CIDADE_FAT AS qryos_CIDADE_FAT,
qryos.RUA_FAT AS qryos_RUA_FAT,
qryos.TIPO_OS AS qryos_TIPO_OS,
qryos.COMBUSTIVEL AS qryos_COMBUSTIVEL,
qryos.FORMA_PAGAMENTO as qryos_FORMA_PAGAMENTO,
NVL(Q_LOGO.logo_por_tipo, Q_LOGO.logo_empresa) as Q_LOGO_LOGO,
qryos.OS_ASSINATURA as qruos_OS_ASSINATURA,
qryos.Inscricao_Estadual as qryos_Inscricao_Estadual,
qryos.cod_segmento as qryos_cod_segmento,
Q_ASSINATURA.ASSINATURA_CONSULTOR AS Q_ASSINA_ASSINATURA_CONSULTOR,
Q_ASSINATURA.ASSINATURA_CLIENTE AS Q_ASSINA_ASSINATURA_CLIENTE
FROM qryOS, qryEmpresa, Q_LOGO, Q_ASSINATURA
WHERE qryOS.NUMERO_LINHA = qryEmpresa.NUMERO_LINHA (+)
		      and qryOS.NUMERO_LINHA = Q_ASSINATURA.NUMERO_LINHA (+)]]>
	</queryString>
	<field name="QRYEMPRESA_NOME" class="java.lang.String"/>
	<field name="QRYEMPRESA_RUA" class="java.lang.String"/>
	<field name="QRYOS_NUMERO_OS" class="java.lang.Double"/>
	<field name="QRYEMPRESA_CEP" class="java.lang.String"/>
	<field name="QRYEMPRESA_CODIGO_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYEMPRESA_FONE" class="java.lang.String"/>
	<field name="QRYEMPRESA_BAIRRO" class="java.lang.String"/>
	<field name="QRYOS_PRISMA" class="java.lang.String"/>
	<field name="QRYOS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="QRYOS_DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="QRYEMPRESA_CIDADE" class="java.lang.String"/>
	<field name="QRYEMPRESA_FAX" class="java.lang.String"/>
	<field name="QRYOS_COR_EXTERNA" class="java.lang.String"/>
	<field name="QRYOS_CHASSI" class="java.lang.String"/>
	<field name="QRYOS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="QRYOS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="QRYOS_KM" class="java.lang.Double"/>
	<field name="QRYOS_OBSERVACAO" class="java.lang.String"/>
	<field name="QRYOS_ANO" class="java.lang.String"/>
	<field name="QRYOS_PLACA" class="java.lang.String"/>
	<field name="QRYOS_COD_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_CLIENTE" class="java.lang.String"/>
	<field name="QRYOS_RUA_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_RES" class="java.lang.String"/>
	<field name="QRYOS_RG" class="java.lang.String"/>
	<field name="QRYOS_CNPJ_CPF" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_COM" class="java.lang.String"/>
	<field name="QRYOS_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="QRYOS_CEP_RES" class="java.lang.String"/>
	<field name="QRYOS_UF_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_CEL" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_FAT" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_CLIENTE" class="java.lang.String"/>
	<field name="QRYOS_CEP_FAT" class="java.lang.String"/>
	<field name="QRYOS_UF_FAT" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_FAT" class="java.lang.String"/>
	<field name="QRYOS_RUA_FAT" class="java.lang.String"/>
	<field name="QRYOS_TIPO_OS" class="java.lang.Double"/>
	<field name="QRYOS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="QRYOS_FORMA_PAGAMENTO" class="java.lang.Double"/>
	<field name="Q_LOGO_LOGO" class="java.awt.Image"/>
	<field name="QRUOS_OS_ASSINATURA" class="java.awt.Image"/>
	<field name="QRYOS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="QRYOS_COD_SEGMENTO" class="java.lang.Double"/>
	<field name="Q_ASSINA_ASSINATURA_CONSULTOR" class="java.awt.Image"/>
	<field name="Q_ASSINA_ASSINATURA_CLIENTE" class="java.awt.Image"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="100">
			<frame>
				<reportElement x="0" y="0" width="555" height="100" uuid="6f2fcc26-e67e-45c5-b758-76952e5a853c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="458" y="2" width="84" height="41" uuid="0e850d14-29bd-45ff-a763-a5fe280ca51d">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{Q_LOGO_LOGO}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="0" y="47" width="221" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="3937cb65-d111-4959-bebd-1e59696ef192">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[MODELO:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="44" y="47" width="174" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="31e06dcd-52e7-43f5-8c74-00b4a11e33cb">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DESCRICAO_MODELO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="225" y="4" width="147" height="30" forecolor="#000000" uuid="d0ebd415-31a6-4a16-b5a8-7c49413cc960">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="21" isBold="true"/>
					</textElement>
					<text><![CDATA[Check List]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="64" y="5" width="35" height="30" uuid="42b3848e-4422-49f8-9640-108fd48b3d60">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} +"crmservice498015.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="0" y="61" width="131" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="60f4fa8f-9039-49a4-a849-0f823e1525a6">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[PLACA:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="35" y="61" width="92" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="5f2fc356-acc1-4767-9799-09aa2c8425ae">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_PLACA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="221" y="47" width="221" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="63a364ad-8b16-48f4-b5f7-2a6e2853f929">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CT:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="242" y="47" width="196" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="c7ad1800-d02d-4283-9e8e-76c18f76129e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NOME_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="442" y="47" width="113" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="f07d7614-88ff-433c-a235-628a116b956f">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[OS.:]]></text>
				</staticText>
				<textField pattern="###0.###;(###0.###-)">
					<reportElement mode="Transparent" x="466" y="47" width="86" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="f6843834-826f-4d5a-a377-362daefb1e4c">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="131" y="61" width="90" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="cabd1003-c33e-4beb-83d8-59020542aa9f">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[KM:]]></text>
				</staticText>
				<textField pattern="#,###.###;(#,###.###-)">
					<reportElement mode="Transparent" x="152" y="61" width="66" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="fa2605c7-7876-4b02-87b0-65a9c6b25499">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_KM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="221" y="61" width="131" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="4e3f31d3-d92f-43da-9ec6-3467c53b74c8">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[PRISMA:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="269" y="61" width="79" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="f0aa20d5-77c5-4f53-9660-34c7c7d81cd7">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_PRISMA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="352" y="61" width="90" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="63034ec2-693f-4c3d-87f6-dc3068905eeb">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[COR:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="442" y="61" width="113" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="875abe9d-8e37-4b61-8916-959cb53cf68d">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="75" width="131" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="1496da46-c16f-48bb-9cb5-09214aecbfcc">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[ENTRADA/DATA:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="131" y="75" width="90" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="9a178c49-a30d-4a83-a308-640a3cfb3d25">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[HORA:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="221" y="75" width="131" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="c89a1eb0-7173-4549-9a12-91af1b197478">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[SAÍDA/DATA:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="352" y="75" width="90" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="d8874c9d-e870-41d4-81d9-4c2a5616ba2d">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[HORA:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="442" y="75" width="113" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="09cdc1fe-3afd-48aa-9386-219adf34468c">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="442" y="61" width="38" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="73251375-72c6-493e-8468-f0a5bf9c1a14">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[ESTAÇÃO
RÁDIO:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="381" y="61" width="57" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="a1f2a7f9-410d-4730-b156-2728c56f65d9">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="386" y="75" width="52" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="c43093ec-53f5-4b98-965c-773daa093095">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="480" y="61" width="72" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="b5f9d477-5f3c-479d-b92c-6efbacd9d750">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["desligado"]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="279" y="75" width="69" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="5936b9b8-2a77-4001-a931-421553d70e1e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="166" y="75" width="52" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="9d695a07-3501-4cbc-8ebf-178abfd901a1">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="62" y="75" width="65" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="8e54a63b-e629-4eef-9406-c27e6ad2b387">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="162">
			<subreport overflowType="NoStretch">
				<reportElement x="0" y="0" width="251" height="142" uuid="06589049-9a6a-42b6-8c99-6ea0bcc79183">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "ChecklistVolksRecepcaoSubCarroceria.jasper"]]></subreportExpression>
			</subreport>
			<subreport overflowType="NoStretch">
				<reportElement x="251" y="0" width="152" height="62" uuid="4efc5882-d2cc-4396-a0b2-c9614c041608"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListVolksRecepacaoSubPneusEstepe.jasper"]]></subreportExpression>
			</subreport>
			<subreport overflowType="NoStretch">
				<reportElement x="251" y="62" width="304" height="80" uuid="e9dae51f-12de-42e6-8ee4-385bfc5cd16c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListVolksRecepacaoSubPertences.jasper"]]></subreportExpression>
			</subreport>
			<subreport overflowType="NoStretch">
				<reportElement x="0" y="142" width="555" height="10" uuid="2afd04f7-54a6-4061-abaa-c3d6aab8b167"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListVolksRecepacaoSubVerificacaoVeiculo.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement x="403" y="0" width="152" height="62" uuid="cbf5ddb2-be5d-418b-b4f6-42c96210f0f0"/>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="152" height="16" forecolor="#000000" backcolor="#E3DCDC" uuid="e2ab4e06-edd4-4231-ab9e-228d558a2a72">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[COMBUSTÍVEL]]></text>
				</staticText>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="35" y="19" width="82" height="40" uuid="a00c963b-8b94-4308-a83d-9d21cf297b81"/>
					<imageExpression><![CDATA[$F{QRYOS_COMBUSTIVEL}  < 19 ? $P{DIR_IMAGE_LOGO} + "crmservice519017.png" :
$F{QRYOS_COMBUSTIVEL}  < 39 ? $P{DIR_IMAGE_LOGO} + "crmservice519018.png" :
$F{QRYOS_COMBUSTIVEL}  < 59 ? $P{DIR_IMAGE_LOGO} + "crmservice519019.png"  :
$F{QRYOS_COMBUSTIVEL}  < 79 ? $P{DIR_IMAGE_LOGO} + "crmservice519020.png" :
$P{DIR_IMAGE_LOGO} + "crmservice519021.png"]]></imageExpression>
				</image>
			</frame>
		</band>
		<band height="30">
			<subreport overflowType="Stretch">
				<reportElement x="0" y="0" width="555" height="30" uuid="53e38ee7-9ab9-4b67-915a-e5ea7e26ff5e"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListVolksRecepacaoSubItens.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="209">
			<frame>
				<reportElement x="0" y="0" width="555" height="70" uuid="967f6457-2c9a-480c-8054-3bf42f72b7a1">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="221" height="18" forecolor="#000000" backcolor="#E3DCDC" uuid="5e01c9d0-2563-4087-b93c-b6e41541295f">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[DE ACORDO COM A VISTORIA]]></text>
				</staticText>
				<image scaleImage="RealHeight" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="422" y="13" width="124" height="26" uuid="34d3b87f-303a-4e8f-b85a-1d3345ffe5f7"/>
					<imageExpression><![CDATA[$F{Q_ASSINA_ASSINATURA_CLIENTE}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="406" y="41" width="149" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="a99cac2d-f849-487d-bb0c-714956b0f1bd">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[ASSINATURA DO CLIENTE]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="224" y="41" width="171" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="3a619e5d-c429-4635-a83f-324d486384bb">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[ASSINATURA DO CONSULTOR]]></text>
				</staticText>
				<frame>
					<reportElement mode="Opaque" x="0" y="19" width="221" height="18" backcolor="#000000" uuid="a5dcbd84-2b18-4eb5-9bc3-86bd857d8700"/>
					<staticText>
						<reportElement mode="Transparent" x="13" y="4" width="74" height="10" forecolor="#FFFFFF" backcolor="#E3DCDC" uuid="bd1785ee-2bb2-4ec1-b425-b75a15de2126">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box leftPadding="5" rightPadding="4">
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[COM PERTENCES]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="4" y="4" width="9" height="10" forecolor="#000000" uuid="f8d1c670-06db-4855-8b07-ae392a02efca">
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7"/>
						</textElement>
						<text><![CDATA[X]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="131" y="4" width="74" height="10" forecolor="#FFFFFF" backcolor="#E3DCDC" uuid="129f9f4c-7dc6-4db5-b709-4a126b9d46a0">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box leftPadding="5" rightPadding="4">
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[SEM PERTENCES]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="122" y="4" width="9" height="10" forecolor="#000000" uuid="707c3c12-1dc5-4443-8e4e-f1cb548663fd">
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7"/>
						</textElement>
						<text><![CDATA[]]></text>
					</staticText>
				</frame>
				<image scaleImage="RealHeight" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="249" y="13" width="124" height="26" uuid="fc339aa6-c88c-4e88-9fa4-f7c57a1a6f16"/>
					<imageExpression><![CDATA[$F{Q_ASSINA_ASSINATURA_CONSULTOR}]]></imageExpression>
				</image>
				<line>
					<reportElement x="0" y="64" width="555" height="1" uuid="fca80a4a-1edb-4832-b1dd-668f4631071b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineStyle="Dashed"/>
					</graphicElement>
				</line>
			</frame>
			<frame>
				<reportElement x="0" y="73" width="555" height="134" uuid="52df510d-504c-427b-8217-90247e2c59aa"/>
				<staticText>
					<reportElement mode="Transparent" x="0" y="61" width="103" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="a739a7fc-9be4-4cb1-a934-caf7f39cd321">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[PLACA:]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="63" y="12" width="35" height="30" uuid="26c2a279-32b1-467f-98e5-5692067eea41">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} +"crmservice498015.png"]]></imageExpression>
				</image>
				<textField>
					<reportElement mode="Transparent" x="33" y="61" width="70" height="10" forecolor="#000000" uuid="e1c735f4-9521-4b00-811d-256cf5dde2df">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_PLACA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="110" y="10" width="314" height="18" forecolor="#000000" backcolor="#E3DCDC" uuid="e435ffd1-96ff-433f-8368-c0172b7b9734">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[COMPROVANTE DE RECEBIMENTO DO VEÍCULO]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="448" y="5" width="84" height="41" uuid="3f4ac4c3-37f8-40a6-8a77-47ad53387156">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{Q_LOGO_LOGO}]]></imageExpression>
				</image>
				<frame>
					<reportElement mode="Transparent" x="157" y="30" width="221" height="18" backcolor="#000000" uuid="52578495-b653-46f3-89c5-12c7a5736fbc"/>
					<staticText>
						<reportElement mode="Transparent" x="13" y="4" width="74" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="94360763-f0ed-4f30-ba2c-3ab296d9ef9c">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box leftPadding="5" rightPadding="4">
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[COM PERTENCES]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="4" y="4" width="9" height="10" forecolor="#000000" uuid="027bf7c7-d49a-4e79-ab82-27ef12526a1b">
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.75"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7"/>
						</textElement>
						<text><![CDATA[X]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="131" y="4" width="74" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="b4e84c6c-b979-4834-ba25-ebf842d4f883">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box leftPadding="5" rightPadding="4">
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[SEM PERTENCES]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="122" y="4" width="9" height="10" forecolor="#000000" uuid="dd96b864-655e-4c0a-a5e3-d69b29f2d661">
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.75"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7"/>
						</textElement>
						<text><![CDATA[]]></text>
					</staticText>
				</frame>
				<staticText>
					<reportElement mode="Transparent" x="103" y="61" width="103" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="a6d990b7-99d9-44c6-9c80-53155180a813">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[MODELO:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="142" y="61" width="63" height="10" forecolor="#000000" uuid="80a2c8a2-d899-4865-812e-a448a371a1d3">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DESCRICAO_MODELO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="206" y="61" width="98" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="4995fa35-b5d3-4ead-aa43-64f29b77cfe4">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[COR:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="229" y="61" width="73" height="10" forecolor="#000000" uuid="a6b0d9d8-7591-4289-80da-d9d52bec2457">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="304" y="61" width="124" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="7a3808c7-86f5-400c-b8d5-b72f0995044a">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[OS:]]></text>
				</staticText>
				<textField pattern="###0.###;(###0.###-)">
					<reportElement mode="Transparent" x="324" y="61" width="103" height="10" forecolor="#000000" uuid="bb20ee5b-dab1-4d94-affc-775a80918e2d">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="428" y="61" width="127" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="6424858a-431c-48ec-ba77-f3d31551c657">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[PRISMA:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="464" y="61" width="86" height="10" forecolor="#000000" uuid="0f500d48-5fd8-4b7e-8754-c8ba8adc6ac4">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_PRISMA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="71" width="152" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="739e1f24-7549-445a-ac8d-6c5a2c038582">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[ENTRADA/DATA:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="62" y="71" width="88" height="10" forecolor="#000000" uuid="b1193722-e1d4-424b-8dc3-70e32de63e0f">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="81" width="152" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="21a66fff-59a5-486e-9f43-18c2693ed764">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[SAÍDA/DATA:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="62" y="81" width="88" height="10" forecolor="#000000" uuid="e912b51f-e0c6-446d-a11b-3f22698c9eb8">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_ENTREGA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="91" width="304" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="737440b7-1ca1-4e26-8007-ed99e4dfa8cd">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[FONE:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="28" y="91" width="274" height="10" forecolor="#000000" uuid="645cc195-1427-4763-8e4f-ce9bd0b645a7">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_FONE}]]></textFieldExpression>
				</textField>
				<image scaleImage="RealHeight" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="367" y="82" width="124" height="26" uuid="9995cbd3-9625-4ade-aed9-3ec4b6a2d568"/>
					<imageExpression><![CDATA[$F{Q_ASSINA_ASSINATURA_CONSULTOR}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="312" y="110" width="234" height="14" forecolor="#000000" backcolor="#E3DCDC" uuid="1234fdb2-2b01-4070-8460-4df822319485">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[ASSINATURA DO CONSULTOR]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="152" y="71" width="152" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="4f6b30ab-966d-486f-9d84-6e9943d7d495">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[HORA:]]></text>
				</staticText>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="180" y="71" width="122" height="10" forecolor="#000000" uuid="60577dbe-25f7-4ba5-afb9-cabc7c52512b">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="152" y="81" width="152" height="10" forecolor="#000000" backcolor="#E3DCDC" uuid="4f8bbbed-9544-4b57-b800-76b2600d30a5">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="5" rightPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[HORA:]]></text>
				</staticText>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="180" y="81" width="122" height="10" forecolor="#000000" uuid="ad29ccce-44a5-46d2-95d8-4efa612286a0">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_ENTREGA}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
