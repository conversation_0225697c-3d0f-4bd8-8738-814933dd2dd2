<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PreOrdemPadraoSubServicos" pageWidth="555" pageHeight="842" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="campoNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_OS_AGENDA" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT 
A.ITEM, A.COD_SERVICO, S.DESCRICAO_SERVICO, 
NVL(A.PRECO_VENDA, 0.0) AS PRECO_VENDA,
OTE.TIPO AS TIPO_OS, 
OTE.TIPO_FABRICA AS TIPOSERVICO, 
NVL(A.VALOR_DESCONTO_SERV,0) AS VALOR_DESCONTO_SERV,
A.TEMPO_PADRAO,
NVL(OT.GARANTIA,'N') AS GARANTIA,
ST.NOME      AS PRODUTIVO_PRISMA,
CAMP.COD_CAMPANHA,
CAMP.ASSUNTO AS ASSUNTO_CAMPANHA,
CASE NVL(CAMP.EH_RECALL,'N') 
  WHEN 'S' THEN 'SIM'
  ELSE 'NÃO' 
 END EH_RECALL 
FROM OS_AGENDA_SERVICOS   A,
       OS_AGENDA            OA,      
       OS_AGENDA_RECLAMACAO OAR,
       SERVICOS             S,       
       OS_TIPOS_EMPRESAS    OTE,
       OS_TIPOS             OT,
       PRISMA_BOX           PB,
       SERVICOS_TECNICOS    ST,
       OS_CAMPANHA          CAMP
 WHERE OA.COD_EMPRESA = OAR.COD_EMPRESA
   AND OA.COD_OS_AGENDA = OAR.COD_OS_AGENDA   
   AND OA.COD_EMPRESA = A.COD_EMPRESA
   AND OA.COD_OS_AGENDA = A.COD_OS_AGENDA      
   AND OAR.COD_EMPRESA = A.COD_EMPRESA
   AND OAR.COD_OS_AGENDA = A.COD_OS_AGENDA   
   AND OAR.ITEM = A.ITEM   
   AND S.COD_SERVICO = A.COD_SERVICO
   AND OTE.TIPO (+)= OAR.COD_TIPO_OS  
   AND OT.TIPO         (+) = OTE.TIPO
   AND OTE.COD_EMPRESA (+)= OAR.COD_EMPRESA      
   AND A.COD_EMPRESA = $P{COD_EMPRESA}
   AND A.COD_OS_AGENDA = $P{COD_OS_AGENDA}
   AND PB.PRISMA (+) = A.PRISMA
   AND ST.COD_EMPRESA (+) = PB.COD_EMPRESA_FILTRO
   AND ST.COD_TECNICO (+) = PB.COD_TECNICO
    AND A.COD_CAMPANHA = CAMP.COD_CAMPANHA(+)
    ORDER BY A.ITEM]]>
	</queryString>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="COD_SERVICO" class="java.lang.String"/>
	<field name="DESCRICAO_SERVICO" class="java.lang.String"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="TIPO_OS" class="java.lang.String"/>
	<field name="TIPOSERVICO" class="java.lang.String"/>
	<field name="VALOR_DESCONTO_SERV" class="java.lang.Double"/>
	<field name="TEMPO_PADRAO" class="java.lang.Double"/>
	<field name="GARANTIA" class="java.lang.String"/>
	<field name="PRODUTIVO_PRISMA" class="java.lang.String"/>
	<field name="COD_CAMPANHA" class="java.lang.String"/>
	<field name="ASSUNTO_CAMPANHA" class="java.lang.String"/>
	<field name="EH_RECALL" class="java.lang.String"/>
	<variable name="TOTAL_SERVICOS" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{PRECO_VENDA}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="TOTAL_DESCONTO_SERVICOS" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{VALOR_DESCONTO_SERV}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="11">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b9e3f3a7-19cd-45e8-8ed6-c1a67586ea41">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[SERVIÇO (Mão de obra)]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="11">
			<staticText>
				<reportElement mode="Opaque" x="40" y="0" width="50" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="73ed2c61-dc67-4714-a8b8-a82e6af9e585">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo de OS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="90" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="effeae3b-1314-4d08-8cbb-1e5c9315f81c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Produtivo]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="270" y="0" width="202" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="deba69b2-afd3-469a-b2f5-8993ff35bf54">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição da Solicitação]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="d64b78a2-04b6-4573-86a6-8f71eec424e3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Item]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="210" y="0" width="60" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="94f31bf4-ce70-427c-ac01-38589ab2bc9f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Código de Operação]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="472" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="5e43e9f7-c5d7-4d99-9431-9fe61b1800a3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="147" y="0" width="63" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="56614557-fe63-434c-935c-0e0cf39e2252">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tempo]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="270" y="0" width="202" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1e6da218-bcea-4afc-9ceb-819528eb6b97">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_SERVICO}]]></textFieldExpression>
			</textField>
			<textField pattern="#0.###;(#0.###-)">
				<reportElement mode="Opaque" x="0" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="751d6850-6609-42fb-86cd-094b1b6ed143">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="40" y="0" width="50" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="dea6bb9a-743a-4f23-83e4-c5a7e4d02cd1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIPO_OS}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="90" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1c5947c7-a166-49dc-a232-9dc87e561229">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRODUTIVO_PRISMA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="210" y="0" width="60" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="680366eb-6eb5-4d9a-9a1b-3de96721461d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_SERVICO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,#00.00#;#,#00.00#-" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="472" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b358acf2-675f-434e-a45f-d640b2aa074e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="474" y="1" width="9" height="9" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="rgba(0, 0, 0, 0.5882353)" uuid="cc9bc803-8d20-4690-8e68-0fad2b06b2ed">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="7" isBold="false"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
			<textField>
				<reportElement mode="Opaque" x="147" y="0" width="63" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="811eae87-2df4-4bc2-b5f7-4e07c91fca85">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
			</textField>
		</band>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$F{COD_CAMPANHA} != null]]></printWhenExpression>
			<staticText>
				<reportElement stretchType="ElementGroupBottom" mode="Opaque" x="0" y="0" width="90" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b81bc335-3020-40ce-a1c0-117b41dfc301">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Código Campanha]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="210" y="0" width="60" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="a158c02b-03b1-4562-bfaa-674b9af63962">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Eh Recall:]]></text>
			</staticText>
			<textField pattern="#0.###;(#0.###-)">
				<reportElement mode="Opaque" x="270" y="0" width="285" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="bd1049a6-3e9e-4d7a-b150-2cee42b1e1b2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{EH_RECALL}]]></textFieldExpression>
			</textField>
			<textField pattern="#0.###;(#0.###-)">
				<reportElement mode="Opaque" x="90" y="0" width="120" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="a7646204-e79b-42e5-bcb4-7299f989492e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_CAMPANHA}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="12">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="472" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="a554598d-5c69-4ed1-b146-1e8f2e36db43">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Estimado de Serviços]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="474" y="1" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="ff1ddad6-a24f-4679-b5f1-b3ef48a50f25">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
			<textField pattern="#,##0.00#;(#,##0.00#-)">
				<reportElement mode="Opaque" x="472" y="0" width="83" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="79d78487-afd3-4333-8b36-a2c44c9b2f33">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{TOTAL_SERVICOS}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="474" y="1" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="rgba(0, 0, 0, 0.98039216)" uuid="065b296b-2caa-4759-89cc-42e88bf26353">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="7" isBold="false"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
