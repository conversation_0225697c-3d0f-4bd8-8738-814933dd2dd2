<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListCheryEntradaSaidaSubStatusAtualVeiculo" columnCount="2" pageWidth="365" pageHeight="28" columnWidth="182" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select cod_empresa, nome from empresas e where e.cod_empresa in (1,2,3)]]>
		</queryString>
		<field name="COD_EMPRESA" class="java.lang.Double"/>
		<field name="NOME" class="java.lang.String"/>
	</subDataset>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[select a.item_descricao, a.selecionado_observacao as observacao
from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
where a.id_checklist = 50000
      		and a.id_grupo = 50000]]>
	</queryString>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="14" splitType="Immediate">
			<frame>
				<reportElement x="0" y="0" width="182" height="14" uuid="98e3a3fc-a687-48e5-a433-1e53d3afce84">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textField>
					<reportElement style="field_null" mode="Transparent" x="2" y="0" width="180" height="14" forecolor="#000000" uuid="88f8c2b6-6cac-406c-843b-fc62406fd9d3">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OBSERVACAO}==null ? $F{ITEM_DESCRICAO} + ": " : $F{ITEM_DESCRICAO} + ": " + $F{OBSERVACAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
