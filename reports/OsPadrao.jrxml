<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsPadrao" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a5d195c3-46e2-4e2e-9575-573e21d1a687">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="true"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="715"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="271"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[11334.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\34944\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="MOSTRAR_SUB_DIAGNOSTICO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<parameter name="ASSINAR_DIGITALMENTE" class="java.lang.String">
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT OS.COD_EMPRESA,
       OS.STATUS_OS,
       OS.COD_DOCUMENTO,
       CASE
         WHEN OS.NUMERO_OS < 0 THEN
          'Orçamento'
         ELSE
          'O.S.'
       END TIPO_DOC,
       OS.NUMERO_OS,
       ABS(OS.NUMERO_OS) AS ABS_OSNUM,
       ABS(NVL(OS.NUMERO_OS_FABRICA,OS.NUMERO_OS)) AS NUMERO_OS_UNICA,
       OS.COD_CLIENTE,
       OS.COD_PRODUTO,
       OS.COD_MODELO,
       OS.TIPO_ENDERECO,
       TO_CHAR(TO_DATE(OS.FRANQUIA,'DD/MM/RRRR'), 'DD/MM/RRRR') FRANQUIA,
       OS.TIPO,
       OS_TIPOS.TIPO_FABRICA,
       OS.VALIDADE,
       OS.OBSERVACAO,
       OS.ORCAMENTO,
       OS.EXTENDIDA,
       OS.SEGURADORA,
       OS.LIBERADO,
       TO_CHAR(TO_DATE(OS.DATA_EMISSAO,'DD/MM/RRRR'), 'DD/MM/RRRR') DATA_EMISSAO,
       OS.HORA_EMISSAO,
       Case
         When (Not OS.DATA_EMISSAO Is Null) And (Not OS.HORA_EMISSAO Is Null) Then
           TO_CHAR(TO_DATE(OS.DATA_EMISSAO,'DD/MM/RRRR'), 'DD/MM/RRRR') || ' ' || OS.HORA_EMISSAO
         When (Not OS.DATA_EMISSAO Is Null) Then
           TO_CHAR(TO_DATE(OS.DATA_EMISSAO,'DD/MM/RRRR'), 'DD/MM/RRRR')
         Else
           ' '
       End DATA_HORA_EMISSAO, 
       OS.DATA_ENTREGA AS DATA_ENTREGA,
       Case
         When (Not OS.DATA_ENTREGA Is Null) Then
           TO_CHAR(TO_DATE(OS.DATA_ENTREGA,'DD/MM/RRRR'), 'DD/MM/RRRR') || ' ' || TO_CHAR(OS.DATA_ENTREGA, 'HH24:MI')
         Else
           ' '
       End DATA_HORA_ENTREGA, 
       NVL(OS.ENTREGA_CLIENTE,'N') AS ENTREGA_CLIENTE,
       OS.HORA_ENCERRADA,
       TO_CHAR(TO_DATE(OS.DATA_ENCERRADA,'DD/MM/RRRR'), 'DD/MM/RRRR') DATA_ENCERRADA,
       OS.HORA_LIBERADO,
       TO_CHAR(TO_DATE(OS.DATA_LIBERADO,'DD/MM/RRRR'), 'DD/MM/RRRR') DATA_LIBERADO,
       NVL(Decode(OS.HORA_PROMETIDA, '00:00', null, OS.HORA_PROMETIDA), ' ') HORA_PROMETIDA,
       TO_CHAR(TO_DATE(OS.DATA_PROMETIDA,'DD/MM/RRRR'), 'DD/MM/RRRR') DATA_PROMETIDA,
       OS.PRIMEIRA_LIBERACAO,
       OS.VALOR_SERVICOS_BRUTO,
       OS.VALOR_ITENS_BRUTO,
       OS.DESCONTOS_SERVICOS,
       OS.DESCONTOS_ITENS,
       NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL) AS INSCRICAO_ESTADUAL,
       (OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) TOTAL_OS_SERVICOS,
       (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS) TOTAL_OS_ITENS,
       (OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO) TOTAL_OS_BRUTO,
       (OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS) TOTAL_OS_DESCONTO,
       ((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) +
       (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS)) TOTAL_OS,
       OS.ORC_SERV_BRUTO,
       OS.ORC_ITEM_BRUTO,
       OS.ORC_SERV_DESCONTO,
       OS.ORC_ITEM_DESCONTO,
       (OS.ORC_SERV_BRUTO - OS.ORC_SERV_DESCONTO) TOTAL_ORC_SERVICOS,
       (OS.ORC_ITEM_BRUTO - OS.ORC_ITEM_DESCONTO) TOTAL_ORC_ITENS,
       (OS.ORC_SERV_BRUTO + OS.ORC_ITEM_BRUTO) TOTAL_ORC_BRUTO,
       (OS.ORC_SERV_DESCONTO + OS.ORC_ITEM_DESCONTO) TOTAL_ORC_DESCONTO,
       ((OS.ORC_SERV_BRUTO - OS.ORC_SERV_DESCONTO) +
       (OS.ORC_ITEM_BRUTO - OS.ORC_ITEM_DESCONTO)) TOTAL_ORC,
       OS.KM_INICIAL,
       OS.KM_FINAL,
       OS.DESLOCAMENTO,
       OS.COD_SEGURADORA,
       OS_DADOS_VEICULOS.ANO,
       OS_DADOS_VEICULOS.HORIMETRO,
       OS_DADOS_VEICULOS.PRISMA,
       TO_CHAR(TO_DATE(OS_DADOS_VEICULOS.DATA_VENDA,'DD/MM/RRRR'), 'DD/MM/RRRR') DATA_VENDA,
       OS_DADOS_VEICULOS.COMBUSTIVEL,
       OS_DADOS_VEICULOS.COR_EXTERNA,
       OS_DADOS_VEICULOS.PLACA,
       OS_DADOS_VEICULOS.NUMERO_RENAVAM,
       OS_DADOS_VEICULOS.KM,
       OS_DADOS_VEICULOS.CHASSI,
       0 AS DESCONTOS_ICMS,
       0 AS DESCONTOS_ISS,
       0 AS DESCONTOS_GARANTIA,
       OS_DADOS_VEICULOS.NUMERO_MOTOR,
       OS_DADOS_VEICULOS.NUMERO_CAMBIO,
       NVL(OS_DADOS_VEICULOS.SERIE, ' ') SERIE,
       OS_DADOS_VEICULOS.BLINDADO,
       OS_DADOS_VEICULOS.NUMERO_DIFERENCIAL,
        NVL(OS_TIPOS_EMPRESAS.IMPRIMIR_QUEM_RETIROU,'N') IMPRIMIR_QUEM_RETIROU,
    MOTORISTAS.CODIGO_MOTORISTA MOTORISTA_CODIGO,
        NVL(MOTORISTAS.NOME_DO_MOTORISTA, ' ') MOTORISTA_NOME,
        NVL(MOTORISTAS.DOCUMENTO, ' ') MOTORISTA_DOCUMENTO,
        NVL (regexp_replace(LPAD(MOTORISTAS.CPF, 11),'([0-9]{3})([0-9]{3})([0-9]{3})','\1.\2.\3-'), ' ') MOTORISTA_CPF,
      (MOTORISTAS.DDD || '-' || MOTORISTAS.TELEFONE) MOTORISTA_TELEFONE,
       PKG_CRM_SERVICE_UTIL.GET_TIPO_OS(OS.COD_EMPRESA, OS.TIPO, NULL) TIPO_DESCRICAO,
       OS_TIPOS.TIPO_FABRICA || ' - ' || OS_TIPOS.DESCRICAO TIPO_DESCRICAO_FAB,
       NVL(OS_TIPOS_EMPRESAS.COD_CLIENTE, OS_TIPOS.COD_CLIENTE) AS TIPO_COD_CLIENTE,
       OS_TIPOS_EMPRESAS.COD_IMAGEM_OS COD_IMG_TP,
       OS_TIPOS.GARANTIA,
       OS_TIPOS.INTERNO,
       EMPRESAS_USUARIOS.NOME_COMPLETO,
       EMPRESAS_USUARIOS.CODIGO_OPCIONAL,
       PRODUTOS.DESCRICAO_PRODUTO || ' / ' ||
       PRODUTOS_MODELOS.DESCRICAO_MODELO DESC_PROD_MOD,
       PRODUTOS_MODELOS.LINHA,
       PRODUTOS_MODELOS.COD_IMAGEM_OS COD_IMG_MOD,
       EMPRESAS.NOME NOME_EMPRESA,
       EMPRESAS.CGC EMPRESA_CGC,
       EMPRESAS.FACHADA EMPRESA_FACHADA,
       EMPRESAS.ESTADO EMPRESA_UF,
       RTRIM(LTRIM(NVL(EMPRESAS.CIDADE, ' '))) EMPRESA_CIDADE,
       EMPRESAS.BAIRRO EMPRESA_BAIRRO,
       EMPRESAS.COMPLEMENTO EMPRESA_CMPTO,
       EMPRESAS.RUA EMPRESA_RUA,
       NVL(EMPRESAS.FONE, ' ') EMPRESA_FONE,
       EMPRESAS.FAX EMPRESA_FAX,
       EMPRESAS.CEP EMPRESA_CEP,
       EMPRESAS.INSCRICAO_ESTADUAL EMPRESA_INSCRICAO_ESTADUAL,
       EMPRESAS.INSCRICAO_MUNICIPAL EMPRESAS_INSCRICAO_MUNICIPAL,
       UF_EMPRESA.DESCRICAO EMPRESA_ESTADO,
       CONCESSIONARIAS.NOME CONCESSIONARIA_NOME,
       CONCESSIONARIAS.UF CONCESSIONARIA_UF,
       RTRIM(LTRIM(NVL(CONCESSIONARIAS.CIDADE, ' '))) CONCESSIONARIA_CIDADE,
       CONCESSIONARIAS.BAIRRO CONCESSIONARIA_BAIRRO,
       CONCESSIONARIAS.ENDERECO CONCESSIONARIA_RUA,
       CONCESSIONARIAS.CEP CONCESSIONARIA_CEP,
       UF_CONCESSIONARIA.DESCRICAO CONCESSIONARIA_ESTADO,
       CLIENTE_DIVERSO.COD_TIPO_CLIENTE,
       NVL(OS.CLIENTE_RAPIDO, CLIENTE_DIVERSO.NOME) AS CLIENTE_NOME,
      NVL(DECODE(CLIENTE_DIVERSO.CPF,
              NULL,
              'IE: ' || NVL(CLIENTE_DIVERSO.INSCRICAO_ESTADUAL, '          '),
              'RG: ' || CLIENTE_DIVERSO.RG), ' ') AS CLIENTE_RG,
       NVL(DECODE(CLIENTE_DIVERSO.CPF,
              NULL,
              'CNPJ: ' || NVL(CLIENTE_DIVERSO.CGC, '          '),
              'CPF: ' || CLIENTE_DIVERSO.CPF), ' ') AS CLIENTE_CGC_CPF,
       
       NVL(DECODE(OS.TIPO_ENDERECO,
              '1',
              CLIENTE_DIVERSO.UF,
              '2',
              CLIENTES.UF_RES,
              '3',
              CLIENTES.UF_COM,
              '4',
              CLIENTES.UF_COBRANCA,
              '5',
              ENDERECO_POR_INSCRICAO.UF,
              ' '), ' ') CLIENTE_UF,
       NVL(DECODE(OS.TIPO_ENDERECO,
              '1',
              UF_DIVERSO.DESCRICAO,
              '2',
              UF_RES.DESCRICAO,
              '3',
              UF_COM.DESCRICAO,
              '4',
              UF_COBRANCA.DESCRICAO,
              '5',
              UF_INSCRICAO.DESCRICAO,
              ' '), ' ') CLIENTE_ESTADO,
       NVL(DECODE(OS.TIPO_ENDERECO,
              '1',
              RTRIM(LTRIM(NVL(CIDADES_DIV.DESCRICAO, ' '))),
              '2',
              RTRIM(LTRIM(NVL(CIDADES_RES.DESCRICAO, ' '))),
              '3',
              RTRIM(LTRIM(NVL(CIDADES_COM.DESCRICAO, ' '))),
              '4',
              RTRIM(LTRIM(NVL(CIDADES_COBRANCA.DESCRICAO, ' '))),
              '5',
              RTRIM(LTRIM(NVL(ENDERECO_POR_INSCRICAO.CIDADE, ' '))),
              ' '), ' ') CLIENTE_CIDADE,
       NVL(DECODE(OS.TIPO_ENDERECO,
              '1',
              RTRIM(LTRIM(NVL(CLIENTE_DIVERSO.BAIRRO, ' '))),
              '2',
              RTRIM(LTRIM(NVL(CLIENTES.BAIRRO_RES, ' '))),
              '3',
              RTRIM(LTRIM(NVL(CLIENTES.BAIRRO_COM, ' '))),
              '4',
              RTRIM(LTRIM(NVL(CLIENTES.BAIRRO_COBRANCA, ' '))),
              '5',
              RTRIM(LTRIM(NVL(ENDERECO_POR_INSCRICAO.BAIRRO, ' '))),
              ' '), ' ') CLIENTE_BAIRRO,
       NVL(DECODE(OS.TIPO_ENDERECO,
              '1',
              CLIENTE_DIVERSO.CEP,
              '2',
              CLIENTES.CEP_RES,
              '3',
              CLIENTES.CEP_COM,
              '4',
              CLIENTES.CEP_COBRANCA,
              '5',
              ENDERECO_POR_INSCRICAO.CEP,
              ' '), ' ') CLIENTE_CEP,
       NVL(DECODE(OS.TIPO_ENDERECO,
              '1',
              CLIENTE_DIVERSO.ENDERECO,
              '2',
              CLIENTES.RUA_RES,
              '3',
              CLIENTES.RUA_COM,
              '4',
              CLIENTES.RUA_COBRANCA,
              '5',
              ENDERECO_POR_INSCRICAO.RUA,
              ' '), ' ') CLIENTE_RUA,
       NVL(DECODE(OS.TIPO_ENDERECO,
              '1',
              CLIENTE_DIVERSO.COMPLEMENTO,
              '2',
              CLIENTES.COMPLEMENTO_RES,
              '3',
              CLIENTES.COMPLEMENTO_COM,
              '4',
              CLIENTES.COMPLEMENTO_COBRANCA,
              '5',
              ENDERECO_POR_INSCRICAO.COMPLEMENTO,
              ' '), ' ') CLIENTE_COMPLEMENTO,
       NVL(DECODE(OS.TIPO_ENDERECO,
              '1',
              ' ',
              '2',
              CLIENTES.FACHADA_RES,
              '3',
              CLIENTES.FACHADA_COM,
              '4',
              CLIENTES.FACHADA_COBRANCA,
              '5',
              ENDERECO_POR_INSCRICAO.FACHADA,
              ' '), ' ') CLIENTE_FACHADA,
       NVL(DECODE(SIGN(LENGTH(OS.CLIENTE_RAPIDO)),
              1,
              NVL(OS.CLIENTE_RAPIDO_FONE, ' '),
              DECODE(OS.TIPO_ENDERECO,
                     1,
                     NVL(CLIENTE_DIVERSO.FONE_CONTATO, ' '),
                     2,
                     NVL(CLIENTES.TELEFONE_RES, ' '),
                     3,
                     NVL(CLIENTES.TELEFONE_COM, ' '),
                     4,
                     NVL(CLIENTES.TELEFONE_RES, ' '),
                     5,
                     NVL(ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO, ' '),
                     ' ')), ' ') AS CLIENTE_FONE,
       NVL(DECODE(SIGN(LENGTH(OS.CLIENTE_RAPIDO)),
              1,
              ' ',
              DECODE(OS.TIPO_ENDERECO,
                     1,
                     NVL(CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO, ' '),
                     2,
                     NVL(CLIENTES.PREFIXO_RES, ' '),
                     3,
                     NVL(CLIENTES.PREFIXO_COM, ' '),
                     4,
                     NVL(CLIENTES.PREFIXO_RES, ' '),
                     5,
                     NVL(ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO, ' '),
                     ' ')), ' ') AS CLIENTE_PREFIXO,
       NVL(CLIENTES.TELEFONE_CEL, ' ') TELEFONE_CEL,
       NVL(CLIENTES.PREFIXO_CEL, ' ') PREFIXO_CEL,
       NVL(CLIENTES.TELEFONE_COM, ' ') AS TELCOM,
       NVL(CLIENTES.PREFIXO_COM, ' ') PREFIXO_COM,
       CLIENTES.RADIO,
       SYSDATE AS DATA_ATUAL,
       OS.COD_SOCIO,
       OS.COD_BANCO,
       OS_TIPOS_EMPRESAS.IMPRIMIR_DADOS_FINANCIAMENTO,
       OS_TIPOS_EMPRESAS.IMPRIMIR_ASSINATURA,
       OS_TIPOS_EMPRESAS.IMPRIMIR_ASSINATURA2,
       OS_TIPOS_EMPRESAS.IMPRIMIR_RG_CPF,
       OS.LOCAL_EXECUCAO,
       CLIENTES.ENDERECO_ELETRONICO AS CLIENTE_EMAIL,
       OS.PECA_USADA_FICA_CLIENTE,
       OS_TIPOS_EMPRESAS.COD_CLIENTE_FABRICA_GARANTIA,
       NVL(OS.TEM_DESCONTO_ITEM, 'N') AS TEM_DESCONTO_ITEM,
       (SELECT NVL(SUM(A.PRECO_VENDA), 0)
          FROM OS_SERVICOS A, SERVICOS B
         WHERE A.COD_SERVICO = B.COD_SERVICO
           AND B.TERCEIROS = 'S'
           AND A.COD_EMPRESA = $P{COD_EMPRESA}
           AND A.NUMERO_OS = $P{NUMERO_OS}) AS TOT_SERV,
       OS_DADOS_VEICULOS.NUMERO_CONTRATO,
       TO_CHAR(TO_DATE(OS_DADOS_VEICULOS.DATA_CONTRATO,'DD/MM/RRRR'), 'DD/MM/RRRR') DATA_CONTRATO,
       NVL(OS_DADOS_VEICULOS.SERIE, ' ') NR_SERIE_VEICULO,
       NVL(TO_CHAR(TO_DATE(OS_DADOS_VEICULOS.DATA_FAB_BATERIA,'DD/MM/RRRR'), 'DD/MM/RRRR'), ' ') DATA_FAB_BATERIA,
       NVL(OS_DADOS_VEICULOS.COD_FAB_BATERIA, ' ') COD_FAB_BATERIA,
       CLIENTE_DIVERSO.IMP_NCM_OS AS IMP_NCM_OS_CLI,
       OS_TIPOS_EMPRESAS.IMP_NCM_OS AS IMP_NCM_OS_TIPO,
       OS_TIPOS_EMPRESAS.IMPRIMIR_ASSINATURA_PRODUTIVO,
       OS.OS_ENTRADA,
       '1ª VIA CLIENTE 1ª VIA CLIENTE 1ª VIA CLIENTE' OS_VIA,
       CO.TEXTO_AIDF,
       EMPRESAS.EMPRESA_NOME_COMPLETO,
       PRODUTOS_MODELOS.MOD_VER_SERIE,
       OS_DADOS_VEICULOS.TEM_GARANTIA_FABRICA,
       OS_DADOS_VEICULOS.TEM_GARANTIA_ESTENDIDA,
       OS.TOTAL_IMPRESSAO_DETALHE,
       OS.TOTAL_IMPRESSAO_FABRICA,
       CASE 
         WHEN (NOT OS_TIPOS_EMPRESAS.COD_CLIENTE_FABRICA_GARANTIA IS NULL) THEN
           OS_TIPOS_EMPRESAS.COD_CLIENTE_FABRICA_GARANTIA
         WHEN (OS_TIPOS_EMPRESAS.COD_CLIENTE_FABRICA_GARANTIA IS NULL) THEN
           NVL(OS_TIPOS_EMPRESAS.COD_CLIENTE, OS_TIPOS.COD_CLIENTE)
         WHEN (NOT OS.COD_SEGURADORA IS NULL) THEN
           OS.COD_SEGURADORA
         ELSE
           0
       END COD_CLIENTE_FABRICA,
       CASE
         WHEN (NOT EMPRESAS.FACHADA IS NULL) AND
              (NOT EMPRESAS.COMPLEMENTO IS NULL) THEN
          SUBSTR(EMPRESAS.RUA, 1, 50) || ', ' ||
          SUBSTR(EMPRESAS.FACHADA, 1, 15) || ', ' ||
          SUBSTR(EMPRESAS.COMPLEMENTO, 1, 15)
         WHEN (NOT EMPRESAS.FACHADA IS NULL) THEN
          SUBSTR(EMPRESAS.RUA, 1, 50) || ', ' ||
          SUBSTR(EMPRESAS.FACHADA, 1, 15)
         ELSE
          SUBSTR(EMPRESAS.RUA, 1, 50)
       END EMPRESA_ENDERECO,
       CASE
         WHEN (NOT EMPRESAS.CEP IS NULL) AND (NOT EMPRESAS.CIDADE IS NULL) AND
              (NOT EMPRESAS.ESTADO IS NULL) AND
              (NOT EMPRESAS.BAIRRO IS NULL) THEN
          EMPRESAS.CEP || ' ' || EMPRESAS.CIDADE || '-' || EMPRESAS.ESTADO || ', ' ||
          EMPRESAS.BAIRRO
         WHEN (NOT EMPRESAS.CEP IS NULL) AND (NOT EMPRESAS.CIDADE IS NULL) AND
              (NOT EMPRESAS.ESTADO IS NULL) THEN
          EMPRESAS.CEP || ' ' || EMPRESAS.CIDADE || '-' || EMPRESAS.ESTADO
         WHEN (NOT EMPRESAS.CEP IS NULL) AND (NOT EMPRESAS.CIDADE IS NULL) AND
              (NOT EMPRESAS.BAIRRO IS NULL) THEN
          EMPRESAS.CEP || ' ' || EMPRESAS.CIDADE || ', ' || EMPRESAS.BAIRRO
         WHEN (NOT EMPRESAS.CEP IS NULL) AND (NOT EMPRESAS.CIDADE IS NULL) THEN
          EMPRESAS.CEP || ' ' || EMPRESAS.CIDADE
         WHEN (NOT EMPRESAS.CEP IS NULL) THEN
          EMPRESAS.CEP
         ELSE
          ' '
       END EMPRESA_ENDERECO1,
      case when OS.Numero_Os_Fabrica is not null then 'O.S.: ' || OS.TIPO || '-' || OS."NUMERO_OS" || ' / ' else '' end ||
       (SELECT CASE
                 WHEN PS.TIPO_CONCESSIONARIA = 14 THEN
                  RTRIM(XMLAGG(XMLELEMENT(E, 'O.S.: ' || OS_TIPOS.TIPO_FABRICA || '-' || OS."NUMERO_OS" || ' / '))
                        .EXTRACT('//text()'),
                        ',')
                 ELSE
                  RTRIM(XMLAGG(XMLELEMENT(E, 'O.S.: ' || OS."TIPO" || '-' || OS."NUMERO_OS" || ' / '))
                        .EXTRACT('//text()'),
                        ',')
               END RELACIONADAS
          FROM "OS" OS, "OS_RELACOES" OS_RELACOES, OS_TIPOS, PARM_SYS PS
         WHERE OS.TIPO = OS_TIPOS.TIPO
           AND (OS_RELACOES.NUMERO_OS_IRMA = OS.NUMERO_OS)
           AND (OS_RELACOES.COD_EMPRESA = OS.COD_EMPRESA)
           AND (OS.COD_EMPRESA = PS.COD_EMPRESA)
           AND (NVL(OS.APAGAR_AO_SAIR, 'N') = 'N')
           AND (OS_RELACOES."COD_EMPRESA" = $P{COD_EMPRESA})
           AND (OS_RELACOES."NUMERO_OS" = $P{NUMERO_OS})           
         GROUP BY PS.TIPO_CONCESSIONARIA
        UNION
        SELECT CASE
                 WHEN PS.TIPO_CONCESSIONARIA = 14 THEN
                  RTRIM(XMLAGG(XMLELEMENT(E, 'Orça.: ' || OS_TIPOS.TIPO_FABRICA || '-' || OS."NUMERO_OS" || ' / '))
                        .EXTRACT('//text()'),
                        ',')
                 ELSE
                  RTRIM(XMLAGG(XMLELEMENT(E, 'Orça.: ' || OS."TIPO" || '-' || OS."NUMERO_OS" || ' / '))
                        .EXTRACT('//text()'),
                        ',')
               END RELACIONADAS
          FROM OS, OS_ORCAMENTOS, OS_TIPOS, PARM_SYS PS
         WHERE (OS_ORCAMENTOS.COD_EMPRESA = OS.COD_EMPRESA)
           AND (OS_ORCAMENTOS.NUMERO_OS = OS.NUMERO_OS)
           AND (OS.TIPO = OS_TIPOS.TIPO)
           AND (OS.COD_EMPRESA = PS.COD_EMPRESA)
           AND (NVL(OS.APAGAR_AO_SAIR, 'N') = 'N')
           AND (OS_ORCAMENTOS.COD_EMPRESA = $P{COD_EMPRESA})
           AND (OS_ORCAMENTOS.NUMERO_ORCAMENTO = $P{NUMERO_OS})
         GROUP BY PS.TIPO_CONCESSIONARIA) RELACIONADAS,
         R.RELATORIO DIAGNOSTICO,
         (SELECT NVL(TEXTO, (SELECT TEXTO
                                                FROM OS_TERMO
                                                WHERE ROWNUM = 1))
          FROM OS_TIPOS_TERMO
          WHERE COD_EMPRESA = OS.Cod_Empresa
           AND TIPO = OS.Tipo
           AND COD_TIPO = 1
           AND ROWNUM = 1) TERMO,
           NVL(OA.SIGNATURE,
           (SELECT OAS.SIGNATURE
              FROM OS_AGENDA OAS
             WHERE OAS.COD_EMPRESA = OS.COD_EMPRESA
               AND OAS.COD_OS_AGENDA = OS.COD_OS_AGENDA)) AS SIGNATURE,
          NVL(OS_ASSINATURA.ASSINATURA, MOB_OS_ASSINATURA.ASSINATURA_CLIENTE) AS ASSINATURA_CLI_CHECKOUT,
          NVL(OS_ASSINATURA.DATA_ASSINATURA, MOB_OS_ASSINATURA.DATA_ASSINATURA_CLIENTE) AS DATA_ASSINATURA_ENTREGA
  FROM OS,
       EMPRESAS,
       EMPRESAS_USUARIOS,
       CLIENTE_DIVERSO,
       CLIENTES,
       CIDADES                CIDADES_RES,
       CIDADES                CIDADES_COM,
       CIDADES                CIDADES_COBRANCA,
       CIDADES                CIDADES_DIV,
       ENDERECO_POR_INSCRICAO,
       OS_TIPOS,
       OS_TIPOS_EMPRESAS,
       OS_DADOS_VEICULOS,
       CONCESSIONARIAS,
       PRODUTOS,
       PRODUTOS_MODELOS,
       UF                     UF_EMPRESA,
       UF                     UF_DIVERSO,
       UF                     UF_RES,
       UF                     UF_COM,
       UF                     UF_COBRANCA,
       UF                     UF_INSCRICAO,
       UF                     UF_CONCESSIONARIA,
       MOTORISTAS,
       CONTROLE_OS            CO,
       EMPRESA_LOGO           EL,
       OS_RELATORIO           R,
       OS_AGENDA              OA,
       OS_ASSINATURA,
       MOB_OS_ASSINATURA
 WHERE (OS.COD_EMPRESA = EMPRESAS.COD_EMPRESA(+))
   AND (OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE(+))
   AND (CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+))
   AND (OS.TIPO = OS_TIPOS.TIPO(+))
   AND (CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES(+))
   AND (CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES(+))
   AND (CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES(+))
   AND (CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES(+))
   AND (OS.NOME = EMPRESAS_USUARIOS.NOME(+))
   AND (OS.INSCRICAO_ESTADUAL =
       ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL(+))
   AND (OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE(+))
   AND (OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS(+))
   AND (OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA(+))
   AND (OS_DADOS_VEICULOS.COD_CONCESSIONARIA =
       CONCESSIONARIAS.COD_CONCESSIONARIA(+))
   AND (OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO)
   AND (OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO)
   AND (OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO)
   AND (OS.CODIGO_MOTORISTA = MOTORISTAS.CODIGO_MOTORISTA(+))
   AND (EMPRESAS.ESTADO = UF_EMPRESA.UF(+))
   AND (CLIENTE_DIVERSO.UF = UF_DIVERSO.UF(+))
   AND (CLIENTES.UF_RES = UF_RES.UF(+))
   AND (CLIENTES.UF_COM = UF_COM.UF(+))
   AND (CLIENTES.UF_COBRANCA = UF_COBRANCA.UF(+))
   AND (ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF(+))
   AND (CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF(+))
   AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
   AND (OS.COD_EMPRESA = OS_TIPOS_EMPRESAS.COD_EMPRESA(+))
   AND (OS.TIPO = OS_TIPOS_EMPRESAS.TIPO(+))
   AND (OS.COD_EMPRESA = CO.COD_EMPRESA(+))
   AND OS.COD_EMPRESA = EL.COD_EMPRESA(+)
   AND OS.COD_EMPRESA = R.COD_EMPRESA(+)
   AND OS.NUMERO_OS = R.NUMERO_OS(+)
   AND OS.COD_EMPRESA = OA.COD_EMPRESA(+)
   AND OS.NUMERO_OS = OA.NUMERO_OS(+)
   AND OS_ASSINATURA.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND OS_ASSINATURA.NUMERO_OS(+) = OS.NUMERO_OS
   AND OS_ASSINATURA.TIPO_ASSINATURA(+) = 'ENTREGA_VEICULO_CLIENTE'
   AND MOB_OS_ASSINATURA.NUMERO_OS(+) = OS.NUMERO_OS
   AND MOB_OS_ASSINATURA.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND MOB_OS_ASSINATURA.APLICACAO(+) = 'E'
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND OS.NUMERO_OS = $P{NUMERO_OS}]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="STATUS_OS" class="java.lang.Double"/>
	<field name="COD_DOCUMENTO" class="java.lang.Double"/>
	<field name="TIPO_DOC" class="java.lang.String"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="ABS_OSNUM" class="java.lang.Double"/>
	<field name="NUMERO_OS_UNICA" class="java.lang.Double"/>
	<field name="COD_CLIENTE" class="java.lang.Double"/>
	<field name="COD_PRODUTO" class="java.lang.Double"/>
	<field name="COD_MODELO" class="java.lang.Double"/>
	<field name="TIPO_ENDERECO" class="java.lang.String"/>
	<field name="FRANQUIA" class="java.lang.String"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="TIPO_FABRICA" class="java.lang.String"/>
	<field name="VALIDADE" class="java.sql.Timestamp"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="ORCAMENTO" class="java.lang.String"/>
	<field name="EXTENDIDA" class="java.lang.String"/>
	<field name="SEGURADORA" class="java.lang.String"/>
	<field name="LIBERADO" class="java.lang.String"/>
	<field name="DATA_EMISSAO" class="java.lang.String"/>
	<field name="HORA_EMISSAO" class="java.lang.String"/>
	<field name="DATA_HORA_EMISSAO" class="java.lang.String"/>
	<field name="DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="DATA_HORA_ENTREGA" class="java.lang.String"/>
	<field name="ENTREGA_CLIENTE" class="java.lang.String"/>
	<field name="HORA_ENCERRADA" class="java.lang.String"/>
	<field name="DATA_ENCERRADA" class="java.lang.String"/>
	<field name="HORA_LIBERADO" class="java.lang.String"/>
	<field name="DATA_LIBERADO" class="java.lang.String"/>
	<field name="HORA_PROMETIDA" class="java.lang.String"/>
	<field name="DATA_PROMETIDA" class="java.lang.String"/>
	<field name="PRIMEIRA_LIBERACAO" class="java.sql.Timestamp"/>
	<field name="VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="DESCONTOS_SERVICOS" class="java.lang.Double"/>
	<field name="DESCONTOS_ITENS" class="java.lang.Double"/>
	<field name="INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="TOTAL_OS_SERVICOS" class="java.lang.Double"/>
	<field name="TOTAL_OS_ITENS" class="java.lang.Double"/>
	<field name="TOTAL_OS_BRUTO" class="java.lang.Double"/>
	<field name="TOTAL_OS_DESCONTO" class="java.lang.Double"/>
	<field name="TOTAL_OS" class="java.lang.Double"/>
	<field name="ORC_SERV_BRUTO" class="java.lang.Double"/>
	<field name="ORC_ITEM_BRUTO" class="java.lang.Double"/>
	<field name="ORC_SERV_DESCONTO" class="java.lang.Double"/>
	<field name="ORC_ITEM_DESCONTO" class="java.lang.Double"/>
	<field name="TOTAL_ORC_SERVICOS" class="java.lang.Double"/>
	<field name="TOTAL_ORC_ITENS" class="java.lang.Double"/>
	<field name="TOTAL_ORC_BRUTO" class="java.lang.Double"/>
	<field name="TOTAL_ORC_DESCONTO" class="java.lang.Double"/>
	<field name="TOTAL_ORC" class="java.lang.Double"/>
	<field name="KM_INICIAL" class="java.lang.Double"/>
	<field name="KM_FINAL" class="java.lang.Double"/>
	<field name="DESLOCAMENTO" class="java.lang.Double"/>
	<field name="COD_SEGURADORA" class="java.lang.Double"/>
	<field name="ANO" class="java.lang.String"/>
	<field name="HORIMETRO" class="java.lang.Double"/>
	<field name="PRISMA" class="java.lang.String"/>
	<field name="DATA_VENDA" class="java.lang.String"/>
	<field name="COMBUSTIVEL" class="java.lang.Double"/>
	<field name="COR_EXTERNA" class="java.lang.String"/>
	<field name="PLACA" class="java.lang.String"/>
	<field name="NUMERO_RENAVAM" class="java.lang.String"/>
	<field name="KM" class="java.lang.Double"/>
	<field name="CHASSI" class="java.lang.String"/>
	<field name="DESCONTOS_ICMS" class="java.lang.Double"/>
	<field name="DESCONTOS_ISS" class="java.lang.Double"/>
	<field name="DESCONTOS_GARANTIA" class="java.lang.Double"/>
	<field name="NUMERO_MOTOR" class="java.lang.String"/>
	<field name="NUMERO_CAMBIO" class="java.lang.String"/>
	<field name="SERIE" class="java.lang.String"/>
	<field name="BLINDADO" class="java.lang.String"/>
	<field name="NUMERO_DIFERENCIAL" class="java.lang.String"/>
	<field name="IMPRIMIR_QUEM_RETIROU" class="java.lang.String"/>
	<field name="MOTORISTA_CODIGO" class="java.lang.Double"/>
	<field name="MOTORISTA_NOME" class="java.lang.String"/>
	<field name="MOTORISTA_DOCUMENTO" class="java.lang.String"/>
	<field name="MOTORISTA_CPF" class="java.lang.String"/>
	<field name="MOTORISTA_TELEFONE" class="java.lang.String"/>
	<field name="TIPO_DESCRICAO" class="java.lang.String"/>
	<field name="TIPO_DESCRICAO_FAB" class="java.lang.String"/>
	<field name="TIPO_COD_CLIENTE" class="java.lang.Double"/>
	<field name="COD_IMG_TP" class="java.lang.Double"/>
	<field name="GARANTIA" class="java.lang.String"/>
	<field name="INTERNO" class="java.lang.String"/>
	<field name="NOME_COMPLETO" class="java.lang.String"/>
	<field name="CODIGO_OPCIONAL" class="java.lang.String"/>
	<field name="DESC_PROD_MOD" class="java.lang.String"/>
	<field name="LINHA" class="java.lang.String"/>
	<field name="COD_IMG_MOD" class="java.lang.Double"/>
	<field name="NOME_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESA_CGC" class="java.lang.String"/>
	<field name="EMPRESA_FACHADA" class="java.lang.String"/>
	<field name="EMPRESA_UF" class="java.lang.String"/>
	<field name="EMPRESA_CIDADE" class="java.lang.String"/>
	<field name="EMPRESA_BAIRRO" class="java.lang.String"/>
	<field name="EMPRESA_CMPTO" class="java.lang.String"/>
	<field name="EMPRESA_RUA" class="java.lang.String"/>
	<field name="EMPRESA_FONE" class="java.lang.String"/>
	<field name="EMPRESA_FAX" class="java.lang.String"/>
	<field name="EMPRESA_CEP" class="java.lang.String"/>
	<field name="EMPRESA_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_MUNICIPAL" class="java.lang.String"/>
	<field name="EMPRESA_ESTADO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="CONCESSIONARIA_UF" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CIDADE" class="java.lang.String"/>
	<field name="CONCESSIONARIA_BAIRRO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_RUA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CEP" class="java.lang.String"/>
	<field name="CONCESSIONARIA_ESTADO" class="java.lang.String"/>
	<field name="COD_TIPO_CLIENTE" class="java.lang.Double"/>
	<field name="CLIENTE_NOME" class="java.lang.String"/>
	<field name="CLIENTE_RG" class="java.lang.String"/>
	<field name="CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="CLIENTE_UF" class="java.lang.String"/>
	<field name="CLIENTE_ESTADO" class="java.lang.String"/>
	<field name="CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="CLIENTE_CEP" class="java.lang.String"/>
	<field name="CLIENTE_RUA" class="java.lang.String"/>
	<field name="CLIENTE_COMPLEMENTO" class="java.lang.String"/>
	<field name="CLIENTE_FACHADA" class="java.lang.String"/>
	<field name="CLIENTE_FONE" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO" class="java.lang.String"/>
	<field name="TELEFONE_CEL" class="java.lang.String"/>
	<field name="PREFIXO_CEL" class="java.lang.String"/>
	<field name="TELCOM" class="java.lang.String"/>
	<field name="PREFIXO_COM" class="java.lang.String"/>
	<field name="RADIO" class="java.lang.String"/>
	<field name="DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="COD_SOCIO" class="java.lang.Double"/>
	<field name="COD_BANCO" class="java.lang.Double"/>
	<field name="IMPRIMIR_DADOS_FINANCIAMENTO" class="java.lang.String"/>
	<field name="IMPRIMIR_ASSINATURA" class="java.lang.String"/>
	<field name="IMPRIMIR_ASSINATURA2" class="java.lang.String"/>
	<field name="IMPRIMIR_RG_CPF" class="java.lang.String"/>
	<field name="LOCAL_EXECUCAO" class="java.lang.String"/>
	<field name="CLIENTE_EMAIL" class="java.lang.String"/>
	<field name="PECA_USADA_FICA_CLIENTE" class="java.lang.String"/>
	<field name="COD_CLIENTE_FABRICA_GARANTIA" class="java.lang.Double"/>
	<field name="TEM_DESCONTO_ITEM" class="java.lang.String"/>
	<field name="TOT_SERV" class="java.lang.Double"/>
	<field name="NUMERO_CONTRATO" class="java.lang.String"/>
	<field name="DATA_CONTRATO" class="java.lang.String"/>
	<field name="NR_SERIE_VEICULO" class="java.lang.String"/>
	<field name="DATA_FAB_BATERIA" class="java.lang.String"/>
	<field name="COD_FAB_BATERIA" class="java.lang.String"/>
	<field name="IMP_NCM_OS_CLI" class="java.lang.String"/>
	<field name="IMP_NCM_OS_TIPO" class="java.lang.String"/>
	<field name="IMPRIMIR_ASSINATURA_PRODUTIVO" class="java.lang.String"/>
	<field name="OS_ENTRADA" class="java.lang.Double"/>
	<field name="OS_VIA" class="java.lang.String"/>
	<field name="TEXTO_AIDF" class="java.lang.String"/>
	<field name="EMPRESA_NOME_COMPLETO" class="java.lang.String"/>
	<field name="MOD_VER_SERIE" class="java.lang.String"/>
	<field name="TEM_GARANTIA_FABRICA" class="java.lang.String"/>
	<field name="TEM_GARANTIA_ESTENDIDA" class="java.lang.String"/>
	<field name="TOTAL_IMPRESSAO_DETALHE" class="java.lang.Double"/>
	<field name="TOTAL_IMPRESSAO_FABRICA" class="java.lang.Double"/>
	<field name="COD_CLIENTE_FABRICA" class="java.lang.Double"/>
	<field name="EMPRESA_ENDERECO" class="java.lang.String"/>
	<field name="EMPRESA_ENDERECO1" class="java.lang.String"/>
	<field name="RELACIONADAS" class="java.lang.String"/>
	<field name="DIAGNOSTICO" class="java.lang.String"/>
	<field name="TERMO" class="java.lang.String"/>
	<field name="SIGNATURE" class="java.awt.Image"/>
	<field name="ASSINATURA_CLI_CHECKOUT" class="java.awt.Image"/>
	<field name="DATA_ASSINATURA_ENTREGA" class="java.sql.Timestamp"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="71" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="274" y="6" width="278" height="22" backcolor="#D9D9D9" uuid="3c86bcb0-c63f-415a-9456-c388f8ecc1d8">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Calibri" size="16" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME_EMPRESA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="274" y="32" width="176" height="12" forecolor="#3668FF" uuid="1f914509-24aa-4455-893c-3c396fc08278"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{EMPRESA_ENDERECO}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Opaque" x="458" y="32" width="94" height="12" forecolor="#3668FF" uuid="11dc90da-469a-49c5-ab2c-996c9e15ed05"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["I.E: " + $F{EMPRESA_INSCRICAO_ESTADUAL}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Opaque" x="458" y="44" width="94" height="14" forecolor="#3668FF" uuid="1e415798-8e5a-4910-9f49-ad2d3b1a5242"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["I.M: " + $F{EMPRESAS_INSCRICAO_MUNICIPAL}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="273" y="44" width="177" height="14" forecolor="#3668FF" uuid="24c60e54-52aa-4e09-b85f-bbd25eff2a32"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{EMPRESA_ENDERECO1}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Opaque" x="273" y="57" width="94" height="14" forecolor="#3668FF" uuid="2323439b-d1d4-4c54-bfe5-ff49ff0cb61c"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Fone: " + $F{EMPRESA_FONE}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Opaque" x="458" y="57" width="94" height="14" forecolor="#3668FF" uuid="ec91a511-9c4e-4e37-9820-9ad2cc5650b1"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["CNPJ: " + $F{EMPRESA_CGC}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="5" width="272" height="66" isRemoveLineWhenBlank="true" uuid="e6b46a5a-93d8-41cc-beee-41c77135cf38">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoLogo.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</title>
	<detail>
		<band height="104">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="3" width="554" height="18" backcolor="#D9D9D9" uuid="f044ce1b-c6b0-40ca-b473-27331128e380">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="70" y="5" width="90" height="14" uuid="6002c66f-057d-468a-bcd1-d100d5b465cf"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo]]></text>
			</staticText>
			<staticText>
				<reportElement x="162" y="5" width="68" height="14" uuid="6aab5035-6025-4aea-a866-0d315ebd327d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Relacionadas]]></text>
			</staticText>
			<staticText>
				<reportElement x="275" y="5" width="70" height="14" uuid="cfdbd40b-5ef4-46ec-9115-0d75cf8d60e2"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Consultor]]></text>
			</staticText>
			<staticText>
				<reportElement x="377" y="5" width="82" height="14" uuid="d385c281-414a-45c7-9dee-816b8a771d18">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Entrada]]></text>
			</staticText>
			<staticText>
				<reportElement x="467" y="5" width="82" height="14" uuid="429a4178-7d6b-4694-aeda-3e3e79179231">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsão Entrega]]></text>
			</staticText>
			<textField>
				<reportElement x="6" y="5" width="62" height="14" uuid="d3cadaa7-a586-4ec4-923e-cb5175e1acaf">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIPO_DOC}]]></textFieldExpression>
			</textField>
			<textField pattern="#.###;(#.###-)">
				<reportElement x="6" y="23" width="62" height="24" uuid="a156ffab-e767-4325-a4a3-c4cde8230fcb">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NUMERO_OS} < 0.0 ? $F{ABS_OSNUM} : $F{NUMERO_OS_UNICA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="70" y="23" width="90" height="12" uuid="54e3638b-b688-4948-9ed9-d65e13a92bb9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIPO_DESCRICAO}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement mode="Transparent" x="70" y="36" width="10" height="10" uuid="6169cd6d-472d-474e-a637-8bdbfe43a650">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="-2" width="10" height="10" uuid="5ce00382-9070-4a92-bef6-0bfb751fd873">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<printWhenExpression><![CDATA[$F{TEM_GARANTIA_FABRICA} == "S"]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<text><![CDATA[X]]></text>
				</staticText>
			</frame>
			<staticText>
				<reportElement x="80" y="35" width="80" height="11" uuid="a8d11142-cad4-4998-b1d7-3428a3111e98"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="9" isBold="false"/>
				</textElement>
				<text><![CDATA[ Garantia Fábrica]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="162" y="23" width="111" height="23" uuid="5fbdb8db-a96a-45c0-95e8-eaba352d037b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RELACIONADAS}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement mode="Transparent" x="275" y="36" width="10" height="10" uuid="942185fa-1c11-4c2c-a350-742fb2d7bfc4">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="10" height="10" uuid="24427301-ca50-4dd7-a81d-344eaf37f51e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<printWhenExpression><![CDATA[$F{TEM_GARANTIA_ESTENDIDA} == "S"]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<text><![CDATA[X]]></text>
				</staticText>
			</frame>
			<staticText>
				<reportElement x="286" y="36" width="80" height="11" uuid="21ba37a6-b3e2-408a-bf1d-1495ef9aaf6a"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="9" isBold="false"/>
				</textElement>
				<text><![CDATA[ Garantia Estendida]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="275" y="23" width="91" height="12" uuid="12a9829b-61e2-4043-8b14-ae0dd41bb52e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME_COMPLETO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="376" y="23" width="85" height="12" uuid="d59fb3f8-fac7-4f62-a7ba-5337e2cf2d30">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DATA_HORA_EMISSAO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="467" y="23" width="85" height="12" uuid="7a894f5d-5db4-499d-b3b2-c94abd2666d7">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DATA_PROMETIDA}+" às "+$F{HORA_PROMETIDA}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="49" width="554" height="18" backcolor="#D9D9D9" uuid="f48d8ac6-3a8b-4ed3-a94b-1f979d5ea7ff">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="6" y="51" width="298" height="14" uuid="d52316be-ebf1-4b72-8c92-b9d12599f8ec">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cliente: " + $F{CLIENTE_NOME}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="320" y="51" width="228" height="14" uuid="3917fff9-0fd2-4aad-bcc4-c31e01c0f54d"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CLIENTE_EMAIL}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="68" width="241" height="12" isRemoveLineWhenBlank="true" uuid="41c429a4-8ffe-4e3f-b492-a1d92bc17509">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CLIENTE_RUA} +  " " + $F{CLIENTE_FACHADA} + " " + $F{CLIENTE_COMPLEMENTO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="80" width="241" height="12" isRemoveLineWhenBlank="true" uuid="2418a0c1-794b-4779-896b-db5c85a3df25">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CLIENTE_BAIRRO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="92" width="241" height="12" isRemoveLineWhenBlank="true" uuid="c640bf14-b3bb-4e82-a49c-53732ec6685e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CLIENTE_CEP} + " " + $F{CLIENTE_CIDADE} + " " +  $F{CLIENTE_UF}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="251" y="68" width="99" height="12" isRemoveLineWhenBlank="true" uuid="0433edc0-8544-474b-9787-a13df03f631e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CLIENTE_RG}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="251" y="80" width="158" height="12" isRemoveLineWhenBlank="true" uuid="71d4f3cd-a7ea-484e-ae42-208c4bb30576">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CLIENTE_CGC_CPF}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="431" y="68" width="117" height="12" isRemoveLineWhenBlank="true" uuid="d037fbc6-1f30-4f0e-a8de-5d123348c308">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Res: (" + $F{CLIENTE_PREFIXO}  + ")" + $F{CLIENTE_FONE}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="431" y="80" width="117" height="12" isRemoveLineWhenBlank="true" uuid="23c6ca57-9669-4acf-a341-9c4dab697b7b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Cel: (" +$F{PREFIXO_CEL} + ")" + $F{TELEFONE_CEL}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="431" y="92" width="117" height="12" isRemoveLineWhenBlank="true" uuid="b55ef023-7afa-4127-b26c-93a3c6fd1f93">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Com: (" +$F{PREFIXO_COM}  + ")" + $F{TELCOM}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="416" y="36" width="62" height="11" uuid="3d8c6690-542a-4f8c-b8e4-e47bcad5bf84">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<printWhenExpression><![CDATA[$F{TIPO_DOC}.equals("Orçamento")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{VALIDADE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="376" y="36" width="39" height="11" uuid="01a4e6ed-5c5d-4213-9db0-ac054adac668">
					<printWhenExpression><![CDATA[$F{TIPO_DOC}.equals("Orçamento")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="9" isBold="false"/>
					<paragraph firstLineIndent="1"/>
				</textElement>
				<text><![CDATA[Validade: ]]></text>
			</staticText>
		</band>
		<band height="10">
			<subreport>
				<reportElement x="1" y="0" width="552" height="10" isRemoveLineWhenBlank="true" uuid="c7156abb-fdfc-455c-9f60-68e2866d661a"/>
				<subreportParameter name="COD_CLIENTE">
					<subreportParameterExpression><![CDATA[$F{COD_SEGURADORA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubGarantia.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="10" splitType="Stretch">
			<subreport>
				<reportElement x="1" y="0" width="552" height="10" isRemoveLineWhenBlank="true" uuid="d78cb2ce-f756-4d6c-a554-9a0f6129eebe"/>
				<subreportParameter name="COD_CLIENTE">
					<subreportParameterExpression><![CDATA[$F{COD_CLIENTE_FABRICA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubFabrica.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="71">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#D9D9D9" uuid="c68d571b-6c1d-4dc2-9eed-904d49902d8c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="6" y="2" width="298" height="14" uuid="5e10d993-42ff-41c6-837b-a72d916d8784">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Veículo: " + $F{DESC_PROD_MOD}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement mode="Transparent" x="491" y="5" width="10" height="10" uuid="d644f701-eddc-4611-9ad0-f68bee67086f">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="10" height="10" uuid="9b643f31-b5d8-4834-92e9-7870c8fbd6ae">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<printWhenExpression><![CDATA[$F{BLINDADO} == "S"]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Calibri" size="7"/>
					</textElement>
					<text><![CDATA[X]]></text>
				</staticText>
			</frame>
			<staticText>
				<reportElement x="503" y="2" width="50" height="15" uuid="fa523d75-11f4-496f-8a62-d4082396fc84"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Blindado]]></text>
			</staticText>
			<textField>
				<reportElement x="2" y="19" width="160" height="12" uuid="fac4a522-459b-4f18-846d-21e43e82bfb8">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Chassi: " + ($F{CHASSI} != null ? $F{CHASSI} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="31" width="160" height="12" uuid="6ad35e8f-8a12-4c3b-b866-f36c20c88e5d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["KM: " + ($F{KM} != null ? $F{KM} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="43" width="160" height="12" uuid="893b1b2e-eef7-48fa-9cdc-f1b73d903b98">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Ano/Mod.: " + ($F{ANO} != null ? $F{ANO} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="167" y="31" width="210" height="12" uuid="ef00080a-e9a8-4abd-98f8-fc553516f3b4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Motorista: " +($F{MOTORISTA_NOME} != null ? $F{MOTORISTA_NOME} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="167" y="43" width="210" height="12" uuid="5e94c592-3ed0-4d12-98f0-c44593444604">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Doc. Motorista.: " +($F{MOTORISTA_DOCUMENTO} != null ? $F{MOTORISTA_DOCUMENTO} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="167" y="19" width="210" height="12" uuid="5d76142f-d700-4feb-9ff5-9c17f773a297">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Cor Externa: " + ($F{COR_EXTERNA} != null ? $F{COR_EXTERNA} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="380" y="31" width="173" height="12" uuid="662e1ef7-e338-46fb-9825-0764897b368e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Dt. Fab. Bateria: " + ($F{DATA_FAB_BATERIA} != null ? $F{DATA_FAB_BATERIA} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="380" y="43" width="173" height="12" uuid="dfd8340d-7650-490c-883f-eb4c3af277aa">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Numero Série.: " + ($F{SERIE} != null ? $F{SERIE} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="380" y="19" width="173" height="12" uuid="405796b0-5a49-4fea-93eb-383614ec60f0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["N. Bateria: " + ($F{COD_FAB_BATERIA} != null ? $F{COD_FAB_BATERIA} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="56" width="160" height="12" uuid="a30b4547-9b81-43bb-a7bc-55824911d9a8">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Placa.: " + ($F{PLACA} != null ? $F{PLACA} : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="167" y="56" width="210" height="12" uuid="cc14d2dd-41c2-4dac-bc2b-a648c1e5d9f0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Número do Motor.: " + ($F{NUMERO_MOTOR} != null ?  $F{NUMERO_MOTOR} : "")]]></textFieldExpression>
			</textField>
		</band>
		<band height="42">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#D9D9D9" uuid="1cbc6a0d-ae07-4539-9f27-a4f39eb38717">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="6" y="2" width="546" height="14" uuid="b244af67-ba9d-4258-95d2-551b4fbc8db5">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Concessionaria: " + $F{CONCESSIONARIA_NOME}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="18" width="178" height="12" isRemoveLineWhenBlank="true" uuid="e8c83394-0376-4c0d-8b29-22ce4c79b602">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CONCESSIONARIA_RUA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="30" width="178" height="12" isRemoveLineWhenBlank="true" uuid="863fdf42-0d6d-4ff9-97a3-0edd68d5d012">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CONCESSIONARIA_BAIRRO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="18" width="160" height="12" isRemoveLineWhenBlank="true" uuid="35cfe64f-4384-430f-a468-094c3d952dfb">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["CEP: " + $F{CONCESSIONARIA_CEP}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="30" width="160" height="12" isRemoveLineWhenBlank="true" uuid="71731669-30a5-4af0-a8a7-155871264642">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CONCESSIONARIA_CIDADE} + " " + $F{CONCESSIONARIA_UF}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="375" y="19" width="173" height="12" isRemoveLineWhenBlank="true" uuid="64bf4e76-6bbb-439c-a92f-7001fc667d7f">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Dt. Venda: " + $F{DATA_VENDA}]]></textFieldExpression>
			</textField>
		</band>
		<band height="10">
			<subreport>
				<reportElement x="0" y="0" width="553" height="10" isRemoveLineWhenBlank="true" uuid="79cdaea9-9485-4942-96d4-5084ff80c993"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubReclamacoes.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="10">
			<subreport>
				<reportElement x="0" y="0" width="553" height="10" isRemoveLineWhenBlank="true" uuid="c5038f1c-3af3-453e-a467-6860cd18cab5"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubServicos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="10">
			<subreport>
				<reportElement x="0" y="0" width="553" height="10" isRemoveLineWhenBlank="true" uuid="74d7cc97-ea9b-4a68-9a00-fd29d89f47e1"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubPecas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="10">
			<subreport>
				<reportElement x="0" y="0" width="553" height="10" isRemoveLineWhenBlank="true" uuid="364cfcc4-44ac-493b-b8b0-569ecee97224"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubFechamento.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="10">
			<subreport>
				<reportElement x="0" y="0" width="553" height="10" isRemoveLineWhenBlank="true" uuid="f1bb1a36-7f21-4ca7-b1ec-64b006743582"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubFormaPagamento.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="10">
			<subreport>
				<reportElement x="0" y="0" width="553" height="10" isRemoveLineWhenBlank="true" uuid="832f7772-034f-49a9-9433-fcf0e285e873">
					<printWhenExpression><![CDATA[$F{OBSERVACAO} != null]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubObservacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="10">
			<subreport>
				<reportElement x="0" y="0" width="553" height="10" isRemoveLineWhenBlank="true" uuid="e262a052-b6ae-4b4c-836d-70fe832e325f">
					<printWhenExpression><![CDATA[$P{MOSTRAR_SUB_DIAGNOSTICO}.equals("S")]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubDiagnostico.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="10">
			<subreport isUsingCache="false" overflowType="Stretch">
				<reportElement key="" x="0" y="0" width="553" height="10" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="25963dd1-a80e-4af3-b753-12263611f0cb"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TIPO">
					<subreportParameterExpression><![CDATA[$F{TIPO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubTermo.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="10">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="0" width="553" height="10" isRemoveLineWhenBlank="true" uuid="03362424-ce3d-483d-99e4-abf2582d2d90"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoSubDecalque.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="175" splitType="Prevent">
			<staticText>
				<reportElement x="2" y="8" width="343" height="25" uuid="6c937063-37cb-4fe7-a4c2-e4a33965e043"/>
				<textElement>
					<font size="12"/>
				</textElement>
				<text><![CDATA[Autorizo a execução dos serviços acima mencionados]]></text>
			</staticText>
			<staticText>
				<reportElement x="64" y="157" width="138" height="14" uuid="a5cadf2a-5159-45bb-8ee9-d490538a5c59"/>
				<textElement textAlignment="Center"/>
				<text><![CDATA[Data Entrada do Veículo]]></text>
			</staticText>
			<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle" onErrorType="Blank">
				<reportElement positionType="Float" mode="Transparent" x="13" y="55" width="250" height="54" uuid="affcc255-24ec-4f2d-8346-f02d6221a6ff">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<imageExpression><![CDATA[$F{SIGNATURE}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="1" y="25" width="273" height="23" uuid="607b622a-a8e6-46a6-a8fd-3415b2725c01"/>
				<textElement textAlignment="Center">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[CHECK-IN]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="112" width="273" height="14" uuid="23800fda-1810-4ca7-9dc8-e06fd1423c64"/>
				<box>
					<topPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center"/>
				<text><![CDATA[Assinatura do Cliente ou pessoa por ele autorizada]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="64" y="135" width="138" height="20" uuid="f60483f9-c165-41ce-b932-1f304494e004"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{DATA_HORA_EMISSAO}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="3" width="555" height="1" uuid="269d9320-703e-4340-a284-33f3e648aea0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
			</line>
			<break>
				<reportElement x="0" y="1" width="100" height="1" uuid="057dd7e3-c691-4b3a-a30a-087a1652ffe5"/>
			</break>
			<frame>
				<reportElement x="280" y="25" width="274" height="146" uuid="396e47fc-a0da-4721-bcb0-c4120f35b883">
					<printWhenExpression><![CDATA["S".equals($F{ENTREGA_CLIENTE}) || $F{DATA_ASSINATURA_ENTREGA} != null || "S".equals($P{ASSINAR_DIGITALMENTE})]]></printWhenExpression>
				</reportElement>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement positionType="Float" mode="Transparent" x="12" y="30" width="250" height="54" uuid="a82fd8ea-1639-4b90-bf3b-721c2cfbad92">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<printWhenExpression><![CDATA[$P{ASSINAR_DIGITALMENTE}.equals("N")]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[$F{ASSINATURA_CLI_CHECKOUT}]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="0" y="87" width="273" height="14" uuid="f05cb37b-9113-4998-ae20-6609f735b264"/>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="DejaVu Sans"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente ou pessoa por ele autorizada]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="0" width="274" height="23" uuid="677ee92b-2d6a-4125-8d04-c05fdd5c5da0"/>
					<textElement textAlignment="Center">
						<font size="14" isBold="true"/>
					</textElement>
					<text><![CDATA[CHECK-OUT]]></text>
				</staticText>
				<staticText>
					<reportElement x="62" y="132" width="138" height="14" uuid="0a1cea66-6072-4f41-a151-d97d78d09e9d"/>
					<textElement textAlignment="Center"/>
					<text><![CDATA[Data Saída do Veículo]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy HH:mm" isBlankWhenNull="true">
					<reportElement x="62" y="110" width="138" height="20" uuid="71ead007-7d43-4b58-8128-e5ed7229a18e"/>
					<textElement textAlignment="Center" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{DATA_ENTREGA} != null ? $F{DATA_HORA_ENTREGA}
: $F{DATA_ASSINATURA_ENTREGA} != null ? $F{DATA_ASSINATURA_ENTREGA}
: $P{ASSINAR_DIGITALMENTE}.equals("S") ? $F{DATA_ATUAL} : $F{DATA_HORA_ENTREGA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="62" y="53" width="149" height="22" forecolor="#FFFFFF" uuid="0773580b-330a-4ea2-b1ec-91591854ec7f"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font size="4"/>
					</textElement>
					<text><![CDATA[#CLIENTE]]></text>
				</staticText>
			</frame>
		</band>
		<band height="262">
			<printWhenExpression><![CDATA[$F{IMPRIMIR_QUEM_RETIROU}.equals("IMPRIMIRNUNCA")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="555" height="262" uuid="c4c64649-1224-4957-a45d-c64ffe4f5db1">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement positionType="Float" mode="Transparent" x="291" y="48" width="250" height="54" uuid="4d92c835-d8a2-4f89-b25c-94ede3f7bb33">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[$F{ENTREGA_CLIENTE}.equals("S") && $P{ASSINAR_DIGITALMENTE}.equals("N")]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[$F{ASSINATURA_CLI_CHECKOUT}]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="279" y="105" width="273" height="14" uuid="6430659a-4abd-461c-9738-00b9362bbf68"/>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center"/>
					<text><![CDATA[Assinatura do Cliente ou pessoa por ele autorizada]]></text>
				</staticText>
				<staticText>
					<reportElement x="63" y="149" width="138" height="14" uuid="a3528ed6-989e-405b-85fc-c3ca7855948c"/>
					<textElement textAlignment="Center"/>
					<text><![CDATA[Data Entrada do Veículo]]></text>
				</staticText>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement positionType="Float" mode="Transparent" x="13" y="48" width="250" height="54" uuid="bedf33b0-9586-420a-938c-ffd68f7f4efa">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{SIGNATURE}]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="0" y="18" width="273" height="23" uuid="d123be0d-2ca1-4303-8f7d-559ac925c2ff"/>
					<textElement textAlignment="Center">
						<font size="14" isBold="true"/>
					</textElement>
					<text><![CDATA[CHECK-IN]]></text>
				</staticText>
				<staticText>
					<reportElement x="278" y="18" width="274" height="23" uuid="c5e8910c-461a-442d-b41f-9b51a1614f09"/>
					<textElement textAlignment="Center">
						<font size="14" isBold="true"/>
					</textElement>
					<text><![CDATA[CHECK-OUT]]></text>
				</staticText>
				<staticText>
					<reportElement x="341" y="149" width="138" height="14" uuid="bc849470-bfc9-42b0-99e6-847b6f2d591f"/>
					<textElement textAlignment="Center"/>
					<text><![CDATA[Data Saída do Veículo]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="105" width="273" height="14" uuid="e05a9680-a75a-4c7f-8ce4-dbd9f7f4586b"/>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center"/>
					<text><![CDATA[Assinatura do Cliente ou pessoa por ele autorizada]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="63" y="127" width="138" height="20" uuid="0a69533e-a70b-4171-b108-807847137d66"/>
					<textElement textAlignment="Center" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="341" y="127" width="138" height="20" uuid="dc469387-138a-40f7-a009-964d56aa6cf9"/>
					<textElement textAlignment="Center" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{DATA_HORA_ENTREGA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="124" y="174" width="273" height="14" uuid="0b3da76d-3a29-4ac4-a13f-8ec70b124826"/>
					<box>
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center">
						<font size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável pela retirada do Veículo]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="224" width="126" height="14" uuid="288956bd-664c-4af1-a1e3-dda038466276"/>
					<box>
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{MOTORISTA_CODIGO} == null?$F{CLIENTE_PREFIXO}: $F{MOTORISTA_TELEFONE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="163" y="224" width="180" height="14" uuid="9ec87f2c-a271-4f74-81da-2ef47b6cdb5b">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MOTORISTA_CODIGO}==null?$F{CLIENTE_RG}.replace("RG: ",""): $F{MOTORISTA_DOCUMENTO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="370" y="224" width="180" height="14" uuid="82bf3f70-da47-4237-9c2a-fa0e2218d708"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MOTORISTA_CODIGO}==null?$F{CLIENTE_CGC_CPF}.replace("CPF: ","").replace("CGC: ",""): $F{MOTORISTA_CPF}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="163" y="238" width="180" height="14" uuid="929279ab-3465-478e-891b-a41b323b8128">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center">
						<font isItalic="false"/>
					</textElement>
					<text><![CDATA[RG]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="238" width="126" height="14" uuid="13062156-c667-4822-84ef-e8611590fa0a"/>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center"/>
					<text><![CDATA[TELEFONE]]></text>
				</staticText>
				<staticText>
					<reportElement x="370" y="238" width="180" height="14" uuid="5cccc6a5-551d-47a6-889a-b909267824c5"/>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center">
						<font isItalic="false"/>
					</textElement>
					<text><![CDATA[CPF/CGC]]></text>
				</staticText>
				<rectangle>
					<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#D9D9D9" uuid="0db947a6-128a-430b-94f7-7525b061ac83">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<printWhenExpression><![CDATA[Boolean.FALSE]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<line>
					<reportElement x="0" y="0" width="555" height="1" uuid="0f223f2e-d61d-452f-8f22-e0761f8acc04">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="6" y="2" width="546" height="14" uuid="237501ec-4991-41d0-9c9a-1583c0b0ac0d">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<printWhenExpression><![CDATA[Boolean.FALSE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Calibri" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Motorista]]></text>
				</staticText>
				<textField>
					<reportElement x="124" y="191" width="273" height="14" uuid="9f024ea7-b5f3-4c81-9921-eb2bb3826259"/>
					<box>
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{MOTORISTA_CODIGO}==null?$F{CLIENTE_NOME}:$F{MOTORISTA_NOME}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="96">
			<printWhenExpression><![CDATA[$F{IMPRIMIR_QUEM_RETIROU}.equals("S") && $F{ENTREGA_CLIENTE}.equals("S")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="555" height="95" uuid="e3046442-2f08-47c9-ae2d-da8b9bebd5f3">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textField>
					<reportElement x="3" y="55" width="126" height="14" uuid="1c6ff26e-bd35-46aa-b0e6-5551ecbdd16a"/>
					<box>
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{MOTORISTA_CODIGO} == null?$F{CLIENTE_PREFIXO}: $F{MOTORISTA_TELEFONE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="163" y="55" width="180" height="14" uuid="9955c360-532a-46b0-b8a2-f3242df17aae">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MOTORISTA_CODIGO}==null?$F{CLIENTE_RG}.replace("RG: ",""): $F{MOTORISTA_DOCUMENTO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="370" y="55" width="180" height="14" uuid="2f5497b8-8643-4abb-b82e-12b04c8923f1"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MOTORISTA_CODIGO}==null?$F{CLIENTE_CGC_CPF}.replace("CPF: ","").replace("CGC: ",""): $F{MOTORISTA_CPF}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="163" y="69" width="180" height="14" uuid="c3f17db9-6ab1-48d0-aa6c-f722943e7e19">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center">
						<font isItalic="false"/>
					</textElement>
					<text><![CDATA[RG]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="69" width="126" height="14" uuid="0c9b32be-4ae5-4f4c-9ecf-ed722b5f0b55"/>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center"/>
					<text><![CDATA[TELEFONE]]></text>
				</staticText>
				<staticText>
					<reportElement x="370" y="69" width="180" height="14" uuid="a58bcbc0-ee39-4b69-8f19-4e1ca428c3eb"/>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center">
						<font isItalic="false"/>
					</textElement>
					<text><![CDATA[CPF/CGC]]></text>
				</staticText>
				<textField>
					<reportElement x="0" y="27" width="554" height="14" uuid="685ec23f-57db-4c10-9cf7-ea97bc8b85ae"/>
					<box>
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{MOTORISTA_CODIGO}==null?$F{CLIENTE_NOME}:$F{MOTORISTA_NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="13" width="553" height="14" uuid="eeb6888f-7513-4e18-96e3-2ba3e2fb502b"/>
					<box>
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center">
						<font size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável pela retirada do Veículo]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
