<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsGwmSubOrcamentos" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT ABS(NUMERO_ORCAMENTO) as NUMERO_ORCAMENTO, ITEM, DESCRICAO_RECLAMACAO, ITEM_ORDER, TIPO, CODIGO, DESCRICAO, QUANTIDADE, VALOR_UNITARIO, VALOR_TOTAL, APROVADO
FROM(
SELECT A.NUMERO_ORCAMENTO
       , B.ITEM
       , B.DESCRICAO AS DESCRICAO_RECLAMACAO
       , (ROW_NUMBER() OVER(PARTITION BY A.NUMERO_ORCAMENTO, B.ITEM ORDER BY B.ITEM)) AS ITEM_ORDER
       ,'SV' AS TIPO
       , C.COD_SERVICO AS CODIGO
       , INITCAP(S.DESCRICAO_SERVICO) AS DESCRICAO
       , C.TEMPO AS QUANTIDADE
       , NVL(C.TOTAL_LIQUIDO, C.PRECO_VENDA) AS VALOR_UNITARIO
       , NVL(C.TOTAL_LIQUIDO, C.PRECO_VENDA) * C.TEMPO AS VALOR_TOTAL
       , DECODE(NVL(C.DESAPROVADO,'N'),'N','Sim', 'Não') AS APROVADO
FROM OS_ORCAMENTOS A
     , OS_ORIGINAL B
     , OS_SERV_ORC C
     , SERVICOS S
WHERE A.NUMERO_OS = $P{NUMERO_OS}
      AND A.COD_EMPRESA = $P{COD_EMPRESA}
      AND A.NUMERO_ORCAMENTO = B.NUMERO_OS
      AND A.COD_EMPRESA = B.COD_EMPRESA
      AND B.NUMERO_OS = C.NUMERO_OS
      AND B.COD_EMPRESA =C.COD_EMPRESA
      AND B.ITEM = C.ITEM
      AND C.COD_SERVICO = S.COD_SERVICO
UNION 
SELECT A.NUMERO_ORCAMENTO
       , B.ITEM
       , B.DESCRICAO AS DESCRICAO_RECLAMACAO
       , (ROW_NUMBER() OVER(PARTITION BY A.NUMERO_ORCAMENTO, B.ITEM ORDER BY D.COD_ITEM)) AS ITEM_ORDER
       , 'PC' AS TIPO
       , D.COD_ITEM AS CODIGO
       , INITCAP(I.DESCRICAO) AS DESCRICAO
       , D.QUANTIDADE AS QUANTIDADE
       , NVL(D.PRECO_LIQUIDO_ORI, D.PRECO_VENDA) AS VALOR_UNITARIO
       , NVL(D.PRECO_LIQUIDO_ORI, D.PRECO_VENDA) * D.QUANTIDADE AS VALOR_TOTAL
       , DECODE(NVL(D.DESAPROVADO,'N'),'N','Sim', 'Não') AS APROVADO
FROM OS_ORCAMENTOS A
     , OS_ORIGINAL B
     , OS_SERV_ORC C
     , OS_ORCAMENTOS_ITENS D
     , SERVICOS S
     , ITENS I
WHERE A.NUMERO_OS = $P{NUMERO_OS}
      AND A.COD_EMPRESA = $P{COD_EMPRESA}
      AND A.NUMERO_ORCAMENTO = B.NUMERO_OS
      AND A.COD_EMPRESA = B.COD_EMPRESA
      AND B.NUMERO_OS = C.NUMERO_OS
      AND B.COD_EMPRESA =C.COD_EMPRESA
      AND B.ITEM = C.ITEM
      AND C.NUMERO_OS = D.NUMERO_OS 
      AND C.COD_EMPRESA = D.COD_EMPRESA
      AND C.COD_SERVICO = D.COD_SERVICO     
      AND C.COD_SERVICO = S.COD_SERVICO
      AND D.COD_ITEM = I.COD_ITEM) T
ORDER BY NUMERO_ORCAMENTO, ITEM, TIPO DESC, ITEM_ORDER]]>
	</queryString>
	<field name="NUMERO_ORCAMENTO" class="java.math.BigDecimal"/>
	<field name="ITEM" class="java.math.BigDecimal"/>
	<field name="DESCRICAO_RECLAMACAO" class="java.lang.String"/>
	<field name="ITEM_ORDER" class="java.math.BigDecimal"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="CODIGO" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.math.BigDecimal"/>
	<field name="VALOR_UNITARIO" class="java.math.BigDecimal"/>
	<field name="VALOR_TOTAL" class="java.math.BigDecimal"/>
	<field name="APROVADO" class="java.lang.String"/>
	<group name="por_orcamento">
		<groupExpression><![CDATA[$F{NUMERO_ORCAMENTO}]]></groupExpression>
		<groupHeader>
			<band height="30">
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="45" height="10" uuid="e745e78c-c47d-4c0b-8a06-06c97f535236">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Orçamento:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="42" y="0" width="42" height="10" uuid="196a77ef-49c6-434e-a6eb-731c7b82ea89">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NUMERO_ORCAMENTO}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="0" y="10" width="555" height="20" uuid="740f5593-4d2e-4106-a348-68f188e0d520">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<staticText>
						<reportElement mode="Transparent" x="56" y="0" width="294" height="10" uuid="2702ae21-2ec9-4411-8718-ad8b4ee3ee37">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Descrição das Recomendações // Peças e Serviços Necessários]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="42" y="0" width="14" height="20" uuid="5dbf0e44-dd54-401a-b56a-e71439bc6586">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[TP]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="126" y="10" width="224" height="10" uuid="f1afdf1f-6700-4380-a5b3-93ea25a641a3">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Descrição do Serviço]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="42" height="20" uuid="ff021c48-c57d-4abc-88d4-39bd64372f58">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Item]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="56" y="10" width="70" height="10" uuid="119e0863-6379-4377-a798-04b9e3f4ea6a">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Código]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="448" y="0" width="62" height="20" uuid="f514d5f9-ab26-4ba7-ba69-81255a2ea265">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Valor Total]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="350" y="0" width="41" height="20" uuid="24f7575b-cd68-413e-9ae9-aedecda2bd98">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Quantidade]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="391" y="0" width="57" height="20" uuid="69311bc6-8ba0-4caf-a99a-404d66a9d247">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Valor Unitário]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="510" y="0" width="45" height="20" uuid="7f180b6b-615e-4274-ad11-725d9407f9ff">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Aprovado]]></text>
					</staticText>
				</frame>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="10">
				<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				<line>
					<reportElement positionType="Float" x="0" y="0" width="555" height="1" uuid="f6633d6d-b46e-4092-8864-5182c673d8eb">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="por_reclamacao">
		<groupExpression><![CDATA[$F{ITEM}]]></groupExpression>
		<groupHeader>
			<band height="10">
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="42" height="10" uuid="34921ce6-6eca-4d3d-918c-26f137cc5e79">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}+ ".0"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="42" y="0" width="14" height="10" uuid="ef1cab7f-4f21-4793-b503-72cebefe5899">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["--"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="56" y="0" width="499" height="10" uuid="4348fd4d-7699-4162-a507-6e9a65dff88a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_RECLAMACAO}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="10" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement key="" isPrintRepeatedValues="false" x="0" y="0" width="555" height="10" uuid="209444c4-2584-47b0-9d73-b61e06668e60">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="42" height="10" uuid="1ee2c5b3-b344-44b3-acff-80f4b861d040">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}+ "." +$F{ITEM_ORDER}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="42" y="0" width="14" height="10" uuid="26d08a45-b633-4abb-835a-12ca9faf6b2d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TIPO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement mode="Transparent" x="350" y="0" width="41" height="10" uuid="68be0752-985e-4fc9-bb5c-950ca32a5cd3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="126" y="0" width="224" height="10" uuid="a13926a1-4b52-4506-b291-87e13eac50b1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="448" y="0" width="62" height="10" uuid="92f1fac7-1963-4a80-99f5-cfec035bae5e">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" rightPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{VALOR_TOTAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="56" y="0" width="70" height="10" uuid="e0889b48-2e93-43a1-9db9-20af89352e5c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CODIGO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="391" y="0" width="57" height="10" uuid="1913896c-d34c-466d-85c2-35664dcc82d4">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" rightPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{VALOR_UNITARIO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="393" y="0" width="10" height="10" forecolor="#3B3B3B" uuid="9fe13e63-b43e-4128-9f4b-27ab3ee1c1e2">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="451" y="0" width="10" height="10" forecolor="#3B3B3B" uuid="0c4ee35b-a1f0-4be9-ab17-bfe01a2a50f6">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement mode="Transparent" x="510" y="0" width="45" height="10" uuid="0d635532-a07c-4a54-88f2-dba2964fd6c3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{APROVADO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="1"/>
	</columnFooter>
</jasperReport>
