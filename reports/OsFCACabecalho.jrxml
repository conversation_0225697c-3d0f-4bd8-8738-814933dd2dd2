<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsFCACabecalho" pageWidth="555" pageHeight="54" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="f2f5d3a6-1ce2-44c8-9781-45d4bedda9b9">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT 
       E.COD_EMPRESA,
       E.EMPRESA_NOME_COMPLETO,
       E.CGC AS CNPJ,
       E.INSCRICAO_ESTADUAL AS INSCRICAO_ESTADUAL,       
       E.INSCRICAO_MUNICIPAL AS IM,
       (E.RUA || ', ' || E.FACHADA) AS ENDERECO,
       E.COMPLEMENTO AS COMPLEMENTO,
       E.BAIRRO AS BAIRRO,
       E.CEP AS CEP,
       E.CIDADE AS CIDADE,    
       E.ESTADO AS ESTADO,
       TRANSLATE(E.FONE, ' -', ' ') AS FONE,
       TRANSLATE(E.FAX, ' -', ' ') AS FAX,
       C.ENDERECO_ELETRONICO AS ENDERECO_ELETRONICO,
       CD.EMPRESA_SITE AS EMPRESA_SITE,
       NVL(E.FUSO_HORARIO,0) AS FUSO_HORARIO,
       EMPRESA_LOGO.LOGO AS EMPRESA_LOGO,
       FABRICA_LOGO.LOGO AS FABRICA_LOGO
  FROM EMPRESAS E,CLIENTES C,CLIENTE_DIVERSO CD,
        
        (SELECT EL.COD_EMPRESA, EL.LOGO AS LOGO
        FROM EMPRESA_LOGO EL) EMPRESA_LOGO,
        
        (SELECT FL.COD_EMPRESA, FL.LOGO AS LOGO
        FROM FABRICA_LOGO FL) FABRICA_LOGO
        
 WHERE E.COD_CLIENTE=C.COD_CLIENTE
   AND C.COD_CLIENTE=CD.COD_CLIENTE
   AND E.COD_EMPRESA = EMPRESA_LOGO.COD_EMPRESA (+)
   AND E.COD_EMPRESA = FABRICA_LOGO.COD_EMPRESA (+)
   AND E.COD_EMPRESA = $P{COD_EMPRESA}]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="EMPRESA_NOME_COMPLETO" class="java.lang.String"/>
	<field name="CNPJ" class="java.lang.String"/>
	<field name="INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="IM" class="java.lang.String"/>
	<field name="ENDERECO" class="java.lang.String"/>
	<field name="COMPLEMENTO" class="java.lang.String"/>
	<field name="BAIRRO" class="java.lang.String"/>
	<field name="CEP" class="java.lang.String"/>
	<field name="CIDADE" class="java.lang.String"/>
	<field name="ESTADO" class="java.lang.String"/>
	<field name="FONE" class="java.lang.String"/>
	<field name="FAX" class="java.lang.String"/>
	<field name="ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="EMPRESA_SITE" class="java.lang.String"/>
	<field name="FUSO_HORARIO" class="java.lang.Double"/>
	<field name="EMPRESA_LOGO" class="java.io.InputStream"/>
	<field name="FABRICA_LOGO" class="java.io.InputStream"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="54" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="54" uuid="2a8e9426-a359-4195-b6f1-da4924c41fd5">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement x="151" y="5" width="248" height="11" uuid="23e7889b-0025-47e9-a958-dfa0ccb44dfd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Nome Concessionária: " + $F{EMPRESA_NOME_COMPLETO}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="1" y="1" width="144" height="53" uuid="5ac1563a-9ee3-4e72-8697-f9ee478d4e33">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<image scaleImage="FillFrame">
						<reportElement x="1" y="1" width="143" height="52" uuid="7ead5868-9764-4629-a8f7-42da7792fc77">
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$F{EMPRESA_LOGO}]]></imageExpression>
					</image>
				</frame>
				<frame>
					<reportElement x="406" y="0" width="149" height="54" uuid="6fba9d2f-cc40-48df-8381-bc60990dfd34">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<image scaleImage="FillFrame">
						<reportElement x="1" y="1" width="147" height="52" uuid="23b97b3a-5a6f-4b12-8763-3f4212c7d483">
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$F{FABRICA_LOGO}]]></imageExpression>
					</image>
				</frame>
				<textField>
					<reportElement x="151" y="16" width="248" height="11" uuid="ce46fd63-24e1-46a8-acb5-070f5570f822">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Endereço: " + $F{ENDERECO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="151" y="27" width="248" height="11" uuid="7882417c-df3b-4d3d-8d23-1bca5d0f733e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["CNPJ: " + $F{CNPJ}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="151" y="38" width="248" height="11" uuid="df572e42-6bc1-48f1-969f-e8946653af8c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Telefone: " + $F{FONE}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</title>
</jasperReport>
