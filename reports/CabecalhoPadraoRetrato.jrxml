<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.19.0.final using JasperReports Library version 6.19.0-646c68931cebf1a58bc65c4359d1f0ca223c5e94  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CabecalhoPadraoRetrato" pageWidth="555" pageHeight="810" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a5d195c3-46e2-4e2e-9575-573e21d1a687">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="DataAdapter.jrdax"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="false"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="715"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="271"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="182"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="807"/>
	<property name="com.jaspersoft.studio.report.description" value=""/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString>
		<![CDATA[SELECT
	   EMPRESAS.NOME NOME_EMPRESA,
	   CASE
         WHEN (NOT EMPRESAS.FACHADA IS NULL) AND
              (NOT EMPRESAS.COMPLEMENTO IS NULL) THEN
          SUBSTR(EMPRESAS.RUA, 1, 50) || ', ' ||
          SUBSTR(EMPRESAS.FACHADA, 1, 15) || ', ' ||
          SUBSTR(EMPRESAS.COMPLEMENTO, 1, 15)
         WHEN (NOT EMPRESAS.FACHADA IS NULL) THEN
          SUBSTR(EMPRESAS.RUA, 1, 50) || ', ' ||
          SUBSTR(EMPRESAS.FACHADA, 1, 15)
         ELSE
          SUBSTR(EMPRESAS.RUA, 1, 50)
       END EMPRESA_ENDERECO,
       CASE
         WHEN (NOT EMPRESAS.CEP IS NULL) AND (NOT EMPRESAS.CIDADE IS NULL) AND
              (NOT EMPRESAS.ESTADO IS NULL) AND
              (NOT EMPRESAS.BAIRRO IS NULL) THEN
          EMPRESAS.CEP || ' ' || EMPRESAS.CIDADE || '-' || EMPRESAS.ESTADO || ', ' ||
          EMPRESAS.BAIRRO
         WHEN (NOT EMPRESAS.CEP IS NULL) AND (NOT EMPRESAS.CIDADE IS NULL) AND
              (NOT EMPRESAS.ESTADO IS NULL) THEN
          EMPRESAS.CEP || ' ' || EMPRESAS.CIDADE || '-' || EMPRESAS.ESTADO
         WHEN (NOT EMPRESAS.CEP IS NULL) AND (NOT EMPRESAS.CIDADE IS NULL) AND
              (NOT EMPRESAS.BAIRRO IS NULL) THEN
          EMPRESAS.CEP || ' ' || EMPRESAS.CIDADE || ', ' || EMPRESAS.BAIRRO
         WHEN (NOT EMPRESAS.CEP IS NULL) AND (NOT EMPRESAS.CIDADE IS NULL) THEN
          EMPRESAS.CEP || ' ' || EMPRESAS.CIDADE
         WHEN (NOT EMPRESAS.CEP IS NULL) THEN
          EMPRESAS.CEP
         ELSE
          ' '
       END EMPRESA_ENDERECO1,
	   EMPRESAS.INSCRICAO_MUNICIPAL EMPRESAS_INSCRICAO_MUNICIPAL,
	   EMPRESAS.INSCRICAO_ESTADUAL EMPRESA_INSCRICAO_ESTADUAL,
	   NVL(EMPRESAS.FONE, ' ') EMPRESA_FONE,
	   EMPRESAS.CGC EMPRESA_CGC,
	   $P{NUMERO_OS} NUMERO_OS,
	   $P{COD_EMPRESA} COD_EMPRESA
FROM EMPRESAS
WHERE 1=1
	AND EMPRESAS.COD_EMPRESA = $P{COD_EMPRESA}]]>
	</queryString>
	<field name="NOME_EMPRESA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="NOME_EMPRESA"/>
		<property name="com.jaspersoft.studio.field.label" value="NOME_EMPRESA"/>
	</field>
	<field name="EMPRESA_ENDERECO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="EMPRESA_ENDERECO"/>
		<property name="com.jaspersoft.studio.field.label" value="EMPRESA_ENDERECO"/>
	</field>
	<field name="EMPRESA_ENDERECO1" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="EMPRESA_ENDERECO1"/>
		<property name="com.jaspersoft.studio.field.label" value="EMPRESA_ENDERECO1"/>
	</field>
	<field name="EMPRESAS_INSCRICAO_MUNICIPAL" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="EMPRESAS_INSCRICAO_MUNICIPAL"/>
		<property name="com.jaspersoft.studio.field.label" value="EMPRESAS_INSCRICAO_MUNICIPAL"/>
	</field>
	<field name="EMPRESA_INSCRICAO_ESTADUAL" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="EMPRESA_INSCRICAO_ESTADUAL"/>
		<property name="com.jaspersoft.studio.field.label" value="EMPRESA_INSCRICAO_ESTADUAL"/>
	</field>
	<field name="EMPRESA_FONE" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="EMPRESA_FONE"/>
		<property name="com.jaspersoft.studio.field.label" value="EMPRESA_FONE"/>
	</field>
	<field name="EMPRESA_CGC" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="EMPRESA_CGC"/>
		<property name="com.jaspersoft.studio.field.label" value="EMPRESA_CGC"/>
	</field>
	<field name="NUMERO_OS" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="NUMERO_OS"/>
		<property name="com.jaspersoft.studio.field.label" value="NUMERO_OS"/>
	</field>
	<field name="COD_EMPRESA" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="COD_EMPRESA"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_EMPRESA"/>
	</field>
	<title>
		<band height="67" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="275" y="2" width="278" height="22" backcolor="#D9D9D9" uuid="3c86bcb0-c63f-415a-9456-c388f8ecc1d8">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Calibri" size="16" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME_EMPRESA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="275" y="28" width="176" height="12" forecolor="#3668FF" uuid="1f914509-24aa-4455-893c-3c396fc08278"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{EMPRESA_ENDERECO}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Opaque" x="459" y="28" width="94" height="12" forecolor="#3668FF" uuid="11dc90da-469a-49c5-ab2c-996c9e15ed05"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["I.E: " + $F{EMPRESA_INSCRICAO_ESTADUAL}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Opaque" x="459" y="40" width="94" height="14" forecolor="#3668FF" uuid="1e415798-8e5a-4910-9f49-ad2d3b1a5242"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["I.M: " + $F{EMPRESAS_INSCRICAO_MUNICIPAL}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="274" y="40" width="177" height="14" forecolor="#3668FF" uuid="24c60e54-52aa-4e09-b85f-bbd25eff2a32"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{EMPRESA_ENDERECO1}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Opaque" x="274" y="53" width="94" height="14" forecolor="#3668FF" uuid="2323439b-d1d4-4c54-bfe5-ff49ff0cb61c"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Fone: " + $F{EMPRESA_FONE}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Opaque" x="459" y="53" width="94" height="14" forecolor="#3668FF" uuid="ec91a511-9c4e-4e37-9820-9ad2cc5650b1"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="8" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["CNPJ: " + $F{EMPRESA_CGC}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="1" y="1" width="272" height="66" isRemoveLineWhenBlank="true" uuid="e6b46a5a-93d8-41cc-beee-41c77135cf38">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsPadraoLogo.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</title>
</jasperReport>
