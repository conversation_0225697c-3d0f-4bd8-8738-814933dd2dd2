<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklistLogo" columnCount="4" printOrder="Horizontal" pageWidth="555" pageHeight="842" columnWidth="138" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="92c3c34a-6a74-4816-b251-78e573d7db94">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Desenvolvimento"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT 
    CASE
      WHEN OS_TIPOS_EMPRESAS.TIPO = 'V' THEN
        OS_TIPOS_EMPRESAS.LOGO
      ELSE
        NVL(OS_TIPOS_EMPRESAS.LOGO, EL.LOGO)
      END LOGO       
  FROM OS,
       EMPRESAS E,
       OS_TIPOS_EMPRESAS,
       EMPRESA_LOGO EL
 WHERE OS.COD_EMPRESA = E.COD_EMPRESA
   AND OS.COD_EMPRESA = OS_TIPOS_EMPRESAS.COD_EMPRESA(+)
   AND OS.TIPO = OS_TIPOS_EMPRESAS.TIPO(+)
   AND OS.COD_EMPRESA = EL.COD_EMPRESA
   AND OS.NUMERO_OS = $P{NUMERO_OS} 
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}]]>
	</queryString>
	<field name="LOGO" class="java.io.InputStream"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band/>
	</title>
	<detail>
		<band height="78" splitType="Stretch">
			<image>
				<reportElement stretchType="RelativeToBandHeight" x="3" y="3" width="270" height="71" uuid="03819333-a478-4980-a3ef-0b2cd8922310"/>
				<imageExpression><![CDATA[$F{LOGO}]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>
