<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsJaguarLandSubServicos" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryNewPage="true" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="FIELD_NULL" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232335.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\29881\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH Q_OS AS
 (SELECT OS.COD_EMPRESA,
         OS_AGENDA.DATA_AGENDADA,
         TO_CHAR(OS_AGENDA.DATA_AGENDADA, 'DD/MM/YYYY HH24:MI') AS D_AG_STG,
         OS.STATUS_OS,
         OS.NUMERO_OS,
         OS.COD_OS_AGENDA,
         ABS(OS.NUMERO_OS) AS ABS_OSNUM,
         OS.COD_CLIENTE,
         OS.CLIENTE_RAPIDO,
         OS.TIPO_ENDERECO,
         OS.OBSERVACAO,
         OS.EXTENDIDA,
         OS.SEGURADORA,
         OS.DATA_EMISSAO,
         (TO_DATE(OS.DATA_EMISSAO, 'DD/MM/YYYY') || ' AS ' ||
         OS.HORA_EMISSAO) AS DH_EMISSAO,
         DECODE(OS.DATA_LIBERADO,
                NULL,
                '',
                (TO_DATE(OS.DATA_LIBERADO, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_LIBERADO)) AS DH_LIBERADO,
         DECODE(OS.DATA_ENCERRADA,
                NULL,
                '',
                (TO_DATE(OS.DATA_ENCERRADA, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_ENCERRADA)) AS DH_ENCERRADO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2) AS DIA_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2) AS MES_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4) AS ANO_EMISSAO,
         OS.HORA_EMISSAO,
         OS.HORA_ENCERRADA,
         OS.DATA_ENCERRADA,
         OS.HORA_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 1, 2) AS HORA24_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 4, 2) AS MINUTO_PROMETIDA,
         
         OS.DATA_PROMETIDA,
         OS.DATA_PROMETIDA_REVISADA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2) AS DIA_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2) AS MES_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4) AS ANO_PROMETIDA,
         
         OS.VALOR_SERVICOS_BRUTO,
         OS.VALOR_ITENS_BRUTO,
         OS.DESCONTOS_SERVICOS,
         OS.DESCONTOS_ITENS,
         (OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS TOTAL_OS_SERVICOS,
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS) AS TOTAL_OS_ITENS,
         (OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO) AS TOTAL_OS_BRUTO,
         (OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS) AS TOTAL_OS_DESCONTO,
         ((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) +
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS)) AS TOTAL_OS,
         OS.COD_SEGURADORA,
         
         OS_DADOS_VEICULOS.ANO,
         OS_DADOS_VEICULOS.HORIMETRO,
         OS_DADOS_VEICULOS.PRISMA,
         OS_DADOS_VEICULOS.DATA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2) AS DIA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2) AS MES_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4) AS ANO_VENDA,
         OS_DADOS_VEICULOS.COMBUSTIVEL,
         OS_DADOS_VEICULOS.COR_EXTERNA,
         OS_DADOS_VEICULOS.PLACA,
         OS_DADOS_VEICULOS.KM,
         OS_DADOS_VEICULOS.CHASSI,
         OS_DADOS_VEICULOS.NUMERO_MOTOR,
         OS_DADOS_VEICULOS.NUMERO_RENAVAM,
         OS_DADOS_VEICULOS.SERIE,
         OS_DADOS_VEICULOS.COD_CONCESSIONARIA,
         OS_DADOS_VEICULOS.ESTADO_PINTURA,
         OS_DADOS_VEICULOS.JOGO_FERRAMENTAS,
         OS_DADOS_VEICULOS.ELASTICOS,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_D,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_E,
         OS_DADOS_VEICULOS.FLANELA,
         OS.TIPO,
         OS_TIPOS.DESCRICAO AS TIPO_DESCRICAO,
         OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO AS TIPO_COM_DESCRICAO,
         OS_TIPOS.GARANTIA,
         OS_TIPOS.REVISAO_GRATUITA,
         OS_TIPOS.INTERNO,
         OS_TIPOS.COD_CLIENTE AS CLIENTE_DO_TIPO,
         OS_TIPOS.OUTRO_CONCESSIONARIA,
         OS.NOME AS CONSULTOR,
         EMPRESAS_USUARIOS.NOME_COMPLETO AS CONSULTOR_COMPLETO,
         PRODUTOS.DESCRICAO_PRODUTO,
         PRODUTOS_MODELOS.DESCRICAO_MODELO,
         (PRODUTOS.DESCRICAO_PRODUTO || ' - ' ||
         PRODUTOS_MODELOS.DESCRICAO_MODELO) DESC_PROD_MOD,
         PRODUTOS_MODELOS.LINHA,
         MARCAS.DESCRICAO_MARCA,
         CONCESSIONARIAS.NOME CONCESSIONARIA_NOME,
         CONCESSIONARIAS.UF CONCESSIONARIA_UF,
         CONCESSIONARIAS.CIDADE CONCESSIONARIA_CIDADE,
         CONCESSIONARIAS.BAIRRO CONCESSIONARIA_BAIRRO,
         CONCESSIONARIAS.ENDERECO CONCESSIONARIA_RUA,
         CONCESSIONARIAS.CEP CONCESSIONARIA_CEP,
         CONCESSIONARIAS.CODIGO_PADRAO CONCESSIONARIA_CODIGO,
         UF_CONCESSIONARIA.DESCRICAO CONCESSIONARIA_ESTADO,
         
         (CASE
           WHEN NVL(OS.OS_ORIGEM_RETORNO, 0) > 0 THEN
            'S'
           ELSE
            'N'
         END) AS AG_RETORNO,
         NVL(OS_AGENDA.CLIENTE_AGUARDA, 'N') AS AG_CLIENTE_AGUARDA,
         NVL(OS_AGENDA.VEICULO_PLATAFORMA, 'N') AS AG_VEICULO_PLATAFORMA,
         NVL(OS_AGENDA.TAXI, 'N') AS AG_TAXI,
         NVL(OS_AGENDA.BLINDADO, 'N') AS AG_BLINDADO,
         NVL(OS_AGENDA.TESTE_RODAGEM, 'N') AS AG_TESTE_RODAGEM,
         NVL(OS_AGENDA.LEVAR_PECAS_SUBSTITUIDAS, 'N') AS AG_LEVAR_PECAS_SUBSTITUIDAS,
         NVL(OS_AGENDA.LAVAR_VEICULO, 'N') AS AG_LAVAR_VEICULO,
         NVL(OS_AGENDA.VEICULO_MODIFICADO, 'N') AS VEICULO_MODIFICADO,
         NVL(OS_AGENDA.DDW_GARANTIA, 'NÃO EXISTENTE') AS DDW_GARANTIA,
         OS.HORA_PROMETIDA_REVISADA,
         OS_TIPOS.TIPO_FABRICA,
         DECODE(OS_AGENDA.REC_INTERATIVA,
                'S',
                'VEÍCULO COM RECEPÇÃO INTERATIVA',
                'N',
                '') REC_INTERATIVA,
         OS_AGENDA.COD_TIPO_IMOBILIZADO,
         DECODE(OS_AGENDA.COD_TIPO_IMOBILIZADO,
                NULL,
                '',
                'VEICULO IMOBILIZADO: ' || TIPO_IMOBILIZADO.DESCRICAO) AS IMOBILIZADO,
         OS_AGENDA.REC_INTERATIVA AS RECEPCAO_INTERATIVA,
         DECODE(OS_AGENDA.COD_TIPO_MOBILIDADE,
                NULL,
                'SEM MOBILIDADE',
                TIPO_MOBILIDADE.DESCRICAO) MOBILIDADE_DESCRICAO,
         NVL((SELECT 'S'
               FROM JLR_CHASSI_BLINDADO J
              WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
                AND ROWNUM < 2),
             'N') AS BLINDADO,
         (SELECT J.NOME_BLINDADORA
            FROM JLR_CHASSI_BLINDADO J
           WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
             AND ROWNUM < 2) AS BLINDADORA
  
    FROM OS,
         OS_DADOS_VEICULOS,
         OS_AGENDA,
         EMPRESAS_USUARIOS,
         VW_OS_TIPOS       OS_TIPOS,
         CONCESSIONARIAS,
         PRODUTOS,
         PRODUTOS_MODELOS,
         MARCAS,
         UF                UF_CONCESSIONARIA,
         TIPO_IMOBILIZADO,
         TIPO_MOBILIDADE
   WHERE OS.TIPO = OS_TIPOS.TIPO
     AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA(+)
     AND OS.NOME = EMPRESAS_USUARIOS.NOME
     AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS(+)
     AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA(+)
     AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+)
     AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO
        
     AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA(+)
     AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS(+)
        
     AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
     AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
     AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
     AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF(+)
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_TIPO_IMOBILIZADO =
         TIPO_IMOBILIZADO.COD_TIPO_IMOBILIZADO(+)
     AND OS_AGENDA.COD_TIPO_MOBILIDADE = TIPO_MOBILIDADE.COD_TIPO_MOBIL(+)),
Q_EMPRESA AS
 (SELECT EMPRESAS.COD_EMPRESA,
         EMPRESAS.NOME NOME_EMPRESA,
         EMPRESAS.CGC,
         EMPRESAS.FACHADA,
         EMPRESAS.ESTADO AS UF,
         (TRIM(EMPRESAS.CIDADE) || ' - ' || TRIM(EMPRESAS.ESTADO)) AS CIDADE,
         EMPRESAS.BAIRRO,
         EMPRESAS.COMPLEMENTO,
         (EMPRESAS.RUA || ', ' || EMPRESAS.FACHADA) AS RUA,
         EMPRESAS.FONE,
         EMPRESAS.FAX,
         EMPRESAS.CEP,
         EMPRESAS.INSCRICAO_MUNICIPAL,
         EMPRESAS.INSCRICAO_SUBSTITUICAO,
         UF.DESCRICAO ESTADO,
         EMPRESAS.INSCRICAO_ESTADUAL,
         TRUNC(SYSDATE) AS DATA_ATUAL,
         SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'), 1, 5) AS HORA_ATUAL_STR,
         CLIENTES.ENDERECO_ELETRONICO AS EMAIL,
         EMPRESA_LOGO.LOGO
    FROM EMPRESAS, EMPRESA_LOGO, UF, CLIENTES
   WHERE EMPRESAS.COD_EMPRESA = $P{COD_EMPRESA}
     AND EMPRESAS.COD_EMPRESA = EMPRESA_LOGO.COD_EMPRESA(+)
     AND EMPRESAS.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND UF.UF = EMPRESAS.ESTADO),
Q_CLIENTE AS
 (SELECT CLIENTE_DIVERSO.COD_CLIENTE,
         
         CLIENTE_DIVERSO.NOME AS NOME,
         CLIENTE_DIVERSO.RG   AS RG,
         
         ('(' || CLIENTES.PREFIXO_RES || ') ' || CLIENTES.TELEFONE_RES) AS TELEFONE_RES,
         CLIENTES.PREFIXO_RES,
         ('(' || CLIENTES.PREFIXO_COM || ') ' || CLIENTES.TELEFONE_COM) AS TELEFONE_COM,
         CLIENTES.PREFIXO_COM,
         ('(' || CLIENTES.PREFIXO_FAX || ') ' || CLIENTES.TELEFONE_FAX) AS TELEFONE_FAX,
         CLIENTES.PREFIXO_FAX,
         ('(' || CLIENTES.PREFIXO_CEL || ') ' || CLIENTES.TELEFONE_CEL) AS TELEFONE_CEL,
         CLIENTES.PREFIXO_CEL,
         
         NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL) AS INSC_ESTAD,
         
         CLIENTE_DIVERSO.CGC,
         CLIENTE_DIVERSO.CPF,
         CLIENTES.COD_CLASSE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.UF,
                2,
                CLIENTES.UF_RES,
                3,
                CLIENTES.UF_COM,
                4,
                CLIENTES.UF_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.UF,
                NULL) UF,
         DECODE(OS.TIPO_ENDERECO,
                1,
                UF_DIVERSO.DESCRICAO,
                2,
                UF_RES.DESCRICAO,
                3,
                UF_COM.DESCRICAO,
                4,
                UF_COBRANCA.DESCRICAO,
                5,
                UF_INSCRICAO.DESCRICAO,
                NULL) ESTADO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CIDADES_DIV.DESCRICAO,
                2,
                CIDADES_RES.DESCRICAO,
                3,
                CIDADES_COM.DESCRICAO,
                4,
                CIDADES_COBRANCA.DESCRICAO,
                5,
                ENDERECO_POR_INSCRICAO.CIDADE,
                NULL) CIDADE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.BAIRRO,
                2,
                CLIENTES.BAIRRO_RES,
                3,
                CLIENTES.BAIRRO_COM,
                4,
                CLIENTES.BAIRRO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.BAIRRO,
                NULL) BAIRRO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                TRANSLATE(TO_CHAR(CLIENTE_DIVERSO.CEP / 1000, '00000.000'),
                          ',.',
                          '.-'),
                2,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_RES / 1000, '00000.000'),
                          ',.',
                          '.-'),
                3,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_COM / 1000, '00000.000'),
                          ',.',
                          '.-'),
                4,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_COBRANCA / 1000, '00000.000'),
                          ',.',
                          '.-'),
                5,
                TRANSLATE(TO_CHAR(ENDERECO_POR_INSCRICAO.CEP / 1000,
                                  '00000.000'),
                          ',.',
                          '.-'),
                NULL) CEP,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.ENDERECO,
                2,
                CLIENTES.RUA_RES,
                3,
                CLIENTES.RUA_COM,
                4,
                CLIENTES.RUA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.RUA,
                NULL) RUA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.COMPLEMENTO,
                2,
                CLIENTES.COMPLEMENTO_RES,
                3,
                CLIENTES.COMPLEMENTO_COM,
                4,
                CLIENTES.COMPLEMENTO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.COMPLEMENTO,
                NULL) COMPLEMENTO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                NULL,
                2,
                CLIENTES.FACHADA_RES,
                3,
                CLIENTES.FACHADA_COM,
                4,
                CLIENTES.FACHADA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.FACHADA,
                NULL) FACHADA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.FONE_CONTATO,
                2,
                CLIENTES.TELEFONE_RES,
                3,
                CLIENTES.TELEFONE_COM,
                4,
                CLIENTES.TELEFONE_CEL,
                5,
                ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
                NULL) FONE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
                2,
                CLIENTES.PREFIXO_RES,
                3,
                CLIENTES.PREFIXO_COM,
                4,
                CLIENTES.PREFIXO_CEL,
                5,
                ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
                NULL) PREFIXO,
         
         CLIENTES.ENDERECO_ELETRONICO,
         CLIENTES.EMAIL_NFE,
         NVL(CLIENTES.EMAIL_TRABALHO, CLIENTES.EMAIL2) AS EMAIL2
  
    FROM OS,
         CLIENTE_DIVERSO,
         CLIENTES,
         ENDERECO_POR_INSCRICAO,
         CIDADES                CIDADES_RES,
         CIDADES                CIDADES_COM,
         CIDADES                CIDADES_COBRANCA,
         CIDADES                CIDADES_DIV,
         UF                     UF_DIVERSO,
         UF                     UF_RES,
         UF                     UF_COM,
         UF                     UF_COBRANCA,
         UF                     UF_INSCRICAO
   WHERE OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE
     AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES(+)
     AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES(+)
     AND OS.INSCRICAO_ESTADUAL =
         ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL(+)
     AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF(+)
     AND CLIENTES.UF_RES = UF_RES.UF(+)
     AND CLIENTES.UF_COM = UF_COM.UF(+)
     AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF(+)
     AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF(+)
        
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}),
Q_HIST_ORC AS
 (SELECT ROW_NUMBER() OVER(ORDER BY ORC.NUMERO_OS) AS NUMERO_LINHA,
         ORC.NUMERO_OS,
         ORC.COD_EMPRESA,
         COUNT(ORC.NUMERO_OS) NUMERO_ORCAMENTOS,
         LISTAGG(ORC.NUMERO_ORCAMENTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS ORCAMENTOS,
         LISTAGG(ORCF.VALOR_BRUTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS VALOR_ORCAMENTO_BRUTO,
         SUM(ORCF.VALOR_BRUTO) TOTAL
    FROM OS_ORCAMENTOS ORC
    LEFT JOIN OS_ORC_FECHAMENTO ORCF
      ON ORCF.NUMERO_OS = ORC.NUMERO_ORCAMENTO
     AND ORCF.COD_EMPRESA = ORC.COD_EMPRESA
   WHERE ORC.NUMERO_OS = $P{NUMERO_OS}
     AND ORC.COD_EMPRESA = $P{COD_EMPRESA}
   GROUP BY ORC.NUMERO_OS, ORC.COD_EMPRESA),

Q_HISTORICO AS
 (SELECT LISTAGG(HISTORICO_OS.N_OS, ', ') WITHIN GROUP(ORDER BY HISTORICO_OS.N_OS) AS HISTORICO_OS,
         HISTORICO_OS.CHASSI,
         HISTORICO_OS.COD_EMPRESA
    FROM (SELECT DISTINCT TO_CHAR(OSD.NUMERO_OS) AS N_OS,
                          OSD.CHASSI,
                          OSD.COD_EMPRESA
            FROM OS_DADOS_VEICULOS OSD, OS, Q_OS
           WHERE OSD.COD_EMPRESA = Q_OS.COD_EMPRESA
             AND OS.NUMERO_OS = OSD.NUMERO_OS
             AND OS.STATUS_OS = 1
             AND OS.NUMERO_OS > 0
             AND OSD.CHASSI = Q_OS.CHASSI
                
             AND ROWNUM < 11
           ORDER BY OSD.NUMERO_OS DESC) HISTORICO_OS
   GROUP BY HISTORICO_OS.CHASSI, HISTORICO_OS.COD_EMPRESA),

QRYDADOSVEICULOS AS
 (SELECT CF.ANO, CF.DATA_COMPRA, CF.CHASSI, CF.PLACA
    FROM CLIENTES_FROTA CF, Q_OS
   WHERE CF.VENDIDO = 'N'
     AND ROWNUM <= 1
     AND ((Q_OS.CHASSI IS NOT NULL AND CF.CHASSI = Q_OS.CHASSI) OR
         (Q_OS.PLACA IS NOT NULL AND CF.PLACA = Q_OS.PLACA))
   ORDER BY CF.DATA_COMPRA DESC),

Q_PARM_SYS as
 (SELECT PARM_SYS.COD_EMPRESA,
         TIPO_TEMPO,
         TIPO_VALOR_OS,
         cod_cliente_balcao,
         tipo_concessionaria,
         CONCESSIONARIAS.codigo_padrao,
         versao2,
         cod_cliente_fabrica_garantia,
         PARM_SYS2.HONDA_IMP_NUMERO_REQUISICAO,
         PARM_SYS3.TERMO_OS_JLR
    FROM PARM_SYS, PARM_SYS2, PARM_SYS3, CONCESSIONARIAS
   WHERE (PARM_SYS.CONCESSIONARIA_NUMERO =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+))
     AND (PARM_SYS2.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS3.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS.COD_EMPRESA = $P{COD_EMPRESA})),

Q_ASSINATURA as
 (select OS_AGENDA.NUMERO_OS,
         OS_AGENDA.COD_EMPRESA,
         OS_AGENDA.SIGNATURE As CLIENTE_RECEPCAO,
         MOB_OS_ASSINATURA_ENTREGA.ASSINATURA_CLIENTE AS CLIENTE_ENTREGA,
         MOB_OS_ASSINATURA_ENTREGA.DATA_ASSINATURA_CLIENTE AS CLIENTE_ENTREGA_DT,
         MOB_OS_ASSINATURA_RECEPCAO.ASSINATURA AS CONSULTOR_RECEPCAO,
         MOB_OS_ASSINATURA_RECEPCAO.DATA_ASSINATURA AS CONSULTOR_RECEPCAO_DT,
         MOB_OS_ASSINATURA_ENTREGA.ASSINATURA AS CONSULTOR_ENTREGA,
         MOB_OS_ASSINATURA_ENTREGA.DATA_ASSINATURA AS CONSULTOR_ENTREGA_DT
    from OS_AGENDA, MOB_OS_ASSINATURA MOB_OS_ASSINATURA_ENTREGA, MOB_OS_ASSINATURA MOB_OS_ASSINATURA_RECEPCAO
   where OS_AGENDA.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_EMPRESA = $P{COD_EMPRESA}
   AND OS_AGENDA.NUMERO_OS = MOB_OS_ASSINATURA_ENTREGA.numero_os (+)
   AND OS_AGENDA.COD_EMPRESA = MOB_OS_ASSINATURA_ENTREGA.cod_empresa (+)
   AND MOB_OS_ASSINATURA_ENTREGA.APLICACAO(+) = 'E' 
   AND OS_AGENDA.NUMERO_OS = MOB_OS_ASSINATURA_RECEPCAO.numero_os (+)
   AND OS_AGENDA.COD_EMPRESA = MOB_OS_ASSINATURA_RECEPCAO.cod_empresa (+)
   AND MOB_OS_ASSINATURA_RECEPCAO.APLICACAO(+) = 'R')

SELECT Q_EMPRESA.NOME_EMPRESA           AS Q_EMPRESA_NOME_EMPRESA,
       Q_EMPRESA.RUA                    AS Q_EMPRESA_RUA,
       Q_EMPRESA.CIDADE                 AS Q_EMPRESA_CIDADE,
       Q_EMPRESA.CEP                    AS Q_EMPRESA_CEP,
       Q_EMPRESA.FONE                   AS Q_EMPRESA_FONE,
       Q_EMPRESA.INSCRICAO_ESTADUAL     AS Q_EMPRESA_INSCRICAO_ESTADUAL,
       Q_EMPRESA.CGC                    AS Q_EMPRESA_CGC,
       Q_OS.TIPO                        AS Q_OS_TIPO,
       Q_OS.D_AG_STG                    AS Q_OS_D_AG_STG,
       Q_OS.CONSULTOR                   AS Q_OS_CONSULTOR,
       Q_OS.PRISMA                      AS Q_OS_PRISMA,
       Q_OS.DATA_PROMETIDA              AS Q_OS_DATA_PROMETIDA,
       Q_OS.DATA_PROMETIDA_REVISADA     AS Q_OS_DATA_PROMETIDA_REVISADA,
       Q_OS.AG_RETORNO                  AS Q_OS_AG_RETORNO,
       Q_OS.AG_CLIENTE_AGUARDA          AS Q_OS_AG_CLIENTE_AGUARDA,
       Q_OS.RECEPCAO_INTERATIVA         AS Q_OS_RECEPCAO_INTERATIVA,
       Q_OS.AG_LAVAR_VEICULO            AS Q_OS_AG_LAVAR_VEICULO,
       Q_OS.AG_BLINDADO                 AS Q_OS_AG_BLINDADO,
       Q_OS.AG_TESTE_RODAGEM            AS Q_OS_AG_TESTE_RODAGEM,
       Q_OS.AG_LEVAR_PECAS_SUBSTITUIDAS AS Q_OS_AG_LEVAR_PECAS_SUBSTITUID,
       Q_OS.VEICULO_MODIFICADO          AS Q_OS_VEICULO_MODIFICADO,
       Q_OS.NUMERO_OS                   AS Q_OS_NUMERO_OS,
       Q_OS.HORA_PROMETIDA              AS Q_OS_HORA_PROMETIDA,
       Q_OS.PLACA                       AS Q_OS_PLACA,
       Q_OS.COMBUSTIVEL					AS Q_OS_COMBUSTIVEL,
       Q_OS.HORA_PROMETIDA_REVISADA     AS Q_OS_HORA_PROMETIDA_REVISADA,
       Q_OS.MOBILIDADE_DESCRICAO        AS Q_OS_MOBILIDADE_DESCRICAO,
       Q_CLIENTE.TELEFONE_CEL           AS Q_CLIENTE_TELEFONE_CEL,
       Q_CLIENTE.TELEFONE_COM           AS Q_CLIENTE_TELEFONE_COM,
       Q_CLIENTE.TELEFONE_RES           AS Q_CLIENTE_TELEFONE_RES,
       Q_CLIENTE.EMAIL_NFE              AS Q_CLIENTE_EMAIL_NFE,
       Q_CLIENTE.EMAIL2                 AS Q_CLIENTE_EMAIL2,
       Q_CLIENTE.ENDERECO_ELETRONICO    AS Q_CLIENTE_ENDERECO_ELETRONICO,
       Q_CLIENTE.RUA                    AS Q_CLIENTE_RUA,
       Q_CLIENTE.BAIRRO                 AS Q_CLIENTE_BAIRRO,
       Q_CLIENTE.NOME                   AS Q_CLIENTE_NOME,
       Q_CLIENTE.CIDADE                 AS Q_CLIENTE_CIDADE,
       Q_CLIENTE.CEP                    AS Q_CLIENTE_CEP,
       Q_CLIENTE.CPF                    AS Q_CLIENTE_CPF,
       Q_CLIENTE.RG                     AS Q_CLIENTE_RG,
       Q_OS.DESC_PROD_MOD               AS Q_OS_DESC_PROD_MOD,
       Q_OS.KM                          AS Q_OS_KM,
       Q_OS.CHASSI                      AS Q_OS_CHASSI,
       Q_OS.COR_EXTERNA                 AS Q_OS_COR_EXTERNA,
       Q_OS.ANO                         AS Q_OS_ANO,
       Q_OS.NUMERO_MOTOR                AS Q_OS_NUMERO_MOTOR,
       Q_OS.DATA_VENDA                  AS Q_OS_DATA_VENDA,
       Q_OS.CONCESSIONARIA_NOME         AS Q_OS_CONCESSIONARIA_NOME,
       Q_OS.TOTAL_OS                    AS Q_OS_TOTAL_OS,
       Q_OS.BLINDADORA                  AS Q_OS_BLINDADORA,
       Q_OS.BLINDADO                    AS Q_OS_BLINDADO,
       Q_OS.DDW_GARANTIA                AS Q_OS_DDW_GARANTIA,
       Q_OS.REC_INTERATIVA              AS Q_OS_REC_INTERATIVA,
       Q_OS.IMOBILIZADO                 AS Q_OS_IMOBILIZADO,
       Q_OS.DH_EMISSAO                  AS Q_OS_DH_EMISSAO,
       Q_OS.DH_ENCERRADO                AS Q_OS_DH_ENCERRADO,
       Q_OS.DH_LIBERADO                 AS Q_OS_DH_LIBERADO,
       Q_OS.TIPO_FABRICA                AS Q_OS_TIPO_FABRICA,
       Q_OS.OBSERVACAO                  AS Q_OS_OBSERVACAO,
       Q_HIST_ORC.ORCAMENTOS            AS Q_HIST_ORC_LISTA_ORCAMENTOS,
       Q_HIST_ORC.TOTAL                 AS Q_HIST_ORC_TOTAL_ORCAMENTOS,
       Q_HISTORICO.HISTORICO_OS         AS Q_HIST_ORC_HISTORICO_OS,
       Q_PARM_SYS.TERMO_OS_JLR          AS Q_PARM_SYS_TERMO_OS_JLR,
       Q_ASSINATURA.CLIENTE_RECEPCAO AS Q_ASSINATURA_CLIENTE_RECEPCAO,
       Q_OS.DATA_EMISSAO				AS Q_ASSINATURA_CLIE_RECEPCAO_DT,
	   Q_OS.HORA_EMISSAO 				AS Q_ASSINATURA_CLIE_RECEPCAO_HR,
       Q_ASSINATURA.CLIENTE_ENTREGA AS Q_ASSINATURA_CLIENTE_ENTREGA,
       Q_ASSINATURA.CLIENTE_ENTREGA_DT AS Q_ASSINATURA_CLIE_ENTREGA_DT,
       Q_ASSINATURA.CONSULTOR_RECEPCAO AS Q_ASSINATURA_CONSULTOR_RECEPC,
       Q_ASSINATURA.CONSULTOR_RECEPCAO_DT AS Q_ASSINATURA_CONS_RECEPCAO_DT,
       Q_ASSINATURA.CONSULTOR_ENTREGA AS Q_ASSINATURA_CONSULTOR_ENTREGA,
       Q_ASSINATURA.CONSULTOR_ENTREGA_DT AS Q_ASSINATURA_CONUL_ENTREGA_DT
  FROM Q_OS,
       Q_EMPRESA,
       Q_CLIENTE,
       Q_HIST_ORC,
       Q_HISTORICO,
       QRYDADOSVEICULOS,
       Q_ASSINATURA,
       Q_PARM_SYS
 WHERE Q_OS.COD_CLIENTE = Q_CLIENTE.COD_CLIENTE(+)
   AND Q_OS.COD_EMPRESA = Q_EMPRESA.COD_EMPRESA(+)
   AND Q_OS.COD_EMPRESA = Q_HIST_ORC.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_HIST_ORC.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_HISTORICO.COD_EMPRESA(+)
   AND Q_OS.CHASSI = Q_HISTORICO.CHASSI(+)
   AND Q_OS.CHASSI = QRYDADOSVEICULOS.CHASSI(+)
   AND Q_OS.PLACA = QRYDADOSVEICULOS.PLACA(+)
   AND Q_OS.COD_EMPRESA = Q_PARM_SYS.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_ASSINATURA.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_ASSINATURA.COD_EMPRESA(+)]]>
	</queryString>
	<field name="Q_EMPRESA_NOME_EMPRESA" class="java.lang.String"/>
	<field name="Q_EMPRESA_RUA" class="java.lang.String"/>
	<field name="Q_EMPRESA_CIDADE" class="java.lang.String"/>
	<field name="Q_EMPRESA_CEP" class="java.lang.String"/>
	<field name="Q_EMPRESA_FONE" class="java.lang.String"/>
	<field name="Q_EMPRESA_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="Q_EMPRESA_CGC" class="java.lang.String"/>
	<field name="Q_OS_TIPO" class="java.lang.String"/>
	<field name="Q_OS_D_AG_STG" class="java.lang.String"/>
	<field name="Q_OS_CONSULTOR" class="java.lang.String"/>
	<field name="Q_OS_PRISMA" class="java.lang.String"/>
	<field name="Q_OS_DATA_PROMETIDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_DATA_PROMETIDA_REVISADA" class="java.sql.Timestamp"/>
	<field name="Q_OS_AG_RETORNO" class="java.lang.String"/>
	<field name="Q_OS_AG_CLIENTE_AGUARDA" class="java.lang.String"/>
	<field name="Q_OS_RECEPCAO_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_AG_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="Q_OS_AG_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_AG_TESTE_RODAGEM" class="java.lang.String"/>
	<field name="Q_OS_AG_LEVAR_PECAS_SUBSTITUID" class="java.lang.String"/>
	<field name="Q_OS_VEICULO_MODIFICADO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_OS" class="java.lang.Integer"/>
	<field name="Q_OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="Q_OS_PLACA" class="java.lang.String"/>
	<field name="Q_OS_COMBUSTIVEL" class="java.lang.Integer"/>
	<field name="Q_OS_HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="Q_OS_MOBILIDADE_DESCRICAO" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_CEL" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_COM" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_RES" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL_NFE" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL2" class="java.lang.String"/>
	<field name="Q_CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="Q_CLIENTE_RUA" class="java.lang.String"/>
	<field name="Q_CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="Q_CLIENTE_NOME" class="java.lang.String"/>
	<field name="Q_CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="Q_CLIENTE_CEP" class="java.lang.String"/>
	<field name="Q_CLIENTE_CPF" class="java.lang.String"/>
	<field name="Q_CLIENTE_RG" class="java.lang.String"/>
	<field name="Q_OS_DESC_PROD_MOD" class="java.lang.String"/>
	<field name="Q_OS_KM" class="java.lang.Integer"/>
	<field name="Q_OS_CHASSI" class="java.lang.String"/>
	<field name="Q_OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="Q_OS_ANO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="Q_OS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="Q_OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="Q_OS_BLINDADORA" class="java.lang.String"/>
	<field name="Q_OS_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_DDW_GARANTIA" class="java.lang.String"/>
	<field name="Q_OS_REC_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_IMOBILIZADO" class="java.lang.String"/>
	<field name="Q_OS_DH_EMISSAO" class="java.lang.String"/>
	<field name="Q_OS_DH_ENCERRADO" class="java.lang.String"/>
	<field name="Q_OS_DH_LIBERADO" class="java.lang.String"/>
	<field name="Q_OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="Q_OS_OBSERVACAO" class="java.lang.String"/>
	<field name="Q_HIST_ORC_LISTA_ORCAMENTOS" class="java.lang.String"/>
	<field name="Q_HIST_ORC_TOTAL_ORCAMENTOS" class="java.lang.Double"/>
	<field name="Q_HIST_ORC_HISTORICO_OS" class="java.lang.String"/>
	<field name="Q_PARM_SYS_TERMO_OS_JLR" class="java.lang.String"/>
	<field name="Q_ASSINATURA_CLIENTE_RECEPCAO" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CLIE_RECEPCAO_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CLIE_RECEPCAO_HR" class="java.lang.String"/>
	<field name="Q_ASSINATURA_CLIENTE_ENTREGA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CLIE_ENTREGA_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CONSULTOR_RECEPC" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CONS_RECEPCAO_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CONSULTOR_ENTREGA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CONUL_ENTREGA_DT" class="java.sql.Timestamp"/>
	<variable name="SubreportRowCount" class="java.lang.Integer">
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<pageHeader>
		<band height="288">
			<frame>
				<reportElement x="0" y="4" width="555" height="60" uuid="86cc3982-0e82-4e2c-bf3e-69612afbeebb"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Left" vAlign="Middle" isUsingCache="true">
					<reportElement x="377" y="4" width="176" height="40" uuid="082a5bc8-ad19-4b1e-b25b-88b13401d533"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600470.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="2" y="4" width="57" height="13" uuid="df34a284-e798-4119-bbf9-57ccad58ce61"/>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Razão Social:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="60" y="4" width="220" height="13" uuid="434f37eb-1edd-4ea7-b60f-d3e7074aabfa"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="23" y="17" width="257" height="13" uuid="8a09e96f-f233-4521-abac-cddd476c5c3f"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_RUA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="2" y="17" width="21" height="13" uuid="591e0a84-43c2-40fb-998d-f395c8536d48"/>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Rua:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="55" y="30" width="143" height="13" uuid="d72fcf2a-573c-4250-ad52-218a7273a05e"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_CIDADE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="2" y="30" width="53" height="13" uuid="167ae609-c514-4ffb-bb6a-cab8cb8abb0a"/>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade/UF:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="201" y="30" width="20" height="13" uuid="2cbd0a58-9d63-4140-9c02-ca8e41df2a6a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="221" y="30" width="59" height="13" uuid="2b2106ef-f4c6-4d55-a9d5-5330fc0dac0a"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="221" y="43" width="59" height="13" uuid="dc7633d2-b754-4b43-957a-8bc023a68700"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_FONE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="201" y="43" width="20" height="13" uuid="cc6eaed3-b248-4553-afc5-9697d43896df"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tel:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="13" y="43" width="76" height="13" uuid="c67a78f1-1fe9-4596-8b94-1a3257cd13f8"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_INSCRICAO_ESTADUAL}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="2" y="43" width="11" height="13" uuid="63bf7ea0-db0b-41be-8df6-bee256858550"/>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[IE:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="115" y="43" width="78" height="13" uuid="3e44725e-4e2a-4202-b078-2e4d854785fd"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_CGC}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="89" y="43" width="26" height="13" uuid="40ebe58a-3124-4d42-8bb9-c072c6dc37eb"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="473" y="44" width="19" height="13" uuid="fed145f0-c669-4001-9761-4dbff8bf2801"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="492" y="44" width="26" height="13" uuid="f3680ecf-9669-4fec-bc22-eff77aadb1f7"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TIPO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="520" y="44" width="17" height="13" uuid="8c06a60a-ad10-4f1c-94a7-66583fdff8cb"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}+ " / "]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report" pattern="#0.###;(#0.###-)">
					<reportElement mode="Transparent" x="537" y="44" width="16" height="13" uuid="10b00e96-9a64-42da-976e-26b0573a1aa8"/>
					<box leftPadding="1"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Math.ceil($V{PAGE_NUMBER}/3.0)]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="64" width="555" height="85" uuid="83dad819-389d-477b-a211-dfb76ee49e7e"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="389" y="42" width="82" height="20" uuid="4f0bed94-8df8-4f90-ba39-2d314108ab41"/>
					<box leftPadding="1">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Lavar o veículo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="303" y="19" width="86" height="23" uuid="e541a25a-8fc1-4ffc-ad07-7eecf33fc710"/>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cliente Retorno]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="555" height="19" backcolor="#E3E3E3" uuid="9f91846c-8b31-4347-8486-a2074ae32bb1"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[ORDEM DE SERVIÇO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="416" y="1" width="136" height="17" uuid="c7f5ee4f-a9ec-46d1-aaec-d3ff023eecbc"/>
					<textElement textAlignment="Left">
						<font size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="39" y="19" width="67" height="34" uuid="cf1872db-b395-41d8-87f1-52b105d82a22"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Agendamento]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="39" y="53" width="67" height="32" uuid="59ed45fe-f42e-4367-8114-9576efbe63c4"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_D_AG_STG}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="106" y="19" width="69" height="34" uuid="4849d43a-2590-45a8-989d-b7604714c1ca"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Consultor de Serviço]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="106" y="53" width="69" height="32" uuid="e4575bfc-6fad-4f08-86fd-9f58242f40f4"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CONSULTOR}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="175" y="19" width="41" height="34" uuid="36ce65d3-cd22-491a-819d-c6dbec1926ae"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Prisma]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="175" y="53" width="41" height="32" uuid="9f5190c6-06b9-43ba-9f14-9114c7cb4be9"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PRISMA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="216" y="53" width="43" height="17" uuid="6ec5fe76-d7e3-4a8b-93d3-fd88f55e6d43"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="259" y="53" width="44" height="17" uuid="cfadc0f4-9d48-41e1-afd5-29789f3acf7f"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA_REVISADA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="303" y="42" width="86" height="21" uuid="beab15b8-d913-4a18-9de7-124eba4df56e"/>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cliente Aguarda]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="303" y="63" width="62" height="22" uuid="5ec1f14a-c8be-43b2-832c-b4db8283c03e"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Recepção Interativa]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="365" y="24" width="21" height="14" backcolor="#E3E3E3" uuid="e6c5f03c-a867-4554-bbb8-ad76ca38a960"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_AG_RETORNO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="365" y="44" width="21" height="14" backcolor="#E3E3E3" uuid="fe445767-0a14-411f-9e9f-537932915d4e"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_AG_CLIENTE_AGUARDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="365" y="65" width="21" height="14" backcolor="#E3E3E3" uuid="a9a1c3d5-ea5e-43b7-b3e5-c1ea8aed5f93"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_RECEPCAO_INTERATIVA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="389" y="19" width="82" height="11" uuid="9426f6ff-1710-4db2-8ef4-e69bba813742"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Mobilidade]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="389" y="62" width="82" height="23" uuid="6e7d2441-41dd-45e7-a5e3-58394f7b0035"/>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Veículo Blindado]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="447" y="45" width="21" height="14" backcolor="#E3E3E3" uuid="d501135a-66e5-44d5-b17b-4963a54ba89e"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_AG_LAVAR_VEICULO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="447" y="66" width="21" height="14" backcolor="#E3E3E3" uuid="10ccbbe4-14db-41c9-ab6f-1798fb687eb2"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_AG_BLINDADO}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="471" y="63" width="84" height="22" uuid="f08fff6c-aeb8-4cf4-b7a2-d10d40ece970"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="51" height="22" uuid="d1fb72e6-e7f0-4093-8fa8-eebc3e8166ca"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Veículo Modificado]]></text>
					</staticText>
				</frame>
				<textField pattern="###0.###;(###0.###-)">
					<reportElement mode="Transparent" x="462" y="1" width="82" height="17" uuid="212fa795-713d-4976-86aa-a9629ade222b"/>
					<textElement textAlignment="Center">
						<font size="13" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="216" y="70" width="43" height="15" uuid="fc62caa7-12c1-45a9-9bb8-1a0fa338fa98"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_HORA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="0" y="53" width="39" height="32" uuid="e592be50-c792-4ada-9d38-************"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PLACA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="19" width="39" height="34" uuid="31b4a29f-38f8-43eb-834a-9ed2c7f1d773"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Placa]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="216" y="35" width="43" height="18" uuid="e25874ff-34d3-4a0a-8e2d-9325f0c8677b"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Original]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="259" y="35" width="44" height="18" uuid="764a371d-b1c0-4f0b-8f11-3805c33d3591"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Revisado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="216" y="19" width="87" height="16" uuid="5fd2f5d0-2c83-4968-a118-6069f43703d7"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Prazo Prometido]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="259" y="70" width="44" height="15" uuid="157fb4f4-13c9-4c88-a641-c0f43150884c"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_HORA_PROMETIDA_REVISADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="389" y="30" width="82" height="12" uuid="ae1e73cf-7276-4bef-a7d8-5a456860b189"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_MOBILIDADE_DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="529" y="67" width="21" height="14" backcolor="#E3E3E3" uuid="b19d74d4-721f-4d93-938a-b9e21991de1d"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_VEICULO_MODIFICADO}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="471" y="42" width="84" height="21" uuid="e6f8b432-8a28-44c2-b188-5c2202c58b07"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="51" height="21" uuid="a59cd3b1-808d-4428-84b7-07110b3c2f0d"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Levar peças substituídas]]></text>
					</staticText>
					<textField>
						<reportElement mode="Opaque" x="58" y="2" width="21" height="14" backcolor="#E3E3E3" uuid="fccefe96-0439-4857-b160-231e2adb1f4e"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_AG_LEVAR_PECAS_SUBSTITUID}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="471" y="19" width="84" height="23" uuid="a61875c6-bb53-454f-9951-d8ae901d3005"/>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="51" height="23" uuid="4cb85d4e-7f07-4b0d-939b-00cc6f25e161"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Teste de Rodagem]]></text>
					</staticText>
					<textField>
						<reportElement mode="Opaque" x="58" y="5" width="21" height="14" backcolor="#E3E3E3" uuid="15a09773-9de6-4465-8a8d-dbed5adb63ca"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_AG_TESTE_RODAGEM}]]></textFieldExpression>
					</textField>
				</frame>
			</frame>
			<frame>
				<reportElement x="0" y="149" width="555" height="67" uuid="f35131c1-8c84-4900-854a-46c23e578b05">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="555" height="11" backcolor="#E3E3E3" uuid="e9821e29-e640-4279-ba7c-798a6670ddf3">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[DADOS DO CLIENTE]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="302" y="11" width="94" height="15" uuid="ef6a4545-d925-4f87-9091-980f4455605f"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true" isUnderline="true"/>
					</textElement>
					<text><![CDATA[Telefone de Contato]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="302" y="40" width="34" height="14" uuid="fd70c52e-a9a5-4f7a-bae3-8008984c9bda"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[2ºOpção]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="302" y="54" width="34" height="13" uuid="061b9060-1729-4df9-b5be-3419545c526d"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[3ºOpção]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="302" y="26" width="34" height="14" uuid="6b76df10-87df-4dc4-8139-a405cf4d6e80"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[1ºOpção]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="336" y="40" width="60" height="14" uuid="6faa8082-18c7-4294-b0c6-f2f134a302d9"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_TELEFONE_CEL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="336" y="54" width="60" height="13" uuid="f80fed6b-9cf4-4a17-9170-0da46d988a06"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_TELEFONE_COM}]]></textFieldExpression>
				</textField>
				<textField pattern="">
					<reportElement mode="Transparent" x="336" y="26" width="60" height="14" uuid="f5f430f5-ea16-417e-8cd4-c9e98ed58a2e"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_TELEFONE_RES}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="396" y="11" width="159" height="15" uuid="e3b7df56-2bb2-49fd-a7ae-98f4c89fc2c5"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true" isUnderline="true"/>
					</textElement>
					<text><![CDATA[E-mail de Contato]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="396" y="40" width="159" height="14" uuid="12f37e33-a37c-4b89-ab33-411a09656cd1"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_EMAIL_NFE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="396" y="54" width="159" height="13" uuid="487d36f8-500a-401d-aabc-235bbd5fd3db"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_EMAIL2}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="396" y="26" width="159" height="14" uuid="70bb9c58-c924-4022-8f24-f026b5fc6f9d"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="11" width="37" height="15" uuid="77f30db8-58db-4344-8013-14577d32e577"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cliente:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="26" width="37" height="14" uuid="d25a2de2-d8f3-4ffc-b5f7-d9f531d499c3"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Endereço:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="37" y="26" width="118" height="14" uuid="1fa8fab1-14c0-487a-9781-bc95cab0efd3"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="184" y="26" width="118" height="14" uuid="614fa805-a32e-4946-bf34-42afd85c5561"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="37" y="11" width="265" height="15" uuid="78147d1e-a9a8-4446-8381-ddaf3ba2e131"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="155" y="26" width="29" height="14" uuid="e8efdd96-83d3-493c-941d-9ed275a074ae"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="40" width="37" height="14" uuid="d9ae2976-09a9-4ae8-ae50-5c952e48c9e3"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="37" y="40" width="118" height="14" uuid="95e78cdc-3be6-4557-949b-b5900147775e"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="184" y="40" width="118" height="14" uuid="dbb8b324-1958-44db-bc31-10a6c5746d5d"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_CEP}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="155" y="40" width="29" height="14" uuid="efdd1b1a-3359-40d1-a530-b96dc033646f"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="54" width="37" height="13" uuid="ae1b067a-6016-44c0-9ef5-106c986595bc"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[CPF:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="37" y="54" width="118" height="13" uuid="563d9881-1d3b-4736-b6c8-56b72676bd13"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_CPF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="184" y="54" width="118" height="13" uuid="5838cf14-8b94-4d68-831a-694ff1c8e539"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_RG}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="155" y="54" width="29" height="13" uuid="b8074dce-ff43-4f9a-9b64-e1a25e271c27"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[RG:]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="216" width="555" height="72" uuid="9ded1a80-66de-403c-b59f-65f0d6b15a49">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="555" height="11" backcolor="#E3E3E3" uuid="f0e218c2-b370-4a85-876f-651a7c588f0d">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[DADOS VEÍCULO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="11" width="35" height="22" uuid="aa078d9a-b711-47d2-88f6-13ff44569569"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Veículo:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="35" y="11" width="178" height="22" uuid="42b760f2-6ba3-4c3b-b284-da96c7c02c28"/>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DESC_PROD_MOD}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="479" y="33" width="28" height="23" uuid="a411a747-98c3-41b5-9526-e6bca7b03aec"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Cor:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="479" y="11" width="28" height="22" uuid="9c2f8a4d-b23a-4b22-bdff-db03dee77fb4"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[KM Atual:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="33" width="35" height="23" uuid="4421c623-dd78-481b-8bad-f3599d3e8651"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Chassi:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="111" y="33" width="34" height="23" uuid="62f3ebbb-e769-49af-904f-b6c90e9b881e"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[N°Motor:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="372" y="11" width="43" height="22" uuid="e372e3af-cb36-4466-8d22-3073e3f76a95"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Ano Fabr./ Modelo:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="372" y="33" width="43" height="23" uuid="252a237d-fd77-4905-b942-6b0b23391cae"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Data Venda:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="507" y="11" width="48" height="22" uuid="06d1bae4-5614-4d52-bcc7-64f3f6f957bc"/>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_KM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="35" y="33" width="76" height="23" uuid="2a0a153e-8b6a-475c-801d-9e7feffc8f85"/>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CHASSI}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="507" y="33" width="48" height="23" uuid="3a801f47-546d-4715-b246-0469dbc58b03"/>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="415" y="11" width="64" height="22" uuid="a9a5069f-1d98-4471-9264-b140160667a5"/>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_ANO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="145" y="33" width="68" height="23" uuid="45bea13a-1fcc-4eeb-99df-def5c8bbef1b"/>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_MOTOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="415" y="33" width="64" height="23" uuid="b94c0a2b-9529-479c-9cf7-f8e3492a7263"/>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="213" y="11" width="56" height="22" uuid="8bd69cb9-236d-45d1-99b9-cdc99eb455f8"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Concessionária Vendedora:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="269" y="11" width="103" height="22" uuid="f4659794-ad61-415e-8efc-dca912f610b3"/>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CONCESSIONARIA_NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="213" y="33" width="56" height="23" uuid="d91b32da-d405-4b30-a4cb-eb52581b0afc"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Combustível:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="100" y="56" width="44" height="16" uuid="31ec37ca-6359-4e8b-8784-68e2dc42f90f"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Blindadora:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="144" y="56" width="411" height="16" uuid="639ff66b-b6c2-4cbb-8c54-a5ff4ae09020"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="5" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_BLINDADORA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="56" width="80" height="16" uuid="ef9a2eeb-9992-4455-84d4-d2172fed2b04"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Blindagem Certificada]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="80" y="56" width="20" height="16" backcolor="#E3E3E3" uuid="30e7959b-3c3f-4847-ad47-6928baaa599e"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_BLINDADO}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="337" y="48" width="1" height="2" uuid="dce66deb-5d01-499c-8922-e5b7e4282548">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="307" y="46" width="1" height="4" uuid="253bbdae-e398-4398-a4c6-a3fb439ad716">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="331" y="46" width="1" height="4" uuid="876d9a83-b342-415c-aae3-b1b3d78fccc2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="325" y="48" width="1" height="2" uuid="2707e605-fc52-4f7e-86c4-e30e4e851ad7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="319" y="44" width="1" height="6" uuid="f5220057-09c6-455a-a218-3aa00d3dfaa5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
				</line>
				<textField>
					<reportElement x="294" y="39" width="51" height="11" uuid="90ab94d6-b0a1-4aea-b721-b32a58110973"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_COMBUSTIVEL} < 19 ? "":
$F{Q_OS_COMBUSTIVEL} < 39 ? "X" :
$F{Q_OS_COMBUSTIVEL} < 59 ? "X   X":
$F{Q_OS_COMBUSTIVEL} < 79 ? "X   X   X":
 "X   X   X   X"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="301" y="48" width="1" height="2" uuid="196d503d-534b-458e-9b25-b89bc620dfeb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="313" y="48" width="1" height="2" uuid="4ad91beb-d832-4af3-8481-b9e154b254f5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="288" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="288" uuid="26ebb2a6-ce82-45c3-a86e-e928b97c37e2">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="23" height="84" isPrintWhenDetailOverflows="true" uuid="5a51abe5-c705-426e-be59-d30076f9ff90">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Solicitações iniciais]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="84" width="23" height="96" isPrintWhenDetailOverflows="true" uuid="1b03a1d2-1176-4d70-adfd-72a63136cf30">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Descrição dos Serviços]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="180" width="23" height="96" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="86332333-418c-4fe2-aae9-f5a20d8f30df">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Peças e Lubrificantes]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="276" width="555" height="12" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="08ab2b8c-f86a-4abd-aab2-0cb269f1f46c">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="65">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[VALOR TOTAL ESTIMADO]]></text>
				</staticText>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement mode="Transparent" x="490" y="277" width="63" height="11" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="54e15da5-1873-4d86-8951-7929e70bf667"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TOTAL_OS}]]></textFieldExpression>
				</textField>
				<subreport isUsingCache="false" overflowType="NoStretch">
					<reportElement positionType="Float" x="23" y="0" width="532" height="84" isRemoveLineWhenBlank="true" uuid="e3b3e1ad-e8ea-4042-9195-1e03c88dd5dd">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsJaguarLandSubReclamacao.jasper"]]></subreportExpression>
				</subreport>
				<subreport isUsingCache="true" runToBottom="true" overflowType="NoStretch">
					<reportElement positionType="Float" x="23" y="84" width="532" height="96" isPrintWhenDetailOverflows="true" uuid="d775b6da-c46b-4a97-85cd-76b6db5d60bc">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsJaguarLandSubServicos.jasper"]]></subreportExpression>
				</subreport>
				<subreport isUsingCache="true" runToBottom="true" overflowType="NoStretch">
					<reportElement positionType="Float" x="23" y="180" width="532" height="96" uuid="c271f485-d412-4496-8f54-69525be3407f">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsJaguarLandSubPecas.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="224">
			<frame>
				<reportElement x="0" y="3" width="555" height="221" uuid="ca577254-7694-427d-9eb3-8e4fc633f0df"/>
				<frame>
					<reportElement x="0" y="0" width="555" height="53" uuid="319a352b-b2c8-4035-8572-65efdd6e632a">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.25" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="1" y="42" width="365" height="11" uuid="6668ffe0-324f-45a8-9563-9150f8f2046d"/>
						<box leftPadding="3"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Responsável pelo contato:                                                               Data:     /      /        Hora      :]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="555" height="14" uuid="208ead82-**************-1c7c477012cd"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<pen lineWidth="0.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Autorizado o(s) Orçamentos(s) Complementar(s) número(s):]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="14" width="555" height="15" uuid="7d69cc33-0366-4671-89ff-c698b217d799"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Valor Total Complementar:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="29" width="555" height="12" uuid="6e1f664b-457f-4565-a273-e6ad115c7d99"/>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Responsável pela autorização:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="192" y="1" width="360" height="12" uuid="2b66c67d-c25b-4dc3-9579-96561dbeb402"/>
						<box leftPadding="3"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_HIST_ORC_LISTA_ORCAMENTOS}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="88" y="15" width="464" height="13" uuid="6a697937-7d59-4717-b427-2410f48ac275"/>
						<box leftPadding="3"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_HIST_ORC_TOTAL_ORCAMENTOS}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="0" y="53" width="555" height="54" uuid="b8367f66-8326-42b4-91cc-28bd3080d943">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textField>
						<reportElement mode="Transparent" x="2" y="22" width="548" height="30" uuid="46018a87-dde9-4b37-b198-e824d7e4512a"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Informações Adicionais: " +( $F{Q_OS_OBSERVACAO} == null? "":$F{Q_OS_OBSERVACAO}) + " - " + ($F{Q_PARM_SYS_TERMO_OS_JLR} == null ? "":$F{Q_PARM_SYS_TERMO_OS_JLR})]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="2" y="2" width="67" height="10" uuid="f43570c9-2a6f-433e-b1e3-15c42ee6f29e">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Histórico de Serviço:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="2" y="12" width="72" height="10" uuid="185c9724-5765-4832-8e81-dae9c9611bfd"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Campanha de Serviço:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="74" y="12" width="453" height="10" uuid="da10020f-9299-4bf6-a66b-39be78d354c9"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DDW_GARANTIA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="4" y="37" width="174" height="14" uuid="f1dd56b8-f8df-462f-ac72-0a6ca033d60b">
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_REC_INTERATIVA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="385" y="37" width="162" height="14" uuid="93cc1706-15c8-4075-9516-c44f5700e56d">
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Right">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_IMOBILIZADO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="69" y="2" width="481" height="10" uuid="02b1844a-246c-45a1-a9a6-ea36a9493332"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["O.S: " +  $F{Q_HIST_ORC_HISTORICO_OS} + "."]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="0" y="107" width="555" height="78" uuid="3aea8426-7833-45e7-ad0e-4fbf88aa0212">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Opaque" x="0" y="0" width="274" height="11" backcolor="#E3E3E3" uuid="7e1d22ef-7428-4363-bdc8-967fa7d31ea0">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Recepção:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="196" y="11" width="78" height="14" uuid="638a6097-7ea7-448e-8ab4-5460c74f0aef">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Data:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="1" y="28" width="195" height="11" uuid="e7d315b0-76bf-4c2b-a802-4cb48554c124">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Assinatura do Responsável ou pessoa por ele autorizada]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="196" y="25" width="78" height="15" uuid="6581d11f-57e7-4aaf-b93c-6f171a779fbc"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Hora:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="284" y="0" width="271" height="11" backcolor="#E3E3E3" uuid="0c893bc8-aa17-42d7-8f89-0a6c03501bd4"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Recepção:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="479" y="11" width="76" height="14" uuid="15b767ec-515d-42f7-acc1-5097381679b3"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Data:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="284" y="28" width="195" height="11" uuid="9ca616b4-4b3c-4801-8cf1-4f276463c52b"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Assinatura do Consultor de Serviços]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="479" y="25" width="76" height="14" uuid="2dc70664-fd18-4fea-80ef-89df267c22e0"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Hora:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="0" y="39" width="274" height="11" backcolor="#E3E3E3" uuid="249ffd52-cce0-41f4-a11d-6ccf1044f024"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Entrega:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="50" width="196" height="17" uuid="b63beb88-1846-4c1a-8385-5ff7f0df6f72">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="196" y="50" width="78" height="14" uuid="57705e04-6381-4b18-a91d-bc8f61a23171">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Data:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="67" width="196" height="11" uuid="2704104c-1377-4f76-9c4e-523c6fdbb4d1"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Top">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Assinatura do Responsável ou pessoa por ele autorizada]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="196" y="64" width="78" height="14" uuid="6bfbb36c-9df4-4bef-a368-9d4f09d7a5d3"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Hora:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="284" y="39" width="271" height="11" backcolor="#E3E3E3" uuid="fb3646e6-1f2a-4143-b4b9-817f55d8cc0b"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Entrega:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="284" y="50" width="195" height="17" uuid="34b53aa3-cdc8-4cb4-997f-1f268f3155b3"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="479" y="50" width="76" height="14" uuid="17536274-8b2a-4497-9dd0-69a0e0df450c"/>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Data:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="284" y="67" width="195" height="11" uuid="b971fb7a-5c9c-4c3a-b40b-40e5c51b319c"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Assinatura do Consultor de Serviços]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="479" y="64" width="76" height="14" uuid="11e8ad04-00e2-4a9a-8f67-f60c77b4ffb2">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Hora:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="284" y="11" width="195" height="17" uuid="2b05adea-7f75-4a3e-93c3-8d4b4a35d678"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="11" width="196" height="17" uuid="a60de29b-efa7-43ba-9dc6-144e26fe4fb7"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[]]></text>
					</staticText>
					<image hAlign="Center" vAlign="Middle" isUsingCache="false">
						<reportElement x="35" y="12" width="128" height="15" uuid="fecb17e7-8714-4e0f-a480-795e3b56846f">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$F{Q_ASSINATURA_CLIENTE_RECEPCAO}]]></imageExpression>
					</image>
					<image hAlign="Center" vAlign="Middle" isUsingCache="false">
						<reportElement x="35" y="51" width="128" height="15" uuid="03e46df6-6dca-44f3-a4b8-606c5b9a1574">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$F{Q_ASSINATURA_CLIENTE_ENTREGA}]]></imageExpression>
					</image>
					<image hAlign="Center" vAlign="Middle" isUsingCache="false">
						<reportElement x="319" y="51" width="128" height="15" uuid="856d19c0-cc39-45cd-b095-bf0604dbdb83">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$F{Q_ASSINATURA_CONSULTOR_ENTREGA}]]></imageExpression>
					</image>
					<image hAlign="Center" vAlign="Middle" isUsingCache="false">
						<reportElement x="319" y="12" width="128" height="15" uuid="daefa055-d39a-43f0-938a-3a4dc922fb64">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$F{Q_ASSINATURA_CONSULTOR_RECEPC}]]></imageExpression>
					</image>
					<textField pattern="MM/dd/yyyy">
						<reportElement mode="Transparent" x="217" y="11" width="57" height="14" uuid="44f59ca7-5700-4415-a280-74361732e49d">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_ASSINATURA_CLIE_RECEPCAO_DT}]]></textFieldExpression>
					</textField>
					<textField pattern="hh:mm">
						<reportElement mode="Transparent" x="217" y="25" width="57" height="14" uuid="1c2c9d99-db1b-43bd-af10-3de0c6d4dd08">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_ASSINATURA_CLIE_RECEPCAO_HR}]]></textFieldExpression>
					</textField>
					<textField pattern="MM/dd/yyyy">
						<reportElement mode="Transparent" x="217" y="50" width="57" height="14" uuid="9f164bf8-d374-41fb-b742-18b448649887">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_ASSINATURA_CLIE_ENTREGA_DT}]]></textFieldExpression>
					</textField>
					<textField pattern="hh:mm">
						<reportElement mode="Transparent" x="217" y="64" width="57" height="14" uuid="6eb79a78-e799-4ea0-a851-fa5ef94f46f7">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_ASSINATURA_CLIE_ENTREGA_DT}]]></textFieldExpression>
					</textField>
					<textField pattern="MM/dd/yyyy">
						<reportElement mode="Transparent" x="500" y="11" width="55" height="14" uuid="1f239752-287e-40c3-9602-53dc52ca2925">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_ASSINATURA_CONS_RECEPCAO_DT}]]></textFieldExpression>
					</textField>
					<textField pattern="hh:mm">
						<reportElement mode="Transparent" x="500" y="25" width="55" height="14" uuid="1d5d3b54-dc8b-4838-ba93-330fdbb678fe">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_ASSINATURA_CONS_RECEPCAO_DT}]]></textFieldExpression>
					</textField>
					<textField pattern="MM/dd/yyyy">
						<reportElement mode="Transparent" x="500" y="50" width="55" height="14" uuid="274e58e3-566f-491c-8cf1-8bb6451f2e66">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_ASSINATURA_CONUL_ENTREGA_DT}]]></textFieldExpression>
					</textField>
					<textField pattern="hh:mm">
						<reportElement mode="Transparent" x="500" y="64" width="55" height="14" uuid="b3df96bd-29c2-4737-9cd7-3588a03e548a">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_ASSINATURA_CONUL_ENTREGA_DT}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="0" y="185" width="555" height="36" uuid="eef4d1d9-34f2-4aa9-b3fe-d56575b38d7c">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="84" height="11" uuid="10c75695-f787-45ce-862a-60c27be6ddd9">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Data / Hora Abertura:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="85" y="0" width="97" height="11" uuid="d4f3c185-976a-41e1-9ab9-c95eacab7112"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DH_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="455" y="0" width="94" height="11" uuid="b8d87b1e-605e-406f-a7d2-f3b9a887a05e"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DH_ENCERRADO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="337" y="0" width="118" height="11" uuid="1d10ec5e-8929-45f7-9375-b4d458980cd9"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Data / Hora do Encerramento:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="201" y="0" width="36" height="11" uuid="06aa8b08-655c-4c1b-9495-ac075da61d89"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Liberado:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="237" y="0" width="92" height="11" uuid="309cecbe-bb34-415b-a1ca-fab664208277"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DH_LIBERADO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="388" y="11" width="67" height="11" uuid="d30afec6-f76b-4d98-9b14-7d7694ce76ff"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Tipo OS Fábrica:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="455" y="11" width="94" height="11" uuid="58a7e669-7916-4b27-806b-8798abb2d108"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_TIPO_FABRICA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="1" y="16" width="210" height="11" uuid="4c235dd6-4834-418b-8d5c-f82eb4e8e700"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_REC_INTERATIVA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="386" y="22" width="161" height="11" uuid="6b825137-1dcb-4807-8cc1-8ed143b69393"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_IMOBILIZADO}]]></textFieldExpression>
					</textField>
				</frame>
			</frame>
		</band>
	</columnFooter>
	<summary>
		<band height="100" splitType="Prevent">
			<subreport isUsingCache="false" overflowType="Stretch">
				<reportElement positionType="Float" x="0" y="0" width="555" height="100" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="4a9d0469-d1d7-4082-8e00-0a020bcc9a7c">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="InitialRowCount">
					<subreportParameterExpression><![CDATA[$V{PAGE_NUMBER}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsJaguarLandSubObservacaoTecnica.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</summary>
</jasperReport>
