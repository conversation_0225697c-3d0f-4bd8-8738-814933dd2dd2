<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="emBranco" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="QUERY_DINAMICA" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["  1 = 1"]]></defaultValueExpression>
	</parameter>
	<parameter name="DATA_INICIAL" class="java.lang.String"/>
	<parameter name="DATA_FINAL" class="java.lang.String"/>
	<parameter name="CONSULTOR" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\31167\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="TIPO_FABRICA" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH Q_EMPRESA AS
 (SELECT EMPRESAS.COD_EMPRESA,
         DECODE(EMPRESAS.COD_MATRIZ,
                NULL,
                EMPRESAS.NOME,
                EMPRESA_MATRIZ.NOME) AS NOME_MATRIZ,
         DECODE(EMPRESAS.COD_MATRIZ, NULL, '', EMPRESAS.NOME) AS NOME_FILIAL,
         TRUNC(SYSDATE) AS SYS_DATA,
         SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'), 1, 5) AS SYS_HORA
    FROM EMPRESAS, EMPRESAS EMPRESA_MATRIZ
   WHERE (EMPRESAS.COD_EMPRESA = $P{COD_EMPRESA})
     AND (EMPRESAS.COD_MATRIZ = EMPRESA_MATRIZ.COD_EMPRESA(+))),
Q_TOTAL AS
 (SELECT COUNT(NUMERO_OS) AS PASSAGENS
    FROM OS,OS_TIPOS
   WHERE OS.TIPO      = OS_TIPOS.TIPO
     AND ORCAMENTO = 'N'
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
    AND $P!{QUERY_DINAMICA}
     )
     
SELECT Q_EMPRESA.NOME_FILIAL AS Q_EMPRESA_NOME_FILIAL,
       Q_EMPRESA.NOME_MATRIZ AS Q_EMPRESA_NOME_MATRIZ,
       Q_EMPRESA.SYS_DATA    AS Q_EMPRESA_SYS_DATA,
       Q_TOTAL.PASSAGENS     AS Q_TOTAL_PASSAGENS

FROM Q_EMPRESA, Q_TOTAL]]>
	</queryString>
	<field name="Q_EMPRESA_NOME_FILIAL" class="java.lang.String"/>
	<field name="Q_EMPRESA_NOME_MATRIZ" class="java.lang.String"/>
	<field name="Q_EMPRESA_SYS_DATA" class="java.sql.Timestamp"/>
	<field name="Q_TOTAL_PASSAGENS" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="55">
			<frame>
				<reportElement x="0" y="0" width="555" height="55" uuid="a7d12759-e46e-4904-8808-444acf07066d">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="555" height="14" uuid="ce85ae56-964a-4f4d-9de4-85908b88344d"/>
					<textElement textAlignment="Center">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Análise Passagens no Departamento de Serviços]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="43" width="39" height="11" uuid="46e678ec-9cb9-4659-ba63-5012bdbb6d5d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Filial:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="30" width="39" height="11" uuid="c9020580-c402-4378-a92d-598623164c2a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Empresa:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="43" y="43" width="364" height="11" uuid="92dcc32f-3198-4db6-8d50-5a3ea2b8d39d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_NOME_FILIAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="43" y="30" width="364" height="11" uuid="fd97658a-8bf6-4712-81e2-3aed28520041"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_NOME_MATRIZ}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="414" y="43" width="73" height="11" uuid="a6b56ce3-e94c-4503-bb81-39d41dc07929"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Data de Impressão:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="414" y="30" width="54" height="11" uuid="c273dccf-6c7d-4703-9a36-8feb404f387c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Página:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="489" y="30" width="11" height="11" uuid="bd4b8e7c-db02-48bb-84cd-7d1a18f3ca2d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="502" y="30" width="13" height="11" uuid="3db876af-bc23-456d-85e0-22987107bc2b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[de]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement mode="Transparent" x="517" y="30" width="11" height="11" uuid="ecd0551e-f100-4faa-8af0-6be3456b9de0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField pattern="MM/dd/yyyy">
					<reportElement mode="Transparent" x="489" y="43" width="47" height="11" uuid="a1e91705-3d95-42ad-9842-2bb7e3069c0e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_SYS_DATA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="0" y="14" width="555" height="14" uuid="6b8c20ce-d37d-4991-a4c3-f219a88b9680"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["Período de " + $P{DATA_INICIAL} + " a " + $P{DATA_FINAL} + " - " + $P{CONSULTOR}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="65" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport isUsingCache="false">
				<reportElement x="0" y="0" width="442" height="58" isRemoveLineWhenBlank="true" uuid="492f2993-914b-4c3c-91ee-bd716de78d28">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="QUERY_DINAMICA">
					<subreportParameterExpression><![CDATA[$P{QUERY_DINAMICA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TOTAL_PASSAGENS">
					<subreportParameterExpression><![CDATA[$F{Q_TOTAL_PASSAGENS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TIPO_FABRICA">
					<subreportParameterExpression><![CDATA[$P{TIPO_FABRICA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "RelatorioDePassagemSubPorTipoOs.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="65">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport isUsingCache="false">
				<reportElement x="0" y="0" width="442" height="58" isRemoveLineWhenBlank="true" uuid="af86df95-e2d8-4ebb-bf1e-146a3632ecb9">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="QUERY_DINAMICA">
					<subreportParameterExpression><![CDATA[$P{QUERY_DINAMICA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TOTAL_PASSAGENS">
					<subreportParameterExpression><![CDATA[$F{Q_TOTAL_PASSAGENS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "RelatorioDePassagemSubSegmentoProdutoModelo.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="58">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport isUsingCache="false">
				<reportElement x="0" y="0" width="442" height="58" isRemoveLineWhenBlank="true" uuid="7744df28-50cd-4fed-8c89-216b2fdce28e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="QUERY_DINAMICA">
					<subreportParameterExpression><![CDATA[$P{QUERY_DINAMICA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TOTAL_PASSAGENS">
					<subreportParameterExpression><![CDATA[$F{Q_TOTAL_PASSAGENS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "RelatorioDePassagemSubPorSetorServico.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
