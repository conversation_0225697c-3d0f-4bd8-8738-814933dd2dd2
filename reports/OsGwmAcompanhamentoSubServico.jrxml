<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsGwmAcompanhamentoSubServico" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[11095.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[select oo.item
       ,oo.descricao
       , odserv.servico_realizado
       , odserv.data_servico_realizado
       , initcap(st.nome) as nome
from os
     , os_original oo
     , OS_DIAGNOSTICO_SINTOMA_ITEM ODSINT
     , servicos_tecnicos st
     , os_diagnostico od
     , OS_DIAGNOSTICO_SERVICO_ITEM ODSERV
where os.numero_os = $P{NUMERO_OS}
      and os.cod_empresa = $P{COD_EMPRESA}
      and oo.numero_os = os.numero_os
      and oo.cod_empresa = os.cod_empresa
      and ODSINT.numero_os = oo.numero_os
      and ODSINT.cod_empresa = oo.cod_empresa
      and ODSINT.item = oo.item
      and st.cod_tecnico(+) = ODSERV.cod_tecnico
      and st.cod_empresa(+) = ODSERV.cod_empresa
      and od.numero_os = os.numero_os
      and od.cod_empresa = os.cod_empresa
      and ODSERV.numero_os = oo.numero_os
      and ODSERV.cod_empresa = oo.cod_empresa
      and ODSERV.item = oo.item]]>
	</queryString>
	<field name="ITEM" class="java.math.BigDecimal"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="SERVICO_REALIZADO" class="java.lang.String"/>
	<field name="DATA_SERVICO_REALIZADO" class="java.sql.Timestamp"/>
	<field name="NOME" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="24" splitType="Prevent">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="24" uuid="7b1cf3c2-6d17-4ab8-9730-8c8ed4e1e4b1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="2" width="140" height="10" uuid="979e23fb-4d9e-4740-bc6b-b0c5ae34fe5b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Reparo]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="13" width="555" height="11" uuid="1d46bd96-6ae5-4613-9932-3d2ec1ba473e">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="170" height="10" uuid="bd6539f0-23aa-40a7-a195-75945cb10f30">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Observação do Serviço Realizado]]></text>
					</staticText>
				</frame>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="64" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement key="" x="0" y="0" width="555" height="64" uuid="209444c4-2584-47b0-9d73-b61e06668e60">
					<property name="ShowOutOfBoundContent" value="false"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="45" height="63" uuid="53f9a68d-0c7f-4678-bb9c-7fbef00a1218">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="241" y="50" width="35" height="10" uuid="c9cbe3f4-3370-4a9d-a626-23a86a7be1d7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[1==2]]></printWhenExpression>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Início:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="476" y="50" width="30" height="10" uuid="bcfc2db9-6d87-4466-9802-80f0f351f1b6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[1==2]]></printWhenExpression>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="314" y="50" width="30" height="10" uuid="0697a3e6-7840-480f-ad5f-1d114e462f41">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="394" y="50" width="44" height="10" uuid="8bfa7c2a-940c-4f6a-978e-90c589144a14">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[1==2]]></printWhenExpression>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Término:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="48" y="50" width="100" height="10" uuid="c79ee4d7-2477-4444-a6da-4dfa7b2c1f12">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Técnico Responsável:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="128" y="50" width="80" height="10" uuid="80469920-2bd9-44d4-9b55-b7322129a1e5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="48" y="5" width="495" height="40" uuid="eea12a5e-643f-4c28-8834-24da4a359d7d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Middle" markup="styled">
						<font size="7" isBold="false"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="0" leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SERVICO_REALIZADO} == null ? "<style isBold='true'>" + $F{DESCRICAO} +"</style>" 
 : "<style isBold='true'>" + $F{DESCRICAO} +"</style>" +  ": " +  $F{SERVICO_REALIZADO}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="339" y="50" width="51" height="10" uuid="f8a4e85d-62d8-4b5f-a282-9a50a4e0f28c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_SERVICO_REALIZADO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="498" y="50" width="51" height="10" uuid="b79c3809-f392-44ab-a9c5-7b0096a6a753">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[1==2]]></printWhenExpression>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[ainda nao definido]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="427" y="50" width="43" height="10" uuid="766d3b19-a32e-45e0-8f02-49495ad0d7c0">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[1==2]]></printWhenExpression>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[ainda nao definido]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="265" y="50" width="43" height="10" uuid="a4b0fc18-d959-475a-b506-cf6501b31e77">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[1==2]]></printWhenExpression>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[ainda nao definido]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band/>
	</columnFooter>
</jasperReport>
