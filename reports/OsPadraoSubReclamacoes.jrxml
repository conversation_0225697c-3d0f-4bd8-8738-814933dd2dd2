<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsPadraoSubReclamacoes" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="0efcef53-ba2f-4102-8b70-a807a9128687">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="344"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="646"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH OS_FABRICA_RELACIONADAS AS (
     SELECT * FROM TABLE(PKG_CRM_SERVICE_UTIL.Get_Table_os_Relac_Num_Fabrica($P{NUMERO_OS},$P{COD_EMPRESA}))
),
Q_OS_RECLAMACOES AS 
(SELECT DECODE(LENGTH(ITEM),2,ITEM,Null) AS ITEM, DESCRICAO, DT_INCLUSAO,
       Nvl((to_Char(DT_INCLUSAO, 'dd/mm/yy hh24:mi') || ' - ' || DECODE(LENGTH(ITEM),2,ITEM,Null) || ' - ' || DESCRICAO),DESCRICAO) Desc_Ford FROM (
SELECT SUBSTR(TO_CHAR(OS_ORIGINAL.ITEM + 100), 2, 2) AS ITEM,
       Decode(OS_TIPOS.GARANTIA,'S',(Nvl(OS_ORIGINAL.COD_GWM_RECLAMACAO,GWM.COD_RECLAMACAO) || DECODE(Nvl(OS_ORIGINAL.COD_GWM_RECLAMACAO,GWM.COD_RECLAMACAO), nULL, '', ' - ') || OS_ORIGINAL.DESCRICAO),    
             (GWM.COD_RECLAMACAO || DECODE(GWM.COD_RECLAMACAO, nULL, '', ' - ') || OS_ORIGINAL.DESCRICAO) )DESCRICAO, OS_ORIGINAL.DT_INCLUSAO
          FROM OS_ORIGINAL, GWM_RECLAMACAO GWM, OS, OS_TIPOS 
 WHERE /*((OS_ORIGINAL.NUMERO_OS = ;NUMERO_OS) 
   AND (OS_ORIGINAL.COD_EMPRESA = ;COD_EMPRESA))
   */
   /*AND (OS.NUMERO_OS = ;NUMERO_OS) 
   AND (OS.COD_EMPRESA = ;COD_EMPRESA)*/
   (OS_ORIGINAL.COD_EMPRESA,OS_ORIGINAL.NUMERO_OS) IN (SELECT * FROM OS_FABRICA_RELACIONADAS)
   AND (OS.COD_EMPRESA,OS.NUMERO_OS) IN (SELECT * FROM OS_FABRICA_RELACIONADAS)
    
   AND (OS_TIPOS.TIPO = OS.TIPO)
   AND (OS_ORIGINAL.COD_GWM_RECLAMACAO = GWM.COD_RECLAMACAO(+))

 UNION

SELECT SUBSTR(TO_CHAR(OS_ORIGINAL.ITEM + 100), 2, 2) || ' - ' || GARANTIA_FORD_CCC.COD_CCC AS ITEM,
        'CCC - '||GARANTIA_FORD_CCC.COD_CCC  || '  ' || GARANTIA_FORD_CCC.DESCRICAO AS DESCRICAO, OS_ORIGINAL.DT_INCLUSAO
  FROM OS_ORIGINAL,
       GARANTIA_FORD_CCC
 WHERE OS_ORIGINAL.COD_GWM_RECLAMACAO = GARANTIA_FORD_CCC.COD_CCC
   /* AND ((OS_ORIGINAL.NUMERO_OS = ;NUMERO_OS)
   AND (OS_ORIGINAL.COD_EMPRESA = ;COD_EMPRESA))*/
   AND (OS_ORIGINAL.COD_EMPRESA,OS_ORIGINAL.NUMERO_OS) IN (SELECT * FROM OS_FABRICA_RELACIONADAS) 

 UNION  

SELECT SUBSTR(TO_CHAR(T.ITEM + 100), 2, 2) || ' - ' || GARANTIA_FORD_CCC.COD_CCC AS ITEM,
        'CCC - '||GARANTIA_FORD_CCC.COD_CCC  || '  ' || GARANTIA_FORD_CCC.DESCRICAO AS DESCRICAO, T.DT_INCLUSAO
  FROM OS_SERVICOS T,
       GARANTIA_FORD_CCC
 WHERE T.COD_CCC = GARANTIA_FORD_CCC.COD_CCC
    /*AND ((T.NUMERO_OS  = ;NUMERO_OS)
   AND (T.COD_EMPRESA = ;COD_EMPRESA)) */
   AND (T.COD_EMPRESA,T.NUMERO_OS) IN (SELECT * FROM OS_FABRICA_RELACIONADAS)
ORDER BY ITEM))
SELECT * FROM Q_OS_RECLAMACOES]]>
	</queryString>
	<field name="ITEM" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="DT_INCLUSAO" class="java.sql.Timestamp"/>
	<field name="DESC_FORD" class="java.lang.String"/>
	<title>
		<band height="19" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="1" width="554" height="18" backcolor="#D9D9D9" uuid="8e72b655-22fe-4155-91d5-649e08d38a2b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="6" y="3" width="100" height="14" uuid="9d2bb599-f3d7-426e-9070-05b1a6d31f06">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Itens Originais]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="15" y="0" width="70" height="12" uuid="7240c292-72cd-4cb6-a6f8-2b1b71a62094"/>
				<textElement>
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="0" width="459" height="12" uuid="*************-4b40-80d1-0bfafdf89306"/>
				<textElement>
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band>
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</summary>
</jasperReport>
