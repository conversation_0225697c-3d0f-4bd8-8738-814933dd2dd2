<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklist" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="e0264917-65bd-4a1b-8ce5-0dffc30094c0">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<style name="vazio_quando_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<parameterDescription><![CDATA[codigo da empresa]]></parameterDescription>
		<defaultValueExpression><![CDATA[34.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[numero da OS]]></parameterDescription>
		<defaultValueExpression><![CDATA[25356.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[camimho para os subrelatorios, é passado via java]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\34676\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="IMP_FOTO_CHK_LIST" class="java.lang.String">
		<parameterDescription><![CDATA[caso deseja imprimir as imagens dos itens de checkList]]></parameterDescription>
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="APLICACAO" class="java.lang.String">
		<parameterDescription><![CDATA[Aplicação de checkList (R-recepção, O-oficina,E-entrega,S-sintomas,T-terceiros,P-pertence)]]></parameterDescription>
		<defaultValueExpression><![CDATA["E"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_SERVICO" class="java.lang.String">
		<parameterDescription><![CDATA[caso APLICACAO = 'T' então tem que passar um codigo de serviço]]></parameterDescription>
		<defaultValueExpression><![CDATA["0"]]></defaultValueExpression>
	</parameter>
	<parameter name="IMP_ITEM_AGRUPADO_FOTO" class="java.lang.String">
		<parameterDescription><![CDATA[caso seja passado 'S' então vai imprimir as imagens junto com os itens]]></parameterDescription>
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="TIPO" class="java.lang.String">
		<parameterDescription><![CDATA[tipo de aplicação (C-checklist, G-gerenciamento, P-Pertence)]]></parameterDescription>
		<defaultValueExpression><![CDATA["C"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_AGENDA" class="java.lang.String">
		<parameterDescription><![CDATA[caso seja agenda deve passar o codigo da agenda (atualmente somente para Sintomas)]]></parameterDescription>
		<defaultValueExpression><![CDATA["0"]]></defaultValueExpression>
	</parameter>
	<parameter name="MODALIDADE" class="java.lang.String">
		<parameterDescription><![CDATA[caso deseja pegar os dados da Agenda passar como "AGENDA" caso pegar os dados da OS passar "OS"]]></parameterDescription>
		<defaultValueExpression><![CDATA["OS"]]></defaultValueExpression>
	</parameter>
	<parameter name="ASSINAR_DIGITALMENTE" class="java.lang.String">
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT OS.COD_EMPRESA,
       OS.NUMERO_OS,
       OS.COD_OS_AGENDA,
       E.NOME EMPRESA,
       INITCAP(CONSULTOR.NOME_COMPLETO) CONSULTOR,
       DECODE(OS.ORCAMENTO, 'S', 'Orçamento. ', 'OS. ') ||
       ABS(OS.NUMERO_OS) || '    {Tipo:' ||
       DECODE(GET_TIPO_FABRICA(OS.COD_EMPRESA),
              'N',
              OS_TIPOS.TIPO,
              OS_TIPOS.TIPO_FABRICA) || '}' IITULO,
       (OS.DATA_EMISSAO || '  ' || OS.HORA_EMISSAO) EMISSAO,
       OS.DATA_ENTREGA,
       DECODE(OS.ORCAMENTO,
              'S',
              'Validade: ' || TO_CHAR(OS.VALIDADE, 'DD/MM'),
              ('Prometido: ' ||
              DECODE(TRUNC(SYSDATE) - TRUNC(OS.DATA_PROMETIDA),
                      0,
                      'Hoje',
                      TO_CHAR(OS.DATA_PROMETIDA, 'DD/MM')) || ' às ' ||
              OS.HORA_PROMETIDA)) PROMESSA,
       INITCAP(NVL(OS.CLIENTE_RAPIDO, CLIENTE_DIVERSO.NOME)) AS NOME_CLIENTE,
       NVL(CLIENTE_DIVERSO.CPF, CLIENTE_DIVERSO.CGC) AS CPF_CGC_CLIENTE,
       DECODE(OS_DADOS_VEICULOS.PLACA,
              NULL,
              '',
              (OS_DADOS_VEICULOS.PLACA || '   ')) ||
       INITCAP(PRODUTOS_MODELOS.DESCRICAO_MODELO) VEICULO,
       OS_DADOS_VEICULOS.ANO,
       OS_DADOS_VEICULOS.CHASSI,
       OS_DADOS_VEICULOS.KM,
     NVL(OS_DADOS_VEICULOS.COMBUSTIVEL,0) COMBUSTIVEL,
       OS_DADOS_VEICULOS.PLACA,
       MOB_OS_ASSINATURA.ASSINATURA_CLIENTE,
       MOB_OS_ASSINATURA.ASSINATURA AS ASSINATURA_CONSULTOR,
       MOB_OS_ASSINATURA.DATA_ASSINATURA_CLIENTE AS DATA_ASSINATURA_CLIENTE,
       MOB_OS_ASSINATURA.DATA_ASSINATURA AS DATA_ASSINATURA_CONSULTOR,
       CASE WHEN OS.NUMERO_OS < 0 THEN 
         OS.ORC_SERV_BRUTO
       ELSE
         OS.VALOR_SERVICOS_BRUTO 
       END AS VALOR_SERVICOS_BRUTO,
       CASE WHEN OS.NUMERO_OS < 0 THEN 
         OS.ORC_ITEM_BRUTO
       ELSE
         OS.VALOR_ITENS_BRUTO 
       END AS VALOR_ITENS_BRUTO,
       CASE WHEN OS.NUMERO_OS < 0 THEN 
         OS.ORC_SERV_BRUTO
       ELSE
         OS.VALOR_SERVICOS_BRUTO 
       END +
       CASE WHEN OS.NUMERO_OS < 0 THEN 
         OS.ORC_ITEM_BRUTO
       ELSE
         OS.VALOR_ITENS_BRUTO 
       END AS VALOR_TOTAL_ITENS_SERV,
       CASE
         WHEN (NOT E.FACHADA IS NULL) AND
              (NOT E.COMPLEMENTO IS NULL) THEN
          SUBSTR(E.RUA, 1, 50) || ', ' ||
          SUBSTR(E.FACHADA, 1, 15) || ', ' ||
          SUBSTR(E.COMPLEMENTO, 1, 15)
         WHEN (NOT E.FACHADA IS NULL) THEN
          SUBSTR(E.RUA, 1, 50) || ', ' ||
          SUBSTR(E.FACHADA, 1, 15)
         ELSE
          SUBSTR(E.RUA, 1, 50)
       END EMPRESA_ENDERECO,
       CASE
         WHEN (NOT E.CEP IS NULL) AND (NOT E.CIDADE IS NULL) AND
              (NOT E.ESTADO IS NULL) AND
              (NOT E.BAIRRO IS NULL) THEN
          E.CEP || ' ' || E.CIDADE || '-' || E.ESTADO || ', ' ||
          E.BAIRRO
         WHEN (NOT E.CEP IS NULL) AND (NOT E.CIDADE IS NULL) AND
              (NOT E.ESTADO IS NULL) THEN
          E.CEP || ' ' || E.CIDADE || '-' || E.ESTADO
         WHEN (NOT E.CEP IS NULL) AND (NOT E.CIDADE IS NULL) AND
              (NOT E.BAIRRO IS NULL) THEN
          E.CEP || ' ' || E.CIDADE || ', ' || E.BAIRRO
         WHEN (NOT E.CEP IS NULL) AND (NOT E.CIDADE IS NULL) THEN
          E.CEP || ' ' || E.CIDADE
         WHEN (NOT E.CEP IS NULL) THEN
          E.CEP
         ELSE
          ' '
       END EMPRESA_ENDERECO1,
       (SELECT COUNT(*)
        FROM MOB_OS_PERTENCE_FOTO F
        WHERE F.COD_EMPRESA = OS.COD_EMPRESA
         AND F.NUMERO_OS = OS.NUMERO_OS) QTDE_FOTOS,
       DECODE(OS.COD_CLIENTE,
              1,
              (A.CLIENTE_DDD_CEL || '-' || A.CLIENTE_FONE_CEL),
              (CLIENTES.PREFIXO_CEL || '-' || CLIENTES.TELEFONE_CEL)) TEL_CEL,                  
       nvl((select p.tipo_concessionaria from parm_sys p where p.cod_empresa = a.cod_empresa),60) as tipo_concessionaria
  FROM OS,
       OS_DADOS_VEICULOS,
       EMPRESAS          E,
       PRODUTOS_MODELOS,
       CLIENTE_DIVERSO,
       CLIENTES,
       OS_TIPOS,
       EMPRESAS_USUARIOS CONSULTOR,
       OS_AGENDA A,
       MOB_OS_ASSINATURA
 WHERE OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS
   AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA
   AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
   AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
   AND OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE
   AND OS.QUEM_ABRIU = CONSULTOR.NOME(+)
   AND OS.COD_EMPRESA = E.COD_EMPRESA
   AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
   AND OS.TIPO = OS_TIPOS.TIPO
   AND OS.COD_EMPRESA = A.COD_EMPRESA(+)
   AND OS.COD_OS_AGENDA = A.COD_OS_AGENDA(+)
   AND OS.NUMERO_OS = $P{NUMERO_OS} 
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND $P{MODALIDADE} = 'OS'
   AND OS.NUMERO_OS = MOB_OS_ASSINATURA.NUMERO_OS(+)
   AND OS.COD_EMPRESA = MOB_OS_ASSINATURA.COD_EMPRESA(+)
   AND MOB_OS_ASSINATURA.APLICACAO(+) = $P{APLICACAO}
UNION ALL
SELECT A.COD_EMPRESA,
       A.NUMERO_OS as NUMERO_OS,
       A.COD_OS_AGENDA,
       E.NOME EMPRESA,
       INITCAP(CONSULTOR.NOME_COMPLETO) CONSULTOR,
       'Agenda. ' ||
       A.COD_OS_AGENDA AS  IITULO,
       TO_CHAR(A.DATA_ABRIDA,'DD/MM/YY HH24:MI') AS EMISSAO,
       null AS DATA_ENTREGA,
       'Prometido: ' ||
              DECODE(TRUNC(SYSDATE) - TRUNC(A.DATA_PROMETIDA),
                      0,
                      'Hoje',
                      TO_CHAR(A.DATA_ABRIDA, 'DD/MM') || ' às ' ||
              TO_CHAR(A.DATA_ABRIDA, 'HH24:MI')) PROMESSA,
       INITCAP(CLIENTE_DIVERSO.NOME) AS NOME_CLIENTE,
       NVL(CLIENTE_DIVERSO.CPF, CLIENTE_DIVERSO.CGC) AS CPF_CGC_CLIENTE,
       DECODE(A.PLACA,
              NULL,
              '',
              (A.PLACA || '   ')) ||
       INITCAP(PRODUTOS_MODELOS.DESCRICAO_MODELO) VEICULO,
       A.ANO,
       A.CHASSI,
       A.KM,
     NVL(A.COMBUSTIVEL,0) COMBUSTIVEL,
       A.PLACA,
       null as ASSINATURA_CLIENTE,
       null as ASSINATURA_CONSULTOR,
       null AS DATA_ASSINATURA_CLIENTE,
       null AS DATA_ASSINATURA_CONSULTOR,
       null AS VALOR_SERVICOS_BRUTO,
       0 AS VALOR_ITENS_BRUTO,
       0 AS VALOR_TOTAL_ITENS_SERV,
       CASE
         WHEN (NOT E.FACHADA IS NULL) AND
              (NOT E.COMPLEMENTO IS NULL) THEN
          SUBSTR(E.RUA, 1, 50) || ', ' ||
          SUBSTR(E.FACHADA, 1, 15) || ', ' ||
          SUBSTR(E.COMPLEMENTO, 1, 15)
         WHEN (NOT E.FACHADA IS NULL) THEN
          SUBSTR(E.RUA, 1, 50) || ', ' ||
          SUBSTR(E.FACHADA, 1, 15)
         ELSE
          SUBSTR(E.RUA, 1, 50)
       END EMPRESA_ENDERECO,
       CASE
         WHEN (NOT E.CEP IS NULL) AND (NOT E.CIDADE IS NULL) AND
              (NOT E.ESTADO IS NULL) AND
              (NOT E.BAIRRO IS NULL) THEN
          E.CEP || ' ' || E.CIDADE || '-' || E.ESTADO || ', ' ||
          E.BAIRRO
         WHEN (NOT E.CEP IS NULL) AND (NOT E.CIDADE IS NULL) AND
              (NOT E.ESTADO IS NULL) THEN
          E.CEP || ' ' || E.CIDADE || '-' || E.ESTADO
         WHEN (NOT E.CEP IS NULL) AND (NOT E.CIDADE IS NULL) AND
              (NOT E.BAIRRO IS NULL) THEN
          E.CEP || ' ' || E.CIDADE || ', ' || E.BAIRRO
         WHEN (NOT E.CEP IS NULL) AND (NOT E.CIDADE IS NULL) THEN
          E.CEP || ' ' || E.CIDADE
         WHEN (NOT E.CEP IS NULL) THEN
          E.CEP
         ELSE
          ' '
       END EMPRESA_ENDERECO1,
       0 QTDE_FOTOS,
       DECODE(A.TIPO_ENDERECO,
              1,
              (A.CLIENTE_DDD_CEL || '-' || A.CLIENTE_FONE_CEL),
              (CLIENTES.PREFIXO_CEL || '-' || CLIENTES.TELEFONE_CEL)) TEL_CEL,                  
       nvl((select p.tipo_concessionaria from parm_sys p where p.cod_empresa = a.cod_empresa),60) as tipo_concessionaria
  FROM EMPRESAS          E,
       PRODUTOS_MODELOS,
       CLIENTE_DIVERSO,
       CLIENTES,
       EMPRESAS_USUARIOS CONSULTOR,
       OS_AGENDA A
 WHERE A.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
   AND A.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
   AND A.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE
   AND A.QUEM_ABRIU = CONSULTOR.NOME(+)
   AND A.COD_EMPRESA = E.COD_EMPRESA (+)
   AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
   AND A.COD_OS_AGENDA = $P{COD_AGENDA}
   AND A.COD_EMPRESA = $P{COD_EMPRESA}
   AND $P{MODALIDADE} = 'AGENDA']]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="EMPRESA" class="java.lang.String"/>
	<field name="CONSULTOR" class="java.lang.String"/>
	<field name="IITULO" class="java.lang.String"/>
	<field name="EMISSAO" class="java.lang.String"/>
	<field name="DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="PROMESSA" class="java.lang.String"/>
	<field name="NOME_CLIENTE" class="java.lang.String"/>
	<field name="CPF_CGC_CLIENTE" class="java.lang.String"/>
	<field name="VEICULO" class="java.lang.String"/>
	<field name="ANO" class="java.lang.String"/>
	<field name="CHASSI" class="java.lang.String"/>
	<field name="KM" class="java.lang.Double"/>
	<field name="COMBUSTIVEL" class="java.lang.Double"/>
	<field name="PLACA" class="java.lang.String"/>
	<field name="ASSINATURA_CLIENTE" class="java.awt.Image"/>
	<field name="ASSINATURA_CONSULTOR" class="java.awt.Image"/>
	<field name="DATA_ASSINATURA_CLIENTE" class="java.sql.Timestamp"/>
	<field name="DATA_ASSINATURA_CONSULTOR" class="java.sql.Timestamp"/>
	<field name="VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="VALOR_TOTAL_ITENS_SERV" class="java.lang.Double"/>
	<field name="EMPRESA_ENDERECO" class="java.lang.String"/>
	<field name="EMPRESA_ENDERECO1" class="java.lang.String"/>
	<field name="QTDE_FOTOS" class="java.lang.Double"/>
	<field name="TEL_CEL" class="java.lang.String"/>
	<field name="TIPO_CONCESSIONARIA" class="java.lang.Double"/>
	<title>
		<band height="164" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="4" y="4" width="548" height="160" isRemoveLineWhenBlank="true" uuid="45512d86-52f9-4aa6-8101-bc7ba322f7cb"/>
				<textField>
					<reportElement x="181" y="3" width="180" height="18" uuid="533c9009-cd43-4f9c-ac36-0282b35f9bf2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="352" y="63" width="50" height="18" uuid="59f27e1b-b60b-42d3-96c8-5261559f460b"/>
					<textElement textAlignment="Left">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Consultor:]]></text>
				</staticText>
				<textField>
					<reportElement x="352" y="83" width="100" height="18" uuid="81effe13-41c5-4384-a13c-f696c45d2974">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textFieldExpression><![CDATA[$F{CONSULTOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="470" y="29" width="74" height="14" uuid="3e7c8cad-44bc-4361-8a9b-69d748e1d36d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left"/>
					<textFieldExpression><![CDATA[$F{EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="63" width="100" height="18" uuid="bfe246c1-3f93-41dd-875e-c360922b0e3c">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Cliente]]></text>
				</staticText>
				<textField>
					<reportElement x="4" y="83" width="174" height="18" uuid="a23fab85-15ed-4d29-87e6-2ec95c47c3b3">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement>
						<font fontName="SansSerif"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="104" width="53" height="18" uuid="1f179c2a-21a3-4dfc-acb5-613be226e67f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Celular]]></text>
				</staticText>
				<textField>
					<reportElement x="4" y="125" width="116" height="18" uuid="6aa57d32-c6d4-46e3-93e6-fa5dcd7abddc">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textFieldExpression><![CDATA[$F{TEL_CEL}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="183" y="63" width="100" height="18" uuid="23f2f0b9-b325-45d0-8eb4-906fea9e04d1">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Veiculo]]></text>
				</staticText>
				<textField>
					<reportElement x="183" y="83" width="162" height="18" uuid="607f3ed8-8cb1-4a90-88d3-d4c2f3988d91">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textFieldExpression><![CDATA[$F{VEICULO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="183" y="104" width="40" height="18" uuid="ef56838b-f53f-49d6-8c10-cbff40ce0a87">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Chassi]]></text>
				</staticText>
				<textField>
					<reportElement x="183" y="125" width="162" height="18" uuid="4ffffedc-bd30-4174-8534-d4a9260eb429"/>
					<textFieldExpression><![CDATA[$F{CHASSI}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="352" y="104" width="40" height="18" uuid="7e0407d7-e523-4cb9-b5cf-d7e9f29aa4d2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Placa]]></text>
				</staticText>
				<textField>
					<reportElement x="352" y="125" width="100" height="18" isRemoveLineWhenBlank="true" uuid="5c35e1a3-fc6c-4d38-828d-caafe1d667af">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<printWhenExpression><![CDATA[!$F{PLACA}.equals(" ")]]></printWhenExpression>
					</reportElement>
					<textFieldExpression><![CDATA[$F{PLACA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="-1" y="59" width="547" height="1" forecolor="#3668FF" uuid="9b492e18-1260-41fc-9a02-2e053a27dfaf">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="2.0" lineColor="#006FFF"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="-2" y="151" width="547" height="1" forecolor="#3668FF" uuid="5b57b8a8-4e9e-4474-b3fb-9339c94debca">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="2.0" lineColor="#006FFF"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement mode="Opaque" x="181" y="35" width="180" height="14" forecolor="#3668FF" uuid="78f3097f-7ae4-496d-8da9-533b859f0515"/>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESA_ENDERECO1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="181" y="22" width="180" height="12" forecolor="#3668FF" uuid="56340ed6-4974-497b-8fb1-be9554a89eba"/>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESA_ENDERECO}]]></textFieldExpression>
				</textField>
				<subreport overflowType="Stretch">
					<reportElement positionType="Float" x="1" y="3" width="178" height="44" isRemoveLineWhenBlank="true" uuid="322298ac-bf40-4eb9-925c-1365fc6b2a75">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"ComprovanteChecklistLogo.jasper"]]></subreportExpression>
				</subreport>
				<textField pattern="">
					<reportElement x="370" y="0" width="174" height="15" uuid="46ae3212-ec3a-4e3d-b32d-8c42be0673f6">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{APLICACAO}.equals("R")? $F{TIPO_CONCESSIONARIA}.equals(16) ? "Ficha de Entrada" : "Checklist Entrada":
$P{APLICACAO}.equals("O")?  $F{TIPO_CONCESSIONARIA}.equals(16) ? "Check List Técnico":"Checklist Oficina":
$P{APLICACAO}.equals("T")?"Checklist Terceiro":
$P{APLICACAO}.equals("E")?"Checklist Entrega":
$P{APLICACAO}.equals("S")?"Checklist Sintomas":
"Checklist"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="462" y="63" width="50" height="18" uuid="98e39fc7-acdb-40b9-8040-3acb2a3eab2a"/>
					<textElement textAlignment="Left">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[KM]]></text>
				</staticText>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement x="462" y="83" width="74" height="18" uuid="a01cd308-9833-4bdf-9ff8-4181aee5cf0d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textFieldExpression><![CDATA[$F{KM}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="462" y="125" width="74" height="17" uuid="ba34e027-a62d-423a-812b-a72a4a4014d8"/>
					<textField>
						<reportElement x="0" y="0" width="74" height="17" uuid="6f23e60d-11bc-4bad-8ffc-b99e730a8701"/>
						<box topPadding="1" leftPadding="6">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{COMBUSTIVEL} < 19 ? "":
$F{COMBUSTIVEL} < 39 ? "X" :
$F{COMBUSTIVEL} < 59 ? "X      X":
$F{COMBUSTIVEL} < 79 ? "X      X      X":
 "X      X      X      X"]]></textFieldExpression>
					</textField>
					<line>
						<reportElement x="64" y="13" width="1" height="4" uuid="ebb4130a-6344-412a-8eae-3b7231707b77">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="1.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="27" y="13" width="1" height="4" uuid="1900fb1c-6c39-4d0f-b94e-eb2927f79545">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="1.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="46" y="13" width="1" height="4" uuid="264d1352-566d-4a38-b625-7b87aeaf1886">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="1.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="19" y="11" width="1" height="6" uuid="38839a77-6b79-4a83-9ee4-687059d8eb06">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="1.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="9" y="13" width="1" height="4" uuid="43a1b926-e719-4935-955d-394bcb8d8de4">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="1.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="55" y="11" width="1" height="6" uuid="657ad71c-1813-4c54-9650-60b3dbd73e95">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="1.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="37" y="9" width="1" height="8" uuid="673952f2-bf41-4efa-8c55-784a37dcadb7">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="2.0"/>
						</graphicElement>
					</line>
				</frame>
				<staticText>
					<reportElement x="462" y="104" width="64" height="18" uuid="cfb47693-d20c-48cd-8538-0051e0d2e09b"/>
					<textElement textAlignment="Left">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Combustível]]></text>
				</staticText>
				<textField pattern="dd/MM/yy  HH:mm">
					<reportElement x="470" y="43" width="74" height="14" uuid="6383d5a6-1ce8-4a71-971d-a83dbeab0b81">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<printWhenExpression><![CDATA[!$P{MODALIDADE}.equals("AGENDA")]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left"/>
					<textFieldExpression><![CDATA[$F{DATA_ENTREGA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="427" y="29" width="43" height="14" uuid="ebe48acd-083f-4993-b7d5-746dae387698">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left"/>
					<text><![CDATA[Emissão:]]></text>
				</staticText>
				<staticText>
					<reportElement x="427" y="43" width="43" height="14" uuid="8db8ba66-9a4b-42ae-945a-1375f9ece8c7">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<printWhenExpression><![CDATA[!$P{MODALIDADE}.equals("AGENDA")]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left"/>
					<text><![CDATA[Entrega:]]></text>
				</staticText>
				<textField>
					<reportElement x="370" y="15" width="174" height="14" uuid="38f54050-4bc4-428c-a1a8-5214ca763f0d"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{IITULO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</title>
	<detail>
		<band height="11" splitType="Stretch">
			<subreport>
				<reportElement x="0" y="0" width="549" height="10" isRemoveLineWhenBlank="true" uuid="658df959-daae-4a1a-8681-9d5a61aa89e8"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="APLICACAO">
					<subreportParameterExpression><![CDATA[$P{APLICACAO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "ComprovanteChecklistImagem.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="11" splitType="Immediate">
			<subreport>
				<reportElement positionType="Float" x="0" y="0" width="550" height="11" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="6a465928-6b33-4815-9844-caa2806bb7b2"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="APLICACAO">
					<subreportParameterExpression><![CDATA[$P{APLICACAO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SERVICO">
					<subreportParameterExpression><![CDATA[$P{COD_SERVICO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="IMP_ITEM_AGRUPADO_FOTO">
					<subreportParameterExpression><![CDATA[$P{IMP_ITEM_AGRUPADO_FOTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TIPO">
					<subreportParameterExpression><![CDATA[$P{TIPO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_AGENDA">
					<subreportParameterExpression><![CDATA[$P{COD_AGENDA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="MODALIDADE">
					<subreportParameterExpression><![CDATA[$P{MODALIDADE}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "ComprovanteChecklistItem.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="12" splitType="Stretch">
			<subreport>
				<reportElement positionType="Float" x="0" y="4" width="550" height="7" isRemoveLineWhenBlank="true" uuid="d7b820b2-aa65-4c7d-a200-e084ce3e98cb">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<printWhenExpression><![CDATA[$P{IMP_ITEM_AGRUPADO_FOTO}.equals("N")]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="APLICACAO">
					<subreportParameterExpression><![CDATA[$P{APLICACAO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"ComprovanteChecklistFotos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="98" splitType="Prevent">
			<printWhenExpression><![CDATA[new Boolean($P{MODALIDADE}.equals("OS"))]]></printWhenExpression>
			<image scaleImage="RealHeight" hAlign="Center" vAlign="Middle" isUsingCache="false" onErrorType="Blank">
				<reportElement x="10" y="14" width="250" height="54" isRemoveLineWhenBlank="true" uuid="6cab57df-31cc-44a6-a451-66a01d749607">
					<printWhenExpression><![CDATA[!$P{APLICACAO}.equals("T")]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[$P{ASSINAR_DIGITALMENTE}.equals("S")?"":$F{ASSINATURA_CLIENTE}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="10" y="70" width="250" height="21" uuid="a8122b07-912e-4bc0-9872-fde85e2e3cc5">
					<printWhenExpression><![CDATA[!$P{APLICACAO}.equals("T")]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Assinatura do Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="61" y="46" width="149" height="22" forecolor="#FFFFFF" uuid="ad84c214-7c36-4d1e-a94a-63d3b1c42d3c"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font size="4"/>
				</textElement>
				<text><![CDATA[#CLIENTE]]></text>
			</staticText>
			<image scaleImage="RealHeight" hAlign="Center" vAlign="Middle" isUsingCache="false" onErrorType="Blank">
				<reportElement x="280" y="14" width="250" height="54" isRemoveLineWhenBlank="true" uuid="31c26f3b-aa28-4b7c-8a76-abe728143312">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<printWhenExpression><![CDATA[!$P{APLICACAO}.equals("T")]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[$P{ASSINAR_DIGITALMENTE}.equals("S")?"":$F{ASSINATURA_CONSULTOR}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="280" y="70" width="250" height="21" uuid="9f611d38-8db6-4bdc-bd0c-d3793eb9562c">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<printWhenExpression><![CDATA[!$P{APLICACAO}.equals("T")]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center">
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{APLICACAO}.equals("O")?"Assinatura do Técnico":
"Assinatura do Consultor"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="330" y="46" width="149" height="22" forecolor="#FFFFFF" uuid="a3832f64-0d57-4624-ba33-e32b9c4b7850">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font size="4"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{APLICACAO}.equals("O")?"#TECNICO":"#CONSULTOR"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
