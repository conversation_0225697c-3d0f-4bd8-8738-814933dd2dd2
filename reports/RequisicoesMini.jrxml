<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RequisicoesMini" pageWidth="186" pageHeight="842" columnWidth="186" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a5d195c3-46e2-4e2e-9575-573e21d1a687">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="false"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="570"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="416"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\31631\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="IMPRIMIR_DADOS_CLIENTE" class="java.lang.String">
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="IMPRIMIR_DADOS_VEICULO" class="java.lang.String">
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="RECADO_DESCRICAO" class="java.lang.String"/>
	<parameter name="IMPRIMIR_LOCACAO" class="java.lang.String">
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="IMPRIMIR_DEFEITO" class="java.lang.String">
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="ORDENAR_POR_CODITEM_OU_LOCACAO" class="java.lang.String">
		<defaultValueExpression><![CDATA["CODITEM"]]></defaultValueExpression>
	</parameter>
	<parameter name="MOSTRAR_CODITEM_OU_PARTNUMBER" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["CODITEM"]]></defaultValueExpression>
	</parameter>
	<parameter name="REQUISICAO" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SQL_FILTRO_REQUISICAO" class="java.lang.String"/>
	<queryString>
		<![CDATA[Select    user as usuario,
				decode(to_char(os.numero_os_fabrica),
              				null,
			               to_char(os.numero_os),
             			   os.numero_os_fabrica || '/' || to_char(os.numero_os)) as numero_os_id,
                os_requisicoes.requisicao,
                os_requisicoes.cod_empresa,
                os_requisicoes.nome,
                eu.nome_completo,
                os_requisicoes.cod_tecnico,
                os_requisicoes.numero_os,
                os_requisicoes.docagenda,
                os.nome as consultor,
                os_requisicoes.data,
                os_requisicoes.qtde_reimpressao,
                sysdate as data_atual,
                decode(empresas.cod_matriz,
                       null,
                       empresas.nome,
                       empresa_matriz.nome) nome_matriz,
                decode(empresas.cod_matriz, null, '', empresas.nome) nome_filial,
                os.tipo,
                os_tipos.tipo_fabrica,
                decode(os_tipos.tipo_fabrica,null,os.tipo, os.tipo || '/' || os_tipos.tipo_fabrica) tipo_os,
                os_tipos.garantia,
                cliente_diverso.cod_tipo_cliente,
                nvl(os.cliente_rapido, cliente_diverso.nome) as nome_cliente,
                decode(cliente_diverso.cpf,
                       null,
                       cliente_diverso.cgc,
                       cliente_diverso.cpf) cgc_cpf_cliente,
                decode(os.tipo_endereco,
                       1,
                       Cliente_diverso.uf,
                       2,
                       Clientes.uf_res,
                       3,
                       Clientes.uf_com,
                       4,
                       Clientes.uf_cobranca,
                       5,
                       Endereco_por_inscricao.uf,
                       null) uf_cliente,
                decode(os.tipo_endereco,
                       1,
                       Cidades_div.descricao,
                       2,
                       Cidades_res.descricao,
                       3,
                       Cidades_com.descricao,
                       4,
                       Cidades_cobranca.descricao,
                       5,
                       Endereco_por_inscricao.cidade,
                       null) cidade_cliente,
                decode(os.tipo_endereco,
                       1,
                       Cliente_diverso.bairro,
                       2,
                       Clientes.bairro_res,
                       3,
                       Clientes.bairro_com,
                       4,
                       Clientes.bairro_cobranca,
                       5,
                       Endereco_por_inscricao.bairro,
                       null) bairro_cliente,
                decode(os.tipo_endereco,
                       1,
                       Cliente_diverso.cep,
                       2,
                       Clientes.cep_res,
                       3,
                       Clientes.cep_com,
                       4,
                       Clientes.cep_cobranca,
                       5,
                       Endereco_por_inscricao.cep,
                       null) cep_cliente,
                decode(os.tipo_endereco,
                       1,
                       Cliente_diverso.endereco,
                       2,
                       Clientes.rua_res,
                       3,
                       Clientes.rua_com,
                       4,
                       Clientes.rua_cobranca,
                       5,
                       Endereco_por_inscricao.rua,
                       null) endereco_cliente,
                decode(os.tipo_endereco,
                       1,
                       Cliente_diverso.complemento,
                       2,
                       Clientes.complemento_res,
                       3,
                       Clientes.complemento_com,
                       4,
                       Clientes.complemento_cobranca,
                       5,
                       Endereco_por_inscricao.complemento,
                       null) complemento_cliente,
                decode(sign(length(os.cliente_rapido)),
                       1,
                       Os.cliente_rapido_fone,
                       decode(os.tipo_endereco,
                              1,
                              Cliente_diverso.fone_contato,
                              2,
                              Clientes.telefone_res,
                              3,
                              Clientes.telefone_com,
                              4,
                              Clientes.telefone_com,
                              5,
                              Endereco_por_inscricao.telefone_contato,
                              null)) as fone_cliente,
                decode(sign(length(os.cliente_rapido)),
                       1,
                       Null,
                       decode(os.tipo_endereco,
                              1,
                              Cliente_diverso.prefixo_fone_contato,
                              2,
                              Clientes.prefixo_res,
                              3,
                              Clientes.prefixo_com,
                              4,
                              Clientes.prefixo_com,
                              5,
                              Endereco_por_inscricao.prefixo_telefone_contato,
                              null)) as prefixo_cliente,
                empresas.fuso_horario,
                os.numero_os_fabrica,
                nvl(os_tipos_empresas.tipo_fabrica_descricao,
                    os_tipos.tipo_fabrica) as tipo_os_emp,
                nvl(os_tipos_empresas.titulo_requisicao, 'N') as titulo_requisicao,
                produtos.descricao_produto,
		       os_dados_veiculos.placa,
		       os_dados_veiculos.prisma,
		       os_dados_veiculos.chassi,
		       st.nome as produtivo      
  from os_requisicoes,
       empresas,
       empresas               empresa_matriz,
       os,
       os_dados_veiculos,
       produtos,
       os_tipos,
       os_tipos_empresas,
       empresas_usuarios      eu,
       cliente_diverso,
       clientes,
       cidades                cidades_com,
       cidades                cidades_res,
       cidades                cidades_div,
       cidades                cidades_cobranca,
       endereco_por_inscricao,
       servicos_tecnicos st

 Where (os_requisicoes.numero_os = os.numero_os)
   and (os_requisicoes.cod_empresa = os.cod_empresa)
   and (os_requisicoes.cod_empresa = empresas.cod_empresa)
   and (empresas.cod_matriz = empresa_matriz.cod_empresa(+))
   and os_requisicoes.nome = eu.nome
   and (os.cod_cliente = cliente_diverso.cod_cliente)
   and (os.cod_cliente = clientes.cod_cliente(+))
   and (cliente_diverso.cod_cidades = cidades_div.cod_cidades(+))
   and (clientes.cod_cid_res = cidades_res.cod_cidades(+))
   and (clientes.cod_cid_com = cidades_com.cod_cidades(+))
   and (clientes.cod_cid_cobranca = cidades_cobranca.cod_cidades(+))
   and (os.inscricao_estadual = endereco_por_inscricao.inscricao_estadual(+))
   and (os.tipo = os_tipos.tipo(+))
   and (os.cod_empresa = os_tipos_empresas.cod_empresa(+))
   and (os.tipo = os_tipos_empresas.tipo(+))
   and (os.cod_empresa = os_dados_veiculos.cod_empresa)
   and (os.numero_os = os_dados_veiculos.numero_os)
   and (os_dados_veiculos.cod_produto = produtos.cod_produto)
   and (os_requisicoes.cod_empresa = st.cod_empresa(+))
   and (os_requisicoes.cod_tecnico = st.cod_tecnico(+))
   and (os_requisicoes.cod_empresa = $P{COD_EMPRESA})
   and (os_requisicoes.numero_os = $P{NUMERO_OS})
   and rownum = 1]]>
	</queryString>
	<field name="USUARIO" class="java.lang.String"/>
	<field name="NUMERO_OS_ID" class="java.lang.String"/>
	<field name="REQUISICAO" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="NOME" class="java.lang.String"/>
	<field name="NOME_COMPLETO" class="java.lang.String"/>
	<field name="COD_TECNICO" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="DOCAGENDA" class="java.lang.Double"/>
	<field name="CONSULTOR" class="java.lang.String"/>
	<field name="DATA" class="java.sql.Timestamp"/>
	<field name="QTDE_REIMPRESSAO" class="java.lang.Double"/>
	<field name="DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="NOME_MATRIZ" class="java.lang.String"/>
	<field name="NOME_FILIAL" class="java.lang.String"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="TIPO_FABRICA" class="java.lang.String"/>
	<field name="TIPO_OS" class="java.lang.String"/>
	<field name="GARANTIA" class="java.lang.String"/>
	<field name="COD_TIPO_CLIENTE" class="java.lang.Double"/>
	<field name="NOME_CLIENTE" class="java.lang.String"/>
	<field name="CGC_CPF_CLIENTE" class="java.lang.String"/>
	<field name="UF_CLIENTE" class="java.lang.String"/>
	<field name="CIDADE_CLIENTE" class="java.lang.String"/>
	<field name="BAIRRO_CLIENTE" class="java.lang.String"/>
	<field name="CEP_CLIENTE" class="java.lang.String"/>
	<field name="ENDERECO_CLIENTE" class="java.lang.String"/>
	<field name="COMPLEMENTO_CLIENTE" class="java.lang.String"/>
	<field name="FONE_CLIENTE" class="java.lang.String"/>
	<field name="PREFIXO_CLIENTE" class="java.lang.String"/>
	<field name="FUSO_HORARIO" class="java.lang.Double"/>
	<field name="NUMERO_OS_FABRICA" class="java.lang.Double"/>
	<field name="TIPO_OS_EMP" class="java.lang.String"/>
	<field name="TITULO_REQUISICAO" class="java.lang.String"/>
	<field name="DESCRICAO_PRODUTO" class="java.lang.String"/>
	<field name="PLACA" class="java.lang.String"/>
	<field name="PRISMA" class="java.lang.String"/>
	<field name="CHASSI" class="java.lang.String"/>
	<field name="PRODUTIVO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="22">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="1" width="186" height="21" uuid="8a40baa9-8f01-464a-978e-4a8d6db35165">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QTDE_REIMPRESSAO} == null?"REQUISICÕES DE O.S.":"REIMPRESSÃO DE REQUISIÇÕES DE O.S."]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="54">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="1" width="186" height="53" uuid="03ed2bcc-a8be-46c6-9801-d93339a84294"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy - HH:mm" isBlankWhenNull="true">
					<reportElement positionType="Float" x="87" y="43" width="98" height="10" uuid="7193de7d-aeb3-456f-a6da-b5d660983b9b">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QTDE_REIMPRESSAO} == null ? $F{DATA} : $F{DATA_ATUAL}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="32" width="84" height="10" uuid="8c8a2017-76dc-437f-8a6e-7373a141e093">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Data/Hora Impressão..:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="37" y="22" width="67" height="10" uuid="6e83bba8-008f-4069-bc27-785ba5734f5b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NUMERO_OS_ID}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="11" width="33" height="10" uuid="bef36668-540b-4885-b064-bed21aaf919a">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Filial..:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="102" y="22" width="35" height="10" uuid="42f6b18f-a45c-4321-80e2-c82d79fef736">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo/Fáb:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="2" y="43" width="84" height="10" uuid="e9df5946-7ebc-4447-a90f-690142db2f14">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Data/Hora Impressão..:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="2" y="22" width="33" height="10" uuid="f06b75b4-ec55-4c33-b872-cca7c9cd6118">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[O.S/ID..:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="37" y="0" width="149" height="10" uuid="cea27488-47c8-4a3b-8344-891b0e0e53a7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
						<font fontName="Calibri" size="8" isBold="false" pdfEncoding=""/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_MATRIZ}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="37" y="11" width="149" height="10" uuid="8cd8a45d-1dae-49b1-aef5-9e99351e9ec8">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_FILIAL}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="141" y="22" width="45" height="10" uuid="e91a9715-2fbf-4a7b-b880-d47b97a9f725">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TIPO_OS}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy - HH:mm" isBlankWhenNull="true">
					<reportElement positionType="Float" x="87" y="32" width="98" height="10" uuid="bf901303-64da-4be0-85f2-eefb19c62fae">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="0" width="36" height="10" uuid="f44b42f4-104a-4e2d-86c4-991db2904f1d">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Empresa:]]></text>
				</staticText>
			</frame>
		</band>
		<band height="22">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$P{REQUISICAO} == null && $P{IMPRIMIR_DADOS_VEICULO}.equals("S")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="186" height="20" uuid="999b6834-29dd-4529-a1c3-3142d5480df2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="37" y="12" width="47" height="10" uuid="5eb9bd7e-3beb-44e8-8a83-5466c95813f5">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PLACA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="1" width="33" height="10" uuid="c6ff69ee-e881-4ae2-bd24-029e76467b9f">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Produto.:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="108" y="12" width="76" height="10" uuid="446365f5-e4b4-4970-acf4-d3cc8c4c9451">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="82" y="12" width="26" height="10" uuid="ce09fc3d-01cd-4cf0-ba50-ba0aa2151a22">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Chassi:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="37" y="1" width="149" height="10" uuid="ad2f4f64-0fbe-4951-a365-a7186da57ac9">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_PRODUTO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="12" width="33" height="10" uuid="bd61c586-2202-432f-8538-2d75f6bdc332">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Placa...:]]></text>
				</staticText>
			</frame>
		</band>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$P{REQUISICAO} != null && $P{IMPRIMIR_DADOS_VEICULO}.equals("S")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="186" height="11" uuid="fbce10d4-c5a1-4ef4-ba62-5d7ebba7d8cb">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement positionType="Float" x="2" y="1" width="33" height="10" uuid="d637905e-a71e-45b0-b363-250e19396b2d">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Produto.:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="115" y="1" width="24" height="10" uuid="1ef356b5-0e38-400f-8275-6cfe0d4120ac">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Placa:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="139" y="1" width="46" height="10" uuid="d900e967-1750-417a-8783-1a27462519ad">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PLACA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="37" y="1" width="79" height="10" uuid="9437efd0-1e08-4ce6-ad37-d9cd9786a754"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_PRODUTO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="36">
			<printWhenExpression><![CDATA[$P{REQUISICAO} != null && $P{IMPRIMIR_DADOS_VEICULO}.equals("S")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="186" height="36" uuid="e688397c-8791-4c7b-ba64-f778127e1e54"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement positionType="Float" x="2" y="1" width="40" height="10" uuid="28dfaf7c-6318-4c81-b6fb-13daae9dee03">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Consultor:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="39" y="1" width="145" height="10" uuid="1e8278fa-b41c-4df6-98c2-1b4d168dc38d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONSULTOR}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="13" width="40" height="10" uuid="cf98bb7d-09c0-434c-95c5-782b58806e6a">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Vendedor:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="39" y="13" width="145" height="10" uuid="23afd496-25a9-4c63-bf8e-35a7c1a86a82">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_COMPLETO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="24" width="40" height="10" uuid="*************-4898-a9e5-7c9a901a35de">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Produtivo:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="39" y="24" width="145" height="10" uuid="ce17bb64-a611-40b9-a2bf-d062643f8b89">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRODUTIVO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="1">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<line>
				<reportElement x="0" y="0" width="186" height="1" uuid="365d06bf-f48c-4c05-8b2d-eb33d944b2a4"/>
				<graphicElement>
					<pen lineWidth="0.45" lineStyle="Dashed"/>
				</graphicElement>
			</line>
		</band>
		<band height="35">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$P{IMPRIMIR_DADOS_CLIENTE}.equals("S")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="186" height="35" uuid="7e22e74b-fc61-495f-8871-5de3a4c13fb4">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement positionType="Float" x="2" y="13" width="40" height="11" uuid="17481e2b-42ff-4a73-96fd-88a506c45db2">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CGC/CPF:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="2" y="2" width="37" height="11" uuid="3f2a10aa-7fe6-43ba-bb0d-42d807efb74a">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente.:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="41" y="13" width="144" height="11" uuid="688af490-e541-482f-b995-c3f0d9ff5fde">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CGC_CPF_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="24" width="37" height="11" uuid="5c36f971-54be-403a-a866-1c5843275f5b">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Telefone:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="39" y="2" width="146" height="11" uuid="165ee119-cc4f-43de-9c26-9f732e0a2e43">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_CLIENTE}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="39" y="24" width="146" height="11" uuid="e8477db2-1792-4ec4-a7b2-b1ed9598a4a9">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FONE_CLIENTE}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="35">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$P{IMPRIMIR_DADOS_CLIENTE}.equals("S")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="186" height="35" uuid="9249c408-6530-41e0-9ba3-299e725d6ce5"/>
				<box>
					<bottomPen lineWidth="0.45" lineStyle="Dashed"/>
				</box>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="39" y="12" width="79" height="11" uuid="4081ed17-3d5c-48e0-9f59-adf6cf6f3b65"/>
					<box topPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{BAIRRO_CLIENTE}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="39" y="0" width="146" height="11" uuid="5f4fa15f-6dba-4a23-9278-e09db34169cf"/>
					<box topPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENDERECO_CLIENTE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="133" y="12" width="52" height="11" uuid="9742accd-a55c-4fb5-bdc9-2a9f130fad3a">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CEP_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="114" y="12" width="20" height="11" uuid="804606be-c2c4-482b-ae15-6c37985da6e8">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="39" y="23" width="147" height="11" uuid="a9b4fe80-32e2-4058-a68d-eb3aa80d3392">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box topPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CIDADE_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="0" width="37" height="11" uuid="281709eb-def1-4cb4-8ca8-315d3c135d76">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Endereço:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" x="161" y="23" width="24" height="11" uuid="f9e7dfea-74aa-48ae-ab67-2f01ed72470e">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box topPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{UF_CLIENTE}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="48">
			<subreport>
				<reportElement x="0" y="1" width="186" height="47" uuid="eaf3b856-41f6-42d1-9aad-188a28cd30d9"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="RECADO_DESCRICAO">
					<subreportParameterExpression><![CDATA[$P{RECADO_DESCRICAO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ORDENAR_POR_CODITEM_OU_LOCACAO">
					<subreportParameterExpression><![CDATA[$P{ORDENAR_POR_CODITEM_OU_LOCACAO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="MOSTRAR_CODITEM_OU_PARTNUMBER">
					<subreportParameterExpression><![CDATA[$P{MOSTRAR_CODITEM_OU_PARTNUMBER}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REQUISICAO">
					<subreportParameterExpression><![CDATA[$P{REQUISICAO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="IMPRIMIR_DEFEITO">
					<subreportParameterExpression><![CDATA[$P{IMPRIMIR_DEFEITO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="IMPRIMIR_LOCACAO">
					<subreportParameterExpression><![CDATA[$P{IMPRIMIR_LOCACAO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "RequisicoesMiniSubItens.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
