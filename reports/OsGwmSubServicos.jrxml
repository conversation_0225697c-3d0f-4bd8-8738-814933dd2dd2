<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsGwmSubPecas" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="teste_freedom.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT OS_SERVICOS.NUMERO_OS, 
 OS_SERVICOS.COD_EMPRESA, 
 ITEM,
 (ROW_NUMBER() OVER(PARTITION BY OS_SERVICOS.ITEM ORDER BY OS_SERVICOS.COD_SERVICO ASC)) AS ITEM_ORDER,
 OS_SERVICOS.COD_SERVICO, 
 SERVICOS.DESCRICAO_SERVICO,
 OS_SERVICOS.TEMPO_PADRAO + NVL((SELECT SUM(OSA.TEMPO_ADICIONAL) FROM OS_SERVICOS_ADICIONAIS OSA
                                  WHERE OSA.COD_EMPRESA = OS_SERVICOS.COD_EMPRESA
                                    AND OSA.NUMERO_OS   = OS_SERVICOS.NUMERO_OS
                                    AND OSA.COD_SERVICO = OS_SERVICOS.COD_SERVICO), 0) AS TEMPO_PADRAO,
 OS_SERVICOS.PRECO_VENDA,
 NVL(OS_SERVICOS.TOTAL_LIQUIDO, OS_SERVICOS.PRECO_VENDA) AS TOTAL_LIQUIDO,
 SERVICOS.COD_SETOR,
 DESCRICAO_SETOR,
   NVL((SELECT ST.NOME 
    FROM OS_TEMPOS_EXECUTADOS OTE
    INNER JOIN SERVICOS_TECNICOS  ST 
      ON (ST.COD_TECNICO = OTE.COD_TECNICO
      AND ST.COD_EMPRESA = OTE.COD_EMPRESA) 
    WHERE OTE.COD_EMPRESA = OS_SERVICOS.COD_EMPRESA
      AND OTE.NUMERO_OS   = OS_SERVICOS.NUMERO_OS
      AND OTE.COD_SERVICO = OS_SERVICOS.COD_SERVICO
      AND ROWNUM <= 1),' ') 
       AS PRODUTIVO_PRISMA,
  NVL(OTE.TIPO_FABRICA,OT.TIPO_FABRICA) AS TIPO_FABRICA_EMPRESA,
  OTE.TIPO_FABRICA AS EMPRESA_TEM ,
  OT.TIPO_FABRICA AS FABRICA_TEM

FROM OS_SERVICOS, SERVICOS, SERVICOS_SETORES, OS, OS_TIPOS_EMPRESAS OTE, OS_TIPOS OT
WHERE OS_SERVICOS.COD_SERVICO = SERVICOS.COD_SERVICO
 AND SERVICOS.COD_SETOR = SERVICOS_SETORES.COD_SETOR
 AND OS_SERVICOS.COD_EMPRESA = $P{COD_EMPRESA}
 AND OS_SERVICOS.NUMERO_OS = $P{NUMERO_OS}
 AND OS.COD_EMPRESA = OS_SERVICOS.COD_EMPRESA
 AND OS.NUMERO_OS = OS_SERVICOS.NUMERO_OS
 AND OS.TIPO = OTE.TIPO
 AND OS.TIPO = OT.TIPO
 AND OS.COD_EMPRESA = OTE.COD_EMPRESA
 
ORDER BY OS_SERVICOS.ITEM, OS_SERVICOS.COD_SERVICO]]>
	</queryString>
	<field name="NUMERO_OS" class="java.math.BigDecimal"/>
	<field name="COD_EMPRESA" class="java.math.BigDecimal"/>
	<field name="ITEM" class="java.math.BigDecimal"/>
	<field name="ITEM_ORDER" class="java.math.BigDecimal"/>
	<field name="COD_SERVICO" class="java.lang.String"/>
	<field name="DESCRICAO_SERVICO" class="java.lang.String"/>
	<field name="TEMPO_PADRAO" class="java.math.BigDecimal"/>
	<field name="PRECO_VENDA" class="java.math.BigDecimal"/>
	<field name="TOTAL_LIQUIDO" class="java.math.BigDecimal"/>
	<field name="COD_SETOR" class="java.lang.String"/>
	<field name="DESCRICAO_SETOR" class="java.lang.String"/>
	<field name="PRODUTIVO_PRISMA" class="java.lang.String"/>
	<field name="TIPO_FABRICA_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESA_TEM" class="java.lang.String"/>
	<field name="FABRICA_TEM" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="20">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="20" uuid="7b1cf3c2-6d17-4ab8-9730-8c8ed4e1e4b1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="140" height="10" uuid="979e23fb-4d9e-4740-bc6b-b0c5ae34fe5b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Serviços]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="42" y="10" width="42" height="10" uuid="7d841074-4cfe-4157-a97e-34f7d7846d04">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo de OS]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="154" y="10" width="280" height="10" uuid="aafdef03-c09f-42df-8cf6-ff807bdb65f9">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Descrição do Serviço]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="10" width="42" height="10" uuid="bd6539f0-23aa-40a7-a195-75945cb10f30">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Item]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="84" y="10" width="70" height="10" uuid="3b14297a-548d-4a9f-8ac9-e69df102d35b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Código de Operação]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="475" y="10" width="80" height="10" uuid="a7f7f20d-81df-4c7e-97be-4957c696a39a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Valor Total]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="434" y="10" width="41" height="10" uuid="b540250e-c4b8-428e-a471-418f7c343d07">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="10" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement key="" isPrintRepeatedValues="false" x="0" y="0" width="555" height="10" uuid="209444c4-2584-47b0-9d73-b61e06668e60">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="42" height="10" uuid="1ee2c5b3-b344-44b3-acff-80f4b861d040">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}+ "." +$F{ITEM_ORDER}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="42" y="0" width="42" height="10" uuid="26d08a45-b633-4abb-835a-12ca9faf6b2d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TIPO_FABRICA_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement mode="Transparent" x="434" y="0" width="41" height="10" uuid="68be0752-985e-4fc9-bb5c-950ca32a5cd3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="154" y="0" width="280" height="10" uuid="a13926a1-4b52-4506-b291-87e13eac50b1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_SERVICO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#">
					<reportElement mode="Transparent" x="475" y="0" width="80" height="10" uuid="92f1fac7-1963-4a80-99f5-cfec035bae5e">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOTAL_LIQUIDO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="84" y="0" width="70" height="10" uuid="e0889b48-2e93-43a1-9db9-20af89352e5c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COD_SERVICO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="1">
			<line>
				<reportElement positionType="Float" x="0" y="0" width="555" height="1" uuid="d20f4a7e-6e7b-4c93-9633-6e5bbef8bdd0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
			</line>
		</band>
	</columnFooter>
</jasperReport>
