<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiSubReclamacao" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="teste_freedom.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT O.ITEM, O.DESCRICAO, 
    (SELECT GFC.DESCRICAO FROM GARANTIA_FORD_LAUDO GFC
     WHERE (GFC.COD_EMPRESA = O.COD_EMPRESA AND GFC.NUMERO_OS = O.NUMERO_OS AND GFC.NUMERO_RECLAMACAO = O.ITEM)
      ) CONSTATACAO,
      
    (SELECT GFD.DESCRICAO FROM GARANTIA_FORD_LAUDO GFD 
     WHERE (GFD.COD_EMPRESA = O.COD_EMPRESA AND GFD.NUMERO_OS = O.NUMERO_OS AND GFD.NUMERO_RECLAMACAO = O.ITEM) 
      ) DIAGNOSTICO  
  FROM OS_ORIGINAL O
          
  WHERE O.NUMERO_OS = $P{NUMERO_OS} AND O.COD_EMPRESA = $P{COD_EMPRESA}
  ORDER BY O.ITEM]]>
	</queryString>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="CONSTATACAO" class="java.lang.String"/>
	<field name="DIAGNOSTICO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="27">
			<staticText>
				<reportElement mode="Transparent" x="2" y="0" width="209" height="14" uuid="42d05d48-3d83-413c-86b3-053dbd24393a">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[DESCRIÇÃO DOS SERVIÇOS SOLICITADOS]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="14" width="555" height="13" uuid="93b81f4b-f31f-4fb3-a914-cb052f574f70"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="3" y="1" width="535" height="11" uuid="a957fd99-4782-4b50-b4bb-e51243871703">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Reclamações]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="13" uuid="73473e48-4f3e-4011-9b55-e5cec026d5db"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="21" y="1" width="528" height="11" uuid="bbd8e813-858c-4570-a689-3fdfe8760d22">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField pattern="#00.###;(#00.###-)">
					<reportElement x="3" y="1" width="17" height="11" uuid="e34e277f-e487-491a-8d9f-c1df46c6bd3b"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="60">
			<frame>
				<reportElement x="0" y="0" width="555" height="60" isRemoveLineWhenBlank="true" uuid="c38d2db4-0bf4-495b-8ed5-afe09932578a">
					<printWhenExpression><![CDATA[$F{CONSTATACAO} != null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement x="0" y="0" width="555" height="29" uuid="083bab1d-ee03-4eae-ab1f-8e38d9a3d4d2"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="2" y="3" width="44" height="13" uuid="d2088e9e-0b63-4fb5-926a-02ff1b704ff3"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Causa:]]></text>
					</staticText>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement mode="Transparent" x="48" y="3" width="501" height="24" uuid="3bc0b5cc-6e78-4f47-9147-4ec50e1c83dd"/>
						<textElement textAlignment="Justified">
							<font size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{CONSTATACAO}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="0" y="29" width="549" height="31" uuid="207b4669-dbca-418e-988c-4cfb7f82eaf9"/>
					<textField isStretchWithOverflow="true">
						<reportElement mode="Transparent" x="48" y="5" width="501" height="24" uuid="3f9b1e3f-**************-ef0c54e2eeb9">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Justified">
							<font size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{DIAGNOSTICO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="2" y="5" width="44" height="13" uuid="564d20c1-ce1a-44db-9bef-082081c0701a"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Correção:]]></text>
					</staticText>
				</frame>
			</frame>
		</band>
	</detail>
	<lastPageFooter>
		<band height="3">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</lastPageFooter>
</jasperReport>
