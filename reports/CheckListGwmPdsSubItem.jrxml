<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListGwmPdsSubItem" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="teste_freedom.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[115582.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="OBRIGATORIO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<parameter name="LISTA_GRUPOS" class="java.lang.String">
		<defaultValueExpression><![CDATA["30050"]]></defaultValueExpression>
	</parameter>
	<parameter name="ID_GRUPO" class="java.lang.Double">
		<defaultValueExpression><![CDATA[30050.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[with DADOS as (
select info.cod_empresa as COD_EMPRESA,
       info.cod_produto as COD_PRODUTO,
       info.cod_modelo as COD_MODELO,
       info.tipo_os as TIPO_OS,
       info.cod_segmento as COD_SEGMENTO
  from (select os.cod_empresa,dv.Cod_Produto, dv.cod_modelo, os.tipo as tipo_os, produtos.cod_segmento
          from os, os_dados_veiculos dv, produtos
         where os.numero_os = dv.numero_os
           and os.cod_empresa = dv.cod_empresa
           and os.cod_empresa = $P{COD_EMPRESA}
           and os.numero_os = $P{NUMERO_OS}
           and produtos.cod_produto = dv.cod_produto) info
),
TODOS_ITENS as ( 
SELECT A.ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */ 
       A.COD_ITEM,
       CASE WHEN SUBSTR(A.DESCRICAO, 2,5) LIKE '%-%' THEN TRIM(SUBSTR(A.DESCRICAO, 1, INSTR(A.DESCRICAO, '-') - 1)) ELSE '' END AS ACAO_ITEM,
       CASE WHEN SUBSTR(A.DESCRICAO, 2,5) LIKE '%-%' THEN TRIM(SUBSTR(A.DESCRICAO, INSTR(A.DESCRICAO, '-') + 1)) ELSE A.DESCRICAO END AS DESCRICAO_ITEM,
       NVL(A.observacao,' ') as OBSERVACAO_ITEM,
       A.RESPOSTA_EH_OBSERVACAO,
       D.DESCRICAO AS DESCRICAO_OPCAO,
       B.DESCRICAO DESCRICAO_GRUPO,
       B.ORDEM AS ORDEM_GRUPO ,
       NVL(C.OBSERVACAO,' ') AS OBSERVACAO,
       ROW_NUMBER() Over (order by A.ID_GRUPO, B.ORDEM, A.ORDEM) as LINHA_RELATORIO
FROM MOB_PERTENCE_ITEM A, MOB_PERTENCE_GRUPO B, MOB_OS_PERTENCE C, MOB_OPCAO D
WHERE A.ID_GRUPO = B.ID_GRUPO
 AND B.ATIVO = 'S'
 AND A.COD_ITEM = C.COD_ITEM (+)
 AND B.APLICACAO = 'R'
 AND C.COD_EMPRESA(+) = $P{COD_EMPRESA}
 AND C.NUMERO_OS(+) = $P{NUMERO_OS}
 AND C.ID_OPCAO = D.ID_OPCAO(+)
 AND A.ATIVO = 'S'
 AND B.ID_GRUPO in ($P!{LISTA_GRUPOS})
 )
select ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */ 
       COD_ITEM,
       ACAO_ITEM,
       CASE WHEN LENGTH(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM) > 300 then LPAD(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM,297) || '...' ELSE DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM END AS DESCRICAO_ITEM_ABREVIADA,
       --lower(CASE WHEN LENGTH(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM || ' ' || OBSERVACAO_ITEM|| ' ' || OBSERVACAO_ITEM) > 300 then LPAD(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM || ' ' || OBSERVACAO_ITEM|| ' ' || OBSERVACAO_ITEM ,297) || '...' ELSE DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM || ' ' || OBSERVACAO_ITEM|| ' ' || OBSERVACAO_ITEM  END) AS DESCRICAO_ITEM_ABREVIADA,
       DESCRICAO_ITEM,
       OBSERVACAO_ITEM,
       RESPOSTA_EH_OBSERVACAO,
       DESCRICAO_OPCAO,
       DESCRICAO_GRUPO,
       OBSERVACAO,
       ORDEM_GRUPO,
       LINHA_RELATORIO
FROM TODOS_ITENS
WHERE ID_GRUPO = $P{ID_GRUPO}
ORDER BY ID_GRUPO]]>
	</queryString>
	<field name="ID_GRUPO" class="java.math.BigDecimal"/>
	<field name="COD_ITEM" class="java.math.BigDecimal"/>
	<field name="ACAO_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM_ABREVIADA" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="OBSERVACAO_ITEM" class="java.lang.String"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="ORDEM_GRUPO" class="java.math.BigDecimal"/>
	<field name="LINHA_RELATORIO" class="java.math.BigDecimal"/>
	<variable name="returnValue" class="java.lang.Double">
		<variableExpression><![CDATA[$V{returnValue}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="14">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean(($F{DESCRICAO_ITEM_ABREVIADA}.length()) < 100)]]></printWhenExpression>
			<frame borderSplitType="DrawBorders">
				<reportElement x="0" y="0" width="555" height="14" isPrintWhenDetailOverflows="true" uuid="0703988c-4b5b-44b6-9da3-5329b13d2953">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="95" y="0" width="31" height="14" isPrintWhenDetailOverflows="true" uuid="6c2564fc-7f4a-47c6-9d82-b6cd1f5d7c04">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ACAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement key="" stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="14" isPrintWhenDetailOverflows="true" uuid="89784aff-f7ba-4acb-a8d1-b0ea1d13fed4">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA_RELATORIO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="530" y="0" width="25" height="14" isPrintWhenDetailOverflows="true" uuid="55c86164-8332-4873-84c3-93d898108fb1">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NG") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="505" y="0" width="25" height="14" isPrintWhenDetailOverflows="true" uuid="7abe8338-30fd-4f30-84ec-f9051edc08c3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="126" y="0" width="379" height="14" backcolor="#A3FFCE" uuid="370d2936-66dc-477b-95b4-73d938dd6346">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM_ABREVIADA}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="28">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{DESCRICAO_ITEM_ABREVIADA}.length() > 100 && $F{DESCRICAO_ITEM_ABREVIADA}.length() <= 200)]]></printWhenExpression>
			<frame borderSplitType="DrawBorders">
				<reportElement x="0" y="0" width="555" height="28" isPrintWhenDetailOverflows="true" uuid="2a81b7ca-9fd5-4d52-9db2-a1ba3feb9319">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="95" y="0" width="31" height="28" isPrintWhenDetailOverflows="true" uuid="7a12e8df-6419-48eb-8422-ce6ee6d2616b">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ACAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement key="" stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="28" isPrintWhenDetailOverflows="true" uuid="6ed7f37e-650a-4251-93ca-c9f13db6e73b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA_RELATORIO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="530" y="0" width="25" height="28" isPrintWhenDetailOverflows="true" uuid="5029f178-43b2-40be-9e19-1d53daebb1c0">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NG") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="505" y="0" width="25" height="28" isPrintWhenDetailOverflows="true" uuid="b7d0c64e-1b8f-4cd3-80fd-0a46815280a3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="126" y="0" width="379" height="28" backcolor="#FFCDCC" uuid="50bada55-fec2-460c-aa8d-51d8660a2fd0">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM_ABREVIADA}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="42">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{DESCRICAO_ITEM_ABREVIADA}.length() > 200)]]></printWhenExpression>
			<frame borderSplitType="DrawBorders">
				<reportElement x="0" y="0" width="555" height="42" isPrintWhenDetailOverflows="true" uuid="66b11279-d5c4-4d1a-a448-0f0a68adacec">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="95" y="0" width="31" height="42" isPrintWhenDetailOverflows="true" uuid="3d66a84a-2745-430e-a9a7-b91afd4b8c06">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ACAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement key="" stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="42" isPrintWhenDetailOverflows="true" uuid="ad1ddb37-94a5-4bf7-8f08-4bcc2067b021">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA_RELATORIO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="530" y="0" width="25" height="42" isPrintWhenDetailOverflows="true" uuid="fe98beee-59a6-4cff-8c5e-eb1a22a1ec35">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NG") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" x="505" y="0" width="25" height="42" isPrintWhenDetailOverflows="true" uuid="7886a8f6-3b11-4f77-a9a3-fcf456192859">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="126" y="0" width="379" height="42" backcolor="#A4AAFC" uuid="0dcbf5e9-7b46-403a-85d9-e149fcb8da5f">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="0" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM_ABREVIADA}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
