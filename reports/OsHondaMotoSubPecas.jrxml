<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHondaSubPecas" pageWidth="167" pageHeight="236" whenNoDataType="AllSectionsNoDetail" columnWidth="167" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="d760b610-66b4-42df-8551-447eb20ebfd0">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT OS.COD_EMPRESA,
  DECODE(OS.SEQUENCIA_DAV, 0, OS_REQUISICOES.COD_ITEM,
                 NVL(ITENS_CUSTOS.COD_FISCAL_ITEM, OS_REQUISICOES.COD_ITEM)) AS COD_ITEM,
 OS_REQUISICOES.REQUISICAO, OS_REQUISICOES.COD_FORNECEDOR,
 DECODE(FORNECEDOR_ESTOQUE.OFICIAL, 'N', '*' || ITENS.DESCRICAO, ITENS.DESCRICAO) AS DESCRICAO,
 OS_REQUISICOES.QUANTIDADE, OS_REQUISICOES.CAUSADORA,
 OS_REQUISICOES.ITEM, ITENS.COD_MAX_DESC,
 NVL(ESTOQUE.QTDE, 0) AS ESTOQUE_QTDE,
 NVL(ESTOQUE.RESERVADO, 0) AS RESERVADO,
 DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
                ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                                      DECODE(ITENS.COD_TRIBUTACAO, '1',
                                        DECODE(PARM_SYS.REGIME_ICMS, 'S',
                                          DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                                            DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                        0),
                                                 0),
                                               OS_TIPOS.AUMENTO_PRECO_PECA),
                                             0),
                                       OS_TIPOS.AUMENTO_PRECO_PECA)) *
                   DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA, 'G', OS_REQUISICOES.PRECO_GARANTIA,
                                                    'F', OS_REQUISICOES.CUSTO_FORNECEDOR, 
                                                         DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
                  ) / 100,
       DECODE(OS_TIPOS.GARANTIA, 'S', OS_REQUISICOES.PRECO_GARANTIA,
         DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
           DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
             ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*OS_REQUISICOES.PRECO_VENDA)/100)))))) AS PRECO_VENDA,
 OS_REQUISICOES.QUANTIDADE *
   DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
     DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
       DECODE(OS_TIPOS.INTERNO, 'S',
                   ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                                         DECODE(ITENS.COD_TRIBUTACAO, '1',
                                           DECODE(PARM_SYS.REGIME_ICMS, 'S',
                                             DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                                               DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                           0),
                                                    0),
                                                  OS_TIPOS.AUMENTO_PRECO_PECA),
                                                0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                     DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA, 'G', OS_REQUISICOES.PRECO_GARANTIA,
                                                      'F', OS_REQUISICOES.CUSTO_FORNECEDOR, 
                                                           DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
                    ) / 100,
         DECODE(OS_TIPOS.GARANTIA, 'S', OS_REQUISICOES.PRECO_GARANTIA,
           DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
             DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
               ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*OS_REQUISICOES.PRECO_VENDA)/100)))))) AS PRECO_TOTAL
FROM OS_REQUISICOES, ESTOQUE, ITENS, OS, VW_OS_TIPOS OS_TIPOS, FORNECEDOR_ESTOQUE , SEGURADORA,
   ITENS_FORNECEDOR, ITENS_CLASSE_CONTABIL ICC, PARM_SYS, PARM_SYS2, ITENS_CUSTOS, OS_TIPOS_EMPRESAS OTE
WHERE (OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM)
  AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR
  AND (OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM (+))
  AND (OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR (+))
  AND (OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA (+))
  AND (OS_REQUISICOES.NUMERO_OS = OS.NUMERO_OS)
  AND (OS_REQUISICOES.COD_EMPRESA = OS.COD_EMPRESA)
  AND (OS.TIPO = OS_TIPOS.TIPO)
  AND (OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA)
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND OS_REQUISICOES.COD_ITEM = ITENS_CUSTOS.COD_ITEM (+)
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_CUSTOS.COD_FORNECEDOR (+)
  AND OS_REQUISICOES.COD_EMPRESA = ITENS_CUSTOS.COD_EMPRESA (+)
  AND OS_REQUISICOES.COD_EMPRESA = $P{COD_EMPRESA}
  AND OS_REQUISICOES.NUMERO_OS = $P{NUMERO_OS}
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
ORDER BY OS_REQUISICOES.ITEM, OS_REQUISICOES.COD_ITEM]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="COD_EMPRESA"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_EMPRESA"/>
	</field>
	<field name="COD_ITEM" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_ITEM"/>
	</field>
	<field name="REQUISICAO" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="REQUISICAO"/>
		<property name="com.jaspersoft.studio.field.label" value="REQUISICAO"/>
	</field>
	<field name="COD_FORNECEDOR" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="COD_FORNECEDOR"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_FORNECEDOR"/>
	</field>
	<field name="DESCRICAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="DESCRICAO"/>
		<property name="com.jaspersoft.studio.field.label" value="DESCRICAO"/>
	</field>
	<field name="QUANTIDADE" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="QUANTIDADE"/>
		<property name="com.jaspersoft.studio.field.label" value="QUANTIDADE"/>
	</field>
	<field name="CAUSADORA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="CAUSADORA"/>
		<property name="com.jaspersoft.studio.field.label" value="CAUSADORA"/>
	</field>
	<field name="ITEM" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="ITEM"/>
	</field>
	<field name="COD_MAX_DESC" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_MAX_DESC"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_MAX_DESC"/>
	</field>
	<field name="ESTOQUE_QTDE" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="ESTOQUE_QTDE"/>
		<property name="com.jaspersoft.studio.field.label" value="ESTOQUE_QTDE"/>
	</field>
	<field name="RESERVADO" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="RESERVADO"/>
		<property name="com.jaspersoft.studio.field.label" value="RESERVADO"/>
	</field>
	<field name="PRECO_VENDA" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="PRECO_VENDA"/>
		<property name="com.jaspersoft.studio.field.label" value="PRECO_VENDA"/>
	</field>
	<field name="PRECO_TOTAL" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="PRECO_TOTAL"/>
		<property name="com.jaspersoft.studio.field.label" value="PRECO_TOTAL"/>
	</field>
	<variable name="somaQuantidade" class="java.lang.Double" resetType="None" calculation="Sum">
		<variableExpression><![CDATA[$F{PRECO_TOTAL}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="23" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="0" y="1" width="110" height="11" uuid="1c214fb4-0d85-43f6-bb61-00a78ab1e82d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="110" y="1" width="57" height="11" uuid="c69d0531-fd9e-4767-89e5-1735da0ba1e7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="ContainerBottom" mode="Opaque" x="0" y="12" width="110" height="11" uuid="f4a19a05-86e6-44a8-9780-bc5da0870ab7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Opaque" x="110" y="12" width="57" height="11" uuid="*************-4000-bc8e-29735323b67e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
