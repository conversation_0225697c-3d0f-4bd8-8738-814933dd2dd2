<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsGwmAcompanhamento" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\35085\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[10805.0]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\temp\\image_zk\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH Q_OS AS
 (SELECT OS.COD_EMPRESA,
         OS_AGENDA.DATA_AGENDADA,
         TO_CHAR(OS_AGENDA.DATA_AGENDADA, 'DD/MM/YYYY HH24:MI') AS D_AG_STG,
         OS.STATUS_OS,
         OS.NUMERO_OS,
         OS.COD_OS_AGENDA,
         ABS(OS.NUMERO_OS) AS ABS_OSNUM,
         OS.COD_CLIENTE,
         OS.CLIENTE_RAPIDO,
         OS.TIPO_ENDERECO,
         OS.OBSERVACAO,
         OS.EXTENDIDA,
         OS.SEGURADORA,
         OS.DATA_EMISSAO,
         (TO_DATE(OS.DATA_EMISSAO, 'DD/MM/YYYY') || ' AS ' ||
         OS.HORA_EMISSAO) AS DH_EMISSAO,
         DECODE(OS.DATA_LIBERADO,
                NULL,
                '',
                (TO_DATE(OS.DATA_LIBERADO, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_LIBERADO)) AS DH_LIBERADO,
         DECODE(OS.DATA_ENCERRADA,
                NULL,
                '',
                (TO_DATE(OS.DATA_ENCERRADA, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_ENCERRADA)) AS DH_ENCERRADO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2) AS DIA_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2) AS MES_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4) AS ANO_EMISSAO,
         OS.HORA_EMISSAO,
         OS.HORA_ENCERRADA,
         OS.DATA_ENCERRADA,
         OS.HORA_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 1, 2) AS HORA24_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 4, 2) AS MINUTO_PROMETIDA,
         
         OS.DATA_PROMETIDA,
         OS.DATA_PROMETIDA_REVISADA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2) AS DIA_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2) AS MES_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4) AS ANO_PROMETIDA,
         
         OS.VALOR_SERVICOS_BRUTO,
         OS.VALOR_ITENS_BRUTO,

         (case when nvl(VALOR_SERVICOS_BRUTO,0) = 0 then 0 else OS.DESCONTOS_SERVICOS / OS.VALOR_SERVICOS_BRUTO END) AS DESCONTO_SERVICO_PORCENT,
         (case when nvl(OS.VALOR_ITENS_BRUTO,0) = 0 then 0 else OS.DESCONTOS_ITENS / OS.VALOR_ITENS_BRUTO END) AS DESCONTO_ITENS_PORCENT,
              
         (OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS TOTAL_SERVICOS_LIQUIDO,
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS) AS TOTAL_ITENS_LIQUIDO,
     
         ((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) +
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS)) AS TOTAL_OS,
         
     OS.COD_SEGURADORA,
         OS_DADOS_VEICULOS.ANO,
         OS_DADOS_VEICULOS.HORIMETRO,
         OS_DADOS_VEICULOS.PRISMA,
         OS_DADOS_VEICULOS.DATA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2) AS DIA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2) AS MES_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4) AS ANO_VENDA,
         OS_DADOS_VEICULOS.COMBUSTIVEL,
         OS_DADOS_VEICULOS.COR_EXTERNA,
         OS_DADOS_VEICULOS.PLACA,
         OS_DADOS_VEICULOS.KM,
         OS_DADOS_VEICULOS.CHASSI,
         OS_DADOS_VEICULOS.NUMERO_MOTOR,
         OS_DADOS_VEICULOS.NUMERO_RENAVAM,
         OS_DADOS_VEICULOS.SERIE,
         OS_DADOS_VEICULOS.COD_CONCESSIONARIA,
         OS_DADOS_VEICULOS.ESTADO_PINTURA,
         OS_DADOS_VEICULOS.JOGO_FERRAMENTAS,
         OS_DADOS_VEICULOS.ELASTICOS,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_D,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_E,
         OS_DADOS_VEICULOS.FLANELA,
         OS.TIPO,
         OS_TIPOS.DESCRICAO AS TIPO_DESCRICAO,
         OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO AS TIPO_COM_DESCRICAO,
         OS_TIPOS.GARANTIA,
         OS_TIPOS.REVISAO_GRATUITA,
         OS_TIPOS.INTERNO,
         OS_TIPOS.COD_CLIENTE AS CLIENTE_DO_TIPO,
         OS_TIPOS.OUTRO_CONCESSIONARIA,
         OS.NOME AS CONSULTOR,
         EMPRESAS_USUARIOS.NOME_COMPLETO AS CONSULTOR_COMPLETO,
         PRODUTOS.DESCRICAO_PRODUTO,
         PRODUTOS_MODELOS.DESCRICAO_MODELO,
         (PRODUTOS.DESCRICAO_PRODUTO || ' - ' ||
         PRODUTOS_MODELOS.DESCRICAO_MODELO) DESC_PROD_MOD,
         PRODUTOS_MODELOS.LINHA,
         MARCAS.DESCRICAO_MARCA,
         CONCESSIONARIAS.NOME CONCESSIONARIA_NOME,
         CONCESSIONARIAS.UF CONCESSIONARIA_UF,
         CONCESSIONARIAS.CIDADE CONCESSIONARIA_CIDADE,
         CONCESSIONARIAS.BAIRRO CONCESSIONARIA_BAIRRO,
         CONCESSIONARIAS.ENDERECO CONCESSIONARIA_RUA,
         CONCESSIONARIAS.CEP CONCESSIONARIA_CEP,
         CONCESSIONARIAS.CODIGO_PADRAO CONCESSIONARIA_CODIGO,
         UF_CONCESSIONARIA.DESCRICAO CONCESSIONARIA_ESTADO,
         
         (CASE
           WHEN NVL(OS.OS_ORIGEM_RETORNO, 0) > 0 THEN
            'S'
           ELSE
            'N'
         END) AS AG_RETORNO,
         NVL(OS_AGENDA.CLIENTE_AGUARDA, 'N') AS AG_CLIENTE_AGUARDA,
         NVL(OS_AGENDA.VEICULO_PLATAFORMA, 'N') AS AG_VEICULO_PLATAFORMA,
         NVL(OS_AGENDA.TAXI, 'N') AS AG_TAXI,
         NVL(OS_AGENDA.BLINDADO, 'N') AS AG_BLINDADO,
         NVL(OS_AGENDA.TESTE_RODAGEM, 'N') AS AG_TESTE_RODAGEM,
         NVL(OS_AGENDA.LEVAR_PECAS_SUBSTITUIDAS, 'N') AS AG_LEVAR_PECAS_SUBSTITUIDAS,
         NVL(OS_AGENDA.LAVAR_VEICULO, 'N') AS AG_LAVAR_VEICULO,
         NVL(OS_AGENDA.VEICULO_MODIFICADO, 'N') AS VEICULO_MODIFICADO,
         NVL(OS_AGENDA.DDW_GARANTIA, 'NÃO EXISTENTE') AS DDW_GARANTIA,
         OS.HORA_PROMETIDA_REVISADA,
         OS_TIPOS.TIPO_FABRICA,
         DECODE(OS_AGENDA.REC_INTERATIVA,
                'S',
                'VEÍCULO COM RECEPÇÃO INTERATIVA',
                'N',
                '') REC_INTERATIVA,
         OS_AGENDA.COD_TIPO_IMOBILIZADO,
         DECODE(OS_AGENDA.COD_TIPO_IMOBILIZADO,
                NULL,
                '',
                'VEICULO IMOBILIZADO: ' || TIPO_IMOBILIZADO.DESCRICAO) AS IMOBILIZADO,
         OS_AGENDA.REC_INTERATIVA AS RECEPCAO_INTERATIVA,
         DECODE(OS_AGENDA.COD_TIPO_MOBILIDADE,
                NULL,
                'SEM MOBILIDADE',
                TIPO_MOBILIDADE.DESCRICAO) MOBILIDADE_DESCRICAO,
         NVL((SELECT 'S'
               FROM JLR_CHASSI_BLINDADO J
              WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
                AND ROWNUM < 2),
             'N') AS BLINDADO,
         (SELECT J.NOME_BLINDADORA
            FROM JLR_CHASSI_BLINDADO J
           WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
             AND ROWNUM < 2) AS BLINDADORA,
             
       FORMA_PGTO.DESCRICAO AS FORMA_PAGAMENTO,
       NVL((SELECT 'SIM' FROM ALUGUEL_VEICULOS WHERE ROWNUM = 1 AND ALUGUEL_VEICULOS.CHASSI = OS_DADOS_VEICULOS.CHASSI),'NÃO') AS VEICULO_LOCACAO,
  	   NVL((SELECT 'SIM'
				FROM PACOTE_REV_GRATIS PRG
				WHERE PRG.CHASSI = OS_DADOS_VEICULOS.CHASSI
				       AND ((OS_DADOS_VEICULOS.KM BETWEEN PRG.KM_MIN AND PRG.KM_MAX) OR 
                                   (TRUNC(OS.DATA_EMISSAO) BETWEEN TRUNC(PRG.DTMIN) AND TRUNC(PRG.DTMAX)))
				      AND ROWNUM = 1), 'NÃO') AS PACOTE_TRANQUILIDADE
    FROM OS,
         OS_DADOS_VEICULOS,
         OS_AGENDA,
         EMPRESAS_USUARIOS,
         VW_OS_TIPOS       OS_TIPOS,
         CONCESSIONARIAS,
         PRODUTOS,
         PRODUTOS_MODELOS,
         MARCAS,
         UF                UF_CONCESSIONARIA,
         TIPO_IMOBILIZADO,
         TIPO_MOBILIDADE,
         OS_PAGAMENTO,
         FORMA_PGTO
   WHERE OS.TIPO = OS_TIPOS.TIPO
     AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA(+)
     AND OS.NOME = EMPRESAS_USUARIOS.NOME
     AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS(+)
     AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA(+)
     AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+)
     AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO
        
     AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA(+)
     AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS(+)
        
     AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
     AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
     AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
     AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF(+)
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_TIPO_IMOBILIZADO = TIPO_IMOBILIZADO.COD_TIPO_IMOBILIZADO(+)
     AND OS_AGENDA.COD_TIPO_MOBILIDADE = TIPO_MOBILIDADE.COD_TIPO_MOBIL(+)
     AND OS.NUMERO_OS = OS_PAGAMENTO.NUMERO_OS(+)
     AND OS.COD_EMPRESA = OS_PAGAMENTO.COD_EMPRESA(+)
     AND OS_PAGAMENTO.COD_FORMA_PGTO = FORMA_PGTO.COD_FORMA_PGTO(+)
     AND OS_PAGAMENTO.COD_EMPRESA  = FORMA_PGTO.COD_EMPRESA(+)
     ),
Q_EMPRESA AS
 (SELECT EMPRESAS.COD_EMPRESA,
         EMPRESAS.NOME NOME_EMPRESA,
         EMPRESAS.CGC,
         EMPRESAS.FACHADA,
         EMPRESAS.ESTADO AS UF,
         (TRIM(EMPRESAS.CIDADE) || ' - ' || TRIM(EMPRESAS.ESTADO)) AS CIDADE,
         EMPRESAS.BAIRRO,
         EMPRESAS.COMPLEMENTO,
         (EMPRESAS.RUA || ', ' || EMPRESAS.FACHADA) AS RUA,
         EMPRESAS.FONE,
         EMPRESAS.FAX,
         EMPRESAS.CEP,
         EMPRESAS.INSCRICAO_MUNICIPAL,
         EMPRESAS.INSCRICAO_SUBSTITUICAO,
         UF.DESCRICAO ESTADO,
         EMPRESAS.INSCRICAO_ESTADUAL,
         TRUNC(SYSDATE) AS DATA_ATUAL,
         SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'), 1, 5) AS HORA_ATUAL_STR,
         CLIENTES.ENDERECO_ELETRONICO AS EMAIL,
         EMPRESA_LOGO.LOGO
    FROM EMPRESAS, EMPRESA_LOGO, UF, CLIENTES
   WHERE EMPRESAS.COD_EMPRESA = $P{COD_EMPRESA}
     AND EMPRESAS.COD_EMPRESA = EMPRESA_LOGO.COD_EMPRESA(+)
     AND EMPRESAS.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND UF.UF = EMPRESAS.ESTADO),
Q_CLIENTE AS
 (SELECT CLIENTE_DIVERSO.COD_CLIENTE,
         
         CLIENTE_DIVERSO.NOME AS NOME,
         CLIENTE_DIVERSO.RG   AS RG,
         
         ('(' || CLIENTES.PREFIXO_RES || ') ' || CLIENTES.TELEFONE_RES) AS TELEFONE_RES,
         CLIENTES.PREFIXO_RES,
         ('(' || CLIENTES.PREFIXO_COM || ') ' || CLIENTES.TELEFONE_COM) AS TELEFONE_COM,
         CLIENTES.PREFIXO_COM,
         ('(' || CLIENTES.PREFIXO_FAX || ') ' || CLIENTES.TELEFONE_FAX) AS TELEFONE_FAX,
         CLIENTES.PREFIXO_FAX,
         ('(' || CLIENTES.PREFIXO_CEL || ') ' || CLIENTES.TELEFONE_CEL) AS TELEFONE_CEL,
         CLIENTES.PREFIXO_CEL,
         
         NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL) AS INSC_ESTAD,
         
         CLIENTE_DIVERSO.CGC,
         CLIENTE_DIVERSO.CPF,
         CLIENTES.COD_CLASSE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.UF,
                2,
                CLIENTES.UF_RES,
                3,
                CLIENTES.UF_COM,
                4,
                CLIENTES.UF_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.UF,
                NULL) UF,
         DECODE(OS.TIPO_ENDERECO,
                1,
                UF_DIVERSO.DESCRICAO,
                2,
                UF_RES.DESCRICAO,
                3,
                UF_COM.DESCRICAO,
                4,
                UF_COBRANCA.DESCRICAO,
                5,
                UF_INSCRICAO.DESCRICAO,
                NULL) ESTADO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CIDADES_DIV.DESCRICAO,
                2,
                CIDADES_RES.DESCRICAO,
                3,
                CIDADES_COM.DESCRICAO,
                4,
                CIDADES_COBRANCA.DESCRICAO,
                5,
                ENDERECO_POR_INSCRICAO.CIDADE,
                NULL) CIDADE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.BAIRRO,
                2,
                CLIENTES.BAIRRO_RES,
                3,
                CLIENTES.BAIRRO_COM,
                4,
                CLIENTES.BAIRRO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.BAIRRO,
                NULL) BAIRRO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                TRANSLATE(TO_CHAR(CLIENTE_DIVERSO.CEP / 1000, '00000.000'),
                          ',.',
                          '.-'),
                2,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_RES / 1000, '00000.000'),
                          ',.',
                          '.-'),
                3,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_COM / 1000, '00000.000'),
                          ',.',
                          '.-'),
                4,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_COBRANCA / 1000, '00000.000'),
                          ',.',
                          '.-'),
                5,
                TRANSLATE(TO_CHAR(ENDERECO_POR_INSCRICAO.CEP / 1000,
                                  '00000.000'),
                          ',.',
                          '.-'),
                NULL) CEP,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.ENDERECO,
                2,
                CLIENTES.RUA_RES,
                3,
                CLIENTES.RUA_COM,
                4,
                CLIENTES.RUA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.RUA,
                NULL) RUA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.COMPLEMENTO,
                2,
                CLIENTES.COMPLEMENTO_RES,
                3,
                CLIENTES.COMPLEMENTO_COM,
                4,
                CLIENTES.COMPLEMENTO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.COMPLEMENTO,
                NULL) COMPLEMENTO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                NULL,
                2,
                CLIENTES.FACHADA_RES,
                3,
                CLIENTES.FACHADA_COM,
                4,
                CLIENTES.FACHADA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.FACHADA,
                NULL) FACHADA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.FONE_CONTATO,
                2,
                CLIENTES.TELEFONE_RES,
                3,
                CLIENTES.TELEFONE_COM,
                4,
                CLIENTES.TELEFONE_CEL,
                5,
                ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
                NULL) FONE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
                2,
                CLIENTES.PREFIXO_RES,
                3,
                CLIENTES.PREFIXO_COM,
                4,
                CLIENTES.PREFIXO_CEL,
                5,
                ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
                NULL) PREFIXO,
         
         CLIENTES.ENDERECO_ELETRONICO,
         CLIENTES.EMAIL_NFE,
         NVL(CLIENTES.EMAIL_TRABALHO, CLIENTES.EMAIL2) AS EMAIL2
  
    FROM OS,
         CLIENTE_DIVERSO,
         CLIENTES,
         ENDERECO_POR_INSCRICAO,
         CIDADES                CIDADES_RES,
         CIDADES                CIDADES_COM,
         CIDADES                CIDADES_COBRANCA,
         CIDADES                CIDADES_DIV,
         UF                     UF_DIVERSO,
         UF                     UF_RES,
         UF                     UF_COM,
         UF                     UF_COBRANCA,
         UF                     UF_INSCRICAO
   WHERE OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE
     AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES(+)
     AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES(+)
     AND OS.INSCRICAO_ESTADUAL =
         ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL(+)
     AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF(+)
     AND CLIENTES.UF_RES = UF_RES.UF(+)
     AND CLIENTES.UF_COM = UF_COM.UF(+)
     AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF(+)
     AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF(+)
        
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}),
Q_HIST_ORC AS
 (SELECT ROW_NUMBER() OVER(ORDER BY ORC.NUMERO_OS) AS NUMERO_LINHA,
         ORC.NUMERO_OS,
         ORC.COD_EMPRESA,
         COUNT(ORC.NUMERO_OS) NUMERO_ORCAMENTOS,
         LISTAGG(ORC.NUMERO_ORCAMENTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS ORCAMENTOS,
         LISTAGG(ORCF.VALOR_BRUTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS VALOR_ORCAMENTO_BRUTO,
         SUM(ORCF.VALOR_BRUTO) TOTAL
    FROM OS_ORCAMENTOS ORC
    LEFT JOIN OS_ORC_FECHAMENTO ORCF
      ON ORCF.NUMERO_OS = ORC.NUMERO_ORCAMENTO
     AND ORCF.COD_EMPRESA = ORC.COD_EMPRESA
   WHERE ORC.NUMERO_OS = $P{NUMERO_OS}
     AND ORC.COD_EMPRESA = $P{COD_EMPRESA}
   GROUP BY ORC.NUMERO_OS, ORC.COD_EMPRESA),

Q_HISTORICO AS
 (SELECT LISTAGG(HISTORICO_OS.N_OS, ', ') WITHIN GROUP(ORDER BY HISTORICO_OS.N_OS) AS HISTORICO_OS,
         HISTORICO_OS.CHASSI,
         HISTORICO_OS.COD_EMPRESA
    FROM (SELECT DISTINCT TO_CHAR(OSD.NUMERO_OS) AS N_OS,
                          OSD.CHASSI,
                          OSD.COD_EMPRESA
            FROM OS_DADOS_VEICULOS OSD, OS, Q_OS
           WHERE OSD.COD_EMPRESA = Q_OS.COD_EMPRESA
             AND OS.NUMERO_OS = OSD.NUMERO_OS
             AND OS.STATUS_OS = 0
             AND OS.NUMERO_OS > 0
             AND OSD.CHASSI = Q_OS.CHASSI
           ORDER BY OSD.NUMERO_OS DESC) HISTORICO_OS
           WHERE ROWNUM <= 3
   GROUP BY HISTORICO_OS.CHASSI, HISTORICO_OS.COD_EMPRESA),

QRYDADOSVEICULOS AS
 (SELECT CF.ANO, CF.DATA_COMPRA, CF.CHASSI, CF.PLACA
    FROM CLIENTES_FROTA CF, Q_OS
   WHERE CF.VENDIDO = 'N'
     AND ROWNUM <= 1
     AND ((Q_OS.CHASSI IS NOT NULL AND CF.CHASSI = Q_OS.CHASSI) OR
         (Q_OS.PLACA IS NOT NULL AND CF.PLACA = Q_OS.PLACA))
   ORDER BY CF.DATA_COMPRA DESC),

Q_PARM_SYS as
 (SELECT PARM_SYS.COD_EMPRESA,
         TIPO_TEMPO,
         TIPO_VALOR_OS,
         cod_cliente_balcao,
         tipo_concessionaria,
         CONCESSIONARIAS.codigo_padrao,
         versao2,
         cod_cliente_fabrica_garantia,
         PARM_SYS2.HONDA_IMP_NUMERO_REQUISICAO,
         PARM_SYS3.TERMO_OS_JLR
    FROM PARM_SYS, PARM_SYS2, PARM_SYS3, CONCESSIONARIAS
   WHERE (PARM_SYS.CONCESSIONARIA_NUMERO =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+))
     AND (PARM_SYS2.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS3.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS.COD_EMPRESA = $P{COD_EMPRESA})),

Q_ASSINATURA as
 (select OS_AGENDA.NUMERO_OS,
         OS_AGENDA.COD_EMPRESA,
         OS_AGENDA.SIGNATURE As CLIENTE_RECEPCAO,
         MOB_OS_ASSINATURA_ENTREGA.ASSINATURA_CLIENTE AS CLIENTE_ENTREGA,
         MOB_OS_ASSINATURA_ENTREGA.DATA_ASSINATURA_CLIENTE AS CLIENTE_ENTREGA_DT,
         MOB_OS_ASSINATURA_RECEPCAO.ASSINATURA AS CONSULTOR_RECEPCAO,
         MOB_OS_ASSINATURA_RECEPCAO.DATA_ASSINATURA AS CONSULTOR_RECEPCAO_DT,
         MOB_OS_ASSINATURA_ENTREGA.ASSINATURA AS CONSULTOR_ENTREGA,
         MOB_OS_ASSINATURA_ENTREGA.DATA_ASSINATURA AS CONSULTOR_ENTREGA_DT
    from OS_AGENDA, MOB_OS_ASSINATURA MOB_OS_ASSINATURA_ENTREGA, MOB_OS_ASSINATURA MOB_OS_ASSINATURA_RECEPCAO
   where OS_AGENDA.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_EMPRESA = $P{COD_EMPRESA}
   AND OS_AGENDA.NUMERO_OS = MOB_OS_ASSINATURA_ENTREGA.numero_os (+)
   AND OS_AGENDA.COD_EMPRESA = MOB_OS_ASSINATURA_ENTREGA.cod_empresa (+)
   AND MOB_OS_ASSINATURA_ENTREGA.APLICACAO(+) = 'E' 
   AND OS_AGENDA.NUMERO_OS = MOB_OS_ASSINATURA_RECEPCAO.numero_os (+)
   AND OS_AGENDA.COD_EMPRESA = MOB_OS_ASSINATURA_RECEPCAO.cod_empresa (+)
   AND MOB_OS_ASSINATURA_RECEPCAO.APLICACAO(+) = 'R'),
   
Q_CAMPANHA AS (
SELECT LISTAGG(initcap(OS_CAMPANHA.ASSUNTO), ', ') WITHIN GROUP(ORDER BY OS_CAMPANHA.COD_CAMPANHA) AS LISTA_CAMPANHAS,
       ODV.NUMERO_OS,
       ODV.COD_EMPRESA
      FROM OS_CAMPANHA, OS_DADOS_VEICULOS ODV
      WHERE ODV.NUMERO_OS = $P{NUMERO_OS}
            AND ODV.COD_EMPRESA = $P{COD_EMPRESA}
            AND (EXISTS (SELECT 'X' FROM CAMPANHA_VEICULOS      	
             WHERE CAMPANHA_VEICULOS.COD_CAMPANHA = OS_CAMPANHA.COD_CAMPANHA
              AND (COD_PRODUTO IS NULL OR COD_PRODUTO = ODV.COD_PRODUTO)
              AND (COD_MODELO IS NULL OR COD_MODELO = ODV.COD_MODELO)
              AND (CHASSI_INICIAL IS NULL OR CHASSI_INICIAL <= SUBSTR(ODV.CHASSI, -LENGTH(CHASSI_INICIAL)))
              AND (CHASSI_FINAL IS NULL OR CHASSI_FINAL >= SUBSTR(ODV.CHASSI, -LENGTH(CHASSI_FINAL)))
              AND (ANO_INICIAL IS NULL OR ANO_INICIAL <= (CASE 
                                                            WHEN TO_NUMBER(SUBSTR(ODV.ANO, 4, 2)) < 50
                                                            THEN 2000 + TO_NUMBER(SUBSTR(ODV.ANO, 4, 2))
                                                            WHEN TO_NUMBER(SUBSTR(ODV.ANO, 4, 2)) > 50
                                                            THEN 1900 + TO_NUMBER(SUBSTR(ODV.ANO, 4, 2))
                                                            ELSE 0
                                                            END))
              AND (ANO_FINAL IS NULL OR ANO_FINAL >= (CASE 
                                                      WHEN TO_NUMBER(SUBSTR(ODV.ANO, 4, 2)) < 50
                                                      THEN 2000 + TO_NUMBER(SUBSTR(ODV.ANO, 4, 2))
                                                      WHEN TO_NUMBER(SUBSTR(ODV.ANO, 4, 2)) > 50
                                                      THEN 1900 + TO_NUMBER(SUBSTR(ODV.ANO, 4, 2))
                                                      ELSE 0
                                                      END)))
             OR
             EXISTS (SELECT 'X' FROM OS_CAMPANHA_CHASSIS
                     WHERE OS_CAMPANHA_CHASSIS.COD_CAMPANHA = OS_CAMPANHA.COD_CAMPANHA
                      AND CHASSI = ODV.CHASSI)
            )
            AND ((DATA_INICIAL IS NULL) OR (TRUNC(SYSDATE) >= TRUNC(DATA_INICIAL)))
            AND ((DATA_FINAL IS NULL) OR (TRUNC(SYSDATE) <= TRUNC(DATA_FINAL)))
            AND NVL(INTERFACE_ATIVO, 'S') <> 'N'
            AND NOT EXISTS(SELECT COD_CAMPANHA
                            FROM OS_CAMPANHA_FEITA
                            WHERE COD_PRODUTO = ODV.COD_PRODUTO
                             AND COD_MODELO = ODV.COD_MODELO
                             AND CHASSI = ODV.CHASSI)
      GROUP BY ODV.NUMERO_OS, ODV.COD_EMPRESA, OS_CAMPANHA.COD_CAMPANHA
      ORDER BY COD_CAMPANHA
)
   



SELECT Q_EMPRESA.NOME_EMPRESA           AS Q_EMPRESA_NOME_EMPRESA,
       Q_EMPRESA.RUA                    AS Q_EMPRESA_RUA,
       Q_EMPRESA.CIDADE                 AS Q_EMPRESA_CIDADE,
       Q_EMPRESA.CEP                    AS Q_EMPRESA_CEP,
       Q_EMPRESA.FONE                   AS Q_EMPRESA_FONE,
       Q_EMPRESA.INSCRICAO_ESTADUAL     AS Q_EMPRESA_INSCRICAO_ESTADUAL,
     Q_EMPRESA.LOGO          AS Q_EMPRESA_LOGO,
       Q_EMPRESA.CGC                    AS Q_EMPRESA_CGC,
       Q_OS.TIPO                        AS Q_OS_TIPO,
       Q_OS.D_AG_STG                    AS Q_OS_D_AG_STG,
     Q_OS.DATA_AGENDADA        AS Q_OS_DATA_AGENDADA,
       Q_OS.CONSULTOR                   AS Q_OS_CONSULTOR,
     Q_OS.CONSULTOR_COMPLETO      AS Q_OS_CONSULTOR_COMPLETO,
       Q_OS.PRISMA                      AS Q_OS_PRISMA,
       Q_OS.DATA_PROMETIDA              AS Q_OS_DATA_PROMETIDA,
       Q_OS.DATA_PROMETIDA_REVISADA     AS Q_OS_DATA_PROMETIDA_REVISADA,
       Q_OS.AG_RETORNO                  AS Q_OS_AG_RETORNO,
       Q_OS.AG_CLIENTE_AGUARDA          AS Q_OS_AG_CLIENTE_AGUARDA,
       Q_OS.RECEPCAO_INTERATIVA         AS Q_OS_RECEPCAO_INTERATIVA,
       Q_OS.AG_LAVAR_VEICULO            AS Q_OS_AG_LAVAR_VEICULO,
       Q_OS.AG_BLINDADO                 AS Q_OS_AG_BLINDADO,
       Q_OS.AG_TESTE_RODAGEM            AS Q_OS_AG_TESTE_RODAGEM,
       Q_OS.AG_LEVAR_PECAS_SUBSTITUIDAS AS Q_OS_AG_LEVAR_PECAS_SUBSTITUID,
       Q_OS.VEICULO_MODIFICADO          AS Q_OS_VEICULO_MODIFICADO,
       Q_OS.NUMERO_OS                   AS Q_OS_NUMERO_OS,
       Q_OS.HORA_PROMETIDA              AS Q_OS_HORA_PROMETIDA,
       Q_OS.PLACA                       AS Q_OS_PLACA,
       Q_OS.COMBUSTIVEL          AS Q_OS_COMBUSTIVEL,
       Q_OS.HORA_PROMETIDA_REVISADA     AS Q_OS_HORA_PROMETIDA_REVISADA,
       Q_OS.MOBILIDADE_DESCRICAO        AS Q_OS_MOBILIDADE_DESCRICAO,
       Q_CLIENTE.TELEFONE_CEL           AS Q_CLIENTE_TELEFONE_CEL,
       Q_CLIENTE.TELEFONE_COM           AS Q_CLIENTE_TELEFONE_COM,
       Q_CLIENTE.TELEFONE_RES           AS Q_CLIENTE_TELEFONE_RES,
       Q_CLIENTE.EMAIL_NFE              AS Q_CLIENTE_EMAIL_NFE,
       Q_CLIENTE.EMAIL2                 AS Q_CLIENTE_EMAIL2,
       Q_CLIENTE.ENDERECO_ELETRONICO    AS Q_CLIENTE_ENDERECO_ELETRONICO,
       Q_CLIENTE.RUA                    AS Q_CLIENTE_RUA,
     Q_CLIENTE.FACHADA        AS Q_CLIENTE_FACHADA,
       Q_CLIENTE.BAIRRO                 AS Q_CLIENTE_BAIRRO,
       Q_CLIENTE.NOME                   AS Q_CLIENTE_NOME,
       Q_CLIENTE.CIDADE                 AS Q_CLIENTE_CIDADE,
       Q_CLIENTE.CEP                    AS Q_CLIENTE_CEP,
       Q_CLIENTE.CPF                    AS Q_CLIENTE_CPF,
       Q_CLIENTE.RG                     AS Q_CLIENTE_RG,
       Q_OS.DESC_PROD_MOD               AS Q_OS_DESC_PROD_MOD,
       Q_OS.KM                          AS Q_OS_KM,
       Q_OS.CHASSI                      AS Q_OS_CHASSI,
       Q_OS.COR_EXTERNA                 AS Q_OS_COR_EXTERNA,
       Q_OS.ANO                         AS Q_OS_ANO,
       Q_OS.NUMERO_MOTOR                AS Q_OS_NUMERO_MOTOR,
       Q_OS.DATA_VENDA                  AS Q_OS_DATA_VENDA,
       Q_OS.CONCESSIONARIA_NOME         AS Q_OS_CONCESSIONARIA_NOME,
       Q_OS.TOTAL_OS                    AS Q_OS_TOTAL_OS,
       Q_OS.BLINDADORA                  AS Q_OS_BLINDADORA,
       Q_OS.BLINDADO                    AS Q_OS_BLINDADO,
       Q_OS.DDW_GARANTIA                AS Q_OS_DDW_GARANTIA,
       Q_OS.REC_INTERATIVA              AS Q_OS_REC_INTERATIVA,
       Q_OS.IMOBILIZADO                 AS Q_OS_IMOBILIZADO,
       Q_OS.DH_EMISSAO                  AS Q_OS_DH_EMISSAO,
       Q_OS.DATA_ENCERRADA              AS Q_OS_DATA_ENCERRADO,
     Q_OS.HORA_ENCERRADA              AS Q_OS_HORA_ENCERRADO,
       Q_OS.DH_LIBERADO                 AS Q_OS_DH_LIBERADO,
       Q_OS.TIPO_FABRICA                AS Q_OS_TIPO_FABRICA,
       Q_OS.OBSERVACAO                  AS Q_OS_OBSERVACAO,
       Q_HIST_ORC.ORCAMENTOS            AS Q_HIST_ORC_LISTA_ORCAMENTOS,
       Q_HIST_ORC.TOTAL                 AS Q_HIST_ORC_TOTAL_ORCAMENTOS,
       Q_HISTORICO.HISTORICO_OS         AS Q_HIST_ORC_HISTORICO_OS,
       Q_PARM_SYS.TERMO_OS_JLR          AS Q_PARM_SYS_TERMO_OS_JLR,
       Q_ASSINATURA.CLIENTE_RECEPCAO   AS Q_ASSINATURA_CLIENTE_RECEPCAO,
       Q_OS.DATA_EMISSAO           AS Q_OS_DATA_EMISSAO,
       Q_OS.HORA_EMISSAO            AS Q_OS_HORA_EMISSAO,
       Q_ASSINATURA.CLIENTE_ENTREGA      AS Q_ASSINATURA_CLIENTE_ENTREGA,
       Q_ASSINATURA.CLIENTE_ENTREGA_DT      AS Q_ASSINATURA_CLIE_ENTREGA_DT,
       Q_ASSINATURA.CONSULTOR_RECEPCAO      AS Q_ASSINATURA_CONSULTOR_RECEPC,
       Q_ASSINATURA.CONSULTOR_RECEPCAO_DT    AS Q_ASSINATURA_CONS_RECEPCAO_DT,
       Q_ASSINATURA.CONSULTOR_ENTREGA        AS Q_ASSINATURA_CONSULTOR_ENTREGA,
       Q_ASSINATURA.CONSULTOR_ENTREGA_DT     AS Q_ASSINATURA_CONUL_ENTREGA_DT,
     Q_OS.TOTAL_SERVICOS_LIQUIDO     AS Q_OS_TOTAL_SERVICOS_LIQUIDO,
     Q_OS.TOTAL_ITENS_LIQUIDO         AS Q_OS_TOTAL_ITENS_LIQUIDO,
     Q_OS.VALOR_SERVICOS_BRUTO    AS Q_OS_VALOR_SERVICOS_BRUTO,
       Q_OS.VALOR_ITENS_BRUTO      AS Q_OS_VALOR_ITENS_BRUTO,
     Q_OS.DESCONTO_SERVICO_PORCENT  AS Q_OS_DESCONTO_SERVICO_PORCENT,
     Q_OS.DESCONTO_ITENS_PORCENT    AS Q_OS_DESCONTO_ITENS_PORCENT,
     Q_OS.FORMA_PAGAMENTO           AS Q_OS_FORMA_PAGAMENTO,
     Q_OS.VEICULO_LOCACAO           AS Q_OS_VEICULO_LOCACAO,
     Q_CAMPANHA.LISTA_CAMPANHAS    AS Q_CAMPANHAS_LISTA_CAMPANHAS,
     Q_OS.PACOTE_TRANQUILIDADE AS Q_OS_PACOTE_TRANQUILIDADE
  FROM Q_OS,
       Q_EMPRESA,
       Q_CLIENTE,
       Q_HIST_ORC,
       Q_HISTORICO,
       QRYDADOSVEICULOS,
       Q_ASSINATURA,
       Q_PARM_SYS,
       Q_CAMPANHA
 WHERE Q_OS.COD_CLIENTE = Q_CLIENTE.COD_CLIENTE(+)
   AND Q_OS.COD_EMPRESA = Q_EMPRESA.COD_EMPRESA(+)
   AND Q_OS.COD_EMPRESA = Q_HIST_ORC.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_HIST_ORC.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_HISTORICO.COD_EMPRESA(+)
   AND Q_OS.CHASSI = Q_HISTORICO.CHASSI(+)
   AND Q_OS.CHASSI = QRYDADOSVEICULOS.CHASSI(+)
   AND Q_OS.PLACA = QRYDADOSVEICULOS.PLACA(+)
   AND Q_OS.COD_EMPRESA = Q_PARM_SYS.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_ASSINATURA.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_ASSINATURA.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_CAMPANHA.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_CAMPANHA.COD_EMPRESA(+)]]>
	</queryString>
	<field name="Q_EMPRESA_NOME_EMPRESA" class="java.lang.String"/>
	<field name="Q_EMPRESA_RUA" class="java.lang.String"/>
	<field name="Q_EMPRESA_CIDADE" class="java.lang.String"/>
	<field name="Q_EMPRESA_CEP" class="java.lang.String"/>
	<field name="Q_EMPRESA_FONE" class="java.lang.String"/>
	<field name="Q_EMPRESA_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="Q_EMPRESA_LOGO" class="java.awt.Image"/>
	<field name="Q_EMPRESA_CGC" class="java.lang.String"/>
	<field name="Q_OS_TIPO" class="java.lang.String"/>
	<field name="Q_OS_D_AG_STG" class="java.lang.String"/>
	<field name="Q_OS_DATA_AGENDADA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CONSULTOR" class="java.lang.String"/>
	<field name="Q_OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="Q_OS_PRISMA" class="java.lang.String"/>
	<field name="Q_OS_DATA_PROMETIDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_DATA_PROMETIDA_REVISADA" class="java.sql.Timestamp"/>
	<field name="Q_OS_AG_RETORNO" class="java.lang.String"/>
	<field name="Q_OS_AG_CLIENTE_AGUARDA" class="java.lang.String"/>
	<field name="Q_OS_RECEPCAO_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_AG_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="Q_OS_AG_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_AG_TESTE_RODAGEM" class="java.lang.String"/>
	<field name="Q_OS_AG_LEVAR_PECAS_SUBSTITUID" class="java.lang.String"/>
	<field name="Q_OS_VEICULO_MODIFICADO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="Q_OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="Q_OS_PLACA" class="java.lang.String"/>
	<field name="Q_OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="Q_OS_HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="Q_OS_MOBILIDADE_DESCRICAO" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_CEL" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_COM" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_RES" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL_NFE" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL2" class="java.lang.String"/>
	<field name="Q_CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="Q_CLIENTE_RUA" class="java.lang.String"/>
	<field name="Q_CLIENTE_FACHADA" class="java.lang.String"/>
	<field name="Q_CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="Q_CLIENTE_NOME" class="java.lang.String"/>
	<field name="Q_CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="Q_CLIENTE_CEP" class="java.lang.String"/>
	<field name="Q_CLIENTE_CPF" class="java.lang.String"/>
	<field name="Q_CLIENTE_RG" class="java.lang.String"/>
	<field name="Q_OS_DESC_PROD_MOD" class="java.lang.String"/>
	<field name="Q_OS_KM" class="java.lang.Double"/>
	<field name="Q_OS_CHASSI" class="java.lang.String"/>
	<field name="Q_OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="Q_OS_ANO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="Q_OS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="Q_OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="Q_OS_BLINDADORA" class="java.lang.String"/>
	<field name="Q_OS_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_DDW_GARANTIA" class="java.lang.String"/>
	<field name="Q_OS_REC_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_IMOBILIZADO" class="java.lang.String"/>
	<field name="Q_OS_DH_EMISSAO" class="java.lang.String"/>
	<field name="Q_OS_DATA_ENCERRADO" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_ENCERRADO" class="java.lang.String"/>
	<field name="Q_OS_DH_LIBERADO" class="java.lang.String"/>
	<field name="Q_OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="Q_OS_OBSERVACAO" class="java.lang.String"/>
	<field name="Q_HIST_ORC_LISTA_ORCAMENTOS" class="java.lang.String"/>
	<field name="Q_HIST_ORC_TOTAL_ORCAMENTOS" class="java.lang.Double"/>
	<field name="Q_HIST_ORC_HISTORICO_OS" class="java.lang.String"/>
	<field name="Q_PARM_SYS_TERMO_OS_JLR" class="java.lang.String"/>
	<field name="Q_ASSINATURA_CLIENTE_RECEPCAO" class="java.awt.Image"/>
	<field name="Q_OS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="Q_ASSINATURA_CLIENTE_ENTREGA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CLIE_ENTREGA_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CONSULTOR_RECEPC" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CONS_RECEPCAO_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CONSULTOR_ENTREGA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CONUL_ENTREGA_DT" class="java.sql.Timestamp"/>
	<field name="Q_OS_TOTAL_SERVICOS_LIQUIDO" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_ITENS_LIQUIDO" class="java.lang.Double"/>
	<field name="Q_OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_DESCONTO_SERVICO_PORCENT" class="java.lang.Double"/>
	<field name="Q_OS_DESCONTO_ITENS_PORCENT" class="java.lang.Double"/>
	<field name="Q_OS_FORMA_PAGAMENTO" class="java.lang.String"/>
	<field name="Q_OS_VEICULO_LOCACAO" class="java.lang.String"/>
	<field name="Q_CAMPANHAS_LISTA_CAMPANHAS" class="java.lang.String"/>
	<field name="Q_OS_PACOTE_TRANQUILIDADE" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="88">
			<frame>
				<reportElement x="0" y="0" width="555" height="84" uuid="31fde87b-dafc-43ab-af60-5e7934f8e6e0">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<frame>
					<reportElement x="0" y="1" width="346" height="58" uuid="55b30501-aa83-4644-bba1-b3c83de1566c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="194" y="20" width="70" height="10" uuid="1bf296d2-b0f9-4f71-b7ba-cd28abcbf0ad">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Entrada]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="76" y="0" width="186" height="20" uuid="0db7ccc2-7eb6-41c2-a655-09e5a784d73d">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle">
							<font size="13" isBold="true"/>
						</textElement>
						<text><![CDATA[ORDEM DE SERVIÇO: Nº]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="264" y="20" width="82" height="10" uuid="96c208f8-29c9-44e5-a541-c20d102d096f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Previsão de Entrega]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="96" y="20" width="98" height="10" uuid="11c939b7-c386-4bc8-b659-74f22817ea10">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Embaixador de Serviço]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="42" y="20" width="54" height="10" uuid="4561ae36-dea3-4ed5-be30-f43bc6a43f36">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Agendado]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="20" width="42" height="10" uuid="453f7c49-8590-4844-8b91-201f3522c183">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Placa]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="50" y="0" width="26" height="10" uuid="eaea8587-be1c-4a77-91ed-e85fc711659f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Hora:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="50" height="10" uuid="9d149449-d3bc-4e5a-b36b-e8826adfc8b9">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Data Abertura:]]></text>
					</staticText>
					<textField pattern="dd/MM/yy">
						<reportElement mode="Transparent" x="0" y="10" width="50" height="10" uuid="bb3ec3f0-d42b-4615-84c1-623d9a88997b">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="50" y="10" width="26" height="10" uuid="bf7d18ef-9843-420a-a975-e7ef86307130">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="#.###;(#.###-)">
						<reportElement mode="Transparent" x="262" y="0" width="74" height="20" uuid="5d1d9747-7256-43c9-b88e-92eacf6f7265">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="13" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_OS}]]></textFieldExpression>
					</textField>
					<textField pattern="dd/MM/yy">
						<reportElement mode="Transparent" x="42" y="30" width="54" height="15" uuid="7dc60da7-f6d8-48a4-93b3-d22af5602722"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_AGENDADA}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="42" y="45" width="54" height="13" uuid="b6d312e6-d5a3-462f-9188-d96d862cbe80"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_AGENDADA}]]></textFieldExpression>
					</textField>
					<textField pattern="dd/MM/yy">
						<reportElement mode="Transparent" x="194" y="30" width="70" height="15" uuid="d5fff862-f281-4eb5-88e7-bf5e75bd5148"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="194" y="45" width="70" height="13" uuid="4775d181-de17-4155-9bac-a7879acb4c05"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="dd/MM/yy">
						<reportElement mode="Transparent" x="264" y="30" width="82" height="15" uuid="dd49b840-3979-405e-bfec-cfe1665fd639"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="264" y="45" width="82" height="13" uuid="7a6311db-878e-411c-a907-c1db070f7c40"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_PROMETIDA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="96" y="30" width="98" height="28" uuid="fd2f87a3-2c73-4b65-9d85-edd579e75c7a"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CONSULTOR}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="0" y="30" width="42" height="28" uuid="0d672c31-89a4-4cb9-a52d-3a8092215e56">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_PLACA}]]></textFieldExpression>
					</textField>
				</frame>
				<textField>
					<reportElement mode="Transparent" x="350" y="58" width="105" height="7" uuid="56602f9a-e591-42dc-b033-5b9aadcc0c48"/>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="366" y="65" width="89" height="7" uuid="2da9e903-2d44-4668-a0bb-3ff0d55adf6e">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_CGC}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="383" y="72" width="72" height="7" uuid="9d96f7e1-1d5e-484e-ab25-440dbcde3d0b">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="503" y="72" width="37" height="7" uuid="de20b1c3-15d4-4b9f-b993-e3de577febf7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_FONE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="350" y="72" width="33" height="7" uuid="ef6a1f85-e0fc-4ced-8f61-c9c2b0f87382">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[ENDEREÇO:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="471" y="72" width="32" height="7" uuid="f913ca35-80c3-477a-ab77-ff02cbd1e630">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[TELEFONE:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="350" y="65" width="16" height="7" uuid="53858bbf-ec70-426e-88f4-a791c488318b">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle">
					<reportElement x="369" y="1" width="68" height="57" uuid="d68e5e8a-24eb-45ee-866a-09ab99467b0f">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice30107.png"]]></imageExpression>
				</image>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="477" y="1" width="68" height="57" uuid="107d08b4-3623-4aeb-9bf7-10f5947f8631">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{Q_EMPRESA_LOGO}]]></imageExpression>
				</image>
				<line>
					<reportElement x="459" y="6" width="1" height="75" uuid="03bd8fa2-09dd-4aaf-a424-d46391200bb7">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="50">
			<subreport isUsingCache="false">
				<reportElement x="0" y="3" width="555" height="41" isRemoveLineWhenBlank="true" uuid="42f2a1da-dee9-4383-b999-5f1c4bcbfbfb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsGwmAcompanhamentoSubSintoma.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport isUsingCache="false">
				<reportElement x="0" y="3" width="555" height="41" isRemoveLineWhenBlank="true" uuid="b51a9907-ac12-4143-87d5-423094c758bc">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsGwmAcompanhamentoSubDiagnostico.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="44">
			<subreport isUsingCache="false">
				<reportElement x="0" y="3" width="555" height="41" isRemoveLineWhenBlank="true" uuid="6ee84c23-ead1-4d31-ab51-8a5b024d0c61">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsGwmAcompanhamentoSubServico.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport isUsingCache="false">
				<reportElement x="0" y="3" width="555" height="41" isRemoveLineWhenBlank="true" uuid="3f465735-9222-48cf-a41a-b9fa8b0ddcec">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsGwmAcompanhamentoSubQualidade.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="50">
			<subreport isUsingCache="false">
				<reportElement x="0" y="3" width="555" height="41" isRemoveLineWhenBlank="true" uuid="0b464f8b-019e-44d0-a177-2a5c3b90d9e7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsGwmAcompanhamentoSubEntrega.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="9">
			<textField>
				<reportElement mode="Transparent" x="500" y="1" width="54" height="8" uuid="2724bdc0-530b-430b-acf3-1d90ca4db1e0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Página " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
