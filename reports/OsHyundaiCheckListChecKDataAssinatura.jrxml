<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiCheckListChecKDataAssinatura" pageWidth="555" pageHeight="74" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[select os.numero_os,
       TRUNC(os.data_emissao) as data_emissao,
       os.hora_emissao,
       TRUNC(assinatura_checkout.data_assinatura) as data_entrega,
       TO_CHAR(assinatura_checkout.data_assinatura, 'HH24:MI') as hora_entrega,
       assinatura_checkin.assinatura_cliente AS OS_ASSINAT_CLIENTE_CHECKIN,
    assinatura_checkout.assinatura_cliente AS OS_ASSINAT_CLIENTE_CHECKOUT,
    assinatura_checkin.assinatura as OS_ASSINAT_CONSULTOR_CHECKIN,
    assinatura_checkout.assinatura as OS_ASSINAT_CONSULTOR_CHECKOUT
from os,
     os_agenda, 
     mob_os_assinatura assinatura_checkin, 
     mob_os_assinatura assinatura_checkout
where os.numero_os = $P{NUMERO_OS}
and os.cod_empresa = $P{COD_EMPRESA}
and os.cod_empresa = assinatura_checkout.cod_empresa (+)
and os.numero_os = assinatura_checkout.numero_os (+)
and assinatura_checkout.aplicacao (+) = 'E'
   
AND os.cod_empresa = assinatura_checkin.cod_empresa (+)
AND os.numero_os = assinatura_checkin.numero_os (+)
AND assinatura_checkin.aplicacao (+) = 'R'

AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA(+)
AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS(+)]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="HORA_EMISSAO" class="java.lang.String"/>
	<field name="DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="HORA_ENTREGA" class="java.lang.String"/>
	<field name="OS_ASSINAT_CLIENTE_CHECKIN" class="java.awt.Image"/>
	<field name="OS_ASSINAT_CLIENTE_CHECKOUT" class="java.awt.Image"/>
	<field name="OS_ASSINAT_CONSULTOR_CHECKIN" class="java.awt.Image"/>
	<field name="OS_ASSINAT_CONSULTOR_CHECKOUT" class="java.awt.Image"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="52" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="275" height="52" uuid="723e9983-62dc-41ca-904f-e37a291104b3">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="62" y="1" width="44" height="12" uuid="4780e2a4-811d-4929-b08b-26747e8f8389">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="41" y="1" width="21" height="12" uuid="39e5e27b-2587-4d16-a118-92a24e11deb4">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="8" y="13" width="127" height="26" uuid="10195b76-68b1-4290-8fc8-89a5c5f65297">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{OS_ASSINAT_CONSULTOR_CHECKIN}]]></imageExpression>
				</image>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="140" y="13" width="127" height="26" uuid="fb01bacf-292c-47c5-b667-1c5aeaebbd30">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{OS_ASSINAT_CLIENTE_CHECKIN}]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="140" y="39" width="127" height="12" uuid="95f7a81e-f8cc-4616-b6fd-2c80fbfeaa7f">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Cliente]]></text>
				</staticText>
				<staticText>
					<reportElement x="8" y="39" width="127" height="12" uuid="5a58423d-405f-4f0f-9a96-3dc0a90dcb77">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Consultor]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="191" y="1" width="44" height="12" uuid="9b41efbc-1784-469f-9ea8-c0dbdcfefd46">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font size="8" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{HORA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="171" y="1" width="20" height="12" uuid="af557cf4-7289-4737-bd0d-905b28ff0fac">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Hora:]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="280" y="0" width="275" height="52" uuid="8b7e9828-22d2-4fb8-86ea-882516162bb9">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="8" y="13" width="127" height="36" uuid="8cbaf674-8131-4c0f-8970-9434472e0e99">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{OS_ASSINAT_CONSULTOR_CHECKOUT}]]></imageExpression>
				</image>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="140" y="13" width="127" height="36" uuid="cf039261-0e41-4450-b5da-9e5b80983cef">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{OS_ASSINAT_CLIENTE_CHECKOUT}]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="8" y="39" width="127" height="12" uuid="23c8a839-44d6-4feb-b9cc-f84b82f97c37">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Consultor]]></text>
				</staticText>
				<staticText>
					<reportElement x="140" y="39" width="127" height="12" uuid="11b7f1d1-2544-4059-85e4-0fdc57d6869c">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Cliente]]></text>
				</staticText>
				<staticText>
					<reportElement x="171" y="1" width="20" height="12" uuid="2165cb8b-fc58-4dc8-b03a-e4085c1b9f98">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Hora:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="62" y="1" width="44" height="12" uuid="554f4906-**************-0cbf9d4ef65c">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_ENTREGA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="41" y="1" width="21" height="12" uuid="82c7ffba-1a03-4a91-922b-33ad0a63431e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="191" y="1" width="44" height="12" uuid="7932df87-f8fb-4e96-84cd-8fffc80bfd89">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font size="8" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{HORA_ENTREGA}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
