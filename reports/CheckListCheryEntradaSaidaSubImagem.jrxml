<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListCheryEntradaSaidaSubImagem" pageWidth="365" pageHeight="265" whenNoDataType="NoPages" columnWidth="365" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="TESTE_FREEDOM.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select 1 FROM DUAL]]>
		</queryString>
		<field name="1" class="java.lang.Boolean"/>
	</subDataset>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\32254\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_SEGMENTO" class="java.lang.Double"/>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT OS.NUMERO_OS,
       OS.COD_EMPRESA,
       MOB_OS_IMAGEM.IMAGEM,
       MOB_OS_IMAGEM.IMAGEM_ORIGINAL,
       (SELECT a.imagem
          FROM mob_imagem a
         WHERE EXISTS (SELECT 1
                  FROM mob_imagem_filtro mif
                 WHERE mif.cod_empresa = $P{COD_EMPRESA}
                   AND mif.cod_segmento = $P{COD_SEGMENTO}
                   AND mif.id_imagem = a.id_imagem)and rownum <2) AS IMAGEM_CARROCERIA_DEFAULT
  FROM MOB_OS_IMAGEM, OS, MOB_OS_ASSINATURA, OS_DADOS_VEICULOS, OS_AGENDA
 WHERE OS.NUMERO_OS = $P{NUMERO_OS}
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND MOB_OS_IMAGEM.APLICACAO(+) = 'R'
   AND MOB_OS_IMAGEM.NUMERO_OS(+) = OS.NUMERO_OS
   AND MOB_OS_IMAGEM.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND MOB_OS_IMAGEM.IMAGEM(+) IS NOT NULL
   AND MOB_OS_ASSINATURA.NUMERO_OS(+) = OS.NUMERO_OS 
   AND MOB_OS_ASSINATURA.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND MOB_OS_ASSINATURA.APLICACAO(+) = 'R'
   AND OS_DADOS_VEICULOS.NUMERO_OS(+) = OS.NUMERO_OS 
   AND OS_DADOS_VEICULOS.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND OS.COD_OS_AGENDA  =  OS_AGENDA.COD_OS_AGENDA(+)
   AND OS.COD_EMPRESA  =  OS_AGENDA.COD_EMPRESA(+)]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="IMAGEM" class="java.awt.Image"/>
	<field name="IMAGEM_ORIGINAL" class="java.io.InputStream"/>
	<field name="IMAGEM_CARROCERIA_DEFAULT" class="java.io.InputStream"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<detail>
		<band height="265" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement mode="Transparent" x="0" y="0" width="365" height="265" uuid="0943d055-0527-448f-8bde-56933017f326">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="0" y="34" width="365" height="202" uuid="85e0f6f1-991c-428f-9f1e-34baccd36029">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{IMAGEM} == null ? $P{DIR_IMAGE_LOGO} + "crmservice43507.png" : $F{IMAGEM}]]></imageExpression>
				</image>
				<frame>
					<reportElement x="0" y="237" width="365" height="28" uuid="636360bb-db0a-449a-89ff-f7c68fcb35b9"/>
					<box>
						<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<subreport overflowType="NoStretch">
						<reportElement x="0" y="0" width="365" height="28" uuid="e6d7b19e-4fa5-4d4c-ad7d-ea3e2fdcb327">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<subreportParameter name="COD_EMPRESA">
							<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="NUMERO_OS">
							<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
						</subreportParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListCheryEntradaSaidaSubStatusAtualVeiculo.jasper"]]></subreportExpression>
					</subreport>
				</frame>
				<frame>
					<reportElement x="0" y="0" width="365" height="14" uuid="e9529537-83e9-402c-8d79-f04df5bf82ff"/>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<subreport overflowType="NoStretch">
						<reportElement x="32" y="0" width="300" height="14" uuid="f51ad9b2-8427-4f42-8bfb-274bf8ae013d">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<subreportParameter name="COD_EMPRESA">
							<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="NUMERO_OS">
							<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
						</subreportParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListCheryEntradaSaidaSubImagemLegenda.jasper"]]></subreportExpression>
					</subreport>
				</frame>
				<staticText>
					<reportElement mode="Transparent" x="0" y="14" width="365" height="20" forecolor="#000000" uuid="ec22da9d-e301-4e6d-9e34-bcde63e91cfa">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[AVARIAS EXISTENTES]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
