<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklistServico" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="afcb4e4b-2cf4-4c11-b5d3-f098eece963e">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="DataAdapter.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT OS_SERVICOS.NUMERO_OS,
       OS_SERVICOS.COD_EMPRESA,
       SERVICOS.DESCRICAO_SERVICO,
       OS_SERVICOS.COD_SERVICO,
       OS_SERVICOS.PRECO_VENDA,
       '' AS STATUS,
       SUBSTR(TO_CHAR(OS_SERVICOS.ITEM + 100), 2, 2) AS ITEM
FROM OS_SERVICOS, SERVICOS, OS_PARADA_MOTIVOS, SERVICOS_SETORES
WHERE OS_SERVICOS.COD_SERVICO = SERVICOS.COD_SERVICO
 AND OS_SERVICOS.COD_MOTIVO = OS_PARADA_MOTIVOS.COD_MOTIVO(+)
 AND OS_SERVICOS.NUMERO_OS = $P{NUMERO_OS} 
 AND OS_SERVICOS.COD_EMPRESA = $P{COD_EMPRESA}  
 AND SERVICOS_SETORES.COD_SETOR = SERVICOS.COD_SETOR
 AND OS_SERVICOS.NUMERO_OS > 0

UNION ALL

SELECT OS_SERV_ORC.NUMERO_OS,
       OS_SERV_ORC.COD_EMPRESA,   
       SERVICOS.DESCRICAO_SERVICO,          
       OS_SERV_ORC.COD_SERVICO,
       OS_SERV_ORC.PRECO_VENDA,
       'A' AS STATUS,      
       SUBSTR(TO_CHAR(OS_SERV_ORC.ITEM + 100), 2, 2) AS ITEM
  FROM OS_SERV_ORC, SERVICOS
 WHERE OS_SERV_ORC.COD_SERVICO = SERVICOS.COD_SERVICO
   AND OS_SERV_ORC.NUMERO_OS = $P{NUMERO_OS} 
   AND OS_SERV_ORC.COD_EMPRESA = $P{COD_EMPRESA} 
 ORDER BY 7, 4]]>
	</queryString>
	<field name="NUMERO_OS" class="java.math.BigDecimal"/>
	<field name="COD_EMPRESA" class="java.math.BigDecimal"/>
	<field name="DESCRICAO_SERVICO" class="java.lang.String"/>
	<field name="COD_SERVICO" class="java.lang.String"/>
	<field name="PRECO_VENDA" class="java.math.BigDecimal"/>
	<field name="STATUS" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.String"/>
	<title>
		<band height="57">
			<staticText>
				<reportElement x="10" y="36" width="20" height="19" uuid="3b991e01-b5ce-4268-bf9b-8bf353a677c8"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[It]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="2" y="32" width="553" height="1" uuid="2868eb4a-6003-455d-b5d4-94b14bcdccd1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="1" y="32" width="1" height="25" uuid="e62a9cb2-0ab6-4067-9a0c-02aa6b5be430">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="554" y="32" width="1" height="25" uuid="6166561a-7740-4792-80e6-38db2784ecaa">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="34" y="33" width="1" height="24" uuid="d7d948e8-c6a9-4cc0-bd65-e2e71108c7f8">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="41" y="36" width="100" height="18" uuid="7afae024-891d-4419-8fb6-ec5ac877d9ec"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Serviço]]></text>
			</staticText>
			<line>
				<reportElement x="144" y="33" width="1" height="24" uuid="8375b6ca-3dac-4538-a385-6687c15c694c">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="150" y="36" width="110" height="18" uuid="334a35e9-7e50-4154-83f9-7689ff00ac88"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição do Serviço]]></text>
			</staticText>
			<line>
				<reportElement x="450" y="33" width="1" height="24" uuid="11a6bf4f-d90d-4b7c-a735-b1dfe64a56a4">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="460" y="36" width="89" height="16" uuid="1898c019-fbb5-496a-be5f-5d995f07a1de"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Final]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="9" width="100" height="16" uuid="1d43c8c6-eb77-40aa-9c32-6ea4574c77f9"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Serviços]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="26" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<line>
				<reportElement x="1" y="0" width="1" height="26" uuid="ea753c0d-87b0-4fb4-85f3-1d03b1a681cb">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="554" y="0" width="1" height="26" uuid="4aac95ea-c469-4783-a441-7d9ee6cefd0e">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="10" y="2" width="20" height="18" uuid="28a29738-41d5-428c-8577-f36eed551578"/>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="41" y="2" width="100" height="18" uuid="3f853313-d26d-4b6b-b075-b5963e50fea0"/>
				<textFieldExpression><![CDATA[$F{COD_SERVICO}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="34" y="0" width="1" height="26" uuid="3bd10bcb-6ef5-441e-9e09-c99f58a5be03">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="144" y="0" width="1" height="26" uuid="2317a9ea-b33d-468b-8168-a96ffa7c8da6">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<textField>
				<reportElement x="150" y="2" width="250" height="18" uuid="63d9ab59-c325-4d30-b6b8-39ed17d1e910"/>
				<textFieldExpression><![CDATA[$F{DESCRICAO_SERVICO}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="450" y="0" width="1" height="26" uuid="d90a6299-8c0a-481f-b87f-9b2965bc6dc2">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<textField pattern="#,##0.00">
				<reportElement x="460" y="2" width="90" height="18" uuid="e3697045-2286-461c-8501-6336e5ad91ec"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="7">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<line>
				<reportElement positionType="Float" x="1" y="0" width="554" height="1" uuid="6d78b3a9-dfcc-4483-b5ee-3b15a29a324f">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
	</summary>
</jasperReport>
