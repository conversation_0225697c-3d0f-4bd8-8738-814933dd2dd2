<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsPadraoSubPecas" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="18f6558a-0b1b-4a9c-aa22-de9f0b8b14e6">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT OS_REQUISICOES.COD_ITEM AS COD_ITEM_CHAVE,
       NVL(ITENS_CUSTOS.COD_FISCAL_ITEM, OS_REQUISICOES.COD_ITEM) AS COD_ITEM,        
       OS_REQUISICOES.REQUISICAO,
       OS_REQUISICOES.COD_FORNECEDOR,
       DECODE(FORNECEDOR_ESTOQUE.OFICIAL,
              'N',
              '*' || ITENS.DESCRICAO,
              ITENS.DESCRICAO) AS DESCRICAO,
       ITENS.UNIDADE,
       OS_REQUISICOES.QUANTIDADE,
       OS_REQUISICOES.CAUSADORA,
       SUBSTR(TO_CHAR(OS_REQUISICOES.ITEM + 100), 2, 2) AS ITEM,
       ITENS.COD_MAX_DESC,
       NVL(ESTOQUE.QTDE, 0) AS ESTOQUE_QTDE,
       NVL(ESTOQUE.RESERVADO, 0) AS RESERVADO,
       OS_REQUISICOES.DESCONTO_POR_ITEM,
       (CASE -- RULE PRICE
         WHEN OS.STATUS_OS = 1 THEN
          OS_REQUISICOES.PRECO_FINAL
         WHEN OS.CORTESIA = 'S' THEN
          OS_REQUISICOES.PRECO_CORTESIA
         WHEN VW_OS_TIPOS.INTERNO = 'S' THEN
          ROUND(((100 + DECODE(VW_OS_TIPOS.AUMENTA_TRIBUTADOS,
                               'S',
                               DECODE(ITENS.COD_TRIBUTACAO,
                                      '1',
                                      DECODE(PARM_SYS.REGIME_ICMS,
                                             'S',
                                             DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                    'S',
                                                    DECODE(ICC.CLASSE_PECA,
                                                           2,
                                                           VW_OS_TIPOS.AUMENTO_PRECO_PECA,
                                                           0),
                                                    0),
                                             VW_OS_TIPOS.AUMENTO_PRECO_PECA),
                                      0),
                               VW_OS_TIPOS.AUMENTO_PRECO_PECA)) *
                DECODE(VW_OS_TIPOS.TIPO_PRECO_PECA,
                        'V',
                        OS_REQUISICOES.PRECO_VENDA,
                        'G',
                        OS_REQUISICOES.PRECO_GARANTIA,
                        'F',
                        OS_REQUISICOES.CUSTO_FORNECEDOR,
                        'P',
                        OS_REQUISICOES.PRECO_ORIGINAL,
                        OS_REQUISICOES.CUSTO_CONTABIL)) / 100,
                NVL(DECODE(PARM_SYS2.PRECO_DE_VENDA_NO_CUSTO,
                           'S',
                           ITENS_CUSTOS.QTDE_CASAS_DECIMAIS,
                           ITENS_FORNECEDOR.QTDE_CASAS_DECIMAIS),
                    2)) 
         WHEN VW_OS_TIPOS.GARANTIA = 'S' THEN
          DECODE(VW_OS_TIPOS.TIPO_PRECO_PECA,
                 'G',
                 OS_REQUISICOES.PRECO_GARANTIA,
                 NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                     OS_REQUISICOES.PRECO_GARANTIA))
         WHEN NVL(OS.FABRICA, 'N') = 'S' THEN
          OS_REQUISICOES.PRECO_GARANTIA
         WHEN SIGN(OS.FRANQUIA) = 1 THEN
          OS_REQUISICOES.PRECO_FRANQUIA
         ELSE
          ROUND(((100 - NVL(SEGURADORA.DESCONTO_REQUISICAO, 0)) / 100) * CASE
                  WHEN VW_OS_TIPOS.TIPO_PRECO_PECA = 'V' THEN
                   OS_REQUISICOES.PRECO_VENDA
                  WHEN (NVL(OS.SEGURADORA, 'N') = 'N') AND
                       (VW_OS_TIPOS.OS_EXTERNA_COM_PRECO_GARANTIA = 'S') THEN
                   OS_REQUISICOES.PRECO_GARANTIA
                  ELSE
                   NVL(OS_REQUISICOES.PRECO_ORIGINAL, OS_REQUISICOES.PRECO_VENDA)
                END,
                DECODE(NVL(SEGURADORA.DESCONTO_REQUISICAO, 0),
                       0,
                       6,
                       NVL(DECODE(PARM_SYS2.PRECO_DE_VENDA_NO_CUSTO,
                                  'S',
                                  ITENS_CUSTOS.QTDE_CASAS_DECIMAIS,
                                  ITENS_FORNECEDOR.QTDE_CASAS_DECIMAIS),
                           2)))
       END) AS PRECO_VENDA,
       NVL(OS_REQUISICOES.PRECO_LIQUIDO,
           ROUND(OS_REQUISICOES.QUANTIDADE * (CASE 
                   WHEN OS.STATUS_OS = 1 THEN
                    OS_REQUISICOES.PRECO_FINAL
                   WHEN OS.CORTESIA = 'S' THEN
                    OS_REQUISICOES.PRECO_CORTESIA
                   WHEN VW_OS_TIPOS.INTERNO = 'S' THEN
                    ROUND(((100 + DECODE(VW_OS_TIPOS.AUMENTA_TRIBUTADOS,
                                         'S',
                                         DECODE(ITENS.COD_TRIBUTACAO,
                                                '1',
                                                DECODE(PARM_SYS.REGIME_ICMS,
                                                       'S',
                                                       DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                              'S',
                                                              DECODE(ICC.CLASSE_PECA,
                                                                     2,
                                                                     VW_OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                     0),
                                                              0),
                                                       VW_OS_TIPOS.AUMENTO_PRECO_PECA),
                                                0),
                                         VW_OS_TIPOS.AUMENTO_PRECO_PECA)) *
                          DECODE(VW_OS_TIPOS.TIPO_PRECO_PECA,
                                  'V',
                                  OS_REQUISICOES.PRECO_VENDA,
                                  'G',
                                  OS_REQUISICOES.PRECO_GARANTIA,
                                  'F',
                                  OS_REQUISICOES.CUSTO_FORNECEDOR,
                                  'P',
                                  OS_REQUISICOES.PRECO_ORIGINAL,
                                  OS_REQUISICOES.CUSTO_CONTABIL)) / 100,
                          NVL(DECODE(PARM_SYS2.PRECO_DE_VENDA_NO_CUSTO,
                                     'S',
                                     ITENS_CUSTOS.QTDE_CASAS_DECIMAIS,
                                     ITENS_FORNECEDOR.QTDE_CASAS_DECIMAIS),
                              2))
                   WHEN VW_OS_TIPOS.GARANTIA = 'S' THEN
                    DECODE(VW_OS_TIPOS.TIPO_PRECO_PECA,
                           'G',
                           OS_REQUISICOES.PRECO_GARANTIA,
                           NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                               OS_REQUISICOES.PRECO_GARANTIA))
                   WHEN NVL(OS.FABRICA, 'N') = 'S' THEN
                    OS_REQUISICOES.PRECO_GARANTIA
                   WHEN SIGN(OS.FRANQUIA) = 1 THEN
                    OS_REQUISICOES.PRECO_FRANQUIA
                   ELSE
                    ROUND(((100 - NVL(SEGURADORA.DESCONTO_REQUISICAO, 0)) / 100) * CASE
                            WHEN VW_OS_TIPOS.TIPO_PRECO_PECA = 'V' THEN
                             OS_REQUISICOES.PRECO_VENDA
                            WHEN (NVL(OS.SEGURADORA, 'N') = 'N') AND
                                 (VW_OS_TIPOS.OS_EXTERNA_COM_PRECO_GARANTIA = 'S') THEN
                             OS_REQUISICOES.PRECO_GARANTIA
                            ELSE
                             NVL(OS_REQUISICOES.PRECO_ORIGINAL, OS_REQUISICOES.PRECO_VENDA)
                          END,
                          DECODE(NVL(SEGURADORA.DESCONTO_REQUISICAO, 0),
                                 0,
                                 6,
                                 NVL(DECODE(PARM_SYS2.PRECO_DE_VENDA_NO_CUSTO,
                                            'S',
                                            ITENS_CUSTOS.QTDE_CASAS_DECIMAIS,
                                            ITENS_FORNECEDOR.QTDE_CASAS_DECIMAIS),
                                     2)))
                 END),
                 2)) AS PRECO_TOTAL,
       OS_REQUISICOES.SEQ_PAF_ITEM,
       'A' STATUS,
       ITENS_FORNECEDOR.NCM,
       'N' AS DESAPROVADO
  FROM OS_REQUISICOES,
       ESTOQUE,
       ITENS,
       OS,
       VW_OS_TIPOS           OS_TIPOS,
       FORNECEDOR_ESTOQUE,
       SEGURADORA,
       ITENS_FORNECEDOR,
       ITENS_CLASSE_CONTABIL ICC,
       PARM_SYS,
       PARM_SYS2,
       ITENS_CUSTOS,
       VW_OS_TIPOS
 WHERE (OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM)
   AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR
   AND (OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM(+))
   AND (OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR(+))
   AND (OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA(+))
   AND (OS_REQUISICOES.NUMERO_OS = OS.NUMERO_OS)
   AND (OS_REQUISICOES.COD_EMPRESA = OS.COD_EMPRESA)
   AND (OS.TIPO = OS_TIPOS.TIPO)
   AND (OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA)
   AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA(+)
   AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
   AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
   AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL(+)
   AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
   AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
   AND OS_REQUISICOES.COD_ITEM = ITENS_CUSTOS.COD_ITEM(+)
   AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_CUSTOS.COD_FORNECEDOR(+)
   AND OS_REQUISICOES.COD_EMPRESA = ITENS_CUSTOS.COD_EMPRESA(+)
   AND OS.TIPO = VW_OS_TIPOS.TIPO
   AND OS.COD_EMPRESA = VW_OS_TIPOS.COD_EMPRESA
	AND (OS_REQUISICOES.COD_EMPRESA, OS_REQUISICOES.NUMERO_OS) IN (
		SELECT * FROM TABLE(PKG_CRM_SERVICE_UTIL.GET_TABLE_OS_RELAC_NUM_FABRICA($P{NUMERO_OS}, $P{COD_EMPRESA}))
	)
	
   AND OS.NUMERO_OS > 0

UNION ALL

SELECT OS_ORCAMENTOS_ITENS.COD_ITEM AS COD_ITEM_CHAVE,
       NVL(ITENS_CUSTOS.COD_FISCAL_ITEM, OS_ORCAMENTOS_ITENS.COD_ITEM) AS COD_ITEM,
       NULL AS REQUISICAO,
       FORNECEDOR_ESTOQUE.COD_FORNECEDOR,
       DECODE(FORNECEDOR_ESTOQUE.OFICIAL,
              'N',
              '*' || ITENS.DESCRICAO,
              ITENS.DESCRICAO) AS DESCRICAO,
       ITENS.UNIDADE,
       OS_ORCAMENTOS_ITENS.QUANTIDADE,
       NULL AS CAUSADORA,
       SUBSTR(TO_CHAR(OS_ORCAMENTOS_ITENS.ITEM + 100), 2, 2) AS ITEM,
       ITENS.COD_MAX_DESC,
       NVL(ESTOQUE.QTDE, 0) AS ESTOQUE_QTDE, 
       NVL(ESTOQUE.RESERVADO, 0) AS RESERVADO,   
       NVL(OS_ORCAMENTOS_ITENS.DESCONTO_POR_ITEM, 0) AS DESCONTO_POR_ITEM,  
       OS_ORCAMENTOS_ITENS.PRECO_VENDA,     
       DECODE(OS.TEM_DESCONTO_ITEM,
              'S',
              NVL(OS_ORCAMENTOS_ITENS.PRECO_LIQUIDO,
                  (OS_ORCAMENTOS_ITENS.PRECO_VENDA *
                  OS_ORCAMENTOS_ITENS.QUANTIDADE)),
              (OS_ORCAMENTOS_ITENS.PRECO_VENDA *
              OS_ORCAMENTOS_ITENS.QUANTIDADE)) AS PRECO_TOTAL,
       OS_ORCAMENTOS_ITENS.SEQ_PAF_ITEM,  
       'A' STATUS,            
       ITENS_FORNECEDOR.NCM,
       OS_ORCAMENTOS_ITENS.DESAPROVADO
  FROM OS_ORCAMENTOS_ITENS,
       ESTOQUE,
       ITENS,
       FORNECEDOR_ESTOQUE,
       ITENS_CUSTOS,
       ITENS_FORNECEDOR,
       OS
 WHERE (OS_ORCAMENTOS_ITENS.COD_ITEM = ITENS.COD_ITEM)
   AND OS_ORCAMENTOS_ITENS.COD_FORNECEDOR =
       FORNECEDOR_ESTOQUE.COD_FORNECEDOR
   AND (OS_ORCAMENTOS_ITENS.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM)
   AND (OS_ORCAMENTOS_ITENS.COD_FORNECEDOR =
       ITENS_FORNECEDOR.COD_FORNECEDOR)
   AND (OS_ORCAMENTOS_ITENS.COD_ITEM = ESTOQUE.COD_ITEM(+))
   AND (OS_ORCAMENTOS_ITENS.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR(+))
   AND (OS_ORCAMENTOS_ITENS.COD_EMPRESA = ESTOQUE.COD_EMPRESA(+))
   AND (OS_ORCAMENTOS_ITENS.COD_ITEM = ITENS_CUSTOS.COD_ITEM(+))
   AND (OS_ORCAMENTOS_ITENS.COD_FORNECEDOR = ITENS_CUSTOS.COD_FORNECEDOR(+))
   AND (OS_ORCAMENTOS_ITENS.COD_EMPRESA = ITENS_CUSTOS.COD_EMPRESA(+))
   AND (OS_ORCAMENTOS_ITENS.COD_EMPRESA = OS.COD_EMPRESA)
   AND (OS_ORCAMENTOS_ITENS.NUMERO_OS = OS.NUMERO_OS)
   AND (OS_ORCAMENTOS_ITENS.COD_EMPRESA, OS_ORCAMENTOS_ITENS.NUMERO_OS) IN (
		SELECT * FROM TABLE(PKG_CRM_SERVICE_UTIL.GET_TABLE_OS_RELAC_NUM_FABRICA($P{NUMERO_OS}, $P{COD_EMPRESA}))
	)

   AND OS.NUMERO_OS < 0
 ORDER BY 9, 2]]>
	</queryString>
	<field name="COD_ITEM_CHAVE" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="REQUISICAO" class="java.lang.Double"/>
	<field name="COD_FORNECEDOR" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="UNIDADE" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.lang.Double"/>
	<field name="CAUSADORA" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.String"/>
	<field name="COD_MAX_DESC" class="java.lang.String"/>
	<field name="ESTOQUE_QTDE" class="java.lang.Double"/>
	<field name="RESERVADO" class="java.lang.Integer"/>
	<field name="DESCONTO_POR_ITEM" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="PRECO_TOTAL" class="java.lang.Double"/>
	<field name="SEQ_PAF_ITEM" class="java.lang.Double"/>
	<field name="STATUS" class="java.lang.String"/>
	<field name="NCM" class="java.lang.String"/>
	<field name="DESAPROVADO" class="java.lang.String"/>
	<title>
		<band height="35" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="4" y="20" width="15" height="15" uuid="660f2197-b49f-4f04-b3c2-ddd4ecaa454d"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[IT]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="20" width="68" height="15" uuid="40d43db5-92c5-4396-a2f1-723487b96194"/>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Código do Item]]></text>
			</staticText>
			<staticText>
				<reportElement x="91" y="20" width="112" height="15" uuid="14179160-05bc-45c5-98d9-f8b1cff77577"/>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição do item]]></text>
			</staticText>
			<staticText>
				<reportElement x="251" y="20" width="20" height="15" uuid="a75c0982-b9fc-4cd9-a93f-5644151f7ced"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[UN]]></text>
			</staticText>
			<staticText>
				<reportElement x="272" y="20" width="20" height="15" uuid="f8c9090c-cc10-4bbb-a08b-9dfb40cdd0a2"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[LD]]></text>
			</staticText>
			<staticText>
				<reportElement x="314" y="20" width="47" height="15" uuid="84bf3f67-aacb-4d31-8bc9-2fea08203e56"/>
				<textElement textAlignment="Left">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Requisição]]></text>
			</staticText>
			<staticText>
				<reportElement x="362" y="20" width="26" height="15" uuid="dc27b2f3-bd41-4d9e-8e71-f494e1a76565"/>
				<textElement textAlignment="Right">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement x="449" y="20" width="50" height="15" uuid="19000713-001f-4b06-9cea-d85f3b8180ac"/>
				<textElement textAlignment="Right">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Preço Unit.]]></text>
			</staticText>
			<staticText>
				<reportElement x="501" y="20" width="50" height="15" uuid="42294cb0-56f6-4db6-b56b-e19d91defa13"/>
				<textElement textAlignment="Right">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Final]]></text>
			</staticText>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="1" width="554" height="18" backcolor="#D9D9D9" uuid="29ae2680-ee48-4cfd-b31c-148413bd5dde">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="6" y="3" width="100" height="14" uuid="283ba266-8096-42cc-9cda-8273589948f2">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Peças]]></text>
			</staticText>
			<staticText>
				<reportElement x="206" y="20" width="42" height="15" uuid="c69973fa-e4d6-4bee-89a6-8269a0bdafa9"/>
				<textElement textAlignment="Left">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[NCM]]></text>
			</staticText>
			<staticText>
				<reportElement x="293" y="20" width="20" height="15" uuid="c3d409ba-cd28-4260-a06e-3472d9f43120"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[CS.]]></text>
			</staticText>
			<staticText>
				<reportElement x="392" y="20" width="56" height="15" uuid="aa851094-48a8-4865-8968-5417785af2f9">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Estoque/Res.]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement x="4" y="0" width="15" height="12" uuid="957ff814-d663-4521-a047-220d38a03aaa"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="21" y="0" width="68" height="12" uuid="d273876f-580e-4b4d-a65f-8095d3b686f6">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
					<paragraph lineSpacing="Single" firstLineIndent="1" tabStopWidth="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_ITEM_CHAVE}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="91" y="0" width="112" height="12" uuid="64b46fbc-1b2b-4c3c-a1ab-b26550d191fb"/>
				<textElement>
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="251" y="0" width="20" height="12" uuid="65d32933-5a24-486e-96d1-613564cdc253">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{UNIDADE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="272" y="0" width="20" height="12" uuid="82ed8312-e83e-4d15-b6cb-56bb5f24591f">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_MAX_DESC}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="314" y="0" width="47" height="12" uuid="a0106d15-d84d-4b24-b2aa-831c6e394155">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{REQUISICAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="362" y="0" width="26" height="12" uuid="db0264aa-f2b7-4d05-88f4-398ab1e8af08">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="449" y="0" width="50" height="12" uuid="94d12ec3-bbf4-4909-883a-c23a4a4a3f5d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="501" y="0" width="50" height="12" uuid="ef20853e-cdd4-4006-abc7-bf81b260533e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_TOTAL}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="206" y="0" width="42" height="12" uuid="fdf1d63c-4599-4ab5-ae68-14cf3571615c"/>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NCM}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="293" y="0" width="20" height="12" uuid="678d1707-72a5-4908-adee-66f0a100f475">
					<printWhenExpression><![CDATA[$F{CAUSADORA} == "S"]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Calibri" isBold="false"/>
				</textElement>
				<text><![CDATA[X]]></text>
			</staticText>
			<textField>
				<reportElement x="392" y="0" width="28" height="12" uuid="1e215a70-a012-414b-98cc-9d4c97ad1c93"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ESTOQUE_QTDE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="420" y="0" width="28" height="12" uuid="34840897-dae9-4899-abd4-790378795bdf">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESERVADO}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="5" y="6" width="542" height="1" uuid="7744b410-4aea-4566-ac3a-9ed5ca6dfb9b">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<printWhenExpression><![CDATA[$F{DESAPROVADO}.equals("S")]]></printWhenExpression>
				</reportElement>
			</line>
		</band>
	</detail>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
