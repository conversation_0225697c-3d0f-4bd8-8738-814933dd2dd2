<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsJaguarLandSubPecas" pageWidth="532" pageHeight="96" columnWidth="532" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH Q_PECAS AS (
SELECT
 DECODE(OS.SEQUENCIA_DAV, 0, OS_REQUISICOES.COD_ITEM,
                 NVL(ITENS_CUSTOS.COD_FISCAL_ITEM, OS_REQUISICOES.COD_ITEM)) AS COD_ITEM,
 OS_REQUISICOES.REQUISICAO, OS_REQUISICOES.COD_FORNECEDOR,
 DECODE(FORNECEDOR_ESTOQUE.OFICIAL, 'N', '*' || ITENS.DESCRICAO, ITENS.DESCRICAO) AS DESCRICAO,
 OS_REQUISICOES.QUANTIDADE, OS_REQUISICOES.CAUSADORA,
 OS_REQUISICOES.ITEM, ITENS.COD_MAX_DESC, ITENS.UNIDADE,
 OS_REQUISICOES.NUMERO_OS,
 0 AS COD_OS_AGENDA,
 NVL(ESTOQUE.QTDE, 0) AS ESTOQUE_QTDE,
 NVL(ESTOQUE.RESERVADO, 0) AS RESERVADO,
 ROUND(DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
             ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                                   DECODE(ITENS.COD_TRIBUTACAO, '1',
                                     DECODE(PARM_SYS.REGIME_ICMS, 'S',
                                       DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                                         DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                     0),
                                              0),
                                            OS_TIPOS.AUMENTO_PRECO_PECA),
                                          0),
                                    OS_TIPOS.AUMENTO_PRECO_PECA)) *
                   DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA, 'G', OS_REQUISICOES.PRECO_GARANTIA,
                                                    'F', OS_REQUISICOES.CUSTO_FORNECEDOR, 
                                                         DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
                  ) / 100,
       DECODE(OS_TIPOS.GARANTIA, 'S', OS_REQUISICOES.PRECO_GARANTIA,
         DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
           DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
             ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*OS_REQUISICOES.PRECO_VENDA)/100)))))),NVL(ITENS_FORNECEDOR.QTDE_CASAS_DECIMAIS,2)) AS PRECO_VENDA,
 ROUND(OS_REQUISICOES.QUANTIDADE * ROUND(
   DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
     DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
       DECODE(OS_TIPOS.INTERNO, 'S',
                   ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                                         DECODE(ITENS.COD_TRIBUTACAO, '1',
                                           DECODE(PARM_SYS.REGIME_ICMS, 'S',
                                             DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                                               DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                           0),
                                                    0),
                                                  OS_TIPOS.AUMENTO_PRECO_PECA),
                                                0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                     DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA, 'G', OS_REQUISICOES.PRECO_GARANTIA,
                                                      'F', OS_REQUISICOES.CUSTO_FORNECEDOR, 
                                                           DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
                    ) / 100,
         DECODE(OS_TIPOS.GARANTIA, 'S', OS_REQUISICOES.PRECO_GARANTIA,
           DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
             DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
               ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*OS_REQUISICOES.PRECO_VENDA)/100)))))),NVL(ITENS_FORNECEDOR.QTDE_CASAS_DECIMAIS,2)),2) AS PRECO_TOTAL
FROM OS_REQUISICOES, ESTOQUE, ITENS, OS, VW_OS_TIPOS OS_TIPOS, FORNECEDOR_ESTOQUE , SEGURADORA,
   ITENS_FORNECEDOR, ITENS_CLASSE_CONTABIL ICC, PARM_SYS, PARM_SYS2, ITENS_CUSTOS,
   OS_TIPOS_EMPRESAS OTE
WHERE (OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM)
  AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR
  AND (OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM (+))
  AND (OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR (+))
  AND (OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA (+))
  AND (OS_REQUISICOES.NUMERO_OS = OS.NUMERO_OS)
  AND (OS_REQUISICOES.COD_EMPRESA = OS.COD_EMPRESA)
  AND (OS.TIPO = OS_TIPOS.TIPO)
  AND (OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA)
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND OS_REQUISICOES.COD_ITEM = ITENS_CUSTOS.COD_ITEM (+)
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_CUSTOS.COD_FORNECEDOR (+)
  AND OS_REQUISICOES.COD_EMPRESA = ITENS_CUSTOS.COD_EMPRESA (+)
  AND (OS_REQUISICOES.NUMERO_OS = $P{NUMERO_OS})
  AND (OS_REQUISICOES.COD_EMPRESA = $P{COD_EMPRESA})
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
ORDER BY OS_REQUISICOES.ITEM, OS_REQUISICOES.COD_ITEM
),

TENHO AS (
SELECT COUNT(*) AS LINHAS
                  FROM OS_REQUISICOES OSR
                 WHERE OSR.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSR.NUMERO_OS = $P{NUMERO_OS}),
MAIOR_QUANTIDADE AS (
       SELECT (MAX(TOT)) AS LINHAS
        FROM (SELECT COUNT(*) AS TOT
                  FROM OS_ORIGINAL OSO
                 WHERE OSO.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSO.NUMERO_OS = $P{NUMERO_OS}
                UNION ALL
                SELECT COUNT(*) AS TOT
                  FROM OS_SERVICOS OSS
                 WHERE OSS.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSS.NUMERO_OS = $P{NUMERO_OS}
                UNION ALL
                SELECT COUNT(*) AS TOT
                  FROM OS_REQUISICOES OSR
                 WHERE OSR.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSR.NUMERO_OS = $P{NUMERO_OS})
),

DEVE_TER AS (
       SELECT CASE WHEN MOD(MAIOR_QUANTIDADE.LINHAS,6) = 0
              THEN MAIOR_QUANTIDADE.LINHAS + 1
              ELSE (MAIOR_QUANTIDADE.LINHAS + ( 7 - MOD(MAIOR_QUANTIDADE.LINHAS,6))) 
              END AS LINHAS
        FROM MAIOR_QUANTIDADE
),

Q_NULLROWS AS (SELECT NULL AS COD_ITEM,
                     NULL AS REQUISICAO, 
                     NULL AS COD_FORNECEDOR,
                     NULL AS DESCRICAO, 
                     NULL AS QUANTIDADE,
                     NULL AS CAUSADORA,
                     NULL AS ITEM,
                     NULL AS COD_MAX_DESC,
                     NULL AS UNIDADE,
                     NULL AS NUMERO_OS,
                     NULL AS COD_AGENDA,
                     NULL AS ESTOQUE_QTDE,
                     NULL AS RESERVADO,
                     NULL AS PRECO_VENDA,
                     NULL AS PRECO_TOTAL
                     FROM DUAL, TENHO,MAIOR_QUANTIDADE,DEVE_TER
                     WHERE MOD(TENHO.LINHAS,6)<>0 OR TENHO.LINHAS = 0
                     CONNECT BY LEVEL < DEVE_TER.LINHAS - TENHO.LINHAS
)


SELECT Q_PECAS.* FROM Q_PECAS
UNION ALL
SELECT Q_NULLROWS.* FROM Q_NULLROWS]]>
	</queryString>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="REQUISICAO" class="java.lang.Double"/>
	<field name="COD_FORNECEDOR" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.lang.Double"/>
	<field name="CAUSADORA" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="COD_MAX_DESC" class="java.lang.String"/>
	<field name="UNIDADE" class="java.lang.String"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="ESTOQUE_QTDE" class="java.lang.Double"/>
	<field name="RESERVADO" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="PRECO_TOTAL" class="java.lang.Double"/>
	<variable name="SOMA" class="java.lang.Double" resetType="Page" calculation="Sum">
		<variableExpression><![CDATA[$F{PRECO_TOTAL} == null ? 0.0 : $F{PRECO_TOTAL}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="12">
			<staticText>
				<reportElement mode="Opaque" x="399" y="0" width="60" height="12" backcolor="#E3E3E3" uuid="b6619be0-ce46-4950-a008-6305774ac47a"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Unitário]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="103" y="0" width="148" height="12" backcolor="#E3E3E3" uuid="e0542ebf-7846-4556-b3b0-4ddd958d417e"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[DESCRIÇÃO DO ITEM]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="37" height="12" backcolor="#E3E3E3" uuid="7e68508d-490e-43e1-a3a7-9f519f74a2ba"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[ÍTEM Nº.]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="330" y="0" width="30" height="12" backcolor="#E3E3E3" uuid="84dd482e-b579-400b-9774-f823ed2acfbb"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="360" y="0" width="39" height="12" backcolor="#E3E3E3" uuid="c15a2446-6aae-4387-a70c-ef8c8d357a75"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Estoque]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="37" y="0" width="66" height="12" backcolor="#E3E3E3" uuid="c92eb9c2-7086-46ba-bd04-26584e944a46"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[CÓD.PEÇA]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="459" y="0" width="73" height="12" backcolor="#E3E3E3" uuid="dd72901c-dd9f-4e41-ae89-e1e226d14b37">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Final]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="251" y="0" width="30" height="12" backcolor="#E3E3E3" uuid="0b496b0b-f2e3-4300-a508-5aa9a33cb6d4"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[UN]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="281" y="0" width="50" height="12" backcolor="#E3E3E3" uuid="6e121e7a-3ec4-47ef-a710-8278b54b357a"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Requisição]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement mode="Transparent" x="251" y="0" width="30" height="12" uuid="e633ee71-d47b-4005-abe8-9949b00182d0"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{UNIDADE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="281" y="0" width="50" height="12" uuid="6c346ba7-c96e-47ad-903c-968d63485e21"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{REQUISICAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="360" y="0" width="39" height="12" uuid="1e17ce6c-c7e1-499f-b19c-30cb3f8df5b1"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ESTOQUE_QTDE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="103" y="0" width="148" height="12" uuid="9f9d27b2-4c37-4b56-8f66-d02d39fa407b"/>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="0" y="0" width="37" height="12" uuid="708b2b1c-7884-4f7f-a3ab-3dadcfcc377c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="331" y="0" width="29" height="12" uuid="3c96f8dc-6a1c-43e8-8711-ce7b5bd72108"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Transparent" x="459" y="0" width="73" height="12" uuid="bac11b92-8d36-4f20-a236-f5dc394ee32b"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_TOTAL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Transparent" x="399" y="0" width="60" height="12" uuid="25010c9e-df02-48e2-a9da-d1c869cc17e7"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="37" y="0" width="66" height="12" uuid="32b81060-2a7e-4fd1-be2f-c1811c3f4853"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_ITEM}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="12">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="459" height="12" backcolor="#E3E3E3" uuid="c43f7da6-a9e1-487d-9eef-b83c8697d607"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="7">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Subtotal PEÇAS]]></text>
			</staticText>
			<textField evaluationTime="Page" pattern="#,##0.00#;#,##0.00#-" isBlankWhenNull="false">
				<reportElement mode="Opaque" x="459" y="0" width="73" height="12" backcolor="#E3E3E3" uuid="cbb30017-7593-4796-ae47-f5238a0d21d4"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SOMA}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
