<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsPadrao" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a5d195c3-46e2-4e2e-9575-573e21d1a687">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="TesteFreedom"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="false"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="356"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="630"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_SERVICO" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT OS.NUMERO_OS,
	OS.COD_EMPRESA,
	$P{COD_SERVICO} COD_SERVICO,
	CASE
		WHEN OS.NUMERO_OS < 0 THEN
			'ORC.: ' || TO_CHAR(ABS(OS.NUMERO_OS))
		ELSE
			'O.S: ' || TO_CHAR(OS.NUMERO_OS)
		END TITULO,
	(PKG_CRM_SERVICE_UTIL.GET_TIPO_OS(OS.COD_EMPRESA, OS.TIPO, 'XX')) TIPO,
	TO_CHAR(TO_DATE(SYSDATE,'DD/MM/RRRR'), 'DD/MM/RRRR') DATA_PEDIDO,
	GET_FIRST_SECOND_NAME(P.DESCRICAO_PRODUTO, 2) || ' / ' || INITCAP(PRODUTOMODELO.DESCRICAO_MODELO) AS VEICULO,  
	ODV.CHASSI,
	ODV.KM,
	NVL(ODV.ANO, PRODUTOMODELO.ANO_MODELO) ANO_MODELO,
	ODV.PLACA,
	SERV.DESCRICAO_SERVICO,
	OS_SERV.PRECO_CUSTO VALOR_COMBINADO,
	NVL(FORN.CPF,FORN.CGC) CPF_CNPJ_FORN,
	FORN.NOME,
	FORN.INSCRICAO_ESTADUAL,
	OS_SERV.MOTIVO
FROM OS,
	OS_DADOS_VEICULOS ODV,
	PRODUTOS_MODELOS  PRODUTOMODELO,
	PRODUTOS          P,
	OS_SERVICOS       OS_SERV,
	SERVICOS          SERV,
	CLIENTE_DIVERSO   FORN
	
WHERE 1 = 1
   AND OS.NUMERO_OS = $P{NUMERO_OS}
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND SERV.COD_SERVICO = $P{COD_SERVICO}
   AND OS.NUMERO_OS   = OS_SERV.NUMERO_OS
   AND OS.COD_EMPRESA = OS_SERV.COD_EMPRESA
   AND OS_SERV.COD_SERVICO = SERV.COD_SERVICO
   AND OS.COD_EMPRESA = ODV.COD_EMPRESA
   AND OS.NUMERO_OS = ODV.NUMERO_OS
   AND OS.COD_PRODUTO = PRODUTOMODELO.COD_PRODUTO
   AND OS.COD_MODELO = PRODUTOMODELO.COD_MODELO
   AND OS.COD_PRODUTO = P.COD_PRODUTO
   AND OS_SERV.COD_CLIENTE = FORN.COD_CLIENTE]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="NUMERO_OS"/>
		<property name="com.jaspersoft.studio.field.label" value="NUMERO_OS"/>
	</field>
	<field name="COD_EMPRESA" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="COD_EMPRESA"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_EMPRESA"/>
	</field>
	<field name="COD_SERVICO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_SERVICO"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_SERVICO"/>
	</field>
	<field name="TITULO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="TITULO"/>
		<property name="com.jaspersoft.studio.field.label" value="TITULO"/>
	</field>
	<field name="TIPO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="TIPO"/>
		<property name="com.jaspersoft.studio.field.label" value="TIPO"/>
	</field>
	<field name="DATA_PEDIDO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="DATA_PEDIDO"/>
		<property name="com.jaspersoft.studio.field.label" value="DATA_PEDIDO"/>
	</field>
	<field name="VEICULO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="VEICULO"/>
		<property name="com.jaspersoft.studio.field.label" value="VEICULO"/>
	</field>
	<field name="CHASSI" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="CHASSI"/>
		<property name="com.jaspersoft.studio.field.label" value="CHASSI"/>
	</field>
	<field name="KM" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="KM"/>
		<property name="com.jaspersoft.studio.field.label" value="KM"/>
	</field>
	<field name="ANO_MODELO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="ANO_MODELO"/>
		<property name="com.jaspersoft.studio.field.label" value="ANO_MODELO"/>
	</field>
	<field name="PLACA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PLACA"/>
		<property name="com.jaspersoft.studio.field.label" value="PLACA"/>
	</field>
	<field name="DESCRICAO_SERVICO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="DESCRICAO_SERVICO"/>
		<property name="com.jaspersoft.studio.field.label" value="DESCRICAO_SERVICO"/>
	</field>
	<field name="VALOR_COMBINADO" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="VALOR_COMBINADO"/>
		<property name="com.jaspersoft.studio.field.label" value="VALOR_COMBINADO"/>
	</field>
	<field name="CPF_CNPJ_FORN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="CPF_CNPJ_FORN"/>
		<property name="com.jaspersoft.studio.field.label" value="CPF_CNPJ_FORN"/>
	</field>
	<field name="NOME" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="NOME"/>
		<property name="com.jaspersoft.studio.field.label" value="NOME"/>
	</field>
	<field name="INSCRICAO_ESTADUAL" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="INSCRICAO_ESTADUAL"/>
		<property name="com.jaspersoft.studio.field.label" value="INSCRICAO_ESTADUAL"/>
	</field>
	<field name="MOTIVO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="MOTIVO"/>
		<property name="com.jaspersoft.studio.field.label" value="MOTIVO"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="71" splitType="Stretch">
			<subreport>
				<reportElement positionType="Float" x="0" y="5" width="554" height="66" isRemoveLineWhenBlank="true" uuid="e6b46a5a-93d8-41cc-beee-41c77135cf38">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"CabecalhoPadraoRetrato.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</title>
	<detail>
		<band height="266">
			<rectangle>
				<reportElement mode="Opaque" x="-1" y="175" width="554" height="18" backcolor="#D9D9D9" uuid="e42693c7-1996-4134-a626-892d37b29efe">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<printWhenExpression><![CDATA[$F{MOTIVO}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="1" y="134" width="554" height="18" backcolor="#D9D9D9" uuid="80cce499-9c01-4579-af4b-96a1a62636fe">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="1" y="95" width="554" height="18" backcolor="#D9D9D9" uuid="1c483dc2-5a40-4e60-9c2b-d3d391d1bfcf">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="60" width="554" height="18" backcolor="#D9D9D9" uuid="c68d571b-6c1d-4dc2-9eed-904d49902d8c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="23" width="554" height="18" backcolor="#D9D9D9" uuid="f044ce1b-c6b0-40ca-b473-27331128e380">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="420" y="25" width="59" height="14" uuid="429a4178-7d6b-4694-aeda-3e3e79179231">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Data pedido:]]></text>
			</staticText>
			<textField>
				<reportElement x="6" y="25" width="114" height="14" uuid="d3cadaa7-a586-4ec4-923e-cb5175e1acaf">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TITULO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="6" y="97" width="264" height="14" uuid="d52316be-ebf1-4b72-8c92-b9d12599f8ec">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="420" y="97" width="128" height="14" uuid="3917fff9-0fd2-4aad-bcc4-c31e01c0f54d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{INSCRICAO_ESTADUAL}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="479" y="25" width="62" height="14" uuid="3c7e30e7-454e-47a3-9d07-d12cd5dbedfc">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DATA_PEDIDO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="40" y="63" width="129" height="12" uuid="fac4a522-459b-4f18-846d-21e43e82bfb8">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHASSI}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="189" y="63" width="90" height="12" uuid="6ad35e8f-8a12-4c3b-b866-f36c20c88e5d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[ $F{KM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="337" y="63" width="83" height="12" uuid="893b1b2e-eef7-48fa-9cdc-f1b73d903b98">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ANO_MODELO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="63" width="63" height="12" uuid="ef00080a-e9a8-4abd-98f8-fc553516f3b4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PLACA}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="278" y="97" width="136" height="14" uuid="e9c14176-d1c9-45e7-a98f-86903acfe648">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CPF_CNPJ_FORN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="6" y="78" width="59" height="14" uuid="58901384-2fec-4e37-b354-4be7eea345f5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Fornecedor]]></text>
			</staticText>
			<staticText>
				<reportElement x="278" y="78" width="59" height="14" uuid="bcecf44c-7b72-4606-a612-bbe40e28e81d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[CNPJ]]></text>
			</staticText>
			<staticText>
				<reportElement x="420" y="78" width="124" height="14" uuid="d14ddad6-09a8-412e-b2d5-e9fac9d7fb85">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Inscrição estadual]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="45" y="44" width="298" height="14" uuid="5e10d993-42ff-41c6-837b-a72d916d8784">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[ $F{VEICULO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="115" width="554" height="19" uuid="676f71db-a333-45ca-bf3d-fcc2990040e7">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Calibri" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Serviço Autorizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="-1" width="334" height="24" uuid="ecff7413-984c-49b4-b2ec-06369738f9f9">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Calibri" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[Requisição externa para serviço terceiro]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="6" y="154" width="119" height="14" uuid="0cf0f90c-d10c-4717-b6e5-fd21e06c1e45">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_SERVICO}]]></textFieldExpression>
			</textField>
			<textField pattern="¤#,##0.00;¤-#,##0.00" isBlankWhenNull="true">
				<reportElement x="446" y="154" width="106" height="14" uuid="241ee1b7-6acc-4232-859a-087347e3eb13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{VALOR_COMBINADO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="127" y="154" width="317" height="14" uuid="1797d79d-d093-4396-b6d7-4fb2a8a04b58">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_SERVICO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="6" y="136" width="59" height="14" uuid="adf17ad5-573f-43bc-8d8b-aa6abe5bc81f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Código]]></text>
			</staticText>
			<staticText>
				<reportElement x="127" y="136" width="59" height="14" uuid="4dddf519-c1a7-4fdc-bcfe-4b86890fab28">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement x="446" y="136" width="106" height="14" uuid="4729983d-2008-4e2c-9fff-5a0fd93f9d75">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor combinado]]></text>
			</staticText>
			<textField>
				<reportElement x="125" y="25" width="114" height="14" uuid="c1fcd9cb-ab34-4b03-bc08-e662de4ed7c7">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIPO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="193" width="553" height="67" uuid="f081bfe7-04aa-419d-a8dd-e0b4ef0bb784">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{MOTIVO}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
				</reportElement>
				<box topPadding="8" leftPadding="8" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{MOTIVO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="6" y="177" width="59" height="14" uuid="6a6bb5ab-7ff3-4935-9a06-095fcd314221">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{MOTIVO}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Observação]]></text>
			</staticText>
			<staticText>
				<reportElement x="6" y="44" width="44" height="14" uuid="0f0c8f63-55d8-469a-bfb3-b3cf6722c7b2">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Veiculo:]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="61" width="17" height="14" uuid="46096ccf-2b8a-435b-b2ac-a323ddf4dc40">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[KM:]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="61" width="49" height="14" uuid="79e8dd57-65b6-40c5-9ba4-51f5415421ca">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Ano/Mod:]]></text>
			</staticText>
			<staticText>
				<reportElement x="420" y="61" width="26" height="14" uuid="6109fe18-a564-416f-b390-814a01b037ac">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Placa:]]></text>
			</staticText>
			<textField>
				<reportElement x="6" y="63" width="36" height="12" uuid="17e7aaea-dae9-446d-b293-92060b2ae834">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Chassi:"]]></textFieldExpression>
			</textField>
		</band>
		<band height="80">
			<staticText>
				<reportElement x="26" y="65" width="220" height="14" uuid="292125d3-6efd-41c1-8bc7-4d0df6e2a97b"/>
				<box>
					<topPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center"/>
				<text><![CDATA[Autorizado por]]></text>
			</staticText>
			<staticText>
				<reportElement x="306" y="65" width="220" height="14" uuid="96d1a305-a190-4133-97de-f282a91586e1"/>
				<box>
					<topPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center"/>
				<text><![CDATA[Visto por]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
