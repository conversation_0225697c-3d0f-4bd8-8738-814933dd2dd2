<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiCheckListChecKObservacaoImagem" pageWidth="180" pageHeight="180" columnWidth="180" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select cod_empresa, nome from empresas e where e.cod_empresa in (1,2,3)]]>
		</queryString>
		<field name="COD_EMPRESA" class="java.lang.Double"/>
		<field name="NOME" class="java.lang.String"/>
	</subDataset>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[25356.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[34.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREL_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[WITH consulta as (
    SELECT MP.COD_EMPRESA,
       MP.NUMERO_OS,
       MP.COD_ITEM,
       MG.DESCRICAO || ' | ' || MI.DESCRICAO || ' - ' || MP.OBSERVACAO AS OBSERVACAO,
       MG.DESCRICAO   AS DESCRICAO_GRUPO,
       MI.DESCRICAO   AS DESCRICAO_ITEM,
       MO.DESCRICAO   AS DESCRICAO_OPCAO,
       IC.DESCRICAO   AS DESCRICAO_ICONE,
       IC.ID_ICONE
  FROM MOB_OS_PERTENCE    MP,
       MOB_PERTENCE_ITEM  MI,
       MOB_OPCAO          MO,
       ICONE_PADRAO       IC,
       MOB_PERTENCE_GRUPO MG
 WHERE MP.NUMERO_OS = $P{NUMERO_OS}
   AND MP.COD_EMPRESA = $P{COD_EMPRESA}
   AND MI.COD_ITEM = MP.COD_ITEM
   AND MG.ID_GRUPO = MI.ID_GRUPO
   AND MG.APLICACAO = 'R'
   AND MP.ID_OPCAO = MO.ID_OPCAO
   AND MO.ID_ICONE = IC.ID_ICONE
   AND MP.ID_IMAGEM IS NOT NULL
   AND MP.OBSERVACAO IS NOT NULL
),

PARAMETROS AS (
           SELECT
              5 MULTIPLO, /*Determina o multiplo da quantidade de linhas*/
              0 MAXIMO_LINHAS /* determina o maximo de linhas, zero é ilimitado, o numero de linhas que a consulta retornar*/
              FROM DUAL
),
CONSULTA_LIMITADA as (
     SELECT consulta.* FROM consulta,parametros where (rownum <= parametros.maximo_linhas or parametros.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL as (
    SELECT * FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL, NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL
    FROM dual,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),parametros.multiplo) <> 0 or (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY level <= parametros.multiplo - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), parametros.multiplo)
)
SELECT CONSULTA_FINAL.COD_EMPRESA,
		       CONSULTA_FINAL.NUMERO_OS,
		       CONSULTA_FINAL.COD_ITEM,
		       CONSULTA_FINAL.OBSERVACAO,
		       CONSULTA_FINAL.DESCRICAO_GRUPO,
		       CONSULTA_FINAL.DESCRICAO_ITEM,
		       CONSULTA_FINAL.DESCRICAO_OPCAO,
		       CONSULTA_FINAL.DESCRICAO_ICONE,
				ICONE_PADRAO.ICONE
FROM CONSULTA_FINAL, ICONE_PADRAO
WHERE CONSULTA_FINAL.ID_ICONE = ICONE_PADRAO.ID_ICONE (+)]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="DESCRICAO_ICONE" class="java.lang.String"/>
	<field name="ICONE" class="java.io.InputStream"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="36" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="180" height="36" uuid="98e3a3fc-a687-48e5-a433-1e53d3afce84"/>
				<box leftPadding="2">
					<pen lineWidth="0.3"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="15" y="0" width="163" height="36" forecolor="#000000" uuid="88f8c2b6-6cac-406c-843b-fc62406fd9d3">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="1" leftPadding="4" bottomPadding="2" rightPadding="4">
						<pen lineWidth="0.2"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isUnderline="false"/>
						<paragraph lineSpacing="Proportional"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
				</textField>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="0" y="0" width="15" height="36" uuid="f69d414c-a6c6-4820-b609-23a1a33ed744">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{ICONE}]]></imageExpression>
				</image>
			</frame>
		</band>
	</detail>
</jasperReport>
