<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RequisicoesObservacao" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="5" bottomMargin="5" uuid="b210b23a-3a04-4bce-bdc3-5829df4bd199">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Desenvolvimento"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="REQUISICAO" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT REQ.OBSERVACAO
          FROM OS_REQUISICOES REQ
          WHERE  REQ.COD_EMPRESA = $P{COD_EMPRESA}
              AND REQ.NUMERO_OS = $P{NUMERO_OS}
              AND REQ.REQUISICAO = $P{REQUISICAO}
              AND NOT REQ.OBSERVACAO IS NULL
              AND ROWNUM = 1]]>
	</queryString>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnFooter>
		<band height="62" splitType="Stretch">
			<staticText>
				<reportElement x="1" y="0" width="19" height="14" uuid="d16e5608-646e-4a05-a047-f3a39e1e6475">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$F{OBSERVACAO} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Obs:]]></text>
			</staticText>
			<textField>
				<reportElement x="20" y="1" width="531" height="59" uuid="6ab788b3-bf20-4233-9bed-ed897b863233">
					<printWhenExpression><![CDATA[$F{OBSERVACAO} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Justified" verticalAlignment="Top">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
</jasperReport>
