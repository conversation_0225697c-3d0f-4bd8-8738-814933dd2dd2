<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsGwmAcompanhamentoSubSintoma" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="true"/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[11095.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\31045\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[select decode(nvl(os.diagnostico_cliente_presente,'N'),'N', 'Não', 'Sim') as diagnostico_cliente_presente
       , decode(nvl(os.diagnostico_confirmado_cliente,'N'),'N', 'Não', 'Sim') as diagnostico_confirmado_cliente
       , decode(nvl(os.tipo_teste,'-'), 'E', 'Estático', 'R', 'Rodagem', 'Não Informado') as tipo_teste
       , ods.detalhes_sintoma
       , ods.item
       , ods.cod_tecnico
       , initcap(st.nome) as nome
       , ods.data_inicio
       , ods.hora_inicio
       , ods.data_fim
       , ods.hora_fim
from os, OS_DIAGNOSTICO_SINTOMA_ITEM ODS, servicos_tecnicos st
where os.numero_os = $P{NUMERO_OS}
      and os.cod_empresa = $P{COD_EMPRESA}
      and ods.numero_os = os.numero_os
      and ods.cod_empresa = os.cod_empresa
      and st.cod_tecnico = ods.cod_tecnico
      and st.cod_empresa = ods.cod_empresa]]>
	</queryString>
	<field name="DIAGNOSTICO_CLIENTE_PRESENTE" class="java.lang.String"/>
	<field name="DIAGNOSTICO_CONFIRMADO_CLIENTE" class="java.lang.String"/>
	<field name="TIPO_TESTE" class="java.lang.String"/>
	<field name="DETALHES_SINTOMA" class="java.lang.String"/>
	<field name="ITEM" class="java.math.BigDecimal"/>
	<field name="COD_TECNICO" class="java.math.BigDecimal"/>
	<field name="NOME" class="java.lang.String"/>
	<field name="DATA_INICIO" class="java.sql.Timestamp"/>
	<field name="HORA_INICIO" class="java.lang.String"/>
	<field name="DATA_FIM" class="java.sql.Timestamp"/>
	<field name="HORA_FIM" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="44">
			<frame>
				<reportElement x="0" y="0" width="555" height="44" uuid="59821ec5-9511-4c89-b677-cc1051e00c32">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="140" height="10" uuid="e07c2dfa-59f6-4367-b7d9-302a6c70d613">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Acompanhamento de Serviço]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="10" width="555" height="34" uuid="fb08f8b3-e4b4-42f8-9ddf-1d7575aa3e03"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="555" height="10" uuid="bd44cada-b316-43df-8cb7-67658fd6d27b">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Detalhes da confirmação de Sintoma]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="10" width="100" height="10" uuid="731498cd-7188-40ba-bc5b-1923f2ad2b94">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Cliente estava presente ?]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="100" y="10" width="87" height="10" uuid="082f6028-9458-49b4-834e-a1285a38b144">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Sintoma Confirmado?]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="187" y="10" width="111" height="10" uuid="def94ed5-016f-4f98-abf6-3a0621f25ef6">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Teste Estárico ou Rodagem?]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="100" y="21" width="87" height="13" uuid="7b51b3f3-be87-4881-b3fa-5994ece7128c">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{DIAGNOSTICO_CONFIRMADO_CLIENTE}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="187" y="21" width="111" height="13" uuid="90ac03c5-b3de-4e6f-b70c-4f8073d5d4d8">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{TIPO_TESTE}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="0" y="21" width="100" height="13" uuid="73bfa77f-00ab-4ae0-beb4-a9f57f48dfc7">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{DIAGNOSTICO_CLIENTE_PRESENTE}]]></textFieldExpression>
					</textField>
				</frame>
				<staticText>
					<reportElement mode="Transparent" x="420" y="0" width="55" height="10" uuid="c293a08d-9863-49c2-a837-a549f9d49474">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Número da Os:]]></text>
				</staticText>
				<textField pattern="###0.###;(###0.###-)">
					<reportElement mode="Transparent" x="475" y="0" width="80" height="10" uuid="43765338-f4f0-455c-b3c5-61117400da8d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{NUMERO_OS}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="68" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement key="" x="0" y="0" width="555" height="68" isPrintWhenDetailOverflows="true" uuid="209444c4-2584-47b0-9d73-b61e06668e60">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="10" y="17" width="537" height="10" uuid="3f802381-1242-43e0-bab0-efa29c084d92">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="10" y="27" width="537" height="10" uuid="3a21a7a1-515d-43ee-b805-2d2213008c66">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="10" y="6" width="537" height="10" uuid="90feb51c-c493-41cb-b99f-233c12ce5760">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="10" y="37" width="537" height="10" uuid="75c4ba9f-f5b4-473f-87bc-221a50e9f566">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="12" y="7" width="532" height="39" uuid="e4622cd3-26eb-43cb-b695-999c7f78cd6a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
						<paragraph lineSpacing="Fixed" lineSpacingSize="8.4" firstLineIndent="1" leftIndent="1"/>
					</textElement>
					<textFieldExpression><![CDATA["                                                                                   " + $F{ITEM} +" - " + $F{DETALHES_SINTOMA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="9" y="50" width="181" height="10" uuid="d8ca531a-141f-468d-aa8d-34bc1fdc9128">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável pela confirmação de Sintoma:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="166" y="50" width="94" height="10" uuid="0bebf66f-6d80-4110-94f6-4916d42fd41a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="262" y="50" width="25" height="10" uuid="3f6860fe-5c5a-461f-94ac-22b5c50da9cc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Início:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="287" y="50" width="40" height="10" uuid="9f79c340-a39a-457d-aca5-8e83a3752c65">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{HORA_INICIO}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="352" y="50" width="40" height="10" uuid="ae6095e5-0418-4216-a214-495a5fe63472">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_INICIO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="329" y="50" width="23" height="10" uuid="09d5a2ed-f8f8-4688-86ec-a2e1b5e9418a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="440" y="50" width="40" height="10" uuid="837704c1-e1ca-4f6f-b4a4-eaa660b544ec">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{HORA_FIM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="405" y="50" width="35" height="10" uuid="342e27db-8c52-45f7-a7aa-0f8f63678491">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Término:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="506" y="50" width="40" height="10" uuid="80d024bf-9e42-4825-9590-2c30a47dda93">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_FIM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="483" y="50" width="23" height="10" uuid="20112fca-4d95-40e0-8a46-214166cdcb1a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="14" y="6" width="186" height="10" uuid="988b745b-bf45-4b18-8ae9-690bebd8220e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Registro Detalhes da Confirmação de Sintoma:]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
