<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListGwmPdsSubGrupoOpcaoNaoAplicavel" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="Items" uuid="fce13576-eeb4-444f-a66e-099024b8e559">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
		<parameter name="NUMERO_OS" class="java.lang.Double">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<parameter name="COD_EMPRESA" class="java.lang.Double"/>
		<parameter name="ID_GRUPO" class="java.lang.Double"/>
		<parameter name="LISTA_GRUPOS" class="java.lang.String">
			<defaultValueExpression><![CDATA["3000"]]></defaultValueExpression>
		</parameter>
		<parameter name="ULTIMO_ITEM" class="java.lang.Integer">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<queryString language="SQL">
			<![CDATA[with DADOS as (
select info.cod_empresa as COD_EMPRESA,
       info.cod_produto as COD_PRODUTO,
       info.cod_modelo as COD_MODELO,
       info.tipo_os as TIPO_OS,
       info.cod_segmento as COD_SEGMENTO
  from (select os.cod_empresa,dv.Cod_Produto, dv.cod_modelo, os.tipo as tipo_os, produtos.cod_segmento
          from os, os_dados_veiculos dv, produtos
         where os.numero_os = dv.numero_os
           and os.cod_empresa = dv.cod_empresa
           and os.cod_empresa = $P{COD_EMPRESA}
           and os.numero_os = $P{NUMERO_OS}
           and produtos.cod_produto = dv.cod_produto) info
),
TODOS_ITENS as ( 
SELECT A.ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */ 
       A.COD_ITEM,
       CASE WHEN SUBSTR(A.DESCRICAO, 2,5) LIKE '%-%' THEN TRIM(SUBSTR(A.DESCRICAO, 1, INSTR(A.DESCRICAO, '-') - 1)) ELSE '' END AS ACAO_ITEM,
       CASE WHEN SUBSTR(A.DESCRICAO, 2,5) LIKE '%-%' THEN TRIM(SUBSTR(A.DESCRICAO, INSTR(A.DESCRICAO, '-') + 1)) ELSE A.DESCRICAO END AS DESCRICAO_ITEM,
       NVL(A.observacao,' ') as OBSERVACAO_ITEM,
       A.RESPOSTA_EH_OBSERVACAO,
       D.DESCRICAO AS DESCRICAO_OPCAO,
       B.DESCRICAO DESCRICAO_GRUPO,
       B.ORDEM AS ORDEM_GRUPO ,
       C.OBSERVACAO,
       ROW_NUMBER() Over (order by A.ID_GRUPO, B.ORDEM, A.ORDEM) as LINHA_RELATORIO
FROM MOB_PERTENCE_ITEM A, MOB_PERTENCE_GRUPO B, MOB_OS_PERTENCE C, MOB_OPCAO D
WHERE A.ID_GRUPO = B.ID_GRUPO
 AND B.ATIVO = 'S'
 AND A.COD_ITEM = C.COD_ITEM (+)
 AND B.APLICACAO = 'R'
 AND C.COD_EMPRESA(+) = $P{COD_EMPRESA}
 AND C.NUMERO_OS(+) = $P{NUMERO_OS}
 AND C.ID_OPCAO = D.ID_OPCAO(+)
 AND B.ID_GRUPO in ($P!{LISTA_GRUPOS})
 )
select ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */ 
       COD_ITEM,
       ACAO_ITEM,
       CASE WHEN LENGTH(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM) > 90 then LPAD(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM,88) || '...' ELSE DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM END AS DESCRICAO_ITEM_ABREVIADA,
       DESCRICAO_ITEM,
       OBSERVACAO_ITEM,
       RESPOSTA_EH_OBSERVACAO,
       DESCRICAO_OPCAO,
       DESCRICAO_GRUPO,
       OBSERVACAO,
       ORDEM_GRUPO,
       LINHA_RELATORIO
FROM TODOS_ITENS
WHERE ID_GRUPO = $P{ID_GRUPO}
ORDER BY ID_GRUPO]]>
		</queryString>
		<field name="ID_GRUPO" class="java.lang.Double"/>
		<field name="COD_ITEM" class="java.lang.Double"/>
		<field name="ACAO_ITEM" class="java.lang.String"/>
		<field name="DESCRICAO_ITEM_ABREVIADA" class="java.lang.String"/>
		<field name="DESCRICAO_ITEM" class="java.lang.String"/>
		<field name="OBSERVACAO_ITEM" class="java.lang.String"/>
		<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
		<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
		<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
		<field name="OBSERVACAO" class="java.lang.String"/>
		<field name="ORDEM_GRUPO" class="java.lang.Double"/>
		<field name="LINHA_RELATORIO" class="java.lang.Double"/>
	</subDataset>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[115582.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="OBRIGATORIO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<parameter name="LISTA_GRUPOS" class="java.lang.String">
		<defaultValueExpression><![CDATA["30000,30050,30150,30200,30250"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\35501\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="ID_CHECKLIST" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[select ID_GRUPO,  GRUPO_DESCRICAO, count(ID_GRUPO) as ULTIMO_ITEM 
from TABLE(PKG_CRM_SERVICE_CHECKLIST.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS}, 0, '')) A
WHERE A.ID_CHECKLIST = $P{ID_CHECKLIST}
GROUP BY ID_GRUPO, GRUPO_DESCRICAO
ORDER BY ID_GRUPO]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="GRUPO_DESCRICAO" class="java.lang.String"/>
	<field name="ULTIMO_ITEM" class="java.lang.Double"/>
	<variable name="returnValue" class="java.lang.Double">
		<variableExpression><![CDATA[$V{returnValue}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="12">
			<staticText>
				<reportElement mode="Opaque" x="95" y="0" width="31" height="12" backcolor="#D9D9D9" uuid="72cf83da-4f9b-4941-8235-68e2fd57f6b8">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Ação]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="505" y="0" width="25" height="12" backcolor="#D9D9D9" uuid="dde76569-11de-44a4-95cb-01c760b92b27">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[NG]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="126" y="0" width="354" height="12" backcolor="#D9D9D9" uuid="a903c18a-699f-44bb-8ca8-02ecf0069357">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Item de Inspeção]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="480" y="0" width="25" height="12" backcolor="#D9D9D9" uuid="817a7db3-4e86-4cbb-90e3-725c63be22e6">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[OK]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="95" height="12" backcolor="#D9D9D9" uuid="aee95b12-acb8-4ba7-91ca-f6ff3f65a614">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[#]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="530" y="0" width="25" height="12" backcolor="#D9D9D9" uuid="74ca4562-51f3-460b-a58e-f7861d3f32a4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[NA]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="14">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame borderSplitType="NoBorders">
				<reportElement x="0" y="0" width="555" height="14" isPrintWhenDetailOverflows="true" uuid="9926ff22-7c01-40fc-b4eb-914f43def204">
					<property name="ShowOutOfBoundContent" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport isUsingCache="false">
					<reportElement x="0" y="0" width="554" height="14" uuid="adc84489-96b6-4657-8ff6-55b9514c268d">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="OBRIGATORIO">
						<subreportParameterExpression><![CDATA[$P{OBRIGATORIO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="ID_GRUPO">
						<subreportParameterExpression><![CDATA[$F{ID_GRUPO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="ID_CHECKLIST">
						<subreportParameterExpression><![CDATA[$P{ID_CHECKLIST}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListGwmPdsSubItemOpcaoNaoAplicavel.jasper"]]></subreportExpression>
				</subreport>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="26" y="1" width="68" height="12" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="e2a83d7e-37bb-4ac6-983e-e1aaa8dea0dd">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GRUPO_DESCRICAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
