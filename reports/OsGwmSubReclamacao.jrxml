<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsGwmSubReclamacao" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString>
		<![CDATA[SELECT DISTINCT 
    NVL(OO.ITEM_OS_FABRICA,OO.ITEM) AS ITEM
  , OO.DESCRICAO
  , OO.COD_RECLAMACAO
  , OO.TIPO
  , NVL(OTE.TIPO_FABRICA,OT.TIPO_FABRICA) AS TIPO_FABRICA_EMPRESA
  ,(SELECT ST.NOME 
          FROM OS_TEMPOS_EXECUTADOS OTE
          INNER JOIN SERVICOS_TECNICOS  ST 
            ON (ST.COD_TECNICO = OTE.COD_TECNICO
            AND ST.COD_EMPRESA = OTE.COD_EMPRESA) 
          WHERE OTE.COD_EMPRESA = OSE.COD_EMPRESA
            AND OTE.NUMERO_OS   = OSE.NUMERO_OS
            AND OTE.COD_SERVICO = OSE.COD_SERVICO
            AND ROWNUM <= 1)  
   AS PRODUTIVO_PRISMA
FROM 
    OS_ORIGINAL       OO
  , OS                OS
  , OS_TIPOS_EMPRESAS OTE 
  , OS_TIPOS          OT
  , OS_SERVICOS       OSE
WHERE 1 = 1
  AND (OS.COD_EMPRESA = OO.COD_EMPRESA
   AND OS.NUMERO_OS = OO.NUMERO_OS)
  AND (NVL(OO.TIPO, OS.TIPO) = OTE.TIPO)
  AND (NVL(OO.TIPO, OS.TIPO) = OT.TIPO)
  AND (OS.COD_EMPRESA = OTE.COD_EMPRESA)
  AND (OSE.COD_EMPRESA       (+) = OO.COD_EMPRESA
  AND  OSE.NUMERO_OS     (+) = OO.NUMERO_OS
  AND  OSE.ITEM          (+) = OO.ITEM)   
AND OO.COD_EMPRESA = $P{COD_EMPRESA} 
  AND ((OS.NUMERO_OS      = $P{NUMERO_OS}) OR 
       (OS.NUMERO_OS_FABRICA = $P{NUMERO_OS}))
ORDER BY ITEM]]>
	</queryString>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="COD_RECLAMACAO" class="java.lang.Double"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="TIPO_FABRICA_EMPRESA" class="java.lang.String"/>
	<field name="PRODUTIVO_PRISMA" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="20">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="20" uuid="7b1cf3c2-6d17-4ab8-9730-8c8ed4e1e4b1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="140" height="10" uuid="979e23fb-4d9e-4740-bc6b-b0c5ae34fe5b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Alegações e/ou Solicitações do Cliente]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="42" y="10" width="42" height="10" uuid="7d841074-4cfe-4157-a97e-34f7d7846d04">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo de OS]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="84" y="10" width="471" height="10" uuid="aafdef03-c09f-42df-8cf6-ff807bdb65f9">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Descrição da Solicitação]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="10" width="42" height="10" uuid="bd6539f0-23aa-40a7-a195-75945cb10f30">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Item]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="10" uuid="209444c4-2584-47b0-9d73-b61e06668e60">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement mode="Transparent" x="0" y="0" width="42" height="10" uuid="5e768aa6-316e-4937-a810-a1e10d41e3ac">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="84" y="0" width="471" height="10" uuid="293b0569-4eba-40d9-9927-60e673e3dfd5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="42" y="0" width="42" height="10" uuid="ba123185-e631-4d8f-9b8e-dca14578dc13">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TIPO_FABRICA_EMPRESA}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="1">
			<line>
				<reportElement positionType="Float" x="0" y="0" width="555" height="1" uuid="aa901890-ba5e-458c-9b13-606e0f922ffe">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
			</line>
		</band>
	</columnFooter>
</jasperReport>
