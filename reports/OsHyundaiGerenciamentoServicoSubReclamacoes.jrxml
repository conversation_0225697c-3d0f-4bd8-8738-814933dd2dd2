<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsJaguarLandSubReclamacao" pageWidth="555" pageHeight="123" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="CONFIRMADO_CLIENTE" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[with origem_ficha_cruzamento as (Select OsO.Cod_Empresa, 
      osO.Numero_Os, 
      osO.Descricao as descricao_reclamacao,        
      osR.COD_RECLAMACAO AS ITEM, 
      osR.RESPOSTA,
      ROW_NUMBER() OVER (PARTITION BY osO.ITEM ORDER BY osO.ITEM) AS linha,
      'CRUZAMENTO_FICHA'origem
From Os_Original osO,
     os_respostas_reclamacao osR,
     cruzamento_ficha
Where osO.Cod_Empresa = $P{COD_EMPRESA}
     and osO.Numero_Os = $P{NUMERO_OS}
     and osO.Reclamacao_cliente = 'S'
     AND osr.COD_EMPRESA (+) = osO.cod_empresa
     AND osr.NUMERO_OS (+) = osO.Numero_Os
     and osr.cod_reclamacao (+) = osO.Item
     and cruzamento_ficha.cod_pergunta = osr.cod_pergunta
     and cruzamento_ficha.tipo = 'SRV'), /* tanto faz SRV ou DAG pois vou precisar somente da reclamação  e não importa de onde venha*/
origem_primeira_linha as (
select * from
(Select OsO.Cod_Empresa, 
      osO.Numero_Os, 
      osO.Descricao as descricao_reclamacao,        
      osR.COD_RECLAMACAO AS ITEM, 
      osR.RESPOSTA,
      ROW_NUMBER() OVER (PARTITION BY osO.ITEM ORDER BY osO.ITEM) AS linha,
      'PRIMEIRA_LINHA'origem
From Os_Original osO,
     os_respostas_reclamacao osR
Where osO.Cod_Empresa = $P{COD_EMPRESA}
     and osO.Numero_Os = $P{NUMERO_OS}
     and osO.Reclamacao_cliente = 'S'
     AND osr.COD_EMPRESA (+) = osO.cod_empresa
     AND osr.NUMERO_OS (+) = osO.Numero_Os
     and osr.cod_reclamacao (+) = osO.Item)
     where linha = 1),
     
consulta as (
  select * from origem_ficha_cruzamento
  union all
  select * from origem_primeira_linha
  where not exists(select 1 from origem_ficha_cruzamento)
),

PARAMETROS AS (
           SELECT
              5 MULTIPLO, /*Determina o multiplo da quantidade de linhas*/
              5 MAXIMO_LINHAS /* determina o maximo de linhas, zero é ilimitado, o numero de linhas que a consulta retornar*/
              FROM DUAL
),
CONSULTA_LIMITADA as (
     SELECT consulta.* FROM consulta,parametros where (rownum <= parametros.maximo_linhas or parametros.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL as (
    SELECT * FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL, NULL, NULL, NULL, NULL, NULL, NULL
    FROM dual,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),parametros.multiplo) <> 0 or (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY level <= parametros.multiplo - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), parametros.multiplo)
)
SELECT * FROM CONSULTA_FINAL]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="DESCRICAO_RECLAMACAO" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="RESPOSTA" class="java.lang.String"/>
	<field name="LINHA" class="java.lang.Double"/>
	<field name="ORIGEM" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="28">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="28" forecolor="#000000" backcolor="#08038F" uuid="1ba7efa1-9f35-4397-a178-1172a1c52468">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineColor="#08038F"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement mode="Transparent" x="4" y="8" width="416" height="14" forecolor="#FFFFFF" uuid="33899d9b-2fd1-4657-88a8-aaaefd727b70"/>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Problemas citados pelo cliene durante o agendamento ou entrevista consultiva:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="455" y="1" width="99" height="14" forecolor="#FFFFFF" uuid="ad055eb3-5e02-4a7e-99a1-e2e9833f3246"/>
				<textElement textAlignment="Left">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Confirmado com o cliente]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="468" y="15" width="73" height="12" forecolor="#FFFFFF" uuid="46a91d47-68b5-47a2-ba34-c92dc80b8091"/>
				<textElement textAlignment="Left">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Sim                 Não]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="19" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="19" uuid="4322e92e-9965-400f-8218-5c9254755c62">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<leftPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="20" y="1" width="436" height="16" forecolor="#08038F" uuid="8fb776c7-6ae3-40c9-9f1d-0068e0f04b93">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_RECLAMACAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="3" y="1" width="15" height="16" forecolor="#08038F" uuid="6fce6c7e-3c5d-4782-9e1d-dd2d2c13fa20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="463" y="2" width="73" height="15" uuid="48ecc23c-8426-4fc9-8acf-7a9a4e37911c">
						<printWhenExpression><![CDATA[new Boolean(!$F{DESCRICAO_RECLAMACAO}.equals(""))]]></printWhenExpression>
					</reportElement>
					<image scaleImage="RealHeight">
						<reportElement x="5" y="0" width="15" height="15" uuid="7dff1819-2212-49a4-bdfe-cc69f1574854">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice15404.png"]]></imageExpression>
					</image>
					<image scaleImage="RealHeight">
						<reportElement x="58" y="0" width="15" height="15" uuid="413767b6-a72b-4d29-85e7-3e453d4baafa">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice15404.png"]]></imageExpression>
					</image>
					<image scaleImage="RealHeight">
						<reportElement x="5" y="0" width="15" height="15" uuid="22ccbb29-8586-4a87-9dc0-dd9dcc186cdd">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<printWhenExpression><![CDATA[new Boolean($P{CONFIRMADO_CLIENTE}.equals("S"))]]></printWhenExpression>
						</reportElement>
						<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice15403.png"]]></imageExpression>
					</image>
				</frame>
			</frame>
		</band>
	</detail>
</jasperReport>
