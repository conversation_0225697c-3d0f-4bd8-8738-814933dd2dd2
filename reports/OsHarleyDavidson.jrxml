<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHarleyDavidson" pageWidth="595" pageHeight="842" whenNoDataType="BlankPage" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isFloatColumnFooter="true" whenResourceMissingType="Empty" uuid="871cb0c8-c266-4282-98fa-d77bc2e0a652">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="395"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="591"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<style name="campo_null" isDefault="true" pattern="" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\27369\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT
  
  
  OS.COD_EMPRESA                    AS OS_COD_EMPRESA,
  OS_AGENDA.SIGNATURE AS OS_ASSINATURA,
  OS.STATUS_OS                    AS OS_STATUS_OS,
  OS.COD_OS_AGENDA                  AS OS_COD_OS_AGENDA,
  NVL(OS.NUMERO_OS, 0)                    AS OS_NUMERO_OS, 
  ABS(OS.NUMERO_OS)                  AS OS_ABS_OSNUM,
  OS.NUMERO_OS_FABRICA                AS OS_NUMERO_OS_FABRICA,
  OS.COD_CLIENTE                    AS OS_COD_CLIENTE, 
  OS.CLIENTE_RAPIDO                  AS OS_CLIENTE_RAPIDO,
  OS.TIPO_ENDERECO                  AS OS_TIPO_ENDERECO,
  NVL(OS.OBSERVACAO, ' ')                    AS OS_OBSERVACAO,
  OS.EXTENDIDA                    AS OS_EXTENDIDA,
  OS.SEGURADORA                    AS OS_SEGURADORA,
  NVL(TO_CHAR(OS.DATA_EMISSAO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2)     AS OS_DIA_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2)     AS OS_MES_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4)     AS OS_ANO_EMISSAO,
  OS.HORA_EMISSAO                    AS OS_HORA_EMISSAO,
  OS.HORA_ENCERRADA                  AS OS_HORA_ENCERRADA,
  OS.DATA_ENCERRADA                  AS OS_DATA_ENCERRADA,
  NVL(OS.HORA_PROMETIDA,' ')                  AS OS_HORA_PROMETIDA, 
  SUBSTR(OS.HORA_PROMETIDA, 1, 2)           AS OS_HORA24_PROMETIDA,
  SUBSTR(OS.HORA_PROMETIDA, 4, 2)           AS OS_MINUTO_PROMETIDA,
  NVL(TO_CHAR(OS.DATA_PROMETIDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PROMETIDA, 
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2)     AS OS_DIA_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2)     AS OS_MES_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4)   AS OS_ANO_PROMETIDA,
  NVL(OS.VALOR_SERVICOS_BRUTO, 0)                AS OS_VALOR_SERVICOS_BRUTO,
  NVL(OS.VALOR_ITENS_BRUTO, 0)                AS OS_VALOR_ITENS_BRUTO,
  NVL(OS.DESCONTOS_SERVICOS, 0)                AS OS_DESCONTOS_SERVICOS,
  NVL(OS.DESCONTOS_ITENS, 0)                  AS OS_DESCONTOS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS,0)                           AS OS_TOTAL_OS_SERVICOS,
  NVL(OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS,0)                                 AS OS_TOTAL_OS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO,0)                            AS OS_TOTAL_OS_BRUTO,
  NVL(OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS,0)                                AS OS_TOTAL_OS_DESCONTO,
  NVL((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) + (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS), 0)   AS OS_TOTAL_OS,
  OS.COD_SEGURADORA                  AS OS_COD_SEGURADORA,
  OS.TIPO                        AS OS_TIPO,
  OS.NOME                       AS CONSULTOR,
  OS.OS_ENTRADA                    AS OS_OS_ENTRADA,
  '1ª VIA CLIENTE 1ª VIA CLIENTE 1ª VIA CLIENTE'     AS OS_VIA,
  OS.TOTAL_IMPRESSAO_FABRICA              AS OS_TOTAL_IMPRESSAO_FABRICA,
  
    
  
  NVL(EMPRESAS_USUARIOS.NOME_COMPLETO, ' ')           AS OS_CONSULTOR_COMPLETO,
  
  
  OS_TIPOS.DESCRICAO                         AS OS_TIPO_DESCRICAO,
  OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO              AS OS_TIPO_COM_DESCRICAO,
  OS_TIPOS.GARANTIA                        AS OS_GARANTIA,
  OS_TIPOS.REVISAO_GRATUITA                    AS OS_REVISAO_GRATUITA,
  OS_TIPOS.INTERNO, OS_TIPOS.COD_CLIENTE               AS OS_CLIENTE_DO_TIPO,
  OS_TIPOS.OUTRO_CONCESSIONARIA                  AS OS_OUTRO_CONCESSIONARIA,
  NVL(OS_TIPOS.TIPO_FABRICA_EMPRESA,OS_TIPOS.TIPO_FABRICA)     AS OS_TIPO_FABRICA,
  
  
  NVL(OS_DADOS_VEICULOS.ANO, ' ')                      AS OS_ANO,
  OS_DADOS_VEICULOS.HORIMETRO                    AS OS_HORIMETRO,
  NVL(OS_DADOS_VEICULOS.PRISMA, ' ')                    AS OS_PRISMA,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2)     AS DIA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2)     AS MES_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4)   AS ANO_VENDA,
  NVL(OS_DADOS_VEICULOS.COMBUSTIVEL,0)                  AS OS_COMBUSTIVEL,
  NVL(OS_DADOS_VEICULOS.COR_EXTERNA, ' ')                  AS OS_COR_EXTERNA,
  NVL(OS_DADOS_VEICULOS.PLACA, ' ')                      AS OS_PLACA,
  NVL(OS_DADOS_VEICULOS.KM, 0)                      AS OS_KM,
  NVL(OS_DADOS_VEICULOS.CHASSI, ' ')                    AS OS_CHASSI,
  NVL(OS_DADOS_VEICULOS.NUMERO_MOTOR, ' ')                  AS OS_NUMERO_MOTOR,
  OS_DADOS_VEICULOS.NUMERO_RENAVAM                AS OS_NUMERO_RENAVAM,
  OS_DADOS_VEICULOS.SERIE                      AS OS_SERIE,
  OS_DADOS_VEICULOS.COD_CONCESSIONARIA              AS OS_COD_CONCESSIONARIA,
  OS_DADOS_VEICULOS.ESTADO_PINTURA                AS OS_ESTADO_PINTURA,
  NVL(OS_DADOS_VEICULOS.JOGO_FERRAMENTAS, ' ')                AS OS_JOGO_FERRAMENTAS,
  NVL(OS_DADOS_VEICULOS.ELASTICOS, ' ')                    AS OS_ELASTICOS,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_D, ' ')                AS OS_TAMPA_LATERAL_D,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_E, ' ')                AS OS_TAMPA_LATERAL_E,
  NVL(OS_DADOS_VEICULOS.FLANELA, ' ')                    AS OS_FLANELA,
  OS_DADOS_VEICULOS.NUMERO_FROTA                  AS OS_NUMERO_FROTA,
  
  
  NVL(CONCESSIONARIAS.NOME, ' ')                          AS CONCESSIONARIA_NOME,
  CONCESSIONARIAS.UF                            AS CONCESSIONARIA_UF,
  CONCESSIONARIAS.CIDADE                        AS CONCESSIONARIA_CIDADE,
  CONCESSIONARIAS.BAIRRO                        AS CONCESSIONARIA_BAIRRO,
  CONCESSIONARIAS.ENDERECO                      AS CONCESSIONARIA_RUA,
  CONCESSIONARIAS.CEP                           AS CONCESSIONARIA_CEP,
  CONCESSIONARIAS.CODIGO_PADRAO                   AS CONCESSIONARIA_CODIGO,
  UF_CONCESSIONARIA.DESCRICAO                   AS CONCESSIONARIA_ESTADO,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_FAB_BATERIA, 'DD/MM/YYYY'),' ')                AS OS_DATA_FAB_BATERIA,
  NVL(OS_DADOS_VEICULOS.COD_FAB_BATERIA, ' ')                               AS OS_COD_FAB_BATERIA,
  
  
  
  PRODUTOS.DESCRICAO_PRODUTO  AS OS_DESCRICAO_PRODUTO,
  
  
  NVL(PRODUTOS.DESCRICAO_PRODUTO || ' / ' || PRODUTOS_MODELOS.DESCRICAO_MODELO,' ')   AS DESC_PROD_MOD,
  PRODUTOS_MODELOS.DESCRICAO_MODELO                      AS OS_DESCRICAO_MODELO,
  PRODUTOS_MODELOS.MOD_VER_SERIE                        AS OS_MOD_VER_SERIE,
  PRODUTOS_MODELOS.LINHA                            AS OS_LINHA,
  
  
  MARCAS.DESCRICAO_MARCA  AS OS_DESCRICAO_MARCA,
  
  
  CO.TEXTO_AIDF  AS OS_TEXTO_AIDF,
  
  
  NVL(EMPRESAS.NOME,' ')           AS NOME_EMPRESA,
  NVL(EMPRESAS.CGC, ' ')                     AS EMPRESAS_CGC,
  EMPRESAS.FACHADA                  AS EMPRESAS_FACHADA,
  EMPRESAS.ESTADO                   AS UF_EMPRESA,
  NVL(EMPRESAS.CIDADE, ' ')                    AS EMPRESAS_CIDADE,
  EMPRESAS.BAIRRO                    AS EMPRESAS_BAIRRO,
  EMPRESAS.COMPLEMENTO                AS EMPRESAS_COMPLEMENTO,
  NVL(EMPRESAS.RUA, ' ')              AS EMPRESAS_RUA,
  NVL(EMPRESAS.FONE, ' ')                    AS EMPRESAS_FONE,
  NVL(EMPRESAS.FAX, ' ')                    AS EMPRESAS_FAX,
  NVL(EMPRESAS.CEP, ' ')                    AS EMPRESAS_CEP,
  EMPRESAS.INSCRICAO_MUNICIPAL            AS EMPRESAS_INSCRICAO_MUNICIPAL,
  EMPRESAS.INSCRICAO_SUBSTITUICAO            AS EMPRESAS_INSC_SUBSTITUICAO,
  NVL(UF_EMPRESA.DESCRICAO, ' ')                  AS EMPRESA_ESTADO,
  NVL(EMPRESAS.INSCRICAO_ESTADUAL, ' ')              AS EMPRESAS_INSCRICAO_ESTADUAL,
  TRUNC(SYSDATE)                     AS EMPRESA_DATA_ATUAL,
  SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'),1, 5)       AS EMPRESA_HORA_ATUAL_STR,
  NVL(CLIENTE_EMPRESA.ENDERECO_ELETRONICO, ' ')         AS EMPRESA_EMAIL,
  
  
  
  
  CLIENTE_DIVERSO.COD_CLIENTE    AS CLIENTE_COD_CLIENTE,
  NVL(CLIENTE_DIVERSO.NOME, ' ')        AS CLIENTE_NOME,
  CLIENTE_DIVERSO.RG          AS CLIENTE_RG,
  CLIENTES.TELEFONE_RES      AS CLIENTE_TELEFONE_RES,
  CLIENTES.PREFIXO_RES      AS CLIENTE_PREFIXO_RES,
  CLIENTES.TELEFONE_COM      AS CLIENTE_TELEFONE_COM,
  CLIENTES.PREFIXO_COM      AS CLIENTE_PREFIXO_COM,
  CLIENTES.TELEFONE_FAX      AS CLIENTE_TELEFONE_FAX,
  CLIENTES.PREFIXO_FAX      AS CLIENTE_PREFIXO_FAX,
  
  NVL(TRIM('-' FROM CLIENTES.TELEFONE_CEL || '-' || CLIENTES.PREFIXO_CEL),' ') AS CLIENTE_CELULAR,
  
  
  CLIENTES.ENDERECO_ELETRONICO  AS CLIENTE_ENDERECO_ELETRONICO,
  NVL(NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL),' ') AS CLIENTE_INSC_ESTAD,
  
  NVL(DECODE(CLIENTE_DIVERSO.CPF,' ','CGC: ' || NVL(CLIENTE_DIVERSO.CGC,' '),'CPF: ' || CLIENTE_DIVERSO.CPF), ' ') AS CLIENTE_CGC_CPF,
  
  CLIENTE_DIVERSO.CGC        AS CLIENTE_CGC,
  CLIENTE_DIVERSO.CPF        AS CLIENTE_CPF,
  CLIENTES.COD_CLASSE        AS CLIENTE_COD_CLASSE,
  
  CASE OS.TIPO_ENDERECO
		WHEN '1' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.ENDERECO  || ', ' || CLIENTE_DIVERSO.COMPLEMENTO || ' ' || CLIENTE_DIVERSO.BAIRRO))
		WHEN '2' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_RES  || ', ' ||CLIENTES.COMPLEMENTO_RES || ' ' || CLIENTES.BAIRRO_RES))
		WHEN '3' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COM  || ', ' ||CLIENTES.COMPLEMENTO_COM || ' ' || CLIENTES.BAIRRO_COM))
		WHEN '4' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COBRANCA  || ', ' ||CLIENTES.COMPLEMENTO_COBRANCA || ' ' || CLIENTES.BAIRRO_COBRANCA))
		WHEN '5' THEN TRIM(',' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.RUA  || ', ' ||ENDERECO_POR_INSCRICAO.COMPLEMENTO || ' ' || ENDERECO_POR_INSCRICAO.BAIRRO))
		ELSE ' '
		END  AS CLIENTE_ENDERECO_COMPLETO,
		

  CASE OS.TIPO_ENDERECO
		WHEN '1' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO))
		WHEN '2' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_RES  || '-' || CLIENTES.TELEFONE_RES))
		WHEN '3' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
		WHEN '4' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
		WHEN '5' THEN TRIM('-' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO  || '-' || ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO))
		ELSE ' '
		END  AS CLIENTE_TELEFONE_COMPLETO,
		

  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.UF,
              2, CLIENTES.UF_RES,
              3, CLIENTES.UF_COM,
              4, CLIENTES.UF_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.UF,
               ' ')                          AS  CLIENTE_UF,
  DECODE(OS.TIPO_ENDERECO, 1, UF_DIVERSO.DESCRICAO,
              2, UF_RES.DESCRICAO,
              3, UF_COM.DESCRICAO,
              4, UF_COBRANCA.DESCRICAO,
              5, UF_INSCRICAO.DESCRICAO,
               ' ')                          AS  CLIENTE_ESTADO,
  DECODE(OS.TIPO_ENDERECO, 1, CIDADES_DIV.DESCRICAO,
              2, CIDADES_RES.DESCRICAO,
              3, CIDADES_COM.DESCRICAO,
              4, CIDADES_COBRANCA.DESCRICAO,
              5, ENDERECO_POR_INSCRICAO.CIDADE,
               ' ')                          AS  CLIENTE_CIDADE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.BAIRRO,
              2, CLIENTES.BAIRRO_RES,
              3, CLIENTES.BAIRRO_COM,
              4, CLIENTES.BAIRRO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.BAIRRO,
               ' ')                          AS  CLIENTE_BAIRRO,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.CEP,
              2, CLIENTES.CEP_RES,
              3, CLIENTES.CEP_COM,
              4, CLIENTES.CEP_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.CEP,
               ' ')                          AS  CLIENTE_CEP,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.ENDERECO,
              2, CLIENTES.RUA_RES,
              3, CLIENTES.RUA_COM,
              4, CLIENTES.RUA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.RUA,
               ' ')                          AS  CLIENTE_RUA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.COMPLEMENTO,
              2, CLIENTES.COMPLEMENTO_RES,
              3, CLIENTES.COMPLEMENTO_COM,
              4, CLIENTES.COMPLEMENTO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.COMPLEMENTO,
               ' ')                          AS  CLIENTE_COMPLEMENTO,
  DECODE(OS.TIPO_ENDERECO, 1, ' ',
              2, CLIENTES.FACHADA_RES,
              3, CLIENTES.FACHADA_COM,
              4, CLIENTES.FACHADA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.FACHADA,
               ' ')                          AS  CLIENTE_FACHADA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.FONE_CONTATO,
              2, CLIENTES.TELEFONE_RES,
              3, CLIENTES.TELEFONE_COM,
              4, CLIENTES.TELEFONE_COM,
              5, ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_FONE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
              2, CLIENTES.PREFIXO_RES,
              3, CLIENTES.PREFIXO_COM,
              4, CLIENTES.PREFIXO_COM,
              5, ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_PREFIXO,
  
  
  
  NVL(FATURAR_CLIENTE_DIVERSO.NOME, ' ')                   AS FATURAR_NOME,
  
  NVL(TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO)),' ') AS FATURAR_FONE,
  
  NVL(UF_FATURAR.DESCRICAO, ' ')                       AS FATURAR_ESTADO,
  NVL(FATURAR_CIDADE.DESCRICAO, FATURAR_CLIENTE_DIVERSO.CIDADE)   AS FATURAR_CIDADE,

  NVL(TRIM(SUBSTR(CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.ENDERECO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.ENDERECO ELSE '' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.COMPLEMENTO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.COMPLEMENTO ELSE ' ' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.BAIRRO)>0 THEN '  ' || FATURAR_CLIENTE_DIVERSO.BAIRRO ELSE '' END
    , 2)), ' ')  AS FATURAR_ENDERECO,
   
   NVL(GREATEST(
    FATURAR_CLIENTE.PREFIXO_RES || '-' || FATURAR_CLIENTE.TELEFONE_RES,
    FATURAR_CLIENTE.PREFIXO_COM || '-' || FATURAR_CLIENTE.TELEFONE_COM,
    FATURAR_CLIENTE.PREFIXO_FAX || '-' || FATURAR_CLIENTE.TELEFONE_FAX
   ), ' ')   AS FATURAR_TELEFONE_FAX,
   
   
   

  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_TERCEIROS 
    ELSE 
    TOTAIS_SERVICOS.VAL_TERCEIROS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_TERCEIROS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_TERCEIROS,
      
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_LAVAGEM
    ELSE 
    TOTAIS_SERVICOS.VAL_LAVAGEM - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_LAVAGEM/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_LAVAGEM,
  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_REVISAO
    ELSE 
    TOTAIS_SERVICOS.VAL_REVISAO - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_REVISAO/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_REVISAO,
    
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_MECANICA
    ELSE 
    TOTAIS_SERVICOS.VAL_MECANICA - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_MECANICA/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_MECANICA,

  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_GERAIS
    ELSE 
    TOTAIS_SERVICOS.VAL_GERAIS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_GERAIS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_GERAIS,
   
  
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_PECAS
    ELSE 
    TOTAL_PECAS.VALOR_PECAS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_PECAS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_PECAS,
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_ACESSORIOS
    ELSE 
    TOTAL_PECAS.VALOR_ACESSORIOS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_ACESSORIOS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_ACESSORIOS, 
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_OUTROS
    ELSE 
    TOTAL_PECAS.VALOR_OUTROS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_OUTROS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_OUTROS, 

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_LUBRIFICANTE
    ELSE 
    TOTAL_PECAS.VALOR_LUBRIFICANTE - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_LUBRIFICANTE/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_LUBRIFICANTE,  

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_COMBUSTIVEL
    ELSE 
    TOTAL_PECAS.VALOR_COMBUSTIVEL - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_COMBUSTIVEL/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_COMBUSTIVEL
    
 
FROM
  
  EMPRESAS,
  UF UF_EMPRESA,
  CLIENTES CLIENTE_EMPRESA,
  
  
  
  OS, OS_AGENDA,EMPRESAS_USUARIOS, VW_OS_TIPOS OS_TIPOS, OS_DADOS_VEICULOS,
  CONCESSIONARIAS,UF UF_CONCESSIONARIA, PRODUTOS, PRODUTOS_MODELOS, MARCAS,
  CONTROLE_OS CO, PARM_SYS,
  
  
  CLIENTE_DIVERSO, CLIENTES,  ENDERECO_POR_INSCRICAO,
  CIDADES Cidades_Res, CIDADES Cidades_Com, CIDADES Cidades_Cobranca, cidades Cidades_Div,
  UF UF_Diverso, UF UF_Res, UF UF_Com, UF UF_Cobranca, UF UF_Inscricao,
  
  
  CLIENTE_DIVERSO FATURAR_CLIENTE_DIVERSO, CLIENTES FATURAR_CLIENTE, CIDADES FATURAR_CIDADE, 
   UF UF_FATURAR,
   
   
  (SELECT
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'T', TOTAIS.PRECO, 0)) AS VAL_TERCEIROS,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'L', TOTAIS.PRECO, 0)) AS VAL_LAVAGEM,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'R', TOTAIS.PRECO, 0)) AS VAL_REVISAO,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'M', TOTAIS.PRECO, 0)) AS VAL_MECANICA,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'G', TOTAIS.PRECO, 0)) AS VAL_GERAIS
  FROM
  (SELECT
  DECODE(S.TERCEIROS, 'S',       'T',
    DECODE(S.LAVAGEM, 'S',       'L',
    DECODE(SS.TIPO_SETOR, 'R', 'R',
                'M', 'M',
                   'G'))) AS TIPO_SERVICO,
  OSS.PRECO_VENDA + NVL((SELECT SUM(PRECO_VENDA) FROM OS_SERVICOS_ADICIONAIS ADI
               WHERE ADI.COD_EMPRESA = OSS.COD_EMPRESA
                 AND ADI.NUMERO_OS   = OSS.NUMERO_OS
                 AND ADI.ITEM        = OSS.ITEM
                 AND ADI.COD_SERVICO = OSS.COD_SERVICO), 0) AS PRECO
  FROM OS_SERVICOS OSS, SERVICOS S, SERVICOS_SETORES SS
  WHERE 1=1
  AND OSS.COD_EMPRESA = $P{COD_EMPRESA} 
  AND OSS.NUMERO_OS = $P{NUMERO_OS} 
  AND OSS.COD_SERVICO = S.COD_SERVICO
  AND S.COD_SETOR = SS.COD_SETOR) TOTAIS ) TOTAIS_SERVICOS,
  
  
  
  (SELECT
  SUM(DECODE(CLASSE_PECA, 1, VALOR, 0)) AS VALOR_PECAS,
  SUM(DECODE(CLASSE_PECA, 2, VALOR, 0)) AS VALOR_ACESSORIOS,
  SUM(DECODE(NVL(CLASSE_PECA, 3), 3, VALOR, 0)) AS VALOR_OUTROS,
  SUM(DECODE(CLASSE_PECA, 4, VALOR, 0)) AS VALOR_LUBRIFICANTE,
  SUM(DECODE(CLASSE_PECA, 5, VALOR, 0)) AS VALOR_COMBUSTIVEL
  FROM (
  SELECT DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1), 
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4)) AS CLASSE_PECA,
  SUM(
  OS_REQUISICOES.QUANTIDADE *
  DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
        ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                    DECODE(ITENS.COD_TRIBUTACAO, '1',
                    DECODE(PARM_SYS.REGIME_ICMS, 'S',
                      DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                      DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                    0),
                         0),
                         OS_TIPOS.AUMENTO_PRECO_PECA),
                       0),
                     OS_TIPOS.AUMENTO_PRECO_PECA)) *
           DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                            'G', OS_REQUISICOES.PRECO_GARANTIA,
                            'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                            'P', OS_REQUISICOES.PRECO_FABRICA,
                            DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
          ) / 100,
     DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                        OS_REQUISICOES.PRECO_GARANTIA),
       DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
       DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
         ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
        DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                            OS_REQUISICOES.PRECO_VENDA))/100))))))) AS VALOR
  FROM OS_REQUISICOES, ITENS, ITENS_FORNECEDOR, OS, VW_OS_TIPOS OS_TIPOS, ITENS_CLASSE_CONTABIL ICC , SEGURADORA,
  PARM_SYS, PARM_SYS2, ITENS_GRUPO_INTERNO IGI, OS_TIPOS_EMPRESAS OTE
  WHERE OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
  AND OS.NUMERO_OS = OS_REQUISICOES.NUMERO_OS  
  AND OS.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA 
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS.TIPO = OS_TIPOS.TIPO
  AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND ITENS.COD_GRUPO_INTERNO = IGI.COD_GRUPO_INTERNO(+)
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
  AND OS.COD_EMPRESA = $P{COD_EMPRESA} 
  AND OS.NUMERO_OS = $P{NUMERO_OS} 
  GROUP BY DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4))
  ) TOTAL) TOTAL_PECAS


WHERE   1 = 1
    
    
    AND OS.COD_EMPRESA = $P{COD_EMPRESA}  
    AND OS.NUMERO_OS = $P{NUMERO_OS}

	AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
    AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS (+)
	
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'

    AND OS.NOME = EMPRESAS_USUARIOS.NOME
  
  AND OS.COD_EMPRESA = PARM_SYS.COD_EMPRESA
    
    AND OS.TIPO = OS_TIPOS.TIPO
    AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA (+)
    
    AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS (+)
    AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA (+)
    
    AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA = CONCESSIONARIAS.COD_CONCESSIONARIA (+)
    
    
    AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF (+)
    
    AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO 
    
    AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
    
    AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
    AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
    
    AND (OS.COD_EMPRESA = CO.COD_EMPRESA(+))
    
    
    AND OS.COD_EMPRESA = EMPRESAS.COD_EMPRESA
    AND EMPRESAS.ESTADO = UF_EMPRESA.UF (+)
    AND EMPRESAS.COD_CLIENTE = CLIENTE_EMPRESA.COD_CLIENTE (+)
    
    
    AND OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES (+)
    AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF (+)
    AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES (+)
    AND OS.INSCRICAO_ESTADUAL = ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL (+)
    AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE (+)
    AND CLIENTES.UF_RES = UF_RES.UF (+)
    AND CLIENTES.UF_COM = UF_COM.UF (+)
    AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF (+)
    AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF (+)
    
    
    AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_CLIENTE.COD_CLIENTE (+)
    AND FATURAR_CLIENTE_DIVERSO.COD_CIDADES =FATURAR_CIDADE.COD_CIDADES (+)
    AND FATURAR_CLIENTE_DIVERSO.UF = UF_FATURAR.UF (+)
    AND OS.COD_CLIENTE = FATURAR_CLIENTE_DIVERSO.COD_CLIENTE (+)]]>
	</queryString>
	<field name="OS_COD_EMPRESA" class="java.lang.Double"/>
	<field name="OS_ASSINATURA" class="java.awt.Image"/>
	<field name="OS_STATUS_OS" class="java.lang.Double"/>
	<field name="OS_COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="OS_ABS_OSNUM" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS_FABRICA" class="java.lang.Double"/>
	<field name="OS_COD_CLIENTE" class="java.lang.Double"/>
	<field name="OS_CLIENTE_RAPIDO" class="java.lang.String"/>
	<field name="OS_TIPO_ENDERECO" class="java.lang.String"/>
	<field name="OS_OBSERVACAO" class="java.lang.String"/>
	<field name="OS_EXTENDIDA" class="java.lang.String"/>
	<field name="OS_SEGURADORA" class="java.lang.String"/>
	<field name="OS_DATA_EMISSAO" class="java.lang.String"/>
	<field name="OS_DIA_EMISSAO" class="java.lang.String"/>
	<field name="OS_MES_EMISSAO" class="java.lang.String"/>
	<field name="OS_ANO_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_ENCERRADA" class="java.lang.String"/>
	<field name="OS_DATA_ENCERRADA" class="java.sql.Timestamp"/>
	<field name="OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_HORA24_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MINUTO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DATA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DIA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MES_PROMETIDA" class="java.lang.String"/>
	<field name="OS_ANO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_BRUTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_DESCONTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="OS_COD_SEGURADORA" class="java.lang.Double"/>
	<field name="OS_TIPO" class="java.lang.String"/>
	<field name="CONSULTOR" class="java.lang.String"/>
	<field name="OS_OS_ENTRADA" class="java.lang.Double"/>
	<field name="OS_VIA" class="java.lang.String"/>
	<field name="OS_TOTAL_IMPRESSAO_FABRICA" class="java.lang.Double"/>
	<field name="OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="OS_TIPO_DESCRICAO" class="java.lang.String"/>
	<field name="OS_TIPO_COM_DESCRICAO" class="java.lang.String"/>
	<field name="OS_GARANTIA" class="java.lang.String"/>
	<field name="OS_REVISAO_GRATUITA" class="java.lang.String"/>
	<field name="INTERNO" class="java.lang.String"/>
	<field name="OS_CLIENTE_DO_TIPO" class="java.lang.Double"/>
	<field name="OS_OUTRO_CONCESSIONARIA" class="java.lang.String"/>
	<field name="OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="OS_ANO" class="java.lang.String"/>
	<field name="OS_HORIMETRO" class="java.lang.Double"/>
	<field name="OS_PRISMA" class="java.lang.String"/>
	<field name="OS_DATA_VENDA" class="java.lang.String"/>
	<field name="DIA_VENDA" class="java.lang.String"/>
	<field name="MES_VENDA" class="java.lang.String"/>
	<field name="ANO_VENDA" class="java.lang.String"/>
	<field name="OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="OS_PLACA" class="java.lang.String"/>
	<field name="OS_KM" class="java.lang.Double"/>
	<field name="OS_CHASSI" class="java.lang.String"/>
	<field name="OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="OS_NUMERO_RENAVAM" class="java.lang.String"/>
	<field name="OS_SERIE" class="java.lang.String"/>
	<field name="OS_COD_CONCESSIONARIA" class="java.lang.Double"/>
	<field name="OS_ESTADO_PINTURA" class="java.lang.String"/>
	<field name="OS_JOGO_FERRAMENTAS" class="java.lang.String"/>
	<field name="OS_ELASTICOS" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_D" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_E" class="java.lang.String"/>
	<field name="OS_FLANELA" class="java.lang.String"/>
	<field name="OS_NUMERO_FROTA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="CONCESSIONARIA_UF" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CIDADE" class="java.lang.String"/>
	<field name="CONCESSIONARIA_BAIRRO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_RUA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CEP" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CODIGO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_ESTADO" class="java.lang.String"/>
	<field name="OS_DATA_FAB_BATERIA" class="java.lang.String"/>
	<field name="OS_COD_FAB_BATERIA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRODUTO" class="java.lang.String"/>
	<field name="DESC_PROD_MOD" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="OS_MOD_VER_SERIE" class="java.lang.String"/>
	<field name="OS_LINHA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MARCA" class="java.lang.String"/>
	<field name="OS_TEXTO_AIDF" class="java.lang.String"/>
	<field name="NOME_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_CGC" class="java.lang.String"/>
	<field name="EMPRESAS_FACHADA" class="java.lang.String"/>
	<field name="UF_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_CIDADE" class="java.lang.String"/>
	<field name="EMPRESAS_BAIRRO" class="java.lang.String"/>
	<field name="EMPRESAS_COMPLEMENTO" class="java.lang.String"/>
	<field name="EMPRESAS_RUA" class="java.lang.String"/>
	<field name="EMPRESAS_FONE" class="java.lang.String"/>
	<field name="EMPRESAS_FAX" class="java.lang.String"/>
	<field name="EMPRESAS_CEP" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_MUNICIPAL" class="java.lang.String"/>
	<field name="EMPRESAS_INSC_SUBSTITUICAO" class="java.lang.String"/>
	<field name="EMPRESA_ESTADO" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="EMPRESA_DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="EMPRESA_HORA_ATUAL_STR" class="java.lang.String"/>
	<field name="EMPRESA_EMAIL" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLIENTE" class="java.lang.Double"/>
	<field name="CLIENTE_NOME" class="java.lang.String"/>
	<field name="CLIENTE_RG" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_RES" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO_RES" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_COM" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO_COM" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_FAX" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO_FAX" class="java.lang.String"/>
	<field name="CLIENTE_CELULAR" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="CLIENTE_INSC_ESTAD" class="java.lang.String"/>
	<field name="CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="CLIENTE_CGC" class="java.lang.String"/>
	<field name="CLIENTE_CPF" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLASSE" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_UF" class="java.lang.String"/>
	<field name="CLIENTE_ESTADO" class="java.lang.String"/>
	<field name="CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="CLIENTE_CEP" class="java.lang.String"/>
	<field name="CLIENTE_RUA" class="java.lang.String"/>
	<field name="CLIENTE_COMPLEMENTO" class="java.lang.String"/>
	<field name="CLIENTE_FACHADA" class="java.lang.String"/>
	<field name="CLIENTE_FONE" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO" class="java.lang.String"/>
	<field name="FATURAR_NOME" class="java.lang.String"/>
	<field name="FATURAR_FONE" class="java.lang.String"/>
	<field name="FATURAR_ESTADO" class="java.lang.String"/>
	<field name="FATURAR_CIDADE" class="java.lang.String"/>
	<field name="FATURAR_ENDERECO" class="java.lang.String"/>
	<field name="FATURAR_TELEFONE_FAX" class="java.lang.String"/>
	<field name="TOT_SERVICOS_VAL_TERCEIROS" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_LAVAGEM" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_REVISAO" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_MECANICA" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_GERAIS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_PECAS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_ACESSORIOS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_OUTROS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_LUBRIFICANTE" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_COMBUSTIVEL" class="java.lang.Double"/>
	<variable name="IMAGE_NAME" class="java.lang.String">
		<initialValueExpression><![CDATA["1-4"]]></initialValueExpression>
	</variable>
	<variable name="PAGINA_ATUAL" class="java.lang.Integer" resetType="None" calculation="Count">
		<variableExpression><![CDATA[$V{PAGE_COUNT}]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="157" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="554" height="78" uuid="5c046093-212e-439b-b9dd-797dfa957178">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement x="337" y="60" width="213" height="14" uuid="0a1a54e5-ee4a-4fd4-99bf-af4843f318d5"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<image scaleImage="RealSize">
					<reportElement x="23" y="3" width="98" height="71" uuid="1a87800e-4272-4af6-af52-ea37e15d6fde">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600473.png"]]></imageExpression>
				</image>
			</frame>
			<frame>
				<reportElement x="0" y="78" width="260" height="79" isPrintWhenDetailOverflows="true" uuid="9ffc8a44-f6bf-4677-8c4e-510ef9a06821">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="3" y="1" width="96" height="11" uuid="888e8b14-596d-49cc-a097-0ea180b20d9f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Nome da Concessionaria]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="12" width="255" height="11" uuid="81e7608a-0bfc-4ea9-88c6-5ec7670dfc72"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="23" width="255" height="11" uuid="dd00bc8b-f96b-4a8c-b5af-36fb6ee79d9e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_RUA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="34" width="23" height="11" uuid="068e198c-bccf-4232-8091-b17e1b11b686">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="45" width="23" height="11" uuid="20e034c4-cf5b-47b3-a4bf-372c52fff036">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tel:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="56" width="33" height="11" uuid="a058bf29-5bda-4382-84df-577a5683ee14">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="67" width="33" height="11" uuid="b8d7d261-e1a6-4f1f-b40e-e1af4c52f59a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[E-Mail:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="34" width="70" height="11" uuid="03fdc31c-a5b4-43b2-a09d-32cf0d650973">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="96" y="34" width="162" height="11" uuid="ee4a37fd-e050-4bda-b5c5-4d8a81ba69e6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_CIDADE} + " - " + $F{UF_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="26" y="45" width="113" height="11" uuid="34cc7fdb-bc53-40ec-bb06-b877fc92b811">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_FONE}.length() > 6 ?  $F{EMPRESAS_FONE}: " "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="139" y="45" width="23" height="11" uuid="908ac912-3a69-4bbf-a5aa-8666e3d7723d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Fax:]]></text>
				</staticText>
				<textField>
					<reportElement x="162" y="45" width="96" height="11" uuid="891a36e2-4a52-4bc8-a624-d3f2de629ab3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_FAX}.length() > 6 ?  $F{EMPRESAS_FAX}: " "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="138" y="56" width="48" height="11" uuid="29fde5bd-637c-443e-a373-ce8c4a0cf45e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Insc.Estad.:]]></text>
				</staticText>
				<textField>
					<reportElement x="36" y="56" width="102" height="11" uuid="edf00bba-70bd-4906-aa06-005a3a444490">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_CGC}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="186" y="56" width="72" height="11" uuid="99e455f1-ca1b-4475-9676-07b75b2f46ce">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_INSCRICAO_ESTADUAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="36" y="67" width="222" height="11" uuid="203be4fe-9faf-4f92-9656-07768a029ce5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESA_EMAIL}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="260" y="78" width="294" height="79" isPrintWhenDetailOverflows="true" uuid="8e664189-45ee-4efb-adfe-973a2b717162">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="89" y="2" width="138" height="11" uuid="6ddfc8e1-44e7-4a47-bcbc-9f87fb139122">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[ORDEM DE SERVIÇO]]></text>
				</staticText>
				<staticText>
					<reportElement x="121" y="13" width="20" height="11" uuid="d7f4c2fc-a11e-459f-b94a-986c30376fbd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº]]></text>
				</staticText>
				<textField>
					<reportElement x="140" y="13" width="70" height="11" uuid="6e7f246c-6bf7-404b-9edb-0698f5232c17">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="23" width="82" height="11" uuid="710c2516-1bc2-45ba-a6e0-696f655468bb">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Condição Pagamento:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="34" width="53" height="11" uuid="47c0d7c7-d915-43ad-9009-51df254724ea">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Data Emissão:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="45" width="133" height="11" uuid="e06cbb65-6ba9-40f1-a05c-10620bec78db">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Orçamento Previo a ser Emitido em:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="56" width="82" height="11" uuid="e2315887-e027-4c05-91cf-38a56b6099ce">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Previsão de Entrega:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="67" width="53" height="11" uuid="5061e896-a563-4ff8-8f94-877ed6d6cec9">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Nº Doc. Fiscal:]]></text>
				</staticText>
				<textField>
					<reportElement x="56" y="67" width="120" height="10" uuid="bd713de3-5e50-4888-a80c-bae4eaa2da8f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="85" y="56" width="195" height="11" uuid="bca017f8-86df-4ab1-9ee5-5f30f0e76b7b">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_PROMETIDA} +" "+$F{OS_HORA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="56" y="34" width="70" height="11" uuid="684123e8-80df-4a2c-9d42-daa8ff7dc56f"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="176" y="67" width="46" height="11" uuid="daf6b257-5da1-405f-8658-0c702116f2ee">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº do Doc.]]></text>
				</staticText>
				<textField>
					<reportElement x="222" y="67" width="58" height="11" uuid="67dfad6b-c7a0-4510-9ff3-9dcf3bb07e5c"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="126" y="34" width="35" height="11" uuid="299c4e10-c7ee-494d-94ce-dc648a3f9683">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Validade:]]></text>
				</staticText>
				<staticText>
					<reportElement x="223" y="34" width="20" height="11" uuid="774a0f1d-3905-40af-9fc5-3d9a9eae9145">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Dias:]]></text>
				</staticText>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="440" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement x="0" y="0" width="306" height="70" isPrintWhenDetailOverflows="true" uuid="16ffd29a-19a0-46fa-b61a-70ddf79d93ee">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="3" y="1" width="23" height="11" uuid="8490dc75-8dac-4535-a469-f59bb3f82478">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="1" width="270" height="11" uuid="d76e24a7-c4dd-41a1-ac5d-17b728a1e52a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="12" width="23" height="11" uuid="279cad57-5141-4b77-b7d0-3cfbd7124e36">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[End.:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="12" width="230" height="11" uuid="70352056-dc12-447d-9055-23a28ce3a8dd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_COMPLETO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="23" width="23" height="11" uuid="b4f1c887-8fc5-4226-aa11-5874f3eeb8d0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="23" width="70" height="11" uuid="38a9fcdd-0dbc-4c9d-bba2-7a09311171bb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="34" width="167" height="11" uuid="374be4e7-6352-4cbc-a584-3b826802e6e8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CGC_CPF}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="45" width="40" height="11" uuid="31feb460-f87b-41ad-9972-cdc16fb4d1dd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tel (Res):]]></text>
				</staticText>
				<textField>
					<reportElement x="43" y="45" width="128" height="11" uuid="ef414931-1b17-401a-9659-12978b1f4bcf">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_TELEFONE_COMPLETO}.length() > 6 ?  $F{CLIENTE_TELEFONE_COMPLETO} : " "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="56" width="23" height="11" uuid="f2f3f45e-43a5-4743-a909-677c46cba2fe">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Email:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="56" width="270" height="11" uuid="0e81a4de-f05b-405f-9341-9e3fb2a17ae8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="256" y="12" width="40" height="11" uuid="9746b252-0bc8-46ac-90c6-4c039d8985fd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FACHADA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="96" y="23" width="30" height="11" uuid="d52225a6-d980-49c3-9dca-cac3bf3775a2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<textField>
					<reportElement x="126" y="23" width="70" height="11" uuid="d444d812-9e46-4c6b-a0ea-8fdc981e8740">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CIDADE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="196" y="23" width="30" height="11" uuid="ae36da56-6758-48fb-b843-815587a6eaf5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Estado:]]></text>
				</staticText>
				<textField>
					<reportElement x="226" y="23" width="70" height="11" uuid="936c5e8f-12b3-4489-878d-cdcc4b86ba53">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_UF}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="171" y="34" width="23" height="11" uuid="35ac7ccf-e1d6-4c0e-91b2-ced672a9a14b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[I.E.:]]></text>
				</staticText>
				<textField>
					<reportElement x="194" y="34" width="102" height="11" uuid="1de77788-3794-40e6-813c-677147b5dc45">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_INSC_ESTAD}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="171" y="45" width="23" height="11" uuid="f8f213b7-5652-4566-a1d2-6f1e5d4df191">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cel.:]]></text>
				</staticText>
				<textField>
					<reportElement x="194" y="45" width="102" height="11" uuid="ea626241-cef2-4e44-a96a-ede0c4169932">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CELULAR}.length() > 6 ?   $F{CLIENTE_CELULAR} : " "]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="306" y="0" width="81" height="70" isPrintWhenDetailOverflows="true" uuid="0bbd82c2-4726-4245-9ce0-574f7d1f868b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="30" y="16" width="44" height="11" uuid="d6416550-0ea1-4380-b7e9-9bcbe3b7d1b0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Externo]]></text>
				</staticText>
				<staticText>
					<reportElement x="30" y="29" width="44" height="11" uuid="9987f162-637c-4f39-a1dc-f8f50dcbf0b6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Garantia]]></text>
				</staticText>
				<staticText>
					<reportElement x="30" y="42" width="44" height="11" uuid="b0e59b27-6dbd-4e15-8ff6-6926a00f9828">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Interno]]></text>
				</staticText>
				<staticText>
					<reportElement x="9" y="3" width="63" height="11" uuid="9e621519-1692-446f-94bb-fad5054dbc50">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo de Serviço]]></text>
				</staticText>
				<textField>
					<reportElement stretchType="ContainerHeight" x="19" y="43" width="9" height="9" uuid="6addd29f-ab82-40ef-a56d-54190a403220">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{INTERNO}.equals("S")?"X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="19" y="30" width="9" height="9" uuid="306ec924-5394-40d4-b61e-4daa8c5fc11e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_GARANTIA}.equals("S")?"X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="19" y="17" width="9" height="9" uuid="25f92297-fcb7-4018-80c4-832dfb5b8695">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_GARANTIA}.equals("N")?"X":" "]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" x="387" y="0" width="167" height="170" isPrintWhenDetailOverflows="true" uuid="aca76750-b095-47ae-9273-a90152704095">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement x="5" y="159" width="158" height="11" uuid="894c3e13-1d68-4adb-95c0-b386f94bded1"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Cód. Fab. Bateria: " + $F{OS_COD_FAB_BATERIA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="49" y="2" width="68" height="11" uuid="c2882f1e-7ec5-4d2e-8918-0ab50fcd2e8c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Dados do Veículo]]></text>
				</staticText>
				<textField>
					<reportElement x="5" y="16" width="158" height="11" uuid="4aa7514d-0bdb-491d-88bd-e3cc9364ee1a"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Modelo: " + $F{DESC_PROD_MOD}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="27" width="158" height="11" uuid="d0e4e6d9-457d-4311-affe-8292bf819d80"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["`Placa: " + $F{OS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="38" width="158" height="11" uuid="3be323ed-6fc9-4bab-aab1-1defd2d4ec7f"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Nº Chassi: " + $F{OS_CHASSI}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="49" width="158" height="11" uuid="bd433340-1c9c-4d84-9111-cbdd52636c1b">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Motor: " + $F{OS_NUMERO_MOTOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="60" width="158" height="11" uuid="8aebb44b-cd26-4103-861f-223e251869cb"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Ano Fabr.: " + $F{OS_ANO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="71" width="158" height="11" uuid="ac0ad7bf-8e35-43a4-9cbe-3da83c99c676"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Cor: " + $F{OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="82" width="158" height="11" uuid="28bf0fac-0718-4c26-a5c2-476eac6006ab"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["KM: " + $F{OS_KM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="93" width="158" height="11" uuid="c3a5fe85-2d82-46de-ba8a-d760cebf2b50"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Data Venda: " + $F{OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="104" width="158" height="11" uuid="9007a9bb-a84b-4ccc-8056-e7a7ba38a483"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Conc. Vendedora: " + $F{CONCESSIONARIA_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="115" width="158" height="11" uuid="1ea0253d-1ea2-41d3-890d-3e0a62a8fa96"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Ultima Conc. Execultante: "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="126" width="158" height="11" uuid="bb4119a4-e159-45a9-8861-0b8c8d4851e7"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Prisma: " + $F{OS_PRISMA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="137" width="158" height="11" uuid="d816bb82-75bd-4f86-bd9a-192aa1a80006"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Consultor Tecnico: " + $F{OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="148" width="158" height="11" uuid="c34daef9-d140-4be5-b1c2-2f1cb3b44030"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Data Fab. Bateria: " + $F{OS_DATA_FAB_BATERIA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement stretchType="ContainerBottom" x="0" y="82" width="387" height="358" isPrintInFirstWholeBand="true" uuid="804dd823-9e6b-49e0-8905-1bf28a88bcc8">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="ShowOutOfBoundContent" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport isUsingCache="false" overflowType="Stretch">
					<reportElement isPrintRepeatedValues="false" x="0" y="1" width="377" height="352" isPrintInFirstWholeBand="true" uuid="6a0869d8-a22b-4004-8a9c-cf29c653b7a1">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHarleyDavidsonSubServicos.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<frame>
				<reportElement stretchType="ContainerBottom" x="387" y="182" width="163" height="256" isPrintInFirstWholeBand="true" uuid="f81100b4-944c-4a8a-a348-bf080122a301">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineColor="#F76D6A"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement stretchType="ContainerHeight" isPrintRepeatedValues="false" x="0" y="0" width="156" height="253" uuid="b46d2035-1b83-44bc-8e3c-6752798b4199">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="ShowOutOfBoundContent" value="false"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<subreport isUsingCache="true" overflowType="Stretch">
						<reportElement stretchType="ContainerBottom" x="0" y="1" width="149" height="252" isPrintWhenDetailOverflows="true" uuid="09031303-0550-46ef-843a-0ba267e72348">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<subreportParameter name="NUMERO_OS">
							<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="COD_EMPRESA">
							<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
						</subreportParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHarleyDavidsonSubPecas.jasper"]]></subreportExpression>
					</subreport>
				</frame>
			</frame>
			<staticText>
				<reportElement mode="Transparent" x="387" y="170" width="167" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="3fa2ba31-c449-4605-998a-dcf91d251b6d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Requisições de Peças]]></text>
			</staticText>
			<line>
				<reportElement stretchType="ElementGroupBottom" x="554" y="181" width="1" height="259" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="c6a11353-98e4-4ccb-8d45-2f44e905704c">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement stretchType="ContainerBottom" x="90" y="70" width="1" height="370" isPrintWhenDetailOverflows="true" uuid="716e7716-8382-4b63-8ae9-4ed06e4b97cf">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement stretchType="ContainerBottom" x="40" y="70" width="1" height="370" isPrintWhenDetailOverflows="true" uuid="48170880-5f82-4e1d-871e-87779f4ad4bf">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement mode="Transparent" x="40" y="70" width="50" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="961d2576-1040-4675-8fb5-88dac0e83ec4">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[TMO]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="90" y="70" width="296" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="1fcaddc3-a31c-4010-bc3a-1d0d523df6a6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[TMO]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="70" width="40" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="3b0df3da-d373-465e-a680-57c438ed0c2c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Item]]></text>
			</staticText>
			<line>
				<reportElement stretchType="ContainerBottom" x="387" y="181" width="1" height="259" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="6ce32a95-c163-4c77-acc4-36f064e968c4">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement stretchType="ContainerBottom" x="0" y="71" width="1" height="368" isPrintWhenDetailOverflows="true" uuid="f7ba563c-e46b-48e8-bd57-0fad1b93abbe">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement stretchType="ContainerBottom" x="503" y="181" width="1" height="259" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b33f9099-4b6c-4374-a636-77ec2cea43f7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band height="196">
			<line>
				<reportElement positionType="Float" stretchType="ContainerBottom" x="0" y="-1" width="554" height="1" uuid="1438d50f-b99a-4dba-b18c-03c8d6ec41a8">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineColor="#000000"/>
				</graphicElement>
			</line>
			<frame>
				<reportElement x="387" y="-1" width="167" height="160" uuid="950ea64e-af6c-4bcb-847e-5de07156c7c0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="167" height="13" uuid="a82ff223-4918-468a-aff5-63bbf0e46b63">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Despesas]]></text>
				</staticText>
				<staticText>
					<reportElement x="1" y="12" width="101" height="11" uuid="3030c48c-9b62-47a2-b322-e450116c6806">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[M.O.-Revisão]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement x="102" y="12" width="65" height="11" uuid="2323ea20-4b18-4bd9-a18e-d62df329d94f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_REVISAO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="102" y="23" width="65" height="11" forecolor="#000000" backcolor="#E0E0E0" uuid="463ca927-d7c1-42d0-bc68-9f13c06689a8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_MECANICA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="1" y="23" width="101" height="11" forecolor="#000000" backcolor="#E0E0E0" uuid="fcbde3ab-8c00-48e0-83bf-f9a0a94547c8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[M.O.-Mecânica]]></text>
				</staticText>
				<staticText>
					<reportElement x="1" y="34" width="101" height="11" uuid="5a3fe67e-5ccd-4f73-89ea-0ccaff6b2524">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Serv.Terceiros]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="102" y="45" width="65" height="11" forecolor="#000000" backcolor="#E0E0E0" uuid="ea6d1071-bb82-4766-a425-b1fe901aeb9c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_LAVAGEM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="1" y="45" width="101" height="11" forecolor="#000000" backcolor="#E0E0E0" uuid="956fd46c-089b-4504-8a9b-f1fc2f81f549">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Lavagem]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement x="102" y="34" width="65" height="11" uuid="fb49b696-0041-4ec5-9ff1-7e343edb7d35">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_TERCEIROS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="56" width="101" height="11" uuid="881b8756-c417-49db-b181-b7597bd0b334">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Serviços Gerais]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="102" y="67" width="65" height="12" forecolor="#000000" backcolor="#E0E0E0" uuid="5c972217-719e-4963-908c-0a92b68293a1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS_SERVICOS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="1" y="67" width="101" height="12" forecolor="#000000" backcolor="#E0E0E0" uuid="bab3880e-013b-4c65-ba8a-4f0907740b86">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Sub-Total M.O.]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement x="102" y="56" width="65" height="11" uuid="42cccadb-3490-414c-8e85-776255c10596">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_GERAIS}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="102" y="112" width="65" height="11" forecolor="#000000" backcolor="#E0E0E0" uuid="b1c57cb6-7355-4a26-95b4-79d1fc3d1809">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_PECAS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="1" y="112" width="101" height="11" forecolor="#000000" backcolor="#E0E0E0" uuid="0d79d324-1f7d-4c77-8ac7-da1bed23dde5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Peças]]></text>
				</staticText>
				<staticText>
					<reportElement x="1" y="79" width="101" height="11" uuid="df94c291-2053-450f-9ef6-5364018bdb15">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Acessórios]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement x="102" y="123" width="65" height="11" uuid="9734b5b3-b4c0-4607-9ea0-f281bf42d9ad">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_LUBRIFICANTE}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="102" y="90" width="65" height="11" forecolor="#000000" backcolor="#E0E0E0" uuid="4c23493f-968a-42b1-b60d-d31f7ca6264a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_OUTROS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="1" y="90" width="101" height="11" forecolor="#000000" backcolor="#E0E0E0" uuid="a1ca4544-4363-41db-aa6b-1e1dd0ef1c8a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Outros]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement x="102" y="101" width="65" height="11" uuid="16059350-645c-4c5a-a4ff-2ccf885911ee">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_COMBUSTIVEL}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="102" y="135" width="65" height="12" forecolor="#000000" backcolor="#E0E0E0" uuid="2c83c66a-4328-448c-a939-8705fb4e25d9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS_ITENS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="101" width="101" height="11" uuid="f5b2ed09-95f9-4e6f-a146-c28701f4c701">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Combustível]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement x="102" y="79" width="65" height="11" uuid="45af7df6-9f12-4c46-943e-b0cf62848be5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_ACESSORIOS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="123" width="101" height="11" uuid="77dfb83d-cf42-40b1-9e11-94aaad674f0e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Lubrificantes]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="1" y="135" width="101" height="12" forecolor="#000000" backcolor="#E0E0E0" uuid="a5a6461e-35d9-4eaf-8170-98a09b893d18">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Sub-Total Peças]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="1" y="147" width="101" height="14" forecolor="#000000" backcolor="#E0E0E0" uuid="5db46efd-086c-4f4a-936b-bd5be2ae24df">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="102" y="147" width="65" height="14" forecolor="#000000" backcolor="#E0E0E0" uuid="4680064b-227c-4dd4-9af0-c3d16a280910">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="327" y="-1" width="60" height="46" uuid="4b6d51c4-**************-3ee5aa1b3483">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="5" y="5" width="47" height="11" uuid="d9630773-e1a9-46d2-8822-9c17eab45b56">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Combustível]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="17" width="51" height="11" uuid="6e88665a-136b-4aa2-8079-a941892d8924"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COMBUSTIVEL} < 19 ? "":
$F{OS_COMBUSTIVEL} < 39 ? "X" :
$F{OS_COMBUSTIVEL} < 59 ? "X   X":
$F{OS_COMBUSTIVEL} < 79 ? "X   X   X":
 "X   X   X   X"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="28" y="22" width="1" height="6" uuid="b7a25494-4bc1-4a63-8fba-932636f5d0ae">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="40" y="24" width="1" height="4" uuid="be3a9b24-6f62-4afa-9060-a9cb465111c0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="16" y="24" width="1" height="4" uuid="a0bd37dd-335e-48ec-af05-75ddc07b9706">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="46" y="26" width="1" height="2" uuid="0badfcca-5c92-4dae-a89e-8b820a3c4681">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="10" y="26" width="1" height="2" uuid="85ba771f-bb6e-4f7b-a323-1b8e2ba81705">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="22" y="26" width="1" height="2" uuid="06654732-912c-4029-a876-953bf3863847">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="34" y="26" width="1" height="2" uuid="7fcd5238-86fd-4802-83a6-062760d766db">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
			</frame>
			<frame>
				<reportElement x="0" y="-1" width="327" height="46" uuid="48d223e6-78f8-4388-ae88-791fb93bd679">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="4" y="1" width="313" height="11" uuid="f6df37e2-bc33-43fb-94cd-0483825f485a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Estado Geral da Pintura:]]></text>
				</staticText>
				<textField>
					<reportElement stretchType="ContainerHeight" x="85" y="13" width="9" height="9" uuid="a4051939-8e10-40ad-ab47-6d54c3e1604f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_JOGO_FERRAMENTAS}.equals("S")?"X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="12" width="81" height="11" uuid="089d7d7e-0223-43f6-8f10-ad9272c5556c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Jogo de Ferramentas:]]></text>
				</staticText>
				<staticText>
					<reportElement x="139" y="12" width="33" height="11" uuid="eed33c27-cf15-415c-bb8f-3d91b32e2cf0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Elástico:]]></text>
				</staticText>
				<staticText>
					<reportElement x="4" y="23" width="55" height="11" uuid="1004f216-b7ca-4193-948e-e95c7be03172">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tampa Lateral:]]></text>
				</staticText>
				<staticText>
					<reportElement x="67" y="23" width="8" height="11" uuid="61db77e4-2c27-4251-9ff6-0b9bc6f75886">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[D]]></text>
				</staticText>
				<staticText>
					<reportElement x="95" y="23" width="8" height="11" uuid="27efe6f3-ba42-45f6-94ce-2803e6b96f2e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[D]]></text>
				</staticText>
				<staticText>
					<reportElement x="139" y="23" width="33" height="11" uuid="392b98d4-27e7-4e26-be3d-dc01d2218555">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Flanela:]]></text>
				</staticText>
				<textField>
					<reportElement x="4" y="34" width="311" height="11" uuid="7f53d5ee-481f-45b4-af7f-4b7213f7c280">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["Obs:" + $F{OS_OBSERVACAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="75" y="24" width="9" height="9" uuid="a088dca3-dc79-4d23-83f8-b3e10bf4819e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TAMPA_LATERAL_D}.equals("S")?"X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="103" y="24" width="9" height="9" uuid="1fd66477-62e5-4619-9e21-ec6ec1b94d82"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TAMPA_LATERAL_E}.equals("S")?"X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="173" y="13" width="9" height="9" uuid="c4d88665-11aa-45ca-b7f3-37ea545de1d5"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_ELASTICOS}.equals("S")?"X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="173" y="24" width="9" height="9" uuid="ecb82c44-ad7b-4912-81c7-f522b0a9552d"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_FLANELA}.equals("S")?"X":" "]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="45" width="387" height="59" uuid="deead1f5-8649-47ab-b99a-5fd67c0492a6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="73" y="1" width="237" height="11" uuid="fa936292-5ee5-4049-b353-473f8b29de99">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Utilizar este campo em caso de serviço reembolsado pela Fabrica]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="11" width="158" height="11" uuid="bc3d89db-87fe-40f2-a204-c0d42444a51c"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Nome: " + $F{FATURAR_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="22" width="375" height="11" uuid="0321a51e-7ca6-452e-ab50-ab0d77c365ff"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Endereço: " + $F{FATURAR_ENDERECO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="33" width="238" height="11" uuid="fc1b93cb-c84b-47bc-8fc5-1f4be61fd8f5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Cidade: " + $F{FATURAR_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="44" width="238" height="11" uuid="04e98aa5-2c39-4c99-8a4e-a8a933d38ac0">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_FONE}.length() > 6 ? "Fone: " +  $F{FATURAR_FONE} :  "Fone: "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="241" y="34" width="137" height="11" uuid="d194495f-a7d4-4c65-be43-8507d8210ac8"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Estado: " + $F{FATURAR_ESTADO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="241" y="45" width="137" height="11" uuid="d5f00e00-f396-49ea-9c56-be3c5f02257b"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_TELEFONE_FAX}.length() > 6 ? "Fax: " +  $F{FATURAR_TELEFONE_FAX} : "Fax: "]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="104" width="387" height="56" uuid="3181de12-73aa-4816-9973-e255927302ae">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="16" y="1" width="309" height="29" uuid="e9136b6b-97d3-49fc-8a78-fba16ecf0400">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Estou ciente das condições apresentas nessa o.s. e que meu veiculo foi deixado na concessionária, conforme acima descrito.]]></text>
				</staticText>
				<staticText>
					<reportElement x="74" y="40" width="214" height="12" uuid="d523e099-1e66-44d1-93cb-e4702924c80b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="160" width="271" height="36" uuid="0b540096-02dd-4b6f-a2fd-86f107140548">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="140" y="23" width="119" height="12" uuid="eba94a6d-b6db-40c1-acae-ef8fe127d3fd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Visto]]></text>
				</staticText>
				<staticText>
					<reportElement x="30" y="16" width="80" height="12" uuid="519706cb-c028-48d9-a795-d78758318a5f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[____/____/________]]></text>
				</staticText>
				<staticText>
					<reportElement x="30" y="4" width="80" height="12" uuid="c759c4a1-a665-4000-beca-5ea18adea0dd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="19"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Caixa]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="271" y="160" width="283" height="36" uuid="62959d2d-9d1c-43af-bb2b-71ba24430ece">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="29" y="17" width="80" height="12" uuid="42a1e439-**************-8086ab4e3f07">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[____/____/________]]></text>
				</staticText>
				<staticText>
					<reportElement x="29" y="5" width="90" height="12" uuid="fa595270-daf9-4052-a6aa-6cb02f7a87a3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Produto Retirado em]]></text>
				</staticText>
				<staticText>
					<reportElement x="140" y="23" width="119" height="12" uuid="3b78cfb8-d464-481d-8bda-24de1cc5e550">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="140" y="2" width="119" height="20" uuid="edf83333-320b-4a29-9870-215504061b31"/>
					<imageExpression><![CDATA[$F{OS_ASSINATURA}]]></imageExpression>
				</image>
			</frame>
		</band>
	</columnFooter>
</jasperReport>
