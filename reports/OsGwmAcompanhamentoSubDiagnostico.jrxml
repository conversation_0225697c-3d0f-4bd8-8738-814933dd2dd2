<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsGwmAcompanhamentoSubDiagnostico" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[11095.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[select oo.item
       ,oo.descricao
       ,(select listagg(orr.resposta, ', ') within GROUP (order by orr.cod_pergunta)   from os_respostas_reclamacao orr where orr.numero_os = oo.numero_os and orr.cod_empresa = oo.cod_empresa and orr.cod_reclamacao = oo.item) as respostas
       , ods.causa
       , initcap(st.nome) as nome
       , ods.data_inicio
       , ods.hora_inicio
       , ods.data_fim
       , ods.hora_fim
       , decode(
        case when od.prover_auxilio = 'S' and instr(', ' || od.item_auxilio_toy || ',', ', '|| oo.item || ',') > 0 then 'S' else 'N' end
       ,'S', 'Sim', 'N', 'Não', 'Não Informado') as PROVER_AUXILIO
from os
     , os_original oo
     , OS_DIAGNOSTICO_SINTOMA_ITEM ODS
     , servicos_tecnicos st
     , os_diagnostico od
where os.numero_os = $P{NUMERO_OS}
      and os.cod_empresa = $P{COD_EMPRESA}
      and oo.numero_os = os.numero_os
      and oo.cod_empresa = os.cod_empresa
      and ods.numero_os = oo.numero_os
      and ods.cod_empresa = oo.cod_empresa
      and ods.item = oo.item
      and st.cod_tecnico(+) = ods.cod_tecnico
      and st.cod_empresa(+) = ods.cod_empresa
      and od.numero_os = os.numero_os
      and od.cod_empresa = os.cod_empresa]]>
	</queryString>
	<field name="ITEM" class="java.math.BigDecimal"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="RESPOSTAS" class="java.lang.String"/>
	<field name="CAUSA" class="java.lang.String"/>
	<field name="NOME" class="java.lang.String"/>
	<field name="DATA_INICIO" class="java.sql.Timestamp"/>
	<field name="HORA_INICIO" class="java.lang.String"/>
	<field name="DATA_FIM" class="java.sql.Timestamp"/>
	<field name="HORA_FIM" class="java.lang.String"/>
	<field name="PROVER_AUXILIO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="24">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="24" uuid="7b1cf3c2-6d17-4ab8-9730-8c8ed4e1e4b1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="2" width="140" height="10" uuid="979e23fb-4d9e-4740-bc6b-b0c5ae34fe5b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Diagnóstico]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="13" width="555" height="11" uuid="1d46bd96-6ae5-4613-9932-3d2ec1ba473e">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="209" height="10" uuid="bd6539f0-23aa-40a7-a195-75945cb10f30">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Resultado do Diagnóstico (Identificação da causa):]]></text>
					</staticText>
				</frame>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="62" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement key="" x="0" y="0" width="555" height="62" uuid="209444c4-2584-47b0-9d73-b61e06668e60">
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="45" height="62" uuid="53f9a68d-0c7f-4678-bb9c-7fbef00a1218">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="239" y="36" width="28" height="10" uuid="c9cbe3f4-3370-4a9d-a626-23a86a7be1d7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Início:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="475" y="36" width="26" height="10" uuid="bcfc2db9-6d87-4466-9802-80f0f351f1b6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="314" y="36" width="26" height="10" uuid="0697a3e6-7840-480f-ad5f-1d114e462f41">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="395" y="36" width="38" height="10" uuid="8bfa7c2a-940c-4f6a-978e-90c589144a14">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Término:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="48" y="36" width="102" height="10" uuid="c79ee4d7-2477-4444-a6da-4dfa7b2c1f12">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Técnico Responsável:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="129" y="36" width="80" height="10" uuid="80469920-2bd9-44d4-9b55-b7322129a1e5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="204" y="48" width="80" height="10" uuid="f5b78b43-5353-4d31-ad44-6435d4b312dc">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PROVER_AUXILIO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="48" y="48" width="182" height="10" uuid="23a8e85f-5113-4b70-8cb8-ef54a46da85f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Realizado STA(Suporte Técnico Avançado)?]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="48" y="5" width="495" height="25" uuid="eea12a5e-643f-4c28-8834-24da4a359d7d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Middle" markup="styled">
						<font size="7" isBold="false"/>
						<paragraph lineSpacing="Single" lineSpacingSize="0.0" firstLineIndent="0" leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESPOSTAS} == null ? "<style isBold='true'>" + $F{DESCRICAO}  + ": " +"</style>" + $F{CAUSA}   
: "<style isBold='true'>" + $F{DESCRICAO}  + " " +"</style>" +  "(" +  $F{RESPOSTAS} + "): " + $F{CAUSA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="264" y="36" width="43" height="10" uuid="67e00404-b93a-421d-ae08-9d370000dc1f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{HORA_INICIO}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="337" y="36" width="51" height="10" uuid="f8a4e85d-62d8-4b5f-a282-9a50a4e0f28c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_INICIO}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="498" y="36" width="51" height="10" uuid="2cc241ac-b229-497b-991e-322ad72e5541">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_FIM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="430" y="36" width="43" height="10" uuid="5bf292a3-2481-452b-9cff-57d9211f4594">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{HORA_FIM}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band/>
	</columnFooter>
</jasperReport>
