<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsMercedesSubServicos" pageWidth="595" pageHeight="500" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern="" isBlankWhenNull="true"/>
	<style name="alternateStyle" backcolor="#E0E0E0" isBlankWhenNull="true">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="IS_NULL" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select o.lgpd_memo  from crm_parm_fluxo o where o.cod_empresa = $P{COD_EMPRESA}]]>
	</queryString>
	<field name="LGPD_MEMO" class="java.lang.String"/>
	<detail>
		<band height="395" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="97" y="27" width="360" height="360" uuid="dba1dfee-3b2a-4989-a0bc-d95aa5f5a219"/>
				<textElement textAlignment="Justified" markup="none">
					<font fontName="SansSerif"/>
					<paragraph lineSpacing="Fixed" lineSpacingSize="15.0" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LGPD_MEMO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="182" y="0" width="190" height="20" uuid="af271d21-8fd0-43ab-9f53-321770d22914"/>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Lei Geral de Proteção de Dados]]></text>
			</staticText>
		</band>
		<band height="45">
			<staticText>
				<reportElement x="142" y="29" width="265" height="16" uuid="c92e617d-737c-48d3-b934-e667393a781c"/>
				<box>
					<topPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[Assinatura Cliente]]></text>
			</staticText>
			<frame>
				<reportElement x="201" y="12" width="149" height="22" uuid="e7992b0b-b130-4c7a-abb3-0b307b9eef05"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="149" height="22" forecolor="#FFFFFF" uuid="2f92aab5-bf3c-4ebd-9555-3445a6e5838d"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font size="4"/>
					</textElement>
					<text><![CDATA[#CLIENTE]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
