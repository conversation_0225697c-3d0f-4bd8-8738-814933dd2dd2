<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaMotoVinteUmItensSubFormularioInspecao" pageWidth="555" pageHeight="212" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232572.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\34613\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\temp\\image_zk\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select DECODE(E.DATA_ASSINATURA_CLIENTE, NULL, 'N', 'S') CLIENTE_CIENTE_INSPECAO,
       E.ASSINATURA_CLIENTE, 
       (SELECT listagg(tb.item_descricao || ': ' || tb.selecionado_observacao,'; ') within group (order by tb.GRUPO_ORDEM, tb.ID_GRUPO, tb.ITEM_ORDEM) AS OBSERVACAO from table(pkg_crm_service_checklist.get_table_checklist_item(OS.COD_EMPRESA, OS.NUMERO_OS, 0,'')) tb where tb.SELECIONADO_OBSERVACAO is not null and tb.ID_GRUPO in (40070,40085,40095)) AS OBSERVACOES
from 
      MOB_OS_ASSINATURA E
     , OS
where OS.NUMERO_OS = $P{NUMERO_OS}
      and OS.COD_EMPRESA = $P{COD_EMPRESA}
      and E.COD_EMPRESA(+) = OS.COD_EMPRESA
      and E.NUMERO_OS(+) = OS.NUMERO_OS
      and E.APLICACAO(+) = 'O']]>
	</queryString>
	<field name="CLIENTE_CIENTE_INSPECAO" class="java.lang.String"/>
	<field name="ASSINATURA_CLIENTE" class="java.awt.Image"/>
	<field name="OBSERVACOES" class="java.lang.String"/>
	<title>
		<band height="36">
			<staticText>
				<reportElement mode="Opaque" x="0" y="22" width="277" height="14" forecolor="#FFFFFF" backcolor="#000000" uuid="250a8086-4b65-4804-a384-34c74c438a61">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box leftPadding="2">
					<pen lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[INSPEÇÃO DE 22 ITENS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="186" y="23" width="30" height="12" forecolor="#FFFFFF" backcolor="#99CC00" uuid="e29edf2a-eafe-4da6-bde1-9b46e9f53be1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[OK]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="216" y="23" width="30" height="12" backcolor="#FFFF00" uuid="b0ddca7a-eff7-446c-bfed-8b13e8e9383d">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="4" isBold="true"/>
				</textElement>
				<text><![CDATA[Necessita Serviço]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="247" y="23" width="30" height="12" forecolor="#FFFFFF" backcolor="#FF0000" uuid="3cff9b37-9e6a-4357-9a95-5e02daca82db">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="4" isBold="true"/>
				</textElement>
				<text><![CDATA[Reparo Imediato]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="277" y="22" width="278" height="14" forecolor="#FFFFFF" backcolor="#000000" uuid="cb632a53-603f-4ebe-8c31-************">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="2">
					<pen lineColor="#080808"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="464" y="23" width="30" height="12" forecolor="#FFFFFF" backcolor="#99CC00" uuid="f21371b3-0fc0-455b-a023-a936f027a7e1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[OK]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="494" y="23" width="30" height="12" backcolor="#FFFF00" uuid="b6044193-62a5-448c-8418-7864487f15c0">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="4" isBold="true"/>
				</textElement>
				<text><![CDATA[Necessita Serviço]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="524" y="23" width="30" height="12" forecolor="#FFFFFF" backcolor="#FF0000" uuid="558b5fb2-f174-48e9-86e1-0d0e1626ab02">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="4" isBold="true"/>
				</textElement>
				<text><![CDATA[Reparo Imediato]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="22" backcolor="#C0C0C0" uuid="38588ec2-8f1e-45a5-9a82-f5b2fdaab324">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="5">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<text><![CDATA[Nossa inspeção ajuda a identificar problemas que possam afetar o funcionamento e performance da sua motocicleta. Nosso pessoal de serviço terá o prazer
de explicar a inspeção realizada e elaborar um orçamento para qualquer item marcado como "ATENÇÃO" ou "MANUTENÇÃO IMEDIATA".]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="176">
			<frame>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="277" height="132" isPrintWhenDetailOverflows="true" uuid="ad137f15-17db-4cbb-bd2f-7bff2f68e787">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<leftPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<subreport overflowType="NoStretch">
					<reportElement x="0" y="0" width="277" height="132" uuid="ea20f1e9-88f3-4cce-b870-f2193a030f2c">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="ID_GRUPO">
						<subreportParameterExpression><![CDATA["40070,40085"]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListHondaMotoVinteUmItensSubFormularioInspecaoItens.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<frame>
				<reportElement stretchType="ContainerBottom" x="277" y="0" width="278" height="154" isPrintWhenDetailOverflows="true" uuid="079176f9-c356-4ceb-8894-85b3f3efff7b">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<leftPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<subreport overflowType="NoStretch">
					<reportElement x="0" y="0" width="277" height="154" uuid="eba0bd1a-17d8-4d14-9721-1335ec80a123">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="ID_GRUPO">
						<subreportParameterExpression><![CDATA[40095]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListHondaMotoVinteUmItensSubFormularioInspecaoItens.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<frame>
				<reportElement mode="Opaque" x="0" y="132" width="277" height="44" backcolor="#C0C0C0" uuid="c6d32f03-0c37-413a-9976-7a998b4ced84">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="277" height="22" uuid="588761d4-1b01-479b-8ba6-56b137e9c356">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[CLIENTE CIENTE DA INSPEÇÃO?]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="13" y="23" width="9" height="7" uuid="5435556b-1883-4d4a-aec8-4d5cb7965cc8">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ASSINATURA_CLIENTE}!=null?"X":""]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="23" y="22" width="14" height="9" uuid="c3148013-c9a1-443e-ac91-f637ed890e06">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[SIM]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="48" y="23" width="9" height="7" uuid="be610ae7-ba71-40a2-993a-6428add899f7">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ASSINATURA_CLIENTE}==null?"X":""]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="58" y="22" width="14" height="9" uuid="6f4d19ba-6132-44b6-b0a2-517fa6b9d303">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[NÃO]]></text>
				</staticText>
				<staticText>
					<reportElement x="84" y="22" width="59" height="9" uuid="627784cd-1de3-45cb-822b-ef149b719bb0">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[VISTO DO CLIENTE:]]></text>
				</staticText>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="144" y="10" width="126" height="20" uuid="9b5be4ee-ba81-441d-94d7-3edb2b9e3a23"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{ASSINATURA_CLIENTE}]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="144" y="31" width="126" height="3" uuid="a20bb374-3bc3-4f83-91fa-7e93770942db">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[VISTO DO CLIENTE:]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement mode="Opaque" x="277" y="154" width="278" height="22" backcolor="#C0C0C0" uuid="10fda4d1-99c6-4936-806c-d31e10790e02">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="81" y="2" width="191" height="18" forecolor="#000000" uuid="c78e1d8a-f193-41ec-9d62-ff96050cf230">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<pen lineWidth="0.2"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="5" isItalic="true" isUnderline="false"/>
						<paragraph lineSpacing="Fixed" lineSpacingSize="8.0" firstLineIndent="1" leftIndent="1" tabStopWidth="0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OBSERVACOES}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="9" y="2" width="72" height="9" uuid="3d539507-1d7c-4cfe-90fe-3867aa80ae10">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="5" isBold="false" isItalic="true"/>
						<paragraph lineSpacing="Fixed" firstLineIndent="1" leftIndent="0" tabStopWidth="1"/>
					</textElement>
					<text><![CDATA[INFORMAÇÕES ADICIONAIS:]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
