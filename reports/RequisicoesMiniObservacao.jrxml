<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RequisicoesMiniSubItens" pageWidth="186" pageHeight="842" columnWidth="186" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a5d195c3-46e2-4e2e-9575-573e21d1a687">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="false"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="570"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="416"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="valor_null" isDefault="true" isBlankWhenNull="true" fontName=""/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="REQUISICAO" class="java.lang.Double"/>
	<parameter name="SQL_FILTRO_REQUISICAO" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{REQUISICAO} == null ? "1 = 1" : "os_requisicoes.requisicao = " + $P{REQUISICAO}]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT OS_REQUISICOES.OBSERVACAO
          FROM OS_REQUISICOES
          WHERE  OS_REQUISICOES.COD_EMPRESA = $P{COD_EMPRESA}
              AND OS_REQUISICOES.NUMERO_OS = $P{NUMERO_OS}
              AND  $P!{SQL_FILTRO_REQUISICAO}
              AND NOT OS_REQUISICOES.OBSERVACAO IS NULL
              AND ROWNUM = 1]]>
	</queryString>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="106">
			<frame>
				<reportElement x="0" y="0" width="186" height="105" uuid="b81f4055-9294-42dd-a4f0-90c7f9d93ff3">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<bottomPen lineWidth="0.45" lineStyle="Dashed"/>
				</box>
				<staticText>
					<reportElement positionType="Float" x="0" y="0" width="20" height="11" uuid="808134a3-**************-b9b16b6bb677">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<printWhenExpression><![CDATA[!$F{OBSERVACAO}.equals("")]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Obs:]]></text>
				</staticText>
				<textField>
					<reportElement positionType="Float" x="20" y="0" width="156" height="105" uuid="2c1e2de2-07f6-44b8-af1c-ee0b1a2f4040">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<printWhenExpression><![CDATA[!$F{OBSERVACAO}.equals("")]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Justified" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnHeader>
</jasperReport>
