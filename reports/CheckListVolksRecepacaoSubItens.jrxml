<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListVolksRecepacaoSubItens" pageWidth="555" pageHeight="800" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select cod_empresa, nome from empresas e where e.cod_empresa in (1,2,3)]]>
		</queryString>
		<field name="COD_EMPRESA" class="java.lang.Double"/>
		<field name="NOME" class="java.lang.String"/>
	</subDataset>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232561.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select a.id_grupo,
       a.grupo_descricao,
       initcap(a.item_descricao) as item_descricao,
       UPPER(a.selecionado_opcao_descricao) as descricao_opcao,
       initcap(a.selecionado_observacao) as observacao
from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
where a.id_checklist = 60000
      and a.id_grupo in (60010, 60030, 60050)
order by a.grupo_ordem, a.item_ordem]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="GRUPO_DESCRICAO" class="java.lang.String"/>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<group name="Group1">
		<groupExpression><![CDATA[$F{ID_GRUPO}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.HorizontalRowLayout"/>
				<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				<frame>
					<reportElement x="0" y="0" width="555" height="18" uuid="64eec948-2747-48e4-b04b-d4209cb421fa">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textField>
						<reportElement mode="Transparent" x="0" y="0" width="320" height="18" forecolor="#000000" backcolor="#E3DCDC" uuid="87347c85-767b-4e90-83a4-c195f85fe494">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box leftPadding="5" rightPadding="4">
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="11" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{GRUPO_DESCRICAO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="320" y="0" width="68" height="18" forecolor="#000000" backcolor="#E3DCDC" uuid="888aecb4-7304-491e-8d85-a34d9c98b97a">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box leftPadding="5" rightPadding="4">
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="9" isBold="true"/>
						</textElement>
						<text><![CDATA[OK]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="388" y="0" width="78" height="18" forecolor="#000000" backcolor="#E3DCDC" uuid="eef7d62a-d7a0-4767-ab94-0858021c6394">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box leftPadding="5" rightPadding="4">
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="9" isBold="true"/>
						</textElement>
						<text><![CDATA[N/OK]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="466" y="0" width="89" height="18" forecolor="#000000" backcolor="#E3DCDC" uuid="0e2a8040-af84-4d37-918d-a3655a3c7c80">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<box leftPadding="5" rightPadding="4">
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="9" isBold="true"/>
						</textElement>
						<text><![CDATA[Reparado]]></text>
					</staticText>
				</frame>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="10" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[!$F{ITEM_DESCRICAO}.equals("Percepções De Uso")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="555" height="10" uuid="98e3a3fc-a687-48e5-a433-1e53d3afce84"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="320" height="10" forecolor="#000000" uuid="ab764b36-97f6-4ec6-bfe0-5c8881ac657f">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="9">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="320" y="0" width="68" height="10" forecolor="#000000" uuid="b04c1209-3373-4a82-84b0-1576bcd05a79">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="388" y="0" width="78" height="10" forecolor="#000000" uuid="29b6fcaf-4ae7-4f96-8469-744c2be0eecb">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NÃO OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="466" y="0" width="89" height="10" forecolor="#000000" uuid="22214289-3d99-494d-889a-030bcc538c99">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("REPARADO") ? "X" : ""]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="10">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$F{ITEM_DESCRICAO}.equals("Percepções De Uso")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="555" height="10" uuid="933f5a37-7217-437f-94f9-d69e43b3ca76"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="550" height="10" forecolor="#000000" uuid="87df8974-3663-4d88-a963-0dd601b65a4e">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="9">
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OBSERVACAO} == null
? $F{ITEM_DESCRICAO} + ":"
: $F{ITEM_DESCRICAO} + ":  " + $F{OBSERVACAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
