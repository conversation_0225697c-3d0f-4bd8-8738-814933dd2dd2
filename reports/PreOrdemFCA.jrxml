<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsFCA" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" isFloatColumnFooter="true" uuid="a6c8faac-fc28-4e0c-a20a-71416dbce697">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="TESTE_FREEDOM.xml"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="353"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="633"/>
	<style name="campoTexto" isDefault="true" vTextAlign="Justified" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\30141\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_OS_AGENDA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH Q_OS_AGENDA AS (
  SELECT COD_EMPRESA
      ,COMBUSTIVEL
      ,COD_OS_AGENDA
      ,STATUS_AGENDA
      ,QUEM_ABRIU
      ,(SELECT EE.NOME_COMPLETO 
          FROM EMPRESAS_USUARIOS EE 
         WHERE EE.COD_EMPRESA = COD_EMPRESA 
           AND EE.NOME = QUEM_ABRIU) AS AGENDADOR
      ,DATA_ABRIDA
      ,COD_CLIENTE
      ,CLIENTE_NOME
      ,CLIENTE_AGUARDA
      ,EH_RETORNO
      ,TRUNC(DATA_AGENDADA) TRUNC_DATA_AGENDADA
      ,TO_CHAR(DATA_AGENDADA, 'HH24:MI') TRUNC_HORA_AGENDADA
      ,TRUNC(DATA_PREVISAO_FIM) TRUNC_DATA_PREVFIM
      ,TO_CHAR(DATA_PREVISAO_FIM, 'HH24:MI') TRUNC_HORA_PREVFIM
      ,CLIENTE_FONE_COM
      ,TRUNC(DATA_ABRIDA) TRUNC_DATA_ABRIDA
      ,TO_CHAR(DATA_ABRIDA, 'HH24:MI') TRUNC_HORA_ABRIDA
      ,CLIENTE_EMAIL
      ,DECODE(COD_CLIENTE, 1, NVL(FONE_CLIENTE_AVULSO,CLIENTE_FONE_RES),CLIENTE_FONE_RES) AS CLIENTE_FONE_RES
      ,CLIENTE_FONE_FAX
      ,DECODE(COD_CLASSE,
              'J',
              NVL(CLIENTE_FONE_COM, CLIENTE_FONE_RES),
              NVL(CLIENTE_FONE_RES, CLIENTE_FONE_COM)) AS CLIENTE_FONE_CONTATO
      ,COD_CLASSE
      ,TELEFONE_CEL AS CLIENTE_TELEFONE_CEL
      ,COD_PRODUTO
      ,COD_MODELO
      ,PLACA
      ,DATA_AGENDADA
      ,DATA_PREVISAO_FIM
      ,NUMERO_OS
      ,OBSERVACOES
      ,CONSULTOR
      ,COR_EXTERNA
      ,DESCRICAO_PRODUTO
      ,DESCRICAO_MODELO
      ,MOD_VER_SERIE
      ,LINHA
      ,DESCRICAO_VEICULO
      ,DECODE(COD_CLASSE, 'J', CGC, CPF) AS CLIENTE_CGC_CPF
      ,DECODE(COD_CLASSE, 'J', INSCRICAO_ESTADUAL, 'ISENTO') AS RG_IE
      ,RG AS CLIENTE_RG
      ,INSCRICAO_ESTADUAL
      ,KM
      ,PRISMA
      ,DESCRICAO_PRISMA
      ,PRODUTIVO_PRISMA
      ,CLIENTE_DT
      ,EH_FIAT_PROFISSIONAL      
      ,COLLABORATION
      ,AG_ATIVO
      ,DATA_CONFIRMADA
      ,HORA_CONFIRMADA
      ,DATA_RECEBIDA
      ,HORA_RECEBIDA
      ,RESPONSAVEL_CONFIRMADA
      ,RESPONSAVEL_RECEBIDA
      ,ULT_NUMERO_OS
      ,NUMERO_MOTOR
      ,CHASSI
      ,('20' || SUBSTR(ANO,1,2) || '/' || '20' || SUBSTR(ANO,4,2)) AS ANO
      ,TERMO_FABRICA
      ,VEICULO_PLATAFORMA
     ,CASE
        WHEN NVL(TOTAL_IMPRESSAO,0) > 1  THEN DATA_ULT_IMP
        END DATA_ULT_IMP
     ,CASE
        WHEN NVL(TOTAL_IMPRESSAO,0) > 1  THEN HORA_ULT_IMP
        END HORA_ULT_IMP
	 ,CASE
        WHEN NVL(TOTAL_IMPRESSAO,0) > 1  THEN DATA_ATUAL_IMP
        END DATA_ATUAL_IMP
     ,CASE
        WHEN NVL(TOTAL_IMPRESSAO,0) > 1  THEN HORA_ATUAL_IMP
        END HORA_ATUAL_IMP
      ,TOTAL_IMPRESSAO 
      ,LAVAR_VEICULO
      ,EH_RECALL
      ,EH_CAMPANHA
      ,TIPO_CONCESSIONARIA
	  ,COD_MARCA
      ,LOGO_FABRICA
      ,TIPO_ATENDIMENTO
      ,LEVAR_PECAS_SUBSTITUIDAS
      ,TEXTO
  FROM (SELECT A.COD_EMPRESA
               ,A.COMBUSTIVEL
              ,A.COD_OS_AGENDA
              ,A.STATUS_AGENDA
              ,A.QUEM_ABRIU
              ,A.DATA_ABRIDA
              ,A.COD_CLIENTE
              ,NVL(NVL(A.CLIENTE_NOME, CD.NOME), 'CONSUMIDOR FINAL') AS CLIENTE_NOME
              ,NVL(A.CLIENTE_AGUARDA, 'N') AS CLIENTE_AGUARDA             
              ,CASE NVL(A.EH_RETORNO, 'N')  -- ,NVL(A.EH_RETORNO, 'N') AS EH_RETORNO
              WHEN 'N' THEN 'N'
              WHEN 'S' THEN 'S'
                ELSE
                  'REDE'
              END AS EH_RETORNO              
              ,DECODE(A.COD_CLIENTE, 1, NVL(CR.EMAIL_CLIENTE_AVULSO,A.EMAIL), NVL(A.EMAIL,C.ENDERECO_ELETRONICO)) AS CLIENTE_EMAIL
                            
              ,('(' || C.PREFIXO_COM || ') ' || NVL(C.TELEFONE_COM,A.CLIENTE_FONE_COM) ) AS CLIENTE_FONE_COM              
              ,('(' || C.PREFIXO_RES || ') ' || NVL(C.TELEFONE_RES,A.CLIENTE_FONE_RES)) CLIENTE_FONE_RES    
              ,('(' || C.PREFIXO_FAX || ') ' || C.TELEFONE_FAX) CLIENTE_FONE_FAX                            
              ,( '(' || C.PREFIXO_CEL || ') ' || NVL(A.CLIENTE_FONE_CEL, C.TELEFONE_CEL))  AS TELEFONE_CEL
              
              ,NVL(C.COD_CLASSE, DECODE(LENGTH(TRIM(CD.CGC)), 18, 'J', 'F')) AS COD_CLASSE
              ,CR.FONE_CLIENTE_AVULSO                                        
              ,CD.CGC
              ,CD.CPF
              ,CD.INSCRICAO_ESTADUAL
              ,CD.RG
              ,A.COD_PRODUTO
              ,A.COD_MODELO
              ,A.PLACA
              ,A.DATA_AGENDADA
              ,A.DATA_PREVISAO_FIM
              ,A.NUMERO_OS
              ,A.OBSERVACOES
              ,(SELECT EU.NOME_COMPLETO FROM EMPRESAS_USUARIOS EU
               WHERE EU.NOME = A.CONSULTOR) AS CONSULTOR
              ,A.COR_EXTERNA
              ,SUBSTR(TRIM(PM.MOD_VER_SERIE) || '-' || TRIM(PM.DESCRICAO_MODELO), 1, 37) AS DESCRICAO_PRODUTO
              ,PM.DESCRICAO_MODELO
              ,PM.MOD_VER_SERIE
              ,PM.LINHA
              ,TRIM(PM.DESCRICAO_MODELO) AS DESCRICAO_VEICULO
              ,A.KM
              ,A.PRISMA
              ,PB.DESCRICAO AS DESCRICAO_PRISMA
              ,ST.NOME      AS PRODUTIVO_PRISMA
              ,NVL(A.CLIENTE_DT, 'N') AS CLIENTE_DT
              ,NVL(A.EH_FIAT_PROFISSIONAL, 'N') AS EH_FIAT_PROFISSIONAL                           
              ,NVL(A.COLLABORATION, 'N') AS COLLABORATION
              ,CASE
                 WHEN CR.TIPO_ATENDIMENTO = 'R' THEN
                  'N'
                 ELSE
                  'S'
               END AS AG_ATIVO
              ,NVL(TO_CHAR(A.DATA_CONFIRMADA, 'DD/MM/YYYY'),'__/__/____') AS DATA_CONFIRMADA
              ,NVL(TO_CHAR(A.DATA_CONFIRMADA, 'HH24:MI'),'___:___') AS HORA_CONFIRMADA
              ,NVL(TO_CHAR(A.DATA_PC_RECEBIDA, 'DD/MM/YYYY'),'__/__/____') AS DATA_RECEBIDA
              ,NVL(TO_CHAR(A.DATA_PC_RECEBIDA, 'HH24:MI'), '___:___') AS HORA_RECEBIDA
              ,(SELECT EU.NOME_COMPLETO
                  FROM EMPRESAS_USUARIOS EU
                 WHERE EU.NOME = A.QUEM_CONFIRMOU) AS RESPONSAVEL_CONFIRMADA
              ,A.QUEM_RECEBEU_PC AS RESPONSAVEL_RECEBIDA
              ,(SELECT MAX(OS.NUMERO_OS) AS ULT_NUMERO_OS
                  FROM OS, OS_DADOS_VEICULOS DV, OS_AGENDA OA
                 WHERE OS.COD_EMPRESA = DV.COD_EMPRESA
                   AND OS.NUMERO_OS = DV.NUMERO_OS
                   AND OA.COD_OS_AGENDA = A.COD_OS_AGENDA
                   AND OS.STATUS_OS = 1
                   AND OS.ORCAMENTO = 'N'
                   AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
                   AND OA.COD_OS_AGENDA = A.COD_OS_AGENDA
                   AND OA.COD_EMPRESA = A.COD_EMPRESA) AS ULT_NUMERO_OS
              ,A.NUMERO_MOTOR
              ,A.CHASSI
              ,A.ANO
              ,(SELECT TMF.DESCRICAO_TERMO 
                  FROM OS_TERMO_FABRICA TMF, 
                       PARM_SYS PSF
                 WHERE PSF.TIPO_CONCESSIONARIA = TMF.TIPO_CONCESSIONARIA
                   AND PSF.COD_EMPRESA = A.COD_EMPRESA) AS TERMO_FABRICA
              ,NVL(A.VEICULO_PLATAFORMA,'N') AS VEICULO_PLATAFORMA
              ,TRUNC(A.DATA_ULT_IMP) AS DATA_ULT_IMP
              ,TO_CHAR(A.DATA_ULT_IMP , 'HH24:MI') AS HORA_ULT_IMP
			  ,TRUNC(SYSDATE) AS DATA_ATUAL_IMP
              ,TO_CHAR(SYSDATE , 'HH24:MI') AS HORA_ATUAL_IMP
              ,A.TOTAL_IMPRESSAO 
              ,NVL(A.LAVAR_VEICULO,'N') AS LAVAR_VEICULO
              ,NVL(A.LEVAR_PECAS_SUBSTITUIDAS,'N') AS LEVAR_PECAS_SUBSTITUIDAS
              ,NVL(A.EH_RECALL,'N') AS EH_RECALL
              ,NVL(A.EH_CAMPANHA,'N') AS EH_CAMPANHA
              ,M.TIPO_CONCESSIONARIA
			  ,P.COD_MARCA
              ,M.LOGO_FABRICA
              ,A.TIPO_ATENDIMENTO
              ,VWCRM.TEXTO
          FROM OS_AGENDA        A
              ,CLIENTE_DIVERSO  CD
              ,CLIENTES         C
              ,PRODUTOS         P
              ,PRODUTOS_MODELOS PM
              ,CRM_EVENTOS      CR
              ,MARCAS           M
              ,PRISMA_BOX       PB
              ,SERVICOS_TECNICOS ST
              ,PARM_SYS PS1
              ,VW_CRM_MSG_LGPD_MARCA_OS VWCRM

         WHERE 1=1
           AND A.COD_EMPRESA = $P{COD_EMPRESA}
           AND A.COD_OS_AGENDA = $P{COD_OS_AGENDA}
           AND A.COD_CLIENTE = CD.COD_CLIENTE(+)
           AND A.COD_CLIENTE = C.COD_CLIENTE(+)
           AND A.COD_PRODUTO = P.COD_PRODUTO
           AND A.COD_PRODUTO = PM.COD_PRODUTO
           AND A.COD_MODELO = PM.COD_MODELO
           AND A.COD_MODELO = PM.COD_MODELO
           AND A.CRM_COD_EVENTO = CR.COD_EVENTO(+)
           AND A.CRM_COD_EMPRESA = CR.COD_EMPRESA(+)
           AND M.COD_MARCA (+) = P.COD_MARCA
           AND PB.PRISMA (+) = A.PRISMA
           AND ST.COD_EMPRESA (+) = PB.COD_EMPRESA
           AND ST.COD_TECNICO (+) = PB.COD_TECNICO
           AND PS1.COD_EMPRESA = A.COD_EMPRESA
           AND VWCRM.COD_TIPO_CONCESSIONARIA (+)= PS1.TIPO_CONCESSIONARIA
           )
   ),

Q_LOGO AS (
SELECT 
       E.COD_EMPRESA,
       E.EMPRESA_NOME_COMPLETO,
       E.CGC AS CGC,
       'I.E.: ' || E.INSCRICAO_ESTADUAL AS INSCRICAO_ESTADUAL,       
       'I.M.: ' || E.INSCRICAO_MUNICIPAL AS IM,
       (E.RUA || ', ' || E.FACHADA) AS RUA,
       'COMP: ' || E.COMPLEMENTO AS COMPLEMENTO,
       'BAIRRO: ' || E.BAIRRO AS BAIRRO,
       'CEP: ' || E.CEP AS CEP,
       'CIDADE: ' || E.CIDADE AS CIDADE,    
       E.ESTADO AS ESTADO,
      TRANSLATE(E.FONE, ' -', ' ') AS FONE,
       'FAX: ' || TRANSLATE(E.FAX, ' -', ' ') AS FAX,
       'E-MAIL:' || C.ENDERECO_ELETRONICO AS ENDERECO_ELETRONICO,
       'SITE: ' || CD.EMPRESA_SITE AS EMPRESA_SITE,
       NVL(E.FUSO_HORARIO,0) AS FUSO_HORARIO
  FROM EMPRESAS E,CLIENTES C,CLIENTE_DIVERSO CD
 WHERE E.COD_CLIENTE=C.COD_CLIENTE
   AND C.COD_CLIENTE=CD.COD_CLIENTE
   AND E.COD_EMPRESA = $P{COD_EMPRESA}),
QRYDADOSVEIC AS (
SELECT NVL(OA.COD_CONCESSIONARIA, CF.COD_CONCESSIONARIA) AS COD_CONCESSIONARIA
      ,CASE
         WHEN CF.ANO IS NOT NULL AND
              LENGTH(TRIM(REPLACE(REPLACE(CF.ANO, '.', ''), ',', ''))) = 5 THEN
          TO_CHAR(TO_DATE(TO_CHAR(SUBSTR(CF.ANO, 1, INSTR(CF.ANO, '/') - 1), '00'),
                          'RR'),
                  'YYYY') || '/' ||
          TO_CHAR(TO_DATE(TO_CHAR(SUBSTR(CF.ANO, 4), '00'), 'RR'), 'YYYY')
       END AS ANO
      ,NVL(OA.COR_EXTERNA, CF.COR_VEICULO) AS COR_VEICULO
      ,NVL(OA.CONCESSIONARIA_DT_VENDA, CF.DATA_COMPRA) AS DATA_COMPRA
      ,CF.CHASSI
      ,NVL(OA.NUMERO_MOTOR, CF.NUMERO_MOTOR) AS NUMERO_MOTOR
      ,NVL(OA.CONCESSIONARIA_DT_VENDA, CF.DATA_COMPRA) AS DATA_VENDA
      ,C.NOME AS CONCESSIONARIA
      ,(SELECT EU.NOME_COMPLETO
          FROM EMPRESAS_USUARIOS EU
         WHERE EU.NOME = CF.NOME_VENDEDOR) AS VENDEDOR
  FROM CLIENTES_FROTA CF, CONCESSIONARIAS C, OS_AGENDA OA
 WHERE NVL(OA.COD_CONCESSIONARIA,CF.COD_CONCESSIONARIA) = C.COD_CONCESSIONARIA
  AND CF.COD_CLIENTE = OA.COD_CLIENTE
  AND CF.COD_PRODUTO = OA.COD_PRODUTO
  AND CF.COD_MODELO = OA.COD_MODELO
  AND OA.COD_OS_AGENDA = $P{COD_OS_AGENDA}
  AND OA.COD_EMPRESA = $P{COD_EMPRESA}
),

Q_HISTORICO AS
 (SELECT LISTAGG(HISTORICO_OS.N_OS, ', ') WITHIN GROUP(ORDER BY HISTORICO_OS.N_OS) AS HISTORICO,
         HISTORICO_OS.CHASSI,
         HISTORICO_OS.COD_EMPRESA
    FROM (SELECT DISTINCT TO_CHAR(OSD.NUMERO_OS) AS N_OS,
                          OSD.CHASSI,
                          OSD.COD_EMPRESA
            FROM OS_DADOS_VEICULOS OSD, OS, Q_OS_AGENDA
           WHERE OSD.COD_EMPRESA = OSD.COD_EMPRESA
             AND OS.NUMERO_OS = OSD.NUMERO_OS
             AND OS.STATUS_OS = 1
             AND OS.NUMERO_OS > 0
             AND OSD.CHASSI = Q_OS_AGENDA.CHASSI
             AND ROWNUM < 11
           ORDER BY OSD.NUMERO_OS DESC) HISTORICO_OS
   GROUP BY HISTORICO_OS.CHASSI, HISTORICO_OS.COD_EMPRESA),
   
   
Q_CLIENTE AS (
SELECT COD_CLIENTE ,DECODE(VAL_DIV,
              GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
              DIV_UF,
              DECODE(VAL_RES,
                     GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                     RES_UF,
                     DECODE(VAL_COM,
                            GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                            COM_UF,
                            COB_UF))) AS UF
      ,DECODE(VAL_DIV,
              GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
              DIV_CEP,
              DECODE(VAL_RES,
                     GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                     RES_CEP,
                     DECODE(VAL_COM,
                            GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                            COM_CEP,
                            COB_CEP))) AS CEP
      ,DECODE(VAL_DIV,
              GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
              DIV_CID,
              DECODE(VAL_RES,
                     GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                     RES_CID,
                     DECODE(VAL_COM,
                            GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                            COM_CID,
                            COB_CID))) AS CIDADE
      ,DECODE(VAL_DIV,
              GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
              DIV_BAIRRO,
              DECODE(VAL_RES,
                     GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                     RES_BAIRRO,
                     DECODE(VAL_COM,
                            GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                            COM_BAIRRO,
                            COB_BAIRRO))) AS BAIRRO
      ,DECODE(VAL_DIV,
              GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
              DIV_RUA,
              DECODE(VAL_RES,
                     GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                     RES_RUA,
                     DECODE(VAL_COM,
                            GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                            COM_RUA,
                            COB_RUA))) AS RUA
      ,DECODE(VAL_DIV,
              GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
              DIV_COMP,
              DECODE(VAL_RES,
                     GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                     RES_COMP,
                     DECODE(VAL_COM,
                            GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                            COM_COMP,
                            COB_COMP))) AS COMPLEMENTO
      ,DECODE(VAL_DIV,
              GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
              DIV_COMP,
              DECODE(VAL_RES,
                     GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                     RES_CONTATO,
                     DECODE(VAL_COM,
                            GREATEST(VAL_DIV, VAL_RES, VAL_COM, VAL_COBRANCA),
                            COM_CONTATO,
                            COB_CONTATO))) AS CONTATO
      ,ENDERECO_ELETRONICO
      ,EMAIL2
  FROM (SELECT CLIENTE_DIVERSO.COD_CLIENTE
              , CLIENTE_DIVERSO.UF AS DIV_UF
              ,NVL(CIDADES_DIV.DESCRICAO, CLIENTE_DIVERSO.CIDADE) AS DIV_CID
              ,CLIENTE_DIVERSO.BAIRRO AS DIV_BAIRRO
              ,CLIENTE_DIVERSO.ENDERECO AS DIV_RUA
              ,CLIENTE_DIVERSO.COMPLEMENTO AS DIV_COMP
              ,CLIENTE_DIVERSO.CEP AS DIV_CEP
              ,CLIENTE_DIVERSO.CONTATO AS DIV_CONTATO
              ,CLIENTES.UF_RES AS RES_UF
              ,CIDADES_RES.DESCRICAO AS RES_CID
              ,CLIENTES.BAIRRO_RES AS RES_BAIRRO
              ,CLIENTES.CEP_RES AS RES_CEP
              ,CLIENTES.RUA_RES AS RES_RUA
              ,CLIENTES.COMPLEMENTO_RES AS RES_COMP
              ,CLIENTES.CONTATO_RES AS RES_CONTATO
              ,CLIENTES.UF_COM AS COM_UF
              ,CIDADES_COM.DESCRICAO AS COM_CID
              ,CLIENTES.BAIRRO_COM AS COM_BAIRRO
              ,CLIENTES.CEP_COM AS COM_CEP
              ,CLIENTES.RUA_COM AS COM_RUA
              ,CLIENTES.COMPLEMENTO_COM AS COM_COMP
              ,CLIENTES.CONTATO_COM AS COM_CONTATO
              ,CLIENTES.UF_COBRANCA AS COB_UF
              ,CIDADES_COBRANCA.DESCRICAO AS COB_CID
              ,CLIENTES.BAIRRO_COBRANCA AS COB_BAIRRO
              ,CLIENTES.CEP_COBRANCA AS COB_CEP
              ,CLIENTES.RUA_COBRANCA AS COB_RUA
              ,CLIENTES.COMPLEMENTO_COBRANCA AS COB_COMP
              ,CLIENTES.CONTATO_COBRANCA AS COB_CONTATO
              ,(SIGN(NVL(LENGTH(CLIENTE_DIVERSO.UF), 0)) +
               SIGN(NVL(LENGTH(CLIENTE_DIVERSO.COD_CIDADES),
                         NVL(LENGTH(CLIENTE_DIVERSO.CIDADE), 0))) +
               SIGN(NVL(LENGTH(CLIENTE_DIVERSO.ENDERECO), 0))) * 4 + 0 AS VAL_DIV
              ,(SIGN(NVL(LENGTH(CLIENTES.UF_RES), 0)) +
               SIGN(NVL(LENGTH(CLIENTES.COD_CID_RES), 0)) +
               SIGN(NVL(LENGTH(CLIENTES.RUA_RES), 0))) * 4 +
               DECODE('F', 'J', 1, 3) AS VAL_RES
              ,(SIGN(NVL(LENGTH(CLIENTES.UF_COM), 0)) +
               SIGN(NVL(LENGTH(CLIENTES.COD_CID_COM), 0)) +
               SIGN(NVL(LENGTH(CLIENTES.RUA_COM), 0))) * 4 +
               DECODE('F', 'J', 3, 2) AS VAL_COM
              ,(SIGN(NVL(LENGTH(CLIENTES.UF_COBRANCA), 0)) +
               SIGN(NVL(LENGTH(CLIENTES.COD_CID_COBRANCA), 0)) +
               SIGN(NVL(LENGTH(CLIENTES.RUA_COBRANCA), 0))) * 4 +
               DECODE('F', 'J', 2, 1) AS VAL_COBRANCA
              ,CLIENTES.ENDERECO_ELETRONICO
              ,NVL(CLIENTES.EMAIL2,CLIENTES.EMAIL_NFE) AS EMAIL2
          FROM CLIENTE_DIVERSO
              ,CLIENTES
              ,CIDADES         CIDADES_DIV
              ,CIDADES         CIDADES_COM
              ,CIDADES         CIDADES_RES
              ,CIDADES         CIDADES_COBRANCA
              ,DADOS_FISICOS
              ,Q_OS_AGENDA
         WHERE CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
           AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES(+)
           AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES(+)
           AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES(+)
           AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES(+)
           AND CLIENTES.COD_CLIENTE = DADOS_FISICOS.COD_CLIENTE(+)
         AND CLIENTE_DIVERSO.COD_CLIENTE = Q_OS_AGENDA.COD_CLIENTE
        )
)

SELECT (Q_OS_AGENDA.DATA_ABRIDA ||' - '|| TRUNC_HORA_ABRIDA) AS AGENDA_DATA_ABRIDA,
	   Q_OS_AGENDA.COD_OS_AGENDA AS AGENDA_COD_OS_AGENDA,
	   (Q_OS_AGENDA.DATA_ULT_IMP || ' - ' || Q_OS_AGENDA.HORA_ULT_IMP) AS AGENDA_DATA_ULT_IMP,
	   (Q_OS_AGENDA.DATA_ATUAL_IMP || ' - ' || Q_OS_AGENDA.HORA_ATUAL_IMP) AS AGENDA_DATA_ATUAL_IMP,
	   Q_OS_AGENDA.DATA_CONFIRMADA AS AGENDA_DATA_CONFIRMADA,
	   Q_OS_AGENDA.HORA_CONFIRMADA AS AGENDA_HORA_CONFIRMADA,
	   Q_OS_AGENDA.RESPONSAVEL_CONFIRMADA AS AGENDA_RESPONSAVEL_CONFIRMADA,
	   Q_OS_AGENDA.DATA_RECEBIDA AS AGENDA_DATA_RECEBIDA,
	   Q_OS_AGENDA.HORA_RECEBIDA AS AGENDA_HORA_RECEBIDA,
	   Q_OS_AGENDA.PLACA AS AGENDA_PLACA,
	   Q_OS_AGENDA.DATA_AGENDADA AS AGENDA_DATA_AGENDADA,
	   Q_OS_AGENDA.AGENDADOR AS AGENDA_AGENDADOR,
	   Q_OS_AGENDA.CONSULTOR AS AGENDA_CONSULTOR,
	   Q_OS_AGENDA.TRUNC_DATA_PREVFIM AS AGENDA_TRUNC_DATA_PREVFIM,
	   Q_OS_AGENDA.TRUNC_HORA_PREVFIM AS AGENDA_TRUNC_HORA_PREVFIM,
	   Q_OS_AGENDA.DESCRICAO_PRISMA AS AGENDA_DESCRICAO_PRISMA,
	   Q_OS_AGENDA.AG_ATIVO AS AGENDA_AG_ATIVO,
	   Q_OS_AGENDA.LAVAR_VEICULO  AS AGENDA_LAVAR_VEICULO,
	   Q_OS_AGENDA.CLIENTE_AGUARDA AS AGENDA_CLIENTE_AGUARDA,
	   Q_OS_AGENDA.EH_FIAT_PROFISSIONAL AS AGENDA_EH_FIAT_PROFISSIONAL,
	   Q_OS_AGENDA.EH_RETORNO AS AGENDA_EH_RETORNO,
	   Q_OS_AGENDA.LEVAR_PECAS_SUBSTITUIDAS AS AGENDA_LEVAR_PECAS_SUBSTDA,
	   Q_OS_AGENDA.VEICULO_PLATAFORMA AS AGENDA_VEICULO_PLATAFORMA,
	   Q_OS_AGENDA.COD_MARCA AS AGENDA_COD_MARCA,
	   Q_OS_AGENDA.CLIENTE_NOME AS NOME_CLIENTE,
	   Q_CLIENTE.COD_CLIENTE as CLIENTE_COD_CLIENTE,
	   Q_CLIENTE.RUA AS CLIENTE_RUA,
	   Q_CLIENTE.CEP AS CLIENTE_CEP,
	   Q_CLIENTE.BAIRRO AS CLIENTE_BAIRRO,
	   Q_CLIENTE.CIDADE AS CLIENTE_CIDADE,
	   Q_OS_AGENDA.CLIENTE_CGC_CPF AS CLIENTE_CGC_CPF,
	   Q_OS_AGENDA.CLIENTE_RG AS CLIENTE_RG,
	   Q_OS_AGENDA.CLIENTE_FONE_COM AS CLIENTE_FONE_COM,
	   Q_OS_AGENDA.CLIENTE_FONE_RES AS CLIENTE_FONE_RES,
	   Q_OS_AGENDA.CLIENTE_FONE_FAX AS CLIENTE_FONE_FAX,
	   Q_OS_AGENDA.CLIENTE_TELEFONE_CEL AS CLIENTE_TELEFONE_CEL,
	   Q_CLIENTE.ENDERECO_ELETRONICO AS CLIENTE_ENDERECO_ELETRONICO,
	   Q_OS_AGENDA.DESCRICAO_PRODUTO AS AGENDA_DESCRICAO_PRODUTO,
	   Q_OS_AGENDA.DESCRICAO_MODELO AS AGENDA_DESCRICAO_MODELO,
	   Q_OS_AGENDA.CHASSI AS AGENDA_CHASSI,
	   Q_OS_AGENDA.ANO AS AGENDA_ANO,
       Q_OS_AGENDA.COMBUSTIVEL AS AGENDA_COMBUSTIVEL,
	   Q_OS_AGENDA.COR_EXTERNA AS AGENDA_COR_EXTERNA,
	   Q_OS_AGENDA.NUMERO_MOTOR AS AGENDA_NUMERO_MOTOR,
	   Q_OS_AGENDA.KM AS AGENDA_KM,
	   QRYDADOSVEIC.DATA_COMPRA AS AGENDA_DATA_COMPRA,
	   QRYDADOSVEIC.CONCESSIONARIA AS AGENDA_CONCESSIONARIA,
	   QRYDADOSVEIC.VENDEDOR AS AGENDA_VENDEDOR,
	   Q_HISTORICO.HISTORICO,
	   Q_OS_AGENDA.OBSERVACOES,
	   Q_OS_AGENDA.TEXTO
  FROM Q_OS_AGENDA, Q_HISTORICO, QRYDADOSVEIC, Q_CLIENTE
 WHERE Q_OS_AGENDA.COD_EMPRESA = Q_HISTORICO.COD_EMPRESA(+)
   AND Q_OS_AGENDA.CHASSI = Q_HISTORICO.CHASSI(+)
   AND Q_OS_AGENDA.CHASSI = QRYDADOSVEIC.CHASSI (+)
   AND Q_OS_AGENDA.COD_CLIENTE = Q_CLIENTE.COD_CLIENTE (+)]]>
	</queryString>
	<field name="AGENDA_DATA_ABRIDA" class="java.lang.String"/>
	<field name="AGENDA_COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="AGENDA_DATA_ULT_IMP" class="java.lang.String"/>
	<field name="AGENDA_DATA_ATUAL_IMP" class="java.lang.String"/>
	<field name="AGENDA_DATA_CONFIRMADA" class="java.lang.String"/>
	<field name="AGENDA_HORA_CONFIRMADA" class="java.lang.String"/>
	<field name="AGENDA_RESPONSAVEL_CONFIRMADA" class="java.lang.String"/>
	<field name="AGENDA_DATA_RECEBIDA" class="java.lang.String"/>
	<field name="AGENDA_HORA_RECEBIDA" class="java.lang.String"/>
	<field name="AGENDA_PLACA" class="java.lang.String"/>
	<field name="AGENDA_DATA_AGENDADA" class="java.sql.Timestamp"/>
	<field name="AGENDA_AGENDADOR" class="java.lang.String"/>
	<field name="AGENDA_CONSULTOR" class="java.lang.String"/>
	<field name="AGENDA_TRUNC_DATA_PREVFIM" class="java.sql.Timestamp"/>
	<field name="AGENDA_TRUNC_HORA_PREVFIM" class="java.lang.String"/>
	<field name="AGENDA_DESCRICAO_PRISMA" class="java.lang.String"/>
	<field name="AGENDA_AG_ATIVO" class="java.lang.String"/>
	<field name="AGENDA_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="AGENDA_CLIENTE_AGUARDA" class="java.lang.String"/>
	<field name="AGENDA_EH_FIAT_PROFISSIONAL" class="java.lang.String"/>
	<field name="AGENDA_EH_RETORNO" class="java.lang.String"/>
	<field name="AGENDA_LEVAR_PECAS_SUBSTDA" class="java.lang.String"/>
	<field name="AGENDA_VEICULO_PLATAFORMA" class="java.lang.String"/>
	<field name="AGENDA_COD_MARCA" class="java.lang.Double"/>
	<field name="NOME_CLIENTE" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLIENTE" class="java.lang.Double"/>
	<field name="CLIENTE_RUA" class="java.lang.String"/>
	<field name="CLIENTE_CEP" class="java.lang.String"/>
	<field name="CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="CLIENTE_RG" class="java.lang.String"/>
	<field name="CLIENTE_FONE_COM" class="java.lang.String"/>
	<field name="CLIENTE_FONE_RES" class="java.lang.String"/>
	<field name="CLIENTE_FONE_FAX" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_CEL" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="AGENDA_DESCRICAO_PRODUTO" class="java.lang.String"/>
	<field name="AGENDA_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="AGENDA_CHASSI" class="java.lang.String"/>
	<field name="AGENDA_ANO" class="java.lang.String"/>
	<field name="AGENDA_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="AGENDA_COR_EXTERNA" class="java.lang.String"/>
	<field name="AGENDA_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="AGENDA_KM" class="java.lang.Double"/>
	<field name="AGENDA_DATA_COMPRA" class="java.sql.Timestamp"/>
	<field name="AGENDA_CONCESSIONARIA" class="java.lang.String"/>
	<field name="AGENDA_VENDEDOR" class="java.lang.String"/>
	<field name="HISTORICO" class="java.lang.String"/>
	<field name="OBSERVACOES" class="java.lang.String"/>
	<field name="TEXTO" class="java.lang.String"/>
	<variable name="TOTAL_PAGES" class="java.lang.Double" resetType="None" incrementType="Report" calculation="Count">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<variable name="TOTAL_SERVICOS" class="java.lang.Double">
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="TOTAL_DESCONTO_SERVICOS" class="java.lang.Double">
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="TOTAL_PECAS" class="java.lang.Double">
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="TOTAL_DESCONTO_PECAS" class="java.lang.Double">
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="109">
			<frame>
				<reportElement x="0" y="55" width="468" height="54" uuid="c99f7e83-f790-4972-8fd8-aa6afb64a1c7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="1" width="107" height="22" uuid="8da84315-a51e-4346-804f-f8d10042b547">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[DATA DE EMISSÃO]]></text>
				</staticText>
				<staticText>
					<reportElement x="107" y="1" width="180" height="22" uuid="22617e5d-1207-40cc-9535-d31606c66c3d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[PRÉ-ORDEM DE SERVIÇO]]></text>
				</staticText>
				<staticText>
					<reportElement x="287" y="1" width="88" height="22" uuid="f0e152de-0de8-4eb1-a990-6d9bec6a5340">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[IMPRESSÃO]]></text>
				</staticText>
				<staticText>
					<reportElement x="375" y="1" width="91" height="22" uuid="1f9a92e4-e1f6-4a0f-a90e-b031d4203502">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[REIMPRESSÃO]]></text>
				</staticText>
				<textField>
					<reportElement x="0" y="23" width="107" height="22" uuid="0b5a0a17-73d1-41d8-8c5e-ff8651d2db47">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_DATA_ABRIDA}]]></textFieldExpression>
				</textField>
				<textField pattern="#.###;(#.###)">
					<reportElement mode="Opaque" x="107" y="23" width="180" height="22" backcolor="#C0C0C0" uuid="a69d4513-aa49-4b9f-8ede-f31b49b77181">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="15" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_COD_OS_AGENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="287" y="23" width="88" height="22" uuid="3117ec66-aceb-4206-a352-3fc26d8eae9d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_DATA_ABRIDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="375" y="23" width="91" height="22" uuid="a3ee5b06-a0ec-4497-b0dc-67c52514d058">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_DATA_ATUAL_IMP}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="468" y="55" width="87" height="54" uuid="7c152e33-13a5-44ba-b18a-6f543636d6d1">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="14" y="12" width="61" height="11" uuid="679a7ebb-acb1-4164-8574-ff8e2a6f089c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Versão: 2.1]]></text>
				</staticText>
				<staticText>
					<reportElement x="13" y="30" width="25" height="11" uuid="9fbc4edf-417e-4e85-97ff-f0a7342c40db">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Página:]]></text>
				</staticText>
				<textField evaluationTime="Page">
					<reportElement x="38" y="30" width="15" height="11" uuid="479e5811-ea16-4bab-992a-4ece9810ccf8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="57" y="30" width="15" height="11" uuid="cb8033f0-f7ae-49ef-a3ba-e717a9e02745">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="53" y="30" width="4" height="11" uuid="9a2bff87-4a47-4a8c-9aba-4081c7a5230a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[/]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="1" width="555" height="54" uuid="a1bd9972-5e23-4aa1-be40-220f61b74ce7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport>
					<reportElement x="0" y="0" width="555" height="54" uuid="d5695af6-92cd-4d98-93fc-c61ff6b8a3f4">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_MARCA">
						<subreportParameterExpression><![CDATA[$F{AGENDA_COD_MARCA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "PreOrdemFCASubCabecalho.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="236">
			<frame>
				<reportElement x="468" y="0" width="87" height="41" uuid="67fd4a36-4183-4fa3-a28d-7eb72c6e1828">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="86" height="13" uuid="1d7e7d11-3de4-4a21-8cb0-3e9d9b3415ca">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[PRISMA]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="4" y="16" width="80" height="13" backcolor="#C0C0C0" uuid="bdac05dd-0092-41b0-b018-f3256fbb377f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="4" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_DESCRICAO_PRISMA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="351" y="0" width="117" height="41" uuid="5ee21c71-306e-4da8-9bca-337f6fb4afb2">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="117" height="13" uuid="6d4582e5-d7cb-4aa0-acac-785f957bc8bd">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[PREVISÃO DE ENTREGA]]></text>
				</staticText>
				<textField pattern="d/M/yyyy" isBlankWhenNull="true">
					<reportElement x="12" y="19" width="56" height="15" uuid="09069d26-d20a-432b-bb9e-11cbee9078d3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_TRUNC_DATA_PREVFIM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="70" y="19" width="35" height="15" uuid="f80af68b-1f68-43aa-969a-59d44c318aa8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_TRUNC_HORA_PREVFIM}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="0" width="351" height="41" uuid="40ae0780-cba6-4ad7-9e96-51acb1133134">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="56" height="13" uuid="b2c3a249-c2db-4289-9140-ade39b173f37">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[PLACA]]></text>
				</staticText>
				<staticText>
					<reportElement x="56" y="0" width="81" height="13" uuid="3f16914b-20d6-44c8-ab7f-08ebd2dc92b0">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[AGENDADO]]></text>
				</staticText>
				<staticText>
					<reportElement x="137" y="0" width="96" height="13" uuid="abd9bf41-e6fc-412e-88ed-920fa79a6ce8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[AGENDADOR]]></text>
				</staticText>
				<staticText>
					<reportElement x="233" y="0" width="118" height="13" uuid="b6acc054-474b-4474-af33-245724b44798">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[CONSULTOR DE SERVIÇO]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="0" y="13" width="56" height="28" backcolor="#C0C0C0" uuid="093c1672-2c70-4cb0-8dcc-1e1195b3ada8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="56" y="13" width="81" height="28" backcolor="#C0C0C0" uuid="5c66b8fc-4002-4cb0-a187-3827eafb30e1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_DATA_AGENDADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="137" y="13" width="96" height="28" backcolor="#C0C0C0" uuid="6186b4b1-8762-45d2-a281-faa8753644e5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_AGENDADOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="233" y="13" width="118" height="28" backcolor="#C0C0C0" uuid="f8db4313-c169-40c3-bd9c-0d07f83d9097">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_CONSULTOR}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="41" width="555" height="72" uuid="43f2fbbb-3df6-4123-af89-e220cc6c9f4f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="2" y="3" width="42" height="11" uuid="d627739b-85a8-4a3a-817e-330e74bf1de1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[AG. Ativo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="47" y="3" width="67" height="11" uuid="087dbf98-d5cd-40ff-86bd-15a8ef7ee1f3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Lavar o Veículo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="117" y="3" width="67" height="11" uuid="eab0b99f-ba6a-4c89-a102-f97fe5e2ec58">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente aguarda: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="187" y="3" width="94" height="11" uuid="9ab56bcd-1859-4de5-9ea0-5ae0aa0df22b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente Fiat Profissional: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="287" y="3" width="67" height="11" uuid="2a4b8916-a0c7-426f-b161-c4357e252d38">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Veículo Retorno: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="360" y="3" width="138" height="11" uuid="d0f38373-af7b-42c3-9a1f-18771bcacbdb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Levar Peças substituidas do veículo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="503" y="3" width="44" height="11" uuid="52bd3599-d07a-4b45-b6d6-7bf6a709b88f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Rebocado: ]]></text>
				</staticText>
				<textField>
					<reportElement stretchType="ContainerHeight" x="16" y="17" width="9" height="9" uuid="74330df6-ccfc-4b9a-874a-b51ebd1d4b1e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_AG_ATIVO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="76" y="17" width="9" height="9" uuid="e0efc2f4-1bbd-444b-8e68-a83ea09dd823">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_LAVAR_VEICULO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="146" y="17" width="9" height="9" uuid="cf2fc57c-1521-40cd-b016-0c4161528881">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_CLIENTE_AGUARDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="229" y="17" width="9" height="9" uuid="f31a9b4e-afef-4c8d-9ed0-25127d22397c">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_EH_FIAT_PROFISSIONAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="315" y="17" width="9" height="9" uuid="72542d7f-f2e5-463d-a45a-09a60ab8169e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_EH_RETORNO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="424" y="17" width="9" height="9" uuid="7bf67b0e-ec62-47e7-8662-c82650050922">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_LEVAR_PECAS_SUBSTDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="520" y="17" width="9" height="9" uuid="099c0edb-dd1c-4f1b-88d8-f763e8759077">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_VEICULO_PLATAFORMA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="32" width="106" height="11" uuid="dc3789da-4fd2-4155-952b-bd5e60471322">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Confirma agendamento:]]></text>
				</staticText>
				<staticText>
					<reportElement x="250" y="32" width="22" height="11" uuid="2875cd61-5486-47a0-99a4-4662c29a2669">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Hora:]]></text>
				</staticText>
				<textField>
					<reportElement x="272" y="32" width="37" height="11" uuid="498245b3-ebaa-4958-8a5f-82da752f70f3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_HORA_CONFIRMADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="380" y="32" width="167" height="11" uuid="7b84f330-a505-43eb-b7c3-fc4e5ed24cab">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_RESPONSAVEL_CONFIRMADA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="324" y="32" width="56" height="11" uuid="96e05594-b8ac-4b35-baee-b40ba70e4ceb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável:]]></text>
				</staticText>
				<staticText>
					<reportElement x="147" y="32" width="23" height="11" uuid="adc1c7e7-9f46-4d3d-8b41-22e58066ff13">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField pattern="">
					<reportElement x="170" y="32" width="60" height="11" uuid="d5738811-7f8c-4d96-ac9f-7f58b03811f4">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_DATA_CONFIRMADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="272" y="45" width="37" height="11" uuid="92c9608c-1010-4714-9e17-9eabd2abd26d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_HORA_RECEBIDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="250" y="45" width="22" height="11" uuid="5c0c1a4f-efa2-447f-a8dc-6288b5eb05fa">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Hora:]]></text>
				</staticText>
				<staticText>
					<reportElement x="147" y="45" width="23" height="11" uuid="0231ebb2-15d8-4432-a96b-7af9fdf69e52">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField pattern="">
					<reportElement x="170" y="45" width="60" height="11" uuid="441d54e4-8e23-4174-99c9-2904b1c68d3d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_DATA_RECEBIDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="45" width="106" height="11" uuid="d6d052bf-80f2-416b-a114-1bdc7b8413e7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Pedido Peças Especiais:]]></text>
				</staticText>
				<textField>
					<reportElement x="380" y="45" width="167" height="11" uuid="e7e7783d-4451-4b3c-9f30-a0e79827fd33">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="324" y="45" width="56" height="11" uuid="3d6cbf55-5dc0-46d5-9472-32e86b59fd7d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável:]]></text>
				</staticText>
				<textField>
					<reportElement x="272" y="58" width="37" height="11" uuid="09f7c677-8b1a-4cbb-ae15-3237854c66f9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_HORA_RECEBIDA}]]></textFieldExpression>
				</textField>
				<textField pattern="">
					<reportElement x="170" y="58" width="60" height="11" uuid="9314d09f-15a1-4e62-9569-683bb24e66a8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_DATA_RECEBIDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="58" width="106" height="11" uuid="b9218e64-2de1-4198-a53d-789a580e8391">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Peças Recebidas:]]></text>
				</staticText>
				<staticText>
					<reportElement x="250" y="58" width="22" height="11" uuid="d10048fe-cb2a-47ef-9ffc-304aa16ca992">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Hora:]]></text>
				</staticText>
				<staticText>
					<reportElement x="147" y="58" width="23" height="11" uuid="7ebdc08a-89b7-48d8-963f-53d424ffd1d6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField>
					<reportElement x="380" y="58" width="167" height="11" uuid="a4189e66-14be-42f7-bd9d-aa4fd12a3282">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="324" y="58" width="56" height="11" uuid="f20ed943-07fb-4826-9435-fc68c9dde525">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável:]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="113" width="555" height="69" uuid="fd53bd83-1cb8-49c2-afd2-e25f797eb559">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement mode="Opaque" x="0" y="0" width="555" height="20" backcolor="#C0C0C0" uuid="44e42e43-3636-4bc7-9413-a911a4173424">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement x="3" y="2" width="74" height="18" uuid="80f30f15-8c8f-45da-9d76-253ef2965fc9">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<text><![CDATA[código/Cliente:]]></text>
					</staticText>
					<textField pattern="#.###;(#.###-)">
						<reportElement x="78" y="2" width="90" height="18" uuid="6d9b9088-f4a5-43e1-b7ac-767a8b2bda1e">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{CLIENTE_COD_CLIENTE}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="169" y="2" width="6" height="18" uuid="db7ec116-2790-4f16-924e-f4f7621fd106">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<text><![CDATA[/]]></text>
					</staticText>
					<textField>
						<reportElement x="176" y="2" width="340" height="18" uuid="8758fe4f-0e51-43a1-9d6f-c7041e202c05">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{NOME_CLIENTE}]]></textFieldExpression>
					</textField>
				</frame>
				<staticText>
					<reportElement x="4" y="22" width="42" height="11" uuid="624e2355-9da9-4f01-9cb3-8a0babe2ecf4">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Endereço: ]]></text>
				</staticText>
				<textField>
					<reportElement x="46" y="22" width="230" height="11" uuid="f5d754ee-9f14-40e1-89e6-1fb2c47b4a79">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_RUA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="33" width="46" height="11" uuid="fba82c4b-2624-40e7-8986-59b8595fa785">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade/UF: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="4" y="44" width="95" height="11" uuid="ca05a1c9-7ccf-4dc0-85b2-7a68d2f69533">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Telefone para contato:]]></text>
				</staticText>
				<staticText>
					<reportElement x="4" y="55" width="35" height="11" uuid="f0deea67-4084-4ff5-9506-96b713e06d67">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[E-mail: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="276" y="22" width="29" height="11" uuid="9c88e0ed-9673-4c52-bcf2-559dd34543fd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement x="275" y="33" width="49" height="11" uuid="175b3488-3b0e-41a1-a216-ebbc4635146e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CPF/CNPJ:]]></text>
				</staticText>
				<staticText>
					<reportElement x="416" y="22" width="47" height="11" uuid="e1487e39-5078-4a27-828e-ead448788659">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[BAIRRO: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="416" y="33" width="29" height="11" uuid="cea4ec4a-3142-4f75-8d2b-31271711bc47">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[RG: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="99" y="44" width="31" height="11" uuid="3397b359-8dcf-4857-bd56-57bdc8e93e29">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Celular:]]></text>
				</staticText>
				<staticText>
					<reportElement x="204" y="44" width="48" height="11" uuid="4cd58fec-a579-4a45-88ef-1644f0cc25d0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Residencial: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="325" y="44" width="42" height="11" uuid="af67099d-0f8f-426e-b847-6af1949664ef">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Comercial:]]></text>
				</staticText>
				<staticText>
					<reportElement x="443" y="44" width="30" height="11" uuid="f9a96c13-3f2f-477f-ad3d-9b341f8ceeaf">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Outros: ]]></text>
				</staticText>
				<textField>
					<reportElement x="50" y="33" width="221" height="11" uuid="2664cdd7-3841-4318-af56-f5d3e7cecb66">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="305" y="22" width="111" height="11" uuid="53cee5fd-0f7c-4b88-ba5a-cb9aed51bf93">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="324" y="33" width="92" height="11" uuid="9caf355c-b8bc-4f62-9cdc-65f32af4dacf">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CGC_CPF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="463" y="22" width="75" height="11" uuid="8e1d1307-3b17-42e2-a2da-8270e9d50671">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="445" y="33" width="75" height="11" uuid="e7b5dd15-a044-465e-820c-22e27ff9221f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_RG}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="473" y="44" width="76" height="11" uuid="9d3b945b-2aeb-4d5a-9692-d9b29e80d3b6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_FAX}.length() > 6.0 ? $F{CLIENTE_FONE_FAX}: " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="367" y="44" width="76" height="11" uuid="7b65b83a-a435-410d-b532-bef72b820ec0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_COM}.length() > 6.0 ? $F{CLIENTE_FONE_COM}: " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="252" y="44" width="72" height="11" uuid="6422717f-cc16-4b27-b5cc-e2390e40311e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_RES}.length() > 6.0 ? $F{CLIENTE_FONE_RES}: " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="130" y="44" width="74" height="11" uuid="461fd2f4-109a-48eb-b368-3b92d16a3bd9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_TELEFONE_CEL}.length() > 6.0 ? $F{CLIENTE_TELEFONE_CEL}: " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="39" y="55" width="274" height="11" uuid="9adfaf8f-8405-4c45-ae98-a83924948e3b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="182" width="555" height="54" uuid="78a618ae-ab40-4e0f-a6a5-4bbcba4e6460">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement mode="Opaque" x="1" y="1" width="300" height="20" backcolor="#C0C0C0" uuid="8bdc86d8-9c99-42e4-b7bd-2b007e695df8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textField>
						<reportElement x="3" y="0" width="184" height="18" uuid="bed784e5-b5bf-4873-a9a7-704b7f76b4a9">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA["MVS/Veículo: " +  $F{AGENDA_DESCRICAO_PRODUTO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="188" y="0" width="92" height="18" uuid="cf57152f-247a-47d9-804b-365af3058bc0">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA["Placa: " + $F{AGENDA_PLACA}]]></textFieldExpression>
					</textField>
				</frame>
				<staticText>
					<reportElement x="309" y="5" width="32" height="11" uuid="2a19994f-15f5-4ff7-a95f-c383943bfe03">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Chassi: ]]></text>
				</staticText>
				<textField>
					<reportElement x="343" y="5" width="100" height="11" uuid="b88d112e-fb2c-44b7-82a2-ef1a372a082f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_CHASSI}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="445" y="5" width="52" height="11" uuid="19e19611-5cd9-46ba-8fe4-f31e917b9312">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Ano/Modelo: ]]></text>
				</staticText>
				<textField>
					<reportElement x="497" y="5" width="52" height="11" uuid="b54da8ae-8fad-4aee-8ba8-5b524bfd3e5a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_ANO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="24" y="25" width="100" height="11" uuid="79e73f3d-c072-41c6-b5d0-d183b9e2c198">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="6" y="25" width="18" height="11" uuid="5bde1429-75b2-4a98-b699-9176fb098458">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cor: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="36" width="92" height="11" uuid="9a060068-53f2-46c7-a12f-1db70e6b8b16">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Distribuidor Vendedor: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="130" y="25" width="42" height="11" uuid="29f47cc2-a1c6-4f96-8435-be2ba93047b8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº Motor: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="244" y="36" width="42" height="11" uuid="1fc65311-b1d0-4305-a855-26fedfafdc78">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Vendedor: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="294" y="25" width="48" height="11" uuid="78c98a4a-a445-4b94-bd67-2e2d6c5f8175">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Km Atual: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="413" y="25" width="56" height="11" uuid="33414441-23fd-4e86-9029-9182664389cb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Combustivel: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="420" y="36" width="50" height="11" uuid="857854c1-338e-4247-ab0b-60c518b651e5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data Venda: ]]></text>
				</staticText>
				<textField>
					<reportElement x="172" y="25" width="100" height="11" uuid="de2dcd39-ce4a-42d9-bda9-aa90f64ae3ae">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_NUMERO_MOTOR}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement x="342" y="25" width="60" height="11" uuid="08b1f74f-c797-42e3-b15c-2398dc6f7a29">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_KM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="98" y="36" width="132" height="11" uuid="f3fdd140-9f5b-4093-a85a-b7c175d4c101">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_CONCESSIONARIA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="286" y="36" width="127" height="11" uuid="f8c368ee-eae8-48d7-9e56-4b9f748c1749">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_VENDEDOR}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="470" y="36" width="74" height="11" uuid="9ae880a5-e313-4977-b578-36f025b15e4c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_DATA_COMPRA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="469" y="25" width="51" height="11" uuid="c0ae3b52-54c4-4f44-a5ec-ab9b7e5b738f"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{AGENDA_COMBUSTIVEL} < 19 ? "":
$F{AGENDA_COMBUSTIVEL} < 39 ? "X" :
$F{AGENDA_COMBUSTIVEL} < 59 ? "X   X":
$F{AGENDA_COMBUSTIVEL} < 79 ? "X   X   X":
 "X   X   X   X"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="476" y="34" width="1" height="2" uuid="fe167363-f632-4387-ab69-f22b28ca4ea1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="482" y="32" width="1" height="4" uuid="685a1447-486d-454d-b05d-9ee576984f38">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="488" y="34" width="1" height="2" uuid="341b046b-ed27-436e-a17c-0a450d630b8d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="494" y="30" width="1" height="6" uuid="86cf4a8b-a0cc-4731-beed-b9006cc9882a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="500" y="34" width="1" height="2" uuid="b44ad26e-2c5d-48ae-9410-a4c1d2e80b78">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="506" y="32" width="1" height="4" uuid="2d5dc407-489d-47be-b930-507fda45f4cb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="512" y="34" width="1" height="2" uuid="9c6f7419-b75d-4cd0-8f4f-de3e884a3f2a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
			</frame>
		</band>
		<band height="22" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<subreport>
				<reportElement x="0" y="0" width="555" height="22" isRemoveLineWhenBlank="true" uuid="8a210cec-d496-40c6-bb7d-36c36fdd724a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_OS_AGENDA">
					<subreportParameterExpression><![CDATA[$P{COD_OS_AGENDA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "PreOrdemFCASubReclamacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="22">
			<subreport>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="555" height="22" isRemoveLineWhenBlank="true" uuid="f8f17a62-1521-4c36-a8ce-367a751971c6">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_OS_AGENDA">
					<subreportParameterExpression><![CDATA[$P{COD_OS_AGENDA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<returnValue subreportVariable="TOTAL_SERVICOS" toVariable="TOTAL_SERVICOS"/>
				<returnValue subreportVariable="TOTAL_DESCONTO_SERVICOS" toVariable="TOTAL_DESCONTO_SERVICOS"/>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "PreOrdemFCASubServicos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="22">
			<subreport>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="555" height="22" isRemoveLineWhenBlank="true" uuid="bb9ff69d-7229-4416-96c1-df50190dca87">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_OS_AGENDA">
					<subreportParameterExpression><![CDATA[$P{COD_OS_AGENDA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<returnValue subreportVariable="TOTAL_PECAS" toVariable="TOTAL_PECAS"/>
				<returnValue subreportVariable="TOTAL_DESCONTO_PECAS" toVariable="TOTAL_DESCONTO_PECAS"/>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "PreOrdemFCASubPecas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="24">
			<frame>
				<reportElement x="0" y="0" width="555" height="24" backcolor="#E3E3E3" uuid="355f167e-93d3-4353-a9f3-44ad35f31a4a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Opaque" x="0" y="12" width="472" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="a616e816-3a76-4822-8def-2faba006d1c7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[VALOR TOTAL ESTIMADO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="472" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="e31c8628-5f35-4ce2-9ace-7c03939d95f7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Descontos]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="474" y="0" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="973fddbb-50c6-4cb0-aa01-8b20922d6262">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="474" y="12" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="ad14c977-5913-4558-99f4-dfd4eb2ed5a4">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement mode="Opaque" x="472" y="0" width="83" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="d2d427df-7ef1-4f4b-bd08-b1fc4f82dc3e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_DESCONTO_SERVICOS} + $V{TOTAL_DESCONTO_PECAS}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement mode="Opaque" x="472" y="12" width="83" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="abfb4d5d-bc4f-44a4-bac0-b74b0932447f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL_SERVICOS} + $V{TOTAL_PECAS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="474" y="1" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="rgba(0, 0, 0, 0.98039216)" uuid="4050b676-69eb-4490-8317-8a97e7dd2e43">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="474" y="13" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="rgba(0, 0, 0, 0.98039216)" uuid="699e774a-1687-4ba8-9224-28a925933cef">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
			</frame>
		</band>
		<band height="94">
			<frame>
				<reportElement x="0" y="0" width="555" height="94" uuid="7141d6f1-6b72-458c-b35f-be2be7c1f304">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="6" y="6" width="394" height="11" uuid="ccb42d2e-53bf-4671-914b-1670b44fd099">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Se o cliente levar as peças será responsável pelo descarte correto dos materiais retirado na concessionária.]]></text>
				</staticText>
				<textField>
					<reportElement x="111" y="17" width="140" height="11" uuid="0c0087f4-7a22-46d0-91c5-b9b21c9bf2a3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{HISTORICO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="6" y="17" width="104" height="11" uuid="70518d31-d1ac-4782-8843-d309fa50ed41">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Histórico de Serviço: OS(s)]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="30" width="54" height="11" uuid="16671f43-b7b6-479c-8919-076882d084ad">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Observações:]]></text>
				</staticText>
				<textField>
					<reportElement x="60" y="30" width="480" height="48" uuid="f4f7cd55-5506-4c67-8bf2-df86242f116d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OBSERVACOES}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="40">
			<frame>
				<reportElement x="0" y="0" width="555" height="40" uuid="de0351fd-033f-44a7-b07a-096096bb87c1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isStretchWithOverflow="true">
					<reportElement x="6" y="6" width="540" height="30" uuid="72ef307c-55a1-4885-8713-0f01c7b01865">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Justified" verticalAlignment="Top">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEXTO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
