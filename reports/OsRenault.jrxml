<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="emBranco" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\27369\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH Q_OS AS
 (SELECT OS.COD_EMPRESA,
         OS.STATUS_OS,
         OS.NUMERO_OS,
         ABS(OS.NUMERO_OS) AS ABS_OSNUM,
         OS.COD_CLIENTE,
         OS.COD_PRODUTO,
         OS.COD_MODELO,
         OS.TIPO_ENDERECO,
         OS.FRANQUIA,
         OS.TIPO,
         OS.VALIDADE,
         OS.OBSERVACAO,
         OS.ORCAMENTO,
         OS.EXTENDIDA,
         OS.SEGURADORA,
         OS.LIBERADO,
         OS.DATA_EMISSAO,
         OS.HORA_EMISSAO,
         OS.DATA_ENTREGA,
         OS.HORA_ENCERRADA,
         OS.DATA_ENCERRADA,
         OS.HORA_LIBERADO,
         OS.DATA_LIBERADO,
         OS.HORA_PROMETIDA,
         OS.DATA_PROMETIDA,
         OS.VALOR_SERVICOS_BRUTO,
         OS.VALOR_ITENS_BRUTO,
         OS.DESCONTOS_SERVICOS,
         OS.DESCONTOS_ITENS,
         NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL) AS INSCRICAO_ESTADUAL,
         (OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) TOTAL_OS_SERVICOS,
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS) TOTAL_OS_ITENS,
         (OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO) TOTAL_OS_BRUTO,
         (OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS) TOTAL_OS_DESCONTO,
         ((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) +
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS)) TOTAL_OS,
         OS.ORC_SERV_BRUTO,
         OS.ORC_ITEM_BRUTO,
         OS.ORC_SERV_DESCONTO,
         OS.ORC_ITEM_DESCONTO,
         (OS.ORC_SERV_BRUTO - OS.ORC_SERV_DESCONTO) TOTAL_ORC_SERVICOS,
         (OS.ORC_ITEM_BRUTO - OS.ORC_ITEM_DESCONTO) TOTAL_ORC_ITENS,
         (OS.ORC_SERV_BRUTO + OS.ORC_ITEM_BRUTO) TOTAL_ORC_BRUTO,
         (OS.ORC_SERV_DESCONTO + OS.ORC_ITEM_DESCONTO) TOTAL_ORC_DESCONTO,
         ((OS.ORC_SERV_BRUTO - OS.ORC_SERV_DESCONTO) +
         (OS.ORC_ITEM_BRUTO - OS.ORC_ITEM_DESCONTO)) TOTAL_ORC,
         OS.KM_INICIAL,
         OS.KM_FINAL,
         OS.DESLOCAMENTO,
         OS.COD_SEGURADORA,
         OS_DADOS_VEICULOS.ANO,
         OS_DADOS_VEICULOS.HORIMETRO,
         OS_DADOS_VEICULOS.PRISMA,
         OS_DADOS_VEICULOS.DATA_VENDA,
         OS_DADOS_VEICULOS.COMBUSTIVEL,
         OS_DADOS_VEICULOS.COR_EXTERNA,
         OS_DADOS_VEICULOS.PLACA,
         OS_DADOS_VEICULOS.KM,
         OS_DADOS_VEICULOS.CHASSI,
         OS_DADOS_VEICULOS.NUMERO_MOTOR,
         OS_DADOS_VEICULOS.NUMERO_CAMBIO,
         OS_DADOS_VEICULOS.SERIE,
         OS_DADOS_VEICULOS.BLINDADO,
         OS_DADOS_VEICULOS.NUMERO_RENAVAM,
         OS.PECA_USADA_FICA_CLIENTE,
         MOTORISTAS.NOME_DO_MOTORISTA,
         MOTORISTAS.DOCUMENTO DOCUMENTO_DO_MOTORISTA,
         OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO TIPO_DESCRICAO,
         NVL(OS_TIPOS_EMPRESAS.COD_CLIENTE, OS_TIPOS.COD_CLIENTE) AS TIPO_COD_CLIENTE,
         OS_TIPOS.GARANTIA,
         OS_TIPOS.INTERNO,
         EMPRESAS_USUARIOS.NOME_COMPLETO,
         PRODUTOS.DESCRICAO_PRODUTO || ' / ' ||
         PRODUTOS_MODELOS.DESCRICAO_MODELO DESC_PROD_MOD,
         PRODUTOS_MODELOS.LINHA,
         EMPRESAS.NOME NOME_EMPRESA,
         EMPRESAS.CGC EMPRESA_CGC,
         EMPRESAS.FACHADA EMPRESA_FACHADA,
         EMPRESAS.ESTADO EMPRESA_UF,
         EMPRESAS.CIDADE EMPRESA_CIDADE,
         EMPRESAS.BAIRRO EMPRESA_BAIRRO,
         EMPRESAS.COMPLEMENTO EMPRESA_CMPTO,
         EMPRESAS.RUA EMPRESA_RUA,
         EMPRESAS.FONE EMPRESA_FONE,
         EMPRESAS.FAX EMPRESA_FAX,
         EMPRESAS.CEP EMPRESA_CEP,
         EMPRESAS.INSCRICAO_ESTADUAL EMPRESA_INSCRICAO_ESTADUAL,
         UF_EMPRESA.DESCRICAO EMPRESA_ESTADO,
         CONCESSIONARIAS.NOME CONCESSIONARIA_NOME,
         CONCESSIONARIAS.UF CONCESSIONARIA_UF,
         CONCESSIONARIAS.CIDADE CONCESSIONARIA_CIDADE,
         CONCESSIONARIAS.BAIRRO CONCESSIONARIA_BAIRRO,
         CONCESSIONARIAS.ENDERECO CONCESSIONARIA_RUA,
         CONCESSIONARIAS.CEP CONCESSIONARIA_CEP,
         UF_CONCESSIONARIA.DESCRICAO CONCESSIONARIA_ESTADO,
         CLIENTE_DIVERSO.COD_TIPO_CLIENTE,
         NVL(OS.CLIENTE_RAPIDO, CLIENTE_DIVERSO.NOME) AS CLIENTE_NOME,
         'RG: ' || NVL(CLIENTE_DIVERSO.RG, '          ') AS CLIENTE_RG,
         DECODE(CLIENTE_DIVERSO.CPF,
                NULL,
                NVL(CLIENTE_DIVERSO.CGC, '          '),
                CLIENTE_DIVERSO.CPF) AS CLIENTE_CGC_CPF,
         
         DECODE(OS.TIPO_ENDERECO,
                '1',
                CLIENTE_DIVERSO.UF,
                '2',
                CLIENTES.UF_RES,
                '3',
                CLIENTES.UF_COM,
                '4',
                CLIENTES.UF_COBRANCA,
                '5',
                ENDERECO_POR_INSCRICAO.UF,
                NULL) CLIENTE_UF,
         DECODE(OS.TIPO_ENDERECO,
                '1',
                UF_DIVERSO.DESCRICAO,
                '2',
                UF_RES.DESCRICAO,
                '3',
                UF_COM.DESCRICAO,
                '4',
                UF_COBRANCA.DESCRICAO,
                '5',
                UF_INSCRICAO.DESCRICAO,
                NULL) CLIENTE_ESTADO,
         DECODE(OS.TIPO_ENDERECO,
                '1',
                CIDADES_DIV.DESCRICAO,
                '2',
                CIDADES_RES.DESCRICAO,
                '3',
                CIDADES_COM.DESCRICAO,
                '4',
                CIDADES_COBRANCA.DESCRICAO,
                '5',
                ENDERECO_POR_INSCRICAO.CIDADE,
                NULL) CLIENTE_CIDADE,
         DECODE(OS.TIPO_ENDERECO,
                '1',
                CLIENTE_DIVERSO.BAIRRO,
                '2',
                CLIENTES.BAIRRO_RES,
                '3',
                CLIENTES.BAIRRO_COM,
                '4',
                CLIENTES.BAIRRO_COBRANCA,
                '5',
                ENDERECO_POR_INSCRICAO.BAIRRO,
                NULL) CLIENTE_BAIRRO,
         DECODE(OS.TIPO_ENDERECO,
                '1',
                CLIENTE_DIVERSO.CEP,
                '2',
                CLIENTES.CEP_RES,
                '3',
                CLIENTES.CEP_COM,
                '4',
                CLIENTES.CEP_COBRANCA,
                '5',
                ENDERECO_POR_INSCRICAO.CEP,
                NULL) CLIENTE_CEP,
         DECODE(OS.TIPO_ENDERECO,
                '1',
                CLIENTE_DIVERSO.ENDERECO,
                '2',
                CLIENTES.RUA_RES,
                '3',
                CLIENTES.RUA_COM,
                '4',
                CLIENTES.RUA_COBRANCA,
                '5',
                ENDERECO_POR_INSCRICAO.RUA,
                NULL) CLIENTE_RUA,
         DECODE(OS.TIPO_ENDERECO,
                '1',
                CLIENTE_DIVERSO.COMPLEMENTO,
                '2',
                CLIENTES.COMPLEMENTO_RES,
                '3',
                CLIENTES.COMPLEMENTO_COM,
                '4',
                CLIENTES.COMPLEMENTO_COBRANCA,
                '5',
                ENDERECO_POR_INSCRICAO.COMPLEMENTO,
                NULL) CLIENTE_COMPLEMENTO,
         DECODE(OS.TIPO_ENDERECO,
                '1',
                NULL,
                '2',
                CLIENTES.FACHADA_RES,
                '3',
                CLIENTES.FACHADA_COM,
                '4',
                CLIENTES.FACHADA_COBRANCA,
                '5',
                ENDERECO_POR_INSCRICAO.FACHADA,
                NULL) CLIENTE_FACHADA,
         DECODE(SIGN(LENGTH(OS.CLIENTE_RAPIDO)),
                1,
                OS.CLIENTE_RAPIDO_FONE,
                DECODE(OS.TIPO_ENDERECO,
                       1,
                       CLIENTE_DIVERSO.FONE_CONTATO,
                       2,
                       CLIENTES.TELEFONE_RES,
                       3,
                       CLIENTES.TELEFONE_COM,
                       4,
                       CLIENTES.TELEFONE_COM,
                       5,
                       ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
                       NULL)) AS CLIENTE_FONE,
         DECODE(SIGN(LENGTH(OS.CLIENTE_RAPIDO)),
                1,
                NULL,
                DECODE(OS.TIPO_ENDERECO,
                       1,
                       CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
                       2,
                       CLIENTES.PREFIXO_RES,
                       3,
                       CLIENTES.PREFIXO_COM,
                       4,
                       CLIENTES.PREFIXO_COM,
                       5,
                       ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
                       NULL)) AS CLIENTE_PREFIXO,
         CLIENTES.TELEFONE_CEL,
         CLIENTES.PREFIXO_CEL,
         SYSDATE,
         OS.COD_SOCIO,
         OS.COD_BANCO,
         OS_TIPOS_EMPRESAS.IMPRIMIR_DADOS_FINANCIAMENTO,
         OS_TIPOS_EMPRESAS.IMPRIMIR_ASSINATURA,
         OS.QUEM_ABRIU
    FROM OS,
         EMPRESAS,
         EMPRESAS_USUARIOS,
         CLIENTE_DIVERSO,
         CLIENTES,
         CIDADES                CIDADES_RES,
         CIDADES                CIDADES_COM,
         CIDADES                CIDADES_COBRANCA,
         CIDADES                CIDADES_DIV,
         ENDERECO_POR_INSCRICAO,
         OS_TIPOS,
         OS_TIPOS_EMPRESAS,
         OS_DADOS_VEICULOS,
         CONCESSIONARIAS,
         PRODUTOS,
         PRODUTOS_MODELOS,
         UF                     UF_EMPRESA,
         UF                     UF_DIVERSO,
         UF                     UF_RES,
         UF                     UF_COM,
         UF                     UF_COBRANCA,
         UF                     UF_INSCRICAO,
         UF                     UF_CONCESSIONARIA,
         MOTORISTAS
   WHERE (OS.COD_EMPRESA = EMPRESAS.COD_EMPRESA)
     AND (OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE)
     AND (CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+))
     AND (OS.TIPO = OS_TIPOS.TIPO)
     AND (CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES(+))
     AND (CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES(+))
     AND (CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES(+))
     AND (CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES(+))
     AND (OS.NOME = EMPRESAS_USUARIOS.NOME)
     AND (OS.INSCRICAO_ESTADUAL =
         ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL(+))
     AND (OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE(+))
     AND (OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS(+))
     AND (OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA(+))
     AND (OS_DADOS_VEICULOS.COD_CONCESSIONARIA =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+))
     AND (OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO)
     AND (OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO)
     AND (OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO)
     AND (OS.CODIGO_MOTORISTA = MOTORISTAS.CODIGO_MOTORISTA(+))
     AND (EMPRESAS.ESTADO = UF_EMPRESA.UF(+))
     AND (CLIENTE_DIVERSO.UF = UF_DIVERSO.UF(+))
     AND (CLIENTES.UF_RES = UF_RES.UF(+))
     AND (CLIENTES.UF_COM = UF_COM.UF(+))
     AND (CLIENTES.UF_COBRANCA = UF_COBRANCA.UF(+))
     AND (ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF(+))
     AND (CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF(+))
     AND (OS.COD_EMPRESA = $P{COD_EMPRESA})
     AND (OS.NUMERO_OS = $P{NUMERO_OS})
     AND (OS.COD_EMPRESA = OS_TIPOS_EMPRESAS.COD_EMPRESA(+))
     AND (OS.TIPO = OS_TIPOS_EMPRESAS.TIPO(+))
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
  
  ),

Q_FABRICA_LOGO AS
 (SELECT FABRICA_LOGO.COD_EMPRESA, FABRICA_LOGO.LOGO
    FROM FABRICA_LOGO
   WHERE (FABRICA_LOGO.COD_EMPRESA = $P{COD_EMPRESA})),

Q_EMPRESA AS
 (SELECT NOME, COD_EMPRESA FROM EMPRESAS WHERE COD_EMPRESA = $P{COD_EMPRESA}),

Q_CALCULADOS AS

 (SELECT Q_OS.NUMERO_OS,
         Q_OS.COD_EMPRESA,
         
         TRIM(',' FROM TRIM(' ' FROM SUBSTR(Q_OS.CLIENTE_CIDADE, 30) || ', ' ||
                   SUBSTR(Q_OS.CLIENTE_ESTADO, 22) || ' ' ||
                   Q_OS.CLIENTE_UF)) AS CIDADE,
         
         TRIM(',' FROM(TRIM(' ' FROM SUBSTR(Q_OS.CLIENTE_RUA, 50) || ', ' ||
                        SUBSTR(Q_OS.CLIENTE_FACHADA, 20) || ' ' ||
                        SUBSTR(Q_OS.CLIENTE_COMPLEMENTO, 20)))) AS ENDERECO
    FROM Q_OS),
  
  Q_ASSINATURA AS
 (SELECT OS_AGENDA.NUMERO_OS,
         OS_AGENDA.COD_EMPRESA,
         OS_AGENDA.SIGNATURE AS ASSINTAURA
    FROM OS_AGENDA
   WHERE OS_AGENDA.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_EMPRESA = $P{COD_EMPRESA})

SELECT Q_OS.DATA_EMISSAO            AS Q_OS_DATA_EMISSAO,
       Q_OS.HORA_EMISSAO            AS Q_OS_HORA_EMISSAO,
       Q_OS.NOME_EMPRESA            AS Q_OS_NOME_EMPRESA,
       Q_OS.EMPRESA_RUA             AS Q_OS_EMPRESA_RUA,
       Q_OS.EMPRESA_FACHADA         AS Q_OS_EMPRESA_FACHADA,
       Q_OS.EMPRESA_BAIRRO          AS Q_OS_EMPRESA_BAIRRO,
       Q_OS.EMPRESA_CIDADE          AS Q_OS_EMPRESA_CIDADE,
       Q_OS.EMPRESA_CGC             AS Q_OS_EMPRESA_CGC,
       Q_OS.EMPRESA_FONE            AS Q_OS_EMPRESA_FONE,
       Q_OS.EMPRESA_FAX             AS Q_OS_EMPRESA_FAX,
       Q_OS.EMPRESA_UF              AS Q_OS_EMPRESA_UF,
       Q_OS.EMPRESA_CEP             AS Q_OS_EMPRESA_CEP,
       Q_OS.NUMERO_OS               AS Q_OS_NUMERO_OS,
       Q_OS.TIPO                    AS Q_OS_TIPO,
       Q_OS.CLIENTE_NOME            AS Q_OS_CLIENTE_NOME,
       Q_OS.CLIENTE_FONE            AS Q_OS_CLIENTE_FONE,
       Q_OS.CLIENTE_CEP             AS Q_OS_CLIENTE_CEP,
       Q_OS.DATA_VENDA              AS Q_OS_DATA_VENDA,
       Q_OS.INSCRICAO_ESTADUAL      AS Q_OS_INSCRICAO_ESTADUAL,
       Q_OS.SERIE                   AS Q_OS_SERIE,
       Q_OS.CLIENTE_CGC_CPF         AS Q_OS_CLIENTE_CGC_CPF,
       Q_OS.PLACA                   AS Q_OS_PLACA,
       Q_OS.DATA_PROMETIDA          AS Q_OS_DATA_PROMETIDA,
       Q_OS.CHASSI                  AS Q_OS_CHASSI,
       Q_OS.COR_EXTERNA             AS Q_OS_COR_EXTERNA,
       Q_OS.DESC_PROD_MOD           AS Q_OS_DESC_PROD_MOD,
       Q_OS.CLIENTE_BAIRRO          AS Q_OS_CLIENTE_BAIRRO,
       Q_OS.CLIENTE_PREFIXO         AS Q_OS_CLIENTE_PREFIXO,
       Q_OS.NUMERO_MOTOR            AS Q_OS_NUMERO_MOTOR,
       Q_OS.HORA_PROMETIDA          AS Q_OS_HORA_PROMETIDA,
       Q_OS.KM                      AS Q_OS_KM,
       Q_OS.NOME_COMPLETO           AS Q_OS_NOME_COMPLETO,
       Q_OS.PECA_USADA_FICA_CLIENTE AS Q_OS_PRESERVAR_PECAS_USADAS,
       Q_CALCULADOS.CIDADE          AS Q_CALCULADOS_CIDADE,
       Q_CALCULADOS.ENDERECO        AS Q_CALCULADOS_ENDECO,
       Q_ASSINATURA.ASSINTAURA                 AS Q_ASSINATURA_ASSINATURA

  FROM Q_OS, Q_EMPRESA, Q_FABRICA_LOGO, Q_CALCULADOS,Q_ASSINATURA
 WHERE Q_OS.COD_EMPRESA = Q_FABRICA_LOGO.COD_EMPRESA(+)
   AND Q_OS.COD_EMPRESA = Q_EMPRESA.COD_EMPRESA(+)
   AND Q_OS.COD_EMPRESA = Q_CALCULADOS.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_ASSINATURA.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_ASSINATURA.COD_EMPRESA(+)   
   AND Q_OS.NUMERO_OS = Q_CALCULADOS.NUMERO_OS]]>
	</queryString>
	<field name="Q_OS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="Q_OS_NOME_EMPRESA" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_RUA" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_FACHADA" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_BAIRRO" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_CIDADE" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_CGC" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_FONE" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_FAX" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_UF" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_CEP" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="Q_OS_TIPO" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_NOME" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_FONE" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_CEP" class="java.lang.String"/>
	<field name="Q_OS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="Q_OS_SERIE" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="Q_OS_PLACA" class="java.lang.String"/>
	<field name="Q_OS_DATA_PROMETIDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CHASSI" class="java.lang.String"/>
	<field name="Q_OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="Q_OS_DESC_PROD_MOD" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_PREFIXO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="Q_OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="Q_OS_KM" class="java.lang.Double"/>
	<field name="Q_OS_NOME_COMPLETO" class="java.lang.String"/>
	<field name="Q_OS_PRESERVAR_PECAS_USADAS" class="java.lang.String"/>
	<field name="Q_CALCULADOS_CIDADE" class="java.lang.String"/>
	<field name="Q_CALCULADOS_ENDECO" class="java.lang.String"/>
	<field name="Q_ASSINATURA_ASSINATURA" class="java.awt.Image"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="230">
			<frame>
				<reportElement x="0" y="0" width="555" height="230" uuid="d00f3b2e-f507-40fb-be60-ed75387b40f3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="4" y="229" width="551" height="1" uuid="b8057f39-8600-4fa3-b72e-ca0b96830dfb"/>
				</line>
				<rectangle>
					<reportElement mode="Transparent" x="4" y="5" width="403" height="69" uuid="ef3f9898-e5fc-49c1-8bb8-d40a1f9f147d"/>
				</rectangle>
				<rectangle>
					<reportElement mode="Transparent" x="468" y="36" width="80" height="27" uuid="14d4414e-6319-4389-9bed-809a85f520ba"/>
				</rectangle>
				<rectangle>
					<reportElement mode="Transparent" x="409" y="36" width="57" height="27" uuid="e4f47bb4-654e-4dae-8189-7f29f6715fd2"/>
				</rectangle>
				<image>
					<reportElement x="7" y="7" width="62" height="62" uuid="1c4ea542-d2db-4e0a-be79-a0561b11e8d0"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600471.png"]]></imageExpression>
				</image>
				<textField>
					<reportElement mode="Transparent" x="474" y="43" width="47" height="13" uuid="cd27753d-63cf-4748-ab52-e2a570d33cdd"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="521" y="43" width="22" height="13" uuid="29935046-bb12-4146-a642-42630cd82305"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="72" y="7" width="224" height="13" uuid="f3559682-1a88-412f-a9c5-860fdff8088d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="72" y="22" width="141" height="13" uuid="e03399ed-89d6-4fa2-af22-1a2df8f6921a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="213" y="22" width="49" height="13" uuid="ba60164d-b906-4af9-b838-26e23b8f55b6"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_FACHADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="71" y="38" width="191" height="14" uuid="ea30f118-994c-47f9-996a-86f664aec6b0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="262" y="38" width="117" height="14" uuid="d038d10f-e0ee-43d0-9013-4ce101e868a2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_CIDADE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="297" y="7" width="26" height="13" uuid="2fec4dc3-8433-4d85-b385-91dc331cd4ae"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="323" y="7" width="72" height="13" uuid="8f22cae4-8338-4d3a-a729-065e19eb1284"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_CGC}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="72" y="55" width="25" height="14" uuid="591240c7-578e-4297-b35c-ee45a88cdc99"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Fone:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="97" y="55" width="49" height="14" uuid="5eb1a1bc-3631-4c47-8b24-917c9e68498d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_FONE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="158" y="55" width="21" height="14" uuid="73e225f3-bfe9-40d8-908a-bbb1b6c47443"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[FAX:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="179" y="55" width="49" height="14" uuid="ede5279d-a37b-4a5a-a13b-dad6d19b15d9"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_FAX}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="243" y="55" width="30" height="14" uuid="4ab98e48-f7a3-4404-8926-e0cd7f2018a1"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_UF}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="308" y="55" width="19" height="14" uuid="e8b742b8-dc16-422f-8d74-2a544a400c1e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="327" y="55" width="71" height="14" uuid="dda5ccf4-f986-4ee4-866d-0b3c3c8c09b6"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="470" y="17" width="78" height="18" uuid="a7cbc149-ee4e-42ce-b3c0-38c7f0bcd343"/>
					<textElement textAlignment="Left">
						<font size="16" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="414" y="17" width="56" height="18" uuid="1a6aef01-16a6-41b6-ae5c-95f3aa166ccf"/>
					<textElement textAlignment="Center">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[O.S. Nº]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="416" y="65" width="43" height="11" uuid="b1b1f9ee-7ca3-4f27-8296-38cded9f170d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Equipe]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="488" y="65" width="43" height="11" uuid="8652716d-a5a1-4773-8608-47219603c0b7"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Entrada]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="415" y="41" width="21" height="17" uuid="02bb2f37-da75-49f6-a91d-ec040a44a862"/>
					<textElement textAlignment="Center">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[TP:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="437" y="41" width="25" height="17" uuid="c1d88120-9a07-4290-bafc-be5f683cc4d0"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TIPO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="6" y="79" width="54" height="14" uuid="1e302c77-57ff-4c33-ad2b-cf7b42a20e5b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cliente]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="6" y="100" width="54" height="14" uuid="3942d1f5-fbac-4bb4-9da7-1e6b41b48c4a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[End./Cidade]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="6" y="121" width="54" height="14" uuid="0b973240-4bbf-4cc6-8863-706293e224c8"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CNPJ/CPF]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="17" y="171" width="37" height="25" uuid="4509c552-64d1-43c9-867a-77878b8114c9"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº de Conta]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="431" y="121" width="42" height="14" uuid="3d0c5db3-a820-4d6f-8522-6455ff9e9489"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Insc. Est.:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="431" y="80" width="41" height="14" uuid="f3eec3cd-ae9a-451a-9cc0-967ac243e1e3"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Telefone:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="431" y="94" width="32" height="13" uuid="dd6cd0d8-b4e0-4214-92c7-7ce3763aaf19"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cep:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="385" y="139" width="128" height="14" uuid="d2169bdb-0b7a-405b-a08e-9c5996a8460c"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data de venda do Veiculo Novo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="165" y="139" width="93" height="14" uuid="d5638807-f28e-4bc0-887e-c6f1e0c25a79"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Previsão de Entrega]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="7" y="203" width="49" height="14" uuid="3e8f4549-132f-4850-ac4c-583607ee11a9"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Ref./ Cód.]]></text>
				</staticText>
				<line>
					<reportElement x="4" y="79" width="1" height="57" uuid="11fc87d9-7676-443e-80ac-7a1537d2204b"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="431" y="107" width="32" height="14" uuid="e54c9f7c-ebf8-4eeb-ab35-7cba032e76b0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Placa:]]></text>
				</staticText>
				<line>
					<reportElement x="549" y="79" width="1" height="58" uuid="19f83ff4-653f-4d72-9f6d-659664840a9e"/>
				</line>
				<line>
					<reportElement x="4" y="136" width="545" height="1" uuid="ac4199a9-43fb-40ce-acc2-802aa240e088"/>
				</line>
				<line>
					<reportElement x="155" y="137" width="1" height="29" uuid="7425f8a2-3fb6-480d-9534-8067b6af5752"/>
				</line>
				<line>
					<reportElement x="4" y="202" width="551" height="1" uuid="065bc762-3d89-4c3d-82dc-211a49662d6e"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="200" y="171" width="46" height="26" uuid="b4c717b0-7421-4d24-80c4-7e1bdbcab5f2"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº Ref. Pedido]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="60" y="79" width="237" height="14" uuid="80b657f6-06f9-4350-8ac5-1cf1904fca5a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="484" y="80" width="60" height="14" uuid="2e6352a4-8998-472b-9761-4a30356d9fc4"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_FONE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="473" y="94" width="71" height="13" uuid="0ac7438a-1bc7-48f1-8010-1c86e4c9117e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="385" y="153" width="128" height="13" uuid="32ccf10e-075a-4344-afc7-a02e55d82772"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="473" y="121" width="71" height="14" uuid="fb07737e-aeba-4c7c-83b4-17245f9337d7"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_INSCRICAO_ESTADUAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="489" y="178" width="62" height="11" uuid="3aac9e61-edf7-4327-a62e-5346b269eedb"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_SERIE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="60" y="93" width="237" height="14" uuid="7ac63388-cb6a-44ab-89e5-7dca932474c5"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CALCULADOS_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="60" y="107" width="237" height="14" uuid="25bc6571-bb42-4caa-ac95-595a8997592a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CALCULADOS_ENDECO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="60" y="121" width="237" height="14" uuid="542b529f-c7f4-4c58-be5b-0e543d172699"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_CGC_CPF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="473" y="107" width="71" height="14" uuid="78d61374-814c-44e1-846f-d4b5af4ebf35"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PLACA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="6" y="139" width="147" height="13" uuid="7e7e9f03-17b4-42f8-a312-33c097d6f30e"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data de emissão/Entrada na Oficina]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="6" y="152" width="147" height="14" uuid="1197f227-c1cc-4707-a8ca-89dd1871e6de"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="165" y="153" width="93" height="13" uuid="592b3b91-dab9-4703-8f8e-4c5be748abc3"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="4" y="166" width="551" height="1" uuid="4efff002-d394-445b-b467-5b5457349651"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="66" y="171" width="73" height="26" uuid="031ff309-35d4-4832-ac89-94ccca1a8bb2"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="250" y="171" width="73" height="26" uuid="85a26095-44cd-4bc5-9adc-75a10b5e95c5"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="364" y="167" width="22" height="11" uuid="3ee8ff55-f0d7-49d2-80a0-7de9b0ce2767"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="440" y="167" width="22" height="11" uuid="5bbf950e-dcce-4b03-8363-232b58921ab8"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cor]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="489" y="167" width="44" height="11" uuid="9eff217b-e08c-4d82-8999-7e248ce5b40a"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº de Série]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="364" y="189" width="60" height="12" uuid="9c366f47-e774-481b-832c-530258dbb762"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº de Chassis]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="424" y="189" width="127" height="12" uuid="d3078ba0-8c1d-4686-999d-b3fdf777ece8"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CHASSI}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="424" y="178" width="65" height="11" uuid="308b2aac-3235-4f04-aeb2-1fc57012005c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="364" y="178" width="60" height="11" uuid="39df7736-9ea7-4acb-babf-19ffeac4ac50"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DESC_PROD_MOD}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="298" y="93" width="130" height="14" uuid="d15218b0-5c3d-450b-a1c9-78114a63d27e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="472" y="80" width="12" height="14" uuid="955560f6-5be1-457f-8180-d657d32e4ea9"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_PREFIXO}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="57" y="167" width="1" height="62" uuid="06465553-e1e9-4f15-9188-838e5ae3e82a"/>
				</line>
				<line>
					<reportElement x="4" y="78" width="545" height="1" uuid="b0cce8be-7672-4d23-882c-bca1a60c7f81"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="423" y="218" width="42" height="11" uuid="78ca3be3-c4dd-4da9-a13d-1212b5ffb3ff"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nr Motor]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="10" y="218" width="43" height="11" uuid="fe7050d0-5641-4ab0-b4fe-3e13b4642980"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Código]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="465" y="217" width="84" height="12" uuid="3c02b055-b194-44f9-955c-f3ca3cd783d1"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_MOTOR}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="393" y="202" width="1" height="28" uuid="741bbe1d-cc18-43d0-8d32-e33fc22226dc"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="365" y="218" width="21" height="11" uuid="053d1776-7402-47e1-a129-073bebb849d2"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[T.P.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="396" y="218" width="21" height="11" uuid="0aa3ba46-6c51-4885-8aac-9132ba900888"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[T.M.]]></text>
				</staticText>
				<line>
					<reportElement x="421" y="203" width="1" height="26" uuid="1dc21e42-7cc6-4880-bb76-6a6cc63f9d03"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="74" y="218" width="282" height="11" uuid="7d395a3c-19e6-499d-a38b-aa7d3e64237d"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Descrição dos Serviços]]></text>
				</staticText>
				<line>
					<reportElement x="4" y="217" width="551" height="1" uuid="b5ab873d-f4a9-4e85-bc62-ae37dc0a6b87"/>
				</line>
				<line>
					<reportElement x="4" y="137" width="1" height="92" uuid="3323b319-0526-4503-bf7d-fc930b86fc85"/>
				</line>
				<line>
					<reportElement x="362" y="136" width="1" height="94" uuid="4f571b12-2aaf-441d-95af-a71530163494"/>
				</line>
				<line>
					<reportElement x="0" y="0" width="555" height="1" uuid="b529f60a-d227-482c-b816-170545aefefb">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="469" y="3" width="34" height="12" uuid="a5316539-0b4f-4aea-a1c8-0532e2f0a93a"/>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Página:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="524" y="3" width="7" height="12" uuid="c7a34d3b-8c99-4979-b931-ca65f2074096"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[/]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="262" y="139" width="93" height="14" uuid="878fe180-d852-4012-8974-5bffb3bf50d1"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Hora Prometida]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="262" y="153" width="93" height="13" uuid="d19169c4-1ece-4e3c-8a83-b168ee3261d8"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_HORA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Page">
					<reportElement mode="Transparent" x="503" y="3" width="21" height="12" uuid="6574bf71-c267-4f4d-93be-193bc682b822"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_COUNT}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement mode="Transparent" x="531" y="3" width="21" height="12" uuid="b0d4891a-3124-4cd2-b24d-00f7d309079c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_COUNT}]]></textFieldExpression>
				</textField>
			</frame>
			<line>
				<reportElement x="0" y="0" width="1" height="230" uuid="0af4dcdd-ef89-4e0d-80c6-f61e5e637c45"/>
			</line>
			<line>
				<reportElement x="554" y="0" width="1" height="230" uuid="bf51ab9e-5f9a-451a-b35c-adb8e66c0887">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="515" splitType="Stretch">
			<frame>
				<reportElement x="0" y="225" width="555" height="290" isPrintWhenDetailOverflows="true" uuid="2674c5f6-d750-4647-9525-0a51992ea46b">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="3" y="2" width="551" height="1" uuid="b5ede50c-d94c-48c8-be0d-efc459e6cc18"/>
				</line>
				<image scaleImage="RealHeight">
					<reportElement x="420" y="3" width="134" height="112" uuid="33682d93-71d9-4567-a7a8-1f27d3e30115"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600472.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="434" y="247" width="102" height="12" uuid="43d5cab4-616c-42c4-8e5e-5be5fb61ecc0"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="433" y="222" width="108" height="9" uuid="15a6adde-1cea-4da8-b586-7a363a802753"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Assinatura Consultor Técnico]]></text>
				</staticText>
				<line>
					<reportElement x="554" y="-1" width="1" height="278" uuid="825010fc-49c6-4c7f-8ea9-cfdb5783cb15"/>
				</line>
				<line>
					<reportElement x="4" y="3" width="1" height="275" uuid="b76f35fc-9cbc-42d4-b87d-f047802ce1ef"/>
				</line>
				<line>
					<reportElement x="419" y="3" width="1" height="112" uuid="32d95c9e-018f-4d2d-9326-71f9a136a521"/>
				</line>
				<line>
					<reportElement x="4" y="30" width="415" height="1" uuid="bd5b3b8f-b4f2-4731-809c-e638807dc0dc"/>
				</line>
				<line>
					<reportElement x="4" y="45" width="415" height="1" uuid="2c3b2270-b307-4219-9350-8c9ab5626eb2"/>
				</line>
				<line>
					<reportElement x="4" y="115" width="550" height="1" uuid="d51b2c4a-8d5a-4f14-971a-a8f3a1afd952"/>
				</line>
				<line>
					<reportElement x="5" y="184" width="415" height="1" uuid="69d7ddff-0d81-49c4-b614-be6a7385f5e7"/>
				</line>
				<rectangle>
					<reportElement mode="Transparent" x="69" y="160" width="59" height="23" uuid="8c65a84c-4ee1-48e0-b760-3d8386a64a7b"/>
				</rectangle>
				<line>
					<reportElement x="0" y="277" width="555" height="1" uuid="572c2890-4733-47ff-b144-a7a4a5c1541c"/>
				</line>
				<line>
					<reportElement x="4" y="257" width="550" height="1" uuid="93ab0d3d-f263-4d7f-81b1-52b94aac8317"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="9" y="33" width="43" height="12" uuid="e47cdca1-7ca8-4902-960f-ee0f4459a33a"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Código]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="23" y="4" width="37" height="25" uuid="a8adc175-70e9-47ab-9528-a7d5957c73d3"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº de Conta]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="66" y="4" width="65" height="25" uuid="fb4f27ac-58f4-4d3e-83bf-20c6bb59a23e"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="179" y="4" width="40" height="25" uuid="261ac98a-abdb-48b7-a620-3c3d3fe6444a"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº Ref. Pedido]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="223" y="4" width="73" height="25" uuid="7f3d815e-6e86-4310-9780-8cc3be520559"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="67" y="32" width="278" height="14" uuid="70dddcd3-e9fb-40b4-a0f5-33fdedfaf51e"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Descrição dos Serviços]]></text>
				</staticText>
				<line>
					<reportElement x="391" y="31" width="1" height="84" uuid="3b3ec9f7-afa4-491d-84c8-a5e9af1b1761"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="366" y="33" width="21" height="13" uuid="05614b4e-1014-4c08-afd2-b9baf7703a41"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[T.P.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="397" y="33" width="21" height="13" uuid="bf12cfc0-6640-4c7c-bb96-05c194bf7ca9"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[T.M.]]></text>
				</staticText>
				<line>
					<reportElement x="361" y="31" width="1" height="84" uuid="42932c1b-21b2-45d4-a865-eea18c19e6da"/>
				</line>
				<line>
					<reportElement x="63" y="31" width="1" height="85" uuid="9da415c8-49f7-43f7-91cb-39c689baa3fa"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="8" y="116" width="4" height="42" uuid="4c50b7bf-53d8-44b8-806d-faa5e2100f03"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font size="4" isBold="true"/>
					</textElement>
					<text><![CDATA[RECLAMO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="40" y="116" width="17" height="43" uuid="b516d800-f2e8-4cc4-a815-0ebf0f9eecb7"/>
					<textElement textAlignment="Center">
						<font size="5" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<line>
					<reportElement x="4" y="158" width="550" height="1" uuid="649d9a45-e063-4f4b-9879-b06398c533e6"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="22" y="160" width="37" height="23" uuid="7d6544f2-75d4-44ed-83d4-a01f6fb6df64"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº de Conta]]></text>
				</staticText>
				<rectangle>
					<reportElement mode="Transparent" x="210" y="160" width="59" height="23" uuid="cb5edff6-0804-4258-8592-e60b58cd1210"/>
				</rectangle>
				<staticText>
					<reportElement mode="Transparent" x="165" y="160" width="41" height="23" uuid="e242c23c-cc48-4dad-97e7-a895179eaac9"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº Ref. Pedido]]></text>
				</staticText>
				<line>
					<reportElement x="420" y="158" width="1" height="99" uuid="dd764053-1523-40e8-81c7-85ec7dea8138"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="421" y="160" width="132" height="32" uuid="154be0ff-2b5d-4445-8483-ac876f5e5607"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Justified" verticalAlignment="Top">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[O Cliente deseja (com exceção das
peças em Garantia e a base de 
troca) conservar as peças 
Substituidas?]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="424" y="192" width="13" height="11" uuid="1e2c5009-2a98-44cd-8289-a3b301639e8e"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PRESERVAR_PECAS_USADAS} == "S" ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="437" y="192" width="22" height="11" uuid="6d164239-b50d-4bcc-b73a-a129b241f62f"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Sim]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="500" y="192" width="13" height="11" uuid="61e0dc8a-1ba9-4f0b-b900-2e955b640f56"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{Q_OS_PRESERVAR_PECAS_USADAS} == "N" || $F{Q_OS_PRESERVAR_PECAS_USADAS} == null) ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="514" y="192" width="22" height="11" uuid="6d45a461-23a2-4724-93dc-bb0ad77ba1ce"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Não]]></text>
				</staticText>
				<line>
					<reportElement x="424" y="211" width="127" height="1" uuid="4239fafc-c68e-43df-b9d5-ed53a5dc28f0"/>
				</line>
				<line>
					<reportElement x="425" y="247" width="125" height="1" uuid="cc1fe57c-8a00-4838-8e86-3e7eb039bb30"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="9" y="187" width="43" height="12" uuid="7856673f-8a01-4c64-956c-04510aeeb6c3"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Código]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="67" y="185" width="278" height="14" uuid="44ad58a1-d389-4b07-96f0-378b0b42c0c0"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Descrição dos Serviços]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="366" y="187" width="21" height="13" uuid="adfc3911-cd35-4e4f-a883-ab25084039f8"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[T.P.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="397" y="187" width="21" height="13" uuid="ecefb1a0-7d6c-439e-b411-13747e86e3c9"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[T.M.]]></text>
				</staticText>
				<line>
					<reportElement x="5" y="199" width="416" height="1" uuid="ecfa477d-3c95-40fd-992f-b50bbfe33b41"/>
				</line>
				<line>
					<reportElement x="63" y="184" width="1" height="74" uuid="73c0ef49-7876-4f68-92e1-9ed6658087b0"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="7" y="257" width="62" height="11" uuid="7424a170-5daf-4e8d-910a-bcd393b244ad"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Observações:]]></text>
				</staticText>
				<line>
					<reportElement x="361" y="184" width="1" height="73" uuid="f7b2c2a8-2342-4afa-aa69-67afc6b4b62c"/>
				</line>
				<line>
					<reportElement x="391" y="185" width="1" height="72" uuid="*************-4bc8-bbaa-5fa1acc4d141"/>
				</line>
				<line>
					<reportElement x="34" y="115" width="1" height="43" uuid="d02a8951-03ca-433f-a775-2b1fb94b37b3"/>
				</line>
				<textField>
					<reportElement mode="Transparent" x="421" y="212" width="133" height="12" uuid="943e80a9-b0cb-4aed-81cb-1fa58a4e83bc"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NOME_COMPLETO}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="0" width="1" height="277" uuid="3c9f35c1-47f6-4b69-a583-ce89f2bc6724"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="5" y="279" width="487" height="10" uuid="0018c9e9-7a95-4da3-8125-6ae74649a5b8"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[1º via: Cliente / 2º via: Quadro de Carga -  Garantia / 3º via:              Contabilidade - Fiscal / 4º via: Cartão - Arquivo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="15" y="116" width="3" height="42" uuid="dd177de3-f346-4270-931c-1216926791fd"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font size="4" isBold="true"/>
					</textElement>
					<text><![CDATA[CLIENTE]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="47" y="116" width="5" height="42" uuid="f5f0b73f-15e1-43e0-b84e-ce5968d715c4"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[A B C D E]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" isUsingCache="false">
					<reportElement x="433" y="231" width="105" height="15" uuid="13e44185-e676-49ee-85c9-8efb2abf3ad0">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{Q_ASSINATURA_ASSINATURA}]]></imageExpression>
				</image>
			</frame>
			<line>
				<reportElement x="554" y="142" width="1" height="81" isPrintWhenDetailOverflows="true" uuid="4d70d111-8f61-4346-b145-86f87f1471b3"/>
			</line>
			<line>
				<reportElement x="34" y="145" width="1" height="79" isPrintWhenDetailOverflows="true" uuid="fd0a602a-92db-4a69-b27c-bc5177be13c6"/>
			</line>
			<line>
				<reportElement x="63" y="144" width="1" height="80" isPrintWhenDetailOverflows="true" uuid="3daeba76-7df2-40fa-bfb3-0a1852efca4a"/>
			</line>
			<staticText>
				<reportElement mode="Transparent" x="6" y="148" width="27" height="73" isPrintWhenDetailOverflows="true" uuid="87c21c60-d1bf-459c-9b0b-a4cc23b8bd2a"/>
				<textElement textAlignment="Left">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="38" y="148" width="17" height="73" isPrintWhenDetailOverflows="true" uuid="d919a73c-b53d-46c3-998c-5634ddc8ede6"/>
				<textElement textAlignment="Center">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<line>
				<reportElement x="4" y="144" width="550" height="1" isPrintWhenDetailOverflows="true" uuid="6608f58d-c97e-4669-8260-5035471caf68"/>
			</line>
			<line>
				<reportElement x="4" y="145" width="1" height="79" isPrintWhenDetailOverflows="true" uuid="cc090f01-1588-4cbb-8d92-5fdf36df6190"/>
			</line>
			<line>
				<reportElement x="0" y="0" width="1" height="225" isPrintWhenDetailOverflows="true" uuid="6acc44fb-9fd1-4c22-861f-a28d5168466c"/>
			</line>
			<line>
				<reportElement x="4" y="223" width="551" height="1" isPrintWhenDetailOverflows="true" uuid="e4c75c57-6b15-487f-8e5a-5f2626a0d2bf"/>
			</line>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement x="5" y="0" width="416" height="144" uuid="3652e075-a474-4b85-a792-16433aef4f5d">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsRenaultSubServicos.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement x="421" y="0" width="134" height="143" isPrintWhenDetailOverflows="true" uuid="8651ef86-4dd3-44f6-8e1e-121f7f24d3e4"/>
				<staticText>
					<reportElement mode="Transparent" x="4" y="2" width="62" height="12" uuid="e3c7fdcf-5173-464c-b478-51bd30be28d6"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Combustível]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="73" y="1" width="59" height="12" uuid="3220721e-edbd-4e1a-a221-40e51ff8e5d3"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Km Entrada]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="74" y="14" width="54" height="14" uuid="bf5ca885-5eea-4234-8679-99cf7dbe447f"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_KM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="3" y="29" width="128" height="114" uuid="feeca5ce-3b2e-470d-ab59-9d342784ac21"/>
					<textElement textAlignment="Justified" verticalAlignment="Top">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Ao assinar a  ordem  de  Serviço, o Cliente reconhece    ter    tomado conhecimento prévio das condições gerais de reparação  transcritas  no verso do presente documento,  das prescrições previstas  na  Carta  de Garantia ou constantes     das Operações   de    Manutenção   em vigor, bem como não nos responsabilizamos por objetos deixados dentro do veiculo na  data  da intervenção Renault]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="0" width="1" height="144" uuid="6d588c79-a578-4623-af66-8c51952c94ae"/>
				</line>
				<line>
					<reportElement x="133" y="0" width="1" height="142" uuid="b0bdd0ee-f3f7-40a4-a731-74e656089e6c"/>
				</line>
			</frame>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement x="68" y="148" width="483" height="72" uuid="db81c28b-b025-4b56-86b4-3bc9fcc378f7">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsRenaultSubReclamacao.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement mode="Transparent" x="22" y="150" width="5" height="70" uuid="90235c18-b03e-4c3d-a616-2908a652765a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[CLIENTE]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="13" y="150" width="6" height="70" uuid="04969e1a-3ecc-477e-a1d4-085068476cbe"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[RECLAMO]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="44" y="150" width="5" height="70" uuid="269bd800-0a53-4cb0-9f62-997e321e2dba"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[ABCDE]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
