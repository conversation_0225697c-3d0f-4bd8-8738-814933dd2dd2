<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListGwmPdsSubItem" pageWidth="555" pageHeight="842" whenNoDataType="BlankPage" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" whenResourceMissingType="Empty" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[115582.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="OBRIGATORIO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<parameter name="ID_GRUPO" class="java.lang.Double">
		<defaultValueExpression><![CDATA[30050.0]]></defaultValueExpression>
	</parameter>
	<parameter name="ID_CHECKLIST" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[with DADOS as (
select info.cod_empresa as COD_EMPRESA,
       info.cod_produto as COD_PRODUTO,
       info.cod_modelo as COD_MODELO,
       info.tipo_os as TIPO_OS,
       info.cod_segmento as COD_SEGMENTO
  from (select os.cod_empresa,dv.Cod_Produto, dv.cod_modelo, os.tipo as tipo_os, produtos.cod_segmento
          from os, os_dados_veiculos dv, produtos
         where os.numero_os = dv.numero_os
           and os.cod_empresa = dv.cod_empresa
           and os.cod_empresa = $P{COD_EMPRESA}
           and os.numero_os = $P{NUMERO_OS}
           and produtos.cod_produto = dv.cod_produto) info
),
TODOS_ITENS as ( 
SELECT A.ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */ 
       A.COD_ITEM,
       NULL AS ACAO_ITEM,
       A.DESCRICAO AS DESCRICAO_ITEM,
       NVL(A.observacao,' ') as OBSERVACAO_ITEM,
       A.RESPOSTA_EH_OBSERVACAO,
       D.DESCRICAO AS DESCRICAO_OPCAO,
       B.DESCRICAO DESCRICAO_GRUPO,
       B.ORDEM AS ORDEM_GRUPO ,
       NVL(C.OBSERVACAO,' ') as OBSERVACAO,
       ROW_NUMBER() Over (order by A.ID_GRUPO, B.ORDEM, A.ORDEM) as LINHA_RELATORIO
FROM MOB_PERTENCE_ITEM A, MOB_PERTENCE_GRUPO B, MOB_OS_PERTENCE C, MOB_OPCAO D, MOB_CRUZA_GRUPO E
WHERE A.ID_GRUPO = B.ID_GRUPO
 AND B.ATIVO = 'S'
 AND A.COD_ITEM = C.COD_ITEM (+)
 AND B.APLICACAO = 'R'
 AND C.COD_EMPRESA(+) = $P{COD_EMPRESA}
 AND C.NUMERO_OS(+) = $P{NUMERO_OS}
 AND C.ID_OPCAO = D.ID_OPCAO(+)
 AND NVL(C.ID_OPCAO,0) != 20021 /* opção: não aplicável, não serão impressos os itens marcados com essa opção */
 AND E.ID_CHECKLIST = $P!{ID_CHECKLIST}
 AND E.ID_GRUPO = B.ID_GRUPO
 )
select ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */ 
       COD_ITEM,
       ACAO_ITEM,
       CASE WHEN LENGTH(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM) > 300 then LPAD(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM,297) || '...' ELSE DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM END AS DESCRICAO_ITEM_ABREVIADA,
       --lower(CASE WHEN LENGTH(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM || ' ' || OBSERVACAO_ITEM|| ' ' || OBSERVACAO_ITEM) > 300 then LPAD(DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM || ' ' || OBSERVACAO_ITEM|| ' ' || OBSERVACAO_ITEM ,297) || '...' ELSE DESCRICAO_ITEM || ' ' || OBSERVACAO_ITEM || ' ' || OBSERVACAO_ITEM|| ' ' || OBSERVACAO_ITEM  END) AS DESCRICAO_ITEM_ABREVIADA,
       DESCRICAO_ITEM,
       OBSERVACAO_ITEM,
       RESPOSTA_EH_OBSERVACAO,
       DESCRICAO_OPCAO,
       DESCRICAO_GRUPO,
       OBSERVACAO,
       ORDEM_GRUPO,
       LINHA_RELATORIO
FROM TODOS_ITENS
WHERE ID_GRUPO = $P{ID_GRUPO}
ORDER BY ID_GRUPO]]>
	</queryString>
	<field name="ID_GRUPO" class="java.math.BigDecimal"/>
	<field name="COD_ITEM" class="java.math.BigDecimal"/>
	<field name="ACAO_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM_ABREVIADA" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="OBSERVACAO_ITEM" class="java.lang.String"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="ORDEM_GRUPO" class="java.math.BigDecimal"/>
	<field name="LINHA_RELATORIO" class="java.math.BigDecimal"/>
	<variable name="returnValue" class="java.lang.Double">
		<variableExpression><![CDATA[$V{returnValue}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="14">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.length() <= 60:false
||
 $F{DESCRICAO_ITEM_ABREVIADA}.length() <= 60]]></printWhenExpression>
			<frame borderSplitType="DrawBorders">
				<reportElement stretchType="ContainerHeight" mode="Transparent" x="0" y="0" width="555" height="14" isPrintWhenDetailOverflows="true" backcolor="#D0CECE" uuid="0703988c-4b5b-44b6-9da3-5329b13d2953">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="329" y="0" width="226" height="14" isPrintWhenDetailOverflows="true" uuid="55c86164-8332-4873-84c3-93d898108fb1">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	 ? $F{OBSERVACAO}
	 : $F{DESCRICAO_OPCAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" mode="Transparent" x="70" y="0" width="259" height="14" uuid="370d2936-66dc-477b-95b4-73d938dd6346">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA_RELATORIO} + " - " + $F{DESCRICAO_ITEM_ABREVIADA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="1" width="70" height="13" backcolor="#D9D9D9" uuid="e3e14ad6-1524-4ee8-a76a-81d8e3213541">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</frame>
		</band>
		<band height="28">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.length() > 60 && $F{OBSERVACAO}.length() <= 120 : false
|| 
$F{DESCRICAO_ITEM_ABREVIADA}.length() > 60 && $F{DESCRICAO_ITEM_ABREVIADA}.length() <= 120]]></printWhenExpression>
			<frame borderSplitType="DrawBorders">
				<reportElement mode="Transparent" x="0" y="0" width="555" height="28" isPrintWhenDetailOverflows="true" backcolor="#D0CECE" uuid="2a81b7ca-9fd5-4d52-9db2-a1ba3feb9319">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="329" y="0" width="226" height="28" isPrintWhenDetailOverflows="true" uuid="39bf7f55-97ef-4e88-88a7-2fb18bb51431">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	 ? $F{OBSERVACAO}
	 : $F{DESCRICAO_OPCAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="70" y="0" width="259" height="28" uuid="4f8d9fe9-c363-4fb4-9fcc-ed4ec671bf9e">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA_RELATORIO} + " - " + $F{DESCRICAO_ITEM_ABREVIADA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="1" width="70" height="27" backcolor="#D9D9D9" uuid="90102971-943c-4fd0-a4ba-ceeb3baf9bea">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</frame>
		</band>
		<band height="42">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.length() > 120:false
||
$F{DESCRICAO_ITEM_ABREVIADA}.length() > 120]]></printWhenExpression>
			<frame borderSplitType="DrawBorders">
				<reportElement mode="Transparent" x="0" y="0" width="555" height="42" isPrintWhenDetailOverflows="true" backcolor="#D0CECE" uuid="66b11279-d5c4-4d1a-a448-0f0a68adacec">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="329" y="0" width="226" height="42" isPrintWhenDetailOverflows="true" uuid="cd6d77b0-57bb-438d-a03a-31a6bdbcd806">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	 ? $F{OBSERVACAO}
	 : $F{DESCRICAO_OPCAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="70" y="0" width="259" height="42" uuid="308903ae-b904-474d-9969-2b3273fd9e8c">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA_RELATORIO} + " - " + $F{DESCRICAO_ITEM_ABREVIADA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="1" width="70" height="41" backcolor="#D9D9D9" uuid="a9ace2cd-bae2-4164-b2bb-ce6d18a0ef51">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8"/>
						<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="1" leftIndent="1" spacingBefore="0" spacingAfter="0" tabStopWidth="40"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
