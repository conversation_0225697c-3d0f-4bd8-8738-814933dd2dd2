<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListCheryEntradaSaidaSubOutrosItens" pageWidth="365" pageHeight="108" columnWidth="365" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select cod_empresa, nome from empresas e where e.cod_empresa in (1,2,3)]]>
		</queryString>
		<field name="COD_EMPRESA" class="java.lang.Double"/>
		<field name="NOME" class="java.lang.String"/>
	</subDataset>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="ID_CHECKLIST" class="java.lang.Double"/>
	<parameter name="ID_GRUPO" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[select a.selecionado_observacao as observacao
from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
where a.id_checklist =50000
      		and a.id_grupo = 50025
      		and a.cod_item = 50036]]>
	</queryString>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="30">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement mode="Opaque" x="0" y="0" width="365" height="30" backcolor="#525252" uuid="b7030bf2-8b84-4806-8a38-2827445c1e3b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="30" y="8" width="304" height="15" forecolor="#FFFFFF" backcolor="#FFFFFF" uuid="9c43df80-8d8b-4ba6-a9e0-3e155781608d">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[PERTENCES DEIXADOS NO INTERIOR DO VEÍCULO]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="78" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="365" height="78" uuid="98e3a3fc-a687-48e5-a433-1e53d3afce84">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="40" y="0" width="322" height="13" forecolor="#000000" uuid="1477f8a6-aa42-4d2d-a79b-e05ae273cc50">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="40" y="0" width="316" height="78" forecolor="#000000" uuid="bf4ea371-6c75-4d4c-b7f0-ae45d775a8c6">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box topPadding="2" leftPadding="2"/>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="7"/>
						<paragraph lineSpacing="Fixed" lineSpacingSize="11.5" firstLineIndent="1" tabStopWidth="1"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="3" y="0" width="37" height="14" forecolor="#000000" uuid="ac5b4e4b-1060-477b-9a26-fb687f046f08">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[Descrição:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="40" y="13" width="322" height="13" forecolor="#000000" uuid="63143bd5-48c7-458e-bdc8-e1ef3b7054a8">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="40" y="26" width="322" height="13" forecolor="#000000" uuid="06aa123c-21a5-4d71-95b3-80f7ab966346">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="40" y="39" width="322" height="13" forecolor="#000000" uuid="825beb40-f031-4a3d-a99d-81b95f2593bc">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="40" y="52" width="322" height="13" forecolor="#000000" uuid="747baf04-01d7-4cc3-ab50-fbacf590c050">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="40" y="65" width="322" height="13" forecolor="#000000" uuid="7b9adaf0-94ba-4d56-8ac8-60b15f51323e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
