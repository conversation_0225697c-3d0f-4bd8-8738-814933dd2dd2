<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiCheckList" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="TESTE_FREEDOM.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="padrao_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[115534.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\31381\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH qryOS AS
 (SELECT ROWNUM AS NUMERO_LINHA,
  OS.COD_EMPRESA,
         OS.NUMERO_OS,
         OS.DATA_EMISSAO,
         V.PRISMA,
         C.NOME NOME_CLIENTE,
         NVL(NULLIF(CD.CGC, ''), CD.CPF) CNPJ_CPF,
         CD.Inscricao_Estadual  Inscricao_Estadual,
         CD.RG,
         NVL(C.RUA_RES, C.RUA_COM) RUA_RES,
         NVL(C.BAIRRO_RES, C.BAIRRO_COM) BAIRRO_RES,
         (SELECT DESCRICAO
            FROM CIDADES
           WHERE COD_CIDADES = NVL(C.COD_CID_RES, C.COD_CID_COM)
             AND ROWNUM = 1) AS CIDADE_RES,
         NVL(C.UF_RES, C.UF_COM) UF_RES,
         NVL(C.CEP_RES, C.CEP_COM) CEP_RES,
         C.ENDERECO_ELETRONICO,
         (NVL(C.PREFIXO_RES, C.PREFIXO_COM) || ' - ' ||
         NVL(C.TELEFONE_RES, C.TELEFONE_COM)) AS TELEFONE_RES,
         (C.PREFIXO_CEL || ' - ' || C.TELEFONE_CEL) AS TELEFONE_CEL,
         (C.PREFIXO_COM || ' - ' || C.TELEFONE_COM) AS TELEFONE_COM,
         NVL(NULLIF(C.RUA_COBRANCA, ''), C.RUA_RES) RUA_FAT,
         NVL(NULLIF(C.CEP_COBRANCA, ''), C.CEP_RES) CEP_FAT,
         NVL(NULLIF(C.BAIRRO_COBRANCA, ''), C.BAIRRO_RES) BAIRRO_FAT,
         NVL(CIDF.DESCRICAO, CIDR.DESCRICAO) CIDADE_FAT,
         NVL(CIDF.UF, CIDR.UF) UF_FAT,
         OS.OBSERVACAO,
         CASE
           WHEN OT.REVISAO_GRATUITA = 'S' THEN
            1 
           WHEN OT.INTERNO = 'S' THEN
            2 
           WHEN OT.GARANTIA = 'S' THEN
            3 
           WHEN OTE.FUNILARIA = 'S' THEN
            4 
           ELSE
            0 
         END TIPO_OS,
         CASE UPPER(FPG.TIPO_PGTO)
           WHEN 'Z' THEN
            1 
           WHEN 'H' THEN
            2 
           WHEN 'V' THEN
            3 
           WHEN 'M' THEN
            4 
           WHEN 'P' THEN
            4 
           ELSE
            0 
         END FORMA_PAGAMENTO,
         PM.DESCRICAO_MODELO,
         V.CHASSI,
         V.COR_EXTERNA,
         V.KM,
         V.DATA_VENDA,
         V.ANO,
         V.PLACA,
         CON.CODIGO_PADRAO COD_REVENDEDOR,
         CON.NOME NOME_REVENDEDOR,
         V.COMBUSTIVEL,
         NVL(OS.NOME, OS.CONSULTOR_RECEPCAO) AS CONSULTOR,
     OS_AGENDA.SIGNATURE AS OS_ASSINATURA,
     PRODUTOS.COD_SEGMENTO
    FROM OS,
     OS_AGENDA,
         OS_DADOS_VEICULOS V,
         OS_TIPOS          OT,
         OS_TIPOS_EMPRESAS OTE,
         OS_PAGAMENTO      OPG,
         FORMA_PGTO        FPG,
         CLIENTES          C,
         CLIENTE_DIVERSO   CD,
         CIDADES           CIDR,
         CIDADES           CIDF,
         PRODUTOS_MODELOS  PM,
         PRODUTOS,
         CONCESSIONARIAS   CON
   WHERE (OS.NUMERO_OS = V.NUMERO_OS AND OS.COD_EMPRESA = V. COD_EMPRESA)
   AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
     AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS (+)
     AND (OS.TIPO = OT.TIPO)
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND (OT.TIPO = OTE.TIPO AND OTE.COD_EMPRESA = OS.COD_EMPRESA)
     AND (OS.NUMERO_OS = OPG.NUMERO_OS(+) AND
         OS.COD_EMPRESA = OPG.COD_EMPRESA(+))
     AND (OPG.COD_FORMA_PGTO = FPG.COD_FORMA_PGTO(+))
     AND (OS.COD_CLIENTE = C.COD_CLIENTE)
     AND (C.COD_CLIENTE = CD.COD_CLIENTE)
     AND C.COD_CID_RES = CIDR.COD_CIDADES(+)
     AND C.COD_CID_COBRANCA = CIDF.COD_CIDADES(+)
        
     AND V.COD_PRODUTO = PM.COD_PRODUTO (+)
     AND V.COD_MODELO = PM.COD_MODELO (+)
     AND PM.COD_PRODUTO = PRODUTOS.COD_PRODUTO (+)
     AND V.COD_CONCESSIONARIA = CON.COD_CONCESSIONARIA
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}),

qryEmpresa AS
 (SELECT ROWNUM AS NUMERO_LINHA, E.NOME,
         E.RUA,
         E.BAIRRO,
         E.CIDADE,
         E.ESTADO,
         E.CEP,
         E.FONE,
         E.FAX,
         C.CODIGO_PADRAO CODIGO_REVENDEDOR
    FROM EMPRESAS E, PARM_SYS P, CONCESSIONARIAS C
   WHERE E.COD_EMPRESA = P.COD_EMPRESA
     AND P.CONCESSIONARIA_NUMERO = C.COD_CONCESSIONARIA(+)
     AND E.COD_EMPRESA = $P{COD_EMPRESA})
SELECT 
qryempresa.NOME AS qryempresa_NOME,
qryempresa.RUA AS qryempresa_RUA,
qryos.NUMERO_OS AS qryos_NUMERO_OS,
qryempresa.CEP AS qryempresa_CEP,
qryempresa.CODIGO_REVENDEDOR AS qryempresa_CODIGO_REVENDEDOR,
qryempresa.FONE AS qryempresa_FONE,
qryempresa.BAIRRO AS qryempresa_BAIRRO,
qryos.PRISMA AS qryos_PRISMA,
qryos.DATA_EMISSAO AS qryos_DATA_EMISSAO,
qryempresa.CIDADE AS qryempresa_CIDADE,
qryempresa.FAX AS qryempresa_FAX,
qryos.COR_EXTERNA AS qryos_COR_EXTERNA,
qryos.CHASSI AS qryos_CHASSI,
qryos.DESCRICAO_MODELO AS qryos_DESCRICAO_MODELO,
qryos.DATA_VENDA AS qryos_DATA_VENDA,
qryos.KM AS qryos_KM,
qryos.OBSERVACAO AS qryos_OBSERVACAO,
qryos.ANO AS qryos_ANO,
qryos.PLACA AS qryos_PLACA,
qryos.COD_REVENDEDOR AS qryos_COD_REVENDEDOR,
qryos.NOME_REVENDEDOR AS qryos_NOME_REVENDEDOR,
qryos.NOME_CLIENTE AS qryos_NOME_CLIENTE,
qryos.RUA_RES AS qryos_RUA_RES,
qryos.BAIRRO_RES AS qryos_BAIRRO_RES,
qryos.RG AS qryos_RG,
qryos.CNPJ_CPF AS qryos_CNPJ_CPF,
qryos.CIDADE_RES AS qryos_CIDADE_RES,
qryos.TELEFONE_COM AS qryos_TELEFONE_COM,
qryos.ENDERECO_ELETRONICO AS qryos_ENDERECO_ELETRONICO,
qryos.CEP_RES AS qryos_CEP_RES,
qryos.UF_RES AS qryos_UF_RES,
qryos.TELEFONE_CEL AS qryos_TELEFONE_CEL,
qryos.TELEFONE_RES AS qryos_TELEFONE_RES,
qryos.BAIRRO_FAT AS qryos_BAIRRO_FAT,
COALESCE (qryos.TELEFONE_COM,qryos.TELEFONE_RES,qryos.TELEFONE_CEL) AS qryos_TELEFONE_CLIENTE,
qryos.CEP_FAT AS qryos_CEP_FAT,
qryos.UF_FAT AS qryos_UF_FAT,
qryos.CIDADE_FAT AS qryos_CIDADE_FAT,
qryos.RUA_FAT AS qryos_RUA_FAT,
qryos.TIPO_OS AS qryos_TIPO_OS,
qryos.COMBUSTIVEL AS qryos_COMBUSTIVEL,
qryos.FORMA_PAGAMENTO as qryos_FORMA_PAGAMENTO,
qryos.OS_ASSINATURA as qruos_OS_ASSINATURA,
qryos.Inscricao_Estadual as qryos_Inscricao_Estadual,
qryos.cod_segmento as qryos_cod_segmento
FROM qryOS, qryEmpresa
WHERE qryOS.NUMERO_LINHA = qryEmpresa.NUMERO_LINHA (+)]]>
	</queryString>
	<field name="QRYEMPRESA_NOME" class="java.lang.String"/>
	<field name="QRYEMPRESA_RUA" class="java.lang.String"/>
	<field name="QRYOS_NUMERO_OS" class="java.lang.Double"/>
	<field name="QRYEMPRESA_CEP" class="java.lang.String"/>
	<field name="QRYEMPRESA_CODIGO_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYEMPRESA_FONE" class="java.lang.String"/>
	<field name="QRYEMPRESA_BAIRRO" class="java.lang.String"/>
	<field name="QRYOS_PRISMA" class="java.lang.String"/>
	<field name="QRYOS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="QRYEMPRESA_CIDADE" class="java.lang.String"/>
	<field name="QRYEMPRESA_FAX" class="java.lang.String"/>
	<field name="QRYOS_COR_EXTERNA" class="java.lang.String"/>
	<field name="QRYOS_CHASSI" class="java.lang.String"/>
	<field name="QRYOS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="QRYOS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="QRYOS_KM" class="java.lang.Double"/>
	<field name="QRYOS_OBSERVACAO" class="java.lang.String"/>
	<field name="QRYOS_ANO" class="java.lang.String"/>
	<field name="QRYOS_PLACA" class="java.lang.String"/>
	<field name="QRYOS_COD_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_CLIENTE" class="java.lang.String"/>
	<field name="QRYOS_RUA_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_RES" class="java.lang.String"/>
	<field name="QRYOS_RG" class="java.lang.String"/>
	<field name="QRYOS_CNPJ_CPF" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_COM" class="java.lang.String"/>
	<field name="QRYOS_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="QRYOS_CEP_RES" class="java.lang.String"/>
	<field name="QRYOS_UF_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_CEL" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_FAT" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_CLIENTE" class="java.lang.String"/>
	<field name="QRYOS_CEP_FAT" class="java.lang.String"/>
	<field name="QRYOS_UF_FAT" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_FAT" class="java.lang.String"/>
	<field name="QRYOS_RUA_FAT" class="java.lang.String"/>
	<field name="QRYOS_TIPO_OS" class="java.lang.Double"/>
	<field name="QRYOS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="QRYOS_FORMA_PAGAMENTO" class="java.lang.Double"/>
	<field name="QRUOS_OS_ASSINATURA" class="java.awt.Image"/>
	<field name="QRYOS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="QRYOS_COD_SEGMENTO" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="10"/>
	</columnHeader>
	<detail>
		<band height="124">
			<frame>
				<reportElement x="0" y="0" width="555" height="124" uuid="efb198ee-50f5-4e9a-be0c-21efb58ff12c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="136" y="24" width="174" height="11" forecolor="#000000" uuid="9ecc69ca-27da-4d2a-8e33-67af7ecf140f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_RUA}]]></textFieldExpression>
				</textField>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="26" y="11" width="110" height="46" uuid="4077afd4-99aa-46fb-affd-a92bbcecdc1c">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} +"crmservice154010.png"]]></imageExpression>
				</image>
				<textField>
					<reportElement mode="Transparent" x="412" y="24" width="127" height="11" forecolor="#000000" uuid="ed116461-1d37-40d7-ba98-baf9cac8a5f8">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="19" y="70" width="56" height="17" forecolor="#000000" backcolor="#E3DCDC" uuid="98855495-31cc-4fe3-83f1-d98240e25760">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box rightPadding="4">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="75" y="70" width="137" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="352a87d9-a78d-402f-b4e8-83e48bc4a082">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NOME_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="19" y="87" width="56" height="17" forecolor="#000000" backcolor="#E3DCDC" uuid="727e2e87-8a4b-4595-b5bb-914afb1106a2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box rightPadding="4">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Placa:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="75" y="87" width="137" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="6495a2d3-4b7d-4c49-a315-cfedd9a8661b">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="268" y="87" width="158" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="c444681c-3636-4793-b464-bcec3463af08">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DESCRICAO_MODELO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="212" y="70" width="56" height="17" forecolor="#000000" backcolor="#E3DCDC" uuid="bde1b025-009e-4aaa-b1c9-654878e8cce2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box rightPadding="4">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Email:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="212" y="87" width="56" height="17" forecolor="#000000" backcolor="#E3DCDC" uuid="0fd127c4-ad9e-47ec-ae8f-b10d0b294011">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box rightPadding="4">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Modelo:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="268" y="70" width="158" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="09749ffc-ddb1-4b38-a520-f48f3c19638e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="469" y="87" width="71" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="6824e20c-b53f-4e5e-a004-86a2ebd926b9">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="426" y="70" width="43" height="17" forecolor="#000000" backcolor="#E3DCDC" uuid="210a6082-7fcf-4cc6-aaf7-ff00181262c3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box rightPadding="4">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Telefone:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="426" y="87" width="43" height="17" forecolor="#000000" backcolor="#E3DCDC" uuid="ae34e1b2-11b0-4b90-ba5e-de448349a301">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box rightPadding="4">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cor:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="469" y="70" width="71" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="d7da6cf0-ebff-4e93-b5bd-b3788df72b05">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TELEFONE_CLIENTE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="161" y="46" width="232" height="11" forecolor="#000000" uuid="d6ac2eb6-c821-4dc6-a499-183edf396420"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_FONE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="161" y="35" width="120" height="11" forecolor="#000000" uuid="206590b2-11a9-4b5f-9e80-cd25fdc81b11"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CNPJ_CPF}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="136" y="35" width="25" height="11" forecolor="#000000" uuid="28981de7-f455-4719-8193-a27b3d30b6f4"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="137" y="46" width="24" height="11" forecolor="#000000" uuid="6247982a-9210-4e0d-a0ef-683c8f200142"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Fone:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="281" y="35" width="59" height="11" forecolor="#000000" uuid="567be839-132f-4d3e-8333-8c63b6cd9460">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Insc. Estadual:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="340" y="35" width="127" height="11" forecolor="#000000" uuid="8e552a76-7f28-49d6-ae47-65950d4bcf72">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_INSCRICAO_ESTADUAL}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="76" y="106" width="403" height="18" forecolor="#000000" uuid="4096e015-6ea9-469b-aa9c-39f707295457">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[Check List - Entrada e Saída Veículo]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="310" y="24" width="58" height="11" forecolor="#000000" uuid="4b972e98-5b6c-4332-9c86-55474c944030">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="375" y="24" width="34" height="11" forecolor="#000000" uuid="5356f83b-0a76-4827-b4a7-85b7cc3fb4cd">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_UF_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="136" y="11" width="403" height="13" forecolor="#000000" uuid="93280f99-c8a5-4cb7-9e1d-1b068596f98c">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="368" y="24" width="7" height="11" uuid="79366ce8-0235-4b49-9c12-ed6c19339e12"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[-]]></text>
				</staticText>
			</frame>
		</band>
		<band height="240">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="0" width="555" height="240" isRemoveLineWhenBlank="true" uuid="0b13197c-a185-429d-b315-188ecc0125f8"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListCheckImagem.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="78">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="4" width="555" height="74" uuid="128d9de3-86dc-47da-b3a0-20d9e88e221c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListChecKCombustivelKm.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="24">
			<subreport>
				<reportElement x="0" y="0" width="275" height="24" uuid="f21f2325-a589-4181-bc74-729b28f54cb9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TIPO_OS">
					<subreportParameterExpression><![CDATA[$F{QRYOS_TIPO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListChecKCheckListEntrada.jasper"]]></subreportExpression>
			</subreport>
			<subreport overflowType="Stretch">
				<reportElement x="280" y="0" width="275" height="24" uuid="06589049-9a6a-42b6-8c99-6ea0bcc79183">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TIPO_OS">
					<subreportParameterExpression><![CDATA[$F{QRYOS_TIPO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListChecKCheckListSaida.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="79">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement x="0" y="5" width="555" height="74" uuid="9d5917fd-49de-4a08-970e-e105602fe9fc">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListChecKDataAssinatura.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="79">
			<subreport isUsingCache="false" runToBottom="false" overflowType="Stretch">
				<reportElement stretchType="ContainerBottom" x="0" y="5" width="555" height="74" uuid="9e99d07e-258f-4906-976d-d381b96f5e83">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListChecKFotos.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band height="17">
			<textField>
				<reportElement x="479" y="6" width="16" height="11" uuid="417d6604-52b5-4f5d-9fad-03d1511231dd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="460" y="6" width="19" height="11" uuid="da026dae-e7c4-4b2e-af22-3d4223f4f1a0"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Pág.]]></text>
			</staticText>
			<staticText>
				<reportElement x="495" y="6" width="15" height="11" uuid="a247a548-0187-4cab-96ad-653a0f139e19"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[de]]></text>
			</staticText>
			<textField evaluationTime="Report">
				<reportElement x="510" y="6" width="16" height="11" uuid="5c1adedb-3fb2-43b7-893a-3e9705a9e18f"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
</jasperReport>
