<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="emBranco" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<subDataset name="q_produtos" uuid="c96004bf-1f0f-4079-8519-e04ed4912068">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<parameter name="COD_SEGMENTO" class="java.lang.Double"/>
		<parameter name="QUERY_DINAMICA" class="java.lang.String">
			<defaultValueExpression><![CDATA["1 = 1"]]></defaultValueExpression>
		</parameter>
		<parameter name="TOTAL_PASSAGENS" class="java.lang.Double"/>
		<queryString>
			<![CDATA[SELECT OS.COD_PRODUTO, PRODUTOS.DESCRICAO_PRODUTO,
  COUNT(NUMERO_OS) AS PASSAGENS2
FROM OS, PRODUTOS, OS_TIPOS
WHERE  OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO
  AND PRODUTOS.COD_SEGMENTO = $P{COD_SEGMENTO}
  AND OS.TIPO = OS_TIPOS.TIPO
  AND OS.ORCAMENTO = 'N'
  AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
  AND $P!{QUERY_DINAMICA}
GROUP BY OS.COD_PRODUTO, PRODUTOS.DESCRICAO_PRODUTO
ORDER BY DESCRICAO_PRODUTO]]>
		</queryString>
		<field name="COD_PRODUTO" class="java.lang.Double"/>
		<field name="DESCRICAO_PRODUTO" class="java.lang.String"/>
		<field name="PASSAGENS2" class="java.lang.Double"/>
	</subDataset>
	<subDataset name="q_modelo" uuid="82252bb8-4b1c-4249-bdcb-ec6218d46529">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<parameter name="COD_PRODUTO" class="java.lang.Double"/>
		<parameter name="QUERY_DINAMICA" class="java.lang.String">
			<defaultValueExpression><![CDATA["1 = 1"]]></defaultValueExpression>
		</parameter>
		<parameter name="TOTAL_PASSAGENS" class="java.lang.Double"/>
		<queryString>
			<![CDATA[SELECT OS.COD_MODELO, PRODUTOS_MODELOS.DESCRICAO_MODELO,
  COUNT(NUMERO_OS) AS PASSAGENS3
FROM OS, PRODUTOS_MODELOS, OS_TIPOS
WHERE OS.COD_PRODUTO = $P{COD_PRODUTO}
  AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
  AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
  AND OS.TIPO = OS_TIPOS.TIPO
  AND OS.ORCAMENTO = 'N'
  AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
  AND $P!{QUERY_DINAMICA}
  
GROUP BY OS.COD_MODELO, PRODUTOS_MODELOS.DESCRICAO_MODELO
ORDER BY DESCRICAO_MODELO]]>
		</queryString>
		<field name="COD_MODELO" class="java.lang.Double"/>
		<field name="DESCRICAO_MODELO" class="java.lang.String"/>
		<field name="PASSAGENS3" class="java.lang.Double"/>
	</subDataset>
	<parameter name="QUERY_DINAMICA" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["1 = 1"]]></defaultValueExpression>
	</parameter>
	<parameter name="TOTAL_PASSAGENS" class="java.lang.Double"/>
	<queryString>
		<![CDATA[SELECT PRODUTOS.COD_SEGMENTO, PRODUTO_SEGMENTO.DESCRICAO_SEGMENTO,
  COUNT(NUMERO_OS) AS PASSAGENS1
FROM OS, PRODUTOS, PRODUTO_SEGMENTO, OS_TIPOS
WHERE  OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO
  AND PRODUTOS.COD_SEGMENTO = PRODUTO_SEGMENTO.COD_SEGMENTO
  AND OS.TIPO = OS_TIPOS.TIPO
  AND OS.ORCAMENTO = 'N'
  AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
  AND $P!{QUERY_DINAMICA}
  
GROUP BY PRODUTOS.COD_SEGMENTO, PRODUTO_SEGMENTO.DESCRICAO_SEGMENTO
ORDER BY COD_SEGMENTO]]>
	</queryString>
	<field name="COD_SEGMENTO" class="java.lang.Double"/>
	<field name="DESCRICAO_SEGMENTO" class="java.lang.String"/>
	<field name="PASSAGENS1" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="29">
			<frame>
				<reportElement x="0" y="0" width="555" height="29" uuid="fa18482f-85e6-432b-a337-fe93389862e0">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="0" y="14" width="1" height="15" uuid="6b41943c-231c-4139-bc38-7b3d525c60a6"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="1" width="248" height="13" uuid="351efa3f-b04f-4d54-b2d7-************"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Passagens por Segmento / Produto / Modelo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="256" y="17" width="94" height="12" uuid="2175bdd9-7966-41e6-8fe2-3318c29ba3f4"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total de Passagens]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="356" y="17" width="82" height="12" uuid="c2ed2d42-aedd-4284-bebd-84a28545f2d8"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[%]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="14" width="441" height="1" uuid="d60527d9-3aa5-4ff3-8a56-39f3522dd64d"/>
				</line>
				<line>
					<reportElement x="252" y="14" width="1" height="15" uuid="e62bdc05-b1fa-485a-b99f-c18a4a9cab2d"/>
				</line>
				<line>
					<reportElement x="351" y="14" width="1" height="15" uuid="3e24008f-bff5-46c8-b5ca-a3482c553444"/>
				</line>
				<line>
					<reportElement x="442" y="14" width="1" height="15" uuid="9043a76c-a0d2-470c-8309-761c8b2eade9"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="17" width="193" height="12" uuid="c0ac12cc-5616-491f-aa27-2d5afdd80d61"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Segmento / Produto / Modelo]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="41" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="443" height="14" uuid="828e9f04-dd17-4441-b42d-beb3af1ca817">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="2" y="0" width="193" height="14" uuid="38298f3e-4108-41ce-8294-26b848047660"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_SEGMENTO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement mode="Transparent" x="256" y="0" width="38" height="14" uuid="b16ae195-2737-4b85-acd4-06635a3dd937"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PASSAGENS1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement mode="Transparent" x="356" y="0" width="30" height="14" uuid="354559f8-3411-43fb-8752-0f0d79cffa45"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PASSAGENS1} * 100 /  $P{TOTAL_PASSAGENS}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="1" width="1" height="13" uuid="003d7bbe-ec23-4b2d-a443-0d10c6edb98a"/>
				</line>
				<line>
					<reportElement x="252" y="1" width="1" height="13" uuid="434f08db-34ec-42d1-bf3c-ba7b00934392"/>
				</line>
				<line>
					<reportElement x="351" y="1" width="1" height="13" uuid="bf735009-26c2-4d43-af5f-73d852f517d8"/>
				</line>
				<line>
					<reportElement x="442" y="1" width="1" height="13" uuid="da976c48-eb8c-449c-8113-4392de37bcae"/>
				</line>
				<line>
					<reportElement x="0" y="0" width="443" height="1" uuid="30df4739-9b2a-4c1d-9d3b-bf7789a4b1c6"/>
				</line>
			</frame>
			<componentElement>
				<reportElement x="0" y="14" width="443" height="27" uuid="68530dbe-c41e-4559-a453-e9f54b7b8677">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="q_produtos" uuid="*************-469f-bc76-437f8335ed4e">
						<datasetParameter name="COD_SEGMENTO">
							<datasetParameterExpression><![CDATA[$F{COD_SEGMENTO}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="TOTAL_PASSAGENS">
							<datasetParameterExpression><![CDATA[$P{TOTAL_PASSAGENS}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="QUERY_DINAMICA">
							<datasetParameterExpression><![CDATA[$P{QUERY_DINAMICA}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:listContents height="27" width="443">
						<componentElement>
							<reportElement x="0" y="13" width="443" height="14" uuid="0b2981ec-1b2f-46af-acc4-24a7ce084a4f">
								<property name="net.sf.jasperreports.export.headertoolbar.table.name" value=""/>
							</reportElement>
							<jr:list printOrder="Vertical">
								<datasetRun subDataset="q_modelo" uuid="673525fc-0c5f-44e6-88f6-d6d3abf9486b">
									<datasetParameter name="COD_PRODUTO">
										<datasetParameterExpression><![CDATA[$F{COD_PRODUTO}]]></datasetParameterExpression>
									</datasetParameter>
									<datasetParameter name="QUERY_DINAMICA">
										<datasetParameterExpression><![CDATA[$P{QUERY_DINAMICA}]]></datasetParameterExpression>
									</datasetParameter>
									<datasetParameter name="TOTAL_PASSAGENS">
										<datasetParameterExpression><![CDATA[$P{TOTAL_PASSAGENS}]]></datasetParameterExpression>
									</datasetParameter>
									<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
								</datasetRun>
								<jr:listContents height="14" width="443">
									<frame>
										<reportElement x="0" y="0" width="443" height="14" uuid="65abaf5e-0804-4b81-81f6-25ee8ed1a228">
											<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
										</reportElement>
										<box>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
										<textField>
											<reportElement mode="Transparent" x="55" y="0" width="193" height="14" uuid="1a551f65-2aba-4ae1-9a42-afba03e47de8"/>
											<textElement textAlignment="Left" verticalAlignment="Middle">
												<font size="8" isBold="false"/>
											</textElement>
											<textFieldExpression><![CDATA[$F{DESCRICAO_MODELO}]]></textFieldExpression>
										</textField>
										<textField pattern="#,##0.###;(#,##0.###-)">
											<reportElement mode="Transparent" x="308" y="0" width="38" height="14" uuid="1cd9e21d-dc65-445c-a3e1-2c61fbb11bb7"/>
											<textElement textAlignment="Right" verticalAlignment="Middle">
												<font size="8" isBold="false"/>
											</textElement>
											<textFieldExpression><![CDATA[$F{PASSAGENS3}]]></textFieldExpression>
										</textField>
										<textField pattern="#,##0.00#;(#,##0.00#-)">
											<reportElement mode="Transparent" x="409" y="0" width="30" height="14" uuid="4a594b85-c972-42e8-8a7b-3aee8b648bdf"/>
											<textElement textAlignment="Right" verticalAlignment="Middle">
												<font size="8" isBold="false"/>
											</textElement>
											<textFieldExpression><![CDATA[$F{PASSAGENS3} * 100 / $P{TOTAL_PASSAGENS}]]></textFieldExpression>
										</textField>
										<line>
											<reportElement x="0" y="1" width="1" height="13" uuid="58c52521-90d1-48a7-ad3b-c3e2e26d7955"/>
										</line>
										<line>
											<reportElement x="252" y="1" width="1" height="13" uuid="6323d32f-9a08-4411-baf3-412fc0008f0b"/>
										</line>
										<line>
											<reportElement x="351" y="1" width="1" height="13" uuid="7c5c4004-a69f-4691-bfd4-13341027cb7a"/>
										</line>
										<line>
											<reportElement x="442" y="1" width="1" height="13" uuid="223cac34-0ad6-43dd-ac13-c4f6eb02047f"/>
										</line>
										<line>
											<reportElement x="0" y="0" width="443" height="1" uuid="18aa2f83-57c1-44e3-bf7c-3f1c0299ba2c"/>
										</line>
										<line>
											<reportElement x="0" y="13" width="443" height="1" uuid="3742c654-ce59-473e-ba45-5046e28bcdf8"/>
										</line>
									</frame>
								</jr:listContents>
							</jr:list>
						</componentElement>
						<frame>
							<reportElement x="0" y="0" width="443" height="14" uuid="fa594fd7-6b2a-4633-9a95-1cfc846acf5e">
								<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
							</reportElement>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement mode="Transparent" x="28" y="0" width="193" height="14" uuid="81740240-9c7d-4faa-b7ad-d28b1ae4430d"/>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font size="8" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{DESCRICAO_PRODUTO}]]></textFieldExpression>
							</textField>
							<textField pattern="#,##0.###;(#,##0.###-)">
								<reportElement mode="Transparent" x="282" y="0" width="38" height="14" uuid="fe3d95c4-7edd-433e-958e-c428de181e75"/>
								<textElement textAlignment="Right" verticalAlignment="Middle">
									<font size="8" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{PASSAGENS2}]]></textFieldExpression>
							</textField>
							<textField pattern="#,##0.00#;(#,##0.00#-)">
								<reportElement mode="Transparent" x="382" y="0" width="30" height="14" uuid="40d0aa01-ef1c-442f-b316-cb145ea9d380"/>
								<textElement textAlignment="Right" verticalAlignment="Middle">
									<font size="8" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{PASSAGENS2} * 100 / $P{TOTAL_PASSAGENS}]]></textFieldExpression>
							</textField>
							<line>
								<reportElement x="0" y="1" width="1" height="12" uuid="8181da79-785d-4236-afaa-e9e3623808ee"/>
							</line>
							<line>
								<reportElement x="252" y="1" width="1" height="12" uuid="b438d5d6-6985-44e3-8b3f-40ad8baaae3a"/>
							</line>
							<line>
								<reportElement x="351" y="1" width="1" height="12" uuid="73e90220-5b3b-43d9-b35c-e9ddc041a4c3"/>
							</line>
							<line>
								<reportElement x="442" y="1" width="1" height="12" uuid="935bc702-84ae-4a1b-94a3-8b7c4e80fa89"/>
							</line>
							<line>
								<reportElement x="0" y="0" width="443" height="1" uuid="1f0b8bb4-9d85-42a3-a740-66f40e194329"/>
							</line>
						</frame>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
	</detail>
	<summary>
		<band height="21">
			<frame>
				<reportElement x="0" y="0" width="555" height="21" uuid="f47134de-af08-40e0-b3aa-36454639fb13">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="0" y="0" width="1" height="13" uuid="aaacbf45-db6c-4cf7-bfb9-8a50f84416bb"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="0" width="50" height="14" uuid="c3e66634-96a6-4393-84b5-981441a4bd06"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="13" width="442" height="1" uuid="11e516f2-579e-45ff-8ce1-ec7111dea5bb"/>
				</line>
				<line>
					<reportElement x="351" y="0" width="1" height="13" uuid="d50c06ed-2397-4659-a388-1351acfd21ab"/>
				</line>
				<line>
					<reportElement x="442" y="0" width="1" height="14" uuid="f61e3b7a-3ce6-47b3-9d08-6a49105cee3c"/>
				</line>
				<line>
					<reportElement x="252" y="0" width="1" height="13" uuid="8e7ef9b1-595c-4482-ab24-ce1daea61734"/>
				</line>
				<textField>
					<reportElement mode="Transparent" x="256" y="0" width="38" height="14" uuid="9fffecf4-192d-453e-80fb-b566b39ef80d"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{TOTAL_PASSAGENS}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</summary>
</jasperReport>
