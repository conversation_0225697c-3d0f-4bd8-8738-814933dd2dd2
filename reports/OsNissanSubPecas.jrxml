<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsNissanSubPecas" pageWidth="555" pageHeight="842" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="camposNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT ROWNUM AS NUM_LISTA, REQUISICOES.* FROM (SELECT DECODE(OS.SEQUENCIA_DAV, 0, OS_REQUISICOES.COD_ITEM,
                NVL(ITENS_CUSTOS.COD_FISCAL_ITEM, OS_REQUISICOES.COD_ITEM)) AS COD_ITEM,
 OS_REQUISICOES.REQUISICAO, OS_REQUISICOES.COD_FORNECEDOR,
 DECODE(FORNECEDOR_ESTOQUE.OFICIAL, 'N', '*' || ITENS.DESCRICAO, ITENS.DESCRICAO) AS DESCRICAO,
 OS_REQUISICOES.QUANTIDADE, OS_REQUISICOES.CAUSADORA,  
 OS_REQUISICOES.ITEM, ITENS.COD_MAX_DESC,
 NVL(ESTOQUE.QTDE, 0) AS ESTOQUE_QTDE,
 NVL(ESTOQUE.RESERVADO, 0) AS RESERVADO,
 DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
             ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                                   DECODE(ITENS.COD_TRIBUTACAO, '1',
                                     DECODE(PARM_SYS.REGIME_ICMS, 'S',
                                       DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                                         DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                     0),
                                              0),
                                            OS_TIPOS.AUMENTO_PRECO_PECA),
                                          0),
                                    OS_TIPOS.AUMENTO_PRECO_PECA)) *
                   DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                                                    'G', OS_REQUISICOES.PRECO_GARANTIA,
                                                    'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                                                    'P', OS_REQUISICOES.PRECO_FABRICA,
                                                    DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
                  ) / 100,
       DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA,'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                                                               OS_REQUISICOES.PRECO_GARANTIA),
         DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
           DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
             ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
               DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                                                         OS_REQUISICOES.PRECO_VENDA) )/100)))))) AS PRECO_VENDA,
 OS_REQUISICOES.QUANTIDADE *
   DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
     DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
       DECODE(OS_TIPOS.INTERNO, 'S',
                   ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                                         DECODE(ITENS.COD_TRIBUTACAO, '1',
                                           DECODE(PARM_SYS.REGIME_ICMS, 'S',
                                             DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                                               DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                           0),
                                                    0),
                                                  OS_TIPOS.AUMENTO_PRECO_PECA),
                                                0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                     DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                                                      'G', OS_REQUISICOES.PRECO_GARANTIA,
                                                      'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                                                      'P', OS_REQUISICOES.PRECO_FABRICA,
                                                      DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
                    ) / 100,
         DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                                                                  OS_REQUISICOES.PRECO_GARANTIA),
           DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
             DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
               ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
                DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                                                          OS_REQUISICOES.PRECO_VENDA))/100)))))) AS PRECO_TOTAL
FROM OS_REQUISICOES, ESTOQUE, ITENS, OS, VW_OS_TIPOS OS_TIPOS, FORNECEDOR_ESTOQUE , SEGURADORA,
   ITENS_FORNECEDOR, ITENS_CLASSE_CONTABIL ICC, PARM_SYS, PARM_SYS2, ITENS_CUSTOS, OS_TIPOS_EMPRESAS OTE
WHERE (OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM)
  AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR
  AND (OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM (+))
  AND (OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR (+))
  AND (OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA (+))
  AND (OS_REQUISICOES.NUMERO_OS = OS.NUMERO_OS)
  AND (OS_REQUISICOES.COD_EMPRESA = OS.COD_EMPRESA)
  AND (OS.TIPO = OS_TIPOS.TIPO)
  AND (OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA)
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = ITENS_CUSTOS.COD_EMPRESA (+)
  AND OS_REQUISICOES.COD_ITEM = ITENS_CUSTOS.COD_ITEM (+)
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_CUSTOS.COD_FORNECEDOR (+)
  AND (OS_REQUISICOES.NUMERO_OS = $P{NUMERO_OS})
  AND (OS_REQUISICOES.COD_EMPRESA = $P{COD_EMPRESA})
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
ORDER BY OS_REQUISICOES.ITEM, OS_REQUISICOES.COD_ITEM) REQUISICOES]]>
	</queryString>
	<field name="NUM_LISTA" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="NUM_LISTA"/>
		<property name="com.jaspersoft.studio.field.label" value="NUM_LISTA"/>
	</field>
	<field name="COD_ITEM" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_ITEM"/>
	</field>
	<field name="REQUISICAO" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="REQUISICAO"/>
		<property name="com.jaspersoft.studio.field.label" value="REQUISICAO"/>
	</field>
	<field name="COD_FORNECEDOR" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="COD_FORNECEDOR"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_FORNECEDOR"/>
	</field>
	<field name="DESCRICAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="DESCRICAO"/>
		<property name="com.jaspersoft.studio.field.label" value="DESCRICAO"/>
	</field>
	<field name="QUANTIDADE" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="QUANTIDADE"/>
		<property name="com.jaspersoft.studio.field.label" value="QUANTIDADE"/>
	</field>
	<field name="CAUSADORA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="CAUSADORA"/>
		<property name="com.jaspersoft.studio.field.label" value="CAUSADORA"/>
	</field>
	<field name="ITEM" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="ITEM"/>
	</field>
	<field name="COD_MAX_DESC" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_MAX_DESC"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_MAX_DESC"/>
	</field>
	<field name="ESTOQUE_QTDE" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="ESTOQUE_QTDE"/>
		<property name="com.jaspersoft.studio.field.label" value="ESTOQUE_QTDE"/>
	</field>
	<field name="RESERVADO" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="RESERVADO"/>
		<property name="com.jaspersoft.studio.field.label" value="RESERVADO"/>
	</field>
	<field name="PRECO_VENDA" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="PRECO_VENDA"/>
		<property name="com.jaspersoft.studio.field.label" value="PRECO_VENDA"/>
	</field>
	<field name="PRECO_TOTAL" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="PRECO_TOTAL"/>
		<property name="com.jaspersoft.studio.field.label" value="PRECO_TOTAL"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="20">
			<staticText>
				<reportElement mode="Opaque" x="60" y="0" width="30" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="73ed2c61-dc67-4714-a8b8-a82e6af9e585">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Nº]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="140" y="0" width="280" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="deba69b2-afd3-469a-b2f5-8993ff35bf54">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição da Solicitação]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="498" y="0" width="57" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="5d4cd358-766d-4f8b-8123-7bb35a4ecde7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total das Peças]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="90" y="0" width="50" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="b70367dc-59ab-4dc9-964b-dfbf4d21883a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Referencia da Peça]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="420" y="0" width="40" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="7874b0be-5a2e-4d6d-8d10-adf00988a0bb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="60" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="d64b78a2-04b6-4573-86a6-8f71eec424e3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<pen lineColor="#DA080B"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#DA080B"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[1. Peças,]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="458" y="0" width="40" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="9ae31089-796b-4d9e-a96b-0b0bdf16417d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Unitário]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="140" y="0" width="280" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1e6da218-bcea-4afc-9ceb-819528eb6b97">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,#00.###;#,#00.###-">
				<reportElement mode="Opaque" x="60" y="0" width="30" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="dea6bb9a-743a-4f23-83e4-c5a7e4d02cd1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="498" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="89a0a6ba-2e1d-4357-af84-9a0ba2922e9f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_TOTAL}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="420" y="0" width="38" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="ec191d4c-a3a7-4f6a-8d0d-8864d4b51123">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="458" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="4954b083-165f-4d9c-854e-bb8fd2d84447">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField pattern="#,#00.###;#,#00.###-">
				<reportElement mode="Opaque" x="90" y="0" width="50" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="aef5ea37-fd42-4248-bd3e-18a9e91222fb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_ITEM}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#00.###;(#00.###-)">
				<reportElement mode="Opaque" x="0" y="0" width="60" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="751d6850-6609-42fb-86cd-094b1b6ed143">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<pen lineColor="#DA080B"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#DA080B"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NUM_LISTA}== 1 ? "Lubrificantes"
: " "]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<line>
				<reportElement x="60" y="-1" width="495" height="1" uuid="fffe0204-2d05-4f76-8bd1-d372f0a6f306"/>
				<graphicElement>
					<pen lineColor="#C0C0C0"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="0" y="-1" width="60" height="1" uuid="05cceb2d-02b1-4f03-af73-65a74935d6ed">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#FFFFFF"/>
				</graphicElement>
			</line>
		</band>
	</columnFooter>
</jasperReport>
