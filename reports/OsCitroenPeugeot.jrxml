<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsCitroenPeugeot" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="Cor1" mode="Opaque" forecolor="#FFFFFF" backcolor="#0076A9">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN") )]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#F57523" pattern=""/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#001F56"/>
		</conditionalStyle>
	</style>
	<style name="Cor2" mode="Opaque" forecolor="#FFFFFF" backcolor="#589EC3" pattern="">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#FF9F67"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#231F20" backcolor="#B2AFC4"/>
		</conditionalStyle>
	</style>
	<style name="Cor3" mode="Opaque" forecolor="#000203" backcolor="#CCE3ED">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#FDE3D3"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#EFEFF3"/>
		</conditionalStyle>
	</style>
	<style name="Cor4" mode="Opaque" forecolor="#000203" backcolor="#D5E2ED">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#FFE2D1"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#F2EEF3"/>
		</conditionalStyle>
	</style>
	<style name="Cor5" mode="Opaque" forecolor="#000203" backcolor="#D4E3FE">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#FFC4AB"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#D6D6D6"/>
		</conditionalStyle>
	</style>
	<style name="FIELD_NULL" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[34.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[25356.00]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\29881\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COPIA_CLIENTE" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="MARCA" class="java.lang.String">
		<parameterDescription><![CDATA[recebe a marca, para mudar a cor do layut de acordo com a marca]]></parameterDescription>
		<defaultValueExpression><![CDATA["CITROEN"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH Q_OS AS (SELECT OS.COD_EMPRESA,
       OS_AGENDA.DATA_AGENDADA,
       TO_CHAR(OS_AGENDA.DATA_AGENDADA, 'DD/MM/YYYY HH24:MI') AS D_AG_STG,
       OS.STATUS_OS,
       OS.NUMERO_OS,
       OS.COD_OS_AGENDA,
       ABS(OS.NUMERO_OS) AS ABS_OSNUM,
       OS.COD_CLIENTE,
       OS.CLIENTE_RAPIDO,
       OS.TIPO_ENDERECO,
       OS.OBSERVACAO,
       OS.EXTENDIDA,
       OS.SEGURADORA,
       OS.DATA_EMISSAO,
       (TO_DATE(OS.DATA_EMISSAO, 'DD/MM/YYYY') || ' AS ' || OS.HORA_EMISSAO) AS DH_EMISSAO,
       DECODE(OS.DATA_LIBERADO,
              NULL,
              '',
              (TO_DATE(OS.DATA_LIBERADO, 'DD/MM/YYYY') || ' AS ' ||
              OS.HORA_LIBERADO)) AS DH_LIBERADO,
       DECODE(OS.DATA_ENCERRADA,
              NULL,
              '',
              (TO_DATE(OS.DATA_ENCERRADA, 'DD/MM/YYYY') || ' AS ' ||
              OS.HORA_ENCERRADA)) AS DH_ENCERRADO,
       SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2) AS DIA_EMISSAO,
       SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2) AS MES_EMISSAO,
       SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4) AS ANO_EMISSAO,
       OS.HORA_EMISSAO,
       OS.HORA_ENCERRADA,
       OS.DATA_ENCERRADA,
       OS.HORA_PROMETIDA,
       SUBSTR(OS.HORA_PROMETIDA, 1, 2) AS HORA24_PROMETIDA,
       SUBSTR(OS.HORA_PROMETIDA, 4, 2) AS MINUTO_PROMETIDA,
       
       OS.DATA_PROMETIDA,
       OS.DATA_PROMETIDA_REVISADA,
       SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2) AS DIA_PROMETIDA,
       SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2) AS MES_PROMETIDA,
       SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4) AS ANO_PROMETIDA,
       
       OS.VALOR_SERVICOS_BRUTO,
       OS.VALOR_ITENS_BRUTO,
       OS.DESCONTOS_SERVICOS,
       OS.DESCONTOS_ITENS,
       (OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS TOTAL_OS_SERVICOS,
       (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS) AS TOTAL_OS_ITENS,
       (OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO) AS TOTAL_OS_BRUTO,
       (OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS) AS TOTAL_OS_DESCONTO,
       ((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) +
       (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS)) AS TOTAL_OS,
       OS.COD_SEGURADORA,
       
       OS_DADOS_VEICULOS.ANO,
       OS_DADOS_VEICULOS.HORIMETRO,
       OS_DADOS_VEICULOS.PRISMA,
       OS_DADOS_VEICULOS.DATA_VENDA,
       SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2) AS DIA_VENDA,
       SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2) AS MES_VENDA,
       SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4) AS ANO_VENDA,
       OS_DADOS_VEICULOS.COMBUSTIVEL,
       OS_DADOS_VEICULOS.COR_EXTERNA,
       OS_DADOS_VEICULOS.PLACA,
       OS_DADOS_VEICULOS.KM,
       OS_DADOS_VEICULOS.CHASSI,
       OS_DADOS_VEICULOS.NUMERO_MOTOR,
       OS_DADOS_VEICULOS.NUMERO_RENAVAM,
       OS_DADOS_VEICULOS.SERIE,
       OS_DADOS_VEICULOS.COD_CONCESSIONARIA,
       OS_DADOS_VEICULOS.ESTADO_PINTURA,
       OS_DADOS_VEICULOS.JOGO_FERRAMENTAS,
       OS_DADOS_VEICULOS.ELASTICOS,
       OS_DADOS_VEICULOS.TAMPA_LATERAL_D,
       OS_DADOS_VEICULOS.TAMPA_LATERAL_E,
       OS_DADOS_VEICULOS.FLANELA,
       OS.TIPO,
       OS_TIPOS.TIPO_FABRICA,
       OS_TIPOS.DESCRICAO AS TIPO_DESCRICAO,
       OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO AS TIPO_COM_DESCRICAO,
       OS_TIPOS.GARANTIA,
       OS_TIPOS.REVISAO_GRATUITA,
       OS_TIPOS.INTERNO,
       OS_TIPOS.COD_CLIENTE AS CLIENTE_DO_TIPO,
       OS_TIPOS.OUTRO_CONCESSIONARIA,
       OS.NOME AS CONSULTOR,
       EMPRESAS_USUARIOS.NOME_COMPLETO AS CONSULTOR_COMPLETO,
       PRODUTOS.DESCRICAO_PRODUTO,
       PRODUTOS_MODELOS.DESCRICAO_MODELO,
       (PRODUTOS.DESCRICAO_PRODUTO || ' - ' ||
       PRODUTOS_MODELOS.DESCRICAO_MODELO) DESC_PROD_MOD,
       PRODUTOS_MODELOS.LINHA,
       MARCAS.DESCRICAO_MARCA,
       CONCESSIONARIAS.NOME CONCESSIONARIA_NOME,
       CONCESSIONARIAS.UF CONCESSIONARIA_UF,
       CONCESSIONARIAS.CIDADE CONCESSIONARIA_CIDADE,
       CONCESSIONARIAS.BAIRRO CONCESSIONARIA_BAIRRO,
       CONCESSIONARIAS.ENDERECO CONCESSIONARIA_RUA,
       CONCESSIONARIAS.CEP CONCESSIONARIA_CEP,
       CONCESSIONARIAS.CODIGO_PADRAO CONCESSIONARIA_CODIGO,
       UF_CONCESSIONARIA.DESCRICAO CONCESSIONARIA_ESTADO,
       TO_CHAR(CLF.VENCIMENTO_GARANTIA_FABRICA, 'DD/MM/YYYY') AS VENCIMENTO_GARANTIA_FABRICA,
       DECODE(OS.CLIENTE_AGUARDOU,'S','X','') AS CLIENTE_AGUARDOU,
       DECODE(OS.LAVAR_VEICULO,'S','X','') AS LAVAR_VEICULO,
       DECODE(OS.PECA_USADA_FICA_CLIENTE,'S','X','') AS PECA_USADA_FICA_CLIENTE,
       DECODE(NVL(OS.MOBILIDADE,'N'),'N','X') AS NAOMOB,
       DECODE(NVL(OS.MOBILIDADE,'N'),'S','X','') AS SIMMOB,
       OMB.DESCRICAO AS DESC_MOBILIDADE,
       DECODE(NVL(OS_TIPOS.RETORNO,'N'),'N','X','') AS RETRABNAO,
       DECODE(NVL(OS_TIPOS.RETORNO,'N'),'S','X','') AS RETRABSIM,
       OS.NUMERO_OPR,  VWCRM.TEXTO, OS_AGENDA.SIGNATURE AS SIGNATURE
  FROM OS,
       OS_DADOS_VEICULOS,
       OS_AGENDA,
       EMPRESAS_USUARIOS,
       VW_OS_TIPOS       OS_TIPOS,
       CONCESSIONARIAS,
       PRODUTOS,
       PRODUTOS_MODELOS,
       MARCAS,
       UF UF_CONCESSIONARIA,
       CLIENTES_FROTA CLF,
       OS_MOBILIDADE_ESPERA OME,
       OS_MOBILIDADE OMB, 
       MARCAS MC,       
       VW_CRM_MSG_LGPD_MARCA_OS VWCRM
 WHERE OS.TIPO = OS_TIPOS.TIPO
   AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA(+)
   AND OS.NOME = EMPRESAS_USUARIOS.NOME
   AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS(+)
   AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA(+)
   AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA = CONCESSIONARIAS.COD_CONCESSIONARIA(+)
   AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO
      
   AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA(+)
   AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS(+)
      
   AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
   AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
   AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
   AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF(+)

   AND CLF.COD_CLIENTE = OS.COD_CLIENTE
   AND CLF.CHASSI      = OS_DADOS_VEICULOS.CHASSI

   AND OME.NUMERO_OS    (+) = OS.NUMERO_OS
   AND OME.COD_EMPRESA  (+) = OS.COD_EMPRESA
   AND OMB.COD_MOBILIDADE (+) = OME.COD_MOBILIDADE
   AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'

   AND MC.COD_MARCA = PRODUTOS.COD_MARCA   
   AND VWCRM.COD_TIPO_CONCESSIONARIA (+) = MC.TIPO_CONCESSIONARIA 

   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND OS.NUMERO_OS = $P{NUMERO_OS}

),

Q_EMPRESA AS (SELECT EMPRESAS.COD_EMPRESA,        
       EMPRESAS.NOME NOME_EMPRESA,
       EMPRESAS.CGC,
       EMPRESAS.FACHADA,
       EMPRESAS.ESTADO AS UF,
       (EMPRESAS.CIDADE || '-' || UF.UF) AS CIDADE,
       EMPRESAS.BAIRRO,
       EMPRESAS.COMPLEMENTO,
       (EMPRESAS.RUA || ', ' || EMPRESAS.FACHADA) AS RUA,
       EMPRESAS.FONE,
       EMPRESAS.FAX,
       EMPRESAS.CEP,
       EMPRESAS.INSCRICAO_MUNICIPAL,
       EMPRESAS.INSCRICAO_SUBSTITUICAO,
       UF.DESCRICAO ESTADO,
       EMPRESAS.INSCRICAO_ESTADUAL,
       TRUNC(SYSDATE) AS DATA_ATUAL,
       SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'), 1, 5) AS HORA_ATUAL_STR,
       EMPRESA_LOGO.LOGO,
       FABRICA_LOGO.LOGO AS LOGO_FABRICA,       
       NVL(CLIENTES.ENDERECO_ELETRONICO,CLIENTES.EMAIL_NFE) AS EMAIL

  FROM EMPRESAS,
       EMPRESA_LOGO,
       FABRICA_LOGO,
       UF,
       CLIENTES
       
 WHERE EMPRESAS.COD_EMPRESA = $P{COD_EMPRESA}
   AND EMPRESAS.COD_EMPRESA = EMPRESA_LOGO.COD_EMPRESA(+)  
   AND EMPRESAS.COD_EMPRESA = FABRICA_LOGO.COD_EMPRESA(+)
   AND EMPRESAS.ESTADO = UF.UF(+)
   AND EMPRESAS.COD_CLIENTE = CLIENTES.COD_CLIENTE (+)),

Q_CLIENTES AS (SELECT CLIENTE_DIVERSO.COD_CLIENTE,
       CLIENTE_DIVERSO.NOME AS NOME,
       CLIENTE_DIVERSO.RG   AS RG,
       
       ('(' || CLIENTES.PREFIXO_RES || ') ' || CLIENTES.TELEFONE_RES) AS TELEFONE_RES,
       CLIENTES.PREFIXO_RES,
       ('(' || CLIENTES.PREFIXO_COM || ') ' || CLIENTES.TELEFONE_COM) AS TELEFONE_COM,
       CLIENTES.PREFIXO_COM,
       ('(' || CLIENTES.PREFIXO_FAX || ') ' || CLIENTES.TELEFONE_FAX) AS TELEFONE_FAX,
       CLIENTES.PREFIXO_FAX,
       ('(' || CLIENTES.PREFIXO_CEL || ') ' || CLIENTES.TELEFONE_CEL) AS TELEFONE_CEL,
       CLIENTES.PREFIXO_CEL,
       
       NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL) AS INSC_ESTAD,
       
       CLIENTE_DIVERSO.CGC,
       CLIENTE_DIVERSO.CPF,
       CLIENTES.COD_CLASSE,
       DECODE(OS.TIPO_ENDERECO,
              1,
              CLIENTE_DIVERSO.UF,
              2,
              CLIENTES.UF_RES,
              3,
              CLIENTES.UF_COM,
              4,
              CLIENTES.UF_COBRANCA,
              5,
              ENDERECO_POR_INSCRICAO.UF,
              NULL) UF,
       DECODE(OS.TIPO_ENDERECO,
              1,
              UF_DIVERSO.DESCRICAO,
              2,
              UF_RES.DESCRICAO,
              3,
              UF_COM.DESCRICAO,
              4,
              UF_COBRANCA.DESCRICAO,
              5,
              UF_INSCRICAO.DESCRICAO,
              NULL) ESTADO,
       DECODE(OS.TIPO_ENDERECO,
              1,
              CIDADES_DIV.DESCRICAO,
              2,
              CIDADES_RES.DESCRICAO,
              3,
              CIDADES_COM.DESCRICAO,
              4,
              CIDADES_COBRANCA.DESCRICAO,
              5,
              ENDERECO_POR_INSCRICAO.CIDADE,
              NULL) CIDADE,
       DECODE(OS.TIPO_ENDERECO,
              1,
              CLIENTE_DIVERSO.BAIRRO,
              2,
              CLIENTES.BAIRRO_RES,
              3,
              CLIENTES.BAIRRO_COM,
              4,
              CLIENTES.BAIRRO_COBRANCA,
              5,
              ENDERECO_POR_INSCRICAO.BAIRRO,
              NULL) BAIRRO,
       DECODE(OS.TIPO_ENDERECO,
              1,
              TRANSLATE(TO_CHAR(CLIENTE_DIVERSO.CEP / 1000, '00000.000'),
                        ',.',
                        '.-'),
              2,
              TRANSLATE(TO_CHAR(CLIENTES.CEP_RES / 1000, '00000.000'),
                        ',.',
                        '.-'),
              3,
              TRANSLATE(TO_CHAR(CLIENTES.CEP_COM / 1000, '00000.000'),
                        ',.',
                        '.-'),
              4,
              TRANSLATE(TO_CHAR(CLIENTES.CEP_COBRANCA / 1000, '00000.000'),
                        ',.',
                        '.-'),
              5,
              TRANSLATE(TO_CHAR(ENDERECO_POR_INSCRICAO.CEP / 1000,
                                '00000.000'),
                        ',.',
                        '.-'),
              NULL) CEP,
       DECODE(OS.TIPO_ENDERECO,
              1,
              CLIENTE_DIVERSO.ENDERECO,
              2,
              CLIENTES.RUA_RES,
              3,
              CLIENTES.RUA_COM,
              4,
              CLIENTES.RUA_COBRANCA,
              5,
              ENDERECO_POR_INSCRICAO.RUA,
              NULL) RUA,
       DECODE(OS.TIPO_ENDERECO,
              1,
              CLIENTE_DIVERSO.COMPLEMENTO,
              2,
              CLIENTES.COMPLEMENTO_RES,
              3,
              CLIENTES.COMPLEMENTO_COM,
              4,
              CLIENTES.COMPLEMENTO_COBRANCA,
              5,
              ENDERECO_POR_INSCRICAO.COMPLEMENTO,
              NULL) COMPLEMENTO,
       DECODE(OS.TIPO_ENDERECO,
              1,
              NULL,
              2,
              CLIENTES.FACHADA_RES,
              3,
              CLIENTES.FACHADA_COM,
              4,
              CLIENTES.FACHADA_COBRANCA,
              5,
              ENDERECO_POR_INSCRICAO.FACHADA,
              NULL) FACHADA,
       DECODE(OS.TIPO_ENDERECO,
              1,
              CLIENTE_DIVERSO.FONE_CONTATO,
              2,
              CLIENTES.TELEFONE_RES,
              3,
              CLIENTES.TELEFONE_COM,
              4,
              CLIENTES.TELEFONE_CEL,
              5,
              ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
              NULL) FONE,
       DECODE(OS.TIPO_ENDERECO,
              1,
              CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
              2,
              CLIENTES.PREFIXO_RES,
              3,
              CLIENTES.PREFIXO_COM,
              4,
              CLIENTES.PREFIXO_CEL,
              5,
              ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
              NULL) PREFIXO,

       CLIENTES.ENDERECO_ELETRONICO,
       CLIENTES.EMAIL_NFE,
       NVL(CLIENTES.EMAIL_TRABALHO, CLIENTES.EMAIL2) AS EMAIL2

  FROM OS,
       CLIENTE_DIVERSO,
       CLIENTES,
       ENDERECO_POR_INSCRICAO,
       CIDADES                CIDADES_RES,
       CIDADES                CIDADES_COM,
       CIDADES                CIDADES_COBRANCA,
       CIDADES                CIDADES_DIV,
       UF                     UF_DIVERSO,
       UF                     UF_RES,
       UF                     UF_COM,
       UF                     UF_COBRANCA,
       UF                     UF_INSCRICAO
 WHERE OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE
   AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
   AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES(+)
   AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES(+)
   AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES(+)
   AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES(+)
   AND OS.INSCRICAO_ESTADUAL = ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL(+)
   AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE(+)
   AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF(+)
   AND CLIENTES.UF_RES = UF_RES.UF(+)
   AND CLIENTES.UF_COM = UF_COM.UF(+)
   AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF(+)
   AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF(+)
   AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
      
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND OS.NUMERO_OS = $P{NUMERO_OS}

)
 

SELECT Q_CLIENTES.NOME AS Q_CLIENTES_NOME,
Q_CLIENTES.RUA AS Q_CLIENTES_RUA,
Q_OS.NUMERO_OS AS Q_OS_NUMERO_OS,
Q_CLIENTES.BAIRRO AS Q_CLIENTES_BAIRRO,
Q_CLIENTES.CIDADE AS Q_CLIENTES_CIDADE,
Q_CLIENTES.TELEFONE_RES AS Q_CLIENTES_TELEFONE_RES,
Q_CLIENTES.TELEFONE_CEL AS Q_CLIENTES_TELEFONE_CEL,
Q_CLIENTES.TELEFONE_COM AS Q_CLIENTES_TELEFONE_COM,
Q_CLIENTES.ENDERECO_ELETRONICO AS Q_CLIENTES_ENDERECO_ELETRONICO,
Q_OS.NUMERO_OPR AS Q_OS_NUMERO_OPR,
Q_OS.DESCRICAO_MODELO AS Q_OS_DESCRICAO_MODELO,
Q_OS.TIPO_FABRICA AS Q_OS_TIPO_FABRICA,
Q_OS.PLACA AS Q_OS_PLACA,
Q_OS.DATA_VENDA AS Q_OS_DATA_VENDA,
Q_OS.CHASSI AS Q_OS_CHASSI,
Q_OS.VENCIMENTO_GARANTIA_FABRICA AS Q_OS_VENCIMENTO_GARANTIA_FABRI,
Q_CLIENTES.CEP AS Q_CLIENTES_CEP,
Q_CLIENTES.UF AS Q_CLIENTES_UF,
Q_EMPRESA.NOME_EMPRESA AS Q_EMPRESA_NOME_EMPRESA,
Q_EMPRESA.RUA AS Q_EMPRESA_RUA,
Q_EMPRESA.BAIRRO AS Q_EMPRESA_BAIRRO,
Q_EMPRESA.CIDADE AS Q_EMPRESA_CIDADE,
Q_EMPRESA.CGC AS Q_EMPRESA_CGC,
Q_EMPRESA.FONE AS Q_EMPRESA_FONE,
Q_OS.KM AS Q_OS_KM,
Q_OS.CLIENTE_AGUARDOU AS Q_OS_CLIENTE_AGUARDOU,
Q_OS.LAVAR_VEICULO AS Q_OS_LAVAR_VEICULO,
Q_OS.OBSERVACAO AS Q_OS_OBSERVACAO,
Q_OS.DESC_MOBILIDADE AS Q_OS_DESC_MOBILIDADE,
Q_OS.SIMMOB AS Q_OS_SIMMOB,
Q_OS.NAOMOB AS Q_OS_NAOMOB,
Q_OS.DATA_EMISSAO AS Q_OS_DATA_EMISSAO,
Q_OS.HORA_EMISSAO AS Q_OS_HORA_EMISSAO,
Q_OS.DATA_PROMETIDA AS Q_OS_DATA_PROMETIDA,
Q_OS.HORA_PROMETIDA AS Q_OS_HORA_PROMETIDA,
Q_OS.TOTAL_OS AS Q_OS_TOTAL_OS,
Q_OS.CONSULTOR_COMPLETO AS Q_OS_CONSULTOR_COMPLETO,
Q_OS.PRISMA AS Q_OS_PRISMA,
Q_OS.RETRABSIM AS Q_OS_RETRABSIM,
Q_OS.RETRABNAO AS Q_OS_RETRABNAO,
Q_OS.PECA_USADA_FICA_CLIENTE AS Q_OS_PECA_USADA_FICA_CLIENTE,
Q_OS.TEXTO As TEXTO_FABRICA,
Q_OS.SIGNATURE As Q_OS_SIGNATURE
FROM Q_OS, Q_EMPRESA, Q_CLIENTES
WHERE Q_OS.COD_EMPRESA = Q_EMPRESA.COD_EMPRESA
      AND Q_OS.COD_CLIENTE = Q_CLIENTES.COD_CLIENTE]]>
	</queryString>
	<field name="Q_CLIENTES_NOME" class="java.lang.String"/>
	<field name="Q_CLIENTES_RUA" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="Q_CLIENTES_BAIRRO" class="java.lang.String"/>
	<field name="Q_CLIENTES_CIDADE" class="java.lang.String"/>
	<field name="Q_CLIENTES_TELEFONE_RES" class="java.lang.String"/>
	<field name="Q_CLIENTES_TELEFONE_CEL" class="java.lang.String"/>
	<field name="Q_CLIENTES_TELEFONE_COM" class="java.lang.String"/>
	<field name="Q_CLIENTES_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_OPR" class="java.lang.String"/>
	<field name="Q_OS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="Q_OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="Q_OS_PLACA" class="java.lang.String"/>
	<field name="Q_OS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CHASSI" class="java.lang.String"/>
	<field name="Q_OS_VENCIMENTO_GARANTIA_FABRI" class="java.lang.String"/>
	<field name="Q_CLIENTES_CEP" class="java.lang.String"/>
	<field name="Q_CLIENTES_UF" class="java.lang.String"/>
	<field name="Q_EMPRESA_NOME_EMPRESA" class="java.lang.String"/>
	<field name="Q_EMPRESA_RUA" class="java.lang.String"/>
	<field name="Q_EMPRESA_BAIRRO" class="java.lang.String"/>
	<field name="Q_EMPRESA_CIDADE" class="java.lang.String"/>
	<field name="Q_EMPRESA_CGC" class="java.lang.String"/>
	<field name="Q_EMPRESA_FONE" class="java.lang.String"/>
	<field name="Q_OS_KM" class="java.lang.Double"/>
	<field name="Q_OS_CLIENTE_AGUARDOU" class="java.lang.String"/>
	<field name="Q_OS_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="Q_OS_OBSERVACAO" class="java.lang.String"/>
	<field name="Q_OS_DESC_MOBILIDADE" class="java.lang.String"/>
	<field name="Q_OS_SIMMOB" class="java.lang.String"/>
	<field name="Q_OS_NAOMOB" class="java.lang.String"/>
	<field name="Q_OS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="Q_OS_DATA_PROMETIDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="Q_OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="Q_OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="Q_OS_PRISMA" class="java.lang.String"/>
	<field name="Q_OS_RETRABSIM" class="java.lang.String"/>
	<field name="Q_OS_RETRABNAO" class="java.lang.String"/>
	<field name="Q_OS_PECA_USADA_FICA_CLIENTE" class="java.lang.String"/>
	<field name="TEXTO_FABRICA" class="java.lang.String"/>
	<field name="Q_OS_SIGNATURE" class="java.io.InputStream"/>
	<variable name="Variable_1" class="java.lang.Integer">
		<variableExpression><![CDATA[1]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<variable name="MOSTRAR_HEADER_COLUMN" class="java.lang.String">
		<variableExpression><![CDATA["S"]]></variableExpression>
		<initialValueExpression><![CDATA["S"]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="330">
			<printWhenExpression><![CDATA[new Boolean($V{MOSTRAR_HEADER_COLUMN}=="S")]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="555" height="330" uuid="fba0c54a-624c-48b8-b3bb-fd5b7f725815"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement x="1" y="25" width="150" height="91" uuid="1b7cbc0d-241a-4abc-9066-920d9f31dfbd">
						<printWhenExpression><![CDATA[Boolean.FALSE]]></printWhenExpression>
					</reportElement>
					<textField>
						<reportElement mode="Transparent" x="0" y="28" width="133" height="10" uuid="9533e353-7fe5-477e-acfc-24b17ff21baa"/>
						<textElement textAlignment="Left">
							<font size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_EMPRESA_NOME_EMPRESA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="0" y="38" width="133" height="10" uuid="39e35891-909b-468c-972b-c9160c09ebb2"/>
						<textElement textAlignment="Left">
							<font size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_EMPRESA_RUA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="0" y="48" width="50" height="10" uuid="5490e9bc-5730-457b-8088-364208de7505"/>
						<textElement textAlignment="Left">
							<font size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_EMPRESA_BAIRRO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="50" y="48" width="5" height="10" uuid="dfcb5c5c-1044-4bed-891f-f23801291265"/>
						<textElement textAlignment="Center">
							<font size="7"/>
						</textElement>
						<text><![CDATA[,]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="55" y="48" width="78" height="10" uuid="09412975-43cd-4164-ad4d-423eb5df798c"/>
						<textElement textAlignment="Left">
							<font size="7"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_EMPRESA_CIDADE}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="0" y="58" width="26" height="10" uuid="45127c96-e78b-4c03-828f-81cd0560b37d"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[CNPJ:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="26" y="58" width="107" height="10" uuid="3961c2e1-25a3-48d5-91fe-3a6b940b0381"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_EMPRESA_CGC}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="0" y="68" width="35" height="10" uuid="20f0190f-973e-40cc-b23d-138987440246"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Telefone:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="35" y="68" width="98" height="10" uuid="7e9adb30-aa2a-4962-88e5-42599e6d7683"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_EMPRESA_FONE}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="0" y="78" width="25" height="10" uuid="c2a80030-ef69-459e-8ea1-155758032a98"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Email:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="25" y="78" width="108" height="10" uuid="4e726380-f0ed-4c36-b618-d9a6d22be15d"/>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["q_empresaEmailVar"]]></textFieldExpression>
					</textField>
				</frame>
				<image hAlign="Left">
					<reportElement x="1" y="1" width="74" height="44" uuid="1558c015-5bb8-4e98-83fc-98d689660f21"/>
					<imageExpression><![CDATA[$P{MARCA}.equals("CITROEN") ? 
		 	$P{COPIA_CLIENTE}.equals("N") ? $P{DIR_IMAGE_LOGO} + "crmservice10303.png" : $P{DIR_IMAGE_LOGO} + "crmservice10302.png"
: $P{MARCA}.equals("PEUGEOT") ? $P{DIR_IMAGE_LOGO} + "crmservice103011.PNG"
: null]]></imageExpression>
				</image>
				<image>
					<reportElement x="494" y="0" width="60" height="49" uuid="e6c3aa99-6e3d-4f96-a601-28c9b5a60136"/>
					<imageExpression><![CDATA[$P{MARCA}.equals("CITROEN") ? $P{DIR_IMAGE_LOGO} + "crmservice10301.png"
: $P{MARCA}.equals("PEUGEOT") ? $P{DIR_IMAGE_LOGO} + "crmservice103010.PNG"
: null]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="292" y="36" width="53" height="10" uuid="a51cf4a2-7202-46b2-895e-814ef3628ddf"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Nº OPR:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="292" y="25" width="53" height="10" uuid="6d23fa08-839f-464d-b5b1-ea24765d614a"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Nº da OS:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="292" y="47" width="53" height="10" uuid="868aa8f5-dce0-42d4-a6ee-dbeb666569be"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Modelo:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="292" y="58" width="53" height="10" uuid="e9995085-b808-4d29-8b30-a60c11bbf2fd"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo de OS:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="292" y="102" width="57" height="10" uuid="62125ed0-5daa-4807-8d54-36648880e633"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Cód. Imputação:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="292" y="91" width="94" height="10" uuid="42809369-3edd-4a10-9181-36556caeeedc"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Fim de contrato de garantia:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="292" y="80" width="25" height="10" uuid="225749c2-a694-4fc7-b2be-6c9ce9d66bb7"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[VIN:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="292" y="69" width="91" height="10" uuid="7c43edeb-99f7-4ece-b95e-3417972df948"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Data de Inicio da Garantia:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="404" y="58" width="24" height="10" uuid="b6dd330f-9c78-47f3-9131-81393e6f2e86"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Placa:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="345" y="25" width="59" height="10" uuid="f15a18ec-c9cb-4528-9a45-796a5fc38c30"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="345" y="36" width="59" height="10" uuid="3b3d055d-d3aa-42ec-a5b1-cf706d678394"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_OPR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="345" y="47" width="134" height="10" uuid="09ede6d6-114c-4e20-91e2-0fcfd28924c4"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DESCRICAO_MODELO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="345" y="58" width="59" height="10" uuid="591dbd07-1212-41f3-921c-52768f3b100d"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TIPO_FABRICA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="428" y="58" width="51" height="10" uuid="f0d690d7-3b8f-4261-9fd0-b60ea163945b"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="383" y="69" width="55" height="10" uuid="768d44e3-5bfc-4827-996d-29425e280e2c"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="317" y="80" width="121" height="10" uuid="c2c0a3f5-18e0-41bb-82bb-ca2df7580f6b"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CHASSI}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="386" y="91" width="52" height="10" uuid="ddcc2d63-d0c9-4415-8b1e-64c2050a23f7"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_VENCIMENTO_GARANTIA_FABRI}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="349" y="102" width="89" height="10" uuid="85ea278c-fbea-4597-81c9-77fc84cd2ae9"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="335" y="158" width="47" height="11" uuid="cbb988dc-d959-4707-8fad-333ce048bc0f"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[B      / D]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="335" y="147" width="47" height="11" uuid="847b305a-27c3-4e48-8ee2-4b9ea373c6b1"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[B      / D]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="280" y="296" width="111" height="10" uuid="439b733a-11fc-43ff-ac30-b5832cd14867"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim     /  Não     / R$:............]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="119" y="285" width="47" height="10" uuid="5de6aaad-5c7e-43e1-8b10-b214e3a12136"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim     /  Não]]></text>
				</staticText>
				<image scaleImage="RetainShape">
					<reportElement x="197" y="179" width="342" height="82" uuid="f305e0c3-14a0-4028-90eb-8a777c838b0a"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice10306.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement key="" style="Cor1" x="0" y="117" width="555" height="15" uuid="d31d39c8-95a8-4aac-89a5-be547f0c69c1"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[INSPEÇÃO FÍSICA]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor2" mode="Opaque" x="185" y="135" width="197" height="12" uuid="32fde232-9c0f-42aa-91b2-f3e391ea3bc8">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[CONTROLE EXTERNO DIANTEIRO]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor2" mode="Opaque" x="387" y="135" width="168" height="12" uuid="b084f000-c74f-451b-b111-1b7cc88e4759"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[CONTROLE EXTERNO TRASEIRO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="117" y="118" width="415" height="12" forecolor="#FFFFFF" uuid="963aee4f-ac33-438d-acf5-42996fb9cb97"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[(Estes controles são constatações visuais de estado externo das peças no dia da recepção do veículo)]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="171" y="149" width="6" height="6" uuid="5818160f-6e8e-453d-a07f-a1800b67e92c">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="1" y="148" width="110" height="18" uuid="f9531319-36de-42b0-aec8-819d2fb5e8f1"/>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Quilometragem:...............]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="113" y="147" width="58" height="10" uuid="ebea3e74-eb81-4dc9-a88d-083d6e70e6a9"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Triângulo/Macaco]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="113" y="157" width="58" height="10" uuid="86204d58-e025-4d84-8e69-4cf0bae6ac16"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Porca Anti-roubo]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="171" y="159" width="6" height="6" uuid="*************-46fb-9123-1c0ee2a8587c">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="113" y="167" width="58" height="10" uuid="9823e88a-4e83-497e-926c-92f69cf22cac"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tapetes]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="171" y="169" width="6" height="6" uuid="ae73a5d8-f2cc-4bc8-985f-b6683f2ab9bb">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="185" y="147" width="58" height="10" uuid="a058f6e7-170a-4c36-a4f0-7ad657691c90"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Pneus diânteiros]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="185" y="157" width="78" height="10" uuid="36c6f366-2e6c-4838-9963-d98c024c56b7"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Limpadores diânteiros]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="387" y="147" width="58" height="10" uuid="b2396a47-98f7-4b41-ba09-380ecd08b932"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Pneus traseiros]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="387" y="157" width="67" height="10" uuid="96982b65-463b-4081-8345-f8f993bb6fb0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Limpador traseiro]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="507" y="147" width="45" height="11" uuid="7ba6e233-a989-47c1-9b68-639587206e87"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[B      / D]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="507" y="158" width="45" height="11" uuid="971a000e-67a0-422c-8dd5-59586da57ef5"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[B      / D]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="387" y="167" width="29" height="10" uuid="067c6b8f-fd46-43cf-a030-7230045977e4"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Estepe]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="416" y="169" width="6" height="6" uuid="7a19fb09-4185-4cac-86cb-a868c502f18a"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="498" y="169" width="6" height="6" uuid="b27d7106-7ff0-442f-95af-8fe024aa1900"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="432" y="167" width="66" height="10" uuid="225678af-3b1c-4117-8c68-b9971ad79a11"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Kit de reparo pneus]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor2" mode="Opaque" x="185" y="263" width="370" height="12" uuid="3229ec5c-3584-455a-96f5-af4cabb900e3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="4"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[SEGURADORA]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor2" mode="Opaque" x="1" y="263" width="181" height="12" uuid="943655d1-b857-451c-adc8-49625508f16f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="4"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[OUTROS]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="275" width="116" height="10" uuid="be34e0a4-49ad-45ab-9001-cf122669d2e7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente aguarda na concessionária:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="285" width="116" height="10" uuid="da462985-5b69-4243-a1cf-81ea1c09bed6">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Solicitação de Mobilidade:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="161" y="287" width="6" height="6" uuid="*************-4e32-bdf2-d18e3c8fc449"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="2" y="295" width="180" height="11" uuid="9f3d0469-377f-431e-9fa7-416e75ccca80"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo de transporte: ............................................             ......]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="306" width="161" height="10" uuid="0883f773-f2f2-48ea-9723-bd7e78bb9e45"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Limpeza do veículo:                         Int.     /  Ext.]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="153" y="308" width="6" height="6" uuid="80fee730-b9e1-4701-9f4a-17fb90e4597d"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="133" y="287" width="6" height="6" uuid="30eed800-125c-4941-a4d1-8dd246a57e0b">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="125" y="308" width="6" height="6" uuid="65e81212-1e06-4f3f-868a-82f065443141"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="185" y="275" width="95" height="10" uuid="c55608b3-cec7-4b7b-bbe7-84fdbc973052"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Assistência remota]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="323" y="277" width="6" height="6" uuid="54155e4f-527e-477a-be9e-cd5d57fb28b8"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="280" y="285" width="46" height="10" uuid="16fbd5fb-58b5-4ce2-9e6d-69a1fbd290cc"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim     /  Não]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="323" y="287" width="6" height="6" uuid="82a3631e-b3c3-40b9-88ff-4522626324c2"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="295" y="288" width="6" height="6" uuid="50f010ef-5db6-472e-b820-e17a3c2b0b75"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="280" y="275" width="46" height="10" uuid="3ab40798-1153-4def-9b1f-74e487558e36"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim     /  Não]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="295" y="277" width="6" height="6" uuid="15c57b80-a6d2-4bc6-bd9a-0c90ebda271f"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="185" y="285" width="95" height="10" uuid="403752d2-1031-4aa5-98ec-676f0e5abd9c"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Cobertura dos custos]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="185" y="296" width="95" height="10" uuid="d06afd84-2960-424f-a8ef-ff8301c9a230"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Franquia]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="280" y="307" width="111" height="10" uuid="e3dd1de1-f47a-437b-b205-47020d64238a"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim     /  Não     / R$:............]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="323" y="309" width="6" height="6" uuid="30c772b3-81eb-473b-b5a6-02518df0b116"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="295" y="309" width="6" height="6" uuid="58cdcbc6-6a4d-4819-a8e8-8f87d6fd7b3e"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="295" y="298" width="6" height="6" uuid="954d65c1-1e8d-4b24-82cb-117d01d948c0"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="185" y="307" width="95" height="10" uuid="c191fd66-84d3-486a-ad30-f2e591401d8f"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Perda Total]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="397" y="275" width="112" height="10" uuid="331ac3f7-dcfb-4f83-9594-98ac57aa1c17"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Pedido de Sinistro:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="397" y="293" width="155" height="10" uuid="07b87b14-3903-4f5c-9a97-d8f13cb1e7e3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[.................................................................]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="397" y="304" width="155" height="10" uuid="81824daa-c442-4ceb-bb6a-0cc0ba947def"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Companhia de seguros/outros terceiros:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="397" y="317" width="155" height="10" uuid="fb7be2d0-0c6e-47f8-bb9b-97c8aafa9e64"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[..................................................................]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="0" y="318" width="555" height="12" uuid="a247ad09-14f1-4295-bf18-c449f52b39c8"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Comentários / Observações:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="323" y="298" width="6" height="6" uuid="44894862-166e-4be3-bf43-0a6618c2ee8a"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="343" y="149" width="6" height="6" uuid="ae4fb5c1-05ed-406f-9061-904a8f833751"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="368" y="149" width="6" height="6" uuid="7334847a-8eea-4bf2-b381-56edbc3e8b4e"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="368" y="160" width="6" height="6" uuid="f4c51b0f-23c4-4be0-b9e6-bd42c23dd7e4"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="343" y="160" width="6" height="6" uuid="5fb10a8d-6e53-461a-be1a-bc5020d7ef13"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField pattern="">
					<reportElement mode="Transparent" x="515" y="149" width="6" height="6" uuid="ed65ad49-7450-4a5a-8392-bd7aed08fd04"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="515" y="160" width="6" height="6" uuid="aefb3fe3-6f64-45cd-b183-4e84031ff8ce"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="540" y="149" width="6" height="6" uuid="e1173885-a951-4ed1-b001-8aa0bbd6e37c"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="540" y="160" width="6" height="6" uuid="18d3a987-af39-4b1f-b725-5d0b3a1c693e"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="356" y="240" width="28" height="10" uuid="347ad145-f56d-41de-bc6f-508bb0108308"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Legenda:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="327" y="250" width="92" height="10" uuid="d5f114f1-a7a3-49fd-8f1a-2618d06ac743"/>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[A: Amassado / R: Riscado / F: Faltante]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="359" y="180" width="52" height="9" uuid="e1d54199-e87d-4c8a-ae3e-df1138ba1d5c"/>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[B:Bom / D:Defeituoso]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor2" mode="Opaque" x="0" y="135" width="180" height="12" uuid="c767272e-62ae-4678-ac78-56cbaf3b5fad"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[CONTROLE INTERNO]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="59" y="152" width="44" height="10" uuid="1955a63e-1c9d-427e-9773-33abd97c6317"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_KM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="93" y="319" width="432" height="10" uuid="8c08aab3-16b0-4e42-986b-d6e195f174d1"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_OBSERVACAO}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="20" y="181" width="27" height="1" uuid="a39a0d88-fb57-4aa7-a9db-bd1ce378dc4f"/>
					<graphicElement>
						<pen lineWidth="11.0"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement mode="Transparent" x="66" y="295" width="113" height="10" uuid="99514b51-6fc4-4e42-bece-f534ce7a80dd"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DESC_MOBILIDADE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="245" y="55" width="20" height="10" uuid="4a3c8f84-5782-4b58-ae61-264c7263545b"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[- UF:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="146" y="35" width="34" height="10" uuid="b36ab9f9-c1dd-4f4e-ab0f-9b2808adf9b6"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Endereço:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="207" y="45" width="7" height="10" uuid="412c1883-9bd2-4a25-9964-55d6470caee7"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[-]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="180" y="81" width="106" height="10" uuid="059dc15b-9155-4886-bf92-ad90d4d85699"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_TELEFONE_CEL}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="146" y="69" width="15" height="10" uuid="6a838f56-454d-4ee0-b44f-92ef25e88db7"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Te:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="180" y="25" width="106" height="10" uuid="e5ab2c7d-5d21-49be-9950-725b1b4331cb"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="146" y="45" width="61" height="10" uuid="e6bfc734-9c00-4692-97ca-27a3769da0a6"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="265" y="55" width="21" height="10" uuid="d8202bc6-2b69-4208-ae79-b18e1fd30147"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_UF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="180" y="35" width="106" height="10" uuid="a69b2ec1-3c09-4144-a3ce-dfd4ffd10927"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_RUA}]]></textFieldExpression>
				</textField>
				<image scaleImage="RetainShape">
					<reportElement x="162" y="69" width="17" height="35" uuid="7ec702b6-e685-4fba-975b-ab7d6c7dcb95"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice10307.png"]]></imageExpression>
				</image>
				<textField>
					<reportElement mode="Transparent" x="180" y="106" width="108" height="10" uuid="f31c6829-def7-4a6c-8978-4c6411812ad2"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="203" y="55" width="42" height="10" uuid="1261333f-0db2-40b5-8d0b-49d1dea0ece6"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="180" y="69" width="106" height="10" uuid="3a494b93-1949-4a88-8f75-8eacb72c7bd4"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_TELEFONE_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="214" y="45" width="72" height="10" uuid="ab229c03-71b5-4ed8-99fb-1ab31e3a38ec"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="180" y="94" width="106" height="10" uuid="1e7cb552-f128-4324-9469-5c3444c071ee"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTES_TELEFONE_COM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="180" y="55" width="23" height="10" uuid="5bffd22e-2e94-4250-a993-db9e5de228de"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="146" y="106" width="33" height="10" uuid="0c997dac-8e31-4b2f-b048-2a96897bc642"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Email:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="146" y="25" width="34" height="10" uuid="00e8e1b1-e168-4daa-8702-89a260107c20"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<image scaleImage="RetainShape">
					<reportElement x="376" y="135" width="9" height="12" uuid="1956b3c6-a863-493c-909c-e42cfa174b78"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice10309.png"]]></imageExpression>
				</image>
				<image scaleImage="RetainShape">
					<reportElement x="173" y="135" width="9" height="12" uuid="43f4880d-e8e2-4b43-99d1-e76219374285"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice10309.png"]]></imageExpression>
				</image>
				<rectangle>
					<reportElement key="" style="Cor4" x="1" y="192" width="181" height="62" uuid="4d61e565-f515-4c92-9df0-71cdd61d34ff"/>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<image scaleImage="RetainShape">
					<reportElement x="1" y="166" width="181" height="95" uuid="32dea161-81e2-4ee9-b290-1035b0669fff"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice10308.png"]]></imageExpression>
				</image>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle">
					<reportElement x="146" y="1" width="255" height="21" uuid="d773a676-eb00-4fe7-afeb-8e381f3d1653">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{MARCA}.equals("CITROEN") ? 
		 	$P{COPIA_CLIENTE}.equals("N") ? $P{DIR_IMAGE_LOGO} + "crmservice10305.png" : $P{DIR_IMAGE_LOGO} + "crmservice10304.png"
: $P{MARCA}.equals("PEUGEOT") ? $P{DIR_IMAGE_LOGO} + "crmservice103012.PNG"
: null]]></imageExpression>
				</image>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="470">
			<printWhenExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("S"))]]></printWhenExpression>
			<subreport isUsingCache="true" overflowType="NoStretch">
				<reportElement x="0" y="1" width="555" height="145" uuid="ca8f2702-3453-425b-848c-91da19af3495">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COPIA_CLIENTE">
					<subreportParameterExpression><![CDATA[$P{COPIA_CLIENTE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="MARCA">
					<subreportParameterExpression><![CDATA[$P{MARCA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<returnValue subreportVariable="MOSTRAR_HEADER_COLUMN" toVariable="MOSTRAR_HEADER_COLUMN"/>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsCitroenPeugeotSubReclamacao.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement positionType="Float" x="0" y="409" width="555" height="60" isPrintWhenDetailOverflows="true" uuid="f6868bad-dc22-4fd6-99a1-5190b1d5fd11">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="ShowOutOfBoundContent" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="139" y="1" width="134" height="58" uuid="4bd27d17-8282-4cf9-86c6-2b403f5c21e1"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[O cliente solicita:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="141" y="11" width="6" height="6" uuid="87f1acd6-c0ac-4818-a70b-4343fca97eba">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="149" y="10" width="124" height="10" uuid="8958f6db-18ff-4a5e-ae20-96a0f120b706"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Ver as peças substituidas]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="141" y="23" width="6" height="6" uuid="a614154a-5fb8-4d6e-9413-044153234827"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="149" y="22" width="124" height="10" uuid="f08d9a6a-e16a-44aa-90da-21e51cae6749"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Levar as peças substituidas]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="141" y="34" width="6" height="6" uuid="0569e6c1-e5f7-4b6a-8545-a626f7c3093f"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="149" y="33" width="124" height="20" uuid="6aca06f8-3377-4349-9e9d-c08d7fc6fba5"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Saber a origem das peças substituidas]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="419" y="1" width="136" height="58" uuid="3a44d5e4-ce84-47e9-ba17-268b8550304e"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Solicito que o serviço acima seja realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="277" y="1" width="139" height="25" uuid="d94e1e65-4bf3-4fe3-badf-b114000a2acd"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Justified">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[1- Fica o concessionário autorizado a realizar testes dinâmicos no veículo responsabilizando-se unicamente pelo reparo em caso de sinistro.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="277" y="26" width="139" height="20" uuid="c5bb36eb-7a04-4e4e-9540-a706c09cfcef"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[2- A concessionária não se responsabiliza por objetos deixados no interior do veículo.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="278" y="46" width="127" height="10" uuid="976a0525-2bdd-4351-b788-3002e0546c96"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[CÓPIA CLIENTE / RECEPÇÃO]]></text>
				</staticText>
				<rectangle>
					<reportElement x="422" y="11" width="128" height="45" uuid="75b20535-e4f0-4374-b09e-4888d4dd5a2e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" isUsingCache="false">
					<reportElement x="424" y="14" width="124" height="39" uuid="e07b9ac6-8edd-4984-86cc-f9f66bd9741b">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{Q_OS_SIGNATURE}]]></imageExpression>
				</image>
			</frame>
			<frame>
				<reportElement key="" positionType="Float" mode="Opaque" x="0" y="278" width="555" height="132" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="12627407-4a43-4d0f-9b3d-b881acaa8220">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="0" y="3" width="136" height="26" uuid="3f525da1-3a20-4f13-9f51-a36fce8955ac"/>
					<box leftPadding="3" bottomPadding="4"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Nº do Prisma:]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="0" y="72" width="136" height="58" uuid="56ccb301-c2e8-4563-bea2-3f281fce4b04"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Concessionário:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="30" width="75" height="10" uuid="23210e06-d0ab-4257-8958-91703f7b15e7"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Terminal Service Box:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="43" width="95" height="10" uuid="be63bfe5-ccc8-43e5-8344-7c229d54bc94"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Consultor de Serviço:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="59" width="133" height="10" uuid="e987c3b1-d7cb-4429-88e7-7f8e77d01327"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[.........................................................]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="138" y="3" width="417" height="127" uuid="ddcbf0da-279b-42f6-88f4-971180b06887"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="140" y="80" width="258" height="10" uuid="d05d5a94-43e9-4e2f-a1cc-75e374f8e05a"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Estou de acordo em receber informações / comunicações eletrônicas provindas:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="140" y="97" width="6" height="6" uuid="1f4f6990-f016-4ffe-b2a2-f1a4143419b3"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="148" y="95" width="250" height="10" uuid="7705e580-3ae4-4d14-b284-252f747a5556"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Das concessionárias Citroën e/ou reparadores autorizados Citroën.]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="140" y="107" width="6" height="6" uuid="ae414bde-89be-411c-9ae2-998082c00061"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="148" y="105" width="250" height="10" uuid="abee4047-3344-44c2-bcd3-52e796120c6d"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[De qualquer empresa pertencente ao grupo Citroën.]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="140" y="117" width="6" height="6" uuid="2f521438-4826-47a8-839b-cb3a55beb41e"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="148" y="115" width="250" height="10" uuid="ef74f918-fcbe-4741-a366-2f3907539052"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[de qualquer parceiro Citroën.]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="2" y="54" width="133" height="10" uuid="ba04e5b0-7363-4c60-8b1e-7cd7e80da1de"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="78" y="30" width="58" height="10" isRemoveLineWhenBlank="true" uuid="b1f7a97e-b0db-4542-af7c-1efd07e59cda"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PRISMA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" x="0" y="145" width="555" height="134" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="cb09e9f0-71ae-4e39-a4fb-317193ff5731">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="0" y="76" width="424" height="14" uuid="f02035bf-e2ac-41a3-93d5-74cb7daf411c"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Observações:]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor1" mode="Opaque" x="0" y="92" width="551" height="15" uuid="ce01326e-b2d0-4963-b825-d26dabf67b2a"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[DE ACORDO CLIENTE]]></text>
				</staticText>
				<textField>
					<reportElement style="Cor2" mode="Opaque" x="0" y="118" width="91" height="15" uuid="692c38ad-c489-4a94-b550-29588ba33ba1"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="1" y="108" width="75" height="10" uuid="eb41e34e-00fe-4c35-ba35-53754c56fd79"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Data recepção veículo:]]></text>
				</staticText>
				<textField>
					<reportElement key="" style="Cor2" mode="Opaque" x="95" y="118" width="42" height="15" uuid="2e359c97-c838-4453-b65b-b56011b4ed80"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="147" y="108" width="84" height="10" uuid="91c84b6e-7b0a-4cd5-9ec8-a9f27e278469"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Data prevista da entrega:]]></text>
				</staticText>
				<textField>
					<reportElement key="" style="Cor2" mode="Opaque" x="147" y="118" width="84" height="15" uuid="c0740409-8e93-4870-af26-7d26143ee1e4"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="" style="Cor2" mode="Opaque" x="235" y="118" width="42" height="15" uuid="b95d32b1-a11e-42c3-9abe-adacc8389152"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_HORA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="286" y="108" width="84" height="10" uuid="dcc2cc12-9bf8-4ead-bf33-b8b8c28edafd"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Data efetiva da entrega:]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="286" y="118" width="84" height="15" uuid="7b198c0f-5e37-489e-9e3d-3e37fc66fe68"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="374" y="118" width="42" height="15" uuid="27687d21-8c6d-4867-8dcd-2103f9a27885"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="428" y="107" width="123" height="27" uuid="287505a9-eae9-4de3-b869-aa17a44b60b5"/>
					<textElement textAlignment="Justified">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[Todos os nossos reparos/diagnósticos devem ser pagos antes da restituição do veículo. O cliente declara que está ciente sobre a condição acima mencionada.]]></text>
				</staticText>
				<rectangle>
					<reportElement style="Cor1" x="427" y="76" width="128" height="31" uuid="f37a7e5b-e315-46be-be9b-1203a4d900b5"/>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement style="Cor4" mode="Opaque" x="429" y="78" width="123" height="27" uuid="e0b0478c-9cdf-4b04-ab8e-19b12cdb263a"/>
					<box>
						<pen lineWidth="2.0" lineColor="#0076A9"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TOTAL_OS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="" style="Cor5" x="0" y="2" width="555" height="72" uuid="3d0b3f40-6f53-4303-b595-579fb73355ee">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box padding="2"/>
					<textElement textAlignment="Justified">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEXTO_FABRICA} == null ? "A Stellantis e a rede de concessionárias tratarão os seus dados pessoais para elaborar proposta de serviços ou venda de veículo e prestar os serviços contratados por você ou para comunicar informações pertinentes ao seu veículo, como realização de recall, manutenções preventivas e garantias. Além disso, na medida do permitido pela legislação, a Stellantis e a concessionária na qual você solicitou proposta de serviços ou aquisição de veículo, ou tenha adquirido um produto ou contratado um serviço, compartilharão seus dados pessoais entre si para, entre outras finalidades, realizar estudos com objetivo de desenvolver melhorias em nossos produtos e serviços, enviar comunicações a você sobre a Stellantis, seus produtos e serviços, bem como entrar em contato para realização de pesquisas de satisfação.  Para saber todas as finalidades para as quais a Stellantis trata seus dados pessoais, e como exercitar seus direitos, consulte nossa Política de Privacidade de Clientes e Política de Privacidade de Potenciais Clientes. É importante que você também consulte a política de privacidade da concessionária, para saber mais sobre como ela trata seus dados pessoais." : $F{TEXTO_FABRICA}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="470">
			<printWhenExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N"))]]></printWhenExpression>
			<frame>
				<reportElement positionType="Float" x="0" y="145" width="555" height="134" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="ab81f891-0438-40f5-9d26-b7a2db074766">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="0" y="76" width="424" height="14" uuid="f0dd6617-097c-4ca3-8706-99a9748c7004"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Observações:]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor1" mode="Opaque" x="0" y="92" width="551" height="15" uuid="9da83299-c225-4c68-8c59-11d1af639bb8"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[DE ACORDO CLIENTE]]></text>
				</staticText>
				<textField>
					<reportElement style="Cor2" mode="Opaque" x="0" y="118" width="91" height="15" uuid="7a657166-d78b-4fe6-ad31-3dd5bb77ada2"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="1" y="108" width="75" height="10" uuid="11531883-cfd1-4bc6-a6d8-c5cad948b1b2"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Data recepção veículo:]]></text>
				</staticText>
				<textField>
					<reportElement key="" style="Cor2" mode="Opaque" x="95" y="118" width="42" height="15" uuid="f92facb2-8b6d-47ad-b519-62e38e31b5e0"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="147" y="108" width="84" height="10" uuid="9b1149c9-cb87-4f76-9008-59e5e23b58aa"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Data prevista da entrega:]]></text>
				</staticText>
				<textField>
					<reportElement key="" style="Cor2" mode="Opaque" x="147" y="118" width="84" height="15" uuid="a7043cde-50fc-4a14-8cb8-7286766896af"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="" style="Cor2" mode="Opaque" x="235" y="118" width="42" height="15" uuid="35c8a051-7a2a-4439-ba51-04181a229886"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_HORA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="286" y="108" width="84" height="10" uuid="7d08262f-38e4-40cf-91e8-14498790e7ea"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Data efetiva da entrega:]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="286" y="118" width="84" height="15" uuid="ed64ffb1-5b69-466b-9dfc-4fddea968985"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="374" y="118" width="42" height="15" uuid="54be55dd-28cc-464d-80b0-48fa03b7b724"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="428" y="107" width="123" height="27" uuid="fe094679-3b10-4ec4-b375-869f38ca9f3b"/>
					<textElement textAlignment="Justified">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[Todos os nossos reparos/diagnósticos devem ser pagos antes da restituição do veículo. O cliente declara que está ciente sobre a condição acima mencionada.]]></text>
				</staticText>
				<rectangle>
					<reportElement style="Cor1" x="427" y="76" width="128" height="31" uuid="3b0f5211-c486-4b93-a723-31fe4c517c17"/>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement style="Cor4" mode="Opaque" x="429" y="78" width="123" height="27" uuid="2d46efdd-014e-4d77-937a-340cc16d653b"/>
					<box>
						<pen lineWidth="2.0" lineColor="#0076A9"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TOTAL_OS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="" style="Cor5" x="0" y="2" width="555" height="72" uuid="c1da7b7a-1649-4aad-90e1-5d8880cde062">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box padding="2"/>
					<textElement textAlignment="Justified">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEXTO_FABRICA} == null ? "A Stellantis e a rede de concessionárias tratarão os seus dados pessoais para elaborar proposta de serviços ou venda de veículo e prestar os serviços contratados por você ou para comunicar informações pertinentes ao seu veículo, como realização de recall, manutenções preventivas e garantias. Além disso, na medida do permitido pela legislação, a Stellantis e a concessionária na qual você solicitou proposta de serviços ou aquisição de veículo, ou tenha adquirido um produto ou contratado um serviço, compartilharão seus dados pessoais entre si para, entre outras finalidades, realizar estudos com objetivo de desenvolver melhorias em nossos produtos e serviços, enviar comunicações a você sobre a Stellantis, seus produtos e serviços, bem como entrar em contato para realização de pesquisas de satisfação.  Para saber todas as finalidades para as quais a Stellantis trata seus dados pessoais, e como exercitar seus direitos, consulte nossa Política de Privacidade de Clientes e Política de Privacidade de Potenciais Clientes. É importante que você também consulte a política de privacidade da concessionária, para saber mais sobre como ela trata seus dados pessoais." : $F{TEXTO_FABRICA}]]></textFieldExpression>
				</textField>
			</frame>
			<subreport overflowType="NoStretch">
				<reportElement x="0" y="0" width="555" height="145" uuid="8335e53a-8263-4e31-b0a9-87ee997d204d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COPIA_CLIENTE">
					<subreportParameterExpression><![CDATA[$P{COPIA_CLIENTE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="MARCA">
					<subreportParameterExpression><![CDATA[$P{MARCA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<returnValue subreportVariable="MOSTRAR_HEADER_COLUMN" toVariable="MOSTRAR_HEADER_COLUMN"/>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsCitroenPeugeotSubReclamacao.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement positionType="Float" x="0" y="410" width="555" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="888d2ac2-c0c3-4ba3-8b28-a415746e0271">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="139" y="1" width="134" height="58" uuid="a01b614c-**************-5c2bae9898c9"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[O cliente solicita:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="141" y="11" width="6" height="6" uuid="86bf82ec-3085-40ed-b37b-0ebbc69c7f46">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="149" y="10" width="124" height="10" uuid="d5fc7bd5-4ea4-4baf-a72b-2a5eb29e7409"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Ver as peças substituidas]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="141" y="23" width="6" height="6" uuid="49cf6b2d-efbd-4621-9cbc-e6a3e7481663"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="149" y="22" width="124" height="10" uuid="58cac2ad-783f-4f36-8d56-502dae8d089d"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Levar as peças substituidas]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="141" y="34" width="6" height="6" uuid="e115dce9-b5bb-4a61-bf66-fdda4d4a2602"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="149" y="33" width="124" height="20" uuid="b10965ca-30de-4751-9fa8-903842265c47"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Saber a origem das peças substituidas]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="419" y="1" width="136" height="58" uuid="49ac4cb0-88cc-4653-80d2-7b7d32fbaa21"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Solicito que o serviço acima seja realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="277" y="1" width="139" height="25" uuid="47dc5bff-b11e-4908-a188-fccef65fcf7a"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Justified">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[1- Fica o concessionário autorizado a realizar testes dinâmicos no veículo responsabilizando-se unicamente pelo reparo em caso de sinistro.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="277" y="26" width="139" height="20" uuid="cb76c643-b8a9-46ef-b672-149790c374c1"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[2- A concessionária não se responsabiliza por objetos deixados no interior do veículo.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="278" y="46" width="127" height="10" uuid="93e466e3-1c9f-4b59-91a3-65fcd99d616f"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[CÓPIA OFICINA]]></text>
				</staticText>
				<rectangle>
					<reportElement x="422" y="11" width="128" height="45" uuid="d49e378a-12b0-4810-976f-b80c8e72f22a">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" isUsingCache="false">
					<reportElement x="424" y="14" width="124" height="39" uuid="7daac57e-2e61-417f-8b20-51dc8ed6ea5b">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{Q_OS_SIGNATURE}]]></imageExpression>
				</image>
			</frame>
			<frame>
				<reportElement positionType="Float" x="0" y="279" width="555" height="129" isPrintWhenDetailOverflows="true" uuid="ff215827-a9bd-44d2-8758-1a0733c0f067">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="0" y="3" width="136" height="26" uuid="bae24389-864c-4ea7-a789-5996154826fa"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Nº do Prisma:]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="0" y="71" width="136" height="58" uuid="523babd7-cf6c-4c66-af33-7c93b378736b"/>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Concessionário:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="30" width="74" height="10" uuid="e2de1be7-3835-4adf-958b-0861b15d7fec"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Terminal Service Box:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="43" width="74" height="10" uuid="66ce078e-7639-4a2f-9b2a-29e9dfc2ad11"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Consultor de Serviço:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="59" width="133" height="10" uuid="ebe6d661-d4c8-465d-be10-c20fa4b07cd2"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[.........................................................]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="139" y="3" width="258" height="83" uuid="d57e0deb-9f61-4891-a8a5-be3e45bd6968"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="141" y="35" width="84" height="15" uuid="4139b800-2609-479f-8fb8-a72fbdf440e3"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[DATA]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="227" y="35" width="43" height="15" uuid="16d7e8ff-7735-4461-84d6-414f005a6ccc"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[HORÁRIO]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="272" y="35" width="124" height="15" uuid="9dc445f9-27d1-4802-a269-f65c412fca63"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[ASSINATURA]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="141" y="6" width="104" height="10" uuid="2c140081-faa7-48e0-b0b9-cc72c05320d0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Veículo elétrico / hibrido]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="141" y="25" width="223" height="10" uuid="598dab4e-8df7-400a-a184-fdefe0ccfa82"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Veículo desligado por: ...............................................................]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="141" y="58" width="223" height="10" uuid="eb0f3683-408c-4fb8-8159-e2e39a4bca0b"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Veículo religado por: ................................................................]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="141" y="68" width="84" height="15" uuid="be60cdd3-5d0a-40d9-8f3d-8a2dba463546"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[DATA]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="227" y="68" width="43" height="15" uuid="648dd79d-7616-4431-8cb3-1dd45b88179a"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[HORÁRIO]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="272" y="68" width="124" height="15" uuid="719f252a-8825-4d94-8027-ab3fe26b3696"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[ASSINATURA]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="139" y="89" width="416" height="41" uuid="938c1973-9488-4696-8f7c-99e58fcf67d8"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="141" y="89" width="255" height="10" uuid="50b99787-94b6-4418-8f7b-1efded4246a8"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Estou de acordo em receber informações / comunicações eletrônica             s provindas:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="141" y="102" width="6" height="6" uuid="8ed7b75a-d286-406e-93d2-1e0dbef1d3d2"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="141" y="112" width="6" height="6" uuid="d776e64a-20cf-49d3-b957-199359a853b3"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="149" y="110" width="139" height="10" uuid="cbdf2e98-8eca-4c0d-8769-d1e4881b4082"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[De qualquer empresapertencente ao grupo]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="141" y="122" width="6" height="6" uuid="edccc994-83fa-46a7-8d46-f8fba081f765"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="149" y="120" width="70" height="10" uuid="893b1380-830b-4c28-a4ed-ec03ae15f009"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[De qualquer parceiro]]></text>
				</staticText>
				<staticText>
					<reportElement style="Cor4" mode="Opaque" x="401" y="2" width="154" height="46" uuid="9ddf7096-1bb4-490c-84dc-e1d62ce85935"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Para o técnico:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="401" y="50" width="84" height="10" uuid="0008e4d5-d5c3-4d04-b696-f42022f7780c"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Retrabalho]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="485" y="50" width="51" height="10" uuid="1fdfade6-272a-407c-b090-576ed99ebc3e"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim       /  Não]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="502" y="52" width="6" height="6" uuid="6012c3a5-c196-4ee8-83d8-86743a4c8660">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="536" y="52" width="6" height="6" uuid="0aff1796-7061-46ff-8ab0-a816bc60646b"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="401" y="62" width="84" height="10" uuid="d36dc0ca-53bd-4c22-9368-264e6708464e"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Operação Preventiva]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="485" y="62" width="51" height="10" uuid="09f1a9dc-7813-4454-99e8-3a3ede0554c9"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim       /  Não]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="502" y="64" width="6" height="6" uuid="11475cd0-f653-4477-be94-9418294eca7f"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="536" y="64" width="6" height="6" uuid="794895bc-563a-4cf4-8833-791006a0a773"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="401" y="74" width="84" height="10" uuid="b03e3d32-8c65-4686-9788-71d506ec6926"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Diagnóstico]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="485" y="76" width="6" height="6" uuid="e6d191be-7ac2-459b-8287-a994945fa69c"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="76" y="30" width="60" height="10" uuid="46e2445c-282f-40f7-aba8-79827d8d15dd"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PRISMA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="2" y="55" width="133" height="10" uuid="4110d4d3-bb0b-46e1-9af0-ef1b969534e2"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="337" y="100" width="102" height="10" uuid="d5b72914-c325-4751-aa47-49f4e2c72e8b"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["ExprVar_Marca"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="287" y="110" width="96" height="10" uuid="1f85cfed-11be-4ac9-853e-16513768f634"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["ExprVar_Marca"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="219" y="120" width="102" height="10" uuid="2342e082-bdc0-4945-9516-26beab5b2491"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["ExprVar_Marca"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="215" y="100" width="27" height="10" uuid="9896b9b0-1331-4c40-8445-8fe7d48a836d"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{MARCA}.equals("CITROEN") ? "Citroen"
: $P{MARCA}.equals("PEUGEOT") ? "Peugeot"
:" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="149" y="100" width="188" height="10" uuid="e43f0d6f-4724-430e-8ad1-a915ebd918d0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["Das concessionárias                e / ou reparadores autorizados"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="50">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N"))]]></printWhenExpression>
			<subreport isUsingCache="false" runToBottom="false" overflowType="Stretch">
				<reportElement x="0" y="2" width="555" height="40" isRemoveLineWhenBlank="true" uuid="0849446a-71fa-4214-b8f1-a21e5888f4c7">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COPIA_CLIENTE">
					<subreportParameterExpression><![CDATA[$P{COPIA_CLIENTE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="MARCA">
					<subreportParameterExpression><![CDATA[$P{MARCA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsCitroenPeugeotSubServicos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="48">
			<printWhenExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N"))]]></printWhenExpression>
			<subreport isUsingCache="false" runToBottom="false" overflowType="Stretch">
				<reportElement x="0" y="0" width="555" height="40" isRemoveLineWhenBlank="true" uuid="216b4455-1d38-43b8-b99c-75618630c391">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COPIA_CLIENTE">
					<subreportParameterExpression><![CDATA[$P{COPIA_CLIENTE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="MARCA">
					<subreportParameterExpression><![CDATA[$P{MARCA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsCitroenPeugeotSubPecas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="158">
			<printWhenExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N"))]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="75" width="555" height="83" uuid="fe3c422a-250c-4a1d-acfb-4a016bd63f4e"/>
				<staticText>
					<reportElement style="Cor1" mode="Opaque" x="0" y="0" width="555" height="15" uuid="c66b87b3-886b-4eef-abd4-2498720aa412">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[APROVAÇÃO DO CLIENTE QUANTO AOS SERVIÇOS ADICIONAIS]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="0" y="18" width="102" height="11" uuid="9683167c-1b30-4113-a134-2dc38af35136"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Consultor de serviço]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="489" y="18" width="66" height="11" uuid="59d90018-416a-4271-aa00-3ebeaec1646c"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[E-mail   SMS   Tel.]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="341" y="18" width="86" height="11" uuid="4f9a1a48-453b-45fd-90a5-921ffacf2029"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Aprovação (Data/Hora)]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="252" y="18" width="85" height="11" uuid="10ee7230-00ee-46a3-952f-f9efbf53a32d"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Solicitação (Data/Hora)]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="106" y="18" width="142" height="11" uuid="03e04464-b807-4033-a4d5-52d5c3d2ae06"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Descrição do serviço]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor2" mode="Opaque" x="431" y="18" width="54" height="11" uuid="1f079d77-30ff-4817-a177-5b010770aa65"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Total]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="106" y="31" width="142" height="11" uuid="836ee1ca-02f3-4694-a32c-b7a435a919d7"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="252" y="31" width="85" height="11" uuid="5a4c80f1-08c6-4225-9c7f-0e42ff3747ef"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="431" y="31" width="54" height="11" uuid="5b0db907-c73f-43f8-8aee-572fbe156720"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="341" y="31" width="86" height="11" uuid="a9afae4b-5f90-4917-9ff2-4dfd947142db"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="0" y="31" width="102" height="11" uuid="95326c23-1bc4-486b-a4c2-c71551ed704f"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="489" y="31" width="66" height="11" uuid="e9afa8f3-98ff-4156-9a31-8af45a81c9b4"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="498" y="33" width="6" height="6" uuid="38f15030-3510-41b8-8ca3-d3bdce2d0d14"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="521" y="33" width="6" height="6" uuid="eb96b089-3ebb-4882-94c9-a37023d11899"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="542" y="33" width="6" height="6" uuid="55c4a069-ef02-40df-ba4b-1492aa8f3e9b"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="489" y="44" width="66" height="11" uuid="0d76c327-f012-4f3f-8528-4590a7e79e72"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="252" y="44" width="85" height="11" uuid="40f2deba-f8ed-4d48-a43e-080c80d74c33"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="542" y="46" width="6" height="6" uuid="154e6d65-c874-4ce9-86f7-6a0fed925073"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="341" y="44" width="86" height="11" uuid="6e5328d1-0f67-4360-8501-8bfe33287115"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="498" y="46" width="6" height="6" uuid="57f1eb2b-5cb4-42b9-865c-404e2e7fbda9"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="521" y="46" width="6" height="6" uuid="4190f8bb-a611-4d3f-81d0-535305a632ad"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="431" y="44" width="54" height="11" uuid="da66d5bd-6699-4101-ae8c-96c6887adbd1"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="0" y="44" width="102" height="11" uuid="c8f45e83-12e9-4128-b291-3f1097e19226"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="106" y="44" width="142" height="11" uuid="61e9e319-460c-474f-8409-553f214f1b4b"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="489" y="57" width="66" height="11" uuid="2b9aed1f-db65-44e5-a5f9-af735413dce5"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="252" y="57" width="85" height="11" uuid="54240ad3-7713-48dc-9f3e-6639f8932b62"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="542" y="59" width="6" height="6" uuid="0e1d08e2-cf02-4eb0-9e16-ef6e5f1f8b75"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="" mode="Opaque" x="341" y="57" width="86" height="11" uuid="db391d3f-fd99-45b2-8390-d1f3f79fef0d"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="498" y="59" width="6" height="6" uuid="2cf0af1a-efaf-477a-901d-3b083dbe1538"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="521" y="59" width="6" height="6" uuid="4ee02538-a02e-4795-8159-29cad5c70cce"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="" mode="Opaque" x="431" y="57" width="54" height="11" uuid="1d8de1eb-b4a5-47fe-9fb3-a654bd9dac0c"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="0" y="57" width="102" height="11" uuid="21c50cb7-beea-4e38-b846-ebb9e64df678"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" mode="Opaque" x="106" y="57" width="142" height="11" uuid="882fbf4e-e533-47fc-b0c8-2d08aa4ada98"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="498" y="72" width="6" height="6" uuid="67fc1838-b415-40bf-ab4e-64875261363c"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="0" y="70" width="102" height="11" uuid="94212b4e-7ded-4a07-a331-1c56d8b9839a"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="252" y="70" width="85" height="11" uuid="e0a658b0-2cd7-4158-b65f-fe619f9c00b3"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="542" y="72" width="6" height="6" uuid="4bcbba59-b1cf-4c4e-8797-19c806f0f068"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="521" y="72" width="6" height="6" uuid="30d83f94-dbef-4305-808d-d4599aebf93b"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="341" y="70" width="86" height="11" uuid="22febc89-66d4-4e71-b02d-bb3dcfc74c15"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="489" y="70" width="66" height="11" uuid="437fa21b-51da-4864-9fdf-06895dd2012f"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="106" y="70" width="142" height="11" uuid="e67273ee-f2c1-44d4-a1bf-17b464e0c0c5"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement key="" style="Cor3" mode="Opaque" x="431" y="70" width="54" height="11" uuid="45230afe-4e89-47bb-93a1-5144f5b364f9"/>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="498" y="72" width="6" height="6" uuid="438a415e-c5d4-4bd1-baa3-506e06298a0b"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="542" y="72" width="6" height="6" uuid="b70a5896-7125-416b-aff6-516082a9ce14"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="521" y="72" width="6" height="6" uuid="a9ae5c58-92d8-463b-8c76-86eb87f74efb"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="0" width="555" height="74" uuid="80fc373a-07b4-4d4a-bee9-e0de364126bc"/>
				<staticText>
					<reportElement style="Cor1" mode="Opaque" x="0" y="0" width="555" height="15" uuid="c714d601-10ab-425b-8636-bf14cca3edea">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="4" bottomPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[AUTO CONTROLE]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="6" y="19" width="171" height="10" uuid="c57b0129-e117-4180-b639-1ed8eda89246"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Os trabalhos na ordem de serviço foram controlados:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="179" y="21" width="6" height="6" uuid="6a64f530-f0b4-4fde-ac3d-ff81d3a2485c"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="194" y="19" width="297" height="10" uuid="78ebf4b1-0ad6-4bf7-8e5e-05385e870f52"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Comentários:............................................................................................................................................]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="6" y="36" width="68" height="10" uuid="b902810b-f699-4c8b-b7b8-5e69701d6382"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Teste de rodagem]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="194" y="36" width="107" height="10" uuid="37933c92-aa6c-4666-9f41-bbe68ef4bdb5"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Comentários:..................................]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="305" y="36" width="140" height="10" uuid="72e57365-a72a-4f9d-9dc6-8e94a2ad7948"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Quilometragem Final:.................................]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="448" y="36" width="43" height="10" uuid="4c0e256f-4311-4e87-8de1-f42e0ab8739a"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Teste correto]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="493" y="36" width="51" height="10" uuid="c63532d6-e645-456b-8592-7e2429b03c8f"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim       /  Não]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="544" y="38" width="6" height="6" uuid="7908b3fe-03fb-4028-966a-cc93a8c768e6"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="510" y="38" width="6" height="6" uuid="cab17802-e08e-42c3-9f63-3e2ef521aeed"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="128" y="36" width="51" height="10" uuid="01507c19-103d-4b70-8cc3-51dab0363725"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim       /  Não]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="179" y="38" width="6" height="6" uuid="76230c54-0248-42a6-8d64-597d6790f8bf"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="145" y="38" width="6" height="6" uuid="22f0a200-c737-43bb-aaeb-e1fb29922d09"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="6" y="53" width="364" height="10" uuid="0687058f-8984-4859-afa8-e24f0deed6bb"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Comentários:..............................................................................................................................................................................]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
