<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Requisicoes" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a5d195c3-46e2-4e2e-9575-573e21d1a687">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="false"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="570"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="416"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="no_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="Endereco" uuid="b5cc2c7c-1709-4206-b75b-66bfdebecbb1">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Desenvolvimento"/>
		<queryString>
			<![CDATA[select  cli.cod_cliente,
        clid.CPF,
        CLI.NOME,
        cli.prefixo_res,
        cli.telefone_res,
        cli.prefixo_com,
        cli.telefone_com,
        CLI.RUA_RES,
        CLI.BAIRRO_RES,
        CLI.CEP_RES,
        CLI.COMPLEMENTO_RES,
        CID.DESCRICAO,
        CID.UF,
        CLI.FACHADA_RES
  from clientes cli, cliente_diverso clid, CIDADES CID
 where cli.cod_cliente = clid.cod_cliente
   AND CLI.COD_CID_RES = CID.COD_CIDADES  
   and cli.cod_cliente = 98374869615]]>
		</queryString>
		<field name="COD_CLIENTE" class="java.math.BigDecimal"/>
		<field name="CPF" class="java.lang.String"/>
		<field name="NOME" class="java.lang.String"/>
		<field name="PREFIXO_RES" class="java.lang.String"/>
		<field name="TELEFONE_RES" class="java.lang.String"/>
		<field name="PREFIXO_COM" class="java.lang.String"/>
		<field name="TELEFONE_COM" class="java.lang.String"/>
		<field name="RUA_RES" class="java.lang.String"/>
		<field name="BAIRRO_RES" class="java.lang.String"/>
		<field name="CEP_RES" class="java.lang.String"/>
		<field name="COMPLEMENTO_RES" class="java.lang.String"/>
		<field name="DESCRICAO" class="java.lang.String"/>
		<field name="UF" class="java.lang.String"/>
		<field name="FACHADA_RES" class="java.lang.String"/>
	</subDataset>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\35085\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT os.cod_empresa,
       os.numero_os,
       os.consultor_recepcao,
        CASE
          WHEN OS.NUMERO_OS < 0 THEN
            'ORC.: ' || TO_CHAR(ABS(OS.NUMERO_OS))
          ELSE
            'O.S: ' || TO_CHAR(OS.NUMERO_OS)
          END TITULO,
        (PKG_CRM_SERVICE_UTIL.GET_TIPO_OS(OS.COD_EMPRESA, OS.TIPO, 'TP')) TIPO,
        (PKG_CRM_SERVICE_UTIL.GET_TIPO_OS(OS.COD_EMPRESA, OS.TIPO, 'XX')) TIPO_DESC,
        TO_CHAR(TO_DATE(SYSDATE,'DD/MM/RRRR'), 'DD/MM/RRRR') DATA_ATUAL,
         INITCAP(PRODUTOMODELO.DESCRICAO_MODELO) AS VEICULO,  
        ODV.CHASSI,
        ODV.KM,
        NVL(ODV.ANO, PRODUTOMODELO.ANO_MODELO) ANO_MODELO,
        ODV.PLACA,
        CLI.COD_CLIENTE,
        CLI_DIV.CPF,
        CLI.NOME,
        CLI.RUA_RES,
        CLI.BAIRRO_RES,
        CLI.CEP_RES,
        CLI.COMPLEMENTO_RES,
        CID.DESCRICAO,
        CID.UF,
        CLI.FACHADA_RES,
        cli.prefixo_res,
        cli.telefone_res,
        cli.prefixo_com,
        cli.telefone_com,
        to_char(trunc(VSEST.DATA),'DD/MM/YYYY') DATA,
        to_char(trunc(sysdate),'DD/MM/YYYY') DATAATUAL_SERV,
        VSEST.OBSERVACAO        
   FROM OS,
        OS_DADOS_VEICULOS ODV,
        PRODUTOS_MODELOS  PRODUTOMODELO,
        PRODUTOS          P,
        CLIENTES          CLI,
        CLIENTE_DIVERSO   CLI_DIV,
        CIDADES           CID ,
        (SELECT VSE.DATA  
               , VSE.OBSERVACAO                   
          FROM VENDAS_SEM_ESTOQUE VSE
         WHERE VSE.COD_EMPRESA =  $P{COD_EMPRESA}
           AND VSE.DOCUMENTO  = $P{NUMERO_OS}
           AND ROWNUM = 1) VSEST        
 WHERE OS.COD_EMPRESA = ODV.COD_EMPRESA
   AND OS.NUMERO_OS = ODV.NUMERO_OS
   AND OS.COD_PRODUTO = PRODUTOMODELO.COD_PRODUTO
   AND OS.COD_MODELO = PRODUTOMODELO.COD_MODELO
   AND OS.COD_CLIENTE = CLI.COD_CLIENTE
   AND OS.COD_PRODUTO = P.COD_PRODUTO
   AND CLI.COD_CLIENTE = CLI_DIV.COD_CLIENTE
   AND CLI.COD_CID_RES = CID.COD_CIDADES    
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND OS.NUMERO_OS = $P{NUMERO_OS}]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="CONSULTOR_RECEPCAO" class="java.lang.String"/>
	<field name="TITULO" class="java.lang.String"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="TIPO_DESC" class="java.lang.String"/>
	<field name="DATA_ATUAL" class="java.lang.String"/>
	<field name="VEICULO" class="java.lang.String"/>
	<field name="CHASSI" class="java.lang.String"/>
	<field name="KM" class="java.lang.Double"/>
	<field name="ANO_MODELO" class="java.lang.String"/>
	<field name="PLACA" class="java.lang.String"/>
	<field name="COD_CLIENTE" class="java.lang.Double"/>
	<field name="CPF" class="java.lang.String"/>
	<field name="NOME" class="java.lang.String"/>
	<field name="RUA_RES" class="java.lang.String"/>
	<field name="BAIRRO_RES" class="java.lang.String"/>
	<field name="CEP_RES" class="java.lang.String"/>
	<field name="COMPLEMENTO_RES" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="UF" class="java.lang.String"/>
	<field name="FACHADA_RES" class="java.lang.String"/>
	<field name="PREFIXO_RES" class="java.lang.String"/>
	<field name="TELEFONE_RES" class="java.lang.String"/>
	<field name="PREFIXO_COM" class="java.lang.String"/>
	<field name="TELEFONE_COM" class="java.lang.String"/>
	<field name="DATA" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="71" splitType="Stretch">
			<subreport>
				<reportElement positionType="Float" x="0" y="5" width="554" height="66" isRemoveLineWhenBlank="true" uuid="e6b46a5a-93d8-41cc-beee-41c77135cf38">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"CabecalhoPadraoRetrato.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</title>
	<pageHeader>
		<band height="115">
			<rectangle>
				<reportElement mode="Opaque" x="1" y="79" width="554" height="18" backcolor="#E6E6E6" uuid="622b368b-3931-43ff-af1e-eefd07bd1912">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="42" width="554" height="18" backcolor="#E6E6E6" uuid="f044ce1b-c6b0-40ca-b473-27331128e380">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="137" y="44" width="42" height="14" uuid="cc7e89cf-f441-46bd-9cce-0637e9e9a757"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIPO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="412" y="44" width="142" height="14" uuid="920e8209-084e-451d-9453-f0971c80d3a9"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CONSULTOR_RECEPCAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="258" y="44" width="104" height="14" uuid="d43ce4de-3380-4d4e-9092-6237bdd71695"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DATA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="22" y="44" width="66" height="14" uuid="35007780-be87-407c-881d-d943472c6669"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new Integer($F{NUMERO_OS}.intValue())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="368" y="44" width="46" height="14" uuid="3eae6f4a-224a-4d4a-881e-479b3a98356f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Consultor:]]></text>
			</staticText>
			<staticText>
				<reportElement x="113" y="44" width="26" height="14" uuid="d2f9aff0-f937-4093-8f3c-0b76c99bba92">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo:]]></text>
			</staticText>
			<staticText>
				<reportElement x="221" y="44" width="39" height="14" uuid="f7759447-cec2-4c0d-b39a-11a5ae492e54">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Emissão:]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="44" width="20" height="14" uuid="4156bb02-3b82-4a89-96f3-2cd63f7c44ee">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[OS:]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="0" width="553" height="34" uuid="e4606971-00eb-4cdc-b2e7-12a9f1cb09a4">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="18" isBold="true"/>
				</textElement>
				<text><![CDATA[AUTORIZAÇÃO DE PEDIDO DE PEÇAS]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="61" width="46" height="14" uuid="59dcb895-0cf7-4826-8a06-3151a1c07fcd">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Veículo:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="39" y="61" width="178" height="14" uuid="5275ceb7-b539-47f0-b23e-5315a0658e4c"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{VEICULO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="222" y="61" width="31" height="14" uuid="27a534e5-dd1c-420f-9594-04ba2e0fef12">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Chassi:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="254" y="61" width="111" height="14" uuid="51c37f1c-5826-4380-a4ef-dda3724b9a4b"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHASSI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="368" y="61" width="26" height="14" uuid="6109fe18-a564-416f-b390-814a01b037ac">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Placa:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="395" y="61" width="78" height="14" uuid="f19708ab-8c80-4a67-ac4c-4cf8ecd56ea3"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PLACA}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="82" width="46" height="14" uuid="7d1d33e8-572c-45d1-a46c-973eff6f2be1">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Cliente:]]></text>
			</staticText>
			<staticText>
				<reportElement x="368" y="82" width="54" height="14" uuid="79a2dab3-35c5-41ee-a723-a26ee9c63c9a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CPF / CNPJ:]]></text>
			</staticText>
			<textField>
				<reportElement x="423" y="82" width="131" height="14" uuid="446aacd0-bb94-48fd-ab92-cbc6ebd383ef"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CPF}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="39" y="82" width="323" height="14" uuid="8b8e143b-1c60-47b7-afec-67ab21246ec4"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="98" width="46" height="14" uuid="d0aaedf2-c057-4145-8afe-5f677c540eff">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Telefone:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="39" y="98" width="121" height="14" uuid="07238b36-5d40-42ff-aa7d-a1fb696a5f4f"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PREFIXO_RES}+"-"+$F{TELEFONE_RES}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="31">
			<subreport>
				<reportElement x="0" y="0" width="554" height="31" uuid="7b09fd7f-a899-43d0-aa8c-51253b90f71f"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$F{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$F{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"AutorizaVendaPendenteItens.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
