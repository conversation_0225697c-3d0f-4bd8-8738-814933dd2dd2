<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RequisicoesItens" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="5" bottomMargin="5" uuid="b210b23a-3a04-4bce-bdc3-5829df4bd199">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Desenvolvimento"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUM_DOC" class="java.lang.Double"/>
	<parameter name="MOSTRAR" class="java.lang.String"/>
	<parameter name="IMPLOCACAO" class="java.lang.String"/>
	<parameter name="ORDENAR" class="java.lang.String"/>
	<parameter name="TIPO_DOC" class="java.lang.Integer"/>
	<queryString language="SQL">
		<![CDATA[select r.COD_EMPRESA,
       r.DOCUMENTO,
       r.CONTROLE_RESERVA,
       r.DATA_RESERVA,
       r.COD_ITEM,
       r.DESCRICAO,
       r.COD_FORNECEDOR,
       r.NOME_FORNECEDOR,
       r.QUANTIDADE,
       r.UNIDADE,
       r.PRECO_VENDA,
       r.TOTAL_ITEM,
       r.COD_LOCAL_ESTOQUE,
       r.NOME_DO_LOCAL,
       r.LOCACAO,
       r.LOCAL_DESCRICAO_COMP,
       r.part_number
  from table(pkg_Crm_service_util.get_rel_reserva_peca_locacao($P{COD_EMPRESA},$P{NUM_DOC},$P{TIPO_DOC})) r
 ORDER BY $P!{ORDENAR} , r.quantidade]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="DOCUMENTO" class="java.lang.Double"/>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="COD_FORNECEDOR" class="java.lang.Double"/>
	<field name="NOME_FORNECEDOR" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.lang.Integer"/>
	<field name="UNIDADE" class="java.lang.String"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="TOTAL_ITEM" class="java.lang.Double"/>
	<field name="COD_LOCAL_ESTOQUE" class="java.lang.Double"/>
	<field name="NOME_DO_LOCAL" class="java.lang.String"/>
	<field name="LOCACAO" class="java.lang.String"/>
	<field name="LOCAL_DESCRICAO_COMP" class="java.lang.String"/>
	<field name="PART_NUMBER" class="java.lang.String"/>
	<field name="CONTROLE_RESERVA" class="java.lang.String"/>
	<variable name="totalItens" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{COD_ITEM}]]></variableExpression>
	</variable>
	<variable name="qtdeTotal" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{QUANTIDADE}]]></variableExpression>
	</variable>
	<variable name="valorTotal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{TOTAL_ITEM}]]></variableExpression>
	</variable>
	<columnHeader>
		<band height="18" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#E6E6E6" uuid="9c26aca8-03ed-48b6-b7b3-b6a4b58c5ae2">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="134" y="2" width="46" height="14" uuid="17cf11c6-6eb7-459c-835e-16cd45da144b">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement x="216" y="2" width="46" height="14" uuid="fc3269ad-5df1-4958-ab4d-c89deecf0e55">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Fornec]]></text>
			</staticText>
			<staticText>
				<reportElement x="274" y="2" width="20" height="14" uuid="5cb4c10c-3d87-4e66-a480-ed0639e78248">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[UN]]></text>
			</staticText>
			<staticText>
				<reportElement x="298" y="2" width="24" height="14" uuid="7502c13f-5f7c-4b1f-bf2b-3d9b55be743e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement x="322" y="2" width="138" height="14" uuid="108f9cdb-6446-480f-a341-88a86332483e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$P{IMPLOCACAO} == "S"]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Local                  Lotação            Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement x="463" y="2" width="45" height="14" uuid="0a4ca2b0-da88-44eb-8fd9-eb35ca75eee1">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Vlr Unitário]]></text>
			</staticText>
			<staticText>
				<reportElement x="512" y="2" width="39" height="14" uuid="19a565f1-a0e3-4883-9917-44df47322fd8">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Vlr Total]]></text>
			</staticText>
			<textField>
				<reportElement x="52" y="2" width="80" height="14" uuid="2ac166ee-0413-4d47-a8d0-6632b92def91"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{MOSTRAR} == "ITEM"?"Cód. Item":"Part Number"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="2" width="46" height="14" uuid="a88f6a61-a18f-4f05-b8a9-5a8a7ec1e018">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Reserva]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="16" splitType="Stretch">
			<textField>
				<reportElement x="52" y="0" width="80" height="14" uuid="81c8b626-138f-4a7e-9dcf-89c00047de7a"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{MOSTRAR} == "ITEM"? $F{COD_ITEM}:$F{PART_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="134" y="0" width="81" height="14" uuid="ada07fed-3c39-4d62-bbd3-200a83549e5b"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="216" y="0" width="56" height="14" uuid="8fff3a6b-eca5-4024-83da-408e9f1ac790"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME_FORNECEDOR}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="274" y="0" width="22" height="14" uuid="219a46a8-1029-4716-b5f5-eabc33a6d3ce"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{UNIDADE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="298" y="0" width="22" height="14" uuid="b05fcef1-7740-4614-8b70-136792690622"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="322" y="0" width="120" height="14" uuid="1a01c733-7b8d-4ac8-a4bc-9f579c62028f">
					<printWhenExpression><![CDATA[$P{IMPLOCACAO} == "S"]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LOCAL_DESCRICAO_COMP}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="463" y="0" width="45" height="14" uuid="ab8952cf-989f-4f76-b9d0-3b7b5b103e9a"/>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="512" y="0" width="39" height="14" uuid="0d2df4ec-b5df-46d3-9a66-88d340b5e4fe"/>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOTAL_ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="2" width="47" height="14" uuid="29f21df5-d11a-47c8-a767-d146863c1b15"/>
				<textElement>
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CONTROLE_RESERVA}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="138" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#E6E6E6" uuid="3e712a3f-c9c7-4c77-8b33-1d037ea65838">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="1" y="2" width="46" height="14" uuid="9b7c2d6a-9636-48f8-97ed-aecfea43244f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Total de Itens:]]></text>
			</staticText>
			<textField>
				<reportElement x="48" y="2" width="52" height="14" uuid="316fe163-c224-4d7b-99f4-5c8299de5c4a"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalItens}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="299" y="4" width="32" height="12" uuid="9ba04c66-a8cc-4955-8600-ff44cef489a3"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{qtdeTotal}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="483" y="4" width="68" height="12" uuid="85482b0e-a18f-4260-9fb6-2501cd4a43e1"/>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{valorTotal}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement mode="Transparent" x="0" y="108" width="180" height="1" uuid="8075a3b7-1236-4f33-9603-f8dfbefee8bc"/>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<frame>
				<reportElement mode="Transparent" x="191" y="108" width="180" height="1" uuid="d4e9a8b3-2031-4a4c-b28b-d31d3a08394f"/>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<frame>
				<reportElement mode="Transparent" x="383" y="108" width="170" height="1" uuid="3c3f68ed-db6e-4c69-8aa5-fd14eac8f600">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<staticText>
				<reportElement x="220" y="109" width="130" height="14" uuid="dbe5f5e6-6da6-49e3-b281-27b174068a49">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Assinatura - Supridor de Peças]]></text>
			</staticText>
			<staticText>
				<reportElement x="381" y="109" width="170" height="14" uuid="2aabbb2b-c80c-4e6d-a960-04f8443da21f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Assinatura Requisitante]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="109" width="180" height="14" uuid="05dca2d1-0abd-4dec-8849-c4f68f648921">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Assinatura Produtivo]]></text>
			</staticText>
		</band>
	</columnFooter>
</jasperReport>
