<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsFCASubServicos" pageWidth="555" pageHeight="55" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="campoNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH Q_OS_FABRICA_RELACIONADAS AS
 (SELECT *
    FROM TABLE(PKG_CRM_SERVICE_UTIL.Get_Table_os_Relac_Num_Fabrica($P{NUMERO_OS},
                                                                   $P{COD_EMPRESA}))),

Q_OS_DESCONTO_FISCAIS as
 (SELECT (CASE
           WHEN NVL(OS_TIPOS_EMPRESAS.IMPRIMIR_IMP_RT_ORC_OS, 'N') = 'S' THEN
            (DESCONTOS_FISCAIS.VALOR_PIS_COFINS_CSLL +
            DESCONTOS_FISCAIS.VALOR_INSS_RETIDO +
            DESCONTOS_FISCAIS.VALOR_IRRF + DESCONTOS_FISCAIS.VALOR_ISS)
           ELSE
            0
         END) AS DESCONTO_FISCAIS_SERVICOS,
         (DESCONTOS_FISCAIS.VALOR_IMPOSTO_IPI +
         DESCONTOS_FISCAIS.VALOR_SUBSTITUICAO_TRIBUTARIA +
         DESCONTOS_FISCAIS.VALOR_FCP_ST) AS DESCONTO_FISCAIS_PECAS
    FROM OS,
         OS_TIPOS_EMPRESAS,
         TABLE(PKG_CRM_SERVICE_UTIL.GET_TABLE_DADOS_FISCAIS_OS($P{NUMERO_OS},
                                                               $P{COD_EMPRESA})) DESCONTOS_FISCAIS
   WHERE 1 = 1
     AND OS_TIPOS_EMPRESAS.TIPO(+) = OS.TIPO
     AND OS_TIPOS_EMPRESAS.COD_EMPRESA = OS.COD_EMPRESA
     AND DESCONTOS_FISCAIS.NUMERO_OS = OS.NUMERO_OS
     AND DESCONTOS_FISCAIS.COD_EMPRESA = OS.COD_EMPRESA
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}),
   
ADICIONAL AS 
    (SELECT S1.COD_SERVICO AS ADI_SRV, SUM(S1.PRECO_VENDA) AS ADI_PRECO         
     FROM OS_SERVICOS_ADICIONAIS S1, OS S2
     WHERE 1=1
       AND S1.COD_EMPRESA = S2.COD_EMPRESA
       AND S1.NUMERO_OS   = S2.NUMERO_OS
       AND S1.COD_EMPRESA = $P{COD_EMPRESA}
       AND NVL(S2.NUMERO_OS_FABRICA,S1.NUMERO_OS) = $P{NUMERO_OS}
     GROUP BY S1.COD_SERVICO)
     
,SERV AS (SELECT 'SERV' AS TIPO,
            NVL(S3.TIPO_MMC, 'S') AS TIPO_MMC, 
            S3.PRECO_VENDA + NVL(S4.ADI_PRECO, 0) AS PRECO_VENDA       
            FROM OS_SERVICOS S3, ADICIONAL S4, OS S5    
            WHERE 1=1
              AND S3.COD_EMPRESA = S5.COD_EMPRESA
              AND S3.NUMERO_OS   = S5.NUMERO_OS
              AND S3.COD_SERVICO = S4.ADI_SRV(+)
              AND S3.COD_EMPRESA = $P{COD_EMPRESA}
              AND NVL(S5.NUMERO_OS_FABRICA,S3.NUMERO_OS) = $P{NUMERO_OS} )     
     
, PEC AS (SELECT
            'PEÇA' AS TIPO,
            NVL(OSS.TIPO_MMC, 'S') AS TIPO_MMC,
            OS_REQUISICOES.QUANTIDADE *
            DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
            DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
            DECODE(OS_TIPOS.INTERNO, 'S',
            ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
            DECODE(ITENS.COD_TRIBUTACAO, '1',
            DECODE(PARM_SYS.REGIME_ICMS, 'S',
            DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
            DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                 0),
            0),
            OS_TIPOS.AUMENTO_PRECO_PECA),
            0),
            OS_TIPOS.AUMENTO_PRECO_PECA)) *
            DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
            'G', OS_REQUISICOES.PRECO_GARANTIA,
            'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
            'P', OS_REQUISICOES.PRECO_ORIGINAL,
            OS_REQUISICOES.CUSTO_CONTABIL)
            ) / 100,
            DECODE(OS_TIPOS.GARANTIA, 'S', OS_REQUISICOES.PRECO_GARANTIA,
            DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
            DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
            ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
            DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
            NVL(OS_REQUISICOES.PRECO_ORIGINAL, OS_REQUISICOES.PRECO_VENDA)) )/100) ))))) AS PRECO_VENDA
            FROM OS_REQUISICOES, ESTOQUE, ITENS, OS, VW_OS_TIPOS OS_TIPOS, FORNECEDOR_ESTOQUE , SEGURADORA,
            ITENS_FORNECEDOR, ITENS_CLASSE_CONTABIL ICC, PARM_SYS, PARM_SYS2, OS_SERVICOS OSS
            WHERE OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM (+)
            AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR (+)
            AND OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM (+)
            AND OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR (+)
            AND OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA (+)
            AND OS.NUMERO_OS = OSS.NUMERO_OS
            AND OS.COD_EMPRESA = OSS.COD_EMPRESA
            AND OS.TIPO = OS_TIPOS.TIPO
            AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA
            AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
            AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM (+)
            AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR (+)
            AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
            AND OS.COD_EMPRESA = PARM_SYS.COD_EMPRESA
            AND OS.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
            AND NVL(OS.NUMERO_OS_FABRICA,OS.NUMERO_OS) = $P{NUMERO_OS}
            AND OS.COD_EMPRESA = $P{COD_EMPRESA}
            AND OSS.COD_EMPRESA =  OS_REQUISICOES.COD_EMPRESA (+)
            AND OSS.NUMERO_OS = OS_REQUISICOES.NUMERO_OS (+)
            AND OSS.COD_SERVICO = OS_REQUISICOES.COD_SERVICO (+)
            AND OSS.ITEM = OS_REQUISICOES.ITEM (+)),

Q_OS_FECHAMENTO AS
 (SELECT NVL(T.SRV_VALOR_TOTAL_BRUTO, 0) SRV_VALOR_TOTAL_BRUTO,
         NVL(T.SRV_DESCONTO, 0) SRV_DESCONTO,
         NVL(T.SRV_DESC_PERCENT, 0) SRV_DESC_PERCENT,
         NVL(T.SRV_OS_LIQUIDO, 0) SRV_OS_LIQUIDO,
         NVL(T.PC_VALOR_TOTAL_BRUTO, 0) PC_VALOR_TOTAL_BRUTO,
         NVL(T.PC_DESCONTO, 0) PC_DESCONTO,
         NVL(T.PC_DESC_PERCENT, 0) PC_DESC_PERCENT,
         NVL(T.PC_OS_LIQUIDO, 0) PC_OS_LIQUIDO,
         NVL(T.SRV_VALOR_TOTAL_BRUTO, 0) + NVL(T.PC_VALOR_TOTAL_BRUTO, 0) TOTAL_BRUTO_GERAL,
         NVL(T.SRV_DESCONTO, 0) + NVL(T.PC_DESCONTO, 0) TOTAL_DESC_GERAL,
         NVL(T.PKG_TOT_IMPOSTOS, 0) PKG_TOT_IMPOSTOS,
         (NVL(T.SRV_VALOR_TOTAL_BRUTO, 0) + NVL(T.PC_VALOR_TOTAL_BRUTO, 0) +
         NVL(T.PKG_TOT_IMPOSTOS, 0)) -
         (NVL(T.SRV_DESCONTO, 0) + NVL(T.PC_DESCONTO, 0)) TOTAL_LIQ_GERAL,
         T.COD_EMPRESA,
         T.NUMERO_OS
    FROM (SELECT CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.VALOR_SERVICOS_BRUTO
                   ELSE
                    OS.ORC_SERV_BRUTO
                 END SRV_VALOR_TOTAL_BRUTO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.DESCONTOS_SERVICOS
                   ELSE
                    OS.ORC_SERV_DESCONTO
                 END SRV_DESCONTO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    CASE
                      WHEN NVL(ROUND(OS.VALOR_SERVICOS_BRUTO, 2), 0) = 0 THEN
                       0
                      ELSE
                       ROUND(100 * NVL(OS.DESCONTOS_SERVICOS, 0) /
                             NVL(OS.VALOR_SERVICOS_BRUTO, 0),
                             2)
                    END
                   ELSE
                    CASE
                      WHEN NVL(ROUND(OS.ORC_SERV_BRUTO, 2), 0) = 0 THEN
                       0
                      ELSE
                       ROUND(100 * NVL(OS.ORC_SERV_DESCONTO, 0) /
                             NVL(OS.ORC_SERV_BRUTO, 0),
                             2)
                    END
                 END SRV_DESC_PERCENT,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS
                   ELSE
                    OS.ORC_SERV_BRUTO - OS.ORC_SERV_DESCONTO
                 END SRV_OS_LIQUIDO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.VALOR_ITENS_BRUTO
                   ELSE
                    OS.ORC_ITEM_BRUTO
                 END PC_VALOR_TOTAL_BRUTO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.DESCONTOS_ITENS
                   ELSE
                    OS.ORC_ITEM_DESCONTO
                 END PC_DESCONTO,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    CASE
                      WHEN NVL(ROUND(OS.VALOR_ITENS_BRUTO, 2), 0) = 0 THEN
                       0
                      ELSE
                       ROUND(100 * NVL(ROUND(OS.DESCONTOS_ITENS, 2), 0) /
                             NVL(ROUND(OS.VALOR_ITENS_BRUTO, 2), 0),
                             2)
                    END
                   ELSE
                    CASE
                      WHEN NVL(ROUND(OS.ORC_ITEM_BRUTO, 2), 0) = 0 THEN
                       0
                      ELSE
                       ROUND(100 * NVL(ROUND(OS.ORC_ITEM_DESCONTO, 2), 0) /
                             NVL(ROUND(OS.ORC_ITEM_BRUTO, 2), 0),
                             2)
                    END
                 END PC_DESC_PERCENT,
                 CASE
                   WHEN OS.NUMERO_OS > 0 THEN
                    OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS
                   ELSE
                    OS.ORC_ITEM_BRUTO - OS.ORC_ITEM_DESCONTO
                 END PC_OS_LIQUIDO,
                 OS.PKG_TOT_IMPOSTOS,
                 OS.NUMERO_OS,
                 OS.COD_EMPRESA
            FROM OS, PARM_SYS2 PS2
           WHERE 1 = 1
             AND PS2.COD_EMPRESA = OS.COD_EMPRESA) T
   WHERE (T.COD_EMPRESA, T.NUMERO_OS) IN
         (SELECT * FROM Q_OS_FABRICA_RELACIONADAS))
SELECT SRV_VALOR_TOTAL_BRUTO,
       SRV_DESCONTO,
       SRV_DESC_PERCENT,
       SRV_OS_LIQUIDO,
       PC_VALOR_TOTAL_BRUTO,
       PC_DESCONTO,
       PC_DESC_PERCENT,
       PC_OS_LIQUIDO,
       TOTAL_BRUTO_GERAL,
       TOTAL_DESC_GERAL,
       PKG_TOT_IMPOSTOS,
       TOTAL_LIQ_GERAL,
       DESCONTO_FISCAIS_PECAS,
       DESCONTO_FISCAIS_SERVICOS,
       SERVICOS_SUGESTAO,
       PECAS_SUGESTAO,
       SERVICOS_FABRICANTE,
       PECAS_FABRICANTE,
       SERVICOS_ACESSORIO,
       PECAS_ACESSORIO
       
       
  FROM (SELECT SUM(SRV_VALOR_TOTAL_BRUTO) AS SRV_VALOR_TOTAL_BRUTO,
               SUM(SRV_DESCONTO)          AS SRV_DESCONTO,
               SUM(SRV_DESC_PERCENT)      AS SRV_DESC_PERCENT,
               SUM(SRV_OS_LIQUIDO)        AS SRV_OS_LIQUIDO,
               SUM(PC_VALOR_TOTAL_BRUTO)  AS PC_VALOR_TOTAL_BRUTO,
               SUM(PC_DESCONTO)           AS PC_DESCONTO,
               SUM(PC_DESC_PERCENT)       AS PC_DESC_PERCENT,
               SUM(PC_OS_LIQUIDO)         AS PC_OS_LIQUIDO,
               SUM(TOTAL_BRUTO_GERAL)     AS TOTAL_BRUTO_GERAL,
               SUM(TOTAL_DESC_GERAL)      AS TOTAL_DESC_GERAL,
               SUM(PKG_TOT_IMPOSTOS)      AS PKG_TOT_IMPOSTOS,
               SUM(TOTAL_LIQ_GERAL)       AS TOTAL_LIQ_GERAL
          FROM Q_OS_FECHAMENTO) SOMA_SERVICOS,
         Q_OS_DESCONTO_FISCAIS,
    ( 
      SELECT SUM(DECODE(SOMA.TIPO_MMC, 'S', DECODE(SOMA.TIPO, 'SERV', SOMA.PRECO_VENDA, 0), 0)) AS SERVICOS_SUGESTAO,
       SUM(DECODE(SOMA.TIPO_MMC, 'S', DECODE(SOMA.TIPO, 'PEÇA', SOMA.PRECO_VENDA, 0), 0))       AS PECAS_SUGESTAO,
       SUM(DECODE(SOMA.TIPO_MMC, 'F', DECODE(SOMA.TIPO, 'SERV', SOMA.PRECO_VENDA, 0), 0))       AS SERVICOS_FABRICANTE,
       SUM(DECODE(SOMA.TIPO_MMC, 'F', DECODE(SOMA.TIPO, 'PEÇA', SOMA.PRECO_VENDA, 0), 0))       AS PECAS_FABRICANTE,
       SUM(DECODE(SOMA.TIPO_MMC, 'A', DECODE(SOMA.TIPO, 'SERV', SOMA.PRECO_VENDA, 0), 0))       AS SERVICOS_ACESSORIO,
       SUM(DECODE(SOMA.TIPO_MMC, 'A', DECODE(SOMA.TIPO, 'PEÇA', SOMA.PRECO_VENDA, 0), 0))       AS PECAS_ACESSORIO
      FROM (SELECT TIPO,TIPO_MMC,PRECO_VENDA FROM SERV
      UNION ALL
      SELECT TIPO,TIPO_MMC,PRECO_VENDA FROM PEC 
      ) SOMA) SOMA_SERVICO_TIPO]]>
	</queryString>
	<field name="SRV_VALOR_TOTAL_BRUTO" class="java.lang.Double"/>
	<field name="SRV_DESCONTO" class="java.lang.Double"/>
	<field name="SRV_DESC_PERCENT" class="java.lang.Double"/>
	<field name="SRV_OS_LIQUIDO" class="java.lang.Double"/>
	<field name="PC_VALOR_TOTAL_BRUTO" class="java.lang.Double"/>
	<field name="PC_DESCONTO" class="java.lang.Double"/>
	<field name="PC_DESC_PERCENT" class="java.lang.Double"/>
	<field name="PC_OS_LIQUIDO" class="java.lang.Double"/>
	<field name="TOTAL_BRUTO_GERAL" class="java.lang.Double"/>
	<field name="TOTAL_DESC_GERAL" class="java.lang.Double"/>
	<field name="PKG_TOT_IMPOSTOS" class="java.lang.Double"/>
	<field name="TOTAL_LIQ_GERAL" class="java.lang.Double"/>
	<field name="DESCONTO_FISCAIS_PECAS" class="java.lang.Double"/>
	<field name="DESCONTO_FISCAIS_SERVICOS" class="java.lang.Double"/>
	<field name="SERVICOS_SUGESTAO" class="java.lang.Double"/>
	<field name="PECAS_SUGESTAO" class="java.lang.Double"/>
	<field name="SERVICOS_FABRICANTE" class="java.lang.Double"/>
	<field name="PECAS_FABRICANTE" class="java.lang.Double"/>
	<field name="SERVICOS_ACESSORIO" class="java.lang.Double"/>
	<field name="PECAS_ACESSORIO" class="java.lang.Double"/>
	<detail>
		<band height="55">
			<frame>
				<reportElement x="0" y="0" width="555" height="55" uuid="e754634b-cfb2-43ea-9a4b-4160e9ea35d8"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="4" y="2" width="60" height="12" uuid="37554637-d277-476f-99f6-dd25e12d7ae7"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Fechamento]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="76" y="5" width="61" height="11" uuid="5106e29b-1d7f-4274-afe0-e124fbe90cb3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[(MITRevisão)]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="156" y="5" width="61" height="11" uuid="a31675a4-497f-4861-b646-86fc87e862e5">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[(sugestão)]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="236" y="5" width="61" height="11" uuid="d7d8a514-4622-479f-be2f-c08ae1e8e0f6"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[(acessórios)]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="316" y="5" width="61" height="11" uuid="69cdc49b-d79b-4d28-9b1c-c8458b93c3f0"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[(descontos)]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="476" y="5" width="61" height="11" uuid="e434aa60-fed4-4e39-9eeb-7219ddb6937d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Total Líquido]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="19" y="16" width="44" height="11" uuid="42ae7ec4-8220-4ab9-af71-dd2620b1f17a">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Serviços:]]></text>
				</staticText>
				<line>
					<reportElement x="19" y="38" width="518" height="1" uuid="79c12fd9-363b-4064-8745-67bc8abfa483"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="19" y="27" width="44" height="11" uuid="a45f6af4-38cf-4cf4-aa50-3003d53b540e">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Peças:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="19" y="39" width="44" height="12" uuid="8575dc54-3d6c-4f0f-913e-0d2ace11af2e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="76" y="16" width="61" height="11" uuid="615d468a-1749-4afa-9584-cf16a8dca06f"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SERVICOS_FABRICANTE}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="76" y="27" width="61" height="11" uuid="f7a2e3e1-ac88-4847-9bc0-29a0a689b667">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PECAS_FABRICANTE}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="156" y="16" width="61" height="11" uuid="02c2df53-eba4-48ad-b626-c149bfad9a16">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SERVICOS_SUGESTAO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="156" y="27" width="61" height="11" uuid="2fc36c6e-41f2-4926-91d5-1a09695f537f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PECAS_SUGESTAO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="236" y="16" width="61" height="11" uuid="9ee8b2ec-0d8e-4978-b5fe-d31f3679a04c"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SERVICOS_ACESSORIO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="236" y="27" width="61" height="11" uuid="4ae87f82-5c04-40a2-80c7-b740e87822f9"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PECAS_ACESSORIO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="76" y="39" width="60" height="11" uuid="d3c4301c-ea11-48da-b86d-0810e6f93195"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SERVICOS_FABRICANTE} + $F{PECAS_FABRICANTE}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="156" y="39" width="61" height="11" uuid="4c1a999b-9264-48f4-b766-1c9bbd83f685"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SERVICOS_SUGESTAO} + $F{PECAS_SUGESTAO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="236" y="39" width="61" height="11" uuid="8e0c35cf-ad8d-4139-bc55-d6ad74283138"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SERVICOS_ACESSORIO} + $F{PECAS_ACESSORIO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="476" y="16" width="61" height="11" uuid="e473d9e7-5c4b-40d2-86be-250c132a40e4"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SRV_VALOR_TOTAL_BRUTO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="476" y="27" width="61" height="11" uuid="81184c83-10f4-4105-80fc-120b69c46cba">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PC_VALOR_TOTAL_BRUTO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="476" y="39" width="61" height="11" uuid="1c6e6c68-083c-4056-9e0e-b1166a570e34"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SRV_VALOR_TOTAL_BRUTO} + $F{PC_VALOR_TOTAL_BRUTO} - ($F{SRV_DESCONTO} + $F{DESCONTO_FISCAIS_SERVICOS} + $F{PC_DESCONTO} + $F{DESCONTO_FISCAIS_PECAS})]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="396" y="5" width="61" height="11" uuid="b182ca3b-84b6-424b-be32-9582c5b9c15c">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[(Impostos)]]></text>
				</staticText>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="316" y="16" width="61" height="11" uuid="7ee3b92e-5ef6-4233-9d5e-8d801d3dcf78">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SRV_DESCONTO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="316" y="27" width="61" height="11" uuid="c3d4f919-150f-4428-b2cb-9687f44337ea"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PC_DESCONTO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="396" y="16" width="61" height="11" uuid="960ed6ce-37d8-4fc2-bd15-c6d0734aa678">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCONTO_FISCAIS_SERVICOS}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="396" y="27" width="61" height="11" uuid="dccdf967-063c-4ed9-a8df-b574b654fc7a">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCONTO_FISCAIS_PECAS}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="316" y="39" width="62" height="11" uuid="bd473251-e46b-4160-a44a-7e595de29243"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SRV_DESCONTO} + $F{PC_DESCONTO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="396" y="39" width="61" height="11" uuid="38ad3885-418b-4b71-82d4-09555ed7584c"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{DESCONTO_FISCAIS_SERVICOS} +$F{DESCONTO_FISCAIS_PECAS}) * - 1.0]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
