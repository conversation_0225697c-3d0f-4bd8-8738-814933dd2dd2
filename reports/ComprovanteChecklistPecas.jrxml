<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklistPecas" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="18f6558a-0b1b-4a9c-aa22-de9f0b8b14e6">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="DataAdapter.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[select os_requisicoes.cod_item as cod_item_chave,
       nvl(itens_custos.cod_fiscal_item, os_requisicoes.cod_item) as cod_item,
       os_requisicoes.requisicao,
       os_requisicoes.cod_fornecedor,
       decode(fornecedor_estoque.oficial,
              'N',
              '*' || itens.descricao,
              itens.descricao) as descricao,
       itens.unidade,
       os_requisicoes.quantidade,
       os_requisicoes.causadora,
       SUBSTR(TO_CHAR(os_requisicoes.ITEM + 100), 2, 2) AS ITEM,
       itens.cod_max_desc,
       nvl(estoque.qtde, 0) as estoque_qtde,
       nvl(estoque.reservado, 0) as reservado,
       os_requisicoes.desconto_por_item,
       (case -- rule Price
         when os.status_os = 1 then
          os_requisicoes.preco_final
         when os.cortesia = 'S' then
          os_requisicoes.preco_cortesia
         when vw_os_tipos.interno = 'S' then
          round(((100 + decode(vw_os_tipos.aumenta_tributados,
                               'S',
                               decode(itens.cod_tributacao,
                                      '1',
                                      decode(parm_sys.regime_icms,
                                             'S',
                                             decode(parm_sys2.acessorio_tributa,
                                                    'S',
                                                    decode(icc.classe_peca,
                                                           2,
                                                           vw_os_tipos.aumento_preco_peca,
                                                           0),
                                                    0),
                                             vw_os_tipos.aumento_preco_peca),
                                      0),
                               vw_os_tipos.aumento_preco_peca)) *
                decode(vw_os_tipos.tipo_preco_peca,
                        'V',
                        os_requisicoes.preco_venda,
                        'G',
                        os_requisicoes.preco_garantia,
                        'F',
                        os_requisicoes.custo_fornecedor,
                        'P',
                        os_requisicoes.preco_original,
                        os_requisicoes.custo_contabil)) / 100,
                nvl(decode(parm_sys2.preco_de_venda_no_custo,
                           'S',
                           itens_custos.qtde_casas_decimais,
                           itens_fornecedor.qtde_casas_decimais),
                    2)) 
         when vw_os_tipos.garantia = 'S' then
          decode(vw_os_tipos.tipo_preco_peca,
                 'G',
                 os_requisicoes.preco_garantia,
                 nvl(os_requisicoes.preco_original,
                     os_requisicoes.preco_garantia))
         when nvl(os.fabrica, 'N') = 'S' then
          os_requisicoes.preco_garantia
         when sign(os.franquia) = 1 then
          os_requisicoes.preco_franquia
         else
          round(((100 - nvl(seguradora.desconto_requisicao, 0)) / 100) * case
                  when vw_os_tipos.tipo_preco_peca = 'V' then
                   os_requisicoes.preco_venda
                  when (nvl(os.seguradora, 'N') = 'N') and
                       (vw_os_tipos.os_externa_com_preco_garantia = 'S') then
                   os_requisicoes.preco_garantia
                  else
                   nvl(os_requisicoes.preco_original, os_requisicoes.preco_venda)
                end,
                decode(nvl(seguradora.desconto_requisicao, 0),
                       0,
                       6,
                       nvl(decode(parm_sys2.preco_de_venda_no_custo,
                                  'S',
                                  itens_custos.qtde_casas_decimais,
                                  itens_fornecedor.qtde_casas_decimais),
                           2)))
       end) as preco_venda,
       nvl(os_requisicoes.preco_liquido,
           round(os_requisicoes.quantidade * (case 
                   when os.status_os = 1 then
                    os_requisicoes.preco_final
                   when os.cortesia = 'S' then
                    os_requisicoes.preco_cortesia
                   when vw_os_tipos.interno = 'S' then
                    round(((100 + decode(vw_os_tipos.aumenta_tributados,
                                         'S',
                                         decode(itens.cod_tributacao,
                                                '1',
                                                decode(parm_sys.regime_icms,
                                                       'S',
                                                       decode(parm_sys2.acessorio_tributa,
                                                              'S',
                                                              decode(icc.classe_peca,
                                                                     2,
                                                                     vw_os_tipos.aumento_preco_peca,
                                                                     0),
                                                              0),
                                                       vw_os_tipos.aumento_preco_peca),
                                                0),
                                         vw_os_tipos.aumento_preco_peca)) *
                          decode(vw_os_tipos.tipo_preco_peca,
                                  'V',
                                  os_requisicoes.preco_venda,
                                  'G',
                                  os_requisicoes.preco_garantia,
                                  'F',
                                  os_requisicoes.custo_fornecedor,
                                  'P',
                                  os_requisicoes.preco_original,
                                  os_requisicoes.custo_contabil)) / 100,
                          nvl(decode(parm_sys2.preco_de_venda_no_custo,
                                     'S',
                                     itens_custos.qtde_casas_decimais,
                                     itens_fornecedor.qtde_casas_decimais),
                              2))
                   when vw_os_tipos.garantia = 'S' then
                    decode(vw_os_tipos.tipo_preco_peca,
                           'G',
                           os_requisicoes.preco_garantia,
                           nvl(os_requisicoes.preco_original,
                               os_requisicoes.preco_garantia))
                   when nvl(os.fabrica, 'N') = 'S' then
                    os_requisicoes.preco_garantia
                   when sign(os.franquia) = 1 then
                    os_requisicoes.preco_franquia
                   else
                    round(((100 - nvl(seguradora.desconto_requisicao, 0)) / 100) * case
                            when vw_os_tipos.tipo_preco_peca = 'V' then
                             os_requisicoes.preco_venda
                            when (nvl(os.seguradora, 'N') = 'N') and
                                 (vw_os_tipos.os_externa_com_preco_garantia = 'S') then
                             os_requisicoes.preco_garantia
                            else
                             nvl(os_requisicoes.preco_original, os_requisicoes.preco_venda)
                          end,
                          decode(nvl(seguradora.desconto_requisicao, 0),
                                 0,
                                 6,
                                 nvl(decode(parm_sys2.preco_de_venda_no_custo,
                                            'S',
                                            itens_custos.qtde_casas_decimais,
                                            itens_fornecedor.qtde_casas_decimais),
                                     2)))
                 end),
                 2)) as Preco_Total,
       os_requisicoes.seq_paf_item,
       'A' status,
       itens_fornecedor.ncm
  from os_requisicoes,
       estoque,
       itens,
       os,
       vw_os_tipos           os_tipos,
       fornecedor_estoque,
       seguradora,
       itens_fornecedor,
       itens_classe_contabil icc,
       parm_sys,
       parm_sys2,
       itens_custos,
       vw_os_tipos
 where (os_requisicoes.cod_item = itens.cod_item)
   and os_requisicoes.cod_fornecedor = fornecedor_estoque.cod_fornecedor
   and (os_requisicoes.cod_item = estoque.cod_item(+))
   and (os_requisicoes.cod_fornecedor = estoque.cod_fornecedor(+))
   and (os_requisicoes.cod_empresa = estoque.cod_empresa(+))
   and (os_requisicoes.numero_os = os.numero_os)
   and (os_requisicoes.cod_empresa = os.cod_empresa)
   and (os.tipo = os_tipos.tipo)
   and (os.cod_empresa = os_tipos.cod_empresa)
   and os.cod_seguradora = seguradora.cod_seguradora(+)
   and os_requisicoes.cod_item = itens_fornecedor.cod_item
   and os_requisicoes.cod_fornecedor = itens_fornecedor.cod_fornecedor
   and itens_fornecedor.cod_classe_contabil = icc.cod_classe_contabil(+)
   and os_requisicoes.cod_empresa = parm_sys.cod_empresa
   and os_requisicoes.cod_empresa = parm_sys2.cod_empresa
   and os_requisicoes.cod_item = itens_custos.cod_item(+)
   and os_requisicoes.cod_fornecedor = itens_custos.cod_fornecedor(+)
   and os_requisicoes.cod_empresa = itens_custos.cod_empresa(+)
   and os.tipo = vw_os_tipos.tipo
   and os.cod_empresa = vw_os_tipos.cod_empresa
   and (os_requisicoes.numero_os = $P{NUMERO_OS} )
   and (os_requisicoes.cod_empresa = $P{COD_EMPRESA} )
   and os.numero_os > 0

union all

select os_orcamentos_itens.cod_item as cod_item_chave,
       nvl(itens_custos.cod_fiscal_item, os_orcamentos_itens.cod_item) as cod_item,
       null as requisicao,
       fornecedor_estoque.cod_fornecedor,
       decode(fornecedor_estoque.oficial,
              'N',
              '*' || itens.descricao,
              itens.descricao) as descricao,
       itens.unidade,
       os_orcamentos_itens.quantidade,
       null as causadora,
       SUBSTR(TO_CHAR(os_orcamentos_itens.item + 100), 2, 2) AS item,
       itens.cod_max_desc,
       nvl(estoque.qtde, 0) as Estoque_Qtde, 
       nvl(estoque.reservado, 0) as reservado,   
       Nvl(os_orcamentos_itens.desconto_por_item, 0) As desconto_por_item,  
       os_orcamentos_itens.preco_venda,     
       decode(os.tem_desconto_item,
              'S',
              nvl(os_orcamentos_itens.preco_liquido,
                  (os_orcamentos_itens.preco_venda *
                  os_orcamentos_itens.quantidade)),
              (os_orcamentos_itens.preco_venda *
              os_orcamentos_itens.quantidade)) As Preco_Total,
       os_orcamentos_itens.seq_paf_item,  
       'A' status,            
       itens_fornecedor.ncm
       
  from os_orcamentos_itens,
       estoque,
       itens,
       fornecedor_estoque,
       itens_custos,
       itens_fornecedor,
       os
 where (os_orcamentos_itens.cod_item = itens.cod_item)
   and os_orcamentos_itens.cod_fornecedor =
       fornecedor_estoque.cod_fornecedor
   and (os_orcamentos_itens.cod_item = itens_fornecedor.cod_item)
   and (os_orcamentos_itens.cod_fornecedor =
       itens_fornecedor.cod_fornecedor)
   and (os_orcamentos_itens.cod_item = estoque.cod_item(+))
   and (os_orcamentos_itens.cod_fornecedor = estoque.cod_fornecedor(+))
   and (os_orcamentos_itens.cod_empresa = estoque.cod_empresa(+))
   and (os_orcamentos_itens.cod_item = itens_custos.cod_item(+))
   and (os_orcamentos_itens.cod_fornecedor = itens_custos.cod_fornecedor(+))
   and (os_orcamentos_itens.cod_empresa = itens_custos.cod_empresa(+))
   and (os_orcamentos_itens.cod_empresa = os.cod_empresa)
   and (os_orcamentos_itens.numero_os = os.numero_os)
   and (os_orcamentos_itens.numero_os = $P{NUMERO_OS})
   and (os_orcamentos_itens.cod_empresa = $P{COD_EMPRESA})
   and os.numero_os < 0
 order by 9, 2]]>
	</queryString>
	<field name="COD_ITEM_CHAVE" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="REQUISICAO" class="java.math.BigDecimal"/>
	<field name="COD_FORNECEDOR" class="java.math.BigDecimal"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="UNIDADE" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.math.BigDecimal"/>
	<field name="CAUSADORA" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.String"/>
	<field name="COD_MAX_DESC" class="java.lang.String"/>
	<field name="ESTOQUE_QTDE" class="java.math.BigDecimal"/>
	<field name="RESERVADO" class="java.math.BigDecimal"/>
	<field name="DESCONTO_POR_ITEM" class="java.math.BigDecimal"/>
	<field name="PRECO_VENDA" class="java.math.BigDecimal"/>
	<field name="PRECO_TOTAL" class="java.math.BigDecimal"/>
	<field name="SEQ_PAF_ITEM" class="java.math.BigDecimal"/>
	<field name="STATUS" class="java.lang.String"/>
	<field name="NCM" class="java.lang.String"/>
	<title>
		<band height="54" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<line>
				<reportElement positionType="Float" x="1" y="29" width="554" height="1" uuid="616e14c7-3943-45dd-baa6-feac17f13b43"/>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="1" y="30" width="1" height="24" uuid="7dad2364-e4a1-48b8-8c5f-56f1d719151f">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="554" y="30" width="1" height="24" uuid="54316f2d-67d1-407a-a240-07f3756b5cef">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="4" y="34" width="15" height="19" uuid="660f2197-b49f-4f04-b3c2-ddd4ecaa454d"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[It]]></text>
			</staticText>
			<line>
				<reportElement x="22" y="30" width="1" height="24" uuid="71afe05b-fca9-4ec2-9d30-d8a0b65bcde7">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="25" y="34" width="85" height="19" uuid="40d43db5-92c5-4396-a2f1-723487b96194"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Código do Item]]></text>
			</staticText>
			<line>
				<reportElement x="120" y="30" width="1" height="24" uuid="7767cb5f-f6ed-4f2d-a45c-6144edb4fada">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="124" y="34" width="112" height="19" uuid="14179160-05bc-45c5-98d9-f8b1cff77577"/>
				<textElement>
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição do item]]></text>
			</staticText>
			<line>
				<reportElement x="292" y="30" width="1" height="24" uuid="488b064f-0735-4ce1-a9a5-b3990444b467">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="295" y="34" width="20" height="19" uuid="a75c0982-b9fc-4cd9-a93f-5644151f7ced"/>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[UN]]></text>
			</staticText>
			<line>
				<reportElement x="317" y="30" width="1" height="24" uuid="e80a33be-c4ad-4ae9-9724-fa371dc3be54">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="320" y="34" width="20" height="19" uuid="f8c9090c-cc10-4bbb-a08b-9dfb40cdd0a2"/>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[LD]]></text>
			</staticText>
			<line>
				<reportElement x="343" y="30" width="1" height="24" uuid="fe1d1c24-3de1-4861-9567-da9f35212e4f">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="346" y="34" width="57" height="19" uuid="84bf3f67-aacb-4d31-8bc9-2fea08203e56"/>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[Requisição]]></text>
			</staticText>
			<line>
				<reportElement x="402" y="30" width="1" height="24" uuid="fd829fc5-0d78-4fb7-b8aa-1ba71444e048">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="405" y="34" width="26" height="19" uuid="dc27b2f3-bd41-4d9e-8e71-f494e1a76565"/>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<line>
				<reportElement x="433" y="30" width="1" height="24" uuid="0400ab82-b4f8-4117-b676-ec038bee44bb">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="437" y="34" width="52" height="19" uuid="19000713-001f-4b06-9cea-d85f3b8180ac"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[Preço Unit.]]></text>
			</staticText>
			<line>
				<reportElement x="492" y="30" width="1" height="24" uuid="eed1488e-5594-4c0f-b0fa-0e1509d74c0f">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="496" y="34" width="54" height="19" uuid="42294cb0-56f6-4db6-b56b-e19d91defa13"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Final]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="7" width="100" height="15" uuid="145b64ee-df7a-4d61-a80c-534b6d67a807"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Peças]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="22" splitType="Stretch">
			<textField>
				<reportElement x="4" y="2" width="15" height="14" uuid="957ff814-d663-4521-a047-220d38a03aaa"/>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="0" width="1" height="22" uuid="40620c69-2375-4e56-95aa-9a33e17e38d8">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="22" y="0" width="1" height="22" uuid="e8510fa5-88c4-4e7f-b787-5630a856b2cb">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<textField>
				<reportElement x="25" y="2" width="68" height="14" uuid="d273876f-580e-4b4d-a65f-8095d3b686f6"/>
				<textFieldExpression><![CDATA[$F{COD_ITEM_CHAVE}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="120" y="0" width="1" height="22" uuid="20f86c8b-71c5-4632-80db-a497db6e6275">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="124" y="2" width="100" height="14" uuid="64b46fbc-1b2b-4c3c-a1ab-b26550d191fb"/>
				<textElement>
					<font fontName="Arial Narrow"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="292" y="0" width="1" height="22" uuid="e87f1f1b-cac1-46e2-b2f7-c2a15f4dd8de">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<textField>
				<reportElement x="295" y="2" width="20" height="14" uuid="65d32933-5a24-486e-96d1-613564cdc253">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial Narrow"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{UNIDADE}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="317" y="0" width="1" height="22" uuid="0b3172bb-d467-4379-be55-a504c9ec8e0a">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<textField>
				<reportElement x="320" y="2" width="20" height="14" uuid="82ed8312-e83e-4d15-b6cb-56bb5f24591f">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial Narrow"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_MAX_DESC}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="343" y="0" width="1" height="22" uuid="11e4f22f-0e55-434e-9ced-1804c314b0fc">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="346" y="2" width="47" height="14" uuid="a0106d15-d84d-4b24-b2aa-831c6e394155">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial Narrow"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{REQUISICAO}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="554" y="0" width="1" height="22" uuid="a22bd607-3712-49d6-b27a-7a89fa1b0747">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="402" y="0" width="1" height="22" uuid="e2a84ddc-3393-488c-9075-35d5dc860c41">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<textField>
				<reportElement x="405" y="2" width="23" height="14" uuid="db0264aa-f2b7-4d05-88f4-398ab1e8af08">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial Narrow"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="433" y="0" width="1" height="22" uuid="0689d5e7-4d7a-4566-ab8f-adf4cd7afde4">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<textField pattern="#,##0.00">
				<reportElement x="437" y="2" width="52" height="14" uuid="94d12ec3-bbf4-4909-883a-c23a4a4a3f5d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial Narrow"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="492" y="0" width="1" height="22" uuid="6d6a0648-433f-4c5a-b93c-90d304d3d527">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<textField pattern="#,##0.00">
				<reportElement x="496" y="2" width="55" height="14" uuid="ef20853e-cdd4-4006-abc7-bf81b260533e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{PRECO_TOTAL}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="11" splitType="Stretch">
			<line>
				<reportElement positionType="Float" x="1" y="0" width="554" height="1" uuid="01002580-985f-4783-8e87-be86e77a0e45">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
	</summary>
</jasperReport>
