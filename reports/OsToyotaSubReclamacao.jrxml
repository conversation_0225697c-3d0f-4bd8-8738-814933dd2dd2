<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsToyotaSubServicos" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT OS_ORIGINAL.ITEM , 
 OS_ORIGINAL.DESCRICAO
FROM  OS_ORIGINAL
WHERE ( ( OS_ORIGINAL.NUMERO_OS = $P{NUMERO_OS} ) AND
  ( OS_ORIGINAL.COD_EMPRESA = $P{COD_EMPRESA} ) )
ORDER BY
 OS_ORIGINAL.ITEM]]>
	</queryString>
	<field name="ITEM" class="java.lang.Integer"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="16">
			<frame>
				<reportElement x="0" y="0" width="555" height="16" uuid="d24c8cb4-128a-4706-b754-3021a65c063e">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="326" y="0" width="1" height="16" uuid="344e2880-b505-4279-a82c-063f919155d3"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="3" width="318" height="10" uuid="a4a0f8c8-b585-4e0a-9c52-9eab169e3769"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Solicitações / Reclamações do Cliente]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="14">
			<frame>
				<reportElement x="0" y="0" width="555" height="14" uuid="5f5c9e09-e70d-46a0-aebf-2d84d3c6552b">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="1" y="1" width="16" height="11" uuid="4f8a7c4e-df5b-4796-9961-4f261280fc32"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="19" y="1" width="305" height="11" uuid="f108e909-240f-4a05-8931-6fc9a7795479"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="326" y="0" width="1" height="14" uuid="a64b985f-6b02-4786-96fa-07d9c16fb0a5"/>
				</line>
			</frame>
		</band>
	</detail>
</jasperReport>
