<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiSubFechamento" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="0efcef53-ba2f-4102-8b70-a807a9128687">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="344"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="646"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[select
  NVL(OS.VALOR_SERVICOS_BRUTO, 0)           									    AS OS_VALOR_SERVICOS_BRUTO,
  NVL(OS.VALOR_ITENS_BRUTO, 0)              									  	AS OS_VALOR_ITENS_BRUTO,
  NVL(OS.DESCONTOS_SERVICOS, 0)             									  	AS OS_DESCONTOS_SERVICOS,
  NVL(OS.DESCONTOS_ITENS, 0)                  										AS OS_DESCONTOS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS,0)          AS OS_TOTAL_OS_SERVICOS,
  NVL(OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS,0)                AS OS_TOTAL_OS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO,0)           AS OS_TOTAL_OS_BRUTO,
  NVL(OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS,0)               AS OS_TOTAL_OS_DESCONTO,
  (CASE
           WHEN NVL(OS_TIPOS_EMPRESAS.IMPRIMIR_IMP_RT_ORC_OS, 'N') = 'S' THEN
            (DESCONTOS_FISCAIS.VALOR_PIS_COFINS_CSLL +
            DESCONTOS_FISCAIS.VALOR_INSS_RETIDO +
            DESCONTOS_FISCAIS.VALOR_IRRF + DESCONTOS_FISCAIS.VALOR_ISS)
           ELSE
            0
           END) AS DESCONTO_FISCAIS_SERVICOS,
   (DESCONTOS_FISCAIS.VALOR_IMPOSTO_IPI +
   DESCONTOS_FISCAIS.VALOR_SUBSTITUICAO_TRIBUTARIA +
   DESCONTOS_FISCAIS.VALOR_FCP_ST) AS DESCONTO_FISCAIS_PECAS
  
  
  from OS,
       OS_TIPOS_EMPRESAS,
       TABLE(PKG_CRM_SERVICE_UTIL.GET_TABLE_DADOS_FISCAIS_OS($P{NUMERO_OS},
                                                             $P{COD_EMPRESA})) DESCONTOS_FISCAIS
  WHERE 1 = 1
     AND OS_TIPOS_EMPRESAS.TIPO(+) = OS.TIPO
     AND OS_TIPOS_EMPRESAS.COD_EMPRESA = OS.COD_EMPRESA
     AND DESCONTOS_FISCAIS.NUMERO_OS = OS.NUMERO_OS
     AND DESCONTOS_FISCAIS.COD_EMPRESA = OS.COD_EMPRESA
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}]]>
	</queryString>
	<field name="OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_BRUTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_DESCONTO" class="java.lang.Double"/>
	<field name="DESCONTO_FISCAIS_SERVICOS" class="java.lang.Double"/>
	<field name="DESCONTO_FISCAIS_PECAS" class="java.lang.Double"/>
	<detail>
		<band height="50">
			<frame>
				<reportElement x="0" y="0" width="555" height="50" uuid="7e517597-f9e7-4a4c-95b6-bb7d2723d452"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="1" y="3" width="61" height="10" uuid="7a5f4ce0-819a-4f6f-b833-1f0245db8613"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Fechamento]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="417" y="3" width="61" height="11" uuid="217cd8ca-5099-4104-9cd9-16662487ebcb"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Serviços + Itens:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="417" y="25" width="61" height="11" uuid="bd26c719-1000-44d9-9547-3d04ea14103a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Desconto:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="417" y="36" width="61" height="11" uuid="e4d5bb6c-cefc-425e-a9f9-acf3483d8c01"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<line>
					<reportElement x="417" y="35" width="112" height="1" uuid="1a53e348-5b75-4b12-9e78-e89532bdd02b"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="255" y="3" width="51" height="11" uuid="026cc03c-f04c-4173-ba62-c7c5f02ab423"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Itens:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="255" y="14" width="51" height="11" uuid="fb426fcd-263d-4141-a8ba-d2c3e27546a3"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Desconto:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="255" y="25" width="51" height="11" uuid="60b799f6-7500-4aab-ba1c-e8828bf14e76"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<line>
					<reportElement x="255" y="24" width="102" height="1" uuid="77b30282-21b0-4859-aa36-f61ce9cab7c7"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="80" y="3" width="51" height="11" uuid="c73df871-89c1-43f3-8957-a794d32b31ce"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Serviços:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="80" y="14" width="51" height="11" uuid="5973cf49-4135-49f8-a17a-cc99ef8ee175"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Desconto:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="80" y="25" width="51" height="11" uuid="5cba283a-5f51-4a04-98bd-333e4fc94b45"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<line>
					<reportElement x="79" y="24" width="103" height="1" uuid="43b93f8e-8e6b-4fe1-8d27-6ba7f09bbd29"/>
				</line>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="131" y="25" width="51" height="11" uuid="05d32a21-0a41-4a0c-8f4d-b1f932c2e23e"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VALOR_SERVICOS_BRUTO} - $F{OS_DESCONTOS_SERVICOS}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="306" y="25" width="51" height="11" uuid="a00011ed-d1e4-4ed1-8f45-f1cb73e9bf58"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VALOR_ITENS_BRUTO} -$F{OS_DESCONTOS_ITENS}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="478" y="36" width="51" height="11" uuid="ff5064b1-b2c0-438f-be4d-5dbacacd8cdc"/>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VALOR_SERVICOS_BRUTO} +$F{OS_VALOR_ITENS_BRUTO} - ($F{OS_DESCONTOS_SERVICOS} + $F{DESCONTO_FISCAIS_SERVICOS} + $F{OS_DESCONTOS_ITENS} + $F{DESCONTO_FISCAIS_PECAS})]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="131" y="3" width="51" height="11" uuid="0bfc0e7d-e4fa-469f-86dd-249db6fdec60"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VALOR_SERVICOS_BRUTO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="131" y="14" width="51" height="11" uuid="d16ccaf4-6c5f-4059-a620-ca53ad5fc047"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DESCONTOS_SERVICOS}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="306" y="3" width="51" height="11" uuid="374e37fe-31d4-4b32-95bc-c5812c9f7522"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VALOR_ITENS_BRUTO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="306" y="14" width="51" height="11" uuid="c2f5a367-625d-4910-8282-13c4c6fac07d"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DESCONTOS_ITENS}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="478" y="3" width="51" height="11" uuid="1426788a-e4b3-48c6-9b92-cd598b8dcef3"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VALOR_SERVICOS_BRUTO}+$F{OS_VALOR_ITENS_BRUTO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="478" y="25" width="51" height="11" uuid="a08302d2-eb69-4025-987a-44ed2bd292e2"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DESCONTOS_SERVICOS} + $F{OS_DESCONTOS_ITENS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="417" y="14" width="61" height="11" uuid="0d89d791-d226-485e-be10-ebac0ed918fd"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[(+)Impostos:]]></text>
				</staticText>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="478" y="14" width="51" height="11" uuid="6412e8c7-254b-449d-a7fc-8bf47f73da02"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCONTO_FISCAIS_SERVICOS} + $F{DESCONTO_FISCAIS_PECAS}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<lastPageFooter>
		<band height="3">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</lastPageFooter>
	<summary>
		<band>
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</summary>
</jasperReport>
