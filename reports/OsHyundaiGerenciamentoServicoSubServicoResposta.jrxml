<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsJaguarLandSubReclamacao" pageWidth="555" pageHeight="130" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[with origem_ficha_cruzamento as (Select OsO.Cod_Empresa, 
      osO.Numero_Os, 
      osO.Descricao as descricao_reclamacao,        
      osR.COD_RECLAMACAO AS ITEM, 
      osR.RESPOSTA RESPOSTA_SERVICO,
      ROW_NUMBER() OVER (PARTITION BY osO.ITEM ORDER BY osO.ITEM) AS linha,
      'CRUZAMENTO_FICHA'origem
From Os_Original osO,
     os_respostas_reclamacao osR,
     cruzamento_ficha
Where osO.Cod_Empresa = $P{COD_EMPRESA}
     and osO.Numero_Os = $P{NUMERO_OS}
     and osO.Reclamacao_cliente = 'S'
     AND osr.COD_EMPRESA (+) = osO.cod_empresa
     AND osr.NUMERO_OS (+) = osO.Numero_Os
     and osr.cod_reclamacao (+) = osO.Item
     and cruzamento_ficha.cod_pergunta = osr.cod_pergunta
     and cruzamento_ficha.tipo = 'SRV'),
origem_primeira_linha as (
select * from
(Select OsO.Cod_Empresa, 
      osO.Numero_Os, 
      osO.Descricao as descricao_reclamacao,        
      osR.COD_RECLAMACAO AS ITEM, 
      osR.RESPOSTA RESPOSTA_SERVICO,
      ROW_NUMBER() OVER (PARTITION BY osO.ITEM ORDER BY osO.ITEM) AS linha,
      'PRIMEIRA_LINHA'origem
From Os_Original osO,
     os_respostas_reclamacao osR
Where osO.Cod_Empresa = $P{COD_EMPRESA}
     and osO.Numero_Os = $P{NUMERO_OS}
     and osO.Reclamacao_cliente = 'S'
     AND osr.COD_EMPRESA (+) = osO.cod_empresa
     AND osr.NUMERO_OS (+) = osO.Numero_Os
     and osr.cod_reclamacao (+) = osO.Item)
     where linha = 1),
     
consulta as (
  select * from origem_ficha_cruzamento
  union all
  select * from origem_primeira_linha
  where not exists(select 1 from origem_ficha_cruzamento)
),

PARAMETROS AS (
           SELECT
              5 MULTIPLO, /*Determina o multiplo da quantidade de linhas*/
              5 MAXIMO_LINHAS /* determina o maximo de linhas, zero é ilimitado, o numero de linhas que a consulta retornar*/
              FROM DUAL
),
CONSULTA_LIMITADA as (
     SELECT consulta.* FROM consulta,parametros where (rownum <= parametros.maximo_linhas or parametros.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL as (
    SELECT * FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL, NULL, NULL, NULL, NULL, NULL, NULL
    FROM dual,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),parametros.multiplo) <> 0 or (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY level <= parametros.multiplo - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), parametros.multiplo)
)
SELECT * FROM CONSULTA_FINAL]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="DESCRICAO_RECLAMACAO" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="RESPOSTA_SERVICO" class="java.lang.String"/>
	<field name="LINHA" class="java.lang.Double"/>
	<field name="ORIGEM" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="35">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="18" backcolor="#08038F" uuid="636524f7-bf1b-4ddb-b360-38242daab75b"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement mode="Transparent" x="4" y="3" width="496" height="14" forecolor="#FFFFFF" uuid="f3495163-0786-4993-887a-36dcdb2c402c"/>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Serviço Realizado (O que foi ou será necessário para resolver o problema?):]]></text>
			</staticText>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="17" width="555" height="18" backcolor="#08038F" uuid="a6fb21f3-6bf3-4416-9529-22412b86f291"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement mode="Transparent" x="4" y="20" width="331" height="14" forecolor="#FFFFFF" uuid="cd40b87c-3c02-4938-b34d-b0224f516d73"/>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição do Serviço:]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="19" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="19" uuid="4322e92e-9965-400f-8218-5c9254755c62">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<leftPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="3" y="1" width="15" height="16" forecolor="#08038F" uuid="4d52c477-336c-4606-b73e-85b45d0157fb">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="20" y="1" width="436" height="16" forecolor="#08038F" uuid="f8e2d60c-384d-4ebb-8bdc-eac01d62be87">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESPOSTA_SERVICO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
