<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="emBranco" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="VALOR_NULO" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232725.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\33790\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String"/>
	<queryString>
		<![CDATA[WITH q_os AS (
	SELECT OS.COD_EMPRESA,OS.STATUS_OS, OS.COD_DOCUMENTO, OS.COD_OS_AGENDA,
 OS.NUMERO_OS, abs(OS.NUMERO_OS) as abs_osNum,
 OS.LAVAR_VEICULO,
 OS.NUMERO_OS_FABRICA,
 OS.COD_CLIENTE,  OS.COD_PRODUTO, OS.COD_MODELO,
 OS.TIPO_ENDERECO, os.franquia, OS.TIPO,OS_TIPOS.TIPO_FABRICA,
 OS.VALIDADE,
 (SELECT OA.OBSERVACOES||' - ' FROM OS_AGENDA OA, PARM_SYS3 P
       WHERE P.COD_EMPRESA = OS.COD_EMPRESA
       AND P.IMPRIMIR_OBS_CONCATENADA = 'S'
       AND OA.COD_EMPRESA = OS.COD_EMPRESA AND OA.COD_OS_AGENDA = OS.COD_OS_AGENDA) || OS.OBSERVACAO OBSERVACAO,
 OS.ORCAMENTO,
 OS.EXTENDIDA,
 OS.SEGURADORA, os.liberado,
 OS.DATA_EMISSAO,
 TRATA_HORA_VERAO_FUSO_CHAR(os.cod_empresa,Os.Hora_Emissao, 'HH24:MI') as HORA_EMISSAO,
 trunc(OS.DATA_entrega) as DATA_entrega,
 to_char(DATA_entrega,'HH24:MI') as hora_entrega,OS.ENTREGA_CLIENTE,
 OS.HORA_ENCERRADA,
 OS.DATA_ENCERRADA,
 OS.HORA_libeRADo,
 OS.DATA_libeRADo,
 OS.HORA_PROMETIDA,
 OS.DATA_PROMETIDA,
 OS.PRIMEIRA_LIBERACAO,
 OS.VALOR_SERVICOS_BRUTO,
 OS.VALOR_ITENS_BRUTO,
 OS.DESCONTOS_SERVICOS,
 OS.DESCONTOS_ITENS,
 nvl(os.inscricao_estadual, cliente_diverso.inscricao_estadual) as inscricao_estadual,
 (OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS)   Total_OS_Servicos,
 (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS)         Total_OS_Itens,
 (OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO)    Total_OS_Bruto,
 (OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS)        Total_OS_Desconto,
 ((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS)
  + (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS))     Total_OS,
 OS.ORC_SERV_BRUTO,
 OS.ORC_ITEM_BRUTO,
 OS.ORC_SERV_DESCONTO,
 OS.ORC_ITEM_DESCONTO,
 (OS.ORC_SERV_BRUTO - OS.ORC_SERV_DESCONTO)          Total_Orc_Servicos,
 (OS.ORC_ITEM_BRUTO - OS.ORC_ITEM_DESCONTO)          Total_Orc_Itens,
 (OS.ORC_SERV_BRUTO + OS.ORC_ITEM_BRUTO)             Total_Orc_Bruto,
 (OS.ORC_SERV_DESCONTO + OS.ORC_ITEM_DESCONTO)       Total_Orc_Desconto,
 ((OS.ORC_SERV_BRUTO - OS.ORC_SERV_DESCONTO)
  + (OS.ORC_ITEM_BRUTO - OS.ORC_ITEM_DESCONTO))      Total_Orc,
 OS.KM_INICIAL,
 OS.KM_FINAL,
 OS.DESLOCAMENTO,
 OS.COD_SEGURADORA,
 OS_DADOS_VEICULOS.ANO,
 OS_DADOS_VEICULOS.HORIMETRO,
 OS_DADOS_VEICULOS.PRISMA,
 OS_DADOS_VEICULOS.DATA_VENDA,
 OS_DADOS_VEICULOS.COMBUSTIVEL,
 OS_DADOS_VEICULOS.COR_EXTERNA,
 OS_DADOS_VEICULOS.PLACA,OS_DADOS_VEICULOS.NUMERO_RENAVAM,
 OS_DADOS_VEICULOS.KM,
 OS_DADOS_VEICULOS.CHASSI, 0 as descontos_icms,
 0 as descontos_iss,
 0 as descontos_garantia,
 OS_DADOS_VEICULOS.numero_motor,
 OS_DADOS_VEICULOS.numero_cambio,
 OS_DADOS_VEICULOS.SERIE,
 OS_DADOS_VEICULOS.BLINDADO,
 OS_DADOS_VEICULOS.numero_diferencial,
 motoristas.nome_do_motorista,
 motoristas.documento documento_do_motorista,
 OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO      Tipo_Descricao,
 os_tipos.tipo_fabrica|| ' - ' || OS_TIPOS.DESCRICAO      Tipo_Descricao_fab,
 nvl(os_tipos_empresas.cod_cliente, os_tipos.cod_cliente) as tipo_cod_cliente,
 OS_TIPOS_EMPRESAS.COD_IMAGEM_OS COD_IMG_TP,
 OS_TIPOS.GARANTIA,
 OS_TIPOS.interno,
 EMPRESAS_USUARIOS.NOME_COMPLETO,
 EMPRESAS_USUARIOS.Codigo_Opcional,
 PRODUTOS.DESCRICAO_PRODUTO || ' / ' || PRODUTOS_MODELOS.DESCRICAO_MODELO
   Desc_Prod_Mod,
 PRODUTOS_MODELOS.LINHA,
 PRODUTOS_MODELOS.COD_IMAGEM_OS COD_IMG_MOD,
 EMPRESAS.NOME Nome_Empresa,
 EMPRESAS.CGC                Empresa_Cgc,
 EMPRESAS.FACHADA            Empresa_Fachada,
 EMPRESAS.ESTADO             Empresa_UF,
 EMPRESAS.CIDADE             Empresa_Cidade,
 EMPRESAS.BAIRRO             Empresa_Bairro,
 EMPRESAS.COMPLEMENTO        Empresa_Cmpto,
 EMPRESAS.RUA                Empresa_Rua,
 EMPRESAS.FONE               Empresa_Fone,
 EMPRESAS.FAX                Empresa_Fax,
 EMPRESAS.CEP                Empresa_CEP,
 empresas.inscricao_estadual empresa_inscricao_estadual,
 empresas.Inscricao_Municipal  empresas_Inscricao_Municipal,
 UF_Empresa.DESCRICAO        Empresa_Estado,
 CONCESSIONARIAS.NOME        Concessionaria_Nome,
 CONCESSIONARIAS.UF          Concessionaria_UF,
 CONCESSIONARIAS.CIDADE      Concessionaria_Cidade,
 CONCESSIONARIAS.BAIRRO      Concessionaria_Bairro,
 CONCESSIONARIAS.ENDERECO    Concessionaria_Rua,
 CONCESSIONARIAS.CEP         Concessionaria_CEP,
 UF_Concessionaria.DESCRICAO Concessionaria_Estado,
 CLIENTE_DIVERSO.COD_TIPO_CLIENTE,
 nvl(os.cliente_rapido, cliente_diverso.nome) as Cliente_Nome,
 nvl(CLIENTE_DIVERSO.RG, '          ') as Cliente_RG,
 Decode(CLIENTE_DIVERSO.CPF, null, Nvl(CLIENTE_DIVERSO.CGC, ''),
                                   CLIENTE_DIVERSO.CPF) as Cliente_CGC_CPF,

 Decode(OS.TIPO_ENDERECO, '1', CLIENTE_DIVERSO.UF,
                          '2', CLIENTES.UF_RES,
                          '3', CLIENTES.UF_COM,
                          '4', CLIENTES.UF_COBRANCA,
                          '5', ENDERECO_POR_INSCRICAO.UF,
                             NULL)                      Cliente_UF,
 Decode(OS.TIPO_ENDERECO, '1', UF_Diverso.DESCRICAO,
                          '2', UF_Res.DESCRICAO,
                          '3', UF_Com.DESCRICAO,
                          '4', UF_Cobranca.DESCRICAO,
                          '5', UF_Inscricao.DESCRICAO,
                             NULL)                      Cliente_Estado,
 Decode(OS.TIPO_ENDERECO, '1', Cidades_Div.DESCRICAO,
                          '2', Cidades_Res.DESCRICAO,
                          '3', Cidades_Com.DESCRICAO,
                          '4', Cidades_Cobranca.DESCRICAO,
                          '5', ENDERECO_POR_INSCRICAO.CIDADE,
                             NULL)                      Cliente_Cidade,
 Decode(OS.TIPO_ENDERECO, '1', CLIENTE_DIVERSO.BAIRRO,
                          '2', CLIENTES.BAIRRO_RES,
                          '3', CLIENTES.BAIRRO_COM,
                          '4', CLIENTES.BAIRRO_COBRANCA,
                          '5', ENDERECO_POR_INSCRICAO.BAIRRO,
                             NULL)                      Cliente_Bairro,
 Decode(OS.TIPO_ENDERECO, '1', CLIENTE_DIVERSO.CEP,
                          '2', CLIENTES.CEP_RES,
                          '3', CLIENTES.CEP_COM,
                          '4', CLIENTES.CEP_COBRANCA,
                          '5', ENDERECO_POR_INSCRICAO.CEP,
                             NULL)                      Cliente_CEP,
 Decode(OS.TIPO_ENDERECO, '1', CLIENTE_DIVERSO.ENDERECO,
                          '2', CLIENTES.RUA_RES,
                          '3', CLIENTES.RUA_COM,
                          '4', CLIENTES.RUA_COBRANCA,
                          '5', ENDERECO_POR_INSCRICAO.RUA,
                             NULL)                      Cliente_Rua,
 Decode(OS.TIPO_ENDERECO, '1', CLIENTE_DIVERSO.COMPLEMENTO,
                          '2', CLIENTES.COMPLEMENTO_RES,
                          '3', CLIENTES.COMPLEMENTO_COM,
                          '4', CLIENTES.COMPLEMENTO_COBRANCA,
                          '5', ENDERECO_POR_INSCRICAO.COMPLEMENTO,
                             NULL)                      Cliente_Complemento,
 Decode(OS.TIPO_ENDERECO, '1', NULL,
                          '2', CLIENTES.FACHADA_RES,
                          '3', CLIENTES.FACHADA_COM,
                          '4', CLIENTES.FACHADA_COBRANCA,
                          '5', ENDERECO_POR_INSCRICAO.FACHADA,
                             NULL)                      Cliente_Fachada,
 decode(sign(length(os.cliente_rapido)), 1, os.cliente_rapido_fone,
   Decode(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.FONE_CONTATO,
                            2, CLIENTES.TELEFONE_RES,
                            3, CLIENTES.TELEFONE_COM,
                            4, CLIENTES.TELEFONE_COM,
                            5, ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
                               NULL))                      as Cliente_Fone,
 decode(sign(length(os.cliente_rapido)), 1, null,
   Decode(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
                            2, CLIENTES.PREFIXO_RES,
                            3, CLIENTES.PREFIXO_COM,
                            4, CLIENTES.PREFIXO_COM,
                            5, ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
                               NULL))                    as cliente_prefixo,
  clientes.telefone_cel, clientes.prefixo_cel,  CLIENTES.TELEFONE_COM as telcom,CLIENTES.PREFIXO_COM, CLIENTES.Radio,
  sysdate,
  os.cod_socio, os.cod_banco, os_tipos_empresas.imprimir_dados_financiamento,
  os_tipos_empresas.imprimir_assinatura, os_tipos_empresas.imprimir_assinatura2, os_tipos_empresas.imprimir_rg_cpf,
  os.local_execucao,clientes.endereco_eletronico as cliente_email,
  os.peca_usada_fica_cliente, os_tipos_empresas.cod_cliente_fabrica_garantia, nvl(os.tem_desconto_item, 'N') as tem_desconto_item,
  (select nvl(sum(a.preco_venda),0) from os_servicos a,servicos b
            where a.cod_servico = b.cod_servico
            and b.terceiros='S'
            and a.cod_empresa =$P{COD_EMPRESA}
            and a.numero_os =$P{NUMERO_OS}) as tot_serv,
   OS_DADOS_VEICULOS.numero_contrato,
   OS_DADOS_VEICULOS.data_contrato,
   OS_DADOS_VEICULOS.SERIE NR_SERIE_VEICULO, OS_DADOS_VEICULOS.DATA_FAB_BATERIA, OS_DADOS_VEICULOS.COD_FAB_BATERIA,
   CLIENTE_DIVERSO.IMP_NCM_OS as IMP_NCM_OS_CLI,
   OS_TIPOS_EMPRESAS.IMP_NCM_OS as IMP_NCM_OS_TIPO,
   OS_TIPOS_EMPRESAS.imprimir_assinatura_produtivo,
   nvl(Os_Tipos_Empresas.Imprimir_Imp_Rt_Orc_Os,'N') Imprimir_Imp_Rt_Orc_Os,
   OS.OS_ENTRADA, '1ª VIA CLIENTE 1ª VIA CLIENTE 1ª VIA CLIENTE' OS_VIA,
   CO.TEXTO_AIDF,   
   empresas.empresa_nome_completo
   ,produtos_modelos.MOD_VER_SERIE,OS_DADOS_VEICULOS.TEM_GARANTIA_FABRICA,OS_DADOS_VEICULOS.TEM_GARANTIA_ESTENDIDA,
   OS.TOTAL_IMPRESSAO_DETALHE,
   OS.TOTAL_IMPRESSAO_FABRICA,
   EMPRESAS.FUSO_HORARIO
   , Nvl(Os_Tipos.Interno         , 'N') As Interno
   , Nvl(Os_Tipos.GARANTIA        , 'N') As GARANTIA
   , (SELECT EU.NOME_COMPLETO 
        FROM EMPRESAS_USUARIOS EU 
       WHERE EU.COD_EMPRESA = OS.COD_EMPRESA
         AND EU.NOME = OS.VENDEDOR_BALCAO) AS VENDEDOR_BALCAO
   ,OTT.DESCRICAO AS LK_OTT_DESC
   ,NVL(OS.ARREDONDAMENTO,0) AS ARREDONDAMENTO
   ,OS.LIBERACAO_ORIGINAL AS LIBERACAO_ORIGINAL,
   (SELECT LISTAGG(CONTROLE, ', ') WITHIN GROUP (ORDER BY CONTROLE) LISTA_VENDAS
  FROM VENDAS
  WHERE COD_EMPRESA = $P{COD_EMPRESA}
  AND  NUMERO_OS = $P{NUMERO_OS}) AS LISTA_VENDAS
FROM OS, EMPRESAS, EMPRESAS_USUARIOS, CLIENTE_DIVERSO, CLIENTES,
 CIDADES Cidades_Res, CIDADES Cidades_Com, CIDADES Cidades_Cobranca, cidades Cidades_Div,
 ENDERECO_POR_INSCRICAO, OS_TIPOS, OS_TIPOS_EMPRESAS, OS_DADOS_VEICULOS,
 CONCESSIONARIAS, PRODUTOS, PRODUTOS_MODELOS,
 UF UF_Empresa, UF UF_Diverso, UF UF_Res, UF UF_Com, UF UF_Cobranca,
 UF UF_Inscricao, UF UF_Concessionaria, motoristas, CONTROLE_OS CO, OS_TIPO_TOL OTT
WHERE (OS.COD_EMPRESA = EMPRESAS.COD_EMPRESA)
 AND (OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE)
 AND (CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE (+))
 and (OS.TIPO = OS_TIPOS.TIPO)
 AND (CLIENTE_DIVERSO.COD_CIDADES = Cidades_Div.COD_CIDADES (+))
 AND (CLIENTES.COD_CID_RES = Cidades_Res.COD_CIDADES (+))
 AND (CLIENTES.COD_CID_COM = Cidades_Com.COD_CIDADES (+))
 AND (CLIENTES.COD_CID_COBRANCA = Cidades_Cobranca.COD_CIDADES (+))
 AND (OS.NOME = EMPRESAS_USUARIOS.NOME)
 AND (OS.INSCRICAO_ESTADUAL = ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL (+))
 AND (OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE (+))
 AND (OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS (+))
 AND (OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA (+))
 AND (OS_DADOS_VEICULOS.COD_CONCESSIONARIA = CONCESSIONARIAS.COD_CONCESSIONARIA (+))
 AND (OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO)
 AND (OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO)
 AND (OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO)
 and (os.codigo_motorista = motoristas.codigo_motorista (+))
 AND (EMPRESAS.ESTADO = UF_Empresa.UF (+))
 AND (CLIENTE_DIVERSO.UF = UF_Diverso.UF (+))
 AND (CLIENTES.UF_RES = UF_Res.UF (+))
 AND (CLIENTES.UF_COM = UF_Com.UF (+))
 AND (CLIENTES.UF_COBRANCA = UF_Cobranca.UF (+))
 AND (ENDERECO_POR_INSCRICAO.UF = UF_Inscricao.UF (+))
 AND (CONCESSIONARIAS.UF = UF_Concessionaria.UF (+))
 AND (OS.COD_EMPRESA = $P{COD_EMPRESA})
 AND (OS.NUMERO_OS = $P{NUMERO_OS})
 AND (OS.COD_EMPRESA = OS_TIPOS_EMPRESAS.COD_EMPRESA(+))
 AND (OS.TIPO = OS_TIPOS_EMPRESAS.TIPO(+))
 AND (OS.COD_EMPRESA = CO.COD_EMPRESA(+))
 AND (OS.COD_TIPO_TOL = OTT.ID (+))
),
Q_HISTORICO AS
 (SELECT LISTAGG(HISTORICO_OS.N_OS, ', ') WITHIN GROUP(ORDER BY HISTORICO_OS.N_OS) AS HISTORICO_OS,
         HISTORICO_OS.CHASSI,
         HISTORICO_OS.COD_EMPRESA
    FROM (SELECT DISTINCT TO_CHAR(OSD.NUMERO_OS) AS N_OS,
                          OSD.CHASSI,
                          OSD.COD_EMPRESA
            FROM OS_DADOS_VEICULOS OSD, OS, Q_OS
           WHERE OSD.COD_EMPRESA = Q_OS.COD_EMPRESA
             AND OS.NUMERO_OS = OSD.NUMERO_OS
             AND OS.STATUS_OS = 1
             AND OS.NUMERO_OS > 0
             AND OSD.CHASSI = Q_OS.CHASSI
                
             AND ROWNUM < 11
           ORDER BY OSD.NUMERO_OS DESC) HISTORICO_OS
   GROUP BY HISTORICO_OS.CHASSI, HISTORICO_OS.COD_EMPRESA),
Q_LOGO AS 
(
select OTE.logo as logo_por_tipo, E.logo as logo_empresa
FROM  EMPRESA_LOGO E, OS_TIPOS_EMPRESAS OTE, Q_OS
 WHERE OTE.COD_EMPRESA = E.COD_EMPRESA (+)
 AND Q_OS.TIPO = OTE.TIPO (+) 
 AND OTE.COD_EMPRESA = Q_OS.COD_EMPRESA
),

Q_OS_TERMO AS
(
select TERMO.foo, TERMO.texto
from(
	select 1 as foo, texto
	from os_tipos_termo, Q_OS
	where os_tipos_termo.cod_empresa = Q_OS.COD_EMPRESA
	and os_tipos_termo.tipo = Q_OS.TIPO  AND cod_tipo = 1
	union
	select 2 as foo, texto
	from os_termo
	where rownum = 1
	order by foo) TERMO
where rownum = 1
	
),

Q_ASSINATURA as
 (select OS_AGENDA.NUMERO_OS,
         OS_AGENDA.COD_EMPRESA,
         OS_AGENDA.SIGNATURE As CLIENTE_RECEPCAO,
         MOB_OS_ASSINATURA_ENTREGA.ASSINATURA_CLIENTE AS CLIENTE_ENTREGA,
         MOB_OS_ASSINATURA_ENTREGA.DATA_ASSINATURA_CLIENTE AS CLIENTE_ENTREGA_DT,
         MOB_OS_ASSINATURA_RECEPCAO.ASSINATURA AS CONSULTOR_RECEPCAO,
         MOB_OS_ASSINATURA_RECEPCAO.DATA_ASSINATURA AS CONSULTOR_RECEPCAO_DT,
         MOB_OS_ASSINATURA_ENTREGA.ASSINATURA AS CONSULTOR_ENTREGA,
         MOB_OS_ASSINATURA_ENTREGA.DATA_ASSINATURA AS CONSULTOR_ENTREGA_DT
    from OS_AGENDA, MOB_OS_ASSINATURA MOB_OS_ASSINATURA_ENTREGA, MOB_OS_ASSINATURA MOB_OS_ASSINATURA_RECEPCAO
   where OS_AGENDA.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_EMPRESA = $P{COD_EMPRESA}
   AND OS_AGENDA.NUMERO_OS = MOB_OS_ASSINATURA_ENTREGA.numero_os (+)
   AND OS_AGENDA.COD_EMPRESA = MOB_OS_ASSINATURA_ENTREGA.cod_empresa (+)
   AND MOB_OS_ASSINATURA_ENTREGA.APLICACAO(+) = 'E' 
   AND OS_AGENDA.NUMERO_OS = MOB_OS_ASSINATURA_RECEPCAO.numero_os (+)
   AND OS_AGENDA.COD_EMPRESA = MOB_OS_ASSINATURA_RECEPCAO.cod_empresa (+)
   AND MOB_OS_ASSINATURA_RECEPCAO.APLICACAO(+) = 'R')

SELECT q_os.OS_VIA AS q_os_OS_VIA,
q_os.NOME_EMPRESA AS q_os_NOME_EMPRESA,
q_os.EMPRESA_CGC AS q_os_EMPRESA_CGC,
q_os.EMPRESA_CEP AS q_os_EMPRESA_CEP,
TRIM(both '-' from TRIM(q_os.EMPRESA_CIDADE ||' - ' || q_os.EMPRESA_ESTADO ||' - '|| q_os.EMPRESA_UF)) AS q_os_Empresa_Cid_Est_UF,
q_os.EMPRESA_BAIRRO AS q_os_EMPRESA_BAIRRO,
TRIM(both '-' from TRIM(q_os.Empresa_Rua ||', '|| q_os.Empresa_Fachada ||' '|| q_os.Empresa_Cmpto)) AS q_os_Empresa_Endereco,
q_os.EMPRESA_FONE AS q_os_EMPRESA_FONE,
q_os.EMPRESA_FAX AS q_os_EMPRESA_FAX,
q_os.PLACA AS q_os_PLACA,
q_os.NOME_COMPLETO AS q_os_NOME_COMPLETO,
q_os.DATA_PROMETIDA AS q_os_DATA_PROMETIDA,
q_os.HORA_EMISSAO AS q_os_HORA_EMISSAO,
q_os.ABS_OSNUM AS q_os_ABS_OSNUM,
q_os.PRISMA AS q_os_PRISMA,
q_os.DATA_ENCERRADA AS q_os_DATA_ENCERRADA,
q_os.TIPO_DESCRICAO AS q_os_TIPO_DESCRICAO,
decode(q_os.COD_TIPO_CLIENTE,1,'S') AS q_os_Cliente_Cadastro,
q_os.CLIENTE_NOME AS q_os_CLIENTE_NOME,
q_os.CLIENTE_BAIRRO AS q_os_CLIENTE_BAIRRO,
TRIM(both '-' from TRIM(q_os.CLIENTE_CIDADE ||' - ' || q_os.CLIENTE_ESTADO ||' - '|| q_os.CLIENTE_UF)) AS q_os_Cliente_Cid_Est_UF,
q_os.FRANQUIA AS q_os_FRANQUIA,
q_os.DATA_ENTREGA AS q_os_DATA_ENTREGA,
q_os.DATA_LIBERADO AS q_os_DATA_LIBERADO,
q_os.NUMERO_CONTRATO AS q_os_NUMERO_CONTRATO,
q_os.DATA_CONTRATO AS q_os_DATA_CONTRATO,
q_os.COD_OS_AGENDA AS q_os_COD_OS_AGENDA,
q_os.TEM_GARANTIA_FABRICA AS q_os_TEM_GARANTIA_FABRICA,
q_os.TEM_GARANTIA_ESTENDIDA AS q_os_TEM_GARANTIA_ESTENDIDA,
q_os.TIPO_FABRICA AS q_os_TIPO_FABRICA,
q_os.VENDEDOR_BALCAO AS q_os_VENDEDOR_BALCAO,
q_os.DESC_PROD_MOD AS q_os_DESC_PROD_MOD,
q_os.ANO AS q_os_ANO,
q_os.KM AS q_os_KM,
q_os.HORIMETRO AS q_os_HORIMETRO,
q_os.CHASSI AS q_os_CHASSI,
q_os.COR_EXTERNA AS q_os_COR_EXTERNA,
q_os.LINHA AS q_os_LINHA,
q_os.CONCESSIONARIA_NOME AS q_os_CONCESSIONARIA_NOME,
q_os.DATA_VENDA AS q_os_DATA_VENDA,
q_os.CONCESSIONARIA_BAIRRO AS q_os_CONCESSIONARIA_BAIRRO,
q_os.CONCESSIONARIA_CEP AS q_os_CONCESSIONARIA_CEP,
q_os.CONCESSIONARIA_RUA AS q_os_CONCESSIONARIA_RUA,
TRIM(both '-' from TRIM(q_os.CONCESSIONARIA_CIDADE ||' - ' || q_os.CONCESSIONARIA_ESTADO ||' - '|| q_os.CONCESSIONARIA_UF)) AS q_os_Concessionaria_Cid_Est_UF,
q_os.NUMERO_MOTOR AS q_os_NUMERO_MOTOR,
q_os.NOME_DO_MOTORISTA AS q_os_NOME_DO_MOTORISTA,
q_os.DOCUMENTO_DO_MOTORISTA AS q_os_DOCUMENTO_DO_MOTORISTA,
q_os.SERIE AS q_os_SERIE,
q_os.BLINDADO AS q_os_VEICULO_BLINDADO,
q_os.NUMERO_DIFERENCIAL AS q_os_NUMERO_DIFERENCIAL,
q_os.NUMERO_CAMBIO AS q_os_NUMERO_CAMBIO,
q_os.NR_SERIE_VEICULO AS q_os_NR_SERIE_VEICULO,
q_os.DATA_FAB_BATERIA AS q_os_DATA_FAB_BATERIA,
q_os.COD_FAB_BATERIA AS q_os_COD_FAB_BATERIA,
q_os.LK_OTT_DESC AS q_os_LK_OTT_DESC,
q_os.VALOR_SERVICOS_BRUTO AS q_os_VALOR_SERVICOS_BRUTO,
q_os.DESCONTOS_SERVICOS AS q_os_DESCONTOS_SERVICOS,
q_os.TOTAL_OS_SERVICOS AS q_os_TOTAL_OS_SERVICOS,
q_os.TOT_SERV AS q_os_TOT_SERV,
q_os.VALOR_ITENS_BRUTO AS q_os_VALOR_ITENS_BRUTO,
q_os.DESCONTOS_ITENS AS q_os_DESCONTOS_ITENS,
q_os.TOTAL_OS_ITENS AS q_os_TOTAL_OS_ITENS,
q_os.TOTAL_OS AS q_os_TOTAL_OS,
q_os.ORC_SERV_BRUTO AS q_os_ORC_SERV_BRUTO,
q_os.ORC_SERV_DESCONTO AS q_os_ORC_SERV_DESCONTO,
q_os.TOTAL_ORC_SERVICOS AS q_os_TOTAL_ORC_SERVICOS,
q_os.ORC_ITEM_BRUTO AS q_os_ORC_ITEM_BRUTO,
q_os.ORC_ITEM_DESCONTO AS q_os_ORC_ITEM_DESCONTO,
q_os.TOTAL_ORC_ITENS AS q_os_TOTAL_ORC_ITENS,
q_os.TOTAL_ORC AS q_os_TOTAL_ORC,
q_os.TOTAL_ORC_BRUTO AS q_os_TOTAL_ORC_BRUTO,
q_os.TOTAL_ORC_DESCONTO AS q_os_TOTAL_ORC_DESCONTO,
q_os.TOTAL_OS_BRUTO AS q_os_TOTAL_OS_BRUTO,
q_os.TOTAL_OS_DESCONTO AS q_os_TOTAL_OS_DESCONTO,
q_os.OBSERVACAO AS q_os_OBSERVACAO,
q_os.LAVAR_VEICULO AS q_os_LAVAR_VEICULO,
sysdate as q_os_data_Atual,
q_os.LISTA_VENDAS AS q_os_LISTA_VENDAS,
q_os.peca_usada_fica_cliente as q_os_PECA_USADA_FICA_CLIENTE,
q_os.Cliente_RG as q_os_CLIENTE_RG,
q_os.Cliente_CGC_CPF as q_os_Cliente_CGC_CPF,
TRIM(BOTH '-' from q_os.cliente_prefixo || '-' || q_os.Cliente_Fone) As q_os_Cliente_fone_contato,
TRIM(BOTH '-' from q_os.prefixo_cel || '-' || q_os.telefone_cel) As q_osCliente_Celular,
TRIM(BOTH '-' from NVL(q_os.PREFIXO_COM, q_os.cliente_prefixo) || '-' || q_os.TELCOM) AS q_os_TELCOM,
TRIM(both '-' from TRIM(q_os.Cliente_Rua ||', '|| q_os.Cliente_Fachada ||' '|| q_os.Cliente_Complemento)) AS q_os_cliente_Endereco,
q_os.Cliente_CEP As q_os_Cliente_CEP,
q_os.cliente_email AS q_os_cliente_email,
q_os.COMBUSTIVEL AS q_os_COMBUSTIVEL,
Q_HISTORICO.HISTORICO_OS AS Q_HIST_ORC_HISTORICO_OS,
q_os.empresa_inscricao_estadual as Q_OSEMPRESA_INSCRICAO_ESTADUAL,
q_os.DATA_EMISSAO AS Q_OS_DATA_EMISSAO,
NVL(Q_LOGO.logo_por_tipo, Q_LOGO.logo_empresa) as Q_LOGO_LOGO,
Q_OS_TERMO.TEXTO as Q_OS_TERMO_TEXTO,
Q_ASSINATURA.CLIENTE_RECEPCAO AS Q_ASSINATURA_CLIENTE_RECEPCAO,
Q_OS.DATA_EMISSAO AS Q_ASSINATURA_CLIE_RECEPCAO_DT,
Q_OS.HORA_EMISSAO AS Q_ASSINATURA_CLIE_RECEPCAO_HR,
Q_ASSINATURA.CLIENTE_ENTREGA AS Q_ASSINATURA_CLIENTE_ENTREGA,
Q_ASSINATURA.CLIENTE_ENTREGA_DT AS Q_ASSINATURA_CLIE_ENTREGA_DT,
Q_ASSINATURA.CONSULTOR_RECEPCAO AS Q_ASSINATURA_CONSULTOR_RECEPC,
Q_ASSINATURA.CONSULTOR_RECEPCAO_DT AS Q_ASSINATURA_CONS_RECEPCAO_DT,
Q_ASSINATURA.CONSULTOR_ENTREGA AS Q_ASSINATURA_CONSULTOR_ENTREGA,
Q_ASSINATURA.CONSULTOR_ENTREGA_DT AS Q_ASSINATURA_CONUL_ENTREGA_DT
  FROM q_os,  Q_HISTORICO, Q_LOGO, Q_OS_TERMO, Q_ASSINATURA
  WHERE 1=1
   AND Q_OS.COD_EMPRESA = Q_HISTORICO.COD_EMPRESA(+)
   AND Q_OS.CHASSI = Q_HISTORICO.CHASSI(+)
   AND Q_OS.NUMERO_OS = Q_ASSINATURA.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_ASSINATURA.COD_EMPRESA(+)]]>
	</queryString>
	<field name="Q_OS_OS_VIA" class="java.lang.String"/>
	<field name="Q_OS_NOME_EMPRESA" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_CGC" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_CEP" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_CID_EST_UF" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_BAIRRO" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_ENDERECO" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_FONE" class="java.lang.String"/>
	<field name="Q_OS_EMPRESA_FAX" class="java.lang.String"/>
	<field name="Q_OS_PLACA" class="java.lang.String"/>
	<field name="Q_OS_NOME_COMPLETO" class="java.lang.String"/>
	<field name="Q_OS_DATA_PROMETIDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="Q_OS_ABS_OSNUM" class="java.lang.Double"/>
	<field name="Q_OS_PRISMA" class="java.lang.String"/>
	<field name="Q_OS_DATA_ENCERRADA" class="java.sql.Timestamp"/>
	<field name="Q_OS_TIPO_DESCRICAO" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_CADASTRO" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_NOME" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_CID_EST_UF" class="java.lang.String"/>
	<field name="Q_OS_FRANQUIA" class="java.lang.Double"/>
	<field name="Q_OS_DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="Q_OS_DATA_LIBERADO" class="java.sql.Timestamp"/>
	<field name="Q_OS_NUMERO_CONTRATO" class="java.lang.String"/>
	<field name="Q_OS_DATA_CONTRATO" class="java.sql.Timestamp"/>
	<field name="Q_OS_COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="Q_OS_TEM_GARANTIA_FABRICA" class="java.lang.String"/>
	<field name="Q_OS_TEM_GARANTIA_ESTENDIDA" class="java.lang.String"/>
	<field name="Q_OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="Q_OS_VENDEDOR_BALCAO" class="java.lang.String"/>
	<field name="Q_OS_DESC_PROD_MOD" class="java.lang.String"/>
	<field name="Q_OS_ANO" class="java.lang.String"/>
	<field name="Q_OS_KM" class="java.lang.Double"/>
	<field name="Q_OS_HORIMETRO" class="java.lang.Double"/>
	<field name="Q_OS_CHASSI" class="java.lang.String"/>
	<field name="Q_OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="Q_OS_LINHA" class="java.lang.String"/>
	<field name="Q_OS_CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="Q_OS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CONCESSIONARIA_BAIRRO" class="java.lang.String"/>
	<field name="Q_OS_CONCESSIONARIA_CEP" class="java.lang.String"/>
	<field name="Q_OS_CONCESSIONARIA_RUA" class="java.lang.String"/>
	<field name="Q_OS_CONCESSIONARIA_CID_EST_UF" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="Q_OS_NOME_DO_MOTORISTA" class="java.lang.String"/>
	<field name="Q_OS_DOCUMENTO_DO_MOTORISTA" class="java.lang.String"/>
	<field name="Q_OS_SERIE" class="java.lang.String"/>
	<field name="Q_OS_VEICULO_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_DIFERENCIAL" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_CAMBIO" class="java.lang.String"/>
	<field name="Q_OS_NR_SERIE_VEICULO" class="java.lang.String"/>
	<field name="Q_OS_DATA_FAB_BATERIA" class="java.sql.Timestamp"/>
	<field name="Q_OS_COD_FAB_BATERIA" class="java.lang.String"/>
	<field name="Q_OS_LK_OTT_DESC" class="java.lang.String"/>
	<field name="Q_OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_DESCONTOS_SERVICOS" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_OS_SERVICOS" class="java.lang.Double"/>
	<field name="Q_OS_TOT_SERV" class="java.lang.Double"/>
	<field name="Q_OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_DESCONTOS_ITENS" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_OS_ITENS" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="Q_OS_ORC_SERV_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_ORC_SERV_DESCONTO" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_ORC_SERVICOS" class="java.lang.Double"/>
	<field name="Q_OS_ORC_ITEM_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_ORC_ITEM_DESCONTO" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_ORC_ITENS" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_ORC" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_ORC_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_ORC_DESCONTO" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_OS_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_OS_DESCONTO" class="java.lang.Double"/>
	<field name="Q_OS_OBSERVACAO" class="java.lang.String"/>
	<field name="Q_OS_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="Q_OS_DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="Q_OS_LISTA_VENDAS" class="java.lang.String"/>
	<field name="Q_OS_PECA_USADA_FICA_CLIENTE" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_RG" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_FONE_CONTATO" class="java.lang.String"/>
	<field name="Q_OSCLIENTE_CELULAR" class="java.lang.String"/>
	<field name="Q_OS_TELCOM" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_ENDERECO" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_CEP" class="java.lang.String"/>
	<field name="Q_OS_CLIENTE_EMAIL" class="java.lang.String"/>
	<field name="Q_OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="Q_HIST_ORC_HISTORICO_OS" class="java.lang.String"/>
	<field name="Q_OSEMPRESA_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="Q_OS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="Q_LOGO_LOGO" class="java.awt.Image"/>
	<field name="Q_OS_TERMO_TEXTO" class="java.lang.String"/>
	<field name="Q_ASSINATURA_CLIENTE_RECEPCAO" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CLIE_RECEPCAO_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CLIE_RECEPCAO_HR" class="java.lang.String"/>
	<field name="Q_ASSINATURA_CLIENTE_ENTREGA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CLIE_ENTREGA_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CONSULTOR_RECEPC" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CONS_RECEPCAO_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CONSULTOR_ENTREGA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CONUL_ENTREGA_DT" class="java.sql.Timestamp"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="104">
			<frame>
				<reportElement x="0" y="49" width="555" height="53" uuid="96128110-cdba-4830-b271-569abec5dc78"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement mode="Transparent" x="480" y="40" width="74" height="12" uuid="29a4a7ef-6e3c-4c28-9f35-2cf85c19d335"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_ABS_OSNUM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="1" y="2" width="36" height="12" uuid="aff9337f-efab-4259-9b1c-925e7a4fc74e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Empresa:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="38" y="2" width="216" height="12" uuid="d6bbb057-0d5c-4317-892a-b74ce159703a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="256" y="2" width="24" height="12" uuid="c292d273-d26c-427e-ad2d-346cc17dd01e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="282" y="2" width="85" height="12" uuid="37e83443-eb62-4f98-aa0d-a25bb42fa26b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_CGC}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="372" y="2" width="182" height="12" uuid="486c36d2-d153-483c-a06c-6dfafe9da307"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OSEMPRESA_INSCRICAO_ESTADUAL}.equals("") ? "" : "Insc.Estad.: " + $F{Q_OSEMPRESA_INSCRICAO_ESTADUAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="504" y="14" width="44" height="12" uuid="93b48c4a-730a-4336-b58d-ab26ebfe8c2e"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_CEP}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="484" y="14" width="20" height="12" uuid="bcd8feb3-0f52-4029-bea9-fa385f7564fd"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="324" y="14" width="160" height="12" uuid="07280668-c7f3-4127-833a-c1b4cd760c2c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_CID_EST_UF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="234" y="14" width="90" height="12" uuid="08bb00c9-1c85-45f8-84ad-f9074e57d7f9"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_BAIRRO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="208" y="14" width="26" height="12" uuid="ea6ecefb-dbad-4138-97de-7e0b0431a429"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="1" y="14" width="207" height="12" uuid="05176772-8245-41c5-afab-4b3edc698eae"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_ENDERECO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="183" y="26" width="23" height="12" uuid="dfbaed03-6e2c-4bd0-a958-f17d1f1ef092"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Fone:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="206" y="26" width="73" height="12" uuid="a0526733-b7c2-403f-8150-41eb6027f60a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_FONE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="287" y="26" width="18" height="12" uuid="1d3d5b55-8c06-452a-bec1-adf9349077f6"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Fax:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="305" y="26" width="71" height="12" uuid="1def164f-ff09-43cc-8fd2-1e624cf50272"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_EMPRESA_FAX}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="467" y="40" width="13" height="12" uuid="224dc3e4-6246-4241-b30b-616a0ae11fcb"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="235" y="38" width="107" height="14" uuid="0ea87ce1-562a-46c2-a8ac-fcfd4c9d1805"/>
					<textElement textAlignment="Center">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[ORDEM DE SERVIÇO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="346" y="40" width="43" height="12" uuid="46867b55-457f-465e-a361-307abb8a3e17"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Impressão:]]></text>
				</staticText>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="432" y="40" width="33" height="12" uuid="cebf2cbc-9f31-43f6-a4ed-8ccc66ff3a75"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_ATUAL}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="5" y="40" width="43" height="13" uuid="039e7ebf-5a96-41fc-98dd-12735513a80c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Impressão:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="389" y="40" width="43" height="12" uuid="984bde09-dcfa-4047-979a-64c9eae108aa"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_ATUAL}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="48" y="40" width="43" height="13" uuid="5c0136d3-26b5-49eb-86a3-129de15d5936"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="91" y="40" width="26" height="13" uuid="d3dd2799-9e24-424a-afc2-aa2a5bb605f7"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="0" width="555" height="48" uuid="6c216374-259f-4b1c-80d7-8989cd06f94b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle">
					<reportElement x="9" y="0" width="92" height="48" uuid="8812cf15-5bb7-401a-b4f2-1644b622f1c5"/>
					<imageExpression><![CDATA[$F{Q_LOGO_LOGO}]]></imageExpression>
				</image>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="254" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="254" uuid="a909be0e-a55f-4173-80c2-713efffb029d">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="465" y="26" width="47" height="12" uuid="496156ac-2992-435b-bca6-3445259ab56c"/>
					<box rightPadding="2"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_ENCERRADA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="400" y="26" width="65" height="12" uuid="9646b099-9430-444e-b893-659265958b16">
						<printWhenExpression><![CDATA[$F{Q_OS_DATA_ENCERRADA}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Encerramento:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="465" y="2" width="47" height="12" uuid="6c386030-6752-4bd2-a60e-83f472cbf243"/>
					<box rightPadding="2"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="400" y="2" width="65" height="12" uuid="2d6c8aa3-f047-42fe-b337-a941819a5ed6"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Entrada:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="235" y="3" width="41" height="12" uuid="f4d20f4b-d0f0-43c0-8fb7-075c46f7fa76"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PRISMA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="205" y="3" width="29" height="12" uuid="de9def4b-0c31-4486-94c0-d7d0816d59d6"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Prisma:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="58" y="3" width="145" height="12" uuid="c334a6ba-d9ea-42e2-a9cb-4b7ac4459df7"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TIPO_DESCRICAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="4" y="3" width="22" height="12" uuid="c47d2ebf-b3b3-44a8-acc6-09c7bd0f805c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="512" y="2" width="12" height="12" uuid="43aa043b-6b09-44ef-9d1f-6b3a1b71c8cf"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[as]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="400" y="14" width="65" height="12" uuid="55175dff-d67d-4051-bd67-6cdb0ba0c3cb"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Previsao Entrega:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="465" y="14" width="47" height="12" uuid="647f4b6b-10aa-4b18-8a54-fc8c3d251eb4"/>
					<box rightPadding="2"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="512" y="14" width="12" height="12" uuid="9b8e3789-5a34-4801-b0d1-126794e8d217"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[as]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="4" y="27" width="54" height="12" uuid="2c05fb9f-b203-4da2-8085-aa7c7a364d88"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Responsável:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="58" y="28" width="167" height="12" uuid="d4012bb2-76fe-4cc1-9db2-7896540a5667"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NOME_COMPLETO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="512" y="26" width="12" height="12" uuid="fbb66ad4-3879-42dd-8a40-d606ad3e12aa">
						<printWhenExpression><![CDATA[$F{Q_OS_DATA_ENCERRADA}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[as]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="228" y="28" width="57" height="12" uuid="9e52953a-d176-4873-8ba8-79f99438d3c0">
						<printWhenExpression><![CDATA[$F{Q_OS_LISTA_VENDAS}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Notas Fiscais:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="285" y="28" width="107" height="12" uuid="db6fd27b-e200-44b9-9fc6-277adc76e971"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_LISTA_VENDAS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="4" y="15" width="54" height="12" uuid="92501940-1aa8-4294-96ee-f74fdeb7d53a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Relacionadas:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="311" y="14" width="37" height="12" uuid="c8226803-eaf0-4fa0-993d-74105ef01854"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Franquia:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="350" y="14" width="41" height="12" uuid="3ec4a868-f161-410b-871d-d24fa697eaae"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_FRANQUIA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="400" y="38" width="65" height="12" uuid="c8b0b885-4720-4d4d-8c5b-418dffb2e10a">
						<printWhenExpression><![CDATA[$F{Q_OS_DATA_ENTREGA}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Entregue:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="465" y="38" width="47" height="12" uuid="97dea253-fc5a-4f66-b1a2-4fae08559d0a"/>
					<box rightPadding="2"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_ENTREGA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="512" y="38" width="12" height="12" uuid="e87c2e2c-52b7-4c9c-916e-cd621c4e97d8">
						<printWhenExpression><![CDATA[$F{Q_OS_DATA_ENTREGA}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[as]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="400" y="50" width="65" height="12" uuid="aaa8d32a-c354-47dd-937c-4088d9b8bef7">
						<printWhenExpression><![CDATA[$F{Q_OS_DATA_LIBERADO}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Liberada:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="465" y="50" width="47" height="12" uuid="ac94a43a-b243-4f94-8d50-0077f5076041"/>
					<box rightPadding="2"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_LIBERADO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="512" y="50" width="12" height="12" uuid="87b28beb-de2d-4e3b-a593-34e0f076d3bc">
						<printWhenExpression><![CDATA[$F{Q_OS_DATA_LIBERADO}.equals("")?Boolean.FALSE : Boolean.TRUE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[as]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="320" y="53" width="9" height="8" uuid="aeb7d457-a562-4f29-adca-62c976c9a3bd">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_LAVAR_VEICULO}.equals("S") ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="331" y="52" width="61" height="12" uuid="0536a3e5-632f-428d-9205-2d98808008f1">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Lavar Veículo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="4" y="40" width="91" height="12" uuid="2f16fca8-88db-423b-a570-8dcddfeeb35e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Nº Contr./Pacote TMAC:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="95" y="40" width="84" height="12" uuid="e38debee-2c32-4068-812a-96822ee01a1e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_CONTRATO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="178" y="40" width="58" height="12" uuid="a2450dd5-60bf-4f4f-b3b2-068862425c1e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Data Ini. Contr.:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="235" y="40" width="43" height="12" uuid="cc0ffe34-3979-42cd-b568-649ced2609b8"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_CONTRATO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="320" y="66" width="9" height="8" uuid="97ecdbb6-9d2b-447f-9149-a54e0d5b5057"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PECA_USADA_FICA_CLIENTE}.equals("S") ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="331" y="65" width="61" height="12" uuid="7d664bb2-03c3-4d50-b1a8-5187cfd83401"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Levar Peças]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="400" y="62" width="65" height="14" uuid="bb5a8e37-b7f1-4abf-b257-36de2b5525ae"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[N. Pré O.S.:]]></text>
				</staticText>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement mode="Transparent" x="464" y="62" width="86" height="14" uuid="93c4272b-a365-414e-86dc-cad50c518a3b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_COD_OS_AGENDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="79" y="52" width="76" height="12" uuid="96afe012-92ff-458f-9e1e-27b5224acb09"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Garantia Fábrica]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="190" y="52" width="76" height="12" uuid="f7a313fd-d4bc-43f6-a098-f306d00bef3f"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Garantia Estendida]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="68" y="54" width="9" height="8" uuid="3331bc32-593f-4715-91b6-04cdb8333e5c"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TEM_GARANTIA_FABRICA}.equals("S") ? "X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="179" y="54" width="9" height="8" uuid="9f2bb5a5-539b-41f4-94a7-11d7533ac70e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TEM_GARANTIA_ESTENDIDA}.equals("S") ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="4" y="76" width="39" height="12" uuid="f8ccdc04-8a50-4dc4-ae5d-259bb2e907ed"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo Fáb.:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="43" y="76" width="87" height="12" uuid="5bc1f1ff-f339-473f-9b45-62a8431abee5"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TIPO_FABRICA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="318" y="77" width="82" height="11" uuid="2640266f-904a-45b9-ac11-31c3a127e032"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Vendedor Balcão :]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="400" y="77" width="150" height="11" uuid="bad924c4-62c9-4dd4-9305-57a1e38edec3"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_VENDEDOR_BALCAO}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="0" y="90" width="555" height="63" uuid="1ebae522-6266-46b4-a6fd-1b5b28156d1f"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="4" y="3" width="54" height="12" uuid="3459ae01-ac29-48b1-b1a7-d20a7823c27b"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Cliente]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="325" y="4" width="9" height="8" uuid="d690f937-0f80-4aef-8942-5916da77c435">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="6" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_CADASTRO}.equals("S") ? "X":" "]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="336" y="2" width="35" height="12" uuid="b3f28027-53a9-4728-8af7-66aa9e3b42d4"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Cadastro]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="58" y="3" width="262" height="12" uuid="6c3fcc1a-df6e-4b71-9434-9cc811d2cb76"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_NOME}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="89" y="27" width="231" height="12" uuid="ea1e8442-4250-4576-b2a2-8503a886c9d8"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_BAIRRO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="58" y="27" width="31" height="12" uuid="9ce648ee-90bc-4867-86b4-538ef540a058"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Bairro:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="323" y="39" width="21" height="12" uuid="cd448c31-c0e5-4a7f-bb7f-f99d20d9207c"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[CEP:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="58" y="39" width="262" height="12" uuid="94fd0c11-ea7c-4f92-a8fc-c813d988e104"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_CID_EST_UF}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="58" y="51" width="32" height="12" uuid="610b37d5-09e5-471a-945c-ea9f92299e11"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Email:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="476" y="15" width="74" height="12" uuid="510d23d3-0773-47d7-8af1-dbd17f9e6e64"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_CGC_CPF}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="476" y="39" width="74" height="12" uuid="e16c2a27-420c-4e8c-a617-62746bc28e39"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OSCLIENTE_CELULAR}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="476" y="27" width="74" height="12" uuid="ee517c39-ab13-466f-98d3-dcf650309dfd"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_FONE_CONTATO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="476" y="3" width="74" height="12" uuid="7724fed4-d84d-4577-a2c8-a66b36d60325"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_RG}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="432" y="3" width="44" height="12" uuid="d965478f-f3b4-4746-b941-421e4f0064b9"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[RG:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="432" y="15" width="44" height="12" uuid="d3ca4a4d-231f-4cf7-8b99-7841e0975d26"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[CPF/CGC:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="432" y="27" width="44" height="12" uuid="183cd712-d812-43d6-826c-82d4878cf6c8"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Fone:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="432" y="39" width="44" height="12" uuid="a56d0f13-f5d1-48a0-9535-fb6242113c0e"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Celular:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="432" y="51" width="44" height="12" uuid="94b889c0-20ae-4b07-be1b-d7fbe692c080"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Tel Com:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="476" y="51" width="74" height="12" uuid="13c79c6c-481b-40ca-ba7e-24bbdd60ea31"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_TELCOM}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="344" y="39" width="56" height="12" uuid="da7a7b08-12f4-488f-9939-1e8ea189b588"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_CEP}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="58" y="15" width="362" height="12" uuid="ac15ef21-99f2-4e79-83f5-3f6cf7467385"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_ENDERECO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="90" y="51" width="230" height="12" uuid="3e43e6af-20c0-472b-aad6-72d3f5d27f3d"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CLIENTE_EMAIL}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="0" y="153" width="555" height="101" uuid="1d2cb15f-41c4-4181-8764-2a722d83222e"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="4" y="2" width="35" height="12" uuid="80be150f-8c41-4868-bfa4-4c15650abd1c"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Veículo]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="39" y="2" width="63" height="12" uuid="9caef298-eac4-4558-8f97-dee5bede35c5"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Produto/Modelo:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="104" y="2" width="210" height="12" uuid="4e6b04fd-7649-4dc0-a93e-479fb96b93c2"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DESC_PROD_MOD}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="39" y="14" width="46" height="12" uuid="f9f97dec-4c9d-4336-a0ce-3c7949e81f56"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Chassi:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="39" y="26" width="46" height="12" uuid="ae75ec25-f539-48aa-9866-7348962181d2"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Cor Externa:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="372" y="26" width="24" height="12" uuid="7e3aaea8-5833-4be7-a136-fe5a659d2bee"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Linha:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="450" y="14" width="28" height="12" uuid="0c0f847c-5cb1-4e8f-ae27-742067410cdd"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Placa:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="450" y="2" width="49" height="12" uuid="b5ad3bd3-43c8-476b-9e75-5178592ae8e2"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Ano/Modelo:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="372" y="2" width="30" height="12" uuid="7b028de8-5564-4418-828a-a5df8d1d6f44"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[KM:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="372" y="14" width="30" height="12" uuid="081fe22a-ac0d-46fe-8362-2460ed3a55f9"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Hr:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="450" y="26" width="49" height="12" uuid="18f91f53-8ba0-402b-a977-886f560e8d17"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Combustivel:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="499" y="2" width="48" height="12" uuid="15c20fa5-b57a-4997-9336-8b417f798041"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_ANO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="499" y="14" width="48" height="12" uuid="598e7827-b94f-4105-89ad-cc6eb1b510f2"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_PLACA}]]></textFieldExpression>
					</textField>
					<textField pattern="#0.###;(#0.###-)">
						<reportElement mode="Transparent" x="403" y="2" width="42" height="12" uuid="4813a973-085e-46dd-90e2-e5f0da561306"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_KM}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="403" y="14" width="42" height="12" uuid="280627bf-0901-40a3-a83a-f83237df0a97"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORIMETRO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="104" y="14" width="129" height="12" uuid="9abd3547-fbb4-4eab-a3eb-93d13094a4dc"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CHASSI}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="104" y="26" width="129" height="12" uuid="93355da7-6b41-4a2b-a376-94a917220184"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_COR_EXTERNA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="403" y="26" width="45" height="12" uuid="53a8fbac-f53d-454e-b07a-a19f6acbc4d9"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_LINHA}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="4" y="62" width="120" height="12" uuid="5c321aea-39af-41b7-9454-d4ca5d18f589"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Concessionária Vendedora]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="124" y="62" width="136" height="12" uuid="75663531-f94d-451c-870c-1381534ffa6f"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CONCESSIONARIA_NOME}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="391" y="62" width="47" height="12" uuid="2ed566b9-8b6d-4d33-9d5b-16649c28c572"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Data Venda:]]></text>
					</staticText>
					<textField pattern="dd/MM/yyyy">
						<reportElement mode="Transparent" x="438" y="62" width="45" height="12" uuid="6ffa9872-47bd-4b7b-8e7c-136583bb9842"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_VENDA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="294" y="74" width="94" height="12" uuid="374027e7-5d5d-485a-bd43-f30e4a23d2c2"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CONCESSIONARIA_BAIRRO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="507" y="62" width="42" height="12" uuid="47e59964-d4a5-43f9-a314-a93b6aac72f3"/>
						<textElement textAlignment="Right">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CONCESSIONARIA_CEP}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="41" y="74" width="226" height="12" uuid="00760d4a-e506-499e-be40-31dad52e767a"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CONCESSIONARIA_RUA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="391" y="74" width="159" height="12" uuid="3540fb8f-ce70-40f0-92e5-8834a20d4f0a"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CONCESSIONARIA_CID_EST_UF}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="268" y="74" width="26" height="12" uuid="7dfa3280-0072-42c2-a638-c7eda2dc8221"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Bairro:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="486" y="62" width="21" height="12" uuid="0c0d1af0-10e0-4c74-bd23-2843faf75ece"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[CEP:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="234" y="14" width="39" height="12" uuid="1e85e634-44e3-479d-80bb-e0736e3fb02c"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Renavan:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="275" y="14" width="94" height="12" uuid="51be9239-5667-4824-91ad-a6bd7cb612c5"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_MOTOR}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="39" y="38" width="63" height="12" uuid="76bba2f6-53d0-47c2-bb5d-a5ae9dcea3e3"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Motorista:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="104" y="38" width="129" height="12" uuid="314e23b4-8ceb-46bb-bb4b-0c1d475a70f7"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_NOME_DO_MOTORISTA}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="372" y="38" width="49" height="12" uuid="3d081b8e-fd3e-40ae-be73-60713ea8b37c"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Documento:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="421" y="38" width="129" height="12" uuid="849ef0fd-fed2-4a58-b774-969505e67312"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DOCUMENTO_DO_MOTORISTA}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="234" y="26" width="30" height="12" uuid="4edc7e71-511c-4c2e-8b6f-ffaeeefd4228"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Serie:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="275" y="26" width="94" height="12" uuid="3792ef56-a10f-4457-919a-43940fc01358"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_SERIE}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="331" y="2" width="38" height="12" uuid="36b43084-57d8-4faa-a7e4-ea88f85b6102"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Blindado]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="321" y="4" width="9" height="8" uuid="c90e32bf-3398-4860-9ae3-683c2d41db09">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_VEICULO_BLINDADO}.equals("S") ? "X":" "]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="275" y="38" width="94" height="12" uuid="7dcbb0f4-c781-438d-a6c6-c3854939dde6"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_DIFERENCIAL}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="234" y="38" width="30" height="12" uuid="369a5162-8275-4dd7-bb7b-7ab7439ef40a"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[DIF:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="261" y="62" width="30" height="12" uuid="f4290464-780e-4ceb-9d07-980e65add28e"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Camb:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="291" y="62" width="97" height="12" uuid="eaa24e5b-2b83-4b19-9a93-2b770af1fad7"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_CAMBIO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="39" y="50" width="63" height="12" uuid="24b92c13-f007-450d-a92e-173b30b84f35"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Nr. Série Veíc.:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="104" y="50" width="129" height="12" uuid="733c5677-6569-48e9-bb5d-d6b0935f9246"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_NR_SERIE_VEICULO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="234" y="50" width="64" height="12" uuid="1bc11e44-329b-4600-9fcc-bb225407f8ed"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Dt. Fab. Bateria:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="307" y="50" width="62" height="12" uuid="30343f73-f4d7-481b-b03d-3eed2c9a5447"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_FAB_BATERIA}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="372" y="50" width="48" height="12" uuid="81708fa3-f38c-417e-8c2d-9a6f8f487f22"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Nº Bateria:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="420" y="50" width="130" height="12" uuid="08aaac44-8b12-49ea-8f5a-b1fc881ce670"/>
						<textElement textAlignment="Left">
							<font size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_COD_FAB_BATERIA}]]></textFieldExpression>
					</textField>
					<line>
						<reportElement x="512" y="33" width="1" height="4" uuid="288137c2-4f7e-44fa-906f-686966f36051">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="0.8"/>
						</graphicElement>
					</line>
					<textField>
						<reportElement x="499" y="26" width="51" height="11" uuid="2b2e363c-9cb0-4733-8a09-cc7be0bd626b"/>
						<box leftPadding="5">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_COMBUSTIVEL} < 19 ? "":
$F{Q_OS_COMBUSTIVEL} < 39 ? "X" :
$F{Q_OS_COMBUSTIVEL} < 59 ? "X   X":
$F{Q_OS_COMBUSTIVEL} < 79 ? "X   X   X":
 "X   X   X   X"]]></textFieldExpression>
					</textField>
					<line>
						<reportElement x="530" y="35" width="1" height="2" uuid="4e2fe632-6908-4945-8d50-c1265d029b45">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="0.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="518" y="35" width="1" height="2" uuid="9f420b63-8f35-4043-9930-8f5f35b2f174">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="0.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="506" y="35" width="1" height="2" uuid="2393fc25-96b2-4daf-93f5-6a0a7e2f4f80">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="0.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="542" y="35" width="1" height="2" uuid="0219bf73-bbe4-4ad9-85ad-fab9573c08fc">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="0.6"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="536" y="33" width="1" height="4" uuid="0bf3c0a2-ffed-4ec0-9d79-0114405ff1d4">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<graphicElement>
							<pen lineWidth="0.8"/>
						</graphicElement>
					</line>
					<line>
						<reportElement x="524" y="31" width="1" height="6" uuid="14ed2df8-4896-4573-82ef-61b7be12fa9a">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
					</line>
				</frame>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="524" y="26" width="26" height="12" uuid="e3569bb9-1e65-4cbd-af44-ab3db8ad6150"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_ENCERRADA}]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="524" y="14" width="26" height="12" uuid="357d2744-294b-4d52-a349-68b27ab54fd5"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="524" y="2" width="26" height="12" uuid="e08cb393-585c-4514-bfbf-e395167906e7"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="524" y="38" width="26" height="12" uuid="f020acda-05b2-4f0d-b78f-2b0d0816a192"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="524" y="50" width="26" height="12" uuid="0fbde8c6-747d-4dc7-a1f6-4ad1d79b383f"/>
					<box leftPadding="2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_LIBERADO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="58" y="16" width="263" height="12" uuid="a21f944a-1cde-4044-b0c6-c11d4e6a737c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_HIST_ORC_HISTORICO_OS}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="84" splitType="Stretch">
			<printWhenExpression><![CDATA[1.0 == 2.0]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="555" height="84" uuid="f9eb589a-87c4-4397-8d37-e22876176519">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image>
					<reportElement x="0" y="3" width="555" height="69" uuid="db1a2fa7-eaca-470c-b1e5-cbcbc6f0eddb"/>
					<imageExpression><![CDATA[""]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="0" y="72" width="44" height="12" uuid="b9ad664e-8bf9-4cae-b62a-3172324f8617"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[LEGENDA:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="44" y="72" width="253" height="12" uuid="271ede2e-df04-407c-852e-8de869a80c0e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[A: AMASSADO | F: FALTANTE | Q: QUEBRADO | R: RISCADO]]></text>
				</staticText>
			</frame>
		</band>
		<band height="30">
			<subreport>
				<reportElement x="0" y="0" width="555" height="30" isRemoveLineWhenBlank="true" uuid="8efe3842-8577-408d-a945-a4fee5e378ee"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsBmwSubReclamacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="40">
			<subreport>
				<reportElement x="0" y="0" width="555" height="40" isRemoveLineWhenBlank="true" uuid="fdea0ac8-4c37-4900-97f0-95a5eed399b8"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsBmwSubPecasServicos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="55">
			<subreport>
				<reportElement x="0" y="0" width="555" height="55" isRemoveLineWhenBlank="true" uuid="159a3fb1-996f-4cd2-90a9-1d6855b4a386"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"OsBmwSubTotais.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="97" splitType="Stretch">
			<frame>
				<reportElement positionType="Float" x="0" y="52" width="555" height="45" uuid="cd8e3353-4311-4c08-a1ff-5d618375678e"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="4" y="1" width="201" height="14" uuid="5d1df50d-712c-4281-9825-0d357e4537cb"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Autorizo a execução dos serviços acima mencionados]]></text>
				</staticText>
				<line>
					<reportElement x="225" y="29" width="287" height="1" uuid="cb181ced-0d0e-439c-90a7-39495f3c657b"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="225" y="31" width="287" height="12" uuid="a35c29fc-102d-467d-ad6b-ee337f9bea9e"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente ou pessoa por ele autorizada]]></text>
				</staticText>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle">
					<reportElement x="278" y="3" width="186" height="26" uuid="59bb860b-9bbc-4f81-9ff0-4daf5065fcb5"/>
					<imageExpression><![CDATA[$F{Q_ASSINATURA_CLIENTE_RECEPCAO}]]></imageExpression>
				</image>
			</frame>
			<frame>
				<reportElement x="0" y="3" width="555" height="14" uuid="02fa673b-d6ce-40cc-bf76-83786e2b8c95"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="4" y="0" width="56" height="12" uuid="9f6649fd-c7b6-4767-b228-6968cebf48c2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Diagnostico:]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="20" width="555" height="28" isPrintWhenDetailOverflows="true" uuid="092593ac-74b4-40be-a29b-f86f6b4b49aa"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isStretchWithOverflow="true">
					<reportElement mode="Transparent" x="8" y="4" width="540" height="20" uuid="0288da70-1129-4d7a-8be6-96efa06931e7"/>
					<box padding="0"/>
					<textElement textAlignment="Justified">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TERMO_TEXTO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
