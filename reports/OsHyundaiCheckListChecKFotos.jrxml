<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiCheckListChecKFotos" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="TESTE_FREEDOM.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\31381\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT count(1) as TEM_REGISTROS
FROM MOB_OS_PERTENCE_FOTO F, MOB_PERTENCE_ITEM PI, MOB_PERTENCE_GRUPO PG, MOB_OS_PERTENCE MOP
WHERE PI.COD_ITEM = F.COD_ITEM 
 AND PI.ID_GRUPO = PG.ID_GRUPO
 AND PG.APLICACAO in ('R', 'E')
 AND F.COD_EMPRESA = $P{COD_EMPRESA} 
 AND F.NUMERO_OS = $P{NUMERO_OS}
 AND MOP.NUMERO_OS = F.NUMERO_OS
 AND MOP.COD_EMPRESA = F.COD_EMPRESA
 AND MOP.COD_ITEM = F.COD_ITEM
ORDER BY F.ORDEM]]>
	</queryString>
	<field name="TEM_REGISTROS" class="java.lang.Double"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<columnHeader>
		<band height="50">
			<printWhenExpression><![CDATA[$F{TEM_REGISTROS} >  0.0]]></printWhenExpression>
			<staticText>
				<reportElement mode="Transparent" x="70" y="20" width="403" height="18" forecolor="#000000" uuid="7d1b9e46-0232-4f7f-b908-fb01e8ada4f5">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[Imagens  - Entrada e Saída Veículo]]></text>
			</staticText>
			<break>
				<reportElement x="0" y="4" width="100" height="1" uuid="c3d2b69c-4dfa-40ac-87ef-54b63523269b"/>
			</break>
		</band>
	</columnHeader>
	<detail>
		<band height="24">
			<subreport>
				<reportElement x="280" y="0" width="275" height="24" isRemoveLineWhenBlank="true" uuid="d955f41d-d807-482c-87d4-52cb1048084b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="APLICACAO">
					<subreportParameterExpression><![CDATA["E"]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListChecKCheckListFotos.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="0" y="0" width="275" height="24" isRemoveLineWhenBlank="true" uuid="0b31fb4f-145c-445f-b880-80c436d8ecc9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListChecKCheckListFotos.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
