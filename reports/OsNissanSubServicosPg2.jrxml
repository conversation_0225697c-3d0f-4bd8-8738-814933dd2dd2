<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsNissanSubServicosPG2" pageWidth="555" pageHeight="842" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0"/>
	<style name="camposNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT 
	   ROW_NUMBER() OVER (ORDER BY OS_ORIGINAL.ITEM) AS NUM_LISTA,
	   OS_ORIGINAL.ITEM as obs_Item, 
       OS_ORIGINAL.OBSERVACAO as Obs_Observacao
FROM OS_ORIGINAL OS_ORIGINAL
WHERE ( ( OS_ORIGINAL.NUMERO_OS = $P{NUMERO_OS} ) AND
  ( OS_ORIGINAL.COD_EMPRESA = $P{COD_EMPRESA} ) )
ORDER BY
 OS_ORIGINAL.ITEM]]>
	</queryString>
	<field name="NUM_LISTA" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="NUM_LISTA"/>
		<property name="com.jaspersoft.studio.field.label" value="NUM_LISTA"/>
	</field>
	<field name="OBS_ITEM" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="OBS_ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="OBS_ITEM"/>
	</field>
	<field name="OBS_OBSERVACAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="OBS_OBSERVACAO"/>
		<property name="com.jaspersoft.studio.field.label" value="OBS_OBSERVACAO"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="11">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="60" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="d64b78a2-04b6-4573-86a6-8f71eec424e3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<pen lineColor="#DA080B"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#DA080B"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Observações]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="60" y="0" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="fa107590-a7f2-4cad-a3f4-f605db0f4c8d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição do Serviço]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="140" y="0" width="415" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="a77f4f17-0548-466a-acec-124b3b81aadd">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0" leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBS_OBSERVACAO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,#00.###;#,#00.###-">
				<reportElement mode="Opaque" x="0" y="0" width="60" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="d02c7a5a-b5c2-4a21-989e-2061704d9bc6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NUM_LISTA} == 1 ? "do Serviço":" "]]></textFieldExpression>
			</textField>
			<textField pattern="#,#00.###;#,#00.###-">
				<reportElement mode="Opaque" x="60" y="0" width="80" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="7b62f7c6-655f-4489-b58a-106379734c20">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBS_ITEM}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
