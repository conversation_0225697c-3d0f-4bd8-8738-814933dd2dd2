<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsNissan" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isFloatColumnFooter="true" uuid="1e4b486e-1a0e-442f-af9e-a2b1b9d4b445">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="RemoverNull" isDefault="true" isBlankWhenNull="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\31830\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString>
		<![CDATA[SELECT
  
  
  OS.COD_EMPRESA                    AS OS_COD_EMPRESA,
  OS_AGENDA.SIGNATURE AS OS_ASSINATURA,
  OS.STATUS_OS                    AS OS_STATUS_OS,
  OS.COD_OS_AGENDA                  AS OS_COD_OS_AGENDA,
  NVL(OS.NUMERO_OS, 0)                    AS OS_NUMERO_OS, 
  ABS(OS.NUMERO_OS)                  AS OS_ABS_OSNUM,
  OS.NUMERO_OS_FABRICA                AS OS_NUMERO_OS_FABRICA,
  OS.COD_CLIENTE                    AS OS_COD_CLIENTE, 
  OS.CLIENTE_RAPIDO                  AS OS_CLIENTE_RAPIDO,
  OS.TIPO_ENDERECO                  AS OS_TIPO_ENDERECO,
  NVL(OS.OBSERVACAO, ' ')                    AS OS_OBSERVACAO,
  OS.EXTENDIDA                    AS OS_EXTENDIDA,
  OS.SEGURADORA                    AS OS_SEGURADORA,
  NVL(TO_CHAR(OS.DATA_EMISSAO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_EMISSAO,
  NVL(TO_CHAR(OS.DATA_LIBERADO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_LIBERADO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2)     AS OS_DIA_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2)     AS OS_MES_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4)     AS OS_ANO_EMISSAO,
  OS.HORA_EMISSAO                    AS OS_HORA_EMISSAO,
  OS.HORA_ENCERRADA                  AS OS_HORA_ENCERRADA,
  OS.DATA_ENCERRADA                  AS OS_DATA_ENCERRADA,
  NVL(OS.HORA_PROMETIDA,' ')                  AS OS_HORA_PROMETIDA, 
  SUBSTR(OS.HORA_PROMETIDA, 1, 2)           AS OS_HORA24_PROMETIDA,
  SUBSTR(OS.HORA_PROMETIDA, 4, 2)           AS OS_MINUTO_PROMETIDA,
  NVL(TO_CHAR(OS.DATA_PROMETIDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PROMETIDA, 
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2)     AS OS_DIA_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2)     AS OS_MES_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4)   AS OS_ANO_PROMETIDA,
  NVL(OS.VALOR_SERVICOS_BRUTO, 0)                AS OS_VALOR_SERVICOS_BRUTO,
  NVL(OS.VALOR_ITENS_BRUTO, 0)                AS OS_VALOR_ITENS_BRUTO,
  NVL(OS.DESCONTOS_SERVICOS, 0)                AS OS_DESCONTOS_SERVICOS,
  NVL(OS.DESCONTOS_ITENS, 0)                  AS OS_DESCONTOS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS,0)                           AS OS_TOTAL_OS_SERVICOS,
  NVL(OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS,0)                                 AS OS_TOTAL_OS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO,0)                            AS OS_TOTAL_OS_BRUTO,
  NVL(OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS,0)                                AS OS_TOTAL_OS_DESCONTO,
  NVL((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) + (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS), 0)   AS OS_TOTAL_OS,
  OS.COD_SEGURADORA                  AS OS_COD_SEGURADORA,
  OS.TIPO                        AS OS_TIPO,
  OS.NOME                       AS CONSULTOR,
  OS.OS_ENTRADA                    AS OS_OS_ENTRADA,
  '1ª VIA CLIENTE 1ª VIA CLIENTE 1ª VIA CLIENTE'     AS OS_VIA,
  OS.TOTAL_IMPRESSAO_FABRICA              AS OS_TOTAL_IMPRESSAO_FABRICA,
  NVL(OS_TIPOS_TERMO.TEXTO,' ')            AS OS_TERMO_TEXTO,
  NVL(TO_CHAR(OS.DATA_TAXI, 'DD/MM/YYYY'),' ')                  AS OS_DATA_TAXI,
  NVL(OS.HORA_TAXI,' ')                  AS OS_HORA_TAXI, 
  NVL(OS.TODO_TRAB,' ') AS OS_TODO_TRABALHO_FEITO,
  NVL(OS.TODO_LIMPO,' ') AS OS_TODO_LIMPO,
  
  NVL(os.pri_rodagem,'N')                  AS OS_PRIMEIRA_RODAGEM,
  NVL(os.seg_rodagem,'N')                  AS OS_SEGUNDA_RODAGEM,
  NVL(TO_CHAR(os.data_pri_rodagem, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PRIMEIRA_RODAGEM,
  NVL(TO_CHAR(os.data_seg_rodagem, 'DD/MM/YYYY'),' ')                  AS OS_DATA_SEGUNDA_RODAGEM,
  NVL(os.os_pri_resp,' ')                  AS OS_PRIM_RESPONSAVEL_RODAGEM,
  NVL(os.os_seg_resp,' ')                  AS OS_SEG_RESPONSAVEL_RODAGEM,
  
 
  
  (SELECT LISTAGG(FORMAS_PAGAMENTO.CONDICAO_PAGAMENTO, '; ') WITHIN GROUP (ORDER BY CONDICAO_PAGAMENTO)        
  FROM (SELECT (F.DESCRICAO || ' ' ||  TO_CHAR(O.VALOR, 'FM999999999D00')) AS CONDICAO_PAGAMENTO FROM OS_PAGAMENTO O, FORMA_PGTO F
  WHERE O.COD_FORMA_PGTO = F.COD_FORMA_PGTO
	  AND O.NUMERO_OS = $P{NUMERO_OS}
	  AND O.COD_EMPRESA= $P{COD_EMPRESA}) FORMAS_PAGAMENTO) AS OS_DESCRICAO_PAGAMENTO,


  
  (SELECT LISTAGG(ORCAMENTOS.NUMERO_OS, ', ') WITHIN GROUP (ORDER BY NUMERO_OS)
	FROM 
	  (SELECT ABS(OS.NUMERO_OS) AS NUMERO_OS
	  FROM OS, OS_STATUS, OS_ORCAMENTOS, OS_TIPOS
	  WHERE OS.STATUS_OS = OS_STATUS.STATUS_OS
		AND OS_ORCAMENTOS.COD_EMPRESA = OS.COD_EMPRESA
		AND OS_ORCAMENTOS.NUMERO_ORCAMENTO = OS.NUMERO_OS
		AND OS.TIPO = OS_TIPOS.TIPO
		AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
		AND OS_ORCAMENTOS.COD_EMPRESA = $P{COD_EMPRESA}
		AND OS_ORCAMENTOS.NUMERO_OS = $P{NUMERO_OS}
	  GROUP BY OS.NUMERO_OS
	  ORDER BY OS.NUMERO_OS) ORCAMENTOS) AS OS_NUMERO_ORCAMENTO,
	  
	(SELECT LISTAGG(ORCAMENTOS.NOME_CLIENTE_APROVOU, ', ') WITHIN GROUP (ORDER BY ORCAMENTOS.NOME_CLIENTE_APROVOU) 
		FROM 
    (SELECT OS_ORCAMENTOS.NOMECLIAPROVOU AS NOME_CLIENTE_APROVOU
    FROM OS, OS_STATUS, OS_ORCAMENTOS, OS_TIPOS
    WHERE OS.STATUS_OS = OS_STATUS.STATUS_OS
    AND OS_ORCAMENTOS.COD_EMPRESA = OS.COD_EMPRESA
    AND OS_ORCAMENTOS.NUMERO_ORCAMENTO = OS.NUMERO_OS
    AND OS.TIPO = OS_TIPOS.TIPO
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
    AND OS_ORCAMENTOS.COD_EMPRESA = 34
    AND OS_ORCAMENTOS.NUMERO_OS = 23493
    GROUP BY OS_ORCAMENTOS.NOMECLIAPROVOU
    ORDER BY OS_ORCAMENTOS.NOMECLIAPROVOU) ORCAMENTOS) AS OS_NOME_CLIENTE_APROVOU,

  NVL(OS.ORC_SERV_BRUTO + OS.ORC_ITEM_BRUTO, 0) AS OS_TOTAL_ORCAMENTO,
  NVL(OS.AUTORIZADO_TODO_ORCAMENTO,'N') OS_ORCAMENTO_AUTORIZADO,
  ' ' AS OS_DATA_ORCAMENTO_APROVADO,
  
  
  
  GARANTIA_DOC.COD_SG_PADRAO AS OS_GARANTIA_SG_PADRAO,
  GARANTIA_DOC.COD_SG_EXEMPLO AS OS_GARANTIA_SG_EXEMPLO,
  
  
  
  NVL(OS.LAVAR_VEICULO,'N') AS OS_LAVAR_VEICULO,
  NVL(OS.CLIENTE_AGUARDOU,'N') AS OS_CLIENTE_AGUARDOU,
  
  NVL(OS_AGENDA.EH_RETORNO,'N') AS OS_EH_RETORNO,
  NVL(OS_AGENDA.EH_RECALL,'N') AS EH_RECALL,
     
   TO_CHAR(OS.DATA_AGENDADA_RECEPCAO, 'DD/MM/YYYY') AS DATA_AGENDAMENTO,
   
    
   TO_CHAR(OS.DATA_PROMETIDA_REVISADA, 'DD/MM/YYYY') AS DATA_PROMETIDA_REVISADA,
   TO_CHAR(OS.HORA_PROMETIDA_REVISADA, 'HH24:MI:SS') AS HORA_PROMETIDA_REVISADA,
   
  
  (SELECT EE.NOME_COMPLETO 
          FROM EMPRESAS_USUARIOS EE 
         WHERE EE.COD_EMPRESA = OS.COD_EMPRESA 
           AND EE.NOME = OS.QUEM_ABRIU) AS OS_NOME_AGENDADOR, 
       
   NVL(OS_AGENDA.EH_FIAT_PROFISSIONAL,'N') AS OS_FIAT_PROFISSIONAL,
   
   NVL(OS.PECA_USADA_FICA_CLIENTE,'N') AS OS_PECA_USADA_FICA_CLIENTE,
   
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'DD/MM/YYYY') AS OS_DATA_ULT_IMRESSAO,
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'HH24:MI:SS') AS OS_HORA_ULT_IMRESSAO,
   
   NVL(OS_AGENDA.VEICULO_PLATAFORMA,'N') AS OS_VEICULO_PLATAFORMA,
   
   TO_CHAR(OS_AGENDA.DATA_AGENDADA , 'HH24:MI') AS OS_HORA_AGENDADA,
   
   (SELECT PB.DESCRICAO
          FROM PRISMA_BOX PB
         WHERE PB.PRISMA = OS_DADOS_VEICULOS.PRISMA) AS OS_DESCRICAO_PRISMA,
   KM_PROXIMA_REVISAO,
   DATA_PROXIMO_SERVICO,
    
  
  NVL(EMPRESAS_USUARIOS.NOME_COMPLETO, ' ')           AS OS_CONSULTOR_COMPLETO,
  EMPRESAS_USUARIOS.CODIGO_OPCIONAL AS OS_CODIGO_OPCIONAL,
  
  
  
  
  OS_TIPOS.DESCRICAO                         AS OS_TIPO_DESCRICAO,
  OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO              AS OS_TIPO_COM_DESCRICAO,
  NVL(OS_TIPOS.GARANTIA,'N')                        AS OS_GARANTIA,
  OS_TIPOS.REVISAO_GRATUITA                    AS OS_REVISAO_GRATUITA,
  NVL(OS_TIPOS.INTERNO,'N')          AS OS_INTERNO,
  OS_TIPOS.COD_CLIENTE               AS OS_CLIENTE_DO_TIPO,
  OS_TIPOS.OUTRO_CONCESSIONARIA                  AS OS_OUTRO_CONCESSIONARIA,
  NVL(OS_TIPOS.TIPO_FABRICA_EMPRESA,OS_TIPOS.TIPO_FABRICA)     AS OS_TIPO_FABRICA,
  
  
  NVL(OS_DADOS_VEICULOS.ANO, ' ')                      AS OS_ANO,
  OS_DADOS_VEICULOS.HORIMETRO                    AS OS_HORIMETRO,
  NVL(OS_DADOS_VEICULOS.PRISMA, ' ')                    AS OS_PRISMA,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2)     AS DIA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2)     AS MES_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4)   AS ANO_VENDA,
  NVL(OS_DADOS_VEICULOS.COMBUSTIVEL,0)                  AS OS_COMBUSTIVEL,
  NVL(OS_DADOS_VEICULOS.COR_EXTERNA, ' ')                  AS OS_COR_EXTERNA,
  NVL(OS_DADOS_VEICULOS.PLACA, ' ')                      AS OS_PLACA,
  NVL(OS_DADOS_VEICULOS.KM, 0)                      AS OS_KM,
  NVL(OS_DADOS_VEICULOS.CHASSI, ' ')                    AS OS_CHASSI,
  NVL(OS_DADOS_VEICULOS.NUMERO_MOTOR, ' ')                  AS OS_NUMERO_MOTOR,
  OS_DADOS_VEICULOS.NUMERO_RENAVAM                AS OS_NUMERO_RENAVAM,
  OS_DADOS_VEICULOS.SERIE                      AS OS_SERIE,
  OS_DADOS_VEICULOS.COD_CONCESSIONARIA              AS OS_COD_CONCESSIONARIA,
  OS_DADOS_VEICULOS.ESTADO_PINTURA                AS OS_ESTADO_PINTURA,
  NVL(OS_DADOS_VEICULOS.JOGO_FERRAMENTAS, ' ')                AS OS_JOGO_FERRAMENTAS,
  NVL(OS_DADOS_VEICULOS.ELASTICOS, ' ')                    AS OS_ELASTICOS,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_D, ' ')                AS OS_TAMPA_LATERAL_D,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_E, ' ')                AS OS_TAMPA_LATERAL_E,
  NVL(OS_DADOS_VEICULOS.FLANELA, ' ')                    AS OS_FLANELA,
  OS_DADOS_VEICULOS.NUMERO_FROTA                  AS OS_NUMERO_FROTA,
  
  
  NVL(CONCESSIONARIAS.NOME, ' ')                          AS CONCESSIONARIA_NOME,
  CONCESSIONARIAS.UF                            AS CONCESSIONARIA_UF,
  CONCESSIONARIAS.CIDADE                        AS CONCESSIONARIA_CIDADE,
  CONCESSIONARIAS.BAIRRO                        AS CONCESSIONARIA_BAIRRO,
  CONCESSIONARIAS.ENDERECO                      AS CONCESSIONARIA_RUA,
  CONCESSIONARIAS.CEP                           AS CONCESSIONARIA_CEP,
  CONCESSIONARIAS.CODIGO_PADRAO                   AS CONCESSIONARIA_CODIGO,
  UF_CONCESSIONARIA.DESCRICAO                   AS CONCESSIONARIA_ESTADO,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_FAB_BATERIA, 'DD/MM/YYYY'),' ')                AS OS_DATA_FAB_BATERIA,
  NVL(OS_DADOS_VEICULOS.COD_FAB_BATERIA, ' ')                               AS OS_COD_FAB_BATERIA,
  NVL((SELECT EMPRESAS_USUARIOS.NOME_COMPLETO
          FROM EMPRESAS_USUARIOS
         WHERE EMPRESAS_USUARIOS.NOME = CLIENTES_FROTA.NOME_VENDEDOR),' ') AS CONCESSIONARIA_VENDEDOR,
  
  
  
  PRODUTOS.DESCRICAO_PRODUTO  AS OS_DESCRICAO_PRODUTO,
  
  
  NVL(PRODUTOS.DESCRICAO_PRODUTO || ' / ' || PRODUTOS_MODELOS.DESCRICAO_MODELO,' ')   AS DESC_PROD_MOD,
  PRODUTOS_MODELOS.DESCRICAO_MODELO                      AS OS_DESCRICAO_MODELO,
  PRODUTOS_MODELOS.MOD_VER_SERIE                        AS OS_MOD_VER_SERIE,
  PRODUTOS_MODELOS.LINHA                            AS OS_LINHA,
  
  
  MARCAS.DESCRICAO_MARCA  AS OS_DESCRICAO_MARCA,
  
  
  CO.TEXTO_AIDF  AS OS_TEXTO_AIDF,
  
  
  NVL(EMPRESAS.NOME,' ')           AS NOME_EMPRESA,
  NVL(EMPRESAS.EMPRESA_NOME_COMPLETO,' ') 		AS EMPRESAS_RAZAO_SOCIAL,

  NVL(EMPRESAS.CGC, ' ')                     AS EMPRESAS_CGC,
  EMPRESAS.FACHADA                  AS EMPRESAS_FACHADA,
  EMPRESAS.ESTADO                   AS UF_EMPRESA,
  NVL(EMPRESAS.CIDADE, ' ')                    AS EMPRESAS_CIDADE,
  EMPRESAS.BAIRRO                    AS EMPRESAS_BAIRRO,
  EMPRESAS.COMPLEMENTO                AS EMPRESAS_COMPLEMENTO,
  NVL(EMPRESAS.RUA, ' ')              AS EMPRESAS_RUA,
  NVL(EMPRESAS.FONE, ' ')                    AS EMPRESAS_FONE,
  NVL(EMPRESAS.FAX, ' ')                    AS EMPRESAS_FAX,
  NVL(EMPRESAS.CEP, ' ')                    AS EMPRESAS_CEP,
  EMPRESAS.INSCRICAO_MUNICIPAL            AS EMPRESAS_INSCRICAO_MUNICIPAL,
  EMPRESAS.INSCRICAO_SUBSTITUICAO            AS EMPRESAS_INSC_SUBSTITUICAO,
  NVL(EMPRESAS.ESTADO,' ') AS EMPRESA_UF,
  NVL(UF_EMPRESA.DESCRICAO, ' ')                  AS EMPRESA_ESTADO,
  NVL(EMPRESAS.INSCRICAO_ESTADUAL, ' ')              AS EMPRESAS_INSCRICAO_ESTADUAL,
  TRUNC(SYSDATE)                     AS EMPRESA_DATA_ATUAL,
  SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'),1, 5)       AS EMPRESA_HORA_ATUAL_STR,
  NVL(CLIENTE_EMPRESA.ENDERECO_ELETRONICO, ' ')         AS EMPRESA_EMAIL,
  NVL(CLIENTE_DIVERSO_EMPRESA.empresa_site, ' ') AS EMPRESA_SITE,
  
  
  
  
 
  CLIENTE_DIVERSO.COD_CLIENTE    AS CLIENTE_COD_CLIENTE,
  NVL(CLIENTE_DIVERSO.NOME, ' ')        AS CLIENTE_NOME,
  NVL(CLIENTE_DIVERSO.RG,' ')          AS CLIENTE_RG,

  
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_CEL || '-' || CLIENTES.TELEFONE_CEL),' ') AS CLIENTE_FONE_CEL,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_RES || '-' || CLIENTES.TELEFONE_RES),' ') AS CLIENTE_FONE_RES,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_COM || '-' || CLIENTES.TELEFONE_COM),' ') AS CLIENTE_FONE_COM,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_FAX|| '-' || CLIENTES.TELEFONE_FAX),' ') AS CLIENTE_FONE_FAX,

  
  
  
  CLIENTES.ENDERECO_ELETRONICO  AS CLIENTE_ENDERECO_ELETRONICO,
  CLIENTES.EMAIL_NFE AS CLIENTE_EMAIL_NFE,
  NVL(NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL),' ') AS CLIENTE_INSC_ESTAD,
  
  NVL(DECODE(CLIENTE_DIVERSO.CPF,' ','CGC: ' || NVL(CLIENTE_DIVERSO.CGC,' '),'CPF: ' || CLIENTE_DIVERSO.CPF), ' ') AS CLIENTE_CGC_CPF,
  
  CLIENTE_DIVERSO.CGC        AS CLIENTE_CGC,
  CLIENTE_DIVERSO.CPF        AS CLIENTE_CPF,
  CLIENTES.COD_CLASSE        AS CLIENTE_COD_CLASSE,
  
  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.ENDERECO  || ', ' || CLIENTE_DIVERSO.COMPLEMENTO || ' ' || CLIENTE_DIVERSO.BAIRRO))
    WHEN '2' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_RES  || ', ' ||CLIENTES.COMPLEMENTO_RES || ' ' || CLIENTES.BAIRRO_RES))
    WHEN '3' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COM  || ', ' ||CLIENTES.COMPLEMENTO_COM || ' ' || CLIENTES.BAIRRO_COM))
    WHEN '4' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COBRANCA  || ', ' ||CLIENTES.COMPLEMENTO_COBRANCA || ' ' || CLIENTES.BAIRRO_COBRANCA))
    WHEN '5' THEN TRIM(',' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.RUA  || ', ' ||ENDERECO_POR_INSCRICAO.COMPLEMENTO || ' ' || ENDERECO_POR_INSCRICAO.BAIRRO))
    ELSE ' '
    END  AS CLIENTE_ENDERECO_COMPLETO,
    

  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO))
    WHEN '2' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_RES  || '-' || CLIENTES.TELEFONE_RES))
    WHEN '3' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '4' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '5' THEN TRIM('-' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO  || '-' || ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO))
    ELSE ' '
    END  AS CLIENTE_TELEFONE_COMPLETO,
    

  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.UF,
              2, CLIENTES.UF_RES,
              3, CLIENTES.UF_COM,
              4, CLIENTES.UF_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.UF,
               ' ')                          AS  CLIENTE_UF,
  DECODE(OS.TIPO_ENDERECO, 1, UF_DIVERSO.DESCRICAO,
              2, UF_RES.DESCRICAO,
              3, UF_COM.DESCRICAO,
              4, UF_COBRANCA.DESCRICAO,
              5, UF_INSCRICAO.DESCRICAO,
               ' ')                          AS  CLIENTE_ESTADO,
  DECODE(OS.TIPO_ENDERECO, 1, CIDADES_DIV.DESCRICAO,
              2, CIDADES_RES.DESCRICAO,
              3, CIDADES_COM.DESCRICAO,
              4, CIDADES_COBRANCA.DESCRICAO,
              5, ENDERECO_POR_INSCRICAO.CIDADE,
               ' ')                          AS  CLIENTE_CIDADE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.BAIRRO,
              2, CLIENTES.BAIRRO_RES,
              3, CLIENTES.BAIRRO_COM,
              4, CLIENTES.BAIRRO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.BAIRRO,
               ' ')                          AS  CLIENTE_BAIRRO,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.CEP,
              2, CLIENTES.CEP_RES,
              3, CLIENTES.CEP_COM,
              4, CLIENTES.CEP_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.CEP,
               ' ')                          AS  CLIENTE_CEP,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.ENDERECO,
              2, CLIENTES.RUA_RES,
              3, CLIENTES.RUA_COM,
              4, CLIENTES.RUA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.RUA,
               ' ')                          AS  CLIENTE_RUA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.COMPLEMENTO,
              2, CLIENTES.COMPLEMENTO_RES,
              3, CLIENTES.COMPLEMENTO_COM,
              4, CLIENTES.COMPLEMENTO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.COMPLEMENTO,
               ' ')                          AS  CLIENTE_COMPLEMENTO,
  DECODE(OS.TIPO_ENDERECO, 1, ' ',
              2, CLIENTES.FACHADA_RES,
              3, CLIENTES.FACHADA_COM,
              4, CLIENTES.FACHADA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.FACHADA,
               ' ')                          AS  CLIENTE_FACHADA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.FONE_CONTATO,
              2, CLIENTES.TELEFONE_RES,
              3, CLIENTES.TELEFONE_COM,
              4, CLIENTES.TELEFONE_COM,
              5, ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_FONE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
              2, CLIENTES.PREFIXO_RES,
              3, CLIENTES.PREFIXO_COM,
              4, CLIENTES.PREFIXO_COM,
              5, ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_PREFIXO,
  
  
  
  NVL(FATURAR_CLIENTE_DIVERSO.NOME, ' ')                   AS FATURAR_NOME,
  
  NVL(TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO)),' ') AS FATURAR_FONE,
  
  NVL(UF_FATURAR.DESCRICAO, ' ')                       AS FATURAR_ESTADO,
  NVL(FATURAR_CIDADE.DESCRICAO, FATURAR_CLIENTE_DIVERSO.CIDADE)   AS FATURAR_CIDADE,

  NVL(TRIM(SUBSTR(CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.ENDERECO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.ENDERECO ELSE '' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.COMPLEMENTO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.COMPLEMENTO ELSE ' ' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.BAIRRO)>0 THEN '  ' || FATURAR_CLIENTE_DIVERSO.BAIRRO ELSE '' END
    , 2)), ' ')  AS FATURAR_ENDERECO,
   
   NVL(GREATEST(
    FATURAR_CLIENTE.PREFIXO_RES || '-' || FATURAR_CLIENTE.TELEFONE_RES,
    FATURAR_CLIENTE.PREFIXO_COM || '-' || FATURAR_CLIENTE.TELEFONE_COM,
    FATURAR_CLIENTE.PREFIXO_FAX || '-' || FATURAR_CLIENTE.TELEFONE_FAX
   ), ' ')   AS FATURAR_TELEFONE_FAX,
   
   
    NVL(FATURAR_CLIENTE.BAIRRO_COM,
    FATURAR_CLIENTE.BAIRRO_RES)   AS FATURAR_BAIRRO,
  
  NVL(FATURAR_CLIENTE.CEP_COM,
    FATURAR_CLIENTE.CEP_RES)   AS FATURAR_CEP,
  
  FATURAR_DADOS_JURIDICOS.CGC AS FATURAR_CGC,
  
  FATURAR_DADOS_JURIDICOS.INSC_ESTADUAL AS FATURAR_IE,
  
   
   
   
   NVL(CLIENTES_FROTA.MEDIA_KM_MENSAL,0) AS MEDIA_KM_MENSAL,
   

  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_TERCEIROS 
    ELSE 
    TOTAIS_SERVICOS.VAL_TERCEIROS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_TERCEIROS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_TERCEIROS,
      
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_LAVAGEM
    ELSE 
    TOTAIS_SERVICOS.VAL_LAVAGEM - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_LAVAGEM/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_LAVAGEM,
  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_REVISAO
    ELSE 
    TOTAIS_SERVICOS.VAL_REVISAO - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_REVISAO/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_REVISAO,
    
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_MECANICA
    ELSE 
    TOTAIS_SERVICOS.VAL_MECANICA - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_MECANICA/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_MECANICA,

  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_GERAIS
    ELSE 
    TOTAIS_SERVICOS.VAL_GERAIS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_GERAIS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_GERAIS,
   
  
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_PECAS
    ELSE 
    TOTAL_PECAS.VALOR_PECAS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_PECAS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_PECAS,
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_ACESSORIOS
    ELSE 
    TOTAL_PECAS.VALOR_ACESSORIOS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_ACESSORIOS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_ACESSORIOS, 
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_OUTROS
    ELSE 
    TOTAL_PECAS.VALOR_OUTROS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_OUTROS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_OUTROS, 

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_LUBRIFICANTE
    ELSE 
    TOTAL_PECAS.VALOR_LUBRIFICANTE - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_LUBRIFICANTE/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_LUBRIFICANTE,  

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_COMBUSTIVEL
    ELSE 
    TOTAL_PECAS.VALOR_COMBUSTIVEL - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_COMBUSTIVEL/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_COMBUSTIVEL
    
 
FROM
  
  EMPRESAS,
  UF UF_EMPRESA,
  CLIENTES CLIENTE_EMPRESA,
  CLIENTE_DIVERSO CLIENTE_DIVERSO_EMPRESA,
  
  
  
  OS, EMPRESAS_USUARIOS, VW_OS_TIPOS OS_TIPOS, OS_DADOS_VEICULOS,
  CONCESSIONARIAS,UF UF_CONCESSIONARIA, PRODUTOS, PRODUTOS_MODELOS, MARCAS,
  CONTROLE_OS CO, PARM_SYS, OS_TIPOS_TERMO, OS_AGENDA, CLIENTES_FROTA, 
  GARANTIA_DOC,
  
  
  CLIENTE_DIVERSO, CLIENTES,  ENDERECO_POR_INSCRICAO,
  CIDADES CIDADES_RES, CIDADES CIDADES_COM, CIDADES CIDADES_COBRANCA, CIDADES CIDADES_DIV,
  UF UF_DIVERSO, UF UF_RES, UF UF_COM, UF UF_COBRANCA, UF UF_INSCRICAO,
  
  
  CLIENTE_DIVERSO FATURAR_CLIENTE_DIVERSO, CLIENTES FATURAR_CLIENTE, CIDADES FATURAR_CIDADE, 
   UF UF_FATURAR, DADOS_JURIDICOS FATURAR_DADOS_JURIDICOS,
   
   
  (SELECT
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'T', TOTAIS.PRECO, 0)) AS VAL_TERCEIROS,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'L', TOTAIS.PRECO, 0)) AS VAL_LAVAGEM,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'R', TOTAIS.PRECO, 0)) AS VAL_REVISAO,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'M', TOTAIS.PRECO, 0)) AS VAL_MECANICA,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'G', TOTAIS.PRECO, 0)) AS VAL_GERAIS
  FROM
  (SELECT
  DECODE(S.TERCEIROS, 'S',       'T',
    DECODE(S.LAVAGEM, 'S',       'L',
    DECODE(SS.TIPO_SETOR, 'R', 'R',
                'M', 'M',
                   'G'))) AS TIPO_SERVICO,
  OSS.PRECO_VENDA + NVL((SELECT SUM(PRECO_VENDA) FROM OS_SERVICOS_ADICIONAIS ADI
               WHERE ADI.COD_EMPRESA = OSS.COD_EMPRESA
                 AND ADI.NUMERO_OS   = OSS.NUMERO_OS
                 AND ADI.ITEM        = OSS.ITEM
                 AND ADI.COD_SERVICO = OSS.COD_SERVICO), 0) AS PRECO
  FROM OS_SERVICOS OSS, SERVICOS S, SERVICOS_SETORES SS
  WHERE 1=1
  AND OSS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OSS.NUMERO_OS = $P{NUMERO_OS}
  AND OSS.COD_SERVICO = S.COD_SERVICO
  AND S.COD_SETOR = SS.COD_SETOR) TOTAIS ) TOTAIS_SERVICOS,
  
  
  
  (SELECT
  SUM(DECODE(CLASSE_PECA, 1, VALOR, 0)) AS VALOR_PECAS,
  SUM(DECODE(CLASSE_PECA, 2, VALOR, 0)) AS VALOR_ACESSORIOS,
  SUM(DECODE(NVL(CLASSE_PECA, 3), 3, VALOR, 0)) AS VALOR_OUTROS,
  SUM(DECODE(CLASSE_PECA, 4, VALOR, 0)) AS VALOR_LUBRIFICANTE,
  SUM(DECODE(CLASSE_PECA, 5, VALOR, 0)) AS VALOR_COMBUSTIVEL
  FROM (
  SELECT DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1), 
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4)) AS CLASSE_PECA,
  SUM(
  OS_REQUISICOES.QUANTIDADE *
  DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
        ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                    DECODE(ITENS.COD_TRIBUTACAO, '1',
                    DECODE(PARM_SYS.REGIME_ICMS, 'S',
                      DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                      DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                    0),
                         0),
                         OS_TIPOS.AUMENTO_PRECO_PECA),
                       0),
                     OS_TIPOS.AUMENTO_PRECO_PECA)) *
           DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                            'G', OS_REQUISICOES.PRECO_GARANTIA,
                            'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                            'P', OS_REQUISICOES.PRECO_FABRICA,
                            DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
          ) / 100,
     DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                        OS_REQUISICOES.PRECO_GARANTIA),
       DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
       DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
         ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
        DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                            OS_REQUISICOES.PRECO_VENDA))/100))))))) AS VALOR
  FROM OS_REQUISICOES, ITENS, ITENS_FORNECEDOR, OS, VW_OS_TIPOS OS_TIPOS, ITENS_CLASSE_CONTABIL ICC , SEGURADORA,
  PARM_SYS, PARM_SYS2, ITENS_GRUPO_INTERNO IGI, OS_TIPOS_EMPRESAS OTE
  WHERE OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
  AND OS.NUMERO_OS = OS_REQUISICOES.NUMERO_OS  
  AND OS.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA 
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS.TIPO = OS_TIPOS.TIPO
  AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND ITENS.COD_GRUPO_INTERNO = IGI.COD_GRUPO_INTERNO(+)
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
  AND OS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OS.NUMERO_OS = $P{NUMERO_OS}
  GROUP BY DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4))
  ) TOTAL) TOTAL_PECAS,
  
  (SELECT CE.KM AS KM_PROXIMA_REVISAO,NVL(C1.DATA_EVENTO, C1.DATA_NOVO_CONTATO) AS DATA_PROXIMO_SERVICO
    FROM OS_DADOS_VEICULOS, CRM_EVENTOS C1, CRM_CICLO_EVENTOS CE
    WHERE C1.COD_CICLO (+) > 0
    AND C1.DATA_EVENTO (+) > SYSDATE - 1
    AND C1.STATUS (+) IN ('P', 'A')
    AND C1.COD_CICLO = CE.COD_CICLO(+)
    AND C1.COD_TIPO_EVENTO = CE.COD_TIPO_EVENTO(+)
    AND NVL(CE.KM (+),0) > 0
    AND OS_DADOS_VEICULOS.CHASSI = C1.VEIC_CHASSI_COMPLETO (+)
    AND OS_DADOS_VEICULOS.NUMERO_OS = $P{NUMERO_OS}
    AND OS_DADOS_VEICULOS.COD_EMPRESA = $P{COD_EMPRESA}
    AND ROWNUM = 1
    ORDER BY DATA_PROXIMO_SERVICO) CRM_EVENTOS


WHERE   1 = 1
    
    
    AND OS.COD_EMPRESA = $P{COD_EMPRESA} 
    AND OS.NUMERO_OS = $P{NUMERO_OS}
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'

    AND OS.NOME = EMPRESAS_USUARIOS.NOME
  
  AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
  AND OS.COD_OS_AGENDA = OS_AGENDA.COD_OS_AGENDA (+)
  
  AND OS.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  
  AND OS.TIPO = OS_TIPOS_TERMO.TIPO (+)
    
  AND OS.COD_EMPRESA =OS_TIPOS_TERMO.COD_EMPRESA (+)
  
  
  
    AND OS.TIPO = OS_TIPOS.TIPO
    AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA (+)
    
    AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS (+)
    AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA (+)
    
    AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA = CONCESSIONARIAS.COD_CONCESSIONARIA (+)
    
    
    AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF (+)
    
    AND OS.COD_CLIENTE = CLIENTES_FROTA.COD_CLIENTE (+)
    AND OS.COD_PRODUTO = CLIENTES_FROTA.COD_PRODUTO (+)
    AND OS.COD_MODELO = CLIENTES_FROTA.COD_MODELO (+)
    AND OS_DADOS_VEICULOS.CHASSI = CLIENTES_FROTA.CHASSI
    
    AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO 
    
    AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
    
    AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
    AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
    
   
    AND OS.COD_EMPRESA = CO.COD_EMPRESA(+)
  
  AND OS.COD_DOCUMENTO = GARANTIA_DOC.COD_DOCUMENTO (+)
 
    
    
    AND OS.COD_EMPRESA = EMPRESAS.COD_EMPRESA
    AND EMPRESAS.ESTADO = UF_EMPRESA.UF (+)
    AND EMPRESAS.COD_CLIENTE = CLIENTE_EMPRESA.COD_CLIENTE (+)
	AND EMPRESAS.COD_CLIENTE = CLIENTE_DIVERSO_EMPRESA.COD_CLIENTE (+)
    
    
    AND OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES (+)
    AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF (+)
    AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES (+)
    AND OS.INSCRICAO_ESTADUAL = ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL (+)
    AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE (+)
    AND CLIENTES.UF_RES = UF_RES.UF (+)
    AND CLIENTES.UF_COM = UF_COM.UF (+)
    AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF (+)
    AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF (+)
    
    
  AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_CLIENTE.COD_CLIENTE (+)
  AND FATURAR_CLIENTE_DIVERSO.COD_CIDADES =FATURAR_CIDADE.COD_CIDADES (+)
  AND FATURAR_CLIENTE_DIVERSO.UF = UF_FATURAR.UF (+)
  AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_DADOS_JURIDICOS.COD_CLIENTE (+)
  AND OS.COD_CLIENTE = FATURAR_CLIENTE_DIVERSO.COD_CLIENTE (+)]]>
	</queryString>
	<field name="OS_COD_EMPRESA" class="java.lang.Double"/>
	<field name="OS_ASSINATURA" class="java.awt.Image"/>
	<field name="OS_STATUS_OS" class="java.lang.Double"/>
	<field name="OS_COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="OS_ABS_OSNUM" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS_FABRICA" class="java.lang.Double"/>
	<field name="OS_COD_CLIENTE" class="java.lang.Double"/>
	<field name="OS_CLIENTE_RAPIDO" class="java.lang.String"/>
	<field name="OS_TIPO_ENDERECO" class="java.lang.String"/>
	<field name="OS_OBSERVACAO" class="java.lang.String"/>
	<field name="OS_EXTENDIDA" class="java.lang.String"/>
	<field name="OS_SEGURADORA" class="java.lang.String"/>
	<field name="OS_DATA_EMISSAO" class="java.lang.String"/>
	<field name="OS_DATA_LIBERADO" class="java.lang.String"/>
	<field name="OS_DIA_EMISSAO" class="java.lang.String"/>
	<field name="OS_MES_EMISSAO" class="java.lang.String"/>
	<field name="OS_ANO_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_ENCERRADA" class="java.lang.String"/>
	<field name="OS_DATA_ENCERRADA" class="java.sql.Timestamp"/>
	<field name="OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_HORA24_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MINUTO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DATA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DIA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MES_PROMETIDA" class="java.lang.String"/>
	<field name="OS_ANO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_BRUTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_DESCONTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="OS_COD_SEGURADORA" class="java.lang.Double"/>
	<field name="OS_TIPO" class="java.lang.String"/>
	<field name="CONSULTOR" class="java.lang.String"/>
	<field name="OS_OS_ENTRADA" class="java.lang.Double"/>
	<field name="OS_VIA" class="java.lang.String"/>
	<field name="OS_TOTAL_IMPRESSAO_FABRICA" class="java.lang.Double"/>
	<field name="OS_TERMO_TEXTO" class="java.lang.String"/>
	<field name="OS_DATA_TAXI" class="java.lang.String"/>
	<field name="OS_HORA_TAXI" class="java.lang.String"/>
	<field name="OS_TODO_TRABALHO_FEITO" class="java.lang.String"/>
	<field name="OS_TODO_LIMPO" class="java.lang.String"/>
	<field name="OS_PRIMEIRA_RODAGEM" class="java.lang.String"/>
	<field name="OS_SEGUNDA_RODAGEM" class="java.lang.String"/>
	<field name="OS_DATA_PRIMEIRA_RODAGEM" class="java.lang.String"/>
	<field name="OS_DATA_SEGUNDA_RODAGEM" class="java.lang.String"/>
	<field name="OS_PRIM_RESPONSAVEL_RODAGEM" class="java.lang.String"/>
	<field name="OS_SEG_RESPONSAVEL_RODAGEM" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PAGAMENTO" class="java.lang.String"/>
	<field name="OS_NUMERO_ORCAMENTO" class="java.lang.String"/>
	<field name="OS_NOME_CLIENTE_APROVOU" class="java.lang.String"/>
	<field name="OS_TOTAL_ORCAMENTO" class="java.lang.Double"/>
	<field name="OS_ORCAMENTO_AUTORIZADO" class="java.lang.String"/>
	<field name="OS_DATA_ORCAMENTO_APROVADO" class="java.lang.String"/>
	<field name="OS_GARANTIA_SG_PADRAO" class="java.lang.String"/>
	<field name="OS_GARANTIA_SG_EXEMPLO" class="java.lang.Double"/>
	<field name="OS_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="OS_CLIENTE_AGUARDOU" class="java.lang.String"/>
	<field name="OS_EH_RETORNO" class="java.lang.String"/>
	<field name="EH_RECALL" class="java.lang.String"/>
	<field name="DATA_AGENDAMENTO" class="java.lang.String"/>
	<field name="DATA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="OS_NOME_AGENDADOR" class="java.lang.String"/>
	<field name="OS_FIAT_PROFISSIONAL" class="java.lang.String"/>
	<field name="OS_PECA_USADA_FICA_CLIENTE" class="java.lang.String"/>
	<field name="OS_DATA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_HORA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_VEICULO_PLATAFORMA" class="java.lang.String"/>
	<field name="OS_HORA_AGENDADA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRISMA" class="java.lang.String"/>
	<field name="KM_PROXIMA_REVISAO" class="java.lang.Double"/>
	<field name="DATA_PROXIMO_SERVICO" class="java.sql.Timestamp"/>
	<field name="OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="OS_CODIGO_OPCIONAL" class="java.lang.String"/>
	<field name="OS_TIPO_DESCRICAO" class="java.lang.String"/>
	<field name="OS_TIPO_COM_DESCRICAO" class="java.lang.String"/>
	<field name="OS_GARANTIA" class="java.lang.String"/>
	<field name="OS_REVISAO_GRATUITA" class="java.lang.String"/>
	<field name="OS_INTERNO" class="java.lang.String"/>
	<field name="OS_CLIENTE_DO_TIPO" class="java.lang.Double"/>
	<field name="OS_OUTRO_CONCESSIONARIA" class="java.lang.String"/>
	<field name="OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="OS_ANO" class="java.lang.String"/>
	<field name="OS_HORIMETRO" class="java.lang.Double"/>
	<field name="OS_PRISMA" class="java.lang.String"/>
	<field name="OS_DATA_VENDA" class="java.lang.String"/>
	<field name="DIA_VENDA" class="java.lang.String"/>
	<field name="MES_VENDA" class="java.lang.String"/>
	<field name="ANO_VENDA" class="java.lang.String"/>
	<field name="OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="OS_PLACA" class="java.lang.String"/>
	<field name="OS_KM" class="java.lang.Double"/>
	<field name="OS_CHASSI" class="java.lang.String"/>
	<field name="OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="OS_NUMERO_RENAVAM" class="java.lang.String"/>
	<field name="OS_SERIE" class="java.lang.String"/>
	<field name="OS_COD_CONCESSIONARIA" class="java.lang.Double"/>
	<field name="OS_ESTADO_PINTURA" class="java.lang.String"/>
	<field name="OS_JOGO_FERRAMENTAS" class="java.lang.String"/>
	<field name="OS_ELASTICOS" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_D" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_E" class="java.lang.String"/>
	<field name="OS_FLANELA" class="java.lang.String"/>
	<field name="OS_NUMERO_FROTA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="CONCESSIONARIA_UF" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CIDADE" class="java.lang.String"/>
	<field name="CONCESSIONARIA_BAIRRO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_RUA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CEP" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CODIGO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_ESTADO" class="java.lang.String"/>
	<field name="OS_DATA_FAB_BATERIA" class="java.lang.String"/>
	<field name="OS_COD_FAB_BATERIA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_VENDEDOR" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRODUTO" class="java.lang.String"/>
	<field name="DESC_PROD_MOD" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="OS_MOD_VER_SERIE" class="java.lang.String"/>
	<field name="OS_LINHA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MARCA" class="java.lang.String"/>
	<field name="OS_TEXTO_AIDF" class="java.lang.String"/>
	<field name="NOME_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_RAZAO_SOCIAL" class="java.lang.String"/>
	<field name="EMPRESAS_CGC" class="java.lang.String"/>
	<field name="EMPRESAS_FACHADA" class="java.lang.String"/>
	<field name="UF_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_CIDADE" class="java.lang.String"/>
	<field name="EMPRESAS_BAIRRO" class="java.lang.String"/>
	<field name="EMPRESAS_COMPLEMENTO" class="java.lang.String"/>
	<field name="EMPRESAS_RUA" class="java.lang.String"/>
	<field name="EMPRESAS_FONE" class="java.lang.String"/>
	<field name="EMPRESAS_FAX" class="java.lang.String"/>
	<field name="EMPRESAS_CEP" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_MUNICIPAL" class="java.lang.String"/>
	<field name="EMPRESAS_INSC_SUBSTITUICAO" class="java.lang.String"/>
	<field name="EMPRESA_UF" class="java.lang.String"/>
	<field name="EMPRESA_ESTADO" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="EMPRESA_DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="EMPRESA_HORA_ATUAL_STR" class="java.lang.String"/>
	<field name="EMPRESA_EMAIL" class="java.lang.String"/>
	<field name="EMPRESA_SITE" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLIENTE" class="java.lang.Double"/>
	<field name="CLIENTE_NOME" class="java.lang.String"/>
	<field name="CLIENTE_RG" class="java.lang.String"/>
	<field name="CLIENTE_FONE_CEL" class="java.lang.String"/>
	<field name="CLIENTE_FONE_RES" class="java.lang.String"/>
	<field name="CLIENTE_FONE_COM" class="java.lang.String"/>
	<field name="CLIENTE_FONE_FAX" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="CLIENTE_EMAIL_NFE" class="java.lang.String"/>
	<field name="CLIENTE_INSC_ESTAD" class="java.lang.String"/>
	<field name="CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="CLIENTE_CGC" class="java.lang.String"/>
	<field name="CLIENTE_CPF" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLASSE" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_UF" class="java.lang.String"/>
	<field name="CLIENTE_ESTADO" class="java.lang.String"/>
	<field name="CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="CLIENTE_CEP" class="java.lang.String"/>
	<field name="CLIENTE_RUA" class="java.lang.String"/>
	<field name="CLIENTE_COMPLEMENTO" class="java.lang.String"/>
	<field name="CLIENTE_FACHADA" class="java.lang.String"/>
	<field name="CLIENTE_FONE" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO" class="java.lang.String"/>
	<field name="FATURAR_NOME" class="java.lang.String"/>
	<field name="FATURAR_FONE" class="java.lang.String"/>
	<field name="FATURAR_ESTADO" class="java.lang.String"/>
	<field name="FATURAR_CIDADE" class="java.lang.String"/>
	<field name="FATURAR_ENDERECO" class="java.lang.String"/>
	<field name="FATURAR_TELEFONE_FAX" class="java.lang.String"/>
	<field name="FATURAR_BAIRRO" class="java.lang.String"/>
	<field name="FATURAR_CEP" class="java.lang.String"/>
	<field name="FATURAR_CGC" class="java.lang.String"/>
	<field name="FATURAR_IE" class="java.lang.String"/>
	<field name="MEDIA_KM_MENSAL" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_TERCEIROS" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_LAVAGEM" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_REVISAO" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_MECANICA" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_GERAIS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_PECAS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_ACESSORIOS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_OUTROS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_LUBRIFICANTE" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_COMBUSTIVEL" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="100">
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="555" height="100" uuid="c9d5421c-b304-4d13-b0f3-3a5b3511f64a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<image scaleImage="FillFrame">
					<reportElement mode="Opaque" x="163" y="-1" width="160" height="70" backcolor="#FFBEBD" uuid="dfa0cb8b-1454-4b74-904a-e504d061033e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600456.png"]]></imageExpression>
				</image>
				<image>
					<reportElement x="31" y="17" width="101" height="20" uuid="960f1ac0-4c4f-43f3-8976-1d36fa26df75">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600455.png"]]></imageExpression>
				</image>
				<image scaleImage="RetainShape">
					<reportElement x="60" y="37" width="40" height="37" uuid="25e59903-2b0e-4990-85c3-28b16836cb7a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600457.png"]]></imageExpression>
				</image>
				<textField>
					<reportElement x="329" y="2" width="223" height="13" uuid="22d2d5d1-3d68-4280-b0da-3ac7a799a1a7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="329" y="19" width="54" height="11" uuid="c77ac7a5-ecbc-4bee-8b02-6d5a97eaf3d9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Razão social:]]></text>
				</staticText>
				<staticText>
					<reportElement x="329" y="30" width="19" height="11" uuid="dd02079c-4ed3-4971-95cc-618d33fc158b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Rua:]]></text>
				</staticText>
				<staticText>
					<reportElement x="329" y="41" width="19" height="11" uuid="52563daa-72d1-4375-9f9a-4895b47f0708">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tel:]]></text>
				</staticText>
				<staticText>
					<reportElement x="329" y="52" width="19" height="11" uuid="5be82632-cdc1-4261-aa7c-cd3ab115ecf0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Site:]]></text>
				</staticText>
				<staticText>
					<reportElement x="329" y="63" width="27" height="11" uuid="4f8a22e6-c9eb-448a-bd67-eb089703b7b9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[E-Mail:]]></text>
				</staticText>
				<staticText>
					<reportElement x="466" y="41" width="20" height="11" uuid="80e7e668-c4a9-499b-9b43-b5bf249ef62d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Fax:]]></text>
				</staticText>
				<staticText>
					<reportElement x="466" y="30" width="20" height="11" uuid="17d36113-e0be-43d6-9044-40ed8a619733">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement x="523" y="30" width="13" height="11" uuid="e997dea4-f734-44c6-885d-2a77caeb2f94">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[UF:]]></text>
				</staticText>
				<line>
					<reportElement mode="Opaque" x="329" y="17" width="226" height="1" forecolor="#DC1417" backcolor="#DC1417" uuid="b0fbfa99-9bd4-46fa-8acc-0ca9789ae0b6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="3.0" lineColor="#E11E4F"/>
					</graphicElement>
				</line>
				<staticText>
					<reportElement mode="Opaque" x="0" y="81" width="555" height="19" forecolor="#FFFFFF" backcolor="#E11E4F" uuid="f8771036-ca5a-4393-90f1-dc52e331557f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<pen lineColor="#E11E4F"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#E11E4F"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#E11E4F"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#E11E4F"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#E11E4F"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[ORDEM DE SERVIÇO]]></text>
				</staticText>
				<textField>
					<reportElement x="383" y="19" width="169" height="11" uuid="f6df2eba-12ea-4574-be52-a4bf18ce5c77">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_RAZAO_SOCIAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="348" y="30" width="118" height="11" uuid="56691a4d-cd8f-4efd-abd7-a9d340fdc5f3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="348" y="41" width="118" height="11" uuid="a01938b4-b3d8-47da-8176-1d4310d265eb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_FONE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="348" y="52" width="204" height="11" uuid="44cf7e8d-f98f-41d2-bc4c-de2c164c56eb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESA_SITE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="356" y="63" width="196" height="11" uuid="533c7f83-52ec-44da-ac43-8bf9ccf47849">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESA_EMAIL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="486" y="30" width="36" height="11" uuid="bbd17fd1-e8c4-4e4f-a62a-cf85501f13ba">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="486" y="41" width="66" height="11" uuid="309d5ec4-1fd3-4675-8715-3755fe1d3266">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_FAX}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="536" y="30" width="16" height="11" uuid="519667ed-32d5-432f-9910-a785b790ca9c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESA_UF}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="297">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="555" height="116" uuid="55a01e3a-efbd-40d1-9dda-9ad674b11f1d"/>
				<box>
					<topPen lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement x="0" y="36" width="278" height="80" uuid="6cca20f8-60fb-47fa-ab6d-aee7efd2aeb5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<staticText>
						<reportElement x="0" y="0" width="47" height="27" uuid="78f0aa9d-4271-4e2a-b1f9-310c5895a989">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[PLACA]]></text>
					</staticText>
					<staticText>
						<reportElement x="47" y="0" width="70" height="27" uuid="e0718947-876b-4c88-895e-20f079b0dc47">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Agendamento
(DATA e HORA)]]></text>
					</staticText>
					<staticText>
						<reportElement x="117" y="0" width="58" height="27" uuid="b01762c9-e3a8-4d24-982f-2d121e4d1cb2">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[CONSULTOR]]></text>
					</staticText>
					<staticText>
						<reportElement x="175" y="0" width="103" height="13" uuid="ce477d22-66b2-4e5d-85c8-c2ff53fcf697">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[PRAZO DE ENTREGA]]></text>
					</staticText>
					<staticText>
						<reportElement x="175" y="13" width="51" height="14" uuid="dca7df32-606f-4053-8fd5-bba4a895df4f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Original]]></text>
					</staticText>
					<textField>
						<reportElement x="0" y="27" width="47" height="27" uuid="969d5495-763c-4166-a71c-bceefcc7cf47">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_PLACA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="47" y="27" width="70" height="27" uuid="1c9ce020-fc73-4d29-b555-0af8d1c1f10f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{DATA_AGENDAMENTO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="117" y="27" width="58" height="27" uuid="15bbe551-f2aa-4715-b706-3466be23c8c2">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="226" y="13" width="52" height="14" uuid="6d30e571-b5d4-4142-9478-df8cba909aee">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Revisado]]></text>
					</staticText>
					<textField>
						<reportElement x="175" y="27" width="51" height="13" uuid="690bb6e7-c784-46c1-8178-3f9793857165">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_DATA_PROMETIDA}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="226" y="27" width="52" height="13" uuid="8950c6ca-48a0-40b9-b6f4-d7a8caf946aa">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[__/__/____]]></text>
					</staticText>
					<staticText>
						<reportElement x="0" y="54" width="278" height="13" uuid="85dacb80-4cf3-44e4-a24b-f6a5e6df7aa1">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[CLIENTE AGUARDA:]]></text>
					</staticText>
					<staticText>
						<reportElement x="0" y="67" width="278" height="13" uuid="697b80a6-5d3f-49b1-ac3d-91f3e21d968c">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[RETORNO:]]></text>
					</staticText>
					<textField>
						<reportElement x="175" y="40" width="51" height="14" uuid="b1dcf021-8e20-4b49-a65d-7606f20a3f2a">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_HORA_PROMETIDA}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="226" y="40" width="52" height="14" uuid="436b271b-92af-48a2-9ba1-580b22ed8e77">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[____:____]]></text>
					</staticText>
					<textField>
						<reportElement x="117" y="55" width="34" height="11" uuid="70afcfac-5d02-49b2-94da-14857e3c2c7d">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_CLIENTE_AGUARDOU}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="165" y="55" width="34" height="11" uuid="3875e511-4c2f-46a4-be96-d89d4789ee37">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_CLIENTE_AGUARDOU}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="116" y="68" width="34" height="11" uuid="a45a1a7d-fec9-454f-a911-3cfee23d91e0">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_EH_RETORNO}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="164" y="68" width="34" height="11" uuid="bcf4e913-cfab-4646-843e-7e34a2b2c66e">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_EH_RETORNO}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="286" y="36" width="269" height="80" uuid="d921ad26-c9c8-4925-8a34-29fa3682fff7">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<staticText>
						<reportElement x="1" y="0" width="136" height="18" uuid="3c8b45fc-02b4-4a60-b5cc-5d6e4a32d8d5">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Confirmação do Agendamento:]]></text>
					</staticText>
					<staticText>
						<reportElement x="137" y="0" width="70" height="18" uuid="8f08c75a-bf76-435d-a7f3-92506ad9c0d4">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Data: __/__/____]]></text>
					</staticText>
					<staticText>
						<reportElement x="207" y="0" width="62" height="18" uuid="42f5c87b-d949-4261-94de-5399b8220d3d">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Hora: ___:___]]></text>
					</staticText>
					<staticText>
						<reportElement x="137" y="18" width="70" height="18" uuid="85973ac3-4eff-46df-a6e3-5d7eadf56285">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Data:]]></text>
					</staticText>
					<staticText>
						<reportElement x="1" y="18" width="136" height="18" uuid="cac2bdeb-cd69-49d1-9dea-2415c7d90e6f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Transporte Alternativo(Taxi):]]></text>
					</staticText>
					<staticText>
						<reportElement x="207" y="18" width="62" height="18" uuid="a18059a1-2369-4f3d-b1bb-df2b1a3a7181">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Hora:]]></text>
					</staticText>
					<staticText>
						<reportElement x="1" y="36" width="155" height="26" uuid="8889a001-5a1c-4c10-8482-4c8bd1e4c282">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Cliente deseja levas as peças substituidas?]]></text>
					</staticText>
					<staticText>
						<reportElement x="156" y="36" width="113" height="26" uuid="e0cdac62-9a1f-4346-b0b1-da4def507f8b">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[]]></text>
					</staticText>
					<staticText>
						<reportElement x="1" y="62" width="155" height="18" uuid="4694fa6c-acd3-4691-9984-d395b3e73eaa">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Cliente deseja ter veiculo lavado?]]></text>
					</staticText>
					<staticText>
						<reportElement x="156" y="62" width="113" height="18" uuid="e499d8aa-8cef-4da1-be4a-c6abca8ae579">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<pen lineColor="#DE083E"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[]]></text>
					</staticText>
					<textField>
						<reportElement x="173" y="44" width="34" height="11" uuid="1ca44eaa-d0e2-4a77-8ca5-0b45d2eeb544">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_PECA_USADA_FICA_CLIENTE}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="221" y="44" width="34" height="11" uuid="19efe21a-4699-43f0-b3f1-6873ef657ccf">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_PECA_USADA_FICA_CLIENTE}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="173" y="66" width="34" height="11" uuid="f4e4cd60-3505-4cc2-85e3-a4c768cf1817">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_LAVAR_VEICULO}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="221" y="66" width="34" height="11" uuid="c6c43636-b2f4-4ae8-9ce1-dec92988c005">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_LAVAR_VEICULO}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="162" y="18" width="44" height="18" uuid="daedea3b-43b0-466e-a503-2c9c48dc7212">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_DATA_TAXI}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="233" y="18" width="35" height="18" uuid="4cf16123-ac0e-4e36-a8ab-ad20dd12dffc">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
						</reportElement>
						<box leftPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_HORA_TAXI}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="165" y="2" width="225" height="30" uuid="21ab78e8-3af7-4a65-928a-8b69b12c2a78"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement x="0" y="0" width="95" height="11" uuid="8e7f6af9-29dd-4caf-a6de-9452037a3d36">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[DATA DE ABERTURA]]></text>
					</staticText>
					<staticText>
						<reportElement x="95" y="0" width="51" height="11" uuid="85e5018c-92c1-4fa6-af76-5ccf37a76ef0">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[HORA]]></text>
					</staticText>
					<staticText>
						<reportElement x="146" y="0" width="79" height="11" uuid="af47f73c-0eaa-4847-878a-38379d67724d">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Nº DA OS]]></text>
					</staticText>
					<textField>
						<reportElement x="0" y="11" width="95" height="19" uuid="23f11bfe-ad95-4d1f-97ca-370db41f6f47">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="95" y="11" width="51" height="19" uuid="e771374d-9b95-41e5-b8c6-cffe49f3d434">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="146" y="11" width="79" height="19" uuid="82f24152-53fb-46f4-b9bd-1aedcecf7540">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_NUMERO_OS}]]></textFieldExpression>
					</textField>
				</frame>
			</frame>
			<frame>
				<reportElement positionType="Float" x="0" y="116" width="555" height="106" uuid="ed70bbb1-ce4d-4b77-8fa2-03219fc31c4d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<staticText>
					<reportElement x="191" y="38" width="18" height="11" uuid="94cbf568-2946-4f8a-9bbc-1c55f458ad4f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[UF:]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="15" width="34" height="11" uuid="1c49d405-f880-4cf5-87f5-74fbe5e99075">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cliente:]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="37" width="34" height="11" uuid="f1a6e7a0-a5f6-4313-9f71-d5483b0006bd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="70" width="34" height="11" uuid="ef0dd92f-977b-49e0-b70b-a951b425f726">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="48" width="34" height="11" uuid="fbed076d-e3b8-468b-bcd1-734ed41bdfa2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CPF:]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="81" width="34" height="11" uuid="dc86025f-2097-421b-be57-73c64d5c1f31">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[RG:]]></text>
				</staticText>
				<staticText>
					<reportElement x="239" y="76" width="52" height="11" uuid="a85076ad-317d-40f2-b9e5-66f8928cd32a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Comercial:]]></text>
				</staticText>
				<staticText>
					<reportElement x="239" y="87" width="52" height="11" uuid="ae56d1a1-b462-4f1c-89f1-e6829fa3cada">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Outros:]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="59" width="34" height="11" uuid="64fe926b-619a-4a63-a16c-2b72acb7d222">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<staticText>
					<reportElement x="239" y="65" width="52" height="11" uuid="687aaf87-610a-4a30-b8be-22c3afb8c7a6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Celular:]]></text>
				</staticText>
				<staticText>
					<reportElement x="239" y="38" width="122" height="11" uuid="e08a20d5-cd26-4731-a077-693745b19e5e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[TELEFONES PARA CONTATO:]]></text>
				</staticText>
				<staticText>
					<reportElement x="239" y="54" width="52" height="11" uuid="1efdb689-cea3-4f62-a3f1-9a6114afd08c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Residencial:]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="26" width="34" height="11" uuid="cb4f056b-90d5-478b-9e0d-bcd5359114f2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Endereço:]]></text>
				</staticText>
				<staticText>
					<reportElement x="373" y="38" width="122" height="11" uuid="7f209ab0-9118-4651-ab03-a11904023ed1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[E-MAILS PARA CONTATO:]]></text>
				</staticText>
				<staticText>
					<reportElement x="373" y="62" width="12" height="11" uuid="0732105f-93b7-48da-b6f9-ea1dfccef8d0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[2:]]></text>
				</staticText>
				<staticText>
					<reportElement x="373" y="51" width="12" height="11" uuid="4ec03239-3863-4870-a50c-c14abc439281">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[1:]]></text>
				</staticText>
				<textField>
					<reportElement x="34" y="70" width="155" height="11" uuid="ba86af00-1f0e-424f-88c5-943bcab8846c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="34" y="37" width="155" height="11" uuid="3faa9f05-a9d5-44d5-b2e3-d9f1a87fc23d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="34" y="26" width="155" height="11" uuid="833add3c-6814-460a-baf3-a338ec9db4e8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="34" y="81" width="155" height="11" uuid="057510b3-2246-4142-9e41-cb1cc9c9cc4d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_RG}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="34" y="59" width="155" height="11" uuid="ee3c2377-d4db-46f7-a5f5-08cc2994b558">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="34" y="15" width="155" height="11" uuid="2c9629c7-b8d7-4378-a3fa-37ecc7b8947f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="34" y="48" width="155" height="11" uuid="bf82c3af-e9da-46b8-ada8-99bce5773f7c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CGC_CPF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="209" y="38" width="26" height="11" uuid="2bdf8b3d-ecde-4f88-896c-6d7020368775">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_UF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="291" y="65" width="70" height="11" uuid="8cdafbb1-f328-445f-85bc-097f4d89d688">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_CEL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="291" y="76" width="70" height="11" uuid="7b250650-e903-487d-b6be-cf28aa0f7c74">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_COM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="291" y="87" width="70" height="11" uuid="8f9bec16-990b-44a4-9a0c-e905788c75e6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_COM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="291" y="54" width="70" height="11" uuid="c75684a4-4f5d-45f6-a34e-e6559220639a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="385" y="51" width="110" height="11" uuid="7b7f51d6-9237-4dc4-ad9d-9ac2e17eb854">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="385" y="62" width="110" height="11" uuid="e6b75296-ce44-4fed-bd54-79a3dec7dfa0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_EMAIL_NFE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="555" height="15" forecolor="#FFFFFF" backcolor="#E11E4F" uuid="4c9fbe3b-4ee4-45c5-8c34-956a440f4dcc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<pen lineWidth="1.0" lineColor="#DE083E"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Informações do Cliente]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="223" width="555" height="44" backcolor="#FFFFFF" uuid="82b0f6c7-5fb8-48ff-bce7-862dc2eec6d4">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<staticText>
					<reportElement x="0" y="17" width="34" height="11" uuid="03775155-ffd9-41d8-8689-09d0ac46c0d5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Modelo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="179" y="17" width="41" height="11" uuid="13a897e9-959f-491f-8a8c-b17464defe0b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Placa:]]></text>
				</staticText>
				<staticText>
					<reportElement x="288" y="17" width="34" height="11" uuid="2de85cd6-1110-441e-af5c-741b3e3ce0ef">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Chassi:]]></text>
				</staticText>
				<staticText>
					<reportElement x="448" y="17" width="21" height="11" uuid="c4b8122a-6b9b-4067-a82d-759f571406fa">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cor:]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="28" width="34" height="11" uuid="ab61f7ba-a599-4391-9d36-b1f574fa142e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Motor:]]></text>
				</staticText>
				<staticText>
					<reportElement x="179" y="28" width="41" height="11" uuid="d7742d81-67be-49dc-a960-3b244eae0c54">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[KM atual:]]></text>
				</staticText>
				<staticText>
					<reportElement x="288" y="28" width="111" height="11" uuid="c031b5d8-7201-4886-b067-39f8281f8fa8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data de Entrega do Veículo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="448" y="28" width="63" height="11" uuid="e4690ce1-dcf2-48c9-ba96-7b0c41ffe002">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Ano Fab./Mod.:]]></text>
				</staticText>
				<textField>
					<reportElement x="34" y="17" width="142" height="11" uuid="c0eb1800-2068-4f18-af37-8c78344d6417">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESC_PROD_MOD}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="34" y="28" width="142" height="11" uuid="458b4122-0b0b-4e76-a8a8-b57a71db42b6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_MOTOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="220" y="17" width="66" height="11" uuid="e5116244-1f6c-4672-9bd8-536fd2d1836c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="220" y="28" width="66" height="11" uuid="3dc0e6a3-1a1e-4d46-8d51-54968fef02e2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_KM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="322" y="17" width="124" height="11" uuid="e27e5156-5fc4-417a-968f-848d24c8869d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_CHASSI}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="399" y="28" width="47" height="11" uuid="113805ae-f71a-4184-ac32-7b86a0e4f6cc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_ENCERRADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="469" y="17" width="83" height="11" uuid="3410254d-4266-413b-8ec0-35db75a37616">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="509" y="28" width="43" height="11" uuid="23c64349-9421-47c8-a65b-d0fa60f89476">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_ANO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="555" height="15" forecolor="#FFFFFF" backcolor="#E11E4F" uuid="891a7296-8b8f-4134-ba4e-79580a6003cc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<pen lineWidth="1.0" lineColor="#DE083E"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Informações do Veículo]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement positionType="Float" stretchType="ContainerHeight" x="0" y="267" width="555" height="30" isRemoveLineWhenBlank="true" uuid="5a068334-0d05-41b0-9003-7925ae133340">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$F{CONCESSIONARIA_NOME}.length() > 1]]></printWhenExpression>
				</reportElement>
				<staticText>
					<reportElement x="0" y="0" width="109" height="11" uuid="a9ca6e07-0a86-4001-9ea5-027b9639b8de">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Concessionária Vendedora:]]></text>
				</staticText>
				<textField>
					<reportElement x="109" y="0" width="280" height="11" uuid="8a46b8d5-97a1-48e5-bff8-af827fa1b6a0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="389" y="0" width="50" height="11" uuid="c31e38ea-810b-4ea7-befd-123bdfd1b221">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data Venda:]]></text>
				</staticText>
				<textField>
					<reportElement x="439" y="0" width="113" height="11" uuid="077939e3-5acb-466a-8530-2c0c533ebb8e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="109" y="11" width="200" height="11" uuid="973b7bfa-81f3-4740-855a-22d238784557">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_RUA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="309" y="11" width="31" height="11" uuid="79a0f33d-07d2-4a0d-9c0d-c6c0b4d8307c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<textField>
					<reportElement x="340" y="11" width="69" height="11" uuid="e16de8dd-65cb-4de9-9787-a5e20f088ded">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="409" y="11" width="143" height="11" uuid="cdb6268e-3234-4a3c-bf53-59f4603a7b2f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_CIDADE} + " - " + $F{CONCESSIONARIA_ESTADO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="22">
			<subreport>
				<reportElement positionType="Float" x="0" y="0" width="555" height="22" isRemoveLineWhenBlank="true" uuid="0b9194c7-8b73-4977-a059-a8ad31f099c0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsNissanSubReclamacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="22">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement positionType="Float" x="0" y="0" width="555" height="22" isRemoveLineWhenBlank="true" uuid="b244ef75-e85e-40c7-a662-a1e939c80d5f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsNissanSubServicos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="31">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<subreport>
				<reportElement positionType="Float" x="0" y="0" width="555" height="31" isRemoveLineWhenBlank="true" uuid="cb59efca-2698-49c0-8279-7479ed7142dd">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsNissanSubPecas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="58">
			<frame>
				<reportElement positionType="Float" mode="Transparent" x="0" y="0" width="555" height="58" backcolor="#FFFFFF" uuid="3aa6e16d-f773-4b21-a766-8f2a504bef03">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<staticText>
					<reportElement mode="Opaque" x="90" y="0" width="465" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="4ccaf6df-c387-464d-b854-18f18f3f707a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Descrição dos serviços adicionais]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="0" width="30" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="ebc00f21-61df-484b-91c9-d807730d20ea">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="11" width="30" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="672f11a2-b41d-466a-8425-c9aeac636adf">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="90" y="11" width="465" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="d24e156a-bd5e-4362-8239-3e6606077126">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="90" y="23" width="465" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="5b44cbcc-64fc-47de-97c4-e2ee94338b37">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="23" width="30" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1c28f275-6d10-4d97-a0f8-a6ff5cb9fb74">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="90" y="35" width="465" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="ae727ef3-eefb-4b03-adfa-898d214dfdb7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="35" width="30" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="59a8db18-9fda-4deb-8760-4648dc78d337">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="90" y="47" width="465" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="13f7fa88-25a9-4801-8411-580bd2b3c831">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="47" width="30" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="d8a86f7b-267f-4148-adf7-c12f0b3b30f0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="60" height="58" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="6c76065e-c165-45e6-a42a-d2505700f910">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box topPadding="3" leftPadding="3" rightPadding="3">
						<pen lineColor="#FFFFFF"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Serviço Adicional]]></text>
				</staticText>
			</frame>
		</band>
		<band height="80">
			<frame>
				<reportElement positionType="Float" mode="Transparent" x="0" y="0" width="555" height="80" backcolor="#FFFFFF" uuid="facaaa1a-b22a-4859-aee0-66d33c1d1440">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<frame>
					<reportElement positionType="Float" mode="Transparent" x="60" y="0" width="495" height="80" backcolor="#FFFFFF" uuid="311d4a96-0504-4f63-a0a5-18ed2c5799c5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="4" y="1" width="319" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="e1673e75-73e7-41f6-810e-a4a4dd694784">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Foi autorizado todo o orçamento (Estimado inicialmente + Serviços adicionais recomentda) ?]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="4" y="15" width="86" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="448192bb-9db3-441c-aa66-a07189071ae1">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Numero do Orçamento:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="4" y="29" width="89" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="c5fd5317-5938-4ca8-8729-7c0c0f5acc71">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Condição de pagamento:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="0" y="43" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="8e237084-837b-4c24-9101-f29656977834">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="7" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Valor Total Revisado.]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="4" y="55" width="55" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="341177fa-b95e-4e3c-91f6-b44898cefdcc">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Aprovado por:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="401" y="43" width="93" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="1923b5e0-59cf-482d-ba12-7c7db75e689c">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_TOTAL_OS}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="418" y="1" width="34" height="11" uuid="ba78b549-3870-48cb-ad5d-50bab170a0d7">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_ORCAMENTO_AUTORIZADO}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="370" y="1" width="34" height="11" uuid="83637272-d72f-4324-9e84-c4b60322481a">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_ORCAMENTO_AUTORIZADO}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="59" y="55" width="279" height="25" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="9d3a3259-6c02-4484-ae3b-0876db14a490">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_NOME_CLIENTE_APROVOU}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="93" y="29" width="230" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="21179184-cd89-47a3-a644-3e3301cc6d56">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_DESCRICAO_PAGAMENTO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="91" y="15" width="232" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="29d14328-9a14-4ead-b08c-b12f2d68aff3">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_NUMERO_ORCAMENTO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="388" y="55" width="103" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="401c6362-5386-4797-af6b-1e0274da0525">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_DATA_ORCAMENTO_APROVADO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="344" y="55" width="44" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="0d4dc14a-fac3-4b7f-9904-5d2d4254f17a">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Data/Hora:]]></text>
					</staticText>
				</frame>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="60" height="80" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="e0e219d6-535e-4371-99ec-a789fbc2f9c8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box topPadding="3" leftPadding="3" rightPadding="3">
						<pen lineColor="#DA080B"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Revisão Geral do Orçamento]]></text>
				</staticText>
			</frame>
		</band>
		<band height="46">
			<frame>
				<reportElement positionType="Float" mode="Transparent" x="0" y="0" width="555" height="46" backcolor="#FFFFFF" uuid="48125b97-b33c-44bd-a811-fceedb1dac77">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Opaque" x="0" y="13" width="60" height="33" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="e6019ab9-c96f-46af-859a-2be198be77c2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" rightPadding="3">
						<pen lineColor="#FFFFFF"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Inspeção Final pelo Consultor de Serviço]]></text>
				</staticText>
				<frame>
					<reportElement x="60" y="0" width="495" height="13" uuid="6e826112-8f91-4ebb-af99-247c5c87f9bd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textField>
						<reportElement x="83" y="1" width="34" height="11" uuid="62a03f73-6804-4091-9b62-2f4524809f0c">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_GARANTIA}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="131" y="1" width="34" height="11" uuid="b41f5014-35fb-4e1b-a14b-2a1f08f21c11">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_GARANTIA}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Opaque" x="190" y="1" width="144" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#E11E4F" uuid="d60b56fe-06dc-4372-a1f7-deba00dfeff6">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Data do término do reparo em garantia]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="0" y="1" width="79" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#E11E4F" uuid="cd7ab6f8-59e9-4911-8e0b-2a5837dcb33a">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Reparo em Garantia]]></text>
					</staticText>
					<textField>
						<reportElement x="343" y="1" width="92" height="11" uuid="9e935d1c-435a-4055-9ce5-903518a6da1f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_GARANTIA}.equals("S")
? $F{OS_DATA_LIBERADO}
:  " "]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement mode="Opaque" x="0" y="0" width="60" height="13" backcolor="#DA080B" uuid="64f46203-e5ab-4600-92dc-0cb120efd607">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</frame>
				<frame>
					<reportElement x="61" y="13" width="494" height="33" uuid="3381b7ab-ad6e-407c-b4a6-dce944ca723b"/>
					<box>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="2" y="16" width="119" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="4cc3940a-cb33-4be4-9ebe-6ee0af6f420a">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[O veículo está limpo dentro/fora?]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="2" y="3" width="103" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="d2a2fabc-2c8c-48ce-acc5-94f68d6fce48">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Foi realizado todo o trabalho solicitado?]]></text>
					</staticText>
					<textField>
						<reportElement x="138" y="3" width="34" height="11" uuid="d74403d0-03aa-451e-bee5-44c2a83cf0d1">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_TODO_TRABALHO_FEITO}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="186" y="3" width="34" height="11" uuid="68d5ff60-eed4-46ab-b963-9e4eda2410c6">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_TODO_TRABALHO_FEITO}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="138" y="16" width="34" height="11" uuid="80bce22d-a4a2-49a7-a36d-70701de27265">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_TODO_LIMPO}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="186" y="16" width="34" height="11" uuid="88a5b91d-a944-4877-ab5f-6c6d1faa50cf">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_TODO_LIMPO}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="272" y="3" width="103" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="95bbfdd1-f6a4-4e5a-b556-edb67e2b39dd">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Data: ____/____/________]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="272" y="16" width="219" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="8028f12f-3b1c-485a-8e57-73be3c1657e9">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Assinatura: _________________________________________]]></text>
					</staticText>
				</frame>
			</frame>
		</band>
		<band height="309">
			<frame>
				<reportElement x="0" y="2" width="555" height="102" uuid="3476684d-**************-9623dd1b126d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="178" height="24" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="ddfba728-e1bc-413b-9cd8-dbe041979c5d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Media Atual de KM : _______________________]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="369" y="0" width="186" height="24" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="2b1e5538-121b-4a35-af92-c5859149b293">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[, estimado para o dia: ____/_____/________]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="178" y="0" width="191" height="24" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="049527db-3bd7-4b3d-89d0-841ff43be13d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Próxima Revisão de: ___________________________]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="29" width="131" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="bf77a113-0566-4457-893e-758d5a1a0005">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box padding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Histórico de Serviço (OSs anteriores):]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="133" y="29" width="417" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="4299311e-7b4b-4969-b0cc-75ce99d0b27e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="40" width="77" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="efddf047-7e80-469a-b4e5-b89f36d022ef">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Campanha de Serviços:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="19" y="52" width="25" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="19aba59e-4d1c-4a87-923d-ba6da663953c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CAMP1]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="49" y="52" width="13" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="7a49e076-8444-4d8a-abc0-0f9e8a1f22db">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="63" y="52" width="46" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="576aff17-389a-417e-bf36-9215e7488f27">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="49" y="66" width="13" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="392df5e6-7b62-4dce-b48e-3ab89b5301e2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="63" y="65" width="56" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="207f0a17-6bbd-48df-b059-74aeb438512d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Não Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="205" y="67" width="56" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="951706a9-a9e0-458b-90b2-9e85d48e5e9d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Não Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="161" y="54" width="25" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="894e1256-7037-410e-bf78-402805edfa45">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CAMP2]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="191" y="54" width="13" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="bf3e6d3b-e40f-4436-9976-5ec8c61e9cb2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="205" y="54" width="46" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="ed9ca86d-a894-480f-ba71-20b278fa6693">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="191" y="68" width="13" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="a288a4eb-098e-4777-8822-8c20f96006da">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="329" y="69" width="13" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="aaccfa79-53f9-4d1e-bf64-485a087b5e0b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="343" y="68" width="56" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="111e0d1e-133c-4155-a82a-60768c13cb4c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Não Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="343" y="55" width="46" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="6f7d0e44-26bb-4c7c-b07c-b66c8a3deb85">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="329" y="55" width="13" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="78906e06-e612-4ab8-bb99-882c0f188f13">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="299" y="55" width="25" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="*************-44e7-b5b6-1edb76d67d71">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CAMP3]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="466" y="70" width="13" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="b15cb8ad-0d33-4450-a780-c57158ef9f86">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="480" y="69" width="56" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="bc0adab7-75e2-4e8a-8d2f-ec52b74565b9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Não Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="480" y="56" width="46" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="ae5ff554-09d4-4a22-acf3-f8f62d54db4a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="466" y="56" width="13" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="6a568b50-675e-4509-9be9-b246fb3689c9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="436" y="56" width="25" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="ac8f8198-e6fe-4069-a919-53ffba2fad82">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CAMP4]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="1" y="84" width="82" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="6f888a54-22b7-4620-b2bb-4909570aab97">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box padding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Informações Adicionais:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="82" y="84" width="466" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="a348ef5f-8786-48e1-90ac-e8c4462f7f3a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="105" width="555" height="153" uuid="6417134f-26d4-4e3c-bc3b-60d3c4cff2a9"/>
				<staticText>
					<reportElement mode="Transparent" x="2" y="5" width="550" height="41" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="8e0c2b6b-1550-4704-8890-185cb9b4ccd5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box padding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Ao Assinar a Ordem de Serviço, o Cliente reconhece ter  o conhecimento prévio das condições gerais de reparação das prescrições previstas no Manual de Garantia do Proprietario ou constantes das Operações de Manutenção em vigor, bem como de que não nos responsabilizamos por objetos deixados dentro do veículo na data de intervenção Nissan.]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="46" width="274" height="50" uuid="173a43d5-2a35-41d6-baf1-0174b2b63bde"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="3" y="3" width="46" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="2a918257-1b4d-402c-84aa-e639a969574f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Recepeção:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="163" y="3" width="103" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="40e274ba-1091-403c-a772-7b9e2b27e8f3">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Data: ____/____/________]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="49" y="24" width="108" height="24" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="c7a22d89-2b08-44c5-9cb3-5029d3ff77ef">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Assinatura do Cliente ou Pessoa por ele Autorizada]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="163" y="15" width="103" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="d74e0896-bfd8-48b2-81e6-26e9ea215887">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Hora: ____:____]]></text>
					</staticText>
					<image hAlign="Center" vAlign="Middle">
						<reportElement x="50" y="2" width="107" height="21" uuid="73b74298-838e-4a66-b9cd-9e1747073da6"/>
						<imageExpression><![CDATA[$F{OS_ASSINATURA}]]></imageExpression>
					</image>
				</frame>
				<frame>
					<reportElement x="281" y="46" width="274" height="50" uuid="b84fab39-d45c-42cb-94d5-5aeed7cab58e"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="3" y="3" width="45" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="624a4a8b-2fb7-4829-9f6e-025d82a66c63">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Entrega:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="163" y="3" width="103" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="3c9be711-4693-4e6d-9933-339811adfd0b">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Data: ____/____/________]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="49" y="24" width="108" height="24" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="59829016-db42-4fb8-b672-75583c96611f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Assinatura do Cliente ou Pessoa por ele Autorizada]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="163" y="15" width="103" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="b9f57f38-88ff-46db-b939-942c60ab5ade">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Hora: ____:____]]></text>
					</staticText>
					<image hAlign="Center" vAlign="Middle">
						<reportElement x="50" y="2" width="107" height="21" uuid="df09515c-35ea-4113-9a6f-d893d39bdb92"/>
						<imageExpression><![CDATA[$F{OS_ASSINATURA}]]></imageExpression>
					</image>
				</frame>
				<frame>
					<reportElement x="0" y="100" width="555" height="50" uuid="aae66d0c-4eaf-413a-aade-06654911e2df">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="2" y="3" width="530" height="36" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="8afe9c4f-762c-44f9-8611-09076f255287">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</reportElement>
						<box leftPadding="3" rightPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Top">
							<font fontName="Calibri" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[DECALQUE DO CHASSI DO VEICULO]]></text>
					</staticText>
				</frame>
			</frame>
			<frame>
				<reportElement positionType="Float" x="0" y="259" width="555" height="50" uuid="475e3445-cd46-4879-abb6-209cedf82cfb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="555" height="15" forecolor="#FFFFFF" backcolor="#E11E4F" uuid="345385d3-6ed3-47f2-aa9b-752b15b239a9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<pen lineWidth="1.0" lineColor="#DE083E"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DE083E"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Explicação do Serviço Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="11" y="20" width="124" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="f57bb080-46fb-4c6f-a969-08a88176aa74">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Foi realizado o 1º Teste de Rodagem]]></text>
				</staticText>
				<textField>
					<reportElement x="187" y="20" width="34" height="11" uuid="6ac93d55-661d-47a3-b783-d8fb1b3be3d7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PRIMEIRA_RODAGEM}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="139" y="20" width="34" height="11" uuid="7aa2ae67-d6a9-4db5-b508-64b2abcc409e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PRIMEIRA_RODAGEM}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="238" y="20" width="22" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="93c55ef4-9321-4f38-821e-************">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField>
					<reportElement x="260" y="20" width="72" height="11" uuid="5aefec54-5167-4f56-954d-6de1dde10c3b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_PRIMEIRA_RODAGEM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="353" y="20" width="48" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="deedfcca-2bf4-4341-a923-e36b05eefbf6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável:]]></text>
				</staticText>
				<textField>
					<reportElement x="401" y="20" width="134" height="11" uuid="c7627623-9ac7-4380-a7ab-b1f5226f3082">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PRIM_RESPONSAVEL_RODAGEM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="11" y="32" width="124" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="738fbef4-9ef0-4da4-9b6e-a57f085cceb7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Foi realizado o 1º Teste de Rodagem]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="238" y="32" width="22" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="d0c75d2e-6ca6-467b-928e-b37941009820">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField>
					<reportElement x="260" y="32" width="72" height="11" uuid="3a82ff55-1e6d-475e-9fe2-c595dd30344a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_SEGUNDA_RODAGEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="187" y="32" width="34" height="11" uuid="567f9d98-4c51-4af7-a8e3-3afd5cf27c98">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_SEGUNDA_RODAGEM}.equals("N")
? "[X] NÃO"
:  "[  ] NÃO"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="401" y="32" width="134" height="11" uuid="d2cd5ba0-101f-4da3-9bec-d485091d9741">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_SEG_RESPONSAVEL_RODAGEM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="353" y="32" width="48" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="487adc1d-95ae-4430-b816-bc1b3b4fa127">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável:]]></text>
				</staticText>
				<textField>
					<reportElement x="139" y="32" width="34" height="11" uuid="6fd25c88-c1e2-4e7e-981c-2ba92a6964e7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_SEGUNDA_RODAGEM}.equals("S")
? "[X] SIM"
:  "[  ] SIM"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="116">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement positionType="Float" mode="Transparent" x="0" y="0" width="555" height="58" backcolor="#FFFFFF" uuid="47c3fbf2-8824-4463-86c7-3800c0ac751a">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<staticText>
					<reportElement mode="Opaque" x="60" y="0" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="81cc9554-c3ab-4dd9-96e0-2953393e42e4">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Diagnóstico encontrado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="11" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="fbfc53cd-f567-43e3-854a-54ccc246d75e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="23" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b6f98e3b-6d54-4714-bc7a-49eec92fdc89">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="35" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="62e9323e-9e0c-4a95-9270-bab71e55e671">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="47" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="94f4542b-7726-461e-861f-90cc4fd9d544">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="60" height="58" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="0cd5b1e4-7691-416d-aab0-10a2e3bfdb96">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box topPadding="3" leftPadding="3" rightPadding="3">
						<pen lineColor="#FFFFFF"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Diagnóstico Encontrado]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Transparent" x="0" y="58" width="555" height="58" backcolor="#FFFFFF" uuid="2bb3d75d-0090-4838-ac7c-4ef07691bdf8">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<staticText>
					<reportElement mode="Opaque" x="60" y="1" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="e0a9f2b3-9f5e-424f-aaa3-454e208f274b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Causas dos Problemas]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="11" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="8a64b86c-47aa-4f1f-bab9-2fd430463de3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="23" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="f5771615-e71a-46c9-81d3-3036e1601de6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="35" width="495" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="137c4222-c2c2-4237-a51d-95dd46a5b406">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="47" width="495" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="af22fac9-7f81-4ef6-8b61-3779188a6d0c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="60" height="57" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="1400cf6f-739b-4360-9625-afcb2027591c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box topPadding="3" leftPadding="3" rightPadding="3">
						<pen lineColor="#FFFFFF"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Causas dos Problemas]]></text>
				</staticText>
			</frame>
		</band>
		<band height="11">
			<frame>
				<reportElement x="60" y="0" width="495" height="11" uuid="dcbc3aeb-4367-44ca-8b53-507518d27a18"/>
				<box>
					<rightPen lineWidth="1.0"/>
				</box>
				<subreport>
					<reportElement positionType="Float" mode="Opaque" x="-1" y="0" width="495" height="11" isRemoveLineWhenBlank="true" backcolor="#FFFFFF" uuid="0cedcc3a-cd4e-4975-8511-4d954912531d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsNissanSubDiagnostico.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<staticText>
				<reportElement stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="60" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="68a810da-bea8-4f74-8b3a-993a8e91947d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" rightPadding="3">
					<pen lineColor="#FFFFFF"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Diagnóstico]]></text>
			</staticText>
		</band>
		<band height="20">
			<frame>
				<reportElement x="60" y="0" width="495" height="20" uuid="df8c46eb-ebf9-4280-b932-b8e929f7cd3a"/>
				<box>
					<rightPen lineWidth="1.0"/>
				</box>
				<subreport>
					<reportElement positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="495" height="20" isRemoveLineWhenBlank="true" uuid="65a2627c-7ab1-4b08-a471-f38f886ac2c8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsNissanSubCausasProblemas.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<staticText>
				<reportElement stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="60" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="1263657f-0715-4faf-a5d4-63e5eefb0953">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" rightPadding="3">
					<pen lineColor="#FFFFFF"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Causa dos Problemas]]></text>
			</staticText>
		</band>
		<band height="33">
			<frame>
				<reportElement x="60" y="0" width="495" height="33" uuid="0f25610c-dff5-457b-9788-853e765866bf"/>
				<box>
					<topPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<subreport>
					<reportElement positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="495" height="33" isRemoveLineWhenBlank="true" uuid="7fa8c7a7-3a70-401b-ae13-3a8830867179">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsNissanSubPecasCausadoraProblemas.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<staticText>
				<reportElement stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="60" height="33" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="c772204b-35c1-45b0-a7a5-5f23539e62f8">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" rightPadding="3">
					<pen lineColor="#FFFFFF"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Peça(s) Causadora do problema]]></text>
			</staticText>
		</band>
		<band height="22">
			<subreport>
				<reportElement positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="555" height="22" isRemoveLineWhenBlank="true" uuid="20e052d5-c742-47e3-b856-98433960a503">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsNissanSubPecasPg2.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="22">
			<subreport>
				<reportElement positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="554" height="22" isRemoveLineWhenBlank="true" uuid="61761eae-20e0-41df-a4ea-f14c77c8c619">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsNissanSubServicosPg2.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="60">
			<frame>
				<reportElement positionType="Float" mode="Transparent" x="0" y="0" width="555" height="42" backcolor="#FFFFFF" uuid="a79d0592-b101-4fda-9fd8-89d65bbad163">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<staticText>
					<reportElement mode="Opaque" x="60" y="28" width="495" height="14" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b9cc64ac-19eb-4572-bc2b-3c623bd70082">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="14" width="495" height="14" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="af9495bc-a355-4f85-b7d4-aee7f90daf54">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="60" y="0" width="495" height="14" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="0d9ebddf-fc17-4658-8e04-b373bd557f8f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="60" height="42" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="a1f58623-5e50-4770-96cb-c0939d24feaa">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" rightPadding="0">
						<pen lineColor="#FFFFFF"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Recomendação de Eventuais Serviços Adicionais]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="46" width="555" height="14" uuid="ddbd2672-7179-4873-98bc-531163dd5dd0">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="1" width="408" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="3ee97b78-d6db-4474-935b-ff0f84acce67">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Técnico Responsável: _________________________________________________________________________________]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="419" y="1" width="134" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="54573775-b2b2-45fb-97c0-cf551806a9b2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data: ___________________________]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
	<lastPageFooter>
		<band height="60">
			<frame>
				<reportElement x="0" y="0" width="555" height="60" uuid="eb42875a-1591-469e-a07e-ccc9cea6c0c3">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="185" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="8d025d5d-a971-4dd3-affd-f792030df1ed">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Medida Atual de KM]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="185" y="0" width="185" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="fc700e64-0110-44db-9e38-1d19208409a9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Medida Atual de KM]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="370" y="0" width="185" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="e039353e-8c78-49c3-955f-aad61ff4abcd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Medida Atual de KM]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="3" y="20" width="544" height="36" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="c774e26e-6edd-4444-bfba-c7a9a6c275b9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Top">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Ao Assinar a Ordem de Serviço, o Cliente reconhece ter o conhecimento prévio das condições gerais de reparação das prescrições previstas no Manual de Garantia do Proprietário ou constantes das Operações de Manutenção em vigor, bem como de que não nos responsabilizamos por objetos deixados dentro do veículo na data de intervenção Nissan]]></text>
				</staticText>
			</frame>
		</band>
	</lastPageFooter>
</jasperReport>
