<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiChecklistSeisPassosSubGrupo" pageWidth="138" pageHeight="842" columnWidth="138" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="teste_freedom.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<style name="fundoAzul" style="default_null" mode="Opaque" forecolor="#FFFFFF" backcolor="#285A95"/>
	<style name="fontAzul" forecolor="#285A95"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="ORDEM_GRUPO" class="java.lang.Integer">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[1]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Desktop\\Relatorios Hyundai 6 partes\\iamgens_testes\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="IMAGEM_GRUPO" class="java.lang.String">
		<defaultValueExpression><![CDATA["i1.png"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[with DADOS as (
select info.cod_empresa as COD_EMPRESA,
       info.cod_produto as COD_PRODUTO,
       info.cod_modelo as COD_MODELO,
       info.tipo_os as TIPO_OS,
       info.cod_segmento as COD_SEGMENTO
  from (select os.cod_empresa,dv.Cod_Produto, dv.cod_modelo, os.tipo as tipo_os, produtos.cod_segmento
          from os, os_dados_veiculos dv, produtos
         where os.numero_os = dv.numero_os
           and os.cod_empresa = dv.cod_empresa
           and os.cod_empresa = $P{COD_EMPRESA}
           and os.numero_os = $P{NUMERO_OS}
           and produtos.cod_produto = dv.cod_produto
           and 'OS' = 'OS') info
),
TODOS_ITENS as ( 
SELECT A.ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */
     'CheckList' AS GRUPO,
       A.DESCRICAO AS DESCRICAO_ITEM,       
       A.ATIVO,
       A.COD_ITEM,
       A.OBRIGATORIO,
       A.ORDEM,
       A.PODE_TER_FOTO,  
       B.DESCRICAO DESCRICAO_GRUPO,
       B.TIPO,       
       B.APLICACAO,
       C.OBSERVACAO,
       D.DESCRICAO AS DESCRICAO_OPCAO,
       A.RESPOSTA_EH_OBSERVACAO,
       DECODE(NVL((SELECT PO.SEMAFORO
                            FROM MOB_PERTENCE_OPCAO PO
                           WHERE PO.COD_ITEM = A.COD_ITEM
                             AND PO.ID_OPCAO = C.ID_OPCAO
                             AND ROWNUM = 1),
                          ''),
                      'G',
                      'GREEN',
                      'R',
                      'RED',
                      'Y',
                      'YELLOW',
                      '') AS COR_OPCAO_SELECIONADA, /* se for GREEM então marco como sim no relatorio */
       B.ORDEM AS ORDEM_GRUPO  
FROM MOB_PERTENCE_ITEM A, MOB_PERTENCE_GRUPO B, MOB_OS_PERTENCE C, MOB_OPCAO D
WHERE A.ID_GRUPO = B.ID_GRUPO
 AND B.TIPO = 'C'
 AND A.ATIVO = 'S'
 AND B.ATIVO = 'S'
 AND A.COD_ITEM = C.COD_ITEM (+)
 AND B.APLICACAO = 'O'
 AND C.COD_EMPRESA(+) = $P{COD_EMPRESA}
 AND C.NUMERO_OS(+) = $P{NUMERO_OS}
 AND C.ID_OPCAO = D.ID_OPCAO  (+)
 AND B.ORDEM = $P{ORDEM_GRUPO}
 ),
FILTRO_CRUZA_EPRESA_SEGMENTO AS ( 
SELECT * /* CAMADA 2 - FILTRO TODOS OS ITENS QUE SEJA DA RESPECTIVA EMPRESA OU NÃO POSSUA EMPRESA */
FROM TODOS_ITENS A
WHERE EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo
                     AND mc.cod_empresa = $P{COD_EMPRESA}
                     AND mc.cod_segmento = (SELECT COD_SEGMENTO FROM DADOS)) 
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo)                               
),
FILTRO_CRUZA_TIPO_OS AS ( 
SELECT * /* CAMADA 3 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO TIPO DE OS OU NÃO POSSUA TIPO DE OS VINCULADO */
FROM FILTRO_CRUZA_EPRESA_SEGMENTO A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_tp_os cto
                  WHERE  cto.id_grupo = a.id_grupo
                         AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                         AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS)
                         AND cto.tipo = (SELECT TIPO_OS FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_tp_os cto
              WHERE  cto.id_grupo = A.id_grupo
                     AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                     AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS))
),
FILTRO_CRUZA_MODELO AS ( 
SELECT * /* CAMADA 4 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO MODELO OU NÃO POSSUA MODELO VINCULADO */
FROM FILTRO_CRUZA_TIPO_OS A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_modelo cm
                  WHERE  cm.id_grupo = a.id_grupo
                         AND cm.cod_produto = (SELECT COD_PRODUTO FROM DADOS)
                         AND cm.cod_modelo = (SELECT COD_MODELO FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_modelo cm
              WHERE   cm.id_grupo = a.id_grupo)
)
select ROW_NUMBER() OVER(ORDER BY FILTRO_CRUZA_MODELO.ORDEM_GRUPO, FILTRO_CRUZA_MODELO.ID_GRUPO, FILTRO_CRUZA_MODELO.ORDEM) AS LINHA,
       ID_GRUPO,
       GRUPO,
       CASE WHEN SUBSTR(DESCRICAO_ITEM, 2,5) like '%-%' then TRIM(SUBSTR(DESCRICAO_ITEM, 1, INSTR(DESCRICAO_ITEM, '-') - 1)) else '' end AS ACAO_ITEM,
       CASE WHEN SUBSTR(DESCRICAO_ITEM, 2,5) like '%-%' then TRIM(SUBSTR(DESCRICAO_ITEM, INSTR(DESCRICAO_ITEM, '-') + 1)) else DESCRICAO_ITEM end AS DESCRICAO_ITEM,
       ATIVO,
       FILTRO_CRUZA_MODELO.COD_ITEM,
       OBRIGATORIO,
       FILTRO_CRUZA_MODELO.ORDEM,
       PODE_TER_FOTO,
       DESCRICAO_GRUPO,
       TIPO,
       APLICACAO,
       OBSERVACAO,
       DESCRICAO_OPCAO,
       RESPOSTA_EH_OBSERVACAO,
       COR_OPCAO_SELECIONADA,
       F.FOTO_ICONE,
       CASE
         WHEN F.FOTO_ICONE IS NULL THEN
          'N'
         ELSE
          'S'
       END TEM_IMAGEM
  from FILTRO_CRUZA_MODELO, MOB_OS_PERTENCE_FOTO F
 WHERE $P{COD_EMPRESA} = F.COD_EMPRESA(+)
   AND $P{NUMERO_OS} = F.NUMERO_OS(+)
   AND FILTRO_CRUZA_MODELO.COD_ITEM = F.COD_ITEM(+)
--AND ROWNUM < 14
 ORDER BY ORDEM_GRUPO, ID_GRUPO, ORDEM]]>
	</queryString>
	<field name="LINHA" class="java.lang.Double"/>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="GRUPO" class="java.lang.String"/>
	<field name="ACAO_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="ATIVO" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="OBRIGATORIO" class="java.lang.String"/>
	<field name="ORDEM" class="java.lang.Double"/>
	<field name="PODE_TER_FOTO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="APLICACAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="COR_OPCAO_SELECIONADA" class="java.lang.String"/>
	<field name="FOTO_ICONE" class="java.awt.Image"/>
	<field name="TEM_IMAGEM" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="15" splitType="Prevent">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement key="" style="fontAzul" isPrintRepeatedValues="false" x="0" y="0" width="138" height="15" uuid="209444c4-2584-47b0-9d73-b61e06668e60">
					<property name="ShowOutOfBoundContent" value="false"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<pen lineColor="#285A95"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="###0.###">
					<reportElement style="fontAzul" mode="Transparent" x="0" y="0" width="12" height="15" uuid="1ee2c5b3-b344-44b3-acff-80f4b861d040">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<pen lineColor="#2A4C7E"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LINHA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="fontAzul" mode="Transparent" x="27" y="0" width="83" height="15" uuid="26d08a45-b633-4abb-835a-12ca9faf6b2d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="fontAzul" mode="Transparent" x="110" y="0" width="14" height="15" uuid="4d19eb3c-c044-4279-957d-c3703557ae3a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<pen lineColor="#2A4C7E"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COR_OPCAO_SELECIONADA}.equals("GREEN")? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="fontAzul" mode="Transparent" x="124" y="0" width="14" height="15" uuid="00aae54d-b43b-4bf7-9726-04f7ed0d51c0">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<pen lineColor="#2A4C7E"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COR_OPCAO_SELECIONADA}.equals("RED") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField pattern="###0.###">
					<reportElement style="fontAzul" mode="Transparent" x="12" y="0" width="15" height="15" uuid="b4d0bdf7-cab0-4608-897b-7522b33860c2">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<pen lineColor="#2A4C7E"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ACAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="fontAzul" mode="Transparent" x="111" y="0" width="26" height="15" uuid="dd2dbf01-b1cc-40d5-a0d2-8d996c8d5ec8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<pen lineColor="#2A4C7E"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")?$F{OBSERVACAO}:""]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
