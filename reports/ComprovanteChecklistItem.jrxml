<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklistItem" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="429"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="571"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<style name="resposta_eh_observacao">
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")]]></conditionExpression>
			<style mode="Opaque" backcolor="#FFFCDC">
				<box leftPadding="3">
					<pen lineWidth="1.0" lineColor="#DBDBDA"/>
				</box>
			</style>
		</conditionalStyle>
	</style>
	<subDataset name="FOTOS_ITENS" uuid="4d77c5a1-309f-4c1a-8704-ea51a24dc7cf">
		<queryString>
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["H:\\NBS\\33790\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="APLICACAO" class="java.lang.String"/>
	<parameter name="COD_SERVICO" class="java.lang.String"/>
	<parameter name="MODALIDADE" class="java.lang.String">
		<defaultValueExpression><![CDATA["OS"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_AGENDA" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="OBRIGATORIO" class="java.lang.String">
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<parameter name="TIPO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C"]]></defaultValueExpression>
	</parameter>
	<parameter name="IMP_ITEM_AGRUPADO_FOTO" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[SELECT 
T.ID_GRUPO,
'Checklist' AS GRUPO,
T.ITEM_DESCRICAO AS DESCRICAO_ITEM,
T.ATIVO, 
T.COD_ITEM,
T.ITEM_OBRIGATORIO AS OBRIGATORIO, 
T.ITEM_ORDEM AS ITEM_ORDEM,
T.GRUPO_ORDEM AS GRUPO_ORDEM,
T.ITEM_PODE_TER_FOTO,
T.GRUPO_DESCRICAO AS DESCRICAO_GRUPO,
T.GRUPO_TIPO AS TIPO,
T.GRUPO_APLICACAO AS APLICACAO,
T.SELECIONADO_OBSERVACAO AS OBSERVACAO,
T.SELECIONADO_OPCAO_DESCRICAO AS DESCRICAO_OPCAO,
T.ITEM_RESPOSTA_EH_OBSERVACAO AS RESPOSTA_EH_OBSERVACAO,
T.ITEM_ID_OPCAO_DEFAULT AS ID_OPCAO_DEFAULT
FROM TABLE(PKG_CRM_SERVICE_CHECKLIST.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS}, $P{COD_AGENDA}, $P{COD_SERVICO})) T
WHERE T.GRUPO_TIPO = $P{TIPO}
    AND T.GRUPO_APLICACAO = $P{APLICACAO}
    AND T.ATIVO = 'S'
    AND (T.SELECIONADO_ID_OPCAO IS NOT NULL OR T.SELECIONADO_OBSERVACAO IS NOT NULL)
ORDER BY GRUPO_ORDEM, ID_GRUPO, ITEM_ORDEM]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="GRUPO" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="ATIVO" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="OBRIGATORIO" class="java.lang.String"/>
	<field name="ITEM_ORDEM" class="java.lang.Double"/>
	<field name="GRUPO_ORDEM" class="java.lang.Double"/>
	<field name="ITEM_PODE_TER_FOTO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="APLICACAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="ID_OPCAO_DEFAULT" class="java.lang.Double"/>
	<group name="Group1">
		<groupExpression><![CDATA[$F{ID_GRUPO}]]></groupExpression>
		<groupHeader>
			<band height="22">
				<rectangle>
					<reportElement mode="Opaque" x="0" y="1" width="554" height="20" backcolor="#0F0F0F" uuid="68cfa900-3224-4543-af3a-d10ab648680a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement x="0" y="2" width="554" height="18" forecolor="#FFFFFF" uuid="34d5790d-e546-4fc1-a0d8-4f202a3f9735">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_GRUPO}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<detail>
		<band height="36">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Opaque" x="38" y="19" width="510" height="17" isRemoveLineWhenBlank="true" uuid="dbe2f20e-0ff7-4e10-a968-58631e8c016e">
					<printWhenExpression><![CDATA[! $F{OBSERVACAO}.equals(" ")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Justified"/>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="14" y="19" width="24" height="17" uuid="4500be8c-8bc0-4975-b88e-60be85a5254c">
					<printWhenExpression><![CDATA[!$F{OBSERVACAO}.equals(" ")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Obs: ]]></text>
			</staticText>
			<frame>
				<reportElement key="" mode="Opaque" x="10" y="2" width="539" height="17" backcolor="#E3E3E3" uuid="ab820b3b-8766-4d37-9d97-9307405edaad"/>
				<textField isStretchWithOverflow="true">
					<reportElement key="" mode="Transparent" x="4" y="0" width="406" height="17" backcolor="#E3E3E3" uuid="3fcff158-846a-4bc1-8bde-a0a4091f081e"/>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement x="414" y="0" width="124" height="17" uuid="bc545816-371e-4467-a38c-2bec21fbaa6c"/>
					<textElement textAlignment="Right"/>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="9">
			<printWhenExpression><![CDATA[$P{IMP_ITEM_AGRUPADO_FOTO}.equals("S")]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="10" y="0" width="539" height="9" isRemoveLineWhenBlank="true" uuid="d77d25b8-5537-4715-b2dd-0e7376137db4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_ITEM">
					<subreportParameterExpression><![CDATA[$F{COD_ITEM}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"ComprovanteChecklistItemFotos.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="7">
			<line>
				<reportElement positionType="Float" x="0" y="3" width="555" height="1" uuid="70ec379b-9c38-4d56-916b-0cf887a2d34c"/>
			</line>
		</band>
	</pageFooter>
</jasperReport>
