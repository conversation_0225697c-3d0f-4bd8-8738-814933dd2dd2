<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaVinteSeteItensRecebimentoSubFormularioInspecao" columnCount="2" pageWidth="575" pageHeight="284" columnWidth="277" leftMargin="10" rightMargin="10" topMargin="0" bottomMargin="0" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select 
  a.id_grupo,
  a.grupo_descricao,
  a.cod_item,
  a.item_descricao,
  UPPER(a.SELECIONADO_OPCAO_DESCRICAO) as descricao_opcao,
  REPLACE(UPPER(a.ITEM_DESCRICAO),'PRESSÃO ','') AS DESCRICAO_ITEM_SUBS_PRESSAO,  /* limitação no jasper não permite, então remover aqui */
  a.ITEM_RESPOSTA_EH_OBSERVACAO AS RESPOSTA_EH_OBSERVACAO,
  a.SELECIONADO_OBSERVACAO
from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
where a.id_checklist = 20000
      AND a.ID_GRUPO IN (20005, 20006, 20007, 20008) -- HONDA CHECKLIST(Inspeção interna/iluminação/externa, Verificação dos Níveis , Sob do veículo)]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="GRUPO_DESCRICAO" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM_SUBS_PRESSAO" class="java.lang.String"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="SELECIONADO_OBSERVACAO" class="java.lang.String"/>
	<variable name="NOME_CAMPO_TEXTO" class="java.lang.String">
		<variableExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S") && $V{NOME_CAMPO_TEXTO}.equals("")  ?
$F{SELECIONADO_OBSERVACAO}== null? $F{DESCRICAO_ITEM_SUBS_PRESSAO} + ": _____": $F{DESCRICAO_ITEM_SUBS_PRESSAO} + ": " + $F{SELECIONADO_OBSERVACAO}
 : $F{RESPOSTA_EH_OBSERVACAO}.equals("S") ?
 
 $F{SELECIONADO_OBSERVACAO}== null ?  $V{NOME_CAMPO_TEXTO} + "   " +$F{DESCRICAO_ITEM_SUBS_PRESSAO} + ": _____" :  $V{NOME_CAMPO_TEXTO}  + "   " + $F{DESCRICAO_ITEM_SUBS_PRESSAO} + ": " + $F{SELECIONADO_OBSERVACAO}
 
: $V{NOME_CAMPO_TEXTO}]]></variableExpression>
		<initialValueExpression><![CDATA[""]]></initialValueExpression>
	</variable>
	<variable name="OBSERVACOES" class="java.lang.String">
		<variableExpression><![CDATA[$F{SELECIONADO_OBSERVACAO}!=null && $F{RESPOSTA_EH_OBSERVACAO}.equals("N")
	? $V{OBSERVACOES}.equals("")
		? "*" + $F{ITEM_DESCRICAO} + ": " + $F{SELECIONADO_OBSERVACAO}
		: $V{OBSERVACOES} +"; " + "*" +   $F{ITEM_DESCRICAO} + ": " + $F{SELECIONADO_OBSERVACAO}
	:$V{OBSERVACOES}]]></variableExpression>
		<initialValueExpression><![CDATA[""]]></initialValueExpression>
	</variable>
	<group name="grupo">
		<groupExpression><![CDATA[$F{ID_GRUPO}]]></groupExpression>
		<groupHeader>
			<band height="13">
				<textField>
					<reportElement mode="Opaque" x="0" y="2" width="277" height="11" backcolor="#D3D5D4" uuid="47a1a47e-c224-42af-85b0-2d68d9c24a63">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GRUPO_DESCRICAO}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<title>
		<band height="47">
			<staticText>
				<reportElement x="0" y="0" width="555" height="22" uuid="c87bf729-858a-4056-bb39-0468888a16b5">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="16" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[FORMULÁRIO DE INSPEÇÃO]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="22" width="555" height="11" uuid="8a0689e7-9d95-4d3a-bd36-8705bcbf4de1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Além dos itens previstos no manual do proprietário, serão inspecionados os itens abaixo:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="33" width="277" height="14" forecolor="#FFFFFF" backcolor="#000000" uuid="250a8086-4b65-4804-a384-34c74c438a61">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[INSPEÇÃO de 27 ITENS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="179" y="33" width="30" height="14" backcolor="#51B674" uuid="e29edf2a-eafe-4da6-bde1-9b46e9f53be1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[OK]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="209" y="33" width="30" height="14" backcolor="#CDDA61" uuid="b0ddca7a-eff7-446c-bfed-8b13e8e9383d">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Atenção]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="238" y="33" width="30" height="14" forecolor="#FFFFFF" backcolor="#D43E5C" uuid="3cff9b37-9e6a-4357-9a95-5e02daca82db">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Reparo Imediato]]></text>
			</staticText>
			<staticText>
				<reportElement x="209" y="38" width="30" height="9" uuid="81ab57e1-1571-4784-82af-50c85386875d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="3" isBold="true"/>
				</textElement>
				<text><![CDATA[(reparo antes próx. revisão)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="277" y="33" width="278" height="14" forecolor="#FFFFFF" backcolor="#000000" uuid="cb632a53-603f-4ebe-8c31-************">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="487" y="38" width="30" height="9" uuid="63e90ebf-e30d-4f42-9cda-3a90880c11e2">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="3" isBold="true"/>
				</textElement>
				<text><![CDATA[(reparo antes próx. revisão)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="457" y="33" width="30" height="14" backcolor="#51B674" uuid="f21371b3-0fc0-455b-a023-a936f027a7e1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[OK]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="487" y="33" width="30" height="14" backcolor="#CDDA61" uuid="b6044193-62a5-448c-8418-7864487f15c0">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Atenção]]></text>
			</staticText>
			<staticText>
				<reportElement x="487" y="38" width="30" height="9" uuid="bf2737e8-28c8-4575-9889-2ab28d5db936">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="3" isBold="true"/>
				</textElement>
				<text><![CDATA[(reparo antes próx. revisão)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="517" y="33" width="30" height="14" forecolor="#FFFFFF" backcolor="#D43E5C" uuid="558b5fb2-f174-48e9-86e1-0d0e1626ab02">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Reparo Imediato]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{RESPOSTA_EH_OBSERVACAO}.equals("N"))]]></printWhenExpression>
			<textField>
				<reportElement x="2" y="1" width="177" height="9" uuid="6245d408-899a-48a8-b27a-0105394cfd60">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="186" y="2" width="12" height="7" backcolor="#51B674" uuid="fe496273-5c34-4038-8730-386279aa16fc">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="246" y="2" width="12" height="7" forecolor="#FFFFFF" backcolor="#D43E5C" uuid="4c1ddc92-be86-4091-af84-034bfbf0dbf4">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("REPARO IMEDIATO") ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="216" y="2" width="12" height="7" backcolor="#CDDA61" uuid="3874d3df-c6fa-4325-b34b-547621eab385">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("ATENÇÃO") ? "X" : ""]]></textFieldExpression>
			</textField>
		</band>
		<band height="10">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{COD_ITEM} == 20058)]]></printWhenExpression>
			<textField evaluationTime="Report">
				<reportElement mode="Transparent" x="43" y="1" width="226" height="9" backcolor="#F7D3D2" uuid="6e1de3b1-1f04-4289-99c4-7335e5e37988">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{NOME_CAMPO_TEXTO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="7" y="1" width="30" height="9" backcolor="#F7D3D2" uuid="840618d8-117a-42c6-9312-d3a14fe04c92">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[Pressão:]]></text>
			</staticText>
		</band>
	</detail>
	<pageFooter>
		<band height="57">
			<staticText>
				<reportElement x="0" y="4" width="168" height="9" uuid="57c200e7-7ff1-4568-ae60-80259b0c4741">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[OBSERVAÇÕES / INFORMAÇÕES ADICIONAIS:]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="17" width="555" height="36" uuid="880685df-da11-498c-acac-2a195909650c">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box leftPadding="0">
					<pen lineWidth="0.3"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="555" height="9" uuid="0cd40727-e860-4b84-85ae-2d4e2149665f">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="0.75"/>
					</box>
					<text><![CDATA[Static Text]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="9" width="555" height="9" uuid="64ca829f-b18d-47f2-958c-3595426a5ba3">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="0.75"/>
					</box>
					<text><![CDATA[Static Text]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="18" width="555" height="9" uuid="19d80c43-fedd-40c4-9fb0-e8054a9a2e8c">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="0.75"/>
					</box>
					<text><![CDATA[Static Text]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="555" height="36" forecolor="#000000" uuid="fe72ffd5-28a2-4ba0-a3d1-ea11246cdb20">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<pen lineWidth="0.2"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="6" isUnderline="false"/>
						<paragraph lineSpacing="Fixed" lineSpacingSize="8.0" firstLineIndent="1" leftIndent="1" tabStopWidth="0"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{OBSERVACOES}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="27" width="555" height="9" uuid="78d2ca3e-d59a-491d-848d-074bcae11bb2">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="0.75"/>
					</box>
					<text><![CDATA[Static Text]]></text>
				</staticText>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
