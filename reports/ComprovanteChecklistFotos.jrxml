<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklistFotos" columnCount="4" printOrder="Horizontal" pageWidth="555" pageHeight="842" columnWidth="138" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="92c3c34a-6a74-4816-b251-78e573d7db94">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="TESTE_FREEDOM"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="APLICACAO" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[SELECT F.FOTO_ICONE
FROM MOB_OS_PERTENCE_FOTO F, MOB_PERTENCE_ITEM PI, MOB_PERTENCE_GRUPO PG
WHERE PI.COD_ITEM = F.COD_ITEM 
 AND PI.ID_GRUPO = PG.ID_GRUPO
 AND PG.APLICACAO =  $P{APLICACAO} 
 AND F.COD_EMPRESA = $P{COD_EMPRESA} 
 AND F.NUMERO_OS = $P{NUMERO_OS}
 AND PI.ATIVO = 'S'
 AND PG.ATIVO = 'S'
ORDER BY F.ORDEM]]>
	</queryString>
	<field name="FOTO_ICONE" class="java.io.InputStream"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="32">
			<staticText>
				<reportElement x="1" y="3" width="552" height="22" uuid="bcbd45af-60c0-4d00-ab7e-9046043e3ef4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[FOTOS DE RECEBIMENTO DO VEÍCULO]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="136" splitType="Stretch">
			<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
				<reportElement x="4" y="4" width="130" height="128" uuid="33ada3c8-cca1-4179-bae8-d3aac26bb721"/>
				<box padding="5">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$F{FOTO_ICONE}]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>
