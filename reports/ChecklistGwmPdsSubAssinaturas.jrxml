<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ChecklistGwmPdsSubAssinaturas" pageWidth="555" pageHeight="74" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="ID_CHECKLIST" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select 
    assinatura_checkin.assinatura_cliente AS ASSINATURA_CLIENTE,
    assinatura_checkin.data_assinatura_cliente as DATA_CLIENTE,
    assinatura_checkin.assinatura as ASSINATURA_RESPONSAVEL,
    assinatura_checkin.data_assinatura as DATA_RESPONSAVEL
from mob_os_assinatura assinatura_checkin
where assinatura_checkin.numero_os = $P{NUMERO_OS}
and assinatura_checkin.cod_empresa = $P{COD_EMPRESA}
AND assinatura_checkin.aplicacao (+) = 'R']]>
	</queryString>
	<field name="ASSINATURA_CLIENTE" class="java.awt.Image"/>
	<field name="DATA_CLIENTE" class="java.sql.Timestamp"/>
	<field name="ASSINATURA_RESPONSAVEL" class="java.awt.Image"/>
	<field name="DATA_RESPONSAVEL" class="java.sql.Timestamp"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="52" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="52" uuid="723e9983-62dc-41ca-904f-e37a291104b3">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<printWhenExpression><![CDATA[$P{ID_CHECKLIST} != 30500.0]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="185" y="3" width="185" height="34" uuid="10195b76-68b1-4290-8fc8-89a5c5f65297">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{ASSINATURA_RESPONSAVEL}]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="214" y="39" width="127" height="12" uuid="5a58423d-405f-4f0f-9a96-3dc0a90dcb77">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Técnico Responsável]]></text>
				</staticText>
			</frame>
		</band>
		<band height="52">
			<frame>
				<reportElement x="0" y="0" width="555" height="52" uuid="27ef3920-6c87-4c20-9bea-ec12fc086bf7">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<printWhenExpression><![CDATA[$P{ID_CHECKLIST} == 30500.0]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="29" y="3" width="185" height="34" uuid="b3b146aa-181b-4140-9192-1db490568bc4">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{ASSINATURA_CLIENTE}]]></imageExpression>
				</image>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="341" y="3" width="185" height="34" uuid="f5babd39-b65c-41f0-a832-1796816f4ad2">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{ASSINATURA_RESPONSAVEL}]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="370" y="38" width="127" height="12" uuid="ed47c665-3dd6-425f-b609-4232e4574e3c">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Assinatura Consultor]]></text>
				</staticText>
				<staticText>
					<reportElement x="58" y="38" width="127" height="12" uuid="851b7347-6969-4fc4-87fa-5147b340a0f6">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Assinatura Cliente]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
