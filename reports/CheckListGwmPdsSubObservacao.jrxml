<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListGwmPdsSubObservacao" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select cod_empresa, nome from empresas e where e.cod_empresa in (1,2,3)]]>
		</queryString>
		<field name="COD_EMPRESA" class="java.lang.Double"/>
		<field name="NOME" class="java.lang.String"/>
	</subDataset>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[25356.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[34.0]]></defaultValueExpression>
	</parameter>
	<parameter name="LISTA_GRUPOS" class="java.lang.String">
		<defaultValueExpression><![CDATA["3000"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[with DADOS as (
select info.cod_empresa as COD_EMPRESA,
       info.cod_produto as COD_PRODUTO,
       info.cod_modelo as COD_MODELO,
       info.tipo_os as TIPO_OS,
       info.cod_segmento as COD_SEGMENTO
  from (select os.cod_empresa,dv.Cod_Produto, dv.cod_modelo, os.tipo as tipo_os, produtos.cod_segmento
          from os, os_dados_veiculos dv, produtos
         where os.numero_os = dv.numero_os
           and os.cod_empresa = dv.cod_empresa
           and os.cod_empresa = $P{COD_EMPRESA}
           and os.numero_os = $P{NUMERO_OS}
           and produtos.cod_produto = dv.cod_produto) info
),
TODOS_ITENS as ( 
SELECT A.ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */ 
       A.COD_ITEM,
       CASE WHEN SUBSTR(A.DESCRICAO, 2,5) LIKE '%-%' THEN TRIM(SUBSTR(A.DESCRICAO, 1, INSTR(A.DESCRICAO, '-') - 1)) ELSE '' END AS ACAO_ITEM,
       CASE WHEN SUBSTR(A.DESCRICAO, 2,5) LIKE '%-%' THEN TRIM(SUBSTR(A.DESCRICAO, INSTR(A.DESCRICAO, '-') + 1)) ELSE A.DESCRICAO END AS DESCRICAO_ITEM,
       NVL(A.observacao,' ') as OBSERVACAO_ITEM,
       A.RESPOSTA_EH_OBSERVACAO,
       D.DESCRICAO AS DESCRICAO_OPCAO,
       B.DESCRICAO DESCRICAO_GRUPO,
       B.ORDEM AS ORDEM_GRUPO ,
       C.OBSERVACAO,
       ROWNUM as LINHA_RELATORIO
FROM MOB_PERTENCE_ITEM A, MOB_PERTENCE_GRUPO B, MOB_OS_PERTENCE C, MOB_OPCAO D
WHERE A.ID_GRUPO = B.ID_GRUPO
 AND B.ATIVO = 'S'
 AND A.COD_ITEM = C.COD_ITEM (+)
 AND B.APLICACAO = 'R'
 AND C.COD_EMPRESA(+) = $P{COD_EMPRESA}
 AND C.NUMERO_OS(+) = $P{NUMERO_OS}
 AND C.ID_OPCAO = D.ID_OPCAO(+)
 AND B.ID_GRUPO in ($P!{LISTA_GRUPOS})
 ),
 
 ITENS_COM_OBSERVACAO AS ( /* pego todos os itens observação e preservo LINHA_RELATORIO para impressão*/
    SELECT TODOS_ITENS.*
    FROM TODOS_ITENS
    WHERE TODOS_ITENS.OBSERVACAO IS NOT NULL
    			 AND NVL(TODOS_ITENS.RESPOSTA_EH_OBSERVACAO,'N') <> 'S'
 ),
 
 PARAMETROS AS (
           SELECT
              3 MULTIPLO, /*Determina o multiplo da quantidade de linhas*/
              0 MAXIMO_LINHAS /* determina o maximo de linhas, zero é ilimitado, o numero de linhas que a consulta retornar*/
              FROM DUAL
),
CONSULTA_LIMITADA as (
     SELECT ITENS_COM_OBSERVACAO.* FROM ITENS_COM_OBSERVACAO,parametros where (rownum <= parametros.maximo_linhas or parametros.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL as (
    SELECT * FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL
    FROM dual,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),parametros.multiplo) <> 0 or (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY level <= parametros.multiplo - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), parametros.multiplo)
)
select ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */ 
       COD_ITEM,
       ACAO_ITEM,
       DESCRICAO_ITEM,
       OBSERVACAO_ITEM,
       RESPOSTA_EH_OBSERVACAO,
       DESCRICAO_OPCAO,
       DESCRICAO_GRUPO,
       lower(RPAD(regexp_replace(OBSERVACAO,'[[:space:]]',' '),100)) AS OBSERVACAO,
       ORDEM_GRUPO,
       LINHA_RELATORIO
FROM CONSULTA_FINAL
ORDER BY ID_GRUPO]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="ACAO_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="OBSERVACAO_ITEM" class="java.io.InputStream"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="ORDEM_GRUPO" class="java.lang.Double"/>
	<field name="LINHA_RELATORIO" class="java.lang.Double"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="14">
			<staticText>
				<reportElement x="0" y="0" width="555" height="14" uuid="683ca6b9-b0d0-4825-a86b-5bab16e9bc4b">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Obs:]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="15">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="30" y="0" width="525" height="15" forecolor="#000000" uuid="28603694-2d9d-4d5a-ad4e-8b7f78d259c2">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.2"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isUnderline="false"/>
					<paragraph lineSpacing="Fixed" lineSpacingSize="12.0" firstLineIndent="1" leftIndent="1" tabStopWidth="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###;(#,##0.###-)">
				<reportElement mode="Transparent" x="0" y="0" width="30" height="15" forecolor="#000000" uuid="ea232931-457b-4d5e-b79c-f1f5ef49bede">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.2"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isUnderline="false"/>
					<paragraph lineSpacing="Fixed" lineSpacingSize="12.0" firstLineIndent="1" leftIndent="1" tabStopWidth="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LINHA_RELATORIO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
