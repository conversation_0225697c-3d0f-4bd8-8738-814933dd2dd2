<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RequisicoesItensCanceladas" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="5" bottomMargin="5" uuid="b210b23a-3a04-4bce-bdc3-5829df4bd199">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Desenvolvimento"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="REQUISICAO" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[
   
SELECT OREQ.COD_EMPRESA,
       OREQ.NUMERO_OS,
       OREQ.REQUISICAO,
       OREQ.DATA,
       TRUNC(OREQ.DATA_EMISSAO) DATA_REQ,
       TO_CHAR(OREQ.DATA_EMISSAO, 'HH:MM:ss') HORA_REQ,                
        OREQ.COD_ITEM,
        ITENS.DESCRICAO,
        OREQ.COD_FORNECEDOR,
        FORNEC.NOME_FORNECEDOR,
        OREQ.QUANTIDADE,
        OREQ.QUANTIDADE AS QTDE_REQUISICAO,
        ITENS.UNIDADE,
        OREQ.PRECO_VENDA  as PRECO_VENDA,
      (NVL(OREQ.QUANTIDADE,1) * NVL(OREQ.PRECO_VENDA,0)) AS TOTAL_ITEM,       
        OREQ.NOME,            
         itens.part_number,
          CASE
            WHEN REQA.ASSINATURA_PRODUTIVO IS NULL THEN
              'N'
            ELSE
              'S'
        END TEM_ASSINATURA_PRODUTIVO,
        REQA.ASSINATURA_PRODUTIVO,
        REQA.COD_TECNICO as COD_PRODUTIVO_ASS,
        REQA.ASSINATURA_ESTOQUISTA,
        REQA.ESTOQUISTA,
        IH.MOTIVO       
   FROM OS_REQUISICOES_CANCELADA OREQ,       
        ITENS              ,
        FORNECEDOR_ESTOQUE FORNEC,    
        OS_REQ_ASSINATURA REQA,
        ITENS_HISTORICO IH
 WHERE OREQ.COD_ITEM = ITENS.COD_ITEM
   AND OREQ.COD_FORNECEDOR = FORNEC.COD_FORNECEDOR
   AND OREQ.COD_EMPRESA = REQA.COD_EMPRESA(+)
   AND OREQ.NUMERO_OS = REQA.NUMERO_OS(+)
   AND OREQ.REQUISICAO = REQA.REQUISICAO(+)
   AND OREQ.COD_EMPRESA = IH.COD_EMPRESA
   AND TO_CHAR(OREQ.NUMERO_OS) = IH.DOCUMENTO
   AND OREQ.COD_FORNECEDOR = IH.COD_FORNECEDOR
   AND OREQ.COD_ITEM = IH.COD_ITEM
   AND OREQ.REQUISICAO = IH.COD_CONTROLE
   AND IH.TIPO_OPERACAO = 'E'
   AND IH.COD_OPERACAO = '05'      
   AND OREQ.COD_EMPRESA = $P{COD_EMPRESA}
   AND OREQ.NUMERO_OS = $P{NUMERO_OS}
   AND OREQ.REQUISICAO = $P{REQUISICAO}
   ORDER BY  OREQ.COD_ITEM  
]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.math.BigDecimal"/>
	<field name="NUMERO_OS" class="java.math.BigDecimal"/>
	<field name="REQUISICAO" class="java.math.BigDecimal"/>
	<field name="DATA" class="java.sql.Timestamp"/>
	<field name="DATA_REQ" class="java.sql.Timestamp"/>
	<field name="HORA_REQ" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="COD_FORNECEDOR" class="java.math.BigDecimal"/>
	<field name="NOME_FORNECEDOR" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.math.BigDecimal"/>
	<field name="QTDE_REQUISICAO" class="java.math.BigDecimal"/>
	<field name="UNIDADE" class="java.lang.String"/>
	<field name="PRECO_VENDA" class="java.math.BigDecimal"/>
	<field name="TOTAL_ITEM" class="java.math.BigDecimal"/>
	<field name="NOME" class="java.lang.String"/>
	<field name="PART_NUMBER" class="java.lang.String"/>
	<field name="TEM_ASSINATURA_PRODUTIVO" class="java.lang.String"/>
	<field name="ASSINATURA_PRODUTIVO" class="java.io.InputStream"/>
	<field name="COD_PRODUTIVO_ASS" class="java.math.BigDecimal"/>
	<field name="ASSINATURA_ESTOQUISTA" class="java.io.InputStream"/>
	<field name="ESTOQUISTA" class="java.lang.String"/>
	<field name="MOTIVO" class="java.lang.String"/>
	<variable name="totalItens" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{COD_ITEM}]]></variableExpression>
	</variable>
	<variable name="qtdeTotal" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{QUANTIDADE}]]></variableExpression>
	</variable>
	<variable name="valorTotal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{TOTAL_ITEM}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="18" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#E6E6E6" uuid="9c26aca8-03ed-48b6-b7b3-b6a4b58c5ae2">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="134" y="2" width="46" height="14" uuid="17cf11c6-6eb7-459c-835e-16cd45da144b">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement x="280" y="2" width="46" height="14" uuid="fc3269ad-5df1-4958-ab4d-c89deecf0e55">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Fornec]]></text>
			</staticText>
			<staticText>
				<reportElement x="404" y="2" width="20" height="14" uuid="5cb4c10c-3d87-4e66-a480-ed0639e78248">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[UN]]></text>
			</staticText>
			<staticText>
				<reportElement x="428" y="2" width="24" height="14" uuid="7502c13f-5f7c-4b1f-bf2b-3d9b55be743e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement x="463" y="2" width="45" height="14" uuid="0a4ca2b0-da88-44eb-8fd9-eb35ca75eee1">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Vlr Unitário]]></text>
			</staticText>
			<staticText>
				<reportElement x="512" y="2" width="39" height="14" uuid="19a565f1-a0e3-4883-9917-44df47322fd8">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Vlr Total]]></text>
			</staticText>
			<staticText>
				<reportElement x="3" y="1" width="46" height="14" uuid="adbe0640-4940-45aa-accd-90536355e485">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Código Item]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="28">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#FFFFFF" uuid="103379ab-211b-4c77-aa78-df38c96b864c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="1" y="2" width="119" height="14" uuid="000e082a-09eb-4ac7-9e7f-ef33cffd0d0b"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[ $F{COD_ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="134" y="2" width="136" height="14" uuid="9b44c278-80e8-4ac7-94d6-e23666c044ed"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="2" width="110" height="14" uuid="eb0d93f6-82a3-4974-9795-a8c03743246b"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME_FORNECEDOR}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="404" y="2" width="22" height="14" uuid="66186e91-d26b-4281-91a9-d89f4630a356"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{UNIDADE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="428" y="2" width="22" height="14" uuid="67cc4da1-17b2-4529-a791-5bc467d61d75"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="463" y="0" width="45" height="14" uuid="6e9d6cd0-f943-4ca2-a68a-6e311c1fa24d"/>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="512" y="0" width="39" height="14" uuid="71572be2-b80c-40d1-8f02-d5c1acf24c71"/>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOTAL_ITEM}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="14" width="29" height="14" uuid="1209b7d9-e9cd-47ac-9d6a-9cd1f851d462">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Motivo:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="28" y="14" width="81" height="14" uuid="e2494246-a2a5-4c9e-84eb-fef0834cbe60"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MOTIVO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="138" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="18" backcolor="#E6E6E6" uuid="3e712a3f-c9c7-4c77-8b33-1d037ea65838">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="1" y="2" width="46" height="14" uuid="9b7c2d6a-9636-48f8-97ed-aecfea43244f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Total de Itens:]]></text>
			</staticText>
			<textField>
				<reportElement x="48" y="2" width="52" height="14" uuid="316fe163-c224-4d7b-99f4-5c8299de5c4a"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalItens}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="247" y="4" width="32" height="12" uuid="9ba04c66-a8cc-4955-8600-ff44cef489a3"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{qtdeTotal}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="483" y="4" width="68" height="12" uuid="85482b0e-a18f-4260-9fb6-2501cd4a43e1"/>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{valorTotal}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement mode="Transparent" x="0" y="108" width="180" height="1" uuid="8075a3b7-1236-4f33-9603-f8dfbefee8bc"/>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<frame>
				<reportElement mode="Transparent" x="191" y="108" width="180" height="1" uuid="d4e9a8b3-2031-4a4c-b28b-d31d3a08394f"/>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<frame>
				<reportElement mode="Transparent" x="383" y="108" width="170" height="1" uuid="3c3f68ed-db6e-4c69-8aa5-fd14eac8f600">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.01"/>
					<topPen lineWidth="1.0"/>
				</box>
			</frame>
			<staticText>
				<reportElement x="220" y="109" width="130" height="14" uuid="dbe5f5e6-6da6-49e3-b281-27b174068a49">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Assinatura - Supridor de Peças]]></text>
			</staticText>
			<staticText>
				<reportElement x="381" y="109" width="170" height="14" uuid="2aabbb2b-c80c-4e6d-a960-04f8443da21f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Assinatura Boqueteiro]]></text>
			</staticText>
			<image>
				<reportElement x="0" y="55" width="180" height="50" uuid="87c26953-bf80-4b82-a879-************"/>
				<imageExpression><![CDATA[$F{ASSINATURA_PRODUTIVO}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="0" y="109" width="180" height="14" uuid="05dca2d1-0abd-4dec-8849-c4f68f648921">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Assinatura Produtivo]]></text>
			</staticText>
			<image>
				<reportElement x="375" y="57" width="180" height="50" uuid="36e10f3f-4aa8-42f2-89ad-4c0f6cc844af"/>
				<imageExpression><![CDATA[$F{ASSINATURA_ESTOQUISTA}]]></imageExpression>
			</image>
		</band>
	</columnFooter>
</jasperReport>
