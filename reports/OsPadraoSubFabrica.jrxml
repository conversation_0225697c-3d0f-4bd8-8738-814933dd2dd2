<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsPadraoSubFabrica" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="2fcbf2f0-3411-417a-af04-97cc3e505799">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_CLIENTE" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT NVL(CLIENTES.NOME, ' ') NOME,
 NVL(CLIENTES.PREFIXO_COM, '  ') PREFIXO_COM, 
 NVL(CLIENTES.TELEFONE_COM, '  ') TELEFONE_COM, 
 CLIENTES.RAMAL_COM, 
 NVL(CLIENTES.UF_COM, ' ') UF_COM,
 NVL(CLIENTES.BAIRRO_COM, ' ') BAIRRO_COM,
 CLIENTES.COD_CID_COM,
 NVL(CLIENTES.RUA_COM, ' ') RUA_COM,
 CLIENTES.CEP_COM, 
 NVL(CLIENTES.COMPLEMENTO_COM, ' ') COMPLEMENTO_COM,
 CLIENTES.FACHADA_COM,
 NVL(CLIENTES.CONTATO_COM, ' ') CONTATO_COM,
 NVL(UF.DESCRICAO, ' ') AS ESTADO,
 DADOS_JURIDICOS.CGC,
 NVL(DADOS_JURIDICOS.INSC_ESTADUAL, ' ') INSC_ESTADUAL,
 NVL(CIDADES.DESCRICAO, ' ') AS CIDADE
FROM CLIENTES, CIDADES, DADOS_JURIDICOS, UF
WHERE (CLIENTES.UF_COM = UF.UF (+))
  AND (CLIENTES.UF_COM = CIDADES.UF (+))
  AND (CLIENTES.COD_CID_COM = CIDADES.COD_CIDADES (+))
  AND (CLIENTES.COD_CLIENTE = DADOS_JURIDICOS.COD_CLIENTE )
  AND (CLIENTES.COD_CLIENTE = $P{COD_CLIENTE})
Union
SELECT NVL(CLIENTES.NOME, NVL(SEGURADORA.SEGURADORA, ' ')) AS NOME,
       NVL(CLIENTES.PREFIXO_COM, '  ') PREFIXO_COM,
       NVL(CLIENTES.TELEFONE_COM, '  ') TELEFONE_COM,
       CLIENTES.RAMAL_COM,
       NVL(CLIENTES.UF_COM, ' ') UF_COM,
       NVL(CLIENTES.BAIRRO_COM, ' ') BAIRRO_COM,
       CLIENTES.COD_CID_COM,
       NVL(CLIENTES.RUA_COM, ' ') RUA_COM,
       CLIENTES.CEP_COM,
       NVL(CLIENTES.COMPLEMENTO_COM, ' ') COMPLEMENTO_COM,
       CLIENTES.FACHADA_COM,
       NVL(CLIENTES.CONTATO_COM, ' ') CONTATO_COM,
       NVL(UF.DESCRICAO, ' ') AS ESTADO,
       DADOS_JURIDICOS.CGC,
       NVL(DADOS_JURIDICOS.INSC_ESTADUAL, ' ') INSC_ESTADUAL,
       NVL(CIDADES.DESCRICAO, ' ') AS CIDADE
  FROM SEGURADORA, CLIENTES, CIDADES, DADOS_JURIDICOS, UF
 WHERE CLIENTES.UF_COM = UF.UF(+)
   AND CLIENTES.UF_COM = CIDADES.UF(+)
   AND CLIENTES.COD_CID_COM = CIDADES.COD_CIDADES(+)
   AND CLIENTES.COD_CLIENTE = DADOS_JURIDICOS.COD_CLIENTE(+)
   AND SEGURADORA.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
   AND (SEGURADORA.COD_SEGURADORA = $P{COD_CLIENTE})]]>
	</queryString>
	<field name="NOME" class="java.lang.String"/>
	<field name="PREFIXO_COM" class="java.lang.String"/>
	<field name="TELEFONE_COM" class="java.lang.String"/>
	<field name="RAMAL_COM" class="java.lang.String"/>
	<field name="UF_COM" class="java.lang.String"/>
	<field name="BAIRRO_COM" class="java.lang.String"/>
	<field name="COD_CID_COM" class="java.lang.Double"/>
	<field name="RUA_COM" class="java.lang.String"/>
	<field name="CEP_COM" class="java.lang.String"/>
	<field name="COMPLEMENTO_COM" class="java.lang.String"/>
	<field name="FACHADA_COM" class="java.lang.String"/>
	<field name="CONTATO_COM" class="java.lang.String"/>
	<field name="ESTADO" class="java.lang.String"/>
	<field name="CGC" class="java.lang.String"/>
	<field name="INSC_ESTADUAL" class="java.lang.String"/>
	<field name="CIDADE" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="44">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="1" width="554" height="18" backcolor="#D9D9D9" uuid="ebef8c78-cea4-4ade-860c-c1c4347d95c3">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="6" y="4" width="298" height="13" uuid="1db81bad-fd78-4ade-864b-f5b5701f95d5"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Fábrica: " + $F{NOME}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="6" y="20" width="241" height="12" uuid="4208a31a-1caf-4954-8aa8-8957a233a99c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RUA_COM} + " " + $F{FACHADA_COM} + ", " +$F{COMPLEMENTO_COM} + " " + $F{BAIRRO_COM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="6" y="32" width="241" height="12" uuid="e0c7a471-b055-418e-a20f-32ca7f60f4b4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CEP_COM} + " " +$F{CIDADE} + " " +  $F{UF_COM}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="310" y="4" width="240" height="13" uuid="0ed38cf2-8670-4f74-be90-0667764ef4f3">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Calibri" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CGC}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="434" y="32" width="117" height="12" isRemoveLineWhenBlank="true" uuid="66e6b106-ad5e-4a69-b627-8d453ebe7ff0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["IE: " + $F{INSC_ESTADUAL}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="434" y="20" width="117" height="12" isRemoveLineWhenBlank="true" uuid="816eee65-073d-4d44-9d3e-43e579d2ba62">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Fone: (" +  $F{PREFIXO_COM} + ")" +  $F{TELEFONE_COM} ]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
