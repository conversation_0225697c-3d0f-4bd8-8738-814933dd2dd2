<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiCheckListChecKCheckListFotos" pageWidth="275" pageHeight="842" columnWidth="275" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[115582.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="OBRIGATORIO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<parameter name="APLICACAO" class="java.lang.String">
		<defaultValueExpression><![CDATA["R"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT F.FOTO_ICONE, PI.DESCRICAO, PG.DESCRICAO AS DESCRICAO_GRUPO, MOP.OBSERVACAO
FROM MOB_OS_PERTENCE_FOTO F, MOB_PERTENCE_ITEM PI, MOB_PERTENCE_GRUPO PG, MOB_OS_PERTENCE MOP
WHERE PI.COD_ITEM = F.COD_ITEM 
 AND PI.ID_GRUPO = PG.ID_GRUPO
AND PG.APLICACAO = $P{APLICACAO}
 AND F.COD_EMPRESA = $P{COD_EMPRESA} 
 AND F.NUMERO_OS = $P{NUMERO_OS}
 AND MOP.NUMERO_OS = F.NUMERO_OS
 AND MOP.COD_EMPRESA = F.COD_EMPRESA
 AND MOP.COD_ITEM = F.COD_ITEM
ORDER BY F.ORDEM]]>
	</queryString>
	<field name="FOTO_ICONE" class="java.awt.Image"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="194" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="275" height="186" uuid="4322e92e-9965-400f-8218-5c9254755c62">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="67" y="13" width="143" height="144" uuid="8051e594-ad12-465d-96d0-8928596c4142"/>
					<imageExpression><![CDATA[$F{FOTO_ICONE}]]></imageExpression>
				</image>
				<textField>
					<reportElement mode="Transparent" x="0" y="162" width="275" height="12" forecolor="#000000" backcolor="#C9C9C9" uuid="6df1de77-ce9d-4d7b-a026-568f15ae8513">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_GRUPO} + " - " + $F{DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement mode="Transparent" x="4" y="174" width="18" height="10" forecolor="#000000" backcolor="#C9C9C9" uuid="bbf936fe-69e1-403c-b199-0986a1e16a1d">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OBSERVACAO} == null
	? ""
	: "Obs:"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement mode="Transparent" x="22" y="174" width="251" height="10" forecolor="#000000" backcolor="#C9C9C9" uuid="33187f4e-4653-42a0-b3da-42d9ba9e23a2">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
