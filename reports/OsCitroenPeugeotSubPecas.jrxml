<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsCitroenPeugeotSubPecas" columnCount="2" printOrder="Horizontal" pageWidth="555" pageHeight="239" whenNoDataType="BlankPage" columnWidth="275" columnSpacing="5" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="d760b610-66b4-42df-8551-447eb20ebfd0">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<style name="Cor1" style="Style1" mode="Opaque" forecolor="#FFFFFF" backcolor="#0076A9">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN") )]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#F57523" pattern=""/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#001F56"/>
		</conditionalStyle>
	</style>
	<style name="Cor2" mode="Opaque" forecolor="#CCE3ED" backcolor="#589EC3" pattern="">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#FDE3D3" backcolor="#FF9F67"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#231F20" backcolor="#B2AFC4"/>
		</conditionalStyle>
	</style>
	<style name="Cor3" style="Style1" mode="Opaque" forecolor="#000203" backcolor="#CCE3ED">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#FDE3D3"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#EFEFF3"/>
		</conditionalStyle>
	</style>
	<style name="alternateStyle" style="Cor3" mode="Opaque">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($F{COR_LINHA} == 1)]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COPIA_CLIENTE" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="MARCA" class="java.lang.String">
		<defaultValueExpression><![CDATA["CITROEN"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[WITH Q_PECAS AS (
SELECT ROW_NUMBER() OVER(ORDER BY 1 ASC) AS ID_RED,
       DECODE(OS.SEQUENCIA_DAV,
              0,
              OS_REQUISICOES.COD_ITEM,
              NVL(ITENS_CUSTOS.COD_FISCAL_ITEM, OS_REQUISICOES.COD_ITEM)) AS COD_ITEM,
       OS_REQUISICOES.REQUISICAO,
       OS_REQUISICOES.COD_FORNECEDOR,
       DECODE(FORNECEDOR_ESTOQUE.OFICIAL,
              'N',
              '*' || ITENS.DESCRICAO,
              ITENS.DESCRICAO) AS DESCRICAO,
       OS_REQUISICOES.QUANTIDADE,
       OS_REQUISICOES.CAUSADORA,
       OS_REQUISICOES.ITEM,
       OS_REQUISICOES.COD_SERVICO,
       SERVICOS.DESCRICAO_SERVICO,
       SERVICOS.TEMPO_PADRAO,
       ITENS.COD_MAX_DESC,
       ITENS.UNIDADE,
       OS_REQUISICOES.NUMERO_OS,
       NVL(OS.COD_OS_AGENDA, 0) AS COD_OS_AGENDA,
       NVL(ESTOQUE.QTDE, 0) AS ESTOQUE_QTDE,
       NVL(ESTOQUE.RESERVADO, 0) AS RESERVADO,
       
       ROUND(DECODE(OS.STATUS_OS,
                    1,
                    OS_REQUISICOES.PRECO_FINAL,
                    DECODE(OS.CORTESIA,
                           'S',
                           OS_REQUISICOES.PRECO_CORTESIA,
                           DECODE(OS_TIPOS.INTERNO,
                                  'S',
                                  ROUND((100 +
                                        DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS,
                                                'S',
                                                DECODE(ITENS.COD_TRIBUTACAO,
                                                       '1',
                                                       DECODE(PARM_SYS.REGIME_ICMS,
                                                              'S',
                                                              DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                                     'S',
                                                                     DECODE(ICC.CLASSE_PECA,
                                                                            2,
                                                                            OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                            0),
                                                                     0),
                                                              OS_TIPOS.AUMENTO_PRECO_PECA),
                                                       0),
                                                OS_TIPOS.AUMENTO_PRECO_PECA)) *
                                        DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                               'V',
                                               OS_REQUISICOES.PRECO_VENDA,
                                               'G',
                                               OS_REQUISICOES.PRECO_GARANTIA,
                                               'F',
                                               OS_REQUISICOES.CUSTO_FORNECEDOR,
                                               DECODE(OTE.CUSTO_MAIS_IMPOSTOS,
                                                      'S',
                                                      OS_REQUISICOES.PRECO_VENDA,
                                                      OS_REQUISICOES.CUSTO_CONTABIL))) / 100,
                                  DECODE(OS_TIPOS.GARANTIA,
                                         'S',
                                         OS_REQUISICOES.PRECO_GARANTIA,
                                         DECODE(NVL(OS.FABRICA, 'N'),
                                                'S',
                                                OS_REQUISICOES.PRECO_GARANTIA,
                                                DECODE(SIGN(OS.FRANQUIA),
                                                       1,
                                                       PRECO_FRANQUIA,
                                                       ROUND((100 - NVL(SEGURADORA.DESCONTO_REQUISICAO,
                                                                        0)) *
                                                             OS_REQUISICOES.PRECO_VENDA) / 100)))))),
             NVL(ITENS_FORNECEDOR.QTDE_CASAS_DECIMAIS, 2)) AS PRECO_VENDA,
       ROUND(OS_REQUISICOES.QUANTIDADE *
             ROUND(DECODE(OS.STATUS_OS,
                          1,
                          OS_REQUISICOES.PRECO_FINAL,
                          DECODE(OS.CORTESIA,
                                 'S',
                                 OS_REQUISICOES.PRECO_CORTESIA,
                                 DECODE(OS_TIPOS.INTERNO,
                                        'S',
                                        ROUND((100 +
                                              DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS,
                                                      'S',
                                                      DECODE(ITENS.COD_TRIBUTACAO,
                                                             '1',
                                                             DECODE(PARM_SYS.REGIME_ICMS,
                                                                    'S',
                                                                    DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                                           'S',
                                                                           DECODE(ICC.CLASSE_PECA,
                                                                                  2,
                                                                                  OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                                  0),
                                                                           0),
                                                                    OS_TIPOS.AUMENTO_PRECO_PECA),
                                                             0),
                                                      OS_TIPOS.AUMENTO_PRECO_PECA)) *
                                              DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                                     'V',
                                                     OS_REQUISICOES.PRECO_VENDA,
                                                     'G',
                                                     OS_REQUISICOES.PRECO_GARANTIA,
                                                     'F',
                                                     OS_REQUISICOES.CUSTO_FORNECEDOR,
                                                     DECODE(OTE.CUSTO_MAIS_IMPOSTOS,
                                                            'S',
                                                            OS_REQUISICOES.PRECO_VENDA,
                                                            OS_REQUISICOES.CUSTO_CONTABIL))) / 100,
                                        DECODE(OS_TIPOS.GARANTIA,
                                               'S',
                                               OS_REQUISICOES.PRECO_GARANTIA,
                                               DECODE(NVL(OS.FABRICA, 'N'),
                                                      'S',
                                                      OS_REQUISICOES.PRECO_GARANTIA,
                                                      DECODE(SIGN(OS.FRANQUIA),
                                                             1,
                                                             PRECO_FRANQUIA,
                                                             ROUND((100 -
                                                                   NVL(SEGURADORA.DESCONTO_REQUISICAO,
                                                                        0)) *
                                                                   OS_REQUISICOES.PRECO_VENDA) / 100)))))),
                   NVL(ITENS_FORNECEDOR.QTDE_CASAS_DECIMAIS, 2)),
             2) AS PRECO_TOTAL

  FROM OS_REQUISICOES,
       SERVICOS,
       ESTOQUE,
       ITENS,
       OS,
       VW_OS_TIPOS           OS_TIPOS,
       FORNECEDOR_ESTOQUE,
       SEGURADORA,
       ITENS_FORNECEDOR,
       ITENS_CLASSE_CONTABIL ICC,
       PARM_SYS,
       PARM_SYS2,
       ITENS_CUSTOS,
       OS_TIPOS_EMPRESAS     OTE

 WHERE (OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM)
   AND SERVICOS.COD_SERVICO = OS_REQUISICOES.COD_SERVICO
   AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR
   AND (OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM(+))
   AND (OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR(+))
   AND (OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA(+))
   AND (OS_REQUISICOES.NUMERO_OS = OS.NUMERO_OS)
   AND (OS_REQUISICOES.COD_EMPRESA = OS.COD_EMPRESA)
   AND (OS.TIPO = OS_TIPOS.TIPO)
   AND (OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA)
   AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA(+)
   AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
   AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
   AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL(+)
   AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
   AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
   AND OS_REQUISICOES.COD_ITEM = ITENS_CUSTOS.COD_ITEM(+)
   AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_CUSTOS.COD_FORNECEDOR(+)
   AND OS_REQUISICOES.COD_EMPRESA = ITENS_CUSTOS.COD_EMPRESA(+)
   AND (OS_REQUISICOES.NUMERO_OS = $P{NUMERO_OS})
   AND (OS_REQUISICOES.COD_EMPRESA = $P{COD_EMPRESA})
   AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
   AND OTE.TIPO = OS.TIPO
 ORDER BY ID_RED, OS_REQUISICOES.ITEM, OS_REQUISICOES.COD_ITEM ),
 
 LINHAS_EXTRAS AS (
   SELECT LEVEL ID1 FROM DUAL CONNECT BY LEVEL <=32
 )
SELECT
MOD(TRUNC((ROWNUM-1)/2),2) AS COR_LINHA
,Q_PECAS.DESCRICAO
,Q_PECAS.QUANTIDADE 
FROM Q_PECAS, LINHAS_EXTRAS
WHERE Q_PECAS.ID_RED(+) = LINHAS_EXTRAS.ID1]]>
	</queryString>
	<field name="COR_LINHA" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="15">
			<staticText>
				<reportElement style="Cor1" mode="Opaque" x="0" y="0" width="555" height="15" uuid="5e449429-c6ec-4060-84ff-c8d52b983c0f">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[LISTA DE PEÇAS]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="16">
			<staticText>
				<reportElement key="" style="Cor2" mode="Opaque" x="170" y="3" width="67" height="13" forecolor="#FFFFFF" uuid="a444e5a3-f5e3-4597-b083-6481e3725e69"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Referência]]></text>
			</staticText>
			<staticText>
				<reportElement key="" style="Cor2" mode="Opaque" x="0" y="3" width="166" height="13" forecolor="#FFFFFF" uuid="49b7d2ac-52ec-46db-a799-bde946333953"/>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement key="" style="Cor2" mode="Opaque" x="241" y="3" width="36" height="13" forecolor="#FFFFFF" uuid="44a3d606-05b9-4725-9f75-dda2f2c45733"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Quant.]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField pattern="#,##0.###;(#,##0.###-)">
				<reportElement key="" style="alternateStyle" mode="Opaque" x="241" y="0" width="36" height="13" uuid="d8e873fe-eb2c-4cf2-85d7-941cae6ef1d8"/>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Auto">
				<reportElement key="" style="alternateStyle" mode="Opaque" x="170" y="0" width="67" height="13" uuid="1dea1435-91da-493e-a735-af995505979c"/>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
			</textField>
			<textField>
				<reportElement key="" style="alternateStyle" mode="Opaque" x="0" y="0" width="166" height="13" uuid="193df370-2289-4628-ba6e-fdd83f9d6c3e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
