<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHondaCarro" pageWidth="595" pageHeight="842" whenNoDataType="BlankPage" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isFloatColumnFooter="true" whenResourceMissingType="Empty" uuid="871cb0c8-c266-4282-98fa-d77bc2e0a652">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="725"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="260"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="181"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="812"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<style name="CAMPO_NULL" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[11334.0]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\temp\\image_zk\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\34944\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="ASSINAR_DIGITALMENTE" class="java.lang.String">
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT
  NVL(OS_ASSINATURA.ASSINATURA, MOB_OS_ASSINATURA.ASSINATURA_CLIENTE) AS ASSINATURA_ENTREGA,
  NVL(OS_ASSINATURA.DATA_ASSINATURA, MOB_OS_ASSINATURA.DATA_ASSINATURA_CLIENTE) AS ASSINATURA_ENTREGA_DATA,
  
  OS_AGENDA.SIGNATURE AS ASSINATURA_ABERTURA,
  OS.COD_EMPRESA                    AS OS_COD_EMPRESA,
  OS.STATUS_OS                    AS OS_STATUS_OS,
  OS.COD_OS_AGENDA                  AS OS_COD_OS_AGENDA,
  NVL(OS.NUMERO_OS, 0)                    AS OS_NUMERO_OS, 
  ABS(OS.NUMERO_OS)                  AS OS_ABS_OSNUM,
  OS.NUMERO_OS_FABRICA                AS OS_NUMERO_OS_FABRICA,
  OS.COD_CLIENTE                    AS OS_COD_CLIENTE, 
  OS.CLIENTE_RAPIDO                  AS OS_CLIENTE_RAPIDO,
  OS.TIPO_ENDERECO                  AS OS_TIPO_ENDERECO,
  NVL(OS.OBSERVACAO, ' ')                    AS OS_OBSERVACAO,
  OS.EXTENDIDA                    AS OS_EXTENDIDA,
  OS.SEGURADORA                    AS OS_SEGURADORA,
  NVL(TO_CHAR(OS.DATA_EMISSAO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_EMISSAO,
  NVL(TO_CHAR(OS.DATA_LIBERADO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_LIBERADO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2)     AS OS_DIA_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2)     AS OS_MES_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4)     AS OS_ANO_EMISSAO,
  OS.HORA_EMISSAO                    AS OS_HORA_EMISSAO,
  OS.HORA_ENCERRADA                  AS OS_HORA_ENCERRADA,
  OS.DATA_ENCERRADA                  AS OS_DATA_ENCERRADA,
  OS.DATA_ENTREGA          AS OS_DATA_ENTREGA,
  NVL(OS.HORA_PROMETIDA,' ')                  AS OS_HORA_PROMETIDA, 
  SUBSTR(OS.HORA_PROMETIDA, 1, 2)           AS OS_HORA24_PROMETIDA,
  SUBSTR(OS.HORA_PROMETIDA, 4, 2)           AS OS_MINUTO_PROMETIDA,
  NVL(TO_CHAR(OS.DATA_PROMETIDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PROMETIDA, 
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2)     AS OS_DIA_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2)     AS OS_MES_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4)   AS OS_ANO_PROMETIDA,
  NVL(OS.VALOR_SERVICOS_BRUTO, 0)                AS OS_VALOR_SERVICOS_BRUTO,
  NVL(OS.VALOR_ITENS_BRUTO, 0)                AS OS_VALOR_ITENS_BRUTO,
  NVL(OS.DESCONTOS_SERVICOS, 0)                AS OS_DESCONTOS_SERVICOS,
  NVL(OS.DESCONTOS_ITENS, 0)                  AS OS_DESCONTOS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS,0)                           AS OS_TOTAL_OS_SERVICOS,
  NVL(OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS,0)                                 AS OS_TOTAL_OS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO,0)                            AS OS_TOTAL_OS_BRUTO,
  NVL(OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS,0)                                AS OS_TOTAL_OS_DESCONTO,
  NVL((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) + (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS), 0)   AS OS_TOTAL_OS,
  OS.COD_SEGURADORA                  AS OS_COD_SEGURADORA,
  OS.TIPO                        AS OS_TIPO,
  OS.NOME                       AS CONSULTOR,
  OS.OS_ENTRADA                    AS OS_OS_ENTRADA,
  '1ª VIA CLIENTE 1ª VIA CLIENTE 1ª VIA CLIENTE'     AS OS_VIA,
  OS.TOTAL_IMPRESSAO_FABRICA              AS OS_TOTAL_IMPRESSAO_FABRICA,
  NVL(OS_TIPOS_TERMO.TEXTO,' ')            AS OS_TERMO_TEXTO,
  NVL(TO_CHAR(OS.DATA_TAXI, 'DD/MM/YYYY'),' ')                  AS OS_DATA_TAXI,
  NVL(OS.HORA_TAXI,' ')                  AS OS_HORA_TAXI, 
  NVL(OS.TODO_TRAB,' ') AS OS_TODO_TRABALHO_FEITO,
  NVL(OS.TODO_LIMPO,' ') AS OS_TODO_LIMPO,
  
  NVL(os.pri_rodagem,'N')                  AS OS_PRIMEIRA_RODAGEM,
  NVL(os.seg_rodagem,'N')                  AS OS_SEGUNDA_RODAGEM,
  NVL(TO_CHAR(os.data_pri_rodagem, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PRIMEIRA_RODAGEM,
  NVL(TO_CHAR(os.data_seg_rodagem, 'DD/MM/YYYY'),' ')                  AS OS_DATA_SEGUNDA_RODAGEM,
  NVL(os.os_pri_resp,' ')                  AS OS_PRIM_RESPONSAVEL_RODAGEM,
  NVL(os.os_seg_resp,' ')                  AS OS_SEG_RESPONSAVEL_RODAGEM,
  
 
  
  (SELECT LISTAGG(FORMAS_PAGAMENTO.CONDICAO_PAGAMENTO, '; ') WITHIN GROUP (ORDER BY CONDICAO_PAGAMENTO)        
  FROM (SELECT (F.DESCRICAO || ' ' ||  TO_CHAR(O.VALOR, 'FM999999999D00')) AS CONDICAO_PAGAMENTO FROM OS_PAGAMENTO O, FORMA_PGTO F
  WHERE O.COD_FORMA_PGTO = F.COD_FORMA_PGTO
    AND O.NUMERO_OS = $P{NUMERO_OS}
    AND O.COD_EMPRESA= $P{COD_EMPRESA}) FORMAS_PAGAMENTO) AS OS_DESCRICAO_PAGAMENTO,


  
  (SELECT LISTAGG(ORCAMENTOS.NUMERO_OS, ', ') WITHIN GROUP (ORDER BY NUMERO_OS)
  FROM 
    (SELECT ABS(OS.NUMERO_OS) AS NUMERO_OS
    FROM OS, OS_STATUS, OS_ORCAMENTOS, OS_TIPOS
    WHERE OS.STATUS_OS = OS_STATUS.STATUS_OS
    AND OS_ORCAMENTOS.COD_EMPRESA = OS.COD_EMPRESA
    AND OS_ORCAMENTOS.NUMERO_ORCAMENTO = OS.NUMERO_OS
    AND OS.TIPO = OS_TIPOS.TIPO
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
    AND OS_ORCAMENTOS.COD_EMPRESA = $P{COD_EMPRESA}
    AND OS_ORCAMENTOS.NUMERO_OS = $P{NUMERO_OS}
    GROUP BY OS.NUMERO_OS
    ORDER BY OS.NUMERO_OS) ORCAMENTOS) AS OS_NUMERO_ORCAMENTO,
    
  (SELECT LISTAGG(ORCAMENTOS.NOME_CLIENTE_APROVOU, ', ') WITHIN GROUP (ORDER BY ORCAMENTOS.NOME_CLIENTE_APROVOU) 
    FROM 
    (SELECT OS_ORCAMENTOS.NOMECLIAPROVOU AS NOME_CLIENTE_APROVOU
    FROM OS, OS_STATUS, OS_ORCAMENTOS, OS_TIPOS
    WHERE OS.STATUS_OS = OS_STATUS.STATUS_OS
    AND OS_ORCAMENTOS.COD_EMPRESA = OS.COD_EMPRESA
    AND OS_ORCAMENTOS.NUMERO_ORCAMENTO = OS.NUMERO_OS
    AND OS.TIPO = OS_TIPOS.TIPO
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
    AND OS_ORCAMENTOS.COD_EMPRESA = 34
    AND OS_ORCAMENTOS.NUMERO_OS = 23493
    GROUP BY OS_ORCAMENTOS.NOMECLIAPROVOU
    ORDER BY OS_ORCAMENTOS.NOMECLIAPROVOU) ORCAMENTOS) AS OS_NOME_CLIENTE_APROVOU,

  NVL(OS.ORC_SERV_BRUTO + OS.ORC_ITEM_BRUTO, 0) AS OS_TOTAL_ORCAMENTO,
  NVL(OS.AUTORIZADO_TODO_ORCAMENTO,'N') OS_ORCAMENTO_AUTORIZADO,
  ' ' AS OS_DATA_ORCAMENTO_APROVADO,
  
  
  
  GARANTIA_DOC.COD_SG_PADRAO AS OS_GARANTIA_SG_PADRAO,
  GARANTIA_DOC.COD_SG_EXEMPLO AS OS_GARANTIA_SG_EXEMPLO,
  
  
  
  NVL(OS.LAVAR_VEICULO,'N') AS OS_LAVAR_VEICULO,
  NVL(OS.CLIENTE_AGUARDOU,'N') AS OS_CLIENTE_AGUARDOU,
  
  NVL(OS_AGENDA.EH_RETORNO,'N') AS OS_EH_RETORNO,
  NVL(OS_AGENDA.EH_RECALL,'N') AS EH_RECALL,
     
   TO_CHAR(OS.DATA_AGENDADA_RECEPCAO, 'DD/MM/YYYY') AS DATA_AGENDAMENTO,
   
    
   TO_CHAR(OS.DATA_PROMETIDA_REVISADA, 'DD/MM/YYYY') AS DATA_PROMETIDA_REVISADA,
   OS.HORA_PROMETIDA_REVISADA,
   
  
  (SELECT EE.NOME_COMPLETO 
          FROM EMPRESAS_USUARIOS EE 
         WHERE EE.COD_EMPRESA = OS.COD_EMPRESA 
           AND EE.NOME = OS.QUEM_ABRIU) AS OS_NOME_AGENDADOR, 
       
   NVL(OS_AGENDA.EH_FIAT_PROFISSIONAL,'N') AS OS_FIAT_PROFISSIONAL,
   
   NVL(OS.PECA_USADA_FICA_CLIENTE,'N') AS OS_PECA_USADA_FICA_CLIENTE,
   
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'DD/MM/YYYY') AS OS_DATA_ULT_IMRESSAO,
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'HH24:MI:SS') AS OS_HORA_ULT_IMRESSAO,
   
   NVL(OS_AGENDA.VEICULO_PLATAFORMA,'N') AS OS_VEICULO_PLATAFORMA,
   
   TO_CHAR(OS_AGENDA.DATA_AGENDADA , 'HH24:MI') AS OS_HORA_AGENDADA,
   
   (SELECT PB.DESCRICAO
          FROM PRISMA_BOX PB
         WHERE PB.PRISMA = OS_DADOS_VEICULOS.PRISMA) AS OS_DESCRICAO_PRISMA,
   KM_PROXIMA_REVISAO,
   DATA_PROXIMO_SERVICO,
    
  
  NVL(EMPRESAS_USUARIOS.NOME_COMPLETO, ' ')           AS OS_CONSULTOR_COMPLETO,
  EMPRESAS_USUARIOS.CODIGO_OPCIONAL AS OS_CODIGO_OPCIONAL,
  
  
  
  
  OS_TIPOS.DESCRICAO                         AS OS_TIPO_DESCRICAO,
  OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO              AS OS_TIPO_COM_DESCRICAO,
  NVL(OS_TIPOS.GARANTIA,'N')                        AS OS_GARANTIA,
  OS_TIPOS.REVISAO_GRATUITA                    AS OS_REVISAO_GRATUITA,
  NVL(OS_TIPOS.INTERNO,'N')          AS OS_INTERNO,
  OS_TIPOS.COD_CLIENTE               AS OS_CLIENTE_DO_TIPO,
  OS_TIPOS.OUTRO_CONCESSIONARIA                  AS OS_OUTRO_CONCESSIONARIA,
  NVL(OS_TIPOS.TIPO_FABRICA_EMPRESA,OS_TIPOS.TIPO_FABRICA)     AS OS_TIPO_FABRICA,
  
  
  NVL(OS_DADOS_VEICULOS.ANO, ' ')                      AS OS_ANO,
  OS_DADOS_VEICULOS.HORIMETRO                    AS OS_HORIMETRO,
  NVL(OS_DADOS_VEICULOS.PRISMA, ' ')                    AS OS_PRISMA,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2)     AS DIA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2)     AS MES_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4)   AS ANO_VENDA,
  NVL(OS_DADOS_VEICULOS.COMBUSTIVEL,0)                  AS OS_COMBUSTIVEL,
  NVL(OS_DADOS_VEICULOS.COR_EXTERNA, ' ')                  AS OS_COR_EXTERNA,
  NVL(OS_DADOS_VEICULOS.PLACA, ' ')                      AS OS_PLACA,
  NVL(OS_DADOS_VEICULOS.KM, 0)                      AS OS_KM,
  NVL(OS_DADOS_VEICULOS.CHASSI, ' ')                    AS OS_CHASSI,
  NVL(OS_DADOS_VEICULOS.NUMERO_MOTOR, ' ')                  AS OS_NUMERO_MOTOR,
  OS_DADOS_VEICULOS.NUMERO_RENAVAM                AS OS_NUMERO_RENAVAM,
  OS_DADOS_VEICULOS.SERIE                      AS OS_SERIE,
  OS_DADOS_VEICULOS.COD_CONCESSIONARIA              AS OS_COD_CONCESSIONARIA,
  OS_DADOS_VEICULOS.ESTADO_PINTURA                AS OS_ESTADO_PINTURA,
  NVL(OS_DADOS_VEICULOS.JOGO_FERRAMENTAS, ' ')                AS OS_JOGO_FERRAMENTAS,
  NVL(OS_DADOS_VEICULOS.ELASTICOS, ' ')                    AS OS_ELASTICOS,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_D, ' ')                AS OS_TAMPA_LATERAL_D,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_E, ' ')                AS OS_TAMPA_LATERAL_E,
  NVL(OS_DADOS_VEICULOS.FLANELA, ' ')                    AS OS_FLANELA,
  OS_DADOS_VEICULOS.NUMERO_FROTA                  AS OS_NUMERO_FROTA,
  
  
  NVL(CONCESSIONARIAS.NOME, ' ')                          AS CONCESSIONARIA_NOME,
  CONCESSIONARIAS.UF                            AS CONCESSIONARIA_UF,
  CONCESSIONARIAS.CIDADE                        AS CONCESSIONARIA_CIDADE,
  CONCESSIONARIAS.BAIRRO                        AS CONCESSIONARIA_BAIRRO,
  CONCESSIONARIAS.ENDERECO                      AS CONCESSIONARIA_RUA,
  CONCESSIONARIAS.CEP                           AS CONCESSIONARIA_CEP,
  CONCESSIONARIAS.CODIGO_PADRAO                   AS CONCESSIONARIA_CODIGO,
  UF_CONCESSIONARIA.DESCRICAO                   AS CONCESSIONARIA_ESTADO,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_FAB_BATERIA, 'DD/MM/YYYY'),' ')                AS OS_DATA_FAB_BATERIA,
  NVL(OS_DADOS_VEICULOS.COD_FAB_BATERIA, ' ')                               AS OS_COD_FAB_BATERIA,
  NVL((SELECT EMPRESAS_USUARIOS.NOME_COMPLETO
          FROM EMPRESAS_USUARIOS, CLIENTES_FROTA
         WHERE EMPRESAS_USUARIOS.NOME = CLIENTES_FROTA.NOME_VENDEDOR
                   AND OS.COD_CLIENTE = CLIENTES_FROTA.COD_CLIENTE
                   AND OS_DADOS_VEICULOS.COD_PRODUTO = CLIENTES_FROTA.COD_PRODUTO
                   AND OS_DADOS_VEICULOS.COD_MODELO = CLIENTES_FROTA.COD_MODELO
                   AND OS_DADOS_VEICULOS.CHASSI = CLIENTES_FROTA.CHASSI
               ),' ') AS CONCESSIONARIA_VENDEDOR,
  
  
  
  PRODUTOS.DESCRICAO_PRODUTO  AS OS_DESCRICAO_PRODUTO,
  
  
  NVL(PRODUTOS.DESCRICAO_PRODUTO || ' / ' || PRODUTOS_MODELOS.DESCRICAO_MODELO,' ')   AS DESC_PROD_MOD,
  PRODUTOS_MODELOS.DESCRICAO_MODELO                      AS OS_DESCRICAO_MODELO,
  PRODUTOS_MODELOS.MOD_VER_SERIE                        AS OS_MOD_VER_SERIE,
  PRODUTOS_MODELOS.LINHA                            AS OS_LINHA,
  
  
  MARCAS.DESCRICAO_MARCA  AS OS_DESCRICAO_MARCA,
  
  
  CO.TEXTO_AIDF  AS OS_TEXTO_AIDF,
  
  
  NVL(EMPRESAS.NOME,' ')           AS EMPRESAS_NOME_EMPRESA,
  NVL(EMPRESAS.EMPRESA_NOME_COMPLETO,' ')     AS EMPRESAS_RAZAO_SOCIAL,

  NVL(EMPRESAS.CGC, ' ')                     AS EMPRESAS_CGC,
  EMPRESAS.FACHADA                  AS EMPRESAS_FACHADA,
  EMPRESAS.ESTADO                   AS UF_EMPRESA,
  NVL(EMPRESAS.CIDADE, ' ')                    AS EMPRESAS_CIDADE,
  EMPRESAS.BAIRRO                    AS EMPRESAS_BAIRRO,
  EMPRESAS.COMPLEMENTO                AS EMPRESAS_COMPLEMENTO,
  NVL(EMPRESAS.RUA, ' ')              AS EMPRESAS_RUA,
  NVL(EMPRESAS.FONE, ' ')                    AS EMPRESAS_FONE,
  NVL(EMPRESAS.FAX, ' ')                    AS EMPRESAS_FAX,
  NVL(EMPRESAS.CEP, ' ')                    AS EMPRESAS_CEP,
  EMPRESAS.INSCRICAO_MUNICIPAL            AS EMPRESAS_INSCRICAO_MUNICIPAL,
  EMPRESAS.INSCRICAO_SUBSTITUICAO            AS EMPRESAS_INSC_SUBSTITUICAO,
  NVL(EMPRESAS.ESTADO,' ') AS EMPRESA_UF,
  NVL(UF_EMPRESA.DESCRICAO, ' ')                  AS EMPRESA_ESTADO,
  NVL(EMPRESAS.INSCRICAO_ESTADUAL, ' ')              AS EMPRESAS_INSCRICAO_ESTADUAL,
  TRUNC(SYSDATE)                     AS EMPRESA_DATA_ATUAL,
  SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'),1, 5)       AS EMPRESA_HORA_ATUAL_STR,
  NVL(CLIENTE_EMPRESA.ENDERECO_ELETRONICO, ' ')         AS EMPRESA_EMAIL,
  NVL(CLIENTE_DIVERSO_EMPRESA.empresa_site, ' ') AS EMPRESA_SITE,
  
  
  
  
 
  CLIENTE_DIVERSO.COD_CLIENTE    AS CLIENTE_COD_CLIENTE,
  NVL(CLIENTE_DIVERSO.NOME, ' ')        AS CLIENTE_NOME,
  NVL(CLIENTE_DIVERSO.RG,' ')          AS CLIENTE_RG,

  
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_CEL || '-' || CLIENTES.TELEFONE_CEL),' ') AS CLIENTE_FONE_CEL,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_RES || '-' || CLIENTES.TELEFONE_RES),' ') AS CLIENTE_FONE_RES,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_COM || '-' || CLIENTES.TELEFONE_COM),' ') AS CLIENTE_FONE_COM,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_FAX|| '-' || CLIENTES.TELEFONE_FAX),' ') AS CLIENTE_FONE_FAX,

  
  
  
  CLIENTES.ENDERECO_ELETRONICO  AS CLIENTE_ENDERECO_ELETRONICO,
  CLIENTES.EMAIL_NFE AS CLIENTE_EMAIL_NFE,
  NVL(NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL),' ') AS CLIENTE_INSC_ESTAD,
  
  NVL(DECODE(CLIENTE_DIVERSO.CPF,' ','CGC: ' || NVL(CLIENTE_DIVERSO.CGC,' '),'CPF: ' || CLIENTE_DIVERSO.CPF), ' ') AS CLIENTE_CGC_CPF,
  
  CLIENTE_DIVERSO.CGC        AS CLIENTE_CGC,
  CLIENTE_DIVERSO.CPF        AS CLIENTE_CPF,
  CLIENTES.COD_CLASSE        AS CLIENTE_COD_CLASSE,
  
  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.ENDERECO  || ', ' || CLIENTE_DIVERSO.COMPLEMENTO || ' ' || CLIENTE_DIVERSO.BAIRRO))
    WHEN '2' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_RES  || ', ' ||CLIENTES.COMPLEMENTO_RES || ' ' || CLIENTES.BAIRRO_RES))
    WHEN '3' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COM  || ', ' ||CLIENTES.COMPLEMENTO_COM || ' ' || CLIENTES.BAIRRO_COM))
    WHEN '4' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COBRANCA  || ', ' ||CLIENTES.COMPLEMENTO_COBRANCA || ' ' || CLIENTES.BAIRRO_COBRANCA))
    WHEN '5' THEN TRIM(',' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.RUA  || ', ' ||ENDERECO_POR_INSCRICAO.COMPLEMENTO || ' ' || ENDERECO_POR_INSCRICAO.BAIRRO))
    ELSE ' '
    END  AS CLIENTE_ENDERECO_COMPLETO,
    

  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO))
    WHEN '2' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_RES  || '-' || CLIENTES.TELEFONE_RES))
    WHEN '3' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '4' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '5' THEN TRIM('-' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO  || '-' || ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO))
    ELSE ' '
    END  AS CLIENTE_TELEFONE_COMPLETO,
    

  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.UF,
              2, CLIENTES.UF_RES,
              3, CLIENTES.UF_COM,
              4, CLIENTES.UF_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.UF,
               ' ')                          AS  CLIENTE_UF,
  DECODE(OS.TIPO_ENDERECO, 1, UF_DIVERSO.DESCRICAO,
              2, UF_RES.DESCRICAO,
              3, UF_COM.DESCRICAO,
              4, UF_COBRANCA.DESCRICAO,
              5, UF_INSCRICAO.DESCRICAO,
               ' ')                          AS  CLIENTE_ESTADO,
  DECODE(OS.TIPO_ENDERECO, 1, CIDADES_DIV.DESCRICAO,
              2, CIDADES_RES.DESCRICAO,
              3, CIDADES_COM.DESCRICAO,
              4, CIDADES_COBRANCA.DESCRICAO,
              5, ENDERECO_POR_INSCRICAO.CIDADE,
               ' ')                          AS  CLIENTE_CIDADE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.BAIRRO,
              2, CLIENTES.BAIRRO_RES,
              3, CLIENTES.BAIRRO_COM,
              4, CLIENTES.BAIRRO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.BAIRRO,
               ' ')                          AS  CLIENTE_BAIRRO,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.CEP,
              2, CLIENTES.CEP_RES,
              3, CLIENTES.CEP_COM,
              4, CLIENTES.CEP_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.CEP,
               ' ')                          AS  CLIENTE_CEP,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.ENDERECO,
              2, CLIENTES.RUA_RES,
              3, CLIENTES.RUA_COM,
              4, CLIENTES.RUA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.RUA,
               ' ')                          AS  CLIENTE_RUA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.COMPLEMENTO,
              2, CLIENTES.COMPLEMENTO_RES,
              3, CLIENTES.COMPLEMENTO_COM,
              4, CLIENTES.COMPLEMENTO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.COMPLEMENTO,
               ' ')                          AS  CLIENTE_COMPLEMENTO,
  DECODE(OS.TIPO_ENDERECO, 1, ' ',
              2, CLIENTES.FACHADA_RES,
              3, CLIENTES.FACHADA_COM,
              4, CLIENTES.FACHADA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.FACHADA,
               ' ')                          AS  CLIENTE_FACHADA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.FONE_CONTATO,
              2, CLIENTES.TELEFONE_RES,
              3, CLIENTES.TELEFONE_COM,
              4, CLIENTES.TELEFONE_COM,
              5, ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_FONE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
              2, CLIENTES.PREFIXO_RES,
              3, CLIENTES.PREFIXO_COM,
              4, CLIENTES.PREFIXO_COM,
              5, ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_PREFIXO,
  
  
  
  NVL(FATURAR_CLIENTE_DIVERSO.NOME, ' ')                   AS FATURAR_NOME,
  
  NVL(TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO)),' ') AS FATURAR_FONE,
  
  NVL(UF_FATURAR.DESCRICAO, ' ')                       AS FATURAR_ESTADO,
  NVL(FATURAR_CIDADE.DESCRICAO, FATURAR_CLIENTE_DIVERSO.CIDADE)   AS FATURAR_CIDADE,

  NVL(TRIM(SUBSTR(CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.ENDERECO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.ENDERECO ELSE '' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.COMPLEMENTO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.COMPLEMENTO ELSE ' ' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.BAIRRO)>0 THEN '  ' || FATURAR_CLIENTE_DIVERSO.BAIRRO ELSE '' END
    , 2)), ' ')  AS FATURAR_ENDERECO,
   
   NVL(GREATEST(
    FATURAR_CLIENTE.PREFIXO_RES || '-' || FATURAR_CLIENTE.TELEFONE_RES,
    FATURAR_CLIENTE.PREFIXO_COM || '-' || FATURAR_CLIENTE.TELEFONE_COM,
    FATURAR_CLIENTE.PREFIXO_FAX || '-' || FATURAR_CLIENTE.TELEFONE_FAX
   ), ' ')   AS FATURAR_TELEFONE_FAX,
   
   
    NVL(FATURAR_CLIENTE.BAIRRO_COM,
    FATURAR_CLIENTE.BAIRRO_RES)   AS FATURAR_BAIRRO,
  
  NVL(FATURAR_CLIENTE.CEP_COM,
    FATURAR_CLIENTE.CEP_RES)   AS FATURAR_CEP,
  
  FATURAR_DADOS_JURIDICOS.CGC AS FATURAR_CGC,
  
  FATURAR_DADOS_JURIDICOS.INSC_ESTADUAL AS FATURAR_IE,
 
   NVL((SELECT CLIENTES_FROTA.MEDIA_KM_MENSAL 
               FROM CLIENTES_FROTA
               WHERE OS.COD_CLIENTE = CLIENTES_FROTA.COD_CLIENTE
                     AND OS_DADOS_VEICULOS.COD_PRODUTO = CLIENTES_FROTA.COD_PRODUTO
                     AND OS_DADOS_VEICULOS.COD_MODELO = CLIENTES_FROTA.COD_MODELO
                     AND OS_DADOS_VEICULOS.CHASSI = CLIENTES_FROTA.CHASSI               
                ),0) AS MEDIA_KM_MENSAL,
   

  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_TERCEIROS 
    ELSE 
    TOTAIS_SERVICOS.VAL_TERCEIROS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_TERCEIROS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_TERCEIROS,
      
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_LAVAGEM
    ELSE 
    TOTAIS_SERVICOS.VAL_LAVAGEM - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_LAVAGEM/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_LAVAGEM,
  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_REVISAO
    ELSE 
    TOTAIS_SERVICOS.VAL_REVISAO - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_REVISAO/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_REVISAO,
    
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_MECANICA
    ELSE 
    TOTAIS_SERVICOS.VAL_MECANICA - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_MECANICA/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_MECANICA,

  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_GERAIS
    ELSE 
    TOTAIS_SERVICOS.VAL_GERAIS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_GERAIS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_GERAIS,
   
  
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_PECAS
    ELSE 
    TOTAL_PECAS.VALOR_PECAS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_PECAS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_PECAS,
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_ACESSORIOS
    ELSE 
    TOTAL_PECAS.VALOR_ACESSORIOS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_ACESSORIOS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_ACESSORIOS, 
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_OUTROS
    ELSE 
    TOTAL_PECAS.VALOR_OUTROS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_OUTROS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_OUTROS, 

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_LUBRIFICANTE
    ELSE 
    TOTAL_PECAS.VALOR_LUBRIFICANTE - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_LUBRIFICANTE/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_LUBRIFICANTE,  

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_COMBUSTIVEL
    ELSE 
    TOTAL_PECAS.VALOR_COMBUSTIVEL - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_COMBUSTIVEL/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_COMBUSTIVEL
  

    
 
FROM
  
  EMPRESAS,
  UF UF_EMPRESA,
  CLIENTES CLIENTE_EMPRESA,
  CLIENTE_DIVERSO CLIENTE_DIVERSO_EMPRESA,
  
  OS, EMPRESAS_USUARIOS, VW_OS_TIPOS OS_TIPOS, OS_DADOS_VEICULOS,
  CONCESSIONARIAS,UF UF_CONCESSIONARIA, PRODUTOS, PRODUTOS_MODELOS, MARCAS,
  CONTROLE_OS CO, PARM_SYS, OS_TIPOS_TERMO, OS_AGENDA,  
  GARANTIA_DOC,
  
  
  CLIENTE_DIVERSO, CLIENTES,  ENDERECO_POR_INSCRICAO,
  CIDADES CIDADES_RES, CIDADES CIDADES_COM, CIDADES CIDADES_COBRANCA, CIDADES CIDADES_DIV,
  UF UF_DIVERSO, UF UF_RES, UF UF_COM, UF UF_COBRANCA, UF UF_INSCRICAO,
  
  
  CLIENTE_DIVERSO FATURAR_CLIENTE_DIVERSO, CLIENTES FATURAR_CLIENTE, CIDADES FATURAR_CIDADE, 
   UF UF_FATURAR, DADOS_JURIDICOS FATURAR_DADOS_JURIDICOS,
   
  (SELECT
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'T', TOTAIS.PRECO, 0)) AS VAL_TERCEIROS,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'L', TOTAIS.PRECO, 0)) AS VAL_LAVAGEM,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'R', TOTAIS.PRECO, 0)) AS VAL_REVISAO,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'M', TOTAIS.PRECO, 0)) AS VAL_MECANICA,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'G', TOTAIS.PRECO, 0)) AS VAL_GERAIS
  FROM
  (SELECT
  DECODE(S.TERCEIROS, 'S',       'T',
    DECODE(S.LAVAGEM, 'S',       'L',
    DECODE(SS.TIPO_SETOR, 'R', 'R',
                'M', 'M',
                   'G'))) AS TIPO_SERVICO,
  OSS.PRECO_VENDA + NVL((SELECT SUM(PRECO_VENDA) FROM OS_SERVICOS_ADICIONAIS ADI
               WHERE ADI.COD_EMPRESA = OSS.COD_EMPRESA
                 AND ADI.NUMERO_OS   = OSS.NUMERO_OS
                 AND ADI.ITEM        = OSS.ITEM
                 AND ADI.COD_SERVICO = OSS.COD_SERVICO), 0) AS PRECO
  FROM OS_SERVICOS OSS, SERVICOS S, SERVICOS_SETORES SS
  WHERE 1=1
  AND OSS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OSS.NUMERO_OS = $P{NUMERO_OS}
  AND OSS.COD_SERVICO = S.COD_SERVICO
  AND S.COD_SETOR = SS.COD_SETOR) TOTAIS ) TOTAIS_SERVICOS,
  
  
  
  (SELECT
  SUM(DECODE(CLASSE_PECA, 1, VALOR, 0)) AS VALOR_PECAS,
  SUM(DECODE(CLASSE_PECA, 2, VALOR, 0)) AS VALOR_ACESSORIOS,
  SUM(DECODE(NVL(CLASSE_PECA, 3), 3, VALOR, 0)) AS VALOR_OUTROS,
  SUM(DECODE(CLASSE_PECA, 4, VALOR, 0)) AS VALOR_LUBRIFICANTE,
  SUM(DECODE(CLASSE_PECA, 5, VALOR, 0)) AS VALOR_COMBUSTIVEL
  FROM (
  SELECT DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1), 
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4)) AS CLASSE_PECA,
  SUM(
  OS_REQUISICOES.QUANTIDADE *
  DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
        ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                    DECODE(ITENS.COD_TRIBUTACAO, '1',
                    DECODE(PARM_SYS.REGIME_ICMS, 'S',
                      DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                      DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                    0),
                         0),
                         OS_TIPOS.AUMENTO_PRECO_PECA),
                       0),
                     OS_TIPOS.AUMENTO_PRECO_PECA)) *
           DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                            'G', OS_REQUISICOES.PRECO_GARANTIA,
                            'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                            'P', OS_REQUISICOES.PRECO_FABRICA,
                            DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
          ) / 100,
     DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                        OS_REQUISICOES.PRECO_GARANTIA),
       DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
       DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
         ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
        DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                            OS_REQUISICOES.PRECO_VENDA))/100))))))) AS VALOR
  FROM OS_REQUISICOES, ITENS, ITENS_FORNECEDOR, OS, VW_OS_TIPOS OS_TIPOS, ITENS_CLASSE_CONTABIL ICC , SEGURADORA,
  PARM_SYS, PARM_SYS2, ITENS_GRUPO_INTERNO IGI, OS_TIPOS_EMPRESAS OTE
  WHERE OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
  AND OS.NUMERO_OS = OS_REQUISICOES.NUMERO_OS  
  AND OS.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA 
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS.TIPO = OS_TIPOS.TIPO
  AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND ITENS.COD_GRUPO_INTERNO = IGI.COD_GRUPO_INTERNO(+)
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
  AND OS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OS.NUMERO_OS = $P{NUMERO_OS}
  GROUP BY DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4))
  ) TOTAL) TOTAL_PECAS,
  
  (SELECT CE.KM AS KM_PROXIMA_REVISAO,NVL(C1.DATA_EVENTO, C1.DATA_NOVO_CONTATO) AS DATA_PROXIMO_SERVICO
    FROM OS_DADOS_VEICULOS, CRM_EVENTOS C1, CRM_CICLO_EVENTOS CE
    WHERE C1.COD_CICLO (+) > 0
    AND C1.DATA_EVENTO (+) > SYSDATE - 1
    AND C1.STATUS (+) IN ('P', 'A')
    AND C1.COD_CICLO = CE.COD_CICLO(+)
    AND C1.COD_TIPO_EVENTO = CE.COD_TIPO_EVENTO(+)
    AND NVL(CE.KM (+),0) > 0
    AND OS_DADOS_VEICULOS.CHASSI = C1.VEIC_CHASSI_COMPLETO (+)
    AND OS_DADOS_VEICULOS.NUMERO_OS = $P{NUMERO_OS}
    AND OS_DADOS_VEICULOS.COD_EMPRESA = $P{COD_EMPRESA}
    AND ROWNUM = 1
    ORDER BY DATA_PROXIMO_SERVICO) CRM_EVENTOS,
    
    OS_ASSINATURA,
    MOB_OS_ASSINATURA

WHERE   1 = 1
    AND OS.COD_EMPRESA = $P{COD_EMPRESA} 
    AND OS.NUMERO_OS = $P{NUMERO_OS}
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'

    AND OS.NOME = EMPRESAS_USUARIOS.NOME(+)
  
    AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
    AND OS.COD_OS_AGENDA = OS_AGENDA.COD_OS_AGENDA (+)
  
    AND OS.COD_EMPRESA = PARM_SYS.COD_EMPRESA(+)
  
    AND OS.TIPO = OS_TIPOS_TERMO.TIPO (+)
    
    AND OS.COD_EMPRESA = OS_TIPOS_TERMO.COD_EMPRESA (+)
  
    AND OS.TIPO = OS_TIPOS.TIPO
    AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA (+)
    
    AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS (+)
    AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA (+)
    
    AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA = CONCESSIONARIAS.COD_CONCESSIONARIA (+)
    
    AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF (+)
    
    AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO 
    
    AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
    
    AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
    AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
       
    AND OS.COD_EMPRESA = CO.COD_EMPRESA(+)
  
    AND OS.COD_DOCUMENTO = GARANTIA_DOC.COD_DOCUMENTO (+)

    AND OS.COD_EMPRESA = EMPRESAS.COD_EMPRESA
    AND EMPRESAS.ESTADO = UF_EMPRESA.UF (+)
    AND EMPRESAS.COD_CLIENTE = CLIENTE_EMPRESA.COD_CLIENTE (+)
    AND EMPRESAS.COD_CLIENTE = CLIENTE_DIVERSO_EMPRESA.COD_CLIENTE (+)
    
    AND OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES (+)
    AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF (+)
    AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES (+)
    AND OS.INSCRICAO_ESTADUAL = ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL (+)
    AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE (+)
    AND CLIENTES.UF_RES = UF_RES.UF (+)
    AND CLIENTES.UF_COM = UF_COM.UF (+)
    AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF (+)
    AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF (+)
    
    AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_CLIENTE.COD_CLIENTE (+)
    AND FATURAR_CLIENTE_DIVERSO.COD_CIDADES =FATURAR_CIDADE.COD_CIDADES (+)
    AND FATURAR_CLIENTE_DIVERSO.UF = UF_FATURAR.UF (+)
    AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_DADOS_JURIDICOS.COD_CLIENTE (+)
    AND OS.COD_CLIENTE = FATURAR_CLIENTE_DIVERSO.COD_CLIENTE (+)
    
    AND OS_ASSINATURA.COD_EMPRESA(+) = OS.COD_EMPRESA
    AND OS_ASSINATURA.NUMERO_OS(+) = OS.NUMERO_OS
    AND OS_ASSINATURA.TIPO_ASSINATURA(+) = 'ENTREGA_VEICULO_CLIENTE'
    
    AND MOB_OS_ASSINATURA.NUMERO_OS(+) = OS.NUMERO_OS
    AND MOB_OS_ASSINATURA.COD_EMPRESA(+) = OS.COD_EMPRESA
    AND MOB_OS_ASSINATURA.APLICACAO(+) = 'E']]>
	</queryString>
	<field name="ASSINATURA_ENTREGA" class="java.awt.Image"/>
	<field name="ASSINATURA_ENTREGA_DATA" class="java.sql.Timestamp"/>
	<field name="ASSINATURA_ABERTURA" class="java.awt.Image"/>
	<field name="OS_COD_EMPRESA" class="java.lang.Double"/>
	<field name="OS_STATUS_OS" class="java.lang.Double"/>
	<field name="OS_COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="OS_ABS_OSNUM" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS_FABRICA" class="java.lang.Double"/>
	<field name="OS_COD_CLIENTE" class="java.lang.Double"/>
	<field name="OS_CLIENTE_RAPIDO" class="java.lang.String"/>
	<field name="OS_TIPO_ENDERECO" class="java.lang.String"/>
	<field name="OS_OBSERVACAO" class="java.lang.String"/>
	<field name="OS_EXTENDIDA" class="java.lang.String"/>
	<field name="OS_SEGURADORA" class="java.lang.String"/>
	<field name="OS_DATA_EMISSAO" class="java.lang.String"/>
	<field name="OS_DATA_LIBERADO" class="java.lang.String"/>
	<field name="OS_DIA_EMISSAO" class="java.lang.String"/>
	<field name="OS_MES_EMISSAO" class="java.lang.String"/>
	<field name="OS_ANO_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_ENCERRADA" class="java.lang.String"/>
	<field name="OS_DATA_ENCERRADA" class="java.sql.Timestamp"/>
	<field name="OS_DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_HORA24_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MINUTO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DATA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DIA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MES_PROMETIDA" class="java.lang.String"/>
	<field name="OS_ANO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_BRUTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_DESCONTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="OS_COD_SEGURADORA" class="java.lang.Double"/>
	<field name="OS_TIPO" class="java.lang.String"/>
	<field name="CONSULTOR" class="java.lang.String"/>
	<field name="OS_OS_ENTRADA" class="java.lang.Double"/>
	<field name="OS_VIA" class="java.lang.String"/>
	<field name="OS_TOTAL_IMPRESSAO_FABRICA" class="java.lang.Double"/>
	<field name="OS_TERMO_TEXTO" class="java.lang.String"/>
	<field name="OS_DATA_TAXI" class="java.lang.String"/>
	<field name="OS_HORA_TAXI" class="java.lang.String"/>
	<field name="OS_TODO_TRABALHO_FEITO" class="java.lang.String"/>
	<field name="OS_TODO_LIMPO" class="java.lang.String"/>
	<field name="OS_PRIMEIRA_RODAGEM" class="java.lang.String"/>
	<field name="OS_SEGUNDA_RODAGEM" class="java.lang.String"/>
	<field name="OS_DATA_PRIMEIRA_RODAGEM" class="java.lang.String"/>
	<field name="OS_DATA_SEGUNDA_RODAGEM" class="java.lang.String"/>
	<field name="OS_PRIM_RESPONSAVEL_RODAGEM" class="java.lang.String"/>
	<field name="OS_SEG_RESPONSAVEL_RODAGEM" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PAGAMENTO" class="java.lang.String"/>
	<field name="OS_NUMERO_ORCAMENTO" class="java.lang.String"/>
	<field name="OS_NOME_CLIENTE_APROVOU" class="java.lang.String"/>
	<field name="OS_TOTAL_ORCAMENTO" class="java.lang.Double"/>
	<field name="OS_ORCAMENTO_AUTORIZADO" class="java.lang.String"/>
	<field name="OS_DATA_ORCAMENTO_APROVADO" class="java.lang.String"/>
	<field name="OS_GARANTIA_SG_PADRAO" class="java.lang.String"/>
	<field name="OS_GARANTIA_SG_EXEMPLO" class="java.lang.Double"/>
	<field name="OS_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="OS_CLIENTE_AGUARDOU" class="java.lang.String"/>
	<field name="OS_EH_RETORNO" class="java.lang.String"/>
	<field name="EH_RECALL" class="java.lang.String"/>
	<field name="DATA_AGENDAMENTO" class="java.lang.String"/>
	<field name="DATA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="OS_NOME_AGENDADOR" class="java.lang.String"/>
	<field name="OS_FIAT_PROFISSIONAL" class="java.lang.String"/>
	<field name="OS_PECA_USADA_FICA_CLIENTE" class="java.lang.String"/>
	<field name="OS_DATA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_HORA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_VEICULO_PLATAFORMA" class="java.lang.String"/>
	<field name="OS_HORA_AGENDADA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRISMA" class="java.lang.String"/>
	<field name="KM_PROXIMA_REVISAO" class="java.lang.Double"/>
	<field name="DATA_PROXIMO_SERVICO" class="java.sql.Timestamp"/>
	<field name="OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="OS_CODIGO_OPCIONAL" class="java.lang.String"/>
	<field name="OS_TIPO_DESCRICAO" class="java.lang.String"/>
	<field name="OS_TIPO_COM_DESCRICAO" class="java.lang.String"/>
	<field name="OS_GARANTIA" class="java.lang.String"/>
	<field name="OS_REVISAO_GRATUITA" class="java.lang.String"/>
	<field name="OS_INTERNO" class="java.lang.String"/>
	<field name="OS_CLIENTE_DO_TIPO" class="java.lang.Double"/>
	<field name="OS_OUTRO_CONCESSIONARIA" class="java.lang.String"/>
	<field name="OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="OS_ANO" class="java.lang.String"/>
	<field name="OS_HORIMETRO" class="java.lang.Double"/>
	<field name="OS_PRISMA" class="java.lang.String"/>
	<field name="OS_DATA_VENDA" class="java.lang.String"/>
	<field name="DIA_VENDA" class="java.lang.String"/>
	<field name="MES_VENDA" class="java.lang.String"/>
	<field name="ANO_VENDA" class="java.lang.String"/>
	<field name="OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="OS_PLACA" class="java.lang.String"/>
	<field name="OS_KM" class="java.lang.Double"/>
	<field name="OS_CHASSI" class="java.lang.String"/>
	<field name="OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="OS_NUMERO_RENAVAM" class="java.lang.String"/>
	<field name="OS_SERIE" class="java.lang.String"/>
	<field name="OS_COD_CONCESSIONARIA" class="java.lang.Double"/>
	<field name="OS_ESTADO_PINTURA" class="java.lang.String"/>
	<field name="OS_JOGO_FERRAMENTAS" class="java.lang.String"/>
	<field name="OS_ELASTICOS" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_D" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_E" class="java.lang.String"/>
	<field name="OS_FLANELA" class="java.lang.String"/>
	<field name="OS_NUMERO_FROTA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="CONCESSIONARIA_UF" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CIDADE" class="java.lang.String"/>
	<field name="CONCESSIONARIA_BAIRRO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_RUA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CEP" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CODIGO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_ESTADO" class="java.lang.String"/>
	<field name="OS_DATA_FAB_BATERIA" class="java.lang.String"/>
	<field name="OS_COD_FAB_BATERIA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_VENDEDOR" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRODUTO" class="java.lang.String"/>
	<field name="DESC_PROD_MOD" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="OS_MOD_VER_SERIE" class="java.lang.String"/>
	<field name="OS_LINHA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MARCA" class="java.lang.String"/>
	<field name="OS_TEXTO_AIDF" class="java.lang.String"/>
	<field name="EMPRESAS_NOME_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_RAZAO_SOCIAL" class="java.lang.String"/>
	<field name="EMPRESAS_CGC" class="java.lang.String"/>
	<field name="EMPRESAS_FACHADA" class="java.lang.String"/>
	<field name="UF_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_CIDADE" class="java.lang.String"/>
	<field name="EMPRESAS_BAIRRO" class="java.lang.String"/>
	<field name="EMPRESAS_COMPLEMENTO" class="java.lang.String"/>
	<field name="EMPRESAS_RUA" class="java.lang.String"/>
	<field name="EMPRESAS_FONE" class="java.lang.String"/>
	<field name="EMPRESAS_FAX" class="java.lang.String"/>
	<field name="EMPRESAS_CEP" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_MUNICIPAL" class="java.lang.String"/>
	<field name="EMPRESAS_INSC_SUBSTITUICAO" class="java.lang.String"/>
	<field name="EMPRESA_UF" class="java.lang.String"/>
	<field name="EMPRESA_ESTADO" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="EMPRESA_DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="EMPRESA_HORA_ATUAL_STR" class="java.lang.String"/>
	<field name="EMPRESA_EMAIL" class="java.lang.String"/>
	<field name="EMPRESA_SITE" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLIENTE" class="java.lang.Double"/>
	<field name="CLIENTE_NOME" class="java.lang.String"/>
	<field name="CLIENTE_RG" class="java.lang.String"/>
	<field name="CLIENTE_FONE_CEL" class="java.lang.String"/>
	<field name="CLIENTE_FONE_RES" class="java.lang.String"/>
	<field name="CLIENTE_FONE_COM" class="java.lang.String"/>
	<field name="CLIENTE_FONE_FAX" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="CLIENTE_EMAIL_NFE" class="java.lang.String"/>
	<field name="CLIENTE_INSC_ESTAD" class="java.lang.String"/>
	<field name="CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="CLIENTE_CGC" class="java.lang.String"/>
	<field name="CLIENTE_CPF" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLASSE" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_UF" class="java.lang.String"/>
	<field name="CLIENTE_ESTADO" class="java.lang.String"/>
	<field name="CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="CLIENTE_CEP" class="java.lang.String"/>
	<field name="CLIENTE_RUA" class="java.lang.String"/>
	<field name="CLIENTE_COMPLEMENTO" class="java.lang.String"/>
	<field name="CLIENTE_FACHADA" class="java.lang.String"/>
	<field name="CLIENTE_FONE" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO" class="java.lang.String"/>
	<field name="FATURAR_NOME" class="java.lang.String"/>
	<field name="FATURAR_FONE" class="java.lang.String"/>
	<field name="FATURAR_ESTADO" class="java.lang.String"/>
	<field name="FATURAR_CIDADE" class="java.lang.String"/>
	<field name="FATURAR_ENDERECO" class="java.lang.String"/>
	<field name="FATURAR_TELEFONE_FAX" class="java.lang.String"/>
	<field name="FATURAR_BAIRRO" class="java.lang.String"/>
	<field name="FATURAR_CEP" class="java.lang.String"/>
	<field name="FATURAR_CGC" class="java.lang.String"/>
	<field name="FATURAR_IE" class="java.lang.String"/>
	<field name="MEDIA_KM_MENSAL" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_TERCEIROS" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_LAVAGEM" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_REVISAO" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_MECANICA" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_GERAIS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_PECAS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_ACESSORIOS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_OUTROS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_LUBRIFICANTE" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_COMBUSTIVEL" class="java.lang.Double"/>
	<variable name="IMAGE_NAME" class="java.lang.String">
		<initialValueExpression><![CDATA["1-4"]]></initialValueExpression>
	</variable>
	<variable name="PAGINA_ATUAL" class="java.lang.Integer" resetType="None" calculation="Count">
		<variableExpression><![CDATA[$V{PAGE_COUNT}]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="122" splitType="Immediate">
			<frame>
				<reportElement x="0" y="0" width="554" height="40" uuid="5c046093-212e-439b-b9dd-797dfa957178">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement x="337" y="24" width="213" height="14" uuid="0a1a54e5-ee4a-4fd4-99bf-af4843f318d5"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<image scaleImage="FillFrame">
					<reportElement x="28" y="3" width="97" height="35" uuid="1a87800e-4272-4af6-af52-ea37e15d6fde">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600452.png"]]></imageExpression>
				</image>
			</frame>
			<frame>
				<reportElement x="0" y="40" width="260" height="82" isPrintWhenDetailOverflows="true" uuid="9ffc8a44-f6bf-4677-8c4e-510ef9a06821">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="3" y="0" width="96" height="11" uuid="888e8b14-596d-49cc-a097-0ea180b20d9f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Nome da Concessionaria]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="11" width="255" height="11" uuid="81e7608a-0bfc-4ea9-88c6-5ec7670dfc72"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="22" width="255" height="11" uuid="dd00bc8b-f96b-4a8c-b5af-36fb6ee79d9e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_RUA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="33" width="23" height="11" uuid="068e198c-bccf-4232-8091-b17e1b11b686">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="44" width="23" height="11" uuid="20e034c4-cf5b-47b3-a4bf-372c52fff036">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tel:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="55" width="33" height="11" uuid="a058bf29-5bda-4382-84df-577a5683ee14">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="66" width="33" height="11" uuid="b8d7d261-e1a6-4f1f-b40e-e1af4c52f59a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[E-Mail:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="33" width="70" height="11" uuid="03fdc31c-a5b4-43b2-a09d-32cf0d650973">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="96" y="33" width="162" height="11" uuid="ee4a37fd-e050-4bda-b5c5-4d8a81ba69e6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_CIDADE} + " - " + $F{UF_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="26" y="44" width="113" height="11" uuid="34cc7fdb-bc53-40ec-bb06-b877fc92b811">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_FONE}.length() > 6 ?  $F{EMPRESAS_FONE}: " "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="139" y="44" width="23" height="11" uuid="908ac912-3a69-4bbf-a5aa-8666e3d7723d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Fax:]]></text>
				</staticText>
				<textField>
					<reportElement x="162" y="44" width="96" height="11" uuid="891a36e2-4a52-4bc8-a624-d3f2de629ab3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_FAX}.length() > 6 ?  $F{EMPRESAS_FAX}: " "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="138" y="55" width="48" height="11" uuid="29fde5bd-637c-443e-a373-ce8c4a0cf45e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Insc.Estad.:]]></text>
				</staticText>
				<textField>
					<reportElement x="36" y="55" width="102" height="11" uuid="edf00bba-70bd-4906-aa06-005a3a444490">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_CGC}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="186" y="55" width="72" height="11" uuid="99e455f1-ca1b-4475-9676-07b75b2f46ce">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_INSCRICAO_ESTADUAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="36" y="66" width="222" height="11" uuid="203be4fe-9faf-4f92-9656-07768a029ce5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESA_EMAIL}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="260" y="40" width="294" height="82" isPrintWhenDetailOverflows="true" uuid="8e664189-45ee-4efb-adfe-973a2b717162">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="89" y="0" width="138" height="11" uuid="6ddfc8e1-44e7-4a47-bcbc-9f87fb139122">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[ORDEM DE SERVIÇO]]></text>
				</staticText>
				<staticText>
					<reportElement x="121" y="11" width="20" height="11" uuid="d7f4c2fc-a11e-459f-b94a-986c30376fbd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº]]></text>
				</staticText>
				<textField pattern="###0.###;(###0.###-)">
					<reportElement x="140" y="11" width="70" height="11" uuid="6e7f246c-6bf7-404b-9edb-0698f5232c17">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="22" width="82" height="11" uuid="710c2516-1bc2-45ba-a6e0-696f655468bb">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Condição Pagamento:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="33" width="53" height="11" uuid="47c0d7c7-d915-43ad-9009-51df254724ea">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Data Emissão:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="44" width="133" height="11" uuid="e06cbb65-6ba9-40f1-a05c-10620bec78db">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Orçamento Previo a ser Emitido em:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="55" width="82" height="11" uuid="e2315887-e027-4c05-91cf-38a56b6099ce">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Previsão de Entrega:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="66" width="53" height="11" uuid="5061e896-a563-4ff8-8f94-877ed6d6cec9">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Nº Doc. Fiscal:]]></text>
				</staticText>
				<textField>
					<reportElement x="56" y="66" width="120" height="10" uuid="bd713de3-5e50-4888-a80c-bae4eaa2da8f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="85" y="55" width="195" height="11" uuid="bca017f8-86df-4ab1-9ee5-5f30f0e76b7b">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_PROMETIDA} +" "+$F{OS_HORA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="56" y="33" width="70" height="11" uuid="684123e8-80df-4a2c-9d42-daa8ff7dc56f"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="176" y="66" width="46" height="11" uuid="daf6b257-5da1-405f-8658-0c702116f2ee">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº do Doc.]]></text>
				</staticText>
				<textField>
					<reportElement x="222" y="66" width="58" height="11" uuid="67dfad6b-c7a0-4510-9ff3-9dcf3bb07e5c"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="126" y="33" width="35" height="11" uuid="299c4e10-c7ee-494d-94ce-dc648a3f9683">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Validade:]]></text>
				</staticText>
				<staticText>
					<reportElement x="223" y="33" width="20" height="11" uuid="774a0f1d-3905-40af-9fc5-3d9a9eae9145">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Dias:]]></text>
				</staticText>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="630" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement x="0" y="0" width="306" height="70" isPrintWhenDetailOverflows="true" uuid="16ffd29a-19a0-46fa-b61a-70ddf79d93ee">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="3" y="1" width="23" height="11" uuid="8490dc75-8dac-4535-a469-f59bb3f82478">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="1" width="270" height="11" uuid="d76e24a7-c4dd-41a1-ac5d-17b728a1e52a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="12" width="23" height="11" uuid="279cad57-5141-4b77-b7d0-3cfbd7124e36">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[End.:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="12" width="230" height="11" uuid="70352056-dc12-447d-9055-23a28ce3a8dd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_COMPLETO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="23" width="23" height="11" uuid="b4f1c887-8fc5-4226-aa11-5874f3eeb8d0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="23" width="70" height="11" uuid="38a9fcdd-0dbc-4c9d-bba2-7a09311171bb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="34" width="167" height="11" uuid="374be4e7-6352-4cbc-a584-3b826802e6e8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CGC_CPF}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="45" width="40" height="11" uuid="31feb460-f87b-41ad-9972-cdc16fb4d1dd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tel (Res):]]></text>
				</staticText>
				<textField>
					<reportElement x="43" y="45" width="128" height="11" uuid="ef414931-1b17-401a-9659-12978b1f4bcf">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_TELEFONE_COMPLETO}.length() > 6 ?  $F{CLIENTE_TELEFONE_COMPLETO} : " "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="56" width="23" height="11" uuid="f2f3f45e-43a5-4743-a909-677c46cba2fe">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Email:]]></text>
				</staticText>
				<textField>
					<reportElement x="26" y="56" width="270" height="11" uuid="0e81a4de-f05b-405f-9341-9e3fb2a17ae8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="256" y="12" width="40" height="11" uuid="9746b252-0bc8-46ac-90c6-4c039d8985fd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FACHADA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="96" y="23" width="30" height="11" uuid="d52225a6-d980-49c3-9dca-cac3bf3775a2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<textField>
					<reportElement x="126" y="23" width="70" height="11" uuid="d444d812-9e46-4c6b-a0ea-8fdc981e8740">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CIDADE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="196" y="23" width="30" height="11" uuid="ae36da56-6758-48fb-b843-815587a6eaf5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Estado:]]></text>
				</staticText>
				<textField>
					<reportElement x="226" y="23" width="70" height="11" uuid="936c5e8f-12b3-4489-878d-cdcc4b86ba53">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_UF}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="171" y="34" width="23" height="11" uuid="35ac7ccf-e1d6-4c0e-91b2-ced672a9a14b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[I.E.:]]></text>
				</staticText>
				<textField>
					<reportElement x="194" y="34" width="102" height="11" uuid="1de77788-3794-40e6-813c-677147b5dc45">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_INSC_ESTAD}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="171" y="45" width="23" height="11" uuid="f8f213b7-5652-4566-a1d2-6f1e5d4df191">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cel.:]]></text>
				</staticText>
				<textField>
					<reportElement x="194" y="45" width="102" height="11" uuid="ea626241-cef2-4e44-a96a-ede0c4169932">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_CEL}.length() > 6 ?   $F{CLIENTE_FONE_CEL} : " "]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="306" y="0" width="81" height="70" isPrintWhenDetailOverflows="true" uuid="0bbd82c2-4726-4245-9ce0-574f7d1f868b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="30" y="16" width="44" height="11" uuid="d6416550-0ea1-4380-b7e9-9bcbe3b7d1b0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Externo]]></text>
				</staticText>
				<staticText>
					<reportElement x="30" y="29" width="44" height="11" uuid="9987f162-637c-4f39-a1dc-f8f50dcbf0b6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Garantia]]></text>
				</staticText>
				<staticText>
					<reportElement x="30" y="42" width="44" height="11" uuid="b0e59b27-6dbd-4e15-8ff6-6926a00f9828">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Interno]]></text>
				</staticText>
				<staticText>
					<reportElement x="9" y="3" width="63" height="11" uuid="9e621519-1692-446f-94bb-fad5054dbc50">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo de Serviço]]></text>
				</staticText>
				<textField>
					<reportElement stretchType="ContainerHeight" x="19" y="43" width="9" height="9" uuid="7b4b09e6-64c3-43d8-bb34-497da5c797aa">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_INTERNO}.equals("S")?"X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="19" y="30" width="9" height="9" uuid="b31a55c1-e376-458e-894a-74b78d68940c">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_GARANTIA}.equals("S")?"X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="19" y="17" width="9" height="9" uuid="86718cbd-93fd-473a-a2fa-83498cb45e6d">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_GARANTIA}.equals("N") && $F{OS_INTERNO}.equals("N") ?"X":" "]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" x="387" y="0" width="167" height="194" isPrintWhenDetailOverflows="true" uuid="aca76750-b095-47ae-9273-a90152704095">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement x="5" y="181" width="158" height="11" uuid="894c3e13-1d68-4adb-95c0-b386f94bded1"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Cód. Fab. Bateria: " + $F{OS_COD_FAB_BATERIA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="49" y="4" width="68" height="11" uuid="c2882f1e-7ec5-4d2e-8918-0ab50fcd2e8c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Dados do Veículo]]></text>
				</staticText>
				<textField>
					<reportElement x="5" y="16" width="158" height="11" uuid="4aa7514d-0bdb-491d-88bd-e3cc9364ee1a"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Modelo: " + $F{DESC_PROD_MOD}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="27" width="158" height="11" uuid="d0e4e6d9-457d-4311-affe-8292bf819d80"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Placa: " + $F{OS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="38" width="158" height="11" uuid="3be323ed-6fc9-4bab-aab1-1defd2d4ec7f"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Nº Chassi: " + $F{OS_CHASSI}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="60" width="158" height="11" uuid="bd433340-1c9c-4d84-9111-cbdd52636c1b">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Motor: " + $F{OS_NUMERO_MOTOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="71" width="158" height="11" uuid="8aebb44b-cd26-4103-861f-223e251869cb"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Ano Fabr.: " + $F{OS_ANO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="82" width="158" height="11" uuid="ac0ad7bf-8e35-43a4-9cbe-3da83c99c676"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Cor: " + $F{OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="115" width="158" height="11" uuid="c3a5fe85-2d82-46de-ba8a-d760cebf2b50"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Data Venda: " + $F{OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="126" width="158" height="11" uuid="9007a9bb-a84b-4ccc-8056-e7a7ba38a483"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Conc. Vendedora: " + $F{CONCESSIONARIA_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="137" width="158" height="11" uuid="1ea0253d-1ea2-41d3-890d-3e0a62a8fa96"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Ultima Conc. Execultante: "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="148" width="158" height="11" uuid="bb4119a4-e159-45a9-8861-0b8c8d4851e7"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Prisma: " + $F{OS_PRISMA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="159" width="158" height="11" uuid="d816bb82-75bd-4f86-bd9a-192aa1a80006"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Consultor Tecnico: " + $F{OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="170" width="158" height="11" uuid="c34daef9-d140-4be5-b1c2-2f1cb3b44030"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Data Fab. Bateria: " + $F{OS_DATA_FAB_BATERIA}]]></textFieldExpression>
				</textField>
				<textField pattern="#,###">
					<reportElement x="20" y="93" width="143" height="11" uuid="629a9e22-1c16-4eef-90ee-1f0a4882e468"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_KM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="5" y="93" width="15" height="11" uuid="6fbf5884-e8de-44e6-a6e4-c40b7302cd4c"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[KM:]]></text>
				</staticText>
				<textField>
					<reportElement x="5" y="104" width="158" height="11" uuid="551d15a8-fb5e-40cd-b169-1d436aee0b61"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_HORIMETRO}==null
? "Horímetro: "
: "Horímetro: " + $F{OS_HORIMETRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="5" y="49" width="158" height="11" uuid="7a17da93-9687-406b-9e54-a45e7245adb3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_FROTA} == null ? "N° Frota: " : "N° Frota: " + $F{OS_NUMERO_FROTA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="70" width="387" height="399" isPrintInFirstWholeBand="true" uuid="804dd823-9e6b-49e0-8905-1bf28a88bcc8">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="ShowOutOfBoundContent" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport isUsingCache="false" overflowType="Stretch">
					<reportElement isPrintRepeatedValues="false" x="1" y="13" width="377" height="384" isPrintInFirstWholeBand="true" uuid="6a0869d8-a22b-4004-8a9c-cf29c653b7a1">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="GARANTIA_OS">
						<subreportParameterExpression><![CDATA[$F{OS_GARANTIA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="INTERNO_OS">
						<subreportParameterExpression><![CDATA[$F{OS_INTERNO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS_FABRICA">
						<subreportParameterExpression><![CDATA[$F{OS_NUMERO_OS_FABRICA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="STATUS_OS">
						<subreportParameterExpression><![CDATA[$F{OS_STATUS_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHondaCarroSubServicos.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<frame>
				<reportElement x="387" y="205" width="163" height="264" isPrintInFirstWholeBand="true" uuid="f81100b4-944c-4a8a-a348-bf080122a301">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<pen lineColor="#F76D6A"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement isPrintRepeatedValues="false" x="0" y="0" width="156" height="264" uuid="b46d2035-1b83-44bc-8e3c-6752798b4199">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="ShowOutOfBoundContent" value="false"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
						<reportElement stretchType="ContainerBottom" x="0" y="0" width="149" height="264" isPrintWhenDetailOverflows="true" uuid="6c222a52-87d9-4226-ad7e-c7cbbb219f9d">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<subreportParameter name="NUMERO_OS">
							<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="COD_EMPRESA">
							<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
						</subreportParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHondaCarroSubPecas.jasper"]]></subreportExpression>
					</subreport>
				</frame>
			</frame>
			<staticText>
				<reportElement mode="Transparent" x="387" y="194" width="167" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="3fa2ba31-c449-4605-998a-dcf91d251b6d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Requisições de Peças]]></text>
			</staticText>
			<line>
				<reportElement x="554" y="205" width="1" height="263" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="c6a11353-98e4-4ccb-8d45-2f44e905704c">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="90" y="82" width="1" height="386" isPrintWhenDetailOverflows="true" uuid="716e7716-8382-4b63-8ae9-4ed06e4b97cf">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="40" y="82" width="1" height="386" isPrintWhenDetailOverflows="true" uuid="48170880-5f82-4e1d-871e-87779f4ad4bf">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement mode="Transparent" x="40" y="70" width="50" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="961d2576-1040-4675-8fb5-88dac0e83ec4">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[TMO]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="90" y="70" width="296" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="1fcaddc3-a31c-4010-bc3a-1d0d523df6a6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[TMO]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="70" width="40" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="3b0df3da-d373-465e-a680-57c438ed0c2c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Item]]></text>
			</staticText>
			<line>
				<reportElement x="387" y="205" width="1" height="263" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="6ce32a95-c163-4c77-acc4-36f064e968c4">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="0" y="-17" width="1" height="485" isPrintWhenDetailOverflows="true" uuid="f7ba563c-e46b-48e8-bd57-0fad1b93abbe">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="503" y="205" width="1" height="264" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b33f9099-4b6c-4374-a636-77ec2cea43f7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<frame>
				<reportElement x="0" y="469" width="288" height="81" isPrintWhenDetailOverflows="true" uuid="94479c24-cf66-4b46-9831-d12454fd030c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="4" y="7" width="237" height="11" isPrintWhenDetailOverflows="true" uuid="1b9f1e19-4cfa-4a25-b116-14ab941a8037">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Utilizar este campo em caso de serviço reembolsado pela Fabrica]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="23" width="275" height="11" isPrintWhenDetailOverflows="true" uuid="820d3233-b096-4eba-9507-3f2fcc95a2ec"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Nome: " + $F{FATURAR_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="34" width="275" height="11" isPrintWhenDetailOverflows="true" uuid="b2f4d1b3-b36b-4581-ab83-f5456691e9ee"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Endereço: " + $F{FATURAR_ENDERECO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="45" width="122" height="11" isPrintWhenDetailOverflows="true" uuid="60d97020-acc3-4ce6-b542-2881886abb89">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Cidade: " + $F{FATURAR_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="57" width="158" height="11" isPrintWhenDetailOverflows="true" uuid="44b2ad40-6bca-47f3-b26e-b2ba1495f1cf">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_FONE}.length() > 6 ? "Fone: " +  $F{FATURAR_FONE} :  "Fone: "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="126" y="46" width="152" height="11" isPrintWhenDetailOverflows="true" uuid="e5a20dca-ee28-49a2-a19d-79de8ad07a2d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Estado: " + $F{FATURAR_ESTADO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="164" y="57" width="115" height="11" isPrintWhenDetailOverflows="true" uuid="e207552f-76ba-406a-b24b-b50e80064538"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_TELEFONE_FAX}.length() > 6 ? "Fax: " +  $F{FATURAR_TELEFONE_FAX} : "Fax: "]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="550" width="288" height="80" isPrintWhenDetailOverflows="true" uuid="b5dbf32c-a6a4-421a-aaa4-e4ddf3c7192d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="10" y="8" width="263" height="29" isPrintWhenDetailOverflows="true" uuid="c10650b6-ba25-4f58-a80c-c78469cf74dd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Estou ciente das condições apresentadas nesta o.s. e que meu veículo foi deixado na concessionaria, conforme acima descrito no formulário "Recebimento de Veículo-Vistoria" apresentado em anexo a esta O.S.]]></text>
				</staticText>
				<staticText>
					<reportElement x="33" y="63" width="214" height="12" isPrintWhenDetailOverflows="true" uuid="55406881-047a-42ef-8c5f-5961cdfe29ad">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente]]></text>
				</staticText>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="33" y="37" width="214" height="25" uuid="a4a0d2ea-e7d6-4e40-9e81-b7ec67c25e85"/>
					<imageExpression><![CDATA[$F{ASSINATURA_ABERTURA}]]></imageExpression>
				</image>
			</frame>
			<frame>
				<reportElement x="288" y="469" width="266" height="161" isPrintWhenDetailOverflows="true" uuid="b909a5a5-0738-4fdf-8357-30b6864e5667">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="266" height="13" isPrintWhenDetailOverflows="true" uuid="f75f94bf-62ac-4598-99e9-d3ff2c350979">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Despesas]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="14" width="67" height="11" isPrintWhenDetailOverflows="true" backcolor="#E0E0E0" uuid="2de84f7f-0663-4819-9b18-02aba4c83be0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[M.O.-Revisão]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="67" y="14" width="67" height="11" isPrintWhenDetailOverflows="true" backcolor="#E0E0E0" uuid="5f66d67d-db16-4fc4-9b3c-c5f19acfe52e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_REVISAO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="67" y="25" width="67" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="90de3770-1bd7-44ca-bcf6-05aca91fbd5c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_MECANICA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="25" width="67" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="a2b744e6-333c-4c39-a125-f8527fd86043">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[M.O.-Mecânica]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="36" width="67" height="11" isPrintWhenDetailOverflows="true" backcolor="#E0E0E0" uuid="4f270d4d-7fd3-4e93-950b-e859680d001e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[M.O.-Funilaria]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="67" y="47" width="67" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="48d1d1db-728a-4361-ac0b-102907f41f7c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_LAVAGEM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="47" width="67" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="8b2ebb92-9918-4075-a9db-76096bb2562a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[M.O.-Pintura]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="67" y="36" width="67" height="11" isPrintWhenDetailOverflows="true" backcolor="#E0E0E0" uuid="7c9f4ac3-e878-4aa0-953e-227c5623ecf1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_TERCEIROS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="59" width="67" height="11" isPrintWhenDetailOverflows="true" backcolor="#E0E0E0" uuid="b32169a8-c058-4bf7-819b-0fab12a8b4ac">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Serv. Terceiros]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="67" y="70" width="67" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="b89f447e-13b3-4562-a55d-884a06314dcd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_LAVAGEM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="0" y="70" width="67" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="b0a16433-d848-4dc4-a042-a8c9735af6a7">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Lavagem]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="67" y="59" width="67" height="11" isPrintWhenDetailOverflows="true" backcolor="#E0E0E0" uuid="4a7ef4bd-b39e-446e-af6f-66811950e7cf">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_SERVICOS_VAL_TERCEIROS}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="201" y="14" width="64" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="0f3e7fa9-48a1-48ea-840c-505c45c4fdf7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_PECAS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="134" y="14" width="67" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="7e83524a-47d3-44d4-a302-9735cc07665e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Peças]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="134" y="25" width="67" height="11" isPrintWhenDetailOverflows="true" uuid="4ae5d61e-02d5-4b6f-8848-856bc7ed389f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Acessórios]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="201" y="47" width="64" height="11" isPrintWhenDetailOverflows="true" uuid="2609cced-70be-441c-8293-427f2da5af42">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_LUBRIFICANTE}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="201" y="59" width="64" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="ff721c45-80e3-4b39-9278-52063f2e476c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_OUTROS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="134" y="59" width="67" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="9455d2e9-6cfd-460a-88f9-1ee721c71f32">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Outros]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="201" y="36" width="64" height="11" isPrintWhenDetailOverflows="true" uuid="3047f38e-09e4-410a-81e1-3f7cb5db3d73">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_COMBUSTIVEL}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="134" y="36" width="67" height="11" isPrintWhenDetailOverflows="true" uuid="98bded39-6637-4d00-aebe-c40371177137">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Combustível]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="201" y="25" width="64" height="11" isPrintWhenDetailOverflows="true" uuid="9fa0a2d8-af5e-406c-b081-ee9bcd81a75c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_ACESSORIOS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="134" y="47" width="67" height="11" isPrintWhenDetailOverflows="true" uuid="5c3a0315-5717-425a-b714-2180cb9757ec">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Lubrificantes]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="134" y="70" width="67" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="2caf4b3c-d378-4c36-bfe5-5cb482f89d16">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Transparent" x="201" y="70" width="64" height="11" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="98704e1e-6ba2-4775-82c5-e64e433d76e1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="134" y="81" width="1" height="79" isPrintWhenDetailOverflows="true" uuid="3798579e-3329-4c26-ad3a-fbe45712fd5c"/>
				</line>
				<staticText>
					<reportElement x="32" y="88" width="80" height="12" isPrintWhenDetailOverflows="true" uuid="721a9feb-1a5a-44f7-95f1-0a830eab098e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="19"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Caixa]]></text>
				</staticText>
				<staticText>
					<reportElement x="32" y="100" width="80" height="12" isPrintWhenDetailOverflows="true" uuid="8137444e-6675-4f55-bfca-b96eb63b6694">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[____/____/________]]></text>
				</staticText>
				<staticText>
					<reportElement x="8" y="141" width="119" height="12" isPrintWhenDetailOverflows="true" uuid="5d6af2ca-77eb-40f6-9362-7333ac6dee33">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Visto]]></text>
				</staticText>
				<staticText>
					<reportElement x="161" y="83" width="80" height="12" isPrintWhenDetailOverflows="true" uuid="d344c46c-dfd3-4d13-87e1-aee27df89bea">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Produto Retirado em]]></text>
				</staticText>
				<staticText>
					<reportElement x="161" y="95" width="80" height="12" isPrintWhenDetailOverflows="true" uuid="3ff7a6cb-84c1-4d68-9773-56279f733592">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[____/____/________]]></text>
				</staticText>
				<staticText>
					<reportElement x="140" y="142" width="119" height="12" isPrintWhenDetailOverflows="true" uuid="c9612b47-ba80-4688-81bf-22899b5a196e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente]]></text>
				</staticText>
				<textField pattern="  dd     MM     yyyy">
					<reportElement x="165" y="95" width="71" height="12" isPrintWhenDetailOverflows="true" uuid="48f030a1-e32c-4c25-9e45-776b8e94c7a9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_ENTREGA} != null ? $F{OS_DATA_ENTREGA}
:$F{ASSINATURA_ENTREGA_DATA} != null  ? $F{ASSINATURA_ENTREGA_DATA}
:$P{ASSINAR_DIGITALMENTE}.equals("S") ? $F{EMPRESA_DATA_ATUAL} 
: $F{OS_DATA_ENTREGA}]]></textFieldExpression>
				</textField>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" isUsingCache="false">
					<reportElement x="140" y="123" width="119" height="19" uuid="bcdb1f2e-3a72-4fe4-bddd-f58bbfe93cd4">
						<printWhenExpression><![CDATA[new Boolean($P{ASSINAR_DIGITALMENTE}.equals("N"))]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[$F{ASSINATURA_ENTREGA}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="141" y="129" width="93" height="18" forecolor="#FFFFFF" uuid="570cf9db-e683-4d34-8b8e-5456e834fca2"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font size="4"/>
					</textElement>
					<text><![CDATA[#CLIENTE]]></text>
				</staticText>
			</frame>
		</band>
		<band height="69" splitType="Stretch">
			<frame>
				<reportElement stretchType="ContainerBottom" isPrintRepeatedValues="false" x="0" y="0" width="554" height="68" isPrintInFirstWholeBand="true" uuid="0ce17b05-be55-4ffe-a636-e1d7c0365388">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="ShowOutOfBoundContent" value="true"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isStretchWithOverflow="true" isBlankWhenNull="false">
					<reportElement x="5" y="1" width="536" height="67" isPrintInFirstWholeBand="true" uuid="5f52f40f-ac4b-48c7-851e-f0d3ce52d4aa">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TERMO_TEXTO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<pageFooter>
		<band height="50">
			<frame>
				<reportElement stretchType="ContainerBottom" isPrintRepeatedValues="false" x="0" y="0" width="554" height="50" isPrintInFirstWholeBand="true" uuid="8af1d95b-2d1f-4905-9a74-165fe3bbc529">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="ShowOutOfBoundContent" value="true"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement x="28" y="4" width="346" height="11" uuid="b751d7d6-d008-4eee-8ae8-bd178d4eeaaa">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="5" y="4" width="23" height="11" uuid="7c745e52-c77b-4a5a-b2c8-c8b00327bbe5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<staticText>
					<reportElement x="378" y="4" width="20" height="11" uuid="cb5f8cd0-6917-4961-a69a-539d9068f93b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº]]></text>
				</staticText>
				<textField>
					<reportElement x="398" y="4" width="146" height="11" uuid="00aabe4f-e164-4a90-b027-b5dbc7af8382">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="5" y="15" width="368" height="11" uuid="08f4bd15-da05-44d4-a159-6c69e9b57468">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Prezado cliente: apresente este comprovante para a retirada de seu veículo.]]></text>
				</staticText>
				<textField>
					<reportElement x="378" y="15" width="167" height="11" uuid="deeeb498-b53c-4e40-aa07-2502b29353f9"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Placa: " + $F{OS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="101" y="26" width="151" height="11" uuid="a8abe599-33c9-4521-bd76-ed46b7431691"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="5" y="26" width="96" height="11" uuid="a84dc183-d652-4a74-863b-4c5abb0ba585">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Concessionaria: ]]></text>
				</staticText>
				<textField>
					<reportElement x="252" y="26" width="122" height="11" uuid="4eb364bf-6f48-435c-b2bc-bc7c02069871">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_FONE}.length() > 6 ?  $F{EMPRESAS_FONE}: " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="378" y="26" width="166" height="11" uuid="b92ecb26-cac8-4c11-9c9c-d2555804d39c"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Prisma Nº/Cor: " + $F{OS_PRISMA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="5" y="37" width="82" height="11" uuid="8d4feba2-d229-4e1d-b0ba-cfe470ce6301">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Previsão de Entrega:]]></text>
				</staticText>
				<textField>
					<reportElement x="87" y="37" width="286" height="11" uuid="a394edae-c81e-45d7-83f8-ba9ca8121418">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_PROMETIDA} +" "+$F{OS_HORA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="378" y="37" width="166" height="11" uuid="0b06637f-ac3d-4fad-9b95-a4d1fdba7fde"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Consultor Tecnico: " + $F{OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
