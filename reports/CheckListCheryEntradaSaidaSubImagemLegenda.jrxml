<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListCheryEntradaSaidaSubImagemLegenda" columnCount="5" pageWidth="300" pageHeight="14" whenNoDataType="BlankPage" columnWidth="60" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select cod_empresa, nome from empresas e where e.cod_empresa in (1,2,3)]]>
		</queryString>
		<field name="COD_EMPRESA" class="java.lang.Double"/>
		<field name="NOME" class="java.lang.String"/>
	</subDataset>
	<queryString language="SQL">
		<![CDATA[SELECT   I.ID_ICONE,
         I.DESCRICAO,
         I.ICONE
FROM     ICONE_PADRAO I
WHERE ROWNUM <= 5]]>
	</queryString>
	<field name="ID_ICONE" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="ICONE" class="java.awt.Image"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="14" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="1" width="60" height="12" uuid="98e3a3fc-a687-48e5-a433-1e53d3afce84"/>
				<textField>
					<reportElement style="field_null" mode="Transparent" x="13" y="0" width="47" height="12" forecolor="#000000" uuid="88f8c2b6-6cac-406c-843b-fc62406fd9d3">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
				</textField>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle" isUsingCache="false" onErrorType="Blank">
					<reportElement x="1" y="0" width="12" height="12" uuid="f69d414c-a6c6-4820-b609-23a1a33ed744"/>
					<imageExpression><![CDATA[$F{ICONE}]]></imageExpression>
				</image>
			</frame>
		</band>
	</detail>
</jasperReport>
