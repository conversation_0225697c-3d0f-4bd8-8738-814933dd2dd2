<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.19.0.final using JasperReports Library version 6.19.0-646c68931cebf1a58bc65c4359d1f0ca223c5e94  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsMercedesSubServicos" pageWidth="386" pageHeight="842" columnWidth="386" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[select COD_EMPRESA, NOME, LOCAL, CONTATO from empresas ]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="COD_EMPRESA"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_EMPRESA"/>
	</field>
	<field name="NOME" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="NOME"/>
		<property name="com.jaspersoft.studio.field.label" value="NOME"/>
	</field>
	<field name="LOCAL" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="LOCAL"/>
		<property name="com.jaspersoft.studio.field.label" value="LOCAL"/>
	</field>
	<field name="CONTATO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="CONTATO"/>
		<property name="com.jaspersoft.studio.field.label" value="CONTATO"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="12">
			<staticText>
				<reportElement mode="Transparent" x="0" y="0" width="40" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="4fb8aac7-7d66-46c8-b20e-d82fd9e69324">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Item]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="40" y="0" width="50" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="fa50b813-4453-4da6-a481-689107b19efa">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[TMO]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="90" y="0" width="296" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#E0E0E0" uuid="02e6aca8-73f4-4ee0-874a-8850ece949f7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[TMO]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="0" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="a86762f7-1fc5-4063-a453-7a278342d176">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_EMPRESA}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Opaque" x="40" y="0" width="50" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="9a1539a4-cf61-4bf1-9aca-6fbe5bb93e5b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CONTATO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="90" y="0" width="239" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="44e19063-4635-472d-8b71-6d28f14fcd1c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="329" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="f629ccce-70d0-43e6-802e-912d34e24d1a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LOCAL}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
