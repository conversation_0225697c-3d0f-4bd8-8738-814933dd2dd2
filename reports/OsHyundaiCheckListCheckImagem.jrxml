<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiCheckListCheckImagem" pageWidth="555" pageHeight="240" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select 1 FROM DUAL]]>
		</queryString>
		<field name="1" class="java.lang.Boolean"/>
	</subDataset>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\28704\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_SEGMENTO" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT OS.NUMERO_OS,
       OS.COD_EMPRESA,
       MOB_OS_IMAGEM.IMAGEM,
       MOB_OS_IMAGEM.IMAGEM_ORIGINAL,
       (SELECT a.imagem
          FROM mob_imagem a
         WHERE EXISTS (SELECT 1
                  FROM mob_imagem_filtro mif
                 WHERE mif.cod_empresa = $P{COD_EMPRESA}
                   AND mif.cod_segmento = $P{COD_SEGMENTO}
                   AND mif.id_imagem = a.id_imagem)and rownum <2) AS IMAGEM_CARROCERIA_DEFAULT
  FROM MOB_OS_IMAGEM, OS
 WHERE OS.NUMERO_OS = $P{NUMERO_OS}
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND MOB_OS_IMAGEM.APLICACAO(+) = 'R'
   AND MOB_OS_IMAGEM.NUMERO_OS(+) = OS.NUMERO_OS
   AND MOB_OS_IMAGEM.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND MOB_OS_IMAGEM.IMAGEM IS NOT NULL]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="IMAGEM" class="java.awt.Image"/>
	<field name="IMAGEM_ORIGINAL" class="java.lang.String"/>
	<field name="IMAGEM_CARROCERIA_DEFAULT" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="225" splitType="Stretch">
			<frame>
				<reportElement mode="Transparent" x="0" y="0" width="555" height="225" uuid="e0867b21-8513-419c-9b5e-28dbf78fa78f">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="18" y="24" width="330" height="190" uuid="b26b8ef6-5ee5-4d9a-a634-7f0495f5e4bb">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{IMAGEM}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="71" y="2" width="403" height="18" forecolor="#000000" uuid="aeaa4419-76aa-4e63-8f01-e147a59cef4f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[Carroceria]]></text>
				</staticText>
				<subreport>
					<reportElement x="359" y="29" width="180" height="180" uuid="5327baef-9485-4416-a55a-d8f3f86671ac"/>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListChecKObservacaoImagem.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
		<band height="15">
			<frame>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="15" backcolor="#E3DCDC" uuid="376e45b8-bdef-4376-bf75-76f4808d93f7">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport>
					<reportElement x="92" y="0" width="425" height="15" uuid="c45fa1a0-c719-4d8e-8d7b-0af040c97355">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListCheckLegendasImagem.jasper"]]></subreportExpression>
				</subreport>
				<staticText>
					<reportElement x="30" y="0" width="62" height="15" uuid="0e35f608-7e34-4bb4-bb8f-e75861d3ec3d">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<text><![CDATA[Legenda:]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
