<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiCheckListChecKCheckListSaida" pageWidth="275" pageHeight="842" columnWidth="275" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="Items" uuid="fce13576-eeb4-444f-a66e-099024b8e559">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
		<parameter name="NUMERO_OS" class="java.lang.Double">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<parameter name="COD_EMPRESA" class="java.lang.Double"/>
		<parameter name="ID_GRUPO" class="java.lang.Double"/>
		<queryString language="SQL">
			<![CDATA[select pi.id_grupo,
       pi.cod_item,
       pi.descricao as descricao_item,
       pi.resposta_eh_observacao,
       mp.observacao,
       mo.descricao as descricao_opcao
  from mob_pertence_item pi, mob_os_pertence mp, mob_opcao mo
 where pi.id_grupo = $P{ID_GRUPO}
   and pi.cod_item = mp.cod_item(+)
   and mo.id_opcao(+) = mp.id_opcao
   and mp.numero_os(+) = $P{NUMERO_OS}
   and mp.cod_empresa(+) = $P{COD_EMPRESA}
   order by pi.cod_item]]>
		</queryString>
		<field name="ID_GRUPO" class="java.lang.Double"/>
		<field name="COD_ITEM" class="java.lang.Double"/>
		<field name="DESCRICAO_ITEM" class="java.lang.String"/>
		<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
		<field name="OBSERVACAO" class="java.lang.String"/>
		<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	</subDataset>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[115582.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="OBRIGATORIO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[with DADOS as (
select info.cod_empresa as COD_EMPRESA,
       info.cod_produto as COD_PRODUTO,
       info.cod_modelo as COD_MODELO,
       info.tipo_os as TIPO_OS,
       info.cod_segmento as COD_SEGMENTO
  from (select os.cod_empresa,dv.Cod_Produto, dv.cod_modelo, os.tipo as tipo_os, produtos.cod_segmento
          from os, os_dados_veiculos dv, produtos
         where os.numero_os = dv.numero_os
           and os.cod_empresa = dv.cod_empresa
           and os.cod_empresa = $P{COD_EMPRESA}
           and os.numero_os = $P{NUMERO_OS}
           and produtos.cod_produto = dv.cod_produto) info
),
TODOS_ITENS as ( 
SELECT A.ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */ 
       B.DESCRICAO DESCRICAO_GRUPO,
       B.ORDEM AS ORDEM_GRUPO  
FROM MOB_PERTENCE_ITEM A, MOB_PERTENCE_GRUPO B, MOB_OS_PERTENCE C, MOB_OPCAO D
WHERE A.ID_GRUPO = B.ID_GRUPO
 AND ((($P{OBRIGATORIO} = 'S') AND (A.OBRIGATORIO = $P{OBRIGATORIO})) OR ($P{OBRIGATORIO} = 'N'))
 AND B.TIPO = 'C'
 AND A.ATIVO = 'S'
 AND B.ATIVO = 'S'
 AND A.COD_ITEM = C.COD_ITEM (+)
 AND B.APLICACAO = 'E'
 AND C.COD_EMPRESA(+) = $P{COD_EMPRESA}
 AND C.NUMERO_OS(+) = $P{NUMERO_OS}
 AND C.ID_OPCAO = D.ID_OPCAO(+)

 ),
FILTRO_CRUZA_EPRESA_SEGMENTO AS ( 
SELECT * /* CAMADA 2 - FILTRO TODOS OS ITENS QUE SEJA DA RESPECTIVA EMPRESA OU NÃO POSSUA EMPRESA */
FROM TODOS_ITENS A
WHERE EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo
                     AND mc.cod_empresa = $P{COD_EMPRESA}
                     AND mc.cod_segmento = (SELECT COD_SEGMENTO FROM DADOS)) 
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo)                               
),
FILTRO_CRUZA_TIPO_OS AS ( 
SELECT * /* CAMADA 3 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO TIPO DE OS OU NÃO POSSUA TIPO DE OS VINCULADO */
FROM FILTRO_CRUZA_EPRESA_SEGMENTO A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_tp_os cto
                  WHERE  cto.id_grupo = a.id_grupo
                         AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                         AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS)
                         AND cto.tipo = (SELECT TIPO_OS FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_tp_os cto
              WHERE  cto.id_grupo = A.id_grupo
                     AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                     AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS))
),
FILTRO_CRUZA_MODELO AS ( 
SELECT * /* CAMADA 4 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO MODELO OU NÃO POSSUA MODELO VINCULADO */
FROM FILTRO_CRUZA_TIPO_OS A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_modelo cm
                  WHERE  cm.id_grupo = a.id_grupo
                         AND cm.cod_produto = (SELECT COD_PRODUTO FROM DADOS)
                         AND cm.cod_modelo = (SELECT COD_MODELO FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_modelo cm
              WHERE   cm.id_grupo = a.id_grupo)
)
select ID_GRUPO,  DESCRICAO_GRUPO from FILTRO_CRUZA_MODELO
GROUP BY ID_GRUPO, DESCRICAO_GRUPO
ORDER BY ID_GRUPO]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="24" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="275" height="24" uuid="4322e92e-9965-400f-8218-5c9254755c62">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<leftPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textField>
					<reportElement mode="Opaque" x="0" y="0" width="275" height="12" forecolor="#000000" backcolor="#C9C9C9" uuid="278e6753-43b1-4a83-88ea-52d5b7cb499f">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_GRUPO}]]></textFieldExpression>
				</textField>
				<componentElement>
					<reportElement x="0" y="12" width="275" height="12" uuid="f28f70f6-490c-401d-9d2b-b5d4865b8cea">
						<property name="net.sf.jasperreports.export.headertoolbar.table.name" value=""/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
						<datasetRun subDataset="Items" uuid="36b353d9-455c-473a-a3b5-be0d6c87b477">
							<datasetParameter name="NUMERO_OS">
								<datasetParameterExpression><![CDATA[$P{NUMERO_OS}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="COD_EMPRESA">
								<datasetParameterExpression><![CDATA[$P{COD_EMPRESA}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ID_GRUPO">
								<datasetParameterExpression><![CDATA[$F{ID_GRUPO}]]></datasetParameterExpression>
							</datasetParameter>
							<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						</datasetRun>
						<jr:listContents height="12" width="275">
							<frame>
								<reportElement x="0" y="0" width="275" height="12" uuid="abadaf91-689c-4f13-8a09-8fb9eca6f288"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField>
									<reportElement x="0" y="0" width="155" height="12" uuid="4a095cf9-ed50-4e2d-9a91-d828d5128a48">
										<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
										<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
									</reportElement>
									<box leftPadding="3">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Left" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true">
									<reportElement x="155" y="0" width="120" height="12" uuid="c131a945-f493-4915-aa98-03de6ca723a1">
										<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
									</reportElement>
									<box leftPadding="3" rightPadding="3">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")? $F{OBSERVACAO} : $F{DESCRICAO_OPCAO}]]></textFieldExpression>
								</textField>
							</frame>
						</jr:listContents>
					</jr:list>
				</componentElement>
			</frame>
		</band>
	</detail>
</jasperReport>
