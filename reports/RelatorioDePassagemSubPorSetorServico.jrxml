<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="emBranco" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<parameter name="TOTAL_PASSAGENS" class="java.lang.Double"/>
	<parameter name="QUERY_DINAMICA" class="java.lang.String">
		<defaultValueExpression><![CDATA["1 = 1"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH LISTADEOS AS
 (SELECT DISTINCT (OS.NUMERO_OS), OS.TIPO, OS.ORCAMENTO, OS.COD_EMPRESA
    FROM OS, OS_TIPOS
   WHERE OS.TIPO = OS_TIPOS.TIPO
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND $P!{QUERY_DINAMICA}
   ORDER BY OS.NUMERO_OS)
   
 SELECT SETOR, DESCRICAO_SETOR, COUNT(DISTINCT(NUMERO_OS)) AS PASSAGENS
 FROM SERVICOS_SETORES,  
        (    
          SELECT LISTADEOS.NUMERO_OS,  
                 NVL(SERVICOS.COD_SETOR, 1) AS SETOR  
          FROM LISTADEOS,  
                 OS_SERVICOS,  
                 SERVICOS  
           WHERE OS_SERVICOS.COD_EMPRESA (+) = LISTADEOS.COD_EMPRESA  
             AND OS_SERVICOS.NUMERO_OS (+)   = LISTADEOS.NUMERO_OS  
             AND SERVICOS.COD_SERVICO (+)    = OS_SERVICOS.COD_SERVICO  
        AND SERVICOS.COD_SETOR IS NOT NULL  
             ORDER BY LISTADEOS.NUMERO_OS  
        )                               
  WHERE SETOR = SERVICOS_SETORES.COD_SETOR  
  GROUP BY SETOR, DESCRICAO_SETOR]]>
	</queryString>
	<field name="SETOR" class="java.lang.String"/>
	<field name="DESCRICAO_SETOR" class="java.lang.String"/>
	<field name="PASSAGENS" class="java.lang.Double"/>
	<variable name="TOTAL_PASSAGENS" class="java.lang.Double" resetType="None" incrementType="Page" calculation="Sum">
		<variableExpression><![CDATA[$F{PASSAGENS}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="29">
			<frame>
				<reportElement x="0" y="0" width="555" height="29" uuid="a1c1fac3-4b4c-427a-97a6-69a5b813e28c">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="0" y="14" width="1" height="15" uuid="6045c3f9-6d1f-4713-91b1-653b1c334e27"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="1" width="196" height="12" uuid="7b249c37-fe7c-4769-8c6e-366f0d9f5687"/>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Passagens por Setor de Serviços]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="256" y="17" width="94" height="12" uuid="fb426e8a-4940-4f23-a5ee-b67838a9229f"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total de Passagens]]></text>
				</staticText>
				<line>
					<reportElement x="1" y="14" width="440" height="1" uuid="72896351-ad4b-4ceb-b02c-cb78a997a5fc"/>
				</line>
				<line>
					<reportElement x="252" y="14" width="1" height="15" uuid="afc3226d-c0e7-4587-b66b-6c0bb141b41f"/>
				</line>
				<line>
					<reportElement x="351" y="14" width="1" height="15" uuid="4e751f8d-d6d5-4339-8ded-e29128836130"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="17" width="231" height="12" uuid="30cd2aa8-d20a-435b-9052-c8a6896c534d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Setor de Serviços]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="356" y="17" width="82" height="12" uuid="693a7ef8-1cd6-4c6c-9e0a-2656420a9f42"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[%]]></text>
				</staticText>
				<line>
					<reportElement x="442" y="14" width="1" height="15" uuid="a0e03e4e-f960-4316-b4f8-f2d936d11979"/>
				</line>
				<line>
					<reportElement x="0" y="28" width="441" height="1" uuid="724a0965-07e1-47bc-82c7-ff9e38c00b03"/>
				</line>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="14" uuid="1743b4a8-a48a-4b82-80c4-b17e46944eb1">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="2" y="0" width="231" height="14" uuid="363b6d3b-94bb-435a-93e9-bd8b655314e7"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_SETOR}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement mode="Transparent" x="256" y="0" width="38" height="14" uuid="9ee0e1a8-4a8f-4ca5-be3c-56bd73bea7c6"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PASSAGENS}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement mode="Transparent" x="356" y="0" width="30" height="14" uuid="79e7b817-7a23-47a9-98fb-491980940e01"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PASSAGENS} * 100 / $P{TOTAL_PASSAGENS}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="1" width="1" height="12" uuid="e6e02df1-fdae-4231-805d-0d7f20bd20e1"/>
				</line>
				<line>
					<reportElement x="252" y="1" width="1" height="12" uuid="11ba8306-a07f-4e72-bcbf-8c48b08d2988"/>
				</line>
				<line>
					<reportElement x="351" y="1" width="1" height="12" uuid="bb7e43ce-4094-46f3-a2a5-eb878172208e"/>
				</line>
				<line>
					<reportElement x="442" y="1" width="1" height="12" uuid="a94f53df-2b55-447c-a5d5-56d15a78dcc5"/>
				</line>
				<line>
					<reportElement x="0" y="13" width="443" height="1" uuid="84ad517d-4315-4dcf-8749-d162209b6b4a"/>
				</line>
			</frame>
		</band>
	</detail>
	<summary>
		<band height="55">
			<frame>
				<reportElement x="0" y="0" width="555" height="55" uuid="2c7e5018-3faf-4cbd-baae-0ec5374d5bdf">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="0" y="55" width="442" height="1" uuid="65232b49-af9b-4ada-963f-9fda370702ed"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="1" width="37" height="13" uuid="69bb6d3b-8e64-4dcc-8ce0-373e1922c29a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Atenção:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="41" y="0" width="398" height="14" uuid="5ea4f6ff-**************-72ef5443c7e9"/>
					<textElement textAlignment="Justified">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Pode haver diferença no total de Passagens por Setor de Serviços ao compará-lo com os demais totais.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="14" width="37" height="13" uuid="e2204039-47cb-4d58-bf82-d4861153198e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Motivos:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="41" y="13" width="398" height="24" uuid="90af34e1-eb71-454c-9172-e393ec0b979d"/>
					<textElement textAlignment="Justified">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[- 1 O.S. pode ter 2 serviços, sendo o 1º serviço do setor A e o 2º serviço do setor B, fazendo com que sejam contadas 2 passagens, apesar de ser apenas 1 O.S.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="41" y="37" width="398" height="14" uuid="*************-4701-9bc4-5992ab39e83f"/>
					<textElement textAlignment="Justified">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[- 1 O.S. pode ter 2 serviços, sendo ambos do mesmo setor, fazendo com que seja contada 1 passagem. ]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="0" width="1" height="55" uuid="2eb599f0-c89b-4858-a6a8-cd04bd8b1317"/>
				</line>
				<line>
					<reportElement x="442" y="0" width="1" height="56" uuid="3b125344-341d-4adb-ba7b-efed2b70115e"/>
				</line>
			</frame>
		</band>
	</summary>
</jasperReport>
