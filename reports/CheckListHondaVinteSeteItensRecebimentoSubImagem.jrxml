<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaVinteSeteItensRecebimentoSubImagem" pageWidth="575" pageHeight="194" whenNoDataType="NoPages" columnWidth="575" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select 1 FROM DUAL]]>
		</queryString>
		<field name="1" class="java.lang.Boolean"/>
	</subDataset>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\30874\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_SEGMENTO" class="java.lang.Double"/>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT OS.NUMERO_OS,
       OS.COD_EMPRESA,
       MOB_OS_IMAGEM.IMAGEM,
       MOB_OS_IMAGEM.IMAGEM_ORIGINAL,
       (SELECT a.imagem
          FROM mob_imagem a
         WHERE EXISTS (SELECT 1
                  FROM mob_imagem_filtro mif
                 WHERE mif.cod_empresa = $P{COD_EMPRESA}
                   AND mif.cod_segmento = $P{COD_SEGMENTO}
                   AND mif.id_imagem = a.id_imagem)and rownum <2) AS IMAGEM_CARROCERIA_DEFAULT,
        MOB_OS_ASSINATURA.ASSINATURA AS ASSINATURA_CONSULTOR,
        MOB_OS_ASSINATURA.ASSINATURA_CLIENTE AS ASSINATURA_CLIENTE,
        OS_DADOS_VEICULOS.COMBUSTIVEL,
        NVL(OS_AGENDA.SERVICO_EXPRESSO,'N') AS SERVICO_EXPRESSO,
        NVL((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) + (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS), 0)   AS OS_TOTAL_OS
  FROM MOB_OS_IMAGEM, OS, MOB_OS_ASSINATURA, OS_DADOS_VEICULOS, OS_AGENDA
 WHERE OS.NUMERO_OS = $P{NUMERO_OS}
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND MOB_OS_IMAGEM.APLICACAO(+) = 'R'
   AND MOB_OS_IMAGEM.NUMERO_OS(+) = OS.NUMERO_OS
   AND MOB_OS_IMAGEM.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND MOB_OS_IMAGEM.IMAGEM(+) IS NOT NULL
   AND MOB_OS_ASSINATURA.NUMERO_OS(+) = OS.NUMERO_OS 
   AND MOB_OS_ASSINATURA.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND MOB_OS_ASSINATURA.APLICACAO(+) = 'R'
   AND OS_DADOS_VEICULOS.NUMERO_OS(+) = OS.NUMERO_OS 
   AND OS_DADOS_VEICULOS.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND OS.COD_OS_AGENDA  =  OS_AGENDA.COD_OS_AGENDA(+)
   AND OS.COD_EMPRESA  =  OS_AGENDA.COD_EMPRESA(+)]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="IMAGEM" class="java.awt.Image"/>
	<field name="IMAGEM_ORIGINAL" class="java.io.InputStream"/>
	<field name="IMAGEM_CARROCERIA_DEFAULT" class="java.io.InputStream"/>
	<field name="ASSINATURA_CONSULTOR" class="java.awt.Image"/>
	<field name="ASSINATURA_CLIENTE" class="java.awt.Image"/>
	<field name="COMBUSTIVEL" class="java.lang.Double"/>
	<field name="SERVICO_EXPRESSO" class="java.lang.String"/>
	<field name="OS_TOTAL_OS" class="java.lang.Double"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<detail>
		<band height="194" splitType="Stretch">
			<frame>
				<reportElement mode="Transparent" x="0" y="0" width="575" height="194" uuid="0943d055-0527-448f-8bde-56933017f326">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="19" y="7" width="388" height="99" uuid="85e0f6f1-991c-428f-9f1e-34baccd36029"/>
					<imageExpression><![CDATA[$F{IMAGEM} == null ? $P{DIR_IMAGE_LOGO} + "crmservice342013.png" : $F{IMAGEM}]]></imageExpression>
				</image>
				<subreport>
					<reportElement x="19" y="113" width="552" height="36" uuid="6a4aa566-1782-47fc-876d-0456e99ab592">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListHondaVinteSeteItensRecebimentoSubImagemObservacao.jasper"]]></subreportExpression>
				</subreport>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="425" y="64" width="140" height="28" uuid="5e166496-aa92-47ed-9e74-dc36acaba076">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{COMBUSTIVEL} < 19 ? $P{DIR_IMAGE_LOGO}+"crmservice342014.png" :
$F{COMBUSTIVEL} < 39 ? $P{DIR_IMAGE_LOGO}+"crmservice342017.png" :
$F{COMBUSTIVEL} < 59 ?$P{DIR_IMAGE_LOGO}+"crmservice342018.png"  :
$F{COMBUSTIVEL} < 79 ? $P{DIR_IMAGE_LOGO}+"crmservice342019.png" :
$P{DIR_IMAGE_LOGO}+"crmservice342020.png"]]></imageExpression>
				</image>
				<subreport overflowType="NoStretch">
					<reportElement x="434" y="20" width="120" height="39" uuid="f51ad9b2-8427-4f42-8bfb-274bf8ae013d">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListHondaVinteSeteItensRecebimentoSubImagemLegendas.jasper"]]></subreportExpression>
				</subreport>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement x="59" y="153" width="92" height="9" uuid="d33db79c-114c-41ba-9db0-ecbcf6b18051">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="32" y="153" width="24" height="9" uuid="9825b451-8553-4f14-a2c5-0944d2fc3f8b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[N° OS:]]></text>
				</staticText>
				<staticText>
					<reportElement x="212" y="153" width="37" height="9" uuid="338fc141-bb00-453e-9897-713ae29fdef7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[OFICINA]]></text>
				</staticText>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle">
					<reportElement x="275" y="152" width="89" height="21" uuid="1003ccb9-f6c4-4aea-aabd-bc0c550076f7">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO}+"crmservice342016.png"]]></imageExpression>
				</image>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement x="476" y="153" width="88" height="9" uuid="2d0b145b-954d-45a1-ae2f-fdf6b6ba9fab">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<bottomPen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="397" y="153" width="79" height="9" uuid="29ffddd7-5ed3-461a-a846-159da05a8493">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[VALOR ORÇ. (PRÉVIO): R$]]></text>
				</staticText>
				<staticText>
					<reportElement x="30" y="181" width="128" height="9" uuid="c3160549-7245-454d-93d4-923ff2a8da02">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="58" y="164" width="72" height="16" uuid="9be47598-c052-4000-a7ae-6a83a71e48b1">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{ASSINATURA_CLIENTE}]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="434" y="181" width="128" height="9" uuid="60346dd4-468d-46a3-8dfc-c0e68ed20bd8">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[Assinatura do Consultor / Vistoriador]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="464" y="164" width="72" height="16" uuid="73d73b98-6935-456d-aa15-12b5ac517351">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{ASSINATURA_CONSULTOR}]]></imageExpression>
				</image>
				<textField>
					<reportElement x="259" y="154" width="14" height="7" uuid="39e46401-88bf-4b0e-9262-05b70b9b6fc0">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SERVICO_EXPRESSO}.equals("S")?"X":""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="197" y="154" width="14" height="7" uuid="6f308f06-8bf9-47fe-8f99-d5c362646d03">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SERVICO_EXPRESSO}.equals("N")?"X":""]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
