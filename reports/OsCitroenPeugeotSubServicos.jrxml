<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsCitroenPeugeotSubServicos" pageWidth="555" pageHeight="250" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="d760b610-66b4-42df-8551-447eb20ebfd0">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<style name="Cor1" style="Style1" mode="Opaque" forecolor="#FFFFFF" backcolor="#0076A9">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN") )]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#F57523" pattern=""/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#001F56"/>
		</conditionalStyle>
	</style>
	<style name="Cor2" mode="Opaque" forecolor="#CCE3ED" backcolor="#589EC3" pattern="">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#FDE3D3" backcolor="#FF9F67"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#231F20" backcolor="#B2AFC4"/>
		</conditionalStyle>
	</style>
	<style name="Cor3" style="Style1" mode="Opaque" forecolor="#000203" backcolor="#CCE3ED">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#FDE3D3"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#EFEFF3"/>
		</conditionalStyle>
	</style>
	<style name="alternateStyle" style="Cor3" mode="Opaque">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 1)]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COPIA_CLIENTE" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="MARCA" class="java.lang.String">
		<defaultValueExpression><![CDATA["CITROEN"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT NVL(C1, D1) ID, OTE.*
  FROM (SELECT ROWNUM C1, 
               OT.NUMERO_OS,
               OT.COD_EMPRESA,
               OT.ITEM,
               OT.COD_SERVICO,
               SRV.DESCRICAO_SERVICO,
               OT.COD_TECNICO,
               TEC.NOME,
               OT.DATA_ENTRADA,
               OT.TEMPO_PAGO
          FROM OS_TEMPOS_EXECUTADOS OT,
               SERVICOS_TECNICOS TEC,
               SERVICOS SRV
         WHERE TEC.COD_EMPRESA = OT.COD_EMPRESA
           AND TEC.COD_TECNICO = OT.COD_TECNICO
           AND NVL(TEC.FICTICIO, 'N') = 'N'
           AND NVL(TEC.ATIVO, 'S') = 'S'
           AND (NVL(TEC.EH_CONSULTOR, 'N') = 'N') 
           AND SRV.COD_SERVICO = OT.COD_SERVICO
           AND OT.COD_EMPRESA = $P{COD_EMPRESA}
           AND OT.NUMERO_OS = $P{NUMERO_OS}
) OTE,
       (SELECT LEVEL D1 FROM DUAL CONNECT BY LEVEL <=20) SEQ
 WHERE OTE.C1(+) = SEQ.D1
ORDER BY ID]]>
	</queryString>
	<field name="ID" class="java.lang.Double"/>
	<field name="C1" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="COD_SERVICO" class="java.lang.String"/>
	<field name="DESCRICAO_SERVICO" class="java.lang.String"/>
	<field name="COD_TECNICO" class="java.lang.Double"/>
	<field name="NOME" class="java.lang.String"/>
	<field name="DATA_ENTRADA" class="java.sql.Timestamp"/>
	<field name="TEMPO_PAGO" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="31">
			<staticText>
				<reportElement style="Cor1" mode="Opaque" x="0" y="0" width="555" height="15" uuid="9db3d874-03f4-4c17-94ea-180b109f4033">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[ATIVIDADE DO TÉCNICO]]></text>
			</staticText>
			<staticText>
				<reportElement key="" style="Cor2" mode="Opaque" x="416" y="18" width="67" height="13" uuid="517b4601-85ff-4d96-88cf-beb21f5bfece">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Data]]></text>
			</staticText>
			<staticText>
				<reportElement key="" style="Cor2" mode="Opaque" x="274" y="18" width="67" height="13" uuid="a444e5a3-f5e3-4597-b083-6481e3725e69"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Nome do Técnico]]></text>
			</staticText>
			<staticText>
				<reportElement key="" style="Cor2" mode="Opaque" x="345" y="18" width="67" height="13" uuid="d4486617-44ba-43ad-9537-553675e55184"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Nº do Técnico]]></text>
			</staticText>
			<staticText>
				<reportElement key="" style="Cor2" mode="Opaque" x="0" y="18" width="270" height="13" uuid="49b7d2ac-52ec-46db-a799-bde946333953">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Serviço]]></text>
			</staticText>
			<staticText>
				<reportElement key="" style="Cor2" mode="Opaque" x="487" y="18" width="68" height="13" uuid="66dbaaba-0de7-4e12-ac23-7e11fb231099"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Tempo]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement key="" style="alternateStyle" mode="Opaque" x="274" y="0" width="67" height="13" uuid="06e98c84-73e0-4f95-8a8d-6c9a6298ad17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOME}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;(#,##0.00#-)">
				<reportElement style="alternateStyle" mode="Opaque" x="487" y="0" width="68" height="13" uuid="94008d0e-650c-4cb7-963c-dc429f4ea257"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEMPO_PAGO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="" style="alternateStyle" mode="Opaque" x="0" y="0" width="270" height="13" uuid="e6e62d4d-53da-4482-8374-266182d50a71"/>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_SERVICO}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.###;(###0.###-)">
				<reportElement key="" style="alternateStyle" mode="Opaque" x="345" y="0" width="67" height="13" uuid="3f79adf7-57a4-46b1-aaae-30078be81872"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_TECNICO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="" style="alternateStyle" mode="Opaque" x="416" y="0" width="67" height="13" uuid="7f457b6a-09cc-47e2-b4db-ce211ef6e6d7"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DATA_ENTRADA}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
