<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiGerenciamentoServicoSubPreEntrega" pageWidth="201" pageHeight="90" columnWidth="201" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="OBRIGATORIO" class="java.lang.String">
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[with DADOS as (
select info.cod_empresa as COD_EMPRESA,
       info.cod_produto as COD_PRODUTO,
       info.cod_modelo as COD_MODELO,
       info.tipo_os as TIPO_OS,
       info.cod_segmento as COD_SEGMENTO
  from (select os.cod_empresa,dv.Cod_Produto, dv.cod_modelo, os.tipo as tipo_os, produtos.cod_segmento
          from os, os_dados_veiculos dv, produtos
         where os.numero_os = dv.numero_os
           and os.cod_empresa = dv.cod_empresa
           and os.cod_empresa = $P{COD_EMPRESA}
           and os.numero_os = $P{NUMERO_OS}
           and produtos.cod_produto = dv.cod_produto) info
),
TODOS_ITENS as ( 
SELECT A.ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */
       A.DESCRICAO AS DESCRICAO_ITEM,       
       A.ATIVO,
       A.COD_ITEM,
       A.OBRIGATORIO,
       A.ORDEM,
       A.PODE_TER_FOTO,  
       B.DESCRICAO DESCRICAO_GRUPO,
       B.TIPO,       
       B.APLICACAO,
       DECODE(NVL((SELECT PO.SEMAFORO
                            FROM MOB_PERTENCE_OPCAO PO
                           WHERE PO.COD_ITEM = A.COD_ITEM
                             AND PO.ID_OPCAO = C.ID_OPCAO
                             AND ROWNUM = 1),
                          'R'),
                      'G',
                      'GREEN',
                      'R',
                      'RED',
                      'Y',
                      'YELLOW',
                      'RED') AS COR_OPCAO_SELECIONADA, /* se for GREEM então marco como sim no relatorio */
       B.ORDEM AS ORDEM_GRUPO 
FROM MOB_PERTENCE_ITEM A, MOB_PERTENCE_GRUPO B, MOB_OS_PERTENCE C, MOB_OPCAO D
WHERE A.ID_GRUPO = B.ID_GRUPO
 AND ((($P{OBRIGATORIO} = 'S') AND (A.OBRIGATORIO = $P{OBRIGATORIO})) OR ($P{OBRIGATORIO} = 'N'))
 AND B.TIPO = 'C'
 AND A.ATIVO = 'S'
 AND B.ATIVO = 'S'
 AND A.COD_ITEM = C.COD_ITEM(+)
 AND B.APLICACAO = 'E'
 AND C.COD_EMPRESA(+) = $P{COD_EMPRESA}
 AND C.NUMERO_OS(+) = $P{NUMERO_OS}
 AND C.ID_OPCAO = D.ID_OPCAO(+)
 ),
FILTRO_CRUZA_EPRESA_SEGMENTO AS ( 
SELECT * /* CAMADA 2 - FILTRO TODOS OS ITENS QUE SEJA DA RESPECTIVA EMPRESA OU NÃO POSSUA EMPRESA */
FROM TODOS_ITENS A
WHERE EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo
                     AND mc.cod_empresa = $P{COD_EMPRESA}
                     AND mc.cod_segmento = (SELECT COD_SEGMENTO FROM DADOS)) 
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo)                               
),
FILTRO_CRUZA_TIPO_OS AS ( 
SELECT * /* CAMADA 3 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO TIPO DE OS OU NÃO POSSUA TIPO DE OS VINCULADO */
FROM FILTRO_CRUZA_EPRESA_SEGMENTO A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_tp_os cto
                  WHERE  cto.id_grupo = a.id_grupo
                         AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                         AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS)
                         AND cto.tipo = (SELECT TIPO_OS FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_tp_os cto
              WHERE  cto.id_grupo = A.id_grupo
                     AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                     AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS))
),
FILTRO_CRUZA_MODELO AS ( 
SELECT * /* CAMADA 4 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO MODELO OU NÃO POSSUA MODELO VINCULADO */
FROM FILTRO_CRUZA_TIPO_OS A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_modelo cm
                  WHERE  cm.id_grupo = a.id_grupo
                         AND cm.cod_produto = (SELECT COD_PRODUTO FROM DADOS)
                         AND cm.cod_modelo = (SELECT COD_MODELO FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_modelo cm
              WHERE   cm.id_grupo = a.id_grupo)
),

CONSULTA AS (
  select ID_GRUPO, DESCRICAO_ITEM, ATIVO, COD_ITEM, OBRIGATORIO, ORDEM, PODE_TER_FOTO,       
       DESCRICAO_GRUPO, TIPO, APLICACAO, COR_OPCAO_SELECIONADA  from FILTRO_CRUZA_MODELO
  ORDER BY ORDEM_GRUPO, ID_GRUPO, ORDEM
),

PARAMETROS AS (
           SELECT /*apartir dessa subquery eu trabalho para sempre retornar multiplo de 5 linhas para imprimir de forma correta no relatorio*/
              6 MULTIPLO, /*Determina o multiplo da quantidade de linhas*/
              6 MAXIMO_LINHAS /* determina o maximo de linhas, zero é ilimitado, o numero de linhas que a consulta retornar*/
              FROM DUAL
),
CONSULTA_LIMITADA as (
     SELECT CONSULTA.* FROM CONSULTA,parametros where (rownum <= parametros.maximo_linhas or parametros.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL as (
    SELECT * /* essa subquery eu trabalho para sempre retornar multiplo de 5 linhas para imprimir de forma correta no relatorio */
    FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL
    FROM dual,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),parametros.multiplo) <> 0 or (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY level <= parametros.multiplo - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), parametros.multiplo)
)
SELECT * /*[PODE ESPECIFICAR OS CAMPOS]*/ FROM CONSULTA_FINAL]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="ATIVO" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="OBRIGATORIO" class="java.lang.String"/>
	<field name="ORDEM" class="java.lang.Double"/>
	<field name="PODE_TER_FOTO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="APLICACAO" class="java.lang.String"/>
	<field name="COR_OPCAO_SELECIONADA" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="15" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField>
				<reportElement key="" mode="Transparent" x="19" y="0" width="167" height="15" forecolor="#08038F" uuid="92b1ee04-fe5e-4dc3-8881-24e309a8d339">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="0" y="0" width="19" height="15" forecolor="#08038F" uuid="5552fab3-2e45-403a-88b0-a7abaf943125">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT} + " - "]]></textFieldExpression>
			</textField>
			<image scaleImage="RealHeight">
				<reportElement x="186" y="0" width="15" height="15" uuid="e2a718a5-e954-4f14-b672-7cb443f3a824">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<printWhenExpression><![CDATA[new Boolean($F{COR_OPCAO_SELECIONADA}.equals("GREEN"))]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice15403.png"]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>
