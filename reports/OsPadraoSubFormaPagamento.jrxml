<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsPadraoSubFormaPagamento" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="0efcef53-ba2f-4102-8b70-a807a9128687">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="344"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="646"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[with dados_parcela as (
SELECT 
   'R$ ' || MAX(c.valor_parcela) || ' - (' || COUNT(c.valor_parcela) || ')Parcelas - ' || MAX(a.descricao) AS parcelado
FROM 
    os b, os_pagamento_parc c, condicao_pagamento a
WHERE 
    b.cod_empresa = c.cod_empresa
    AND b.numero_os = c.numero_os
    AND a.cod_condicao_pagamento = c.cod_condicao_pagamento(+)
    AND b.cod_empresa = $P{COD_EMPRESA}
    AND b.numero_os = $P{NUMERO_OS}),
dados_pagamento as (
  select 
      case when FP.TIPO_PGTO = 'P' then
             TO_CHAR(op.valor, 'FM999,999,999,999.00') || ' - ' || fp.descricao || '. ' || nvl((select parcelado from dados_parcela),'')
           else    
             TO_CHAR(op.valor, 'FM999,999,999,999.00') || ' - ' || fp.descricao || '.'
      end as forma_pagamento,
      case when FP.TIPO_PGTO = 'P' 
           then op.juros_valor
           else 0
      end as juros
from os_pagamento op,
     forma_pgto   fp
where op.cod_forma_pgto = fp.cod_forma_pgto
      and op.cod_empresa = fp.cod_empresa
      and op.COD_EMPRESA = $P{COD_EMPRESA} 
      AND op.NUMERO_OS =  $P{NUMERO_OS}
group by op.cod_empresa,
          op.numero_os,
          op.cod_forma_pgto,
          op.valor,
          op.juros_valor,
          op.juros_taxa,
          op.obs,
          op.TIPO_PARCELA_CARTAO,
          op.QTDE_PARCELA_CARTAO,
          fp.tipo_pgto,
          fp.TIPO_COBRANCA_PIX,
          fp.descricao,
          OP.TIPO_DESC,
          OP.COD_CONDICAO_PAGAMENTO
Order BY op.valor
)
select dados_pagamento.forma_pagamento,
       sum(dados_pagamento.juros) over () as JUROS
from dados_pagamento]]>
	</queryString>
	<field name="FORMA_PAGAMENTO" class="java.lang.String"/>
	<field name="JUROS" class="java.lang.Double"/>
	<title>
		<band height="19" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="1" width="554" height="18" backcolor="#D9D9D9" uuid="8e72b655-22fe-4155-91d5-649e08d38a2b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="6" y="3" width="274" height="14" uuid="9d2bb599-f3d7-426e-9070-05b1a6d31f06">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Condições de Pagamento]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="6" y="0" width="548" height="12" uuid="*************-4b40-80d1-0bfafdf89306"/>
				<textElement>
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{FORMA_PAGAMENTO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="15">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="57" y="0" width="484" height="12" uuid="3a132b13-1a3c-4e84-8aa7-59235f7e947f"/>
				<textElement>
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{JUROS}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="6" y="0" width="51" height="12" uuid="b9c976c5-bad6-4872-bf90-1c57975f38f8"/>
				<textElement>
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Outras Desp.:]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
