<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="emBranco" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="null_default" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[232335.0]]></defaultValueExpression>
	</parameter>
	<parameter name="InitialRowCount" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[WITH CONSULTA AS (
 SELECT OS_SERVICOS.NUMERO_OS,
 0 AS COD_OS_AGENDA,
 OS_SERVICOS.COD_EMPRESA,
 OS_SERVICOS.ITEM, 
 OS_SERVICOS.COD_SERVICO, 
 SERVICOS.DESCRICAO_SERVICO,
NVL(OS_SERVICOS.TEMPO_PADRAO + NVL(ADI_TEMPO, 0),0) AS TEMPO_PADRAO,
 NVL(OS_SERVICOS.TOTAL_LIQUIDO, OS_SERVICOS.PRECO_VENDA) AS TOTALVENDA,
 OS_SERVICOS.PRECO_VENDA,
 OS_SERVICOS.COD_CCC,
 SERVICOS.COD_SETOR,
 DESCRICAO_SETOR,
(SELECT O.COD_FABRICA
FROM OS_TEMPOS_EXECUTADOS T, SERVICOS_TECNICOS O
 WHERE T.COD_TECNICO = O.COD_TECNICO(+)
   AND T.NUMERO_OS = $P{NUMERO_OS}
   AND T.COD_EMPRESA = $P{COD_EMPRESA}
   AND T.COD_SERVICO = OS_SERVICOS.COD_SERVICO) AS COD_FABRICA
FROM OS_SERVICOS, SERVICOS, SERVICOS_SETORES,
  (SELECT COD_SERVICO AS ADI_SRV, SUM(TEMPO_ADICIONAL) AS ADI_TEMPO
   FROM OS_SERVICOS_ADICIONAIS
   WHERE COD_EMPRESA = $P{COD_EMPRESA}
    AND NUMERO_OS = $P{NUMERO_OS}
   GROUP BY COD_SERVICO)
WHERE OS_SERVICOS.COD_SERVICO = SERVICOS.COD_SERVICO
 AND SERVICOS.COD_SETOR = SERVICOS_SETORES.COD_SETOR
 AND OS_SERVICOS.COD_SERVICO = ADI_SRV (+)
 AND OS_SERVICOS.NUMERO_OS = $P{NUMERO_OS}
 AND OS_SERVICOS.COD_EMPRESA = $P{COD_EMPRESA}
ORDER BY OS_SERVICOS.ITEM, OS_SERVICOS.COD_SERVICO
),

PARAMETROS AS (
           SELECT
              3 MULTIPLO, /*DETERMINA O MULTIPLO DA QUANTIDADE DE LINHAS*/
              0 MAXIMO_LINHAS /* DETERMINA O MAXIMO DE LINHAS, ZERO É ILIMITADO, O NUMERO DE LINHAS QUE A CONSULTA RETORNAR*/
              FROM DUAL
),
CONSULTA_LIMITADA AS (
     SELECT CONSULTA.* FROM CONSULTA,PARAMETROS WHERE (ROWNUM <= PARAMETROS.MAXIMO_LINHAS OR PARAMETROS.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL AS (
    SELECT * FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL
    FROM DUAL,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),PARAMETROS.MULTIPLO) <> 0 OR (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY LEVEL <= PARAMETROS.MULTIPLO - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), PARAMETROS.MULTIPLO)
)
SELECT * /*[PODE ESPECIFICAR OS CAMPOS]*/ FROM CONSULTA_FINAL]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Integer"/>
	<field name="COD_OS_AGENDA" class="java.lang.Integer"/>
	<field name="COD_EMPRESA" class="java.lang.Integer"/>
	<field name="ITEM" class="java.lang.Integer"/>
	<field name="COD_SERVICO" class="java.lang.String"/>
	<field name="DESCRICAO_SERVICO" class="java.lang.String"/>
	<field name="TEMPO_PADRAO" class="java.lang.Double"/>
	<field name="TOTALVENDA" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="COD_CCC" class="java.lang.String"/>
	<field name="COD_SETOR" class="java.lang.String"/>
	<field name="DESCRICAO_SETOR" class="java.lang.String"/>
	<field name="COD_FABRICA" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="43">
			<staticText>
				<reportElement mode="Opaque" x="0" y="17" width="185" height="26" backcolor="#E3E3E3" uuid="c6bb75a7-958e-4d0a-b8f5-2f31efa4443a"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Instruções do Chefe de Oficina]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="185" y="17" width="172" height="26" backcolor="#E3E3E3" uuid="e0b6a820-047e-43d1-89d3-166b0bf192d2"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Descrição dos Técnicos]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="356" y="17" width="96" height="26" backcolor="#E3E3E3" uuid="cfae23f9-6153-4e36-95ec-2aa22ca8f859"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Cód Operação]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="452" y="17" width="56" height="26" backcolor="#E3E3E3" uuid="d5145b65-073e-4d82-9c09-e2dd7c869881"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Tempo Padrão]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="508" y="17" width="47" height="26" backcolor="#E3E3E3" uuid="a93323b4-ee15-4431-a813-5da9fd22f8ca"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Cód Probl]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="5" y="2" width="30" height="14" uuid="acc6f79e-5048-49c8-be9c-c53215d96475"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Nº O.S:]]></text>
			</staticText>
			<textField>
				<reportElement mode="Transparent" x="35" y="2" width="80" height="14" uuid="71a67f30-bf4c-481e-841b-c55b72079f70"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NUMERO_OS}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.###;(###0.###-)">
				<reportElement mode="Transparent" x="475" y="2" width="42" height="14" uuid="22942dea-e7aa-4a0f-9e7c-b5e84b31c34d"/>
				<box rightPadding="0"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Math.ceil($V{PAGE_NUMBER}/2.0)]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="#,##0.###;(#,##0.###-)">
				<reportElement mode="Transparent" x="523" y="2" width="26" height="14" uuid="6acf4973-f8c6-4428-ab6c-c99d119fa142"/>
				<box rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Math.ceil($V{PAGE_NUMBER}/2.0)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="517" y="2" width="6" height="14" uuid="26878e88-e590-4fe8-9293-1089ddd8db87"/>
				<box rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[ / ]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="180" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="176" uuid="032a6db8-c537-4b81-966d-e3301bc1e450"/>
				<rectangle>
					<reportElement mode="Transparent" x="0" y="0" width="185" height="176" uuid="3e17aa82-563a-48ba-876e-912ec954369d"/>
				</rectangle>
				<rectangle>
					<reportElement mode="Transparent" x="356" y="0" width="96" height="176" uuid="6681c28a-d320-4beb-86b4-2ce64013a5e3"/>
				</rectangle>
				<rectangle>
					<reportElement mode="Transparent" x="452" y="0" width="56" height="176" uuid="a85ffa19-8d57-4398-a823-e8f2ce43d644"/>
				</rectangle>
				<rectangle>
					<reportElement mode="Transparent" x="508" y="0" width="47" height="176" uuid="26279047-46c2-4494-b0eb-7cdc9957dfe4"/>
				</rectangle>
				<rectangle>
					<reportElement mode="Transparent" x="185" y="0" width="171" height="176" uuid="66025b63-7834-4d0e-8d47-46df750ab8c1"/>
				</rectangle>
				<line>
					<reportElement x="0" y="112" width="555" height="1" uuid="431b5487-f15e-446a-9828-72864c65609f"/>
				</line>
				<line>
					<reportElement x="0" y="95" width="555" height="1" uuid="e3430d12-a24f-418d-9d2e-9502b896a9a8"/>
				</line>
				<line>
					<reportElement x="0" y="79" width="555" height="1" uuid="b3be7024-430b-46fb-9c12-88d7fb3197eb"/>
				</line>
				<line>
					<reportElement x="0" y="63" width="555" height="1" uuid="74f04b68-c4cd-4f53-bd4c-c5f752e5967b"/>
				</line>
				<line>
					<reportElement x="0" y="48" width="555" height="1" uuid="a680c072-784d-47ff-ba99-e6412330ff9b"/>
				</line>
				<line>
					<reportElement x="0" y="31" width="555" height="1" uuid="989b9b0e-1b72-4c9a-8abb-6d650ba38055"/>
				</line>
				<line>
					<reportElement x="0" y="15" width="555" height="1" uuid="31f19f5c-2c75-4a26-9743-06c007757f08"/>
				</line>
				<line>
					<reportElement x="0" y="127" width="555" height="1" uuid="c677f00f-5fd1-4345-a6fe-c70e20fd6c60"/>
				</line>
				<line>
					<reportElement x="0" y="142" width="555" height="1" uuid="007306a1-a31f-4f06-a9c4-01f82d7bfbb2"/>
				</line>
				<line>
					<reportElement x="0" y="157" width="555" height="1" uuid="1d413783-4c75-4a28-b831-64f6088d1465"/>
				</line>
				<staticText>
					<reportElement mode="Opaque" x="418" y="146" width="131" height="25" uuid="e5fe9902-b223-4515-b5cd-94cceb6488e2"/>
					<box topPadding="0" leftPadding="2" bottomPadding="2" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Assinatura Técnico]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="361" y="146" width="51" height="25" uuid="e59789e7-21cc-49f8-ab43-85a7cfd40a2c"/>
					<box topPadding="0" leftPadding="2" bottomPadding="2" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[No. Téc]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="357" y="1" width="90" height="14" uuid="950e0bc8-3c91-4f66-aa47-8b1f3e4cbc48"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COD_SERVICO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement mode="Transparent" x="455" y="1" width="50" height="14" uuid="8b82ae10-474f-4a63-9523-bc4278d63c3a"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
				</textField>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement mode="Transparent" x="362" y="148" width="48" height="12" uuid="2cb589d6-c32e-40ae-ab37-9ebede0b14f7"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COD_FABRICA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="3" y="3" width="25" height="24" uuid="46f408f1-4260-4b3c-bead-84e85fc7858d"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="13" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="187">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="129" uuid="c3f8871d-16a2-4447-a79c-c2f3b45d97a2"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Opaque" x="165" y="16" width="14" height="13" uuid="5b5fb4fe-5c39-4bfa-a8c5-113ba31aac72"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="555" height="13" backcolor="#E3E3E3" uuid="db8cfc52-598a-49e0-ad32-f409705f4237"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[TESTE DE RODAGEM]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="34" width="544" height="14" uuid="a60664ab-e56e-407c-9eb8-57d6003558de"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Foi a realizado o 2º, Teste de Rodagem                  ____/____/______     ____:_____      _____________________________________________]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="53" width="544" height="14" uuid="2eb846b2-1b8e-4c87-b75e-0e0cd2398762"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Foi a realizado o 3º, Teste de Rodagem                  ____/____/______     ____:_____      _____________________________________________]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="165" y="54" width="14" height="13" uuid="e812128d-**************-d45e11994ee3"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="165" y="34" width="14" height="13" uuid="fba99cf6-281d-4e3f-8e39-eace748fed5c"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="69" width="554" height="13" backcolor="#E3E3E3" uuid="f2597794-edef-40de-af83-8953e143eb95"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CONTROLE DE QUALIDADE]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="4" y="84" width="544" height="14" uuid="94babfcf-74a8-49ac-96c5-038246cb66e8"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[1. Verificar a solicitação inicial do cliente e comparar com a Descrição do Técnicos do Serviço Realizado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="412" y="84" width="14" height="12" uuid="b9fa1511-6902-474b-abae-b2bb170ea9b9"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="412" y="98" width="14" height="12" uuid="259ce6c1-a230-45e1-a92b-5e63b96e1258"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="4" y="96" width="544" height="14" uuid="48fc96f3-1892-4d58-b314-e17702815298"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[2. Verificar o completo preenchimento do Check List de Qualidade]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="15" width="544" height="14" uuid="8d91fa40-3677-4a5c-becf-e6159355b3d2">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Foi a realizado o 2º, Teste de Rodagem                  ____/____/______     ____:_____      _____________________________________________]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="412" y="98" width="14" height="12" uuid="bd9eb2f6-495f-4a7e-9b5d-f57b2183b59c"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<line>
					<reportElement x="331" y="124" width="218" height="1" uuid="7fb22473-f2bc-48fe-a980-33b3f2573c87"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="4" y="112" width="544" height="14" uuid="9609eb5e-b4a8-40a7-84b6-bfe41f324022"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Resp. pelo Controle de Qualidade:]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="129" width="555" height="58" uuid="0670e66a-724e-4998-9111-a76409eeb5d9">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<rectangle>
					<reportElement x="94" y="13" width="26" height="45" uuid="8963b6d9-2884-4c1b-8682-b32d1d15ef68"/>
				</rectangle>
				<rectangle>
					<reportElement x="68" y="13" width="26" height="45" uuid="74f3df8c-4e45-4a54-97e4-f133672f6841"/>
				</rectangle>
				<rectangle>
					<reportElement x="0" y="13" width="68" height="45" uuid="1f43ef17-7bb8-43fd-893f-8da21fea1107"/>
				</rectangle>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="68" height="13" backcolor="#E3E3E3" uuid="b0d4e0a9-7c01-498f-ae8c-573fd4b2042a"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[DEV. PEÇAS]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="68" y="0" width="26" height="13" backcolor="#E3E3E3" uuid="eb512af3-2023-4544-9a9b-b39f52de99fd"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[SIM]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="94" y="0" width="26" height="13" backcolor="#E3E3E3" uuid="125a75ab-c822-4736-ac70-6568b21cb807"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[NÃO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="120" y="0" width="332" height="13" backcolor="#E3E3E3" uuid="54c4522d-2bb8-462e-99a7-65ed382767d7"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[REPAROS ADICIONAIS]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="451" y="0" width="104" height="13" backcolor="#E3E3E3" uuid="f1d5b5ec-969f-45d9-8ca1-aa09372d971a"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[VALIDAÇÃO DA OS]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="24" width="451" height="1" uuid="c1ab3902-844f-479d-aacb-cc1ee3d21040">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.7"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="0" y="35" width="555" height="1" uuid="d4dd6cd0-842a-4c6d-bbb8-bdcd473d917f">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.7"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="0" y="46" width="451" height="1" uuid="23f90078-8646-4516-99ac-3a65f550f70c">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.7"/>
					</graphicElement>
				</line>
				<frame>
					<reportElement x="451" y="13" width="104" height="45" uuid="2f339354-aabd-42a6-b6cb-b2cffdf9a8ec"/>
					<box>
						<leftPen lineWidth="1.0"/>
					</box>
					<staticText>
						<reportElement mode="Opaque" x="10" y="23" width="89" height="21" uuid="036a7485-4d48-42ba-bb20-f6c4e101a28a">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Right" verticalAlignment="Bottom">
							<font size="8" isBold="false"/>
						</textElement>
						<text><![CDATA[Data]]></text>
					</staticText>
				</frame>
			</frame>
		</band>
	</columnFooter>
</jasperReport>
