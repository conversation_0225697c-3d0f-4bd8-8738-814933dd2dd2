<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsKIASubServicos" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT OS_SERVICOS.NUMERO_OS,
 OS_SERVICOS.COD_EMPRESA,
 OS_SERVICOS.ITEM,
 OS_SERVICOS.COD_SERVICO,
 SERVICOS.DESCRICAO_SERVICO,
 OS_SERVICOS.TEMPO_PADRAO,
 OS_SERVICOS.PRECO_VENDA,
 OS_SERVICOS.SEQ_PAF_ITEM,
 'A' AS STATUS
FROM OS_SERVICOS, SERVICOS
WHERE OS_SERVICOS.COD_SERVICO = SERVICOS.COD_SERVICO
  AND OS_SERVICOS.NUMERO_OS = $P{NUMERO_OS}
  AND OS_SERVICOS.COD_EMPRESA = $P{COD_EMPRESA}
ORDER BY OS_SERVICOS.ITEM, OS_SERVICOS.cod_servico]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="COD_SERVICO" class="java.lang.String"/>
	<field name="DESCRICAO_SERVICO" class="java.lang.String"/>
	<field name="TEMPO_PADRAO" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="SEQ_PAF_ITEM" class="java.lang.Double"/>
	<field name="STATUS" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="29">
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="555" height="16" uuid="25b1a9ce-420b-47ba-a8f0-195abf6abfd7"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="2" y="3" width="226" height="10" uuid="8a599c40-4916-4acf-a32e-b4fe167ec96b"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Serviços]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="354" y="2" width="140" height="10" uuid="39970758-fe8a-42c1-ab56-b8f704e65a57"/>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Valor de Mão de Obra por Hora:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="499" y="2" width="55" height="11" uuid="5b1ecc66-871d-4e9d-b659-b3c9bc8544d7"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["osValor_Hora"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="16" width="555" height="13" uuid="6fd4babb-f491-41b3-b808-801aa7045b90"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="2" y="2" width="21" height="10" uuid="5198da1f-3297-4f80-b5de-1e95ab6b5b64"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Item]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="26" y="2" width="82" height="10" uuid="adf5fb09-741d-40a7-9dfe-985c9b2f484d"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Código do Serviço]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="110" y="2" width="318" height="10" uuid="1adc8e07-97c3-469a-b4df-abf9cbf6bded"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Descrição do Serviço]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="432" y="2" width="62" height="10" uuid="94113d59-7b6a-4227-9866-418d32cf6702"/>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Tempo Padrão]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="499" y="3" width="55" height="10" uuid="b34e9f81-117e-4653-adec-17422e233b43"/>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Valor Final]]></text>
				</staticText>
				<line>
					<reportElement x="24" y="0" width="1" height="13" uuid="a751c206-c5b0-4afd-8ed6-31cd79aa2a01"/>
				</line>
				<line>
					<reportElement x="109" y="0" width="1" height="13" uuid="98250e7e-6753-4fdc-9434-f082b7e020b7"/>
				</line>
				<line>
					<reportElement x="429" y="0" width="1" height="13" uuid="dd7cf250-c5c6-4dac-a3de-42a091035308"/>
				</line>
				<line>
					<reportElement x="496" y="0" width="1" height="13" uuid="f886b2fe-ed2a-4723-96e0-91cd870064d8"/>
				</line>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="12">
			<frame>
				<reportElement x="0" y="0" width="555" height="12" uuid="c0db9480-866d-4690-946f-b3967caff3ad">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="2" y="1" width="21" height="11" uuid="5b6ce700-8fe7-4d2d-ad75-9fddef581667"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="26" y="1" width="82" height="11" uuid="1ee64d59-8091-4cca-b28c-ec1f6a7dc82b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COD_SERVICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="110" y="1" width="318" height="11" uuid="ab078f07-6d25-46d3-b1c4-deff41a796f0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_SERVICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="432" y="1" width="62" height="11" uuid="dbe7a9b6-52da-45b2-85ec-61be06202f45"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement mode="Transparent" x="499" y="1" width="55" height="11" uuid="dae14b66-f82b-4ff8-a4eb-f21eccf7e116"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="24" y="0" width="1" height="12" uuid="f7c2c348-74ac-41e2-8548-b66f692ffdc1"/>
				</line>
				<line>
					<reportElement x="109" y="0" width="1" height="12" uuid="8adeb1d1-2fd8-4553-adfd-66ecdebecdd3"/>
				</line>
				<line>
					<reportElement x="429" y="0" width="1" height="12" uuid="26369cb1-dcaf-427f-9bc5-7436a646ccac"/>
				</line>
				<line>
					<reportElement x="496" y="0" width="1" height="12" uuid="664efb3b-cee4-4341-a612-ac24b9f5d6c1"/>
				</line>
			</frame>
		</band>
	</detail>
</jasperReport>
