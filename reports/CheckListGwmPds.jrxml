<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListGwmPds" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="0" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="padrao_null" isDefault="true" isBlankWhenNull="true"/>
	<style name="corPadraoGwm" forecolor="#0070C0">
		<conditionalStyle>
			<conditionExpression><![CDATA[$P{ID_CHECKLIST} == 30500.0]]></conditionExpression>
			<style mode="Transparent" forecolor="#6068D6"/>
		</conditionalStyle>
	</style>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\35501\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="LISTA_GRUPOS" class="java.lang.String">
		<defaultValueExpression><![CDATA["30500,30525,30550,30575,30600,30625,30650"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBTITULO_RELATORIO" class="java.lang.String">
		<defaultValueExpression><![CDATA["[HAVAL H6 HEV]"]]></defaultValueExpression>
	</parameter>
	<parameter name="ID_CHECKLIST" class="java.lang.Double"/>
	<queryString>
		<![CDATA[WITH qryOS AS
 (SELECT ROWNUM AS NUMERO_LINHA,
  OS.COD_EMPRESA,
         OS.NUMERO_OS,
         OS.DATA_EMISSAO,
         V.PRISMA,
         C.NOME NOME_CLIENTE,
         NVL(NULLIF(CD.CGC, ''), CD.CPF) CNPJ_CPF,
         CD.Inscricao_Estadual  Inscricao_Estadual,
         CD.RG,
         NVL(C.RUA_RES, C.RUA_COM) RUA_RES,
         NVL(C.BAIRRO_RES, C.BAIRRO_COM) BAIRRO_RES,
         (SELECT DESCRICAO
            FROM CIDADES
           WHERE COD_CIDADES = NVL(C.COD_CID_RES, C.COD_CID_COM)
             AND ROWNUM = 1) AS CIDADE_RES,
         NVL(C.UF_RES, C.UF_COM) UF_RES,
         NVL(C.CEP_RES, C.CEP_COM) CEP_RES,
         C.ENDERECO_ELETRONICO,
         (NVL(C.PREFIXO_RES, C.PREFIXO_COM) || ' - ' ||
         NVL(C.TELEFONE_RES, C.TELEFONE_COM)) AS TELEFONE_RES,
         (C.PREFIXO_CEL || ' - ' || C.TELEFONE_CEL) AS TELEFONE_CEL,
         (C.PREFIXO_COM || ' - ' || C.TELEFONE_COM) AS TELEFONE_COM,
         NVL(NULLIF(C.RUA_COBRANCA, ''), C.RUA_RES) RUA_FAT,
         NVL(NULLIF(C.CEP_COBRANCA, ''), C.CEP_RES) CEP_FAT,
         NVL(NULLIF(C.BAIRRO_COBRANCA, ''), C.BAIRRO_RES) BAIRRO_FAT,
         NVL(CIDF.DESCRICAO, CIDR.DESCRICAO) CIDADE_FAT,
         NVL(CIDF.UF, CIDR.UF) UF_FAT,
         OS.OBSERVACAO,
         CASE
           WHEN OT.REVISAO_GRATUITA = 'S' THEN
            1 
           WHEN OT.INTERNO = 'S' THEN
            2 
           WHEN OT.GARANTIA = 'S' THEN
            3 
           WHEN OTE.FUNILARIA = 'S' THEN
            4 
           ELSE
            0 
         END TIPO_OS,
         CASE UPPER(FPG.TIPO_PGTO)
           WHEN 'Z' THEN
            1 
           WHEN 'H' THEN
            2 
           WHEN 'V' THEN
            3 
           WHEN 'M' THEN
            4 
           WHEN 'P' THEN
            4 
           ELSE
            0 
         END FORMA_PAGAMENTO,
         PM.DESCRICAO_MODELO,
         V.CHASSI,
         V.COR_EXTERNA,
         V.KM,
         V.DATA_VENDA,
         V.ANO,
         V.PLACA,
         CON.CODIGO_PADRAO COD_REVENDEDOR,
         CON.NOME NOME_REVENDEDOR,
         V.COMBUSTIVEL,
         NVL(OS.NOME, OS.CONSULTOR_RECEPCAO) AS CONSULTOR,
		 NVL(CONSULTOR_USUARIO.NOME_COMPLETO, ' ')  AS CONSULTOR_COMPLETO,
		 NVL((SELECT INITCAP(EMPRESAS_FUNCOES.DESCRICAO) FROM EMPRESAS_FUNCOES
				WHERE EMPRESAS_FUNCOES.COD_FUNCAO = 1
				and ROWNUM = 1
				), '') AS CONSULTOR_FUNCAO,
     OS_AGENDA.SIGNATURE AS OS_ASSINATURA,
     PRODUTOS.COD_SEGMENTO,
	 SYSDATE AS DATA_HOJE
    FROM OS,
     OS_AGENDA,
         OS_DADOS_VEICULOS V,
         OS_TIPOS          OT,
         OS_TIPOS_EMPRESAS OTE,
         OS_PAGAMENTO      OPG,
         FORMA_PGTO        FPG,
         CLIENTES          C,
         CLIENTE_DIVERSO   CD,
         CIDADES           CIDR,
         CIDADES           CIDF,
         PRODUTOS_MODELOS  PM,
         PRODUTOS,
         CONCESSIONARIAS   CON,
		 EMPRESAS_USUARIOS CONSULTOR_USUARIO
   WHERE (OS.NUMERO_OS = V.NUMERO_OS AND OS.COD_EMPRESA = V. COD_EMPRESA)
   AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
     AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS (+)
     AND (OS.TIPO = OT.TIPO)
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND (OT.TIPO = OTE.TIPO AND OTE.COD_EMPRESA = OS.COD_EMPRESA)
     AND (OS.NUMERO_OS = OPG.NUMERO_OS(+) AND
         OS.COD_EMPRESA = OPG.COD_EMPRESA(+))
     AND (OPG.COD_FORMA_PGTO = FPG.COD_FORMA_PGTO(+))
     AND (OPG.COD_EMPRESA = FPG.COD_EMPRESA(+))
     AND (OS.COD_CLIENTE = C.COD_CLIENTE)
     AND (C.COD_CLIENTE = CD.COD_CLIENTE)
     AND C.COD_CID_RES = CIDR.COD_CIDADES(+)
     AND C.COD_CID_COBRANCA = CIDF.COD_CIDADES(+)
        
     AND V.COD_PRODUTO = PM.COD_PRODUTO (+)
     AND V.COD_MODELO = PM.COD_MODELO (+)
     AND PM.COD_PRODUTO = PRODUTOS.COD_PRODUTO (+)
     AND V.COD_CONCESSIONARIA = CON.COD_CONCESSIONARIA
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
	 AND OS.NOME = CONSULTOR_USUARIO.NOME(+)),

qryEmpresa AS
 (SELECT ROWNUM AS NUMERO_LINHA, E.NOME,
         E.RUA,
         E.BAIRRO,
         E.CIDADE,
         E.ESTADO,
         E.CEP,
         E.FONE,
         E.FAX,
         C.CODIGO_PADRAO CODIGO_REVENDEDOR
    FROM EMPRESAS E, PARM_SYS P, CONCESSIONARIAS C
   WHERE E.COD_EMPRESA = P.COD_EMPRESA
     AND P.CONCESSIONARIA_NUMERO = C.COD_CONCESSIONARIA(+)
     AND E.COD_EMPRESA = $P{COD_EMPRESA})
SELECT 
qryempresa.NOME AS qryempresa_NOME,
qryempresa.RUA AS qryempresa_RUA,
qryos.NUMERO_OS AS qryos_NUMERO_OS,
qryempresa.CEP AS qryempresa_CEP,
qryempresa.CODIGO_REVENDEDOR AS qryempresa_CODIGO_REVENDEDOR,
qryempresa.FONE AS qryempresa_FONE,
qryempresa.BAIRRO AS qryempresa_BAIRRO,
qryos.PRISMA AS qryos_PRISMA,
qryos.DATA_EMISSAO AS qryos_DATA_EMISSAO,
qryempresa.CIDADE AS qryempresa_CIDADE,
QRYEMPRESA.ESTADO AS QRYEMPRESA_ESTADO,
qryempresa.FAX AS qryempresa_FAX,
qryos.COR_EXTERNA AS qryos_COR_EXTERNA,
qryos.CHASSI AS qryos_CHASSI,
qryos.DESCRICAO_MODELO AS qryos_DESCRICAO_MODELO,
qryos.DATA_VENDA AS qryos_DATA_VENDA,
qryos.KM AS qryos_KM,
qryos.OBSERVACAO AS qryos_OBSERVACAO,
qryos.ANO AS qryos_ANO,
qryos.PLACA AS qryos_PLACA,
qryos.CONSULTOR_COMPLETO AS qryos_CONSULTOR_COMPLETO,
qryos.CONSULTOR_FUNCAO AS qryos_CONSULTOR_FUNCAO,
qryos.DATA_HOJE AS qryos_DATA_HOJE,
qryos.COD_REVENDEDOR AS qryos_COD_REVENDEDOR,
qryos.NOME_REVENDEDOR AS qryos_NOME_REVENDEDOR,
qryos.NOME_CLIENTE AS qryos_NOME_CLIENTE,
qryos.RUA_RES AS qryos_RUA_RES,
qryos.BAIRRO_RES AS qryos_BAIRRO_RES,
qryos.RG AS qryos_RG,
qryos.CNPJ_CPF AS qryos_CNPJ_CPF,
qryos.CIDADE_RES AS qryos_CIDADE_RES,
qryos.TELEFONE_COM AS qryos_TELEFONE_COM,
qryos.ENDERECO_ELETRONICO AS qryos_ENDERECO_ELETRONICO,
qryos.CEP_RES AS qryos_CEP_RES,
qryos.UF_RES AS qryos_UF_RES,
qryos.TELEFONE_CEL AS qryos_TELEFONE_CEL,
qryos.TELEFONE_RES AS qryos_TELEFONE_RES,
qryos.BAIRRO_FAT AS qryos_BAIRRO_FAT,
COALESCE (qryos.TELEFONE_COM,qryos.TELEFONE_RES,qryos.TELEFONE_CEL) AS qryos_TELEFONE_CLIENTE,
qryos.CEP_FAT AS qryos_CEP_FAT,
qryos.UF_FAT AS qryos_UF_FAT,
qryos.CIDADE_FAT AS qryos_CIDADE_FAT,
qryos.RUA_FAT AS qryos_RUA_FAT,
qryos.TIPO_OS AS qryos_TIPO_OS,
qryos.COMBUSTIVEL AS qryos_COMBUSTIVEL,
qryos.FORMA_PAGAMENTO as qryos_FORMA_PAGAMENTO,
qryos.OS_ASSINATURA as qruos_OS_ASSINATURA,
qryos.Inscricao_Estadual as qryos_Inscricao_Estadual,
qryos.cod_segmento as qryos_cod_segmento,
substr(qryos.chassi,1,1) as Chassi_pos1,
substr(qryos.chassi,2,1) as Chassi_pos2,
substr(qryos.chassi,3,1) as Chassi_pos3,
substr(qryos.chassi,4,1) as Chassi_pos4,
substr(qryos.chassi,5,1) as Chassi_pos5,
substr(qryos.chassi,6,1) as Chassi_pos6,
substr(qryos.chassi,7,1) as Chassi_pos7,
substr(qryos.chassi,8,1) as Chassi_pos8,
substr(qryos.chassi,9,1) as Chassi_pos9,
substr(qryos.chassi,10,1) as Chassi_pos10,
substr(qryos.chassi,11,1) as Chassi_pos11,
substr(qryos.chassi,12,1) as Chassi_pos12,
substr(qryos.chassi,13,1) as Chassi_pos13,
substr(qryos.chassi,14,1) as Chassi_pos14,
substr(qryos.chassi,15,1) as Chassi_pos15,
substr(qryos.chassi,16,1) as Chassi_pos16,
substr(qryos.chassi,17,1) as Chassi_pos17
FROM qryOS, qryEmpresa
WHERE qryOS.NUMERO_LINHA = qryEmpresa.NUMERO_LINHA (+)]]>
	</queryString>
	<field name="QRYEMPRESA_NOME" class="java.lang.String"/>
	<field name="QRYEMPRESA_RUA" class="java.lang.String"/>
	<field name="QRYOS_NUMERO_OS" class="java.lang.Double"/>
	<field name="QRYEMPRESA_CEP" class="java.lang.String"/>
	<field name="QRYEMPRESA_CODIGO_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYEMPRESA_FONE" class="java.lang.String"/>
	<field name="QRYEMPRESA_BAIRRO" class="java.lang.String"/>
	<field name="QRYOS_PRISMA" class="java.lang.String"/>
	<field name="QRYOS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="QRYEMPRESA_CIDADE" class="java.lang.String"/>
	<field name="QRYEMPRESA_ESTADO" class="java.lang.String"/>
	<field name="QRYEMPRESA_FAX" class="java.lang.String"/>
	<field name="QRYOS_COR_EXTERNA" class="java.lang.String"/>
	<field name="QRYOS_CHASSI" class="java.lang.String"/>
	<field name="QRYOS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="QRYOS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="QRYOS_KM" class="java.lang.Double"/>
	<field name="QRYOS_OBSERVACAO" class="java.lang.String"/>
	<field name="QRYOS_ANO" class="java.lang.String"/>
	<field name="QRYOS_PLACA" class="java.lang.String"/>
	<field name="QRYOS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="QRYOS_CONSULTOR_FUNCAO" class="java.lang.String"/>
	<field name="QRYOS_DATA_HOJE" class="java.sql.Timestamp"/>
	<field name="QRYOS_COD_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_CLIENTE" class="java.lang.String"/>
	<field name="QRYOS_RUA_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_RES" class="java.lang.String"/>
	<field name="QRYOS_RG" class="java.lang.String"/>
	<field name="QRYOS_CNPJ_CPF" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_COM" class="java.lang.String"/>
	<field name="QRYOS_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="QRYOS_CEP_RES" class="java.lang.String"/>
	<field name="QRYOS_UF_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_CEL" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_FAT" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_CLIENTE" class="java.lang.String"/>
	<field name="QRYOS_CEP_FAT" class="java.lang.String"/>
	<field name="QRYOS_UF_FAT" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_FAT" class="java.lang.String"/>
	<field name="QRYOS_RUA_FAT" class="java.lang.String"/>
	<field name="QRYOS_TIPO_OS" class="java.lang.Double"/>
	<field name="QRYOS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="QRYOS_FORMA_PAGAMENTO" class="java.lang.Double"/>
	<field name="QRUOS_OS_ASSINATURA" class="java.awt.Image"/>
	<field name="QRYOS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="QRYOS_COD_SEGMENTO" class="java.lang.Double"/>
	<field name="CHASSI_POS1" class="java.lang.String"/>
	<field name="CHASSI_POS2" class="java.lang.String"/>
	<field name="CHASSI_POS3" class="java.lang.String"/>
	<field name="CHASSI_POS4" class="java.lang.String"/>
	<field name="CHASSI_POS5" class="java.lang.String"/>
	<field name="CHASSI_POS6" class="java.lang.String"/>
	<field name="CHASSI_POS7" class="java.lang.String"/>
	<field name="CHASSI_POS8" class="java.lang.String"/>
	<field name="CHASSI_POS9" class="java.lang.String"/>
	<field name="CHASSI_POS10" class="java.lang.String"/>
	<field name="CHASSI_POS11" class="java.lang.String"/>
	<field name="CHASSI_POS12" class="java.lang.String"/>
	<field name="CHASSI_POS13" class="java.lang.String"/>
	<field name="CHASSI_POS14" class="java.lang.String"/>
	<field name="CHASSI_POS15" class="java.lang.String"/>
	<field name="CHASSI_POS16" class="java.lang.String"/>
	<field name="CHASSI_POS17" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="10"/>
	</columnHeader>
	<detail>
		<band height="167">
			<frame>
				<reportElement x="0" y="0" width="555" height="167" uuid="efb198ee-50f5-4e9a-be0c-21efb58ff12c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="4" y="61" width="177" height="14" forecolor="#000000" uuid="4096e015-6ea9-469b-aa9c-39f707295457">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[1. CONCESSIONÁRIO]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="0" width="555" height="57" uuid="f0f2681b-e228-421e-8512-79bff4a09ef6"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<frame>
						<reportElement x="0" y="0" width="100" height="57" uuid="e757a472-30d7-4471-a612-296439141e2c"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" onErrorType="Blank">
							<reportElement x="23" y="2" width="54" height="44" uuid="4077afd4-99aa-46fb-affd-a92bbcecdc1c">
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							</reportElement>
							<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} +"crmservice371021.png"]]></imageExpression>
						</image>
						<staticText>
							<reportElement mode="Opaque" x="3" y="45" width="94" height="8" forecolor="#000000" backcolor="#FFFFFF" uuid="291396ae-9f98-4a94-87ff-9176bd7cd599">
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							</reportElement>
							<box leftPadding="3" rightPadding="0">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="SansSerif" size="6" isBold="true"/>
							</textElement>
							<text><![CDATA[Service Engineering Dept]]></text>
						</staticText>
					</frame>
					<textField>
						<reportElement style="corPadraoGwm" mode="Opaque" x="181" y="30" width="258" height="24" uuid="2093e669-5398-4797-b8ad-3ed91251d717">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box leftPadding="3" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="15" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$P{SUBTITULO_RELATORIO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Opaque" x="181" y="5" width="258" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="3b7ea458-7bca-4799-9be6-9a5a09804b41">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box leftPadding="3" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="15" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$P{ID_CHECKLIST} == 30500.0 ? "CheckList Recepção" : "PRE DELIVERY SERVICE (PDS)"]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="509" y="0" width="46" height="57" uuid="a4a83744-a8b9-4bea-9c2d-0ad0e13b0760">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box leftPadding="0" rightPadding="0">
							<pen lineColor="#000000"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="SansSerif" size="8" isBold="true"/>
						</textElement>
						<text><![CDATA[Ver.00
08/2025]]></text>
					</staticText>
				</frame>
				<staticText>
					<reportElement mode="Transparent" x="24" y="77" width="59" height="12" forecolor="#000000" uuid="b3ebe16f-42f2-4733-92eb-328ebf8e5d2e">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false" isItalic="true"/>
					</textElement>
					<text><![CDATA[1.1) Estado:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="128" y="77" width="59" height="12" forecolor="#000000" uuid="c89e2b72-f9c0-41d8-af6e-79b979ba9d9f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false" isItalic="true"/>
					</textElement>
					<text><![CDATA[1.2) Cidade:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="25" y="91" width="124" height="12" forecolor="#000000" uuid="4213d5ed-3d1b-4e1d-b9ed-c78f993ba97a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[1.3) Nome do Concessionário:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="4" y="109" width="177" height="14" forecolor="#000000" uuid="11dfff58-12b9-4589-9b15-5744a2e18a81">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[2. VEÍCULO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="17" y="127" width="374" height="12" forecolor="#000000" backcolor="#D9D9D9" uuid="67c74fc4-45f8-42d7-b797-b5a97f4eacff">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[VIN]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="391" y="127" width="88" height="12" forecolor="#000000" backcolor="#D9D9D9" uuid="6dc581a9-e2e8-4a5d-a6b0-7f7b52f29dec">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Quilometragem(km)]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="479" y="127" width="56" height="12" forecolor="#000000" backcolor="#D9D9D9" uuid="05196325-fa20-4f8d-a7d4-d7c51707f4e3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="17" y="139" width="22" height="22" forecolor="#000000" uuid="c123d3b2-1cc0-40c5-9a58-4079cea18184">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="171" y="139" width="22" height="22" forecolor="#000000" uuid="1e2742fa-9430-4539-af58-395008c315e2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS8}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="281" y="139" width="22" height="22" forecolor="#000000" uuid="3bbb5224-2c60-4969-b5c3-1fdb331fa5d2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS13}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="149" y="139" width="22" height="22" forecolor="#000000" uuid="302e1538-90e0-4460-bd6d-bf7a1a231f29">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS7}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="347" y="139" width="22" height="22" forecolor="#000000" uuid="107063db-f6b4-47e5-bf99-0f04baa711e4">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS16}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="105" y="139" width="22" height="22" forecolor="#000000" uuid="d87124a3-37c9-4719-ab56-3e5438b467f8">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS5}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="237" y="139" width="22" height="22" forecolor="#000000" uuid="65aa560d-1a42-4c29-a309-c0460dac2d3a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS11}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="325" y="139" width="22" height="22" forecolor="#000000" uuid="5989a46c-1c0d-4eae-b02f-933156ec1c2a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS15}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="83" y="139" width="22" height="22" forecolor="#000000" uuid="540f0500-a67f-4f6f-85df-5432202f9519">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS4}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="369" y="139" width="22" height="22" forecolor="#000000" uuid="cf8d9e86-ac54-47ca-b4cb-7da3f39fb57d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS17}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="193" y="139" width="22" height="22" forecolor="#000000" uuid="fd224c5f-f2ea-402c-a7db-1732891a1aad">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS9}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="215" y="139" width="22" height="22" forecolor="#000000" uuid="3c118dc0-d1b2-4bc4-9f2f-e101aeee7ba2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS10}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="127" y="139" width="22" height="22" forecolor="#000000" uuid="bb0a8aee-3002-4e92-9605-f53881c92629">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS6}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="61" y="139" width="22" height="22" forecolor="#000000" uuid="9956443b-e390-4cd0-9a59-6dbaeb586fe7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS3}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="259" y="139" width="22" height="22" forecolor="#000000" uuid="123427b4-9090-48f3-8f44-d4bd8e2d2e1f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS12}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="303" y="139" width="22" height="22" forecolor="#000000" uuid="5e8a6df9-3ccc-4899-927e-25e49bbe91c4">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS14}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="39" y="139" width="22" height="22" forecolor="#000000" uuid="ee470df7-98aa-486b-ad30-7babedd9c410">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CHASSI_POS2}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="149" y="91" width="387" height="12" forecolor="#000000" uuid="b7099997-8f94-422d-ba3c-c1053362179e">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="187" y="77" width="349" height="12" forecolor="#000000" uuid="885184c0-915d-42d4-b911-575587fd1ffb">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="83" y="77" width="42" height="12" forecolor="#000000" uuid="a5f3b3ab-0ed3-442a-947d-37b8dae30ddd">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_ESTADO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,###.###;(#,###.###-)">
					<reportElement mode="Transparent" x="391" y="139" width="88" height="22" forecolor="#000000" uuid="1e489db9-b7dc-4dd4-9680-03d05533cbf2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_KM}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="479" y="139" width="56" height="22" forecolor="#000000" uuid="d0a2398b-99f2-4d7d-aa27-7bf0062f9cf0">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="34">
			<printWhenExpression><![CDATA[$P{ID_CHECKLIST} != 30500]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="555" height="34" uuid="221ebda0-155f-4a52-8053-a553758e0da9"/>
				<box>
					<leftPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<frame>
					<reportElement mode="Opaque" x="18" y="20" width="518" height="12" backcolor="#FFE699" uuid="16347230-d440-4f9b-9302-327b5f4c394b"/>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="44" height="12" forecolor="#000000" uuid="265114df-8b2c-4187-9bc2-718c9c28c277">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="9" isBold="true"/>
						</textElement>
						<text><![CDATA[Legenda*:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="50" y="0" width="8" height="12" forecolor="#000000" uuid="a9846458-a1ff-4cc4-8dbc-b00387c6e28a">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="9" isBold="true"/>
						</textElement>
						<text><![CDATA[I:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="58" y="0" width="50" height="12" forecolor="#000000" uuid="9d353155-8d93-4592-924a-ecf6b2796494">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="9" isBold="false"/>
						</textElement>
						<text><![CDATA[Inspecionar]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="115" y="0" width="11" height="12" forecolor="#000000" uuid="991a145d-4e19-4267-b6d5-3c34a8492e4a">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="9" isBold="true"/>
						</textElement>
						<text><![CDATA[A:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="126" y="0" width="140" height="12" forecolor="#000000" uuid="56bc26fd-d0bc-43d3-be9c-e8020f148285">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="9" isBold="false"/>
						</textElement>
						<text><![CDATA[Apertar / Ajustar (*Se necessário)]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="270" y="0" width="11" height="12" forecolor="#000000" uuid="e33aecf8-acc4-40e3-9ebb-a3f8dec4fbaa">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="9" isBold="true"/>
						</textElement>
						<text><![CDATA[R:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="281" y="0" width="140" height="12" forecolor="#000000" uuid="67617a6c-1dec-4e9d-9e7d-1a2f53cbf45e">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="9" isBold="false"/>
						</textElement>
						<text><![CDATA[Remover]]></text>
					</staticText>
				</frame>
				<staticText>
					<reportElement mode="Transparent" x="5" y="4" width="177" height="14" forecolor="#000000" uuid="6e49bbec-5ca3-4800-b19b-29264d5eb066">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[3. CHECKLIST]]></text>
				</staticText>
			</frame>
		</band>
		<band height="30">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$P{ID_CHECKLIST} != 30500.0 && $P{ID_CHECKLIST} != 30750.0 && $P{ID_CHECKLIST} != 31550.0]]></printWhenExpression>
			<subreport isUsingCache="false" overflowType="Stretch">
				<reportElement key="" x="0" y="0" width="555" height="30" isRemoveLineWhenBlank="true" uuid="0b13197c-a185-429d-b315-188ecc0125f8">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="LISTA_GRUPOS">
					<subreportParameterExpression><![CDATA[$P{LISTA_GRUPOS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListGwmPdsSubGrupo.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="30">
			<printWhenExpression><![CDATA[$P{ID_CHECKLIST} == 30500.0]]></printWhenExpression>
			<subreport overflowType="Stretch">
				<reportElement key="" x="0" y="0" width="555" height="30" isRemoveLineWhenBlank="true" uuid="68564f53-867c-4579-a6e3-b4b1427688a4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="LISTA_GRUPOS">
					<subreportParameterExpression><![CDATA[$P{LISTA_GRUPOS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListGwmPdsSubGrupoPadrao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="30">
			<printWhenExpression><![CDATA[$P{ID_CHECKLIST} == 30750.0 || $P{ID_CHECKLIST} == 31550.0]]></printWhenExpression>
			<subreport isUsingCache="false" overflowType="Stretch">
				<reportElement key="" x="0" y="0" width="555" height="30" isRemoveLineWhenBlank="true" uuid="f2eb27c9-5f87-4fca-af31-da4f574e72a0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ID_CHECKLIST">
					<subreportParameterExpression><![CDATA[$P{ID_CHECKLIST}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListGwmPdsSubGrupoOpcaoNaoAplicavel.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="27">
			<printWhenExpression><![CDATA[Boolean.FALSE]]></printWhenExpression>
			<subreport>
				<reportElement x="0" y="8" width="555" height="19" isRemoveLineWhenBlank="true" uuid="d86ecbbe-e310-427f-a1ff-efa22e96b6f0"/>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="LISTA_GRUPOS">
					<subreportParameterExpression><![CDATA[$P{LISTA_GRUPOS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListGwmPdsSubObservacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="90">
			<printWhenExpression><![CDATA[$P{ID_CHECKLIST} != 30500]]></printWhenExpression>
			<frame>
				<reportElement mode="Opaque" x="1" y="8" width="554" height="72" backcolor="#D9D9D9" uuid="09143d0f-d5eb-498d-8d84-e7fa5603873f"/>
				<staticText>
					<reportElement mode="Transparent" x="3" y="0" width="124" height="12" forecolor="#000000" uuid="5de0923f-7146-4aba-9f0a-0c37d9d0fba0">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Nota:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="3" y="14" width="466" height="12" forecolor="#000000" uuid="712c631a-c0d2-4097-9351-e50d44b11338">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Confirmo que todos os itens listados foram devidamente checados]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="3" y="28" width="67" height="12" forecolor="#000000" uuid="0c6aec8b-444d-4d3d-b4fa-4c8248d0d636">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="3" y="42" width="67" height="12" forecolor="#000000" uuid="ff9783c1-eff1-4763-9e34-d0e630cc4787">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Função/Cargo:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="3" y="56" width="67" height="12" forecolor="#000000" uuid="05ee1753-ba2c-44a5-8855-303b06933e08">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="70" y="28" width="400" height="12" forecolor="#000000" uuid="5c5c11d2-dc92-4e55-b6ad-75ce5d335fe6">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CONSULTOR_COMPLETO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="70" y="42" width="400" height="12" forecolor="#000000" uuid="555d2316-cf57-4586-a90f-deabff907b1d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CONSULTOR_FUNCAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="70" y="56" width="400" height="12" forecolor="#000000" uuid="a7d9bb7f-8f14-4aaf-9b48-21c358b7ae85">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_HOJE}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="50">
			<subreport overflowType="Stretch">
				<reportElement key="" x="0" y="0" width="555" height="30" isRemoveLineWhenBlank="true" uuid="fa475351-5226-43f1-bab0-3106906e8e49">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ID_CHECKLIST">
					<subreportParameterExpression><![CDATA[$P{ID_CHECKLIST}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListGwmPdsSubDiarioDeBordo.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="290">
			<printWhenExpression><![CDATA[Boolean.FALSE]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="8" width="554" height="282" uuid="85368165-84b5-4cbe-abcc-abfdf11e93c8"/>
				<staticText>
					<reportElement mode="Transparent" x="2" y="1" width="130" height="12" forecolor="#000000" uuid="bf8d22bc-ad70-4bfb-b452-c135a09722ec">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Solicitações ao Distruibuidor:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="3" y="14" width="548" height="26" forecolor="#000000" uuid="c407155a-5fc8-48ed-8f08-7fe44063fe65">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[1) Após o término da inspeção do veículo com o devido preenchimento deste formulário PDS, solicitamos que este seja enviado para o e-mail (<EMAIL>);]]></text>
				</staticText>
				<frame>
					<reportElement x="3" y="46" width="548" height="26" uuid="1982639a-9f23-463f-bcdf-b816e2fc4317"/>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="542" height="26" forecolor="#000000" uuid="a9e44645-b37f-4c3b-924b-ed55ca2ac764">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="9" isBold="false"/>
						</textElement>
						<text><![CDATA[2) O Arquivo deve ser renomeado ANTES do envio para a GWM      e deverá conter informações padronizadas do seu distribuidor e do veículo inspecionado, conforme modelo abaixo:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Opaque" x="90" y="0" width="183" height="12" forecolor="#000000" uuid="9795abc8-f7f5-4e3f-afb4-98aac46086ce">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="9" isBold="true" isUnderline="true"/>
						</textElement>
						<text><![CDATA[renomeados ANTES do envio para a GWM]]></text>
					</staticText>
				</frame>
				<image scaleImage="RealSize" hAlign="Center" vAlign="Middle">
					<reportElement x="62" y="114" width="403" height="76" uuid="4557e9b3-0e45-42e6-99b5-a075f9299095"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice386026.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="330" y="232" width="218" height="12" forecolor="#000000" uuid="7b5b2f73-6073-40ea-a831-2d09ae38ed5d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Atenciosamente,]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="330" y="244" width="218" height="12" forecolor="#000000" uuid="437cf031-9f05-4510-a981-7fdfbeeedb5c">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[ENGENHARIA DE SERVIÇOS GWM]]></text>
				</staticText>
				<textField pattern="MMMM, yyyy">
					<reportElement mode="Transparent" x="330" y="256" width="185" height="12" forecolor="#000000" uuid="9910ec85-d664-4d67-9022-4bc227e2857d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="9" isBold="false" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_HOJE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="515" y="256" width="31" height="12" forecolor="#000000" uuid="7d09559f-802c-4fa1-ad29-a502896da22e">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="9" isBold="false" isItalic="true"/>
					</textElement>
					<text><![CDATA[(Ver01)]]></text>
				</staticText>
			</frame>
		</band>
		<band height="36">
			<subreport overflowType="Stretch">
				<reportElement key="" x="0" y="0" width="555" height="30" isRemoveLineWhenBlank="true" uuid="7f6e9bb4-b56a-46bc-871a-393454db048e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ID_CHECKLIST">
					<subreportParameterExpression><![CDATA[$P{ID_CHECKLIST}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "ChecklistGwmPdsSubAssinaturas.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band height="17">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="249" y="0" width="62" height="12" uuid="19940020-07f0-49cf-8b67-452b270696cb"/>
				<textField>
					<reportElement x="26" y="0" width="16" height="12" uuid="417d6604-52b5-4f5d-9fad-03d1511231dd"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="0" width="26" height="12" uuid="da026dae-e7c4-4b2e-af22-3d4223f4f1a0">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Página]]></text>
				</staticText>
				<staticText>
					<reportElement x="42" y="0" width="4" height="12" uuid="a247a548-0187-4cab-96ad-653a0f139e19"/>
					<textElement verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[/]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement x="46" y="0" width="16" height="12" uuid="5c1adedb-3fb2-43b7-893a-3e9705a9e18f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnFooter>
</jasperReport>
