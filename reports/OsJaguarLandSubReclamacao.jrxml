<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsJaguarLandSubReclamacao" pageWidth="532" pageHeight="84" whenNoDataType="AllSectionsNoDetail" columnWidth="532" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH Q_RECLAMACAO AS (
SELECT OSO.COD_EMPRESA, 
       OSO.NUMERO_OS, 
       0 AS COD_OS_AGENDA,
       OSO.ITEM, 
       OSO.DESCRICAO,
       OSO.COD_GWM_RECLAMACAO
  FROM OS_ORIGINAL OSO
 WHERE OSO.COD_EMPRESA = $P{COD_EMPRESA}
   AND OSO.NUMERO_OS = $P{NUMERO_OS}
   ORDER BY OSO.ITEM 
),

TENHO AS (
SELECT COUNT(*) AS LINHAS
                  FROM OS_ORIGINAL OSO
                 WHERE OSO.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSO.NUMERO_OS = $P{NUMERO_OS}
),

MAIOR_QUANTIDADE AS (
       SELECT (MAX(TOT)) AS LINHAS
        FROM (SELECT COUNT(*) AS TOT
                  FROM OS_ORIGINAL OSO
                 WHERE OSO.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSO.NUMERO_OS = $P{NUMERO_OS}
                UNION ALL
                SELECT COUNT(*) AS TOT
                  FROM OS_SERVICOS OSS
                 WHERE OSS.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSS.NUMERO_OS = $P{NUMERO_OS}
                UNION ALL
                SELECT COUNT(*) AS TOT
                  FROM OS_REQUISICOES OSR
                 WHERE OSR.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSR.NUMERO_OS = $P{NUMERO_OS})
),

DEVE_TER AS (
       SELECT CASE WHEN MOD(MAIOR_QUANTIDADE.LINHAS,6) = 0
              THEN MAIOR_QUANTIDADE.LINHAS + 1
              ELSE (MAIOR_QUANTIDADE.LINHAS + ( 7 - MOD(MAIOR_QUANTIDADE.LINHAS,6))) 
              END AS LINHAS
        FROM MAIOR_QUANTIDADE
),

Q_NULLROWS AS (SELECT NULL AS COD_EMPRESA, 
                     NULL AS NUMERO_OS, 
                     NULL AS COD_OS_AGENDA,
                     NULL AS ITEM, 
                     NULL AS DESCRICAO,
                     NULL AS COD_GWM_RECLAMACAO
                     FROM DUAL, TENHO,MAIOR_QUANTIDADE,DEVE_TER
                     WHERE MOD(TENHO.LINHAS,6)<>0 OR TENHO.LINHAS = 0
                     CONNECT BY LEVEL < DEVE_TER.LINHAS - TENHO.LINHAS
)

SELECT Q_RECLAMACAO.* FROM Q_RECLAMACAO
UNION ALL
SELECT Q_NULLROWS.* FROM Q_NULLROWS]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="COD_GWM_RECLAMACAO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="12" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="105" y="0" width="427" height="12" backcolor="#E3E3E3" uuid="f580425e-573e-4f1e-8a1d-be3a3a5e94a0"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[VOZ DO CLIENTE]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="43" height="12" backcolor="#E3E3E3" uuid="8020aab8-8da2-4f1c-a13f-f0e1fba3c1a3"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[ÍTEM Nº.]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="43" y="0" width="62" height="12" backcolor="#E3E3E3" uuid="7157b20a-2a9b-496b-8d3b-ff1e0047e6a6"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[CCC]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement mode="Transparent" x="0" y="0" width="43" height="12" uuid="69e4b9c6-0d21-4db9-91a4-0a401e04e6fe"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_GWM_RECLAMACAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="43" y="0" width="62" height="12" uuid="f1f69431-24c4-4644-86a7-b43af530d15d"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="105" y="0" width="427" height="12" uuid="0f1c7aea-88ff-460b-ae9b-276b54185318"/>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
