<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsFCASubServicos" pageWidth="555" pageHeight="842" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="campoNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT 'Serviço' AS TIPO,
       OS_SERVICOS.ITEM,
       OS_SERVICOS.COD_SERVICO AS ORDEM,
       OS_SERVICOS.COD_SERVICO AS COD_ITEM,
       SERVICOS.DESCRICAO_SERVICO AS DESCRICAO,
       NVL(OS_SERVICOS.TEMPO_PADRAO, 1) AS TEMPO_PADRAO,
       OS_SERVICOS.PRECO_VENDA + NVL(ADI.PRECO_ADI, 0) AS PRECO_VENDA,
       OS_SERVICOS.PRECO_VENDA + NVL(ADI.PRECO_ADI, 0) AS PRECO_TOTAL,
       0.00 AS EST_DISP
  FROM OS_SERVICOS,
       SERVICOS,
       (SELECT SUM(OS_SERVICOS_ADICIONAIS.PRECO_VENDA) AS PRECO_ADI,
               OS_SERVICOS_ADICIONAIS.COD_SERVICO
          FROM OS_SERVICOS_ADICIONAIS
         WHERE OS_SERVICOS_ADICIONAIS.NUMERO_OS = $P{NUMERO_OS}
           AND OS_SERVICOS_ADICIONAIS.COD_EMPRESA = $P{COD_EMPRESA}
         GROUP BY OS_SERVICOS_ADICIONAIS.COD_SERVICO) ADI

 WHERE OS_SERVICOS.COD_SERVICO = SERVICOS.COD_SERVICO
   AND OS_SERVICOS.NUMERO_OS = $P{NUMERO_OS}
   AND OS_SERVICOS.COD_EMPRESA = $P{COD_EMPRESA}
   AND NVL(OS_SERVICOS.TIPO_MMC, 'S') = 'S'
   AND OS_SERVICOS.COD_SERVICO = ADI.COD_SERVICO(+)

UNION

SELECT 'Peça' AS TIPO,
       OSS.ITEM,
       OS_REQUISICOES.COD_SERVICO AS ORDEM,
       ITENS.COD_ITEM AS COD_ITEM,
       DECODE(FORNECEDOR_ESTOQUE.OFICIAL,
              'N',
              '*' || ITENS.DESCRICAO,
              ITENS.DESCRICAO) AS DESCRICAO,
       OS_REQUISICOES.QUANTIDADE,
       DECODE(OS.STATUS_OS,
              1,
              OS_REQUISICOES.PRECO_FINAL,
              DECODE(OS.CORTESIA,
                     'S',
                     OS_REQUISICOES.PRECO_CORTESIA,
                     DECODE(OS_TIPOS.INTERNO,
                            'S',
                            ROUND((100 +
                                  DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS,
                                          'S',
                                          DECODE(ITENS.COD_TRIBUTACAO,
                                                 '1',
                                                 DECODE(PARM_SYS.REGIME_ICMS,
                                                        'S',
                                                        DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                               'S',
                                                               DECODE(ICC.CLASSE_PECA,
                                                                      2,
                                                                      OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                      0),
                                                               0),
                                                        OS_TIPOS.AUMENTO_PRECO_PECA),
                                                 0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                                  DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                         'V',
                                         OS_REQUISICOES.PRECO_VENDA,
                                         'G',
                                         OS_REQUISICOES.PRECO_GARANTIA,
                                         'F',
                                         OS_REQUISICOES.CUSTO_FORNECEDOR,
                                         'P',
                                         OS_REQUISICOES.PRECO_ORIGINAL,
                                         OS_REQUISICOES.CUSTO_CONTABIL),
                                  6) / 100,
                            DECODE(OS_TIPOS.GARANTIA,
                                   'S',
                                   DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                          'P',
                                          NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                              OS_REQUISICOES.PRECO_GARANTIA),
                                          OS_REQUISICOES.PRECO_GARANTIA),
                                   DECODE(NVL(OS.FABRICA, 'N'),
                                          'S',
                                          OS_REQUISICOES.PRECO_GARANTIA,
                                          DECODE(SIGN(OS.FRANQUIA),
                                                 1,
                                                 PRECO_FRANQUIA,
                                                 ROUND((100 - NVL(SEGURADORA.DESCONTO_REQUISICAO,
                                                                  0)) *
                                                       DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                                              'P',
                                                              NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                                                  OS_REQUISICOES.PRECO_VENDA),
                                                              OS_REQUISICOES.PRECO_VENDA),
                                                       6) / 100)))))) AS PRECO_VENDA,
       OS_REQUISICOES.QUANTIDADE *
       DECODE(OS.STATUS_OS,
              1,
              OS_REQUISICOES.PRECO_FINAL,
              DECODE(OS.CORTESIA,
                     'S',
                     OS_REQUISICOES.PRECO_CORTESIA,
                     DECODE(OS_TIPOS.INTERNO,
                            'S',
                            ROUND((100 +
                                  DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS,
                                          'S',
                                          DECODE(ITENS.COD_TRIBUTACAO,
                                                 '1',
                                                 DECODE(PARM_SYS.REGIME_ICMS,
                                                        'S',
                                                        DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                               'S',
                                                               DECODE(ICC.CLASSE_PECA,
                                                                      2,
                                                                      OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                      0),
                                                               0),
                                                        OS_TIPOS.AUMENTO_PRECO_PECA),
                                                 0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                                  DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                         'V',
                                         OS_REQUISICOES.PRECO_VENDA,
                                         'G',
                                         OS_REQUISICOES.PRECO_GARANTIA,
                                         'F',
                                         OS_REQUISICOES.CUSTO_FORNECEDOR,
                                         'P',
                                         OS_REQUISICOES.PRECO_ORIGINAL,
                                         OS_REQUISICOES.CUSTO_CONTABIL),
                                  6) / 100,
                            DECODE(OS_TIPOS.GARANTIA,
                                   'S',
                                   OS_REQUISICOES.PRECO_GARANTIA,
                                   DECODE(NVL(OS.FABRICA, 'N'),
                                          'S',
                                          OS_REQUISICOES.PRECO_GARANTIA,
                                          DECODE(SIGN(OS.FRANQUIA),
                                                 1,
                                                 PRECO_FRANQUIA,
                                                 ROUND((100 - NVL(SEGURADORA.DESCONTO_REQUISICAO,
                                                                  0)) *
                                                       DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                                              'V',
                                                              OS_REQUISICOES.PRECO_VENDA,
                                                              NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                                                  OS_REQUISICOES.PRECO_VENDA)),
                                                       6) / 100)))))) AS PRECO_TOTAL,
       NVL(ESTOQUE.QTDE, 0) - NVL(ESTOQUE.RESERVADO, 0) AS EST_DISP
  FROM OS_REQUISICOES,
       ESTOQUE,
       ITENS,
       OS,
       VW_OS_TIPOS           OS_TIPOS,
       FORNECEDOR_ESTOQUE,
       SEGURADORA,
       ITENS_FORNECEDOR,
       ITENS_CLASSE_CONTABIL ICC,
       PARM_SYS,
       PARM_SYS2,
       OS_SERVICOS           OSS
 WHERE (OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM)
   AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR
   AND (OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM(+))
   AND (OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR(+))
   AND (OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA(+))
   AND (OS_REQUISICOES.NUMERO_OS = OS.NUMERO_OS)
   AND (OS_REQUISICOES.COD_EMPRESA = OS.COD_EMPRESA)
   AND (OS.TIPO = OS_TIPOS.TIPO)
   AND (OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA)
   AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA(+)
   AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
   AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
   AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL(+)
   AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
   AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
   AND (OS_REQUISICOES.NUMERO_OS = $P{NUMERO_OS})
   AND (OS_REQUISICOES.COD_EMPRESA = $P{COD_EMPRESA})
   AND OS_REQUISICOES.COD_EMPRESA = OSS.COD_EMPRESA
   AND OS_REQUISICOES.NUMERO_OS = OSS.NUMERO_OS
   AND OS_REQUISICOES.ITEM = OSS.ITEM
   AND OS_REQUISICOES.COD_SERVICO = OSS.COD_SERVICO
   AND NVL(OSS.TIPO_MMC, 'S') = 'S'

 ORDER BY ORDEM, TIPO DESC]]>
	</queryString>
	<field name="TIPO" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="ORDEM" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="TEMPO_PADRAO" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="PRECO_TOTAL" class="java.lang.Double"/>
	<field name="EST_DISP" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="27">
			<frame>
				<reportElement x="0" y="0" width="555" height="27" uuid="0accd5a8-e502-4c5d-8e8d-0981ff555504"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="5" y="2" width="143" height="12" uuid="f6023108-fc60-4b5c-9d7a-e38b22b7d067"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Sugestão {peças e serviços}]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="15" width="16" height="12" uuid="69f31242-bb37-4520-b310-01551e37488b"/>
					<box>
						<topPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[It]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="16" y="15" width="35" height="12" uuid="7f28eec8-b0be-43b6-9b65-6b8b749e5f0f"/>
					<box leftPadding="3">
						<topPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="51" y="15" width="100" height="12" uuid="3254a223-a8f2-4cef-92ab-1ed6e5092958"/>
					<box leftPadding="3">
						<topPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Código]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="151" y="15" width="175" height="12" uuid="55eab8b7-1a25-4724-8822-85ec6dd332d2"/>
					<box leftPadding="3">
						<topPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Descrição]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="357" y="15" width="44" height="12" uuid="e3095f57-1c4f-4078-a391-6a57a9be63fb"/>
					<box>
						<topPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Qtde]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="401" y="15" width="66" height="12" uuid="6c64031e-0f01-4079-8791-c6dc8a7af7db"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Preço Unitário]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="467" y="15" width="88" height="12" uuid="fefd560c-ba61-462f-ad48-5dde484e8fc9"/>
					<box rightPadding="3">
						<topPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Preço Total]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="326" y="15" width="31" height="12" uuid="2d791dc7-f57c-4ff4-b7c7-be8598efaa1f"/>
					<box>
						<topPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Disp]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="13">
			<frame>
				<reportElement x="0" y="0" width="555" height="13" uuid="f978ebad-9180-4940-b1a7-6bde176b825e">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement mode="Transparent" x="357" y="0" width="44" height="13" uuid="e072e22c-3309-4ca7-b15c-f0059da65583"/>
					<box rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
				</textField>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement mode="Transparent" x="0" y="0" width="16" height="13" uuid="9c319f9c-bfe9-4b4f-b023-2967efe5180b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="16" y="0" width="35" height="13" uuid="d44e938f-f164-479e-975b-e914d9c31c78"/>
					<box leftPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TIPO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="51" y="0" width="100" height="13" uuid="450c878b-b3b4-4564-b672-ebd50a696998"/>
					<box leftPadding="5">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COD_ITEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="151" y="0" width="175" height="13" uuid="0a117629-9f5e-46a8-b23e-3dac156582e1"/>
					<box leftPadding="5">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
						<paragraph tabStopWidth="40"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="401" y="0" width="66" height="13" uuid="3c2ea919-2450-4a38-a4bd-2b7d2c2cd3f2"/>
					<box rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement mode="Transparent" x="467" y="0" width="88" height="13" uuid="9cbdaf75-44a1-4f53-a00c-265f3af6c062"/>
					<box rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRECO_TOTAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="326" y="0" width="31" height="13" uuid="486cd3d2-5b79-4ca5-b74e-2d7518949011"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EST_DISP}> 0.0 ? "✓":""]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
