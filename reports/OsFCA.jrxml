<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsFCA" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" isFloatColumnFooter="true" uuid="a6c8faac-fc28-4e0c-a20a-71416dbce697">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="353"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="633"/>
	<style name="campoTexto" isDefault="true" vTextAlign="Justified" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\27369\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT
  
  OS_AGENDA.SIGNATURE AS OS_ASSINATURA,
  OS.COD_EMPRESA                    AS OS_COD_EMPRESA,
  OS.STATUS_OS                    AS OS_STATUS_OS,
  OS.COD_OS_AGENDA                  AS OS_COD_OS_AGENDA,
  NVL(OS.NUMERO_OS, 0)                    AS OS_NUMERO_OS, 
  ABS(OS.NUMERO_OS)                  AS OS_ABS_OSNUM,
  OS.NUMERO_OS_FABRICA                AS OS_NUMERO_OS_FABRICA,
  OS.COD_CLIENTE                    AS OS_COD_CLIENTE, 
  OS.CLIENTE_RAPIDO                  AS OS_CLIENTE_RAPIDO,
  OS.TIPO_ENDERECO                  AS OS_TIPO_ENDERECO,
  NVL(OS.OBSERVACAO, ' ')                    AS OS_OBSERVACAO,
  OS.EXTENDIDA                    AS OS_EXTENDIDA,
  OS.SEGURADORA                    AS OS_SEGURADORA,
  NVL(TO_CHAR(OS.DATA_EMISSAO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_EMISSAO,
  NVL(TO_CHAR(OS.DATA_LIBERADO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_LIBERADO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2)     AS OS_DIA_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2)     AS OS_MES_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4)     AS OS_ANO_EMISSAO,
  OS.HORA_EMISSAO                    AS OS_HORA_EMISSAO,
  OS.HORA_ENCERRADA                  AS OS_HORA_ENCERRADA,
  OS.DATA_ENCERRADA                  AS OS_DATA_ENCERRADA,
  NVL(OS.HORA_PROMETIDA,' ')                  AS OS_HORA_PROMETIDA, 
  SUBSTR(OS.HORA_PROMETIDA, 1, 2)           AS OS_HORA24_PROMETIDA,
  SUBSTR(OS.HORA_PROMETIDA, 4, 2)           AS OS_MINUTO_PROMETIDA,
  NVL(TO_CHAR(OS.DATA_PROMETIDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PROMETIDA, 
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2)     AS OS_DIA_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2)     AS OS_MES_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4)   AS OS_ANO_PROMETIDA,
  NVL(OS.VALOR_SERVICOS_BRUTO, 0)                AS OS_VALOR_SERVICOS_BRUTO,
  NVL(OS.VALOR_ITENS_BRUTO, 0)                AS OS_VALOR_ITENS_BRUTO,
  NVL(OS.DESCONTOS_SERVICOS, 0)                AS OS_DESCONTOS_SERVICOS,
  NVL(OS.DESCONTOS_ITENS, 0)                  AS OS_DESCONTOS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS,0)                           AS OS_TOTAL_OS_SERVICOS,
  NVL(OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS,0)                                 AS OS_TOTAL_OS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO,0)                            AS OS_TOTAL_OS_BRUTO,
  NVL(OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS,0)                                AS OS_TOTAL_OS_DESCONTO,
  NVL((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) + (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS), 0)   AS OS_TOTAL_OS,
  OS.COD_SEGURADORA                  AS OS_COD_SEGURADORA,
  OS.TIPO                        AS OS_TIPO,
  OS.NOME                       AS CONSULTOR,
  OS.OS_ENTRADA                    AS OS_OS_ENTRADA,
  '1ª VIA CLIENTE 1ª VIA CLIENTE 1ª VIA CLIENTE'     AS OS_VIA,
  OS.TOTAL_IMPRESSAO_FABRICA              AS OS_TOTAL_IMPRESSAO_FABRICA,
  NVL(OS_TIPOS_TERMO.TEXTO,' ')            AS OS_TERMO_TEXTO,
  NVL(TO_CHAR(OS.DATA_TAXI, 'DD/MM/YYYY'),' ')                  AS OS_DATA_TAXI,
  NVL(OS.HORA_TAXI,' ')                  AS OS_HORA_TAXI, 
  NVL(OS.TODO_TRAB,' ') AS OS_TODO_TRABALHO_FEITO,
  NVL(OS.TODO_LIMPO,' ') AS OS_TODO_LIMPO,
  
  NVL(os.pri_rodagem,'N')                  AS OS_PRIMEIRA_RODAGEM,
  NVL(os.seg_rodagem,'N')                  AS OS_SEGUNDA_RODAGEM,
  NVL(TO_CHAR(os.data_pri_rodagem, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PRIMEIRA_RODAGEM,
  NVL(TO_CHAR(os.data_seg_rodagem, 'DD/MM/YYYY'),' ')                  AS OS_DATA_SEGUNDA_RODAGEM,
  NVL(os.os_pri_resp,' ')                  AS OS_PRIM_RESPONSAVEL_RODAGEM,
  NVL(os.os_seg_resp,' ')                  AS OS_SEG_RESPONSAVEL_RODAGEM,
  
 
  
  (SELECT LISTAGG(FORMAS_PAGAMENTO.CONDICAO_PAGAMENTO, '; ') WITHIN GROUP (ORDER BY CONDICAO_PAGAMENTO)        
  FROM (SELECT (F.DESCRICAO || ' ' ||  TO_CHAR(O.VALOR, 'FM999999999D00')) AS CONDICAO_PAGAMENTO FROM OS_PAGAMENTO O, FORMA_PGTO F
  WHERE O.COD_FORMA_PGTO = F.COD_FORMA_PGTO
	  AND O.NUMERO_OS = $P{NUMERO_OS}
	  AND O.COD_EMPRESA= $P{COD_EMPRESA}) FORMAS_PAGAMENTO) AS OS_DESCRICAO_PAGAMENTO,

	
	TIPO_PAGAMENTO.AVISTA		AS OS_PAGAMENTO_DINHEIRO,
	TIPO_PAGAMENTO.CARTAO		AS OS_PAGAMENTO_CARTAO,


  
  (SELECT LISTAGG(ORCAMENTOS.NUMERO_OS, ', ') WITHIN GROUP (ORDER BY NUMERO_OS)
	FROM 
	  (SELECT ABS(OS.NUMERO_OS) AS NUMERO_OS
	  FROM OS, OS_STATUS, OS_ORCAMENTOS, OS_TIPOS
	  WHERE OS.STATUS_OS = OS_STATUS.STATUS_OS
		AND OS_ORCAMENTOS.COD_EMPRESA = OS.COD_EMPRESA
		AND OS_ORCAMENTOS.NUMERO_ORCAMENTO = OS.NUMERO_OS
		AND OS.TIPO = OS_TIPOS.TIPO
		AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
		AND OS_ORCAMENTOS.COD_EMPRESA = $P{COD_EMPRESA}
		AND OS_ORCAMENTOS.NUMERO_OS = $P{NUMERO_OS}
	  GROUP BY OS.NUMERO_OS
	  ORDER BY OS.NUMERO_OS) ORCAMENTOS) AS OS_NUMERO_ORCAMENTO,
	  
	(SELECT LISTAGG(ORCAMENTOS.NOME_CLIENTE_APROVOU, ', ') WITHIN GROUP (ORDER BY ORCAMENTOS.NOME_CLIENTE_APROVOU) 
		FROM 
    (SELECT OS_ORCAMENTOS.NOMECLIAPROVOU AS NOME_CLIENTE_APROVOU
    FROM OS, OS_STATUS, OS_ORCAMENTOS, OS_TIPOS
    WHERE OS.STATUS_OS = OS_STATUS.STATUS_OS
    AND OS_ORCAMENTOS.COD_EMPRESA = OS.COD_EMPRESA
    AND OS_ORCAMENTOS.NUMERO_ORCAMENTO = OS.NUMERO_OS
    AND OS.TIPO = OS_TIPOS.TIPO
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
    AND OS_ORCAMENTOS.COD_EMPRESA = 34
    AND OS_ORCAMENTOS.NUMERO_OS = 23493
    GROUP BY OS_ORCAMENTOS.NOMECLIAPROVOU
    ORDER BY OS_ORCAMENTOS.NOMECLIAPROVOU) ORCAMENTOS) AS OS_NOME_CLIENTE_APROVOU,

  NVL(OS.ORC_SERV_BRUTO + OS.ORC_ITEM_BRUTO, 0) AS OS_TOTAL_ORCAMENTO,
  NVL(OS.AUTORIZADO_TODO_ORCAMENTO,'N') OS_ORCAMENTO_AUTORIZADO,
  ' ' AS OS_DATA_ORCAMENTO_APROVADO,
  
  
  
  GARANTIA_DOC.COD_SG_PADRAO AS OS_GARANTIA_SG_PADRAO,
  GARANTIA_DOC.COD_SG_EXEMPLO AS OS_GARANTIA_SG_EXEMPLO,
  
  
  
  OS.LAVAR_VEICULO AS OS_LAVAR_VEICULO,
  OS.CLIENTE_AGUARDOU AS OS_CLIENTE_AGUARDOU,
  
  CASE NVL(OS_AGENDA.EH_RETORNO,'N')
         WHEN 'N' THEN 'N'
         WHEN 'S' THEN 'S'
         ELSE
          'REDE'
       END AS OS_EH_RETORNO,

  NVL(OS_AGENDA.EH_RECALL,'N') AS EH_RECALL,
     
   TO_CHAR(OS.DATA_AGENDADA_RECEPCAO, 'DD/MM/YYYY') AS DATA_AGENDAMENTO,
   
    
   TO_CHAR(OS.DATA_PROMETIDA_REVISADA, 'DD/MM/YYYY') AS DATA_PROMETIDA_REVISADA,
   TO_CHAR(OS.HORA_PROMETIDA_REVISADA, 'HH24:MI:SS') AS HORA_PROMETIDA_REVISADA,
   
  
  (SELECT EE.NOME_COMPLETO 
          FROM EMPRESAS_USUARIOS EE 
         WHERE EE.COD_EMPRESA = OS.COD_EMPRESA 
           AND EE.NOME = OS.QUEM_ABRIU) AS OS_NOME_AGENDADOR, 
       
   OS_AGENDA.EH_FIAT_PROFISSIONAL AS OS_FIAT_PROFISSIONAL,
   
   OS.PECA_USADA_FICA_CLIENTE AS OS_PECA_USADA_FICA_CLIENTE,
   
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'DD/MM/YYYY') AS OS_DATA_ULT_IMRESSAO,
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'HH24:MI') AS OS_HORA_ULT_IMRESSAO,
   
   NVL(OS_AGENDA.VEICULO_PLATAFORMA,'N') AS OS_VEICULO_PLATAFORMA,
   
   TO_CHAR(OS_AGENDA.DATA_AGENDADA , 'HH24:MI') AS OS_HORA_AGENDADA,
   
   (SELECT PB.DESCRICAO
          FROM PRISMA_BOX PB
         WHERE PB.PRISMA = OS_DADOS_VEICULOS.PRISMA) AS OS_DESCRICAO_PRISMA,
   KM_PROXIMA_REVISAO,
   DATA_PROXIMO_SERVICO,
    
  
  NVL(EMPRESAS_USUARIOS.NOME_COMPLETO, ' ')           AS OS_CONSULTOR_COMPLETO,
  EMPRESAS_USUARIOS.CODIGO_OPCIONAL AS OS_CODIGO_OPCIONAL,
  
  
  
  
  OS_TIPOS.DESCRICAO                         AS OS_TIPO_DESCRICAO,
  OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO              AS OS_TIPO_COM_DESCRICAO,
  NVL(OS_TIPOS.GARANTIA,'N')                        AS OS_GARANTIA,
  OS_TIPOS.REVISAO_GRATUITA                    AS OS_REVISAO_GRATUITA,
  NVL(OS_TIPOS.INTERNO,'N')          AS OS_INTERNO,
  OS_TIPOS.COD_CLIENTE               AS OS_CLIENTE_DO_TIPO,
  OS_TIPOS.OUTRO_CONCESSIONARIA                  AS OS_OUTRO_CONCESSIONARIA,
  NVL(OS_TIPOS.TIPO_FABRICA_EMPRESA,OS_TIPOS.TIPO_FABRICA)     AS OS_TIPO_FABRICA,
  
  
  NVL(OS_DADOS_VEICULOS.ANO, ' ')                      AS OS_ANO,
  OS_DADOS_VEICULOS.HORIMETRO                    AS OS_HORIMETRO,
  NVL(OS_DADOS_VEICULOS.PRISMA, ' ')                    AS OS_PRISMA,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2)     AS DIA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2)     AS MES_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4)   AS ANO_VENDA,
  NVL(OS_DADOS_VEICULOS.COMBUSTIVEL,0)                  AS OS_COMBUSTIVEL,
  NVL(OS_DADOS_VEICULOS.COR_EXTERNA, ' ')                  AS OS_COR_EXTERNA,
  NVL(OS_DADOS_VEICULOS.PLACA, ' ')                      AS OS_PLACA,
  NVL(OS_DADOS_VEICULOS.KM, 0)                      AS OS_KM,
  NVL(OS_DADOS_VEICULOS.CHASSI, ' ')                    AS OS_CHASSI,
  NVL(OS_DADOS_VEICULOS.NUMERO_MOTOR, ' ')                  AS OS_NUMERO_MOTOR,
  OS_DADOS_VEICULOS.NUMERO_RENAVAM                AS OS_NUMERO_RENAVAM,
  OS_DADOS_VEICULOS.SERIE                      AS OS_SERIE,
  OS_DADOS_VEICULOS.COD_CONCESSIONARIA              AS OS_COD_CONCESSIONARIA,
  OS_DADOS_VEICULOS.ESTADO_PINTURA                AS OS_ESTADO_PINTURA,
  NVL(OS_DADOS_VEICULOS.JOGO_FERRAMENTAS, ' ')                AS OS_JOGO_FERRAMENTAS,
  NVL(OS_DADOS_VEICULOS.ELASTICOS, ' ')                    AS OS_ELASTICOS,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_D, ' ')                AS OS_TAMPA_LATERAL_D,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_E, ' ')                AS OS_TAMPA_LATERAL_E,
  NVL(OS_DADOS_VEICULOS.FLANELA, ' ')                    AS OS_FLANELA,
  OS_DADOS_VEICULOS.NUMERO_FROTA                  AS OS_NUMERO_FROTA,
  
  
  NVL(CONCESSIONARIAS.NOME, ' ')                          AS CONCESSIONARIA_NOME,
  CONCESSIONARIAS.UF                            AS CONCESSIONARIA_UF,
  CONCESSIONARIAS.CIDADE                        AS CONCESSIONARIA_CIDADE,
  CONCESSIONARIAS.BAIRRO                        AS CONCESSIONARIA_BAIRRO,
  CONCESSIONARIAS.ENDERECO                      AS CONCESSIONARIA_RUA,
  CONCESSIONARIAS.CEP                           AS CONCESSIONARIA_CEP,
  CONCESSIONARIAS.CODIGO_PADRAO                   AS CONCESSIONARIA_CODIGO,
  UF_CONCESSIONARIA.DESCRICAO                   AS CONCESSIONARIA_ESTADO,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_FAB_BATERIA, 'DD/MM/YYYY'),' ')                AS OS_DATA_FAB_BATERIA,
  NVL(OS_DADOS_VEICULOS.COD_FAB_BATERIA, ' ')                               AS OS_COD_FAB_BATERIA,
  NVL((SELECT EMPRESAS_USUARIOS.NOME_COMPLETO
          FROM EMPRESAS_USUARIOS
         WHERE EMPRESAS_USUARIOS.NOME = CLIENTES_FROTA.NOME_VENDEDOR),' ') AS CONCESSIONARIA_VENDEDOR,
  
  
  
  PRODUTOS.DESCRICAO_PRODUTO  AS OS_DESCRICAO_PRODUTO,
  
  
  NVL(PRODUTOS.DESCRICAO_PRODUTO || ' / ' || PRODUTOS_MODELOS.DESCRICAO_MODELO,' ')   AS DESC_PROD_MOD,
  PRODUTOS_MODELOS.DESCRICAO_MODELO                      AS OS_DESCRICAO_MODELO,
  PRODUTOS_MODELOS.MOD_VER_SERIE                        AS OS_MOD_VER_SERIE,
  PRODUTOS_MODELOS.LINHA                            AS OS_LINHA,
  
  
  MARCAS.DESCRICAO_MARCA  AS OS_DESCRICAO_MARCA,
  
  
  CO.TEXTO_AIDF  AS OS_TEXTO_AIDF,
  
  
  NVL(EMPRESAS.NOME,' ')           AS EMPRESAS_NOME_EMPRESA,
  NVL(EMPRESAS.EMPRESA_NOME_COMPLETO,' ') 		AS EMPRESAS_RAZAO_SOCIAL,

  NVL(EMPRESAS.CGC, ' ')                     AS EMPRESAS_CGC,
  EMPRESAS.FACHADA                  AS EMPRESAS_FACHADA,
  EMPRESAS.ESTADO                   AS UF_EMPRESA,
  NVL(EMPRESAS.CIDADE, ' ')                    AS EMPRESAS_CIDADE,
  EMPRESAS.BAIRRO                    AS EMPRESAS_BAIRRO,
  EMPRESAS.COMPLEMENTO                AS EMPRESAS_COMPLEMENTO,
  NVL(EMPRESAS.RUA, ' ')              AS EMPRESAS_RUA,
  NVL(EMPRESAS.FONE, ' ')                    AS EMPRESAS_FONE,
  NVL(EMPRESAS.FAX, ' ')                    AS EMPRESAS_FAX,
  NVL(EMPRESAS.CEP, ' ')                    AS EMPRESAS_CEP,
  EMPRESAS.INSCRICAO_MUNICIPAL            AS EMPRESAS_INSCRICAO_MUNICIPAL,
  EMPRESAS.INSCRICAO_SUBSTITUICAO            AS EMPRESAS_INSC_SUBSTITUICAO,
  NVL(EMPRESAS.ESTADO,' ') AS EMPRESA_UF,
  NVL(UF_EMPRESA.DESCRICAO, ' ')                  AS EMPRESA_ESTADO,
  NVL(EMPRESAS.INSCRICAO_ESTADUAL, ' ')              AS EMPRESAS_INSCRICAO_ESTADUAL,
  TRUNC(SYSDATE)                     AS EMPRESA_DATA_ATUAL,
  SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'),1, 5)       AS EMPRESA_HORA_ATUAL_STR,
  NVL(CLIENTE_EMPRESA.ENDERECO_ELETRONICO, ' ')         AS EMPRESA_EMAIL,
  NVL(CLIENTE_DIVERSO_EMPRESA.empresa_site, ' ') AS EMPRESA_SITE,
  
  
  
  
 
  CLIENTE_DIVERSO.COD_CLIENTE    AS CLIENTE_COD_CLIENTE,
  NVL(CLIENTE_DIVERSO.NOME, ' ')        AS CLIENTE_NOME,
  NVL(CLIENTE_DIVERSO.RG,' ')          AS CLIENTE_RG,

  
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_CEL || '-' || CLIENTES.TELEFONE_CEL),' ') AS CLIENTE_FONE_CEL,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_RES || '-' || CLIENTES.TELEFONE_RES),' ') AS CLIENTE_FONE_RES,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_COM || '-' || CLIENTES.TELEFONE_COM),' ') AS CLIENTE_FONE_COM,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_FAX|| '-' || CLIENTES.TELEFONE_FAX),' ') AS CLIENTE_FONE_FAX,

  
  
  
  CLIENTES.ENDERECO_ELETRONICO  AS CLIENTE_ENDERECO_ELETRONICO,
  CLIENTES.EMAIL_NFE AS CLIENTE_EMAIL_NFE,
  NVL(NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL),' ') AS CLIENTE_INSC_ESTAD,
  
  NVL(DECODE(CLIENTE_DIVERSO.CPF,' ','CGC: ' || NVL(CLIENTE_DIVERSO.CGC,' '),'CPF: ' || CLIENTE_DIVERSO.CPF), ' ') AS CLIENTE_CGC_CPF,
  
  CLIENTE_DIVERSO.CGC        AS CLIENTE_CGC,
  CLIENTE_DIVERSO.CPF        AS CLIENTE_CPF,
  CLIENTES.COD_CLASSE        AS CLIENTE_COD_CLASSE,
  
  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.ENDERECO  || ', ' || CLIENTE_DIVERSO.COMPLEMENTO || ' ' || CLIENTE_DIVERSO.BAIRRO))
    WHEN '2' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_RES  || ', ' ||CLIENTES.COMPLEMENTO_RES || ' ' || CLIENTES.BAIRRO_RES))
    WHEN '3' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COM  || ', ' ||CLIENTES.COMPLEMENTO_COM || ' ' || CLIENTES.BAIRRO_COM))
    WHEN '4' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COBRANCA  || ', ' ||CLIENTES.COMPLEMENTO_COBRANCA || ' ' || CLIENTES.BAIRRO_COBRANCA))
    WHEN '5' THEN TRIM(',' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.RUA  || ', ' ||ENDERECO_POR_INSCRICAO.COMPLEMENTO || ' ' || ENDERECO_POR_INSCRICAO.BAIRRO))
    ELSE ' '
    END  AS CLIENTE_ENDERECO_COMPLETO,
    

  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO))
    WHEN '2' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_RES  || '-' || CLIENTES.TELEFONE_RES))
    WHEN '3' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '4' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '5' THEN TRIM('-' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO  || '-' || ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO))
    ELSE ' '
    END  AS CLIENTE_TELEFONE_COMPLETO,
    

  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.UF,
              2, CLIENTES.UF_RES,
              3, CLIENTES.UF_COM,
              4, CLIENTES.UF_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.UF,
               ' ')                          AS  CLIENTE_UF,
  DECODE(OS.TIPO_ENDERECO, 1, UF_DIVERSO.DESCRICAO,
              2, UF_RES.DESCRICAO,
              3, UF_COM.DESCRICAO,
              4, UF_COBRANCA.DESCRICAO,
              5, UF_INSCRICAO.DESCRICAO,
               ' ')                          AS  CLIENTE_ESTADO,
  DECODE(OS.TIPO_ENDERECO, 1, CIDADES_DIV.DESCRICAO,
              2, CIDADES_RES.DESCRICAO,
              3, CIDADES_COM.DESCRICAO,
              4, CIDADES_COBRANCA.DESCRICAO,
              5, ENDERECO_POR_INSCRICAO.CIDADE,
               ' ')                          AS  CLIENTE_CIDADE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.BAIRRO,
              2, CLIENTES.BAIRRO_RES,
              3, CLIENTES.BAIRRO_COM,
              4, CLIENTES.BAIRRO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.BAIRRO,
               ' ')                          AS  CLIENTE_BAIRRO,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.CEP,
              2, CLIENTES.CEP_RES,
              3, CLIENTES.CEP_COM,
              4, CLIENTES.CEP_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.CEP,
               ' ')                          AS  CLIENTE_CEP,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.ENDERECO,
              2, CLIENTES.RUA_RES,
              3, CLIENTES.RUA_COM,
              4, CLIENTES.RUA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.RUA,
               ' ')                          AS  CLIENTE_RUA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.COMPLEMENTO,
              2, CLIENTES.COMPLEMENTO_RES,
              3, CLIENTES.COMPLEMENTO_COM,
              4, CLIENTES.COMPLEMENTO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.COMPLEMENTO,
               ' ')                          AS  CLIENTE_COMPLEMENTO,
  DECODE(OS.TIPO_ENDERECO, 1, ' ',
              2, CLIENTES.FACHADA_RES,
              3, CLIENTES.FACHADA_COM,
              4, CLIENTES.FACHADA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.FACHADA,
               ' ')                          AS  CLIENTE_FACHADA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.FONE_CONTATO,
              2, CLIENTES.TELEFONE_RES,
              3, CLIENTES.TELEFONE_COM,
              4, CLIENTES.TELEFONE_COM,
              5, ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_FONE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
              2, CLIENTES.PREFIXO_RES,
              3, CLIENTES.PREFIXO_COM,
              4, CLIENTES.PREFIXO_COM,
              5, ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_PREFIXO,
  
  
  
  NVL(FATURAR_CLIENTE_DIVERSO.NOME, ' ')                   AS FATURAR_NOME,
  
  NVL(TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO)),' ') AS FATURAR_FONE,
  
  NVL(UF_FATURAR.DESCRICAO, ' ')                       AS FATURAR_ESTADO,
  NVL(FATURAR_CIDADE.DESCRICAO, FATURAR_CLIENTE_DIVERSO.CIDADE)   AS FATURAR_CIDADE,

  NVL(TRIM(SUBSTR(CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.ENDERECO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.ENDERECO ELSE '' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.COMPLEMENTO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.COMPLEMENTO ELSE ' ' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.BAIRRO)>0 THEN '  ' || FATURAR_CLIENTE_DIVERSO.BAIRRO ELSE '' END
    , 2)), ' ')  AS FATURAR_ENDERECO,
   
   NVL(GREATEST(
    FATURAR_CLIENTE.PREFIXO_RES || '-' || FATURAR_CLIENTE.TELEFONE_RES,
    FATURAR_CLIENTE.PREFIXO_COM || '-' || FATURAR_CLIENTE.TELEFONE_COM,
    FATURAR_CLIENTE.PREFIXO_FAX || '-' || FATURAR_CLIENTE.TELEFONE_FAX
   ), ' ')   AS FATURAR_TELEFONE_FAX,
   
   
    NVL(FATURAR_CLIENTE.BAIRRO_COM,
    FATURAR_CLIENTE.BAIRRO_RES)   AS FATURAR_BAIRRO,
  
  NVL(FATURAR_CLIENTE.CEP_COM,
    FATURAR_CLIENTE.CEP_RES)   AS FATURAR_CEP,
  
  FATURAR_DADOS_JURIDICOS.CGC AS FATURAR_CGC,
  
  FATURAR_DADOS_JURIDICOS.INSC_ESTADUAL AS FATURAR_IE,
  
   
   
   
   NVL(CLIENTES_FROTA.MEDIA_KM_MENSAL,0) AS MEDIA_KM_MENSAL,
   

  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_TERCEIROS 
    ELSE 
    TOTAIS_SERVICOS.VAL_TERCEIROS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_TERCEIROS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_TERCEIROS,
      
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_LAVAGEM
    ELSE 
    TOTAIS_SERVICOS.VAL_LAVAGEM - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_LAVAGEM/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_LAVAGEM,
  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_REVISAO
    ELSE 
    TOTAIS_SERVICOS.VAL_REVISAO - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_REVISAO/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_REVISAO,
    
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_MECANICA
    ELSE 
    TOTAIS_SERVICOS.VAL_MECANICA - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_MECANICA/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_MECANICA,

  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_GERAIS
    ELSE 
    TOTAIS_SERVICOS.VAL_GERAIS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_GERAIS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_GERAIS,
   
  
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_PECAS
    ELSE 
    TOTAL_PECAS.VALOR_PECAS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_PECAS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_PECAS,
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_ACESSORIOS
    ELSE 
    TOTAL_PECAS.VALOR_ACESSORIOS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_ACESSORIOS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_ACESSORIOS, 
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_OUTROS
    ELSE 
    TOTAL_PECAS.VALOR_OUTROS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_OUTROS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_OUTROS, 

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_LUBRIFICANTE
    ELSE 
    TOTAL_PECAS.VALOR_LUBRIFICANTE - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_LUBRIFICANTE/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_LUBRIFICANTE,  

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_COMBUSTIVEL
    ELSE 
    TOTAL_PECAS.VALOR_COMBUSTIVEL - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_COMBUSTIVEL/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_COMBUSTIVEL,
	
	

	OS_PRE_ORDEM.PENT_REVISAR_DOCUMENTACAO,
	OS_PRE_ORDEM.PENT_CONFIRMAR_LIMPEZA,
	OS_PRE_ORDEM.PENT_LIVRETE_PORTALUVAS,
	OS_PRE_ORDEM.PENT_SEP_PEC_SUBSTITUIDAS,
	OS_PRE_ORDEM.PENT_TELEFONAR_CLIENTE,
	OS_PRE_ORDEM.PENT_CONTATO_CLIENTE1,
	OS_PRE_ORDEM.PENT_CONTATO_CLIENTE2,
	OS_PRE_ORDEM.PENT_CONTATO_CLIENTE1_HORA,
	OS_PRE_ORDEM.PENT_CONTATO_CLIENTE2_HORA,
	OS_PRE_ORDEM.ENT_EXPLICAR_SERVICO,
	OS_PRE_ORDEM.ENT_EXPLICAR_CUSTO,
	OS_PRE_ORDEM.ENT_ACOMPANHAR_CLIENTE,
	OS_PRE_ORDEM.ENT_MOSTRAR_PEC_SUBSTITUIDAS,
	OS_PRE_ORDEM.ENT_RETIRAR_PROTECAO,
	OS_PRE_ORDEM.RESULTADO_ENTREGA,       
	OS_PRE_ORDEM.RESPONSAVEL_ENTREGA AS OS_PRE_ORDEM_RESP_ENTREGA,
	OS_PRE_ORDEM.DATA_ENTREGA,
	OS_PRE_ORDEM.TEXTO AS OS_PRE_ORDEM_TEXTO
    
 
FROM
  
  EMPRESAS,
  UF UF_EMPRESA,
  CLIENTES CLIENTE_EMPRESA,
  CLIENTE_DIVERSO CLIENTE_DIVERSO_EMPRESA,
  
  
  
  OS, EMPRESAS_USUARIOS, VW_OS_TIPOS OS_TIPOS, OS_DADOS_VEICULOS,
  CONCESSIONARIAS,UF UF_CONCESSIONARIA, PRODUTOS, PRODUTOS_MODELOS, MARCAS,
  CONTROLE_OS CO, PARM_SYS, OS_TIPOS_TERMO, OS_AGENDA, CLIENTES_FROTA, 
  GARANTIA_DOC,
  
  
  CLIENTE_DIVERSO, CLIENTES,  ENDERECO_POR_INSCRICAO,
  CIDADES CIDADES_RES, CIDADES CIDADES_COM, CIDADES CIDADES_COBRANCA, CIDADES CIDADES_DIV,
  UF UF_DIVERSO, UF UF_RES, UF UF_COM, UF UF_COBRANCA, UF UF_INSCRICAO,
  
  
  CLIENTE_DIVERSO FATURAR_CLIENTE_DIVERSO, CLIENTES FATURAR_CLIENTE, CIDADES FATURAR_CIDADE, 
   UF UF_FATURAR, DADOS_JURIDICOS FATURAR_DADOS_JURIDICOS,
   
   
  (SELECT
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'T', TOTAIS.PRECO, 0)) AS VAL_TERCEIROS,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'L', TOTAIS.PRECO, 0)) AS VAL_LAVAGEM,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'R', TOTAIS.PRECO, 0)) AS VAL_REVISAO,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'M', TOTAIS.PRECO, 0)) AS VAL_MECANICA,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'G', TOTAIS.PRECO, 0)) AS VAL_GERAIS
  FROM
  (SELECT
  DECODE(S.TERCEIROS, 'S',       'T',
    DECODE(S.LAVAGEM, 'S',       'L',
    DECODE(SS.TIPO_SETOR, 'R', 'R',
                'M', 'M',
                   'G'))) AS TIPO_SERVICO,
  OSS.PRECO_VENDA + NVL((SELECT SUM(PRECO_VENDA) FROM OS_SERVICOS_ADICIONAIS ADI
               WHERE ADI.COD_EMPRESA = OSS.COD_EMPRESA
                 AND ADI.NUMERO_OS   = OSS.NUMERO_OS
                 AND ADI.ITEM        = OSS.ITEM
                 AND ADI.COD_SERVICO = OSS.COD_SERVICO), 0) AS PRECO
  FROM OS_SERVICOS OSS, SERVICOS S, SERVICOS_SETORES SS
  WHERE 1=1
  AND OSS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OSS.NUMERO_OS = $P{NUMERO_OS}
  AND OSS.COD_SERVICO = S.COD_SERVICO
  AND S.COD_SETOR = SS.COD_SETOR) TOTAIS ) TOTAIS_SERVICOS,
  
  
  
  (SELECT
  SUM(DECODE(CLASSE_PECA, 1, VALOR, 0)) AS VALOR_PECAS,
  SUM(DECODE(CLASSE_PECA, 2, VALOR, 0)) AS VALOR_ACESSORIOS,
  SUM(DECODE(NVL(CLASSE_PECA, 3), 3, VALOR, 0)) AS VALOR_OUTROS,
  SUM(DECODE(CLASSE_PECA, 4, VALOR, 0)) AS VALOR_LUBRIFICANTE,
  SUM(DECODE(CLASSE_PECA, 5, VALOR, 0)) AS VALOR_COMBUSTIVEL
  FROM (
  SELECT DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1), 
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4)) AS CLASSE_PECA,
  SUM(
  OS_REQUISICOES.QUANTIDADE *
  DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
        ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                    DECODE(ITENS.COD_TRIBUTACAO, '1',
                    DECODE(PARM_SYS.REGIME_ICMS, 'S',
                      DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                      DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                    0),
                         0),
                         OS_TIPOS.AUMENTO_PRECO_PECA),
                       0),
                     OS_TIPOS.AUMENTO_PRECO_PECA)) *
           DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                            'G', OS_REQUISICOES.PRECO_GARANTIA,
                            'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                            'P', OS_REQUISICOES.PRECO_FABRICA,
                            DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
          ) / 100,
     DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                        OS_REQUISICOES.PRECO_GARANTIA),
       DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
       DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
         ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
        DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                            OS_REQUISICOES.PRECO_VENDA))/100))))))) AS VALOR
  FROM OS_REQUISICOES, ITENS, ITENS_FORNECEDOR, OS, VW_OS_TIPOS OS_TIPOS, ITENS_CLASSE_CONTABIL ICC , SEGURADORA,
  PARM_SYS, PARM_SYS2, ITENS_GRUPO_INTERNO IGI, OS_TIPOS_EMPRESAS OTE
  WHERE OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM (+)
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM (+)
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR (+)
  AND OS.NUMERO_OS = OS_REQUISICOES.NUMERO_OS  (+)
  AND OS.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA (+)
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS.TIPO = OS_TIPOS.TIPO (+)
  AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA (+)
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA (+)
  AND ITENS.COD_GRUPO_INTERNO = IGI.COD_GRUPO_INTERNO(+)
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA 
  AND OTE.TIPO        = OS.TIPO
  AND OS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OS.NUMERO_OS = $P{NUMERO_OS}
  GROUP BY DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4))
  ) TOTAL) TOTAL_PECAS,
  
  (SELECT CE.KM AS KM_PROXIMA_REVISAO,NVL(C1.DATA_EVENTO, C1.DATA_NOVO_CONTATO) AS DATA_PROXIMO_SERVICO
    FROM OS_DADOS_VEICULOS, CRM_EVENTOS C1, CRM_CICLO_EVENTOS CE
    WHERE C1.COD_CICLO (+) > 0
    AND C1.DATA_EVENTO (+) > SYSDATE - 1
    AND C1.STATUS (+) IN ('P', 'A')
    AND C1.COD_CICLO = CE.COD_CICLO(+)
    AND C1.COD_TIPO_EVENTO = CE.COD_TIPO_EVENTO(+)
    AND NVL(CE.KM (+),0) > 0
    AND OS_DADOS_VEICULOS.CHASSI = C1.VEIC_CHASSI_COMPLETO (+)
    AND OS_DADOS_VEICULOS.NUMERO_OS = $P{NUMERO_OS}
    AND OS_DADOS_VEICULOS.COD_EMPRESA = $P{COD_EMPRESA}
    AND ROWNUM = 1
    ORDER BY DATA_PROXIMO_SERVICO) CRM_EVENTOS,

	(select osp.cod_empresa,
       osp.numero_os,
       pgto.tipo_pgto,
       pgto.descricao,       
       Decode(tipo_pgto,'V','S','N') as Avista,  
       Decode(tipo_pgto,'Z','S','N') as Cartao 	
    from os, os_pagamento  osp,
     forma_pgto pgto
    where os.cod_empresa = osp.cod_empresa (+)
	  and os.numero_os = osp.numero_os (+) 
	  and osp.cod_empresa=pgto.cod_empresa (+)
	  and osp.cod_forma_pgto=pgto.cod_forma_pgto (+)
      and os.numero_os = $P{NUMERO_OS}
	  and os.cod_empresa = $P{COD_EMPRESA}) tipo_pagamento,

	(SELECT 
	NVL(TO_CHAR(OS.DATA_EMISSAO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_EMISSAO,
	NVL(OS.NUMERO_OS, 0)                    AS OS_NUMERO_OS, 
	TO_CHAR(OS.DATA_ULT_IMP_FAB, 'DD/MM/YYYY') AS OS_DATA_ULT_IMRESSAO,
	TO_CHAR(OS.DATA_ULT_IMP_FAB, 'HH24:MI:SS') AS OS_HORA_ULT_IMRESSAO,
	OS_DIAGNOSTICO.PENT_REVISAR_DOCUMENTACAO,
	OS_DIAGNOSTICO.PENT_CONFIRMAR_LIMPEZA,
	OS_DIAGNOSTICO.PENT_LIVRETE_PORTALUVAS,
	OS_DIAGNOSTICO.PENT_SEP_PEC_SUBSTITUIDAS,
	OS_DIAGNOSTICO.PENT_TELEFONAR_CLIENTE,
	to_char(OS_DIAGNOSTICO.PENT_CONTATO_CLIENTE1) as PENT_CONTATO_CLIENTE1,
	to_char(OS_DIAGNOSTICO.PENT_CONTATO_CLIENTE2) as PENT_CONTATO_CLIENTE2,
	OS_DIAGNOSTICO.PENT_CONTATO_CLIENTE1_HORA,
	OS_DIAGNOSTICO.PENT_CONTATO_CLIENTE2_HORA,
	OS_DIAGNOSTICO.ENT_EXPLICAR_SERVICO,
	OS_DIAGNOSTICO.ENT_EXPLICAR_CUSTO,
	OS_DIAGNOSTICO.ENT_ACOMPANHAR_CLIENTE,
	OS_DIAGNOSTICO.ENT_MOSTRAR_PEC_SUBSTITUIDAS,
	OS_DIAGNOSTICO.ENT_RETIRAR_PROTECAO,
	OS_DIAGNOSTICO.RESULTADO_ENTREGA,       
	(SELECT EMP.NOME_COMPLETO FROM EMPRESAS_USUARIOS EMP WHERE NOME = OS_DIAGNOSTICO.RESPONSAVEL_ENTREGA) AS RESPONSAVEL_ENTREGA,
	OS_DIAGNOSTICO.DATA_ENTREGA,
	VWCRM.TEXTO

FROM OS, OS_DIAGNOSTICO, VW_CRM_MSG_LGPD_MARCA_OS VWCRM, PARM_SYS
WHERE 1 = 1
    AND OS.COD_EMPRESA = $P{COD_EMPRESA} 
    AND OS.NUMERO_OS = $P{NUMERO_OS}
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
	AND OS.COD_EMPRESA = OS_DIAGNOSTICO.COD_EMPRESA (+)
	AND OS.NUMERO_OS = OS_DIAGNOSTICO.NUMERO_OS (+)
	AND oS.COD_EMPRESA = PARM_SYS.COD_EMPRESA(+)
	AND PARM_SYS.TIPO_CONCESSIONARIA  = VWCRM.COD_TIPO_CONCESSIONARIA (+)) OS_PRE_ORDEM


WHERE   1 = 1
    
    
    AND OS.COD_EMPRESA = $P{COD_EMPRESA} 
    AND OS.NUMERO_OS = $P{NUMERO_OS}
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'

    AND OS.NOME = EMPRESAS_USUARIOS.NOME
  
  AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
  AND OS.COD_OS_AGENDA = OS_AGENDA.COD_OS_AGENDA (+)
  
  AND OS.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  
  AND OS.TIPO = OS_TIPOS_TERMO.TIPO (+)
    
  AND OS.COD_EMPRESA =OS_TIPOS_TERMO.COD_EMPRESA (+)
  
  
  
    AND OS.TIPO = OS_TIPOS.TIPO
    AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA (+)
    
    AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS (+)
    AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA (+)
    
    AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA = CONCESSIONARIAS.COD_CONCESSIONARIA (+)
    
    
    AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF (+)
    
    AND OS.COD_CLIENTE = CLIENTES_FROTA.COD_CLIENTE (+)
    AND OS.COD_PRODUTO = CLIENTES_FROTA.COD_PRODUTO (+)
    AND OS.COD_MODELO = CLIENTES_FROTA.COD_MODELO (+)
    AND OS_DADOS_VEICULOS.CHASSI = CLIENTES_FROTA.CHASSI
    
    AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO 
    
    AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
    
    AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
    AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
    
   
    AND OS.COD_EMPRESA = CO.COD_EMPRESA(+)
  
  AND OS.COD_DOCUMENTO = GARANTIA_DOC.COD_DOCUMENTO (+)
 
    
    
    AND OS.COD_EMPRESA = EMPRESAS.COD_EMPRESA
    AND EMPRESAS.ESTADO = UF_EMPRESA.UF (+)
    AND EMPRESAS.COD_CLIENTE = CLIENTE_EMPRESA.COD_CLIENTE (+)
	AND EMPRESAS.COD_CLIENTE = CLIENTE_DIVERSO_EMPRESA.COD_CLIENTE (+)
    
    
    AND OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES (+)
    AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF (+)
    AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES (+)
    AND OS.INSCRICAO_ESTADUAL = ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL (+)
    AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE (+)
    AND CLIENTES.UF_RES = UF_RES.UF (+)
    AND CLIENTES.UF_COM = UF_COM.UF (+)
    AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF (+)
    AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF (+)
    
    
  AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_CLIENTE.COD_CLIENTE (+)
  AND FATURAR_CLIENTE_DIVERSO.COD_CIDADES =FATURAR_CIDADE.COD_CIDADES (+)
  AND FATURAR_CLIENTE_DIVERSO.UF = UF_FATURAR.UF (+)
  AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_DADOS_JURIDICOS.COD_CLIENTE (+)
  AND OS.COD_CLIENTE = FATURAR_CLIENTE_DIVERSO.COD_CLIENTE (+)]]>
	</queryString>
	<field name="OS_ASSINATURA" class="java.awt.Image"/>
	<field name="OS_COD_EMPRESA" class="java.lang.Double"/>
	<field name="OS_STATUS_OS" class="java.lang.Double"/>
	<field name="OS_COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="OS_ABS_OSNUM" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS_FABRICA" class="java.lang.Double"/>
	<field name="OS_COD_CLIENTE" class="java.lang.Double"/>
	<field name="OS_CLIENTE_RAPIDO" class="java.lang.String"/>
	<field name="OS_TIPO_ENDERECO" class="java.lang.String"/>
	<field name="OS_OBSERVACAO" class="java.lang.String"/>
	<field name="OS_EXTENDIDA" class="java.lang.String"/>
	<field name="OS_SEGURADORA" class="java.lang.String"/>
	<field name="OS_DATA_EMISSAO" class="java.lang.String"/>
	<field name="OS_DATA_LIBERADO" class="java.lang.String"/>
	<field name="OS_DIA_EMISSAO" class="java.lang.String"/>
	<field name="OS_MES_EMISSAO" class="java.lang.String"/>
	<field name="OS_ANO_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_ENCERRADA" class="java.lang.String"/>
	<field name="OS_DATA_ENCERRADA" class="java.sql.Timestamp"/>
	<field name="OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_HORA24_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MINUTO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DATA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DIA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MES_PROMETIDA" class="java.lang.String"/>
	<field name="OS_ANO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_BRUTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_DESCONTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="OS_COD_SEGURADORA" class="java.lang.Double"/>
	<field name="OS_TIPO" class="java.lang.String"/>
	<field name="CONSULTOR" class="java.lang.String"/>
	<field name="OS_OS_ENTRADA" class="java.lang.Double"/>
	<field name="OS_VIA" class="java.lang.String"/>
	<field name="OS_TOTAL_IMPRESSAO_FABRICA" class="java.lang.Double"/>
	<field name="OS_TERMO_TEXTO" class="java.lang.String"/>
	<field name="OS_DATA_TAXI" class="java.lang.String"/>
	<field name="OS_HORA_TAXI" class="java.lang.String"/>
	<field name="OS_TODO_TRABALHO_FEITO" class="java.lang.String"/>
	<field name="OS_TODO_LIMPO" class="java.lang.String"/>
	<field name="OS_PRIMEIRA_RODAGEM" class="java.lang.String"/>
	<field name="OS_SEGUNDA_RODAGEM" class="java.lang.String"/>
	<field name="OS_DATA_PRIMEIRA_RODAGEM" class="java.lang.String"/>
	<field name="OS_DATA_SEGUNDA_RODAGEM" class="java.lang.String"/>
	<field name="OS_PRIM_RESPONSAVEL_RODAGEM" class="java.lang.String"/>
	<field name="OS_SEG_RESPONSAVEL_RODAGEM" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PAGAMENTO" class="java.lang.String"/>
	<field name="OS_PAGAMENTO_DINHEIRO" class="java.lang.String"/>
	<field name="OS_PAGAMENTO_CARTAO" class="java.lang.String"/>
	<field name="OS_NUMERO_ORCAMENTO" class="java.lang.String"/>
	<field name="OS_NOME_CLIENTE_APROVOU" class="java.lang.String"/>
	<field name="OS_TOTAL_ORCAMENTO" class="java.lang.Double"/>
	<field name="OS_ORCAMENTO_AUTORIZADO" class="java.lang.String"/>
	<field name="OS_DATA_ORCAMENTO_APROVADO" class="java.lang.String"/>
	<field name="OS_GARANTIA_SG_PADRAO" class="java.lang.String"/>
	<field name="OS_GARANTIA_SG_EXEMPLO" class="java.lang.Double"/>
	<field name="OS_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="OS_CLIENTE_AGUARDOU" class="java.lang.String"/>
	<field name="OS_EH_RETORNO" class="java.lang.String"/>
	<field name="EH_RECALL" class="java.lang.String"/>
	<field name="DATA_AGENDAMENTO" class="java.lang.String"/>
	<field name="DATA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="OS_NOME_AGENDADOR" class="java.lang.String"/>
	<field name="OS_FIAT_PROFISSIONAL" class="java.lang.String"/>
	<field name="OS_PECA_USADA_FICA_CLIENTE" class="java.lang.String"/>
	<field name="OS_DATA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_HORA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_VEICULO_PLATAFORMA" class="java.lang.String"/>
	<field name="OS_HORA_AGENDADA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRISMA" class="java.lang.String"/>
	<field name="KM_PROXIMA_REVISAO" class="java.lang.Double"/>
	<field name="DATA_PROXIMO_SERVICO" class="java.sql.Timestamp"/>
	<field name="OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="OS_CODIGO_OPCIONAL" class="java.lang.String"/>
	<field name="OS_TIPO_DESCRICAO" class="java.lang.String"/>
	<field name="OS_TIPO_COM_DESCRICAO" class="java.lang.String"/>
	<field name="OS_GARANTIA" class="java.lang.String"/>
	<field name="OS_REVISAO_GRATUITA" class="java.lang.String"/>
	<field name="OS_INTERNO" class="java.lang.String"/>
	<field name="OS_CLIENTE_DO_TIPO" class="java.lang.Double"/>
	<field name="OS_OUTRO_CONCESSIONARIA" class="java.lang.String"/>
	<field name="OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="OS_ANO" class="java.lang.String"/>
	<field name="OS_HORIMETRO" class="java.lang.Double"/>
	<field name="OS_PRISMA" class="java.lang.String"/>
	<field name="OS_DATA_VENDA" class="java.lang.String"/>
	<field name="DIA_VENDA" class="java.lang.String"/>
	<field name="MES_VENDA" class="java.lang.String"/>
	<field name="ANO_VENDA" class="java.lang.String"/>
	<field name="OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="OS_PLACA" class="java.lang.String"/>
	<field name="OS_KM" class="java.lang.Double"/>
	<field name="OS_CHASSI" class="java.lang.String"/>
	<field name="OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="OS_NUMERO_RENAVAM" class="java.lang.String"/>
	<field name="OS_SERIE" class="java.lang.String"/>
	<field name="OS_COD_CONCESSIONARIA" class="java.lang.Double"/>
	<field name="OS_ESTADO_PINTURA" class="java.lang.String"/>
	<field name="OS_JOGO_FERRAMENTAS" class="java.lang.String"/>
	<field name="OS_ELASTICOS" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_D" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_E" class="java.lang.String"/>
	<field name="OS_FLANELA" class="java.lang.String"/>
	<field name="OS_NUMERO_FROTA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="CONCESSIONARIA_UF" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CIDADE" class="java.lang.String"/>
	<field name="CONCESSIONARIA_BAIRRO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_RUA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CEP" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CODIGO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_ESTADO" class="java.lang.String"/>
	<field name="OS_DATA_FAB_BATERIA" class="java.lang.String"/>
	<field name="OS_COD_FAB_BATERIA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_VENDEDOR" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRODUTO" class="java.lang.String"/>
	<field name="DESC_PROD_MOD" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="OS_MOD_VER_SERIE" class="java.lang.String"/>
	<field name="OS_LINHA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MARCA" class="java.lang.String"/>
	<field name="OS_TEXTO_AIDF" class="java.lang.String"/>
	<field name="EMPRESAS_NOME_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_RAZAO_SOCIAL" class="java.lang.String"/>
	<field name="EMPRESAS_CGC" class="java.lang.String"/>
	<field name="EMPRESAS_FACHADA" class="java.lang.String"/>
	<field name="UF_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_CIDADE" class="java.lang.String"/>
	<field name="EMPRESAS_BAIRRO" class="java.lang.String"/>
	<field name="EMPRESAS_COMPLEMENTO" class="java.lang.String"/>
	<field name="EMPRESAS_RUA" class="java.lang.String"/>
	<field name="EMPRESAS_FONE" class="java.lang.String"/>
	<field name="EMPRESAS_FAX" class="java.lang.String"/>
	<field name="EMPRESAS_CEP" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_MUNICIPAL" class="java.lang.String"/>
	<field name="EMPRESAS_INSC_SUBSTITUICAO" class="java.lang.String"/>
	<field name="EMPRESA_UF" class="java.lang.String"/>
	<field name="EMPRESA_ESTADO" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="EMPRESA_DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="EMPRESA_HORA_ATUAL_STR" class="java.lang.String"/>
	<field name="EMPRESA_EMAIL" class="java.lang.String"/>
	<field name="EMPRESA_SITE" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLIENTE" class="java.lang.Double"/>
	<field name="CLIENTE_NOME" class="java.lang.String"/>
	<field name="CLIENTE_RG" class="java.lang.String"/>
	<field name="CLIENTE_FONE_CEL" class="java.lang.String"/>
	<field name="CLIENTE_FONE_RES" class="java.lang.String"/>
	<field name="CLIENTE_FONE_COM" class="java.lang.String"/>
	<field name="CLIENTE_FONE_FAX" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="CLIENTE_EMAIL_NFE" class="java.lang.String"/>
	<field name="CLIENTE_INSC_ESTAD" class="java.lang.String"/>
	<field name="CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="CLIENTE_CGC" class="java.lang.String"/>
	<field name="CLIENTE_CPF" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLASSE" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_UF" class="java.lang.String"/>
	<field name="CLIENTE_ESTADO" class="java.lang.String"/>
	<field name="CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="CLIENTE_CEP" class="java.lang.String"/>
	<field name="CLIENTE_RUA" class="java.lang.String"/>
	<field name="CLIENTE_COMPLEMENTO" class="java.lang.String"/>
	<field name="CLIENTE_FACHADA" class="java.lang.String"/>
	<field name="CLIENTE_FONE" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO" class="java.lang.String"/>
	<field name="FATURAR_NOME" class="java.lang.String"/>
	<field name="FATURAR_FONE" class="java.lang.String"/>
	<field name="FATURAR_ESTADO" class="java.lang.String"/>
	<field name="FATURAR_CIDADE" class="java.lang.String"/>
	<field name="FATURAR_ENDERECO" class="java.lang.String"/>
	<field name="FATURAR_TELEFONE_FAX" class="java.lang.String"/>
	<field name="FATURAR_BAIRRO" class="java.lang.String"/>
	<field name="FATURAR_CEP" class="java.lang.String"/>
	<field name="FATURAR_CGC" class="java.lang.String"/>
	<field name="FATURAR_IE" class="java.lang.String"/>
	<field name="MEDIA_KM_MENSAL" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_TERCEIROS" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_LAVAGEM" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_REVISAO" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_MECANICA" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_GERAIS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_PECAS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_ACESSORIOS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_OUTROS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_LUBRIFICANTE" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="PENT_REVISAR_DOCUMENTACAO" class="java.lang.String"/>
	<field name="PENT_CONFIRMAR_LIMPEZA" class="java.lang.String"/>
	<field name="PENT_LIVRETE_PORTALUVAS" class="java.lang.String"/>
	<field name="PENT_SEP_PEC_SUBSTITUIDAS" class="java.lang.String"/>
	<field name="PENT_TELEFONAR_CLIENTE" class="java.lang.String"/>
	<field name="PENT_CONTATO_CLIENTE1" class="java.lang.String"/>
	<field name="PENT_CONTATO_CLIENTE2" class="java.lang.String"/>
	<field name="PENT_CONTATO_CLIENTE1_HORA" class="java.lang.String"/>
	<field name="PENT_CONTATO_CLIENTE2_HORA" class="java.lang.String"/>
	<field name="ENT_EXPLICAR_SERVICO" class="java.lang.String"/>
	<field name="ENT_EXPLICAR_CUSTO" class="java.lang.String"/>
	<field name="ENT_ACOMPANHAR_CLIENTE" class="java.lang.String"/>
	<field name="ENT_MOSTRAR_PEC_SUBSTITUIDAS" class="java.lang.String"/>
	<field name="ENT_RETIRAR_PROTECAO" class="java.lang.String"/>
	<field name="RESULTADO_ENTREGA" class="java.lang.String"/>
	<field name="OS_PRE_ORDEM_RESP_ENTREGA" class="java.lang.String"/>
	<field name="DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="OS_PRE_ORDEM_TEXTO" class="java.lang.String"/>
	<variable name="TOTAL_PAGES" class="java.lang.Integer" resetType="None" incrementType="Report" calculation="Highest">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="109">
			<frame>
				<reportElement x="0" y="55" width="468" height="54" uuid="c99f7e83-f790-4972-8fd8-aa6afb64a1c7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="1" width="107" height="22" uuid="8da84315-a51e-4346-804f-f8d10042b547">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[DATA DE EMISSÃO]]></text>
				</staticText>
				<staticText>
					<reportElement x="107" y="1" width="180" height="22" uuid="22617e5d-1207-40cc-9535-d31606c66c3d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[ORDEM DE SERVIÇO]]></text>
				</staticText>
				<staticText>
					<reportElement x="287" y="1" width="88" height="22" uuid="f0e152de-0de8-4eb1-a990-6d9bec6a5340">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[IMPRESSÃO]]></text>
				</staticText>
				<staticText>
					<reportElement x="375" y="1" width="91" height="22" uuid="1f9a92e4-e1f6-4a0f-a90e-b031d4203502">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[REIMPRESSÃO]]></text>
				</staticText>
				<textField>
					<reportElement x="0" y="23" width="107" height="22" uuid="0b5a0a17-73d1-41d8-8c5e-ff8651d2db47">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField pattern="#.###;(#.###)">
					<reportElement mode="Opaque" x="107" y="23" width="180" height="22" backcolor="#C0C0C0" uuid="a69d4513-aa49-4b9f-8ede-f31b49b77181">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="15" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="287" y="23" width="88" height="22" uuid="3117ec66-aceb-4206-a352-3fc26d8eae9d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO} + " - " + $F{OS_HORA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="375" y="23" width="91" height="22" uuid="a3ee5b06-a0ec-4497-b0dc-67c52514d058">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_ULT_IMRESSAO} + " - " + $F{OS_HORA_ULT_IMRESSAO}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="468" y="55" width="87" height="54" uuid="7c152e33-13a5-44ba-b18a-6f543636d6d1">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="14" y="12" width="61" height="11" uuid="679a7ebb-acb1-4164-8574-ff8e2a6f089c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Versão: 2.1]]></text>
				</staticText>
				<staticText>
					<reportElement x="13" y="30" width="25" height="11" uuid="9fbc4edf-417e-4e85-97ff-f0a7342c40db">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Página:]]></text>
				</staticText>
				<textField evaluationTime="Page">
					<reportElement x="38" y="30" width="15" height="11" uuid="479e5811-ea16-4bab-992a-4ece9810ccf8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="57" y="30" width="15" height="11" uuid="cb8033f0-f7ae-49ef-a3ba-e717a9e02745">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="53" y="30" width="4" height="11" uuid="9a2bff87-4a47-4a8c-9aba-4081c7a5230a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[/]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="1" width="555" height="54" uuid="a1bd9972-5e23-4aa1-be40-220f61b74ce7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport>
					<reportElement x="0" y="0" width="555" height="54" uuid="d5695af6-92cd-4d98-93fc-c61ff6b8a3f4">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsFCACabecalho.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="198">
			<frame>
				<reportElement x="468" y="0" width="87" height="41" uuid="67fd4a36-4183-4fa3-a28d-7eb72c6e1828">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="86" height="13" uuid="1d7e7d11-3de4-4a21-8cb0-3e9d9b3415ca">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[PRISMA]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="4" y="16" width="80" height="13" backcolor="#C0C0C0" uuid="bdac05dd-0092-41b0-b018-f3256fbb377f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="4" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DESCRICAO_PRISMA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="343" y="0" width="125" height="41" uuid="5ee21c71-306e-4da8-9bca-337f6fb4afb2">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="125" height="13" uuid="6d4582e5-d7cb-4aa0-acac-785f957bc8bd">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[PREVISÃO DE ENTREGA]]></text>
				</staticText>
				<staticText>
					<reportElement x="-1" y="13" width="62" height="13" uuid="723d11fc-df42-443d-8bd0-4e2c56f9ab23">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[ORIGINAL]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="26" width="61" height="15" uuid="09069d26-d20a-432b-bb9e-11cbee9078d3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_PROMETIDA} + " - " + $F{OS_HORA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="61" y="13" width="64" height="13" uuid="0334291f-9ff4-4273-b8f3-38c9fa629909">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[REVISADO]]></text>
				</staticText>
				<textField>
					<reportElement x="61" y="26" width="64" height="15" uuid="f80af68b-1f68-43aa-969a-59d44c318aa8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_PROMETIDA_REVISADA} + " - " + $F{HORA_PROMETIDA_REVISADA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="0" width="343" height="41" uuid="40ae0780-cba6-4ad7-9e96-51acb1133134">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="0" width="56" height="13" uuid="b2c3a249-c2db-4289-9140-ade39b173f37">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[PLACA]]></text>
				</staticText>
				<staticText>
					<reportElement x="56" y="0" width="81" height="13" uuid="3f16914b-20d6-44c8-ab7f-08ebd2dc92b0">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[AGENDADO]]></text>
				</staticText>
				<staticText>
					<reportElement x="137" y="0" width="96" height="13" uuid="abd9bf41-e6fc-412e-88ed-920fa79a6ce8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[AGENDADOR]]></text>
				</staticText>
				<staticText>
					<reportElement x="233" y="0" width="110" height="13" uuid="b6acc054-474b-4474-af33-245724b44798">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[CONSULTOR DE SERVIÇO]]></text>
				</staticText>
				<textField>
					<reportElement mode="Opaque" x="0" y="13" width="56" height="28" backcolor="#C0C0C0" uuid="093c1672-2c70-4cb0-8dcc-1e1195b3ada8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="13" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="56" y="13" width="81" height="28" backcolor="#C0C0C0" uuid="5c66b8fc-4002-4cb0-a187-3827eafb30e1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_AGENDAMENTO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="137" y="13" width="96" height="28" backcolor="#C0C0C0" uuid="6186b4b1-8762-45d2-a281-faa8753644e5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NOME_AGENDADOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="233" y="13" width="110" height="28" backcolor="#C0C0C0" uuid="f8db4313-c169-40c3-bd9c-0d07f83d9097">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="41" width="555" height="34" uuid="43f2fbbb-3df6-4123-af89-e220cc6c9f4f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="2" y="3" width="42" height="11" uuid="d627739b-85a8-4a3a-817e-330e74bf1de1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[AG. Ativo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="47" y="3" width="67" height="11" uuid="087dbf98-d5cd-40ff-86bd-15a8ef7ee1f3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Lavar o Veículo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="117" y="3" width="67" height="11" uuid="eab0b99f-ba6a-4c89-a102-f97fe5e2ec58">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente aguarda: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="187" y="3" width="94" height="11" uuid="9ab56bcd-1859-4de5-9ea0-5ae0aa0df22b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente Fiat Profissional: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="284" y="3" width="67" height="11" uuid="2a4b8916-a0c7-426f-b161-c4357e252d38">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Veículo Retorno: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="360" y="3" width="138" height="11" uuid="d0f38373-af7b-42c3-9a1f-18771bcacbdb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Levar Peças substituidas do veículo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="503" y="3" width="44" height="11" uuid="52bd3599-d07a-4b45-b6d6-7bf6a709b88f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Rebocado: ]]></text>
				</staticText>
				<textField>
					<reportElement stretchType="ContainerHeight" x="16" y="17" width="9" height="9" uuid="74330df6-ccfc-4b9a-874a-b51ebd1d4b1e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA["N"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="76" y="17" width="9" height="9" uuid="e0efc2f4-1bbd-444b-8e68-a83ea09dd823">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_LAVAR_VEICULO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="146" y="17" width="9" height="9" uuid="cf2fc57c-1521-40cd-b016-0c4161528881">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_CLIENTE_AGUARDOU}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="229" y="17" width="9" height="9" uuid="f31a9b4e-afef-4c8d-9ed0-25127d22397c">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_FIAT_PROFISSIONAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="313" y="17" width="9" height="9" uuid="72542d7f-f2e5-463d-a45a-09a60ab8169e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_EH_RETORNO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="424" y="17" width="9" height="9" uuid="7bf67b0e-ec62-47e7-8662-c82650050922">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PECA_USADA_FICA_CLIENTE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="520" y="17" width="9" height="9" uuid="099c0edb-dd1c-4f1b-88d8-f763e8759077">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="true"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VEICULO_PLATAFORMA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="75" width="555" height="69" uuid="fd53bd83-1cb8-49c2-afd2-e25f797eb559">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement mode="Opaque" x="0" y="0" width="555" height="20" backcolor="#C0C0C0" uuid="44e42e43-3636-4bc7-9413-a911a4173424">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textField pattern="#.###;(#.###-)">
						<reportElement x="78" y="2" width="90" height="18" uuid="7c92e92e-7728-469c-9782-828db1faa2c3">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{CLIENTE_COD_CLIENTE}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="176" y="2" width="340" height="18" uuid="cf459ae1-83aa-4506-91ec-c83de7630978">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{CLIENTE_NOME}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="3" y="2" width="74" height="18" uuid="04b9552f-e109-4e0c-b2e0-aec59aa89365">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<text><![CDATA[código/Cliente:]]></text>
					</staticText>
					<staticText>
						<reportElement x="169" y="2" width="6" height="18" uuid="8264d6ed-046f-4f6c-a95b-6f20ac4c6a80">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<text><![CDATA[/]]></text>
					</staticText>
				</frame>
				<staticText>
					<reportElement x="4" y="22" width="42" height="11" uuid="624e2355-9da9-4f01-9cb3-8a0babe2ecf4">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Endereço: ]]></text>
				</staticText>
				<textField>
					<reportElement x="46" y="22" width="230" height="11" uuid="f5d754ee-9f14-40e1-89e6-1fb2c47b4a79">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_RUA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="33" width="46" height="11" uuid="fba82c4b-2624-40e7-8986-59b8595fa785">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade/UF: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="4" y="44" width="95" height="11" uuid="ca05a1c9-7ccf-4dc0-85b2-7a68d2f69533">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Telefone para contato:]]></text>
				</staticText>
				<staticText>
					<reportElement x="4" y="55" width="35" height="11" uuid="f0deea67-4084-4ff5-9506-96b713e06d67">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[E-mail: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="276" y="22" width="29" height="11" uuid="9c88e0ed-9673-4c52-bcf2-559dd34543fd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement x="275" y="33" width="49" height="11" uuid="175b3488-3b0e-41a1-a216-ebbc4635146e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CPF/CNPJ:]]></text>
				</staticText>
				<staticText>
					<reportElement x="416" y="22" width="47" height="11" uuid="e1487e39-5078-4a27-828e-ead448788659">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[BAIRRO: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="416" y="33" width="29" height="11" uuid="cea4ec4a-3142-4f75-8d2b-31271711bc47">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[RG: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="99" y="44" width="31" height="11" uuid="3397b359-8dcf-4857-bd56-57bdc8e93e29">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Celular:]]></text>
				</staticText>
				<staticText>
					<reportElement x="204" y="44" width="48" height="11" uuid="4cd58fec-a579-4a45-88ef-1644f0cc25d0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Residencial: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="325" y="44" width="42" height="11" uuid="af67099d-0f8f-426e-b847-6af1949664ef">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Comercial:]]></text>
				</staticText>
				<staticText>
					<reportElement x="443" y="44" width="30" height="11" uuid="f9a96c13-3f2f-477f-ad3d-9b341f8ceeaf">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Outros: ]]></text>
				</staticText>
				<textField>
					<reportElement x="50" y="33" width="221" height="11" uuid="2664cdd7-3841-4318-af56-f5d3e7cecb66">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="305" y="22" width="111" height="11" uuid="53cee5fd-0f7c-4b88-ba5a-cb9aed51bf93">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="324" y="33" width="92" height="11" uuid="9caf355c-b8bc-4f62-9cdc-65f32af4dacf">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CGC}/*$F{CLIENTE_CPF} == null ? $F{CLIENTE_CPF}: $F{CLIENTE_CGC}*/]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="463" y="22" width="75" height="11" uuid="8e1d1307-3b17-42e2-a2da-8270e9d50671">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="445" y="33" width="75" height="11" uuid="e7b5dd15-a044-465e-820c-22e27ff9221f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_RG}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="473" y="44" width="76" height="11" uuid="9d3b945b-2aeb-4d5a-9692-d9b29e80d3b6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_COM}.length() > 6 ? $F{CLIENTE_FONE_COM}: " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="367" y="44" width="76" height="11" uuid="7b65b83a-a435-410d-b532-bef72b820ec0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_COM}.length() > 6 ? $F{CLIENTE_FONE_COM}: " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="252" y="44" width="72" height="11" uuid="6422717f-cc16-4b27-b5cc-e2390e40311e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_RES}.length() > 6 ? $F{CLIENTE_FONE_RES}: " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="130" y="44" width="74" height="11" uuid="461fd2f4-109a-48eb-b368-3b92d16a3bd9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_CEL}.length() > 6 ? $F{CLIENTE_FONE_CEL}: " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="39" y="55" width="274" height="11" uuid="9adfaf8f-8405-4c45-ae98-a83924948e3b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="144" width="555" height="54" uuid="78a618ae-ab40-4e0f-a6a5-4bbcba4e6460">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement mode="Opaque" x="1" y="1" width="300" height="20" backcolor="#C0C0C0" uuid="8bdc86d8-9c99-42e4-b7bd-2b007e695df8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textField>
						<reportElement x="3" y="0" width="184" height="18" uuid="bed784e5-b5bf-4873-a9a7-704b7f76b4a9">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA["MVS/Veículo: " +  $F{OS_DESCRICAO_PRODUTO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="188" y="0" width="92" height="18" uuid="cf57152f-247a-47d9-804b-365af3058bc0">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="11" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA["Placa: " + $F{OS_PLACA}]]></textFieldExpression>
					</textField>
				</frame>
				<staticText>
					<reportElement x="309" y="5" width="32" height="11" uuid="2a19994f-15f5-4ff7-a95f-c383943bfe03">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Chassi: ]]></text>
				</staticText>
				<textField>
					<reportElement x="343" y="5" width="100" height="11" uuid="b88d112e-fb2c-44b7-82a2-ef1a372a082f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_CHASSI}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="445" y="5" width="52" height="11" uuid="19e19611-5cd9-46ba-8fe4-f31e917b9312">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Ano/Modelo: ]]></text>
				</staticText>
				<textField>
					<reportElement x="497" y="5" width="52" height="11" uuid="b54da8ae-8fad-4aee-8ba8-5b524bfd3e5a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_ANO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="24" y="25" width="100" height="11" uuid="79e73f3d-c072-41c6-b5d0-d183b9e2c198">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="6" y="25" width="18" height="11" uuid="5bde1429-75b2-4a98-b699-9176fb098458">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cor: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="36" width="92" height="11" uuid="9a060068-53f2-46c7-a12f-1db70e6b8b16">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Distribuidor Vendedor: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="130" y="25" width="42" height="11" uuid="29f47cc2-a1c6-4f96-8435-be2ba93047b8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº Motor: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="244" y="36" width="42" height="11" uuid="1fc65311-b1d0-4305-a855-26fedfafdc78">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Vendedor: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="294" y="25" width="48" height="11" uuid="78c98a4a-a445-4b94-bd67-2e2d6c5f8175">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Km Atual: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="413" y="25" width="56" height="11" uuid="33414441-23fd-4e86-9029-9182664389cb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Combustivel: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="420" y="36" width="50" height="11" uuid="857854c1-338e-4247-ab0b-60c518b651e5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data Venda: ]]></text>
				</staticText>
				<textField>
					<reportElement x="172" y="25" width="100" height="11" uuid="de2dcd39-ce4a-42d9-bda9-aa90f64ae3ae">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_MOTOR}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement x="342" y="25" width="60" height="11" uuid="08b1f74f-c797-42e3-b15c-2398dc6f7a29">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_KM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="98" y="36" width="132" height="11" uuid="f3fdd140-9f5b-4093-a85a-b7c175d4c101">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="286" y="36" width="127" height="11" uuid="f8c368ee-eae8-48d7-9e56-4b9f748c1749">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_VENDEDOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="470" y="36" width="74" height="11" uuid="9ae880a5-e313-4977-b578-36f025b15e4c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="469" y="25" width="51" height="11" uuid="c0ae3b52-54c4-4f44-a5ec-ab9b7e5b738f"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COMBUSTIVEL} < 19 ? "":
$F{OS_COMBUSTIVEL} < 39 ? "X" :
$F{OS_COMBUSTIVEL} < 59 ? "X   X":
$F{OS_COMBUSTIVEL} < 79 ? "X   X   X":
 "X   X   X   X"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="476" y="34" width="1" height="2" uuid="fe167363-f632-4387-ab69-f22b28ca4ea1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="482" y="32" width="1" height="4" uuid="685a1447-486d-454d-b05d-9ee576984f38">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="488" y="34" width="1" height="2" uuid="341b046b-ed27-436e-a17c-0a450d630b8d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="494" y="30" width="1" height="6" uuid="86cf4a8b-a0cc-4731-beed-b9006cc9882a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="500" y="34" width="1" height="2" uuid="b44ad26e-2c5d-48ae-9410-a4c1d2e80b78">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="506" y="32" width="1" height="4" uuid="2d5dc407-489d-47be-b930-507fda45f4cb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="512" y="34" width="1" height="2" uuid="9c6f7419-b75d-4cd0-8f4f-de3e884a3f2a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
			</frame>
		</band>
		<band height="22" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<subreport>
				<reportElement x="0" y="0" width="555" height="22" isRemoveLineWhenBlank="true" uuid="8a210cec-d496-40c6-bb7d-36c36fdd724a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsFCASubReclamacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="22">
			<subreport>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="555" height="22" isRemoveLineWhenBlank="true" uuid="f8f17a62-1521-4c36-a8ce-367a751971c6">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsFCASubServicos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="22">
			<subreport>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="555" height="22" isRemoveLineWhenBlank="true" uuid="bb9ff69d-7229-4416-96c1-df50190dca87">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsFCASubPecas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="96">
			<staticText>
				<reportElement mode="Transparent" x="39" y="72" width="516" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="1058c184-68bf-41f8-ae5f-8f734e8e56ac">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição do Serviço]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="60" width="555" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="1f96c84e-c2ad-4c0d-adbe-e7514ef2ada7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[OUTROS SERVIÇOS NECESSÁRIOS / RECOMENDADOS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="39" y="84" width="516" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="3ccb9a96-3c27-47d8-80f1-c0642b2accc8">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="84" width="39" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="65fb4e91-7255-4f67-aefe-71220cbadd98">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="72" width="39" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="0de9a9cc-a5e0-44e2-975c-de763b61c09b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Item]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="48" width="555" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="af2ffe81-dec0-470e-a83b-d4455b0eb9fc">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valores de mão de obra e peças, realizados exclusivamente em garantia, serão responsabilidade da montadora.]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="0" width="555" height="48" backcolor="#E3E3E3" uuid="355f167e-93d3-4353-a9f3-44ad35f31a4a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="472" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="5912ab83-dd10-4e1c-b101-3b2f0617677a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Total Estimado de Serviços]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="472" y="0" width="83" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="7fcd05d4-9a11-4ddc-9cf5-e9a764fe7ef2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS_SERVICOS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="0" y="12" width="472" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="30627bd0-57f4-43a9-9f75-5632bd8944ba">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Total Estimado de Peças e Lubrificantes]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement positionType="Float" mode="Opaque" x="472" y="12" width="83" height="12" isPrintInFirstWholeBand="true" backcolor="#E3E3E3" uuid="93ff3824-485f-4ec9-8449-7b5a9d1c2834">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TOT_PECAS_VALOR_PECAS} + $F{TOT_PECAS_VALOR_LUBRIFICANTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Opaque" x="0" y="36" width="472" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="a616e816-3a76-4822-8def-2faba006d1c7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[VALRO TOTAL ESTIMADO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Opaque" x="0" y="24" width="472" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="e31c8628-5f35-4ce2-9ace-7c03939d95f7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Descontos]]></text>
				</staticText>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="472" y="24" width="83" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="d3de3e7d-febb-4ecf-b1d9-6e90e246d353">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS_DESCONTO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;#,##0.00#-">
					<reportElement mode="Opaque" x="472" y="36" width="83" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="e36e8f9b-b5db-414c-9ce5-51b82a9114d2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="474" y="1" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="f1b6f137-5847-44ca-8f70-13099121fd85">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="474" y="13" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="fb8dcf3c-933d-48ec-af3d-245f1bf9483d">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="474" y="25" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="973fddbb-50c6-4cb0-aa01-8b20922d6262">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="474" y="37" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="ad14c977-5913-4558-99f4-dfd4eb2ed5a4">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[R$]]></text>
				</staticText>
			</frame>
		</band>
		<band height="190">
			<staticText>
				<reportElement mode="Transparent" x="0" y="0" width="555" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="e22e9b82-9995-4f5d-bcc7-ff6aad363a29">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Revisão Orçamento - Autorizado o Orçamento Revisado ?]]></text>
			</staticText>
			<textField>
				<reportElement stretchType="ContainerHeight" x="326" y="3" width="14" height="14" uuid="0b8b4e40-4cc7-43da-b791-d2200a0d75c4">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="true"/>
					<paragraph lineSpacingSize="1.0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OS_NUMERO_ORCAMENTO} == null ? "N":"S"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="310" y="5" width="15" height="11" uuid="54f7d257-deb3-4ec3-9f6c-5779c8e885d0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Sim]]></text>
			</staticText>
			<staticText>
				<reportElement x="364" y="5" width="16" height="11" uuid="fdb49830-5f1a-4949-88ab-c875f7489d60">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Não]]></text>
			</staticText>
			<textField>
				<reportElement stretchType="ContainerHeight" x="381" y="3" width="14" height="14" uuid="19a650e2-d033-4d81-ae19-91a68b74090e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="true"/>
					<paragraph lineSpacingSize="1.0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OS_NUMERO_ORCAMENTO} == null ? "S":"N"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="0" y="20" width="555" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="83ccf598-78a4-4452-8282-fb796f857066">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Geral (Previsto Inicial + Recomendado) R$]]></text>
			</staticText>
			<staticText>
				<reportElement x="364" y="21" width="70" height="11" uuid="d841dbd3-51fb-4bfe-a0b8-2d388ea54f46">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Nº do Orçamento:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="32" width="555" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="c450f168-cd65-4b90-8a8b-8034e392a77f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Responsável pela AUTORIZAÇÃO:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="44" width="555" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="3a8d44aa-9db7-4c8f-9ac0-29ce3834690b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Responsável pelo CONTRATO: ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="56" width="555" height="20" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="f76ad0df-be40-4b43-b3a7-f96d93fb7f24">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="5" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Condição de Pagamento: ]]></text>
			</staticText>
			<textField>
				<reportElement stretchType="ContainerHeight" x="161" y="59" width="14" height="14" uuid="3b52dcb9-9317-4c14-82cd-4b8937882f12">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="true"/>
					<paragraph lineSpacingSize="1.0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OS_PAGAMENTO_DINHEIRO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="125" y="61" width="36" height="11" uuid="b99e99e6-2f65-4af2-b1c0-86256929c177">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Dinheiro:]]></text>
			</staticText>
			<staticText>
				<reportElement x="202" y="61" width="103" height="11" uuid="7c35bf4f-4bf3-4d0d-a173-e2010eb713f0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Cartão de Crédito / Débito:]]></text>
			</staticText>
			<textField>
				<reportElement stretchType="ContainerHeight" x="306" y="59" width="14" height="14" uuid="61312d8c-5964-42e6-8703-c2f9517d2d9f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="true"/>
					<paragraph lineSpacingSize="1.0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OS_PAGAMENTO_CARTAO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="76" width="146" height="20" uuid="6b3c00eb-3d7f-4cfd-8262-42df54248733">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="4">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Média de KM atual:]]></text>
			</staticText>
			<staticText>
				<reportElement x="146" y="76" width="192" height="20" uuid="79c7f8a8-038d-4e96-acbd-4bf3195e6d8a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Próxima revisão:]]></text>
			</staticText>
			<staticText>
				<reportElement x="338" y="76" width="217" height="20" uuid="e8c45852-e245-495a-9858-89a75a8e05fa">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Próxima revisão:]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="96" width="555" height="94" uuid="7141d6f1-6b72-458c-b35f-be2be7c1f304">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="6" y="6" width="394" height="11" uuid="ccb42d2e-53bf-4671-914b-1670b44fd099">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Se o cliente levar as peças será responsável pelo descarte correto dos materiais retirado na concessionária.]]></text>
				</staticText>
				<textField>
					<reportElement x="111" y="17" width="140" height="11" uuid="0c0087f4-7a22-46d0-91c5-b9b21c9bf2a3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="6" y="17" width="104" height="11" uuid="70518d31-d1ac-4782-8843-d309fa50ed41">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Histórico de Serviço: OS(s)]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="28" width="26" height="11" uuid="58ade0a3-677d-42c6-b24d-a5d3b4f91c11">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Recall:]]></text>
				</staticText>
				<textField>
					<reportElement x="33" y="28" width="140" height="11" uuid="5d135414-55ff-4e4b-8954-9f21534f55fb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EH_RECALL}.equals("N") ? "NÃO": "SIM"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="6" y="39" width="54" height="11" uuid="16671f43-b7b6-479c-8919-076882d084ad">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Observações:]]></text>
				</staticText>
				<textField>
					<reportElement x="60" y="39" width="480" height="48" uuid="f4f7cd55-5506-4c67-8bf2-df86242f116d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_OBSERVACAO}]]></textFieldExpression>
				</textField>
			</frame>
			<textField pattern="#,###.00#;#,###.00#-">
				<reportElement x="73" y="80" width="70" height="11" uuid="ade722eb-53be-4b42-bce3-cf5ae0117cd2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MEDIA_KM_MENSAL}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="207" y="80" width="122" height="11" uuid="e0abefce-8c66-460c-b3c3-0b9891e2a3c7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KM_PROXIMA_REVISAO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###;#,##0.###-">
				<reportElement x="139" y="32" width="325" height="11" uuid="659a3a2f-07eb-46ab-8d47-2074ac023fee">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OS_NOME_CLIENTE_APROVOU}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="120" y="44" width="325" height="11" uuid="4995523a-7833-48b3-86d4-cc732c92fc92">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OS_NOME_CLIENTE_APROVOU}]]></textFieldExpression>
			</textField>
			<textField pattern="#.###;(#.###-)">
				<reportElement x="434" y="20" width="86" height="11" uuid="fbe7f978-5fbe-499c-84b6-240d0572ce21">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OS_NUMERO_ORCAMENTO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#">
				<reportElement x="182" y="20" width="86" height="12" uuid="0a32e39a-ff07-487c-ae90-beff57e53093">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OS_TOTAL_ORCAMENTO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="398" y="80" width="122" height="11" uuid="4e7e8d26-4629-4d79-b747-b6213ec20796">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DATA_PROXIMO_SERVICO}]]></textFieldExpression>
			</textField>
		</band>
		<band height="301">
			<break>
				<reportElement x="0" y="-1" width="100" height="1" uuid="807dec49-d0aa-4644-8a2e-9380e76c7360"/>
			</break>
			<frame>
				<reportElement x="0" y="0" width="555" height="120" uuid="e6d459cf-4209-441f-9a12-c1126e458819">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="108" y="1" width="62" height="14" uuid="1f3a02d7-4b77-4dcb-ad36-0cd1c734b513">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Pré-Entrega]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="27" width="204" height="11" uuid="80cf19cd-ab7b-4752-9aae-0d5cbf97cb90">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Revisar toda Documentação]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="40" width="204" height="11" uuid="8bddbdc0-d207-4391-a684-d89124e06b36">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Confirmar a condição do Veículo (Limpeza)]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="53" width="204" height="11" uuid="c60ecaad-8008-4d76-9660-3cbff5a706d8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Colocar o Livrete de garantia no porta-luva]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="66" width="204" height="11" uuid="69e10885-a672-4450-96fd-c0609da64d40">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sempre as peças substituidas (se o cliente solicitar)]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="79" width="204" height="12" uuid="1e51d7ec-3c3d-4341-892e-4922f6779657">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Telefonar para o cliente e informar que o veículo está pronto]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="93" width="86" height="11" uuid="b56dd052-6047-43cb-9942-9e56cc38e7a3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[1º Contato com o Cliente:]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="106" width="86" height="11" uuid="b4f51f92-34c3-4a46-b99a-531fc2cc554f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[2º Contato com o Cliente:]]></text>
				</staticText>
				<line>
					<reportElement x="264" y="0" width="1" height="120" uuid="fe1a8f23-5f35-4eb7-8f4b-81d899c85638">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="26" width="20" height="13" uuid="b3672ad5-747b-42ec-b060-a1a6359f626a">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_REVISAR_DOCUMENTACAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="39" width="20" height="13" uuid="b73cc983-d811-4690-b60d-ccce1ec3df4e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONFIRMAR_LIMPEZA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="52" width="20" height="13" uuid="c65a7ad8-3b07-4a3b-a82d-2ad135fa12a5">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_LIVRETE_PORTALUVAS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="65" width="20" height="13" uuid="1b00a5ff-f35b-4f66-ab91-8e56795006e5">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_SEP_PEC_SUBSTITUIDAS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="78" width="20" height="13" uuid="ad5651ca-3029-4985-8d72-3576a8e920c8">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_TELEFONAR_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="268" y="27" width="204" height="11" uuid="492b1388-0aa4-49be-892d-27bf66f7121b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Explicar o serviço realizado]]></text>
				</staticText>
				<staticText>
					<reportElement x="268" y="79" width="204" height="12" uuid="4d40acdd-16d3-4d01-b40a-8d02d3c05149">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Retirar capas de proteções]]></text>
				</staticText>
				<staticText>
					<reportElement x="268" y="53" width="204" height="11" uuid="6a8f8631-dcef-4dac-aa01-e65b4e97ba2a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Mostrar as peças substituidas (se o cliente solicitar)]]></text>
				</staticText>
				<staticText>
					<reportElement x="268" y="40" width="204" height="11" uuid="697f0d59-7e18-4c80-8262-a2de5ee44e98">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Explicar o custo final]]></text>
				</staticText>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="39" width="20" height="13" uuid="a8948449-7817-4292-b02a-32f5ca27d86a">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_EXPLICAR_CUSTO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="26" width="20" height="13" uuid="b541c995-20bf-4fbd-86af-dfac70b937e3">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_EXPLICAR_SERVICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="65" width="20" height="13" uuid="edfc9bc4-bde0-41e2-8638-3aeada350721">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_ACOMPANHAR_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="268" y="66" width="204" height="11" uuid="f7584343-4c72-4d1e-b1e8-181f2fd4cf83">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Acompanhar o cliente ao caixa]]></text>
				</staticText>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="78" width="20" height="13" uuid="a5a42a0f-a6ab-4a9b-81f8-8e2edee8bc7d">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_RETIRAR_PROTECAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="52" width="20" height="13" uuid="30379fe8-fa6b-4903-a84a-6bce0652bf07">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_MOSTRAR_PEC_SUBSTITUIDAS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="372" y="1" width="62" height="14" uuid="43ff7f7a-5785-4b21-b227-c1afd7e75c04">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Pré-Entrega]]></text>
				</staticText>
				<textField>
					<reportElement x="92" y="106" width="59" height="11" uuid="7150085a-b560-434c-ad8f-15068ea752ed">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONTATO_CLIENTE2}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="151" y="106" width="59" height="11" uuid="a86b7ea4-aab3-4a99-a9cb-850ee6f74715">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONTATO_CLIENTE2_HORA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="92" y="93" width="59" height="11" uuid="ca0efe87-1440-49f1-a58a-fabc7aa9ec94">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONTATO_CLIENTE1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="151" y="93" width="59" height="11" uuid="e36d5c60-e740-41f5-9fe2-fcec5c1df1e2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONTATO_CLIENTE1_HORA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="120" width="555" height="34" uuid="628f36db-95bc-424f-9d3b-67178abb9157">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="7" y="2" width="62" height="14" uuid="230b5585-629e-4629-a2b1-1d4b4a80d506">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Resultado:]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="16" width="70" height="14" uuid="fe2e40eb-6205-4230-bc89-1a7d3d61b576">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável:]]></text>
				</staticText>
				<staticText>
					<reportElement x="340" y="16" width="28" height="14" uuid="c5053eba-a812-4dd4-8faa-4660a7490b72">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField>
					<reportElement x="69" y="2" width="482" height="14" uuid="a8d434fa-63df-4d16-bbf9-58077af0ee6c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESULTADO_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="76" y="16" width="154" height="14" uuid="fe31cdbb-14c3-466d-8819-7dfefcd897c9">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PRE_ORDEM_RESP_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="368" y="16" width="154" height="14" uuid="a6aca517-967a-46e3-89c8-86dc6ea51f28">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_ENTREGA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement stretchType="ContainerHeight" x="0" y="243" width="555" height="58" uuid="cfe5c154-fc0a-47a8-a280-9a53d31d8a32">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="7" y="2" width="70" height="14" uuid="0cf3c27d-300f-447d-88ac-f34209d33a71">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Recepção:]]></text>
				</staticText>
				<line>
					<reportElement x="264" y="0" width="1" height="58" uuid="38a9cff8-3928-4207-a0ac-dc186ca87f71">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="405" y="3" width="64" height="14" uuid="18c62fd5-51a0-479d-99fb-e1b14f172790">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data de Entrega:]]></text>
				</staticText>
				<staticText>
					<reportElement x="17" y="41" width="229" height="14" uuid="5f625d55-7204-4e0b-bf04-1f126129fd10">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente ou Pessoa Por ele Autorizada (Igual a CNH)]]></text>
				</staticText>
				<staticText>
					<reportElement x="281" y="41" width="229" height="14" uuid="9f389c49-4781-425f-a4a3-63fe05658851">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente ou Pessoa Por ele Autorizada (Igual a CNH)]]></text>
				</staticText>
				<textField>
					<reportElement x="469" y="3" width="83" height="14" uuid="67353432-e06e-4f99-b059-12a748af7b5e">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="271" y="3" width="64" height="14" uuid="4f485337-e5dc-4355-973e-4c632fd3396c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Entrega:]]></text>
				</staticText>
				<frame>
					<reportElement stretchType="ContainerHeight" x="0" y="-89" width="555" height="89" uuid="220aab12-8695-4487-90e2-d4e5084f8c0c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textField>
						<reportElement x="6" y="3" width="540" height="83" uuid="c374bfdd-ed1b-4207-b9d4-ef63a243cdb8">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<textElement verticalAlignment="Top">
							<font fontName="SansSerif" size="8" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_PRE_ORDEM_TEXTO}]]></textFieldExpression>
					</textField>
				</frame>
				<image hAlign="Center" vAlign="Middle" isUsingCache="false" isLazy="true">
					<reportElement x="17" y="17" width="229" height="21" uuid="86d2633b-130c-463a-9dc7-55d959719b97"/>
					<imageExpression><![CDATA[$F{OS_ASSINATURA}]]></imageExpression>
				</image>
				<image hAlign="Center" vAlign="Middle" isUsingCache="false" isLazy="true">
					<reportElement x="280" y="19" width="229" height="21" uuid="fdde919f-36b2-4f77-9341-68f312fa7c41"/>
					<imageExpression><![CDATA[$F{OS_ASSINATURA}]]></imageExpression>
				</image>
			</frame>
		</band>
	</detail>
</jasperReport>
