<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.19.0.final using JasperReports Library version 6.19.0-646c68931cebf1a58bc65c4359d1f0ca223c5e94  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PreOrdemFCA" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="dbe9d5bf-91c0-41e4-83e9-225619d478cd">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="firld_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\marcas\\FCA\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.String"/>
	<parameter name="NUMERO_OS" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT 
	NVL(TO_CHAR(OS.DATA_EMISSAO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_EMISSAO,
	NVL(OS.NUMERO_OS, 0)                    AS OS_NUMERO_OS, 
	TO_CHAR(OS.DATA_ULT_IMP_FAB, 'DD/MM/YYYY') AS OS_DATA_ULT_IMRESSAO,
	TO_CHAR(OS.DATA_ULT_IMP_FAB, 'HH24:MI:SS') AS OS_HORA_ULT_IMRESSAO,
	OS_DIAGNOSTICO.PENT_REVISAR_DOCUMENTACAO,
	OS_DIAGNOSTICO.PENT_CONFIRMAR_LIMPEZA,
	OS_DIAGNOSTICO.PENT_LIVRETE_PORTALUVAS,
	OS_DIAGNOSTICO.PENT_SEP_PEC_SUBSTITUIDAS,
	OS_DIAGNOSTICO.PENT_TELEFONAR_CLIENTE,
	to_char(OS_DIAGNOSTICO.PENT_CONTATO_CLIENTE1) as PENT_CONTATO_CLIENTE1,
	to_char(OS_DIAGNOSTICO.PENT_CONTATO_CLIENTE2) as PENT_CONTATO_CLIENTE2,
	OS_DIAGNOSTICO.PENT_CONTATO_CLIENTE1_HORA,
	OS_DIAGNOSTICO.PENT_CONTATO_CLIENTE2_HORA,
	OS_DIAGNOSTICO.ENT_EXPLICAR_SERVICO,
	OS_DIAGNOSTICO.ENT_EXPLICAR_CUSTO,
	OS_DIAGNOSTICO.ENT_ACOMPANHAR_CLIENTE,
	OS_DIAGNOSTICO.ENT_MOSTRAR_PEC_SUBSTITUIDAS,
	OS_DIAGNOSTICO.ENT_RETIRAR_PROTECAO,
	OS_DIAGNOSTICO.RESULTADO_ENTREGA,       
	(SELECT EMP.NOME_COMPLETO FROM EMPRESAS_USUARIOS EMP WHERE NOME = OS_DIAGNOSTICO.RESPONSAVEL_ENTREGA) AS RESPONSAVEL_ENTREGA,
	OS_DIAGNOSTICO.DATA_ENTREGA,
	VWCRM.TEXTO

FROM OS, OS_DIAGNOSTICO, VW_CRM_MSG_LGPD_MARCA_OS VWCRM, PARM_SYS
WHERE 1 = 1
    AND OS.COD_EMPRESA = $P{COD_EMPRESA} 
    AND OS.NUMERO_OS = $P{NUMERO_OS}
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
	AND OS.COD_EMPRESA = OS_DIAGNOSTICO.COD_EMPRESA (+)
	AND OS.NUMERO_OS = OS_DIAGNOSTICO.NUMERO_OS (+)
	AND oS.COD_EMPRESA = PARM_SYS.COD_EMPRESA(+)
	AND PARM_SYS.TIPO_CONCESSIONARIA  = VWCRM.COD_TIPO_CONCESSIONARIA (+)
	
]]>
	</queryString>
	<field name="OS_DATA_EMISSAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="OS_DATA_EMISSAO"/>
		<property name="com.jaspersoft.studio.field.label" value="OS_DATA_EMISSAO"/>
	</field>
	<field name="OS_NUMERO_OS" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="OS_NUMERO_OS"/>
		<property name="com.jaspersoft.studio.field.label" value="OS_NUMERO_OS"/>
	</field>
	<field name="OS_DATA_ULT_IMRESSAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="OS_DATA_ULT_IMRESSAO"/>
		<property name="com.jaspersoft.studio.field.label" value="OS_DATA_ULT_IMRESSAO"/>
	</field>
	<field name="OS_HORA_ULT_IMRESSAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="OS_HORA_ULT_IMRESSAO"/>
		<property name="com.jaspersoft.studio.field.label" value="OS_HORA_ULT_IMRESSAO"/>
	</field>
	<field name="PENT_REVISAR_DOCUMENTACAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PENT_REVISAR_DOCUMENTACAO"/>
		<property name="com.jaspersoft.studio.field.label" value="PENT_REVISAR_DOCUMENTACAO"/>
	</field>
	<field name="PENT_CONFIRMAR_LIMPEZA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PENT_CONFIRMAR_LIMPEZA"/>
		<property name="com.jaspersoft.studio.field.label" value="PENT_CONFIRMAR_LIMPEZA"/>
	</field>
	<field name="PENT_LIVRETE_PORTALUVAS" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PENT_LIVRETE_PORTALUVAS"/>
		<property name="com.jaspersoft.studio.field.label" value="PENT_LIVRETE_PORTALUVAS"/>
	</field>
	<field name="PENT_SEP_PEC_SUBSTITUIDAS" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PENT_SEP_PEC_SUBSTITUIDAS"/>
		<property name="com.jaspersoft.studio.field.label" value="PENT_SEP_PEC_SUBSTITUIDAS"/>
	</field>
	<field name="PENT_TELEFONAR_CLIENTE" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PENT_TELEFONAR_CLIENTE"/>
		<property name="com.jaspersoft.studio.field.label" value="PENT_TELEFONAR_CLIENTE"/>
	</field>
	<field name="PENT_CONTATO_CLIENTE1" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PENT_CONTATO_CLIENTE1"/>
		<property name="com.jaspersoft.studio.field.label" value="PENT_CONTATO_CLIENTE1"/>
	</field>
	<field name="PENT_CONTATO_CLIENTE2" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PENT_CONTATO_CLIENTE2"/>
		<property name="com.jaspersoft.studio.field.label" value="PENT_CONTATO_CLIENTE2"/>
	</field>
	<field name="PENT_CONTATO_CLIENTE1_HORA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PENT_CONTATO_CLIENTE1_HORA"/>
		<property name="com.jaspersoft.studio.field.label" value="PENT_CONTATO_CLIENTE1_HORA"/>
	</field>
	<field name="PENT_CONTATO_CLIENTE2_HORA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PENT_CONTATO_CLIENTE2_HORA"/>
		<property name="com.jaspersoft.studio.field.label" value="PENT_CONTATO_CLIENTE2_HORA"/>
	</field>
	<field name="ENT_EXPLICAR_SERVICO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="ENT_EXPLICAR_SERVICO"/>
		<property name="com.jaspersoft.studio.field.label" value="ENT_EXPLICAR_SERVICO"/>
	</field>
	<field name="ENT_EXPLICAR_CUSTO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="ENT_EXPLICAR_CUSTO"/>
		<property name="com.jaspersoft.studio.field.label" value="ENT_EXPLICAR_CUSTO"/>
	</field>
	<field name="ENT_ACOMPANHAR_CLIENTE" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="ENT_ACOMPANHAR_CLIENTE"/>
		<property name="com.jaspersoft.studio.field.label" value="ENT_ACOMPANHAR_CLIENTE"/>
	</field>
	<field name="ENT_MOSTRAR_PEC_SUBSTITUIDAS" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="ENT_MOSTRAR_PEC_SUBSTITUIDAS"/>
		<property name="com.jaspersoft.studio.field.label" value="ENT_MOSTRAR_PEC_SUBSTITUIDAS"/>
	</field>
	<field name="ENT_RETIRAR_PROTECAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="ENT_RETIRAR_PROTECAO"/>
		<property name="com.jaspersoft.studio.field.label" value="ENT_RETIRAR_PROTECAO"/>
	</field>
	<field name="RESULTADO_ENTREGA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="RESULTADO_ENTREGA"/>
		<property name="com.jaspersoft.studio.field.label" value="RESULTADO_ENTREGA"/>
	</field>
	<field name="RESPONSAVEL_ENTREGA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="RESPONSAVEL_ENTREGA"/>
		<property name="com.jaspersoft.studio.field.label" value="RESPONSAVEL_ENTREGA"/>
	</field>
	<field name="DATA_ENTREGA" class="java.sql.Timestamp">
		<property name="com.jaspersoft.studio.field.name" value="DATA_ENTREGA"/>
		<property name="com.jaspersoft.studio.field.label" value="DATA_ENTREGA"/>
	</field>
	<field name="TEXTO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="TEXTO"/>
		<property name="com.jaspersoft.studio.field.label" value="TEXTO"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="108" splitType="Stretch">
			<frame>
				<reportElement x="0" y="54" width="468" height="54" uuid="332c7660-d7d5-426a-9400-d9edb60d2d69">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="0" y="1" width="107" height="22" uuid="6d07a055-665f-4b0b-afcd-02d40309959f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[DATA DE EMISSÃO]]></text>
				</staticText>
				<staticText>
					<reportElement x="107" y="1" width="180" height="22" uuid="338e47ee-b140-4716-9ee8-aff206518ea9">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[ORDEM DE SERVIÇO]]></text>
				</staticText>
				<staticText>
					<reportElement x="287" y="1" width="88" height="22" uuid="95c39a4c-679a-4eca-bdc9-b3dc496757e3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[IMPRESSÃO]]></text>
				</staticText>
				<staticText>
					<reportElement x="375" y="1" width="91" height="22" uuid="e1abe3d2-f703-4086-ba08-421eb8d2e706">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[REIMPRESSÃO]]></text>
				</staticText>
				<textField>
					<reportElement x="0" y="23" width="107" height="22" uuid="0086c2ec-dcc9-4726-8915-03286bf66dee">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="107" y="23" width="180" height="22" backcolor="#C0C0C0" uuid="5d781122-a793-451c-a74e-ec4befd666b1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="15" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="287" y="23" width="88" height="22" uuid="0d6e4734-ac51-4ac4-8a3b-888fcc973057">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="375" y="23" width="91" height="22" uuid="b12e4b03-5be3-41e6-ad3f-54f78a883b97">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_ULT_IMRESSAO}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="468" y="54" width="87" height="54" uuid="f27aa455-fffd-444f-a4ea-4313e948f91d">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="14" y="12" width="61" height="11" uuid="b61af92b-233b-498f-a6e4-87c93df67f84">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Versão: 2.1]]></text>
				</staticText>
				<staticText>
					<reportElement x="13" y="30" width="25" height="11" uuid="06bf9020-6b2d-4c0a-9edf-2294c1bf28f1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Página:]]></text>
				</staticText>
				<textField evaluationTime="Page">
					<reportElement x="38" y="30" width="15" height="11" uuid="764068d5-9cd5-4e1e-8599-27191c79586f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="57" y="30" width="15" height="11" uuid="9705a8d2-cb7d-47fc-8197-29df6b60251a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="53" y="30" width="4" height="11" uuid="03cd847f-9b8b-4f25-81f3-0c5b2cabc4f2">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[/]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="0" width="555" height="54" uuid="dab195aa-4916-4d06-938d-a326fd9fe0e4">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport>
					<reportElement x="0" y="0" width="555" height="54" uuid="27401d67-1909-42ac-bf43-833de420e71d">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsFCACabecalho.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
	</title>
	<detail>
		<band height="321" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="120" uuid="6831ee3f-b455-483c-a18f-11a45279d895">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="108" y="1" width="62" height="14" uuid="5056fc6e-ab32-412b-ad85-a16509859f82">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Pré-Entrega]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="27" width="204" height="11" uuid="8efc9fb6-e91e-43bf-980a-09f88c0f8cd3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Revisar toda Documentação]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="40" width="204" height="11" uuid="424c4b43-19b8-4555-b069-6be74d809e9d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Confirmar a condição do Veículo (Limpeza)]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="53" width="204" height="11" uuid="a9afeec2-f769-41af-8984-a10cef267ee0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Colocar o Livrete de garantia no porta-luva]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="66" width="204" height="11" uuid="a18f628c-8d17-4c60-b976-d318b280d3a7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Sempre as peças substituidas (se o cliente solicitar)]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="79" width="204" height="12" uuid="728d93c1-211a-4743-bf58-85c76d94f0df">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Telefonar para o cliente e informar que o veículo está pronto]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="93" width="86" height="11" uuid="59b1fee4-edc1-4087-8b3f-28890c81580b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[1º Contato com o Cliente:]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="106" width="86" height="11" uuid="d3fcf52b-e85a-4d19-a3b4-b538b7c7537c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[2º Contato com o Cliente:]]></text>
				</staticText>
				<line>
					<reportElement x="264" y="0" width="1" height="120" uuid="f6ccea13-8d6c-43b6-888d-f6e558002469">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="26" width="20" height="13" uuid="15d04386-d23b-4c6f-9be3-382f1f5d1059">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_REVISAR_DOCUMENTACAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="39" width="20" height="13" uuid="d15bccaa-ce9b-4f7c-ba48-b38a65172a8a">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONFIRMAR_LIMPEZA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="52" width="20" height="13" uuid="9b58c8da-348c-4104-b06f-4fe12cec0e4d">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_LIVRETE_PORTALUVAS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="65" width="20" height="13" uuid="90f37963-b8a1-4394-b427-5537bd007cb6">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_SEP_PEC_SUBSTITUIDAS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="237" y="78" width="20" height="13" uuid="3af74635-f089-4134-9afd-8fc2faf60c43">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_TELEFONAR_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="268" y="27" width="204" height="11" uuid="c59b5e36-7f9e-4919-a2ec-b8a4f2b55dd9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Explicar o serviço realizado]]></text>
				</staticText>
				<staticText>
					<reportElement x="268" y="79" width="204" height="12" uuid="80c3b2ed-ed94-41be-83e6-cfe9669adb78">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Retirar capas de proteções]]></text>
				</staticText>
				<staticText>
					<reportElement x="268" y="53" width="204" height="11" uuid="a920345b-738c-4a55-a9ae-6fba970d1d33">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Mostrar as peças substituidas (se o cliente solicitar)]]></text>
				</staticText>
				<staticText>
					<reportElement x="268" y="40" width="204" height="11" uuid="d4afa5b9-187a-49ee-aaad-1a87659cca35">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Explicar o custo final]]></text>
				</staticText>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="39" width="20" height="13" uuid="073ba835-db88-431d-8fba-430662f9e333">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_EXPLICAR_CUSTO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="26" width="20" height="13" uuid="5db9672e-2826-48e5-9a47-fe5f7784d968">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_EXPLICAR_SERVICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="65" width="20" height="13" uuid="db562714-09fe-44a1-8e19-b3f4b42364d5">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_ACOMPANHAR_CLIENTE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="268" y="66" width="204" height="11" uuid="db2e4354-cbfe-4b41-b8c5-f93bb2829d03">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Acompanhar o cliente ao caixa]]></text>
				</staticText>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="78" width="20" height="13" uuid="33cb71b6-4017-42ca-9370-6cf15721bc9e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_RETIRAR_PROTECAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="ContainerHeight" x="503" y="52" width="20" height="13" uuid="b8c76593-0c66-4f47-b538-c67e4c7c644f">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
						<paragraph lineSpacingSize="1.0"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ENT_MOSTRAR_PEC_SUBSTITUIDAS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="372" y="1" width="62" height="14" uuid="516d2e7a-e47b-4863-a76d-2fdd0c86180e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Pré-Entrega]]></text>
				</staticText>
				<textField>
					<reportElement x="92" y="106" width="59" height="11" uuid="97b28b28-365b-4de2-92f1-0554352835d3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONTATO_CLIENTE2}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="151" y="106" width="59" height="11" uuid="84997c1f-f4ee-48db-a950-c6b4ec30fa73">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONTATO_CLIENTE2_HORA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="92" y="93" width="59" height="11" uuid="e8d71cf1-8eab-4333-b687-a582b563bdde">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONTATO_CLIENTE1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="151" y="93" width="59" height="11" uuid="0a3c2774-0838-4338-bd73-46026097e5b1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PENT_CONTATO_CLIENTE1_HORA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="120" width="555" height="34" uuid="3e94885e-8231-4f8c-abe6-7c660a919e00">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="7" y="2" width="62" height="14" uuid="49a020ac-1cd6-4533-89f0-753f08946590">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Resultado:]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="16" width="70" height="14" uuid="deaa2b59-c21a-4993-8efd-e77fea9b9ada">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável:]]></text>
				</staticText>
				<staticText>
					<reportElement x="340" y="16" width="28" height="14" uuid="4a25552c-7dd3-4d1c-9a05-f21516929b72">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField>
					<reportElement x="69" y="2" width="482" height="14" uuid="b8894ebd-bddc-4ea9-a314-c2533480c6ed">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESULTADO_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="76" y="16" width="154" height="14" uuid="8b1cf046-a792-4e26-8795-4eef4d422c24">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESPONSAVEL_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="368" y="16" width="154" height="14" uuid="567bf8b6-dace-45b3-885d-e45b8f42f155">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_ENTREGA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement stretchType="ContainerHeight" x="0" y="243" width="555" height="58" uuid="be33f815-af28-4d13-9dc8-7ec33a95b48e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="7" y="5" width="70" height="14" uuid="941e63da-9dc5-4441-aa17-23f00e94f578">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Recepção:]]></text>
				</staticText>
				<line>
					<reportElement x="264" y="0" width="1" height="58" uuid="3f493501-18db-4137-9d1a-054870212d24">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<textField>
					<reportElement x="6" y="-86" width="540" height="83" uuid="1a9a26de-2c7b-4148-adf0-1763eec0a2d5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEXTO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="405" y="6" width="64" height="14" uuid="a7cafc8b-f492-4fae-8602-bf1ac45cadee">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data de Entrega:]]></text>
				</staticText>
				<staticText>
					<reportElement x="17" y="41" width="229" height="14" uuid="d931c364-0ae1-475e-abfe-0303ec1afc6f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente ou Pessoa Por ele Autorizada (Igual a CNH)]]></text>
				</staticText>
				<staticText>
					<reportElement x="281" y="41" width="229" height="14" uuid="9c705778-c4d5-4291-9b3b-9fba5414e5d8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente ou Pessoa Por ele Autorizada (Igual a CNH)]]></text>
				</staticText>
				<textField>
					<reportElement x="469" y="6" width="83" height="14" uuid="c2dbc690-335b-4435-b3e0-71ae55a40ffd">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="271" y="6" width="64" height="14" uuid="35159fa2-4bb6-481f-baa7-6f64fab22269">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Entrega:]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement stretchType="ContainerHeight" x="0" y="154" width="555" height="89" uuid="1d520ff2-ca35-41e5-af8a-c5ac804241a4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</frame>
		</band>
	</detail>
</jasperReport>
