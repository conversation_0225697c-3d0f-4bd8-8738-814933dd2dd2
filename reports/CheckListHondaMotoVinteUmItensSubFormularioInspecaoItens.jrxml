<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaMotoVinteUmItensSubFormularioInspecao" pageWidth="277" pageHeight="211" columnWidth="277" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232572.0]]></defaultValueExpression>
	</parameter>
	<parameter name="ID_GRUPO" class="java.lang.String">
		<defaultValueExpression><![CDATA["40095"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select tb.ID_GRUPO,
	 	    tb.GRUPO_DESCRICAO,
	 	    tb.COD_ITEM, tb.ITEM_DESCRICAO,
	 	    UPPER(tb.SELECIONADO_OPCAO_DESCRICAO) as SELECIONADO_OPCAO_DESCRICAO,
	 	     tb.SELECIONADO_OBSERVACAO AS OBSERVACAO
from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS}, 0,'')) tb
where tb.ID_GRUPO in ($P!{ID_GRUPO})
order by tb.grupo_ordem, tb.id_grupo, tb.item_ordem]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="GRUPO_DESCRICAO" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="SELECIONADO_OPCAO_DESCRICAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.io.InputStream"/>
	<group name="grupo">
		<groupExpression><![CDATA[$F{ID_GRUPO}]]></groupExpression>
		<groupHeader>
			<band height="11">
				<textField>
					<reportElement mode="Opaque" x="0" y="0" width="277" height="11" backcolor="#D3D5D4" uuid="47a1a47e-c224-42af-85b0-2d68d9c24a63">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GRUPO_DESCRICAO}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<detail>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[!$F{ITEM_DESCRICAO}.equals("Pneu Dianteiro (desgaste e bolhas)") && !$F{ITEM_DESCRICAO}.equals("Pneu Traseiro (desgaste e bolhas)")]]></printWhenExpression>
			<frame>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="277" height="11" isPrintWhenDetailOverflows="true" uuid="ad137f15-17db-4cbb-bd2f-7bff2f68e787"/>
				<box>
					<leftPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textField>
					<reportElement mode="Opaque" x="255" y="2" width="9" height="7" forecolor="#FFFFFF" backcolor="#FF0000" uuid="96f4f414-c0a3-4dbd-a936-00f97c99140c">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SELECIONADO_OPCAO_DESCRICAO}.equals("MANUTENÇÃO IMEDIATA") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="225" y="2" width="9" height="7" backcolor="#FFFF00" uuid="0de1773b-78d4-4aa5-822c-c91aff1be577">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SELECIONADO_OPCAO_DESCRICAO}.equals("ATENÇÃO") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="195" y="2" width="9" height="7" backcolor="#99CC00" uuid="3aa46f38-ec6c-4e59-8035-fcc15a8d05fc">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SELECIONADO_OPCAO_DESCRICAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="0" width="187" height="11" uuid="eb27096c-8e07-489a-92aa-0343c7822420">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<printWhenExpression><![CDATA[$F{ITEM_DESCRICAO}.equals("Pneu Dianteiro (desgaste e bolhas)") || $F{ITEM_DESCRICAO}.equals("Pneu Traseiro (desgaste e bolhas)")]]></printWhenExpression>
			<frame>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="277" height="11" isPrintWhenDetailOverflows="true" uuid="e65635eb-9364-44a8-9855-bd8a090934d2"/>
				<box>
					<leftPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textField>
					<reportElement mode="Opaque" x="255" y="2" width="9" height="7" forecolor="#FFFFFF" backcolor="#FF0000" uuid="4250b3d6-d024-420d-9a9c-989ad329dffb">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SELECIONADO_OPCAO_DESCRICAO}.equals("MANUTENÇÃO IMEDIATA") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="225" y="2" width="9" height="7" backcolor="#FFFF00" uuid="49924c9a-c944-4dda-b138-69ebab5655d6">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SELECIONADO_OPCAO_DESCRICAO}.equals("ATENÇÃO") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="195" y="2" width="9" height="7" backcolor="#99CC00" uuid="3c60a7fa-053d-4e5a-b763-54a820225552">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SELECIONADO_OPCAO_DESCRICAO}.equals("OK") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="179" y="1" width="16" height="9" uuid="e4cac785-f805-4bad-9339-8eafda5253d8">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}.equals("Pneu Dianteiro (desgaste e bolhas)") ? "Diant" : "Tras"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="209" y="1" width="16" height="9" uuid="e201d252-84f6-4f50-ba01-b39ada89d9ba">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}.equals("Pneu Dianteiro (desgaste e bolhas)") ? "Diant" : "Tras"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="239" y="1" width="16" height="9" uuid="cd75cd70-d0ae-4508-96c4-705f92494039">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}.equals("Pneu Dianteiro (desgaste e bolhas)") ? "Diant" : "Tras"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="0" width="174" height="11" uuid="991c6b52-6393-4c15-9def-47092aa617fa">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{ITEM_DESCRICAO}.equals("Pneu Traseiro (desgaste e bolhas)")]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[Pneu (desgaste e bolhas)]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
