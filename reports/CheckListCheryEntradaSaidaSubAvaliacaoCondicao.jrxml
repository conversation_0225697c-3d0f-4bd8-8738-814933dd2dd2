<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListCheryEntradaSaidaSubAvaliacaoCondicao" pageWidth="179" pageHeight="108" columnWidth="179" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select cod_empresa, nome from empresas e where e.cod_empresa in (1,2,3)]]>
		</queryString>
		<field name="COD_EMPRESA" class="java.lang.Double"/>
		<field name="NOME" class="java.lang.String"/>
	</subDataset>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="ID_CHECKLIST" class="java.lang.Double"/>
	<parameter name="ID_GRUPO" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH consulta as (
    select a.item_descricao, a.selecionado_opcao_descricao as descricao_opcao
	from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
	where a.id_checklist = 50000
	      		and a.id_grupo in 50050
),

PARAMETROS AS (
           SELECT
              6 MULTIPLO, /*Determina o multiplo da quantidade de linhas*/
              0 MAXIMO_LINHAS /* determina o maximo de linhas, zero é ilimitado, o numero de linhas que a consulta retornar*/
              FROM DUAL
),
CONSULTA_LIMITADA as (
     SELECT consulta.* FROM consulta,parametros where (rownum <= parametros.maximo_linhas or parametros.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL as (
    SELECT * FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL, NULL
    FROM dual,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),parametros.multiplo) <> 0 or (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY level <= parametros.multiplo - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), parametros.multiplo)
)
SELECT * /*[PODE ESPECIFICAR OS CAMPOS]*/ FROM CONSULTA_FINAL]]>
	</queryString>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="30">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement mode="Opaque" x="0" y="0" width="179" height="30" backcolor="#525252" uuid="b7030bf2-8b84-4806-8a38-2827445c1e3b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="89" height="30" forecolor="#FFFFFF" backcolor="#FFFFFF" uuid="9c43df80-8d8b-4ba6-a9e0-3e155781608d">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="3" rightPadding="0">
						<pen lineWidth="0.75" lineColor="#030303"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Item]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="119" y="0" width="30" height="30" forecolor="#FFFFFF" backcolor="#FFFFFF" uuid="2e2c6fe4-bdca-43d8-90a4-2ceaf2022368">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<pen lineWidth="0.75" lineColor="#030303"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Regular]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="149" y="0" width="30" height="30" forecolor="#FFFFFF" backcolor="#FFFFFF" uuid="f8a80cb5-9360-4a6e-9fa9-86c2c33ff3fd"/>
					<box leftPadding="0" rightPadding="0">
						<pen lineWidth="0.75" lineColor="#030303"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Ruim]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="89" y="0" width="30" height="30" forecolor="#FFFFFF" backcolor="#FFFFFF" uuid="6818a42b-c44f-4e9d-a4a8-77eef240838b">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<pen lineWidth="0.75" lineColor="#030303"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="SansSerif" size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Bom]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="179" height="13" uuid="98e3a3fc-a687-48e5-a433-1e53d3afce84"/>
				<box>
					<topPen lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="149" y="0" width="30" height="13" forecolor="#000000" uuid="c424cddc-8593-44b2-b84d-8c9b3b76785a">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("Ruim") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="119" y="0" width="30" height="13" forecolor="#000000" uuid="b3316a5d-c19a-4b36-bdff-99f9bee8b473">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("Regular") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="89" height="13" forecolor="#000000" uuid="97cefa9c-e5c2-496e-a528-258dbdca31e1">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box leftPadding="3">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="89" y="0" width="30" height="13" forecolor="#000000" uuid="7411ec9c-969a-428e-be02-cf84d2992ab3">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("Bom") ? "X" : ""]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
