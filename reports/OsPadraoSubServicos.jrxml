<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsPadraoSubServicos" pageWidth="595" pageHeight="842" whenNoDataType="NoPages" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="afcb4e4b-2cf4-4c11-b5d3-f098eece963e">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT OS_SERVICOS.NUMERO_OS,
       OS_SERVICOS.COD_EMPRESA,
       SERVICOS.DESCRICAO_SERVICO,
       OS_SERVICOS.COD_SERVICO,
       OS_SERVICOS.PRECO_VENDA,
       '' AS STATUS,
       SUBSTR(TO_CHAR(OS_SERVICOS.ITEM + 100), 2, 2) AS ITEM,
       OS_SERVICOS.TEMPO_PADRAO TMO,
       'N' AS DESAPROVADO
FROM OS_SERVICOS, SERVICOS, OS_PARADA_MOTIVOS, SERVICOS_SETORES
WHERE OS_SERVICOS.COD_SERVICO = SERVICOS.COD_SERVICO
 AND OS_SERVICOS.COD_MOTIVO = OS_PARADA_MOTIVOS.COD_MOTIVO(+)
 AND (OS_SERVICOS.COD_EMPRESA, OS_SERVICOS.NUMERO_OS) IN (
   SELECT * FROM TABLE(PKG_CRM_SERVICE_UTIL.GET_TABLE_OS_RELAC_NUM_FABRICA($P{NUMERO_OS}, $P{COD_EMPRESA}))
 )
 AND SERVICOS_SETORES.COD_SETOR = SERVICOS.COD_SETOR
 AND OS_SERVICOS.NUMERO_OS > 0

UNION ALL

SELECT OS_SERV_ORC.NUMERO_OS,
       OS_SERV_ORC.COD_EMPRESA,   
       SERVICOS.DESCRICAO_SERVICO,          
       OS_SERV_ORC.COD_SERVICO,
       OS_SERV_ORC.PRECO_VENDA,
       'A' AS STATUS,      
       SUBSTR(TO_CHAR(OS_SERV_ORC.ITEM + 100), 2, 2) AS ITEM,
       OS_SERV_ORC.TEMPO TMO,
       OS_SERV_ORC.DESAPROVADO
  FROM OS_SERV_ORC, SERVICOS
 WHERE OS_SERV_ORC.COD_SERVICO = SERVICOS.COD_SERVICO

   AND (OS_SERV_ORC.COD_EMPRESA, OS_SERV_ORC.NUMERO_OS) IN (
   SELECT * FROM TABLE(PKG_CRM_SERVICE_UTIL.GET_TABLE_OS_RELAC_NUM_FABRICA($P{NUMERO_OS}, $P{COD_EMPRESA}))
   )
   
   
 ORDER BY 7, 4]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="DESCRICAO_SERVICO" class="java.lang.String"/>
	<field name="COD_SERVICO" class="java.lang.String"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="STATUS" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.String"/>
	<field name="TMO" class="java.lang.Double"/>
	<field name="DESAPROVADO" class="java.lang.String"/>
	<title>
		<band height="35">
			<staticText>
				<reportElement x="6" y="20" width="20" height="14" uuid="3b991e01-b5ce-4268-bf9b-8bf353a677c8"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[IT]]></text>
			</staticText>
			<staticText>
				<reportElement x="37" y="20" width="43" height="14" uuid="7afae024-891d-4419-8fb6-ec5ac877d9ec"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[TMO]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="20" width="110" height="14" uuid="334a35e9-7e50-4154-83f9-7689ff00ac88"/>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement x="456" y="20" width="89" height="14" uuid="1898c019-fbb5-496a-be5f-5d995f07a1de"/>
				<textElement textAlignment="Right">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="1" width="554" height="18" backcolor="#D9D9D9" uuid="ed5bf0c2-a29e-4852-867d-5573bdbb6234">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="6" y="3" width="100" height="13" uuid="24ee5b5d-5a5f-4715-9bc1-4789b0f9c594">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Serviços]]></text>
			</staticText>
			<staticText>
				<reportElement x="85" y="20" width="81" height="14" uuid="ce5fb0cf-ece4-4a28-a41f-3715f8ee9597"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" isBold="true"/>
				</textElement>
				<text><![CDATA[Cod. Serviço]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="16" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="6" y="1" width="20" height="12" uuid="28a29738-41d5-428c-8577-f36eed551578"/>
				<textElement textAlignment="Center">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="37" y="1" width="43" height="12" uuid="3f853313-d26d-4b6b-b075-b5963e50fea0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TMO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="173" y="1" width="250" height="12" uuid="63d9ab59-c325-4d30-b6b8-39ed17d1e910">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_SERVICO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="456" y="1" width="90" height="12" uuid="e3697045-2286-461c-8501-6336e5ad91ec"/>
				<textElement textAlignment="Right">
					<font fontName="Calibri" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="85" y="1" width="81" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="f3544579-1968-4a1f-92fe-984bbbac0755">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Calibri" size="8"/>
					<paragraph firstLineIndent="0" tabStopWidth="40"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_SERVICO}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="5" y="7" width="542" height="1" uuid="6bdf7a62-c277-4942-aec9-f1bd8e0c8295">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<printWhenExpression><![CDATA[$F{DESAPROVADO}.equals("S")]]></printWhenExpression>
				</reportElement>
			</line>
		</band>
	</detail>
	<summary>
		<band>
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</summary>
</jasperReport>
