<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaMotoVinteUmItensSubOrcamentoServicosAdicionais" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.report.description" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[WITH consulta as (
    SELECT null as COD_SERVICO, null as COD_ITEN FROM DUAL
),

PARAMETROS AS (
           SELECT
              3 MULTIPLO, /*Determina o multiplo da quantidade de linhas*/
              3 MAXIMO_LINHAS /* determina o maximo de linhas, zero é ilimitado, o numero de linhas que a consulta retornar*/
              FROM DUAL
),
CONSULTA_LIMITADA as (
     SELECT consulta.* FROM consulta,parametros where (rownum <= parametros.maximo_linhas or parametros.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL as (
    SELECT * FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL, NULL
    FROM dual,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),parametros.multiplo) <> 0 or (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY level <= parametros.multiplo - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), parametros.multiplo)
)
SELECT * /*[PODE ESPECIFICAR OS CAMPOS]*/ FROM CONSULTA_FINAL]]>
	</queryString>
	<field name="COD_SERVICO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_SERVICO"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_SERVICO"/>
	</field>
	<field name="COD_ITEN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_ITEN"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_ITEN"/>
	</field>
	<group name="GRUPO_SERVICO">
		<groupExpression><![CDATA[$F{COD_SERVICO}]]></groupExpression>
		<groupHeader>
			<band height="19">
				<frame>
					<reportElement x="0" y="0" width="398" height="19" isPrintWhenDetailOverflows="true" uuid="e468d132-4171-4ea4-9353-1e805ee89034"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="86" height="9" uuid="e4be465b-eb74-4f4e-b3a9-6771bedfdc22">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[SERVIÇO REQUISITADO]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="367" height="9" uuid="984e7fa0-5415-4dd2-87e1-d0c8b1d8a3bf">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
					</textField>
				</frame>
				<frame>
					<reportElement x="470" y="0" width="85" height="19" isPrintWhenDetailOverflows="true" uuid="fb98eaf5-659c-4d8c-b220-8dae57a29149"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="46" height="9" uuid="f673d394-b891-4723-bd1d-d4950f1f03b5">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[MÃO DE OBRA]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="76" height="9" uuid="410fdd82-16c2-4447-ae9e-78bc68393b20">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
					</textField>
				</frame>
				<frame>
					<reportElement x="398" y="0" width="72" height="19" isPrintWhenDetailOverflows="true" uuid="d625bdec-5706-4023-b980-d1e1bcd8049e"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="46" height="9" uuid="26480964-4b53-46e6-b865-f60fac46d4b8">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[HORAS]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="54" height="9" uuid="0734c28b-37eb-4414-b26a-d09a3ea881c1">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
					</textField>
				</frame>
			</band>
		</groupHeader>
	</group>
	<columnHeader>
		<band height="13">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="13" forecolor="#FFFFFF" backcolor="#000000" uuid="0e3eeb7f-8e2d-43ad-a12f-1ff359450a81">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[ORÇAMENTOS DE SEVIÇOS ADICIONAIS]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="19">
			<frame>
				<reportElement x="0" y="0" width="80" height="19" isPrintWhenDetailOverflows="true" uuid="d722f36a-d87d-4cce-9eb4-0034f34036d4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<staticText>
					<reportElement x="3" y="0" width="46" height="9" printWhenGroupChanges="GRUPO_SERVICO" uuid="843c0829-4659-4ef6-95a4-7bb97e460e7f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[$V{PAGE_COUNT} == 1]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[QTDE]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="9" width="69" height="9" uuid="2e983cd6-d0ae-4a0a-bfb2-61ab854cee24">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
				</textField>
			</frame>
			<frame>
				<reportElement x="80" y="0" width="114" height="19" isPrintWhenDetailOverflows="true" uuid="57b66041-e464-419e-9167-1f0fac33e559"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<staticText>
					<reportElement x="3" y="0" width="46" height="9" uuid="77e44d01-18ee-47ae-91ba-22ff60f2ce65">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[$V{PAGE_COUNT} == 1]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[COD]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="9" width="107" height="9" uuid="a7173284-f8d8-4977-8a07-d7f3d55ea9ef">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
				</textField>
			</frame>
			<frame>
				<reportElement x="194" y="0" width="204" height="19" isPrintWhenDetailOverflows="true" uuid="345d2f32-f811-4329-9303-42d086296a5f"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<staticText>
					<reportElement x="3" y="0" width="68" height="9" uuid="f4cd2efb-d3e9-41a7-9988-016484684469">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[$V{PAGE_COUNT} == 1]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[NOME DA PEÇA]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="9" width="197" height="9" uuid="c9c45c71-2c18-48e8-b8cc-963ab09f1607">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
				</textField>
			</frame>
			<frame>
				<reportElement x="398" y="0" width="72" height="19" isPrintWhenDetailOverflows="true" uuid="49a3e82b-8967-4f9a-b406-025b076a8f7a"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<staticText>
					<reportElement x="3" y="0" width="54" height="9" uuid="2254d87d-e8d2-44c9-8594-301f78aad93d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[$V{PAGE_COUNT} == 1]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[VALOR UNITÁRIO]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="9" width="54" height="9" uuid="3499bc86-f654-469e-ab1d-4a0520e89872">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
				</textField>
			</frame>
			<frame>
				<reportElement x="470" y="0" width="85" height="19" isPrintWhenDetailOverflows="true" uuid="ea0e71bd-52b6-4a22-9aa0-8a2374a8c2b1"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<staticText>
					<reportElement x="3" y="0" width="46" height="9" uuid="1ba90814-cf61-4bf2-8096-17081c3f04e3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[$V{PAGE_COUNT} == 1]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[VALOR]]></text>
				</staticText>
				<textField>
					<reportElement x="3" y="9" width="76" height="9" uuid="9293671e-fce6-49ae-8dee-97b9398ed683">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="57">
			<frame>
				<reportElement x="0" y="0" width="555" height="57" uuid="7861ccd1-7812-47a7-907c-6c44ce02875f">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<frame>
					<reportElement x="482" y="0" width="73" height="19" isPrintWhenDetailOverflows="true" uuid="24a2f603-d678-4c02-934e-f3b6a022bbbd"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<textField>
						<reportElement x="9" y="5" width="54" height="9" uuid="b80617f9-af8e-4159-99c9-b609bfcd42a7">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
					</textField>
				</frame>
				<frame>
					<reportElement x="482" y="19" width="73" height="19" isPrintWhenDetailOverflows="true" uuid="52e3df7b-b130-43b0-a090-7369235169af"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<textField>
						<reportElement x="9" y="5" width="54" height="9" uuid="35237be0-b6fc-4ebc-950c-cb4784564f61">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
					</textField>
				</frame>
				<frame>
					<reportElement x="482" y="38" width="73" height="19" isPrintWhenDetailOverflows="true" uuid="ccd1c62b-8c5a-4fea-8d79-ea140f233be1"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<textField>
						<reportElement x="9" y="5" width="54" height="9" uuid="c099a533-6ef1-41de-abe8-e4ec60e644f2">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
					</textField>
				</frame>
				<staticText>
					<reportElement x="427" y="43" width="54" height="9" uuid="f3f07e65-f6e7-4993-a4a1-ad4f7eba859b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[TOTAL]]></text>
				</staticText>
				<staticText>
					<reportElement x="410" y="24" width="71" height="9" uuid="e603fd3f-a4d8-4acf-a1b4-cc4d99ba271f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[SERVICO EXPRESSO]]></text>
				</staticText>
				<staticText>
					<reportElement x="427" y="5" width="54" height="9" uuid="737667f9-001f-461b-a0ee-43f21ec8d978">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[SUB TOTAL]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="89" y="16" width="122" height="22" uuid="ba303218-1239-4ef5-a402-4206b15ead48">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
				</image>
				<staticText>
					<reportElement x="88" y="40" width="125" height="9" uuid="b966a604-b2de-4cc3-b96f-33f1e9a35a9e">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<text><![CDATA[AUTORIZAÇÃO DO CLIENTE]]></text>
				</staticText>
			</frame>
		</band>
	</columnFooter>
</jasperReport>
