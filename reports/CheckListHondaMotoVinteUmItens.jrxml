<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaMotoVinteUmItens" pageWidth="595" pageHeight="842" columnWidth="575" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10" isSummaryWithPageHeaderAndFooter="true" isFloatColumnFooter="true" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\34613\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\temp\\image_zk\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH Q_OS AS
 (SELECT OS.COD_EMPRESA,
         OS_AGENDA.DATA_AGENDADA,
         TO_CHAR(OS_AGENDA.DATA_AGENDADA, 'DD/MM/YYYY HH24:MI') AS D_AG_STG,
         OS.STATUS_OS,
         OS.NUMERO_OS,
         OS.COD_OS_AGENDA,
         ABS(OS.NUMERO_OS) AS ABS_OSNUM,
         OS.COD_CLIENTE,
         OS.CLIENTE_RAPIDO,
         OS.TIPO_ENDERECO,
         OS.OBSERVACAO,
         OS.EXTENDIDA,
         OS.SEGURADORA,
         OS.DATA_EMISSAO,
		 OS.DATA_ENTREGA,
         (TO_DATE(OS.DATA_EMISSAO, 'DD/MM/YYYY') || ' AS ' ||
         OS.HORA_EMISSAO) AS DH_EMISSAO,
         DECODE(OS.DATA_LIBERADO,
                NULL,
                '',
                (TO_DATE(OS.DATA_LIBERADO, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_LIBERADO)) AS DH_LIBERADO,
         DECODE(OS.DATA_ENCERRADA,
                NULL,
                '',
                (TO_DATE(OS.DATA_ENCERRADA, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_ENCERRADA)) AS DH_ENCERRADO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2) AS DIA_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2) AS MES_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4) AS ANO_EMISSAO,
         OS.HORA_EMISSAO,
         OS.HORA_ENCERRADA,
         OS.DATA_ENCERRADA,
         OS.HORA_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 1, 2) AS HORA24_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 4, 2) AS MINUTO_PROMETIDA,
         
         OS.DATA_PROMETIDA,
         OS.DATA_PROMETIDA_REVISADA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2) AS DIA_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2) AS MES_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4) AS ANO_PROMETIDA,
         
         OS.VALOR_SERVICOS_BRUTO,
         OS.VALOR_ITENS_BRUTO,

		(case when nvl(VALOR_SERVICOS_BRUTO,0) = 0 then 0 else OS.DESCONTOS_SERVICOS / OS.VALOR_SERVICOS_BRUTO END) AS DESCONTO_SERVICO_PORCENT,
		(case when nvl(OS.VALOR_ITENS_BRUTO,0) = 0 then 0 else OS.DESCONTOS_ITENS / OS.VALOR_ITENS_BRUTO END) AS DESCONTO_ITENS_PORCENT,
		 
         (OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS TOTAL_SERVICOS_LIQUIDO,
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS) AS TOTAL_ITENS_LIQUIDO,
		 
         ((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) +
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS)) AS TOTAL_OS,
         
		 OS.COD_SEGURADORA,
         OS_DADOS_VEICULOS.ANO,
         OS_DADOS_VEICULOS.HORIMETRO,
         OS_DADOS_VEICULOS.PRISMA,
         OS_DADOS_VEICULOS.DATA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2) AS DIA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2) AS MES_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4) AS ANO_VENDA,
         OS_DADOS_VEICULOS.COMBUSTIVEL,
         OS_DADOS_VEICULOS.COR_EXTERNA,
         OS_DADOS_VEICULOS.PLACA,
         OS_DADOS_VEICULOS.KM,
         OS_DADOS_VEICULOS.CHASSI,
         OS_DADOS_VEICULOS.NUMERO_MOTOR,
         OS_DADOS_VEICULOS.NUMERO_RENAVAM,
         OS_DADOS_VEICULOS.SERIE,
         OS_DADOS_VEICULOS.COD_CONCESSIONARIA,
         OS_DADOS_VEICULOS.ESTADO_PINTURA,
         OS_DADOS_VEICULOS.JOGO_FERRAMENTAS,
         OS_DADOS_VEICULOS.ELASTICOS,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_D,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_E,
         OS_DADOS_VEICULOS.FLANELA,
         OS.TIPO,
         OS_TIPOS.DESCRICAO AS TIPO_DESCRICAO,
         OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO AS TIPO_COM_DESCRICAO,
         OS_TIPOS.GARANTIA,
         OS_TIPOS.REVISAO_GRATUITA,
         OS_TIPOS.INTERNO,
         OS_TIPOS.COD_CLIENTE AS CLIENTE_DO_TIPO,
         OS_TIPOS.OUTRO_CONCESSIONARIA,
         OS.NOME AS CONSULTOR,
         EMPRESAS_USUARIOS.NOME_COMPLETO AS CONSULTOR_COMPLETO,
         PRODUTOS.DESCRICAO_PRODUTO,
		 PRODUTOS.COD_SEGMENTO,
         PRODUTOS_MODELOS.DESCRICAO_MODELO,
         (PRODUTOS.DESCRICAO_PRODUTO || ' - ' ||
         PRODUTOS_MODELOS.DESCRICAO_MODELO) DESC_PROD_MOD,
         PRODUTOS_MODELOS.LINHA,
         MARCAS.DESCRICAO_MARCA,
         CONCESSIONARIAS.NOME CONCESSIONARIA_NOME,
         CONCESSIONARIAS.UF CONCESSIONARIA_UF,
         CONCESSIONARIAS.CIDADE CONCESSIONARIA_CIDADE,
         CONCESSIONARIAS.BAIRRO CONCESSIONARIA_BAIRRO,
         CONCESSIONARIAS.ENDERECO CONCESSIONARIA_RUA,
         CONCESSIONARIAS.CEP CONCESSIONARIA_CEP,
         CONCESSIONARIAS.CODIGO_PADRAO CONCESSIONARIA_CODIGO,
         UF_CONCESSIONARIA.DESCRICAO CONCESSIONARIA_ESTADO,
         
         (CASE
           WHEN NVL(OS.OS_ORIGEM_RETORNO, 0) > 0 THEN
            'S'
           ELSE
            'N'
         END) AS AG_RETORNO,
         NVL(OS_AGENDA.CLIENTE_AGUARDA, 'N') AS AG_CLIENTE_AGUARDA,
         NVL(OS_AGENDA.VEICULO_PLATAFORMA, 'N') AS AG_VEICULO_PLATAFORMA,
         NVL(OS_AGENDA.TAXI, 'N') AS AG_TAXI,
         NVL(OS_AGENDA.BLINDADO, 'N') AS AG_BLINDADO,
         NVL(OS_AGENDA.TESTE_RODAGEM, 'N') AS AG_TESTE_RODAGEM,
         NVL(OS_AGENDA.LEVAR_PECAS_SUBSTITUIDAS, 'N') AS AG_LEVAR_PECAS_SUBSTITUIDAS,
         NVL(OS_AGENDA.LAVAR_VEICULO, 'N') AS AG_LAVAR_VEICULO,
         NVL(OS_AGENDA.VEICULO_MODIFICADO, 'N') AS VEICULO_MODIFICADO,
         NVL(OS_AGENDA.DDW_GARANTIA, 'NÃO EXISTENTE') AS DDW_GARANTIA,
         OS.HORA_PROMETIDA_REVISADA,
         OS_TIPOS.TIPO_FABRICA,
         DECODE(OS_AGENDA.REC_INTERATIVA,
                'S',
                'VEÍCULO COM RECEPÇÃO INTERATIVA',
                'N',
                '') REC_INTERATIVA,
         OS_AGENDA.COD_TIPO_IMOBILIZADO,
         DECODE(OS_AGENDA.COD_TIPO_IMOBILIZADO,
                NULL,
                '',
                'VEICULO IMOBILIZADO: ' || TIPO_IMOBILIZADO.DESCRICAO) AS IMOBILIZADO,
         OS_AGENDA.REC_INTERATIVA AS RECEPCAO_INTERATIVA,
         DECODE(OS_AGENDA.COD_TIPO_MOBILIDADE,
                NULL,
                'SEM MOBILIDADE',
                TIPO_MOBILIDADE.DESCRICAO) MOBILIDADE_DESCRICAO,
         NVL((SELECT 'S'
               FROM JLR_CHASSI_BLINDADO J
              WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
                AND ROWNUM < 2),
             'N') AS BLINDADO,
         (SELECT J.NOME_BLINDADORA
            FROM JLR_CHASSI_BLINDADO J
           WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
             AND ROWNUM < 2) AS BLINDADORA
  
    FROM OS,
         OS_DADOS_VEICULOS,
         OS_AGENDA,
         EMPRESAS_USUARIOS,
         VW_OS_TIPOS       OS_TIPOS,
         CONCESSIONARIAS,
         PRODUTOS,
         PRODUTOS_MODELOS,
         MARCAS,
         UF                UF_CONCESSIONARIA,
         TIPO_IMOBILIZADO,
         TIPO_MOBILIDADE
   WHERE OS.TIPO = OS_TIPOS.TIPO
     AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA(+)
     AND OS.NOME = EMPRESAS_USUARIOS.NOME
     AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS(+)
     AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA(+)
     AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+)
     AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO
        
     AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA(+)
     AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS(+)
        
     AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
     AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
     AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
     AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF(+)
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_TIPO_IMOBILIZADO =
         TIPO_IMOBILIZADO.COD_TIPO_IMOBILIZADO(+)
     AND OS_AGENDA.COD_TIPO_MOBILIDADE = TIPO_MOBILIDADE.COD_TIPO_MOBIL(+)),
Q_EMPRESA AS
 (SELECT EMPRESAS.COD_EMPRESA,
         EMPRESAS.NOME NOME_EMPRESA,
         EMPRESAS.CGC,
         EMPRESAS.FACHADA,
         EMPRESAS.ESTADO AS UF,
         (TRIM(EMPRESAS.CIDADE) || ' - ' || TRIM(EMPRESAS.ESTADO)) AS CIDADE,
         EMPRESAS.BAIRRO,
         EMPRESAS.COMPLEMENTO,
         (EMPRESAS.RUA || ', ' || EMPRESAS.FACHADA) AS RUA,
         EMPRESAS.FONE,
         EMPRESAS.FAX,
         EMPRESAS.CEP,
         EMPRESAS.INSCRICAO_MUNICIPAL,
         EMPRESAS.INSCRICAO_SUBSTITUICAO,
         UF.DESCRICAO ESTADO,
         EMPRESAS.INSCRICAO_ESTADUAL,
         TRUNC(SYSDATE) AS DATA_ATUAL,
         SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'), 1, 5) AS HORA_ATUAL_STR,
         CLIENTES.ENDERECO_ELETRONICO AS EMAIL,
         EMPRESA_LOGO.LOGO
    FROM EMPRESAS, EMPRESA_LOGO, UF, CLIENTES
   WHERE EMPRESAS.COD_EMPRESA = $P{COD_EMPRESA}
     AND EMPRESAS.COD_EMPRESA = EMPRESA_LOGO.COD_EMPRESA(+)
     AND EMPRESAS.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND UF.UF = EMPRESAS.ESTADO),
Q_CLIENTE AS
 (SELECT CLIENTE_DIVERSO.COD_CLIENTE,
         
         CLIENTE_DIVERSO.NOME AS NOME,
         CLIENTE_DIVERSO.RG   AS RG,
         
         ('(' || CLIENTES.PREFIXO_RES || ') ' || CLIENTES.TELEFONE_RES) AS TELEFONE_RES,
         CLIENTES.PREFIXO_RES,
         ('(' || CLIENTES.PREFIXO_COM || ') ' || CLIENTES.TELEFONE_COM) AS TELEFONE_COM,
         CLIENTES.PREFIXO_COM,
         ('(' || CLIENTES.PREFIXO_FAX || ') ' || CLIENTES.TELEFONE_FAX) AS TELEFONE_FAX,
         CLIENTES.PREFIXO_FAX,
         ('(' || CLIENTES.PREFIXO_CEL || ') ' || CLIENTES.TELEFONE_CEL) AS TELEFONE_CEL,
         CLIENTES.PREFIXO_CEL,
         
         NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL) AS INSC_ESTAD,
         
         CLIENTE_DIVERSO.CGC,
         CLIENTE_DIVERSO.CPF,
         CLIENTES.COD_CLASSE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.UF,
                2,
                CLIENTES.UF_RES,
                3,
                CLIENTES.UF_COM,
                4,
                CLIENTES.UF_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.UF,
                NULL) UF,
         DECODE(OS.TIPO_ENDERECO,
                1,
                UF_DIVERSO.DESCRICAO,
                2,
                UF_RES.DESCRICAO,
                3,
                UF_COM.DESCRICAO,
                4,
                UF_COBRANCA.DESCRICAO,
                5,
                UF_INSCRICAO.DESCRICAO,
                NULL) ESTADO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CIDADES_DIV.DESCRICAO,
                2,
                CIDADES_RES.DESCRICAO,
                3,
                CIDADES_COM.DESCRICAO,
                4,
                CIDADES_COBRANCA.DESCRICAO,
                5,
                ENDERECO_POR_INSCRICAO.CIDADE,
                NULL) CIDADE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.BAIRRO,
                2,
                CLIENTES.BAIRRO_RES,
                3,
                CLIENTES.BAIRRO_COM,
                4,
                CLIENTES.BAIRRO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.BAIRRO,
                NULL) BAIRRO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                TRANSLATE(TO_CHAR(CLIENTE_DIVERSO.CEP / 1000, '00000.000'),
                          ',.',
                          '.-'),
                2,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_RES / 1000, '00000.000'),
                          ',.',
                          '.-'),
                3,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_COM / 1000, '00000.000'),
                          ',.',
                          '.-'),
                4,
                TRANSLATE(TO_CHAR(CLIENTES.CEP_COBRANCA / 1000, '00000.000'),
                          ',.',
                          '.-'),
                5,
                TRANSLATE(TO_CHAR(ENDERECO_POR_INSCRICAO.CEP / 1000,
                                  '00000.000'),
                          ',.',
                          '.-'),
                NULL) CEP,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.ENDERECO,
                2,
                CLIENTES.RUA_RES,
                3,
                CLIENTES.RUA_COM,
                4,
                CLIENTES.RUA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.RUA,
                NULL) RUA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.COMPLEMENTO,
                2,
                CLIENTES.COMPLEMENTO_RES,
                3,
                CLIENTES.COMPLEMENTO_COM,
                4,
                CLIENTES.COMPLEMENTO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.COMPLEMENTO,
                NULL) COMPLEMENTO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                NULL,
                2,
                CLIENTES.FACHADA_RES,
                3,
                CLIENTES.FACHADA_COM,
                4,
                CLIENTES.FACHADA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.FACHADA,
                NULL) FACHADA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.FONE_CONTATO,
                2,
                CLIENTES.TELEFONE_RES,
                3,
                CLIENTES.TELEFONE_COM,
                4,
                CLIENTES.TELEFONE_CEL,
                5,
                ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
                NULL) FONE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
                2,
                CLIENTES.PREFIXO_RES,
                3,
                CLIENTES.PREFIXO_COM,
                4,
                CLIENTES.PREFIXO_CEL,
                5,
                ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
                NULL) PREFIXO,
         
         CLIENTES.ENDERECO_ELETRONICO,
         CLIENTES.EMAIL_NFE,
         NVL(CLIENTES.EMAIL_TRABALHO, CLIENTES.EMAIL2) AS EMAIL2
  
    FROM OS,
         CLIENTE_DIVERSO,
         CLIENTES,
         ENDERECO_POR_INSCRICAO,
         CIDADES                CIDADES_RES,
         CIDADES                CIDADES_COM,
         CIDADES                CIDADES_COBRANCA,
         CIDADES                CIDADES_DIV,
         UF                     UF_DIVERSO,
         UF                     UF_RES,
         UF                     UF_COM,
         UF                     UF_COBRANCA,
         UF                     UF_INSCRICAO
   WHERE OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE
     AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES(+)
     AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES(+)
     AND OS.INSCRICAO_ESTADUAL =
         ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL(+)
     AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF(+)
     AND CLIENTES.UF_RES = UF_RES.UF(+)
     AND CLIENTES.UF_COM = UF_COM.UF(+)
     AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF(+)
     AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF(+)
        
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}),
Q_HIST_ORC AS
 (SELECT ROW_NUMBER() OVER(ORDER BY ORC.NUMERO_OS) AS NUMERO_LINHA,
         ORC.NUMERO_OS,
         ORC.COD_EMPRESA,
         COUNT(ORC.NUMERO_OS) NUMERO_ORCAMENTOS,
         LISTAGG(ORC.NUMERO_ORCAMENTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS ORCAMENTOS,
         LISTAGG(ORCF.VALOR_BRUTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS VALOR_ORCAMENTO_BRUTO,
         SUM(ORCF.VALOR_BRUTO) TOTAL
    FROM OS_ORCAMENTOS ORC
    LEFT JOIN OS_ORC_FECHAMENTO ORCF
      ON ORCF.NUMERO_OS = ORC.NUMERO_ORCAMENTO
     AND ORCF.COD_EMPRESA = ORC.COD_EMPRESA
   WHERE ORC.NUMERO_OS = $P{NUMERO_OS}
     AND ORC.COD_EMPRESA = $P{COD_EMPRESA}
   GROUP BY ORC.NUMERO_OS, ORC.COD_EMPRESA),

Q_HISTORICO AS
 (SELECT LISTAGG(HISTORICO_OS.N_OS, ', ') WITHIN GROUP(ORDER BY HISTORICO_OS.N_OS) AS HISTORICO_OS,
         HISTORICO_OS.CHASSI,
         HISTORICO_OS.COD_EMPRESA
    FROM (SELECT DISTINCT TO_CHAR(OSD.NUMERO_OS) AS N_OS,
                          OSD.CHASSI,
                          OSD.COD_EMPRESA
            FROM OS_DADOS_VEICULOS OSD, OS, Q_OS
           WHERE OSD.COD_EMPRESA = Q_OS.COD_EMPRESA
             AND OS.NUMERO_OS = OSD.NUMERO_OS
             AND OS.STATUS_OS = 1
             AND OS.NUMERO_OS > 0
             AND OSD.CHASSI = Q_OS.CHASSI
                
             AND ROWNUM < 11
           ORDER BY OSD.NUMERO_OS DESC) HISTORICO_OS
   GROUP BY HISTORICO_OS.CHASSI, HISTORICO_OS.COD_EMPRESA),

QRYDADOSVEICULOS AS
 (SELECT CF.ANO, CF.DATA_COMPRA, CF.CHASSI, CF.PLACA
    FROM CLIENTES_FROTA CF, Q_OS
   WHERE CF.VENDIDO = 'N'
     AND ROWNUM <= 1
     AND ((Q_OS.CHASSI IS NOT NULL AND CF.CHASSI = Q_OS.CHASSI) OR
         (Q_OS.PLACA IS NOT NULL AND CF.PLACA = Q_OS.PLACA))
   ORDER BY CF.DATA_COMPRA DESC),

Q_PARM_SYS as
 (SELECT PARM_SYS.COD_EMPRESA,
         TIPO_TEMPO,
         TIPO_VALOR_OS,
         cod_cliente_balcao,
         tipo_concessionaria,
         CONCESSIONARIAS.codigo_padrao,
         versao2,
         cod_cliente_fabrica_garantia,
         PARM_SYS2.HONDA_IMP_NUMERO_REQUISICAO,
         PARM_SYS3.TERMO_OS_JLR
    FROM PARM_SYS, PARM_SYS2, PARM_SYS3, CONCESSIONARIAS
   WHERE (PARM_SYS.CONCESSIONARIA_NUMERO =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+))
     AND (PARM_SYS2.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS3.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS.COD_EMPRESA = $P{COD_EMPRESA})),

Q_ASSINATURA as
 (select OS_AGENDA.NUMERO_OS,
         OS_AGENDA.COD_EMPRESA,
         MOB_OS_ASSINATURA_RECEPCAO.ASSINATURA_CLIENTE AS CLIENTE_RECEPCAO,
		 MOB_OS_ASSINATURA_RECEPCAO.DATA_ASSINATURA_CLIENTE AS CLIENTE_RECEPCAO_DT,
         MOB_OS_ASSINATURA_ENTREGA.ASSINATURA_CLIENTE AS CLIENTE_ENTREGA,
         MOB_OS_ASSINATURA_ENTREGA.DATA_ASSINATURA_CLIENTE AS CLIENTE_ENTREGA_DT,
         MOB_OS_ASSINATURA_RECEPCAO.ASSINATURA AS CONSULTOR_RECEPCAO,
         MOB_OS_ASSINATURA_RECEPCAO.DATA_ASSINATURA AS CONSULTOR_RECEPCAO_DT,
         MOB_OS_ASSINATURA_ENTREGA.ASSINATURA AS CONSULTOR_ENTREGA,
         MOB_OS_ASSINATURA_ENTREGA.DATA_ASSINATURA AS CONSULTOR_ENTREGA_DT,
		 MOB_OS_ASSINATURA_OFICINA.ASSINATURA_CLIENTE AS CLIENTE_OFICINA,
		 MOB_OS_ASSINATURA_OFICINA.DATA_ASSINATURA_CLIENTE AS CLIENTE_OFICINA_DT,
		 MOB_OS_ASSINATURA_OFICINA.ASSINATURA AS CONSULTOR_OFICINA,
		 MOB_OS_ASSINATURA_OFICINA.DATA_ASSINATURA AS CONSULTOR_OFICINA_DT
    from OS_AGENDA, MOB_OS_ASSINATURA MOB_OS_ASSINATURA_ENTREGA, MOB_OS_ASSINATURA MOB_OS_ASSINATURA_RECEPCAO, MOB_OS_ASSINATURA MOB_OS_ASSINATURA_OFICINA
   where OS_AGENDA.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_EMPRESA = $P{COD_EMPRESA}
   AND OS_AGENDA.NUMERO_OS = MOB_OS_ASSINATURA_ENTREGA.numero_os (+)
   AND OS_AGENDA.COD_EMPRESA = MOB_OS_ASSINATURA_ENTREGA.cod_empresa (+)
   AND MOB_OS_ASSINATURA_ENTREGA.APLICACAO(+) = 'E' 
   AND OS_AGENDA.NUMERO_OS = MOB_OS_ASSINATURA_RECEPCAO.numero_os (+)
   AND OS_AGENDA.COD_EMPRESA = MOB_OS_ASSINATURA_RECEPCAO.cod_empresa (+)
   AND MOB_OS_ASSINATURA_RECEPCAO.APLICACAO(+) = 'R'
   
   AND OS_AGENDA.NUMERO_OS = MOB_OS_ASSINATURA_OFICINA.numero_os (+)
   AND OS_AGENDA.COD_EMPRESA = MOB_OS_ASSINATURA_OFICINA.cod_empresa (+)
   AND MOB_OS_ASSINATURA_OFICINA.APLICACAO(+) = 'O')

SELECT Q_EMPRESA.NOME_EMPRESA           AS Q_EMPRESA_NOME_EMPRESA,
       Q_EMPRESA.RUA                    AS Q_EMPRESA_RUA,
       Q_EMPRESA.CIDADE                 AS Q_EMPRESA_CIDADE,
       Q_EMPRESA.CEP                    AS Q_EMPRESA_CEP,
       Q_EMPRESA.FONE                   AS Q_EMPRESA_FONE,
       Q_EMPRESA.INSCRICAO_ESTADUAL     AS Q_EMPRESA_INSCRICAO_ESTADUAL,
	   Q_EMPRESA.LOGO					AS Q_EMPRESA_LOGO,
       Q_EMPRESA.CGC                    AS Q_EMPRESA_CGC,
	   Q_EMPRESA.EMAIL					AS Q_EMPRESA_EMAIL,
	   Q_EMPRESA.FAX					AS Q_EMPRESA_FAX,
	   Q_EMPRESA.DATA_ATUAL				AS Q_EMPRESA_DATA_ATUAL,
       Q_OS.TIPO                        AS Q_OS_TIPO,
       Q_OS.D_AG_STG                    AS Q_OS_D_AG_STG,
	   Q_OS.DATA_AGENDADA				AS Q_OS_DATA_AGENDADA,
	   Q_OS.DATA_ENTREGA				AS Q_OS_DATA_ENTREGA,
       Q_OS.CONSULTOR                   AS Q_OS_CONSULTOR,
	   Q_OS.CONSULTOR_COMPLETO			AS Q_OS_CONSULTOR_COMPLETO,
       Q_OS.PRISMA                      AS Q_OS_PRISMA,
       Q_OS.DATA_PROMETIDA              AS Q_OS_DATA_PROMETIDA,
       Q_OS.DATA_PROMETIDA_REVISADA     AS Q_OS_DATA_PROMETIDA_REVISADA,
       Q_OS.AG_RETORNO                  AS Q_OS_AG_RETORNO,
       Q_OS.AG_CLIENTE_AGUARDA          AS Q_OS_AG_CLIENTE_AGUARDA,
       Q_OS.RECEPCAO_INTERATIVA         AS Q_OS_RECEPCAO_INTERATIVA,
       Q_OS.AG_LAVAR_VEICULO            AS Q_OS_AG_LAVAR_VEICULO,
       Q_OS.AG_BLINDADO                 AS Q_OS_AG_BLINDADO,
       Q_OS.AG_TESTE_RODAGEM            AS Q_OS_AG_TESTE_RODAGEM,
       Q_OS.AG_LEVAR_PECAS_SUBSTITUIDAS AS Q_OS_AG_LEVAR_PECAS_SUBSTITUID,
       Q_OS.VEICULO_MODIFICADO          AS Q_OS_VEICULO_MODIFICADO,
       Q_OS.NUMERO_OS                   AS Q_OS_NUMERO_OS,
       Q_OS.HORA_PROMETIDA              AS Q_OS_HORA_PROMETIDA,
       Q_OS.PLACA                       AS Q_OS_PLACA,
       Q_OS.COMBUSTIVEL					AS Q_OS_COMBUSTIVEL,
       Q_OS.HORA_PROMETIDA_REVISADA     AS Q_OS_HORA_PROMETIDA_REVISADA,
       Q_OS.MOBILIDADE_DESCRICAO        AS Q_OS_MOBILIDADE_DESCRICAO,
       Q_CLIENTE.TELEFONE_CEL           AS Q_CLIENTE_TELEFONE_CEL,
       Q_CLIENTE.TELEFONE_COM           AS Q_CLIENTE_TELEFONE_COM,
       Q_CLIENTE.TELEFONE_RES           AS Q_CLIENTE_TELEFONE_RES,
       Q_CLIENTE.EMAIL_NFE              AS Q_CLIENTE_EMAIL_NFE,
       Q_CLIENTE.EMAIL2                 AS Q_CLIENTE_EMAIL2,
       Q_CLIENTE.ENDERECO_ELETRONICO    AS Q_CLIENTE_ENDERECO_ELETRONICO,
       Q_CLIENTE.RUA                    AS Q_CLIENTE_RUA,
       Q_CLIENTE.BAIRRO                 AS Q_CLIENTE_BAIRRO,
       Q_CLIENTE.NOME                   AS Q_CLIENTE_NOME,
       Q_CLIENTE.CIDADE                 AS Q_CLIENTE_CIDADE,
       Q_CLIENTE.CEP                    AS Q_CLIENTE_CEP,
       Q_CLIENTE.CPF                    AS Q_CLIENTE_CPF,
       Q_CLIENTE.RG                     AS Q_CLIENTE_RG,
       Q_OS.DESC_PROD_MOD               AS Q_OS_DESC_PROD_MOD,
       Q_OS.KM                          AS Q_OS_KM,
       Q_OS.CHASSI                      AS Q_OS_CHASSI,
       Q_OS.COR_EXTERNA                 AS Q_OS_COR_EXTERNA,
       Q_OS.ANO                         AS Q_OS_ANO,
       Q_OS.NUMERO_MOTOR                AS Q_OS_NUMERO_MOTOR,
       Q_OS.DATA_VENDA                  AS Q_OS_DATA_VENDA,
       Q_OS.CONCESSIONARIA_NOME         AS Q_OS_CONCESSIONARIA_NOME,
       Q_OS.TOTAL_OS                    AS Q_OS_TOTAL_OS,
       Q_OS.BLINDADORA                  AS Q_OS_BLINDADORA,
       Q_OS.BLINDADO                    AS Q_OS_BLINDADO,
       Q_OS.DDW_GARANTIA                AS Q_OS_DDW_GARANTIA,
       Q_OS.REC_INTERATIVA              AS Q_OS_REC_INTERATIVA,
       Q_OS.IMOBILIZADO                 AS Q_OS_IMOBILIZADO,
       Q_OS.DH_EMISSAO                  AS Q_OS_DH_EMISSAO,
       Q_OS.DATA_ENCERRADA              AS Q_OS_DATA_ENCERRADO,
	   Q_OS.HORA_ENCERRADA              AS Q_OS_HORA_ENCERRADO,
       Q_OS.DH_LIBERADO                 AS Q_OS_DH_LIBERADO,
       Q_OS.TIPO_FABRICA                AS Q_OS_TIPO_FABRICA,
       Q_OS.OBSERVACAO                  AS Q_OS_OBSERVACAO,
       Q_HIST_ORC.ORCAMENTOS            AS Q_HIST_ORC_LISTA_ORCAMENTOS,
       Q_HIST_ORC.TOTAL                 AS Q_HIST_ORC_TOTAL_ORCAMENTOS,
       Q_HISTORICO.HISTORICO_OS         AS Q_HIST_ORC_HISTORICO_OS,
       Q_PARM_SYS.TERMO_OS_JLR          AS Q_PARM_SYS_TERMO_OS_JLR,
       Q_ASSINATURA.CLIENTE_RECEPCAO 	AS Q_ASSINATURA_CLIENTE_RECEPCAO,
       Q_OS.DATA_EMISSAO					 AS Q_OS_DATA_EMISSAO,
	   Q_OS.HORA_EMISSAO 					 AS Q_OS_HORA_EMISSAO,
       Q_ASSINATURA.CLIENTE_ENTREGA 		 AS Q_ASSINATURA_CLIENTE_ENTREGA,
       Q_ASSINATURA.CLIENTE_ENTREGA_DT 		 AS Q_ASSINATURA_CLIE_ENTREGA_DT,
       Q_ASSINATURA.CONSULTOR_RECEPCAO 		 AS Q_ASSINATURA_CONSULTOR_RECEPC,
       Q_ASSINATURA.CONSULTOR_RECEPCAO_DT    AS Q_ASSINATURA_CONS_RECEPCAO_DT,
       Q_ASSINATURA.CONSULTOR_ENTREGA 	  	 AS Q_ASSINATURA_CONSULTOR_ENTREGA,
       Q_ASSINATURA.CONSULTOR_ENTREGA_DT     AS Q_ASSINATURA_CONUL_ENTREGA_DT,
	   Q_ASSINATURA.CLIENTE_OFICINA				AS Q_ASSINATURA_CLIENTE_OFICINA,
	   Q_ASSINATURA.CLIENTE_OFICINA_DT			AS Q_ASSINATURA_CLIE_OFICINA_DT,
	   Q_OS.TOTAL_SERVICOS_LIQUIDO		 AS Q_OS_TOTAL_SERVICOS_LIQUIDO,
	   Q_OS.TOTAL_ITENS_LIQUIDO				 AS Q_OS_TOTAL_ITENS_LIQUIDO,
	   Q_OS.VALOR_SERVICOS_BRUTO		AS Q_OS_VALOR_SERVICOS_BRUTO,
       Q_OS.VALOR_ITENS_BRUTO			AS Q_OS_VALOR_ITENS_BRUTO,
	   Q_OS.DESCONTO_SERVICO_PORCENT	AS Q_OS_DESCONTO_SERVICO_PORCENT,
	   Q_OS.DESCONTO_ITENS_PORCENT		AS Q_OS_DESCONTO_ITENS_PORCENT,
	   Q_OS.COD_SEGMENTO 				AS Q_OS_COD_SEGMENTO
  FROM Q_OS,
       Q_EMPRESA,
       Q_CLIENTE,
       Q_HIST_ORC,
       Q_HISTORICO,
       QRYDADOSVEICULOS,
       Q_ASSINATURA,
       Q_PARM_SYS
 WHERE Q_OS.COD_CLIENTE = Q_CLIENTE.COD_CLIENTE(+)
   AND Q_OS.COD_EMPRESA = Q_EMPRESA.COD_EMPRESA(+)
   AND Q_OS.COD_EMPRESA = Q_HIST_ORC.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_HIST_ORC.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_HISTORICO.COD_EMPRESA(+)
   AND Q_OS.CHASSI = Q_HISTORICO.CHASSI(+)
   AND Q_OS.CHASSI = QRYDADOSVEICULOS.CHASSI(+)
   AND Q_OS.PLACA = QRYDADOSVEICULOS.PLACA(+)
   AND Q_OS.COD_EMPRESA = Q_PARM_SYS.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_ASSINATURA.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_ASSINATURA.COD_EMPRESA(+)]]>
	</queryString>
	<field name="Q_EMPRESA_NOME_EMPRESA" class="java.lang.String"/>
	<field name="Q_EMPRESA_RUA" class="java.lang.String"/>
	<field name="Q_EMPRESA_CIDADE" class="java.lang.String"/>
	<field name="Q_EMPRESA_CEP" class="java.lang.String"/>
	<field name="Q_EMPRESA_FONE" class="java.lang.String"/>
	<field name="Q_EMPRESA_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="Q_EMPRESA_LOGO" class="java.lang.Object"/>
	<field name="Q_EMPRESA_CGC" class="java.lang.String"/>
	<field name="Q_EMPRESA_EMAIL" class="java.lang.String"/>
	<field name="Q_EMPRESA_FAX" class="java.lang.String"/>
	<field name="Q_EMPRESA_DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="Q_OS_TIPO" class="java.lang.String"/>
	<field name="Q_OS_D_AG_STG" class="java.lang.String"/>
	<field name="Q_OS_DATA_AGENDADA" class="java.sql.Timestamp"/>
	<field name="Q_OS_DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CONSULTOR" class="java.lang.String"/>
	<field name="Q_OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="Q_OS_PRISMA" class="java.lang.String"/>
	<field name="Q_OS_DATA_PROMETIDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_DATA_PROMETIDA_REVISADA" class="java.sql.Timestamp"/>
	<field name="Q_OS_AG_RETORNO" class="java.lang.String"/>
	<field name="Q_OS_AG_CLIENTE_AGUARDA" class="java.lang.String"/>
	<field name="Q_OS_RECEPCAO_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_AG_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="Q_OS_AG_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_AG_TESTE_RODAGEM" class="java.lang.String"/>
	<field name="Q_OS_AG_LEVAR_PECAS_SUBSTITUID" class="java.lang.String"/>
	<field name="Q_OS_VEICULO_MODIFICADO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="Q_OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="Q_OS_PLACA" class="java.lang.String"/>
	<field name="Q_OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="Q_OS_HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="Q_OS_MOBILIDADE_DESCRICAO" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_CEL" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_COM" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_RES" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL_NFE" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL2" class="java.lang.String"/>
	<field name="Q_CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="Q_CLIENTE_RUA" class="java.lang.String"/>
	<field name="Q_CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="Q_CLIENTE_NOME" class="java.lang.String"/>
	<field name="Q_CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="Q_CLIENTE_CEP" class="java.lang.String"/>
	<field name="Q_CLIENTE_CPF" class="java.lang.String"/>
	<field name="Q_CLIENTE_RG" class="java.lang.String"/>
	<field name="Q_OS_DESC_PROD_MOD" class="java.lang.String"/>
	<field name="Q_OS_KM" class="java.lang.Double"/>
	<field name="Q_OS_CHASSI" class="java.lang.String"/>
	<field name="Q_OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="Q_OS_ANO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="Q_OS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="Q_OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="Q_OS_BLINDADORA" class="java.lang.String"/>
	<field name="Q_OS_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_DDW_GARANTIA" class="java.lang.String"/>
	<field name="Q_OS_REC_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_IMOBILIZADO" class="java.lang.String"/>
	<field name="Q_OS_DH_EMISSAO" class="java.lang.String"/>
	<field name="Q_OS_DATA_ENCERRADO" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_ENCERRADO" class="java.lang.String"/>
	<field name="Q_OS_DH_LIBERADO" class="java.lang.String"/>
	<field name="Q_OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="Q_OS_OBSERVACAO" class="java.lang.String"/>
	<field name="Q_HIST_ORC_LISTA_ORCAMENTOS" class="java.lang.String"/>
	<field name="Q_HIST_ORC_TOTAL_ORCAMENTOS" class="java.lang.Double"/>
	<field name="Q_HIST_ORC_HISTORICO_OS" class="java.lang.String"/>
	<field name="Q_PARM_SYS_TERMO_OS_JLR" class="java.lang.String"/>
	<field name="Q_ASSINATURA_CLIENTE_RECEPCAO" class="java.awt.Image"/>
	<field name="Q_OS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="Q_ASSINATURA_CLIENTE_ENTREGA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CLIE_ENTREGA_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CONSULTOR_RECEPC" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CONS_RECEPCAO_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CONSULTOR_ENTREGA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CONUL_ENTREGA_DT" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CLIENTE_OFICINA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CLIE_OFICINA_DT" class="java.sql.Timestamp"/>
	<field name="Q_OS_TOTAL_SERVICOS_LIQUIDO" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_ITENS_LIQUIDO" class="java.lang.Double"/>
	<field name="Q_OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_DESCONTO_SERVICO_PORCENT" class="java.lang.Double"/>
	<field name="Q_OS_DESCONTO_ITENS_PORCENT" class="java.lang.Double"/>
	<field name="Q_OS_COD_SEGMENTO" class="java.lang.Double"/>
	<pageHeader>
		<band height="91">
			<frame>
				<reportElement x="0" y="1" width="575" height="46" uuid="c50ea998-64c7-44dc-9b8a-3322226420c2"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="23" y="1" width="49" height="43" uuid="5883a369-6b92-4e9d-9ce7-897892b326a9">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice386023.png"]]></imageExpression>
				</image>
				<textField>
					<reportElement x="260" y="17" width="310" height="19" forecolor="#FF0000" uuid="fe07170a-c0f1-4dbd-9b1b-725fbc2e66e5">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="47" width="575" height="44" uuid="be985de0-fdc9-4dfd-9331-7bdbc1922040"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textField>
					<reportElement x="18" y="9" width="212" height="9" uuid="15c54859-2b52-475f-b57a-5687559edba2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="18" y="18" width="212" height="9" uuid="118cc50f-57b6-40a3-abd6-6ef761883305">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="33" y="27" width="197" height="9" uuid="1eadfadf-e59d-4c77-a436-65030813e7a0">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_CEP} + " - " + $F{Q_EMPRESA_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="264" y="9" width="86" height="9" uuid="dead333c-66f5-492a-bf4b-c5466c52d26c">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_FONE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="273" y="27" width="77" height="9" uuid="beadc481-c58d-4153-b241-9034931d56ec">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_EMAIL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="269" y="18" width="81" height="9" uuid="1b212d5c-deb9-467f-b28b-670bf90fc8be">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_CGC}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="18" y="27" width="15" height="9" uuid="c2b0265e-1112-4a54-ade9-96dd1c31b52c">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement x="249" y="9" width="15" height="9" uuid="0f83af7f-4f3a-4c2e-a498-d5fe30e0b6b5">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[TEL:]]></text>
				</staticText>
				<staticText>
					<reportElement x="249" y="18" width="20" height="9" uuid="939c18f4-747e-4965-9182-ea1835d20003">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<staticText>
					<reportElement x="249" y="27" width="24" height="9" uuid="c4161178-b7f9-47b5-9af2-caa6fdd15856">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[E-MAIL:]]></text>
				</staticText>
				<staticText>
					<reportElement x="396" y="9" width="15" height="9" uuid="dddcfe23-0238-4734-a3fe-73b01edd4a3a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[FAX:]]></text>
				</staticText>
				<staticText>
					<reportElement x="396" y="27" width="71" height="9" uuid="878e0e92-40f0-446e-a83f-3b0fb1b77ab3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[ORDEM DE SERVIÇO Nº]]></text>
				</staticText>
				<staticText>
					<reportElement x="396" y="18" width="44" height="9" uuid="eb9fc4c1-ce77-4e48-8339-b71fbb416094">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[INSC. EST. N°]]></text>
				</staticText>
				<textField>
					<reportElement x="440" y="18" width="90" height="9" uuid="58c9ef42-9878-4533-8e6e-862fc7ae14f8">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_INSCRICAO_ESTADUAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="411" y="9" width="119" height="9" uuid="e1b14f2d-b4b7-459b-8df1-a6c0d7f2b6d1">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_FAX}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="467" y="27" width="63" height="9" uuid="ef27a353-b783-4421-9dcc-bb5ee9fef3d4">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="202">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="575" height="86" uuid="9205d058-8eba-4383-bbdf-2eb50eb8146d">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport overflowType="NoStretch">
					<reportElement x="23" y="14" width="382" height="66" uuid="a693b0c4-9d01-4533-8e09-137b0185aae1"/>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"CheckListHondaMotoVinteUmItensSubServicos.jasper"]]></subreportExpression>
				</subreport>
				<staticText>
					<reportElement x="21" y="4" width="90" height="9" uuid="62c15606-f4ca-4703-a42a-6e9f0d309f7b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[SERVIÇO REQUISITADO:]]></text>
				</staticText>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="462" y="6" width="91" height="22" uuid="0f72bd34-48f3-4710-ac4f-3ce5b1f0e8a9">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO}+"crmservice386025.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="462" y="28" width="48" height="9" uuid="472b1bb4-75d9-4a65-8176-2fd284e89169">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[Nº OS Sistema]]></text>
				</staticText>
				<textField pattern="###0.###;(###0.###-)">
					<reportElement x="462" y="37" width="85" height="14" uuid="30090beb-9657-4c18-9cdf-ec54c38945b7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="462" y="53" width="48" height="9" uuid="ae13d9ee-9b25-49b0-a493-ec076776d03b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<text><![CDATA[Orçamento]]></text>
				</staticText>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement x="462" y="62" width="85" height="14" uuid="a5a993af-2aa2-4d76-b24e-2539ce095452">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box leftPadding="4">
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TOTAL_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="0" width="18" height="86" uuid="1c920202-f351-4534-94dc-81fc1d61daa3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="5">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="Left">
						<font size="4" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[ETAPA 1 - Serviço Expresso]]></text>
				</staticText>
				<staticText>
					<reportElement x="10" y="0" width="6" height="86" uuid="86eba615-cd07-41f8-8d02-a3eea2700e40">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left">
						<font size="4" isBold="false" isItalic="false"/>
					</textElement>
					<text><![CDATA[Solicitação de Serviços]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="86" width="575" height="116" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="5cb9f55d-206f-4fbf-9d80-4eb73de4bdbd"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement x="20" y="2" width="241" height="19" isPrintWhenDetailOverflows="true" uuid="eb0d823d-24ae-4fb0-8e49-3727b3304a3f">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="30" height="9" uuid="8d65bf29-a2da-4ac1-860d-65533ae555e2">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[NOME:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="223" height="9" uuid="52a4eb22-d1b0-4fa3-8c9e-4491a53c5dd3">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_CLIENTE_NOME}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="261" y="2" width="201" height="19" uuid="35b38d78-c67e-41d1-a3d7-943e85282502">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="30" height="9" uuid="ff855fc9-92cb-4e89-99a3-62ed2d3cf1c2">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[MODELO:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="193" height="9" uuid="2ed48ba1-41a1-4804-8fb1-2ed626fef5ac">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DESC_PROD_MOD}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="462" y="2" width="113" height="19" uuid="adf74890-a9c0-4e7d-9a53-894913dfd0de">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="45" height="9" uuid="70cc28c5-fc09-4020-8d90-61c17e594437">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[DATA:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="103" height="9" uuid="8b178906-4697-4842-86d8-2f7a0fc09f1d">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="20" y="21" width="190" height="19" uuid="62378718-eb03-41e7-9499-ef6cb6e93487">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="30" height="9" uuid="734651ef-2244-461e-95d2-d40f963da9dc">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[CHASSI:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="156" height="9" uuid="282d7136-1529-4710-a50a-413e424c6eb1">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CHASSI}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="210" y="21" width="172" height="19" uuid="c6f9d8f5-08c3-45a9-8ffb-5904f088f107"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="30" height="9" uuid="ff9644f9-2eeb-4eee-b5fa-ac137ac22bdf">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[PLACA:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="163" height="9" uuid="2995716a-14ec-42f1-bcb1-d424d48ffb59">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_PLACA}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="382" y="21" width="90" height="19" uuid="fa3653ce-ee7b-4b34-bd39-7e14010a68e7"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="45" height="9" uuid="d4061564-b712-491d-9bf2-fb8043e04472">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[KM:]]></text>
					</staticText>
					<textField pattern="#,##0.###;(#,##0.###-)">
						<reportElement x="3" y="9" width="83" height="9" uuid="b07ca135-253d-40be-907a-b01220960c22">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_KM}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="472" y="21" width="103" height="19" uuid="164000dd-dbba-44ae-af13-f1d3cf1c85f7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="31" height="18" uuid="efd7a7bb-eb48-475d-abb3-ea3e6774a5f8">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[HORA DE
ENTRADA:]]></text>
					</staticText>
					<textField>
						<reportElement x="34" y="9" width="46" height="9" uuid="f093ded3-deb1-4b27-8474-338a76841dcd">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="20" y="40" width="135" height="19" uuid="cd9eef49-dea0-428e-8ea2-6bd9d8978ae8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="18" y="2" width="54" height="9" uuid="612724f9-14e0-41cf-90e3-c0ad40bd31a4">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[NOVO CLIETE:]]></text>
					</staticText>
					<textField>
						<reportElement x="5" y="4" width="11" height="10" uuid="b0737619-4c99-49fe-84d0-cc7e4b0535e9">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.75"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[""]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="18" y="9" width="109" height="6" uuid="27953fc3-d63c-4f27-a1a0-ccbb0ac5cbd1">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="4"/>
						</textElement>
						<text><![CDATA[Favor completar demais campos para cadastramento]]></text>
					</staticText>
				</frame>
				<frame>
					<reportElement x="155" y="40" width="135" height="19" uuid="764f6340-cea6-4804-9715-eddb03abe1d2"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="30" height="9" uuid="a8a18031-8b9d-4e29-a79f-f16364fdecc7">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[TEL:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="124" height="9" uuid="06080490-c228-4be2-a2a5-341f98c4b755">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_CLIENTE_TELEFONE_RES}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="290" y="40" width="140" height="19" uuid="cf550b90-49ae-43ca-83ef-5863b2a77c82"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="30" height="9" uuid="c0aeb052-27fa-4949-bf92-fd07955cb3cb">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[CELULAR:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="133" height="9" uuid="e9005177-aaba-46e6-9527-9299d9cec6ee">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_CLIENTE_TELEFONE_CEL}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="430" y="40" width="145" height="19" uuid="7d427d7d-15ff-483e-bfcc-6a5189e121cb">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="35" height="18" uuid="cf951beb-2d0a-4dd7-a186-472ed6d1931d">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[HORA
SAÍDA:]]></text>
					</staticText>
					<frame>
						<reportElement x="42" y="0" width="52" height="19" uuid="d274276e-318f-4b2d-8aff-dd88e4e69f40">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.5"/>
						</box>
						<staticText>
							<reportElement x="3" y="0" width="45" height="7" uuid="b57b291c-5913-44de-8734-4997f8c11de4">
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							</reportElement>
							<textElement textAlignment="Left" verticalAlignment="Middle">
								<font size="5"/>
							</textElement>
							<text><![CDATA[Previsão]]></text>
						</staticText>
						<textField>
							<reportElement x="3" y="9" width="45" height="9" uuid="4318d108-11a1-4265-8d0c-f04c68070200">
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							</reportElement>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="6"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{Q_OS_HORA_PROMETIDA}]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement x="94" y="0" width="51" height="19" uuid="592ca734-89a9-46bb-9291-ef41a49cbb26"/>
						<box>
							<pen lineWidth="0.5"/>
						</box>
						<staticText>
							<reportElement x="3" y="0" width="45" height="7" uuid="c5431bf9-f199-402f-8b3b-69866b080bca">
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							</reportElement>
							<textElement textAlignment="Left" verticalAlignment="Middle">
								<font size="5"/>
							</textElement>
							<text><![CDATA[REAL]]></text>
						</staticText>
						<textField pattern="HH:mm">
							<reportElement x="3" y="9" width="45" height="9" uuid="e194692c-add4-4174-afa1-7cb3e7f44005">
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							</reportElement>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="6"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{Q_OS_DATA_ENTREGA}]]></textFieldExpression>
						</textField>
					</frame>
				</frame>
				<frame>
					<reportElement x="20" y="59" width="241" height="19" uuid="df81d13b-a074-40c4-a8f9-6787b3be0528"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="37" height="9" uuid="3925058d-c120-4442-8057-96bbe4e3bc91">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[ENDEREÇO:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="223" height="9" uuid="2c2ef652-85ff-47eb-9b1e-c95fd2ad4894">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_CLIENTE_RUA}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="155" y="78" width="135" height="19" uuid="4f6d0225-75e5-4725-93f0-4f9a2524d43d"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="30" height="9" uuid="67f44a56-ded7-49ad-b13b-d701fecc9393">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[CPF:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="124" height="9" uuid="2b4d5329-864f-494c-9e88-28460d982a49">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_CLIENTE_CPF}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="20" y="78" width="135" height="19" uuid="793aed91-8c06-45a3-bb84-63ee1fec282b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="37" height="9" uuid="9703ae16-b6c7-4099-a753-2e591c2ec1b2">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[RG:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="124" height="9" uuid="4c5de279-3d51-4f60-8f9f-178f457ab44d">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_CLIENTE_RG}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="20" y="97" width="270" height="19" uuid="55783b5d-7514-4fca-ae8a-cab32bff35d8">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="37" height="9" uuid="43598876-41e6-4efb-a1c2-1bd8da3d5c87">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[E-MAIL:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="254" height="9" uuid="48a275c8-c92d-4cf1-9848-209f73116b9b">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_CLIENTE_ENDERECO_ELETRONICO}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="290" y="78" width="285" height="38" uuid="2c85e545-8646-4977-bc9c-273060a9595a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="22" y="28" width="239" height="9" uuid="5ab5f549-f5e5-49bf-baa0-14e8cd107b22">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="5"/>
						</textElement>
						<text><![CDATA[ASSINATURA AUTORIZANDO O SERVIÇO EXPRESSO DE ACORDO COM O CHECKLIST]]></text>
					</staticText>
					<image hAlign="Center" vAlign="Middle">
						<reportElement x="44" y="4" width="193" height="22" uuid="068066f3-324d-44f3-9f35-ede075064f84">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$F{Q_ASSINATURA_CLIENTE_RECEPCAO}]]></imageExpression>
					</image>
				</frame>
				<frame>
					<reportElement x="261" y="59" width="201" height="19" uuid="97eeee49-9a18-4287-a3b8-e012c3a2a1d3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="30" height="9" uuid="45a7e33d-677b-4d42-b2ea-fdf0683a51b4">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[CIDADE:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="193" height="9" uuid="7d058e10-0348-49fe-b06e-bb108f4f53d1">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_CLIENTE_CIDADE}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="462" y="59" width="113" height="19" uuid="1525fa24-dac2-48ed-96f7-95e87c11ab4b"/>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<staticText>
						<reportElement x="3" y="0" width="45" height="9" uuid="59c778ab-a89a-4417-8961-0ccfffe1f26a">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<text><![CDATA[CEP:]]></text>
					</staticText>
					<textField>
						<reportElement x="3" y="9" width="103" height="9" uuid="745881f6-f40e-4bff-b676-6c72eb547160">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="6"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_EMPRESA_CEP}]]></textFieldExpression>
					</textField>
				</frame>
				<staticText>
					<reportElement x="0" y="0" width="18" height="116" uuid="ff3b02e1-d561-4aa4-a3e8-63566ee59f5a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="5">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="Left">
						<font size="4" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[ETAPA 2 - Cadastro do cliente]]></text>
				</staticText>
				<staticText>
					<reportElement x="10" y="0" width="6" height="116" uuid="abb69607-011d-4bfc-b4c2-ce09cb628072">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left">
						<font size="4" isBold="false" isItalic="false"/>
					</textElement>
					<text><![CDATA[Preencher por completo se for novo cliente]]></text>
				</staticText>
			</frame>
		</band>
		<band height="146">
			<frame>
				<reportElement x="0" y="0" width="575" height="146" uuid="fb3781d2-6138-46e0-a09d-9f9c78357e09">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport overflowType="NoStretch">
					<reportElement x="20" y="3" width="555" height="143" uuid="fd2b6b45-7387-41e7-9985-c14d10c17564">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DIR_IMAGE_LOGO">
						<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_SEGMENTO">
						<subreportParameterExpression><![CDATA[$F{Q_OS_COD_SEGMENTO}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"CheckListHondaMotoVinteUmItensSubImagem.jasper"]]></subreportExpression>
				</subreport>
				<staticText>
					<reportElement x="0" y="0" width="18" height="146" uuid="ec1d791f-2739-40a4-8670-3f4fc9fa50c8">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="5">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="Left">
						<font size="4" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[ETAPA 3 - Check List]]></text>
				</staticText>
				<staticText>
					<reportElement x="10" y="0" width="6" height="146" uuid="ba50024d-c145-44e6-a1d0-123cda565683">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left">
						<font size="4" isBold="false" isItalic="false"/>
					</textElement>
					<text><![CDATA[Anotar qualquer avaria no veículo]]></text>
				</staticText>
			</frame>
		</band>
		<band height="212">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="575" height="211" uuid="e17c68a7-ad59-44b9-99e0-111709244309"/>
				<box>
					<topPen lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport overflowType="NoStretch">
					<reportElement x="20" y="0" width="555" height="212" uuid="408a3fdd-1959-480e-aa7e-8f436357082b">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DIR_IMAGE_LOGO">
						<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_SEGMENTO">
						<subreportParameterExpression><![CDATA[$F{Q_OS_COD_SEGMENTO}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"CheckListHondaMotoVinteUmItensSubFormularioInspecao.jasper"]]></subreportExpression>
				</subreport>
				<staticText>
					<reportElement x="0" y="0" width="18" height="211" uuid="ba921295-cec2-4f61-a122-368be6e12eaa">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="5">
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="Left">
						<font size="4" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[ETAPA 4 - Inspeção de 22 Itens]]></text>
				</staticText>
				<staticText>
					<reportElement x="10" y="0" width="6" height="211" uuid="b70a93f6-b4b2-47c4-bad6-62e8e33f397b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left">
						<font size="4" isBold="false" isItalic="false"/>
					</textElement>
					<text><![CDATA[Deve ser totalmente preenchido pelo técnico. Obter visto do cliente]]></text>
				</staticText>
			</frame>
		</band>
		<band height="146">
			<frame>
				<reportElement x="0" y="0" width="575" height="146" uuid="f74ec799-e18c-4885-91bf-e21775fb38f7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<subreport overflowType="NoStretch">
					<reportElement x="20" y="0" width="555" height="146" uuid="ab61fa85-0c89-45a2-b64b-2fa602348a79">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DIR_IMAGE_LOGO">
						<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_SEGMENTO">
						<subreportParameterExpression><![CDATA[$F{Q_OS_COD_SEGMENTO}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"CheckListHondaMotoVinteUmItensSubOrcamentoServicosAdicionais.jasper"]]></subreportExpression>
				</subreport>
				<staticText>
					<reportElement x="10" y="0" width="6" height="146" uuid="74d8575d-c63e-44b4-a050-5db00bba15f8">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left">
						<font size="4" isBold="false" isItalic="false"/>
					</textElement>
					<text><![CDATA[Orçamentos com itens verificados na Inspeção]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="0" width="18" height="146" uuid="de5e6655-b5c8-4c1a-90b1-d73f7af0392a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="0" leftPadding="5">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="Left">
						<font size="4" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[ETAPA 5 - Serviços Adicionais]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
