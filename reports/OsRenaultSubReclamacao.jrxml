<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsRenaultSubReclamacao" pageWidth="479" pageHeight="72" whenNoDataType="AllSectionsNoDetail" columnWidth="479" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="473"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="526"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH Q_SERVICOS AS
 (SELECT ROWNUM AS SEQITEM,
         O.COD_SERVICO,
         S.DESCRICAO_SERVICO,
         S.TEMPO_PADRAO
    FROM OS_SERVICOS O, SERVICOS S
   WHERE O.COD_SERVICO = S.COD_SERVICO
     AND ((O.NUMERO_OS = $P{NUMERO_OS}) AND (O.COD_EMPRESA = $P{COD_EMPRESA}))
  
   ORDER BY O.NUMERO_OS),

Q_OS_ORIGINAL AS
 (SELECT SUBSTR(TO_CHAR(OS_ORIGINAL.ITEM + 100), 2, 2) AS ITEM,
         OS_ORIGINAL.DESCRICAO,
         OS_ORIGINAL.COD_GWM_RECLAMACAO
    FROM OS_ORIGINAL
   WHERE ((OS_ORIGINAL.NUMERO_OS = $P{NUMERO_OS}) AND
         (OS_ORIGINAL.COD_EMPRESA = $P{COD_EMPRESA}))
  
   ORDER BY ITEM),

TENHO AS
 (SELECT COUNT(*) AS LINHAS FROM Q_OS_ORIGINAL),

NUMERO_PAGINAS AS
 (SELECT (MAX(TOT)) AS MAX_PAGINAS
    FROM (SELECT CEIL(COUNT(*)/12) AS TOT
            FROM Q_SERVICOS
          UNION ALL
          SELECT CEIL(COUNT(*)/6) AS TOT FROM Q_OS_ORIGINAL)),

Q_NULLROWS AS
 (SELECT NULL AS ITEM,
         NULL AS DESCRICAO,
         NULL AS COD_GWM_RECLAMACAO
    FROM DUAL, TENHO, NUMERO_PAGINAS
   WHERE NUMERO_PAGINAS.MAX_PAGINAS * 6 - TENHO.LINHAS > 0
  CONNECT BY LEVEL <= NUMERO_PAGINAS.MAX_PAGINAS * 6  - TENHO.LINHAS)

SELECT 
       S.*
  FROM Q_OS_ORIGINAL S
UNION ALL
SELECT D.*
  FROM Q_NULLROWS D]]>
	</queryString>
	<field name="ITEM" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="COD_GWM_RECLAMACAO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement mode="Transparent" x="0" y="1" width="479" height="10" uuid="9a7be817-b2eb-4155-907b-acff4daefa37">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box leftPadding="3"/>
				<textElement textAlignment="Left">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
