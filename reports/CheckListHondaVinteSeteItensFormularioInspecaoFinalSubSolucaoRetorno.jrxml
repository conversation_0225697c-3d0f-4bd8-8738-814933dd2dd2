<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaVinteSeteItensFormularioInspecaoFinalSubSolucaoRetorno" pageWidth="575" pageHeight="123" columnWidth="555" leftMargin="10" rightMargin="10" topMargin="0" bottomMargin="0" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select 
  a.item_descricao,
  a.selecionado_observacao
from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
where a.id_checklist = 20000
      and a.id_grupo = 20009 -- honda checklist(inspeção laudo)
      and a.cod_item = 20066 -- 'Solução do retorno - técnico / pilloto']]>
	</queryString>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="SELECIONADO_OBSERVACAO" class="java.lang.String"/>
	<detail>
		<band height="123">
			<textField>
				<reportElement x="1" y="3" width="553" height="9" uuid="6245d408-899a-48a8-b27a-0105394cfd60">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="2" y="15" width="552" height="18" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="a6d09741-ef13-43f6-a97b-516c246ea579">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="5" isUnderline="true"/>
					<paragraph lineSpacing="Double" lineSpacingSize="2.0"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement stretchType="ContainerBottom" mode="Transparent" x="2" y="24" width="552" height="98" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="6faea785-d990-49e4-8b70-899026ea75a5">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="6" isUnderline="false"/>
					<paragraph lineSpacing="Fixed" lineSpacingSize="16.6" firstLineIndent="1" leftIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SELECIONADO_OBSERVACAO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="2" y="33" width="552" height="18" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="26025169-01a3-4f0d-95cc-60c47a9b4c26">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="5" isUnderline="true"/>
					<paragraph lineSpacing="Double" lineSpacingSize="2.0"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="2" y="51" width="552" height="18" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="3e0778b6-63ac-4556-b16b-2170d256bd2e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="5" isUnderline="true"/>
					<paragraph lineSpacing="Double" lineSpacingSize="2.0"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="2" y="69" width="552" height="18" isPrintWhenDetailOverflows="true" forecolor="#DB908F" backcolor="#FFFFFF" uuid="88dc4553-78a4-4e9a-b73f-ba9112ebf7c4">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="5" isUnderline="true"/>
					<paragraph lineSpacing="Double" lineSpacingSize="2.0"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="2" y="87" width="552" height="18" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="b6ac1a00-babb-46fa-bc50-4b1d0f2da1bc">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="5" isUnderline="true"/>
					<paragraph lineSpacing="Double" lineSpacingSize="2.0"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="2" y="105" width="552" height="18" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF" uuid="66a873e3-101f-42ef-8d64-bbc18936b406">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="5" isUnderline="true"/>
					<paragraph lineSpacing="Double" lineSpacingSize="2.0"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
