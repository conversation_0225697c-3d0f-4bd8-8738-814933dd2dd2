<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsNissanSubServicos" pageWidth="555" pageHeight="842" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="camposNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT ROWNUM NUM_LISTA,OS_SERVICOS.NUMERO_OS,
 OS_SERVICOS.COD_EMPRESA,
 OS_SERVICOS.ITEM,
 OS_SERVICOS.COD_SERVICO,
 SERVICOS.DESCRICAO_SERVICO,
 OS_SERVICOS.TEMPO_PADRAO,
 OS_SERVICOS.PRECO_VENDA
FROM OS_SERVICOS, SERVICOS
WHERE OS_SERVICOS.COD_SERVICO = SERVICOS.COD_SERVICO
  AND OS_SERVICOS.NUMERO_OS = $P{NUMERO_OS}
  AND OS_SERVICOS.COD_EMPRESA = $P{COD_EMPRESA}
ORDER BY OS_SERVICOS.ITEM, OS_SERVICOS.cod_servico]]>
	</queryString>
	<field name="NUM_LISTA" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="NUM_LISTA"/>
		<property name="com.jaspersoft.studio.field.label" value="NUM_LISTA"/>
	</field>
	<field name="NUMERO_OS" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="NUMERO_OS"/>
		<property name="com.jaspersoft.studio.field.label" value="NUMERO_OS"/>
	</field>
	<field name="COD_EMPRESA" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="COD_EMPRESA"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_EMPRESA"/>
	</field>
	<field name="ITEM" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="ITEM"/>
	</field>
	<field name="COD_SERVICO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_SERVICO"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_SERVICO"/>
	</field>
	<field name="DESCRICAO_SERVICO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="DESCRICAO_SERVICO"/>
		<property name="com.jaspersoft.studio.field.label" value="DESCRICAO_SERVICO"/>
	</field>
	<field name="TEMPO_PADRAO" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="TEMPO_PADRAO"/>
		<property name="com.jaspersoft.studio.field.label" value="TEMPO_PADRAO"/>
	</field>
	<field name="PRECO_VENDA" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="PRECO_VENDA"/>
		<property name="com.jaspersoft.studio.field.label" value="PRECO_VENDA"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="11">
			<staticText>
				<reportElement mode="Opaque" x="60" y="0" width="30" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="73ed2c61-dc67-4714-a8b8-a82e6af9e585">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Nº]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="90" y="0" width="408" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="deba69b2-afd3-469a-b2f5-8993ff35bf54">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição da Solicitação]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="498" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#C0C0C0" uuid="5d4cd358-766d-4f8b-8123-7bb35a4ecde7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Produtivo]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="60" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="d64b78a2-04b6-4573-86a6-8f71eec424e3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="3" rightPadding="3">
					<pen lineColor="#DA080B"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#DA080B"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[1. Serviços]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="90" y="0" width="408" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1e6da218-bcea-4afc-9ceb-819528eb6b97">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_SERVICO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,#00.###;#,#00.###-">
				<reportElement mode="Opaque" x="60" y="0" width="30" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="dea6bb9a-743a-4f23-83e4-c5a7e4d02cd1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#00.###;(#00.###-)">
				<reportElement mode="Opaque" x="0" y="0" width="60" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#DA080B" uuid="751d6850-6609-42fb-86cd-094b1b6ed143">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<pen lineColor="#DA080B"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#DA080B"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#DA080B"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="498" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="89a0a6ba-2e1d-4357-af84-9a0ba2922e9f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<line>
				<reportElement x="60" y="-1" width="495" height="1" uuid="b4fc83a4-5715-4057-83ae-c632312909d1"/>
				<graphicElement>
					<pen lineColor="#C0C0C0"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="0" y="-1" width="60" height="1" uuid="49ebeff5-a166-4636-9c27-5f0bcf05cc3a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineColor="#FFFFFF"/>
				</graphicElement>
			</line>
		</band>
	</columnFooter>
</jasperReport>
