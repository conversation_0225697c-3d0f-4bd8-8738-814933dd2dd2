<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiChecklistSeisPassos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="842" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<style name="fundoAzul" style="default_null" mode="Opaque" forecolor="#FFFFFF" backcolor="#285A95"/>
	<style name="fontAzul" forecolor="#285A95"/>
	<style name="fundoAzulClaro" style="default_null" forecolor="#285A95" backcolor="#E4EEFA"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\31167\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH QRY_OS AS
 (SELECT OS.NUMERO_OS,
         OS.COD_EMPRESA,
         OSV.KM,
         NVL(OS_AGENDA.SIGNATURE,
             (SELECT OAS.SIGNATURE
                FROM OS_AGENDA OAS
               WHERE OAS.COD_EMPRESA = OS.COD_EMPRESA
                 AND OAS.COD_OS_AGENDA = OS.COD_OS_AGENDA)) AS OS_ASSINATURA_CLIENTE,
         OS.DATA_EMISSAO AS DATA_EMISSAO,
         OS.HORA_EMISSAO AS HORA_EMISSAO,
         INITCAP(CD.NOME) AS NOME_CLIENTE
    FROM OS, OS_DADOS_VEICULOS OSV, OS_AGENDA, CLIENTE_DIVERSO CD
   WHERE 1 = 1
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.COD_EMPRESA = OSV.COD_EMPRESA(+)
     AND OS.NUMERO_OS = OSV.NUMERO_OS(+)
     AND OS_AGENDA.COD_OS_AGENDA(+) = OS.COD_OS_AGENDA
     AND OS_AGENDA.COD_EMPRESA(+) = OS.COD_EMPRESA
     AND OS.COD_CLIENTE = CD.COD_CLIENTE(+)),

QRY_PRODUTIVO AS
 (SELECT OSR.COD_EMPRESA,
         OSR.NUMERO_OS,
         EXE.COD_TECNICO AS COD_PRODUTIVO,
         TEC.NOME        AS TECNICO
    FROM OS_SERVICOS OSR, OS_TEMPOS_EXECUTADOS EXE, SERVICOS_TECNICOS TEC
   WHERE EXE.COD_EMPRESA = OSR.COD_EMPRESA
     AND EXE.NUMERO_OS = OSR.NUMERO_OS
     AND TEC.COD_TECNICO = EXE.COD_TECNICO
     AND OSR.COD_EMPRESA = $P{COD_EMPRESA}
     AND OSR.NUMERO_OS = $P{NUMERO_OS}
     AND ROWNUM = 1),

QRY_ASSINATURA AS
 (SELECT MOS.ASSINATURA_CLIENTE AS ASSINATURA_CLIENTE_OFICINA,
         MOS.ASSINATURA AS ASSINATURA_CONSULTOR_OFICINA,
         MOS.DATA_ASSINATURA,
         MOS.NUMERO_OS,
         MOS.COD_EMPRESA
    FROM MOB_OS_ASSINATURA MOS
   WHERE 1 = 1
     AND MOS.APLICACAO = 'O'
     AND MOS.NUMERO_OS = $P{NUMERO_OS}
     AND MOS.COD_EMPRESA = $P{COD_EMPRESA})

SELECT QRY_OS.NUMERO_OS,
       QRY_OS.COD_EMPRESA,
       QRY_PRODUTIVO.TECNICO,
       QRY_OS.OS_ASSINATURA_CLIENTE,
       QRY_OS.NOME_CLIENTE,
       QRY_ASSINATURA.ASSINATURA_CONSULTOR_OFICINA,
       QRY_ASSINATURA.ASSINATURA_CLIENTE_OFICINA,
       QRY_ASSINATURA.DATA_ASSINATURA,
       QRY_OS.KM
  FROM QRY_OS, QRY_PRODUTIVO, QRY_ASSINATURA
 WHERE QRY_OS.NUMERO_OS = QRY_PRODUTIVO.NUMERO_OS (+)
   AND QRY_OS.COD_EMPRESA = QRY_PRODUTIVO.COD_EMPRESA (+)
   AND QRY_OS.NUMERO_OS = QRY_ASSINATURA.NUMERO_OS(+)
   AND QRY_OS.COD_EMPRESA = QRY_ASSINATURA.COD_EMPRESA(+)]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="TECNICO" class="java.lang.String"/>
	<field name="OS_ASSINATURA_CLIENTE" class="java.awt.Image"/>
	<field name="NOME_CLIENTE" class="java.lang.String"/>
	<field name="ASSINATURA_CONSULTOR_OFICINA" class="java.awt.Image"/>
	<field name="ASSINATURA_CLIENTE_OFICINA" class="java.awt.Image"/>
	<field name="DATA_ASSINATURA" class="java.sql.Timestamp"/>
	<field name="KM" class="java.lang.Double"/>
	<pageHeader>
		<band height="99">
			<frame>
				<reportElement x="0" y="9" width="841" height="90" uuid="c965979e-4349-4103-987a-551b477882a8"/>
				<frame>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="0" width="445" height="45" forecolor="#1712A1" backcolor="#285A95" uuid="17f46b53-45a8-4236-aebf-e10abf28c69c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<staticText>
						<reportElement style="fundoAzul" mode="Transparent" x="30" y="0" width="300" height="30" uuid="2689f0f3-095c-4e3a-830f-a85d46897256">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
						</reportElement>
						<textElement>
							<font fontName="SansSerif" size="18" isBold="true"/>
						</textElement>
						<text><![CDATA[Hyundai Service Number 1]]></text>
					</staticText>
					<staticText>
						<reportElement style="fundoAzul" mode="Transparent" x="30" y="25" width="210" height="22" uuid="1b60dde2-5d8e-489e-93c0-b6f5db33165f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement>
							<font fontName="SansSerif" size="13" isBold="false"/>
						</textElement>
						<text><![CDATA[6 Passos de Manutenção]]></text>
					</staticText>
				</frame>
				<line>
					<reportElement style="fundoAzul" x="0" y="50" width="841" height="1" uuid="e3e7f25f-4cac-4fc2-a184-23805617eabc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="5.0" lineColor="#265087"/>
					</graphicElement>
				</line>
				<image onErrorType="Blank">
					<reportElement x="463" y="7" width="37" height="37" uuid="c6b9a889-fa06-4dd6-8341-3815e1a579c1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice331012.png"]]></imageExpression>
				</image>
				<image hAlign="Center" vAlign="Middle" isUsingCache="false" onErrorType="Blank">
					<reportElement x="730" y="20" width="110" height="26" uuid="386538e2-1490-4e97-96b1-6e6b95be5974">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice331011.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement style="fontAzul" x="14" y="78" width="36" height="12" forecolor="#2A4C7E" uuid="f0f14521-0099-4dd0-9543-765b00e76980">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Técnico:]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="489" y="61" width="352" height="14" uuid="3bd87d3f-ea6a-4b63-9b21-d5a3aa8c7257">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box rightPadding="6"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[10.000 / 20.000 / 30.000 / 40.000 / 50.000 / 60.000 / 70.000 / 80.000 / 90.000 / 100.000 KM]]></text>
				</staticText>
				<staticText>
					<reportElement style="fontAzul" x="300" y="78" width="72" height="12" forecolor="#2A4C7E" uuid="fc68ed60-04aa-454d-a0aa-b16543eb49f3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Quilometragem:]]></text>
				</staticText>
				<staticText>
					<reportElement style="fontAzul" x="480" y="78" width="32" height="12" forecolor="#2A4C7E" uuid="eb97c15a-af99-49ae-9e5e-1b2489b6ef99">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[N° OS:]]></text>
				</staticText>
				<staticText>
					<reportElement style="fontAzul" x="683" y="78" width="24" height="12" forecolor="#2A4C7E" uuid="ce670054-3ef0-4819-9eb5-0c310383b6fe">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField>
					<reportElement style="fontAzul" x="50" y="78" width="240" height="12" forecolor="#2A4C7E" uuid="1c176481-6744-483d-b54b-38e13b178844">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TECNICO}]]></textFieldExpression>
				</textField>
				<textField pattern="#.###">
					<reportElement style="fontAzul" x="372" y="78" width="28" height="12" forecolor="#2A4C7E" uuid="80ca8039-06f8-4bfd-b7fb-50629dbcd1a6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="SansSerif" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KM}]]></textFieldExpression>
				</textField>
				<textField pattern="#.###">
					<reportElement style="fontAzul" x="512" y="78" width="66" height="12" forecolor="#2A4C7E" uuid="e38c1cfc-50bb-4841-bfa4-4a5b39a04cd3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NUMERO_OS}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement style="fontAzul" x="707" y="78" width="82" height="12" forecolor="#2A4C7E" uuid="93cb8863-769c-4274-bf18-b757ac951f85">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_ASSINATURA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="fontAzul" x="400" y="78" width="37" height="12" forecolor="#2A4C7E" uuid="b16efe25-69fe-4052-974b-49f1209210dd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="9"/>
					</textElement>
					<text><![CDATA[KM]]></text>
				</staticText>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="454" splitType="Immediate">
			<frame>
				<reportElement x="282" y="341" width="552" height="88" isPrintWhenDetailOverflows="true" uuid="3c7d85d9-5c0d-4648-88a7-ec4e9346c385">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<staticText>
					<reportElement style="fundoAzulClaro" mode="Opaque" x="0" y="0" width="138" height="88" uuid="08a8c81a-9b2c-41bf-9781-750f9ae50789">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<textElement textAlignment="Left" markup="html">
						<font size="7" isBold="false"/>
						<paragraph lineSpacing="Proportional"/>
					</textElement>
					<text><![CDATA[Peças:
* 2,9 e 3,3 Lts. Óleo lubrificante do motor 1.0 e 1.6 respectivamente.
* Filtro de óleo do motor – PN: 2630005503
* Arruela do bujão do carter – PN: 2151323001
* Filtro de combustível – PN: 31980S000
* Demais Peças Consultar com o Balconista de Oficina.]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzulClaro" mode="Opaque" x="138" y="34" width="276" height="54" uuid="3f5bddf7-a882-45bc-970d-fc2b2f992d8b">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Middle" markup="none">
						<font size="7" isBold="false"/>
						<paragraph lineSpacing="Proportional"/>
					</textElement>
					<text><![CDATA[*1: Ajuste se houver ruído excessivo ou vibração do motor (para verificação e ajuste o motor deve estar frio).
*2: Primeira substituição com 100.000 Km e as demais a cada 40.000 Km ou 24 meses.
*3: Substitua o óleo da transmissão manual sempre que o veículo passar por áreas inundadas.]]></text>
				</staticText>
				<frame>
					<reportElement style="fundoAzulClaro" mode="Opaque" x="138" y="0" width="276" height="34" uuid="699cf851-6cfd-4eb7-8348-759ec294f5fe"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<staticText>
						<reportElement style="fundoAzulClaro" mode="Opaque" x="7" y="3" width="58" height="28" uuid="52624ac4-a077-4726-aaf1-ee8134770f2a">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
							<font size="7" isBold="false"/>
							<paragraph lineSpacing="Proportional"/>
						</textElement>
						<text><![CDATA[Tempo padrão:
10.000 = 0,7]]></text>
					</staticText>
					<staticText>
						<reportElement style="fundoAzulClaro" mode="Opaque" x="75" y="3" width="58" height="28" uuid="9473c2cb-33b0-42e0-988c-d3bf577e88de">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
							<font size="7" isBold="false"/>
							<paragraph lineSpacing="Proportional"/>
						</textElement>
						<text><![CDATA[20.000 = 0,7
30.000 = 0,9
40.000 = 1,1
]]></text>
					</staticText>
					<staticText>
						<reportElement style="fundoAzulClaro" mode="Opaque" x="143" y="3" width="58" height="28" uuid="0effe165-d602-4d50-95e1-98a3f974a20e">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
							<font size="7" isBold="false"/>
							<paragraph lineSpacing="Proportional"/>
						</textElement>
						<text><![CDATA[50.000 = 0,8
60.000 = 1,2
70.000 = 0.8]]></text>
					</staticText>
					<staticText>
						<reportElement style="fundoAzulClaro" mode="Opaque" x="211" y="3" width="58" height="28" uuid="629dd1a1-0664-4e77-8006-c7eb62116c53">
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
							<font size="7" isBold="false"/>
							<paragraph lineSpacing="Proportional"/>
						</textElement>
						<text><![CDATA[80.000 = 1,1
90.000 = 0,9
100.000 = 1,1]]></text>
					</staticText>
				</frame>
				<staticText>
					<reportElement style="fundoAzulClaro" mode="Opaque" x="414" y="0" width="138" height="88" uuid="373ca442-a2a7-4b01-bd3f-216751442bf3">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font size="7" isBold="false"/>
						<paragraph lineSpacing="Proportional"/>
					</textElement>
					<text><![CDATA[I – Inspecionar / Ajustar 
R – Remover
S – Substituir / Trocar
C – Completar
A – Apertar no torque especificado manul de Reparações
P – Preparação]]></text>
				</staticText>
			</frame>
			<line>
				<reportElement style="fontAzul" x="6" y="1" width="1" height="428" isPrintWhenDetailOverflows="true" uuid="0f6764ff-d1f3-41d9-89c5-12a880cc4425">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement style="fontAzul" x="143" y="1" width="1" height="428" isPrintWhenDetailOverflows="true" uuid="b51f590b-4ca7-4c81-937e-fc2535612e0c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement style="fontAzul" x="281" y="2" width="1" height="339" isPrintWhenDetailOverflows="true" uuid="7398bf63-d855-4b2f-8528-16f1cf547a35">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement style="fontAzul" x="419" y="2" width="1" height="339" isPrintWhenDetailOverflows="true" uuid="238da4d7-0eda-4099-adda-ae20d5610124">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement style="fontAzul" x="557" y="2" width="1" height="339" isPrintWhenDetailOverflows="true" uuid="b18ba1c9-1efa-4684-975b-1143caef2005">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement style="fontAzul" x="695" y="2" width="1" height="339" isPrintWhenDetailOverflows="true" uuid="28e6119d-4ff1-44e3-b2eb-53caceb0d6db">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement style="fontAzul" x="833" y="2" width="1" height="339" isPrintWhenDetailOverflows="true" uuid="cdd97206-e6f5-4e83-be8c-4c8ea59a58da">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement stretchType="ContainerBottom" isPrintRepeatedValues="false" x="6" y="76" width="138" height="351" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="faf3ee75-a838-4f55-a162-a72f13fdb223">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ORDEM_GRUPO">
					<subreportParameterExpression><![CDATA[1]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListSeisPartesSubGrupo.jasper"]]></subreportExpression>
			</subreport>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement isPrintRepeatedValues="false" x="282" y="76" width="138" height="263" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="26962583-591d-458d-9032-00520a1b04e4">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ORDEM_GRUPO">
					<subreportParameterExpression><![CDATA[3]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListSeisPartesSubGrupo.jasper"]]></subreportExpression>
			</subreport>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement isPrintRepeatedValues="false" x="420" y="76" width="138" height="262" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="c0375cf0-9df8-428b-b52f-20c0390937b3">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ORDEM_GRUPO">
					<subreportParameterExpression><![CDATA[4]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListSeisPartesSubGrupo.jasper"]]></subreportExpression>
			</subreport>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement isPrintRepeatedValues="false" x="558" y="76" width="138" height="264" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="aa9bbe2d-dbd4-4088-bc99-ef2ca4e4acd7">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ORDEM_GRUPO">
					<subreportParameterExpression><![CDATA[5]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListSeisPartesSubGrupo.jasper"]]></subreportExpression>
			</subreport>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement isPrintRepeatedValues="false" x="696" y="76" width="138" height="264" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="5a53d5e1-5dc3-423d-b711-4c92fb473f04">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ORDEM_GRUPO">
					<subreportParameterExpression><![CDATA[6]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListSeisPartesSubGrupo.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement style="fundoAzulClaro" mode="Opaque" x="6" y="429" width="828" height="25" isPrintWhenDetailOverflows="true" uuid="6eb7cb38-7c49-47c0-af6d-bb27c2110af1">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="ShowOutOfBoundContent" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
				</box>
				<staticText>
					<reportElement style="fontAzul" x="2" y="1" width="48" height="10" forecolor="#2A4C7E" uuid="7e568266-ce9d-4b85-82fc-49a220ebdd69">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Observações: ]]></text>
				</staticText>
			</frame>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement x="144" y="76" width="138" height="351" uuid="28566826-8fe9-443a-b283-b5bedfdf68fb">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ORDEM_GRUPO">
					<subreportParameterExpression><![CDATA[2]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiCheckListSeisPartesSubGrupo.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement x="144" y="1" width="138" height="75" isPrintWhenDetailOverflows="true" uuid="5af2973c-9a69-4e98-a2f9-15e0435f693b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#4A59BA"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#4A59BA"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#4A59BA"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#4A59BA"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#4A59BA"/>
				</box>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="62" width="12" height="13" uuid="959ffbe1-96f3-4db0-989e-09f50910a9cf">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineStyle="Solid"/>
						<leftPen lineWidth="1.0" lineStyle="Solid"/>
						<bottomPen lineStyle="Solid"/>
						<rightPen lineWidth="1.0" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[n°]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="27" y="62" width="83" height="13" uuid="1f37a3a8-40ab-4023-9d4d-3b6fb3b4b4d0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Componente]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="124" y="62" width="13" height="13" forecolor="#FFFFFF" uuid="c6e2d7fa-33f6-4cd9-a817-6fb9885877ef">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[nok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="110" y="62" width="14" height="13" forecolor="#FFFFFF" uuid="ddfbf706-8a40-4f93-8a41-fc14fc31f219">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="12" y="62" width="15" height="13" uuid="bd91a15c-0c1b-4629-9133-8416acc3fabe">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ação]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="43" y="14" width="53" height="48" uuid="04e18f0f-fc33-44f0-88e6-65730dea45a2">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice33108.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement style="fontAzul" mode="Transparent" x="3" y="15" width="19" height="19" uuid="313ac23f-38ea-489e-88ea-5bdf46a2b2d5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[2]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="0" width="137" height="13" uuid="11983008-8b37-45ab-9826-1e2d953deac4">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Veículo no Solo e no box de serviço (externo)]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="6" y="1" width="138" height="75" isPrintWhenDetailOverflows="true" uuid="9376ea11-b9b1-4b61-aaee-32002ce28d5a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#4A59BA"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="62" width="12" height="13" uuid="97d69313-274d-4b43-bd53-287f8f8f3e47">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineStyle="Solid"/>
						<leftPen lineWidth="0.0" lineStyle="Solid"/>
						<bottomPen lineStyle="Solid"/>
						<rightPen lineWidth="1.0" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[n°]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="27" y="62" width="83" height="13" uuid="0f498fa4-07c4-474d-8505-99d2a4106eab">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Componente]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="124" y="62" width="13" height="13" forecolor="#FFFFFF" uuid="d0e4aeea-020a-4d8f-9102-ec5464d7b828">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[nok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="110" y="62" width="14" height="13" forecolor="#FFFFFF" uuid="636cda21-9455-4f98-b579-eda11f25e0fd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="12" y="62" width="15" height="13" uuid="0085881f-e498-4838-8e34-83af5751f7b2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ação]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="43" y="14" width="53" height="48" uuid="9bec3f1f-c983-4ff4-b229-ff681e0015c1">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice33108.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement style="fontAzul" mode="Transparent" x="3" y="15" width="19" height="19" uuid="bf74497b-edb8-481e-9ee0-28742dacff48">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[1]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="0" width="137" height="13" uuid="de50686c-b672-48dd-86f2-8446f103edfc">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Veículo no Solo e no box de serviço (externo)]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="696" y="1" width="138" height="75" isPrintWhenDetailOverflows="true" uuid="4ad55491-5463-4030-a9a7-4b4972373c8a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#4A59BA"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="62" width="12" height="13" uuid="1efd7fa0-6786-42f1-90c5-063fb5516ced">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineStyle="Solid"/>
						<leftPen lineWidth="1.0" lineStyle="Solid"/>
						<bottomPen lineStyle="Solid"/>
						<rightPen lineWidth="1.0" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[n°]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="27" y="62" width="83" height="13" uuid="3f520fd8-94e3-4f10-a9b2-90d2e8d21f94">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Componente]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="124" y="62" width="14" height="13" forecolor="#FFFFFF" uuid="9dda1ee6-2409-45b9-a515-7659d19dd85c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[nok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="110" y="62" width="14" height="13" forecolor="#FFFFFF" uuid="0548be0e-a8c2-4895-a288-61f9631b6013">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="12" y="62" width="15" height="13" uuid="14bcb163-5503-4b17-bfaa-607a968fd9a6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ação]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="43" y="14" width="53" height="48" uuid="1ea77d0b-0e4f-46cf-a3e5-7720d17e889c">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice33108.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement style="fontAzul" mode="Transparent" x="3" y="15" width="19" height="19" uuid="5868b9a3-7b93-4fcc-a6f2-eb49b89a5eab">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[6]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="0" width="138" height="13" uuid="d6ad9dc8-68b2-4826-9e53-bd8794772575">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Veículo no Solo e no box de serviço (externo)]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="420" y="1" width="138" height="75" isPrintWhenDetailOverflows="true" uuid="15d64c34-4592-41b8-9968-4a6d88bf1891">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#4A59BA"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="62" width="12" height="13" uuid="097e2f4e-622a-4805-b210-a6e56cd23966">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineStyle="Solid"/>
						<leftPen lineWidth="1.0" lineStyle="Solid"/>
						<bottomPen lineStyle="Solid"/>
						<rightPen lineWidth="1.0" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[n°]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="27" y="62" width="83" height="13" uuid="47623408-08a9-4d0b-8c87-6b4e203984c0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Componente]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="124" y="62" width="13" height="13" forecolor="#FFFFFF" uuid="40f77d84-c2a6-4b5e-95c9-1391938e9aee">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[nok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="110" y="62" width="14" height="13" forecolor="#FFFFFF" uuid="b62c11f2-d396-46de-ae09-3021483150fa">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="12" y="62" width="15" height="13" uuid="70e82765-4981-4e5e-af85-94ce02af1e58">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ação]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="43" y="14" width="53" height="48" uuid="aa309866-af6e-4ed5-b42f-4bf1564c8a9c">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice331010.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement style="fontAzul" mode="Transparent" x="3" y="15" width="19" height="19" uuid="381acb17-a47c-43d6-bbb6-da79eb09a1b2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[4]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="0" width="137" height="13" uuid="659c842d-114b-4b8c-8b06-9928b486a022">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Veículo no Solo e no box de serviço (externo)]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="282" y="1" width="138" height="75" isPrintWhenDetailOverflows="true" uuid="f925628d-4a7e-406d-ad54-b2d6236c9f64">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#4A59BA"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="62" width="12" height="13" uuid="506b5d88-2746-4f5b-8253-df01a81beb34">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineStyle="Solid"/>
						<leftPen lineWidth="1.0" lineStyle="Solid"/>
						<bottomPen lineStyle="Solid"/>
						<rightPen lineWidth="1.0" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[n°]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="27" y="62" width="83" height="13" uuid="c1d97df7-ae3b-4264-9a05-643f0f656d97">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Componente]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="124" y="62" width="13" height="13" forecolor="#FFFFFF" uuid="d53a80a1-08ec-4827-9e87-32051d85edce">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[nok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="110" y="62" width="14" height="13" forecolor="#FFFFFF" uuid="ab4be730-a3cb-448b-85c0-8d3c17c46f6a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="12" y="62" width="15" height="13" uuid="1abe2bcd-1bc2-4dde-bb08-a1e9b9d87ef9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ação]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="43" y="14" width="53" height="48" uuid="efb08d23-d511-40cd-a6a5-f126e003815e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice33109.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement style="fontAzul" mode="Transparent" x="3" y="15" width="19" height="19" uuid="85fc63b0-8170-4f11-b41b-ee71000637e1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[3]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="0" width="137" height="13" uuid="931e7617-ec63-4b78-8e1d-a617e2e2a16a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Veículo no Solo e no box de serviço (externo)]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="558" y="1" width="138" height="75" isPrintWhenDetailOverflows="true" uuid="96412a44-c77a-4ab6-bebe-b3e06b6b9018">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineColor="#4A59BA"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="62" width="12" height="13" uuid="6ce7439a-a9fa-4e62-b02a-7a324f90eb3a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineStyle="Solid"/>
						<leftPen lineWidth="1.0" lineStyle="Solid"/>
						<bottomPen lineStyle="Solid"/>
						<rightPen lineWidth="1.0" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[n°]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="27" y="62" width="83" height="13" uuid="9a400345-8642-4469-a735-f8d1ed1cd285">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Componente]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="124" y="62" width="13" height="13" forecolor="#FFFFFF" uuid="bcd1c5ea-1695-4ac7-9394-0fe80e742b75">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[nok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="110" y="62" width="14" height="13" forecolor="#FFFFFF" uuid="17695602-4a97-4687-a619-fd8e7e1a423c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ok]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="12" y="62" width="15" height="13" uuid="3de1aa48-cd8f-4bc5-ac6e-805e24f7f61d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[ação]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="43" y="14" width="53" height="48" uuid="3cdd947e-9b30-4f41-a899-a6e7bdcacee1">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice33109.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement style="fontAzul" mode="Transparent" x="3" y="15" width="19" height="19" uuid="5be04c2d-ae01-4487-aa41-2e58e82de670">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#285A95"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[5]]></text>
				</staticText>
				<staticText>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="0" width="137" height="13" uuid="488ac034-6ea7-468b-887b-8728787ad749">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Veículo no Solo e no box de serviço (externo)]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="39">
			<frame>
				<reportElement x="0" y="1" width="842" height="37" uuid="5f103562-b3b4-4d13-991c-17b5ae5570c1"/>
				<line>
					<reportElement style="fundoAzul" mode="Opaque" x="0" y="36" width="842" height="1" uuid="019436ae-a7a4-43dc-9ba8-13f453990bba">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="7.0" lineStyle="Solid" lineColor="#285A95"/>
					</graphicElement>
				</line>
				<staticText>
					<reportElement style="fontAzul" x="286" y="-1" width="271" height="10" forecolor="#2A4C7E" uuid="af63dbd9-8b0e-4cc1-ad60-241ea34b7a1d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Declaro que foi realizada a entrega técnica e recebi instruções sobre o veículo]]></text>
				</staticText>
				<staticText>
					<reportElement style="fontAzul" x="15" y="23" width="271" height="10" forecolor="#2A4C7E" uuid="0859fa15-5b72-492f-ae11-6616a23556da">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome do Cliente]]></text>
				</staticText>
				<staticText>
					<reportElement style="fontAzul" x="300" y="23" width="310" height="10" forecolor="#2A4C7E" uuid="4ee32650-9ef6-410c-908f-f417d6803a7d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Assinatura]]></text>
				</staticText>
				<staticText>
					<reportElement style="fontAzul" x="621" y="23" width="191" height="10" forecolor="#2A4C7E" uuid="933b5df8-432c-46b6-b399-b838d527693a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<textField>
					<reportElement style="fontAzul" x="15" y="13" width="271" height="10" forecolor="#2A4C7E" uuid="7027adf4-c0a2-46fe-ac02-67bb97323793">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_CLIENTE}]]></textFieldExpression>
				</textField>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="380" y="9" width="133" height="13" uuid="3d34bd5b-9818-4670-859f-e6c766add25d"/>
					<imageExpression><![CDATA[$F{ASSINATURA_CLIENTE_OFICINA}]]></imageExpression>
				</image>
				<textField pattern="dd/MM/yyyy">
					<reportElement style="fontAzul" x="621" y="13" width="191" height="10" forecolor="#2A4C7E" uuid="9c603833-3025-4df6-8f51-21a0b871b718">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_ASSINATURA}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnFooter>
</jasperReport>
