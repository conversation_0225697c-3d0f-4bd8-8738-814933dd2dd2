<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ChecklistGwmPdsSubItemFoto" columnCount="4" printOrder="Horizontal" pageWidth="450" pageHeight="842" columnWidth="112" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="92c3c34a-6a74-4816-b251-78e573d7db94">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_ITEM" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT F.FOTO_ICONE
FROM MOB_OS_PERTENCE_FOTO F, MOB_PERTENCE_ITEM PI, MOB_PERTENCE_GRUPO PG
WHERE PI.COD_ITEM = F.COD_ITEM 
 AND PI.ID_GRUPO = PG.ID_GRUPO
 AND F.COD_EMPRESA = $P{COD_EMPRESA} 
 AND F.NUMERO_OS = $P{NUMERO_OS}
 AND F.COD_ITEM = $P{COD_ITEM}
ORDER BY F.ORDEM]]>
	</queryString>
	<field name="FOTO_ICONE" class="java.awt.Image"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="104" splitType="Stretch">
			<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" onErrorType="Blank">
				<reportElement x="2" y="5" width="107" height="94" uuid="5f15539f-9537-4ba8-9a97-18aff46242fe">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box padding="5">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$F{FOTO_ICONE}]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>
