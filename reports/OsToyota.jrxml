<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsToyota" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\27369\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT

  
  
  OS.TIPO_TOYOTA AS OS_TIPO_TOYOTA,
  
  
  (SELECT NVL(CM.VALOR_CONTRATO, 0) AS VALOR_CONTRATO
  FROM CONTRATOS_MANUTENCAO CM
  WHERE CM.COD_CLIENTE = OS.COD_CLIENTE
  AND CM.CHASSI      = OS_DADOS_VEICULOS.CHASSI
  AND CM.TIPO_OS     = OS.TIPO
  AND EXISTS (SELECT * 
              FROM CONTRATO_REVISOES CR
              WHERE CR.COD_RECLAMACAO IS NOT NULL
                AND CR.NUMERO_OS IS NOT NULL
                AND CR.NUMERO_CONTRATO = CM.NUMERO_CONTRATO)) AS OS_VALOR_PACOTE_PRE_PAGO,
  

  
  OS.COD_EMPRESA                    AS OS_COD_EMPRESA,
  OS.STATUS_OS                    AS OS_STATUS_OS,
  OS.COD_OS_AGENDA                  AS OS_COD_OS_AGENDA,
  NVL(OS.NUMERO_OS, 0)                    AS OS_NUMERO_OS, 
  ABS(OS.NUMERO_OS)                  AS OS_ABS_OSNUM,
  OS.NUMERO_OS_FABRICA                AS OS_NUMERO_OS_FABRICA,
  OS.COD_CLIENTE                    AS OS_COD_CLIENTE, 
  OS.CLIENTE_RAPIDO                  AS OS_CLIENTE_RAPIDO,
  OS.TIPO_ENDERECO                  AS OS_TIPO_ENDERECO,
  NVL(OS.OBSERVACAO, ' ')                    AS OS_OBSERVACAO,
  OS.EXTENDIDA                    AS OS_EXTENDIDA,
  OS.SEGURADORA                    AS OS_SEGURADORA,
  NVL(TO_CHAR(OS.DATA_EMISSAO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_EMISSAO,
  NVL(TO_CHAR(OS.DATA_LIBERADO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_LIBERADO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2)     AS OS_DIA_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2)     AS OS_MES_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4)     AS OS_ANO_EMISSAO,
  OS.HORA_EMISSAO                    AS OS_HORA_EMISSAO,
  OS.HORA_ENCERRADA                  AS OS_HORA_ENCERRADA,
  OS.DATA_ENCERRADA                  AS OS_DATA_ENCERRADA,
  NVL(OS.HORA_PROMETIDA,' ')                  AS OS_HORA_PROMETIDA, 
  SUBSTR(OS.HORA_PROMETIDA, 1, 2)           AS OS_HORA24_PROMETIDA,
  SUBSTR(OS.HORA_PROMETIDA, 4, 2)           AS OS_MINUTO_PROMETIDA,
  NVL(TO_CHAR(OS.DATA_PROMETIDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PROMETIDA, 
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2)     AS OS_DIA_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2)     AS OS_MES_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4)   AS OS_ANO_PROMETIDA,
  NVL(OS.VALOR_SERVICOS_BRUTO, 0)                AS OS_VALOR_SERVICOS_BRUTO,
  NVL(OS.VALOR_ITENS_BRUTO, 0)                AS OS_VALOR_ITENS_BRUTO,
  NVL(OS.DESCONTOS_SERVICOS, 0)                AS OS_DESCONTOS_SERVICOS,
  NVL(OS.DESCONTOS_ITENS, 0)                  AS OS_DESCONTOS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS,0)                           AS OS_TOTAL_OS_SERVICOS,
  NVL(OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS,0)                                 AS OS_TOTAL_OS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO,0)                            AS OS_TOTAL_OS_BRUTO,
  NVL(OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS,0)                                AS OS_TOTAL_OS_DESCONTO,
  NVL((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) + (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS), 0)   AS OS_TOTAL_OS,
  OS.COD_SEGURADORA                  AS OS_COD_SEGURADORA,
  OS.TIPO                        AS OS_TIPO,
  OS.NOME                       AS CONSULTOR,
  OS.OS_ENTRADA                    AS OS_OS_ENTRADA,
  '1ª VIA CLIENTE 1ª VIA CLIENTE 1ª VIA CLIENTE'     AS OS_VIA,
  OS.TOTAL_IMPRESSAO_FABRICA              AS OS_TOTAL_IMPRESSAO_FABRICA,
  NVL(OS_TIPOS_TERMO.TEXTO,' ')            AS OS_TERMO_TEXTO,
  NVL(TO_CHAR(OS.DATA_TAXI, 'DD/MM/YYYY'),' ')                  AS OS_DATA_TAXI,
  NVL(OS.HORA_TAXI,' ')                  AS OS_HORA_TAXI, 
  NVL(OS.TODO_TRAB,' ') AS OS_TODO_TRABALHO_FEITO,
  NVL(OS.TODO_LIMPO,' ') AS OS_TODO_LIMPO,
  
  NVL(os.pri_rodagem,'N')                  AS OS_PRIMEIRA_RODAGEM,
  NVL(os.seg_rodagem,'N')                  AS OS_SEGUNDA_RODAGEM,
  NVL(TO_CHAR(os.data_pri_rodagem, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PRIMEIRA_RODAGEM,
  NVL(TO_CHAR(os.data_seg_rodagem, 'DD/MM/YYYY'),' ')                  AS OS_DATA_SEGUNDA_RODAGEM,
  NVL(os.os_pri_resp,' ')                  AS OS_PRIM_RESPONSAVEL_RODAGEM,
  NVL(os.os_seg_resp,' ')                  AS OS_SEG_RESPONSAVEL_RODAGEM,
  
 
  
  (SELECT LISTAGG(FORMAS_PAGAMENTO.CONDICAO_PAGAMENTO, '; ') WITHIN GROUP (ORDER BY CONDICAO_PAGAMENTO)        
  FROM (SELECT (F.DESCRICAO || ' ' ||  TO_CHAR(O.VALOR, 'FM999999999D00')) AS CONDICAO_PAGAMENTO FROM OS_PAGAMENTO O, FORMA_PGTO F
  WHERE O.COD_FORMA_PGTO = F.COD_FORMA_PGTO
    AND O.NUMERO_OS = $P{NUMERO_OS}
    AND O.COD_EMPRESA= $P{COD_EMPRESA}) FORMAS_PAGAMENTO) AS OS_DESCRICAO_PAGAMENTO,

  
  MOTORISTAS.NOME_DO_MOTORISTA AS MOTORISTA_NOME,
  MOTORISTAS.DOCUMENTO AS MOTORISTA_DOCUMENTO,

  
  (SELECT LISTAGG(ORCAMENTOS.NUMERO_OS, ', ') WITHIN GROUP (ORDER BY NUMERO_OS)
  FROM 
    (SELECT ABS(OS.NUMERO_OS) AS NUMERO_OS
    FROM OS, OS_STATUS, OS_ORCAMENTOS, OS_TIPOS
    WHERE OS.STATUS_OS = OS_STATUS.STATUS_OS
    AND OS_ORCAMENTOS.COD_EMPRESA = OS.COD_EMPRESA
    AND OS_ORCAMENTOS.NUMERO_ORCAMENTO = OS.NUMERO_OS
    AND OS.TIPO = OS_TIPOS.TIPO
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
    AND OS_ORCAMENTOS.COD_EMPRESA = $P{COD_EMPRESA}
    AND OS_ORCAMENTOS.NUMERO_OS = $P{NUMERO_OS}
    GROUP BY OS.NUMERO_OS
    ORDER BY OS.NUMERO_OS) ORCAMENTOS) AS OS_NUMERO_ORCAMENTO,
    
  (SELECT LISTAGG(ORCAMENTOS.NOME_CLIENTE_APROVOU, ', ') WITHIN GROUP (ORDER BY ORCAMENTOS.NOME_CLIENTE_APROVOU) 
    FROM 
    (SELECT OS_ORCAMENTOS.NOMECLIAPROVOU AS NOME_CLIENTE_APROVOU
    FROM OS, OS_STATUS, OS_ORCAMENTOS, OS_TIPOS
    WHERE OS.STATUS_OS = OS_STATUS.STATUS_OS
    AND OS_ORCAMENTOS.COD_EMPRESA = OS.COD_EMPRESA
    AND OS_ORCAMENTOS.NUMERO_ORCAMENTO = OS.NUMERO_OS
    AND OS.TIPO = OS_TIPOS.TIPO
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
    AND OS_ORCAMENTOS.COD_EMPRESA = 34
    AND OS_ORCAMENTOS.NUMERO_OS = 23493
    GROUP BY OS_ORCAMENTOS.NOMECLIAPROVOU
    ORDER BY OS_ORCAMENTOS.NOMECLIAPROVOU) ORCAMENTOS) AS OS_NOME_CLIENTE_APROVOU,

  NVL(OS.ORC_SERV_BRUTO + OS.ORC_ITEM_BRUTO, 0) AS OS_TOTAL_ORCAMENTO,
  NVL(OS.AUTORIZADO_TODO_ORCAMENTO,'N') OS_ORCAMENTO_AUTORIZADO,
  ' ' AS OS_DATA_ORCAMENTO_APROVADO,
  
  
  
  GARANTIA_DOC.COD_SG_PADRAO AS OS_GARANTIA_SG_PADRAO,
  GARANTIA_DOC.COD_SG_EXEMPLO AS OS_GARANTIA_SG_EXEMPLO,
  
  
  
  NVL(OS.LAVAR_VEICULO,'N') AS OS_LAVAR_VEICULO,
  NVL(OS.CLIENTE_AGUARDOU,'N') AS OS_CLIENTE_AGUARDOU,
  
  NVL(OS_AGENDA.EH_RETORNO,'N') AS OS_EH_RETORNO,
  NVL(OS_AGENDA.EH_RECALL,'N') AS EH_RECALL,
     
   TO_CHAR(OS.DATA_AGENDADA_RECEPCAO, 'DD/MM/YYYY') AS DATA_AGENDAMENTO,
   
    
   TO_CHAR(OS.DATA_PROMETIDA_REVISADA, 'DD/MM/YYYY') AS DATA_PROMETIDA_REVISADA,
   TO_CHAR(OS.HORA_PROMETIDA_REVISADA, 'HH24:MI:SS') AS HORA_PROMETIDA_REVISADA,
   
  
  (SELECT EE.NOME_COMPLETO 
          FROM EMPRESAS_USUARIOS EE 
         WHERE EE.COD_EMPRESA = OS.COD_EMPRESA 
           AND EE.NOME = OS.QUEM_ABRIU) AS OS_NOME_AGENDADOR, 
       
   NVL(OS_AGENDA.EH_FIAT_PROFISSIONAL,'N') AS OS_FIAT_PROFISSIONAL,
   
   NVL(OS.PECA_USADA_FICA_CLIENTE,'N') AS OS_PECA_USADA_FICA_CLIENTE,
   
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'DD/MM/YYYY') AS OS_DATA_ULT_IMRESSAO,
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'HH24:MI:SS') AS OS_HORA_ULT_IMRESSAO,
   
   NVL(OS_AGENDA.VEICULO_PLATAFORMA,'N') AS OS_VEICULO_PLATAFORMA,
   
   TO_CHAR(OS_AGENDA.DATA_AGENDADA , 'HH24:MI') AS OS_HORA_AGENDADA,
   NVL(TO_CHAR(OS_AGENDA.DATA_AGENDADA, 'DD/MM/YYYY'),' ') AS OS_DATA_AGENDADA,
   
   TO_CHAR(OS_AGENDA.DATA_CONFIRMADA , 'HH24:MI') AS OS_HORA_CONFIRMADA,
   NVL(TO_CHAR(OS_AGENDA.DATA_CONFIRMADA, 'DD/MM/YYYY'),' ') AS OS_DATA_CONFIRMADA,
   
   (SELECT PB.DESCRICAO
          FROM PRISMA_BOX PB
         WHERE PB.PRISMA = OS_DADOS_VEICULOS.PRISMA) AS OS_DESCRICAO_PRISMA,
   KM_PROXIMA_REVISAO,
   DATA_PROXIMO_SERVICO,
    
  
  NVL(EMPRESAS_USUARIOS.NOME_COMPLETO, ' ')           AS OS_CONSULTOR_COMPLETO,
  EMPRESAS_USUARIOS.CODIGO_OPCIONAL AS OS_CODIGO_OPCIONAL,
  
  
  
  
  OS_TIPOS.DESCRICAO                         AS OS_TIPO_DESCRICAO,
  OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO              AS OS_TIPO_COM_DESCRICAO,
  NVL(OS_TIPOS.GARANTIA,'N')                        AS OS_GARANTIA,
  OS_TIPOS.REVISAO_GRATUITA                    AS OS_REVISAO_GRATUITA,
  NVL(OS_TIPOS.INTERNO,'N')          AS OS_INTERNO,
  OS_TIPOS.COD_CLIENTE               AS OS_CLIENTE_DO_TIPO,
  OS_TIPOS.OUTRO_CONCESSIONARIA                  AS OS_OUTRO_CONCESSIONARIA,
  NVL(OS_TIPOS.TIPO_FABRICA_EMPRESA,OS_TIPOS.TIPO_FABRICA)     AS OS_TIPO_FABRICA,
  
  
  NVL(OS_DADOS_VEICULOS.ANO, ' ')                      AS OS_ANO,
  OS_DADOS_VEICULOS.HORIMETRO                    AS OS_HORIMETRO,
  NVL(OS_DADOS_VEICULOS.PRISMA, ' ')                    AS OS_PRISMA,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2)     AS DIA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2)     AS MES_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4)   AS ANO_VENDA,
  NVL(OS_DADOS_VEICULOS.COMBUSTIVEL,0)                  AS OS_COMBUSTIVEL,
  NVL(OS_DADOS_VEICULOS.COR_EXTERNA, ' ')                  AS OS_COR_EXTERNA,
  NVL(OS_DADOS_VEICULOS.PLACA, ' ')                      AS OS_PLACA,
  NVL(OS_DADOS_VEICULOS.KM, 0)                      AS OS_KM,
  NVL(OS_DADOS_VEICULOS.CHASSI, ' ')                    AS OS_CHASSI,
  NVL(OS_DADOS_VEICULOS.NUMERO_MOTOR, ' ')                  AS OS_NUMERO_MOTOR,
  OS_DADOS_VEICULOS.NUMERO_RENAVAM                AS OS_NUMERO_RENAVAM,
  OS_DADOS_VEICULOS.SERIE                      AS OS_SERIE,
  OS_DADOS_VEICULOS.COD_CONCESSIONARIA              AS OS_COD_CONCESSIONARIA,
  OS_DADOS_VEICULOS.ESTADO_PINTURA                AS OS_ESTADO_PINTURA,
  NVL(OS_DADOS_VEICULOS.JOGO_FERRAMENTAS, ' ')                AS OS_JOGO_FERRAMENTAS,
  NVL(OS_DADOS_VEICULOS.ELASTICOS, ' ')                    AS OS_ELASTICOS,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_D, ' ')                AS OS_TAMPA_LATERAL_D,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_E, ' ')                AS OS_TAMPA_LATERAL_E,
  NVL(OS_DADOS_VEICULOS.FLANELA, ' ')                    AS OS_FLANELA,
  OS_DADOS_VEICULOS.NUMERO_FROTA                  AS OS_NUMERO_FROTA,
  
  
  NVL(CONCESSIONARIAS.NOME, ' ')                          AS CONCESSIONARIA_NOME,
  CONCESSIONARIAS.UF                            AS CONCESSIONARIA_UF,
  CONCESSIONARIAS.CIDADE                        AS CONCESSIONARIA_CIDADE,
  CONCESSIONARIAS.BAIRRO                        AS CONCESSIONARIA_BAIRRO,
  CONCESSIONARIAS.ENDERECO                      AS CONCESSIONARIA_RUA,
  CONCESSIONARIAS.CEP                           AS CONCESSIONARIA_CEP,
  CONCESSIONARIAS.CODIGO_PADRAO                   AS CONCESSIONARIA_CODIGO,
  UF_CONCESSIONARIA.DESCRICAO                   AS CONCESSIONARIA_ESTADO,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_FAB_BATERIA, 'DD/MM/YYYY'),' ')                AS OS_DATA_FAB_BATERIA,
  NVL(OS_DADOS_VEICULOS.COD_FAB_BATERIA, ' ')                               AS OS_COD_FAB_BATERIA,
  NVL((SELECT EMPRESAS_USUARIOS.NOME_COMPLETO
          FROM EMPRESAS_USUARIOS
         WHERE EMPRESAS_USUARIOS.NOME = CLIENTES_FROTA.NOME_VENDEDOR),' ') AS CONCESSIONARIA_VENDEDOR,
  
  
  
  PRODUTOS.DESCRICAO_PRODUTO  AS OS_DESCRICAO_PRODUTO,
  
  
  NVL(PRODUTOS.DESCRICAO_PRODUTO || ' / ' || PRODUTOS_MODELOS.DESCRICAO_MODELO,' ')   AS DESC_PROD_MOD,
  PRODUTOS_MODELOS.DESCRICAO_MODELO                      AS OS_DESCRICAO_MODELO,
  PRODUTOS_MODELOS.MOD_VER_SERIE                        AS OS_MOD_VER_SERIE,
  PRODUTOS_MODELOS.LINHA                            AS OS_LINHA,
  
  
  MARCAS.DESCRICAO_MARCA  AS OS_DESCRICAO_MARCA,
  
  
  CO.TEXTO_AIDF  AS OS_TEXTO_AIDF,
  
  
  NVL(EMPRESAS.NOME,' ')           AS EMPRESAS_NOME_EMPRESA,
  NVL(EMPRESAS.EMPRESA_NOME_COMPLETO,' ')     AS EMPRESAS_RAZAO_SOCIAL,

  NVL(EMPRESAS.CGC, ' ')                     AS EMPRESAS_CGC,
  EMPRESAS.FACHADA                  AS EMPRESAS_FACHADA,
  EMPRESAS.ESTADO                   AS UF_EMPRESA,
  NVL(EMPRESAS.CIDADE, ' ')                    AS EMPRESAS_CIDADE,
  EMPRESAS.BAIRRO                    AS EMPRESAS_BAIRRO,
  EMPRESAS.COMPLEMENTO                AS EMPRESAS_COMPLEMENTO,
  NVL(EMPRESAS.RUA, ' ')              AS EMPRESAS_RUA,
  NVL(EMPRESAS.FONE, ' ')                    AS EMPRESAS_FONE,
  NVL(EMPRESAS.FAX, ' ')                    AS EMPRESAS_FAX,
  NVL(EMPRESAS.CEP, ' ')                    AS EMPRESAS_CEP,
  EMPRESAS.INSCRICAO_MUNICIPAL            AS EMPRESAS_INSCRICAO_MUNICIPAL,
  EMPRESAS.INSCRICAO_SUBSTITUICAO            AS EMPRESAS_INSC_SUBSTITUICAO,
  NVL(EMPRESAS.ESTADO,' ') AS EMPRESA_UF,
  NVL(UF_EMPRESA.DESCRICAO, ' ')                  AS EMPRESA_ESTADO,
  NVL(EMPRESAS.INSCRICAO_ESTADUAL, ' ')              AS EMPRESAS_INSCRICAO_ESTADUAL,
  TRUNC(SYSDATE)                     AS EMPRESA_DATA_ATUAL,
  SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'),1, 5)       AS EMPRESA_HORA_ATUAL_STR,
  NVL(CLIENTE_EMPRESA.ENDERECO_ELETRONICO, ' ')         AS EMPRESA_EMAIL,
  NVL(CLIENTE_DIVERSO_EMPRESA.empresa_site, ' ') AS EMPRESA_SITE,
  
  
  
  
 
  CLIENTE_DIVERSO.COD_CLIENTE    AS CLIENTE_COD_CLIENTE,
  NVL(CLIENTE_DIVERSO.NOME, ' ')        AS CLIENTE_NOME,
  NVL(CLIENTE_DIVERSO.RG,' ')          AS CLIENTE_RG,

  
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_CEL || '-' || CLIENTES.TELEFONE_CEL),' ') AS CLIENTE_FONE_CEL,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_RES || '-' || CLIENTES.TELEFONE_RES),' ') AS CLIENTE_FONE_RES,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_COM || '-' || CLIENTES.TELEFONE_COM),' ') AS CLIENTE_FONE_COM,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_FAX|| '-' || CLIENTES.TELEFONE_FAX),' ') AS CLIENTE_FONE_FAX,


  
  
  
  CLIENTES.ENDERECO_ELETRONICO  AS CLIENTE_ENDERECO_ELETRONICO,
  CLIENTES.EMAIL_NFE AS CLIENTE_EMAIL_NFE,
  NVL(NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL),' ') AS CLIENTE_INSC_ESTAD,
  
  NVL(DECODE(CLIENTE_DIVERSO.CPF,' ','CGC: ' || NVL(CLIENTE_DIVERSO.CGC,' '),'CPF: ' || CLIENTE_DIVERSO.CPF), ' ') AS CLIENTE_CGC_CPF,
  
  CLIENTE_DIVERSO.CGC        AS CLIENTE_CGC,
  CLIENTE_DIVERSO.CPF        AS CLIENTE_CPF,
  CLIENTES.COD_CLASSE        AS CLIENTE_COD_CLASSE,
  
  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.ENDERECO  || ', ' || CLIENTE_DIVERSO.COMPLEMENTO || ' ' || CLIENTE_DIVERSO.BAIRRO))
    WHEN '2' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_RES  || ', ' ||CLIENTES.COMPLEMENTO_RES || ' ' || CLIENTES.BAIRRO_RES))
    WHEN '3' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COM  || ', ' ||CLIENTES.COMPLEMENTO_COM || ' ' || CLIENTES.BAIRRO_COM))
    WHEN '4' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COBRANCA  || ', ' ||CLIENTES.COMPLEMENTO_COBRANCA || ' ' || CLIENTES.BAIRRO_COBRANCA))
    WHEN '5' THEN TRIM(',' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.RUA  || ', ' ||ENDERECO_POR_INSCRICAO.COMPLEMENTO || ' ' || ENDERECO_POR_INSCRICAO.BAIRRO))
    ELSE ' '
    END  AS CLIENTE_ENDERECO_COMPLETO,
    

  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO))
    WHEN '2' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_RES  || '-' || CLIENTES.TELEFONE_RES))
    WHEN '3' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '4' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '5' THEN TRIM('-' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO  || '-' || ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO))
    ELSE ' '
    END  AS CLIENTE_TELEFONE_COMPLETO,
    

  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.UF,
              2, CLIENTES.UF_RES,
              3, CLIENTES.UF_COM,
              4, CLIENTES.UF_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.UF,
               ' ')                          AS  CLIENTE_UF,
  DECODE(OS.TIPO_ENDERECO, 1, UF_DIVERSO.DESCRICAO,
              2, UF_RES.DESCRICAO,
              3, UF_COM.DESCRICAO,
              4, UF_COBRANCA.DESCRICAO,
              5, UF_INSCRICAO.DESCRICAO,
               ' ')                          AS  CLIENTE_ESTADO,
  DECODE(OS.TIPO_ENDERECO, 1, CIDADES_DIV.DESCRICAO,
              2, CIDADES_RES.DESCRICAO,
              3, CIDADES_COM.DESCRICAO,
              4, CIDADES_COBRANCA.DESCRICAO,
              5, ENDERECO_POR_INSCRICAO.CIDADE,
               ' ')                          AS  CLIENTE_CIDADE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.BAIRRO,
              2, CLIENTES.BAIRRO_RES,
              3, CLIENTES.BAIRRO_COM,
              4, CLIENTES.BAIRRO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.BAIRRO,
               ' ')                          AS  CLIENTE_BAIRRO,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.CEP,
              2, CLIENTES.CEP_RES,
              3, CLIENTES.CEP_COM,
              4, CLIENTES.CEP_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.CEP,
               ' ')                          AS  CLIENTE_CEP,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.ENDERECO,
              2, CLIENTES.RUA_RES,
              3, CLIENTES.RUA_COM,
              4, CLIENTES.RUA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.RUA,
               ' ')                          AS  CLIENTE_RUA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.COMPLEMENTO,
              2, CLIENTES.COMPLEMENTO_RES,
              3, CLIENTES.COMPLEMENTO_COM,
              4, CLIENTES.COMPLEMENTO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.COMPLEMENTO,
               ' ')                          AS  CLIENTE_COMPLEMENTO,
  DECODE(OS.TIPO_ENDERECO, 1, ' ',
              2, CLIENTES.FACHADA_RES,
              3, CLIENTES.FACHADA_COM,
              4, CLIENTES.FACHADA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.FACHADA,
               ' ')                          AS  CLIENTE_FACHADA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.FONE_CONTATO,
              2, CLIENTES.TELEFONE_RES,
              3, CLIENTES.TELEFONE_COM,
              4, CLIENTES.TELEFONE_COM,
              5, ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_FONE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
              2, CLIENTES.PREFIXO_RES,
              3, CLIENTES.PREFIXO_COM,
              4, CLIENTES.PREFIXO_COM,
              5, ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_PREFIXO,
  
  
  
  NVL(FATURAR_CLIENTE_DIVERSO.NOME, ' ')                   AS FATURAR_NOME,
  
  NVL(TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO)),' ') AS FATURAR_FONE,
  
  NVL(UF_FATURAR.DESCRICAO, ' ')                       AS FATURAR_ESTADO,
  NVL(FATURAR_CIDADE.DESCRICAO, FATURAR_CLIENTE_DIVERSO.CIDADE)   AS FATURAR_CIDADE,

  NVL(TRIM(SUBSTR(CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.ENDERECO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.ENDERECO ELSE '' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.COMPLEMENTO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.COMPLEMENTO ELSE ' ' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.BAIRRO)>0 THEN '  ' || FATURAR_CLIENTE_DIVERSO.BAIRRO ELSE '' END
    , 2)), ' ')  AS FATURAR_ENDERECO,
   
   NVL(GREATEST(
    FATURAR_CLIENTE.PREFIXO_RES || '-' || FATURAR_CLIENTE.TELEFONE_RES,
    FATURAR_CLIENTE.PREFIXO_COM || '-' || FATURAR_CLIENTE.TELEFONE_COM,
    FATURAR_CLIENTE.PREFIXO_FAX || '-' || FATURAR_CLIENTE.TELEFONE_FAX
   ), ' ')   AS FATURAR_TELEFONE_FAX,
   
   
    NVL(FATURAR_CLIENTE.BAIRRO_COM,
    FATURAR_CLIENTE.BAIRRO_RES)   AS FATURAR_BAIRRO,
  
  NVL(FATURAR_CLIENTE.CEP_COM,
    FATURAR_CLIENTE.CEP_RES)   AS FATURAR_CEP,
  
  FATURAR_DADOS_JURIDICOS.CGC AS FATURAR_CGC,
  
  FATURAR_DADOS_JURIDICOS.INSC_ESTADUAL AS FATURAR_IE,
  
   
   
   
   NVL(CLIENTES_FROTA.MEDIA_KM_MENSAL,0) AS MEDIA_KM_MENSAL,
   
   

  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_TERCEIROS 
    ELSE 
    TOTAIS_SERVICOS.VAL_TERCEIROS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_TERCEIROS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_TERCEIROS,
      
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_LAVAGEM
    ELSE 
    TOTAIS_SERVICOS.VAL_LAVAGEM - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_LAVAGEM/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_LAVAGEM,
  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_REVISAO
    ELSE 
    TOTAIS_SERVICOS.VAL_REVISAO - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_REVISAO/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_REVISAO,
    
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_MECANICA
    ELSE 
    TOTAIS_SERVICOS.VAL_MECANICA - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_MECANICA/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_MECANICA,

  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_GERAIS
    ELSE 
    TOTAIS_SERVICOS.VAL_GERAIS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_GERAIS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_GERAIS,
   
  
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_PECAS
    ELSE 
    TOTAL_PECAS.VALOR_PECAS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_PECAS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_PECAS,
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_ACESSORIOS
    ELSE 
    TOTAL_PECAS.VALOR_ACESSORIOS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_ACESSORIOS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_ACESSORIOS, 
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_OUTROS
    ELSE 
    TOTAL_PECAS.VALOR_OUTROS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_OUTROS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_OUTROS, 

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_LUBRIFICANTE
    ELSE 
    TOTAL_PECAS.VALOR_LUBRIFICANTE - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_LUBRIFICANTE/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_LUBRIFICANTE,  

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_COMBUSTIVEL
    ELSE 
    TOTAL_PECAS.VALOR_COMBUSTIVEL - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_COMBUSTIVEL/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_COMBUSTIVEL,
	
	EMPRESA_LOGO.LOGO AS EMPRESA_LOGO,
   FABRICA_LOGO.LOGO AS FABRICA_LOGO,
   
   RELATORIO_TECNICO.RELATORIO AS  OS_RELATORIO_TECNICO,
   
   OS_AGENDA.SIGNATURE AS OS_ASSINATURA
   
    
    
  
 
FROM
  
  EMPRESAS,
  UF UF_EMPRESA,
  CLIENTES CLIENTE_EMPRESA,
  CLIENTE_DIVERSO CLIENTE_DIVERSO_EMPRESA,
  
  
  
  OS, EMPRESAS_USUARIOS, VW_OS_TIPOS OS_TIPOS, OS_DADOS_VEICULOS,
  CONCESSIONARIAS,UF UF_CONCESSIONARIA, PRODUTOS, PRODUTOS_MODELOS, MARCAS,
  CONTROLE_OS CO, PARM_SYS, OS_TIPOS_TERMO, OS_AGENDA, CLIENTES_FROTA, 
  GARANTIA_DOC,

  
  MOTORISTAS,
  
  
  CLIENTE_DIVERSO, CLIENTES,  ENDERECO_POR_INSCRICAO,
  CIDADES CIDADES_RES, CIDADES CIDADES_COM, CIDADES CIDADES_COBRANCA, CIDADES CIDADES_DIV,
  UF UF_DIVERSO, UF UF_RES, UF UF_COM, UF UF_COBRANCA, UF UF_INSCRICAO,
  
  
  CLIENTE_DIVERSO FATURAR_CLIENTE_DIVERSO, CLIENTES FATURAR_CLIENTE, CIDADES FATURAR_CIDADE, 
   UF UF_FATURAR, DADOS_JURIDICOS FATURAR_DADOS_JURIDICOS,
   
   
  (SELECT
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'T', TOTAIS.PRECO, 0)) AS VAL_TERCEIROS,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'L', TOTAIS.PRECO, 0)) AS VAL_LAVAGEM,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'R', TOTAIS.PRECO, 0)) AS VAL_REVISAO,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'M', TOTAIS.PRECO, 0)) AS VAL_MECANICA,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'G', TOTAIS.PRECO, 0)) AS VAL_GERAIS
  FROM
  (SELECT
  DECODE(S.TERCEIROS, 'S',       'T',
    DECODE(S.LAVAGEM, 'S',       'L',
    DECODE(SS.TIPO_SETOR, 'R', 'R',
                'M', 'M',
                   'G'))) AS TIPO_SERVICO,
  OSS.PRECO_VENDA + NVL((SELECT SUM(PRECO_VENDA) FROM OS_SERVICOS_ADICIONAIS ADI
               WHERE ADI.COD_EMPRESA = OSS.COD_EMPRESA
                 AND ADI.NUMERO_OS   = OSS.NUMERO_OS
                 AND ADI.ITEM        = OSS.ITEM
                 AND ADI.COD_SERVICO = OSS.COD_SERVICO), 0) AS PRECO
  FROM OS_SERVICOS OSS, SERVICOS S, SERVICOS_SETORES SS
  WHERE 1=1
  AND OSS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OSS.NUMERO_OS = $P{NUMERO_OS}
  AND OSS.COD_SERVICO = S.COD_SERVICO
  AND S.COD_SETOR = SS.COD_SETOR) TOTAIS ) TOTAIS_SERVICOS,
  
  
  
  (SELECT
  SUM(DECODE(CLASSE_PECA, 1, VALOR, 0)) AS VALOR_PECAS,
  SUM(DECODE(CLASSE_PECA, 2, VALOR, 0)) AS VALOR_ACESSORIOS,
  SUM(DECODE(NVL(CLASSE_PECA, 3), 3, VALOR, 0)) AS VALOR_OUTROS,
  SUM(DECODE(CLASSE_PECA, 4, VALOR, 0)) AS VALOR_LUBRIFICANTE,
  SUM(DECODE(CLASSE_PECA, 5, VALOR, 0)) AS VALOR_COMBUSTIVEL
  FROM (
  SELECT DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1), 
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4)) AS CLASSE_PECA,
  SUM(
  OS_REQUISICOES.QUANTIDADE *
  DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
        ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                    DECODE(ITENS.COD_TRIBUTACAO, '1',
                    DECODE(PARM_SYS.REGIME_ICMS, 'S',
                      DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                      DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                    0),
                         0),
                         OS_TIPOS.AUMENTO_PRECO_PECA),
                       0),
                     OS_TIPOS.AUMENTO_PRECO_PECA)) *
           DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                            'G', OS_REQUISICOES.PRECO_GARANTIA,
                            'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                            'P', OS_REQUISICOES.PRECO_FABRICA,
                            DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
          ) / 100,
     DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                        OS_REQUISICOES.PRECO_GARANTIA),
       DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
       DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
         ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
        DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                            OS_REQUISICOES.PRECO_VENDA))/100))))))) AS VALOR
  FROM OS_REQUISICOES, ITENS, ITENS_FORNECEDOR, OS, VW_OS_TIPOS OS_TIPOS, ITENS_CLASSE_CONTABIL ICC , SEGURADORA,
  PARM_SYS, PARM_SYS2, ITENS_GRUPO_INTERNO IGI, OS_TIPOS_EMPRESAS OTE
  WHERE OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
  AND OS.NUMERO_OS = OS_REQUISICOES.NUMERO_OS  
  AND OS.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA 
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS.TIPO = OS_TIPOS.TIPO
  AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND ITENS.COD_GRUPO_INTERNO = IGI.COD_GRUPO_INTERNO(+)
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
  AND OS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OS.NUMERO_OS = $P{NUMERO_OS}
  GROUP BY DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4))
  ) TOTAL) TOTAL_PECAS,
  
  (SELECT CE.KM AS KM_PROXIMA_REVISAO,NVL(C1.DATA_EVENTO, C1.DATA_NOVO_CONTATO) AS DATA_PROXIMO_SERVICO
    FROM OS_DADOS_VEICULOS, CRM_EVENTOS C1, CRM_CICLO_EVENTOS CE
    WHERE C1.COD_CICLO (+) > 0
    AND C1.DATA_EVENTO (+) > SYSDATE - 1
    AND C1.STATUS (+) IN ('P', 'A')
    AND C1.COD_CICLO = CE.COD_CICLO(+)
    AND C1.COD_TIPO_EVENTO = CE.COD_TIPO_EVENTO(+)
    AND NVL(CE.KM (+),0) > 0
    AND OS_DADOS_VEICULOS.CHASSI = C1.VEIC_CHASSI_COMPLETO (+)
    AND OS_DADOS_VEICULOS.NUMERO_OS = $P{NUMERO_OS}
    AND OS_DADOS_VEICULOS.COD_EMPRESA = $P{COD_EMPRESA}
    AND ROWNUM = 1
    ORDER BY DATA_PROXIMO_SERVICO) CRM_EVENTOS,

  (SELECT EL.COD_EMPRESA, EL.LOGO AS LOGO
  FROM EMPRESA_LOGO EL) EMPRESA_LOGO,
        
    (SELECT FL.COD_EMPRESA, FL.LOGO AS LOGO
     FROM FABRICA_LOGO FL) FABRICA_LOGO,
	
	 
  (SELECT OS_RELATORIO.RELATORIO, OS_RELATORIO.NUMERO_OS, OS_RELATORIO.COD_EMPRESA
  FROM OS_RELATORIO ) RELATORIO_TECNICO


WHERE   1 = 1
    
    
    AND OS.COD_EMPRESA = $P{COD_EMPRESA} 
    AND OS.NUMERO_OS = $P{NUMERO_OS}
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'

    AND OS.NOME = EMPRESAS_USUARIOS.NOME
  
  AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
  AND OS.COD_OS_AGENDA = OS_AGENDA.COD_OS_AGENDA (+)
  
  AND OS.COD_EMPRESA = PARM_SYS.COD_EMPRESA (+)
  
  AND OS.TIPO = OS_TIPOS_TERMO.TIPO (+)
    
  AND OS.COD_EMPRESA =OS_TIPOS_TERMO.COD_EMPRESA (+)
  
  
  
    AND OS.TIPO = OS_TIPOS.TIPO
    AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA (+)
    
    AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS (+)
    AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA (+)
    
    AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA = CONCESSIONARIAS.COD_CONCESSIONARIA (+)
    
    
    AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF (+)
    
    AND OS.COD_CLIENTE = CLIENTES_FROTA.COD_CLIENTE (+)
    AND OS.COD_PRODUTO = CLIENTES_FROTA.COD_PRODUTO (+)
    AND OS.COD_MODELO = CLIENTES_FROTA.COD_MODELO (+)
    AND OS_DADOS_VEICULOS.CHASSI = CLIENTES_FROTA.CHASSI
    
    AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO 
    
    AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
    
    AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
    AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
    
   
    AND OS.COD_EMPRESA = CO.COD_EMPRESA(+)
  
  AND OS.COD_DOCUMENTO = GARANTIA_DOC.COD_DOCUMENTO (+)
  
  
   AND OS.CODIGO_MOTORISTA = MOTORISTAS.CODIGO_MOTORISTA (+)
 
    
    
    AND OS.COD_EMPRESA = EMPRESAS.COD_EMPRESA
    AND EMPRESAS.ESTADO = UF_EMPRESA.UF (+)
    AND EMPRESAS.COD_CLIENTE = CLIENTE_EMPRESA.COD_CLIENTE (+)
  AND EMPRESAS.COD_CLIENTE = CLIENTE_DIVERSO_EMPRESA.COD_CLIENTE (+)
    
    
    AND OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES (+)
    AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF (+)
    AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES (+)
    AND OS.INSCRICAO_ESTADUAL = ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL (+)
    AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE (+)
    AND CLIENTES.UF_RES = UF_RES.UF (+)
    AND CLIENTES.UF_COM = UF_COM.UF (+)
    AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF (+)
    AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF (+)
    
    
  AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_CLIENTE.COD_CLIENTE (+)
  AND FATURAR_CLIENTE_DIVERSO.COD_CIDADES =FATURAR_CIDADE.COD_CIDADES (+)
  AND FATURAR_CLIENTE_DIVERSO.UF = UF_FATURAR.UF (+)
  AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_DADOS_JURIDICOS.COD_CLIENTE (+)
  AND OS.COD_CLIENTE = FATURAR_CLIENTE_DIVERSO.COD_CLIENTE (+)

  
   AND OS.COD_EMPRESA = EMPRESA_LOGO.COD_EMPRESA (+)
   AND OS.COD_EMPRESA = FABRICA_LOGO.COD_EMPRESA (+)
   
   
   AND OS.COD_EMPRESA = RELATORIO_TECNICO.NUMERO_OS (+)
   AND OS.COD_EMPRESA = RELATORIO_TECNICO.COD_EMPRESA (+)]]>
	</queryString>
	<field name="OS_TIPO_TOYOTA" class="java.lang.String"/>
	<field name="OS_VALOR_PACOTE_PRE_PAGO" class="java.lang.Double"/>
	<field name="OS_COD_EMPRESA" class="java.lang.Double"/>
	<field name="OS_STATUS_OS" class="java.lang.Double"/>
	<field name="OS_COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="OS_ABS_OSNUM" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS_FABRICA" class="java.lang.Double"/>
	<field name="OS_COD_CLIENTE" class="java.lang.Double"/>
	<field name="OS_CLIENTE_RAPIDO" class="java.lang.String"/>
	<field name="OS_TIPO_ENDERECO" class="java.lang.String"/>
	<field name="OS_OBSERVACAO" class="java.lang.String"/>
	<field name="OS_EXTENDIDA" class="java.lang.String"/>
	<field name="OS_SEGURADORA" class="java.lang.String"/>
	<field name="OS_DATA_EMISSAO" class="java.lang.String"/>
	<field name="OS_DATA_LIBERADO" class="java.lang.String"/>
	<field name="OS_DIA_EMISSAO" class="java.lang.String"/>
	<field name="OS_MES_EMISSAO" class="java.lang.String"/>
	<field name="OS_ANO_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_ENCERRADA" class="java.lang.String"/>
	<field name="OS_DATA_ENCERRADA" class="java.sql.Timestamp"/>
	<field name="OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_HORA24_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MINUTO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DATA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DIA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MES_PROMETIDA" class="java.lang.String"/>
	<field name="OS_ANO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_BRUTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_DESCONTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="OS_COD_SEGURADORA" class="java.lang.Double"/>
	<field name="OS_TIPO" class="java.lang.String"/>
	<field name="CONSULTOR" class="java.lang.String"/>
	<field name="OS_OS_ENTRADA" class="java.lang.Double"/>
	<field name="OS_VIA" class="java.lang.String"/>
	<field name="OS_TOTAL_IMPRESSAO_FABRICA" class="java.lang.Double"/>
	<field name="OS_TERMO_TEXTO" class="java.lang.String"/>
	<field name="OS_DATA_TAXI" class="java.lang.String"/>
	<field name="OS_HORA_TAXI" class="java.lang.String"/>
	<field name="OS_TODO_TRABALHO_FEITO" class="java.lang.String"/>
	<field name="OS_TODO_LIMPO" class="java.lang.String"/>
	<field name="OS_PRIMEIRA_RODAGEM" class="java.lang.String"/>
	<field name="OS_SEGUNDA_RODAGEM" class="java.lang.String"/>
	<field name="OS_DATA_PRIMEIRA_RODAGEM" class="java.lang.String"/>
	<field name="OS_DATA_SEGUNDA_RODAGEM" class="java.lang.String"/>
	<field name="OS_PRIM_RESPONSAVEL_RODAGEM" class="java.lang.String"/>
	<field name="OS_SEG_RESPONSAVEL_RODAGEM" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PAGAMENTO" class="java.lang.String"/>
	<field name="MOTORISTA_NOME" class="java.lang.String"/>
	<field name="MOTORISTA_DOCUMENTO" class="java.lang.String"/>
	<field name="OS_NUMERO_ORCAMENTO" class="java.lang.String"/>
	<field name="OS_NOME_CLIENTE_APROVOU" class="java.lang.String"/>
	<field name="OS_TOTAL_ORCAMENTO" class="java.lang.Double"/>
	<field name="OS_ORCAMENTO_AUTORIZADO" class="java.lang.String"/>
	<field name="OS_DATA_ORCAMENTO_APROVADO" class="java.lang.String"/>
	<field name="OS_GARANTIA_SG_PADRAO" class="java.lang.String"/>
	<field name="OS_GARANTIA_SG_EXEMPLO" class="java.lang.Double"/>
	<field name="OS_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="OS_CLIENTE_AGUARDOU" class="java.lang.String"/>
	<field name="OS_EH_RETORNO" class="java.lang.String"/>
	<field name="EH_RECALL" class="java.lang.String"/>
	<field name="DATA_AGENDAMENTO" class="java.lang.String"/>
	<field name="DATA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="OS_NOME_AGENDADOR" class="java.lang.String"/>
	<field name="OS_FIAT_PROFISSIONAL" class="java.lang.String"/>
	<field name="OS_PECA_USADA_FICA_CLIENTE" class="java.lang.String"/>
	<field name="OS_DATA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_HORA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_VEICULO_PLATAFORMA" class="java.lang.String"/>
	<field name="OS_HORA_AGENDADA" class="java.lang.String"/>
	<field name="OS_DATA_AGENDADA" class="java.lang.String"/>
	<field name="OS_HORA_CONFIRMADA" class="java.lang.String"/>
	<field name="OS_DATA_CONFIRMADA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRISMA" class="java.lang.String"/>
	<field name="KM_PROXIMA_REVISAO" class="java.lang.Double"/>
	<field name="DATA_PROXIMO_SERVICO" class="java.sql.Timestamp"/>
	<field name="OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="OS_CODIGO_OPCIONAL" class="java.lang.String"/>
	<field name="OS_TIPO_DESCRICAO" class="java.lang.String"/>
	<field name="OS_TIPO_COM_DESCRICAO" class="java.lang.String"/>
	<field name="OS_GARANTIA" class="java.lang.String"/>
	<field name="OS_REVISAO_GRATUITA" class="java.lang.String"/>
	<field name="OS_INTERNO" class="java.lang.String"/>
	<field name="OS_CLIENTE_DO_TIPO" class="java.lang.Double"/>
	<field name="OS_OUTRO_CONCESSIONARIA" class="java.lang.String"/>
	<field name="OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="OS_ANO" class="java.lang.String"/>
	<field name="OS_HORIMETRO" class="java.lang.Double"/>
	<field name="OS_PRISMA" class="java.lang.String"/>
	<field name="OS_DATA_VENDA" class="java.lang.String"/>
	<field name="DIA_VENDA" class="java.lang.String"/>
	<field name="MES_VENDA" class="java.lang.String"/>
	<field name="ANO_VENDA" class="java.lang.String"/>
	<field name="OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="OS_PLACA" class="java.lang.String"/>
	<field name="OS_KM" class="java.lang.Double"/>
	<field name="OS_CHASSI" class="java.lang.String"/>
	<field name="OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="OS_NUMERO_RENAVAM" class="java.lang.String"/>
	<field name="OS_SERIE" class="java.lang.String"/>
	<field name="OS_COD_CONCESSIONARIA" class="java.lang.Double"/>
	<field name="OS_ESTADO_PINTURA" class="java.lang.String"/>
	<field name="OS_JOGO_FERRAMENTAS" class="java.lang.String"/>
	<field name="OS_ELASTICOS" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_D" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_E" class="java.lang.String"/>
	<field name="OS_FLANELA" class="java.lang.String"/>
	<field name="OS_NUMERO_FROTA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="CONCESSIONARIA_UF" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CIDADE" class="java.lang.String"/>
	<field name="CONCESSIONARIA_BAIRRO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_RUA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CEP" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CODIGO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_ESTADO" class="java.lang.String"/>
	<field name="OS_DATA_FAB_BATERIA" class="java.lang.String"/>
	<field name="OS_COD_FAB_BATERIA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_VENDEDOR" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRODUTO" class="java.lang.String"/>
	<field name="DESC_PROD_MOD" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="OS_MOD_VER_SERIE" class="java.lang.String"/>
	<field name="OS_LINHA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MARCA" class="java.lang.String"/>
	<field name="OS_TEXTO_AIDF" class="java.lang.String"/>
	<field name="EMPRESAS_NOME_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_RAZAO_SOCIAL" class="java.lang.String"/>
	<field name="EMPRESAS_CGC" class="java.lang.String"/>
	<field name="EMPRESAS_FACHADA" class="java.lang.String"/>
	<field name="UF_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_CIDADE" class="java.lang.String"/>
	<field name="EMPRESAS_BAIRRO" class="java.lang.String"/>
	<field name="EMPRESAS_COMPLEMENTO" class="java.lang.String"/>
	<field name="EMPRESAS_RUA" class="java.lang.String"/>
	<field name="EMPRESAS_FONE" class="java.lang.String"/>
	<field name="EMPRESAS_FAX" class="java.lang.String"/>
	<field name="EMPRESAS_CEP" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_MUNICIPAL" class="java.lang.String"/>
	<field name="EMPRESAS_INSC_SUBSTITUICAO" class="java.lang.String"/>
	<field name="EMPRESA_UF" class="java.lang.String"/>
	<field name="EMPRESA_ESTADO" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="EMPRESA_DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="EMPRESA_HORA_ATUAL_STR" class="java.lang.String"/>
	<field name="EMPRESA_EMAIL" class="java.lang.String"/>
	<field name="EMPRESA_SITE" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLIENTE" class="java.lang.Double"/>
	<field name="CLIENTE_NOME" class="java.lang.String"/>
	<field name="CLIENTE_RG" class="java.lang.String"/>
	<field name="CLIENTE_FONE_CEL" class="java.lang.String"/>
	<field name="CLIENTE_FONE_RES" class="java.lang.String"/>
	<field name="CLIENTE_FONE_COM" class="java.lang.String"/>
	<field name="CLIENTE_FONE_FAX" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="CLIENTE_EMAIL_NFE" class="java.lang.String"/>
	<field name="CLIENTE_INSC_ESTAD" class="java.lang.String"/>
	<field name="CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="CLIENTE_CGC" class="java.lang.String"/>
	<field name="CLIENTE_CPF" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLASSE" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_UF" class="java.lang.String"/>
	<field name="CLIENTE_ESTADO" class="java.lang.String"/>
	<field name="CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="CLIENTE_CEP" class="java.lang.String"/>
	<field name="CLIENTE_RUA" class="java.lang.String"/>
	<field name="CLIENTE_COMPLEMENTO" class="java.lang.String"/>
	<field name="CLIENTE_FACHADA" class="java.lang.String"/>
	<field name="CLIENTE_FONE" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO" class="java.lang.String"/>
	<field name="FATURAR_NOME" class="java.lang.String"/>
	<field name="FATURAR_FONE" class="java.lang.String"/>
	<field name="FATURAR_ESTADO" class="java.lang.String"/>
	<field name="FATURAR_CIDADE" class="java.lang.String"/>
	<field name="FATURAR_ENDERECO" class="java.lang.String"/>
	<field name="FATURAR_TELEFONE_FAX" class="java.lang.String"/>
	<field name="FATURAR_BAIRRO" class="java.lang.String"/>
	<field name="FATURAR_CEP" class="java.lang.String"/>
	<field name="FATURAR_CGC" class="java.lang.String"/>
	<field name="FATURAR_IE" class="java.lang.String"/>
	<field name="MEDIA_KM_MENSAL" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_TERCEIROS" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_LAVAGEM" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_REVISAO" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_MECANICA" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_GERAIS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_PECAS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_ACESSORIOS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_OUTROS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_LUBRIFICANTE" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="EMPRESA_LOGO" class="java.awt.Image"/>
	<field name="FABRICA_LOGO" class="java.awt.Image"/>
	<field name="OS_RELATORIO_TECNICO" class="java.lang.String"/>
	<field name="OS_ASSINATURA" class="java.awt.Image"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="47">
			<frame>
				<reportElement x="0" y="0" width="555" height="47" uuid="4aff9f8d-4c18-4b3b-9182-5a20fd1248be"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image scaleImage="RealHeight" isUsingCache="true">
					<reportElement x="2" y="1" width="144" height="31" uuid="c38712d0-97da-4f6b-b7c0-643abfa0f11f">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600465.png"]]></imageExpression>
				</image>
				<image scaleImage="FillFrame" hAlign="Center" vAlign="Middle" isUsingCache="true">
					<reportElement x="405" y="1" width="146" height="31" uuid="92ff027c-07c2-4bfb-9739-823db5946bef">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{EMPRESA_LOGO}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="219" y="19" width="46" height="11" uuid="a578a9d4-6108-4200-9a53-0221355372bd"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Impressão:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="264" y="19" width="75" height="11" uuid="5f56c66d-d113-4d9c-8079-ba440697acf3"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESA_HORA_ATUAL_STR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="297" y="4" width="48" height="12" uuid="f18d3ad3-a8b4-4054-9a48-9a5116fce77c"/>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="213" y="4" width="84" height="12" uuid="ec654b4f-b9ac-49fb-b085-966c4f964594"/>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Ordem de Serviço:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="353" y="8" width="15" height="16" uuid="70fd1d74-96f1-4ebc-8983-1dd7b589dff4">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[1]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="368" y="8" width="15" height="16" uuid="c606bbc9-3230-4105-a0ce-db51d221f2dc"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[2]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="383" y="8" width="15" height="16" uuid="2f50a0b3-6894-4c46-9030-caa88c686cd8"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[3]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="405" y="33" width="138" height="11" uuid="5c000c68-3d9c-4d90-9deb-adbbd8b388fe">
						<printWhenExpression><![CDATA[Boolean.FALSE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Nº Doc. Fiscal.:___________________]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="32" width="79" height="10" uuid="23f2fdf7-0849-4426-9dec-07196f2fa2a4">
						<printWhenExpression><![CDATA[Boolean.FALSE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Nº do Documento:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="81" y="32" width="107" height="11" uuid="45bb96b0-f5a5-4976-9fac-a6b61a00a852">
						<printWhenExpression><![CDATA[Boolean.FALSE]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="193" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="86" uuid="3b56afd3-3dce-404f-8161-5d9b2c264620"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="271" y="65" width="276" height="10" uuid="16119d50-1eb3-4ba5-a907-e8553fc305d6"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="206" y="2" width="60" height="11" uuid="9700a994-1f9b-45e2-8dde-2aabef9725ea"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="271" y="75" width="194" height="11" uuid="0420f38e-2dc2-4d97-ba4d-287e5f510d4c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESC_PROD_MOD}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="465" y="75" width="79" height="11" uuid="39c203d2-d542-4871-9432-4cef133c0183"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="2" y="2" width="21" height="11" uuid="4ff50b3c-6273-4699-b9fb-f41f2efca48d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="24" y="2" width="22" height="11" uuid="2265238a-6b03-404f-8c03-d2f3b47409fa"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TIPO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="52" y="2" width="57" height="11" uuid="53a00809-5d78-47ce-b57d-a3d9fe127eba"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Relacionadas:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="109" y="2" width="96" height="11" uuid="3b2909f4-5b8f-44ff-837f-f8ae54c05040"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="271" y="2" width="39" height="10" uuid="da56f3ed-456b-4d29-9083-d4e1408a5546"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Entrada]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="348" y="2" width="45" height="10" uuid="076ed1e5-40d6-4eed-93dd-f7d6d499e217"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[C.Tecnico]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="397" y="2" width="29" height="10" uuid="326bf378-e914-4f8e-9849-d21dbcf51b3f"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Prisma]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="428" y="2" width="41" height="10" uuid="cc3bed9c-129d-4f04-893d-e33f0a991073"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Placa]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="470" y="2" width="45" height="10" uuid="2fd79605-2c46-4a82-9799-5ca1dbb6e90f"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Prometida]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="516" y="2" width="39" height="10" uuid="c6152f3f-1ba2-4d03-b92f-b3619f3d4cf6"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Revisada]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="11" width="154" height="11" uuid="3e983f55-2fdb-4e80-ac38-a3d133338f80"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[1º Contato com o cliente veiculo pronto.]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="271" y="13" width="40" height="10" uuid="d7b144c7-2bea-48d8-a7db-f48888f81ab1"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="270" y="22" width="41" height="10" uuid="e90e56cf-6a54-4bce-811e-89df6b25d6e2"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_HORA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="428" y="13" width="41" height="10" uuid="24a54bb6-af58-48b0-b055-211211ce5851"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="396" y="13" width="29" height="10" uuid="92ba051f-813d-41b5-b9ff-cc509e51a341"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PRISMA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="347" y="13" width="48" height="10" uuid="51743c32-ee34-416b-b29f-b1e9576bd609"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONSULTOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="470" y="13" width="45" height="10" uuid="eea13b0e-f406-47b3-aa16-586429992cfd"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="470" y="22" width="45" height="10" uuid="d1a0e2f6-705a-43b8-9e80-9d8c46796e35"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_HORA_PROMETIDA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="270" y="1" width="1" height="86" uuid="ec61d230-d85a-4182-aa80-f5715a4a8769"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="163" y="13" width="67" height="11" uuid="72a8ed83-58de-4605-8402-2d8c42aea916"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[___/___/______]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="233" y="13" width="34" height="11" uuid="d75322c2-4fc6-4940-af5d-935351a21a65"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[___:___]]></text>
				</staticText>
				<line>
					<reportElement x="270" y="32" width="284" height="1" uuid="7e506aad-114f-4317-a52f-194a0ecd8553"/>
				</line>
				<line>
					<reportElement x="394" y="1" width="1" height="31" uuid="3c42ccec-1167-4806-8f63-4cfcccdf93ab"/>
				</line>
				<line>
					<reportElement x="426" y="1" width="1" height="31" uuid="d184b948-3993-499c-88db-41262c9762ae"/>
				</line>
				<line>
					<reportElement x="468" y="1" width="1" height="31" uuid="648bb042-d738-410d-9824-19465733646d"/>
				</line>
				<line>
					<reportElement x="515" y="1" width="1" height="31" uuid="2e2d657b-8ae4-4a92-afa4-d65311afb358"/>
				</line>
				<line>
					<reportElement x="310" y="1" width="1" height="31" uuid="4ff94ab5-39df-4ad5-be33-5844d707e514"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="21" width="154" height="11" uuid="79f299c1-0f37-4b59-ad8a-0cca3b67fa16"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[2º Contato com o cliente veiculo pronto.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="163" y="22" width="67" height="11" uuid="2711d7d8-a3a6-472f-a8e8-b9b9ee60ab2b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[___/___/______]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="233" y="22" width="34" height="11" uuid="86b648db-0f13-4cfe-aba1-06bc62ce517b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[___:___]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="30" width="97" height="10" uuid="39e973d6-9ccc-4fc7-ab13-e4b77c62153c"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Forma de Pagamento]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="39" width="85" height="11" uuid="780f5bed-6234-4f16-8cdd-2cea94b89c61"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Boleto Bancário]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="122" y="39" width="39" height="11" uuid="0abd73e6-d827-4ad0-9772-d5f82e5ce8d0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Dinheiro]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="122" y="48" width="39" height="11" uuid="8b421076-b577-4f02-a2ca-fbcc4d8308c6"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cartão]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="203" y="39" width="37" height="11" uuid="147f2c6d-034b-47a1-a08c-2687a9d6f374"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cheque]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="48" width="85" height="11" uuid="095b0855-d243-4ea7-af01-85c2626a5f88"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Contra Apresentação]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="203" y="48" width="37" height="11" uuid="65cfecd0-1f7c-4e1c-9834-8232becd346f"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Outros]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="162" y="41" width="7" height="7" uuid="965ded41-7241-4ac8-bcde-33472dfb6384"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="162" y="50" width="7" height="7" uuid="c42b3767-25d5-4998-b040-ed5f49130a53"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="240" y="42" width="7" height="7" uuid="c3f12680-cf01-49e6-b3fd-f90dd862f3fa"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="240" y="51" width="7" height="7" uuid="b7855f71-56e6-4429-8abf-92b24b7ad5f1"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="89" y="41" width="7" height="7" uuid="be71a038-3208-46ac-b21f-f3456c7a9360"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="89" y="50" width="7" height="7" uuid="52c76b21-3f1e-4407-9d9c-1c469d4695f6"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="271" y="34" width="72" height="11" uuid="cefb3ac3-32d0-44be-a06b-4844171fe0a1"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente Agendado]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="361" y="34" width="64" height="11" uuid="55ad44d3-149a-45ef-820e-98d7b170ac99"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Man. Espressa]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="361" y="44" width="64" height="11" uuid="b2cc4854-f9a0-41a7-9edf-1e670d51e864"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Lavar o veiculo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="441" y="34" width="100" height="11" uuid="c63c837a-7e9e-4278-8f36-031d4640e50d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Reter Peças Subistituidas]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="271" y="44" width="72" height="11" uuid="898b7b29-fb34-4f2b-8535-b7a6f7238824"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente Aguarda]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="441" y="44" width="100" height="11" uuid="1bfce134-46c9-4d6b-a444-81f0ef101652"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Plataforma Leva e Trás]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="429" y="45" width="7" height="7" uuid="a430f4ce-aaaa-4d4d-9e9e-9bbe9118d14c"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_LAVAR_VEICULO} == "S" ?  "X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="542" y="35" width="7" height="7" uuid="dcabedf8-7420-4b7c-a305-0c2883e0cff3"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PECA_USADA_FICA_CLIENTE} == "N" ? "X":" "]]></textFieldExpression>
				</textField>
				<textField pattern="">
					<reportElement mode="Transparent" x="542" y="45" width="7" height="7" uuid="e5d46866-1c92-4bdf-ad1b-484ecbefd179"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="361" y="53" width="64" height="11" uuid="c378c68f-39d6-42e9-a5e8-95eac41c0e33"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Serv. Terceiros]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="271" y="53" width="72" height="11" uuid="72fd7d9d-5c25-49d0-abd7-d968aaa1044b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente Retira]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="441" y="53" width="100" height="11" uuid="586b592f-15fe-4c61-a970-118f5198e140"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Retorno]]></text>
				</staticText>
				<textField pattern="">
					<reportElement mode="Transparent" x="429" y="55" width="7" height="7" uuid="d16ddb02-0cf4-4136-8fb9-1744bfd0a5f7"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="542" y="55" width="7" height="7" uuid="9b34572c-b9ed-4897-a1c3-2c69df3a3867"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_EH_RETORNO} == "S" ? "X":" "]]></textFieldExpression>
				</textField>
				<textField pattern="">
					<reportElement mode="Transparent" x="344" y="56" width="7" height="7" uuid="cc88c8a8-761e-402a-936f-dbdcec35ddd6"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="2" y="62" width="70" height="11" uuid="cd214332-e338-45be-a77a-22440af19361"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Valor Estimado R$]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="117" y="62" width="96" height="11" uuid="79a59a15-bf2c-4a24-af52-6e485ee8ea81"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Revisão do Orçamento R$]]></text>
				</staticText>
				<line>
					<reportElement x="213" y="72" width="51" height="1" uuid="c7da2370-283c-467f-9743-c76e7514c464"/>
				</line>
				<line>
					<reportElement x="72" y="72" width="41" height="1" uuid="a4f030a3-12d5-454e-a9eb-baaec7a5eb1d"/>
				</line>
				<line>
					<reportElement x="56" y="83" width="66" height="1" uuid="6e12d460-e379-486e-b486-21705c100bca"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="73" width="54" height="11" uuid="fc526602-3db2-48b5-9f4a-1ab4fc4c381a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Autorizado por]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="142" y="73" width="67" height="11" uuid="0470a712-9ae3-47ab-89d0-c3628b497977"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[___/___/______]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="229" y="73" width="36" height="11" uuid="7af77f36-688f-4cf2-83d9-5e4e88d06537"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[___:___]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="123" y="73" width="19" height="11" uuid="907d9f6c-65b0-42b3-930b-064be8cfd0e0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="209" y="73" width="20" height="11" uuid="b85d8d03-82f9-4c0a-a234-f8efd2e59ecd"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Hora]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="429" y="35" width="7" height="7" uuid="ca2d16ef-01d9-4763-9755-7f3c1b03c73d"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TIPO_TOYOTA} == "4" ? "X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="344" y="46" width="7" height="7" uuid="31a772b4-7057-4ef0-97b9-1274635a66ef"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_CLIENTE_AGUARDOU} == "S" ? "X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="344" y="35" width="7" height="7" uuid="32334b9d-4011-4ca5-b8d1-b97f0477c793"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COD_OS_AGENDA} == null ? " ": "X"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="346" y="1" width="1" height="31" uuid="3a80293e-370f-4013-a7d3-1586657f3095"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="312" y="2" width="33" height="10" uuid="092b2cff-1fcc-4f6c-819c-3af530dbcc73"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Agenda]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="309" y="13" width="38" height="10" uuid="fcc729ec-30a8-49e0-be32-f65645a0a90c"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_CONFIRMADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="310" y="23" width="36" height="10" uuid="9f5b0c78-7915-4978-b6d9-49edf876cd30"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_HORA_CONFIRMADA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="86" width="555" height="46" uuid="64ebd51b-8867-4cf3-9f2f-d9111d7c7c05"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="1" y="0" width="36" height="10" uuid="9a03c69b-45be-4f58-9820-4b0f3a53bf8e"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cliente:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="37" y="0" width="295" height="11" uuid="1fc145cb-cbdd-40bc-a88b-26fa1cc98b1b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="37" y="11" width="270" height="11" uuid="1ea2a828-1326-4125-ad65-2bb5fa6b7170"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_COMPLETO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="66" y="22" width="266" height="11" uuid="b0b81203-8a0c-454b-bd51-bf886f1295bc"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="37" y="33" width="190" height="11" uuid="ec46f197-966e-4164-887e-f0fe360caada"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="245" y="33" width="16" height="11" uuid="c66d7473-1748-4224-abf6-77234482f436"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_UF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="284" y="33" width="48" height="11" uuid="c1208b68-3349-4c72-88f1-a8b130ef4194"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CEP}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="227" y="33" width="18" height="11" uuid="d498872d-ab31-4935-bbca-154226ecc9d7"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[UF:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="261" y="33" width="23" height="11" uuid="009ce0dc-c0cb-4b70-9a5d-61804645222d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="336" y="0" width="38" height="11" uuid="83bcce58-8978-447b-a896-20f3ad651c09"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tel. Res:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="336" y="11" width="38" height="11" uuid="2d419a5c-b4f9-45a1-8248-41709e44ce1c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tel. Cel:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="336" y="22" width="38" height="11" uuid="c79cd0d5-1f3b-4522-8601-************"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Tel. Com:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="374" y="0" width="73" height="11" uuid="320e0364-f468-4b2f-9b2a-aa21a8e5fe18"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="374" y="11" width="73" height="11" uuid="d6248da2-8b61-4400-8548-3a523843e132"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_CEL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="374" y="22" width="73" height="11" uuid="6b50c094-4c39-467c-802d-ea52f2d905ff"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_COM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="475" y="11" width="79" height="11" uuid="837ac4e3-5c56-425d-9c2a-d282a0af02c2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CGC} == null ? $F{CLIENTE_CPF}:$F{CLIENTE_CGC}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="377" y="33" width="177" height="11" uuid="7a9d0f86-8a59-4237-8521-aff818f77000"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MOTORISTA_NOME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="336" y="33" width="41" height="11" uuid="f2e6348a-5573-414b-bc86-3492dec16198"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Motorista:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="37" y="22" width="29" height="11" uuid="e9d713e1-edec-422e-bd19-cbd7c47e67ee"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="451" y="11" width="24" height="11" uuid="f8ef877b-e12a-48b0-9394-8c82b39de727"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CGC} == null ? "CPF: ":"CGC: "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="451" y="22" width="24" height="11" uuid="9e1591fd-e380-4bc6-8040-069774101a40"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_INSC_ESTAD} == " " ? "RG: " : "I.E: "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="475" y="22" width="79" height="11" uuid="94a0d6ac-ad82-4b43-b5c8-2aae08e1e00b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_INSC_ESTAD} == " " ? $F{CLIENTE_RG} : $F{CLIENTE_INSC_ESTAD}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="451" y="0" width="103" height="11" uuid="0c11421e-cdb1-4514-b5df-dd56e4cbedf8"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="310" y="11" width="22" height="11" uuid="93b11067-8e77-436b-b383-c0e751c64a53"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FACHADA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="132" width="555" height="37" uuid="63ae2d4c-4b37-433d-b4b2-b6b17433f76f"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="0" y="1" width="1" height="36" uuid="966d3475-c61f-4187-b0cb-7e9169fe3494"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="1" y="0" width="38" height="10" uuid="9a942fd2-0be3-4ab6-b691-8718eb770581"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Veiculo:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="41" y="0" width="281" height="11" uuid="ab1d0178-e3f6-4af4-9ad7-a18e3bbf080d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESC_PROD_MOD}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="357" y="0" width="28" height="11" uuid="500f0dd0-1ee2-433b-979c-c535b914d8d2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[KM:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="445" y="0" width="52" height="11" uuid="df007740-e8f6-48db-82e8-3f4a0383a86a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Ano/Modelo:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="497" y="0" width="52" height="11" uuid="a7ad2510-e204-409f-8a15-03de24846a95"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_ANO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="72" y="11" width="250" height="11" uuid="4e739c3b-f460-4932-80c6-23ac41f4b246"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_CHASSI}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="357" y="11" width="28" height="11" uuid="85cc4bc0-78e2-4038-a051-a56e078dd3d0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Linha:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="385" y="11" width="58" height="11" uuid="cc49c268-34a5-41a6-b55c-94da81a13123"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_LINHA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="445" y="11" width="52" height="11" uuid="ac490ecc-81a5-4e67-9393-9970b74081ba"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Placa:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="497" y="11" width="52" height="11" uuid="9af04df6-2961-4796-a479-fb88d7d44ef9"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="59" y="22" width="263" height="11" uuid="1d97bc87-6c65-4bba-b004-317b34236352"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="357" y="22" width="28" height="11" uuid="0224080a-54e7-4cca-aaae-c4873ce5af38"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Motor:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="385" y="22" width="58" height="11" uuid="a169abd7-032e-4dcb-ade7-aa463cb4fe55"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_MOTOR}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="445" y="22" width="52" height="11" uuid="ff954916-8076-41a0-ad1f-6eb7cc25c070"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Combustivel:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="41" y="11" width="31" height="11" uuid="ea676f29-bb8f-4b6e-b6d7-31d4eec5a547"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Chassi:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="41" y="22" width="18" height="11" uuid="2b429de5-00e3-4b42-b0c1-c587805d4a43"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Cor:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="385" y="0" width="58" height="11" uuid="e56e6714-1021-414c-be62-571be5caf30a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_KM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="497" y="22" width="51" height="11" uuid="3c8847ce-30f2-4ded-8c7c-dd422ae399ae"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COMBUSTIVEL} < 19 ? "":
$F{OS_COMBUSTIVEL} < 39 ? "X" :
$F{OS_COMBUSTIVEL} < 59 ? "X   X":
$F{OS_COMBUSTIVEL} < 79 ? "X   X   X":
 "X   X   X   X"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="504" y="31" width="1" height="2" uuid="ef5604d7-55e9-46e7-a98c-6493d1e151ff">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="510" y="29" width="1" height="4" uuid="80d9ac95-d0f4-4ec4-a02d-24d1c6b9c644">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="516" y="31" width="1" height="2" uuid="d21e6fa5-c561-499e-a062-81d3de9b4bf3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="522" y="27" width="1" height="6" uuid="510e8c1c-1032-48c5-8a24-87f5bdad013d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="528" y="31" width="1" height="2" uuid="8fb0f1b0-a919-4b73-b777-1ecae026ab5e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="534" y="29" width="1" height="4" uuid="2b7f375f-89d9-42e1-bb7d-9dc02195a4ec">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="540" y="31" width="1" height="2" uuid="0b0dc158-c738-41a5-a95f-2eb2c910e954">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
			</frame>
			<frame>
				<reportElement x="0" y="169" width="555" height="24" uuid="6f260914-47b1-44d1-9f92-913744e4c294"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="0" y="1" width="1" height="23" uuid="2d5557be-8823-40b7-97b6-dbc9acbc86e0"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="1" y="1" width="129" height="11" uuid="af6b3b0c-2009-47e2-8a47-346807dab786"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Concessionaria Vendedora:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="130" y="1" width="264" height="11" uuid="fefd5b53-34ae-4560-bc29-2afb27854921"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="41" y="12" width="227" height="11" uuid="1f134d9f-6531-405f-9716-30c6f7ec86de"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="297" y="12" width="97" height="11" uuid="bbe81c33-5988-442d-9ee4-0b08b80b4cc7"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="394" y="12" width="161" height="11" uuid="d941b80d-8216-4fe5-916c-c7b0d86c878b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_CIDADE} + " " + $F{CONCESSIONARIA_ESTADO} + " " + $F{CONCESSIONARIA_UF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="511" y="1" width="43" height="11" uuid="95fe21f9-e833-43c6-ac28-8bbc15ca413a"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="443" y="1" width="46" height="11" uuid="cbee17b3-dc0d-4c5b-8668-dab4ce26083b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="394" y="1" width="49" height="11" uuid="583bc71b-da9e-44ea-9065-f4b579a65e2e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Data Venda:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="489" y="1" width="22" height="11" uuid="79ecb930-72d9-4d9e-87ef-eb45b748deda"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="268" y="12" width="29" height="11" uuid="5cb3df69-16f1-4b74-8bef-9d0ee3a7c1d8"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
			</frame>
		</band>
		<band height="30">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement x="0" y="0" width="555" height="30" isRemoveLineWhenBlank="true" uuid="f6ee1e9f-55dd-4261-92dd-c7dde1798d50">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsToyotaSubReclamacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="87">
			<frame>
				<reportElement stretchType="ContainerHeight" x="0" y="0" width="555" height="87" uuid="720fe214-597f-4305-9c26-b18427940db6">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement stretchType="ContainerHeight" x="326" y="1" width="1" height="86" uuid="020681df-077a-4b2d-9dcf-05c19ce3d471"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="326" y="2" width="228" height="10" uuid="ca3135ba-03b0-47af-a4ed-2424ea38c703"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Relatório de Técnico]]></text>
				</staticText>
				<textField isStretchWithOverflow="true">
					<reportElement mode="Transparent" x="331" y="14" width="221" height="13" uuid="64881ed7-31d9-46de-9d87-d31f8a97cf60"/>
					<textElement textAlignment="Left" markup="none">
						<font fontName="SansSerif" size="8" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_RELATORIO_TECNICO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="25">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement positionType="Float" x="0" y="0" width="555" height="25" isRemoveLineWhenBlank="true" uuid="755e1b39-8bec-425a-a56d-09d291f60e74">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsToyotaSubServicos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="23">
			<subreport>
				<reportElement positionType="Float" x="0" y="0" width="555" height="23" isRemoveLineWhenBlank="true" uuid="b9452d19-5c36-4f68-9d9e-d34f21b0c15b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsToyotaSubPecas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="224" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="39" uuid="f604d58f-aef2-4228-99e6-bf4f2d06e7fd"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="0" y="1" width="1" height="38" uuid="b3bbded6-b538-4000-b344-a3054e1ff143"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="1" y="3" width="61" height="10" uuid="8dba5c00-72c2-4832-bcb4-c8cc804709b4"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Fechamento]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="417" y="3" width="61" height="11" uuid="ccaa5ffe-01e9-4191-8414-f84484604d00"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[ServiçosItens:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="417" y="14" width="61" height="11" uuid="97b1bac5-97c4-4271-882d-98042f150725"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Desconto:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="417" y="25" width="61" height="11" uuid="7f571797-3db0-47d0-8875-346ef3440439"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<line>
					<reportElement x="417" y="24" width="112" height="1" uuid="5a7b8aff-e027-400f-956c-da519a69179e"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="255" y="3" width="51" height="11" uuid="5de36c68-67f5-45a0-a8ce-56950e061e38"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Itens:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="255" y="14" width="51" height="11" uuid="08088b16-c87c-404f-b09e-ef933e9dfa76"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Desconto:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="255" y="25" width="51" height="11" uuid="f8047859-bf55-451f-ad48-cd1880bc444f"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<line>
					<reportElement x="255" y="24" width="102" height="1" uuid="4b02e1ba-14b9-430b-8be5-a92ad67dede4"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="80" y="3" width="51" height="11" uuid="090a84fa-751a-46c9-8bbb-ec4d23380634"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Serviços:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="80" y="14" width="51" height="11" uuid="1653dc8f-f9c8-4d07-ba60-572a1edf6df0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Desconto:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="80" y="25" width="51" height="11" uuid="0757d599-ad83-4d60-93ac-74588bd54922"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<line>
					<reportElement x="79" y="24" width="103" height="1" uuid="a5996830-b71b-410c-b352-66e15bb22716"/>
				</line>
				<textField>
					<reportElement mode="Transparent" x="131" y="25" width="51" height="11" uuid="eb67fc41-8e43-441b-b3f6-d9fcb274c973"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS_SERVICOS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="306" y="25" width="51" height="11" uuid="b3822cd2-0823-4e0d-9058-ca9c436f2350"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS_ITENS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="478" y="25" width="51" height="10" uuid="1d7827cf-d535-4619-8613-b3182e6d61a8"/>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="131" y="3" width="51" height="11" uuid="60b5288f-7060-43ee-9e0b-5d7b64c3baa9"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VALOR_SERVICOS_BRUTO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="131" y="14" width="51" height="11" uuid="58da22b7-8c25-42a7-9354-640e3f7d710e"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DESCONTOS_SERVICOS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="306" y="3" width="51" height="11" uuid="cddf04cb-5caa-46cc-932e-9d5859450e1a"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VALOR_ITENS_BRUTO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="306" y="14" width="51" height="11" uuid="1f9a4d0e-1078-4b00-b1fc-67d2aa43ca78"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DESCONTOS_ITENS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="478" y="3" width="51" height="11" uuid="6a7b7a01-7dea-4ea1-a9bc-451e3ffa1d52"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS_BRUTO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="478" y="14" width="51" height="11" uuid="3ddbc1e2-2108-4f1d-ab3a-d0f41958f6d6"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TOTAL_OS_DESCONTO}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="39" width="555" height="15" uuid="e3fc1d4d-90c6-4399-88c2-0fcdbbb62014"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="0" y="1" width="1" height="14" uuid="931da300-789a-4b9d-884e-07996ec69875"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="59" y="2" width="49" height="11" uuid="0c963afa-7f16-41ce-be85-a6d19c287e9e"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Diagnóstico]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="110" y="2" width="42" height="11" uuid="bbee013d-0eed-4abc-89e2-6ca3835c1a62"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Técnico]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="153" y="2" width="87" height="11" uuid="f3187dc7-32dc-40a8-bafd-753740003eaf"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Controle de Qualidade]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="242" y="2" width="42" height="11" uuid="886820fd-bc33-4a33-a878-483abc6cedc4"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Entrega]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="285" y="2" width="270" height="11" uuid="0b977570-3cd3-4500-89ee-9e0d41959248"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Acompanhamento Pós-Serviço]]></text>
				</staticText>
				<line>
					<reportElement x="57" y="1" width="1" height="14" uuid="500ceb19-2c2e-402e-a7b4-acf47823cbbe"/>
				</line>
				<line>
					<reportElement x="108" y="1" width="1" height="14" uuid="fa7b9899-0dff-4324-8e66-a96961ea6208"/>
				</line>
				<line>
					<reportElement x="151" y="1" width="1" height="14" uuid="ba3ce76b-f35d-4b51-a87b-87e6a6822664"/>
				</line>
				<line>
					<reportElement x="240" y="1" width="1" height="14" uuid="32876e0f-995b-4a07-b56e-44e1418d4439"/>
				</line>
				<line>
					<reportElement x="284" y="1" width="1" height="14" uuid="ecf7a18f-e250-47bd-87b7-7e1b0b7d7080"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="2" width="55" height="11" uuid="eee080b6-c05a-466c-a649-46c9da64baa9"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Agendamento]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="54" width="555" height="26" uuid="8af80705-dae2-42d2-9db6-f301d60b5e7c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="286" y="1" width="51" height="8" uuid="616fa8e6-a0bb-4dfc-8ec4-59a55e3a0307"/>
					<textElement textAlignment="Center">
						<font size="5" isBold="true"/>
					</textElement>
					<text><![CDATA[Preferência]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="339" y="1" width="82" height="8" uuid="39f0b9d1-5223-4989-92ba-68a7da7ce7fd"/>
					<textElement textAlignment="Center">
						<font size="5" isBold="true"/>
					</textElement>
					<text><![CDATA[Comentários]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="422" y="1" width="66" height="8" uuid="e1a16670-df09-4c7a-8a57-3febc99df7e6"/>
					<textElement textAlignment="Center">
						<font size="5" isBold="true"/>
					</textElement>
					<text><![CDATA[Pessoa Contatada]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="489" y="1" width="66" height="8" uuid="f6ae66df-4d90-445a-ba12-4497a571c115"/>
					<textElement textAlignment="Center">
						<font size="5" isBold="true"/>
					</textElement>
					<text><![CDATA[Data / Realiz. Por]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="1" width="1" height="25" uuid="d3e70409-39e0-4533-aec4-81d9354fbea5"/>
				</line>
				<line>
					<reportElement x="57" y="1" width="1" height="25" uuid="54df6858-5785-422f-b106-3e8cf7f8645b"/>
				</line>
				<line>
					<reportElement x="108" y="1" width="1" height="25" uuid="628bd172-38b5-4a3c-a797-535b3d80aa29"/>
				</line>
				<line>
					<reportElement x="151" y="1" width="1" height="25" uuid="d43614af-9fd2-4716-ad2b-9100db07d8c3"/>
				</line>
				<line>
					<reportElement x="240" y="1" width="1" height="25" uuid="022e55f9-c7c1-4bb6-ba60-12ab0455c922"/>
				</line>
				<line>
					<reportElement x="284" y="1" width="1" height="25" uuid="24a7cf4a-c424-4cb0-a624-4067e3abce97"/>
				</line>
				<line>
					<reportElement x="337" y="1" width="1" height="25" uuid="92ae9d8e-aecf-4aae-a75c-b8602c6df3ab"/>
				</line>
				<line>
					<reportElement x="421" y="1" width="1" height="25" uuid="b41c7d83-8b23-43d5-8768-3607e1ba4134"/>
				</line>
				<line>
					<reportElement x="489" y="1" width="1" height="25" uuid="d6494a45-9374-45b5-a8a5-d3eea7de3b6b"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="2" y="7" width="55" height="11" uuid="7309ffcc-2a1d-4f95-a20c-b21963c69bdf"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Sim   /   Não]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="80" width="555" height="29" uuid="d43c8065-354f-4a47-b353-8a4c59bea224"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="5" y="3" width="547" height="10" uuid="ede6621e-b99a-4173-945d-9a00d9d6afbd"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_TERMO_TEXTO}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="1" width="1" height="28" uuid="6b2de898-96a9-4f84-b63f-d80dbe78afff"/>
				</line>
				<textField>
					<reportElement mode="Transparent" x="4" y="3" width="158" height="10" forecolor="#F71814" uuid="7248e27d-ca37-4829-88da-a4cf3bde8ee2"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_VALOR_PACOTE_PRE_PAGO} == null ? " " : "**REVISÃO NA MEDIDA R$" + new DecimalFormat("0.00").format($F{OS_VALOR_PACOTE_PRE_PAGO} )]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="109" width="555" height="42" uuid="e9be24fc-3288-4797-a24c-2d4f5d809481"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<line>
					<reportElement x="134" y="26" width="289" height="1" uuid="5719f8d6-518a-40d8-a510-3ce33b61a9f3"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="135" y="29" width="289" height="11" uuid="e8febc6e-446c-4dbd-848a-f3b75e6524f7"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente ou pessoa por ele autorizada]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="1" width="1" height="42" uuid="a59edad5-3c79-4d61-9021-ffa788f29400"/>
				</line>
				<image hAlign="Center" vAlign="Middle" isUsingCache="true">
					<reportElement x="132" y="1" width="290" height="24" uuid="6c9cd3d3-5996-43d6-8642-fc72abe6dc6b"/>
					<imageExpression><![CDATA[$F{OS_ASSINATURA}]]></imageExpression>
				</image>
			</frame>
			<frame>
				<reportElement x="0" y="151" width="555" height="57" uuid="ab30af26-c09d-44e9-befb-3b3ed20bfb77"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="1" y="5" width="553" height="12" uuid="e1d3a04a-a9b6-4b68-9c0d-f8ae33750c66"/>
					<textElement textAlignment="Center">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="1" y="18" width="553" height="11" uuid="bf66d92a-43f2-4dcb-9ad3-bbaab57e269e"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_RUA} + " " + $F{EMPRESAS_COMPLEMENTO} + ", "  + $F{EMPRESAS_BAIRRO} + " - "  + $F{CLIENTE_CIDADE} + " - " + $F{EMPRESA_ESTADO} + " - " + $F{EMPRESAS_CEP} + " CGC: " + $F{FATURAR_CGC} + " IE: " + $F{EMPRESAS_INSCRICAO_ESTADUAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="1" y="30" width="553" height="11" uuid="4016d22c-2907-4821-96b2-a5df4c27378c"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["Tel: " + $F{EMPRESAS_FONE} + " Fax:" +  $F{EMPRESAS_FAX}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="208" width="555" height="16" uuid="15b561db-9cd1-47ef-bb5e-e3d7cb19d3b0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="7" y="0" width="545" height="11" uuid="141ceb81-d3b6-4086-bba3-516bc3844b07"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[É Vedada a autenticação deste documento]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
