<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsFCASubPecas" pageWidth="555" pageHeight="842" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="campoNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_OS_AGENDA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[25755.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT A.COD_ITEM
      ,A.ITEM AS ITEMREQUISICOES
      ,I.DESCRICAO
      ,NVL(A.QTDE,0) AS QUANTIDADE
      ,NVL(A.PRECO_VENDA, 0.0) AS PRECO_VENDA
      ,NVL(A.QTDE * A.PRECO_VENDA,0.0) AS PRECO_TOTAL
      ,OTE.TIPO_FABRICA AS TIPOREQUISICOES
      ,OTE.TIPO AS TIPO_OS
      ,NVL(OT.GARANTIA,'N') AS GARANTIA
      ,A.COD_FORNECEDOR
      ,NVL(A.VALOR_DESCONTO_ITEM,0) AS VALOR_DESCONTO_ITEM
      ,ST.NOME      AS PRODUTIVO_PRISMA
  FROM OS_AGENDA_PECAS      A,
       OS_AGENDA            OA,
       ITENS_FORNECEDOR     F,
       ITENS                I,             
       OS_AGENDA_RECLAMACAO OAR,
       OS_TIPOS_EMPRESAS    OTE,
       OS_TIPOS             OT,
       OS_AGENDA_SERVICOS   OAS,
       PRISMA_BOX           PB,
       SERVICOS_TECNICOS    ST
 WHERE OA.COD_EMPRESA = A.COD_EMPRESA   
   AND OA.COD_OS_AGENDA = A.COD_OS_AGENDA
   AND F.COD_ITEM = A.COD_ITEM 
   AND F.COD_FORNECEDOR = A.COD_FORNECEDOR   
   AND I.COD_ITEM = A.COD_ITEM      
   AND OAR.COD_EMPRESA = A.COD_EMPRESA
   AND OAR.COD_OS_AGENDA = A.COD_OS_AGENDA
   AND OAR.ITEM = A.ITEM             
   AND OTE.COD_EMPRESA (+) = OAR.COD_EMPRESA 
   AND OTE.TIPO (+) = OAR.COD_TIPO_OS 
   AND OT.TIPO         (+) = OTE.TIPO
   AND A.COD_EMPRESA = $P{COD_EMPRESA}
   AND A.COD_OS_AGENDA     = $P{COD_OS_AGENDA}
   AND OAS.COD_EMPRESA     = A.COD_EMPRESA
   AND OAS.ITEM            = A.ITEM
   AND OAS.COD_OS_AGENDA   = A.COD_OS_AGENDA   
   AND OAS.COD_SERVICO     = A.COD_SERVICO 
   AND PB.PRISMA       (+) = OAS.PRISMA
   AND ST.COD_EMPRESA  (+) = PB.COD_EMPRESA_FILTRO
   AND ST.COD_TECNICO  (+) = PB.COD_TECNICO]]>
	</queryString>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="ITEMREQUISICOES" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="PRECO_TOTAL" class="java.lang.Double"/>
	<field name="TIPOREQUISICOES" class="java.lang.String"/>
	<field name="TIPO_OS" class="java.lang.String"/>
	<field name="GARANTIA" class="java.lang.String"/>
	<field name="COD_FORNECEDOR" class="java.lang.Double"/>
	<field name="VALOR_DESCONTO_ITEM" class="java.lang.Double"/>
	<field name="PRODUTIVO_PRISMA" class="java.lang.String"/>
	<variable name="TOTAL_PECAS" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{PRECO_TOTAL}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="TOTAL_DESCONTO_PECAS" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{VALOR_DESCONTO_ITEM}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="11">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b9e3f3a7-19cd-45e8-8ed6-c1a67586ea41">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[PEÇAS E LUBRIFICANTES]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="11">
			<staticText>
				<reportElement mode="Opaque" x="40" y="0" width="50" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="73ed2c61-dc67-4714-a8b8-a82e6af9e585">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo de OS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="90" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="effeae3b-1314-4d08-8cbb-1e5c9315f81c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Produtivo]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="230" y="0" width="140" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="deba69b2-afd3-469a-b2f5-8993ff35bf54">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição da Peças e Lubrificantes]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="d64b78a2-04b6-4573-86a6-8f71eec424e3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Item]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="147" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="94f31bf4-ce70-427c-ac01-38589ab2bc9f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Código do Produto]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="472" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="5e43e9f7-c5d7-4d99-9431-9fe61b1800a3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="370" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="c57e66bf-d141-4416-8893-64c1130f0ff2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="410" y="0" width="62" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="f296a42c-0818-44c3-b996-522138e76714">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Unitario]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="230" y="0" width="140" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1e6da218-bcea-4afc-9ceb-819528eb6b97">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###;#,##0.###-">
				<reportElement mode="Opaque" x="0" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="751d6850-6609-42fb-86cd-094b1b6ed143">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEMREQUISICOES}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="40" y="0" width="50" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="dea6bb9a-743a-4f23-83e4-c5a7e4d02cd1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIPO_OS}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="90" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1c5947c7-a166-49dc-a232-9dc87e561229">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRODUTIVO_PRISMA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="147" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="680366eb-6eb5-4d9a-9a1b-3de96721461d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_ITEM}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Opaque" x="472" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b358acf2-675f-434e-a45f-d640b2aa074e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_TOTAL}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Opaque" x="410" y="0" width="62" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="cc8a72ee-4b90-40b0-8396-8e1e4337c5c2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###;#,##0.###-">
				<reportElement mode="Opaque" x="370" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="18772301-372c-4fe4-8549-6261634c4b82">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="474" y="1" width="9" height="9" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="rgba(0, 0, 0, 0.5882353)" uuid="f1de991b-c340-4664-a3b9-c903e0a317b9">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="7" isBold="false"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="412" y="1" width="9" height="9" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="rgba(0, 0, 0, 0.5882353)" uuid="299654b1-59cc-4a57-a79d-65bdd3e46250">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="7" isBold="false"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
		</band>
	</detail>
	<summary>
		<band height="12">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="472" height="12" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" backcolor="#E3E3E3" uuid="5cda46e1-1987-4d88-b558-10c31a61e9b4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Estimado de Peças e Lubrificantes]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="474" y="0" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="98922382-ef16-40b7-986e-84296ae313b9">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
			<textField evaluationTime="Report" pattern="#,##0.00#;(#,##0.00#-)">
				<reportElement positionType="Float" mode="Opaque" x="472" y="0" width="83" height="12" isPrintInFirstWholeBand="true" backcolor="#E3E3E3" uuid="a984c7ba-3be2-44f1-abaa-158491f2b031">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{TOTAL_PECAS}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="474" y="1" width="9" height="10" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="rgba(0, 0, 0, 0.98039216)" uuid="9731c7cb-2500-4555-8b5a-df314145f0ab">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="7" isBold="false"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
