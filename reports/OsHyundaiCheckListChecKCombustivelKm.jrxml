<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiCheckListChecKCombustivelKm" pageWidth="555" pageHeight="74" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select numero_os,
       cod_empresa,
       km as km_entrada,
       nvl(km_saida, km) as km_saida,
       combustivel as combustivel_entrada,
       nvl(combustivel_saida, combustivel) as combustivel_saida
  from os_dados_veiculos
 where os_dados_veiculos.numero_os = $P{NUMERO_OS}
   and os_dados_veiculos.cod_empresa = $P{COD_EMPRESA}]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="KM_ENTRADA" class="java.lang.Double"/>
	<field name="KM_SAIDA" class="java.lang.Double"/>
	<field name="COMBUSTIVEL_ENTRADA" class="java.lang.Double"/>
	<field name="COMBUSTIVEL_SAIDA" class="java.lang.Double"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="74" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="17" width="275" height="52" uuid="723e9983-62dc-41ca-904f-e37a291104b3">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement x="155" y="0" width="120" height="12" uuid="4780e2a4-811d-4929-b08b-26747e8f8389"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KM_ENTRADA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="0" width="155" height="12" uuid="39e5e27b-2587-4d16-a118-92a24e11deb4">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Km do Veículo]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="12" width="155" height="40" uuid="a8b9c4f0-4deb-4c17-83cd-0695bf95f489">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Combustível]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="163" y="13" width="105" height="39" uuid="32686f8a-f4c2-4e71-881e-fb4107e94477">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + 
($F{COMBUSTIVEL_ENTRADA} <= 19 ? "crmservice15405.png" : 
($F{COMBUSTIVEL_ENTRADA} <= 39 ? "crmservice15406.png" : 
($F{COMBUSTIVEL_ENTRADA} <= 59 ? "crmservice15407.png" : 
($F{COMBUSTIVEL_ENTRADA} <= 79 ? "crmservice15408.png" : 
"crmservice15409.png"))))]]></imageExpression>
				</image>
			</frame>
			<frame>
				<reportElement x="280" y="17" width="275" height="52" uuid="50bd8ce0-5c2d-46d8-9300-9be63b15fd36">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="#.###;(#.###-)">
					<reportElement x="155" y="0" width="120" height="12" uuid="e1e970df-533f-45d2-8b1f-fa5a437390bf"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KM_SAIDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="0" width="155" height="12" uuid="de5011d5-5bf1-4d81-8116-ed3b91ca6702">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Km do Veículo]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="12" width="155" height="40" uuid="e336e24a-0298-42b4-8cb5-bdb4d541f041">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Combustível]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="163" y="13" width="105" height="39" uuid="f5b6285f-6660-4ead-8e72-a08dbd09f96d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + 
($F{COMBUSTIVEL_SAIDA} <= 19 ? "crmservice15405.png" : 
($F{COMBUSTIVEL_SAIDA} <= 39 ? "crmservice15406.png" : 
($F{COMBUSTIVEL_SAIDA} <= 59 ? "crmservice15407.png" : 
($F{COMBUSTIVEL_SAIDA} <= 79 ? "crmservice15408.png" : 
"crmservice15409.png"))))]]></imageExpression>
				</image>
			</frame>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="275" height="14" forecolor="#FFFFFF" backcolor="#3B3939" uuid="f9d37f67-6b66-4dac-93cb-3fedba58b4a5">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Entrada]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="280" y="0" width="275" height="14" forecolor="#FFFFFF" backcolor="#3B3939" uuid="72641ebb-0ea3-4b4a-8a63-f8c6f2451672">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Entrega]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
