<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsFord" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a6c8faac-fc28-4e0c-a20a-71416dbce697">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<style name="CAMPO_NULL" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_SUBREPORT" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT
  
  
  OS.COD_EMPRESA                    AS OS_COD_EMPRESA,
  OS.STATUS_OS                    AS OS_STATUS_OS,
  OS.COD_OS_AGENDA                  AS OS_COD_OS_AGENDA,
  NVL(OS.NUMERO_OS, 0)                    AS OS_NUMERO_OS, 
  ABS(OS.NUMERO_OS)                  AS OS_ABS_OSNUM,
  OS.NUMERO_OS_FABRICA                AS OS_NUMERO_OS_FABRICA,
  OS.COD_CLIENTE                    AS OS_COD_CLIENTE, 
  OS.CLIENTE_RAPIDO                  AS OS_CLIENTE_RAPIDO,
  OS.TIPO_ENDERECO                  AS OS_TIPO_ENDERECO,
  NVL(OS.OBSERVACAO, ' ')                    AS OS_OBSERVACAO,
  OS.EXTENDIDA                    AS OS_EXTENDIDA,
  OS.SEGURADORA                    AS OS_SEGURADORA,
  NVL(TO_CHAR(OS.DATA_EMISSAO, 'DD/MM/YYYY'),' ')                    AS OS_DATA_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2)     AS OS_DIA_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2)     AS OS_MES_EMISSAO,
  SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4)     AS OS_ANO_EMISSAO,
  OS.HORA_EMISSAO                    AS OS_HORA_EMISSAO,
  OS.HORA_ENCERRADA                  AS OS_HORA_ENCERRADA,
  OS.DATA_ENCERRADA                  AS OS_DATA_ENCERRADA,
  NVL(OS.HORA_PROMETIDA,' ')                  AS OS_HORA_PROMETIDA, 
  SUBSTR(OS.HORA_PROMETIDA, 1, 2)           AS OS_HORA24_PROMETIDA,
  SUBSTR(OS.HORA_PROMETIDA, 4, 2)           AS OS_MINUTO_PROMETIDA,
  NVL(TO_CHAR(OS.DATA_PROMETIDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_PROMETIDA, 
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2)     AS OS_DIA_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2)     AS OS_MES_PROMETIDA,
  SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4)   AS OS_ANO_PROMETIDA,
  NVL(OS.VALOR_SERVICOS_BRUTO, 0)                AS OS_VALOR_SERVICOS_BRUTO,
  NVL(OS.VALOR_ITENS_BRUTO, 0)                AS OS_VALOR_ITENS_BRUTO,
  NVL(OS.DESCONTOS_SERVICOS, 0)                AS OS_DESCONTOS_SERVICOS,
  NVL(OS.DESCONTOS_ITENS, 0)                  AS OS_DESCONTOS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS,0)                           AS OS_TOTAL_OS_SERVICOS,
  NVL(OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS,0)                                 AS OS_TOTAL_OS_ITENS,
  NVL(OS.VALOR_SERVICOS_BRUTO + OS.VALOR_ITENS_BRUTO,0)                            AS OS_TOTAL_OS_BRUTO,
  NVL(OS.DESCONTOS_ITENS + OS.DESCONTOS_SERVICOS,0)                                AS OS_TOTAL_OS_DESCONTO,
  NVL((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) + (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS), 0)   AS OS_TOTAL_OS,
  OS.COD_SEGURADORA                  AS OS_COD_SEGURADORA,
  OS.TIPO                        AS OS_TIPO,
  OS.NOME                       AS CONSULTOR,
  OS.OS_ENTRADA                    AS OS_OS_ENTRADA,
  '1ª VIA CLIENTE 1ª VIA CLIENTE 1ª VIA CLIENTE'     AS OS_VIA,
  OS.TOTAL_IMPRESSAO_FABRICA              AS OS_TOTAL_IMPRESSAO_FABRICA,
  NVL(OS_TIPOS_TERMO.TEXTO,' ')            AS OS_TERMO_TEXTO,
  
  
  DECODE(TIPO_PGTO,'V','S','N') AS OS_AVISTA,  
  DECODE(TIPO_PGTO,'Z','S','N') AS OS_CARTAO, 


  
  ABS(OS_ORCAMENTOS.NUMERO_ORCAMENTO) AS OS_NUMERO_ORCAMENTO,
  NVL(OS_ORCAMENTOS.NOMECLIAPROVOU,CLIENTES.NOME) AS OS_NOME_CLIENTE_APROVOU,
  NVL(OS.ORC_SERV_BRUTO + OS.ORC_ITEM_BRUTO, 0) AS OS_TOTAL_ORCAMENTO,
  
  
  GARANTIA_DOC.COD_SG_PADRAO AS OS_GARANTIA_SG_PADRAO,
  GARANTIA_DOC.COD_SG_EXEMPLO AS OS_GARANTIA_SG_EXEMPLO,
  
  
  
  NVL(OS.LAVAR_VEICULO,'N') AS OS_LAVAR_VEICULO,
  NVL(OS.CLIENTE_AGUARDOU,'N') AS OS_CLIENTE_AGUARDOU,
  
  NVL(OS_AGENDA.EH_RETORNO,'N') AS OS_EH_RETORNO,
  NVL(OS_AGENDA.EH_RECALL,'N') AS EH_RECALL,
     
   TO_CHAR(OS.DATA_AGENDADA_RECEPCAO, 'DD/MM/YYYY') AS DATA_AGENDAMENTO,
   
    
   TO_CHAR(OS.DATA_PROMETIDA_REVISADA, 'DD/MM/YYYY') AS DATA_PROMETIDA_REVISADA,
   TO_CHAR(OS.HORA_PROMETIDA_REVISADA, 'HH24:MI:SS') AS HORA_PROMETIDA_REVISADA,
   
  
  (SELECT EE.NOME_COMPLETO 
          FROM EMPRESAS_USUARIOS EE 
         WHERE EE.COD_EMPRESA = OS.COD_EMPRESA 
           AND EE.NOME = OS.QUEM_ABRIU) AS OS_NOME_AGENDADOR, 
       
   NVL(OS_AGENDA.EH_FIAT_PROFISSIONAL,'N') AS OS_FIAT_PROFISSIONAL,
   
   NVL(OS.PECA_USADA_FICA_CLIENTE,'N') AS OS_PECA_USADA_FICA_CLIENTE,
   
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'DD/MM/YYYY') AS OS_DATA_ULT_IMRESSAO,
   TO_CHAR(OS.DATA_ULT_IMP_FAB, 'HH24:MI:SS') AS OS_HORA_ULT_IMRESSAO,
   
   NVL(OS_AGENDA.VEICULO_PLATAFORMA,'N') AS OS_VEICULO_PLATAFORMA,
   
   TO_CHAR(OS_AGENDA.DATA_AGENDADA , 'HH24:MI') AS OS_HORA_AGENDADA,
   
   (SELECT PB.DESCRICAO
          FROM PRISMA_BOX PB
         WHERE PB.PRISMA = OS_DADOS_VEICULOS.PRISMA) AS OS_DESCRICAO_PRISMA,
   KM_PROXIMA_REVISAO,
   DATA_PROXIMO_SERVICO,
    
  
  NVL(EMPRESAS_USUARIOS.NOME_COMPLETO, ' ')           AS OS_CONSULTOR_COMPLETO,
  EMPRESAS_USUARIOS.CODIGO_OPCIONAL AS OS_CODIGO_OPCIONAL,
  
  
  
  
  OS_TIPOS.DESCRICAO                         AS OS_TIPO_DESCRICAO,
  OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO              AS OS_TIPO_COM_DESCRICAO,
  NVL(OS_TIPOS.GARANTIA,'N')                        AS OS_GARANTIA,
  OS_TIPOS.REVISAO_GRATUITA                    AS OS_REVISAO_GRATUITA,
  NVL(OS_TIPOS.INTERNO,'N')          AS OS_INTERNO,
  OS_TIPOS.COD_CLIENTE               AS OS_CLIENTE_DO_TIPO,
  OS_TIPOS.OUTRO_CONCESSIONARIA                  AS OS_OUTRO_CONCESSIONARIA,
  NVL(OS_TIPOS.TIPO_FABRICA_EMPRESA,OS_TIPOS.TIPO_FABRICA)     AS OS_TIPO_FABRICA,
  
  
  NVL(OS_DADOS_VEICULOS.ANO, ' ')                      AS OS_ANO,
  OS_DADOS_VEICULOS.HORIMETRO                    AS OS_HORIMETRO,
  NVL(OS_DADOS_VEICULOS.PRISMA, ' ')                    AS OS_PRISMA,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD/MM/YYYY'),' ')                  AS OS_DATA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2)     AS DIA_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2)     AS MES_VENDA,
  SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4)   AS ANO_VENDA,
  NVL(OS_DADOS_VEICULOS.COMBUSTIVEL,0)                  AS OS_COMBUSTIVEL,
  NVL(OS_DADOS_VEICULOS.COR_EXTERNA, ' ')                  AS OS_COR_EXTERNA,
  NVL(OS_DADOS_VEICULOS.PLACA, ' ')                      AS OS_PLACA,
  NVL(OS_DADOS_VEICULOS.KM, 0)                      AS OS_KM,
  NVL(OS_DADOS_VEICULOS.CHASSI, ' ')                    AS OS_CHASSI,
  NVL(OS_DADOS_VEICULOS.NUMERO_MOTOR, ' ')                  AS OS_NUMERO_MOTOR,
  OS_DADOS_VEICULOS.NUMERO_RENAVAM                AS OS_NUMERO_RENAVAM,
  OS_DADOS_VEICULOS.SERIE                      AS OS_SERIE,
  OS_DADOS_VEICULOS.COD_CONCESSIONARIA              AS OS_COD_CONCESSIONARIA,
  OS_DADOS_VEICULOS.ESTADO_PINTURA                AS OS_ESTADO_PINTURA,
  NVL(OS_DADOS_VEICULOS.JOGO_FERRAMENTAS, ' ')                AS OS_JOGO_FERRAMENTAS,
  NVL(OS_DADOS_VEICULOS.ELASTICOS, ' ')                    AS OS_ELASTICOS,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_D, ' ')                AS OS_TAMPA_LATERAL_D,
  NVL(OS_DADOS_VEICULOS.TAMPA_LATERAL_E, ' ')                AS OS_TAMPA_LATERAL_E,
  NVL(OS_DADOS_VEICULOS.FLANELA, ' ')                    AS OS_FLANELA,
  OS_DADOS_VEICULOS.NUMERO_FROTA                  AS OS_NUMERO_FROTA,
  
  
  NVL(CONCESSIONARIAS.NOME, ' ')                          AS CONCESSIONARIA_NOME,
  CONCESSIONARIAS.UF                            AS CONCESSIONARIA_UF,
  CONCESSIONARIAS.CIDADE                        AS CONCESSIONARIA_CIDADE,
  CONCESSIONARIAS.BAIRRO                        AS CONCESSIONARIA_BAIRRO,
  CONCESSIONARIAS.ENDERECO                      AS CONCESSIONARIA_RUA,
  CONCESSIONARIAS.CEP                           AS CONCESSIONARIA_CEP,
  CONCESSIONARIAS.CODIGO_PADRAO                   AS CONCESSIONARIA_CODIGO,
  UF_CONCESSIONARIA.DESCRICAO                   AS CONCESSIONARIA_ESTADO,
  NVL(TO_CHAR(OS_DADOS_VEICULOS.DATA_FAB_BATERIA, 'DD/MM/YYYY'),' ')                AS OS_DATA_FAB_BATERIA,
  NVL(OS_DADOS_VEICULOS.COD_FAB_BATERIA, ' ')                               AS OS_COD_FAB_BATERIA,
  NVL((SELECT EMPRESAS_USUARIOS.NOME_COMPLETO
          FROM EMPRESAS_USUARIOS
         WHERE EMPRESAS_USUARIOS.NOME = CLIENTES_FROTA.NOME_VENDEDOR),' ') AS CONCESSIONARIA_VENDEDOR,
  
  
  
  PRODUTOS.DESCRICAO_PRODUTO  AS OS_DESCRICAO_PRODUTO,
  
  
  NVL(PRODUTOS.DESCRICAO_PRODUTO || ' / ' || PRODUTOS_MODELOS.DESCRICAO_MODELO,' ')   AS DESC_PROD_MOD,
  PRODUTOS_MODELOS.DESCRICAO_MODELO                      AS OS_DESCRICAO_MODELO,
  PRODUTOS_MODELOS.MOD_VER_SERIE                        AS OS_MOD_VER_SERIE,
  PRODUTOS_MODELOS.LINHA                            AS OS_LINHA,
  
  
  MARCAS.DESCRICAO_MARCA  AS OS_DESCRICAO_MARCA,
  
  
  CO.TEXTO_AIDF  AS OS_TEXTO_AIDF,
  
  
  NVL(EMPRESAS.NOME,' ')           AS NOME_EMPRESA,
  NVL(EMPRESAS.CGC, ' ')                     AS EMPRESAS_CGC,
  EMPRESAS.FACHADA                  AS EMPRESAS_FACHADA,
  EMPRESAS.ESTADO                   AS UF_EMPRESA,
  NVL(EMPRESAS.CIDADE, ' ')                    AS EMPRESAS_CIDADE,
  EMPRESAS.BAIRRO                    AS EMPRESAS_BAIRRO,
  EMPRESAS.COMPLEMENTO                AS EMPRESAS_COMPLEMENTO,
  NVL(EMPRESAS.RUA, ' ')              AS EMPRESAS_RUA,
  NVL(EMPRESAS.FONE, ' ')                    AS EMPRESAS_FONE,
  NVL(EMPRESAS.FAX, ' ')                    AS EMPRESAS_FAX,
  NVL(EMPRESAS.CEP, ' ')                    AS EMPRESAS_CEP,
  EMPRESAS.INSCRICAO_MUNICIPAL            AS EMPRESAS_INSCRICAO_MUNICIPAL,
  EMPRESAS.INSCRICAO_SUBSTITUICAO            AS EMPRESAS_INSC_SUBSTITUICAO,
  NVL(UF_EMPRESA.DESCRICAO, ' ')                  AS EMPRESA_ESTADO,
  NVL(EMPRESAS.INSCRICAO_ESTADUAL, ' ')              AS EMPRESAS_INSCRICAO_ESTADUAL,
  TRUNC(SYSDATE)                     AS EMPRESA_DATA_ATUAL,
  SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'),1, 5)       AS EMPRESA_HORA_ATUAL_STR,
  NVL(CLIENTE_EMPRESA.ENDERECO_ELETRONICO, ' ')         AS EMPRESA_EMAIL,
  
  
  
  
 
  CLIENTE_DIVERSO.COD_CLIENTE    AS CLIENTE_COD_CLIENTE,
  NVL(CLIENTE_DIVERSO.NOME, ' ')        AS CLIENTE_NOME,
  NVL(CLIENTE_DIVERSO.RG,' ')          AS CLIENTE_RG,

  
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_CEL || '-' || CLIENTES.TELEFONE_CEL),' ') AS CLIENTE_FONE_CEL,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_RES || '-' || CLIENTES.TELEFONE_RES),' ') AS CLIENTE_FONE_RES,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_COM || '-' || CLIENTES.TELEFONE_COM),' ') AS CLIENTE_FONE_COM,
  NVL(TRIM('-' FROM CLIENTES.PREFIXO_FAX|| '-' || CLIENTES.TELEFONE_FAX),' ') AS CLIENTE_FONE_FAX,

  
  
  
  CLIENTES.ENDERECO_ELETRONICO  AS CLIENTE_ENDERECO_ELETRONICO,
  NVL(NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL),' ') AS CLIENTE_INSC_ESTAD,
  
  NVL(DECODE(CLIENTE_DIVERSO.CPF,' ','CGC: ' || NVL(CLIENTE_DIVERSO.CGC,' '),'CPF: ' || CLIENTE_DIVERSO.CPF), ' ') AS CLIENTE_CGC_CPF,
  
  CLIENTE_DIVERSO.CGC        AS CLIENTE_CGC,
  CLIENTE_DIVERSO.CPF        AS CLIENTE_CPF,
  CLIENTES.COD_CLASSE        AS CLIENTE_COD_CLASSE,
  
  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.ENDERECO  || ', ' || CLIENTE_DIVERSO.COMPLEMENTO || ' ' || CLIENTE_DIVERSO.BAIRRO))
    WHEN '2' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_RES  || ', ' ||CLIENTES.COMPLEMENTO_RES || ' ' || CLIENTES.BAIRRO_RES))
    WHEN '3' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COM  || ', ' ||CLIENTES.COMPLEMENTO_COM || ' ' || CLIENTES.BAIRRO_COM))
    WHEN '4' THEN TRIM(',' FROM TRIM( ' '  FROM  CLIENTES.RUA_COBRANCA  || ', ' ||CLIENTES.COMPLEMENTO_COBRANCA || ' ' || CLIENTES.BAIRRO_COBRANCA))
    WHEN '5' THEN TRIM(',' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.RUA  || ', ' ||ENDERECO_POR_INSCRICAO.COMPLEMENTO || ' ' || ENDERECO_POR_INSCRICAO.BAIRRO))
    ELSE ' '
    END  AS CLIENTE_ENDERECO_COMPLETO,
    

  CASE OS.TIPO_ENDERECO
    WHEN '1' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO))
    WHEN '2' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_RES  || '-' || CLIENTES.TELEFONE_RES))
    WHEN '3' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '4' THEN TRIM('-' FROM TRIM( ' '  FROM  CLIENTES.PREFIXO_COM  || '-' || CLIENTES.TELEFONE_COM))
    WHEN '5' THEN TRIM('-' FROM TRIM( ' '  FROM  ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO  || '-' || ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO))
    ELSE ' '
    END  AS CLIENTE_TELEFONE_COMPLETO,
    

  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.UF,
              2, CLIENTES.UF_RES,
              3, CLIENTES.UF_COM,
              4, CLIENTES.UF_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.UF,
               ' ')                          AS  CLIENTE_UF,
  DECODE(OS.TIPO_ENDERECO, 1, UF_DIVERSO.DESCRICAO,
              2, UF_RES.DESCRICAO,
              3, UF_COM.DESCRICAO,
              4, UF_COBRANCA.DESCRICAO,
              5, UF_INSCRICAO.DESCRICAO,
               ' ')                          AS  CLIENTE_ESTADO,
  DECODE(OS.TIPO_ENDERECO, 1, CIDADES_DIV.DESCRICAO,
              2, CIDADES_RES.DESCRICAO,
              3, CIDADES_COM.DESCRICAO,
              4, CIDADES_COBRANCA.DESCRICAO,
              5, ENDERECO_POR_INSCRICAO.CIDADE,
               ' ')                          AS  CLIENTE_CIDADE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.BAIRRO,
              2, CLIENTES.BAIRRO_RES,
              3, CLIENTES.BAIRRO_COM,
              4, CLIENTES.BAIRRO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.BAIRRO,
               ' ')                          AS  CLIENTE_BAIRRO,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.CEP,
              2, CLIENTES.CEP_RES,
              3, CLIENTES.CEP_COM,
              4, CLIENTES.CEP_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.CEP,
               ' ')                          AS  CLIENTE_CEP,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.ENDERECO,
              2, CLIENTES.RUA_RES,
              3, CLIENTES.RUA_COM,
              4, CLIENTES.RUA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.RUA,
               ' ')                          AS  CLIENTE_RUA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.COMPLEMENTO,
              2, CLIENTES.COMPLEMENTO_RES,
              3, CLIENTES.COMPLEMENTO_COM,
              4, CLIENTES.COMPLEMENTO_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.COMPLEMENTO,
               ' ')                          AS  CLIENTE_COMPLEMENTO,
  DECODE(OS.TIPO_ENDERECO, 1, ' ',
              2, CLIENTES.FACHADA_RES,
              3, CLIENTES.FACHADA_COM,
              4, CLIENTES.FACHADA_COBRANCA,
              5, ENDERECO_POR_INSCRICAO.FACHADA,
               ' ')                          AS  CLIENTE_FACHADA,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.FONE_CONTATO,
              2, CLIENTES.TELEFONE_RES,
              3, CLIENTES.TELEFONE_COM,
              4, CLIENTES.TELEFONE_COM,
              5, ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_FONE,
  DECODE(OS.TIPO_ENDERECO, 1, CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
              2, CLIENTES.PREFIXO_RES,
              3, CLIENTES.PREFIXO_COM,
              4, CLIENTES.PREFIXO_COM,
              5, ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
               ' ')                          AS  CLIENTE_PREFIXO,
  
  
  
  NVL(FATURAR_CLIENTE_DIVERSO.NOME, ' ')                   AS FATURAR_NOME,
  
  NVL(TRIM('-' FROM TRIM( ' '  FROM  CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO  || '-' || CLIENTE_DIVERSO.FONE_CONTATO)),' ') AS FATURAR_FONE,
  
  NVL(UF_FATURAR.DESCRICAO, ' ')                       AS FATURAR_ESTADO,
  NVL(FATURAR_CIDADE.DESCRICAO, FATURAR_CLIENTE_DIVERSO.CIDADE)   AS FATURAR_CIDADE,

  NVL(TRIM(SUBSTR(CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.ENDERECO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.ENDERECO ELSE '' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.COMPLEMENTO)>0 THEN ', ' || FATURAR_CLIENTE_DIVERSO.COMPLEMENTO ELSE ' ' END ||
     CASE WHEN LENGTH(FATURAR_CLIENTE_DIVERSO.BAIRRO)>0 THEN '  ' || FATURAR_CLIENTE_DIVERSO.BAIRRO ELSE '' END
    , 2)), ' ')  AS FATURAR_ENDERECO,
   
   NVL(GREATEST(
    FATURAR_CLIENTE.PREFIXO_RES || '-' || FATURAR_CLIENTE.TELEFONE_RES,
    FATURAR_CLIENTE.PREFIXO_COM || '-' || FATURAR_CLIENTE.TELEFONE_COM,
    FATURAR_CLIENTE.PREFIXO_FAX || '-' || FATURAR_CLIENTE.TELEFONE_FAX
   ), ' ')   AS FATURAR_TELEFONE_FAX,
   
   
    NVL(FATURAR_CLIENTE.BAIRRO_COM,
    FATURAR_CLIENTE.BAIRRO_RES)   AS FATURAR_BAIRRO,
  
  NVL(FATURAR_CLIENTE.CEP_COM,
    FATURAR_CLIENTE.CEP_RES)   AS FATURAR_CEP,
  
  FATURAR_DADOS_JURIDICOS.CGC AS FATURAR_CGC,
  
  FATURAR_DADOS_JURIDICOS.INSC_ESTADUAL AS FATURAR_IE,
  
   
   
   
   NVL(CLIENTES_FROTA.MEDIA_KM_MENSAL,0) AS MEDIA_KM_MENSAL,
   

  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_TERCEIROS 
    ELSE 
    TOTAIS_SERVICOS.VAL_TERCEIROS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_TERCEIROS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_TERCEIROS,
      
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_LAVAGEM
    ELSE 
    TOTAIS_SERVICOS.VAL_LAVAGEM - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_LAVAGEM/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_LAVAGEM,
  
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_REVISAO
    ELSE 
    TOTAIS_SERVICOS.VAL_REVISAO - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_REVISAO/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_REVISAO,
    
  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_MECANICA
    ELSE 
    TOTAIS_SERVICOS.VAL_MECANICA - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_MECANICA/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_MECANICA,

  NVL(CASE WHEN OS.VALOR_SERVICOS_BRUTO <=0 THEN 
    TOTAIS_SERVICOS.VAL_GERAIS
    ELSE 
    TOTAIS_SERVICOS.VAL_GERAIS - (OS.DESCONTOS_SERVICOS * TOTAIS_SERVICOS.VAL_GERAIS/OS.VALOR_SERVICOS_BRUTO)
    END, 0) AS TOT_SERVICOS_VAL_GERAIS,
   
  
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_PECAS
    ELSE 
    TOTAL_PECAS.VALOR_PECAS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_PECAS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_PECAS,
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_ACESSORIOS
    ELSE 
    TOTAL_PECAS.VALOR_ACESSORIOS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_ACESSORIOS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_ACESSORIOS, 
    
  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_OUTROS
    ELSE 
    TOTAL_PECAS.VALOR_OUTROS - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_OUTROS/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_OUTROS, 

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_LUBRIFICANTE
    ELSE 
    TOTAL_PECAS.VALOR_LUBRIFICANTE - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_LUBRIFICANTE/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_LUBRIFICANTE,  

  NVL(CASE WHEN OS.VALOR_ITENS_BRUTO <=0 THEN 
    TOTAL_PECAS.VALOR_COMBUSTIVEL
    ELSE 
    TOTAL_PECAS.VALOR_COMBUSTIVEL - (OS.DESCONTOS_ITENS * TOTAL_PECAS.VALOR_COMBUSTIVEL/OS.VALOR_ITENS_BRUTO)
    END, 0) AS TOT_PECAS_VALOR_COMBUSTIVEL
    
 
FROM
  
  EMPRESAS,
  UF UF_EMPRESA,
  CLIENTES CLIENTE_EMPRESA,
  
  
  
  OS, EMPRESAS_USUARIOS, VW_OS_TIPOS OS_TIPOS, OS_DADOS_VEICULOS,
  CONCESSIONARIAS,UF UF_CONCESSIONARIA, PRODUTOS, PRODUTOS_MODELOS, MARCAS,
  CONTROLE_OS CO, PARM_SYS, OS_TIPOS_TERMO, OS_AGENDA, CLIENTES_FROTA, OS_PAGAMENTO,
  FORMA_PGTO, OS_ORCAMENTOS, GARANTIA_DOC,
  
  
  CLIENTE_DIVERSO, CLIENTES,  ENDERECO_POR_INSCRICAO,
  CIDADES CIDADES_RES, CIDADES CIDADES_COM, CIDADES CIDADES_COBRANCA, CIDADES CIDADES_DIV,
  UF UF_DIVERSO, UF UF_RES, UF UF_COM, UF UF_COBRANCA, UF UF_INSCRICAO,
  
  
  CLIENTE_DIVERSO FATURAR_CLIENTE_DIVERSO, CLIENTES FATURAR_CLIENTE, CIDADES FATURAR_CIDADE, 
   UF UF_FATURAR, DADOS_JURIDICOS FATURAR_DADOS_JURIDICOS,
   
   
  (SELECT
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'T', TOTAIS.PRECO, 0)) AS VAL_TERCEIROS,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'L', TOTAIS.PRECO, 0)) AS VAL_LAVAGEM,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'R', TOTAIS.PRECO, 0)) AS VAL_REVISAO,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'M', TOTAIS.PRECO, 0)) AS VAL_MECANICA,
  SUM(DECODE(TOTAIS.TIPO_SERVICO, 'G', TOTAIS.PRECO, 0)) AS VAL_GERAIS
  FROM
  (SELECT
  DECODE(S.TERCEIROS, 'S',       'T',
    DECODE(S.LAVAGEM, 'S',       'L',
    DECODE(SS.TIPO_SETOR, 'R', 'R',
                'M', 'M',
                   'G'))) AS TIPO_SERVICO,
  OSS.PRECO_VENDA + NVL((SELECT SUM(PRECO_VENDA) FROM OS_SERVICOS_ADICIONAIS ADI
               WHERE ADI.COD_EMPRESA = OSS.COD_EMPRESA
                 AND ADI.NUMERO_OS   = OSS.NUMERO_OS
                 AND ADI.ITEM        = OSS.ITEM
                 AND ADI.COD_SERVICO = OSS.COD_SERVICO), 0) AS PRECO
  FROM OS_SERVICOS OSS, SERVICOS S, SERVICOS_SETORES SS
  WHERE 1=1
  AND OSS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OSS.NUMERO_OS = $P{NUMERO_OS}
  AND OSS.COD_SERVICO = S.COD_SERVICO
  AND S.COD_SETOR = SS.COD_SETOR) TOTAIS ) TOTAIS_SERVICOS,
  
  
  
  (SELECT
  SUM(DECODE(CLASSE_PECA, 1, VALOR, 0)) AS VALOR_PECAS,
  SUM(DECODE(CLASSE_PECA, 2, VALOR, 0)) AS VALOR_ACESSORIOS,
  SUM(DECODE(NVL(CLASSE_PECA, 3), 3, VALOR, 0)) AS VALOR_OUTROS,
  SUM(DECODE(CLASSE_PECA, 4, VALOR, 0)) AS VALOR_LUBRIFICANTE,
  SUM(DECODE(CLASSE_PECA, 5, VALOR, 0)) AS VALOR_COMBUSTIVEL
  FROM (
  SELECT DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1), 
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4)) AS CLASSE_PECA,
  SUM(
  OS_REQUISICOES.QUANTIDADE *
  DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
        ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                    DECODE(ITENS.COD_TRIBUTACAO, '1',
                    DECODE(PARM_SYS.REGIME_ICMS, 'S',
                      DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                      DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                    0),
                         0),
                         OS_TIPOS.AUMENTO_PRECO_PECA),
                       0),
                     OS_TIPOS.AUMENTO_PRECO_PECA)) *
           DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                            'G', OS_REQUISICOES.PRECO_GARANTIA,
                            'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                            'P', OS_REQUISICOES.PRECO_FABRICA,
                            DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL))
          ) / 100,
     DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                        OS_REQUISICOES.PRECO_GARANTIA),
       DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
       DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
         ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
        DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                            OS_REQUISICOES.PRECO_VENDA))/100))))))) AS VALOR
  FROM OS_REQUISICOES, ITENS, ITENS_FORNECEDOR, OS, VW_OS_TIPOS OS_TIPOS, ITENS_CLASSE_CONTABIL ICC , SEGURADORA,
  PARM_SYS, PARM_SYS2, ITENS_GRUPO_INTERNO IGI, OS_TIPOS_EMPRESAS OTE
  WHERE OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
  AND OS.NUMERO_OS = OS_REQUISICOES.NUMERO_OS  
  AND OS.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA 
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS.TIPO = OS_TIPOS.TIPO
  AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND ITENS.COD_GRUPO_INTERNO = IGI.COD_GRUPO_INTERNO(+)
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
  AND OS.COD_EMPRESA = $P{COD_EMPRESA}
  AND OS.NUMERO_OS = $P{NUMERO_OS}
  GROUP BY DECODE(ICC.CLASSE_PECA, 1, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 2, DECODE(NVL(IGI.ACESSORIO, 'N'), 'S', 2, 1),
                 3, ICC.CLASSE_PECA,
                 4, DECODE(NVL(ITENS.EH_COMBUSTIVEL, 'N'), 'S', DECODE(NVL(ITENS.EH_LUBRIFICANTE, 'N'), 'S', 4, 5), 4))
  ) TOTAL) TOTAL_PECAS,
  
  (SELECT CE.KM AS KM_PROXIMA_REVISAO,NVL(C1.DATA_EVENTO, C1.DATA_NOVO_CONTATO) AS DATA_PROXIMO_SERVICO
    FROM OS_DADOS_VEICULOS, CRM_EVENTOS C1, CRM_CICLO_EVENTOS CE
    WHERE C1.COD_CICLO (+) > 0
    AND C1.DATA_EVENTO (+) > SYSDATE - 1
    AND C1.STATUS (+) IN ('P', 'A')
    AND C1.COD_CICLO = CE.COD_CICLO(+)
    AND C1.COD_TIPO_EVENTO = CE.COD_TIPO_EVENTO(+)
    AND NVL(CE.KM (+),0) > 0
    AND OS_DADOS_VEICULOS.CHASSI = C1.VEIC_CHASSI_COMPLETO (+)
    AND OS_DADOS_VEICULOS.NUMERO_OS = $P{NUMERO_OS}
    AND OS_DADOS_VEICULOS.COD_EMPRESA = $P{COD_EMPRESA}
    AND ROWNUM = 1
    ORDER BY DATA_PROXIMO_SERVICO) CRM_EVENTOS


WHERE   1 = 1
    
    
    AND OS.COD_EMPRESA = $P{COD_EMPRESA} 
    AND OS.NUMERO_OS = $P{NUMERO_OS}
    AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'

    AND OS.NOME = EMPRESAS_USUARIOS.NOME
  
  AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
  AND OS.COD_OS_AGENDA = OS_AGENDA.COD_OS_AGENDA (+)
  
  AND OS.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  
  AND OS.TIPO = OS_TIPOS_TERMO.TIPO (+)
    
  AND OS.COD_EMPRESA =OS_TIPOS_TERMO.COD_EMPRESA (+)
  
  
  
    AND OS.TIPO = OS_TIPOS.TIPO
    AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA (+)
    
    AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS (+)
    AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA (+)
    
    AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA = CONCESSIONARIAS.COD_CONCESSIONARIA (+)
    
    
    AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF (+)
    
    AND OS.COD_CLIENTE = CLIENTES_FROTA.COD_CLIENTE (+)
    AND OS.COD_PRODUTO = CLIENTES_FROTA.COD_PRODUTO (+)
    AND OS.COD_MODELO = CLIENTES_FROTA.COD_MODELO (+)
    AND OS_DADOS_VEICULOS.CHASSI = CLIENTES_FROTA.CHASSI
    
    AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO 
    
    AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
    
    AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
    AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
    
   
    AND OS.COD_EMPRESA = CO.COD_EMPRESA(+)
  
  AND OS.COD_DOCUMENTO = GARANTIA_DOC.COD_DOCUMENTO (+)
  
  
  AND OS.NUMERO_OS = OS_PAGAMENTO.NUMERO_OS(+)
  AND OS.COD_EMPRESA = OS_PAGAMENTO.COD_EMPRESA (+)
  AND OS_PAGAMENTO.COD_EMPRESA= FORMA_PGTO.COD_EMPRESA (+)
  AND OS_PAGAMENTO.COD_FORMA_PGTO =FORMA_PGTO.COD_FORMA_PGTO (+)
  
  
  AND OS.NUMERO_OS = OS_ORCAMENTOS.NUMERO_OS (+)
  AND OS.COD_EMPRESA = OS_ORCAMENTOS.COD_EMPRESA (+)
  
  
  
    
    
    AND OS.COD_EMPRESA = EMPRESAS.COD_EMPRESA
    AND EMPRESAS.ESTADO = UF_EMPRESA.UF (+)
    AND EMPRESAS.COD_CLIENTE = CLIENTE_EMPRESA.COD_CLIENTE (+)
    
    
    AND OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE (+)
    AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES (+)
    AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF (+)
    AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES (+)
    AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES (+)
    AND OS.INSCRICAO_ESTADUAL = ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL (+)
    AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE (+)
    AND CLIENTES.UF_RES = UF_RES.UF (+)
    AND CLIENTES.UF_COM = UF_COM.UF (+)
    AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF (+)
    AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF (+)
    
    
    AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_CLIENTE.COD_CLIENTE (+)
    AND FATURAR_CLIENTE_DIVERSO.COD_CIDADES =FATURAR_CIDADE.COD_CIDADES (+)
    AND FATURAR_CLIENTE_DIVERSO.UF = UF_FATURAR.UF (+)
    AND FATURAR_CLIENTE_DIVERSO.COD_CLIENTE = FATURAR_DADOS_JURIDICOS.COD_CLIENTE (+)
  AND OS.COD_CLIENTE = FATURAR_CLIENTE_DIVERSO.COD_CLIENTE (+)]]>
	</queryString>
	<field name="OS_COD_EMPRESA" class="java.lang.Double"/>
	<field name="OS_STATUS_OS" class="java.lang.Double"/>
	<field name="OS_COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="OS_ABS_OSNUM" class="java.lang.Double"/>
	<field name="OS_NUMERO_OS_FABRICA" class="java.lang.Double"/>
	<field name="OS_COD_CLIENTE" class="java.lang.Double"/>
	<field name="OS_CLIENTE_RAPIDO" class="java.lang.String"/>
	<field name="OS_TIPO_ENDERECO" class="java.lang.String"/>
	<field name="OS_OBSERVACAO" class="java.lang.String"/>
	<field name="OS_EXTENDIDA" class="java.lang.String"/>
	<field name="OS_SEGURADORA" class="java.lang.String"/>
	<field name="OS_DATA_EMISSAO" class="java.lang.String"/>
	<field name="OS_DIA_EMISSAO" class="java.lang.String"/>
	<field name="OS_MES_EMISSAO" class="java.lang.String"/>
	<field name="OS_ANO_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="OS_HORA_ENCERRADA" class="java.lang.String"/>
	<field name="OS_DATA_ENCERRADA" class="java.sql.Timestamp"/>
	<field name="OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_HORA24_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MINUTO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DATA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_DIA_PROMETIDA" class="java.lang.String"/>
	<field name="OS_MES_PROMETIDA" class="java.lang.String"/>
	<field name="OS_ANO_PROMETIDA" class="java.lang.String"/>
	<field name="OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_DESCONTOS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_SERVICOS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_ITENS" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_BRUTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS_DESCONTO" class="java.lang.Double"/>
	<field name="OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="OS_COD_SEGURADORA" class="java.lang.Double"/>
	<field name="OS_TIPO" class="java.lang.String"/>
	<field name="CONSULTOR" class="java.lang.String"/>
	<field name="OS_OS_ENTRADA" class="java.lang.Double"/>
	<field name="OS_VIA" class="java.lang.String"/>
	<field name="OS_TOTAL_IMPRESSAO_FABRICA" class="java.lang.Double"/>
	<field name="OS_TERMO_TEXTO" class="java.lang.String"/>
	<field name="OS_AVISTA" class="java.lang.String"/>
	<field name="OS_CARTAO" class="java.lang.String"/>
	<field name="OS_NUMERO_ORCAMENTO" class="java.lang.Double"/>
	<field name="OS_NOME_CLIENTE_APROVOU" class="java.lang.String"/>
	<field name="OS_TOTAL_ORCAMENTO" class="java.lang.Double"/>
	<field name="OS_GARANTIA_SG_PADRAO" class="java.lang.String"/>
	<field name="OS_GARANTIA_SG_EXEMPLO" class="java.lang.Double"/>
	<field name="OS_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="OS_CLIENTE_AGUARDOU" class="java.lang.String"/>
	<field name="OS_EH_RETORNO" class="java.lang.String"/>
	<field name="EH_RECALL" class="java.lang.String"/>
	<field name="DATA_AGENDAMENTO" class="java.lang.String"/>
	<field name="DATA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="OS_NOME_AGENDADOR" class="java.lang.String"/>
	<field name="OS_FIAT_PROFISSIONAL" class="java.lang.String"/>
	<field name="OS_PECA_USADA_FICA_CLIENTE" class="java.lang.String"/>
	<field name="OS_DATA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_HORA_ULT_IMRESSAO" class="java.lang.String"/>
	<field name="OS_VEICULO_PLATAFORMA" class="java.lang.String"/>
	<field name="OS_HORA_AGENDADA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRISMA" class="java.lang.String"/>
	<field name="KM_PROXIMA_REVISAO" class="java.lang.Double"/>
	<field name="DATA_PROXIMO_SERVICO" class="java.sql.Timestamp"/>
	<field name="OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="OS_CODIGO_OPCIONAL" class="java.lang.String"/>
	<field name="OS_TIPO_DESCRICAO" class="java.lang.String"/>
	<field name="OS_TIPO_COM_DESCRICAO" class="java.lang.String"/>
	<field name="OS_GARANTIA" class="java.lang.String"/>
	<field name="OS_REVISAO_GRATUITA" class="java.lang.String"/>
	<field name="OS_INTERNO" class="java.lang.String"/>
	<field name="OS_CLIENTE_DO_TIPO" class="java.lang.Double"/>
	<field name="OS_OUTRO_CONCESSIONARIA" class="java.lang.String"/>
	<field name="OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="OS_ANO" class="java.lang.String"/>
	<field name="OS_HORIMETRO" class="java.lang.Double"/>
	<field name="OS_PRISMA" class="java.lang.String"/>
	<field name="OS_DATA_VENDA" class="java.lang.String"/>
	<field name="DIA_VENDA" class="java.lang.String"/>
	<field name="MES_VENDA" class="java.lang.String"/>
	<field name="ANO_VENDA" class="java.lang.String"/>
	<field name="OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="OS_PLACA" class="java.lang.String"/>
	<field name="OS_KM" class="java.lang.Double"/>
	<field name="OS_CHASSI" class="java.lang.String"/>
	<field name="OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="OS_NUMERO_RENAVAM" class="java.lang.String"/>
	<field name="OS_SERIE" class="java.lang.String"/>
	<field name="OS_COD_CONCESSIONARIA" class="java.lang.Double"/>
	<field name="OS_ESTADO_PINTURA" class="java.lang.String"/>
	<field name="OS_JOGO_FERRAMENTAS" class="java.lang.String"/>
	<field name="OS_ELASTICOS" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_D" class="java.lang.String"/>
	<field name="OS_TAMPA_LATERAL_E" class="java.lang.String"/>
	<field name="OS_FLANELA" class="java.lang.String"/>
	<field name="OS_NUMERO_FROTA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="CONCESSIONARIA_UF" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CIDADE" class="java.lang.String"/>
	<field name="CONCESSIONARIA_BAIRRO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_RUA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CEP" class="java.lang.String"/>
	<field name="CONCESSIONARIA_CODIGO" class="java.lang.String"/>
	<field name="CONCESSIONARIA_ESTADO" class="java.lang.String"/>
	<field name="OS_DATA_FAB_BATERIA" class="java.lang.String"/>
	<field name="OS_COD_FAB_BATERIA" class="java.lang.String"/>
	<field name="CONCESSIONARIA_VENDEDOR" class="java.lang.String"/>
	<field name="OS_DESCRICAO_PRODUTO" class="java.lang.String"/>
	<field name="DESC_PROD_MOD" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="OS_MOD_VER_SERIE" class="java.lang.String"/>
	<field name="OS_LINHA" class="java.lang.String"/>
	<field name="OS_DESCRICAO_MARCA" class="java.lang.String"/>
	<field name="OS_TEXTO_AIDF" class="java.lang.String"/>
	<field name="NOME_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_CGC" class="java.lang.String"/>
	<field name="EMPRESAS_FACHADA" class="java.lang.String"/>
	<field name="UF_EMPRESA" class="java.lang.String"/>
	<field name="EMPRESAS_CIDADE" class="java.lang.String"/>
	<field name="EMPRESAS_BAIRRO" class="java.lang.String"/>
	<field name="EMPRESAS_COMPLEMENTO" class="java.lang.String"/>
	<field name="EMPRESAS_RUA" class="java.lang.String"/>
	<field name="EMPRESAS_FONE" class="java.lang.String"/>
	<field name="EMPRESAS_FAX" class="java.lang.String"/>
	<field name="EMPRESAS_CEP" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_MUNICIPAL" class="java.lang.String"/>
	<field name="EMPRESAS_INSC_SUBSTITUICAO" class="java.lang.String"/>
	<field name="EMPRESA_ESTADO" class="java.lang.String"/>
	<field name="EMPRESAS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="EMPRESA_DATA_ATUAL" class="java.sql.Timestamp"/>
	<field name="EMPRESA_HORA_ATUAL_STR" class="java.lang.String"/>
	<field name="EMPRESA_EMAIL" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLIENTE" class="java.lang.Double"/>
	<field name="CLIENTE_NOME" class="java.lang.String"/>
	<field name="CLIENTE_RG" class="java.lang.String"/>
	<field name="CLIENTE_FONE_CEL" class="java.lang.String"/>
	<field name="CLIENTE_FONE_RES" class="java.lang.String"/>
	<field name="CLIENTE_FONE_COM" class="java.lang.String"/>
	<field name="CLIENTE_FONE_FAX" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="CLIENTE_INSC_ESTAD" class="java.lang.String"/>
	<field name="CLIENTE_CGC_CPF" class="java.lang.String"/>
	<field name="CLIENTE_CGC" class="java.lang.String"/>
	<field name="CLIENTE_CPF" class="java.lang.String"/>
	<field name="CLIENTE_COD_CLASSE" class="java.lang.String"/>
	<field name="CLIENTE_ENDERECO_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_TELEFONE_COMPLETO" class="java.lang.String"/>
	<field name="CLIENTE_UF" class="java.lang.String"/>
	<field name="CLIENTE_ESTADO" class="java.lang.String"/>
	<field name="CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="CLIENTE_CEP" class="java.lang.String"/>
	<field name="CLIENTE_RUA" class="java.lang.String"/>
	<field name="CLIENTE_COMPLEMENTO" class="java.lang.String"/>
	<field name="CLIENTE_FACHADA" class="java.lang.String"/>
	<field name="CLIENTE_FONE" class="java.lang.String"/>
	<field name="CLIENTE_PREFIXO" class="java.lang.String"/>
	<field name="FATURAR_NOME" class="java.lang.String"/>
	<field name="FATURAR_FONE" class="java.lang.String"/>
	<field name="FATURAR_ESTADO" class="java.lang.String"/>
	<field name="FATURAR_CIDADE" class="java.lang.String"/>
	<field name="FATURAR_ENDERECO" class="java.lang.String"/>
	<field name="FATURAR_TELEFONE_FAX" class="java.lang.String"/>
	<field name="FATURAR_BAIRRO" class="java.lang.String"/>
	<field name="FATURAR_CEP" class="java.lang.String"/>
	<field name="FATURAR_CGC" class="java.lang.String"/>
	<field name="FATURAR_IE" class="java.lang.String"/>
	<field name="MEDIA_KM_MENSAL" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_TERCEIROS" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_LAVAGEM" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_REVISAO" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_MECANICA" class="java.lang.Double"/>
	<field name="TOT_SERVICOS_VAL_GERAIS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_PECAS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_ACESSORIOS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_OUTROS" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_LUBRIFICANTE" class="java.lang.Double"/>
	<field name="TOT_PECAS_VALOR_COMBUSTIVEL" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="292" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="15" uuid="31776af4-f078-418b-a43a-02e65206f4d4">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="10" y="0" width="407" height="15" uuid="4835cdb9-3e9a-47cb-b539-7b439aa4c93a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[Ordem de Serviço]]></text>
				</staticText>
				<textField>
					<reportElement x="490" y="2" width="61" height="11" uuid="064f8e45-9b51-4d49-88d4-ec79d2b7f44c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Nº " + $F{OS_NUMERO_OS}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="15" width="555" height="159" uuid="e11f5dc5-9c21-435d-b162-283e1f8c8a3a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<frame>
					<reportElement x="417" y="0" width="138" height="159" uuid="9a336147-4fb6-4ad8-b4fe-1f56422d2b0e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement x="4" y="4" width="121" height="11" uuid="5ff73ff0-bec0-4b2a-895e-f6de5f2f755c"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<text><![CDATA[Data    /      Hora de Abertura]]></text>
					</staticText>
					<staticText>
						<reportElement x="4" y="41" width="123" height="11" uuid="*************-4cfc-9c40-ab87db63f695"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<text><![CDATA[Data    /    Hora de Encerramento]]></text>
					</staticText>
					<staticText>
						<reportElement x="4" y="73" width="123" height="11" uuid="03d73963-d254-4a6e-b8d0-d442d0e32a37"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<text><![CDATA[Valor de Reparo / Orçamento]]></text>
					</staticText>
					<staticText>
						<reportElement x="4" y="98" width="67" height="11" uuid="dd612cb3-aa43-45d1-9ed2-9c2d5471968e"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<text><![CDATA[Numero Cor Prisma:]]></text>
					</staticText>
					<staticText>
						<reportElement x="4" y="109" width="28" height="11" uuid="8bdc30dd-3955-4373-977b-f006fa3a5e3b"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<text><![CDATA[ID.Stars:]]></text>
					</staticText>
					<staticText>
						<reportElement x="4" y="120" width="37" height="11" uuid="10f90368-66e9-467e-b1a2-3adea2700b1d"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<text><![CDATA[C. Tecnico:]]></text>
					</staticText>
					<staticText>
						<reportElement x="4" y="131" width="37" height="11" uuid="56b1bae5-ee17-46f7-a62c-8e95a9f9540a"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<text><![CDATA[SG Padrão:]]></text>
					</staticText>
					<staticText>
						<reportElement x="4" y="142" width="47" height="11" uuid="c7479712-4f22-47ad-ad91-d1748b6f30e9"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<text><![CDATA[EX SG Padrão:]]></text>
					</staticText>
					<textField>
						<reportElement x="5" y="16" width="46" height="11" uuid="a2e59a70-577f-48b2-bc64-a21126d2143a"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="56" y="16" width="46" height="11" uuid="6259298a-0333-4b00-9845-868cf11ccf22"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="5" y="53" width="46" height="11" uuid="dc194abf-c1e7-40ea-aa81-8594bd931536"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="56" y="53" width="71" height="11" uuid="2e330699-07d4-41f0-ad08-563fbd854b3c"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="¤#,##0.00;¤-#,##0.00">
						<reportElement x="6" y="85" width="121" height="11" uuid="74423f24-4dc1-4871-b5e7-469e82bea082"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_TOTAL_OS}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="71" y="98" width="56" height="11" uuid="4eb5c2ab-110e-4132-80aa-030c489e4e87"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_TOTAL_ORCAMENTO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="32" y="109" width="95" height="11" uuid="d062a01c-901f-4c20-8ff7-1893adc80201"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_CODIGO_OPCIONAL}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="41" y="120" width="86" height="11" uuid="bf3d1f8d-ce06-4493-b83c-5194ab1e1a85"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="41" y="131" width="86" height="11" uuid="2e29cdf7-878d-46ee-8082-d41765869073"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_GARANTIA_SG_PADRAO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="51" y="142" width="76" height="11" uuid="8b4d1229-8963-4b1f-9df5-8e4037a81a71"/>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font fontName="Calibri" size="8"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{OS_GARANTIA_SG_EXEMPLO}]]></textFieldExpression>
					</textField>
				</frame>
				<image scaleImage="FillFrame">
					<reportElement x="0" y="0" width="87" height="72" uuid="90bc31b2-c2d6-4b1a-b323-eb0b0db07529">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600454.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="91" y="3" width="42" height="11" uuid="38e0e100-6733-4934-9b4b-5654fdfae7a8"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Distribuidor]]></text>
				</staticText>
				<staticText>
					<reportElement x="91" y="15" width="42" height="11" uuid="e8ba4cd8-e44c-4c2f-923d-33ad31e66c7f"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Endereço]]></text>
				</staticText>
				<staticText>
					<reportElement x="91" y="38" width="19" height="11" uuid="289caac4-0d4a-46ba-b9c4-08cfa9cec7e9"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Fone]]></text>
				</staticText>
				<staticText>
					<reportElement x="91" y="27" width="42" height="11" uuid="31d49998-b747-42dd-be73-a06179a8e714"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[CGC:]]></text>
				</staticText>
				<staticText>
					<reportElement x="172" y="38" width="19" height="11" uuid="692a566e-cae9-4ac8-afa1-e6e70364faed"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Fax]]></text>
				</staticText>
				<textField>
					<reportElement x="133" y="3" width="266" height="11" uuid="6154a7d3-23bc-4880-b6e4-d72cfe4663a3"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="133" y="15" width="266" height="11" uuid="097d0398-ea72-493d-9dcb-50f6af5ab77d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="110" y="38" width="62" height="11" uuid="719b6860-97c6-4929-b5e8-eb09367c2bdf"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_FONE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="191" y="38" width="82" height="11" uuid="e602e738-c13f-42b5-b538-0d32ead64253"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_FAX}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="133" y="27" width="82" height="11" uuid="b87a3465-93ac-4bcf-83dc-6594012448ae"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_CGC}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="247" y="27" width="12" height="11" uuid="878f0da5-3d99-4b7b-bb3c-b357a817eb3b"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[IE:]]></text>
				</staticText>
				<textField>
					<reportElement x="259" y="27" width="82" height="11" uuid="2076517c-fb3a-4adf-8a20-b0390f57f8bc"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EMPRESAS_CGC}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="87" width="417" height="15" uuid="0cf16a22-ec77-4981-be7c-89bdce742520">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="9" y="0" width="106" height="15" uuid="48d2d0a9-aac3-4dcf-8452-458e9107c6a3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[DADOS DO CLIENTE]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="102" width="417" height="72" uuid="89c70f3c-fc02-4eb2-836c-8ecee7c18f41">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="3" y="3" width="34" height="11" uuid="ca335426-ee20-4e50-baaf-9a25a692896b"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="14" width="34" height="11" uuid="c009482f-b9d5-44c8-9b9f-e0fd8023263f"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Endereço:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="25" width="34" height="11" uuid="49265604-66c1-4e28-aef3-5a4aa1a3adcc"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="36" width="34" height="11" uuid="1d2291a6-eccc-4087-8e17-b906c6676e78"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="47" width="34" height="11" uuid="258154ac-193b-4d92-baca-32081fdae0fe"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[CPF:]]></text>
				</staticText>
				<staticText>
					<reportElement x="193" y="36" width="13" height="11" uuid="ee7d1814-3f87-425c-bc1f-dfeb3cd49f8a"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[UF:]]></text>
				</staticText>
				<staticText>
					<reportElement x="311" y="4" width="31" height="11" uuid="b62b3c87-c1d6-4221-8728-8e503675913b"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Tel. Res:]]></text>
				</staticText>
				<staticText>
					<reportElement x="311" y="15" width="31" height="11" uuid="dff6a308-2ba7-47ab-a79c-a6a8de3fa50d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Tel. Cel:]]></text>
				</staticText>
				<staticText>
					<reportElement x="311" y="26" width="31" height="11" uuid="c8ef5985-3a27-44b0-bcf3-e830aad5e745"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Tel. Com:]]></text>
				</staticText>
				<staticText>
					<reportElement x="119" y="47" width="16" height="11" uuid="beada80a-f96c-41a4-9721-28410b4b5672"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[RG:]]></text>
				</staticText>
				<staticText>
					<reportElement x="216" y="47" width="16" height="11" uuid="1df990a3-fdeb-4cbd-beaf-e8de4bf56caa"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<textField>
					<reportElement x="37" y="14" width="266" height="11" uuid="9b61c8a7-9651-4476-8da1-a4296b8765fd"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_ENDERECO_COMPLETO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="37" y="3" width="266" height="11" uuid="f3be1c1f-2091-42fc-94f9-35f9c0d69a39"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="37" y="36" width="152" height="11" uuid="97d4fcaa-aa9c-4a8c-8dbe-b0f19534eff8"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="37" y="25" width="266" height="11" uuid="0be00ac3-a268-4fac-b8ff-21dab22963af"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="37" y="47" width="76" height="11" uuid="0b45e124-d24b-45a6-9f8b-db04bca20d28"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CGC_CPF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="135" y="47" width="76" height="11" uuid="d118be0b-0e90-4c75-b2aa-45ad3c6dade9"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_RG}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="206" y="36" width="76" height="11" uuid="e4fb63a0-e168-43bd-8ceb-d9d49f20c430"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_UF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="232" y="47" width="76" height="11" uuid="0b542cad-2e57-4a89-b933-71d71523349d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="342" y="4" width="72" height="11" uuid="ff3fd3aa-ae8f-4a79-bb76-05afff8b1cb6"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="342" y="15" width="72" height="11" uuid="8f5aa0ad-a91f-429f-8574-5371ad5b2515"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_CEL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="342" y="26" width="72" height="11" uuid="7424d784-0185-4abd-b668-199096d67f84"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CLIENTE_FONE_COM}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="174" width="555" height="15" uuid="b666a7d9-e231-4ba5-a239-2ca80d9fb864">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="9" y="0" width="290" height="15" uuid="fcb10845-77a1-426f-b839-240d426fe134">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[DADOS IDENTIFICAÇÃO DO VEICULO]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="189" width="555" height="50" uuid="c94bfd09-01b7-4276-9b34-9a18dcbc0997">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="2" y="3" width="47" height="11" uuid="accda401-8182-4d75-86bf-e08db88e4522"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Modelo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="2" y="14" width="47" height="11" uuid="8fc2b5d7-1d4d-4343-a971-ec49c5dbbce8"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Chassi:]]></text>
				</staticText>
				<staticText>
					<reportElement x="2" y="25" width="47" height="11" uuid="79df3975-6662-4599-a09b-45584d3554a1"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Cor:]]></text>
				</staticText>
				<staticText>
					<reportElement x="2" y="36" width="47" height="11" uuid="14acf82a-3edf-4337-8eab-ddb0767593dd"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Distrib:]]></text>
				</staticText>
				<staticText>
					<reportElement x="340" y="4" width="27" height="11" uuid="0f077864-aa21-466a-977e-83b0695c67fe"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[KM:]]></text>
				</staticText>
				<staticText>
					<reportElement x="340" y="15" width="27" height="11" uuid="f7cb0faa-d0e1-4319-a9a6-a8789a313526"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[TMA:]]></text>
				</staticText>
				<staticText>
					<reportElement x="340" y="26" width="27" height="11" uuid="06acfe6e-f344-4ba3-8199-73beb65cf700"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Motor:]]></text>
				</staticText>
				<staticText>
					<reportElement x="441" y="15" width="47" height="11" uuid="50fa5077-9a30-490d-91a0-f2977acb09b4"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Placa:]]></text>
				</staticText>
				<staticText>
					<reportElement x="441" y="4" width="47" height="11" uuid="60d76d9e-00bc-44a2-a601-9b5abef23529"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Ano/Modelo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="441" y="26" width="47" height="11" uuid="138de256-0a25-437d-be6f-3b97f2ecaf5e"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Combustivel]]></text>
				</staticText>
				<staticText>
					<reportElement x="441" y="37" width="47" height="11" uuid="d0ff4bbd-31ec-4ea7-8c16-a32ea6520847"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Data Venda:]]></text>
				</staticText>
				<textField>
					<reportElement x="49" y="3" width="281" height="11" uuid="f9243e91-7ee0-42b5-9ddf-aee1f8bb6273"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESC_PROD_MOD}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="49" y="14" width="281" height="11" uuid="3e737396-bd44-4f34-a583-e35140cee021"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_CHASSI}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="49" y="25" width="281" height="11" uuid="0096586d-4f58-480b-85aa-2264cc398c99"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="49" y="36" width="281" height="11" uuid="7cf85f2e-e440-4c1e-9264-ec05d14b45dc"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CONCESSIONARIA_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="367" y="26" width="63" height="11" uuid="383e22ef-95c8-4303-b0d9-92859ad681f7"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_NUMERO_MOTOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="367" y="15" width="63" height="11" uuid="88cf5fe2-a2b6-4f04-9da4-a03c64099f2a"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_LINHA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="367" y="4" width="63" height="11" uuid="8cda3430-74ff-44c2-8f0a-5ed0a0beeeb6"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_KM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="488" y="15" width="63" height="11" uuid="8a8462a9-f1c3-475a-a6e2-bd34729abd6f"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="488" y="4" width="63" height="11" uuid="ac90a6b4-0570-4a89-9b88-655c177751fa"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_ANO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="488" y="37" width="63" height="11" uuid="c2f66e25-620a-4574-8369-6c6618bc4d84"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="489" y="26" width="51" height="11" uuid="de18ff39-e1db-4058-8ef3-65b0ee050703"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_COMBUSTIVEL} < 19 ? "":
$F{OS_COMBUSTIVEL} < 39 ? "X" :
$F{OS_COMBUSTIVEL} < 59 ? "X   X":
$F{OS_COMBUSTIVEL} < 79 ? "X   X   X":
 "X   X   X   X"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="496" y="35" width="1" height="2" uuid="880eae45-ffd4-44b0-bc25-28241220cc9a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="502" y="33" width="1" height="4" uuid="64c6a3d1-8be5-4203-92f3-2bef4620f453">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="508" y="35" width="1" height="2" uuid="0853e718-5265-4a32-b374-8148f8d5e306">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="514" y="31" width="1" height="6" uuid="2e8ee505-4005-49af-814c-9c91bfdaad26">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="520" y="35" width="1" height="2" uuid="1a0f03c5-387d-4cc1-9971-35c0c069e173">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="526" y="33" width="1" height="4" uuid="b95350e0-e827-4310-95c3-f22d51d526df">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="532" y="35" width="1" height="2" uuid="1cdbef01-b18f-4a29-94d9-2a3f3ca35262">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
			</frame>
			<frame>
				<reportElement x="0" y="239" width="555" height="15" uuid="c8a283fb-f9a2-40ef-8465-ea6e51d740fe">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="9" y="0" width="290" height="15" uuid="659c826d-8bb3-42cb-a2b2-6373b18e4b8c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<text><![CDATA[DADOS DA FATURA]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="254" width="555" height="38" uuid="9c2a4209-abc3-4771-9a7b-0462ca626520">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="3" y="25" width="39" height="11" uuid="6571eeb4-e641-4ba5-ad53-e6a6f6198c3d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="3" width="39" height="11" uuid="7289df28-68b1-46ef-9a45-ec8995447b6d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<staticText>
					<reportElement x="3" y="14" width="39" height="11" uuid="69c58308-2275-4ad2-95ed-d8bedc9cf03c"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Endereço:]]></text>
				</staticText>
				<staticText>
					<reportElement x="343" y="14" width="26" height="11" uuid="cc9d1e57-8848-4b70-bc0d-5a6db8abe9a0"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Tel:]]></text>
				</staticText>
				<staticText>
					<reportElement x="343" y="3" width="26" height="11" uuid="e4b030a9-72ca-411d-9b7e-1d6c686473b1"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement x="343" y="25" width="26" height="11" uuid="da9d1279-7402-4614-af03-fbc602d81aa7"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Estado:]]></text>
				</staticText>
				<staticText>
					<reportElement x="446" y="3" width="39" height="11" uuid="a0d263fa-ad1b-4a02-ab3d-4a2a21128b7c"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Insc. Estad.:]]></text>
				</staticText>
				<staticText>
					<reportElement x="446" y="14" width="39" height="11" uuid="9ddabe45-d9d8-4baf-87a0-5226d924e3ba"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[CGC:]]></text>
				</staticText>
				<textField>
					<reportElement x="42" y="14" width="298" height="11" uuid="26b9ae08-4301-4483-b73c-0755a933c1f8"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_ENDERECO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="42" y="25" width="298" height="11" uuid="afd3418e-43d4-43c6-b735-acd940778ec8"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="42" y="3" width="298" height="11" uuid="80030d9c-1760-4396-a65d-550da7408f80"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="369" y="14" width="63" height="11" uuid="8b984f4d-0edd-4084-9905-b29854473053"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_FONE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="369" y="25" width="63" height="11" uuid="cbf33a6f-b965-4357-acf3-7da70c58623a"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_ESTADO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="369" y="3" width="63" height="11" uuid="b49e2369-7fb2-42cb-9a90-8736eae6d8d7"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="485" y="3" width="63" height="11" uuid="96e31e13-2ef7-432b-9bfc-bad845c50604"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_IE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="485" y="14" width="63" height="11" uuid="cda5f4dd-b431-4437-8733-66e26f23a1c1"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{FATURAR_CGC}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="22">
			<subreport>
				<reportElement stretchType="ContainerHeight" x="0" y="0" width="555" height="22" isPrintWhenDetailOverflows="true" uuid="c460a2dc-9fa8-4553-a7cb-cbe799c19af3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{DIR_SUBREPORT} + "OsFordSubServicos.jrxml"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<summary>
		<band height="119" splitType="Stretch">
			<frame>
				<reportElement x="0" y="-1" width="555" height="40" uuid="aa4a9ec1-0fa0-4fa1-8a09-4ee3e11df2ef">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement x="9" y="-1" width="533" height="39" uuid="36935eed-**************-89e6f82e617c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box topPadding="3" leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{OS_OBSERVACAO}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="39" width="555" height="79" uuid="6ea3e906-c13c-43cf-9de5-146293c330ed">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="159" y="32" width="236" height="15" uuid="fa2104a6-f3b3-4b7e-86d2-be6812de1cfd"/>
					<box>
						<topPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente]]></text>
				</staticText>
				<staticText>
					<reportElement x="213" y="3" width="128" height="15" uuid="1db8360c-a3c2-40ec-8eb3-cae202ac651f"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[AUTORIZO A REALIZAÇÃO DO SERVIÇO]]></text>
				</staticText>
				<staticText>
					<reportElement x="214" y="56" width="126" height="15" uuid="f001f5dc-2a5a-499a-8227-9417e94a2842"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Calibri" size="8"/>
					</textElement>
					<text><![CDATA[DATA: _______/______/___________]]></text>
				</staticText>
			</frame>
		</band>
	</summary>
</jasperReport>
