<?xml version="1.0" encoding="UTF-8"?>
<project name="jaspertask" basedir=".">
    <path id="jasper.classpath">
        <fileset dir="../../target_reports/jar/" includes="*.jar" />
    </path>
    <taskdef name="jrc" classname="net.sf.jasperreports.ant.JRAntCompileTask" classpathref="jasper.classpath" />
    <target name="compileReports">
        <jrc destdir="../../target_reports/reports/">
            <src>
                <fileset dir="../">
                    <include name="**/*.jrxml"/>
                </fileset>
            </src>
            <classpath refid="jasper.classpath"/>
        </jrc>
    </target>
</project>