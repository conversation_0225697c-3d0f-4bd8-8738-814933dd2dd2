<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsToyotaSubPecas" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString>
		<![CDATA[SELECT
 DECODE(OS.SEQUENCIA_DAV, 0, OS_REQUISICOES.COD_ITEM,
                             NVL(ITENS_CUSTOS.COD_FISCAL_ITEM,OS_REQUISICOES.COD_ITEM)) COD_ITEM,
 OS_REQUISICOES.REQUISICAO, OS_REQUISICOES.COD_FORNECEDOR,
 DECODE(FORNECEDOR_ESTOQUE.OFICIAL, 'N', '*' || ITENS.DESCRICAO, ITENS.DESCRICAO) AS DESCRICAO,
 OS_REQUISICOES.QUANTIDADE, OS_REQUISICOES.CAUSADORA,  
 OS_REQUISICOES.ITEM, ITENS.COD_MAX_DESC,
 NVL(ESTOQUE.QTDE, 0) AS ESTOQUE_QTDE,
 NVL(ESTOQUE.RESERVADO, 0) AS RESERVADO,
 DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
   DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
     DECODE(OS_TIPOS.INTERNO, 'S',
             ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                                   DECODE(ITENS.COD_TRIBUTACAO, '1',
                                     DECODE(PARM_SYS.REGIME_ICMS, 'S',
                                       DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                                         DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                     0),
                                              0),
                                            OS_TIPOS.AUMENTO_PRECO_PECA),
                                          0),
                                    OS_TIPOS.AUMENTO_PRECO_PECA)) *
                   DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                                                    'G', OS_REQUISICOES.PRECO_GARANTIA,
                                                    'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                                                    'P', OS_REQUISICOES.PRECO_FABRICA,
                                                    OS_REQUISICOES.CUSTO_CONTABIL)
                  ) / 100,
       DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA,'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                                                               OS_REQUISICOES.PRECO_GARANTIA),
         DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
           DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
             ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
               DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                                                         OS_REQUISICOES.PRECO_VENDA) )/100)))))) AS PRECO_VENDA,
 OS_REQUISICOES.QUANTIDADE *
   DECODE(OS.STATUS_OS, 1,  OS_REQUISICOES.PRECO_FINAL,
     DECODE(OS.CORTESIA, 'S', OS_REQUISICOES.PRECO_CORTESIA,
       DECODE(OS_TIPOS.INTERNO, 'S',
                   ROUND((100 + DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS, 'S' ,
                                         DECODE(ITENS.COD_TRIBUTACAO, '1',
                                           DECODE(PARM_SYS.REGIME_ICMS, 'S',
                                             DECODE(PARM_SYS2.ACESSORIO_TRIBUTA, 'S',
                                               DECODE(ICC.CLASSE_PECA,  2, OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                           0),
                                                    0),
                                                  OS_TIPOS.AUMENTO_PRECO_PECA),
                                                0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                     DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'V', OS_REQUISICOES.PRECO_VENDA,
                                                      'G', OS_REQUISICOES.PRECO_GARANTIA,
                                                      'F', OS_REQUISICOES.CUSTO_FORNECEDOR,
                                                      'P', OS_REQUISICOES.PRECO_FABRICA,
                                                      OS_REQUISICOES.CUSTO_CONTABIL)
                    ) / 100,
         DECODE(OS_TIPOS.GARANTIA, 'S', DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_GARANTIA),
                                                                                  OS_REQUISICOES.PRECO_GARANTIA),
           DECODE(NVL(OS.FABRICA, 'N'), 'S', OS_REQUISICOES.PRECO_GARANTIA,
             DECODE(SIGN(OS.FRANQUIA), 1, PRECO_FRANQUIA,
               ROUND((100-NVL(SEGURADORA.DESCONTO_REQUISICAO, 0))*
                DECODE(OS_TIPOS.TIPO_PRECO_PECA, 'P', NVL(OS_REQUISICOES.PRECO_FABRICA, OS_REQUISICOES.PRECO_VENDA),
                                                          OS_REQUISICOES.PRECO_VENDA))/100)))))) AS PRECO_TOTAL,
      OS_REQUISICOES.SEQ_PAF_ITEM,
      'A' AS STATUS
FROM OS_REQUISICOES, ESTOQUE, ITENS, OS, VW_OS_TIPOS OS_TIPOS, FORNECEDOR_ESTOQUE , SEGURADORA,
   ITENS_FORNECEDOR, ITENS_CLASSE_CONTABIL ICC, PARM_SYS, PARM_SYS2, ITENS_CUSTOS
WHERE (OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM)
  AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR
  AND (OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM (+))
  AND (OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR (+))
  AND (OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA (+))
  AND (OS_REQUISICOES.NUMERO_OS = OS.NUMERO_OS)
  AND (OS_REQUISICOES.COD_EMPRESA = OS.COD_EMPRESA)
  AND (OS.TIPO = OS_TIPOS.TIPO)
  AND (OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA)
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
  AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL (+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = ITENS_CUSTOS.COD_EMPRESA (+)
  AND OS_REQUISICOES.COD_ITEM  = ITENS_CUSTOS.COD_ITEM (+)
  AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_CUSTOS.COD_FORNECEDOR (+)
  AND (OS_REQUISICOES.NUMERO_OS = $P{NUMERO_OS})
  AND (OS_REQUISICOES.COD_EMPRESA = $P{COD_EMPRESA})
ORDER BY OS_REQUISICOES.ITEM, OS_REQUISICOES.COD_ITEM]]>
	</queryString>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="REQUISICAO" class="java.lang.Double"/>
	<field name="COD_FORNECEDOR" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.lang.Double"/>
	<field name="CAUSADORA" class="java.lang.String"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="COD_MAX_DESC" class="java.lang.String"/>
	<field name="ESTOQUE_QTDE" class="java.lang.Double"/>
	<field name="RESERVADO" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="PRECO_TOTAL" class="java.lang.Double"/>
	<field name="SEQ_PAF_ITEM" class="java.lang.Double"/>
	<field name="STATUS" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="27">
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="555" height="16" uuid="8f4bea14-f928-4292-97d9-8208092658d8"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="2" y="3" width="226" height="10" uuid="cdefac57-f583-4ac5-b8c4-a5257746dec1"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Requisições]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="0" y="16" width="555" height="11" uuid="4ded9633-9ea0-419f-8744-5375e2072398">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="294" y="1" width="17" height="10" uuid="a7335bbd-44de-45dd-a7fc-c3626609bcec"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cs.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="278" y="1" width="15" height="10" uuid="b1959c71-d930-4bf0-976a-7d0078a448e9"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[LD]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="1" width="22" height="10" uuid="c811038d-61c0-490c-b55e-bfd1504c237e"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Item]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="26" y="1" width="97" height="10" uuid="70a659da-67a9-4d88-97d8-5e943b266bba"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Código do Item]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="126" y="1" width="151" height="10" uuid="71b0c25f-73a2-48c8-9047-277effc250c3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Descrição do Item]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="311" y="1" width="51" height="10" uuid="46364ee8-b889-4503-b6e8-df3fed8ffbbb"/>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Requisição]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="364" y="1" width="25" height="10" uuid="cf0cbd73-83e8-4628-903f-ead8a3499e54"/>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Qtde]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="392" y="1" width="52" height="10" uuid="ed08d243-6341-4575-bd95-e5c7c5ef775f"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Estoque]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="448" y="1" width="57" height="10" uuid="e6f24526-f3d3-4222-b54f-b57775a47626"/>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Preço Unit.]]></text>
				</staticText>
				<line>
					<reportElement x="24" y="0" width="1" height="11" uuid="b583bf77-8c1a-4a33-a950-d934595a42e6"/>
				</line>
				<line>
					<reportElement x="124" y="0" width="1" height="11" uuid="51d8946f-1eb7-4a65-9674-d44d22ef72be"/>
				</line>
				<line>
					<reportElement x="277" y="0" width="1" height="11" uuid="597ee35c-2605-46c4-9bce-0de5c15cb6f3"/>
				</line>
				<line>
					<reportElement x="293" y="0" width="1" height="11" uuid="0b849155-c09d-4d84-b2a7-13b755416bf9"/>
				</line>
				<line>
					<reportElement x="311" y="0" width="1" height="11" uuid="0feda98e-10d8-4284-9054-ed1feb0cd518"/>
				</line>
				<line>
					<reportElement x="363" y="0" width="1" height="11" uuid="151fcf7b-35e2-47cd-8def-ecad37180b60"/>
				</line>
				<line>
					<reportElement x="391" y="0" width="1" height="11" uuid="1d17a6da-85fc-46ef-a97e-c84686ae60a0"/>
				</line>
				<line>
					<reportElement x="446" y="0" width="1" height="11" uuid="feab6cbd-4a0d-4446-bd14-77e0bc825f9e"/>
				</line>
				<line>
					<reportElement x="505" y="0" width="1" height="11" uuid="16bd1f6e-86aa-42b3-ab7f-ae711146d691"/>
				</line>
				<staticText>
					<reportElement mode="Transparent" x="506" y="1" width="47" height="10" uuid="4e8d5a03-347f-4e4c-bb4c-b086eee09f2d"/>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Valor Final]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="12" uuid="dde6e70c-7304-4f85-93ab-89f39849c8c0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="1" y="1" width="21" height="11" uuid="50aed6b2-e14b-4b9c-b84b-f2d14931f861">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="26" y="1" width="97" height="11" uuid="7ea264d4-1c77-4640-8af2-5f667ebbff21"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COD_ITEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="126" y="1" width="151" height="11" uuid="732e2d03-1d8d-47db-a23c-d55cb093e6d0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="278" y="1" width="15" height="11" uuid="64309bb6-434d-49c8-8592-19a0fe95c67b"/>
					<textElement textAlignment="Center">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COD_MAX_DESC}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="312" y="1" width="51" height="11" uuid="3c12e177-71f2-4786-8b33-c42acd36aa6d"/>
					<box rightPadding="3"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{REQUISICAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="364" y="1" width="25" height="11" uuid="c06bea9b-bf32-45c4-a411-33498b2f2969"/>
					<box rightPadding="3"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="391" y="1" width="28" height="11" uuid="9f1c272d-dd2e-4149-a689-6d2f4f0d7f60"/>
					<box rightPadding="3"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ESTOQUE_QTDE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="422" y="1" width="24" height="11" uuid="40fb89b3-1bec-46d0-86b7-859535fb32c6"/>
					<box rightPadding="3"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{RESERVADO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="448" y="1" width="57" height="11" uuid="4d04f7a7-f10f-43b3-ab73-b3b4d7de4194"/>
					<box rightPadding="3"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="506" y="1" width="49" height="11" uuid="c378d59c-6c9e-4bf7-894a-0de914bdcb7a"/>
					<box rightPadding="3"/>
					<textElement textAlignment="Right">
						<font size="8" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRECO_TOTAL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="298" y="2" width="8" height="8" uuid="ae7854ce-0edb-40dc-8143-e1d5d4947b06">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CAUSADORA} == "S" ? "X":" "]]></textFieldExpression>
					<patternExpression><![CDATA[]]></patternExpression>
				</textField>
				<line>
					<reportElement x="24" y="0" width="1" height="12" uuid="ac2025e5-201f-4f25-9f2a-b19f966e8be4"/>
				</line>
				<line>
					<reportElement x="419" y="0" width="1" height="12" uuid="c688c9c4-e83e-484d-93e2-06e442e8c9b6"/>
				</line>
				<line>
					<reportElement x="124" y="0" width="1" height="12" uuid="c8e9aaaa-3f31-4f97-b7c3-2367083eee48"/>
				</line>
				<line>
					<reportElement x="505" y="1" width="1" height="12" uuid="45d3c152-c8e9-4b30-8490-5d5fe0ecee5e"/>
				</line>
				<line>
					<reportElement x="446" y="0" width="1" height="12" uuid="d40f36b9-c3b4-4825-a0d5-43a7416396e4"/>
				</line>
				<line>
					<reportElement x="391" y="0" width="1" height="12" uuid="6ceecb43-4ce7-455d-913e-08fb44b89ded"/>
				</line>
				<line>
					<reportElement x="363" y="0" width="1" height="12" uuid="c9ab39d9-abbb-4bae-a4dc-7ca24c92e09f"/>
				</line>
				<line>
					<reportElement x="311" y="0" width="1" height="12" uuid="3a3da7d7-ca5c-4c3e-be4f-eb1d6b2aa3f5"/>
				</line>
				<line>
					<reportElement x="293" y="0" width="1" height="12" uuid="d5b77a82-ab85-44c2-ba8b-d2fcaa861d7e"/>
				</line>
				<line>
					<reportElement x="277" y="0" width="1" height="12" uuid="7a7c8c7d-4c22-414f-b139-a5b3e64d96ad"/>
				</line>
			</frame>
		</band>
	</detail>
</jasperReport>
