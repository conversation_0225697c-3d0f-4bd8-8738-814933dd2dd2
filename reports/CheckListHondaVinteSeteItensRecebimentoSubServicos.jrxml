<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaVinteSeteItensRecebimentoSubServicos" columnCount="3" pageWidth="575" pageHeight="44" columnWidth="191" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select initcap(a.item_descricao) as item_descricao
		  , UPPER(a.selecionado_opcao_descricao) as descricao_opcao
		  , a.item_resposta_eh_observacao as resposta_eh_observacao
		  , a.selecionado_observacao as observacao
from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
where a.id_checklist = 20000
      and a.id_grupo = 20000]]>
	</queryString>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<detail>
		<band height="11">
			<printWhenExpression><![CDATA[new Boolean($F{RESPOSTA_EH_OBSERVACAO}.equals("N"))]]></printWhenExpression>
			<textField>
				<reportElement x="10" y="2" width="14" height="7" uuid="77ddab6e-f0ff-490c-b762-e870c423dece">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("INCLUSO")
? "X" 
: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="28" y="1" width="157" height="9" uuid="6245d408-899a-48a8-b27a-0105394cfd60">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{RESPOSTA_EH_OBSERVACAO}.equals("S"))]]></printWhenExpression>
			<textField>
				<reportElement x="28" y="1" width="22" height="9" uuid="add3aab8-6c18-42db-843d-c9568c56cbf9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA["Outros"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="10" y="2" width="14" height="7" uuid="8026cb4c-c493-40d5-b147-fb28467bf79e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}.equals("") ?"":"X"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="50" y="1" width="135" height="9" uuid="ea7492a4-2d3c-402f-8e69-9df6225bacb3">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
