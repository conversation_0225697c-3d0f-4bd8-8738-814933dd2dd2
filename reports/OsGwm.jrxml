<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsGwm" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBSH.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="340"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="649"/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\34944\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[10805.0]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\temp\\image_zk\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="ASSINAR_DIGITALMENTE" class="java.lang.String">
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[WITH Q_OS AS
 (SELECT OS.COD_EMPRESA,
         OS_AGENDA.DATA_AGENDADA,
         TO_CHAR(OS_AGENDA.DATA_AGENDADA, 'DD/MM/YYYY HH24:MI') AS D_AG_STG,
         OS.STATUS_OS,
         OS.NUMERO_OS,
         OS.COD_OS_AGENDA,
         ABS(OS.NUMERO_OS) AS ABS_OSNUM,
         OS.COD_CLIENTE,
         OS.CLIENTE_RAPIDO,
         OS.TIPO_ENDERECO,
         OS.OBSERVACAO,
         OS.EXTENDIDA,
         OS.SEGURADORA,
         OS.DATA_EMISSAO,
		 OS.DATA_ENTREGA,
         (TO_DATE(OS.DATA_EMISSAO, 'DD/MM/YYYY') || ' AS ' ||
         OS.HORA_EMISSAO) AS DH_EMISSAO,
         DECODE(OS.DATA_LIBERADO,
                NULL,
                '',
                (TO_DATE(OS.DATA_LIBERADO, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_LIBERADO)) AS DH_LIBERADO,
         DECODE(OS.DATA_ENCERRADA,
                NULL,
                '',
                (TO_DATE(OS.DATA_ENCERRADA, 'DD/MM/YYYY') || ' AS ' ||
                OS.HORA_ENCERRADA)) AS DH_ENCERRADO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'DD'), 1, 2) AS DIA_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'MM'), 1, 2) AS MES_EMISSAO,
         SUBSTR(TO_CHAR(OS.DATA_EMISSAO, 'YYYY'), 1, 4) AS ANO_EMISSAO,
         OS.HORA_EMISSAO,
         OS.HORA_ENCERRADA,
         OS.DATA_ENCERRADA,
         OS.HORA_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 1, 2) AS HORA24_PROMETIDA,
         SUBSTR(OS.HORA_PROMETIDA, 4, 2) AS MINUTO_PROMETIDA,
         
         OS.DATA_PROMETIDA,
         OS.DATA_PROMETIDA_REVISADA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'DD'), 1, 2) AS DIA_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'MM'), 1, 2) AS MES_PROMETIDA,
         SUBSTR(TO_CHAR(OS.DATA_PROMETIDA, 'YYYY'), 1, 4) AS ANO_PROMETIDA,
         
         OS.VALOR_SERVICOS_BRUTO,
         OS.VALOR_ITENS_BRUTO,

         (case when nvl(VALOR_SERVICOS_BRUTO,0) = 0 then 0 else OS.DESCONTOS_SERVICOS / OS.VALOR_SERVICOS_BRUTO END) AS DESCONTO_SERVICO_PORCENT,
         (case when nvl(OS.VALOR_ITENS_BRUTO,0) = 0 then 0 else OS.DESCONTOS_ITENS / OS.VALOR_ITENS_BRUTO END) AS DESCONTO_ITENS_PORCENT,
              
         (OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) AS TOTAL_SERVICOS_LIQUIDO,
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS) AS TOTAL_ITENS_LIQUIDO,
     
         ((OS.VALOR_SERVICOS_BRUTO - OS.DESCONTOS_SERVICOS) +
         (OS.VALOR_ITENS_BRUTO - OS.DESCONTOS_ITENS)) AS TOTAL_OS,
         
     OS.COD_SEGURADORA,
         OS_DADOS_VEICULOS.ANO,
         OS_DADOS_VEICULOS.HORIMETRO,
         OS_DADOS_VEICULOS.PRISMA,
         OS_DADOS_VEICULOS.DATA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'DD'), 1, 2) AS DIA_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'MM'), 1, 2) AS MES_VENDA,
         SUBSTR(TO_CHAR(OS_DADOS_VEICULOS.DATA_VENDA, 'YYYY'), 1, 4) AS ANO_VENDA,
         OS_DADOS_VEICULOS.COMBUSTIVEL,
         OS_DADOS_VEICULOS.COR_EXTERNA,
         OS_DADOS_VEICULOS.PLACA,
         OS_DADOS_VEICULOS.KM,
         OS_DADOS_VEICULOS.CHASSI,
         OS_DADOS_VEICULOS.NUMERO_MOTOR,
         OS_DADOS_VEICULOS.NUMERO_RENAVAM,
         OS_DADOS_VEICULOS.SERIE,
         OS_DADOS_VEICULOS.COD_CONCESSIONARIA,
         OS_DADOS_VEICULOS.ESTADO_PINTURA,
         OS_DADOS_VEICULOS.JOGO_FERRAMENTAS,
         OS_DADOS_VEICULOS.ELASTICOS,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_D,
         OS_DADOS_VEICULOS.TAMPA_LATERAL_E,
         OS_DADOS_VEICULOS.FLANELA,
         OS.TIPO,
         OS_TIPOS.DESCRICAO AS TIPO_DESCRICAO,
         OS.TIPO || ' - ' || OS_TIPOS.DESCRICAO AS TIPO_COM_DESCRICAO,
         OS_TIPOS.GARANTIA,
         OS_TIPOS.REVISAO_GRATUITA,
         OS_TIPOS.INTERNO,
         OS_TIPOS.COD_CLIENTE AS CLIENTE_DO_TIPO,
         OS_TIPOS.OUTRO_CONCESSIONARIA,
         OS.NOME AS CONSULTOR,
         EMPRESAS_USUARIOS.NOME_COMPLETO AS CONSULTOR_COMPLETO,
         PRODUTOS.DESCRICAO_PRODUTO,
         PRODUTOS_MODELOS.DESCRICAO_MODELO,
         (PRODUTOS.DESCRICAO_PRODUTO || ' - ' ||
         PRODUTOS_MODELOS.DESCRICAO_MODELO) DESC_PROD_MOD,
         PRODUTOS_MODELOS.LINHA,
         MARCAS.DESCRICAO_MARCA,
         CONCESSIONARIAS.NOME CONCESSIONARIA_NOME,
         CONCESSIONARIAS.UF CONCESSIONARIA_UF,
         CONCESSIONARIAS.CIDADE CONCESSIONARIA_CIDADE,
         CONCESSIONARIAS.BAIRRO CONCESSIONARIA_BAIRRO,
         CONCESSIONARIAS.ENDERECO CONCESSIONARIA_RUA,
         CONCESSIONARIAS.CEP CONCESSIONARIA_CEP,
         CONCESSIONARIAS.CODIGO_PADRAO CONCESSIONARIA_CODIGO,
         UF_CONCESSIONARIA.DESCRICAO CONCESSIONARIA_ESTADO,
         
         (CASE
           WHEN NVL(OS.OS_ORIGEM_RETORNO, 0) > 0 THEN
            'S'
           ELSE
            'N'
         END) AS AG_RETORNO,
         NVL(OS_AGENDA.CLIENTE_AGUARDA, 'N') AS AG_CLIENTE_AGUARDA,
         NVL(OS_AGENDA.VEICULO_PLATAFORMA, 'N') AS AG_VEICULO_PLATAFORMA,
         NVL(OS_AGENDA.TAXI, 'N') AS AG_TAXI,
         NVL(OS_AGENDA.BLINDADO, 'N') AS AG_BLINDADO,
         NVL(OS_AGENDA.TESTE_RODAGEM, 'N') AS AG_TESTE_RODAGEM,
         NVL(OS_AGENDA.LEVAR_PECAS_SUBSTITUIDAS, 'N') AS AG_LEVAR_PECAS_SUBSTITUIDAS,
         NVL(OS_AGENDA.LAVAR_VEICULO, 'N') AS AG_LAVAR_VEICULO,
         NVL(OS_AGENDA.VEICULO_MODIFICADO, 'N') AS VEICULO_MODIFICADO,
         NVL(OS_AGENDA.DDW_GARANTIA, 'NÃO EXISTENTE') AS DDW_GARANTIA,
         OS.HORA_PROMETIDA_REVISADA,
         OS_TIPOS.TIPO_FABRICA,
         DECODE(OS_AGENDA.REC_INTERATIVA,
                'S',
                'VEÍCULO COM RECEPÇÃO INTERATIVA',
                'N',
                '') REC_INTERATIVA,
         OS_AGENDA.COD_TIPO_IMOBILIZADO,
         DECODE(OS_AGENDA.COD_TIPO_IMOBILIZADO,
                NULL,
                '',
                'VEICULO IMOBILIZADO: ' || TIPO_IMOBILIZADO.DESCRICAO) AS IMOBILIZADO,
         OS_AGENDA.REC_INTERATIVA AS RECEPCAO_INTERATIVA,
         DECODE(OS_AGENDA.COD_TIPO_MOBILIDADE,
                NULL,
                'SEM MOBILIDADE',
                TIPO_MOBILIDADE.DESCRICAO) MOBILIDADE_DESCRICAO,
         NVL((SELECT 'S'
               FROM JLR_CHASSI_BLINDADO J
              WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
                AND ROWNUM < 2),
             'N') AS BLINDADO,
         (SELECT J.NOME_BLINDADORA
            FROM JLR_CHASSI_BLINDADO J
           WHERE J.CHASSI = OS_DADOS_VEICULOS.CHASSI
             AND ROWNUM < 2) AS BLINDADORA,
             
       FORMA_PGTO.DESCRICAO AS FORMA_PAGAMENTO,
       NVL((SELECT 'SIM' FROM ALUGUEL_VEICULOS WHERE ROWNUM = 1 AND ALUGUEL_VEICULOS.CHASSI = OS_DADOS_VEICULOS.CHASSI),'NÃO') AS VEICULO_LOCACAO,
       NVL((SELECT 'SIM'
        FROM PACOTE_REV_GRATIS PRG
        WHERE PRG.CHASSI = OS_DADOS_VEICULOS.CHASSI
             AND ((OS_DADOS_VEICULOS.KM BETWEEN PRG.KM_MIN AND PRG.KM_MAX) OR 
                                (TRUNC(OS.DATA_EMISSAO) BETWEEN TRUNC(PRG.DTMIN) AND TRUNC(PRG.DTMAX)))
              AND ROWNUM = 1), 'NÃO') AS PACOTE_TRANQUILIDADE
    FROM OS,
         OS_DADOS_VEICULOS,
         OS_AGENDA,
         EMPRESAS_USUARIOS,
         VW_OS_TIPOS       OS_TIPOS,
         CONCESSIONARIAS,
         PRODUTOS,
         PRODUTOS_MODELOS,
         MARCAS,
         UF                UF_CONCESSIONARIA,
         TIPO_IMOBILIZADO,
         TIPO_MOBILIDADE,
         OS_PAGAMENTO,
         FORMA_PGTO
   WHERE OS.TIPO = OS_TIPOS.TIPO
     AND OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA(+)
     AND OS.NOME = EMPRESAS_USUARIOS.NOME
     AND OS.NUMERO_OS = OS_DADOS_VEICULOS.NUMERO_OS(+)
     AND OS.COD_EMPRESA = OS_DADOS_VEICULOS.COD_EMPRESA(+)
     AND OS_DADOS_VEICULOS.COD_CONCESSIONARIA =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+)
     AND OS.COD_PRODUTO = PRODUTOS.COD_PRODUTO
        
     AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA(+)
     AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS(+)
        
     AND PRODUTOS.COD_MARCA = MARCAS.COD_MARCA
     AND OS.COD_PRODUTO = PRODUTOS_MODELOS.COD_PRODUTO
     AND OS.COD_MODELO = PRODUTOS_MODELOS.COD_MODELO
     AND CONCESSIONARIAS.UF = UF_CONCESSIONARIA.UF(+)
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS_AGENDA.COD_TIPO_IMOBILIZADO = TIPO_IMOBILIZADO.COD_TIPO_IMOBILIZADO(+)
     AND OS_AGENDA.COD_TIPO_MOBILIDADE = TIPO_MOBILIDADE.COD_TIPO_MOBIL(+)
     AND OS.NUMERO_OS = OS_PAGAMENTO.NUMERO_OS(+)
     AND OS.COD_EMPRESA = OS_PAGAMENTO.COD_EMPRESA(+)
     AND OS_PAGAMENTO.COD_FORMA_PGTO = FORMA_PGTO.COD_FORMA_PGTO(+)
     AND OS_PAGAMENTO.COD_EMPRESA  = FORMA_PGTO.COD_EMPRESA(+)
     ),
Q_EMPRESA AS
 (SELECT EMPRESAS.COD_EMPRESA,
         EMPRESAS.NOME NOME_EMPRESA,
         EMPRESAS.CGC,
         EMPRESAS.FACHADA,
         EMPRESAS.ESTADO AS UF,
         (TRIM(EMPRESAS.CIDADE) || ' - ' || TRIM(EMPRESAS.ESTADO)) AS CIDADE,
         EMPRESAS.BAIRRO,
         EMPRESAS.COMPLEMENTO,
         (EMPRESAS.RUA || ', ' || EMPRESAS.FACHADA) AS RUA,
         EMPRESAS.FONE,
         EMPRESAS.FAX,
         EMPRESAS.CEP,
         EMPRESAS.INSCRICAO_MUNICIPAL,
         EMPRESAS.INSCRICAO_SUBSTITUICAO,
         UF.DESCRICAO ESTADO,
         EMPRESAS.INSCRICAO_ESTADUAL,
         SYSDATE AS DATA_ATUAL,
         SUBSTR(TO_CHAR(SYSDATE, 'HH24:MI'), 1, 5) AS HORA_ATUAL_STR,
         CLIENTES.ENDERECO_ELETRONICO AS EMAIL,
         EMPRESA_LOGO.LOGO
    FROM EMPRESAS, EMPRESA_LOGO, UF, CLIENTES
   WHERE EMPRESAS.COD_EMPRESA = $P{COD_EMPRESA}
     AND EMPRESAS.COD_EMPRESA = EMPRESA_LOGO.COD_EMPRESA(+)
     AND EMPRESAS.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND UF.UF = EMPRESAS.ESTADO),
Q_CLIENTE AS
 (SELECT CLIENTE_DIVERSO.COD_CLIENTE,
         
         CLIENTE_DIVERSO.NOME AS NOME,
         CLIENTE_DIVERSO.RG   AS RG,
         
         ('(' || CLIENTES.PREFIXO_RES || ') ' || CLIENTES.TELEFONE_RES) AS TELEFONE_RES,
         CLIENTES.PREFIXO_RES,
         ('(' || CLIENTES.PREFIXO_COM || ') ' || CLIENTES.TELEFONE_COM) AS TELEFONE_COM,
         CLIENTES.PREFIXO_COM,
         ('(' || CLIENTES.PREFIXO_FAX || ') ' || CLIENTES.TELEFONE_FAX) AS TELEFONE_FAX,
         CLIENTES.PREFIXO_FAX,
         ('(' || CLIENTES.PREFIXO_CEL || ') ' || CLIENTES.TELEFONE_CEL) AS TELEFONE_CEL,
         CLIENTES.PREFIXO_CEL,
         
         NVL(OS.INSCRICAO_ESTADUAL, CLIENTE_DIVERSO.INSCRICAO_ESTADUAL) AS INSC_ESTAD,
         
         CLIENTE_DIVERSO.CGC,
         CLIENTE_DIVERSO.CPF,
         CLIENTES.COD_CLASSE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.UF,
                2,
                CLIENTES.UF_RES,
                3,
                CLIENTES.UF_COM,
                4,
                CLIENTES.UF_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.UF,
                NULL) UF,
         DECODE(OS.TIPO_ENDERECO,
                1,
                UF_DIVERSO.DESCRICAO,
                2,
                UF_RES.DESCRICAO,
                3,
                UF_COM.DESCRICAO,
                4,
                UF_COBRANCA.DESCRICAO,
                5,
                UF_INSCRICAO.DESCRICAO,
                NULL) ESTADO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CIDADES_DIV.DESCRICAO,
                2,
                CIDADES_RES.DESCRICAO,
                3,
                CIDADES_COM.DESCRICAO,
                4,
                CIDADES_COBRANCA.DESCRICAO,
                5,
                ENDERECO_POR_INSCRICAO.CIDADE,
                NULL) CIDADE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.BAIRRO,
                2,
                CLIENTES.BAIRRO_RES,
                3,
                CLIENTES.BAIRRO_COM,
                4,
                CLIENTES.BAIRRO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.BAIRRO,
                NULL) BAIRRO,
             DECODE(OS.TIPO_ENDERECO,
                1,
                TRANSLATE(TO_CHAR(TO_NUMBER(REGEXP_REPLACE(CLIENTE_DIVERSO.CEP, '[^0-9]', '')) / 1000, '00000.000'),
                          ',.',
                          '.-'),
                2,
                TRANSLATE(TO_CHAR(TO_NUMBER(REGEXP_REPLACE(CLIENTES.CEP_RES, '[^0-9]', '')) / 1000, '00000.000'),
                          ',.',
                          '.-'),
                3,
                TRANSLATE(TO_CHAR(TO_NUMBER(REGEXP_REPLACE(CLIENTES.CEP_COM, '[^0-9]', '')) / 1000, '00000.000'),
                          ',.',
                          '.-'),
                4,
                TRANSLATE(TO_CHAR(TO_NUMBER(REGEXP_REPLACE(CLIENTES.CEP_COBRANCA, '[^0-9]', '')) / 1000, '00000.000'),
                          ',.',
                          '.-'),
                5,
                TRANSLATE(TO_CHAR(TO_NUMBER(REGEXP_REPLACE(ENDERECO_POR_INSCRICAO.CEP, '[^0-9]', '')) / 1000,
                                  '00000.000'),
                          ',.',
                          '.-'),
                NULL) CEP,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.ENDERECO,
                2,
                CLIENTES.RUA_RES,
                3,
                CLIENTES.RUA_COM,
                4,
                CLIENTES.RUA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.RUA,
                NULL) RUA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.COMPLEMENTO,
                2,
                CLIENTES.COMPLEMENTO_RES,
                3,
                CLIENTES.COMPLEMENTO_COM,
                4,
                CLIENTES.COMPLEMENTO_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.COMPLEMENTO,
                NULL) COMPLEMENTO,
         DECODE(OS.TIPO_ENDERECO,
                1,
                NULL,
                2,
                CLIENTES.FACHADA_RES,
                3,
                CLIENTES.FACHADA_COM,
                4,
                CLIENTES.FACHADA_COBRANCA,
                5,
                ENDERECO_POR_INSCRICAO.FACHADA,
                NULL) FACHADA,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.FONE_CONTATO,
                2,
                CLIENTES.TELEFONE_RES,
                3,
                CLIENTES.TELEFONE_COM,
                4,
                CLIENTES.TELEFONE_CEL,
                5,
                ENDERECO_POR_INSCRICAO.TELEFONE_CONTATO,
                NULL) FONE,
         DECODE(OS.TIPO_ENDERECO,
                1,
                CLIENTE_DIVERSO.PREFIXO_FONE_CONTATO,
                2,
                CLIENTES.PREFIXO_RES,
                3,
                CLIENTES.PREFIXO_COM,
                4,
                CLIENTES.PREFIXO_CEL,
                5,
                ENDERECO_POR_INSCRICAO.PREFIXO_TELEFONE_CONTATO,
                NULL) PREFIXO,
         
         CLIENTES.ENDERECO_ELETRONICO,
         CLIENTES.EMAIL_NFE,
         NVL(CLIENTES.EMAIL_TRABALHO, CLIENTES.EMAIL2) AS EMAIL2
  
    FROM OS,
         CLIENTE_DIVERSO,
         CLIENTES,
         ENDERECO_POR_INSCRICAO,
         CIDADES                CIDADES_RES,
         CIDADES                CIDADES_COM,
         CIDADES                CIDADES_COBRANCA,
         CIDADES                CIDADES_DIV,
         UF                     UF_DIVERSO,
         UF                     UF_RES,
         UF                     UF_COM,
         UF                     UF_COBRANCA,
         UF                     UF_INSCRICAO
   WHERE OS.COD_CLIENTE = CLIENTE_DIVERSO.COD_CLIENTE
     AND CLIENTE_DIVERSO.COD_CLIENTE = CLIENTES.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.COD_CIDADES = CIDADES_DIV.COD_CIDADES(+)
     AND CLIENTES.COD_CID_RES = CIDADES_RES.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COM = CIDADES_COM.COD_CIDADES(+)
     AND CLIENTES.COD_CID_COBRANCA = CIDADES_COBRANCA.COD_CIDADES(+)
     AND OS.INSCRICAO_ESTADUAL =
         ENDERECO_POR_INSCRICAO.INSCRICAO_ESTADUAL(+)
     AND OS.COD_CLIENTE = ENDERECO_POR_INSCRICAO.COD_CLIENTE(+)
     AND CLIENTE_DIVERSO.UF = UF_DIVERSO.UF(+)
     AND CLIENTES.UF_RES = UF_RES.UF(+)
     AND CLIENTES.UF_COM = UF_COM.UF(+)
     AND CLIENTES.UF_COBRANCA = UF_COBRANCA.UF(+)
     AND ENDERECO_POR_INSCRICAO.UF = UF_INSCRICAO.UF(+)
        
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS.NUMERO_OS = $P{NUMERO_OS}),
Q_HIST_ORC AS
 (SELECT ROW_NUMBER() OVER(ORDER BY ORC.NUMERO_OS) AS NUMERO_LINHA,
         ORC.NUMERO_OS,
         ORC.COD_EMPRESA,
         COUNT(ORC.NUMERO_OS) NUMERO_ORCAMENTOS,
         LISTAGG(ORC.NUMERO_ORCAMENTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS ORCAMENTOS,
         LISTAGG(ORCF.VALOR_BRUTO, ', ') WITHIN GROUP(ORDER BY ORC.NUMERO_OS) AS VALOR_ORCAMENTO_BRUTO,
         SUM(ORCF.VALOR_BRUTO) TOTAL
    FROM OS_ORCAMENTOS ORC
    LEFT JOIN OS_ORC_FECHAMENTO ORCF
      ON ORCF.NUMERO_OS = ORC.NUMERO_ORCAMENTO
     AND ORCF.COD_EMPRESA = ORC.COD_EMPRESA
   WHERE ORC.NUMERO_OS = $P{NUMERO_OS}
     AND ORC.COD_EMPRESA = $P{COD_EMPRESA}
   GROUP BY ORC.NUMERO_OS, ORC.COD_EMPRESA),

Q_HISTORICO AS
 (SELECT LISTAGG(HISTORICO_OS.N_OS, ', ') WITHIN GROUP(ORDER BY HISTORICO_OS.N_OS) AS HISTORICO_OS,
         HISTORICO_OS.CHASSI,
         HISTORICO_OS.COD_EMPRESA
    FROM (SELECT DISTINCT TO_CHAR(OSD.NUMERO_OS) AS N_OS,
                          OSD.CHASSI,
                          OSD.COD_EMPRESA
            FROM OS_DADOS_VEICULOS OSD, OS, Q_OS
           WHERE OSD.COD_EMPRESA = Q_OS.COD_EMPRESA
             AND OS.NUMERO_OS = OSD.NUMERO_OS
             AND OS.STATUS_OS = 0
             AND OS.NUMERO_OS > 0
             AND OSD.CHASSI = Q_OS.CHASSI
           ORDER BY OSD.NUMERO_OS DESC) HISTORICO_OS
           WHERE ROWNUM <= 3
   GROUP BY HISTORICO_OS.CHASSI, HISTORICO_OS.COD_EMPRESA),

QRYDADOSVEICULOS AS
 (SELECT CF.ANO, CF.DATA_COMPRA, CF.CHASSI, CF.PLACA
    FROM CLIENTES_FROTA CF, Q_OS
   WHERE CF.VENDIDO = 'N'
     AND ROWNUM <= 1
     AND ((Q_OS.CHASSI IS NOT NULL AND CF.CHASSI = Q_OS.CHASSI) OR
         (Q_OS.PLACA IS NOT NULL AND CF.PLACA = Q_OS.PLACA))
   ORDER BY CF.DATA_COMPRA DESC),

Q_PARM_SYS as
 (SELECT PARM_SYS.COD_EMPRESA,
         TIPO_TEMPO,
         TIPO_VALOR_OS,
         cod_cliente_balcao,
         tipo_concessionaria,
         CONCESSIONARIAS.codigo_padrao,
         versao2,
         cod_cliente_fabrica_garantia,
         PARM_SYS2.HONDA_IMP_NUMERO_REQUISICAO,
         PARM_SYS3.TERMO_OS_JLR
    FROM PARM_SYS, PARM_SYS2, PARM_SYS3, CONCESSIONARIAS
   WHERE (PARM_SYS.CONCESSIONARIA_NUMERO =
         CONCESSIONARIAS.COD_CONCESSIONARIA(+))
     AND (PARM_SYS2.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS3.COD_EMPRESA = PARM_SYS.COD_EMPRESA)
     AND (PARM_SYS.COD_EMPRESA = $P{COD_EMPRESA})),

Q_ASSINATURA as
(select OS.NUMERO_OS,
         OS.COD_EMPRESA,
         OS_AGENDA.SIGNATURE As CLIENTE_RECEPCAO,
         NVL(OS_ASSINATURA.ASSINATURA, MOB_OS_ASSINATURA.ASSINATURA_CLIENTE) AS CLIENTE_ENTREGA,
	     NVL(OS_ASSINATURA.DATA_ASSINATURA, MOB_OS_ASSINATURA.DATA_ASSINATURA_CLIENTE) AS CLIENTE_ENTREGA_DT
    from OS_AGENDA, OS_ASSINATURA, OS, MOB_OS_ASSINATURA
   where OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}
	 AND OS_AGENDA.NUMERO_OS(+) = OS.NUMERO_OS
     AND OS_AGENDA.COD_EMPRESA(+) = OS.COD_EMPRESA
	 AND OS.NUMERO_OS = OS_ASSINATURA.numero_os (+)
	 AND OS.COD_EMPRESA = OS_ASSINATURA.cod_empresa (+)
	 AND MOB_OS_ASSINATURA.NUMERO_OS(+) = OS.NUMERO_OS
     AND MOB_OS_ASSINATURA.COD_EMPRESA(+) = OS.COD_EMPRESA
     AND MOB_OS_ASSINATURA.APLICACAO(+) = 'E' 
),
   
Q_CAMPANHA AS (
SELECT LISTAGG(initcap(OS_CAMPANHA.ASSUNTO), ', ') WITHIN GROUP(ORDER BY OS_CAMPANHA.COD_CAMPANHA) AS LISTA_CAMPANHAS,
       ODV.NUMERO_OS,
       ODV.COD_EMPRESA
      FROM OS_CAMPANHA, OS_DADOS_VEICULOS ODV
      WHERE ODV.NUMERO_OS = $P{NUMERO_OS}
            AND ODV.COD_EMPRESA = $P{COD_EMPRESA}
            AND (EXISTS (SELECT 'X' FROM CAMPANHA_VEICULOS        
             WHERE CAMPANHA_VEICULOS.COD_CAMPANHA = OS_CAMPANHA.COD_CAMPANHA
              AND (COD_PRODUTO IS NULL OR COD_PRODUTO = ODV.COD_PRODUTO)
              AND (COD_MODELO IS NULL OR COD_MODELO = ODV.COD_MODELO)
              AND (CHASSI_INICIAL IS NULL OR CHASSI_INICIAL <= SUBSTR(ODV.CHASSI, -LENGTH(CHASSI_INICIAL)))
              AND (CHASSI_FINAL IS NULL OR CHASSI_FINAL >= SUBSTR(ODV.CHASSI, -LENGTH(CHASSI_FINAL)))
              AND (ANO_INICIAL IS NULL OR ANO_INICIAL <= (CASE 
                                                            WHEN TO_NUMBER(SUBSTR(ODV.ANO, 4, 2)) < 50
                                                            THEN 2000 + TO_NUMBER(SUBSTR(ODV.ANO, 4, 2))
                                                            WHEN TO_NUMBER(SUBSTR(ODV.ANO, 4, 2)) > 50
                                                            THEN 1900 + TO_NUMBER(SUBSTR(ODV.ANO, 4, 2))
                                                            ELSE 0
                                                            END))
              AND (ANO_FINAL IS NULL OR ANO_FINAL >= (CASE 
                                                      WHEN TO_NUMBER(SUBSTR(ODV.ANO, 4, 2)) < 50
                                                      THEN 2000 + TO_NUMBER(SUBSTR(ODV.ANO, 4, 2))
                                                      WHEN TO_NUMBER(SUBSTR(ODV.ANO, 4, 2)) > 50
                                                      THEN 1900 + TO_NUMBER(SUBSTR(ODV.ANO, 4, 2))
                                                      ELSE 0
                                                      END)))
             OR
             EXISTS (SELECT 'X' FROM OS_CAMPANHA_CHASSIS
                     WHERE OS_CAMPANHA_CHASSIS.COD_CAMPANHA = OS_CAMPANHA.COD_CAMPANHA
                      AND CHASSI = ODV.CHASSI)
            )
            AND ((DATA_INICIAL IS NULL) OR (TRUNC(SYSDATE) >= TRUNC(DATA_INICIAL)))
            AND ((DATA_FINAL IS NULL) OR (TRUNC(SYSDATE) <= TRUNC(DATA_FINAL)))
            AND NVL(INTERFACE_ATIVO, 'S') <> 'N'
            AND NOT EXISTS(SELECT COD_CAMPANHA
                            FROM OS_CAMPANHA_FEITA
                            WHERE COD_PRODUTO = ODV.COD_PRODUTO
                             AND COD_MODELO = ODV.COD_MODELO
                             AND CHASSI = ODV.CHASSI)
      GROUP BY ODV.NUMERO_OS, ODV.COD_EMPRESA, OS_CAMPANHA.COD_CAMPANHA
      ORDER BY COD_CAMPANHA
),
Q_TOTAL_ORCAMENTOS AS (
   SELECT 
    os.numero_os,
    os.cod_empresa,
    sum(dados.total_nota) AS TOTAL_ORC,
    sum(dados.total_descontos) AS TOTAL_ORC_DESC,
      sum(dados.total_nota) - sum(dados.total_descontos) AS TOTAL_ORC_LIQ
  FROM 
    os_orcamentos os, TABLE(pkg_crm_service_util.GET_TABLE_DADOS_FISCAIS_OS(os.numero_orcamento, os.cod_empresa)) dados
  where os.numero_os = $P{NUMERO_OS}
      and os.cod_empresa = $P{COD_EMPRESA}
  group by os.numero_os, os.cod_empresa
)

SELECT Q_EMPRESA.NOME_EMPRESA           AS Q_EMPRESA_NOME_EMPRESA,
       Q_EMPRESA.RUA                    AS Q_EMPRESA_RUA,
       Q_EMPRESA.CIDADE                 AS Q_EMPRESA_CIDADE,
       Q_EMPRESA.CEP                    AS Q_EMPRESA_CEP,
       Q_EMPRESA.FONE                   AS Q_EMPRESA_FONE,
       Q_EMPRESA.INSCRICAO_ESTADUAL     AS Q_EMPRESA_INSCRICAO_ESTADUAL,
     Q_EMPRESA.LOGO          AS Q_EMPRESA_LOGO,
       Q_EMPRESA.CGC                    AS Q_EMPRESA_CGC,
       Q_OS.TIPO                        AS Q_OS_TIPO,
       Q_OS.D_AG_STG                    AS Q_OS_D_AG_STG,
     Q_OS.DATA_AGENDADA        AS Q_OS_DATA_AGENDADA,
       Q_OS.CONSULTOR                   AS Q_OS_CONSULTOR,
     Q_OS.CONSULTOR_COMPLETO      AS Q_OS_CONSULTOR_COMPLETO,
       Q_OS.PRISMA                      AS Q_OS_PRISMA,
       Q_OS.DATA_PROMETIDA              AS Q_OS_DATA_PROMETIDA,
       Q_OS.DATA_PROMETIDA_REVISADA     AS Q_OS_DATA_PROMETIDA_REVISADA,
       Q_OS.AG_RETORNO                  AS Q_OS_AG_RETORNO,
       Q_OS.AG_CLIENTE_AGUARDA          AS Q_OS_AG_CLIENTE_AGUARDA,
       Q_OS.RECEPCAO_INTERATIVA         AS Q_OS_RECEPCAO_INTERATIVA,
       Q_OS.AG_LAVAR_VEICULO            AS Q_OS_AG_LAVAR_VEICULO,
       Q_OS.AG_BLINDADO                 AS Q_OS_AG_BLINDADO,
       Q_OS.AG_TESTE_RODAGEM            AS Q_OS_AG_TESTE_RODAGEM,
       Q_OS.AG_LEVAR_PECAS_SUBSTITUIDAS AS Q_OS_AG_LEVAR_PECAS_SUBSTITUID,
       Q_OS.VEICULO_MODIFICADO          AS Q_OS_VEICULO_MODIFICADO,
       Q_OS.NUMERO_OS                   AS Q_OS_NUMERO_OS,
       Q_OS.HORA_PROMETIDA              AS Q_OS_HORA_PROMETIDA,
       Q_OS.PLACA                       AS Q_OS_PLACA,
       Q_OS.COMBUSTIVEL          AS Q_OS_COMBUSTIVEL,
       Q_OS.HORA_PROMETIDA_REVISADA     AS Q_OS_HORA_PROMETIDA_REVISADA,
       Q_OS.MOBILIDADE_DESCRICAO        AS Q_OS_MOBILIDADE_DESCRICAO,
       Q_CLIENTE.TELEFONE_CEL           AS Q_CLIENTE_TELEFONE_CEL,
       Q_CLIENTE.TELEFONE_COM           AS Q_CLIENTE_TELEFONE_COM,
       Q_CLIENTE.TELEFONE_RES           AS Q_CLIENTE_TELEFONE_RES,
       Q_CLIENTE.EMAIL_NFE              AS Q_CLIENTE_EMAIL_NFE,
       Q_CLIENTE.EMAIL2                 AS Q_CLIENTE_EMAIL2,
       Q_CLIENTE.ENDERECO_ELETRONICO    AS Q_CLIENTE_ENDERECO_ELETRONICO,
       Q_CLIENTE.RUA                    AS Q_CLIENTE_RUA,
     Q_CLIENTE.FACHADA        AS Q_CLIENTE_FACHADA,
       Q_CLIENTE.BAIRRO                 AS Q_CLIENTE_BAIRRO,
       Q_CLIENTE.NOME                   AS Q_CLIENTE_NOME,
       Q_CLIENTE.CIDADE                 AS Q_CLIENTE_CIDADE,
       Q_CLIENTE.CEP                    AS Q_CLIENTE_CEP,
       Q_CLIENTE.CPF                    AS Q_CLIENTE_CPF,
       Q_CLIENTE.RG                     AS Q_CLIENTE_RG,
       Q_OS.DESC_PROD_MOD               AS Q_OS_DESC_PROD_MOD,
       Q_OS.KM                          AS Q_OS_KM,
       Q_OS.CHASSI                      AS Q_OS_CHASSI,
       Q_OS.COR_EXTERNA                 AS Q_OS_COR_EXTERNA,
       Q_OS.ANO                         AS Q_OS_ANO,
       Q_OS.NUMERO_MOTOR                AS Q_OS_NUMERO_MOTOR,
       Q_OS.DATA_VENDA                  AS Q_OS_DATA_VENDA,
       Q_OS.CONCESSIONARIA_NOME         AS Q_OS_CONCESSIONARIA_NOME,
       Q_OS.TOTAL_OS                    AS Q_OS_TOTAL_OS,
       Q_OS.BLINDADORA                  AS Q_OS_BLINDADORA,
       Q_OS.BLINDADO                    AS Q_OS_BLINDADO,
       Q_OS.DDW_GARANTIA                AS Q_OS_DDW_GARANTIA,
       Q_OS.REC_INTERATIVA              AS Q_OS_REC_INTERATIVA,
       Q_OS.IMOBILIZADO                 AS Q_OS_IMOBILIZADO,
       Q_OS.DH_EMISSAO                  AS Q_OS_DH_EMISSAO,
       Q_OS.DATA_ENCERRADA              AS Q_OS_DATA_ENCERRADO,
     Q_OS.HORA_ENCERRADA              AS Q_OS_HORA_ENCERRADO,
       Q_OS.DH_LIBERADO                 AS Q_OS_DH_LIBERADO,
       Q_OS.TIPO_FABRICA                AS Q_OS_TIPO_FABRICA,
       Q_OS.OBSERVACAO                  AS Q_OS_OBSERVACAO,
       Q_HIST_ORC.ORCAMENTOS            AS Q_HIST_ORC_LISTA_ORCAMENTOS,
       Q_HIST_ORC.TOTAL                 AS Q_HIST_ORC_TOTAL_ORCAMENTOS,
       Q_HISTORICO.HISTORICO_OS         AS Q_HIST_ORC_HISTORICO_OS,
       Q_PARM_SYS.TERMO_OS_JLR          AS Q_PARM_SYS_TERMO_OS_JLR,
       Q_ASSINATURA.CLIENTE_RECEPCAO   AS Q_ASSINATURA_CLIENTE_RECEPCAO,
       Q_OS.DATA_EMISSAO           AS Q_OS_DATA_EMISSAO,
       Q_OS.HORA_EMISSAO            AS Q_OS_HORA_EMISSAO,
	   Q_OS.DATA_ENTREGA			AS Q_OS_DATA_ENTREGA,
       Q_ASSINATURA.CLIENTE_ENTREGA      AS Q_ASSINATURA_CLIENTE_ENTREGA,
       Q_ASSINATURA.CLIENTE_ENTREGA_DT      AS Q_ASSINATURA_CLIE_ENTREGA_DT,
     Q_OS.TOTAL_SERVICOS_LIQUIDO     AS Q_OS_TOTAL_SERVICOS_LIQUIDO,
     Q_OS.TOTAL_ITENS_LIQUIDO         AS Q_OS_TOTAL_ITENS_LIQUIDO,
     Q_OS.VALOR_SERVICOS_BRUTO    AS Q_OS_VALOR_SERVICOS_BRUTO,
       Q_OS.VALOR_ITENS_BRUTO      AS Q_OS_VALOR_ITENS_BRUTO,
     Q_OS.DESCONTO_SERVICO_PORCENT  AS Q_OS_DESCONTO_SERVICO_PORCENT,
     Q_OS.DESCONTO_ITENS_PORCENT    AS Q_OS_DESCONTO_ITENS_PORCENT,
     Q_OS.FORMA_PAGAMENTO           AS Q_OS_FORMA_PAGAMENTO,
     Q_OS.VEICULO_LOCACAO           AS Q_OS_VEICULO_LOCACAO,
     Q_CAMPANHA.LISTA_CAMPANHAS    AS Q_CAMPANHAS_LISTA_CAMPANHAS,
     Q_OS.PACOTE_TRANQUILIDADE AS Q_OS_PACOTE_TRANQUILIDADE,
     NVL(Q_TOTAL_ORCAMENTOS.TOTAL_ORC,0) AS Q_TOTAL_ORCAMENTOS_ORC,
     NVL(Q_TOTAL_ORCAMENTOS.TOTAL_ORC_DESC,0) AS Q_TOTAL_ORCAMENTOS_ORC_DESC,
     NVL(Q_TOTAL_ORCAMENTOS.TOTAL_ORC_LIQ,0)    AS Q_TOTAL_ORCAMENTOS_ORC_LIQ,
	 Q_EMPRESA.DATA_ATUAL						AS Q_EMPRESA_DATA_ATUAL
  FROM Q_OS,
       Q_EMPRESA,
       Q_CLIENTE,
       Q_HIST_ORC,
       Q_HISTORICO,
       QRYDADOSVEICULOS,
       Q_ASSINATURA,
       Q_PARM_SYS,
       Q_CAMPANHA,
     Q_TOTAL_ORCAMENTOS
 WHERE Q_OS.COD_CLIENTE = Q_CLIENTE.COD_CLIENTE(+)
   AND Q_OS.COD_EMPRESA = Q_EMPRESA.COD_EMPRESA(+)
   AND Q_OS.COD_EMPRESA = Q_HIST_ORC.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_HIST_ORC.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_HISTORICO.COD_EMPRESA(+)
   AND Q_OS.CHASSI = Q_HISTORICO.CHASSI(+)
   AND Q_OS.CHASSI = QRYDADOSVEICULOS.CHASSI(+)
   AND Q_OS.PLACA = QRYDADOSVEICULOS.PLACA(+)
   AND Q_OS.COD_EMPRESA = Q_PARM_SYS.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_ASSINATURA.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_ASSINATURA.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_CAMPANHA.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_CAMPANHA.COD_EMPRESA(+)
   AND Q_OS.NUMERO_OS = Q_TOTAL_ORCAMENTOS.NUMERO_OS(+)
   AND Q_OS.COD_EMPRESA = Q_TOTAL_ORCAMENTOS.COD_EMPRESA(+)]]>
	</queryString>
	<field name="Q_EMPRESA_NOME_EMPRESA" class="java.lang.String"/>
	<field name="Q_EMPRESA_RUA" class="java.lang.String"/>
	<field name="Q_EMPRESA_CIDADE" class="java.lang.String"/>
	<field name="Q_EMPRESA_CEP" class="java.lang.String"/>
	<field name="Q_EMPRESA_FONE" class="java.lang.String"/>
	<field name="Q_EMPRESA_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="Q_EMPRESA_LOGO" class="java.awt.Image"/>
	<field name="Q_EMPRESA_CGC" class="java.lang.String"/>
	<field name="Q_OS_TIPO" class="java.lang.String"/>
	<field name="Q_OS_D_AG_STG" class="java.lang.String"/>
	<field name="Q_OS_DATA_AGENDADA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CONSULTOR" class="java.lang.String"/>
	<field name="Q_OS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="Q_OS_PRISMA" class="java.lang.String"/>
	<field name="Q_OS_DATA_PROMETIDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_DATA_PROMETIDA_REVISADA" class="java.sql.Timestamp"/>
	<field name="Q_OS_AG_RETORNO" class="java.lang.String"/>
	<field name="Q_OS_AG_CLIENTE_AGUARDA" class="java.lang.String"/>
	<field name="Q_OS_RECEPCAO_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_AG_LAVAR_VEICULO" class="java.lang.String"/>
	<field name="Q_OS_AG_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_AG_TESTE_RODAGEM" class="java.lang.String"/>
	<field name="Q_OS_AG_LEVAR_PECAS_SUBSTITUID" class="java.lang.String"/>
	<field name="Q_OS_VEICULO_MODIFICADO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_OS" class="java.lang.Double"/>
	<field name="Q_OS_HORA_PROMETIDA" class="java.lang.String"/>
	<field name="Q_OS_PLACA" class="java.lang.String"/>
	<field name="Q_OS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="Q_OS_HORA_PROMETIDA_REVISADA" class="java.lang.String"/>
	<field name="Q_OS_MOBILIDADE_DESCRICAO" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_CEL" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_COM" class="java.lang.String"/>
	<field name="Q_CLIENTE_TELEFONE_RES" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL_NFE" class="java.lang.String"/>
	<field name="Q_CLIENTE_EMAIL2" class="java.lang.String"/>
	<field name="Q_CLIENTE_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="Q_CLIENTE_RUA" class="java.lang.String"/>
	<field name="Q_CLIENTE_FACHADA" class="java.lang.String"/>
	<field name="Q_CLIENTE_BAIRRO" class="java.lang.String"/>
	<field name="Q_CLIENTE_NOME" class="java.lang.String"/>
	<field name="Q_CLIENTE_CIDADE" class="java.lang.String"/>
	<field name="Q_CLIENTE_CEP" class="java.lang.String"/>
	<field name="Q_CLIENTE_CPF" class="java.lang.String"/>
	<field name="Q_CLIENTE_RG" class="java.lang.String"/>
	<field name="Q_OS_DESC_PROD_MOD" class="java.lang.String"/>
	<field name="Q_OS_KM" class="java.lang.Double"/>
	<field name="Q_OS_CHASSI" class="java.lang.String"/>
	<field name="Q_OS_COR_EXTERNA" class="java.lang.String"/>
	<field name="Q_OS_ANO" class="java.lang.String"/>
	<field name="Q_OS_NUMERO_MOTOR" class="java.lang.String"/>
	<field name="Q_OS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="Q_OS_CONCESSIONARIA_NOME" class="java.lang.String"/>
	<field name="Q_OS_TOTAL_OS" class="java.lang.Double"/>
	<field name="Q_OS_BLINDADORA" class="java.lang.String"/>
	<field name="Q_OS_BLINDADO" class="java.lang.String"/>
	<field name="Q_OS_DDW_GARANTIA" class="java.lang.String"/>
	<field name="Q_OS_REC_INTERATIVA" class="java.lang.String"/>
	<field name="Q_OS_IMOBILIZADO" class="java.lang.String"/>
	<field name="Q_OS_DH_EMISSAO" class="java.lang.String"/>
	<field name="Q_OS_DATA_ENCERRADO" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_ENCERRADO" class="java.lang.String"/>
	<field name="Q_OS_DH_LIBERADO" class="java.lang.String"/>
	<field name="Q_OS_TIPO_FABRICA" class="java.lang.String"/>
	<field name="Q_OS_OBSERVACAO" class="java.lang.String"/>
	<field name="Q_HIST_ORC_LISTA_ORCAMENTOS" class="java.lang.String"/>
	<field name="Q_HIST_ORC_TOTAL_ORCAMENTOS" class="java.lang.Double"/>
	<field name="Q_HIST_ORC_HISTORICO_OS" class="java.lang.String"/>
	<field name="Q_PARM_SYS_TERMO_OS_JLR" class="java.lang.String"/>
	<field name="Q_ASSINATURA_CLIENTE_RECEPCAO" class="java.awt.Image"/>
	<field name="Q_OS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="Q_OS_HORA_EMISSAO" class="java.lang.String"/>
	<field name="Q_OS_DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="Q_ASSINATURA_CLIENTE_ENTREGA" class="java.awt.Image"/>
	<field name="Q_ASSINATURA_CLIE_ENTREGA_DT" class="java.sql.Timestamp"/>
	<field name="Q_OS_TOTAL_SERVICOS_LIQUIDO" class="java.lang.Double"/>
	<field name="Q_OS_TOTAL_ITENS_LIQUIDO" class="java.lang.Double"/>
	<field name="Q_OS_VALOR_SERVICOS_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_VALOR_ITENS_BRUTO" class="java.lang.Double"/>
	<field name="Q_OS_DESCONTO_SERVICO_PORCENT" class="java.lang.Double"/>
	<field name="Q_OS_DESCONTO_ITENS_PORCENT" class="java.lang.Double"/>
	<field name="Q_OS_FORMA_PAGAMENTO" class="java.lang.String"/>
	<field name="Q_OS_VEICULO_LOCACAO" class="java.lang.String"/>
	<field name="Q_CAMPANHAS_LISTA_CAMPANHAS" class="java.lang.String"/>
	<field name="Q_OS_PACOTE_TRANQUILIDADE" class="java.lang.String"/>
	<field name="Q_TOTAL_ORCAMENTOS_ORC" class="java.lang.Double"/>
	<field name="Q_TOTAL_ORCAMENTOS_ORC_DESC" class="java.lang.Double"/>
	<field name="Q_TOTAL_ORCAMENTOS_ORC_LIQ" class="java.lang.Double"/>
	<field name="Q_EMPRESA_DATA_ATUAL" class="java.sql.Timestamp"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="98">
			<frame>
				<reportElement x="0" y="0" width="555" height="94" uuid="31fde87b-dafc-43ab-af60-5e7934f8e6e0">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<frame>
					<reportElement x="0" y="0" width="346" height="94" uuid="55b30501-aa83-4644-bba1-b3c83de1566c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="194" y="20" width="70" height="10" uuid="1bf296d2-b0f9-4f71-b7ba-cd28abcbf0ad">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Entrada]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="66" width="43" height="18" uuid="c32955cf-820e-4ead-824f-b76524b8ba8f">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Cliente
Aguarda]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="43" y="66" width="55" height="18" uuid="2eb924ea-44cd-4683-9473-3dbbd73a883d">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Cliente
Retorno]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="98" y="66" width="99" height="15" uuid="66e6799d-610a-4f3f-9475-e9df277f9146">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Lavar o Veículo]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="197" y="66" width="149" height="9" uuid="34c2230a-8b35-420d-8a78-7c30c7c42943">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Previsão de Entrega REVISADA]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="76" y="0" width="186" height="20" uuid="0db7ccc2-7eb6-41c2-a655-09e5a784d73d">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle">
							<font size="13" isBold="true"/>
						</textElement>
						<text><![CDATA[ORDEM DE SERVIÇO: Nº]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="264" y="20" width="82" height="10" uuid="96c208f8-29c9-44e5-a541-c20d102d096f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Previsão de Entrega]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="96" y="20" width="98" height="10" uuid="11c939b7-c386-4bc8-b659-74f22817ea10">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Embaixador de Serviço]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="42" y="20" width="54" height="10" uuid="4561ae36-dea3-4ed5-be30-f43bc6a43f36">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Agendado]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="20" width="42" height="10" uuid="453f7c49-8590-4844-8b91-201f3522c183">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Placa]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="50" y="0" width="26" height="10" uuid="eaea8587-be1c-4a77-91ed-e85fc711659f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Hora:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="50" height="10" uuid="9d149449-d3bc-4e5a-b36b-e8826adfc8b9">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Data Abertura:]]></text>
					</staticText>
					<textField pattern="dd/MM/yy">
						<reportElement mode="Transparent" x="0" y="10" width="50" height="10" uuid="bb3ec3f0-d42b-4615-84c1-623d9a88997b">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="50" y="10" width="26" height="10" uuid="bf7d18ef-9843-420a-a975-e7ef86307130">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="#.###;(#.###-)">
						<reportElement mode="Transparent" x="262" y="0" width="74" height="20" uuid="5d1d9747-7256-43c9-b88e-92eacf6f7265">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box leftPadding="3">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Middle">
							<font size="13" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_NUMERO_OS}]]></textFieldExpression>
					</textField>
					<textField pattern="dd/MM/yy">
						<reportElement mode="Transparent" x="42" y="30" width="54" height="15" uuid="7dc60da7-f6d8-48a4-93b3-d22af5602722"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_AGENDADA}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="42" y="45" width="54" height="13" uuid="b6d312e6-d5a3-462f-9188-d96d862cbe80"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_AGENDADA}]]></textFieldExpression>
					</textField>
					<textField pattern="dd/MM/yy">
						<reportElement mode="Transparent" x="194" y="30" width="70" height="15" uuid="d5fff862-f281-4eb5-88e7-bf5e75bd5148"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="194" y="45" width="70" height="13" uuid="4775d181-de17-4155-9bac-a7879acb4c05"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="dd/MM/yy">
						<reportElement mode="Transparent" x="264" y="30" width="82" height="15" uuid="dd49b840-3979-405e-bfec-cfe1665fd639"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="264" y="45" width="82" height="13" uuid="7a6311db-878e-411c-a907-c1db070f7c40"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_PROMETIDA}]]></textFieldExpression>
					</textField>
					<textField pattern="dd/MM/yy">
						<reportElement mode="Transparent" x="197" y="75" width="149" height="9" uuid="4989a025-c9cd-4321-a483-093fb26a2307">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_PROMETIDA_REVISADA}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="197" y="84" width="149" height="10" uuid="2943132d-97c2-4da6-b833-eebc17cb952e">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_PROMETIDA_REVISADA}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="0" y="84" width="43" height="10" uuid="27dc59b2-e1f0-44e1-80a9-102053cc0155">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_AG_CLIENTE_AGUARDA}.equals("S")? "SIM" : "NÃO"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="98" y="81" width="99" height="13" uuid="114f97d9-c18a-4b00-aca3-a3af782dc5a4">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_AG_LAVAR_VEICULO}.equals("S") ? "SIM" : "NÃO"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="43" y="84" width="55" height="10" uuid="21423608-3d74-42a5-94a0-08306f7554f5">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_AG_RETORNO}.equals("S")? "SIM" : "NÃO"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="96" y="30" width="98" height="28" uuid="fd2f87a3-2c73-4b65-9d85-edd579e75c7a"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CONSULTOR}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="0" y="30" width="42" height="28" uuid="0d672c31-89a4-4cb9-a52d-3a8092215e56">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<box>
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_PLACA}]]></textFieldExpression>
					</textField>
				</frame>
				<textField>
					<reportElement mode="Transparent" x="350" y="58" width="105" height="7" uuid="56602f9a-e591-42dc-b033-5b9aadcc0c48"/>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_NOME_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="366" y="65" width="89" height="7" uuid="2da9e903-2d44-4668-a0bb-3ff0d55adf6e">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_CGC}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="383" y="72" width="72" height="7" uuid="9d96f7e1-1d5e-484e-ab25-440dbcde3d0b">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="503" y="72" width="37" height="7" uuid="de20b1c3-15d4-4b9f-b993-e3de577febf7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_FONE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="350" y="72" width="33" height="7" uuid="ef6a1f85-e0fc-4ced-8f61-c9c2b0f87382">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[ENDEREÇO:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="471" y="72" width="32" height="7" uuid="f913ca35-80c3-477a-ab77-ff02cbd1e630">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[TELEFONE:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="350" y="65" width="16" height="7" uuid="53858bbf-ec70-426e-88f4-a791c488318b">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="5" isBold="false"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle">
					<reportElement x="369" y="1" width="68" height="57" uuid="d68e5e8a-24eb-45ee-866a-09ab99467b0f">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice30107.png"]]></imageExpression>
				</image>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="477" y="1" width="68" height="57" uuid="107d08b4-3623-4aeb-9bf7-10f5947f8631">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<imageExpression><![CDATA[$F{Q_EMPRESA_LOGO}]]></imageExpression>
				</image>
				<line>
					<reportElement x="459" y="6" width="1" height="75" uuid="03bd8fa2-09dd-4aaf-a424-d46391200bb7">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="90">
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="555" height="51" uuid="70434376-ce27-432d-9506-6b1a9cbd0ca0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="2" y="1" width="27" height="10" uuid="f4e38b5c-7b53-41b1-b25f-1c652b233c8d"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cliente:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="150" y="1" width="17" height="10" uuid="6550beae-0149-4101-9b35-9c80ec6b9210"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[CPF:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="289" y="1" width="77" height="10" uuid="19be9702-2162-47bb-a68e-afde526b92df"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Condutor do Veículo:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="11" width="35" height="10" uuid="4fb2f00e-a327-44ff-b95e-dfcb377f9f9d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Endereço:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="150" y="11" width="30" height="10" uuid="b8cc3cf4-484b-4adf-83e0-c2c2f9d44b42"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Número:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="218" y="11" width="26" height="10" uuid="e72db119-c292-44fd-8c7d-e9f46df1f336"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="21" width="27" height="10" uuid="67e5c65a-b74a-4087-a52c-c5e3ccec00fc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="150" y="21" width="17" height="10" uuid="cd411edb-2a62-4006-ad14-e3293c6f4c0d"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="31" width="27" height="10" uuid="86f73a81-7353-42af-a821-8e5b399c92ef">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Celular:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="150" y="31" width="74" height="10" uuid="bbef8eb1-a5ea-4460-b4c6-d4ccde19ec6a"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Residencial/Comercial:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="41" width="25" height="10" uuid="cb9f96e9-a376-403c-b1a4-cff3b6e4e371">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[E-mail:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="29" y="21" width="119" height="10" uuid="06daf547-eaaa-46ad-b37c-d32623e95ba1"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_CIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="29" y="1" width="119" height="10" uuid="4b59fe42-3935-404c-9366-62d477878e5a"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="37" y="11" width="111" height="10" uuid="49a1efb2-63e6-4d7e-9e63-d0d1092de250"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_RUA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="27" y="41" width="121" height="10" uuid="905753b0-e88f-4049-8c97-3faa125b2756"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="29" y="31" width="119" height="10" uuid="1ca32804-49ec-40c6-9f33-9c760b1c6aad"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_TELEFONE_CEL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="167" y="1" width="81" height="10" uuid="f26707b2-197f-4a91-ac41-f5124abf0e3b"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_CPF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="180" y="11" width="38" height="10" uuid="58c5dbd5-9d7f-4454-a75d-a11d1436c8b2"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_FACHADA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="167" y="21" width="81" height="10" uuid="028baa6a-0501-4316-948d-23e25e4b5b35"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_EMPRESA_CEP}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="224" y="31" width="70" height="10" uuid="8d360cfb-d4a3-47ee-8f73-ac4e75fc28fb"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_TELEFONE_COM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="244" y="11" width="94" height="10" uuid="200b57d0-4503-4fc6-89ee-2fea84e75ebd"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="366" y="1" width="174" height="10" uuid="fb180070-e045-44d6-8b30-b6a4412c3ef0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CLIENTE_NOME}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" x="0" y="57" width="555" height="33" uuid="0e5b86c5-8fc9-453e-a351-24ae3a084f29">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="2" y="1" width="76" height="10" uuid="a7f4f19d-b07d-4c45-8966-e7b6e7bc67f8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Distribuidor da Venda:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="153" y="1" width="52" height="10" uuid="6fc0f002-babf-4e3b-b8ef-163a43ba7c03">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data da Venda:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="251" y="1" width="27" height="10" uuid="ebdac1c4-a1fd-4425-a35a-088e9225786d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Chassi:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="356" y="1" width="33" height="10" uuid="f35d17a2-3d25-4254-9c32-f6a3d47f6e01">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Ano/Mod:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="414" y="1" width="35" height="10" uuid="2bb4c599-0582-4a90-a104-48aaf8523447">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[KM Atual:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="2" y="14" width="26" height="10" uuid="f02307d5-4195-45ae-823f-6d065afc28da">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Veículo:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="221" y="14" width="66" height="11" uuid="71713f31-5d97-43c3-8904-35ae8dde8a97">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Nível Combustível:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="361" y="14" width="48" height="11" uuid="4e95d387-6de2-4957-be73-d8e69468dddb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Nível Bateria:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="472" y="14" width="15" height="10" uuid="b46af24e-3aad-46e9-90ce-2a3f92186a2a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cor:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="78" y="1" width="72" height="10" uuid="531b1dad-d4ee-4ea8-ad70-3a57742dd9fc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CONCESSIONARIA_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="29" y="14" width="188" height="10" uuid="06301b3f-7f18-4d24-b7c5-0c0f1af9af3f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DESC_PROD_MOD}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="205" y="1" width="43" height="10" uuid="229089ac-06e3-4b6a-8c40-5e18caa357e6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="278" y="1" width="76" height="10" uuid="ca11295c-f2b9-4dab-8021-d3f917fbcbb3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CHASSI}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="488" y="14" width="64" height="10" uuid="a56b9cb1-0344-4dd3-a0cb-e075deb856fa">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="389" y="1" width="23" height="10" uuid="c0a7bda8-f5b9-449a-9042-86280140bd65">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_ANO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement mode="Transparent" x="449" y="1" width="49" height="10" uuid="d154d8b7-c3a0-4787-aa4e-c9e88ca66e11">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_KM}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="294" y="23" width="1" height="2" uuid="f09116ea-e0e6-42e2-af51-e44ebac6028c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="330" y="23" width="1" height="2" uuid="81cfd1f3-f66c-4f9e-8ecb-665a9889c6d5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="306" y="23" width="1" height="2" uuid="fccb1413-78c4-4baf-84f8-ba0611638fef">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="318" y="23" width="1" height="2" uuid="a75f9f9c-0a79-4d01-8b58-1c5bdb9ba2ae">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="312" y="19" width="1" height="6" uuid="25515f7a-6653-4ef2-9a04-7bef3391dcdc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="324" y="21" width="1" height="4" uuid="04e8358a-cef0-4b3e-a42f-055f77c9cd42">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="300" y="21" width="1" height="4" uuid="57ee355d-e036-4262-a78d-413c8f4e07f8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="287" y="14" width="51" height="11" uuid="50dd6f40-1c38-4a0e-a4bf-0ce889d5214d"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_COMBUSTIVEL} < 19 ? "":
$F{Q_OS_COMBUSTIVEL} < 39 ? "X" :
$F{Q_OS_COMBUSTIVEL} < 59 ? "X   X":
$F{Q_OS_COMBUSTIVEL} < 79 ? "X   X   X":
 "X   X   X   X"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="453" y="23" width="1" height="2" uuid="de7c16e9-2996-4106-8ef1-a5bb79a096b1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="447" y="21" width="1" height="4" uuid="acbdab91-c12e-4f54-8c3f-949b69331a7d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="423" y="21" width="1" height="4" uuid="98951dbe-5abf-464a-9f4f-312cb3a4f6a3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.8"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="441" y="23" width="1" height="2" uuid="3af13b09-31aa-4fdc-8a2c-c217969b30f1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="410" y="14" width="51" height="11" uuid="2ce9491f-6d11-49ff-9ea3-1a745ccda3d0"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="429" y="23" width="1" height="2" uuid="29df8d96-11e2-4eac-b0ee-6464154d026e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
				<line>
					<reportElement x="435" y="19" width="1" height="6" uuid="711b5824-ea80-4f8c-8525-6af46dd5f09c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="417" y="23" width="1" height="2" uuid="669b3dc8-d6ba-4ce0-b379-74193eca554b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.6"/>
					</graphicElement>
				</line>
			</frame>
		</band>
		<band height="36">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<subreport isUsingCache="false">
				<reportElement key="" x="0" y="3" width="555" height="30" isRemoveLineWhenBlank="true" uuid="4530e495-b48e-4f63-96d1-3bebd2312006">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsGwmSubReclamacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="36">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<subreport isUsingCache="false">
				<reportElement x="0" y="3" width="555" height="30" isRemoveLineWhenBlank="true" uuid="3c55ed31-b65e-4e63-9d27-cb35c896e4c4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsGwmSubPecas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="42">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<subreport isUsingCache="false">
				<reportElement x="0" y="3" width="555" height="30" isRemoveLineWhenBlank="true" uuid="8d6704d5-2e23-4ea8-9bd9-14a8472d8227">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsGwmSubServicos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="42">
			<subreport>
				<reportElement x="0" y="3" width="555" height="30" isRemoveLineWhenBlank="true" uuid="d2e059d5-2ee3-43a1-89cf-a4db2dd3511d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsGwmSubOrcamentos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="61">
			<staticText>
				<reportElement mode="Transparent" x="0" y="0" width="44" height="10" uuid="b8c0fc14-1db0-423e-aa6b-44452d9fa3bf">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="7" isBold="false"/>
				</textElement>
				<text><![CDATA[Fechamento]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="10" width="554" height="51" uuid="1919f333-4106-4afc-83cc-0036bffb4a66"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="388" y="0" width="42" height="11" uuid="db2da90d-33a2-41dd-8c25-ca8965bd46ae">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Total Bruto]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="430" y="0" width="42" height="11" uuid="0ebb84a8-1fd3-470f-a004-ebc9a9bd8d2c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Desconto]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="472" y="0" width="82" height="11" uuid="4dd94543-a386-4745-a3a3-10ca025599e5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Total Liquido]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="340" y="11" width="46" height="10" uuid="0793fe0b-ecbf-4cae-b2f1-db532344014c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Peças]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="340" y="21" width="46" height="10" uuid="bfbce8e9-a93d-4b2c-80f1-2410ad6122d2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Serviços]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="340" y="31" width="46" height="10" uuid="c7de3618-9417-46c8-967e-25f39bc26224">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Orçamento]]></text>
				</staticText>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="430" y="21" width="42" height="10" uuid="789b1840-33f2-4aab-8ee1-2bf14ed2c643">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DESCONTO_SERVICO_PORCENT}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="472" y="21" width="82" height="10" uuid="01ace4f3-52f2-4853-b4eb-e87b492869d1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TOTAL_SERVICOS_LIQUIDO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="472" y="11" width="82" height="10" uuid="0539ecd8-f905-4b69-8f88-093f4bdb85e6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_TOTAL_ITENS_LIQUIDO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="388" y="11" width="42" height="10" uuid="e5307826-7368-4feb-bc2e-3f2948939887">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_VALOR_ITENS_BRUTO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="388" y="21" width="42" height="10" uuid="0c28d8dc-fb03-4d2a-a209-725d356e70fa">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_VALOR_SERVICOS_BRUTO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="430" y="11" width="42" height="10" uuid="e5a5e1a5-925d-4103-b76b-72dcfb6405a6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_DESCONTO_ITENS_PORCENT}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="0" y="41" width="554" height="10" uuid="0a9f498b-00d2-405c-9040-dbb4a35c38c8"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="352" y="0" width="34" height="10" uuid="ee88a845-5f7b-4e6d-aa03-015b5ce3bbbf">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Right">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Total]]></text>
					</staticText>
					<textField pattern="#,#00.00#">
						<reportElement mode="Transparent" x="472" y="0" width="82" height="10" uuid="4cbd2850-075c-4281-9a38-26cc0ca41fd5">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_TOTAL_SERVICOS_LIQUIDO} + $F{Q_OS_TOTAL_ITENS_LIQUIDO} + $F{Q_TOTAL_ORCAMENTOS_ORC_LIQ}]]></textFieldExpression>
					</textField>
					<textField pattern="#,#00.00#">
						<reportElement mode="Transparent" x="388" y="0" width="42" height="10" uuid="3333e459-056c-472b-bac3-90616df5222d">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="7" isBold="true"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_VALOR_SERVICOS_BRUTO} + $F{Q_OS_VALOR_ITENS_BRUTO} + $F{Q_TOTAL_ORCAMENTOS_ORC}]]></textFieldExpression>
					</textField>
				</frame>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="430" y="31" width="42" height="10" uuid="edca16e3-5320-4143-97e5-3461a3879d63">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_TOTAL_ORCAMENTOS_ORC_DESC}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="388" y="31" width="42" height="10" uuid="3810d720-ba72-4ab2-a7ec-f604c0141e44">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_TOTAL_ORCAMENTOS_ORC}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="472" y="31" width="82" height="10" uuid="ddd95a93-c3d5-42a9-991f-7ccac6f9f0e2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_TOTAL_ORCAMENTOS_ORC_LIQ}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="217" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="554" height="152" uuid="bc4d2440-68eb-43a2-8bdb-c52b17ed6916"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="10" width="108" height="10" uuid="733982bf-e978-4195-8533-28f2661d63fe">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável pela aprovação:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="20" width="108" height="10" uuid="aa02f3e5-3b68-4b82-b23d-6517a81edb94">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável pelo contato:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="30" width="108" height="10" uuid="36c44805-46cf-4478-bde1-d903798b58c0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Condição de pagamento:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="40" width="108" height="10" uuid="5bfb23a8-f2f5-41d4-ad0e-d26930bcff59">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Veículo de locação:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="50" width="108" height="10" uuid="2cce6cab-7791-4c6f-839f-6eb5578eaece">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Pacote Tranquilidade:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="60" width="108" height="10" uuid="0e799dd8-19ec-49c5-92be-5c6a1efe58ac">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Histórico de Serviço (OS)s:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="70" width="108" height="10" uuid="896d5c89-27a5-451b-87f7-72bc6ca809e5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Campanha de Serviço:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="80" width="108" height="10" uuid="7c1ac51b-6aba-4bc2-b06b-00b4f9d04143">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Informações Adicionais:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="108" y="70" width="437" height="10" uuid="702bad69-def5-4727-ae15-85fa35115562">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_CAMPANHAS_LISTA_CAMPANHAS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="108" y="30" width="241" height="10" uuid="8adde5f4-bcdd-46b3-8c97-a6320b33f9a6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_FORMA_PAGAMENTO}]]></textFieldExpression>
				</textField>
				<textField hyperlinkType="Reference">
					<reportElement mode="Transparent" x="108" y="60" width="241" height="10" uuid="45b0906c-1242-455f-939d-661bca36a48c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_HIST_ORC_HISTORICO_OS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="108" y="10" width="241" height="10" uuid="197e3924-0784-4a2e-a83d-3ffcd91c91b9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="108" y="50" width="241" height="10" uuid="9b086230-817b-4679-a1f2-4c02fb7667a8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_PACOTE_TRANQUILIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="108" y="80" width="241" height="10" uuid="fa958f62-f5a4-45a6-8cb8-2b60ca31eec7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="108" y="40" width="241" height="10" uuid="904009b7-6f30-433a-91a6-55c0660dc012">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_VEICULO_LOCACAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="108" y="20" width="241" height="10" uuid="c16ef657-46d6-48e4-b80c-d43afb9f04f0">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Q_OS_CONSULTOR_COMPLETO}]]></textFieldExpression>
				</textField>
				<textField hyperlinkType="Reference">
					<reportElement positionType="Float" mode="Transparent" x="3" y="108" width="545" height="16" forecolor="#2F89DC" uuid="0686083f-eeaf-4234-b44e-3bb41208b03d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Justified">
						<font size="6" isBold="false" isItalic="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA["https://storage.googleapis.com/movilepay/LP-GWM/Pol%C3%ADtica-de-Privacidade-GWM-Mercado-livre.pdf#deal_print_id=82a33940-c146-11ed-b93f-e7b393eefca6&c_id=button-normal&c_element_order=2&c_campaign=TRACKING&c_uid=82a33940-c146-11ed-b93f-e7b393eefca6"]]></textFieldExpression>
					<hyperlinkReferenceExpression><![CDATA["https://storage.googleapis.com/movilepay/LP-GWM/Pol%C3%ADtica-de-Privacidade-GWM-Mercado-livre.pdf#deal_print_id=82a33940-c146-11ed-b93f-e7b393eefca6&c_id=button-normal&c_element_order=2&c_campaign=TRACKING&c_uid=82a33940-c146-11ed-b93f-e7b393eefca6"]]></hyperlinkReferenceExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="3" y="99" width="545" height="8" uuid="245375d2-2ce3-436b-836a-ef818d49b612">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Justified">
						<font size="6" isBold="false" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA["O Cliente autoriza o concessionário " + $F{Q_EMPRESA_NOME_EMPRESA} + " a coletar e armazenar dados pessoais para fins de cumprimento das obrigações aqui descritas, bem como para os fins previstos na Política de Privacidade disponível em:"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="3" y="126" width="544" height="24" uuid="a39ebac9-c8b6-4d1f-aa42-a9627fcf66cc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Justified">
						<font size="6" isBold="false" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA["O(A) Cliente autoriza a realização dos serviços aqui descritos pela  "+ $F{Q_EMPRESA_NOME_EMPRESA} +". Após aprovação dos orçamentos, o prazo de entrega poderá ser alterado. A "+ $F{Q_EMPRESA_NOME_EMPRESA}+" e a GWM não se responsabilizam por objetos deixados no veículo. O(A) Cliente autoriza realizar o teste de rodagem por um membro responsável da "+ $F{Q_EMPRESA_NOME_EMPRESA} +", caso necessário. Outros serviços e/ou custos adicionais não computados nesta Ordem de Serviço poderão ser necessários e estarão descritos na Ordem de Serviço de Entrega, após a análise de seu veículo mediante prévia aprovação."]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" x="0" y="152" width="555" height="61" uuid="b3452b55-8884-4ed6-ac15-9c2229566e84"/>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="3" y="53" width="179" height="8" uuid="e01b360a-4df6-4970-9f29-726ba9abff2a"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="6" isBold="false" isItalic="true"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente ou pessoa por ele autorizada]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="0" width="555" height="10" uuid="a4343dd8-892e-405f-9a00-22c184141906"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="0" y="0" width="72" height="10" uuid="daee1117-560f-4a18-be81-2706d674f56c">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Técnico Responsável:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="172" y="0" width="19" height="10" uuid="83482be8-ad04-49d1-9760-d773ea00c1c7">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Início:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="231" y="0" width="19" height="10" uuid="9c94af04-01be-49e4-b96e-d9330c5750bf">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Data:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="409" y="0" width="28" height="10" uuid="dc7e7e35-871c-41c3-b91f-4238fd26a621">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Término:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="469" y="0" width="18" height="10" uuid="94ddd415-b04e-485a-a161-cc0b984c5b57">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Data:]]></text>
					</staticText>
					<textField>
						<reportElement mode="Transparent" x="191" y="0" width="35" height="10" uuid="404122cf-1041-42ba-97ff-4f6ca2c1cdb0">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="250" y="0" width="155" height="10" uuid="60cb47fc-e5da-4ebb-8d5a-8db509280c87">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="487" y="0" width="43" height="10" uuid="771a6e2f-1a91-499f-8a28-44299cb85e1d">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_ENCERRADO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="437" y="0" width="29" height="10" uuid="c5d62753-**************-b445fdc440ba">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_ENCERRADO}]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement mode="Transparent" x="72" y="0" width="98" height="10" uuid="fea9c988-0ec8-4c9d-a1e9-626b36d2967b">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_CONSULTOR}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="0" y="10" width="265" height="43" uuid="7277cc83-9856-44bd-a5b5-409974e7a685">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="2" y="0" width="42" height="10" uuid="001fd1e7-601e-433c-ae60-871a22283c09">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Recepção]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="91" y="0" width="19" height="10" uuid="9369cdf6-3c5a-4a88-a5aa-12c3e5e11241">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Data:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="194" y="0" width="19" height="10" uuid="7b390e0d-0a0b-41a8-94ac-5ccd0910c86b">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Hora:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="2" y="10" width="42" height="10" uuid="8875a0fe-2129-4ff2-98d1-fa1e48850f71">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Assinatura:]]></text>
					</staticText>
					<textField pattern="dd/MM/yyyy">
						<reportElement mode="Transparent" x="110" y="0" width="47" height="10" uuid="9bbfe149-a114-4500-a573-c8124379c5d7">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="213" y="0" width="22" height="10" uuid="8748b645-0f70-406a-a246-c0ae7f805e9b">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_HORA_EMISSAO}]]></textFieldExpression>
					</textField>
					<image hAlign="Center">
						<reportElement x="46" y="10" width="214" height="25" uuid="99178555-295b-4159-97e0-88ecbabe3744">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$F{Q_ASSINATURA_CLIENTE_RECEPCAO}]]></imageExpression>
					</image>
				</frame>
				<frame>
					<reportElement x="265" y="10" width="290" height="43" uuid="cf85a6d6-3213-49c1-a507-2c444a9a78e6"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="2" y="0" width="41" height="10" uuid="31d75840-d2c5-4ce0-9834-a98db6bb745f">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Entrega]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="81" y="0" width="18" height="10" uuid="dd2f4c73-**************-4415a35660a5">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Data:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="163" y="0" width="18" height="10" uuid="8d5b5ee2-19db-4cb9-894d-6c2647d51cca">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Hora:]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="2" y="10" width="41" height="10" uuid="0460dc53-5543-4b79-bafe-0dd4d5a4e235">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="true"/>
						</textElement>
						<text><![CDATA[Assinatura:]]></text>
					</staticText>
					<textField pattern="dd/MM/yyyy">
						<reportElement mode="Transparent" x="99" y="0" width="47" height="10" uuid="3d35e7ed-8fb9-407e-96de-b0e8e36b8ac4">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_ENTREGA} != null ? $F{Q_OS_DATA_ENTREGA}
:$F{Q_ASSINATURA_CLIE_ENTREGA_DT} != null  ? $F{Q_ASSINATURA_CLIE_ENTREGA_DT}
:$P{ASSINAR_DIGITALMENTE}.equals("S") ? $F{Q_EMPRESA_DATA_ATUAL} 
: $F{Q_OS_DATA_ENTREGA}]]></textFieldExpression>
					</textField>
					<textField pattern="HH:mm">
						<reportElement mode="Transparent" x="181" y="0" width="27" height="10" uuid="49febff0-8afd-411f-9cb8-c9ad401c3c11">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</reportElement>
						<textElement textAlignment="Left">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{Q_OS_DATA_ENTREGA} != null ? $F{Q_OS_DATA_ENTREGA}
:$F{Q_ASSINATURA_CLIE_ENTREGA_DT} != null  ? $F{Q_ASSINATURA_CLIE_ENTREGA_DT}
:$P{ASSINAR_DIGITALMENTE}.equals("S") ? $F{Q_EMPRESA_DATA_ATUAL} 
: $F{Q_OS_DATA_ENTREGA}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement mode="Transparent" x="60" y="23" width="93" height="18" forecolor="#FFFFFF" uuid="e5435557-fc02-4c3c-8d6e-1db68eec9033"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0">
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Left" verticalAlignment="Bottom">
							<font size="4"/>
						</textElement>
						<text><![CDATA[#CLIENTE]]></text>
					</staticText>
					<image hAlign="Center">
						<reportElement x="45" y="10" width="214" height="25" uuid="0d823bc5-74e9-4d01-8780-abad1578ca95">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<printWhenExpression><![CDATA[new Boolean($P{ASSINAR_DIGITALMENTE}.equals("N"))]]></printWhenExpression>
						</reportElement>
						<imageExpression><![CDATA[$F{Q_ASSINATURA_CLIENTE_ENTREGA}]]></imageExpression>
					</image>
				</frame>
			</frame>
		</band>
	</detail>
	<pageFooter>
		<band height="9">
			<textField>
				<reportElement mode="Transparent" x="500" y="1" width="54" height="8" uuid="2724bdc0-530b-430b-acf3-1d90ca4db1e0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Página " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
