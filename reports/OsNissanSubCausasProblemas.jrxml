<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsNissanSubCausasProblemas" pageWidth="494" pageHeight="842" whenNoDataType="NoPages" columnWidth="494" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="camposNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT
     ROW_NUMBER() OVER (ORDER BY  OSO.ITEM) AS NUM_LISTA,
     OSO.ITEM, 
       OSO.COD_PROBLEMA, 
       OSO.OBSERVACAO,
       GFL.TIPO,
       GFL.DESCRICAO
  FROM OS_ORIGINAL OSO,
       GARANTIA_FORD_LAUDO GFL,
       (SELECT '1' FROM DUAL CONNECT BY LEVEL < 5) TESTE
 WHERE GFL.COD_EMPRESA (+) = OSO.COD_EMPRESA
    AND GFL.NUMERO_OS (+) = OSO.NUMERO_OS    
    AND GFL.NUMERO_RECLAMACAO (+) = OSO.ITEM    
    AND OSO.COD_EMPRESA=$P{COD_EMPRESA}
    AND OSO.NUMERO_OS=$P{NUMERO_OS}
    AND OSO.COD_PROBLEMA IS NOT NULL
  ORDER BY ITEM]]>
	</queryString>
	<field name="NUM_LISTA" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="NUM_LISTA"/>
		<property name="com.jaspersoft.studio.field.label" value="NUM_LISTA"/>
	</field>
	<field name="ITEM" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="ITEM"/>
	</field>
	<field name="COD_PROBLEMA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_PROBLEMA"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_PROBLEMA"/>
	</field>
	<field name="OBSERVACAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="OBSERVACAO"/>
		<property name="com.jaspersoft.studio.field.label" value="OBSERVACAO"/>
	</field>
	<field name="TIPO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="TIPO"/>
		<property name="com.jaspersoft.studio.field.label" value="TIPO"/>
	</field>
	<field name="DESCRICAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="DESCRICAO"/>
		<property name="com.jaspersoft.studio.field.label" value="DESCRICAO"/>
	</field>
	<variable name="REGISTROS" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{ITEM}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="11" splitType="Immediate">
			<textField isStretchWithOverflow="true" evaluationTime="Auto">
				<reportElement style="camposNull" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="494" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1e6da218-bcea-4afc-9ceb-819528eb6b97">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Bottom">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO} == null
 ? $F{COD_PROBLEMA}
 : $F{COD_PROBLEMA} +" - "+  $F{DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
