<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaVinteSeteItensRecebimentoSubExtras" columnCount="4" pageWidth="575" pageHeight="77" columnWidth="143" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select a.item_resposta_eh_observacao as resposta_eh_observacao,
		    a.grupo_descricao,
		    a.selecionado_observacao as observacao,
		    Initcap(a.item_descricao) as item_descricao,
		    UPPER(a.selecionado_opcao_descricao) as descricao_opcao
from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
where a.id_checklist = 20000
      AND a.ID_GRUPO IN (20001, 20002, 20003, 20004) -- HONDA CHECKLIST(PARTES INTERNAS DO DOCUMENTO, PARTES EXTERNAS A SEGURANÇA, REPAROS RAPIDOS, ACESSORIOS)]]>
	</queryString>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="GRUPO_DESCRICAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<columnHeader>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField>
				<reportElement x="0" y="0" width="143" height="11" uuid="e66032e9-0d07-40c4-abbd-fe1620d282e0">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GRUPO_DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{RESPOSTA_EH_OBSERVACAO}.equals("N"))]]></printWhenExpression>
			<textField>
				<reportElement x="10" y="1" width="70" height="9" uuid="6245d408-899a-48a8-b27a-0105394cfd60">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="84" y="1" width="5" height="9" uuid="d23feb46-1ef2-442e-81e4-ba12fd1125ac">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[S]]></text>
			</staticText>
			<staticText>
				<reportElement x="109" y="1" width="5" height="9" uuid="caace420-a152-47a8-ab6e-44d7031cdb3d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[N]]></text>
			</staticText>
			<textField>
				<reportElement x="90" y="2" width="14" height="7" uuid="fe496273-5c34-4038-8730-386279aa16fc">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("SIM") ?"X":""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="115" y="2" width="14" height="7" uuid="4c1ddc92-be86-4091-af84-034bfbf0dbf4">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("NÃO") ?"X":""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{RESPOSTA_EH_OBSERVACAO}.equals("S"))]]></printWhenExpression>
			<staticText>
				<reportElement x="109" y="1" width="5" height="9" uuid="3f6f27bd-57b3-49cc-8f7e-03286377657b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[N]]></text>
			</staticText>
			<staticText>
				<reportElement x="84" y="1" width="5" height="9" uuid="dd6422f0-a84f-490b-98aa-bdc1145955e1">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[S]]></text>
			</staticText>
			<staticText>
				<reportElement x="115" y="2" width="14" height="7" uuid="f90d271f-3bd6-4dd5-9eb3-ef5db46e1e8f">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement x="10" y="1" width="70" height="9" uuid="48ada159-7cb8-4c0b-9b5c-8f9e64e2e670">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="90" y="2" width="14" height="7" uuid="6de11c04-652c-4736-b63f-49b5947a3e85">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}.equals("") 
	?""
	:"X"]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
</jasperReport>
