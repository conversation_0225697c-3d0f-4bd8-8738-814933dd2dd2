<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsMercedesSubServicos" pageWidth="386" pageHeight="842" columnWidth="386" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern="" isBlankWhenNull="true"/>
	<style name="alternateStyle" backcolor="#E0E0E0" isBlankWhenNull="true">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="IS_NULL" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[25623]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[34.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS_FABRICA" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[25623.0]]></defaultValueExpression>
	</parameter>
	<parameter name="STATUS_OS" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["0"]]></defaultValueExpression>
	</parameter>
	<parameter name="INTERNO_OS" class="java.lang.String">
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<parameter name="GARANTIA_OS" class="java.lang.String">
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<parameter name="SQL_FILTRO_RECLAMACAO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[" SELECT A1.COD_EMPRESA, A1.NUMERO_OS" +
" FROM OS A1, VW_OS_TIPOS A2" + 
" WHERE 1 = 1 " + 
" AND A1.COD_EMPRESA = A2.COD_EMPRESA" + 
" AND A1.TIPO        = A2.TIPO" + 
" AND A1.COD_EMPRESA = " + $P{COD_EMPRESA}  +
(($P{NUMERO_OS_FABRICA} != null) ?
	" AND A1.NUMERO_OS_FABRICA = " + $P{NUMERO_OS_FABRICA} :
	" AND A1.NUMERO_OS = " + $P{NUMERO_OS} +
	" AND A1.STATUS_OS = '" + $P{STATUS_OS} + "'"
) + 
($P{INTERNO_OS} == "S" ? 
	" AND NVL(A2.INTERNO, 'N') = 'S' ":
	($P{GARANTIA_OS} == "S" ? 
	    " AND NVL(A2.GARANTIA, 'N') = 'S'":
	     " AND NVL(A2.GARANTIA, 'N') <> 'S' " +
	     " AND NVL(A2.INTERNO, 'N') <> 'S'"
	     )
)]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[WITH Q_SERVICO AS
 (SELECT OS_SERVICOS.NUMERO_OS,
         OS_SERVICOS.COD_EMPRESA,
         OS_SERVICOS.ITEM,
         OS_SERVICOS.COD_SERVICO,
         SERVICOS.DESCRICAO_SERVICO AS DESCRICAO,
         OS_SERVICOS.TEMPO_PADRAO +
         NVL((SELECT SUM(OSA.TEMPO_ADICIONAL)
               FROM OS_SERVICOS_ADICIONAIS OSA
              WHERE OSA.COD_EMPRESA = OS_SERVICOS.COD_EMPRESA
                AND OSA.NUMERO_OS = OS_SERVICOS.NUMERO_OS
                AND OSA.COD_SERVICO = OS_SERVICOS.COD_SERVICO),
             0) AS TEMPO_PADRAO,
         OS_SERVICOS.PRECO_VENDA,
         SERVICOS.COD_SETOR,
         DESCRICAO_SETOR,
         2 AS IDAUX
    FROM OS_SERVICOS, SERVICOS, SERVICOS_SETORES
   WHERE OS_SERVICOS.COD_SERVICO = SERVICOS.COD_SERVICO
     AND SERVICOS.COD_SETOR = SERVICOS_SETORES.COD_SETOR
     AND OS_SERVICOS.COD_EMPRESA = $P{COD_EMPRESA}
     AND OS_SERVICOS.NUMERO_OS = $P{NUMERO_OS}
   ORDER BY OS_SERVICOS.ITEM, OS_SERVICOS.COD_SERVICO),
Q_RECLAMACAO AS
 (SELECT OO.NUMERO_OS,
         OO.COD_EMPRESA,
         OO.ITEM,
         NULL           AS COD_SERVICO,
         OO.DESCRICAO   AS DESCRICAO,
         NULL           AS TEMPO_PADRAO,
         NULL           AS PRECO_VENDA,
         NULL           AS COD_SETOR,
         NULL           AS DESCRICAO_SETOR,
         1 AS IDAUX
    FROM OS_ORIGINAL OO
   WHERE 1 = 1
     AND (OO.COD_EMPRESA, OO.NUMERO_OS) IN ( SELECT * FROM table(PKG_CRM_SERVICE_UTIL.Get_Table_os_Relac_Num_Fabrica($P{NUMERO_OS},$P{COD_EMPRESA})))
   ORDER BY OO.ITEM)
SELECT NUMERO_OS,
       COD_EMPRESA,
       ITEM,
       COD_SERVICO,
       DESCRICAO,
       TEMPO_PADRAO,
       PRECO_VENDA,
       COD_SETOR,
       DESCRICAO_SETOR,
       IDAUX
  FROM Q_RECLAMACAO
UNION ALL
SELECT NUMERO_OS,
       COD_EMPRESA,
       ITEM,
       COD_SERVICO,
       DESCRICAO,
       TEMPO_PADRAO,
       PRECO_VENDA,
       COD_SETOR,
       DESCRICAO_SETOR,
       IDAUX
  FROM Q_SERVICO
 ORDER BY ITEM, IDAUX]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="COD_SERVICO" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="TEMPO_PADRAO" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="COD_SETOR" class="java.lang.String"/>
	<field name="DESCRICAO_SETOR" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="11" splitType="Stretch">
			<textField>
				<reportElement style="alternateStyle" mode="Opaque" x="0" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="a86762f7-1fc5-4063-a453-7a278342d176">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement style="alternateStyle" mode="Opaque" x="40" y="0" width="50" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="9a1539a4-cf61-4bf1-9aca-6fbe5bb93e5b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="alternateStyle" mode="Opaque" x="90" y="0" width="239" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="44e19063-4635-472d-8b71-6d28f14fcd1c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{COD_SERVICO}==null? "": $F{COD_SERVICO} + " - " ) + $F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField pattern="¤#,##0.00;¤-#,##0.00">
				<reportElement style="alternateStyle" mode="Opaque" x="329" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="f629ccce-70d0-43e6-802e-912d34e24d1a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
