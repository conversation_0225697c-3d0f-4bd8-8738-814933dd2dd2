<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaMotoVinteUmItensSubServicos" columnCount="2" pageWidth="382" pageHeight="66" columnWidth="191" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="71917097-e8b0-4ca7-b4e5-b7a939dd2115">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[with DADOS as (
select info.cod_empresa as COD_EMPRESA,
       info.cod_produto as COD_PRODUTO,
       info.cod_modelo as COD_MODELO,
       info.tipo_os as TIPO_OS,
       info.cod_segmento as COD_SEGMENTO
  from (select os.cod_empresa,dv.Cod_Produto, dv.cod_modelo, os.tipo as tipo_os, produtos.cod_segmento
          from os, os_dados_veiculos dv, produtos
         where os.numero_os = dv.numero_os
           and os.cod_empresa = dv.cod_empresa
           and os.cod_empresa = $P{COD_EMPRESA}
           and os.numero_os = $P{NUMERO_OS}
           and produtos.cod_produto = dv.cod_produto
           and 'OS' = 'OS') info
),
TODOS_ITENS as ( 
SELECT A.ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */
     'CheckList' AS GRUPO,
       A.DESCRICAO AS DESCRICAO_ITEM,       
       A.ATIVO,
       A.COD_ITEM,
       A.OBRIGATORIO,
       A.ORDEM,
       A.PODE_TER_FOTO,  
       B.DESCRICAO DESCRICAO_GRUPO,
       B.TIPO,       
       B.APLICACAO,
       C.OBSERVACAO,
       D.DESCRICAO AS DESCRICAO_OPCAO,
       A.RESPOSTA_EH_OBSERVACAO,
       DECODE(NVL((SELECT PO.SEMAFORO
                            FROM MOB_PERTENCE_OPCAO PO
                           WHERE PO.COD_ITEM = A.COD_ITEM
                             AND PO.ID_OPCAO = C.ID_OPCAO
                             AND ROWNUM = 1),
                          ''),
                      'G',
                      'GREEN',
                      'R',
                      'RED',
                      'Y',
                      'YELLOW',
                      '') AS COR_OPCAO_SELECIONADA, /* se for GREEM então marco como sim no relatorio */
       B.ORDEM AS ORDEM_GRUPO  
FROM MOB_PERTENCE_ITEM A, MOB_PERTENCE_GRUPO B, MOB_OS_PERTENCE C, MOB_OPCAO D
WHERE A.ID_GRUPO = B.ID_GRUPO
 AND B.TIPO = 'C'
 AND A.COD_ITEM = C.COD_ITEM (+)
 AND B.APLICACAO = 'R'
 AND C.COD_EMPRESA(+) = $P{COD_EMPRESA}
 AND C.NUMERO_OS(+) = $P{NUMERO_OS}
 AND C.ID_OPCAO = D.ID_OPCAO  (+)
 AND B.ID_GRUPO = 40000 -- HONDA CHECKLIST(SERVIÇOS REQUISITADOS)
 ),
FILTRO_CRUZA_EPRESA_SEGMENTO AS ( 
SELECT * /* CAMADA 2 - FILTRO TODOS OS ITENS QUE SEJA DA RESPECTIVA EMPRESA OU NÃO POSSUA EMPRESA */
FROM TODOS_ITENS A
WHERE EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo
                     AND mc.cod_empresa = $P{COD_EMPRESA}
                     AND mc.cod_segmento = (SELECT COD_SEGMENTO FROM DADOS)) 
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo)                               
),
FILTRO_CRUZA_TIPO_OS AS ( 
SELECT * /* CAMADA 3 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO TIPO DE OS OU NÃO POSSUA TIPO DE OS VINCULADO */
FROM FILTRO_CRUZA_EPRESA_SEGMENTO A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_tp_os cto
                  WHERE  cto.id_grupo = a.id_grupo
                         AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                         AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS)
                         AND cto.tipo = (SELECT TIPO_OS FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_tp_os cto
              WHERE  cto.id_grupo = A.id_grupo
                     AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                     AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS))
),
FILTRO_CRUZA_MODELO AS ( 
SELECT * /* CAMADA 4 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO MODELO OU NÃO POSSUA MODELO VINCULADO */
FROM FILTRO_CRUZA_TIPO_OS A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_modelo cm
                  WHERE  cm.id_grupo = a.id_grupo
                         AND cm.cod_produto = (SELECT COD_PRODUTO FROM DADOS)
                         AND cm.cod_modelo = (SELECT COD_MODELO FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_modelo cm
              WHERE   cm.id_grupo = a.id_grupo)
)
select ROW_NUMBER() OVER(ORDER BY FILTRO_CRUZA_MODELO.ORDEM_GRUPO, FILTRO_CRUZA_MODELO.ID_GRUPO, FILTRO_CRUZA_MODELO.ORDEM) AS LINHA,
       ID_GRUPO,
       GRUPO,
       CASE WHEN SUBSTR(DESCRICAO_ITEM, 2,5) like '%-%' then TRIM(SUBSTR(DESCRICAO_ITEM, 1, INSTR(DESCRICAO_ITEM, '-') - 1)) else '' end AS ACAO_ITEM,
       DESCRICAO_ITEM,
       ATIVO,
       FILTRO_CRUZA_MODELO.COD_ITEM,
       OBRIGATORIO,
       FILTRO_CRUZA_MODELO.ORDEM,
       PODE_TER_FOTO,
       DESCRICAO_GRUPO,
       TIPO,
       APLICACAO,
       CASE WHEN LENGTH(OBSERVACAO) > 30 then
              SUBSTR(OBSERVACAO,0,30) || '...'
            ELSE
              OBSERVACAO
       END as OBSERVACAO,
       DESCRICAO_OPCAO,
       RESPOSTA_EH_OBSERVACAO,
       COR_OPCAO_SELECIONADA,
       F.FOTO_ICONE,
       CASE
         WHEN F.FOTO_ICONE IS NULL THEN
          'N'
         ELSE
          'S'
       END TEM_IMAGEM
  from FILTRO_CRUZA_MODELO, MOB_OS_PERTENCE_FOTO F
 WHERE $P{COD_EMPRESA} = F.COD_EMPRESA(+)
   AND $P{NUMERO_OS} = F.NUMERO_OS(+)
   AND FILTRO_CRUZA_MODELO.COD_ITEM = F.COD_ITEM(+)
 ORDER BY ORDEM_GRUPO, ID_GRUPO, ORDEM]]>
	</queryString>
	<field name="LINHA" class="java.lang.Double"/>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="GRUPO" class="java.lang.String"/>
	<field name="ACAO_ITEM" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="ATIVO" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="OBRIGATORIO" class="java.lang.String"/>
	<field name="ORDEM" class="java.lang.Double"/>
	<field name="PODE_TER_FOTO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="APLICACAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<field name="RESPOSTA_EH_OBSERVACAO" class="java.lang.String"/>
	<field name="COR_OPCAO_SELECIONADA" class="java.lang.String"/>
	<field name="FOTO_ICONE" class="java.awt.Image"/>
	<field name="TEM_IMAGEM" class="java.lang.String"/>
	<detail>
		<band height="11">
			<printWhenExpression><![CDATA[new Boolean($F{RESPOSTA_EH_OBSERVACAO}.equals("N") && !$F{DESCRICAO_ITEM}.equals("Revisão") && !$F{DESCRICAO_ITEM}.equals("Substituição de cabos") && !$F{DESCRICAO_ITEM}.equals("Troca de pneu(s)") && !$F{DESCRICAO_ITEM}.equals("Acessórios"))]]></printWhenExpression>
			<textField>
				<reportElement x="11" y="2" width="9" height="7" uuid="77ddab6e-f0ff-490c-b762-e870c423dece">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.equals("") ?"":"X" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="23" y="1" width="165" height="9" uuid="6245d408-899a-48a8-b27a-0105394cfd60">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM}]]></textFieldExpression>
			</textField>
		</band>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{DESCRICAO_ITEM}.equals("Revisão"))]]></printWhenExpression>
			<textField>
				<reportElement x="23" y="1" width="28" height="9" uuid="add3aab8-6c18-42db-843d-c9568c56cbf9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA["Revisão"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="2" width="9" height="7" uuid="8026cb4c-c493-40d5-b147-fb28467bf79e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.equals("") ?"":"X" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="57" y="2" width="9" height="7" uuid="96c540dc-8d1a-47c3-82ba-c4844475d72c">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("1") ?"X":"" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="51" y="2" width="6" height="7" uuid="4e92e8d3-2fe9-4753-9508-7f6f8828c9b0">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[1ª]]></text>
			</staticText>
			<staticText>
				<reportElement x="67" y="2" width="6" height="7" uuid="ff874dab-859f-48aa-9a19-f96a58ddbdc5">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[2ª]]></text>
			</staticText>
			<textField>
				<reportElement x="73" y="2" width="9" height="7" uuid="76799c35-b2f0-424a-88ad-c138495f9393">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("2") ?"X":"" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="89" y="2" width="9" height="7" uuid="a951829f-6aea-4e41-9199-0951cb494c50">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("3") ?"X":"" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="83" y="2" width="6" height="7" uuid="1d042d9a-3ac8-4a0f-96a8-9f5f48469421">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[3ª]]></text>
			</staticText>
			<textField>
				<reportElement x="105" y="2" width="9" height="7" uuid="4ef3055c-b414-4e69-b452-af2cde1c52b1">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("4") ?"X":"" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="99" y="2" width="6" height="7" uuid="19a631c8-50e2-4b0c-a65c-9ce24a895141">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[4ª]]></text>
			</staticText>
			<staticText>
				<reportElement x="115" y="2" width="6" height="7" uuid="af7ff1c2-2a5e-4e21-9300-ed515402cd3e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[5ª]]></text>
			</staticText>
			<textField>
				<reportElement x="121" y="2" width="9" height="7" uuid="ae9abca6-2e6b-441c-b4fa-2119903731ef">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("5") ?"X":"" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="131" y="2" width="6" height="7" uuid="f763967f-75e9-4a83-852b-65a5e6e3e529">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[6ª]]></text>
			</staticText>
			<textField>
				<reportElement x="137" y="2" width="9" height="7" uuid="ca763dba-b023-425f-8a99-ebb574e071f5">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("6") ?"X":"" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="147" y="2" width="6" height="7" uuid="f8ad35ac-6381-4f9b-a508-63447cd821e6">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[7ª]]></text>
			</staticText>
			<textField>
				<reportElement x="153" y="2" width="9" height="7" uuid="5d8df72d-dd7d-4b42-94a6-ad7bee15a07a">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("7") ?"X":"" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="179" y="2" width="9" height="7" uuid="5a0f4829-5391-4358-a57d-810650c0c78a">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? !$F{OBSERVACAO}.replace("ª","").equals("1")
	  && !$F{OBSERVACAO}.replace("ª","").equals("2")
	  && !$F{OBSERVACAO}.replace("ª","").equals("3")
	  && !$F{OBSERVACAO}.replace("ª","").equals("4")
	  && !$F{OBSERVACAO}.replace("ª","").equals("5")
	  && !$F{OBSERVACAO}.replace("ª","").equals("6")
	  && !$F{OBSERVACAO}.replace("ª","").equals("7")
	 ?"X":"" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="165" y="2" width="13" height="7" uuid="d8e6516e-727b-43f0-ba3e-03f8c5fc3f0d">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? !$F{OBSERVACAO}.replace("ª","").equals("1")
	  && !$F{OBSERVACAO}.replace("ª","").equals("2")
	  && !$F{OBSERVACAO}.replace("ª","").equals("3")
	  && !$F{OBSERVACAO}.replace("ª","").equals("4")
	  && !$F{OBSERVACAO}.replace("ª","").equals("5")
	  && !$F{OBSERVACAO}.replace("ª","").equals("6")
	  && !$F{OBSERVACAO}.replace("ª","").equals("7")
	 ?$F{OBSERVACAO}.replace("ª","") + "ª":"" 
	: $F{COR_OPCAO_SELECIONADA}.equals("GREEN")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
		</band>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{DESCRICAO_ITEM}.equals("Troca de pneu(s)"))]]></printWhenExpression>
			<textField>
				<reportElement x="137" y="2" width="9" height="7" uuid="9541d3f3-6628-4ff6-bcfa-2406a672ed72">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("1") ?"X":"" 
	: $F{DESCRICAO_OPCAO}.equals("Traseiro")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="117" y="2" width="20" height="7" uuid="f9f4e74d-85fa-430c-a8a3-499fe2b74ed0">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Traseiro]]></text>
			</staticText>
			<staticText>
				<reportElement x="82" y="2" width="22" height="7" uuid="8b640626-e08c-4d52-aa75-37aa36ef6eb4">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Dianteiro]]></text>
			</staticText>
			<textField>
				<reportElement x="23" y="1" width="57" height="9" uuid="178f761f-24da-41b4-b925-f2e675355fa5">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA["Troca de pneu(s):"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="104" y="2" width="9" height="7" uuid="1b314ef9-bcc2-4172-af3b-468bf843a71e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("1") ?"X":"" 
	: $F{DESCRICAO_OPCAO}.equals("Dianteiro")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="2" width="9" height="7" uuid="28d9dc75-fd0c-46f3-9ea9-c30efb00cf69">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.equals("") ?"":"X" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
		</band>
		<band height="11">
			<printWhenExpression><![CDATA[new Boolean($F{DESCRICAO_ITEM}.equals("Acessórios"))]]></printWhenExpression>
			<textField>
				<reportElement x="81" y="2" width="9" height="7" uuid="1dbd0bb1-c4bb-4aaf-a15c-89832d5c0722">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("1") ?"X":"" 
	: $F{DESCRICAO_OPCAO}.equals("Honda")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="23" y="1" width="38" height="9" uuid="157e803a-ff14-4956-9ce8-7d13f5dd539c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA["Acessórios:"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="94" y="2" width="35" height="7" uuid="5e02ca43-0cc9-4da7-8c7a-e398f699b148">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Outras Marcas]]></text>
			</staticText>
			<textField>
				<reportElement x="11" y="2" width="9" height="7" uuid="5cb62f6e-4d21-4581-b9c0-246e460d1b9e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.equals("") ?"":"X" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="129" y="2" width="9" height="7" uuid="e0d005b5-ff35-45fb-850a-e965526912ca">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.replace("ª","").equals("1") ?"X":"" 
	: $F{DESCRICAO_OPCAO}.equals("Outras Marcas")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="63" y="2" width="18" height="7" uuid="70068aa9-cb69-4998-bc94-f891454bb512">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Honda]]></text>
			</staticText>
		</band>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{DESCRICAO_ITEM}.equals("Substituição de cabos"))]]></printWhenExpression>
			<textField>
				<reportElement x="11" y="2" width="9" height="7" uuid="af127d0c-e6ff-4397-a2e6-e921941a8f13">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.equals("") ?"":"X" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="23" y="1" width="65" height="9" uuid="7c42f5b2-51c2-4b7e-a87a-3c0393c5d651">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA["Substituição de cabos"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="88" y="1" width="100" height="9" uuid="7d9562f2-4712-4ab3-8435-7c40a0a5d708">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
			</textField>
		</band>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[new Boolean($F{DESCRICAO_ITEM}.equals("Outro"))]]></printWhenExpression>
			<textField>
				<reportElement x="11" y="2" width="9" height="7" uuid="f2d27ad4-df37-435a-bfc5-d33e79616f19">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{RESPOSTA_EH_OBSERVACAO}.equals("S")
	? $F{OBSERVACAO}.equals("") ?"":"X" 
	: $F{COR_OPCAO_SELECIONADA}.equals("RED")
		? "X" 
		: ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="46" y="1" width="142" height="9" uuid="4c61ce10-efba-4c33-95d0-3f659c1063bd">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="23" y="1" width="23" height="9" uuid="5e098162-e11b-4c69-aa78-0bfa98828498">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA["Outro"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
