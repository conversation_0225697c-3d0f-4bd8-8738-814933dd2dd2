<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklistReclamacoes" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="0efcef53-ba2f-4102-8b70-a807a9128687">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="344"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="646"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="DataAdapter.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT DECODE(LENGTH(ITEM),2,ITEM,Null) AS ITEM, DESCRICAO, DT_INCLUSAO,
       Nvl((to_Char(DT_INCLUSAO, 'dd/mm/yy hh24:mi') || ' - ' || DECODE(LENGTH(ITEM),2,ITEM,Null) || ' - ' || DESCRICAO),DESCRICAO) Desc_Ford FROM (
SELECT SUBSTR(TO_CHAR(OS_ORIGINAL.ITEM + 100), 2, 2) AS ITEM,
       Decode(OS_TIPOS.GARANTIA,'S',(Nvl(OS_ORIGINAL.COD_GWM_RECLAMACAO,GWM.COD_RECLAMACAO) || DECODE(Nvl(OS_ORIGINAL.COD_GWM_RECLAMACAO,GWM.COD_RECLAMACAO), nULL, '', ' - ') || OS_ORIGINAL.DESCRICAO),    
             (GWM.COD_RECLAMACAO || DECODE(GWM.COD_RECLAMACAO, nULL, '', ' - ') || OS_ORIGINAL.DESCRICAO) )DESCRICAO, OS_ORIGINAL.DT_INCLUSAO
          FROM OS_ORIGINAL, GWM_RECLAMACAO GWM, OS, OS_TIPOS 
 WHERE ((OS_ORIGINAL.NUMERO_OS = $P{NUMERO_OS}) 
   AND (OS_ORIGINAL.COD_EMPRESA = $P{COD_EMPRESA}))
   AND (OS.NUMERO_OS = $P{NUMERO_OS}) 
   AND (OS.COD_EMPRESA = $P{COD_EMPRESA}) 
   AND (OS_TIPOS.TIPO = OS.TIPO)
   AND (OS_ORIGINAL.COD_GWM_RECLAMACAO = GWM.COD_RECLAMACAO(+))

 UNION

SELECT SUBSTR(TO_CHAR(OS_ORIGINAL.ITEM + 100), 2, 2) || ' - ' || GARANTIA_FORD_CCC.COD_CCC AS ITEM,
        'CCC - '||GARANTIA_FORD_CCC.COD_CCC  || '  ' || GARANTIA_FORD_CCC.DESCRICAO AS DESCRICAO, OS_ORIGINAL.DT_INCLUSAO
  FROM OS_ORIGINAL,
       GARANTIA_FORD_CCC
 WHERE OS_ORIGINAL.COD_GWM_RECLAMACAO = GARANTIA_FORD_CCC.COD_CCC
   AND ((OS_ORIGINAL.NUMERO_OS = $P{NUMERO_OS})
   AND (OS_ORIGINAL.COD_EMPRESA = $P{COD_EMPRESA}))

 UNION  

SELECT SUBSTR(TO_CHAR(T.ITEM + 100), 2, 2) || ' - ' || GARANTIA_FORD_CCC.COD_CCC AS ITEM,
        'CCC - '||GARANTIA_FORD_CCC.COD_CCC  || '  ' || GARANTIA_FORD_CCC.DESCRICAO AS DESCRICAO, T.DT_INCLUSAO
  FROM OS_SERVICOS T,
       GARANTIA_FORD_CCC
 WHERE T.COD_CCC = GARANTIA_FORD_CCC.COD_CCC
   AND ((T.NUMERO_OS  = $P{NUMERO_OS})
   AND (T.COD_EMPRESA = $P{COD_EMPRESA}))
ORDER BY ITEM)]]>
	</queryString>
	<field name="ITEM" class="java.lang.String"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="DT_INCLUSAO" class="java.sql.Timestamp"/>
	<field name="DESC_FORD" class="java.lang.String"/>
	<title>
		<band height="26" splitType="Stretch">
			<staticText>
				<reportElement x="15" y="7" width="200" height="15" uuid="24c48c20-53ab-4ac5-a7aa-2b31e9db5c64"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Itens Originais]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="2" y="1" width="553" height="1" uuid="04bac2d7-3bc5-44ed-a739-1874ad27bba4">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="554" y="2" width="1" height="24" uuid="04af316d-211f-4dd1-9b84-7f0cad5de4ed">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="1" y="1" width="1" height="24" uuid="e1f4e1a7-7b8a-4336-aac6-aa28dfca2ad8">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
		</band>
	</title>
	<detail>
		<band height="25" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="15" y="0" width="70" height="20" uuid="7240c292-72cd-4cb6-a6f8-2b1b71a62094"/>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="0" width="450" height="20" uuid="*************-4b40-80d1-0bfafdf89306"/>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="-1" width="1" height="26" uuid="3f73e503-5080-4309-8b0c-7098e169b9d0">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="554" y="-1" width="1" height="26" uuid="f7318e4c-d3fd-4ce8-b65f-3b64a6229dae">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
			</line>
		</band>
	</detail>
	<summary>
		<band height="5">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<line>
				<reportElement positionType="Float" x="1" y="0" width="554" height="1" uuid="44778884-bb30-4873-beac-59c0d569b054">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
	</summary>
</jasperReport>
