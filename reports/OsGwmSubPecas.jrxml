<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsGwmSubPecas" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="c4260c06-1161-4f2f-a930-695e6a452fad">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="default_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString>
		<![CDATA[SELECT OS_REQUISICOES.ITEM
       ,(ROW_NUMBER() OVER(PARTITION BY OS_REQUISICOES.ITEM ORDER BY OS_REQUISICOES.COD_SERVICO, OS_REQUISICOES.COD_ITEM ASC)) AS ITEM_ORDER
      ,DECODE(FORNECEDOR_ESTOQUE.OFICIAL,
              'N',
              '*' || ITENS.DESCRICAO,
              ITENS.DESCRICAO) AS DESCRICAO
      ,OS_REQUISICOES.QUANTIDADE
      ,OS_REQUISICOES.COD_ITEM
      ,DECODE(OS.STATUS_OS,
              1,
              OS_REQUISICOES.PRECO_FINAL,
              DECODE(OS.CORTESIA,
                     'S',
                     OS_REQUISICOES.PRECO_CORTESIA,
                     DECODE(OS_TIPOS.INTERNO,
                            'S',
                            ROUND((100 +
                                  DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS,
                                          'S',
                                          DECODE(ITENS.COD_TRIBUTACAO,
                                                 '1',
                                                 DECODE(PARM_SYS.REGIME_ICMS,
                                                        'S',
                                                        DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                               'S',
                                                               DECODE(ICC.CLASSE_PECA,
                                                                      2,
                                                                      OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                           0),
                                                               0),
                                                        OS_TIPOS.AUMENTO_PRECO_PECA),
                                                0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                                  DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                         'V',
                                         OS_REQUISICOES.PRECO_VENDA,
                                         'G',
                                         OS_REQUISICOES.PRECO_GARANTIA,
                                         'F',
                                         OS_REQUISICOES.CUSTO_FORNECEDOR,
                                         'P',
                                         OS_REQUISICOES.PRECO_ORIGINAL,
                                         DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL)),4) / 100,
                            DECODE(OS_TIPOS.GARANTIA,
                                   'S',
                                   DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                          'P',
                                          NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                              OS_REQUISICOES.PRECO_GARANTIA),
                                          OS_REQUISICOES.PRECO_GARANTIA),
                                   DECODE(NVL(OS.FABRICA, 'N'),
                                          'S',
                                          OS_REQUISICOES.PRECO_GARANTIA,
                                          DECODE(SIGN(OS.FRANQUIA),
                                                 1,
                                                 PRECO_FRANQUIA,
                                                 ROUND((100 - NVL(SEGURADORA.DESCONTO_REQUISICAO,
                                                                  0)) *
                                                       DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                                              'P',
                                                              NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                                                  OS_REQUISICOES.PRECO_VENDA),
                                                              DECODE(NVL(OS.SEGURADORA,
                                                                         'N'),
                                                                     'N',
                                                                     DECODE(OS_TIPOS.OS_EXTERNA_COM_PRECO_GARANTIA,
                                                                            'S',
                                                                            OS_REQUISICOES.PRECO_GARANTIA,
                                                                            OS_REQUISICOES.PRECO_VENDA),
                                                                     OS_REQUISICOES.PRECO_VENDA)),
                                                       4) / 100)))))) AS PRECO_VENDA
      ,OS_REQUISICOES.QUANTIDADE *
       DECODE(OS.STATUS_OS,
              1,
              OS_REQUISICOES.PRECO_FINAL,
              DECODE(OS.CORTESIA,
                     'S',
                     OS_REQUISICOES.PRECO_CORTESIA,
                     DECODE(OS_TIPOS.INTERNO,
                            'S',
                            ROUND((100 +
                                  DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS,
                                          'S',
                                          DECODE(ITENS.COD_TRIBUTACAO,
                                                 '1',
                                                 DECODE(PARM_SYS.REGIME_ICMS,
                                                        'S',
                                                        DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                               'S',
                                                               DECODE(ICC.CLASSE_PECA,
                                                                      2,
                                                                      OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                           0),
                                                               0),
                                                        OS_TIPOS.AUMENTO_PRECO_PECA),
                                                0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                                  DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                         'V',
                                         OS_REQUISICOES.PRECO_VENDA,
                                         'G',
                                         OS_REQUISICOES.PRECO_GARANTIA,
                                         'F',
                                         OS_REQUISICOES.CUSTO_FORNECEDOR,
                                         'P',
                                         OS_REQUISICOES.PRECO_ORIGINAL,
                                         DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL)),
                                  4) / 100,
                            DECODE(OS_TIPOS.GARANTIA,
                                   'S',
                                   OS_REQUISICOES.PRECO_GARANTIA,
                                   DECODE(NVL(OS.FABRICA, 'N'),
                                          'S',
                                          OS_REQUISICOES.PRECO_GARANTIA,
                                          DECODE(SIGN(OS.FRANQUIA),
                                                 1,
                                                 PRECO_FRANQUIA,
                                                 ROUND((100 - NVL(SEGURADORA.DESCONTO_REQUISICAO,
                                                                  0)) *
                                                       DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                                              'V',
                                                              OS_REQUISICOES.PRECO_VENDA,
                                                              DECODE(NVL(OS.SEGURADORA,
                                                                         'N'),
                                                                     'N',
                                                                     DECODE(OS_TIPOS.OS_EXTERNA_COM_PRECO_GARANTIA,
                                                                            'S',
                                                                            OS_REQUISICOES.PRECO_GARANTIA,
                                                                            NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                                                                OS_REQUISICOES.PRECO_VENDA)),
                                                                     NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                                                         OS_REQUISICOES.PRECO_VENDA))),
                                                       4) / 100)))))) AS PRECO_TOTAL
      , NVL(OTE.TIPO_FABRICA,OT.TIPO_FABRICA) AS TIPO_FABRICA_EMPRESA
      ,VOR.PRECO_LIQUIDO
      ,OS_REQUISICOES.COD_FORNECEDOR
      ,OS_REQUISICOES.TIPO
      ,(SELECT ST.NOME 
          FROM OS_TEMPOS_EXECUTADOS OTE
          INNER JOIN SERVICOS_TECNICOS  ST 
            ON (ST.COD_TECNICO = OTE.COD_TECNICO
            AND ST.COD_EMPRESA = OTE.COD_EMPRESA) 
          WHERE OTE.COD_EMPRESA = OSS.COD_EMPRESA
            AND OTE.NUMERO_OS   = OSS.NUMERO_OS
            AND OTE.COD_SERVICO = OSS.COD_SERVICO
            AND ROWNUM <= 1)  
       AS PRODUTIVO_PRISMA
      ,NVL(OT.GARANTIA,'N') AS GARANTIA 
      ,OS.TIPO AS TIPO_OS
  FROM OS_REQUISICOES
      ,ESTOQUE
      ,ITENS
      ,OS
      ,VW_OS_TIPOS           OS_TIPOS
      ,FORNECEDOR_ESTOQUE
      ,SEGURADORA
      ,ITENS_FORNECEDOR
      ,ITENS_CLASSE_CONTABIL ICC
      ,PARM_SYS
      ,PARM_SYS2
      ,OS_TIPOS_EMPRESAS     OTE
      ,OS_TIPOS             OT
      , VW_OS_REQUISITADOS  VOR
      ,OS_SERVICOS        OSS
WHERE 1=1 
   AND OS_REQUISICOES.COD_EMPRESA    = VOR.COD_EMPRESA 
   AND OS_REQUISICOES.REQUISICAO     = VOR.REQUISICAO  
   AND OS_REQUISICOES.COD_FORNECEDOR = VOR.COD_FORNECEDOR 
   AND OS_REQUISICOES.COD_ITEM       = VOR.COD_ITEM
   AND (OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM)
   AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR
  AND (OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM (+))
   AND (OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR (+))
  AND (OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA (+))
  AND (OS_REQUISICOES.NUMERO_OS = OS.NUMERO_OS)
  AND (OS_REQUISICOES.COD_EMPRESA = OS.COD_EMPRESA)
  AND (OS.TIPO = OS_TIPOS.TIPO)
  AND (OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA)
   AND (OS.TIPO = OTE.TIPO)
   AND OT.TIPO         (+) = OTE.TIPO
   AND (OS.COD_EMPRESA = OTE.COD_EMPRESA)
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
   AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
   AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL(+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
  
   AND (OSS.COD_EMPRESA (+) = OS_REQUISICOES.COD_EMPRESA
   AND OSS.NUMERO_OS    (+) = OS_REQUISICOES.NUMERO_OS
   AND OSS.ITEM         (+) = OS_REQUISICOES.ITEM
   AND OSS.COD_SERVICO  (+) = OS_REQUISICOES.COD_SERVICO)
   AND OS.NUMERO_OS = $P{NUMERO_OS}
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   order by OS_REQUISICOES.ITEM, OS_REQUISICOES.COD_SERVICO, OS_REQUISICOES.COD_ITEM]]>
	</queryString>
	<field name="ITEM" class="java.math.BigDecimal"/>
	<field name="ITEM_ORDER" class="java.math.BigDecimal"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.math.BigDecimal"/>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="PRECO_VENDA" class="java.math.BigDecimal"/>
	<field name="PRECO_TOTAL" class="java.math.BigDecimal"/>
	<field name="TIPO_FABRICA_EMPRESA" class="java.lang.String"/>
	<field name="PRECO_LIQUIDO" class="java.math.BigDecimal"/>
	<field name="COD_FORNECEDOR" class="java.math.BigDecimal"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="PRODUTIVO_PRISMA" class="java.lang.String"/>
	<field name="GARANTIA" class="java.lang.String"/>
	<field name="TIPO_OS" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="20">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="20" uuid="7b1cf3c2-6d17-4ab8-9730-8c8ed4e1e4b1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="140" height="10" uuid="979e23fb-4d9e-4740-bc6b-b0c5ae34fe5b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Peças]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="42" y="10" width="42" height="10" uuid="7d841074-4cfe-4157-a97e-34f7d7846d04">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo de OS]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="154" y="10" width="280" height="10" uuid="aafdef03-c09f-42df-8cf6-ff807bdb65f9">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Descrição das peças e Lubrificantes]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="10" width="42" height="10" uuid="bd6539f0-23aa-40a7-a195-75945cb10f30">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Item]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="84" y="10" width="70" height="10" uuid="3b14297a-548d-4a9f-8ac9-e69df102d35b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Código do Produto]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="475" y="10" width="80" height="10" uuid="a7f7f20d-81df-4c7e-97be-4957c696a39a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Valor Total]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="434" y="10" width="41" height="10" uuid="b540250e-c4b8-428e-a471-418f7c343d07">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="10" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<frame>
				<reportElement x="0" y="0" width="555" height="10" uuid="209444c4-2584-47b0-9d73-b61e06668e60">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="42" y="0" width="42" height="10" uuid="a7fb0f8a-0d63-4336-8f63-db653fd57674">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TIPO_FABRICA_EMPRESA}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement mode="Transparent" x="0" y="0" width="42" height="10" uuid="5ba2d287-64b0-4abc-8559-b936189100f0">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ITEM} + "." + $F{ITEM_ORDER}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###">
					<reportElement mode="Transparent" x="434" y="0" width="41" height="10" uuid="f5c002f1-33b9-4607-9c0c-63a537367dea">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
				</textField>
				<textField pattern="#,#00.00#">
					<reportElement mode="Transparent" x="475" y="0" width="80" height="10" uuid="82f567e8-239b-414a-9130-5d7102e0fef7">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRECO_LIQUIDO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="84" y="0" width="70" height="10" uuid="da0d316a-d8ff-44f8-8e95-648297eb42ad">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box leftPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{COD_ITEM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="154" y="0" width="280" height="10" uuid="4797ac7f-c5b1-4cba-8e55-051f3170b1f6">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box leftPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="1">
			<line>
				<reportElement positionType="Float" x="0" y="0" width="555" height="1" uuid="1bce2e3f-7686-4f58-b190-4150337ff9a1">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
			</line>
		</band>
	</columnFooter>
</jasperReport>
