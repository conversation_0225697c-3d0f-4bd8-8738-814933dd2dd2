<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RequisicoesMiniSubItens" pageWidth="186" pageHeight="842" columnWidth="186" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a5d195c3-46e2-4e2e-9575-573e21d1a687">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="false"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="570"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="416"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="RECADO_DESCRICAO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="ORDENAR_POR_CODITEM_OU_LOCACAO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["CODITEM"]]></defaultValueExpression>
	</parameter>
	<parameter name="MOSTRAR_CODITEM_OU_PARTNUMBER" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["CODITEM"]]></defaultValueExpression>
	</parameter>
	<parameter name="SQL_ORDERBY" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{ORDENAR_POR_CODITEM_OU_LOCACAO}.equals("LOCACAO")
? "Order by qry_requisicao.locacao asc"
: "order by qry_requisicao.cod_item asc"]]></defaultValueExpression>
	</parameter>
	<parameter name="REQUISICAO" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SQL_FILTER_REQUISICAO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[$P{REQUISICAO} == null
? "1=1"
: "os_requisicoes.requisicao =101272"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[with qry_requisicao as (
select os_requisicoes.requisicao,
 os_requisicoes.cod_item,
 os_requisicoes.cod_fornecedor,
 os_requisicoes.data,
 os_requisicoes.cod_empresa,
 itens_fornecedor.part_number,
 fornecedor_estoque.nome_fornecedor,
 case when parm_sys3.forcar_serie_pecas = 'S' and os_requisicoes.chassi_prod_forca is not null then substr(itens.descricao,1,35) || ' - ' || os_requisicoes.chassi_prod_forca
   else itens.descricao end as descricao_item,
 itens.unidade,
 os_requisicoes.quantidade,
 compra.numero_nota,compra.serie_nota,
 compra.data_emissao,
 os_requisicoes.defeito,
 -- preco
   (case  -- rule Price
         when os.status_os = 1 then
          os_requisicoes.preco_final
         when os.cortesia = 'S' then
          os_requisicoes.preco_cortesia
         when vw_os_tipos.interno = 'S' then
              round(((100 + decode(vw_os_tipos.aumenta_tributados, 'S' ,
                                       decode(itens.cod_tributacao, '1',
                                         decode(parm_sys.regime_icms, 'S',
                                           decode(parm_sys2.acessorio_tributa, 'S',
                                             decode(icc.classe_peca,  2, vw_os_tipos.aumento_preco_peca,
                                                                         0),
                                                  0),
                                                vw_os_tipos.aumento_preco_peca),
                                              0),
                                        vw_os_tipos.aumento_preco_peca)) *
                   decode(vw_os_tipos.tipo_preco_peca, 'V', os_requisicoes.preco_venda,
                                                       'G', os_requisicoes.preco_garantia,
                                                       'F', os_requisicoes.custo_fornecedor,
                                                       'P', os_requisicoes.preco_original,
                                                       decode(ote.custo_mais_impostos, 'S', os_requisicoes.preco_venda, os_requisicoes.custo_contabil))
                  ) / 100, nvl( decode(parm_sys2.preco_de_venda_no_custo, 'S', itens_custos.qtde_casas_decimais, itens_fornecedor.qtde_casas_decimais), 2))---
        when vw_os_tipos.garantia = 'S' then
            decode(vw_os_tipos.tipo_preco_peca, 'G', os_requisicoes.preco_garantia, nvl(os_requisicoes.preco_original, os_requisicoes.preco_garantia))
        when nvl(os.fabrica, 'N') = 'S' then
            os_requisicoes.preco_garantia
        when sign(os.franquia) = 1 then
            os_requisicoes.preco_franquia
        else
            round(((100 - nvl(seguradora.desconto_requisicao, 0)) / 100) *
                   case when vw_os_tipos.tipo_preco_peca = 'V' then
                             os_requisicoes.preco_venda
                        when (nvl(os.seguradora, 'N') = 'N') and (vw_os_tipos.os_externa_com_preco_garantia = 'S')  then
                            os_requisicoes.preco_garantia
                        else
                           nvl(os_requisicoes.preco_original,os_requisicoes.preco_venda)
                    end,
                      decode( nvl(seguradora.desconto_requisicao, 0), 0, 6,
                             nvl( decode(parm_sys2.preco_de_venda_no_custo, 'S', itens_custos.qtde_casas_decimais, itens_fornecedor.qtde_casas_decimais), 2)))
         end) as Preco,
     round(os_requisicoes.quantidade *
     ( case  -- rule Price
           when os.status_os = 1 then
            os_requisicoes.preco_final
           when os.cortesia = 'S' then
            os_requisicoes.preco_cortesia
           when vw_os_tipos.interno = 'S' then
                round(((100 + decode(vw_os_tipos.aumenta_tributados, 'S' ,
                                         decode(itens.cod_tributacao, '1',
                                           decode(parm_sys.regime_icms, 'S',
                                             decode(parm_sys2.acessorio_tributa, 'S',
                                               decode(icc.classe_peca,  2, vw_os_tipos.aumento_preco_peca,
                                                                           0),
                                                    0),
                                                  vw_os_tipos.aumento_preco_peca),
                                                0),
                                          vw_os_tipos.aumento_preco_peca)) *
                     decode(vw_os_tipos.tipo_preco_peca, 'V', os_requisicoes.preco_venda,
                                                         'G', os_requisicoes.preco_garantia,
                                                         'F', os_requisicoes.custo_fornecedor,
                                                         'P', os_requisicoes.preco_original,
                                                         decode(ote.custo_mais_impostos, 'S', os_requisicoes.preco_venda, os_requisicoes.custo_contabil))
                    ) / 100, nvl( decode(parm_sys2.preco_de_venda_no_custo, 'S', itens_custos.qtde_casas_decimais, itens_fornecedor.qtde_casas_decimais), 2))
          when vw_os_tipos.garantia = 'S' then
              decode(vw_os_tipos.tipo_preco_peca, 'G', os_requisicoes.preco_garantia, nvl(os_requisicoes.preco_original, os_requisicoes.preco_garantia))
          when nvl(os.fabrica, 'N') = 'S' then
              os_requisicoes.preco_garantia
          when sign(os.franquia) = 1 then
              os_requisicoes.preco_franquia
          else
              round(((100 - nvl(seguradora.desconto_requisicao, 0)) / 100) *
                     case when vw_os_tipos.tipo_preco_peca = 'V' then
                               os_requisicoes.preco_venda
                          when (nvl(os.seguradora, 'N') = 'N') and (vw_os_tipos.os_externa_com_preco_garantia = 'S')  then
                              os_requisicoes.preco_garantia
                          else
                             nvl(os_requisicoes.preco_original,os_requisicoes.preco_venda)
                      end,
                        decode( nvl(seguradora.desconto_requisicao, 0), 0, 6,
                               nvl( decode(parm_sys2.preco_de_venda_no_custo, 'S', itens_custos.qtde_casas_decimais, itens_fornecedor.qtde_casas_decimais), 2 )))
           end) , 2) as Preco_Total
     , lr1.locacao
from os_requisicoes, itens_fornecedor, fornecedor_estoque, itens,
  compra, os, vw_os_tipos, seguradora,
  itens_classe_contabil icc, parm_sys, parm_sys2, parm_sys3, local_requisicao lr1
  ,itens_custos, os_tipos_empresas ote
where os_requisicoes.cod_item       = itens_fornecedor.cod_item
 and os_requisicoes.cod_fornecedor  = itens_fornecedor.cod_fornecedor
 and itens_fornecedor.cod_fornecedor  = fornecedor_estoque.cod_fornecedor
 and itens_fornecedor.cod_item        = itens.cod_item
 and os_requisicoes.cod_empresa_nota  = compra.cod_empresa (+)
 and os_requisicoes.cod_controle_nota = compra.cod_controle (+)
 and os_requisicoes.numero_os = os.numero_os
 and os_requisicoes.cod_empresa = os.cod_empresa
 and os.tipo = vw_os_tipos.tipo
 and os.cod_empresa = vw_os_tipos.cod_empresa
 and os.cod_seguradora = seguradora.cod_seguradora (+)
 and itens_fornecedor.cod_classe_contabil = icc.cod_classe_contabil (+)
 and os_requisicoes.cod_empresa = parm_sys.cod_empresa
 and os_requisicoes.cod_empresa = parm_sys2.cod_empresa
 and os_requisicoes.cod_empresa = parm_sys3.cod_empresa
 and os_requisicoes.cod_empresa = lr1.cod_empresa(+)
 and os_requisicoes.requisicao = lr1.requisicao(+)
 and os_requisicoes.cod_fornecedor = lr1.cod_fornecedor(+)
 and os_requisicoes.cod_item = lr1.cod_item(+)
 and os_requisicoes.cod_empresa = itens_custos.cod_empresa(+)
 and os_requisicoes.cod_item = itens_custos.cod_item(+)
 and os_requisicoes.cod_fornecedor = itens_custos.cod_fornecedor(+)
 and os.cod_empresa     = $P{COD_EMPRESA}
 and os.numero_os       = $P{NUMERO_OS}
 and $P!{SQL_FILTER_REQUISICAO}
 and ote.cod_empresa = os_requisicoes.cod_empresa
 and ote.tipo        = os.tipo),
 
qry_nota as (/* busco as ultimas notas dos fornecedores e itens, caso tenha mais de uma nota, pega a mais recente */
  SELECT data_emissao, data_entrada, numero_nota, serie_nota, cod_fornecedor, cod_item
  FROM (
    SELECT c.data_emissao, c.data_entrada, c.numero_nota, c.serie_nota, ci.cod_fornecedor, ci.cod_item,
           (ROW_NUMBER() OVER (partition by ci.cod_fornecedor, ci.cod_item order by c.data_entrada desc)) AS linha
    FROM compra c, compras_itens ci, qry_requisicao
    WHERE c.cod_empresa = qry_requisicao.cod_empresa
      AND trunc(c.data_entrada) <= qry_requisicao.data
      AND ci.cod_empresa = c.cod_empresa
      AND ci.cod_controle = c.cod_controle
      AND ci.qtde > nvl(ci.qtde_devolvido, 0)
      AND ci.cod_item = qry_requisicao.cod_item
      AND ci.cod_fornecedor = qry_requisicao.cod_fornecedor
      AND c.cod_operacao = 5
      AND EXISTS (SELECT 1 FROM fabrica WHERE fabrica.cod_cliente = c.cod_cliente)
  )
  WHERE linha = 1
)

select  qry_requisicao.requisicao,
 qry_requisicao.cod_item,
 qry_requisicao.cod_fornecedor,
 qry_requisicao.data,
 qry_requisicao.cod_empresa,
 qry_requisicao.part_number,
 qry_requisicao.nome_fornecedor,
 qry_requisicao.descricao_item,
 qry_requisicao.unidade,
 qry_requisicao.quantidade,
 nvl(qry_requisicao.numero_nota,qry_nota.numero_nota) as numero_nota,
 nvl(qry_requisicao.serie_nota,qry_nota.serie_nota) serie_nota,
 nvl(qry_requisicao.numero_nota,qry_nota.numero_nota) || '/' || nvl(qry_requisicao.serie_nota,qry_nota.serie_nota) as nota_serie,
 nvl(qry_requisicao.data_emissao,qry_nota.data_emissao) data_emissao,
 qry_requisicao.defeito,
 qry_requisicao.Preco,
 qry_requisicao.Preco_Total,
 qry_requisicao.locacao
from qry_requisicao, qry_nota
where qry_requisicao.cod_fornecedor = qry_nota.cod_fornecedor(+)
      and qry_requisicao.cod_item = qry_nota.cod_item(+)
$P!{SQL_ORDERBY}]]>
	</queryString>
	<field name="REQUISICAO" class="java.lang.Double"/>
	<field name="COD_ITEM" class="java.lang.String"/>
	<field name="COD_FORNECEDOR" class="java.lang.Double"/>
	<field name="DATA" class="java.sql.Timestamp"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="PART_NUMBER" class="java.lang.String"/>
	<field name="NOME_FORNECEDOR" class="java.lang.String"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="UNIDADE" class="java.lang.String"/>
	<field name="QUANTIDADE" class="java.lang.Double"/>
	<field name="NUMERO_NOTA" class="java.lang.Double"/>
	<field name="SERIE_NOTA" class="java.lang.String"/>
	<field name="NOTA_SERIE" class="java.lang.String"/>
	<field name="DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="DEFEITO" class="java.lang.String"/>
	<field name="PRECO" class="java.lang.Double"/>
	<field name="PRECO_TOTAL" class="java.lang.Double"/>
	<field name="LOCACAO" class="java.lang.String"/>
	<variable name="ITENS" class="java.lang.Double" calculation="Count">
		<variableExpression><![CDATA[$F{COD_ITEM}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="QTDE" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{QUANTIDADE}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="TOTAL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{PRECO_TOTAL}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="TESTE" class="java.lang.String" incrementType="Report">
		<initialValueExpression><![CDATA[$P{ORDENAR_POR_CODITEM_OU_LOCACAO}.equals("LOCACAO")
? "order by qry_requisicao.locacao asc"
: "order by qry_requisicao.cod_item asc"]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="40">
			<frame>
				<reportElement x="0" y="1" width="186" height="39" uuid="40363bc5-fdab-4d4c-9fa4-b972eefdfabf"/>
				<box>
					<bottomPen lineWidth="0.45" lineStyle="Dashed"/>
				</box>
				<staticText>
					<reportElement positionType="Float" x="5" y="10" width="177" height="9" uuid="906cd971-505d-406e-819b-e8278d40ec09">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Descrição do Item]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="63" y="28" width="49" height="9" uuid="e021d3bd-0229-4a13-8c18-7bc17e09bc25">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[NF Data]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="156" y="1" width="22" height="9" uuid="701d6339-2666-43c1-8697-2c3d8d08229a">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[UN]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="164" y="19" width="18" height="9" uuid="5f52ff32-f6ff-4c67-ab33-7c951d820f17">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Qtde]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="113" y="28" width="34" height="9" uuid="07e96de6-a3e6-4177-b610-0d9ea095af42">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Vlr Unit]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="5" y="28" width="57" height="9" uuid="6f8c0b18-265b-4916-973d-49c01675dd1c">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[NF Ent/Ser]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="148" y="28" width="34" height="9" uuid="91b8b454-6517-4324-a103-db634447751f">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Vlr Total]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="5" y="19" width="96" height="9" uuid="3c0a9976-6ab7-4181-ba45-be93aaa12f3c">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Fornecedor]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="102" y="19" width="61" height="9" uuid="248c6f1b-dfc6-4df5-a295-82f6f98914a3">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Locação]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="2" y="1" width="37" height="9" uuid="9ccf7ef4-e2ef-4a03-8324-e08d56a75a3b">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Requisição]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="40" y="1" width="42" height="9" uuid="924f0675-e691-453f-9cce-2843e9a61137">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<textField>
					<reportElement positionType="Float" x="83" y="1" width="72" height="9" uuid="27abc1e5-092e-450c-a196-7baaa7683367">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{MOSTRAR_CODITEM_OU_PARTNUMBER}.equals("CODITEM")
?  "Codigo do Item"
: "Part Number"
]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="40">
			<frame>
				<reportElement x="0" y="1" width="186" height="39" uuid="158d1f8a-e518-4d9a-b7aa-ac72bd034792"/>
				<box>
					<bottomPen lineWidth="0.45" lineStyle="Dashed"/>
				</box>
				<textField>
					<reportElement positionType="Float" x="5" y="10" width="177" height="9" uuid="668a5142-9620-4e6c-ac71-09769f63ac59">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#">
					<reportElement positionType="Float" x="113" y="28" width="34" height="9" uuid="44d1c90d-09ff-4f58-baae-db64cfd39ae2">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRECO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="156" y="1" width="22" height="9" uuid="755b1413-6a5d-4a1f-98f1-70365f463fa2">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{UNIDADE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="83" y="1" width="72" height="9" uuid="c84c9ca0-a297-49b0-946b-600f1ea17094">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{MOSTRAR_CODITEM_OU_PARTNUMBER}.equals("CODITEM")
?  $F{COD_ITEM}
: $F{PART_NUMBER}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="102" y="19" width="61" height="9" uuid="3f911b53-e842-4fb7-9a56-2a333b569041">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LOCACAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="5" y="28" width="57" height="9" uuid="1a0b5c41-6b5c-4e19-89d1-554f8cb353eb">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<printWhenExpression><![CDATA[$F{NUMERO_NOTA} != null && $F{SERIE_NOTA} != null]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOTA_SERIE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="40" y="1" width="42" height="9" uuid="c0d529e3-8b05-45a9-8d14-1911b2cdcd82">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="5" y="19" width="96" height="9" uuid="02fbc145-c051-4790-8347-3c66266289de">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOME_FORNECEDOR}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#">
					<reportElement positionType="Float" x="148" y="28" width="34" height="9" uuid="5017e15b-0c55-4bde-9921-dcde93169060">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{PRECO_TOTAL}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement positionType="Float" x="63" y="28" width="49" height="9" uuid="7b77dd6d-e58d-4c1f-9409-ca7595e178ef">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="2" y="1" width="37" height="9" uuid="49ff56ef-e459-4ace-905c-4e946a69d973">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{REQUISICAO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,###.###;(#,###.###-)">
					<reportElement positionType="Float" x="164" y="19" width="18" height="9" uuid="9ee2a8c6-a41b-4487-a861-f4e662b92985">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<summary>
		<band height="23">
			<frame>
				<reportElement x="0" y="1" width="186" height="22" uuid="2749b2c2-4760-4f89-9669-0cbef10f0e27"/>
				<textField>
					<reportElement positionType="Float" x="29" y="11" width="153" height="9" uuid="42f88b94-5825-48dd-b9e0-ebf3c35d4510">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{RECADO_DESCRICAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="69" y="2" width="23" height="9" uuid="ed86ccfe-7544-4e1e-aab5-42167734a945">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Qtde.:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="2" y="2" width="23" height="9" uuid="47d1b391-033b-4f1e-a1f5-e0e3ab37a2b6">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Itens: ]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="124" y="2" width="23" height="9" uuid="28dc52f4-e886-4cec-bebe-7c675e532905">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement positionType="Float" x="92" y="2" width="13" height="9" uuid="c2ef9197-9570-420d-817f-70aa7eb1a811">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{QTDE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="11" width="27" height="9" uuid="8eab69c6-d6a5-4fca-871e-6d2f4017807b">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<text><![CDATA[Recado:]]></text>
				</staticText>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement positionType="Float" x="25" y="2" width="15" height="9" uuid="710def21-117b-4a39-9066-1bc2d7fa21f9">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{ITENS}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00#;(#,##0.00#-)">
					<reportElement positionType="Float" x="148" y="2" width="34" height="9" uuid="6dabe13d-b60d-4acb-b9bd-7aa105042700">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Monospaced" size="6" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TOTAL}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</summary>
</jasperReport>
