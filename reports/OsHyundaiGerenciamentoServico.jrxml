<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundaiGerenciamentoServico" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="teste_freedom.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\30378\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH Q_OS AS
 (SELECT OS.NUMERO_OS,
         OS.COD_EMPRESA,
         EMP.NOME_COMPLETO AS QUEM_ENTREGOU,
         OS.DATA_ENTREGA,
         MOB_OS_ASSINATURA.ASSINATURA_CLIENTE,
         ASSINATURA_QUALIDADE.ASSINATURA ASSINATUTA_QUALIDADE,
         ASSINATURA_QUALIDADE.DATA_ASSINATURA DATA_ASSINATURA_QUALIDADE,
         DIAGNOSTICO_CONFIRMADO_CLIENTE,
         OS.DATA_PRIMEIRO_CONTATO_ENTREGA,
         OS.DATA_SEGUNDO_CONTATO_ENTREGA,
         OS.TIPO,
         TO_CHAR(SYSDATE, 'DD/MM/YYYY') AS DATA_HOJE,
     PRODUTOS.COD_SEGMENTO 
    FROM OS, EMPRESAS_USUARIOS EMP, MOB_OS_ASSINATURA, MOB_OS_ASSINATURA ASSINATURA_QUALIDADE, OS_DADOS_VEICULOS, PRODUTOS_MODELOS, PRODUTOS
   WHERE EMP.COD_EMPRESA(+) = OS.COD_EMPRESA
     AND EMP.NOME(+) = OS.QUEM_ENTREGOU
     AND OS.NUMERO_OS = MOB_OS_ASSINATURA.NUMERO_OS(+)
     AND OS.COD_EMPRESA = MOB_OS_ASSINATURA.COD_EMPRESA(+)
     AND MOB_OS_ASSINATURA.APLICACAO(+) = 'E'
     
     AND OS.NUMERO_OS = ASSINATURA_QUALIDADE.NUMERO_OS(+)
     AND OS.COD_EMPRESA = ASSINATURA_QUALIDADE.COD_EMPRESA(+)
     AND ASSINATURA_QUALIDADE.APLICACAO(+) = 'Q'
     
   AND OS_DADOS_VEICULOS.NUMERO_OS (+) = OS.NUMERO_OS
   AND OS_DADOS_VEICULOS.COD_EMPRESA (+) = OS.COD_EMPRESA
   AND PRODUTOS_MODELOS.COD_PRODUTO (+) = OS_DADOS_VEICULOS.COD_PRODUTO
   AND PRODUTOS_MODELOS.COD_MODELO (+)  = OS_DADOS_VEICULOS.COD_MODELO
   AND PRODUTOS_MODELOS.COD_PRODUTO = PRODUTOS.COD_PRODUTO (+)
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}),
Q_TECNICO AS
 (SELECT EXE.NUMERO_OS,
         EXE.COD_EMPRESA,
         EXE.COD_TECNICO AS COD_PRODUTIVO,
         TEC.NOME        AS TECNICO
    FROM OS_SERVICOS OSR, OS_TEMPOS_EXECUTADOS EXE, SERVICOS_TECNICOS TEC
   WHERE EXE.COD_EMPRESA(+) = OSR.COD_EMPRESA
     AND EXE.NUMERO_OS(+) = OSR.NUMERO_OS
     AND TEC.COD_TECNICO(+) = EXE.COD_TECNICO
     AND OSR.COD_EMPRESA = $P{COD_EMPRESA}
     AND OSR.NUMERO_OS = $P{NUMERO_OS}
     AND ROWNUM = 1)

SELECT Q_OS.NUMERO_OS,
       Q_OS.COD_EMPRESA,
       Q_OS.QUEM_ENTREGOU,
       Q_OS.DATA_ENTREGA,
       Q_OS.ASSINATURA_CLIENTE,
       Q_OS.ASSINATUTA_QUALIDADE,
       Q_OS.DATA_ASSINATURA_QUALIDADE,
       Q_OS.DIAGNOSTICO_CONFIRMADO_CLIENTE,
       Q_OS.DATA_PRIMEIRO_CONTATO_ENTREGA,
       Q_OS.DATA_SEGUNDO_CONTATO_ENTREGA,
       Q_OS.TIPO,
       Q_OS.DATA_HOJE,
       Q_OS.COD_SEGMENTO,
     Q_TECNICO.COD_PRODUTIVO,
       Q_TECNICO.TECNICO
FROM Q_TECNICO, Q_OS
WHERE Q_OS.NUMERO_OS = Q_TECNICO.NUMERO_OS(+)
AND Q_OS.COD_EMPRESA = Q_TECNICO.COD_EMPRESA(+)]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="QUEM_ENTREGOU" class="java.lang.String"/>
	<field name="DATA_ENTREGA" class="java.sql.Timestamp"/>
	<field name="ASSINATURA_CLIENTE" class="java.awt.Image"/>
	<field name="ASSINATUTA_QUALIDADE" class="java.awt.Image"/>
	<field name="DATA_ASSINATURA_QUALIDADE" class="java.sql.Timestamp"/>
	<field name="DIAGNOSTICO_CONFIRMADO_CLIENTE" class="java.lang.String"/>
	<field name="DATA_PRIMEIRO_CONTATO_ENTREGA" class="java.sql.Timestamp"/>
	<field name="DATA_SEGUNDO_CONTATO_ENTREGA" class="java.sql.Timestamp"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="DATA_HOJE" class="java.lang.String"/>
	<field name="COD_SEGMENTO" class="java.lang.Double"/>
	<field name="COD_PRODUTIVO" class="java.lang.Double"/>
	<field name="TECNICO" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="131">
			<frame>
				<reportElement x="0" y="0" width="555" height="131" uuid="07f15641-dac0-4fd9-a95d-daf782cc022e"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image scaleImage="FillFrame">
					<reportElement x="1" y="5" width="553" height="101" uuid="c029682d-641c-44b2-b0b2-152b60ea5282"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice15401.PNG"]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="3" y="110" width="551" height="17" forecolor="#08038F" uuid="047f9358-38f8-4fce-b1d0-06ae740091e6"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[Número da Ordem de Serviço:]]></text>
				</staticText>
				<textField pattern="#.###;(#.###-)">
					<reportElement mode="Transparent" x="193" y="110" width="176" height="16" forecolor="#08038F" uuid="ed38d823-b079-4b45-9a34-1d94ff77625c"/>
					<textElement textAlignment="Left">
						<font size="12" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NUMERO_OS}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="638">
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement x="0" y="0" width="555" height="123" uuid="e965c06f-7562-4aa1-b9ee-cf4219e2c52a">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="CONFIRMADO_CLIENTE">
					<subreportParameterExpression><![CDATA[$F{DIAGNOSTICO_CONFIRMADO_CLIENTE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiGerenciamentoServicoSubReclamacoes.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement x="0" y="434" width="555" height="204" isPrintWhenDetailOverflows="true" uuid="09ec0e73-3290-46c4-90aa-f9751d026be5">
					<property name="ShowOutOfBoundContent" value="false"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="4" y="3" width="550" height="17" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="360f821c-038f-4875-9133-312b5ada51ae"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Responsável pelo Controle de Qualidade:                                                                 Data:]]></text>
				</staticText>
				<rectangle>
					<reportElement mode="Opaque" x="0" y="21" width="555" height="18" isPrintWhenDetailOverflows="true" backcolor="#08038F" uuid="73a7f461-12a5-41a1-ba6a-f6c43d289958"/>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<staticText>
					<reportElement mode="Transparent" x="5" y="23" width="331" height="14" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" uuid="5bae02ff-791b-4e2f-bc61-74a69f05192c"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Entrega]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="25" y="117" width="112" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="0c320e54-c04e-4dd3-bf6f-f4b17923525b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[1º Contato com o cliente:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="25" y="132" width="112" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="5ded7023-109b-46aa-b938-572115a9c872"/>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[2º Contato com o cliente:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="25" y="147" width="112" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="fa503e88-15ee-4aa6-b478-a13a0d078fb9"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Consultor de Serviço:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="8" y="182" width="109" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="365e4487-b28b-448b-ad53-3780b4e2e285"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Assinatura do cliente:]]></text>
				</staticText>
				<image>
					<reportElement x="467" y="148" width="71" height="49" isPrintWhenDetailOverflows="true" uuid="9c5e9a09-ab0e-47aa-af9c-663c22603b67"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice15402.PNG"]]></imageExpression>
				</image>
				<textField>
					<reportElement mode="Transparent" x="368" y="147" width="89" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="aa4a376e-93f0-4c1e-b4a0-412565881be4"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_HOJE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="137" y="147" width="184" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="f50f30d7-a4c0-4d85-b9c6-e4030cadb337"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QUEM_ENTREGOU}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="341" y="147" width="27" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="e805d25d-55eb-4bcd-aced-53004565c847"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="327" y="49" width="21" height="81" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="0ef81a6d-25aa-4d4c-8477-37dc71855b50">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Center" rotation="Left">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[Entrega]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="3" y="49" width="21" height="81" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="0a67acb3-7887-4d25-b526-115e27549fcf"/>
					<textElement textAlignment="Center" rotation="Left">
						<font size="13" isBold="true"/>
					</textElement>
					<text><![CDATA[Pré-entrega]]></text>
				</staticText>
				<subreport isUsingCache="true" runToBottom="true" overflowType="NoStretch">
					<reportElement x="25" y="40" width="299" height="75" uuid="a7fceed3-cc0a-407b-8e10-a6297076c877">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_SEGMENTO">
						<subreportParameterExpression><![CDATA[$F{COD_SEGMENTO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="TIPO_OS">
						<subreportParameterExpression><![CDATA[$F{TIPO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DIR_IMAGE_LOGO">
						<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiGerenciamentoServicoSubPreEntrega.jasper"]]></subreportExpression>
				</subreport>
				<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
					<reportElement x="350" y="41" width="201" height="90" uuid="f7f42f79-8d46-45b5-a9cf-166ce1bfa184">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<subreportParameter name="COD_EMPRESA">
						<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="NUMERO_OS">
						<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="COD_SEGMENTO">
						<subreportParameterExpression><![CDATA[$F{COD_SEGMENTO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="TIPO_OS">
						<subreportParameterExpression><![CDATA[$F{TIPO}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="DIR_IMAGE_LOGO">
						<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiGerenciamentoServicoSubEntrega.jasper"]]></subreportExpression>
				</subreport>
				<image>
					<reportElement x="117" y="169" width="131" height="31" uuid="02bcd3e7-4e80-4822-b25d-350fff72cb6a"/>
					<box>
						<pen lineColor="#08038F"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{ASSINATURA_CLIENTE}]]></imageExpression>
				</image>
				<line>
					<reportElement x="118" y="194" width="130" height="1" forecolor="#03088F" uuid="2672ab6a-14aa-460a-8f6c-1c1e26a42a70">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
				</line>
				<image>
					<reportElement x="206" y="2" width="106" height="18" uuid="610a7820-4f08-43f2-89f2-9e41562cf6e3"/>
					<box>
						<pen lineColor="#08038F"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<imageExpression><![CDATA[$F{ASSINATUTA_QUALIDADE}]]></imageExpression>
				</image>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="409" y="4" width="127" height="16" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="1a066fc3-dd58-41ed-b503-53cc24fb0256">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_ASSINATURA_QUALIDADE}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="137" y="117" width="53" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="ae7c7ab6-14dc-4543-9daa-112525528b3c"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_PRIMEIRO_CONTATO_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="194" y="117" width="67" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="e4210160-9df0-4827-b5a4-805187b3bd71"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_PRIMEIRO_CONTATO_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField pattern="HH:mm">
					<reportElement mode="Transparent" x="194" y="132" width="67" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="0a1f7d6c-33b0-4882-8aff-3a472176e23f"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_SEGUNDO_CONTATO_ENTREGA}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement mode="Transparent" x="137" y="132" width="53" height="15" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="f8271419-d535-4cc8-b8bf-e3ddd51978f3"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_SEGUNDO_CONTATO_ENTREGA}]]></textFieldExpression>
				</textField>
			</frame>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement x="0" y="123" width="555" height="123" uuid="3f6cd954-bb25-4cfa-be56-1743577855bd">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiGerenciamentoServicoSubDiagnosticoResposta.jasper"]]></subreportExpression>
			</subreport>
			<subreport isUsingCache="false" runToBottom="false" overflowType="NoStretch">
				<reportElement x="0" y="246" width="555" height="130" uuid="5244e2b2-2a69-4745-9462-2a2a1b527eb7">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiGerenciamentoServicoSubServicoResposta.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement x="0" y="376" width="555" height="24" uuid="01a1311a-4eb1-4339-8801-a7a176d3f819">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="4" y="4" width="106" height="17" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="2501d597-6036-45ee-8d50-6a79e116c1c8"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Técnico Responsável:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="368" y="4" width="28" height="17" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="2315c845-d9be-4649-bc87-bf007535a483"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="110" y="4" width="256" height="17" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="57fe738f-2363-4c1d-9e4c-786722d2b09b"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TECNICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="396" y="4" width="106" height="17" isPrintWhenDetailOverflows="true" forecolor="#08038F" uuid="bc69faec-83bf-4e41-bf23-64fc975e4ce3"/>
					<textElement textAlignment="Left">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DATA_HOJE}]]></textFieldExpression>
				</textField>
			</frame>
			<subreport isUsingCache="true" runToBottom="true" overflowType="NoStretch">
				<reportElement x="0" y="400" width="555" height="34" uuid="77470872-f267-471c-82bc-af0f2ce79ba2">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="OBRIGATORIO">
					<subreportParameterExpression><![CDATA["N"]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiGerenciamentoServicoSubQualidade.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
