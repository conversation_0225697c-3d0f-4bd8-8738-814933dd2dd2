<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsJaguarLandSubReclamacao" pageWidth="555" pageHeight="34" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="OBRIGATORIO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["N"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[with DADOS as (
select info.cod_empresa as COD_EMPRESA,
       info.cod_produto as COD_PRODUTO,
       info.cod_modelo as COD_MODELO,
       info.tipo_os as TIPO_OS,
       info.cod_segmento as COD_SEGMENTO
  from (select os.cod_empresa,dv.Cod_Produto, dv.cod_modelo, os.tipo as tipo_os, produtos.cod_segmento
          from os, os_dados_veiculos dv, produtos
         where os.numero_os = dv.numero_os
           and os.cod_empresa = dv.cod_empresa
           and os.cod_empresa = $P{COD_EMPRESA}
           and os.numero_os = $P{NUMERO_OS}
           and produtos.cod_produto = dv.cod_produto) info
),
TODOS_ITENS as ( 
SELECT A.ID_GRUPO, /* CAMADA 1 - FILTRO TODOS OS ITENS QUE SEJA DA APLICAÇÃO ESPECIFICA */
       A.DESCRICAO AS DESCRICAO_ITEM,       
       A.ATIVO,
       A.COD_ITEM,
       A.OBRIGATORIO,
       A.ORDEM,
       A.PODE_TER_FOTO,  
       B.DESCRICAO DESCRICAO_GRUPO,
       B.TIPO,       
       B.APLICACAO,
       DECODE(NVL((SELECT PO.SEMAFORO
                            FROM MOB_PERTENCE_OPCAO PO
                           WHERE PO.COD_ITEM = A.COD_ITEM
                             AND PO.ID_OPCAO = C.ID_OPCAO
                             AND ROWNUM = 1),
                          'R'),
                      'G',
                      'GREEN',
                      'R',
                      'RED',
                      'Y',
                      'YELLOW',
                      'RED') AS COR_OPCAO_SELECIONADA, /* se for GREEM então marco como sim no relatorio */
       B.ORDEM AS ORDEM_GRUPO 
FROM MOB_PERTENCE_ITEM A, MOB_PERTENCE_GRUPO B, MOB_OS_PERTENCE C, MOB_OPCAO D
WHERE A.ID_GRUPO = B.ID_GRUPO
 AND ((($P{OBRIGATORIO} = 'S') AND (A.OBRIGATORIO = $P{OBRIGATORIO})) OR ($P{OBRIGATORIO} = 'N'))
 AND B.TIPO = 'G'
 AND A.ATIVO = 'S'
 AND B.ATIVO = 'S'
 AND A.COD_ITEM = C.COD_ITEM(+)
 AND B.APLICACAO = 'Q'
 AND C.COD_EMPRESA(+) = $P{COD_EMPRESA}
 AND C.NUMERO_OS(+) = $P{NUMERO_OS}
 AND C.ID_OPCAO = D.ID_OPCAO(+)
 ),
FILTRO_CRUZA_EPRESA_SEGMENTO AS ( 
SELECT * /* CAMADA 2 - FILTRO TODOS OS ITENS QUE SEJA DA RESPECTIVA EMPRESA OU NÃO POSSUA EMPRESA */
FROM TODOS_ITENS A
WHERE EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo
                     AND mc.cod_empresa = $P{COD_EMPRESA}
                     AND mc.cod_segmento = (SELECT COD_SEGMENTO FROM DADOS)) 
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_empresa mc
              WHERE  mc.id_grupo = A.id_grupo)                               
),
FILTRO_CRUZA_TIPO_OS AS ( 
SELECT * /* CAMADA 3 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO TIPO DE OS OU NÃO POSSUA TIPO DE OS VINCULADO */
FROM FILTRO_CRUZA_EPRESA_SEGMENTO A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_tp_os cto
                  WHERE  cto.id_grupo = a.id_grupo
                         AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                         AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS)
                         AND cto.tipo = (SELECT TIPO_OS FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_tp_os cto
              WHERE  cto.id_grupo = A.id_grupo
                     AND CTO.COD_EMPRESA = $P{COD_EMPRESA}
                     AND CTO.COD_SEGMENTO = (SELECT COD_SEGMENTO FROM DADOS))
),
FILTRO_CRUZA_MODELO AS ( 
SELECT * /* CAMADA 4 - FILTRO TODOS OS ITENS QUE SEJA DO RESPECTIVO MODELO OU NÃO POSSUA MODELO VINCULADO */
FROM FILTRO_CRUZA_TIPO_OS A
WHERE EXISTS(SELECT 1
                  FROM   mob_cruza_modelo cm
                  WHERE  cm.id_grupo = a.id_grupo
                         AND cm.cod_produto = (SELECT COD_PRODUTO FROM DADOS)
                         AND cm.cod_modelo = (SELECT COD_MODELO FROM DADOS))
      OR NOT EXISTS(SELECT 1
              FROM   mob_cruza_modelo cm
              WHERE   cm.id_grupo = a.id_grupo)
),

CONSULTA AS (
  select ID_GRUPO, DESCRICAO_ITEM, ATIVO, COD_ITEM, OBRIGATORIO, ORDEM, PODE_TER_FOTO,       
       DESCRICAO_GRUPO, TIPO, APLICACAO, COR_OPCAO_SELECIONADA  from FILTRO_CRUZA_MODELO
  ORDER BY ORDEM_GRUPO, ID_GRUPO, ORDEM
),

PARAMETROS AS (
           SELECT /*apartir dessa subquery eu trabalho para sempre retornar multiplo de 5 linhas para imprimir de forma correta no relatorio*/
              1 MULTIPLO, /*Determina o multiplo da quantidade de linhas*/
              1 MAXIMO_LINHAS /* determina o maximo de linhas, zero é ilimitado, o numero de linhas que a consulta retornar*/
              FROM DUAL
),
CONSULTA_LIMITADA as (
     SELECT CONSULTA.* FROM CONSULTA,parametros where (rownum <= parametros.maximo_linhas or parametros.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL as (
    SELECT * /* essa subquery eu trabalho para sempre retornar multiplo de 5 linhas para imprimir de forma correta no relatorio */
    FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL
    FROM dual,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),parametros.multiplo) <> 0 or (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY level <= parametros.multiplo - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), parametros.multiplo)
)
SELECT * /*[PODE ESPECIFICAR OS CAMPOS]*/ FROM CONSULTA_FINAL]]>
	</queryString>
	<field name="ID_GRUPO" class="java.lang.Double"/>
	<field name="DESCRICAO_ITEM" class="java.lang.String"/>
	<field name="ATIVO" class="java.lang.String"/>
	<field name="COD_ITEM" class="java.lang.Double"/>
	<field name="OBRIGATORIO" class="java.lang.String"/>
	<field name="ORDEM" class="java.lang.Double"/>
	<field name="PODE_TER_FOTO" class="java.lang.String"/>
	<field name="DESCRICAO_GRUPO" class="java.lang.String"/>
	<field name="TIPO" class="java.lang.String"/>
	<field name="APLICACAO" class="java.lang.String"/>
	<field name="COR_OPCAO_SELECIONADA" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="18">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="18" isPrintWhenDetailOverflows="true" backcolor="#08038F" uuid="7c11f51b-f896-4f5d-b37c-38c60524f35f"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement mode="Transparent" x="3" y="2" width="331" height="14" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" uuid="f80622b1-b6ca-4aaa-aa09-e838d82ee01c"/>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Controle de Qualidade]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="16" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="16" uuid="4322e92e-9965-400f-8218-5c9254755c62">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<leftPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textField>
					<reportElement mode="Transparent" x="3" y="0" width="426" height="16" forecolor="#08038F" uuid="8fb776c7-6ae3-40c9-9f1d-0068e0f04b93">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_ITEM}]]></textFieldExpression>
				</textField>
				<image scaleImage="RealHeight">
					<reportElement x="463" y="0" width="15" height="16" uuid="4f2a0a5c-7db3-4716-a1fe-71355021e8b8">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[new Boolean($F{COR_OPCAO_SELECIONADA}.equals("GREEN"))]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice15403.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="440" y="0" width="23" height="16" forecolor="#08038F" uuid="9e692eae-4d36-442d-9363-05c62d2a55e3">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Sim]]></text>
				</staticText>
				<staticText>
					<reportElement x="501" y="0" width="23" height="16" forecolor="#08038F" uuid="85d96c05-0bbf-4450-a978-f010cba81636">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Não]]></text>
				</staticText>
				<image scaleImage="RealHeight">
					<reportElement x="524" y="0" width="15" height="16" uuid="217ce6ef-d8ea-4328-96e6-b915a4310281">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<printWhenExpression><![CDATA[new Boolean(!($F{COR_OPCAO_SELECIONADA}.equals("GREEN")))]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice15403.png"]]></imageExpression>
				</image>
			</frame>
		</band>
	</detail>
</jasperReport>
