<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklistImagemItem" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="e6136d16-684f-4d67-8d0b-7e50fb5a1b63">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="DataAdapter.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false"/>
	<queryString>
		<![CDATA[SELECT 'Checklist' AS GRUPO,
       GRUPO.DESCRICAO AS DESC_GRUPO,
       ITEM.DESCRICAO AS DESC_ITEM,
       'Situação da(o)(s) ' || ITEM.DESCRICAO AS DESC_ITEM_SIT,
       OP.DESCRICAO DESC_OPCAO,
       COALESCE(A.OBSERVACAO, ' ') AS OBSERVACAO,
       GRUPO.ORDEM AS ORDEM_GRUPO,
       ITEM.ORDEM AS ORDEM_ITEM, 
       A.NUMERO_OS,
       A.COD_EMPRESA, 
       A.COD_ITEM,
       IP.ICONE
  FROM MOB_OS_PERTENCE    A,
       MOB_PERTENCE_GRUPO GRUPO,
       MOB_PERTENCE_ITEM  ITEM,
       MOB_PERTENCE_OPCAO POPX,
       MOB_OPCAO          OP, 
       ICONE_PADRAO       IP
WHERE A.COD_ITEM = ITEM.COD_ITEM
 AND ITEM.ID_GRUPO = GRUPO.ID_GRUPO
 AND A.ID_OPCAO = OP.ID_OPCAO
 AND ITEM.COD_ITEM = POPX.COD_ITEM
 AND A.ID_OPCAO = POPX.ID_OPCAO
 AND OP.ID_ICONE = IP.ID_ICONE
 AND GRUPO.TIPO = 'C'
 AND A.ID_IMAGEM IS NOT NULL
 AND A.NUMERO_OS = $P{NUMERO_OS}
 AND A.COD_EMPRESA = $P{COD_EMPRESA}
ORDER BY 1, 6, 7]]>
	</queryString>
	<field name="GRUPO" class="java.lang.String"/>
	<field name="DESC_GRUPO" class="java.lang.String"/>
	<field name="DESC_ITEM" class="java.lang.String"/>
	<field name="DESC_ITEM_SIT" class="java.lang.String"/>
	<field name="DESC_OPCAO" class="java.lang.String"/>
	<field name="OBSERVACAO" class="java.lang.String"/>
	<field name="ORDEM_GRUPO" class="java.math.BigDecimal"/>
	<field name="ORDEM_ITEM" class="java.math.BigDecimal"/>
	<field name="NUMERO_OS" class="java.math.BigDecimal"/>
	<field name="COD_EMPRESA" class="java.math.BigDecimal"/>
	<field name="COD_ITEM" class="java.math.BigDecimal"/>
	<field name="ICONE" class="java.io.InputStream"/>
	<detail>
		<band height="46" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="595" height="20" uuid="06e2e757-f6ec-4008-855c-a24092c22775"/>
				<textElement>
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESC_ITEM}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="0" y="20" width="24" height="24" uuid="fb42d031-94e3-4a30-b79f-764a6916eb7b">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<imageExpression><![CDATA[$F{ICONE}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="30" y="20" width="270" height="24" uuid="1dde8230-9347-4fc4-8db5-3a02316cb036"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{DESC_ITEM_SIT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="310" y="20" width="280" height="24" uuid="e574fc79-12ea-4af5-95c8-5d09572d0b65"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{OBSERVACAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
