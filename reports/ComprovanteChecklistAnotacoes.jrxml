<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteChecklistAnotacoes" pageWidth="555" pageHeight="842" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="193ef2f2-dd94-4b85-9068-b78e9bb5a77c">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="teste_freedom"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\projects\\negocio_delphi\\reports\\crmservice\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT O.DESCRICAO
FROM OS_ORIGINAL O
WHERE O.NUMERO_OS = $P{NUMERO_OS} 
 AND O.COD_EMPRESA = $P{COD_EMPRESA}]]>
	</queryString>
	<field name="DESCRICAO" class="java.lang.String"/>
	<columnHeader>
		<band height="22" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="100" height="18" uuid="0f54385b-7f53-4a57-b294-c3b027694fa6">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Anotações]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement positionType="Float" x="0" y="0" width="555" height="18" uuid="0bd5578a-2324-4ca6-a382-3fe6a56eb2c3">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
