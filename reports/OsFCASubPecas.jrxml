<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsFCASubPecas" pageWidth="555" pageHeight="842" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="campoNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT ROWNUM AS ITEM
      ,DECODE(FORNECEDOR_ESTOQUE.OFICIAL,
              'N',
              '*' || ITENS.DESCRICAO,
              ITENS.DESCRICAO) AS DESCRICAO
      ,OS_REQUISICOES.QUANTIDADE
      ,OS_REQUISICOES.COD_ITEM
      ,DECODE(OS.STATUS_OS,
              1,
              OS_REQUISICOES.PRECO_FINAL,
              DECODE(OS.CORTESIA,
                     'S',
                     OS_REQUISICOES.PRECO_CORTESIA,
                     DECODE(OS_TIPOS.INTERNO,
                            'S',
                            ROUND((100 +
                                  DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS,
                                          'S',
                                          DECODE(ITENS.COD_TRIBUTACAO,
                                                 '1',
                                                 DECODE(PARM_SYS.REGIME_ICMS,
                                                        'S',
                                                        DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                               'S',
                                                               DECODE(ICC.CLASSE_PECA,
                                                                      2,
                                                                      OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                           0),
                                                               0),
                                                        OS_TIPOS.AUMENTO_PRECO_PECA),
                                                0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                                  DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                         'V',
                                         OS_REQUISICOES.PRECO_VENDA,
                                         'G',
                                         OS_REQUISICOES.PRECO_GARANTIA,
                                         'F',
                                         OS_REQUISICOES.CUSTO_FORNECEDOR,
                                         'P',
                                         OS_REQUISICOES.PRECO_ORIGINAL,
                                         DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL)),4) / 100,
                            DECODE(OS_TIPOS.GARANTIA,
                                   'S',
                                   DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                          'P',
                                          NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                              OS_REQUISICOES.PRECO_GARANTIA),
                                          OS_REQUISICOES.PRECO_GARANTIA),
                                   DECODE(NVL(OS.FABRICA, 'N'),
                                          'S',
                                          OS_REQUISICOES.PRECO_GARANTIA,
                                          DECODE(SIGN(OS.FRANQUIA),
                                                 1,
                                                 PRECO_FRANQUIA,
                                                 ROUND((100 - NVL(SEGURADORA.DESCONTO_REQUISICAO,
                                                                  0)) *
                                                       DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                                              'P',
                                                              NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                                                  OS_REQUISICOES.PRECO_VENDA),
                                                              DECODE(NVL(OS.SEGURADORA,
                                                                         'N'),
                                                                     'N',
                                                                     DECODE(OS_TIPOS.OS_EXTERNA_COM_PRECO_GARANTIA,
                                                                            'S',
                                                                            OS_REQUISICOES.PRECO_GARANTIA,
                                                                            OS_REQUISICOES.PRECO_VENDA),
                                                                     OS_REQUISICOES.PRECO_VENDA)),
                                                       4) / 100)))))) AS PRECO_VENDA
      ,OS_REQUISICOES.QUANTIDADE *
       DECODE(OS.STATUS_OS,
              1,
              OS_REQUISICOES.PRECO_FINAL,
              DECODE(OS.CORTESIA,
                     'S',
                     OS_REQUISICOES.PRECO_CORTESIA,
                     DECODE(OS_TIPOS.INTERNO,
                            'S',
                            ROUND((100 +
                                  DECODE(OS_TIPOS.AUMENTA_TRIBUTADOS,
                                          'S',
                                          DECODE(ITENS.COD_TRIBUTACAO,
                                                 '1',
                                                 DECODE(PARM_SYS.REGIME_ICMS,
                                                        'S',
                                                        DECODE(PARM_SYS2.ACESSORIO_TRIBUTA,
                                                               'S',
                                                               DECODE(ICC.CLASSE_PECA,
                                                                      2,
                                                                      OS_TIPOS.AUMENTO_PRECO_PECA,
                                                                           0),
                                                               0),
                                                        OS_TIPOS.AUMENTO_PRECO_PECA),
                                                0),
                                          OS_TIPOS.AUMENTO_PRECO_PECA)) *
                                  DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                         'V',
                                         OS_REQUISICOES.PRECO_VENDA,
                                         'G',
                                         OS_REQUISICOES.PRECO_GARANTIA,
                                         'F',
                                         OS_REQUISICOES.CUSTO_FORNECEDOR,
                                         'P',
                                         OS_REQUISICOES.PRECO_ORIGINAL,
                                         DECODE(OTE.CUSTO_MAIS_IMPOSTOS, 'S', OS_REQUISICOES.PRECO_VENDA, OS_REQUISICOES.CUSTO_CONTABIL)),
                                  4) / 100,
                            DECODE(OS_TIPOS.GARANTIA,
                                   'S',
                                   OS_REQUISICOES.PRECO_GARANTIA,
                                   DECODE(NVL(OS.FABRICA, 'N'),
                                          'S',
                                          OS_REQUISICOES.PRECO_GARANTIA,
                                          DECODE(SIGN(OS.FRANQUIA),
                                                 1,
                                                 PRECO_FRANQUIA,
                                                 ROUND((100 - NVL(SEGURADORA.DESCONTO_REQUISICAO,
                                                                  0)) *
                                                       DECODE(OS_TIPOS.TIPO_PRECO_PECA,
                                                              'V',
                                                              OS_REQUISICOES.PRECO_VENDA,
                                                              DECODE(NVL(OS.SEGURADORA,
                                                                         'N'),
                                                                     'N',
                                                                     DECODE(OS_TIPOS.OS_EXTERNA_COM_PRECO_GARANTIA,
                                                                            'S',
                                                                            OS_REQUISICOES.PRECO_GARANTIA,
                                                                            NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                                                                OS_REQUISICOES.PRECO_VENDA)),
                                                                     NVL(OS_REQUISICOES.PRECO_ORIGINAL,
                                                                         OS_REQUISICOES.PRECO_VENDA))),
                                                       4) / 100)))))) AS PRECO_TOTAL
      ,OTE.TIPO_FABRICA AS TIPO_FABRICA_EMPRESA
      ,VOR.PRECO_LIQUIDO
      ,OS_REQUISICOES.COD_FORNECEDOR
      ,OS_REQUISICOES.TIPO
      ,(SELECT ST.NOME 
          FROM OS_TEMPOS_EXECUTADOS OTE
          INNER JOIN SERVICOS_TECNICOS  ST 
            ON (ST.COD_TECNICO = OTE.COD_TECNICO
            AND ST.COD_EMPRESA = OTE.COD_EMPRESA) 
          WHERE OTE.COD_EMPRESA = OSS.COD_EMPRESA
            AND OTE.NUMERO_OS   = OSS.NUMERO_OS
            AND OTE.COD_SERVICO = OSS.COD_SERVICO
            AND ROWNUM <= 1)  
       AS PRODUTIVO_PRISMA
      ,NVL(OT.GARANTIA,'N') AS GARANTIA 
      ,OS.TIPO AS TIPO_OS
  FROM OS_REQUISICOES
      ,ESTOQUE
      ,ITENS
      ,OS
      ,VW_OS_TIPOS           OS_TIPOS
      ,FORNECEDOR_ESTOQUE
      ,SEGURADORA
      ,ITENS_FORNECEDOR
      ,ITENS_CLASSE_CONTABIL ICC
      ,PARM_SYS
      ,PARM_SYS2
      ,OS_TIPOS_EMPRESAS     OTE
      ,OS_TIPOS             OT
      , VW_OS_REQUISITADOS  VOR
      ,OS_SERVICOS        OSS
WHERE 1=1 
   AND OS_REQUISICOES.COD_EMPRESA    = VOR.COD_EMPRESA 
   AND OS_REQUISICOES.REQUISICAO     = VOR.REQUISICAO  
   AND OS_REQUISICOES.COD_FORNECEDOR = VOR.COD_FORNECEDOR 
   AND OS_REQUISICOES.COD_ITEM       = VOR.COD_ITEM
   AND (OS_REQUISICOES.COD_ITEM = ITENS.COD_ITEM)
   AND OS_REQUISICOES.COD_FORNECEDOR = FORNECEDOR_ESTOQUE.COD_FORNECEDOR
  AND (OS_REQUISICOES.COD_ITEM = ESTOQUE.COD_ITEM (+))
   AND (OS_REQUISICOES.COD_FORNECEDOR = ESTOQUE.COD_FORNECEDOR (+))
  AND (OS_REQUISICOES.COD_EMPRESA = ESTOQUE.COD_EMPRESA (+))
  AND (OS_REQUISICOES.NUMERO_OS = OS.NUMERO_OS)
  AND (OS_REQUISICOES.COD_EMPRESA = OS.COD_EMPRESA)
  AND (OS.TIPO = OS_TIPOS.TIPO)
  AND (OS.COD_EMPRESA = OS_TIPOS.COD_EMPRESA)
   AND (OS.TIPO = OTE.TIPO)
   AND OT.TIPO         (+) = OTE.TIPO
   AND (OS.COD_EMPRESA = OTE.COD_EMPRESA)
  AND OS.COD_SEGURADORA = SEGURADORA.COD_SEGURADORA (+)
  AND OS_REQUISICOES.COD_ITEM = ITENS_FORNECEDOR.COD_ITEM
   AND OS_REQUISICOES.COD_FORNECEDOR = ITENS_FORNECEDOR.COD_FORNECEDOR
   AND ITENS_FORNECEDOR.COD_CLASSE_CONTABIL = ICC.COD_CLASSE_CONTABIL(+)
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS.COD_EMPRESA
  AND OS_REQUISICOES.COD_EMPRESA = PARM_SYS2.COD_EMPRESA
  AND OTE.COD_EMPRESA = OS_REQUISICOES.COD_EMPRESA
  AND OTE.TIPO        = OS.TIPO
  
   AND (OSS.COD_EMPRESA (+) = OS_REQUISICOES.COD_EMPRESA
   AND OSS.NUMERO_OS    (+) = OS_REQUISICOES.NUMERO_OS
   AND OSS.ITEM         (+) = OS_REQUISICOES.ITEM
   AND OSS.COD_SERVICO  (+) = OS_REQUISICOES.COD_SERVICO)
   AND OS.NUMERO_OS = $P{NUMERO_OS}
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}]]>
	</queryString>
	<field name="ITEM" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="ITEM"/>
	</field>
	<field name="DESCRICAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="DESCRICAO"/>
		<property name="com.jaspersoft.studio.field.label" value="DESCRICAO"/>
	</field>
	<field name="QUANTIDADE" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="QUANTIDADE"/>
		<property name="com.jaspersoft.studio.field.label" value="QUANTIDADE"/>
	</field>
	<field name="COD_ITEM" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_ITEM"/>
	</field>
	<field name="PRECO_VENDA" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="PRECO_VENDA"/>
		<property name="com.jaspersoft.studio.field.label" value="PRECO_VENDA"/>
	</field>
	<field name="PRECO_TOTAL" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="PRECO_TOTAL"/>
		<property name="com.jaspersoft.studio.field.label" value="PRECO_TOTAL"/>
	</field>
	<field name="TIPO_FABRICA_EMPRESA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="TIPO_FABRICA_EMPRESA"/>
		<property name="com.jaspersoft.studio.field.label" value="TIPO_FABRICA_EMPRESA"/>
	</field>
	<field name="PRECO_LIQUIDO" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="PRECO_LIQUIDO"/>
		<property name="com.jaspersoft.studio.field.label" value="PRECO_LIQUIDO"/>
	</field>
	<field name="COD_FORNECEDOR" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="COD_FORNECEDOR"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_FORNECEDOR"/>
	</field>
	<field name="TIPO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="TIPO"/>
		<property name="com.jaspersoft.studio.field.label" value="TIPO"/>
	</field>
	<field name="PRODUTIVO_PRISMA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="PRODUTIVO_PRISMA"/>
		<property name="com.jaspersoft.studio.field.label" value="PRODUTIVO_PRISMA"/>
	</field>
	<field name="GARANTIA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="GARANTIA"/>
		<property name="com.jaspersoft.studio.field.label" value="GARANTIA"/>
	</field>
	<field name="TIPO_OS" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="TIPO_OS"/>
		<property name="com.jaspersoft.studio.field.label" value="TIPO_OS"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="11">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b9e3f3a7-19cd-45e8-8ed6-c1a67586ea41">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[PEÇAS E LUBRIFICANTES]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="11">
			<staticText>
				<reportElement mode="Opaque" x="40" y="0" width="50" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="73ed2c61-dc67-4714-a8b8-a82e6af9e585">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo de OS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="90" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="effeae3b-1314-4d08-8cbb-1e5c9315f81c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Produtivo]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="230" y="0" width="140" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="deba69b2-afd3-469a-b2f5-8993ff35bf54">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Descrição da Peças e Lubrificantes]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="d64b78a2-04b6-4573-86a6-8f71eec424e3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Item]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="147" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="94f31bf4-ce70-427c-ac01-38589ab2bc9f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Código do Produto]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="472" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="5e43e9f7-c5d7-4d99-9431-9fe61b1800a3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="370" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="c57e66bf-d141-4416-8893-64c1130f0ff2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="410" y="0" width="62" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="f296a42c-0818-44c3-b996-522138e76714">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Unitario]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="230" y="0" width="140" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1e6da218-bcea-4afc-9ceb-819528eb6b97">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###;#,##0.###-">
				<reportElement mode="Opaque" x="0" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="751d6850-6609-42fb-86cd-094b1b6ed143">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="40" y="0" width="50" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="dea6bb9a-743a-4f23-83e4-c5a7e4d02cd1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIPO_OS}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="90" y="0" width="57" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1c5947c7-a166-49dc-a232-9dc87e561229">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRODUTIVO_PRISMA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="147" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="680366eb-6eb5-4d9a-9a1b-3de96721461d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_ITEM}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Opaque" x="472" y="0" width="83" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b358acf2-675f-434e-a45f-d640b2aa074e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_LIQUIDO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Opaque" x="410" y="0" width="62" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="cc8a72ee-4b90-40b0-8396-8e1e4337c5c2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_LIQUIDO} / $F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###;#,##0.###-">
				<reportElement mode="Opaque" x="370" y="0" width="40" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="18772301-372c-4fe4-8549-6261634c4b82">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUANTIDADE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="474" y="1" width="9" height="9" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="rgba(0, 0, 0, 0.5882353)" uuid="f1de991b-c340-4664-a3b9-c903e0a317b9">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="7" isBold="false"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="412" y="1" width="9" height="9" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" forecolor="rgba(0, 0, 0, 0.5882353)" uuid="299654b1-59cc-4a57-a79d-65bdd3e46250">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Calibri" size="7" isBold="false"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
