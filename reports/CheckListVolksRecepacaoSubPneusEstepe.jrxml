<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListVolksRecepacaoSubPneusEstepe" pageWidth="152" pageHeight="58" columnWidth="152" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select cod_empresa, nome from empresas e where e.cod_empresa in (1,2,3)]]>
		</queryString>
		<field name="COD_EMPRESA" class="java.lang.Double"/>
		<field name="NOME" class="java.lang.String"/>
	</subDataset>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232561.0]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[WITH consulta as (
    select UPPER(a.item_descricao) as item_descricao,
    UPPER(a.selecionado_opcao_descricao) as descricao_opcao
	from table(pkg_crm_service_checklist.get_table_checklist_item($P{COD_EMPRESA}, $P{NUMERO_OS},null, null)) A
	where a.id_checklist = 60000
	      		and a.id_grupo = 60000
      		
),

PARAMETROS AS (
           SELECT
              2 MULTIPLO, /*Determina o multiplo da quantidade de linhas*/
              0 MAXIMO_LINHAS /* determina o maximo de linhas, zero é ilimitado, o numero de linhas que a consulta retornar*/
              FROM DUAL
),
CONSULTA_LIMITADA as (
     SELECT consulta.* FROM consulta,parametros where (rownum <= parametros.maximo_linhas or parametros.MAXIMO_LINHAS = 0)         
),
CONSULTA_FINAL as (
    SELECT * FROM CONSULTA_LIMITADA
    UNION ALL
    SELECT NULL, NULL
    FROM dual,PARAMETROS
    WHERE MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA),parametros.multiplo) <> 0 or (SELECT COUNT(*) FROM CONSULTA_LIMITADA) = 0
    CONNECT BY level <= parametros.multiplo - MOD((SELECT COUNT(*) FROM CONSULTA_LIMITADA), parametros.multiplo)
)
SELECT * /*[PODE ESPECIFICAR OS CAMPOS]*/ FROM CONSULTA_FINAL]]>
	</queryString>
	<field name="ITEM_DESCRICAO" class="java.lang.String"/>
	<field name="DESCRICAO_OPCAO" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="26" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="16" width="152" height="10" uuid="98e3a3fc-a687-48e5-a433-1e53d3afce84">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="107" y="0" width="45" height="10" forecolor="#000000" uuid="b1bb0b2d-c622-43de-97ef-32e005dbca0b">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[(  ) Ruim]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="45" y="0" width="62" height="10" forecolor="#000000" uuid="e23cd67b-42c2-422c-bc66-e8ad6d836aa3">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[(  )Regular]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="45" height="10" forecolor="#000000" uuid="20832b2f-4917-4ea6-9c19-5c4fed1db6d4">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<text><![CDATA[(  )  Bom]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="9" y="0" width="9" height="10" forecolor="#000000" uuid="b04c1209-3373-4a82-84b0-1576bcd05a79">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("BOM") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="59" y="0" width="9" height="10" forecolor="#000000" uuid="c157604f-957d-4a8f-bc0d-2ee55e8c570a">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("REGULAR") ? "X" : ""]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="116" y="0" width="9" height="10" forecolor="#000000" uuid="87d2cb49-4f52-49ee-872f-f39dc4b0ee7b">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DESCRICAO_OPCAO}.equals("RUIM") ? "X" : ""]]></textFieldExpression>
				</textField>
			</frame>
			<textField>
				<reportElement mode="Transparent" x="0" y="0" width="152" height="16" forecolor="#000000" backcolor="#E3DCDC" uuid="96f7e22b-192b-4cb8-8d6c-5926088fadd5">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box leftPadding="5" rightPadding="4">
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM_DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
		<band height="10">
			<printWhenExpression><![CDATA[$F{ITEM_DESCRICAO}.equals("PNEUS")]]></printWhenExpression>
			<staticText>
				<reportElement mode="Transparent" x="0" y="0" width="152" height="10" forecolor="#000000" uuid="91f6fc70-5450-4a33-976c-86019f726ab5"/>
				<box>
					<pen lineWidth="0.75"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
