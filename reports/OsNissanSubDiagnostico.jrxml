<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsNissanSubDiagnostico" pageWidth="494" pageHeight="842" whenNoDataType="NoPages" columnWidth="494" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="camposNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT 
		ROWNUM NUM_LISTA,
		G.DESCRICAO
 FROM GARANTIA_FORD_LAUDO G
 WHERE G.COD_EMPRESA       =$P{COD_EMPRESA}
 AND   G.NUMERO_OS         =$P{NUMERO_OS}
 AND   (G.TIPO='D')
 UNION ALL
 SELECT ROWNUM NUM_LISTA, '  ' AS DESCRICAO FROM DUAL
 WHERE NOT EXISTS(SELECT 1 FROM GARANTIA_FORD_LAUDO GL
 WHERE GL.COD_EMPRESA       =$P{COD_EMPRESA}
 AND   GL.NUMERO_OS         =$P{NUMERO_OS}
 AND   (GL.TIPO='D') )]]>
	</queryString>
	<field name="NUM_LISTA" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="NUM_LISTA"/>
		<property name="com.jaspersoft.studio.field.label" value="NUM_LISTA"/>
	</field>
	<field name="DESCRICAO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="DESCRICAO"/>
		<property name="com.jaspersoft.studio.field.label" value="DESCRICAO"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="1">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="494" height="11" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="1e6da218-bcea-4afc-9ceb-819528eb6b97">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
