<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsNissanSubPecasCausadorasProblema" pageWidth="494" pageHeight="842" whenNoDataType="NoPages" columnWidth="494" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="camposNull" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[SELECT 
  CEIL (ROWNUM/2) AS ITEM
  ,LISTAGG(
  CASE OSR.TIPO
    WHEN 'R' THEN GFL.DESCRICAO
    ELSE OSR.COD_ITEM
  END
  , ', ') WITHIN GROUP (ORDER BY ROWNUM) AS GRUPO_PECAS
FROM
    OS_REQUISICOES OSR, 
    GARANTIA_FORD_LAUDO GFL
WHERE GFL.COD_EMPRESA (+) = OSR.COD_EMPRESA 
    AND GFL.NUMERO_OS (+) = OSR.NUMERO_OS 
    AND OSR.COD_EMPRESA = $P{COD_EMPRESA} 
    AND OSR.NUMERO_OS = $P{NUMERO_OS} 
    AND OSR.CAUSADORA = 'S'
GROUP BY CEIL (ROWNUM/2)]]>
	</queryString>
	<field name="ITEM" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="ITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="ITEM"/>
	</field>
	<field name="GRUPO_PECAS" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="GRUPO_PECAS"/>
		<property name="com.jaspersoft.studio.field.label" value="GRUPO_PECAS"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="11" splitType="Stretch">
			<textField>
				<reportElement stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="494" height="11" isPrintWhenDetailOverflows="true" uuid="1e6da218-bcea-4afc-9ceb-819528eb6b97">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box leftPadding="3" rightPadding="3">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Calibri" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GRUPO_PECAS}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
