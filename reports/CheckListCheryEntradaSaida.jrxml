<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListCheryPadrao" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="0" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="TESTE_FREEDOM.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="padrao_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["H:\\NBS\\32254\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens_relatorio\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="LISTA_GRUPOS" class="java.lang.String">
		<defaultValueExpression><![CDATA["30500,30525,30550,30575,30600,30625,30650"]]></defaultValueExpression>
	</parameter>
	<parameter name="ID_CHECKLIST" class="java.lang.Double"/>
	<queryString>
		<![CDATA[WITH qryOS AS
(SELECT ROWNUM AS NUMERO_LINHA,
OS.COD_EMPRESA,
       OS.NUMERO_OS,
       to_date(to_char(OS.DATA_EMISSAO,'DD/MM/YYYY') || ' ' || os.hora_emissao, 'DD/MM/YYYY HH24:MI') as data_emissao,
       V.PRISMA,
       C.NOME NOME_CLIENTE,
       NVL(NULLIF(CD.CGC, ''), CD.CPF) CNPJ_CPF,
       CD.Inscricao_Estadual  Inscricao_Estadual,
       CD.RG,
       NVL(C.RUA_RES, C.RUA_COM) RUA_RES,
       NVL(C.BAIRRO_RES, C.BAIRRO_COM) BAIRRO_RES,
       (SELECT DESCRICAO
          FROM CIDADES
         WHERE COD_CIDADES = NVL(C.COD_CID_RES, C.COD_CID_COM)
           AND ROWNUM = 1) AS CIDADE_RES,
       NVL(C.UF_RES, C.UF_COM) UF_RES,
       NVL(C.CEP_RES, C.CEP_COM) CEP_RES,
       C.ENDERECO_ELETRONICO,
       (NVL(C.PREFIXO_RES, C.PREFIXO_COM) || ' - ' ||
       NVL(C.TELEFONE_RES, C.TELEFONE_COM)) AS TELEFONE_RES,
       (C.PREFIXO_CEL || ' - ' || C.TELEFONE_CEL) AS TELEFONE_CEL,
       (C.PREFIXO_COM || ' - ' || C.TELEFONE_COM) AS TELEFONE_COM,
       NVL(NULLIF(C.RUA_COBRANCA, ''), C.RUA_RES) RUA_FAT,
       NVL(NULLIF(C.CEP_COBRANCA, ''), C.CEP_RES) CEP_FAT,
       NVL(NULLIF(C.BAIRRO_COBRANCA, ''), C.BAIRRO_RES) BAIRRO_FAT,
       NVL(CIDF.DESCRICAO, CIDR.DESCRICAO) CIDADE_FAT,
       NVL(CIDF.UF, CIDR.UF) UF_FAT,
       OS.OBSERVACAO,
       CASE
         WHEN OT.REVISAO_GRATUITA = 'S' THEN
          1 
         WHEN OT.INTERNO = 'S' THEN
          2 
         WHEN OT.GARANTIA = 'S' THEN
          3 
         WHEN OTE.FUNILARIA = 'S' THEN
          4 
         ELSE
          0 
       END TIPO_OS,
       CASE UPPER(FPG.TIPO_PGTO)
         WHEN 'Z' THEN
          1 
         WHEN 'H' THEN
          2 
         WHEN 'V' THEN
          3 
         WHEN 'M' THEN
          4 
         WHEN 'P' THEN
          4 
         ELSE
          0 
       END FORMA_PAGAMENTO,
       PM.DESCRICAO_MODELO,
       V.CHASSI,
       V.COR_EXTERNA,
       V.KM,
       V.DATA_VENDA,
       V.ANO,
       V.PLACA,
       CON.CODIGO_PADRAO COD_REVENDEDOR,
       CON.NOME NOME_REVENDEDOR,
       V.COMBUSTIVEL,
       NVL(OS.NOME, OS.CONSULTOR_RECEPCAO) AS CONSULTOR,
   NVL(CONSULTOR_USUARIO.NOME_COMPLETO, ' ')  AS CONSULTOR_COMPLETO,
   NVL((SELECT INITCAP(EMPRESAS_FUNCOES.DESCRICAO) FROM EMPRESAS_FUNCOES
      WHERE EMPRESAS_FUNCOES.COD_FUNCAO = 1
      and ROWNUM = 1
      ), '') AS CONSULTOR_FUNCAO,
   OS_AGENDA.SIGNATURE AS OS_ASSINATURA,
   PRODUTOS.COD_SEGMENTO,
 SYSDATE AS DATA_HOJE
  FROM OS,
   OS_AGENDA,
       OS_DADOS_VEICULOS V,
       OS_TIPOS          OT,
       OS_TIPOS_EMPRESAS OTE,
       OS_PAGAMENTO      OPG,
       FORMA_PGTO        FPG,
       CLIENTES          C,
       CLIENTE_DIVERSO   CD,
       CIDADES           CIDR,
       CIDADES           CIDF,
       PRODUTOS_MODELOS  PM,
       PRODUTOS,
       CONCESSIONARIAS   CON,
   EMPRESAS_USUARIOS CONSULTOR_USUARIO
 WHERE (OS.NUMERO_OS = V.NUMERO_OS AND OS.COD_EMPRESA = V. COD_EMPRESA)
 AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
   AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS (+)
   AND (OS.TIPO = OT.TIPO)
   AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
   AND (OT.TIPO = OTE.TIPO AND OTE.COD_EMPRESA = OS.COD_EMPRESA)
   AND (OS.NUMERO_OS = OPG.NUMERO_OS(+) AND
       OS.COD_EMPRESA = OPG.COD_EMPRESA(+))
   AND  OPG.COD_FORMA_PGTO = FPG.COD_FORMA_PGTO(+)
   AND  OPG.COD_EMPRESA = FPG.COD_EMPRESA(+)
   AND (OS.COD_CLIENTE = C.COD_CLIENTE)
   AND (C.COD_CLIENTE = CD.COD_CLIENTE)
   AND C.COD_CID_RES = CIDR.COD_CIDADES(+)
   AND C.COD_CID_COBRANCA = CIDF.COD_CIDADES(+)
        
   AND V.COD_PRODUTO = PM.COD_PRODUTO (+)
   AND V.COD_MODELO = PM.COD_MODELO (+)
   AND PM.COD_PRODUTO = PRODUTOS.COD_PRODUTO (+)
   AND V.COD_CONCESSIONARIA = CON.COD_CONCESSIONARIA
   AND OS.NUMERO_OS = $P{NUMERO_OS}
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
 AND OS.NOME = CONSULTOR_USUARIO.NOME(+)),

qryEmpresa AS
(SELECT ROWNUM AS NUMERO_LINHA, E.NOME,
       E.RUA,
       E.BAIRRO,
       E.CIDADE,
       E.ESTADO,
       E.CEP,
       E.FONE,
       E.FAX,
       C.CODIGO_PADRAO CODIGO_REVENDEDOR
  FROM EMPRESAS E, PARM_SYS P, CONCESSIONARIAS C
 WHERE E.COD_EMPRESA = P.COD_EMPRESA
   AND P.CONCESSIONARIA_NUMERO = C.COD_CONCESSIONARIA(+)
   AND E.COD_EMPRESA = $P{COD_EMPRESA}),

qryAssinaturas AS
(Select rownum as numero_linha, entrada.assinatura_cliente as assinatura_entrada_cliente, entrada.assinatura as assinatura_entrada_tecnico, entrada.data_assinatura_cliente as assinatura_entrada_data,
   saida.assinatura_cliente as assinatura_saida_cliente, saida.assinatura as assinatura_saida_tecnico, saida.data_assinatura_cliente as assinatura_saida_data       
  from mob_os_assinatura entrada, mob_os_assinatura saida, os
  where os.numero_os = $P{NUMERO_OS}
        and os.cod_empresa = $P{COD_EMPRESA}
        and os.numero_os = entrada.numero_os(+)
        and os.cod_empresa = entrada.cod_empresa(+) 
        and entrada.aplicacao(+) = 'R'
        and os.numero_os = saida.numero_os(+)
        and os.cod_empresa = saida.cod_empresa(+)
        and saida.aplicacao(+) = 'E'
)

SELECT 
qryempresa.NOME AS qryempresa_NOME,
qryempresa.RUA AS qryempresa_RUA,
qryos.NUMERO_OS AS qryos_NUMERO_OS,
qryempresa.CEP AS qryempresa_CEP,
qryempresa.CODIGO_REVENDEDOR AS qryempresa_CODIGO_REVENDEDOR,
qryempresa.FONE AS qryempresa_FONE,
qryempresa.BAIRRO AS qryempresa_BAIRRO,
qryos.PRISMA AS qryos_PRISMA,
qryos.DATA_EMISSAO AS qryos_DATA_EMISSAO,
qryempresa.CIDADE AS qryempresa_CIDADE,
QRYEMPRESA.ESTADO AS QRYEMPRESA_ESTADO,
qryempresa.FAX AS qryempresa_FAX,
qryos.COR_EXTERNA AS qryos_COR_EXTERNA,
qryos.CHASSI AS qryos_CHASSI,
qryos.DESCRICAO_MODELO AS qryos_DESCRICAO_MODELO,
qryos.DATA_VENDA AS qryos_DATA_VENDA,
qryos.KM AS qryos_KM,
qryos.OBSERVACAO AS qryos_OBSERVACAO,
qryos.ANO AS qryos_ANO,
qryos.PLACA AS qryos_PLACA,
qryos.CONSULTOR_COMPLETO AS qryos_CONSULTOR_COMPLETO,
qryos.CONSULTOR_FUNCAO AS qryos_CONSULTOR_FUNCAO,
qryos.DATA_HOJE AS qryos_DATA_HOJE,
qryos.COD_REVENDEDOR AS qryos_COD_REVENDEDOR,
qryos.NOME_REVENDEDOR AS qryos_NOME_REVENDEDOR,
qryos.NOME_CLIENTE AS qryos_NOME_CLIENTE,
qryos.RUA_RES AS qryos_RUA_RES,
qryos.BAIRRO_RES AS qryos_BAIRRO_RES,
qryos.RG AS qryos_RG,
qryos.CNPJ_CPF AS qryos_CNPJ_CPF,
qryos.CIDADE_RES AS qryos_CIDADE_RES,
qryos.TELEFONE_COM AS qryos_TELEFONE_COM,
qryos.ENDERECO_ELETRONICO AS qryos_ENDERECO_ELETRONICO,
qryos.CEP_RES AS qryos_CEP_RES,
qryos.UF_RES AS qryos_UF_RES,
qryos.TELEFONE_CEL AS qryos_TELEFONE_CEL,
qryos.TELEFONE_RES AS qryos_TELEFONE_RES,
qryos.BAIRRO_FAT AS qryos_BAIRRO_FAT,
COALESCE (qryos.TELEFONE_COM,qryos.TELEFONE_RES,qryos.TELEFONE_CEL) AS qryos_TELEFONE_CLIENTE,
qryos.CEP_FAT AS qryos_CEP_FAT,
qryos.UF_FAT AS qryos_UF_FAT,
qryos.CIDADE_FAT AS qryos_CIDADE_FAT,
qryos.RUA_FAT AS qryos_RUA_FAT,
qryos.TIPO_OS AS qryos_TIPO_OS,
qryos.COMBUSTIVEL AS qryos_COMBUSTIVEL,
qryos.FORMA_PAGAMENTO as qryos_FORMA_PAGAMENTO,
qryos.OS_ASSINATURA as qruos_OS_ASSINATURA,
qryos.Inscricao_Estadual as qryos_Inscricao_Estadual,
qryos.cod_segmento as qryos_cod_segmento,
qryAssinaturas.ASSINATURA_ENTRADA_CLIENTE,
qryAssinaturas.ASSINATURA_ENTRADA_TECNICO,
qryAssinaturas.ASSINATURA_ENTRADA_DATA,
qryAssinaturas.ASSINATURA_SAIDA_CLIENTE,
qryAssinaturas.ASSINATURA_SAIDA_TECNICO,
qryAssinaturas.ASSINATURA_SAIDA_DATA
FROM qryOS, qryEmpresa, qryAssinaturas
WHERE qryOS.NUMERO_LINHA = qryEmpresa.NUMERO_LINHA (+)
  and qryOS.NUMERO_LINHA = qryAssinaturas.numero_linha (+)]]>
	</queryString>
	<field name="QRYEMPRESA_NOME" class="java.lang.String"/>
	<field name="QRYEMPRESA_RUA" class="java.lang.String"/>
	<field name="QRYOS_NUMERO_OS" class="java.lang.Double"/>
	<field name="QRYEMPRESA_CEP" class="java.lang.String"/>
	<field name="QRYEMPRESA_CODIGO_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYEMPRESA_FONE" class="java.lang.String"/>
	<field name="QRYEMPRESA_BAIRRO" class="java.lang.String"/>
	<field name="QRYOS_PRISMA" class="java.lang.String"/>
	<field name="QRYOS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="QRYEMPRESA_CIDADE" class="java.lang.String"/>
	<field name="QRYEMPRESA_ESTADO" class="java.lang.String"/>
	<field name="QRYEMPRESA_FAX" class="java.lang.String"/>
	<field name="QRYOS_COR_EXTERNA" class="java.lang.String"/>
	<field name="QRYOS_CHASSI" class="java.lang.String"/>
	<field name="QRYOS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="QRYOS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="QRYOS_KM" class="java.lang.Double"/>
	<field name="QRYOS_OBSERVACAO" class="java.lang.String"/>
	<field name="QRYOS_ANO" class="java.lang.String"/>
	<field name="QRYOS_PLACA" class="java.lang.String"/>
	<field name="QRYOS_CONSULTOR_COMPLETO" class="java.lang.String"/>
	<field name="QRYOS_CONSULTOR_FUNCAO" class="java.lang.String"/>
	<field name="QRYOS_DATA_HOJE" class="java.sql.Timestamp"/>
	<field name="QRYOS_COD_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_CLIENTE" class="java.lang.String"/>
	<field name="QRYOS_RUA_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_RES" class="java.lang.String"/>
	<field name="QRYOS_RG" class="java.lang.String"/>
	<field name="QRYOS_CNPJ_CPF" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_COM" class="java.lang.String"/>
	<field name="QRYOS_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="QRYOS_CEP_RES" class="java.lang.String"/>
	<field name="QRYOS_UF_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_CEL" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_FAT" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_CLIENTE" class="java.lang.String"/>
	<field name="QRYOS_CEP_FAT" class="java.lang.String"/>
	<field name="QRYOS_UF_FAT" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_FAT" class="java.lang.String"/>
	<field name="QRYOS_RUA_FAT" class="java.lang.String"/>
	<field name="QRYOS_TIPO_OS" class="java.lang.Double"/>
	<field name="QRYOS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="QRYOS_FORMA_PAGAMENTO" class="java.lang.Double"/>
	<field name="QRUOS_OS_ASSINATURA" class="java.awt.Image"/>
	<field name="QRYOS_INSCRICAO_ESTADUAL" class="java.lang.String"/>
	<field name="QRYOS_COD_SEGMENTO" class="java.lang.Double"/>
	<field name="ASSINATURA_ENTRADA_CLIENTE" class="java.awt.Image"/>
	<field name="ASSINATURA_ENTRADA_TECNICO" class="java.awt.Image"/>
	<field name="ASSINATURA_ENTRADA_DATA" class="java.sql.Timestamp"/>
	<field name="ASSINATURA_SAIDA_CLIENTE" class="java.awt.Image"/>
	<field name="ASSINATURA_SAIDA_TECNICO" class="java.awt.Image"/>
	<field name="ASSINATURA_SAIDA_DATA" class="java.sql.Timestamp"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="124">
			<frame>
				<reportElement x="0" y="0" width="555" height="124" uuid="2145c494-2878-4a32-8db2-01b69a37a96c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="49" width="375" height="30" forecolor="#000000" uuid="5a9a3bda-0038-4d7e-8da6-ba7a76c4258b"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Bottom">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[Nome do Cliente:]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="0" width="555" height="48" uuid="b395e938-4e67-4fc5-ae79-d9650e46aa2b">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<frame>
						<reportElement mode="Opaque" x="105" y="1" width="450" height="37" backcolor="#525252" uuid="15334e08-7e19-4ae0-bb48-db7a7f918a9b"/>
						<staticText>
							<reportElement mode="Transparent" x="9" y="0" width="244" height="15" forecolor="#FFFFFF" backcolor="#FFFFFF" uuid="5c7c6777-3a4d-430b-ab23-e11e03c9cb07">
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							</reportElement>
							<box leftPadding="3" rightPadding="0">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Left" verticalAlignment="Middle">
								<font fontName="SansSerif" size="10" isBold="true"/>
							</textElement>
							<text><![CDATA[CHECK - LIST : ENTRADA / SAÍDA]]></text>
						</staticText>
						<staticText>
							<reportElement mode="Transparent" x="9" y="14" width="282" height="16" forecolor="#FFFFFF" uuid="6ad58a28-cdae-4004-8c23-705837ff0922">
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							</reportElement>
							<box leftPadding="3" rightPadding="0">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Left" verticalAlignment="Middle">
								<font fontName="SansSerif" size="8" isBold="false" isItalic="true"/>
							</textElement>
							<text><![CDATA[(Inspeção do veículo antes e depois da execução de um serviço)]]></text>
						</staticText>
					</frame>
					<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" isUsingCache="false">
						<reportElement x="1" y="1" width="104" height="42" uuid="98bf55b2-f395-4fd1-ad28-fe071786e1a4">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						</reportElement>
						<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} +"crmservice43501.png"]]></imageExpression>
					</image>
				</frame>
				<staticText>
					<reportElement mode="Transparent" x="375" y="49" width="82" height="30" forecolor="#000000" uuid="78f636a8-985e-4ee2-9d4d-9c160c7bfcdb">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[OS:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="457" y="49" width="98" height="30" forecolor="#000000" uuid="1b9507f8-326e-40c8-b43d-36423b77f267"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[Placa:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="0" y="84" width="222" height="30" forecolor="#000000" uuid="9f2d6c35-d049-4de2-bf19-a42f19cf0396"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[Veículo:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="223" y="84" width="154" height="30" forecolor="#000000" uuid="99166b38-431f-4cab-87bd-74fbc38e55ef">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[Km de entrada]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="377" y="84" width="178" height="30" forecolor="#000000" uuid="3a588bd3-21a6-4398-9004-0354ac3d5d72">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="11" bottomPadding="0" rightPadding="0">
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[Data: __/__/____]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="479" y="93" width="59" height="14" forecolor="#000000" uuid="9d3751d3-bca1-4e3f-8b17-2ad60daf96b6">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<text><![CDATA[Hora:__:__ h]]></text>
				</staticText>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement mode="Transparent" x="384" y="57" width="70" height="21" forecolor="#000000" uuid="84def50b-90f2-446b-bf84-50b9c0a41ae6">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="13" isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<textField pattern="#0.###;(#0.###-)">
					<reportElement mode="Transparent" x="466" y="57" width="87" height="22" forecolor="#000000" uuid="8012ebb9-86a7-4fd6-a789-2e46e7ddeccf">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="12" isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="82" y="60" width="289" height="16" forecolor="#000000" uuid="0a84dee3-e982-4101-bd54-35cdbbe0fd7a">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Dashed" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="9" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NOME_CLIENTE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="16" y="97" width="194" height="14" forecolor="#000000" uuid="4e434b9d-9d21-468c-90bf-a03a679fafad">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="11" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DESCRICAO_MODELO}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.###;(#,##0.###-)">
					<reportElement mode="Transparent" x="239" y="97" width="132" height="14" forecolor="#000000" uuid="f6ce247e-c881-48f0-94be-d3e7dde9b169">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_KM}]]></textFieldExpression>
				</textField>
				<textField pattern="dd MM yyyy">
					<reportElement mode="Transparent" x="414" y="93" width="53" height="14" forecolor="#000000" uuid="00a278e1-debf-4ac3-8024-d787acb09099">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField pattern="HH mm">
					<reportElement mode="Transparent" x="503" y="93" width="27" height="14" forecolor="#000000" uuid="820573e1-85b2-4352-a6e7-9fcf944a9803">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="267">
			<image>
				<reportElement x="394" y="1" width="147" height="40" uuid="3d68bb99-0a0f-455b-9ecf-85ab66069166">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<imageExpression><![CDATA[$F{QRYOS_COMBUSTIVEL}  < 19 ? $P{DIR_IMAGE_LOGO} + "crmservice519017.png" :
$F{QRYOS_COMBUSTIVEL}  < 39 ? $P{DIR_IMAGE_LOGO} + "crmservice519018.png" :
$F{QRYOS_COMBUSTIVEL}  < 59 ? $P{DIR_IMAGE_LOGO} + "crmservice519019.png"  :
$F{QRYOS_COMBUSTIVEL}  < 79 ? $P{DIR_IMAGE_LOGO} + "crmservice519020.png" :
$P{DIR_IMAGE_LOGO} + "crmservice519021.png"]]></imageExpression>
			</image>
			<subreport overflowType="Stretch">
				<reportElement key="" x="0" y="2" width="365" height="265" isRemoveLineWhenBlank="true" uuid="9ead0092-81d2-426c-82ea-524d248a830b">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_SEGMENTO">
					<subreportParameterExpression><![CDATA[$F{QRYOS_COD_SEGMENTO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DIR_IMAGE_LOGO">
					<subreportParameterExpression><![CDATA[$P{DIR_IMAGE_LOGO}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListCheryEntradaSaidaSubImagem.jasper"]]></subreportExpression>
			</subreport>
			<subreport overflowType="Stretch">
				<reportElement key="" x="376" y="42" width="179" height="225" isRemoveLineWhenBlank="true" uuid="21055644-f08d-4a7c-a389-35e4cd5b560f">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListCheryEntradaSaidaSubPertences.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="108">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport overflowType="Stretch">
				<reportElement key="" x="0" y="0" width="365" height="108" isRemoveLineWhenBlank="true" uuid="0b13197c-a185-429d-b315-188ecc0125f8">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListCheryEntradaSaidaSubOutrosItens.jasper"]]></subreportExpression>
			</subreport>
			<subreport overflowType="Stretch">
				<reportElement key="" x="376" y="0" width="179" height="108" isRemoveLineWhenBlank="true" uuid="9e1eb096-5141-4670-b1a0-d6a72c870ff0">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListCheryEntradaSaidaSubAvaliacaoCondicao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="300">
			<frame>
				<reportElement x="0" y="6" width="555" height="136" uuid="566733f2-00e4-42e8-a7c9-240ce65ea282"/>
				<staticText>
					<reportElement mode="Transparent" x="108" y="73" width="59" height="14" forecolor="#000000" uuid="681274eb-4b69-4444-844b-58297321a174">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<text><![CDATA[Hora:__:__ h]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="11" y="64" width="178" height="30" forecolor="#000000" uuid="c46f82d7-788a-47b4-a50d-8e81eb0070dc">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="11" bottomPadding="0" rightPadding="0">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[Data: __/__/____]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="212" y="0" width="130" height="12" forecolor="#000000" uuid="81fe7d02-2777-43b5-93c3-ced7b4fe1991">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Termo de Entrada do Veículo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="3" y="16" width="548" height="26" forecolor="#000000" uuid="30fcfee2-33ae-4288-b277-cfb13911c5e7">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Declaro que retirei todo os objetos e pertences pessoais do veículo acima descrito, bem como estou de acordo com as observações constantes na presente vistoria.]]></text>
				</staticText>
				<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle">
					<reportElement x="364" y="45" width="145" height="24" uuid="e3f54c97-5cff-4367-a034-785bbcd9f571"/>
					<imageExpression><![CDATA[$F{ASSINATURA_ENTRADA_CLIENTE}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="336" y="71" width="202" height="12" forecolor="#000000" uuid="6d5f0425-c75b-48d5-a240-67e96e6bc345">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente]]></text>
				</staticText>
				<image scaleImage="RealSize" hAlign="Center" vAlign="Middle">
					<reportElement x="364" y="93" width="145" height="24" uuid="82fa4548-4ff1-4562-8063-55d5d1895d88"/>
					<imageExpression><![CDATA[$F{ASSINATURA_ENTRADA_TECNICO}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="336" y="120" width="202" height="12" forecolor="#000000" uuid="dcf96ccc-026e-4e89-bc39-a85eced6f69d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura Técnico]]></text>
				</staticText>
				<textField pattern="HH mm">
					<reportElement mode="Transparent" x="132" y="73" width="27" height="14" forecolor="#000000" uuid="61bede89-0af5-4402-99fa-284da33d13d6">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ASSINATURA_ENTRADA_DATA}]]></textFieldExpression>
				</textField>
				<textField pattern="dd MM yyyy">
					<reportElement mode="Transparent" x="48" y="73" width="53" height="14" forecolor="#000000" uuid="5cc8594c-d9a7-4f65-bda7-32656a29c422">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ASSINATURA_ENTRADA_DATA}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="146" width="555" height="154" uuid="a6bd8461-3224-47ca-b7ea-76155d80496f">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<staticText>
					<reportElement mode="Transparent" x="108" y="103" width="59" height="14" forecolor="#000000" uuid="4e60a212-59f4-4b83-8df8-79b74f75320f">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<text><![CDATA[Hora:__:__ h]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="11" y="94" width="178" height="30" forecolor="#000000" uuid="55e53339-2ae7-4751-9f14-68cfbf725204">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="11" bottomPadding="0" rightPadding="0">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<text><![CDATA[Data: __/__/____]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="212" y="0" width="130" height="12" forecolor="#000000" uuid="d236d8e1-1066-4f76-bbe8-57958a70cf20">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Termo de Saída do Veículo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="3" y="16" width="548" height="44" forecolor="#000000" uuid="9ebdddc7-98f7-4465-be7e-fd8ce5e741a5">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Declaro neste ato, ter recebido o veiculo acima descrito, com os serviços e peças aplicados e com as devidas explicações, conforme minha autorização prévia. Declado ainda ter recebido conselhos sobre manutenções e futuras intervenções e que o referido veículo encontra-se nas condições de conservação de quando da retirada, bem como todos os acessórios.]]></text>
				</staticText>
				<image scaleImage="RealSize" hAlign="Center" vAlign="Middle">
					<reportElement x="364" y="62" width="145" height="24" uuid="2b9f57fe-53ce-4841-85a6-8d97ccd588ff"/>
					<imageExpression><![CDATA[$F{ASSINATURA_SAIDA_CLIENTE}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="336" y="88" width="202" height="12" forecolor="#000000" uuid="246d9295-d1bc-4cfd-a4fe-1838e045dd33">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura do Cliente]]></text>
				</staticText>
				<image scaleImage="RealSize" hAlign="Center" vAlign="Middle">
					<reportElement x="364" y="110" width="145" height="24" uuid="5354f7dd-03da-4c2c-810f-b6550168fe73"/>
					<imageExpression><![CDATA[$F{ASSINATURA_SAIDA_TECNICO}]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="336" y="136" width="202" height="12" forecolor="#000000" uuid="2c93ae55-3eb8-4cfa-a396-c69637ae84ae">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Assinatura Técnico]]></text>
				</staticText>
				<textField pattern="HH mm">
					<reportElement mode="Transparent" x="132" y="103" width="27" height="14" forecolor="#000000" uuid="9504d5bf-35e1-4d4d-a651-f91f38ec76c8">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ASSINATURA_SAIDA_DATA}]]></textFieldExpression>
				</textField>
				<textField pattern="dd MM yyyy">
					<reportElement mode="Transparent" x="48" y="103" width="53" height="14" forecolor="#000000" uuid="f9173cd0-a9df-4156-ac0b-cdb51fb43ae4">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box leftPadding="0" rightPadding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="SansSerif" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ASSINATURA_SAIDA_DATA}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="17">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</columnFooter>
</jasperReport>
