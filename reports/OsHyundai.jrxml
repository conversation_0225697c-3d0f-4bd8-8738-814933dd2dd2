<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsHyundai" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isFloatColumnFooter="true" uuid="da968964-d63c-4089-abe4-9ca20f6e7012">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MARIO_BANCO"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="Dataset1" whenResourceMissingType="Empty" uuid="47a4b801-128a-407f-ac16-29b77189a8bc">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
		<parameter name="NUMERO_OS" class="java.lang.Double"/>
		<parameter name="COD_EMPRESA" class="java.lang.Double"/>
		<queryString language="SQL">
			<![CDATA[SELECT O.ID_CHECKLIST, O.VALOR
  FROM OS_CHECKLIST O
  WHERE O.NUMERO_OS = $P{NUMERO_OS} 
    AND O.COD_EMPRESA = $P{COD_EMPRESA}
    AND O.ID_CHECKLIST = 'CHK_ESCAPAMENTO']]>
		</queryString>
		<field name="ID_CHECKLIST" class="java.lang.String"/>
		<field name="VALOR" class="java.lang.Integer"/>
	</subDataset>
	<subDataset name="Dataset2" uuid="f42b840f-635d-41af-842e-8fccdbf887eb">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
		<parameter name="NUMERO_OS" class="java.lang.Double"/>
		<parameter name="COD_EMPRESA" class="java.lang.Double"/>
		<queryString language="SQL">
			<![CDATA[WITH QRYOSCHECKLIST AS
 (SELECT O.ID_CHECKLIST, O.VALOR
  FROM OS_CHECKLIST O
  WHERE O.NUMERO_OS = $P{NUMERO_OS} 
    AND O.COD_EMPRESA = $P{COD_EMPRESA})
SELECT QRYOSCHECKLIST_RESULT.CHK_PNEUSDE,
       QRYOSCHECKLIST_RESULT.CHK_PNEUSDD,
       QRYOSCHECKLIST_RESULT.CHK_PNEUSTE,
       QRYOSCHECKLIST_RESULT.CHK_PNEUSTD
  FROM QRYOSCHECKLIST PIVOT(LISTAGG(VALOR) WITHIN GROUP(ORDER BY QRYOSCHECKLIST.ID_CHECKLIST)
        FOR ID_CHECKLIST IN ('CHK_PNEUSDE' AS CHK_PNEUSDE,
                               'CHK_PNEUSDD' AS CHK_PNEUSDD,
                               'CHK_PNEUSTE' AS CHK_PNEUSTE,
                               'CHK_PNEUSTD' AS CHK_PNEUSTD)) QRYOSCHECKLIST_RESULT]]>
		</queryString>
		<field name="CHK_PNEUSDE" class="java.lang.String"/>
		<field name="CHK_PNEUSDD" class="java.lang.String"/>
		<field name="CHK_PNEUSTE" class="java.lang.String"/>
		<field name="CHK_PNEUSTD" class="java.lang.String"/>
	</subDataset>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\31830\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="NUMERO_OS" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[232756.0]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_EMPRESA" class="java.lang.Double">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[2.0]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH qryOS AS
 (SELECT ROWNUM AS NUMERO_LINHA,
  OS.COD_EMPRESA,
         OS.NUMERO_OS,
         TO_DATE(os.data_emissao || ' ' || os.hora_emissao, 'DD/MM/YY HH24:MI') AS DATA_EMISSAO,
         V.PRISMA,
         C.NOME NOME_CLIENTE,
         NVL(NULLIF(CD.CGC, ''), CD.CPF) CNPJ_CPF,
         CD.RG,
         NVL(C.RUA_RES, C.RUA_COM) RUA_RES,
         NVL(C.BAIRRO_RES, C.BAIRRO_COM) BAIRRO_RES,
         (SELECT DESCRICAO
            FROM CIDADES
           WHERE COD_CIDADES = NVL(C.COD_CID_RES, C.COD_CID_COM)
             AND ROWNUM = 1) AS CIDADE_RES,
         NVL(C.UF_RES, C.UF_COM) UF_RES,
         NVL(C.CEP_RES, C.CEP_COM) CEP_RES,
         C.ENDERECO_ELETRONICO,
         (NVL(C.PREFIXO_RES, C.PREFIXO_COM) || ' - ' ||
         NVL(C.TELEFONE_RES, C.TELEFONE_COM)) AS TELEFONE_RES,
         (C.PREFIXO_CEL || ' - ' || C.TELEFONE_CEL) AS TELEFONE_CEL,
         (C.PREFIXO_COM || ' - ' || C.TELEFONE_COM) AS TELEFONE_COM,
         NVL(NULLIF(C.RUA_COBRANCA, ''), C.RUA_RES) RUA_FAT,
         NVL(NULLIF(C.CEP_COBRANCA, ''), C.CEP_RES) CEP_FAT,
         NVL(NULLIF(C.BAIRRO_COBRANCA, ''), C.BAIRRO_RES) BAIRRO_FAT,
         NVL(CIDF.DESCRICAO, CIDR.DESCRICAO) CIDADE_FAT,
         NVL(CIDF.UF, CIDR.UF) UF_FAT,
         OS.OBSERVACAO,
         CASE
           WHEN OT.REVISAO_GRATUITA = 'S' THEN
            1 
           WHEN OT.INTERNO = 'S' THEN
            2 
           WHEN OT.GARANTIA = 'S' THEN
            3 
           WHEN OTE.FUNILARIA = 'S' THEN
            4 
           ELSE
            0 
         END TIPO_OS,
         CASE UPPER(FPG.TIPO_PGTO)
           WHEN 'Z' THEN
            1 
           WHEN 'H' THEN
            2 
           WHEN 'V' THEN
            3 
           WHEN 'M' THEN
            4 
           WHEN 'P' THEN
            4 
           ELSE
            0 
         END FORMA_PAGAMENTO,
         PM.DESCRICAO_MODELO,
         V.CHASSI,
         V.COR_EXTERNA,
         V.KM,
         V.DATA_VENDA,
         V.ANO,
         V.PLACA,
         CON.CODIGO_PADRAO COD_REVENDEDOR,
         CON.NOME NOME_REVENDEDOR,
         V.COMBUSTIVEL,
         NVL(OS.NOME, OS.CONSULTOR_RECEPCAO) AS CONSULTOR,
		 NVL(OS_AGENDA.SIGNATURE,
           (SELECT OAS.SIGNATURE
              FROM OS_AGENDA OAS
             WHERE OAS.COD_EMPRESA = OS.COD_EMPRESA
               AND OAS.COD_OS_AGENDA = OS.COD_OS_AGENDA)) AS OS_ASSINATURA,
	  (SELECT A.DATA_ASSINATURA
       FROM OS_ASSINATURA A
      WHERE A.COD_EMPRESA = OS.COD_EMPRESA
        AND A.NUMERO_OS = OS.NUMERO_OS
        AND A.TIPO_ASSINATURA = 'ENTREGA_VEICULO_CLIENTE'
        AND ROWNUM = 1) AS DATA_ENTREGA_CLIENTE,
     (SELECT A.ASSINATURA
       FROM OS_ASSINATURA A
      WHERE A.COD_EMPRESA = OS.COD_EMPRESA
        AND A.NUMERO_OS = OS.NUMERO_OS
        AND A.TIPO_ASSINATURA = 'ENTREGA_VEICULO_CLIENTE'
        AND ROWNUM = 1) AS ASSINATURA_ENTREGA_CLIENTE,
        NVL(OS_AGENDA.CLIENTE_AGUARDA,'N')  AS CLIENTE_AGUARDA
    FROM OS,
		 OS_AGENDA,
         OS_DADOS_VEICULOS V,
         OS_TIPOS          OT,
         OS_TIPOS_EMPRESAS OTE,
         OS_PAGAMENTO      OPG,
         FORMA_PGTO        FPG,
         CLIENTES          C,
         CLIENTE_DIVERSO   CD,
         CIDADES           CIDR,
         CIDADES           CIDF,
         PRODUTOS_MODELOS  PM,
         CONCESSIONARIAS   CON
   WHERE (OS.NUMERO_OS = V.NUMERO_OS AND OS.COD_EMPRESA = V. COD_EMPRESA)
	 AND OS.COD_EMPRESA = OS_AGENDA.COD_EMPRESA (+)
     AND OS.NUMERO_OS = OS_AGENDA.NUMERO_OS (+)
     AND (OS.TIPO = OT.TIPO)
     AND NVL(OS.APAGAR_AO_SAIR, 'N') = 'N'
     AND (OT.TIPO = OTE.TIPO AND OTE.COD_EMPRESA = OS.COD_EMPRESA)
     AND (OS.NUMERO_OS = OPG.NUMERO_OS(+) AND
         OS.COD_EMPRESA = OPG.COD_EMPRESA(+))
     AND (OPG.COD_FORMA_PGTO = FPG.COD_FORMA_PGTO(+))
     AND (OS.COD_CLIENTE = C.COD_CLIENTE)
     AND (C.COD_CLIENTE = CD.COD_CLIENTE)
     AND C.COD_CID_RES = CIDR.COD_CIDADES(+)
     AND C.COD_CID_COBRANCA = CIDF.COD_CIDADES(+)
        
     AND (V.COD_PRODUTO = PM.COD_PRODUTO AND V.COD_MODELO = PM.COD_MODELO)
     AND (V.COD_CONCESSIONARIA = CON.COD_CONCESSIONARIA)
     AND OS.NUMERO_OS = $P{NUMERO_OS}
     AND OS.COD_EMPRESA = $P{COD_EMPRESA}),

qryEmpresa AS
 (SELECT ROWNUM AS NUMERO_LINHA, E.NOME,
         E.RUA,
         E.BAIRRO,
         E.CIDADE,
         E.ESTADO,
         E.CEP,
         E.FONE,
         E.FAX,
         C.CODIGO_PADRAO CODIGO_REVENDEDOR,
         CPF.LGPD_MEMO
    FROM EMPRESAS E, PARM_SYS P, CONCESSIONARIAS C, CRM_PARM_FLUXO CPF
   WHERE E.COD_EMPRESA = P.COD_EMPRESA
     AND E.COD_EMPRESA = CPF.COD_EMPRESA
     AND P.CONCESSIONARIA_NUMERO = C.COD_CONCESSIONARIA(+)
     AND E.COD_EMPRESA = $P{COD_EMPRESA}),

qryProdutivo AS
 (SELECT ROWNUM AS NUMERO_LINHA, OSR.COD_EMPRESA, OSR.NUMERO_OS, EXE.COD_TECNICO AS COD_PRODUTIVO, TEC.NOME AS TECNICO
    FROM OS_SERVICOS OSR, OS_TEMPOS_EXECUTADOS EXE, SERVICOS_TECNICOS TEC
   WHERE EXE.COD_EMPRESA = OSR.COD_EMPRESA
     AND EXE.NUMERO_OS = OSR.NUMERO_OS
     AND TEC.COD_TECNICO = EXE.COD_TECNICO
     AND OSR.COD_EMPRESA = $P{COD_EMPRESA}
     AND OSR.NUMERO_OS = $P{NUMERO_OS} 
     AND ROWNUM = 1),
qryCheckList AS
(select
   1 as NUMERO_LINHA,
  (SELECT O.VALOR
      FROM OS_CHECKLIST O
      WHERE O.NUMERO_OS = $P{NUMERO_OS} 
      AND O.COD_EMPRESA = $P{COD_EMPRESA}
      AND O.ID_CHECKLIST = 'CHK_PNEUSDD'
      ) AS CHK_PNEUSDD,
  (SELECT O.VALOR
      FROM OS_CHECKLIST O
      WHERE O.NUMERO_OS = $P{NUMERO_OS} 
      AND O.COD_EMPRESA = $P{COD_EMPRESA}
      AND O.ID_CHECKLIST = 'CHK_PNEUSDE'
      ) AS CHK_PNEUSDE,
  (SELECT O.VALOR
      FROM OS_CHECKLIST O
      WHERE O.NUMERO_OS = $P{NUMERO_OS} 
      AND O.COD_EMPRESA = $P{COD_EMPRESA}
      AND O.ID_CHECKLIST = 'CHK_PNEUSTD'
      ) AS CHK_PNEUSTD,
  (SELECT O.VALOR
      FROM OS_CHECKLIST O
      WHERE O.NUMERO_OS = $P{NUMERO_OS} 
      AND O.COD_EMPRESA = $P{COD_EMPRESA}
      AND O.ID_CHECKLIST = 'CHK_PNEUSTE'
      ) AS CHK_PNEUSTE,
  (SELECT O.VALOR
      FROM OS_CHECKLIST O
      WHERE O.NUMERO_OS = $P{NUMERO_OS} 
      AND O.COD_EMPRESA = $P{COD_EMPRESA}
      AND O.ID_CHECKLIST = 'CHK_ESCAPAMENTO'
      ) AS CHK_ESCAPAMENTO
 from dual
),
qryAssinatura as (
select 1 as NUMERO_LINHA
       ,assinatura_entrada.assinatura as consultor_entrada
       ,assinatura_entrada.data_assinatura consultor_dt_entrada
from
(select moa.assinatura
        ,moa.data_assinatura from mob_os_assinatura moa
where moa.numero_os = $P{NUMERO_OS}
      and moa.cod_empresa = $P{COD_EMPRESA}
      and moa.aplicacao = 'R') assinatura_entrada
)
SELECT 
qryempresa.NOME AS qryempresa_NOME,
qryempresa.RUA AS qryempresa_RUA,
qryos.NUMERO_OS AS qryos_NUMERO_OS,
qryempresa.CEP AS qryempresa_CEP,
qryempresa.CODIGO_REVENDEDOR AS qryempresa_CODIGO_REVENDEDOR,
qryempresa.FONE AS qryempresa_FONE,
qryempresa.BAIRRO AS qryempresa_BAIRRO,
qryempresa.LGPD_MEMO AS qryempresa_LGPD_MEMO,
qryos.PRISMA AS qryos_PRISMA,
qryos.DATA_EMISSAO AS qryos_DATA_EMISSAO,
qryempresa.CIDADE AS qryempresa_CIDADE,
qryempresa.FAX AS qryempresa_FAX,
qryos.COR_EXTERNA AS qryos_COR_EXTERNA,
qryos.CHASSI AS qryos_CHASSI,
qryos.DESCRICAO_MODELO AS qryos_DESCRICAO_MODELO,
qryos.DATA_VENDA AS qryos_DATA_VENDA,
qryos.KM AS qryos_KM,
qryos.OBSERVACAO AS qryos_OBSERVACAO,
qryos.ANO AS qryos_ANO,
qryos.PLACA AS qryos_PLACA,
qryos.COD_REVENDEDOR AS qryos_COD_REVENDEDOR,
qryos.NOME_REVENDEDOR AS qryos_NOME_REVENDEDOR,
qryos.NOME_CLIENTE AS qryos_NOME_CLIENTE,
qryos.RUA_RES AS qryos_RUA_RES,
qryos.BAIRRO_RES AS qryos_BAIRRO_RES,
qryos.RG AS qryos_RG,
qryos.CNPJ_CPF AS qryos_CNPJ_CPF,
qryos.CIDADE_RES AS qryos_CIDADE_RES,
qryos.TELEFONE_COM AS qryos_TELEFONE_COM,
qryos.ENDERECO_ELETRONICO AS qryos_ENDERECO_ELETRONICO,
qryos.CEP_RES AS qryos_CEP_RES,
qryos.UF_RES AS qryos_UF_RES,
qryos.TELEFONE_CEL AS qryos_TELEFONE_CEL,
qryos.TELEFONE_RES AS qryos_TELEFONE_RES,
qryos.BAIRRO_FAT AS qryos_BAIRRO_FAT,
qryos.CEP_FAT AS qryos_CEP_FAT,
qryos.UF_FAT AS qryos_UF_FAT,
qryos.CIDADE_FAT AS qryos_CIDADE_FAT,
qryos.RUA_FAT AS qryos_RUA_FAT,
qryos.TIPO_OS AS qryos_TIPO_OS,
qryos.CLIENTE_AGUARDA AS qryos_CLIENTE_AGUARDA,
qryos.COMBUSTIVEL AS qryos_COMBUSTIVEL,
qryos.FORMA_PAGAMENTO as qryos_FORMA_PAGAMENTO,
qryos.OS_ASSINATURA as qruos_OS_ASSINATURA,
qryos.DATA_ENTREGA_CLIENTE as qryos_DATA_ENTREGA_CLIENTE,
qryos.ASSINATURA_ENTREGA_CLIENTE as qryos_ASS_ENTREGA_CLIENTE,
qryCheckList.CHK_PNEUSDD as qryCheckList_CHK_PNEUSDD,
qryCheckList.CHK_PNEUSDE as qryCheckList_CHK_PNEUSDE,
qryCheckList.CHK_PNEUSTD as qryCheckList_CHK_PNEUSTD,
qryCheckList.CHK_PNEUSTE as qryCheckList_CHK_PNEUSTE,
qryCheckList.CHK_ESCAPAMENTO as qryCheckList_CHK_ESCAPAMENTO,
qryAssinatura.consultor_entrada as qryAssina_consultor_entrada,
qryAssinatura.consultor_dt_entrada as qryAssina_consultor_dt_entrada
FROM qryOS, qryEmpresa, qryProdutivo, qryCheckList, qryAssinatura
WHERE qryOS.NUMERO_LINHA = qryEmpresa.NUMERO_LINHA (+)
      AND qryOS.NUMERO_LINHA = qryProdutivo.NUMERO_LINHA (+)
      AND qryOS.NUMERO_LINHA = qryCheckList.NUMERO_LINHA (+)
      AND qryOS.NUMERO_LINHA = qryAssinatura.NUMERO_LINHA (+)]]>
	</queryString>
	<field name="QRYEMPRESA_NOME" class="java.lang.String"/>
	<field name="QRYEMPRESA_RUA" class="java.lang.String"/>
	<field name="QRYOS_NUMERO_OS" class="java.lang.Double"/>
	<field name="QRYEMPRESA_CEP" class="java.lang.String"/>
	<field name="QRYEMPRESA_CODIGO_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYEMPRESA_FONE" class="java.lang.String"/>
	<field name="QRYEMPRESA_BAIRRO" class="java.lang.String"/>
	<field name="QRYEMPRESA_LGPD_MEMO" class="java.lang.String"/>
	<field name="QRYOS_PRISMA" class="java.lang.String"/>
	<field name="QRYOS_DATA_EMISSAO" class="java.sql.Timestamp"/>
	<field name="QRYEMPRESA_CIDADE" class="java.lang.String"/>
	<field name="QRYEMPRESA_FAX" class="java.lang.String"/>
	<field name="QRYOS_COR_EXTERNA" class="java.lang.String"/>
	<field name="QRYOS_CHASSI" class="java.lang.String"/>
	<field name="QRYOS_DESCRICAO_MODELO" class="java.lang.String"/>
	<field name="QRYOS_DATA_VENDA" class="java.sql.Timestamp"/>
	<field name="QRYOS_KM" class="java.lang.Double"/>
	<field name="QRYOS_OBSERVACAO" class="java.lang.String"/>
	<field name="QRYOS_ANO" class="java.lang.String"/>
	<field name="QRYOS_PLACA" class="java.lang.String"/>
	<field name="QRYOS_COD_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_REVENDEDOR" class="java.lang.String"/>
	<field name="QRYOS_NOME_CLIENTE" class="java.lang.String"/>
	<field name="QRYOS_RUA_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_RES" class="java.lang.String"/>
	<field name="QRYOS_RG" class="java.lang.String"/>
	<field name="QRYOS_CNPJ_CPF" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_COM" class="java.lang.String"/>
	<field name="QRYOS_ENDERECO_ELETRONICO" class="java.lang.String"/>
	<field name="QRYOS_CEP_RES" class="java.lang.String"/>
	<field name="QRYOS_UF_RES" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_CEL" class="java.lang.String"/>
	<field name="QRYOS_TELEFONE_RES" class="java.lang.String"/>
	<field name="QRYOS_BAIRRO_FAT" class="java.lang.String"/>
	<field name="QRYOS_CEP_FAT" class="java.lang.String"/>
	<field name="QRYOS_UF_FAT" class="java.lang.String"/>
	<field name="QRYOS_CIDADE_FAT" class="java.lang.String"/>
	<field name="QRYOS_RUA_FAT" class="java.lang.String"/>
	<field name="QRYOS_TIPO_OS" class="java.lang.Double"/>
	<field name="QRYOS_CLIENTE_AGUARDA" class="java.lang.String"/>
	<field name="QRYOS_COMBUSTIVEL" class="java.lang.Double"/>
	<field name="QRYOS_FORMA_PAGAMENTO" class="java.lang.Double"/>
	<field name="QRUOS_OS_ASSINATURA" class="java.awt.Image"/>
	<field name="QRYOS_DATA_ENTREGA_CLIENTE" class="java.sql.Timestamp"/>
	<field name="QRYOS_ASS_ENTREGA_CLIENTE" class="java.awt.Image"/>
	<field name="QRYCHECKLIST_CHK_PNEUSDD" class="java.lang.Double"/>
	<field name="QRYCHECKLIST_CHK_PNEUSDE" class="java.lang.Double"/>
	<field name="QRYCHECKLIST_CHK_PNEUSTD" class="java.lang.Double"/>
	<field name="QRYCHECKLIST_CHK_PNEUSTE" class="java.lang.Double"/>
	<field name="QRYCHECKLIST_CHK_ESCAPAMENTO" class="java.lang.Double"/>
	<field name="QRYASSINA_CONSULTOR_ENTRADA" class="java.awt.Image"/>
	<field name="QRYASSINA_CONSULTOR_DT_ENTRADA" class="java.sql.Timestamp"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="76">
			<frame>
				<reportElement x="0" y="0" width="555" height="76" uuid="2557f863-1c7f-4959-9f56-ad770dfdf5da"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="1" y="1" width="122" height="66" uuid="1798578a-f3fb-4961-8b26-f4bbb5c3f6ec"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice154010.png"]]></imageExpression>
				</image>
				<textField>
					<reportElement mode="Transparent" x="125" y="0" width="185" height="13" uuid="1e33d56f-2872-48ae-a8a5-91de9b6c6ae9"/>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_NOME}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="125" y="13" width="185" height="13" uuid="8766bf6c-d730-4613-85ee-709e10e49fec"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_RUA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="301" y="27" width="18" height="13" uuid="eabddabf-8ded-4094-a848-************"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="371" y="0" width="96" height="13" uuid="9cba20e0-41e8-4623-b5bd-9e97d5ff3325"/>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Ordem de Serviços nº]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="371" y="13" width="30" height="13" uuid="42af2262-56b7-4add-9566-5be1bbc90011"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Abertura:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="469" y="0" width="56" height="13" uuid="ded7162d-15e4-4007-8c34-0b27762d8059"/>
					<textElement textAlignment="Left">
						<font size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NUMERO_OS}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="320" y="27" width="41" height="13" uuid="00716f14-29ec-47be-896b-f1f03c9ec9a0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_CEP}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="218" y="27" width="4" height="13" uuid="1f2982d6-5fd3-4e08-8082-2e3935d6c984"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[-]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="371" y="27" width="17" height="13" uuid="537b1421-e18b-495f-8699-46a9854dd9ba"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tipo:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="197" y="54" width="39" height="13" uuid="095d692a-f831-4ce6-8e90-2d3c5d4d5a61"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_CODIGO_REVENDEDOR}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="124" y="54" width="70" height="13" uuid="cfcc15a7-e35e-468d-8825-02951314710c"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Cód. do Revendedor:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="140" y="40" width="51" height="13" uuid="1d95c904-e366-4b3d-bd74-44695b968900"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_FONE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="124" y="40" width="14" height="13" uuid="9f789990-f10a-4449-ac12-e10fac18b010"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tel.:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="125" y="27" width="92" height="13" uuid="10822a7b-ce69-4c71-9b7f-cff69438f372"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_BAIRRO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="510" y="13" width="31" height="13" uuid="9967f66f-bb41-4c94-a132-75a696052e05"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_PRISMA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="482" y="13" width="26" height="13" uuid="1255d98a-0133-4e2a-90f2-42c666917f2b"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Prisma:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="403" y="13" width="63" height="13" uuid="1ac23ec9-d3aa-48e8-aedc-446c2b951bdb"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="225" y="27" width="70" height="13" uuid="4bf0766a-49da-480d-85ca-71cbd3058485"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_CIDADE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="202" y="40" width="17" height="13" uuid="fe2863ce-29cf-4e44-890b-a97e2375e8e8"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[FAX.:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="221" y="40" width="51" height="13" uuid="22f18efd-8d7d-4772-a7bd-1cffe8c92da7"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_FAX}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="394" y="27" width="8" height="9" uuid="8ed91b95-ac8c-4e6d-b2a8-0e036e192b64"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TIPO_OS} == 0 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="404" y="27" width="23" height="13" uuid="e172dee5-dd09-4768-b137-2925ecefa267"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="445" y="27" width="29" height="13" uuid="b3dff1ec-c644-4840-91fa-b4c2b972655e"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Revisão]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="436" y="27" width="8" height="9" uuid="1af9449a-054f-4f12-8567-71d6b63d9eee"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TIPO_OS} == 1 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="487" y="27" width="23" height="13" uuid="11202bb3-0f43-4cb7-b7af-a782453ecddc"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Interna]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="478" y="27" width="8" height="9" uuid="3d15abbe-791a-451c-b9ee-437c91cf5553"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TIPO_OS} == 2 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="404" y="40" width="29" height="13" uuid="5038d536-12fc-471e-8537-1717fd548558"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Garantia]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="394" y="40" width="8" height="9" uuid="e177db6f-c19f-44ad-8220-d654656c61a5"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TIPO_OS} == 3 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="445" y="40" width="55" height="13" uuid="1a0610e5-bf0f-4d88-901c-3960f0fba1a0"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Funilaria/Pintura]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="436" y="40" width="8" height="9" uuid="ea6dedfb-72be-46e1-b29f-addcc1470161"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TIPO_OS} == 4 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="516" y="54" width="18" height="13" uuid="188f830f-0ade-4c6e-8c9f-239d8221a13f"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Pág.:]]></text>
				</staticText>
				<textField evaluationTime="Page">
					<reportElement mode="Transparent" x="536" y="54" width="12" height="13" uuid="2e901c32-7425-40c7-b66e-3ffc9e73c90d"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="394" y="53" width="8" height="9" uuid="74576b21-a2ec-4489-a402-b21ccb105e5c"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CLIENTE_AGUARDA}.equals("S")? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="403" y="53" width="57" height="13" uuid="5bab4a55-2aeb-4ba0-8320-398cd5c008f8"/>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Cliente Aguarda]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="376" splitType="Stretch">
			<frame>
				<reportElement x="0" y="1" width="555" height="375" uuid="4f075301-b490-4db5-ac92-f755b5f87937">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<rectangle>
					<reportElement mode="Transparent" x="0" y="93" width="555" height="80" uuid="467e88e9-0941-4e12-baa6-c0b833f3d3ef">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
				</rectangle>
				<staticText>
					<reportElement mode="Transparent" x="1" y="0" width="114" height="13" uuid="24ed18ff-5644-4d0f-8f8e-325555a430af"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[DADOS DO CLIENTE]]></text>
				</staticText>
				<rectangle>
					<reportElement mode="Transparent" x="0" y="13" width="555" height="68" uuid="77de771d-66b5-462f-b52c-6dc9909270a0"/>
				</rectangle>
				<staticText>
					<reportElement mode="Transparent" x="5" y="19" width="26" height="13" uuid="43d36bfc-3ea3-4d27-bb05-76773bc4fb70"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="33" width="40" height="13" uuid="33ee7dfc-0771-4d37-a428-172447a214e4"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Endereço:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="280" y="19" width="44" height="13" uuid="1aa1093b-b1a2-4751-aaa2-c93474425c2c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CNPJ/CPF:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="47" width="31" height="13" uuid="6e41d19f-a75b-495e-844b-32a63d3221cf"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="62" width="37" height="13" uuid="bb91d2df-071e-423f-a762-87c96efb038d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Telefone:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="336" y="33" width="27" height="13" uuid="1c0b2c4a-cae4-4884-bb2c-938f651f5015"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="410" y="19" width="16" height="13" uuid="70770acf-7a3b-4eb7-ba48-106786a3ef18"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[RG:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="208" y="47" width="15" height="13" uuid="c9f26c86-4eb9-4caf-b9a2-21f373c38d95"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[UF:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="252" y="47" width="22" height="13" uuid="8165ae9d-0227-4857-b8cb-1c06a3270919"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="336" y="47" width="28" height="13" uuid="d63d1862-81ec-45c3-87a1-c48eac854bbe"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[E-mail:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="321" y="62" width="41" height="13" uuid="0ea1b934-006e-4893-ad81-3a878cb1e8b7"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Comercial:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="196" y="62" width="30" height="13" uuid="43261101-9f31-45f2-8e3f-98315e469bfc"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Celular:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="53" y="62" width="47" height="13" uuid="16450c19-a7a6-4112-bc47-61b44a561333"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Residencial:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="1" y="80" width="156" height="13" uuid="d00ba64d-b858-4743-91a9-be83f827396f"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[DADOS PARA FATURAMENTO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="112" width="40" height="13" uuid="d92d5459-67f0-49e7-b56f-c879d425a4e2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Endereço:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="98" width="26" height="13" uuid="867b36fe-d46c-4e25-9254-305695e316bf"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="125" width="31" height="13" uuid="2856279f-83c7-4697-b173-8cfdc81303d3"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="154" width="78" height="13" uuid="77ab885b-3116-4e9b-af2b-82633ab80b4f"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo de Pagamento:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="139" width="37" height="13" uuid="ce4ac45b-3962-4b9b-92bb-d4f7f9ca18b1"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Telefone:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="53" y="139" width="47" height="13" uuid="36b94153-8ea7-46ec-8b51-de661b2da5e9"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Residencial:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="196" y="139" width="30" height="13" uuid="820e2a8b-e705-4272-8e9f-2b62b7d24cf5"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Celular:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="321" y="139" width="41" height="13" uuid="d6789491-4218-41a3-8f69-91d7e998408d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Comercial:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="208" y="125" width="15" height="13" uuid="f0111bf3-c1dc-4d17-8164-236391a01282"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[UF:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="280" y="98" width="44" height="13" uuid="1fec5977-b527-411c-8286-85bda5bb5448"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CNPJ/CPF:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="252" y="125" width="22" height="13" uuid="507829be-363a-482c-bf6a-439b535a17ed"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CEP:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="336" y="125" width="28" height="13" uuid="8b081e59-2eef-4abd-936c-0afe26a85413"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[E-mail:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="336" y="112" width="27" height="13" uuid="7c738dfd-1e8b-4761-976e-b708ac8bf064"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Bairro:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="410" y="98" width="16" height="13" uuid="eee113d0-159e-4d58-9c52-2322cb090e7a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[RG:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="84" y="154" width="8" height="9" uuid="c8d1e86b-c24c-4605-9f3d-728a09b1fc57"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_FORMA_PAGAMENTO} == 1 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="94" y="154" width="31" height="13" uuid="6fefb368-64b4-4cc7-aca5-46a30bfb9a7b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cartão]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="135" y="154" width="8" height="9" uuid="c3f2c17c-9989-4cb8-b9f5-7359d795c6c2"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_FORMA_PAGAMENTO} == 2 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="144" y="154" width="36" height="13" uuid="eb532e61-42bc-4c91-89ac-57131f4c6780"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cheque]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="185" y="154" width="8" height="9" uuid="cf9497df-97a5-4920-be83-c6212f1abad9"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_FORMA_PAGAMENTO} == 3 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="195" y="154" width="36" height="13" uuid="a9435257-701a-40ad-b89a-259b8dae1338"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Dinheiro]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="297" y="154" width="31" height="13" uuid="138b261c-a3ae-4667-b101-fbf719921957"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Outros:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="287" y="154" width="8" height="9" uuid="7c638ee6-8799-4e92-a980-177f2f4efd27"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_FORMA_PAGAMENTO} == 0 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="246" y="154" width="38" height="13" uuid="ec074e83-c9d3-4caf-9802-78107227ddca"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Faturado]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="236" y="154" width="8" height="9" uuid="ed3219ff-d0c3-4a88-b166-5a841d1b0af9"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_FORMA_PAGAMENTO} == 4 ? "X":" "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="329" y="166" width="139" height="1" uuid="08dea1b0-d081-45cf-8a01-21b997c94f5f"/>
				</line>
				<rectangle>
					<reportElement mode="Transparent" x="0" y="186" width="555" height="189" uuid="36b6f453-6fa5-42fb-8fa0-258053c4e3f3">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
				</rectangle>
				<staticText>
					<reportElement mode="Transparent" x="1" y="173" width="113" height="13" uuid="a7c1e01b-06bd-45e8-992f-3f09f1bceb3c"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[DADOS DO VEÍCULO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="316" width="70" height="13" uuid="dd8626bb-11cf-4139-92ae-165160a014e4"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome Revendedor:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="298" width="78" height="13" uuid="bf832dff-456e-47fc-a880-04f7e3f3a899"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cód. Rev. de Origem:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="281" width="24" height="13" uuid="88de14a4-31fc-42ab-af9c-c370f72e3d90"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Placa:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="263" width="41" height="13" uuid="4be6c916-086d-48df-808a-1a889c562ae6"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Ano Mod.:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="245" width="64" height="13" uuid="60662a9c-e4af-46e1-ab8f-6ece1cd2f2c0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data da Venda:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="227" width="18" height="13" uuid="11942594-98d7-4190-aec7-94556bb4b9c2"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Cor:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="209" width="31" height="13" uuid="9b58dd01-5f24-4d16-9306-2a75d2dc2372"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Chassi:]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="5" y="191" width="32" height="13" uuid="5772e130-2348-49d2-b07d-e51722b4bf86"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Modelo:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="23" y="227" width="91" height="13" uuid="93dcd10f-a367-4da2-9d9d-ff1b9778557c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_COR_EXTERNA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="38" y="209" width="89" height="13" uuid="c7d23c62-2f1c-445d-9d35-2d57ab404db6"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CHASSI}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="38" y="191" width="136" height="13" uuid="e516e241-ab31-4b71-a777-8f95cd56f397"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DESCRICAO_MODELO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="70" y="245" width="48" height="13" uuid="ce9855e7-8730-4ad9-a34e-e4426c30810a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_DATA_VENDA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="122" y="227" width="17" height="13" uuid="02a2dbd4-53a2-43be-9684-a549824603e8"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[KM:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="139" y="227" width="36" height="13" uuid="e8aa8cb5-477a-4975-8211-c68af2827038"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_KM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="420" y="186" width="129" height="21" uuid="9b6b9c7b-2a94-42ae-99ea-cd2703ce9f98"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[TODAS AS AVARIAS DEVEM SER CLARAMENTE MARCADAS]]></text>
				</staticText>
				<rectangle>
					<reportElement mode="Transparent" x="5" y="334" width="386" height="35" uuid="717c16f2-232a-4af7-812c-049c61582d72"/>
				</rectangle>
				<staticText>
					<reportElement mode="Transparent" x="8" y="337" width="17" height="13" uuid="6316fc79-622c-403d-80ed-90b79aa69b03"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Obs:]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="28" y="337" width="360" height="29" uuid="d7fef0bf-a0c8-4571-b0e0-7052c46539cb"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_OBSERVACAO}]]></textFieldExpression>
				</textField>
				<image>
					<reportElement x="404" y="220" width="147" height="150" uuid="5abfa71f-e335-41e4-bbf5-dc1d0ec2c081"/>
					<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice4600464.png"]]></imageExpression>
				</image>
				<staticText>
					<reportElement mode="Transparent" x="419" y="207" width="129" height="12" uuid="73894c58-f492-46b8-91b6-0ba68d5cc8bd"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[X - Amassado               O - Risco]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="46" y="263" width="31" height="13" uuid="4f773761-4b1d-4819-ad0e-888fe51cad00"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_ANO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="30" y="281" width="45" height="13" uuid="ebd30ef9-555f-4812-8183-d0d6e59aad02"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_PLACA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="87" y="298" width="70" height="13" uuid="62b00c35-56fa-4de9-a55c-a49abd952a3d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_COD_REVENDEDOR}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="79" y="316" width="91" height="13" uuid="127ef4f5-54cf-4e0c-b6c5-e5c6c196d98d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NOME_REVENDEDOR}]]></textFieldExpression>
				</textField>
				<image scaleImage="RealHeight">
					<reportElement x="312" y="209" width="73" height="43" uuid="c1ce36b7-b1b2-439a-9878-ce416de5748f"/>
					<imageExpression><![CDATA[$F{QRYOS_COMBUSTIVEL} < 19 ? $P{DIR_IMAGE_LOGO} + "crmservice4600458.png":
$F{QRYOS_COMBUSTIVEL} < 39 ? $P{DIR_IMAGE_LOGO} + "crmservice4600459.png":
$F{QRYOS_COMBUSTIVEL} < 59 ? $P{DIR_IMAGE_LOGO} + "crmservice4600460.png":
$F{QRYOS_COMBUSTIVEL} < 79 ? $P{DIR_IMAGE_LOGO} + "crmservice4600461.png":
$P{DIR_IMAGE_LOGO} + "crmservice4600462.png"]]></imageExpression>
				</image>
				<textField>
					<reportElement mode="Transparent" x="34" y="19" width="239" height="13" uuid="14d66756-e5f7-4ffa-892f-adf7052663bc"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NOME_CLIENTE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="49" y="33" width="262" height="13" uuid="cf4d8322-653e-48c0-aa65-42263cbf297f"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_RUA_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="364" y="33" width="180" height="13" uuid="de4da2b4-423c-41f7-bad0-1483177bc75d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_BAIRRO_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="429" y="19" width="125" height="13" uuid="a48aa1bd-82db-44c0-902a-afcd3555d28b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_RG}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="325" y="19" width="82" height="13" uuid="c8128392-967d-49f1-a172-c7ddb9a68b85"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CNPJ_CPF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="37" y="47" width="150" height="13" uuid="ef40bca7-0c79-4933-a1de-0cd03624557b"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CIDADE_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="364" y="62" width="66" height="13" uuid="b73927d2-e2f7-49fd-b5c9-c6a9adc471c0"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TELEFONE_COM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="364" y="47" width="181" height="13" uuid="cc51b272-85cd-4490-a503-7461ad8e3c27"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="272" y="47" width="46" height="13" uuid="e73f20b6-a639-4c43-b504-28cdd5bdd210"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CEP_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="224" y="47" width="18" height="13" uuid="0805615c-96ed-4e9f-9755-95d1b21da17d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_UF_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="227" y="62" width="66" height="13" uuid="b2601768-f5bc-460e-a5f7-d02b315b2a9f"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TELEFONE_CEL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="100" y="62" width="66" height="13" uuid="9271aa4c-eb94-4c69-801b-f3615fce1ebd"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TELEFONE_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="34" y="97" width="239" height="13" uuid="2806f717-dc6e-4682-9ac5-f5908fa14890"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_NOME_CLIENTE}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="325" y="97" width="82" height="13" uuid="fc6319bd-8655-45e3-acb8-57294103ae3d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CNPJ_CPF}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="429" y="97" width="125" height="13" uuid="ecd29d6f-fdd3-449d-8ab9-ac842201de3c"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_RG}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="364" y="111" width="180" height="13" uuid="b4fabcbc-8143-4c22-a6d9-7b83093b077e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_BAIRRO_FAT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="364" y="125" width="181" height="13" uuid="345a3b44-dc37-4b6b-a469-a521a921ac12"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_ENDERECO_ELETRONICO}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="364" y="139" width="66" height="13" uuid="4703ca41-09c4-43e2-91f4-9db96ebe6784"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TELEFONE_COM}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="227" y="139" width="66" height="13" uuid="f6e56073-7dca-44a3-b3fa-bb20f12e797e"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TELEFONE_CEL}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="272" y="125" width="46" height="13" uuid="13014f8c-b092-4b5b-9d98-f0326adc2542"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CEP_FAT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="224" y="125" width="18" height="13" uuid="d69f7e9f-87e6-4fc9-b8e2-a57f0b8a1e9d"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_UF_FAT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="37" y="125" width="150" height="13" uuid="da912cf7-46ef-47f8-b74d-e8edc2320b3a"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_CIDADE_FAT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="100" y="139" width="66" height="13" uuid="2ae593e1-d115-4238-bdc5-762f15729ad4"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_TELEFONE_RES}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="49" y="111" width="262" height="13" uuid="05304098-a46c-49b5-9a34-9c2e13598647"/>
					<textElement textAlignment="Left">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYOS_RUA_FAT}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="180" y="304" width="20" height="10" uuid="79377ee6-625d-4ca2-bada-7d60760a4e7d"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[T.E.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="294" width="20" height="10" uuid="4f488e6b-c937-4a11-94e5-06f892337782"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[D.D.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="246" y="274" width="23" height="10" uuid="0d821715-42bd-4357-977f-4f1fdb62cbcd"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Ruim]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="274" width="20" height="10" uuid="0ff4d432-70d6-4dc0-8c73-c871da4b333b"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[REF.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="200" y="274" width="23" height="10" uuid="1f7b5b7c-8395-41b5-bda6-05cb64429042"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Bom]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="264" width="89" height="10" uuid="308415e0-913b-4282-aa6d-cb418d365f23"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[PNEUS]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="284" width="20" height="10" uuid="21a06f84-2b2a-45b5-a76e-84188b8a9276"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[D.E.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="314" width="20" height="10" uuid="af6ac3fc-**************-ea389170d706"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[T.D.]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="223" y="274" width="23" height="10" uuid="99b41172-5001-48de-9033-777688604d15"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Médio]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="200" y="284" width="23" height="10" uuid="859c7581-fc92-428d-9651-29aa0539e870"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSDE}== 1 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="223" y="284" width="23" height="10" uuid="ecfe5a30-9d42-46e0-a668-bb2aaab6c980"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSDE}== 2 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="246" y="284" width="23" height="10" uuid="25e657eb-68e6-4f47-9ff8-28312596f860"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSDE}== 3 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="223" y="294" width="23" height="10" uuid="fa725f9d-5fc0-4279-883d-13661eaebee9"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSDD} == 2 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="246" y="294" width="23" height="10" uuid="6a8c7135-e1cc-430e-84d1-c22cb933d720"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSDD} == 3 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="223" y="304" width="23" height="10" uuid="d1d24417-73ba-4435-8f8a-1ea60318fd87"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSTE}== 2 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="246" y="304" width="23" height="10" uuid="bf315939-122c-45db-8eb0-f473420e9ff4"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSTE}== 3 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="223" y="314" width="23" height="10" uuid="3620a7f1-5f2f-426d-99bd-9efc63b98cfa"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSTD}== 2 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="246" y="314" width="23" height="10" uuid="4e3d61d7-e682-4c7a-bb92-47a8ced74ff3"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSTD}== 3 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="200" y="314" width="23" height="10" uuid="0a3e946e-ea28-461c-bdc8-32bc83b391c1"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSTD}== 1 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="200" y="304" width="23" height="10" uuid="9f544f02-9e69-4df3-9dee-24f085bdaf32"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSTE}== 1 ? "X" : " "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="200" y="294" width="23" height="10" uuid="0cf889b6-5495-4b84-8355-0784c6303e80"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_PNEUSDD} == 1 ? "X" : " "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="313" y="309" width="55" height="10" uuid="2be042b6-87d3-4a06-9497-384b84ccce24"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Ruim]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="368" y="299" width="11" height="10" uuid="24e54631-521b-48cd-ad8f-9ba0bf7e6f7f"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_ESCAPAMENTO} == 2 ? "X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="368" y="309" width="11" height="10" uuid="d0771da6-b904-4f72-a5aa-8d9f5a738457"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_ESCAPAMENTO} == 3 ? "X":" "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="368" y="289" width="11" height="10" uuid="b25c1ffd-2b91-4dcb-997c-32d46ad9280c"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYCHECKLIST_CHK_ESCAPAMENTO} == 1 ? "X":" "]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="313" y="279" width="66" height="10" uuid="b07eb7ea-51b1-4f85-959b-5af858d1f9f7"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[ESCAPAMENTO]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="313" y="289" width="55" height="10" uuid="fdc0d077-111e-4da1-94b2-f31a4c5c1ce2"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Bom]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="313" y="299" width="55" height="10" uuid="e8cedf93-63b8-4aba-a962-f9f78748ad30"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Médio]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="227" y="249" width="11" height="10" uuid="2ce146c3-6747-43f0-9a9f-fc953a47b9d3"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="290" y="249" width="11" height="10" uuid="c6a1339f-ff42-4a88-9d79-4c592e6c551a"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="227" y="209" width="11" height="10" uuid="a6fa8ce2-1018-4d31-820a-c67b8c40587e"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="227" y="219" width="11" height="10" uuid="6affc5b5-b4e0-40eb-babe-44d01c7437ab"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="290" y="229" width="11" height="10" uuid="1ab19e64-e1d8-4e47-a131-eca7617012d8"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="243" y="199" width="47" height="10" uuid="4faf20e1-8239-4364-ad78-c8fc7fc50030"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Macaco]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="227" y="229" width="11" height="10" uuid="b825fe3f-9bb7-4867-a92f-11875b23ce23"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="243" y="209" width="47" height="10" uuid="266f935e-1515-4f5a-a127-31730aef437e"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Manual]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="243" y="229" width="47" height="10" uuid="e720824c-61c0-4b04-8eed-008d7290e907"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Triângulo]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="190" width="121" height="10" uuid="97861b5d-4e44-44c4-992d-02289eaaf202"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[CHECK LIST]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="290" y="239" width="11" height="10" uuid="4856aa2a-a3ad-407c-b72e-6f4bed48ba27"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="243" y="239" width="47" height="10" uuid="8530b81b-2a3a-4bbb-b5c8-4d8ef529de7a"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Radio/CD]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="243" y="249" width="47" height="10" uuid="0444dd80-1f9b-41af-9433-412f2fbfe80d"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="290" y="209" width="11" height="10" uuid="1fb99049-1ffd-4f52-8b13-4310d79a1c9e"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="227" y="199" width="11" height="10" uuid="7e08ddcf-021f-403c-a123-e01a86ebb2e8"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="290" y="199" width="11" height="10" uuid="5e09bb55-f863-4591-a427-22679121fcae"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<textField>
					<reportElement mode="Transparent" x="290" y="219" width="11" height="10" uuid="7f869d85-094b-4e42-acad-3ec9b5025411"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="243" y="219" width="47" height="10" uuid="68fb9937-dafc-4736-a3f9-46377e382e4e"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Tapetes]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="227" y="239" width="11" height="10" uuid="098c501c-4113-4f28-a2af-d84df9493f28"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7"/>
					</textElement>
				</textField>
				<staticText>
					<reportElement mode="Transparent" x="180" y="199" width="47" height="10" uuid="34c23d92-e578-4fcb-a01b-89d535b57067"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Antena]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="249" width="47" height="10" uuid="7909ed8f-b3b3-44bf-b7ba-a97e4842fcc3"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Ferramentas]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="239" width="47" height="10" uuid="6fe4a18c-a68f-4a02-a0ff-9ddffc366509"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Extintor]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="229" width="47" height="10" uuid="d5f88292-c6b0-41b3-b1e9-6e53a69ccba8"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Estepe]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="219" width="47" height="10" uuid="43ed15e9-11e8-4aaa-9e83-4478d1b4805c"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Documento]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="180" y="209" width="47" height="10" uuid="83134ef1-f9e7-408b-a2a4-9bfaff553f54"/>
					<box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Calotas]]></text>
				</staticText>
			</frame>
		</band>
		<band height="43" splitType="Immediate">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="555" height="43" isRemoveLineWhenBlank="true" uuid="224a3bae-d0a2-447c-b460-28ffae916958">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiSubReclamacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="44">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="555" height="44" isRemoveLineWhenBlank="true" uuid="a607d174-7f04-4b8f-8540-97510119dca9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiSubServicos.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="42">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="555" height="42" isRemoveLineWhenBlank="true" uuid="3bb7df6a-7099-4588-9af3-5056dfde5e13">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiSubPecas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="53" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement stretchType="ContainerBottom" x="0" y="0" width="555" height="53" isRemoveLineWhenBlank="true" uuid="9c03ed0d-e0ef-4f0d-bcdc-e27540b18a19">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<subreportParameter name="NUMERO_OS">
					<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_EMPRESA">
					<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "OsHyundaiSubFechamento.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band height="148">
			<frame>
				<reportElement x="0" y="0" width="555" height="148" uuid="6d0015c3-6d26-4561-ae96-c7920050899b">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement mode="Transparent" x="0" y="0" width="212" height="14" uuid="b60dbc83-4314-47d7-9e5d-a68860b61128"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="7" isBold="false"/>
					</textElement>
					<text><![CDATA[Lei Geral de Proteção de Dados]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="0" y="14" width="212" height="132" uuid="4805794c-4d19-4bb5-9e8d-0661dfd0eade"/>
					<box topPadding="0" leftPadding="5" bottomPadding="5" rightPadding="3">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Justified" verticalAlignment="Top">
						<font size="7" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{QRYEMPRESA_LGPD_MEMO}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement positionType="Float" x="222" y="0" width="333" height="51" uuid="0cb3616e-ed11-42ff-813e-a7dd09b97be3"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<staticText>
						<reportElement mode="Transparent" x="1" y="2" width="328" height="24" uuid="faa4d2e7-3221-4dbc-8acb-674c4b7043d0"/>
						<box topPadding="1" leftPadding="3"/>
						<textElement textAlignment="Justified" verticalAlignment="Top">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Autorizo a revenda a receber o veículo acima para executar os serviços descritos nesta Ordem de Serviços, bem como o necessário teste de rua com o veículo.]]></text>
					</staticText>
					<image hAlign="Center" vAlign="Middle" isUsingCache="false">
						<reportElement x="7" y="22" width="162" height="18" uuid="5b43ffff-1eb3-4d76-bbe1-e7ba6642e5d3"/>
						<imageExpression><![CDATA[$F{QRUOS_OS_ASSINATURA}]]></imageExpression>
					</image>
					<staticText>
						<reportElement mode="Transparent" x="6" y="41" width="163" height="10" uuid="d8cde91b-c136-4e46-be99-306942ac4f32"/>
						<box>
							<topPen lineWidth="1.0"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[ASSINATURA DO CLIENTE]]></text>
					</staticText>
					<textField pattern="dd/MM/yyyy HH:mm">
						<reportElement mode="Transparent" x="179" y="25" width="107" height="13" uuid="e5c14686-2397-41f9-8416-1c4e8c681df9"/>
						<textElement textAlignment="Center" verticalAlignment="Bottom">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="179" y="38" width="107" height="13" uuid="5dd7b3bb-35f9-4af5-ac10-2a62f6c78cae">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7"/>
						</textElement>
						<text><![CDATA[DATA DE ENTRADA]]></text>
					</staticText>
				</frame>
				<frame>
					<reportElement x="222" y="53" width="333" height="51" uuid="86c778aa-ffdc-4658-a66a-b5284bfcd4f1"/>
					<rectangle>
						<reportElement mode="Transparent" x="0" y="0" width="333" height="51" uuid="eab78123-f3a9-4276-bf56-bb6f9467a11b"/>
					</rectangle>
					<staticText>
						<reportElement mode="Transparent" x="1" y="1" width="328" height="24" uuid="8ad06186-72b0-4749-b525-ff3e0f366be2"/>
						<box topPadding="2" leftPadding="3"/>
						<textElement textAlignment="Justified">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Declaro ter recebido o veículo acima descrito em perfeitas condições devidamente reparado, no estado original da entrega com todos seus pertences e equipamentos]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="6" y="41" width="163" height="10" uuid="f4583c90-22a0-42f1-b441-21f34d1f6e81"/>
						<box>
							<topPen lineWidth="1.0"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[ASSINATURA DO CLIENTE]]></text>
					</staticText>
					<staticText>
						<reportElement x="180" y="37" width="106" height="13" uuid="685f49da-e922-48b0-a5bf-1656e9655c60">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7"/>
						</textElement>
						<text><![CDATA[DATA DE SAÍDA]]></text>
					</staticText>
					<image hAlign="Center" vAlign="Middle" isUsingCache="false">
						<reportElement x="7" y="22" width="162" height="18" uuid="d71d401d-df93-4e33-81ae-780311e8bfb5"/>
						<imageExpression><![CDATA[$F{QRYOS_ASS_ENTREGA_CLIENTE}]]></imageExpression>
					</image>
					<textField pattern="dd/MM/yyyy HH:mm">
						<reportElement mode="Transparent" x="180" y="23" width="107" height="13" uuid="83b1486d-d125-4e2f-ab9b-afbb8ec47461"/>
						<textElement textAlignment="Center" verticalAlignment="Bottom">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{QRYOS_DATA_ENTREGA_CLIENTE}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="222" y="106" width="333" height="41" uuid="585b5655-779e-4079-b207-6a56e305c866"/>
					<rectangle>
						<reportElement mode="Transparent" x="0" y="0" width="333" height="41" uuid="20ffe0b3-a079-4777-af97-8c97596e693b">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
					</rectangle>
					<staticText>
						<reportElement mode="Transparent" x="6" y="29" width="163" height="11" uuid="bd3001bb-f273-4a29-98b2-d839cbd17f0a"/>
						<box>
							<topPen lineWidth="1.0"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[ASSINATURA DO CONSULTOR]]></text>
					</staticText>
					<staticText>
						<reportElement mode="Transparent" x="1" y="1" width="328" height="13" uuid="4ba2488f-3cf9-4700-b197-84f926c19e06"/>
						<textElement textAlignment="Center">
							<font size="7" isBold="false"/>
						</textElement>
						<text><![CDATA[Nome e assinatura do Consultor]]></text>
					</staticText>
					<image hAlign="Center" vAlign="Middle" isUsingCache="false">
						<reportElement x="7" y="10" width="162" height="18" uuid="98612cc0-8cb0-4fd8-b20f-58727437e95a"/>
						<imageExpression><![CDATA[$F{QRYASSINA_CONSULTOR_ENTRADA}]]></imageExpression>
					</image>
					<textField pattern="dd/MM/yyyy">
						<reportElement mode="Transparent" x="179" y="14" width="107" height="13" uuid="bc1b4007-addd-4313-b36a-a7a2f7905886"/>
						<textElement textAlignment="Center" verticalAlignment="Bottom">
							<font size="7" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{QRYOS_DATA_EMISSAO}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="179" y="27" width="107" height="13" uuid="83cffb43-6df5-4040-a9de-cac7c4f6f5f7">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="1.0"/>
						</box>
						<textElement textAlignment="Center">
							<font size="7"/>
						</textElement>
						<text><![CDATA[DATA DE ENTRADA]]></text>
					</staticText>
				</frame>
			</frame>
		</band>
	</columnFooter>
</jasperReport>
