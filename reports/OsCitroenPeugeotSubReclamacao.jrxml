<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsCitroenPeugeotSubReclamacao" pageWidth="555" pageHeight="145" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="d760b610-66b4-42df-8551-447eb20ebfd0">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Style1" isDefault="true" isBlankWhenNull="true"/>
	<style name="Cor1" style="Style1" mode="Opaque" forecolor="#FFFFFF" backcolor="#0076A9">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN") )]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#F57523" pattern=""/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#001F56"/>
		</conditionalStyle>
	</style>
	<style name="Cor3" style="Style1" mode="Opaque" forecolor="#231F20" backcolor="#CCE3ED">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{COPIA_CLIENTE}.equals("N") && $P{MARCA}.equals("CITROEN"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#FDE3D3"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($P{MARCA}.equals("PEUGEOT"))]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#EFEFF3"/>
		</conditionalStyle>
	</style>
	<style name="alternateStyle" style="Cor3" mode="Opaque" forecolor="#000000">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 1)]]></conditionExpression>
			<style mode="Opaque" forecolor="#000000" backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COPIA_CLIENTE" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["S"]]></defaultValueExpression>
	</parameter>
	<parameter name="MARCA" class="java.lang.String">
		<defaultValueExpression><![CDATA["CITROEN"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[WITH Q_ITENS AS (SELECT
       OSO.COD_EMPRESA,
       OSO.NUMERO_OS,
       0 AS COD_OS_AGENDA,
       OSO.ITEM,
       (OSO.DESCRICAO || '-' || SRV.DESCRICAO_SERVICO) AS DESCRICAO,
       NVL(OSS.PRECO_VENDA,0) AS PRECO_VENDA,
       DECODE(OSS.STATUS,2,'X','') AS STATUS_SR
  FROM OS_ORIGINAL OSO, OS_SERVICOS OSS, SERVICOS SRV
 WHERE (OSS.COD_EMPRESA (+) = OSO.COD_EMPRESA)
   AND (OSS.NUMERO_OS (+) = OSO.NUMERO_OS)
   AND (OSS.ITEM (+) = OSO.ITEM)
   AND (SRV.COD_SERVICO (+) = OSS.COD_SERVICO)   
   AND OSO.COD_EMPRESA = $P{COD_EMPRESA}
   AND OSO.NUMERO_OS = $P{NUMERO_OS}
 ORDER BY OSO.ITEM),
 
LINHAS_EXTRAS AS (
  SELECT 
       NULL AS COD_EMPRESA,
       NULL AS NUMERO_OS,
       NULL AS COD_OS_AGENDA,
       NULL AS ITEM,
       NULL AS DESCRICAO,
       NULL AS PRECO_VENDA,
       NULL AS STATUS_SR
  FROM (SELECT COUNT(*) AS QUANTIDADE FROM Q_ITENS) QUANTIDADE_ITENS
  WHERE (CEIL(QUANTIDADE_ITENS.QUANTIDADE/10)*10 - QUANTIDADE_ITENS.QUANTIDADE > 0)
  CONNECT BY LEVEL <= (CEIL(QUANTIDADE_ITENS.QUANTIDADE/10)*10 - QUANTIDADE_ITENS.QUANTIDADE)
)

SELECT Q_ITENS.*
FROM Q_ITENS
UNION ALL
SELECT LINHAS_EXTRAS.*
FROM LINHAS_EXTRAS]]>
	</queryString>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="DESCRICAO" class="java.lang.String"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="STATUS_SR" class="java.lang.String"/>
	<variable name="MOSTRAR_HEADER_COLUMN" class="java.lang.String" resetType="None">
		<variableExpression><![CDATA["N"]]></variableExpression>
		<initialValueExpression><![CDATA["N"]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="15">
			<staticText>
				<reportElement style="Cor1" mode="Opaque" x="0" y="0" width="555" height="15" uuid="9db3d874-03f4-4c17-94ea-180b109f4033">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[SOLICITAÇÃO DO CLIENTE / DESCRIÇÃO DO SERVIÇO                                                            ORÇAMENTO]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement key="" style="alternateStyle" mode="Opaque" x="396" y="0" width="136" height="13" uuid="06e98c84-73e0-4f95-8a8d-6c9a6298ad17">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="alternateStyle" mode="Opaque" x="536" y="0" width="19" height="13" uuid="94008d0e-650c-4cb7-963c-dc429f4ea257"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="" style="alternateStyle" mode="Opaque" x="0" y="0" width="392" height="13" uuid="e6e62d4d-53da-4482-8374-266182d50a71">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="4" bottomPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
