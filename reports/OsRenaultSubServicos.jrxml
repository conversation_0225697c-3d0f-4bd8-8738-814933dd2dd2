<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsRenaultSubServicos" pageWidth="416" pageHeight="120" whenNoDataType="AllSectionsNoDetail" columnWidth="416" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="473"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="526"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH Q_SERVICOS AS
 (SELECT ROWNUM AS SEQITEM,
         O.COD_SERVICO,
         S.DESCRICAO_SERVICO,
         S.TEMPO_PADRAO
    FROM OS_SERVICOS O, SERVICOS S
   WHERE O.COD_SERVICO = S.COD_SERVICO
     AND ((O.NUMERO_OS = $P{NUMERO_OS}) AND (O.COD_EMPRESA = $P{COD_EMPRESA}))
  
   ORDER BY O.NUMERO_OS),

Q_OS_ORIGINAL AS
 (SELECT SUBSTR(TO_CHAR(OS_ORIGINAL.ITEM + 100), 2, 2) AS ITEM,
         OS_ORIGINAL.DESCRICAO,
         OS_ORIGINAL.COD_GWM_RECLAMACAO
    FROM OS_ORIGINAL
   WHERE ((OS_ORIGINAL.NUMERO_OS = $P{NUMERO_OS}) AND
         (OS_ORIGINAL.COD_EMPRESA = $P{COD_EMPRESA}))
  
   ORDER BY ITEM),

TENHO AS
 (SELECT COUNT(*) AS LINHAS FROM Q_SERVICOS),

NUMERO_PAGINAS AS
 (SELECT (MAX(TOT)) AS MAX_PAGINAS
    FROM (SELECT CEIL(COUNT(*)/12) AS TOT
            FROM Q_SERVICOS
          UNION ALL
          SELECT CEIL(COUNT(*)/6) AS TOT FROM Q_OS_ORIGINAL)),

Q_NULLROWS AS
 (SELECT NULL AS SEQITEM,
         NULL AS COD_SERVICO,
         NULL AS DESCRICAO_SERVICO,
         NULL AS TEMPO_PADRAO
    FROM DUAL, TENHO, NUMERO_PAGINAS
   WHERE NUMERO_PAGINAS.MAX_PAGINAS * 12 - TENHO.LINHAS > 0
  CONNECT BY LEVEL <= NUMERO_PAGINAS.MAX_PAGINAS * 12  - TENHO.LINHAS)

SELECT 
       S.*
  FROM Q_SERVICOS S
UNION ALL
SELECT D.*
  FROM Q_NULLROWS D]]>
	</queryString>
	<field name="SEQITEM" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="SEQITEM"/>
		<property name="com.jaspersoft.studio.field.label" value="SEQITEM"/>
	</field>
	<field name="COD_SERVICO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="COD_SERVICO"/>
		<property name="com.jaspersoft.studio.field.label" value="COD_SERVICO"/>
	</field>
	<field name="DESCRICAO_SERVICO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="DESCRICAO_SERVICO"/>
		<property name="com.jaspersoft.studio.field.label" value="DESCRICAO_SERVICO"/>
	</field>
	<field name="TEMPO_PADRAO" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="TEMPO_PADRAO"/>
		<property name="com.jaspersoft.studio.field.label" value="TEMPO_PADRAO"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="12" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField>
				<reportElement mode="Transparent" x="0" y="1" width="52" height="10" uuid="9a7be817-b2eb-4155-907b-acff4daefa37">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{COD_SERVICO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="53" y="1" width="304" height="10" uuid="ac06b644-105e-4277-a601-34fa96020d28">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box leftPadding="3"/>
				<textElement textAlignment="Left">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SEQITEM}==null? ("") : ($F{SEQITEM} + " - " + $F{DESCRICAO_SERVICO})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="358" y="1" width="30" height="10" uuid="77e4292c-f6e0-4f25-8de7-c3ba14381cfc">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="389" y="1" width="27" height="10" uuid="f859713a-cd73-4eac-a2e0-57d5897a202e">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="52" y="0" width="1" height="12" uuid="1252726a-5a03-4b2a-aca5-e9f5f912a566">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="357" y="0" width="1" height="12" uuid="771b54eb-e168-4787-b311-05ae684fa95e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="388" y="0" width="1" height="12" uuid="7793026b-75dc-4d6f-b288-172bd0465673">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="0" y="0" width="1" height="12" uuid="42d681c5-c3d3-419d-a86d-0c3086344707">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
		</band>
	</detail>
</jasperReport>
