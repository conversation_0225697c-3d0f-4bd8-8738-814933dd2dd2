<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OsJaguarLandSubServicos" pageWidth="532" pageHeight="96" whenNoDataType="AllSectionsNoDetail" columnWidth="532" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CRMService"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="473"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="526"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<queryString language="SQL">
		<![CDATA[WITH Q_SERVICOS AS (
SELECT OS_SERVICOS.NUMERO_OS,
 0 AS COD_OS_AGENDA,
 OS_SERVICOS.COD_EMPRESA, 
 OS_SERVICOS.ITEM, 
 OS_SERVICOS.COD_SERVICO, 
 SERVICOS.DESCRICAO_SERVICO,
 OS_SERVICOS.TEMPO_PADRAO + NVL(ADI_TEMPO, 0) AS TEMPO_PADRAO,
 NVL(OS_SERVICOS.TOTAL_LIQUIDO, OS_SERVICOS.PRECO_VENDA) AS TOTALVENDA,
 OS_SERVICOS.PRECO_VENDA,
 OS_SERVICOS.COD_CCC,
 SERVICOS.COD_SETOR,
 DESCRICAO_SETOR
FROM OS_SERVICOS, SERVICOS, SERVICOS_SETORES,
  (SELECT COD_SERVICO AS ADI_SRV, SUM(TEMPO_ADICIONAL) AS ADI_TEMPO
   FROM OS_SERVICOS_ADICIONAIS
   WHERE COD_EMPRESA = $P{COD_EMPRESA}
    AND NUMERO_OS = $P{NUMERO_OS}
   GROUP BY COD_SERVICO)
WHERE OS_SERVICOS.COD_SERVICO = SERVICOS.COD_SERVICO
 AND SERVICOS.COD_SETOR = SERVICOS_SETORES.COD_SETOR
 AND OS_SERVICOS.COD_SERVICO = ADI_SRV (+)
 AND OS_SERVICOS.NUMERO_OS = $P{NUMERO_OS}
 AND OS_SERVICOS.COD_EMPRESA = $P{COD_EMPRESA}
ORDER BY OS_SERVICOS.ITEM, OS_SERVICOS.COD_SERVICO   
),

TENHO AS (
SELECT COUNT(*) AS LINHAS
                  FROM OS_SERVICOS OSS
                 WHERE OSS.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSS.NUMERO_OS = $P{NUMERO_OS}
),

MAIOR_QUANTIDADE AS (
       SELECT (MAX(TOT)) AS LINHAS
        FROM (SELECT COUNT(*) AS TOT
                  FROM OS_ORIGINAL OSO
                 WHERE OSO.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSO.NUMERO_OS = $P{NUMERO_OS}
                UNION ALL
                SELECT COUNT(*) AS TOT
                  FROM OS_SERVICOS OSS
                 WHERE OSS.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSS.NUMERO_OS = $P{NUMERO_OS}
                UNION ALL
                SELECT COUNT(*) AS TOT
                  FROM OS_REQUISICOES OSR
                 WHERE OSR.COD_EMPRESA = $P{COD_EMPRESA}
                   AND OSR.NUMERO_OS = $P{NUMERO_OS})
),

DEVE_TER AS (
       SELECT CASE WHEN MOD(MAIOR_QUANTIDADE.LINHAS,6) = 0
              THEN MAIOR_QUANTIDADE.LINHAS + 1
              ELSE (MAIOR_QUANTIDADE.LINHAS + ( 7 - MOD(MAIOR_QUANTIDADE.LINHAS,6))) 
              END AS LINHAS
        FROM MAIOR_QUANTIDADE
),

Q_NULLROWS AS (SELECT NULL NUMERO_OS,
               NULL AS COD_OS_AGENDA,
               NULL AS COD_EMPRESA, 
               NULL AS ITEM, 
               NULL AS COD_SERVICO, 
               NULL AS DESCRICAO_SERVICO,
               NULL AS TEMPO_PADRAO,
               NULL AS TOTALVENDA,
               NULL AS PRECO_VENDA,
               NULL AS COD_CCC,
               NULL AS COD_SETOR,
               NULL AS DESCRICAO_SETOR
       FROM DUAL, TENHO,MAIOR_QUANTIDADE,DEVE_TER
       WHERE MOD(TENHO.LINHAS,6)<>0   OR TENHO.LINHAS = 0
       CONNECT BY LEVEL < DEVE_TER.LINHAS - TENHO.LINHAS
)
 
SELECT * FROM Q_SERVICOS
UNION ALL
SELECT * FROM Q_NULLROWS]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_OS_AGENDA" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="ITEM" class="java.lang.Double"/>
	<field name="COD_SERVICO" class="java.lang.String"/>
	<field name="DESCRICAO_SERVICO" class="java.lang.String"/>
	<field name="TEMPO_PADRAO" class="java.lang.Double"/>
	<field name="TOTALVENDA" class="java.lang.Double"/>
	<field name="PRECO_VENDA" class="java.lang.Double"/>
	<field name="COD_CCC" class="java.lang.String"/>
	<field name="COD_SETOR" class="java.lang.String"/>
	<field name="DESCRICAO_SETOR" class="java.lang.String"/>
	<variable name="SOMA" class="java.lang.Double" resetType="Page" calculation="Sum">
		<variableExpression><![CDATA[$F{TOTALVENDA} == null ? 0.0 : $F{TOTALVENDA}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="12">
			<staticText>
				<reportElement mode="Opaque" x="397" y="0" width="63" height="12" backcolor="#E3E3E3" uuid="52bc4906-533e-4833-8a69-ff9b5332abde"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[VALOR DE MO]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="459" y="0" width="73" height="12" backcolor="#E3E3E3" uuid="a358c33e-048c-4f50-8d11-e415a6a5b7a0"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[VALOR TOTAL]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="36" height="12" backcolor="#E3E3E3" uuid="fbaf7dc4-4e00-40b9-8e96-a99f2e2a2145"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[ÍTEM Nº.]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="36" y="0" width="322" height="12" backcolor="#E3E3E3" uuid="365f92b8-4011-466d-a518-b69a701f2246"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[DESCRIÇÃO]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="358" y="0" width="39" height="12" backcolor="#E3E3E3" uuid="6820dd8e-6d8b-4842-9142-a79aa21ba750"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[T.P.]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Transparent" x="459" y="0" width="73" height="12" uuid="b89c5cd7-cc27-4b9c-b1b6-f59f19f82e6a"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOTALVENDA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="358" y="0" width="39" height="12" uuid="3aeba59d-6649-46d6-9d7d-58407a0f8e6f"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEMPO_PADRAO}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00#;#,##0.00#-">
				<reportElement mode="Transparent" x="397" y="0" width="62" height="12" uuid="fd3d57ac-90f9-4ad4-a179-25ceec3d9586"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PRECO_VENDA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="36" y="0" width="322" height="12" uuid="923794f5-0cb8-4e74-aa7d-50659138fb59"/>
				<box leftPadding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DESCRICAO_SERVICO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="0" y="0" width="36" height="12" uuid="9f241b2f-f885-479b-8187-ca40e8fda60a"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ITEM}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="12">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="459" height="12" backcolor="#E3E3E3" uuid="f10bff9e-78d5-483e-a1c0-8a38df85216e"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="7">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Subtotal SERVIÇOS]]></text>
			</staticText>
			<textField evaluationTime="Page" pattern="#,##0.00#;#,##0.00#-" isBlankWhenNull="false">
				<reportElement mode="Opaque" x="459" y="0" width="73" height="12" backcolor="#E3E3E3" uuid="cfe2b6a9-89e0-4109-83dd-8bd5fd64ecfc"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SOMA}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
