<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.4.0.final using JasperReports Library version 6.4.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CheckListHondaMotoVinteUmItensSubImagem" pageWidth="555" pageHeight="143" whenNoDataType="NoPages" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="3f99e0d0-d7ce-4e4b-bb8b-973e0219935c">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NBS3.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="alternarCor" forecolor="#171616" backcolor="#E0E0E0" pattern=""/>
	<style name="alternateStyle" backcolor="#E0E0E0">
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() % 2 == 0)]]></conditionExpression>
			<style backcolor="#FFFFFF"/>
		</conditionalStyle>
	</style>
	<style name="field_null" isDefault="true" isBlankWhenNull="true"/>
	<subDataset name="listaTeste" uuid="1aae6cce-2249-4c5f-a4de-60c411ee5960">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="PROG11_NBSGM.xml"/>
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<queryString>
			<![CDATA[select 1 FROM DUAL]]>
		</queryString>
		<field name="1" class="java.lang.Boolean"/>
	</subDataset>
	<parameter name="NUMERO_OS" class="java.lang.Double"/>
	<parameter name="COD_EMPRESA" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["H:\\NBS\\34613\\FREEDOM\\crmservice\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="COD_SEGMENTO" class="java.lang.Double"/>
	<parameter name="DIR_IMAGE_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Pictures\\imagens\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="SQL">
		<![CDATA[SELECT OS.NUMERO_OS,
       OS.COD_EMPRESA,
       MOB_OS_IMAGEM.IMAGEM,
       MOB_OS_IMAGEM.IMAGEM_ORIGINAL,
       (SELECT a.imagem
          FROM mob_imagem a
         WHERE EXISTS (SELECT 1
                  FROM mob_imagem_filtro mif
                 WHERE mif.cod_empresa = $P{COD_EMPRESA}
                   AND mif.cod_segmento = $P{COD_SEGMENTO}
                   AND mif.id_imagem = a.id_imagem)and rownum <2) AS IMAGEM_CARROCERIA_DEFAULT,
        MOB_OS_ASSINATURA.ASSINATURA AS ASSINATURA_CONSULTOR,
        MOB_OS_ASSINATURA.ASSINATURA_CLIENTE AS ASSINATURA_CLIENTE,
        OS_DADOS_VEICULOS.COMBUSTIVEL,
        NVL(OS_AGENDA.SERVICO_EXPRESSO,'N') AS SERVICO_EXPRESSO,
        DECODE(MOB_OS_IMAGEM.NUMERO_OS, NULL, 'N','S') AS COM_AVARIA
  FROM MOB_OS_IMAGEM, OS, MOB_OS_ASSINATURA, OS_DADOS_VEICULOS, OS_AGENDA
 WHERE OS.NUMERO_OS = $P{NUMERO_OS}
   AND OS.COD_EMPRESA = $P{COD_EMPRESA}
   AND MOB_OS_IMAGEM.APLICACAO(+) = 'R'
   AND MOB_OS_IMAGEM.NUMERO_OS(+) = OS.NUMERO_OS
   AND MOB_OS_IMAGEM.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND MOB_OS_IMAGEM.IMAGEM(+) IS NOT NULL
   AND MOB_OS_ASSINATURA.NUMERO_OS(+) = OS.NUMERO_OS 
   AND MOB_OS_ASSINATURA.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND MOB_OS_ASSINATURA.APLICACAO(+) = 'R'
   AND OS_DADOS_VEICULOS.NUMERO_OS(+) = OS.NUMERO_OS 
   AND OS_DADOS_VEICULOS.COD_EMPRESA(+) = OS.COD_EMPRESA
   AND OS.COD_OS_AGENDA  =  OS_AGENDA.COD_OS_AGENDA(+)
   AND OS.COD_EMPRESA  =  OS_AGENDA.COD_EMPRESA(+)]]>
	</queryString>
	<field name="NUMERO_OS" class="java.lang.Double"/>
	<field name="COD_EMPRESA" class="java.lang.Double"/>
	<field name="IMAGEM" class="java.awt.Image"/>
	<field name="IMAGEM_ORIGINAL" class="java.io.InputStream"/>
	<field name="IMAGEM_CARROCERIA_DEFAULT" class="java.io.InputStream"/>
	<field name="ASSINATURA_CONSULTOR" class="java.awt.Image"/>
	<field name="ASSINATURA_CLIENTE" class="java.awt.Image"/>
	<field name="COMBUSTIVEL" class="java.lang.Double"/>
	<field name="SERVICO_EXPRESSO" class="java.lang.String"/>
	<field name="COM_AVARIA" class="java.lang.String"/>
	<variable name="numero_item" class="java.lang.Double" calculation="Count"/>
	<detail>
		<band height="143" splitType="Stretch">
			<frame>
				<reportElement mode="Transparent" x="0" y="0" width="555" height="143" uuid="0943d055-0527-448f-8bde-56933017f326">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="78" y="17" width="412" height="103" uuid="85e0f6f1-991c-428f-9f1e-34baccd36029"/>
					<imageExpression><![CDATA[$F{IMAGEM} == null ? $P{DIR_IMAGE_LOGO} + "crmservice541023.png" : $F{IMAGEM}]]></imageExpression>
				</image>
				<frame>
					<reportElement x="0" y="122" width="555" height="21" uuid="636360bb-db0a-449a-89ff-f7c68fcb35b9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<subreport>
						<reportElement x="2" y="0" width="481" height="21" uuid="6a4aa566-1782-47fc-876d-0456e99ab592">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</reportElement>
						<subreportParameter name="COD_EMPRESA">
							<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="NUMERO_OS">
							<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
						</subreportParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListHondaMotoVinteUmItensSubImagemObservacao.jasper"]]></subreportExpression>
					</subreport>
					<frame>
						<reportElement x="483" y="-1" width="72" height="22" uuid="fef58ee6-7c3b-4c88-90a8-7e8d1faadf08"/>
						<box>
							<pen lineWidth="0.5"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement x="22" y="10" width="12" height="10" uuid="093ef8ee-eea6-41f5-b84f-63860083b04d">
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							</reportElement>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="5"/>
							</textElement>
							<textFieldExpression><![CDATA[""]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="1" y="1" width="69" height="7" forecolor="#000000" uuid="c0b43863-4101-4784-9aa6-dc06107494dd">
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							</reportElement>
							<box>
								<pen lineWidth="0.75"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="5"/>
							</textElement>
							<text><![CDATA[Motocicleta com Baú]]></text>
						</staticText>
						<staticText>
							<reportElement x="8" y="12" width="13" height="7" forecolor="#000000" uuid="bd37d296-cda9-4b16-9317-d852fafe0945">
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							</reportElement>
							<box>
								<pen lineWidth="0.75"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="5"/>
							</textElement>
							<text><![CDATA[SIM]]></text>
						</staticText>
						<textField>
							<reportElement x="52" y="10" width="12" height="10" uuid="d0ac580d-d3f9-42ba-a54f-8fcfa05a51aa">
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							</reportElement>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="5"/>
							</textElement>
							<textFieldExpression><![CDATA[""]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="38" y="12" width="13" height="7" forecolor="#000000" uuid="45483251-a61e-4ab3-a608-8f069c52b749">
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							</reportElement>
							<box>
								<pen lineWidth="0.75"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="5"/>
							</textElement>
							<text><![CDATA[NÃO]]></text>
						</staticText>
					</frame>
				</frame>
				<frame>
					<reportElement x="493" y="33" width="60" height="86" uuid="3cad18bc-f539-4d36-9529-0dabaaa6219d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
					</box>
					<subreport overflowType="NoStretch">
						<reportElement x="0" y="0" width="60" height="86" uuid="f51ad9b2-8427-4f42-8bfb-274bf8ae013d">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<subreportParameter name="COD_EMPRESA">
							<subreportParameterExpression><![CDATA[$P{COD_EMPRESA}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="NUMERO_OS">
							<subreportParameterExpression><![CDATA[$P{NUMERO_OS}]]></subreportParameterExpression>
						</subreportParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "CheckListHondaMotoVinteUmItensSubImagemLegendas.jasper"]]></subreportExpression>
					</subreport>
				</frame>
				<frame>
					<reportElement x="2" y="33" width="60" height="86" uuid="49a1022e-521a-4ea7-b71c-4ea3b44f5bf2">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" onErrorType="Blank">
						<reportElement x="3" y="11" width="40" height="74" uuid="b206cffa-1c35-45e6-85d4-58b93a4f1f27"/>
						<imageExpression><![CDATA[$P{DIR_IMAGE_LOGO} + "crmservice541024.png"]]></imageExpression>
					</image>
					<staticText>
						<reportElement x="3" y="2" width="52" height="7" uuid="491af90f-d243-4da9-af47-2fc257f8b787">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="5"/>
						</textElement>
						<text><![CDATA[COMBUSTíVEL]]></text>
					</staticText>
					<textField>
						<reportElement x="33" y="74" width="6" height="6" uuid="be0b237e-c946-4ebb-af30-1d9a4e0373b0">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.5"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="3"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{COMBUSTIVEL} < 19 ? "X" :
$F{COMBUSTIVEL} < 39 ? "" :
$F{COMBUSTIVEL} < 59 ? "" :
$F{COMBUSTIVEL} < 79 ? "" :
""]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="44" y="60" width="6" height="6" uuid="f6dd3123-6c97-44fb-93e0-c386e09da551">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.5"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="3"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{COMBUSTIVEL} < 19 ? "" :
$F{COMBUSTIVEL} < 39 ? "X" :
$F{COMBUSTIVEL} < 59 ? "" :
$F{COMBUSTIVEL} < 79 ? "" :
""]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="47" y="45" width="6" height="6" uuid="4a8fd313-1292-40ec-a3fc-4596f4188f8d">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.5"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="3"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{COMBUSTIVEL} < 19 ? "" :
$F{COMBUSTIVEL} < 39 ? "" :
$F{COMBUSTIVEL} < 59 ? "X" :
$F{COMBUSTIVEL} < 79 ? "" :
""]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="44" y="29" width="6" height="6" uuid="1c209dbe-a137-4876-89b5-3f5632f03e6c">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.5"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="3"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{COMBUSTIVEL} < 19 ? "" :
$F{COMBUSTIVEL} < 39 ? "" :
$F{COMBUSTIVEL} < 59 ? "" :
$F{COMBUSTIVEL} < 79 ? "X" :
""]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="33" y="15" width="6" height="6" uuid="b38957a4-127d-42c6-99c9-c1eb74ae3f5a">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.5"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="3"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{COMBUSTIVEL} < 19 ? "" :
$F{COMBUSTIVEL} < 39 ? "" :
$F{COMBUSTIVEL} < 59 ? ""  :
$F{COMBUSTIVEL} < 79 ? "" :
"X"]]></textFieldExpression>
					</textField>
				</frame>
				<staticText>
					<reportElement mode="Opaque" x="0" y="0" width="555" height="13" forecolor="#FFFFFF" backcolor="#000000" uuid="ec22da9d-e301-4e6d-9e34-bcde63e91cfa">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box>
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[CHECKLIST]]></text>
				</staticText>
				<frame>
					<reportElement x="2" y="14" width="60" height="17" uuid="3969fec2-00d5-448e-98b8-b86fba932caf"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textField>
						<reportElement x="46" y="4" width="12" height="10" uuid="f43c1350-f004-43ec-918d-4544faed490c">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.5"/>
							<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="5"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{COM_AVARIA}.equals("N") ? "S" : ""]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="2" y="2" width="43" height="14" uuid="8d24f7b9-4a90-4e66-a511-14923a93ca84">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="5"/>
						</textElement>
						<text><![CDATA[MOTOCICLETA
SEM AVARIAS]]></text>
					</staticText>
				</frame>
				<line>
					<reportElement x="13" y="121" width="530" height="1" uuid="af2b7c20-334a-41d1-b076-2fd71d725fd5"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<frame>
					<reportElement x="493" y="14" width="60" height="17" uuid="5d795b9c-5889-4e3e-bc6a-223b08e99be3"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textField>
						<reportElement x="46" y="4" width="12" height="10" uuid="ae4676c0-65f6-4446-a6b3-04b2d5dd248d">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.5"/>
							<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="5"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{COM_AVARIA}.equals("S") ? "S" : ""]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="2" y="2" width="43" height="14" forecolor="#FF0000" uuid="e631f287-cec0-4930-916c-7f9160a02ca3">
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						</reportElement>
						<box>
							<pen lineWidth="0.75"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="5"/>
						</textElement>
						<text><![CDATA[MOTOCICLETA
COM AVARIAS]]></text>
					</staticText>
				</frame>
			</frame>
		</band>
	</detail>
</jasperReport>
