{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "CrmServiceApplication",
      "request": "launch",
      "mainClass": "crmservice.CrmServiceApplication",
      "projectName": "crmservice",
      "vmArgs": "-Dspring.profiles.active=dev -DSYS_PARAM_FILE=C:/java/fserver_spring.xml -Djava.util.Arrays.useLegacyMergeSort=true -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=6001 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Xmx1g -XX:HotswapAgent=fatjar -XX:HotswapAgent=core -XX:HotswapAgent=external",
      "cwd": "${workspaceFolder}",
    }
  ]
}