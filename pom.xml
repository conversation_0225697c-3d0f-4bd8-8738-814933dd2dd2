<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>nbs-shared</groupId>
    <artifactId>nbs-empresa-zk</artifactId>
    <version>**********.L35501</version>
    <packaging>jar</packaging>
    <description>Modulo no formato biblioteca de rotinas que concentra as rotinas de uma empresa para os modulos do
        sistema NBS.
    </description>
    <properties>
        <project.build.sourceEncoding>windows-1252</project.build.sourceEncoding>
        <endorsed.dir>${project.build.directory}/endorsed</endorsed.dir>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <freedom.version>*******</freedom.version>
    </properties>
    <parent>
        <groupId>br.com.nbs.freedom</groupId>
        <artifactId>freedom-frontend-starter</artifactId>
        <version>********</version>
    </parent>
    <dependencies>
        <dependency>
			<groupId>nbs-shared</groupId>
			<artifactId>nbs-util-zk</artifactId>
			<version>**********.L35501</version>
        </dependency>
        <dependency>
			<groupId>nbs-shared</groupId>
			<artifactId>cursores-zk</artifactId>
			<version>**********.L35501</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.13.1</version>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
            <version>4.0.1</version>
        </dependency>
        <dependency>
            <groupId>nbs-shared</groupId>
            <artifactId>encrypt-nbs</artifactId>
            <version>1.0.0.3.62341</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.31</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>b60e1586178c-releases</name>
            <url>https://jfrog.nbsi.com.br:8091/artifactory/artifactory</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>b60e1586178c-snapshots</name>
            <url>https://jfrog.nbsi.com.br:8091/artifactory/artifactory</url>
        </snapshotRepository>
    </distributionManagement>

</project>
