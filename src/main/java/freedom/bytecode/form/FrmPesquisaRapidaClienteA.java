package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmPesquisaRapidaClienteW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.StringUtil;

public class FrmPesquisaRapidaClienteA extends FrmPesquisaRapidaClienteW {
    private static final long serialVersionUID = 20130827081850L;
    private boolean aceitar;

    @Override
    public void btnVoltarClick(Event<Object> event) {
        close();
    }

    @Override
    public void btnPesquisarClick(Event<Object> event) {
        this.filtrarCliente();
    }

    @Override
    public void edtPesqRapidaClienteEnter(Event<Object> event) {
        this.filtrarCliente();
    }

    @Override
    public void btnAceitarClick(Event<Object> event) {
        this.aceitarPesquisa();
        this.close();
    }

    private void aceitarPesquisa(){
       if (this.tbLeadsConsultaClientes.count() == 0) {
           EmpresaUtil.showInformationMessage("Selecione um cliente antes de aceitar.");
           this.edtPesqRapidaCliente.setFocus();
           return;
       }

    }

    public boolean isAceitar(){
        return this.aceitar;
    }

    public void filtrarCliente() {
        try {
            String cliente = this.edtPesqRapidaCliente.getValue().asString().trim();
            if (cliente.trim().equals("")) {
                EmpresaUtil.showInformationMessage("Nome ou CPF/CNPJ, obrigatório o preenchimento!");
                return;
            }
            String docCPFOuCNPJ = StringUtil.removerCaracteresNaoNumericos(cliente);
            if ((docCPFOuCNPJ.length() == 11)
                    || (docCPFOuCNPJ.length() == 14)) {
                this.rn.filtrarCliente(docCPFOuCNPJ);
            } else {
                this.rn.filtrarCliente(cliente);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao filtrar cliente",
                    dataException);
        }
    }

}
