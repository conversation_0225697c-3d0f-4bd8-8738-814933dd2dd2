package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmHomeClienteW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.ApplicationUtil;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;
import freedom.util.WorkListFactory;
import java.util.logging.Level;
import java.util.logging.Logger;
import static freedom.util.CastUtil.asString;

public class FrmHomeClienteA extends FrmHomeClienteW {

    private static final long serialVersionUID = 20180503081850L;

    private static final String USUARIO_COD_FUNCAO = "USUARIO_COD_FUNCAO";

    private static final String USUARIO_COD_EMPRESA = "USUARIO_COD_EMPRESA";

    private static final String IMAGES = "/images/";

    private static final String APPLICATION_NAME = "APPLICATION_NAME";

    private final IWorkList wl = WorkListFactory.getInstance();

    private String usuarioLogado = "";

    private int codFuncao = 0;

    private int codEmpresa = 0;

    public void setImageFormDefault(boolean imageFormDefault) {
    }

    public String getUsuarioLogado() {
        return usuarioLogado;
    }

    public int getCodFuncao() {
        return codFuncao;
    }

    public int getCodEmpresa() {
        return codEmpresa;
    }

    public FrmHomeClienteA() {
        try {
            this.gpPesqCodigo.setVisible(Boolean.TRUE);
            this.usuarioLogado = this.wl.get("USUARIO_LOGADO").asString();
            String versao = ApplicationUtil.getApplicationVersion();
            this.lblVersao.setValue(versao == null ? "*******" : versao);
            this.hbBoxVersao.invalidate();
            // registrar o pageControl principal da aplicacao
            FormUtil.registerPgCtrl(this.pgctrlPrincipal);
            // inicializar table para últimos acessos
            this.rn.getCliente(this.wl.get("COD_CLIENTE_LOGADO").asLong());
            StringBuilder nomeCliente = new StringBuilder();
            int count = 0;
            for (String nome : this.tbClienteDiverso.getNOME().asString().split(" ")) {
                nomeCliente.append(nome).append(" ");
                count++;
                if (count == 3) {
                    break;
                }
            }
            this.tabHome.setCaption(nomeCliente.toString());
            this.imgLogo.setImageSrc(IMAGES + this.wl.sysget(APPLICATION_NAME).asString() + "310038.png?version=1");
            this.imgBuscar.setImageSrc(IMAGES + this.wl.sysget(APPLICATION_NAME).asString() + "700089.png?version=1");
            this.imageUsuario.setImageSrc(IMAGES + this.wl.sysget(APPLICATION_NAME).asString() + "700090.png?version=1");
        } catch (Exception exception) {
            Logger.getLogger(FrmHomeClienteA.class.getName()).log(Level.SEVERE,
                    null,
                    exception);
        }
    }

    public void setInfoUser(String user) {
        try {
            this.tbUserInformation.close();
            this.tbUserInformation.clearFilters();
            this.tbUserInformation.clearParams();
            this.tbUserInformation.addParam("USUARIO", user);
            this.tbUserInformation.open();
            this.codFuncao = this.tbUserInformation.getCOD_FUNCAO().asInteger();
            this.codEmpresa = this.tbUserInformation.getCOD_EMPRESA().asInteger();
            ApplicationUtil.setValue(USUARIO_COD_FUNCAO,
                    asString(this.codFuncao));
            ApplicationUtil.setValue(USUARIO_COD_EMPRESA,
                    asString(this.codEmpresa));
        } catch (DataException dataException) {
            EmpresaUtil.showError("Validação de acesso",
                            dataException);
        }
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        this.setInfoUser(this.usuarioLogado);
    }

    @Override
    public void imageUsuarioClick(final Event<Object> event) {
        this.popMenuLogin.open(this.imageUsuario);
    }

    @Override
    public void mmAlterarSenhaClick(final Event<Object> event) {
        FrmAlterarSenhaA frmAlterarSenhaA = new FrmAlterarSenhaA();
        FormUtil.createTab(frmAlterarSenhaA,
                event1 -> {

                }, true);
    }

    @Override
    public void mmSairClick(final Event<Object> event) {

        Dialog.create()
                .title("Confirma")
                .message("Deseja realmente sair do sistema?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        wl.remove("CLIENTE_LOGADO");
                        wl.remove(IWorkList.USUARIO_LOGADO);
                        FormUtil.redirect("client?frmlogincliente");
                    }
                });
    }

    @Override
    public void pgctrlPrincipalChange(final Event<Object> event) {
        // sempre que for removido uma aba ao pagecontrol, testar pra ver se
        // precisa fechar o borderPanel do menu
        // borderPanel.setOpen("W", pgctrlPrincipal.getPageCount() == 1);
    }

    @Override
    public void mmPerfilClick(final Event<Object> event) {
        FrmPerfilA frmPerfilA = new FrmPerfilA(this.usuarioLogado);
        FormUtil.createTab(frmPerfilA,
                event1 -> {
                }, true);
    }

    @Override
    public void lblSistemaClick(final Event<Object> event) {
    }

    @Override
    public void imgLogoClick(final Event<Object> event) {
        FormUtil.redirect("http://www.nbsi.com.br", true);
    }

    @Override
    public void mmHelpClick(final Event<Object> event) {
    }

    @Override
    public void edtBuscarCodigoEnter(Event<Object> event) {
    }

    @Override
    public void edtBuscarCodigoChanging(Event<Object> event) {
        btnSearchCodigo.setVisible(!((Value) event.getValue()).isEmpty() && ((Value) event.getValue()).asString().length() > 3);
    }

    @Override
    public void btnSearchCodigoClick(final Event<Object> event) {
    }

    @Override
    public void iconClassNovoClick(final Event<Object> event) {
    }

    @Override
    public void hBoxPesquisasClick(Event<Object> event) {
    }

}
