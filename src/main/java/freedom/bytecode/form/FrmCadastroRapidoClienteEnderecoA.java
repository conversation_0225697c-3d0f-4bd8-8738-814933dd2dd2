package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmCadastroRapidoClienteEnderecoW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import freedom.util.FRLogger;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

public class FrmCadastroRapidoClienteEnderecoA extends FrmCadastroRapidoClienteEnderecoW {

    private static final long serialVersionUID = 20130827081850L;

    public static final String INFORMACAO = "Informação";

    @Getter
    boolean ok = false;

    private double codEmpresa;

    private String tipoPessoa;

    private double codCliente = 0.0;

    private String inscricao = null;

    private boolean usaApiConsulta = false;

    private String infoSituacaoCadastralIntegracao(double codEmpresa,
                                                   double codClienteApi,
                                                   String tipoPessoa,
                                                   String ie,
                                                   Value rfbSituacao,
                                                   Value rfbCadastroIrregular,
                                                   Value sintegraSituacao,
                                                   Value sintegraCadastroIsento,
                                                   Value sintegraCadastroIrregular,
                                                   Value sintegraMultiplasIe) {
        String retFuncao = "";
        try {
            retFuncao = this.rn.infoSituacaoCadastralIntegracao(codEmpresa,
                    codClienteApi,
                    tipoPessoa,
                    ie,
                    rfbSituacao,
                    rfbCadastroIrregular,
                    sintegraSituacao,
                    sintegraCadastroIsento,
                    sintegraCadastroIrregular,
                    sintegraMultiplasIe);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao obter a informação da situação cadastral da integração",
                    dataException);
        }
        return retFuncao;
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        FRLogger.log("Formulário: " + this.getName(),
                this.getClass());
        this.vBoxStatusCadastro.setVisible(false);
        if (!this.usaApiConsulta) {
            return;
        }
        this.vBoxStatusCadastro.setVisible(true);
        this.hBoxSituacaoCadReceitaFederal.setVisible(true);
        this.hBoxSituacaoCadSintegra.setVisible(true);
        this.lblSintegraMultiIe.setVisible(false);
        this.lblSituacaoCadReceitaFederal.setVisible(true);
        this.lblCadastroIrregular.setVisible(false);
        this.hBoxSituacaoCadSintegra.setVisible(false);
        this.lblSituacaoSintegra.setCaption(" -- ");
        this.lblSituacaoSintegra.setHint("");
        this.lblSituacaoReceitaFederal.setCaption(" -- ");
        this.lblSituacaoReceitaFederal.setHint("");
        this.lblSintegraMultiIe.setHint("");
        this.lblSituacaoCadSintegra.setHint("");
        Value rfbSituacao = new Value(null);
        Value rfbSituacaoMensagem = new Value(null);
        Value sintegraSituacao = new Value(null);
        Value sintegraMensagem = new Value(null);
        Value sintegraCadastroIsento = new Value(null);
        Value sintegraMultiplasIe = new Value(null);
        String infoSituacaoCadastralIntegracao = this.infoSituacaoCadastralIntegracao(this.codEmpresa,
                this.codCliente,
                this.tipoPessoa,
                this.inscricao,
                rfbSituacao,
                rfbSituacaoMensagem,
                sintegraSituacao,
                sintegraMensagem,
                sintegraCadastroIsento,
                sintegraMultiplasIe);
        if (!infoSituacaoCadastralIntegracao.equals("S")){
            this.lblCadastroIrregular.setVisible(true);
        }
        if (StringUtils.isNotBlank(this.inscricao)) {
            this.hBoxSituacaoCadSintegra.setVisible(true);
        }
        this.lblSituacaoReceitaFederal.setHint(rfbSituacaoMensagem.asString());
        this.lblSituacaoSintegra.setHint(sintegraMensagem.asString());
        if (StringUtils.isNotBlank(sintegraMultiplasIe.asString())){
            boolean hBoxSituacaoCadSintegraVisible = this.hBoxSituacaoCadSintegra.isVisible();
            if (!hBoxSituacaoCadSintegraVisible){
                this.hBoxSituacaoCadSintegra.setVisible(true);
            }
            this.lblSintegraMultiIe.setVisible(true);
            this.lblSintegraMultiIe.setHint(sintegraMultiplasIe.asString());
            this.lblSituacaoCadSintegra.setHint("Múltiplas Ie"
                    + System.lineSeparator()
                    + sintegraMultiplasIe.asString());
        }
        this.lblSituacaoSintegra.setCaption(sintegraSituacao.asString());
        this.lblSituacaoReceitaFederal.setCaption(rfbSituacao.asString());
        this.cboEnderecoAtivo.setValue("S");
        FreedomUtilities.invokeLater(() -> this.edtNomePropriedade.setFocus());
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        this.close();
    }

    @Override
    public void btnConfirmarClick(final Event<Object> event) {
        String rua = this.edtRua.getValue().asString().trim();
        if ((rua.length() < 3)) {
            String mensagem = "O campo \""
                    + this.lblRua.getCaption()
                    + "\" deve ser preenchido com, no mínimo, \"3\" (três) caracteres.";
            Dialog.create()
                    .title(FrmCadastroRapidoClienteEnderecoA.INFORMACAO)
                    .message(mensagem)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtRua.setFocus()));
            return;
        }
        String bairro = this.edtBairro.getValue().asString().trim();
        if (bairro.isEmpty()) {
            String mensagem = "O campo \""
                    + this.lblBairro.getCaption()
                    + "\" deve ser preenchido.";
            Dialog.create()
                    .title(FrmCadastroRapidoClienteEnderecoA.INFORMACAO)
                    .message(mensagem)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtBairro.setFocus()));
            return;
        }
        String uf = this.cboUF.getValue().asString().trim();
        if (uf.isEmpty()) {
            Dialog.create()
                    .title(FrmCadastroRapidoClienteEnderecoA.INFORMACAO)
                    .message("O campo \"UF\" deve ser preenchido.")
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.cboUF.setFocus()));
            return;
        }
        String cidade = this.cboCidade.getValue().asString().trim();
        if (cidade.isEmpty()){
            Dialog.create()
                    .title(FrmCadastroRapidoClienteEnderecoA.INFORMACAO)
                    .message("O campo \"CIDADE\" deve ser preenchido.")
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.cboCidade.setFocus()));
            return;
        }
        String cep = this.edtCEP.getValue().asString().trim();
        if (cep.isEmpty()){
            Dialog.create()
                    .title(FrmCadastroRapidoClienteEnderecoA.INFORMACAO)
                    .message("O campo \"CEP\" deve ser preenchido.")
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtCEP.setFocus()));
            return;
        }
        this.ok = true;
        this.close();
    }

    @Override
    public void lblSituacaoCadSintegraClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    @Override
    public void lblSituacaoSintegraClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    @Override
    public void lblSintegraMultiIeClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    private void exibirSintegraMultiplasIe() {
        if (StringUtils.isNotBlank(this.lblSintegraMultiIe.getHint())){
            EmpresaUtil.showInformationMessage("Inscrições Estaduais"
                    + System.lineSeparator()
                    + this.lblSintegraMultiIe.getHint().replace(",",
                    ("," + System.lineSeparator())));
        }
    }

    private void openUfCidades(String uf,
                               double codCidade) {
        try {
            this.rn.openUfCidades(uf,
                    codCidade);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao abrir ufCidades",
                    dataException);
        }
    }

    public void iniciarUfCidades(String uf,
                                 double codCidade) {
        this.openUfCidades(uf,
                codCidade);
        this.cboUF.setValue(uf);
        this.cboCidade.setValue(codCidade);
    }

    @Override
    public void cboUFClearClick(final Event<Object> event) {
        this.cboUF.setText("");
        this.openCidades("**");
    }

    private void openCidades(String uf) {
        try {
            this.rn.openCidades(uf);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao abrir as cidades",
                    dataException);
        }
    }

    @Override
    public void cboUFExit(final Event<Object> event) {
        String uf = this.cboUF.getValue().asString();
        if (uf.isEmpty()) {
            this.cboUF.setText("");
            this.openCidades("**");
        } else if (!this.cboUF.getValue().asString().equals(this.tbCidades.getUF().asString())) {
            this.openCidades(this.cboUF.getValue().asString());
        }
    }

    @Override
    public void cbbCidadeExit(final Event<Object> event) {
        String cidade = this.cboCidade.getValue().asString();
        if (cidade.isEmpty()) {
            this.cboCidade.setText("");
        }
    }

    public void iniciarAlterarEnderecoPorInscricao(double codEmpresa,
                                                   double codCliente,
                                                   String tipoPessoa,
                                                   String inscricao,
                                                   String uf,
                                                   Double codCidade,
                                                   boolean usaApiConsulta) {
        this.usaApiConsulta = usaApiConsulta;
        this.codCliente = codCliente;
        this.inscricao = inscricao;
        this.codEmpresa = codEmpresa;
        this.tipoPessoa = tipoPessoa;
        iniciarUfCidades(uf, codCidade);
    }

    @Override
    public void cboUFChange(Event<Object> event) {
        this.cboCidade.setFocus();
        this.cboCidade.setOpen(true);
    }

    @Override
    public void cboCidadeChange(Event<Object> event) {
        this.edtCEP.setFocus();
        this.edtCEP.setSelectionRange(0,
                (this.edtCEP.getValue().asString().length() + 1));
    }
}
