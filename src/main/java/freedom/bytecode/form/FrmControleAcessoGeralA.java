package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmControleAcessoGeralW;
import freedom.client.controls.impl.TFFontExpression;
import freedom.client.controls.impl.treegrid.TFTreeGridModel;
import freedom.client.event.Event;
import freedom.client.event.UploadEvent;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.FreedomUtilities;
import freedom.client.util.IDialog;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.util.*;
import freedom.util.file.ImagesUtil;
import org.zkoss.zul.Tree;
import freedom.client.controls.impl.TFGrid;
import freedom.client.controls.impl.TFTable;

public class FrmControleAcessoGeralA extends FrmControleAcessoGeralW {

    private static final long serialVersionUID = 20130827081850L;

    private final GridUtil gridUtil = new GridUtil();

    private int codFuncao;

    private int[] selectionPath = null;

    private String codAcessoNew;

    private String pesquisaAvancadaLogin = "";

    private String pesquisaAvancadaNomeCompleto = "";

    private long pesquisaAvancadaCodEmpresa = 0L;

    private int pesquisaAvancadaCodDepartamento = 0;

    private int pesquisaAvancadaCodDivisao = 0;

    private String pesquisaAvancadaCPF = "";

    private String pesquisaAvancadaAtivo = Ativo.TODOS.getCodigo();

    private String pesquisaAvancadaCadBD = CadBD.TODOS.getCodigo();

    private final IWorkList wl = WorkListFactory.getInstance();

    public FrmControleAcessoGeralA() {
        this.filtrarEmpresasFuncoes();
        int codFuncaoUsuarioLogado = EmpresaUtil.getCodFuncaoUserLogged();
        this.cbbEmpresasFuncoes.setValue(codFuncaoUsuarioLogado);
        this.atribuirColorAcessos();
        this.filtrarControleAcesso();
        this.FLabel4.setVisible(false);
        this.cbbTipoVendedor.setVisible(false);
    }

    private void exportExcel(TFTable table,
                             TFGrid grid) {
        try {
            this.gridUtil.exportarExcel(table,
                    grid);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao exportar excel",
                    dataException);
        }
    }

    private void atribuirColorAcessos() {
        TFFontExpression expression1 = new TFFontExpression();
        expression1.setExpression("COD_ACESSO_FUNCAO IS NULL");
        expression1.setEvalType("etExpression");
        expression1.setFontColor("clRed");
        this.treeGridAcessos.getColumns().forEach(column -> {
            if (!column.getFieldName().isEmpty()) {
                column.getFont().add(expression1);
            }
        });
        TFFontExpression expression2 = new TFFontExpression();
        expression2.setExpression("COD_ACESSO_FUNCAO IS NOT NULL");
        expression2.setEvalType("etExpression");
        expression2.setFontColor("clGreen");
        this.treeGridAcessos.getColumns().forEach(column -> {
            if (!column.getFieldName().isEmpty()) {
                column.getFont().add(expression2);
            }
        });
    }

    private void atualizarVars() {
        this.codFuncao = this.cbbEmpresasFuncoes.getValue().asInteger();
    }

    private void carregarGridAgentesFuncao(int codFuncao,
                                           String pesquisaAvancadaLogin,
                                           String pesquisaAvancadaNomeCompleto,
                                           long pesquisaAvancadaCodEmpresa,
                                           int pesquisaAvancadaCodDepartamento,
                                           int pesquisaAvancadaCodDivisao,
                                           String pesquisaAvancadaCPF,
                                           String pesquisaAvancadaAtivo,
                                           String pesquisaAvancadaCadBD) {
        try {
            this.rn.carregarGridAgentesFuncao(codFuncao,
                    pesquisaAvancadaLogin,
                    pesquisaAvancadaNomeCompleto,
                    pesquisaAvancadaCodEmpresa,
                    pesquisaAvancadaCodDepartamento,
                    pesquisaAvancadaCodDivisao,
                    pesquisaAvancadaCPF,
                    pesquisaAvancadaAtivo,
                    pesquisaAvancadaCadBD);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao filtrar os usuários da função",
                    dataException);
        }
    }

    private void filtrarControleAcesso() {
        try {
            this.atualizarVars();
            String pesquisa = this.edtPesquisa.getValue().asString();
            this.rn.filtrarControleAcesso(this.codFuncao,
                    SystemUtil.getSistema(),
                    pesquisa);
            this.carregarGridAgentesFuncao(this.codFuncao,
                    this.pesquisaAvancadaLogin,
                    this.pesquisaAvancadaNomeCompleto,
                    this.pesquisaAvancadaCodEmpresa,
                    this.pesquisaAvancadaCodDepartamento,
                    this.pesquisaAvancadaCodDivisao,
                    this.pesquisaAvancadaCPF,
                    this.pesquisaAvancadaAtivo,
                    this.pesquisaAvancadaCadBD);
        } catch (Exception exception) {
            EmpresaUtil.showError("Erro ao consultar acessos",
                    exception);
        }
    }

    private void filtrarEmpresasFuncoes() {
        try {
            this.rn.filtrarEmpresasFuncoes();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao consultar funções",
                    dataException);
        }
    }

    @Override
    public void cbbEmpresasFuncoesChange(Event<Object> event) {
        this.filtrarControleAcesso();
    }

    private void atribuirPermissao() {
        // Pega a Linha selecionada da Grid
        int[] linhaSelecionadaDaGrade = ((TFTreeGridModel) ((Tree) this.treeGridAcessos.getImpl()).getModel()).getSelectionPath();
        if (linhaSelecionadaDaGrade != null) {
            this.selectionPath = linhaSelecionadaDaGrade;
        }
        try {
            boolean incluiAcesso = this.tbControleAcesso.getCOD_ACESSO_FUNCAO().isNull();
            String codAcesso = this.tbControleAcesso.getCOD_ACESSO().asString();
            this.codAcessoNew = codAcesso;
            try {
                this.tbControleAcesso.disableControls();
                this.rn.setPermissao(incluiAcesso,
                        this.codFuncao,
                        codAcesso);
                this.tbControleAcesso.edit();
                if (incluiAcesso) {
                    String codAcesso2 = this.tbControleAcesso.getCOD_ACESSO().asString();
                    this.tbControleAcesso.setCOD_ACESSO_FUNCAO(codAcesso2);
                } else {
                    this.tbControleAcesso.setCOD_ACESSO_FUNCAO(null);
                }
                this.tbControleAcesso.post();
            } finally {
                this.tbControleAcesso.enableControls();
            }
            this.setItemTreeGrid();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao atribuir/remover permissão",
                    dataException);
        }
    }

    private void habilitarCompEdicao(boolean habilita) {
        this.edtEmail.setEnabled(habilita);
        this.edtFone.setEnabled(habilita);
        this.cbbTipoVendedor.setEnabled(habilita);
        this.imageFoto.setEnabled(habilita);
        this.edtRamal.setEnabled(habilita);
    }

    @Override
    public void btnEditarClick(final Event<Object> event) {
        try {
            this.habilitarCompEdicao(true);
            this.btnSalvar.setEnabled(true);
            this.btnCancelar.setEnabled(true);
            this.btnEditar.setEnabled(false);
            this.tbPerfilUsuarios.edit();
            boolean fotoNula = this.tbPerfilUsuarios.getFOTO().isNull();
            this.lblLimparFoto.setVisible(!fotoNula);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao editar",
                    dataException);
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            this.habilitarCompEdicao(false);
            this.btnSalvar.setEnabled(false);
            this.btnCancelar.setEnabled(false);
            this.btnEditar.setEnabled(true);
            this.tbPerfilUsuarios.post();
            this.rn.salvarDadosAgendaFuncao();
            this.lblLimparFoto.setVisible(false);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao salvar",
                    dataException);
        }
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        try {
            this.habilitarCompEdicao(false);
            this.btnSalvar.setEnabled(false);
            this.btnCancelar.setEnabled(false);
            this.btnEditar.setEnabled(true);
            this.tbPerfilUsuarios.cancel();
            this.lblLimparFoto.setVisible(false);
        } catch (DataException exception) {
            EmpresaUtil.showError("Erro ao cancelar",
                    exception);
        }
    }

    private byte[] converterMiniatura(byte[] imageBytes,
                                      String fileName) throws Exception {
        ImagesUtil imagesUtil = new ImagesUtil();
        return imagesUtil.converterMiniatura(imageBytes,
                fileName,
                200);
    }

    @Override
    public void imageFotoClick(final Event<Object> event) {
        FileUpload.get("Upload Imagem Perfil",
                "Upload",
                (UploadEvent uploadEvent) -> {
            byte[] imageBytes = ImageUtil.streamToByteArray(uploadEvent.getStreamData());
            this.tbPerfilUsuarios.edit();
            this.tbPerfilUsuarios.setFOTO(imageBytes);
            this.tbPerfilUsuarios.setFOTO_ICONE(converterMiniatura(imageBytes,
                    uploadEvent.getFileName()));
            this.tbPerfilUsuarios.post();
            boolean fotoNula = this.tbPerfilUsuarios.getFOTO().isNull();
            this.lblLimparFoto.setVisible(!fotoNula);
        });
    }

    @Override
    public void iconClassHelpClick(final Event<Object> event) {
        FormUtil.redirect("http://ajuda.nbsi.com.br:84/index.php/Acessos",
                true);
    }

    @Override
    public void lblLimparFotoClick(final Event<Object> event) {
        try {
            this.tbPerfilUsuarios.edit();
            this.tbPerfilUsuarios.setFOTO(null);
            this.tbPerfilUsuarios.setFOTO_ICONE(null);
            this.tbPerfilUsuarios.post();
            this.lblLimparFoto.setVisible(false);
        } catch (DataException exception) {
            EmpresaUtil.showError("Erro ao limpar foto",
                    exception);
        }
    }

    @Override
    public void treeGridAcessosAlterarAcessoClick(final Event<Object> event) {
        this.atribuirPermissao();
    }

    @Override
    public void mmExportarExcelGridAgentesFuncaoClick(Event<Object> event) {
        this.exportExcel(this.tbPerfilUsuarios,
                this.gridAgentesFuncao);
    }

    @Override
    public void btnRefreshClick(final Event<Object> event) {
        this.filtrarControleAcesso();
    }

    @Override
    public void edtPesquisaEnter(final Event<Object> event) {
        this.filtrarControleAcesso();
    }

    public void setItemTreeGrid() {
        if (this.selectionPath != null) {
            FreedomUtilities.invokeLater(() -> {
                ((Tree) this.treeGridAcessos.getImpl()).renderItemByPath(this.selectionPath);
                try {
                    this.tbControleAcesso.locate("COD_ACESSO", this.codAcessoNew);
                } catch (Exception exception) {
                    EmpresaUtil.showError("ERRO",
                            exception);
                }
            });
        }
    }

    @Override
    public void iconClassLimparClick(final Event<Object> event) {
        this.edtPesquisa.clear();
        this.edtPesquisa.setFocus();
    }

    @Override
    public void btnEditarPerfilClick(Event<Object> event) {
        String loginUsuarioSelecionado = this.tbPerfilUsuarios.getNOME().asString();
        FrmPerfilA frmPerfilA = new FrmPerfilA(loginUsuarioSelecionado);
        FormUtil.createTab(frmPerfilA,
                null,
                true);
    }

    @Override
    public void btnPesquisaAvancadaAgentesFuncaoClick(Event<Object> event) {
        FrmPesquisaAvancadaAgentesFuncaoA frmPesquisaAvancadaAgentesFuncaoA = new FrmPesquisaAvancadaAgentesFuncaoA(this.pesquisaAvancadaLogin,
                this.pesquisaAvancadaNomeCompleto,
                this.pesquisaAvancadaCodEmpresa,
                this.pesquisaAvancadaCodDepartamento,
                this.pesquisaAvancadaCodDivisao,
                this.pesquisaAvancadaCPF,
                this.pesquisaAvancadaAtivo,
                this.pesquisaAvancadaCadBD);
        FormUtil.doShow(frmPesquisaAvancadaAgentesFuncaoA,
                t -> {
            boolean frmPesquisaAvancadaAgentesFuncaoAFechadoAoPesquisar = frmPesquisaAvancadaAgentesFuncaoA.isFechadoAoPesquisar();
            if (frmPesquisaAvancadaAgentesFuncaoAFechadoAoPesquisar) {
                this.pesquisaAvancadaLogin = frmPesquisaAvancadaAgentesFuncaoA.edtLogin.getValue().asString();
                this.pesquisaAvancadaNomeCompleto = frmPesquisaAvancadaAgentesFuncaoA.edtNome.getValue().asString();
                this.pesquisaAvancadaCodEmpresa = frmPesquisaAvancadaAgentesFuncaoA.cboEmpresa.getValue().asLong();
                this.pesquisaAvancadaCodDepartamento = frmPesquisaAvancadaAgentesFuncaoA.cboDepartamento.getValue().asInteger();
                this.pesquisaAvancadaCodDivisao = frmPesquisaAvancadaAgentesFuncaoA.cboDivisao.getValue().asInteger();
                this.pesquisaAvancadaCPF = frmPesquisaAvancadaAgentesFuncaoA.edtCPF.getValue().asString();
                this.pesquisaAvancadaAtivo = frmPesquisaAvancadaAgentesFuncaoA.cboAtivo.getValue().asString();
                this.pesquisaAvancadaCadBD = frmPesquisaAvancadaAgentesFuncaoA.cboCadBD.getValue().asString();
                this.definirVisibilidadeBtnPesquisaAvancadaAgentesFuncao();
                this.carregarGridAgentesFuncao(this.codFuncao,
                        this.pesquisaAvancadaLogin,
                        this.pesquisaAvancadaNomeCompleto,
                        this.pesquisaAvancadaCodEmpresa,
                        this.pesquisaAvancadaCodDepartamento,
                        this.pesquisaAvancadaCodDivisao,
                        this.pesquisaAvancadaCPF,
                        this.pesquisaAvancadaAtivo,
                        this.pesquisaAvancadaCadBD);
            }
        });
    }

    private void definirVisibilidadeBtnPesquisaAvancadaAgentesFuncao() {
        this.btnLimparPesquisaAvancada.setVisible(!(this.pesquisaAvancadaLogin.isEmpty()
                && this.pesquisaAvancadaNomeCompleto.isEmpty()
                && (this.pesquisaAvancadaCodEmpresa == 0L)
                && (this.pesquisaAvancadaCodDepartamento == 0)
                && (this.pesquisaAvancadaCodDivisao == 0)
                && (this.pesquisaAvancadaCPF.isEmpty())
                && this.pesquisaAvancadaAtivo.equals(Ativo.TODOS.getCodigo())
                && this.pesquisaAvancadaCadBD.equals(CadBD.TODOS.getCodigo())));
    }

    @Override
    public void btnLimparPesquisaAvancadaClick(Event<Object> event) {
        Dialog.create()
                .title("Confirmação")
                .message("Deseja realmente limpar a pesquisa avançada?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        this.pesquisaAvancadaLogin = "";
                        this.pesquisaAvancadaNomeCompleto = "";
                        this.pesquisaAvancadaCodEmpresa = 0L;
                        this.pesquisaAvancadaCodDepartamento = 0;
                        this.pesquisaAvancadaCodDivisao = 0;
                        this.pesquisaAvancadaCPF = "";
                        this.pesquisaAvancadaAtivo = Ativo.TODOS.getCodigo();
                        this.pesquisaAvancadaCadBD = CadBD.TODOS.getCodigo();
                        this.definirVisibilidadeBtnPesquisaAvancadaAgentesFuncao();
                        this.carregarGridAgentesFuncao(this.codFuncao,
                                this.pesquisaAvancadaLogin,
                                this.pesquisaAvancadaNomeCompleto,
                                this.pesquisaAvancadaCodEmpresa,
                                this.pesquisaAvancadaCodDepartamento,
                                this.pesquisaAvancadaCodDivisao,
                                this.pesquisaAvancadaCPF,
                                this.pesquisaAvancadaAtivo,
                                this.pesquisaAvancadaCadBD);
                    }
                });
    }

    private void definirVisibilidadeColunasGridAgentesFuncaoEBtnPesquisaAvancadaAgentesFuncao() {
        boolean ehCRMParts = EmpresaUtil.isCrmParts();
        this.gridAgentesFuncao.getColumns().forEach(coluna -> {
            if ((coluna.getFieldName().equals("NOME"))
                    || (coluna.getFieldName().equals("NOME_COMPLETO"))) {
                coluna.setVisible(true);
            } else {
                coluna.setVisible(ehCRMParts);
            }
        });
        this.hBoxGradeSeparador01.setVisible(ehCRMParts);
        this.vBoxGradeBtnPesquisaAvancada.setVisible(ehCRMParts);
        this.hBoxGradeSeparador02.setVisible(ehCRMParts);
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.definirVisibilidadeColunasGridAgentesFuncaoEBtnPesquisaAvancadaAgentesFuncao();
    }

}