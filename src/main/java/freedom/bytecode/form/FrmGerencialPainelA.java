package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmGerencialPainelW;
import freedom.bytecode.rn.SelecaoGenericaRNA;
import freedom.client.controls.impl.TFHBox;
import freedom.client.event.Event;
import freedom.client.event.RenderTemplateEvent;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.data.impl.View;
import freedom.util.DateUtils;
import freedom.util.EmpresaUtil;
import org.zkoss.zk.ui.Executions;
import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.Date;

public class FrmGerencialPainelA extends FrmGerencialPainelW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String SCLASS_BASE = "SCLASS_BASE";

    private static final String CL_WHITE = "clWhite";

    private final Integer codEmpresaUsuarioLog = EmpresaUtil.getCodEmpresaUserLogged();

    private Integer dia = 0;

    private Integer mes = 0;

    private Integer ano = 0;

    private String anoMesFiltro = "";

    private Integer idGrupoClasse = 0;

    private String filtroGraficoDiario = "A";

    private String filtroGraficoMensal = "E";

    private final SelecaoGenericaRNA selComboQuebra = new SelecaoGenericaRNA();

    public FrmGerencialPainelA() {
        initForm();
    }

    private void initForm() {
        atribuiMesAnoVigente();
        try {
            hboxMesAtual.setAttribute(FrmGerencialPainelA.SCLASS_BASE, "hbbuttonleftradius");
            hboxUltimos30Dias.setAttribute(FrmGerencialPainelA.SCLASS_BASE, "hbbuttonrightradius");

            hboxEstatistica.setAttribute(FrmGerencialPainelA.SCLASS_BASE, "hbbuttonleftradius");
            hboxUltimos12Meses.setAttribute(FrmGerencialPainelA.SCLASS_BASE, "hbbuttonrightradius");

            // Orione quer o idGrupoClasse fixo por aplicação
            // identificar o idGrupoClasse a ser fixado na tabela bsc_grupo_classe
            // Alterar aqui FrmGerencialPainelA e FrmPainelIndicadoresA

            URL url = new URL(((HttpServletRequest) Executions.getCurrent().getNativeRequest()).getRequestURL().toString());
            if (url.toString().toLowerCase().contains("crmservice")) {
                idGrupoClasse = 3;
            } else if (url.toString().toLowerCase().contains("crmparts")) {
                idGrupoClasse = 6;
            } else if (url.toString().toLowerCase().contains("crmgold")) {
                idGrupoClasse = 1;
            }

            this.rn.carregaPainelUsuario(idGrupoClasse);
            this.cbbPainel.setValue(this.tbPainelGerencialUsuario.getID().asInteger());
            cbbAno.setValue(ano);
            cbbMes.setValue(mes);
            rn.carregaComboEmpresa();
            cbbEmpresasCruzadas.setValue(codEmpresaUsuarioLog);
        } catch (DataException | MalformedURLException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
        atualizarPainelIndicadores();
        if (dia <= 15) {
            setFiltroGraficoDiario("U");
        } else {
            setFiltroGraficoDiario("A");
        }
        setFiltroGraficoMensal("E");
        atualizaGraficoMensal();
        atualizaGraficoDiario();
    }

    private void atribuiMesAnoVigente() {
        try {
            Date dataAtual = DateUtils.getDbDate();
            Calendar cal = Calendar.getInstance();
            cal.setTime(dataAtual);
            dia = cal.get(Calendar.DAY_OF_MONTH);
            mes = cal.get(Calendar.MONTH) + 1;
            ano = cal.get(Calendar.YEAR);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void formatMesAno() {
        //Formata e guarda ano/Mes para padrao da consulta nas view
        ano = cbbAno.getValue().asInteger();
        mes = cbbMes.getValue().asInteger();
        DecimalFormat fMes = new DecimalFormat("00");
        anoMesFiltro = ano.toString() + "/" + fMes.format(mes);
    }

    @Override
    public void btnPreferenciaClick(final Event<Object> event) {
        FrmGerencialPainelPreferenciaA frm = new FrmGerencialPainelPreferenciaA(idGrupoClasse);
        FormUtil.doShow(frm, t -> {
            if (frm.isOk()) {
                atualizaPreferencia();
            }
        });
    }

    private void atualizaPreferencia() {
        try {
            rn.carregaPainelUsuario(idGrupoClasse);
            cbbPainel.setValue(tbPainelGerencialUsuario.getID().asInteger());
            cbbAno.setValue(ano);
            cbbMes.setValue(mes);
            atualizarPainelIndicadores();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void atualizarPainelIndicadores() {
        try {
            formatMesAno();
            rn.atualizaPainel(cbbPainel.getValue().asInteger(), cbbEmpresasCruzadas.getValue().asInteger(), anoMesFiltro);
            vboxPrincipal.invalidate();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnAtualizarClick(final Event<Object> event) {
        atualizarPainelIndicadores();
    }

    @Override
    public void cbbEmpresasCruzadasChange(final Event<Object> event) {
        atualizarPainelIndicadores();
    }

    @Override
    public void cbbEmpresasCruzadasClearClick(final Event<Object> event) {
        atualizarPainelIndicadores();
    }

    @Override
    public void cbbPainelChange(final Event<Object> event) {
        atualizarPainelIndicadores();
    }

    @Override
    public void cbbMesChange(final Event<Object> event) {
        atualizarPainelIndicadores();
    }

    @Override
    public void cbbAnoChange(final Event<Object> event) {
        atualizarPainelIndicadores();
    }

    @Override
    public void gridGerencialColumns0RenderTemplate(RenderTemplateEvent<Value> event) {
        try {
            View dados = tbGridGerencialIndicadores.getView();
            dados.gotoBookmark(event.getBookmark());

            // Imagem da placa
            String nomeComponent = event.getTarget().getName();
            if ((nomeComponent != null)
                    && (nomeComponent.equals("hboxLinhaTemplate"))) {
                if (dados.getField("LINHA").asInteger() % 2 == 0) {
                    ((TFHBox) event.getTarget()).setColor("#EAECEA");
                } else {
                    ((TFHBox) event.getTarget()).setColor(FrmGerencialPainelA.CL_WHITE);
                }
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("ERRO", ex);
        }
    }

    @Override
    public void tbGridGerencialIndicadoresAfterScroll(final Event<Object> event) {
        atualizaGraficoMensal();
        if (!tbGridGerencialIndicadores.getBSC_VIEW_DIARIA().asString().equals("")) {
            atualizaGraficoDiario();
        } else {
            vboxGraficoDiario.setVisible(false);
        }
        atualizaComboGraficoQuebra();
    }

    private void atualizaGraficoMensal() {
        try {

            if (tbGridGerencialIndicadores.count() > 0) {
                vboxGraficoMensal.setVisible(true);

                if (filtroGraficoMensal.equals("E")) {
                    tbGraficoIndicadoresMes.close();
                    if (!tbGridGerencialIndicadores.getBSC_VIEW_NOME().asString().equals("") && !tbGridGerencialIndicadores.getBSC_VIEW_CAMPO().asString().equals("")) {
                        rn.carregaGraficoMensal(cbbEmpresasCruzadas.getValue().asInteger(), ano, mes,
                                tbGridGerencialIndicadores.getBSC_VIEW_NOME().asString(), tbGridGerencialIndicadores.getBSC_VIEW_CAMPO().asString());
                    }
                    int tbGraficoIndicadoresMesCount = this.tbGraficoIndicadoresMes.count();
                    this.chartbarEstatistica.setVisible(tbGraficoIndicadoresMesCount > 0);
                } else {
                    tbGraficoIndUlt12Mes.disableControls();
                    if (!tbGridGerencialIndicadores.getBSC_VIEW_CAMPO().asString().equals("") && !tbGridGerencialIndicadores.getBSC_VIEW_NOME().asString().equals("")) {
                        tbGraficoIndUlt12Mes.copyFrom(rn.getTableGraficoUltimos12Meses(tbGridGerencialIndicadores.getBSC_VIEW_CAMPO().asString(),
                                tbGridGerencialIndicadores.getBSC_VIEW_NOME().asString(),
                                cbbEmpresasCruzadas.getValue().asInteger(),
                                ano,
                                mes), true, true);
                    }
                    int tbGraficoIndUlt12MesCount = this.tbGraficoIndUlt12Mes.count();
                    this.chartbarUltimos12Meses.setVisible(tbGraficoIndUlt12MesCount > 0);
                    this.tbGraficoIndUlt12Mes.enableControls();

                }
            } else {
                vboxGraficoMensal.setVisible(false);
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void atualizaGraficoDiario() {
        try {
            tbGraficoIndicadoresDiario.disableControls();
            if (!tbGridGerencialIndicadores.getBSC_VIEW_CAMPO().asString().equals("") && !tbGridGerencialIndicadores.getBSC_VIEW_DIARIA().asString().equals("")) {
                tbGraficoIndicadoresDiario.copyFrom(rn.getTableGraficoDiario(tbGridGerencialIndicadores.getBSC_VIEW_CAMPO().asString(),
                        tbGridGerencialIndicadores.getBSC_VIEW_DIARIA().asString(),
                        cbbEmpresasCruzadas.getValue().asInteger(),
                        ano,
                        mes,
                        filtroGraficoDiario), true, true);
            }
            int tbGraficoIndicadoresDiarioCount = this.tbGraficoIndicadoresDiario.count();
            this.vboxGraficoDiario.setVisible(tbGraficoIndicadoresDiarioCount > 0);
            this.tbGraficoIndicadoresDiario.enableControls();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void setFiltroGraficoDiario(String tipo) {
        String colorDefault = FrmGerencialPainelA.CL_WHITE;
        String colorDestaque = "clSilver";
        hboxMesAtual.setColor(colorDefault);
        hboxUltimos30Dias.setColor(colorDefault);
        if (tipo.equals("A")) {
            hboxMesAtual.setColor(colorDestaque);
            filtroGraficoDiario = "A";
        } else {
            hboxUltimos30Dias.setColor(colorDestaque);
            filtroGraficoDiario = "U";
        }
        if (!tbGridGerencialIndicadores.getBSC_VIEW_DIARIA().asString().equals("")) {
            atualizaGraficoDiario();
        }
    }

    @Override
    public void hboxMesAtualClick(final Event<Object> event) {
        setFiltroGraficoDiario("A");
    }

    @Override
    public void hboxUltimos30DiasClick(final Event<Object> event) {
        setFiltroGraficoDiario("U");
    }

    private void atualizaComboGraficoQuebra() {
        try {
            tbComboQuebra.close();
            if (tbGridGerencialIndicadores.getID().asInteger() > 0) {
                rn.carregaComboGraficoQuebra(cbbEmpresasCruzadas.getValue().asInteger(), ano, mes, tbGridGerencialIndicadores.getID().asInteger());
            }

            if (tbComboQuebra.count() > 0) {
                cbbQuebra.setValue(tbComboQuebra.getID_QUEBRA().asInteger());
                vboxGraficoQuebra.setVisible(true);
                carregaGraficoQuebra();
            } else {
                vboxGraficoQuebra.setVisible(false);
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void cbbQuebraChange(final Event<Object> event) {
        this.carregaGraficoQuebra();
    }

    private void comboSelecaoQuebra() {
        try {
            tbSelecaoGenerica.close();
            String view = tbComboQuebra.getBSC_VIEW_NOME().asString();
            String key = "QUEBRA_DESCRICAO AS KEY";
            String desc = "QUEBRA_DESCRICAO";
            String filtro = "cod_empresa = " + cbbEmpresasCruzadas.getValue().asInteger() + " and ano_mes = '" + anoMesFiltro + "'";
            selComboQuebra.setValoresIniciais(view, key, desc, filtro, 0);
            tbSelecaoGenerica.disableControls();
            tbSelecaoGenerica.copyFrom(selComboQuebra.getTableGenerica("", ""), true, true);
            tbSelecaoGenerica.enableControls();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void carregaGraficoQuebra() {
        try {
            chartPizzaQuebra.setLegendAlign("left");
            chartPizzaQuebra.setLegendVerticalAlign("top");
            chartPizzaQuebra.applyProperties();

            String view = tbComboQuebra.getBSC_VIEW_NOME().asString();
            String campo = tbComboQuebra.getBSC_VIEW_CAMPO().asString();
            rn.carregaGraficoQuebra(campo, view, "", cbbEmpresasCruzadas.getValue().asInteger(), anoMesFiltro);
            int tbGraficoIndicadoresQuebraCount = this.tbGraficoIndicadoresQuebra.count();
            this.vboxGraficoQuebra.setVisible(tbGraficoIndicadoresQuebraCount > 0);

        } catch (DataException ex) {
            if (tbGraficoIndicadoresQuebra.count() == 0) {
                vboxGraficoQuebra.setVisible(false);
            } else {
                EmpresaUtil.showError("Erro", ex);
            }
        }
    }

    @Override
    public void hboxEstatisticaClick(final Event<Object> event) {
        setFiltroGraficoMensal("E");
    }

    @Override
    public void hboxUltimos12MesesClick(final Event<Object> event) {
        setFiltroGraficoMensal("U");
    }

    private void setFiltroGraficoMensal(String tipo) {
        String colorDefault = FrmGerencialPainelA.CL_WHITE;
        String colorDestaque = "clSilver";
        hboxEstatistica.setColor(colorDefault);
        hboxUltimos12Meses.setColor(colorDefault);
        if (tipo.equals("E")) {
            hboxEstatistica.setColor(colorDestaque);
            filtroGraficoMensal = "E";
            chartbarEstatistica.setVisible(true);
            chartbarUltimos12Meses.setVisible(false);
        } else {
            hboxUltimos12Meses.setColor(colorDestaque);
            filtroGraficoMensal = "U";
            chartbarEstatistica.setVisible(false);
            chartbarUltimos12Meses.setVisible(true);
        }
        atualizaGraficoMensal();
    }

}