package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.client.util.Dialog;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmBtoBCadastroA extends FrmBtoBCadastroW {

    private static final long serialVersionUID = 20130827081850L;

    @Override
    public void FrmBtoBCadastrokeyActionExcluir(Event<Object> event) {
        excluir();
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        excluir();
    }

    @Override
    public void gridPrincipalClickImageDelete(Event<Object> event) {
        excluir();
    }

    void excluir() {
        try {
            tbBtobEnderecosCadastrados.close();
            tbBtobEnderecosCadastrados.addFilter("ID_LINK");
            tbBtobEnderecosCadastrados.addParam("ID_LINK", tbBtobLink.getID_LINK());
            tbBtobEnderecosCadastrados.open();
            if (tbBtobEnderecosCadastrados.isEmpty()) {
                onExcluir();
            } else {
                EmpresaUtil.showMessage("Crm Parts", "Não pode ser apagado, já tem cadastro utilizando.");
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

}
