package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmFichaItemEliminarReservaW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;
import lombok.Getter;

public class FrmFichaItemEliminarReservaA extends FrmFichaItemEliminarReservaW {

    private static final long serialVersionUID = 20130827081850L;

    @Getter
    private boolean ok;

    public double codEmpresa;

    private void excluir() {

    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        boolean rdButtonEliminarParcialChecked = this.rdButtonEliminarParcial.isChecked();
        if (rdButtonEliminarParcialChecked) {
            int qtdeEliminarReserva = this.edtQtdeEliminarReserva.getValue().asInteger();
            if (qtdeEliminarReserva <= 0) {
                String mensagem = (
                        Constantes.O_CAMPO
                                + this.lblQtdeASerLiberada.getCaption()
                                + Constantes.DEVE_SER_PREENCHIDO_COM_VALOR_MAIOR_QUE_0_ZERO
                );
                Dialog.create()
                        .showNotificationInfo(
                                mensagem
                                , Constantes.AFTER_CENTER     // A mensagem aparece abaixo da âncora, alinhada ao centro
                                , 10000                       // Tempo em milissegundos para exibir a mensagem
                                , this.edtQtdeEliminarReserva // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                , true                        // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
                this.edtQtdeEliminarReserva.setFocus();
                this.edtQtdeEliminarReserva.setSelectionRange(
                        0
                        ,(this.edtQtdeEliminarReserva.getValue().asString().length() + 1)
                );
            return;
            }
            int qtdeReservada = this.edtQtdeReservada.getValue().asInteger();
            if (qtdeEliminarReserva >= qtdeReservada) {
                String mensagem = (
                        Constantes.O_CAMPO
                                + this.lblQtdeASerLiberada.getCaption()
                                + "\" deve ser preenchido com valor menor que o campo \""
                                + this.lblQtdeReservada.getCaption()
                                + "\"."
                );
                Dialog.create()
                        .showNotificationInfo(
                                mensagem
                                ,Constantes.AFTER_CENTER     // A mensagem aparece abaixo da âncora, alinhada ao centro
                                ,10000                       // Tempo em milissegundos para exibir a mensagem
                                ,this.edtQtdeEliminarReserva // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                ,true                        // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
                this.edtQtdeEliminarReserva.setFocus();
                this.edtQtdeEliminarReserva.setSelectionRange(
                        0
                        ,(this.edtQtdeEliminarReserva.getValue().asString().length() + 1)
                );
                return;
            }
        }
        String observacao = this.edtObservacao.getValue().asString();
        boolean observacaoEmpty = observacao.isEmpty();
        if (observacaoEmpty) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblObservacao.getCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                            ,10000                   // Tempo em milissegundos para exibir a mensagem
                            ,this.edtObservacao      // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtObservacao.setFocus();
            return;
        }
        int quantidadeExcluir;
        if (rdButtonEliminarParcialChecked) {
            quantidadeExcluir = this.edtQtdeEliminarReserva.getValue().asInteger();
        } else {
            quantidadeExcluir = this.edtQtdeReservada.getValue().asInteger();
        }
        int controleReserva = this.edtControleReserva.getValue().asInteger();
        String loginUsuarioLogado = EmpresaUtil.getUserLogged();
        this.excluirReservaParcialItem(
                this.codEmpresa
                ,controleReserva
                ,quantidadeExcluir
                ,observacao
                ,loginUsuarioLogado
        );
        Dialog.create()
                .showToastInfo(
                        "A reserva foi excluída." // Mensagem
                        ,Constantes.MIDDLE_CENTER // Posição de exibição - No meio da tela ao centro
                        ,10000                    // Milissegundos de exibição da mensagem
                        ,true                     // Exibe X para fechá-la antes de encerrar o tempo de exibição
                );
        this.ok = true;
        this.close();
    }

    private void excluirReservaParcialItem(
            double codEmpresa
            ,double controleReserva
            ,double qtdeExc
            ,String observacao
            ,String loginUsuarioLogado
    ) {
        try {
            this.rn.excluirReservaParcialItem(
                    codEmpresa
                    ,controleReserva
                    ,qtdeExc
                    ,observacao
                    ,loginUsuarioLogado
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao excluir a reserva parcial do item"
                    ,dataException
            );
        }
    }

    public void setarEliminarReservaTotal() {
        this.edtQtdeEliminarReserva.clear();
        this.edtQtdeEliminarReserva.setEnabled(false);
        this.edtObservacao.setFocus();
    }

    public void setarEliminarReservaParcial() {
        this.edtQtdeEliminarReserva.setEnabled(true);
        this.edtQtdeEliminarReserva.setFocus();
    }

    @Override
    public void rdButtonEliminarTotalCheck(Event<Object> event) {
        this.setarEliminarReservaTotal();
    }

    @Override
    public void rdButtonEliminarParcialCheck(Event<Object> event) {
        this.setarEliminarReservaParcial();
    }

    @Override
    public void edtObservacaoEnter(Event<Object> event) {
        this.btnExcluirClick(event);
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.edtObservacao.setFocus();
    }

}