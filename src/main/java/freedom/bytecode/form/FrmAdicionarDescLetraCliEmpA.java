package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAdicionarDescLetraCliEmpW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;
import lombok.Getter;

@Getter
public class FrmAdicionarDescLetraCliEmpA extends FrmAdicionarDescLetraCliEmpW {

    private static final long serialVersionUID = 20130827081850L;

    private String codigoDaLetraDeDesconto = "";

    private double descontoParaOCliente = 0.0;

    private long codigoDaEmpresaLetraDeDesconto = 0L;

    public FrmAdicionarDescLetraCliEmpA() {
        try {
            this.rn.carregarLetrasDeDeconto();
            this.setCaption("Adicionar desconto por letra/empresa para o cliente");
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar letras de desconto"
                    ,dataException
            );
        }
        try {
            this.rn.carregarEmpresaLetraDesconto();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar empresas de desconto"
                    ,dataException
            );
        }
    }

    public void setCodigoDaLetraDeDesconto(String codigoDaLetraDeDesconto) {
        this.codigoDaLetraDeDesconto = codigoDaLetraDeDesconto;
        this.cboLetraDeDesconto.setValue(this.codigoDaLetraDeDesconto);
    }

    public void setDescontoParaOCliente(double descontoParaOCliente) {
        this.descontoParaOCliente = descontoParaOCliente;
        this.edtDescontoClienteEmpresa.setValue(this.descontoParaOCliente);
    }

    public void setCodigoDaEmpresaLetraDeDesconto(long codigoDaEmpresaLetraDeDesconto) {
        this.codigoDaEmpresaLetraDeDesconto = codigoDaEmpresaLetraDeDesconto;
        this.cboEmpresaLetraDesconto.setValue(this.codigoDaEmpresaLetraDeDesconto);
    }

    @Override
    public void cboLetraDeDescontoChange(final Event<Object> event) {
        double percentualDeDescontoDaLetra = this.tbItensPerDescFlag.getDESCONTO().asDecimal();
        if (this.edtDescontoClienteEmpresa.getValue().asDecimal().equals(0.0)) {
            this.edtDescontoClienteEmpresa.setValue(percentualDeDescontoDaLetra);
            this.edtDescontoClienteEmpresa.select();
        }
        this.edtDescontoClienteEmpresa.setFocus();
    }

    private void salvar() {
        this.codigoDaLetraDeDesconto = this.cboLetraDeDesconto.getValue().asString();
        boolean codigoDaLetraDeDescontoEmpty = this.codigoDaLetraDeDesconto.isEmpty();
        if (codigoDaLetraDeDescontoEmpty) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblLetraDeDesconto.getCaption()
                            + "\" deve ser preenchido."
            );
            Dialog.create()
                    .title(Constantes.INFORMACAO)
                    .message(mensagem)
                    .showInformation((Event event1) -> FreedomUtilities.invokeLater(() -> {
                        this.cboLetraDeDesconto.setFocus();
                        this.cboLetraDeDesconto.setOpen(true);
                    }));
            return;
        }
        this.descontoParaOCliente = this.edtDescontoClienteEmpresa.getValue().asDecimal();
        if ((this.descontoParaOCliente > 99.99)
                || (this.descontoParaOCliente < 0.01)) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblDecimalLetraEmpresa.getCaption()
                            + "\" deve ser preenchido com o valor entre \"0,01\" e \"99,99\"."
            );
            Dialog.create()
                    .title(Constantes.INFORMACAO)
                    .message(mensagem)
                    .showInformation((Event event1) -> FreedomUtilities.invokeLater(() -> this.edtDescontoClienteEmpresa.setFocus()));
            return;
        }
        this.codigoDaEmpresaLetraDeDesconto = this.cboEmpresaLetraDesconto.getValue().asLong();
        if (this.codigoDaEmpresaLetraDeDesconto == 0.0) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblEmpresaLetraDesconto.getCaption()
                            + "\" deve ser preenchido."
            );
            Dialog.create()
                    .title(Constantes.INFORMACAO)
                    .message(mensagem)
                    .showInformation((Event event1) -> FreedomUtilities.invokeLater(() -> {
                        this.cboEmpresaLetraDesconto.setFocus();
                        this.cboEmpresaLetraDesconto.setOpen(true);
                    }));
            return;
        }
        this.close();
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        this.salvar();
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        String letra = this.cboLetraDeDesconto.getText().trim();
        if (letra.isEmpty()) {
            FreedomUtilities.invokeLater(() -> {
                this.cboLetraDeDesconto.setFocus();
                this.cboLetraDeDesconto.setOpen(true);
            });
        } else {
            this.edtDescontoClienteEmpresa.setFocus();
            this.edtDescontoClienteEmpresa.setSelectionRange(
                    0
                    ,(this.edtDescontoClienteEmpresa.getValue().asString().length() + 1)
            );
        }

    }

    @Override
    public void cboEmpresaLetraDescontoEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtDescontoClienteEmpresaEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void cboLetraDeDescontoEnter(Event<Object> event) {
        this.salvar();
    }

}
