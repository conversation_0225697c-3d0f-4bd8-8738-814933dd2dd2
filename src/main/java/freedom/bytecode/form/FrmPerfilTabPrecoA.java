package freedom.bytecode.form;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.form.wizard.*;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import freedom.client.util.Dialog;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.EmpresaUtil;
import freedom.util.WorkListFactory;

public class FrmPerfilTabPrecoA extends FrmPerfilTabPrecoW {
    private static final long serialVersionUID = 20130827081850L;
    private String usuarioLogado = EmpresaUtil.getUserLogged();    

    public boolean podeAlterarTabPreco() {
        boolean podeAlterar;
        try {
            podeAlterar = EmpresaUtil.validarAcesso("K0305");
            if (podeAlterar) {
                tbPerfilTabPreco.close();
                tbPerfilTabPreco.addParam("COD_EMPRESA", EmpresaUtil.getCodEmpresaUserLogged());
                tbPerfilTabPreco.addParam("USUARIO_LOGADO", usuarioLogado);
                tbPerfilTabPreco.open();
            }
        } catch (Exception e) {
            podeAlterar = false;
            Dialog.create()
                    .title("Falha ao validar acesso")
                    .message(e.getMessage())
                    .showException(e);
        }        
        return podeAlterar;
    }

    @Override
    public void btnSalvarClick(final Event event) {
        try {
            EMPRESAS_USUARIOS tbUser = new EMPRESAS_USUARIOS("tbUser");
            tbUser.addFilter("NOME");
            tbUser.addParam("NOME", usuarioLogado);
            tbUser.open();
            tbUser.edit();
            tbUser.setCOD_EMPRESA_DIVISAO(tbPerfilTabPreco.getCOD_EMPRESA_DIVISAO());
            tbUser.post();
            tbUser.applyUpdates();
            close();
        } catch (Exception e) {
            Dialog.create()
                    .title("Falha ao salvar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

}
