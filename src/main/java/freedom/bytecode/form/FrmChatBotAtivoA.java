package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmChatBotAtivoW;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmChatBotAtivoA extends FrmChatBotAtivoW {

    private static final long serialVersionUID = 20130827081850L;

    private boolean isEdicaoDisparo = false;

    private boolean isEdicaoChatBot = false;

    private boolean isEdicaoTemplate = false;

    private boolean isNovoTemplate = false;

    public FrmChatBotAtivoA() {
        try {
            rn.carregarDescartes();
            rn.carregarEmailModelo();
            rn.carregarCadastroWhatsapp();
            rn.carregarEmpresas();
            rn.carregarGridDisparo("", true);
            enabledEdicaoDisparo(false);
            enabledEdicaoChatBot(false);
            enabledEdicaoTemplate(false);
            tabListagem.setFontSize(12);
            tabCadastro.setFontSize(12);
            tabChatBot.setFontSize(12);
        } catch (DataException e) {
            Dialog.create().title("Erro ao processar Filtro").message(e.getMessage()).showException(e);
        }
    }


    @Override
    public void filtrarGrid(final Event<Object> event) {
        try {
            rn.carregarGridDisparo(fTbxDisparoDescricao.getValue().asString(), fchkDisparoAtivo.getValue().asStrBool());
        } catch (DataException e) {
            Dialog.create().title("Erro ao processar Filtro").message(e.getMessage()).showException(e);
        }
    }

    private void enabledEdicaoDisparo(Boolean isEnabled) {
        isEdicaoDisparo = isEnabled;
        btnConsultarDisparo.setEnabled(!isEdicaoDisparo);
        btnAlterarDisparo.setEnabled(!isEdicaoDisparo);
        btnSalvarDisparo.setEnabled(isEdicaoDisparo);
        btnCancelarDisparo.setEnabled(isEdicaoDisparo);
        gridDisparo.setEnabled(!isEdicaoDisparo);
        etxbDisparoDescricao.setEnabled(isEdicaoDisparo);
        ecmbDisparoTemplate.setEnabled(isEdicaoDisparo);
        echkDisparoAtivo.setEnabled(isEdicaoDisparo);
        cbbAplServico.setEnabled(isEdicaoDisparo);
    }

    private void enabledEdicaoChatBot(Boolean isEnabled) {
        isEdicaoChatBot = isEnabled;
        btnAlterarChatBot.setEnabled(isEdicaoDisparo && !isEdicaoChatBot);
        btnSalvarChatBot.setEnabled(isEdicaoDisparo && isEdicaoChatBot);
        btnCancelarChatBot.setEnabled(isEdicaoDisparo && isEdicaoChatBot);
        gridChatBot.setEnabled(!(isEdicaoDisparo && isEdicaoChatBot));
        // Regra passada por Orione - 27/04/2020 [Fábio Ramires - Luiz Fernando]
        // Link só pro 1
        // Motivo de Perda pro 2,3,4 e 5
        int idItemChatBot = tbDisparoChatbot.getID_ITEM().asInteger();
        etxChatBotbDescricaoAlternativa.setEnabled(isEdicaoDisparo && isEdicaoChatBot);
        etxbChatBotMascara.setEnabled(isEdicaoDisparo && isEdicaoChatBot);
        echkChatBotAtivo.setEnabled(isEdicaoDisparo && isEdicaoChatBot);
        etxbChatBotLink.setEnabled(isEdicaoDisparo && isEdicaoChatBot && idItemChatBot == 1);
        ecmbMotivoPerda.setEnabled(isEdicaoDisparo && isEdicaoChatBot && (idItemChatBot == 2 || idItemChatBot == 3 || idItemChatBot == 4 || idItemChatBot == 5));
        ememoChatBotMensagemRetorno.setEnabled(isEdicaoDisparo && isEdicaoChatBot);
    }

    private void enabledEdicaoTemplate(Boolean isEnabled) {
        isEdicaoTemplate = isEnabled;
        btnNovoTemplate.setEnabled(isEdicaoDisparo && !isEdicaoTemplate);
        btnExcluirTemplate.setEnabled(isEdicaoDisparo && !isEdicaoTemplate);
        btnAlterarTemplate.setEnabled(isEdicaoDisparo && !isEdicaoTemplate);
        btnSalvarTemplate.setEnabled(isEdicaoDisparo && isEdicaoTemplate);
        btnCancelarTemplate.setEnabled(isEdicaoDisparo && isEdicaoTemplate);
        gridTemplate.setEnabled(!(isEdicaoDisparo && isEdicaoTemplate));
        ecmbTemplateEmpresa.setEnabled(isEdicaoDisparo && isEdicaoTemplate);
        ecmbTemplateTemplate.setEnabled(isEdicaoDisparo && isEdicaoTemplate);
    }

    @Override
    public void btnAlterarDisparoClick(final Event<Object> event) {
        try {
            if (rn.nenhumRegistroGridDisparoSelecionado()) {
                EmpresaUtil.showWarning("Erro ao tentar editar Disparo", "Obrigatório Selecionar um Registro a ser Alterado.");
                PageControlDisparo.selectTab(0);
                return;
            }
            rn.alterarDisparo();
            enabledEdicaoDisparo(true);
            enabledEdicaoChatBot(false);
            enabledEdicaoTemplate(false);
            if (PageControlDisparo.getSelectedIndex() == 0) {
                PageControlDisparo.selectTab(1);
            }
            etxbDisparoDescricao.setFocus();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao Editar Registro Markup Modelo", e);
        }
    }

    @Override
    public void btnCancelarDisparoClick(final Event<Object> event) {
        try {
            rn.cancelarAlteracaoDisparo();
            enabledEdicaoDisparo(false);
            enabledEdicaoChatBot(false);
            enabledEdicaoTemplate(false);
            PageControlDisparo.selectTab(0);
            rn.carregarGridDisparo(fTbxDisparoDescricao.getValue().asString(), fchkDisparoAtivo.getValue().asStrBool());
            rn.selecionarRegistroAnteriorGridDisparo();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao Cancelar", e);
        }
    }

    @Override
    public void btnSalvarDisparoClick(final Event<Object> event) {
        try {
            if (isEdicaoChatBot) {
                salvarChatbotClick();
            }
            if (isEdicaoChatBot) {
                return;
            }
            if (isEdicaoTemplate) {
                SalvarTemplateClick();
            }
            if (isEdicaoTemplate) {
                return;
            }
            if (validaCamposDisparo()) {
                rn.salvarDisparo();
                enabledEdicaoDisparo(false);
                enabledEdicaoChatBot(false);
                enabledEdicaoTemplate(false);
                PageControlDisparo.selectTab(0);
                rn.carregarGridDisparo(fTbxDisparoDescricao.getValue().asString(), fchkDisparoAtivo.getValue().asStrBool());
                rn.selecionarRegistroAnteriorGridDisparo();
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao tentar salvar.", e);
        }
    }

    public boolean validaCamposDisparo() {
        if (etxbDisparoDescricao.getValue().asString().equals("")) {
            Dialog.create().title("Atenção").message("É obrigatório preencher o campo Descrição.").showInformation(((EventListener) event1 -> {
                PageControlDisparo.selectTab(1);
                etxbDisparoDescricao.setFocus();
            }));
            return false;
        }
        if (ecmbDisparoTemplate.getValue().isNull()) {
            Dialog.create().title("Atenção").message("É obrigatório selecionar um Template").showInformation(((EventListener) event1 -> {
                PageControlDisparo.selectTab(1);
                ecmbDisparoTemplate.setFocus();
            }));
            return false;
        }
        return true;
    }

    public boolean validaCamposTemplates() {
        try {
            if (ecmbTemplateEmpresa.getValue().asString().equals("")) {
                Dialog.create().title("Atenção").message("É obrigatório selecionar uma Empresa.").showInformation(((EventListener) event1 -> {
                    PageControlDisparo.selectTab(3);
                    ecmbTemplateEmpresa.setFocus();
                }));
                return false;
            }
            if (ecmbTemplateTemplate.getValue().isNull()) {
                Dialog.create().title("Atenção").message("É obrigatório selecionar um Template").showInformation(((EventListener) event1 -> {
                    PageControlDisparo.selectTab(3);
                    ecmbTemplateTemplate.setFocus();
                }));
                return false;
            }
            int idEmpresa = ecmbTemplateEmpresa.getValue().asInteger();
            int idTemplate = ecmbTemplateTemplate.getValue().asInteger();
            String validacaoEmpresaTemplate = rn.validarEmpresaXTemplete(idEmpresa, idTemplate);
            if (!validacaoEmpresaTemplate.equals("")) {
                Dialog.create().title("Atenção").message(validacaoEmpresaTemplate).showInformation(((EventListener) event1 -> {
                    PageControlDisparo.selectTab(3);
                    ecmbTemplateTemplate.setFocus();
                }));
                return false;
            }
            if (rn.validarExistenciaEmpresaTemplate(idEmpresa, idTemplate)) {
                Dialog.create().title("Atenção").message("A empresa selecionada já está vinculada a este template.").showInformation(((EventListener) event1 -> {
                    PageControlDisparo.selectTab(3);
                    ecmbTemplateTemplate.setFocus();
                }));
                return false;
            }
            return true;
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao Valildar Item", e);
            return false;
        }
    }

    public boolean validaCamposChatBot() {
        try {
            Integer item = rn.validarMascara();
            if (!item.equals(-1)) {
                Dialog.create().title("Atenção").message("Não pode existir sequência com mascara duplicada. O item " + item.toString() + " já utiliza essa mascara.").showInformation(((EventListener) event1 -> {
                    PageControlDisparo.selectTab(2);
                    etxbChatBotMascara.setFocus();
                }));
                etxbChatBotMascara.setFocus();
                return false;
            }
            return true;
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao Valildar Item", e);
            return true;
        }
    }

    @Override
    public void tbDisparo2AfterScroll(final Event<Object> event) {
        carregarDadosDisparoChatBotSelecionado();
        carregarDadosDisparoTemplateEmpresaSelecionado();
    }

    public void carregarDadosDisparoChatBotSelecionado() {
        try {
            rn.carregarDadosDisparoChatBot();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados do Item chatBot: ", ex);
        }
    }

    public void carregarDadosDisparoTemplateEmpresaSelecionado() {
        try {
            rn.carregarDadosDisparoTemplateEmpresa();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados do Item empresaXtemplate: ", ex);
        }
    }

    @Override
    public void btnSalvarChatBotClick(final Event<Object> event) {
        salvarChatbotClick();
    }

    public void salvarChatbotClick() {
        try {
            if (validaCamposChatBot()) {
                rn.salvarChatBot();
                enabledEdicaoChatBot(false);
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao tentar salvar.", e);
        }
    }

    @Override
    public void btnCancelarChatBotClick(final Event<Object> event) {
        try {
            rn.cancelarChatBot();
            enabledEdicaoChatBot(false);
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao tentar cancelar ChatBot.", e);
        }
    }

    @Override
    public void btnAlterarChatBotClick(final Event event) {
        try {
            if (rn.nenhumRegistroGridDisparoChatBotSelecionado()) {
                EmpresaUtil.showWarning("Erro ao editador chatBot", "Obrigatório Selecionar um Registro a ser Alterado.");
                return;
            }
            rn.alterarChatBot();
            enabledEdicaoChatBot(true);
            etxChatBotbDescricaoAlternativa.setFocus();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao tentar Alterar Dados do Item: ", ex);
        }
    }

    @Override
    public void btnNovoTemplateClick(final Event<Object> event) {
        try {
            isNovoTemplate = true;
            rn.novoTemplate();
            enabledEdicaoTemplate(true);
            ecmbTemplateEmpresa.setFocus();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao tentar criar novo item template: ", e);
        }
    }

    @Override
    public void btnAlterarTemplateClick(final Event<Object> event) {
        try {
            if (rn.nenhumRegistroGridDisparoTemplateEmpresaSelecionado()) {
                EmpresaUtil.showWarning("Erro ao editar templateXempresa", "Obrigatório Selecionar um Registro a ser Alterado.");
                return;
            }
            rn.alterarTemplate();
            enabledEdicaoTemplate(true);
            // ecmbTemplateEmpresa.setEnabled(false);
            ecmbTemplateEmpresa.setFocus();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao tentar alterar item template: ", e);
        }
    }

    @Override
    public void btnSalvarTemplateClick(final Event<Object> event) {
        SalvarTemplateClick();
    }

    public void SalvarTemplateClick() {
        try {
            if (validaCamposTemplates()) {
                String nomeEmpresa = ecmbTemplateEmpresa.getValue().asString() + " - " + ecmbTemplateEmpresa.getText();
                rn.salvarTemplate(nomeEmpresa);
                enabledEdicaoTemplate(false);
                isNovoTemplate = false;
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao tentar salvar  item template: ", e);
        }
    }

    @Override
    public void btnCancelarTemplateClick(final Event<Object> event) {
        try {
            rn.cancelarTemplate();
            if (isNovoTemplate) {
                rn.deletarNovoTempleteCancelado();
            }
            enabledEdicaoTemplate(false);
            isNovoTemplate = false;
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao tentar cancelar  item template: ", e);
        }
    }

    @Override
    public void btnExcluirTemplateClick(final Event<Object> event) {
        try {
            if (rn.nenhumRegistroGridDisparoTemplateEmpresaSelecionado()) {
                EmpresaUtil.showWarning("Erro ao Excluir templateXempresa", "Obrigatório Selecionar um Registro a ser Alterado.");
                return;
            }
            Dialog.create().title("Exclusão empresa x template").message("Tem certeza que deseja excluir o vinculo entre esta empresa e este template?").confirmSimNao((responseSimNao) -> {
                try {
                    if (responseSimNao.equals("1")) {
                        rn.excluirDisparoTemplateEmpresa();
                    }
                } catch (Exception e) {
                    EmpresaUtil.showError("Falha ao tentar Excluir o registro.", e);
                }
            });
        } catch (Exception e) {
            EmpresaUtil.showError("Falha ao tentar excluir item template: ", e);
        }
    }


}
