package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAguardarPagamentoPosSitefW;
import freedom.client.event.Event;
import freedom.data.Value;
import freedom.util.DateUtils;
import freedom.util.EmpresaUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

public class FrmAguardarPagamentoPosSitefA extends FrmAguardarPagamentoPosSitefW {
    private static final long serialVersionUID = 20130827081850L;

    private long horaInicial;

    private long aguardarAte;
    private boolean timerOcupado = false;
    private int pontos = 0;
    private boolean podeFechar = false;

    private boolean jaRecebeu = false;
    private double codEmpresa;
    private boolean emitirNotaFiscal = false;

    private Double idPagamento;
    private boolean usuarioQuerCancelar = false;

    @Override
    public void frmShow(Event<Object> event) {
        consultarPagamentoPos();
        if (this.podeFechar) {
            encerrarAguardarPOS();
        }
        setTimer();
    }

    private void consultarPagamentoPos() {
        String respFunc = rn.consultarPagamentoPos(this.idPagamento);
        if (respFunc.equals("OK")) {
            timerPos.setEnabled(false);
            this.podeFechar = true;
            this.emitirNotaFiscal = true;
            this.jaRecebeu = true;
        } else if (this.usuarioQuerCancelar) {
            this.podeFechar = true;
            this.jaRecebeu = respFunc.equals("PARCIAL");
        } else if ((aguardarAte > 0) && (System.currentTimeMillis() > aguardarAte)) {
            timerPos.setEnabled(false);
            this.podeFechar = true;
            EmpresaUtil.showInformationMessage("Pagamento POS-Sitef: Confirmação de pagamento não localizada, tente novamente, faturamento não será realizado.");
        }
    }

    private void setTimer() {
        long aguardeMinutos = rn.aguardeTempoPosSitefAprovado(this.codEmpresa);
        this.pontos = 1;
        this.horaInicial = System.currentTimeMillis();
        this.aguardarAte = this.horaInicial + (aguardeMinutos * 60 * 1000);
        atualizarStatus();
        timerPos.setInterval(1200);
        timerPos.setRepeats(true);
        timerPos.setEnabled(true);
    }

    private void atualizarStatus() {
        long tmp = System.currentTimeMillis() - this.horaInicial;
        String   msg = "[" + DateUtils.format(new Date(tmp), "mm:ss") + "]  " +
                "Verificando aprovação de pagamento" +
                StringUtils.rightPad("", this.pontos * 2, " .");
        lblStatusIntegracao.setCaption(msg);
        lblStatusIntegracao.setHint("Verificando aprovação de pagamento [" + this.idPagamento + "]'");
        hBoxStatus.invalidate();
    }

    @Override
    public void hBoxCancelarClick(Event<Object> event) {
        usuarioSoliciouCancelar();
    }

    @Override
    public void hBoxSepCancelarClick(Event<Object> event) {
        usuarioSoliciouCancelar();
    }

    @Override
    public void vBoxIconCancelarClick(Event<Object> event) {
        usuarioSoliciouCancelar();
    }

    @Override
    public void iconCancelarClick(Event<Object> event) {
        usuarioSoliciouCancelar();
    }

    @Override
    public void vBoxBtnCancelarClick(Event<Object> event) {
        usuarioSoliciouCancelar();
    }

    public void lblAcaoCancelarClick(Event<Object> event) {
        usuarioSoliciouCancelar();
    }

    private void usuarioSoliciouCancelar() {
        this.usuarioQuerCancelar = true;
    }

    @Override
    public void timerPosTimer(Event<Object> event) {
        if (!this.timerOcupado) {
            this.timerOcupado = true;
            try {
                this.pontos = this.pontos + 1;
                if (this.pontos > 10) {
                    this.pontos = 1;
                }
                consultarPagamentoPos();
                if (this.podeFechar) {
                    encerrarAguardarPOS();
                }
                atualizarStatus();
            } finally {
                this.timerOcupado = false;
            }
        }
    }

    private void encerrarAguardarPOS() {
        if (this.idPagamento > 0) {
            if (!this.jaRecebeu) {
                rn.apagarPagamentoPOS(this.idPagamento);
            }
        } else {
            this.jaRecebeu = true;
        }
        close();
    }

    public String pagamentoPOSOrcMapa(Double codEmpresa, Double codOrcMapa, Double codCliente) {
        Value idPagamento = new Value(null) ;
        this.codEmpresa = codEmpresa;
        String respFunc = rn.gravarPagamentoPOS(this.codEmpresa, codOrcMapa, codCliente, idPagamento);
        this.idPagamento = idPagamento.asDecimal();
        return respFunc;
    }

    public void idPagamentoPOS(Double acodEmpresa, Double aidPagamento){
        this.codEmpresa = acodEmpresa;
        this.idPagamento = aidPagamento;
    }

    public boolean podeEmitirNotaFiscal() {
        return this.emitirNotaFiscal;
    }

    public boolean perguntarSePodeEmitirNFSemSitef() {
        if (this.jaRecebeu) {
            return false;
        }
        if (this.usuarioQuerCancelar) {
            return rn.perguntarSePodeEmitirNFSemSitef();
        } else {
            return false;
        }
    }
}
