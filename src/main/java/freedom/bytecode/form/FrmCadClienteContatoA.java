package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmCadClienteContatoW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;
import freedom.util.StringUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import static freedom.util.ClienteDiversoUtil.getCodClienteMascarado;
import static freedom.util.ClienteDiversoUtil.getNomeCliente;

public class FrmCadClienteContatoA extends FrmCadClienteContatoW {

    private static final long serialVersionUID = 20130827081850L;

    public static final String O_CAMPO = "O campo \"";

    public static final String DEVE_SER_PREENCHIDO = "\" deve ser preenchido.";

    public static final String CONFIRMACAO = "Confirmação";

    public static final String END_CENTER = "end_center";

    public static final String START_CENTER = "start_center";

    @Setter
    private long codCliente = 0L;

    @Getter
    private boolean fechadoAoVoltar = false;

    @Getter
    private boolean aceitar = false;

    @Override
    public void FFormCreate(Event<Object> event) {
        String caption = "Contatos da empresa \""
                + getNomeCliente(this.codCliente)
                + " ["
                + getCodClienteMascarado(this.codCliente)
                + "]"
                + "\"";
        this.setCaption(caption);
        this.carregarCboAreaDeContato();
        this.carregarCboFiltroAreaDeContato();
        //region Definir tamanho máximo
        this.edtDDDResidencial.setMaxlength(2);
        this.edtTelefoneResidencial.setMaxlength(9);
        this.edtDDDCelular.setMaxlength(2);
        this.edtTelefoneCelular.setMaxlength(9);
        this.edtDDDWhatsapp.setMaxlength(2);
        this.edtTelefoneWhatsapp.setMaxlength(9);
        //endregion
        //region Definir campos obrigatórios
        this.edtContato.setRequired(true);
        this.cboContatoSexo.setRequired(false);
        this.cboAreaDeContato.setRequired(true);
        this.edtCPF.setRequired(false);
        this.dtNascimento.setRequired(false);
        this.edtCNH.setRequired(false);
        this.edtPassaporteCarteira.setRequired(false);
        this.edtRG.setRequired(false);
        this.edtOrgaoEmissor.setRequired(false);
        this.dtEmissao.setRequired(false);
        this.cboPoliticamenteExposto.setRequired(false);
        this.edtDDDResidencial.setRequired(false);
        this.edtTelefoneResidencial.setRequired(false);
        this.edtDDDCelular.setRequired(true);
        this.edtTelefoneCelular.setRequired(true);
        this.edtDDDWhatsapp.setRequired(true);
        this.edtTelefoneWhatsapp.setRequired(true);
        this.edtEmail.setRequired(true);
        this.edtFuncao.setRequired(false);
        this.edtTime.setRequired(false);
        this.edtHobby.setRequired(false);
        //endregion
        this.tbsCadastro.setVisible(false);
        this.edtFiltroContato.setFocus();
    }

    @Override
    public void icoLimparTelefoneResidencialClick(Event<Object> event) {
        this.edtDDDResidencial.clear();
        this.edtTelefoneResidencial.clear();
        this.edtTelefoneResidencial.setFocus();
    }

    @Override
    public void icoLimparTelefoneCelularClick(Event<Object> event) {
        this.edtDDDCelular.clear();
        this.edtTelefoneCelular.clear();
        this.edtTelefoneCelular.setFocus();
    }

    @Override
    public void icoLimparTelefoneWhatsappClick(Event<Object> event) {
        this.edtDDDWhatsapp.clear();
        this.edtTelefoneWhatsapp.clear();
        this.edtTelefoneWhatsapp.setFocus();
    }

    private void carregarGrdContatos(long codCliente,
                                     String contato,
                                     String responsavelPelaPesquisaDaFabrica,
                                     String areaDeContato) {
        try {
            this.rn.carregarGrdContatos(codCliente,
                    contato,
                    responsavelPelaPesquisaDaFabrica,
                    areaDeContato);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar a grade",
                    dataException);
        }
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        this.fechadoAoVoltar = true;
        this.close();
    }

    @Override
    public void btnNovoClick(final Event<Object> event) {
        //region Guias - Definir visibilidade e foco
        this.tbsCadastro.setVisible(true);
        this.pgcPrincipal.selectTab(this.tbsCadastro);
        this.tbsListagem.setVisible(false);
        //endregion
        //region Botões - Definir enabled
        this.btnPesquisar.setEnabled(false);
        this.btnNovo.setEnabled(false);
        this.btnAlterar.setEnabled(false);
        this.btnExcluir.setEnabled(false);
        this.btnSalvar.setEnabled(true);
        this.btnCancelar.setEnabled(true);
        //endregion
        //region Limpar campos
        this.edtContato.clear();
        this.cboContatoSexo.clear();
        this.cboAreaDeContato.clear();
        this.edtCPF.clear();
        this.dtNascimento.clear();
        this.edtCNH.clear();
        this.edtPassaporteCarteira.clear();
        this.edtRG.clear();
        this.edtOrgaoEmissor.clear();
        this.dtEmissao.clear();
        this.cboPoliticamenteExposto.clear();
        this.edtDDDResidencial.clear();
        this.edtTelefoneResidencial.clear();
        this.edtDDDCelular.clear();
        this.edtTelefoneCelular.clear();
        this.edtDDDWhatsapp.clear();
        this.edtTelefoneWhatsapp.clear();
        this.edtEmail.clear();
        this.cboEhComprador.clear();
        this.cboEhPreposto.clear();
        this.cboResponsavelPelaPesquisaDaFabrica.clear();
        this.cboResponsavelPelaGarantiaDaMontadora.clear();
        this.edtFuncao.clear();
        this.edtTime.clear();
        this.edtHobby.clear();
        //endregion
        this.edtContato.setFocus();
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        //region Guias - Definir visibilidade e foco
        this.tbsListagem.setVisible(true);
        this.pgcPrincipal.selectTab(this.tbsListagem);
        this.tbsCadastro.setVisible(false);
        //endregion
        //region Botões - Definir enabled
        this.btnPesquisar.setEnabled(true);
        this.btnNovo.setEnabled(true);
        boolean tbClienteContatoNotEmpty = !this.tbClienteContato.isEmpty();
        this.btnAlterar.setEnabled(tbClienteContatoNotEmpty);
        this.btnExcluir.setEnabled(tbClienteContatoNotEmpty);
        this.btnSalvar.setEnabled(false);
        this.btnCancelar.setEnabled(false);
        //endregion
        this.btnPesquisarClick(event);
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        //region Guias - Definir visibilidade e foco
        this.tbsCadastro.setVisible(true);
        this.pgcPrincipal.selectTab(this.tbsCadastro);
        this.tbsListagem.setVisible(false);
        //endregion
        //region Botões - Definir enabled
        this.btnPesquisar.setEnabled(false);
        this.btnNovo.setEnabled(false);
        this.btnAlterar.setEnabled(false);
        this.btnExcluir.setEnabled(false);
        this.btnSalvar.setEnabled(true);
        this.btnCancelar.setEnabled(true);
        //endregion
        this.edtContato.setFocus();
        this.edtContato.setSelectionRange(0,
                (this.edtContato.getValue().asString().length() + 1));
        StringBuilder cpf = new StringBuilder(this.edtCPF.getValue().asString().trim());
        if (cpf.length() != 11) {
            while (cpf.length() < 11) {
                cpf.insert(0, "0");
            }
            if (cpf.toString().replace("0", "").isEmpty()) {
                this.edtCPF.clear();
            } else {
                this.edtCPF.setValue(cpf.toString());
            }
        }
    }

    private void excluirContato() {
        try {
            this.rn.excluirContato();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao excluir o contato",
                    dataException);
        }
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        String contato = this.tbClienteContato.getCONTATO().asString().trim();
        String mensagem = "Deseja realmente excluir o contato \""
                + contato
                + "\"?";
        Dialog.create()
                .title(FrmCadClienteContatoA.CONFIRMACAO)
                .message(mensagem)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        this.excluirContato();
                        this.btnPesquisarClick(event);
                    }
                });
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        //region Validar preenchimento dos campos
        //region CPF
        long cpf = this.edtCPF.getValue().asLong();
        StringBuilder cpfSB = new StringBuilder(String.valueOf(cpf));
        while (cpfSB.length() < 11) {
            cpfSB.insert(0, 0);
        }
        boolean cpfValido = this.isCPFValido(cpfSB.toString());
        if ((!cpfValido)
                && (cpf > 0L)) {
            String mensagem = FrmCadClienteContatoA.O_CAMPO
                    + this.lblCPF.getCaption()
                    + "\" deve ser preenchido com um valor válido.";
            Dialog.create()
                    .showNotificationInfo(mensagem,
                            FrmCadClienteContatoA.END_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                            3000,                             // Tempo em milissegundos para exibir a mensagem
                            this.edtCPF,                      // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            true);                            // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
            this.edtCPF.setFocus();
            this.edtCPF.setSelectionRange(0,
                    (this.edtCPF.getValue().asString().length() + 1));
            return;
        }
        //endregion
        //region Contato
        boolean edtContatoRequired = this.edtContato.isRequired();
        if (edtContatoRequired) {
            String contato = this.edtContato.getValue().asString().trim();
            if ((!contato.contains(" "))) {
                String mensagem = FrmCadClienteContatoA.O_CAMPO
                        + this.lblContato.getCaption()
                        + "\" deve ser preenchido com o nome e o sobrenome. Ex.: José Silva";
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.END_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                             // Tempo em milissegundos para exibir a mensagem
                                this.edtContato,                  // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                            // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.edtContato.setFocus();
                this.edtContato.setSelectionRange(0,
                        (this.edtContato.getValue().asString().length() + 1));
                return;
            }
        }
        //endregion
        //region Área contato
        boolean cboAreaContatoRequired = this.cboAreaDeContato.isRequired();
        if (cboAreaContatoRequired) {
            String areaContato = this.cboAreaDeContato.getValue().asString().trim();
            if (areaContato.isEmpty()) {
                String mensagem = FrmCadClienteContatoA.O_CAMPO
                        + this.lblAreaDeContato.getCaption()
                        + FrmCadClienteContatoA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.START_CENTER, // a mensagem aparece à esquerda da âncora, alinhada ao meio.
                                3000,                               // Tempo em milissegundos para exibir a mensagem
                                this.cboAreaDeContato,              // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                              // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.cboAreaDeContato.setFocus();
                this.cboAreaDeContato.setOpen(true);
                return;
            }
        }
        //endregion
        //region DDD Celular
        boolean dddCelularRequired = this.edtDDDCelular.isRequired();
        boolean dddCelularNotEmpty = !this.edtDDDCelular.getValue().asString().isEmpty();
        if (dddCelularRequired
                || dddCelularNotEmpty) {
            String mensagem = FrmCadClienteContatoA.O_CAMPO
                    + this.lblCelular.getCaption()
                    + "\" deve ser preenchido com o DDD válido com 2 dígitos. Ex.: 65";
            String dddCelular = this.edtDDDCelular.getValue().asString().replace("-", "").trim();
            if ((dddCelular.length() != 2)) {
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.END_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                             // Tempo em milissegundos para exibir a mensagem
                                this.edtDDDCelular,               // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                            // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.edtDDDCelular.setFocus();
                this.edtDDDCelular.setSelectionRange(0,
                        (this.edtDDDCelular.getValue().asString().length() + 1));
                return;
            }
        }
        //endregion
        //region Telefone Celular
        boolean telefoneCelularRequired = this.edtTelefoneCelular.isRequired();
        boolean telefoneCelularNotEmpty = !this.edtTelefoneCelular.getValue().asString().isEmpty();
        if (telefoneCelularRequired
                || telefoneCelularNotEmpty) {
            String mensagem = FrmCadClienteContatoA.O_CAMPO
                    + this.lblCelular.getCaption()
                    + "\" deve ser preenchido com o telefone válido com 9 dígitos. Ex.: 999999999";
            String telefoneCelular = this.edtTelefoneCelular.getValue().asString().replace("-", "").trim();
            if ((telefoneCelular.length() != 9)) {
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.END_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                             // Tempo em milissegundos para exibir a mensagem
                                this.edtTelefoneCelular,          // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                            // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.edtTelefoneCelular.setFocus();
                this.edtTelefoneCelular.setSelectionRange(0,
                        (this.edtTelefoneCelular.getValue().asString().length() + 1));
                return;
            }
        }
        //endregion
        //region DDD Whatsapp
        boolean dddWhatsappRequired = this.edtDDDWhatsapp.isRequired();
        boolean dddWhatsappNotEmpty = !this.edtDDDWhatsapp.getValue().asString().isEmpty();
        if (dddWhatsappRequired
                || dddWhatsappNotEmpty) {
            String mensagem = FrmCadClienteContatoA.O_CAMPO
                    + this.lblWhatsapp.getCaption()
                    + "\" deve ser preenchido com o DDD válido com 2 dígitos. Ex.: 65";
            String dddWhatsapp = this.edtDDDWhatsapp.getValue().asString().replace("-", "").trim();
            if ((dddWhatsapp.length() != 2)) {
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.END_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                             // Tempo em milissegundos para exibir a mensagem
                                this.edtDDDWhatsapp,              // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                            // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.edtDDDWhatsapp.setFocus();
                this.edtDDDWhatsapp.setSelectionRange(0,
                        (this.edtDDDWhatsapp.getValue().asString().length() + 1));
                return;
            }
        }
        //endregion
        //region Telefone Whatsapp
        boolean telefoneWhatsappRequired = this.edtTelefoneWhatsapp.isRequired();
        boolean telefoneWhatsappNotEmpty = !this.edtTelefoneWhatsapp.getValue().asString().isEmpty();
        if (telefoneWhatsappRequired
                || telefoneWhatsappNotEmpty) {
            String mensagem = FrmCadClienteContatoA.O_CAMPO
                    + this.lblWhatsapp.getCaption()
                    + "\" deve ser preenchido com o telefone válido com 9 dígitos. Ex.: 999999999";
            String telefoneWhatsapp = this.edtTelefoneWhatsapp.getValue().asString().replace("-", "").trim();
            if ((telefoneWhatsapp.length() != 9)) {
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.START_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                               // Tempo em milissegundos para exibir a mensagem
                                this.edtTelefoneWhatsapp,           // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                              // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.edtTelefoneWhatsapp.setFocus();
                this.edtTelefoneWhatsapp.setSelectionRange(0,
                        (this.edtTelefoneWhatsapp.getValue().asString().length() + 1));
                return;
            }
        }
        //endregion
        //region E-mail
        boolean emailRequired = this.edtEmail.isRequired();
        if (emailRequired) {
            String eMail = this.edtEmail.getValue().asString().trim();
            boolean emailValido = StringUtil.isEmailValido(eMail);
            if (eMail.isEmpty()
                    || (!emailValido)) {
                String mensagem = FrmCadClienteContatoA.O_CAMPO
                        + this.lblEmail.getCaption()
                        + "\" deve ser preenchido com algum e-mail válido. Ex.: <EMAIL>";
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.END_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                             // Tempo em milissegundos para exibir a mensagem
                                this.edtEmail,                    // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                            // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.edtEmail.setFocus();
                this.edtEmail.setSelectionRange(0,
                        (this.edtEmail.getValue().asString().length() + 1));
                return;
            }
        }
        //endregion
        //region É comprador
        boolean cboEhCompradorRequired = this.cboEhComprador.isRequired();
        if (cboEhCompradorRequired) {
            String ehComprador = this.cboEhComprador.getValue().asString();
            if (ehComprador.isEmpty()) {
                String mensagem = FrmCadClienteContatoA.O_CAMPO
                        + this.lblEhComprador.getCaption()
                        + FrmCadClienteContatoA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.END_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                             // Tempo em milissegundos para exibir a mensagem
                                this.cboEhComprador,              // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                            // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.cboEhComprador.setFocus();
                this.cboEhComprador.setOpen(true);
                return;
            }
            boolean existeAlgumContatoComprador = this.existeAlgumContatoComprador(this.codCliente);
            if ((!existeAlgumContatoComprador)
                    && (!ehComprador.equals("S"))) {
                String mensagem = "O cliente deve ter, no mínimo, um contato cadastrado como comprador.";
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.END_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                             // Tempo em milissegundos para exibir a mensagem
                                this.cboEhComprador,              // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                            // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.cboEhComprador.setFocus();
                this.cboEhComprador.setOpen(true);
                this.cboEhComprador.setValue("S");
                return;
            }
        }
        //endregion
        //region Responsável pela garantia da montadora
        boolean cboResponsavelPelaGarantiaDaMontadoraRequired = this.cboResponsavelPelaGarantiaDaMontadora.isRequired();
        if (cboResponsavelPelaGarantiaDaMontadoraRequired) {
            String responsavelPelaGarantiaDaMontadora = this.cboResponsavelPelaGarantiaDaMontadora.getValue().asString();
            if (responsavelPelaGarantiaDaMontadora.isEmpty()) {
                String mensagem = FrmCadClienteContatoA.O_CAMPO
                        + this.lblResponsavelPelaGarantiaDaMontadora.getCaption()
                        + FrmCadClienteContatoA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.START_CENTER,         // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                                       // Tempo em milissegundos para exibir a mensagem
                                this.cboResponsavelPelaGarantiaDaMontadora, // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                                      // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.cboResponsavelPelaGarantiaDaMontadora.setFocus();
                this.cboResponsavelPelaGarantiaDaMontadora.setOpen(true);
                return;
            }
        }
        //endregion
        //region É preposto
        boolean cboEhPrepostoRequired = this.cboEhPreposto.isRequired();
        if (cboEhPrepostoRequired) {
            String ehPreposto = this.cboEhPreposto.getValue().asString();
            if (ehPreposto.isEmpty()) {
                String mensagem = FrmCadClienteContatoA.O_CAMPO
                        + this.lblEhPreposto.getCaption()
                        + FrmCadClienteContatoA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.START_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                               // Tempo em milissegundos para exibir a mensagem
                                this.cboEhPreposto,                 // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                              // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.cboEhPreposto.setFocus();
                this.cboEhPreposto.setOpen(true);
                return;
            }
        }
        String ehPreposto = this.cboEhPreposto.getValue().asString();
        if (ehPreposto.equals("S")
                && (cpf == 0L)) {
            String mensagem = "O preposto deve ter, no mínimo, o CPF preenchido.";
            Dialog.create()
                    .showNotificationInfo(mensagem,
                            FrmCadClienteContatoA.END_CENTER, // a mensagem aparece à direita da âncora, alinhada ao meio.
                            3000,                             // Tempo em milissegundos para exibir a mensagem
                            this.edtCPF,                      // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            true);                            // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
            this.edtCPF.setRequired(true);
            this.edtCPF.setFocus();
            this.edtCPF.setSelectionRange(0,
                    (this.edtCPF.getValue().asString().length() + 1));
            return;
        } else {
            this.edtCPF.setRequired(false);
        }
        //endregion
        //region Responsável pela pesquisa da fábrica
        boolean cboResponsavelPelaPesquisaDaFabricaRequired = this.cboResponsavelPelaPesquisaDaFabrica.isRequired();
        if (cboResponsavelPelaPesquisaDaFabricaRequired) {
            String responsavelPelaPesquisaDaFabrica = this.cboResponsavelPelaPesquisaDaFabrica.getValue().asString();
            if (responsavelPelaPesquisaDaFabrica.isEmpty()) {
                String mensagem = FrmCadClienteContatoA.O_CAMPO
                        + this.lblResponsavelPelaPesquisaDaFabrica.getCaption()
                        + FrmCadClienteContatoA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .showNotificationInfo(mensagem,
                                FrmCadClienteContatoA.END_CENTER,         // a mensagem aparece à direita da âncora, alinhada ao meio.
                                3000,                                     // Tempo em milissegundos para exibir a mensagem
                                this.cboResponsavelPelaPesquisaDaFabrica, // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                true);                                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                this.cboResponsavelPelaPesquisaDaFabrica.setFocus();
                this.cboResponsavelPelaPesquisaDaFabrica.setOpen(true);
                return;
            }
        }
        //endregion
        //endregion
        long idContato = this.tbClienteContato.getID_CONTATO().asLong();
        String contato = this.edtContato.getValue().asString().trim();
        String sexo = this.cboContatoSexo.getValue().asString();
        String area = this.cboAreaDeContato.getValue().asString();
        Date nascimento = this.dtNascimento.getValue().asDate();
        String cnh = this.edtCNH.getValue().asString().trim();
        String passaporte = this.edtPassaporteCarteira.getValue().asString().trim();
        String rg = this.edtRG.getValue().asString().trim();
        String rgOrgaoEmissor = this.edtOrgaoEmissor.getValue().asString().trim();
        Date rgDataEmissao = this.dtEmissao.getValue().asDate();
        int politicamenteExposto = this.cboPoliticamenteExposto.getValue().asInteger();
        String dddResidencial = this.edtDDDResidencial.getValue().asString().trim();
        String telefoneResidencial = this.edtTelefoneResidencial.getValue().asString().trim();
        String dddCelular = this.edtDDDCelular.getValue().asString().trim();
        String telefoneCelular = this.edtTelefoneCelular.getValue().asString().trim();
        String dddWhatsapp = this.edtDDDWhatsapp.getValue().asString().trim();
        String telefoneWhatsapp = this.edtTelefoneWhatsapp.getValue().asString().trim();
        String email = this.edtEmail.getValue().asString().trim();
        String ehComprador = this.cboEhComprador.getValue().asString();
        String responsavelPelaPesquisaDaFabrica = this.cboResponsavelPelaPesquisaDaFabrica.getValue().asString();
        String ehResponsavelPelaGarantiaDaMontadora = this.cboResponsavelPelaGarantiaDaMontadora.getValue().asString();
        String funcao = this.edtFuncao.getValue().asString().trim();
        String time = this.edtTime.getValue().asString().trim();
        String hobby = this.edtHobby.getValue().asString().trim();
        String usuarioLogado = EmpresaUtil.getUserLogged();
        this.incluirAlterarContato(this.codCliente,
                idContato,
                contato,
                sexo,
                area,
                cpf,
                nascimento,
                cnh,
                passaporte,
                rg,
                rgOrgaoEmissor,
                rgDataEmissao,
                politicamenteExposto,
                dddResidencial,
                telefoneResidencial,
                dddCelular,
                telefoneCelular,
                dddWhatsapp,
                telefoneWhatsapp,
                email,
                ehComprador,
                ehPreposto,
                responsavelPelaPesquisaDaFabrica,
                ehResponsavelPelaGarantiaDaMontadora,
                funcao,
                time,
                hobby,
                usuarioLogado);
        //region Guias - Definir visibilidade e foco
        this.tbsListagem.setVisible(true);
        this.pgcPrincipal.selectTab(this.tbsListagem);
        this.tbsCadastro.setVisible(false);
        //endregion
        //region Botões - Definir enabled
        this.btnPesquisar.setEnabled(true);
        this.btnNovo.setEnabled(true);
        boolean tbClienteContatoNotEmpty = !this.tbClienteContato.isEmpty();
        this.btnAlterar.setEnabled(tbClienteContatoNotEmpty);
        this.btnExcluir.setEnabled(tbClienteContatoNotEmpty);
        this.btnSalvar.setEnabled(false);
        this.btnCancelar.setEnabled(false);
        //endregion
        this.btnPesquisarClick(event);
    }

    public void pesquisar() {
        String contatoFiltrado = this.edtFiltroContato.getValue().asString();
        String responsavelPelaPesquisaDaFabrica = this.cboFiltroResponsavelPesqFabr.getValue().asString();
        String areaDeContatoFiltrada = this.cboFiltroAreaDeContato.getValue().asString();
        this.carregarGrdContatos(
                this.codCliente
                ,contatoFiltrado
                ,responsavelPelaPesquisaDaFabrica
                ,areaDeContatoFiltrada
        );
        //region Botões - Definir enabled
        this.btnPesquisar.setEnabled(
                true
        );
        this.btnNovo.setEnabled(
                true
        );
        boolean tbClienteContatoNotEmpty = !this.tbClienteContato.isEmpty();
        this.btnAlterar.setEnabled(
                tbClienteContatoNotEmpty
        );
        this.btnExcluir.setEnabled(
                tbClienteContatoNotEmpty
        );
        this.btnSalvar.setEnabled(
                false
        );
        this.btnCancelar.setEnabled(
                false
        );
        //endregion
    }

    @Override
    public void btnPesquisarClick(Event<Object> event) {
        this.pesquisar();
    }

    private void incluirAlterarContato(long codCliente,
                                       long idContato,
                                       String contato,
                                       String sexo,
                                       String area,
                                       long cpf,
                                       Date nascimento,
                                       String cnh,
                                       String passaporte,
                                       String rg,
                                       String rgOrgaoEmissor,
                                       Date rgDataEmissao,
                                       int politicamenteExposto,
                                       String dddResidencial,
                                       String telefoneResidencial,
                                       String dddCelular,
                                       String telefoneCelular,
                                       String dddWhatsapp,
                                       String telefoneWhatsapp,
                                       String email,
                                       String ehComprador,
                                       String ehPreposto,
                                       String responsavelPelaPesquisaDaFabrica,
                                       String ehResponsavelPelaGarantiaDaMontadora,
                                       String funcao,
                                       String time,
                                       String hobby,
                                       String usuarioLogado) {
        try {
            this.rn.incluirAlterarContato(codCliente,
                    idContato,
                    contato,
                    sexo,
                    area,
                    cpf,
                    nascimento,
                    cnh,
                    passaporte,
                    rg,
                    rgOrgaoEmissor,
                    rgDataEmissao,
                    politicamenteExposto,
                    dddResidencial,
                    telefoneResidencial,
                    dddCelular,
                    telefoneCelular,
                    dddWhatsapp,
                    telefoneWhatsapp,
                    email,
                    ehComprador,
                    ehPreposto,
                    responsavelPelaPesquisaDaFabrica,
                    ehResponsavelPelaGarantiaDaMontadora,
                    funcao,
                    time,
                    hobby,
                    usuarioLogado);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao incluir/alterar o contato",
                    dataException);
        }
    }

    @Override
    public void btnPesquisarContatoClick(Event<Object> event) {
        FrmPesquisaClienteA frmPesquisaClienteA = new FrmPesquisaClienteA();
        frmPesquisaClienteA.setCheckExistEvento(false);
        String contato = this.edtContato.getValue().asString().trim();
        if (!contato.isEmpty()) {
            frmPesquisaClienteA.edtPesquisarCliente.setValue(contato);
        }
        FormUtil.doShow(frmPesquisaClienteA,
                t -> {
                    boolean frmPesquisaClienteAFechadoAoAceitar = frmPesquisaClienteA.isFechadoAoAceitar();
                    if (frmPesquisaClienteAFechadoAoAceitar) {
                        String nomeCliente = frmPesquisaClienteA.tbLeadsConsultaClientes.getCLIENTE().asString().trim();
                        if (nomeCliente.length() > 50) {
                            nomeCliente = nomeCliente.substring(0, 50);
                        }
                        this.edtContato.setValue(nomeCliente);
                        String cpf = frmPesquisaClienteA.tbLeadsConsultaClientes.getCPF().asString().trim();
                        this.edtCPF.setValue(cpf);
                        String sexo = frmPesquisaClienteA.tbLeadsConsultaClientes.getCOD_SEXO().asString().trim();
                        this.cboContatoSexo.setValue(sexo);
                        Date nascimento = frmPesquisaClienteA.tbLeadsConsultaClientes.getANIVERSARIO().asDate();
                        this.dtNascimento.setValue(nascimento);
                        String cnh = frmPesquisaClienteA.tbLeadsConsultaClientes.getCNH().asString().trim();
                        this.edtCNH.setValue(cnh);
                        String passaporte = frmPesquisaClienteA.tbLeadsConsultaClientes.getIDENT_ESTRANGEIRO().asString().trim();
                        this.edtPassaporteCarteira.setValue(passaporte);
                        String rg = frmPesquisaClienteA.tbLeadsConsultaClientes.getRG_NUMERO().asString().trim();
                        this.edtRG.setValue(rg);
                        String rgOrgaoEmissor = frmPesquisaClienteA.tbLeadsConsultaClientes.getRG_EMISSOR().asString().trim();
                        this.edtOrgaoEmissor.setValue(rgOrgaoEmissor);
                        Date rgDataEmissao = frmPesquisaClienteA.tbLeadsConsultaClientes.getRG_DATA_EMISSAO().asDate();
                        this.dtEmissao.setValue(rgDataEmissao);
                        int politicamenteExposto = frmPesquisaClienteA.tbLeadsConsultaClientes.getPOLITICAMENTE_EXPOSTO().asInteger();
                        this.cboPoliticamenteExposto.setValue(politicamenteExposto);
                        String dddResidencial = frmPesquisaClienteA.tbLeadsConsultaClientes.getPREFIXO_RES().asString().trim();
                        this.edtDDDResidencial.setValue(dddResidencial);
                        String telefoneResidencial = frmPesquisaClienteA.tbLeadsConsultaClientes.getTELEFONE_RES().asString().trim();
                        this.edtTelefoneResidencial.setValue(telefoneResidencial);
                        String dddCelular = frmPesquisaClienteA.tbLeadsConsultaClientes.getPREFIXO_CEL().asString().trim();
                        this.edtDDDCelular.setValue(dddCelular);
                        String telefoneCelular = frmPesquisaClienteA.tbLeadsConsultaClientes.getTELEFONE_CEL().asString().trim();
                        this.edtTelefoneCelular.setValue(telefoneCelular);
                        String dddWhatsapp = frmPesquisaClienteA.tbLeadsConsultaClientes.getPREFIXO_WHASTSAPP().asString().trim();
                        this.edtDDDWhatsapp.setValue(dddWhatsapp);
                        String telefoneWhatsapp = frmPesquisaClienteA.tbLeadsConsultaClientes.getTELEFONE_WHASTSAPP().asString().trim();
                        this.edtTelefoneWhatsapp.setValue(telefoneWhatsapp);
                        String email = frmPesquisaClienteA.tbLeadsConsultaClientes.getEMAIL().asString().trim();
                        this.edtEmail.setValue(email);
                        String funcao = frmPesquisaClienteA.tbLeadsConsultaClientes.getDESCRICAO_PROFISSAO().asString().trim();
                        this.edtFuncao.setValue(funcao);
                    }
                });
        this.edtContato.setFocus();
        this.edtContato.setSelectionRange(0,
                (this.edtContato.getValue().asString().length() + 1));
    }

    private boolean existeAlgumContatoComprador(long codCliente) {
        boolean retFuncao = false;
        try {
            retFuncao = this.rn.existeAlgumContatoComprador(codCliente);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao verificar se existe algum contato comprador",
                    dataException);
        }
        return retFuncao;
    }

    @Override
    public void edtCPFChange(Event<Object> event) {
        String cpf = this.edtCPF.getValue().asString().trim();
        if (cpf.equals("00000000000")) {
            this.edtCPF.clear();
        }
    }

    private boolean isCPFValido(String cpf) {
        try {
            return this.rn.isCPFValido(cpf);
        } catch (DataException dataException) {
            return false;
        }
    }

    @Override
    public void icoAreaDeContatoClick(Event<Object> event) {
        boolean possuiAcesso;
        if (EmpresaUtil.isCrmParts()) {
            possuiAcesso = EmpresaUtil.validarAcesso("K0605");
        } else {
            possuiAcesso = EmpresaUtil.validarAcesso("B2205");
        }
        if (!possuiAcesso) {
            return;
        }
        FrmCadAreaDeContatoA frmCadAreaDeContatoA = new FrmCadAreaDeContatoA();
        FormUtil.doShow(frmCadAreaDeContatoA,
                t -> {
                    boolean frmCadAreaDeContatoAFechadoAoAceitar = frmCadAreaDeContatoA.isFechadoAoAceitar();
                    if (frmCadAreaDeContatoAFechadoAoAceitar) {
                        this.carregarCboAreaDeContato();
                        String areaDeContatoSelecionada = frmCadAreaDeContatoA.tbClienteContatoTipo.getAREA_CONTATO().asString();
                        this.cboAreaDeContato.setValue(areaDeContatoSelecionada);
                        this.cboAreaDeContato.setFocus();
                    }
                });
    }

    private void carregarCboAreaDeContato() {
        try {
            this.rn.carregarCboAreaDeContato();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar as áreas do contato",
                    dataException);
        }
    }

    private void carregarCboFiltroAreaDeContato() {
        try {
            this.rn.carregarCboFiltroAreaDeContato();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar o filtro das áreas do contato",
                    dataException);
        }
    }

    @Override
    public void icoCelularClick(Event<Object> event) {
        String dddWhatsapp = this.edtDDDWhatsapp.getValue().asString();
        String telefoneWhatsapp = this.edtTelefoneWhatsapp.getValue().asString();
        if ((!dddWhatsapp.isEmpty()) &&
                (!telefoneWhatsapp.isEmpty())) {
            Dialog.create()
                    .title(FrmCadClienteContatoA.CONFIRMACAO)
                    .message("Deseja copiar o telefone do whatsapp para o celular?")
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            this.edtDDDCelular.setValue(dddWhatsapp);
                            this.edtTelefoneCelular.setValue(telefoneWhatsapp);
                            this.edtTelefoneCelular.setFocus();
                            this.edtTelefoneCelular.setSelectionRange(0,
                                    (this.edtTelefoneCelular.getValue().asString().length() + 1));
                        }
                    });
        } else {
            this.edtDDDCelular.setFocus();
            this.edtDDDCelular.setSelectionRange(0,
                    (this.edtDDDCelular.getValue().asString().length() + 1));
        }
    }

    @Override
    public void icoWhatsappClick(Event<Object> event) {
        String dddCelular = this.edtDDDCelular.getValue().asString();
        String telefoneCelular = this.edtTelefoneCelular.getValue().asString();
        if ((!dddCelular.isEmpty())
                && (!telefoneCelular.isEmpty())) {
            Dialog.create()
                    .title(FrmCadClienteContatoA.CONFIRMACAO)
                    .message("Deseja copiar o telefone do celular para o whatsapp?")
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            this.edtDDDWhatsapp.setValue(dddCelular);
                            this.edtTelefoneWhatsapp.setValue(telefoneCelular);
                            this.edtTelefoneWhatsapp.setFocus();
                            this.edtTelefoneWhatsapp.setSelectionRange(0,
                                    (this.edtTelefoneWhatsapp.getValue().asString().length() + 1));
                        }
                    });
        } else {
            this.edtDDDWhatsapp.setFocus();
            this.edtDDDWhatsapp.setSelectionRange(0,
                    (this.edtDDDWhatsapp.getValue().asString().length() + 1));
        }
    }

    @Override
    public void iconLimparFuncaoClick(Event<Object> event) {
        this.edtFuncao.clear();
        this.edtFuncao.setFocus();
    }

    @Override
    public void iconLimparHobbyClick(Event<Object> event) {
        this.edtHobby.clear();
        this.edtHobby.setFocus();
    }

    @Override
    public void iconLimparTimeClick(Event<Object> event) {
        this.edtTime.clear();
        this.edtTime.setFocus();
    }

    @Override
    public void iconLimparPesquisaClick(Event<Object> event) {
        this.edtFiltroContato.clear();
        this.edtFiltroContato.setFocus();
    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        if (tbClienteContato.getID_CONTATO().asInteger() <= 0) {
            EmpresaUtil.showMessage("Atenção", "Selecione um Contato para Aceitar!");
            return;
        }
        this.aceitar = true;
        this.close();
    }

}