package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmGerencialPainelFiltroW;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmGerencialPainelFiltroA extends FrmGerencialPainelFiltroW {
    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;
    private Integer painelId = 0;
    private Integer codEmpresa = 0;
    private Integer mes = 0;
    private Integer ano = 0;
    private Integer idGrupoClasse = 0;

    public FrmGerencialPainelFiltroA(Integer painelId, Integer codEmpresa, Integer mes, Integer ano, int idGrupoClasse) {
        this.painelId = painelId;
        this.codEmpresa = codEmpresa;
        this.mes = mes;
        this.ano = ano;
        this.idGrupoClasse = idGrupoClasse;
        initForm();
    }

    private void initForm() {
        try {
            rn.carregaPainelUsuario();
            rn.carregaComboEmpresa();
//            Sempre que carregar tela, pegar Painel default
            painelId = tbPainelGerencialUsuario.getID().asInteger();
            cbbPaineis.setValue(painelId);
            cbbEmpresas.setValue(codEmpresa);
            cbbMes.setValue(mes);
            cbbAno.setValue(ano);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnVoltarClick(final Event event) {
        ok = false;
        close();
    }

    @Override
    public void btnAceitarClick(final Event event) {
        ok = true;
        close();
    }

    @Override
    public void btnPreferenciaClick(final Event event) {
        FrmGerencialPainelPreferenciaA frm = new FrmGerencialPainelPreferenciaA(idGrupoClasse);
        FormUtil.doShow(frm, (EventListener) t -> {
            try {
                rn.carregaPainelUsuario();
//            Sempre que carregar tela, pegar Painel default
                painelId = tbPainelGerencialUsuario.getID().asInteger();
                cbbPaineis.setValue(painelId);
            } catch (DataException ex) {
                EmpresaUtil.showError("Erro", ex);
            }
        });
    }

    public boolean isOk() {
        return ok;
    }

    public Integer getPainelId() {
        return cbbPaineis.getValue().asInteger();
    }

    public Integer getCodEmpresa() {
        return cbbEmpresas.getValue().asInteger();
    }

    public Integer getMes() {
        return cbbMes.getValue().asInteger();
    }

    public Integer getAno() {
        return cbbAno.getValue().asInteger();
    }
}
