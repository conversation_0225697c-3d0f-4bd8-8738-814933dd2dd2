package freedom.bytecode.form;

import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.IDialog;
import freedom.data.TableState;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.form.wizard.*;
import freedom.bytecode.rn.wizard.*;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;

import java.util.HashMap;
import java.util.Map;

import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;

import java.rmi.registry.*;

import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

public class FrmTipoDocumentoA extends FrmTipoDocumentoW {
    private static final long serialVersionUID = 20130827081850L;

    public FrmTipoDocumentoA() {
        cbbFilAtivo.setValue("S");
        consultar();
        habilitaComp(false);

//        enquando o modulo de assinar documentos nao estiver pronto, estes novos atributos ficaram invisivel a pedido do Luís
        lblConsultor.setVisible(false);
        lblGerente.setVisible(false);
        lblPermitirObrigarAss.setVisible(false);
        lblProdutivoAssina.setVisible(false);
        lblClienteAssina.setVisible(false);

        chkClienteAssina.setVisible(false);
        chkConsultorAssina.setVisible(false);
        chkGerenteAssina.setVisible(false);
        chkProdutivoAssina.setVisible(false);
        cbbObrigarAssinatura.setVisible(false);
    }

    @Override
    public void btnConsultarClick(Event<Object> event) {
        consultar();
        habilitaComp(false);
    }

    @Override
    public void btnNovoClick(Event<Object> event) {
        if (rn.getModulo().equals("S")) {
            if (!EmpresaUtil.validarAcesso("B3133")) {
                return;
            }
        } else if (rn.getModulo().equals("P")) {
            if (!EmpresaUtil.validarAcesso("K1415")) {
                return;
            }
        }

        try {
            pgPrincipal.selectTab(1);
            tbTipoDocumento.append();
            chkAtivo.setChecked(true);
            chkProdutivoAssina.setChecked(false);
            chkGerenteAssina.setChecked(false);
            chkConsultorAssina.setChecked(false);
            chkClienteAssina.setChecked(false);
            habilitaComp(true);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro Novo Tipo Documento", ex);
        }
    }

    @Override
    public void btnAlterarClick(Event<Object> event) {
        if (rn.getModulo().equals("S")) {
            if (!EmpresaUtil.validarAcesso("B3134")) {
                return;
            }
        } else if (rn.getModulo().equals("P")) {
            if (!EmpresaUtil.validarAcesso("K1416")) {
                return;
            }
        }

        try {
            tbTipoDocumento.edit();
            pgPrincipal.selectTab(1);
            habilitaComp(true);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro Novo Tipo Documento", ex);
        }
    }

    @Override
    public void btnExcluirClick(Event<Object> event) {
        habilitaComp(false);
        Dialog.create()
                .title("Confirma Exclusão?")
                .message("Confirma Excluir o Tipo Documento: " + tbTipoDocumento.getDESCRICAO().asString() + " ?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            tbTipoDocumento.delete();
                            tbTipoDocumento.post();
                            tbTipoDocumento.applyUpdates();
                            tbTipoDocumento.commitUpdates();
                            consultar();
                        } catch (DataException ex) {
                            Dialog.create()
                                    .title("Erro ao excluir")
                                    .message(ex.getMessage())
                                    .showException(ex);
                        }
                    }
                });
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        try {
            if (valida()) {
                if (tbTipoDocumento.getState() == TableState.INSERTING) {
                    tbTipoDocumento.setID_TIPO_DOCUMENTO(rn.getIdTipoDocumento());
                    tbTipoDocumento.setMODULO(rn.getModulo());
                }
                tbTipoDocumento.post();
                tbTipoDocumento.applyUpdates();
                tbTipoDocumento.commitUpdates();
                consultar();
                pgPrincipal.selectTab(0);
                habilitaComp(false);
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro Novo Tipo Documento", ex);
        }
    }

    @Override
    public void btnCancelarClick(Event<Object> event) {
        tbTipoDocumento.close();
        consultar();
        pgPrincipal.selectTab(0);
        habilitaComp(false);
    }

    private void consultar() {
        try {
            rn.consultarTipoDoc(cbbFilAtivo.getValue().asString());
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void habilitaComp(Boolean enabled) {
        gridPrincipal.setEnabled(!enabled);
        btnConsultar.setEnabled(!enabled);
        btnNovo.setEnabled(!enabled);
        btnAlterar.setEnabled(!enabled && !tbTipoDocumento.isEmpty());
        btnExcluir.setEnabled(!enabled && !tbTipoDocumento.isEmpty());
        btnCancelar.setEnabled(enabled);
        btnSalvar.setEnabled(enabled);

        edIdTipoDoc.setEnabled(false);
        edtDescricao.setEnabled(enabled && (!tbTipoDocumento.isEmpty() || tbTipoDocumento.getState() == TableState.INSERTING));
        cbbObrigarAssinatura.setEnabled(enabled && (!tbTipoDocumento.isEmpty() || tbTipoDocumento.getState() == TableState.INSERTING));
        chkAtivo.setEnabled(enabled && (!tbTipoDocumento.isEmpty() || tbTipoDocumento.getState() == TableState.MODIFYING));
        chkClienteAssina.setEnabled(enabled && (!tbTipoDocumento.isEmpty() || tbTipoDocumento.getState() == TableState.INSERTING));
        chkConsultorAssina.setEnabled(enabled && (!tbTipoDocumento.isEmpty() || tbTipoDocumento.getState() == TableState.INSERTING));
        chkGerenteAssina.setEnabled(enabled && (!tbTipoDocumento.isEmpty() || tbTipoDocumento.getState() == TableState.INSERTING));
        chkProdutivoAssina.setEnabled(enabled && (!tbTipoDocumento.isEmpty() || tbTipoDocumento.getState() == TableState.INSERTING));
    }

    private boolean valida() {
        boolean ret = true;

        if (edtDescricao.getValue().asString().equals("")) {
            EmpresaUtil.showMessage("Atenção", "Informe Descrição do tipo de Arquivo!");
            return false;
        }

        return ret;
    }

}
