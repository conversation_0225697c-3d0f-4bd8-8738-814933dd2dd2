package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmSelecionarPeriodoW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.util.DateUtils;
import lombok.Getter;

import java.util.Date;

public class FrmSelecionarPeriodoA extends FrmSelecionarPeriodoW {

    private static final long serialVersionUID = 20130827081850L;

    @Getter
    private Date dataInicial;

    @Getter
    private Date dataFinal;

    final Date data1Milissegundo = new Date(1);

    @Getter
    private boolean ok = false;

    /**
     * Exibir uma página para preencher data inicial e final.
     *
     * @param dataInicial Crie a constante abaixo:
     *                    final Date DATA_1_MILISSEGUNDO = new Date(1);
     *                    Instancie com a constante DATA_1_MILISSEGUNDO para exibir a página 'Selecionar
     *                    período' com a data inicial vazia;
     *                    Instancie com outra data para exibir a página 'Selecionar período' com a data
     *                    inicial preenchida.
     * @param dataFinal   Crie a constante abaixo:
     *                    final Date DATA_1_MILISSEGUNDO = new Date(1);
     *                    Instancie com a constante DATA_1_MILISSEGUNDO para exibir a página 'Selecionar
     *                    período' com a data final vazia;
     *                    Instancie com outra data para exibir a página 'Selecionar período' com a data
     *                    final preenchida.
     *                    ----------------------------------------------------------------------------------------------------
     *                    01    — Quando esta página for chamada pela primeira vez, a página de origem não terá as datas de
     *                    início e fim, então:
     *                    01.01 — Chamará o construtor conforme o exemplo abaixo:
     *                    FrmSelecionarPeriodoA frmSelecionarPeriodoA = new FrmSelecionarPeriodoA(DATA_1_MILISSEGUNDO,
     *                    DATA_1_MILISSEGUNDO);
     *                    01.02 — Nesse cenário, os componentes de data devem ser exibidos vazios.
     *                    02    — Quando esta página for chamada pela segunda vez, a página de origem pode ter as datas de
     *                    início e fim, então:
     *                    02.01 — Chamará o construtor conforme o exemplo abaixo:
     *                    FrmSelecionarPeriodoA frmSelecionarPeriodoA = new FrmSelecionarPeriodoA(this.dataInicial,
     *                    this.dataFinal);
     *                    02.02 — Nesse cenário os componentes de data devem ser exibidos preenchidos com as datas do
     *                    construtor, permitindo ao usuário alterá-las.
     *                    03    — Caso o usuário feche o formulário clicando no X, as datas retornarão os valores utilizados
     *                    para instanciar esta página ou o valor null
     */
    public FrmSelecionarPeriodoA(Date dataInicial,
                                 Date dataFinal) {
        if (DateUtils.equals(dataInicial,
                this.data1Milissegundo)) {
            this.dataInicial = null;
        } else {
            this.dataInicial = dataInicial;
        }
        this.dtInicial.setValue(this.dataInicial);
        if (DateUtils.equals(dataFinal,
                this.data1Milissegundo)) {
            this.dataFinal = null;
        } else {
            this.dataFinal = dataFinal;
        }
        this.dtFinal.setValue(this.dataFinal);
    }

    @Override
    public void frmShow(Event<Object> event) {
        this.dtInicial.setFocus();
        this.dtInicial.setSelectionRange(0,
                (this.dtInicial.getValue().asString().length() + 1));
    }

    @Override
    public void dtInicialEnter(Event<Object> event) {
        dtFinal.setFocus();
        this.dtFinal.setSelectionRange(0,
                (this.dtFinal.getValue().asString().length() + 1));
    }

    @Override
    public void dtFinalEnter(Event<Object> event) {
        aceitar();
    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        aceitar();
    }

    private void aceitar() {
        this.dataInicial = this.dtInicial.getValue().asDate();
        String tituloDaMensagem = "Informação";
        if (this.dataInicial == null) {
            String mensagem = "A data inicial deve ser preenchida.";
            Dialog.create()
                    .title(tituloDaMensagem)
                    .message(mensagem)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.dtInicial.setFocus()));
            return;
        }
        this.dataFinal = this.dtFinal.getValue().asDate();
        if (this.dataFinal == null) {
            String mensagem = "A data final deve ser preenchida.";
            Dialog.create()
                    .title(tituloDaMensagem)
                    .message(mensagem)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.dtFinal.setFocus()));
            return;
        }
        if (DateUtils.lessThan(this.dataFinal,
                this.dataInicial)) {
            final String MASCARA_DD_MM_YYYY = "dd/MM/yyyy";
            String mensagem = "A data inicial ("
                    + DateUtils.format(this.dataInicial,
                    MASCARA_DD_MM_YYYY)
                    + ") deve ser menor ou igual à data final ("
                    + DateUtils.format(this.dataFinal,
                    MASCARA_DD_MM_YYYY)
                    + ").";
            Dialog.create()
                    .title(tituloDaMensagem)
                    .message(mensagem)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.dtInicial.setFocus()));
            return;
        }
        this.ok = true;
        this.close();
    }

}
