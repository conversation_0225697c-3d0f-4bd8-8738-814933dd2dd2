package freedom.bytecode.form;

import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.form.wizard.*;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.connection.SessionUtil;
import freedom.criptografia.AesUtil;
import freedom.util.ApplicationUtil;
import freedom.util.EmpresaUtil;
import freedom.util.WorkListFactory;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class FrmLoginClienteA extends FrmLoginClienteW {

    private static final long serialVersionUID = 20130827081850L;
    private static final String DATASOURCE_SELECIONADO = "DATASOURCE_SELECIONADO";
    private static final String USUARIO_SELECIONADO = "USUARIO_SELECIONADO";
    private static final String SENHA_USUARIO = "SENHA_USUARIO";
    private static final String LEMBRAR_SENHA = "LEMBRAR_SENHA";
    private static final String PASSPHRASE = "NBS%OFFICE%456";
    private IWorkList wl = WorkListFactory.getInstance();
    private boolean flag = false;
    String DSCliente = "";

    public FrmLoginClienteA() {
        final StringBuilder sb = new StringBuilder();
        Map<String, String> themes = ApplicationUtil.getThemeList().entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        themes.keySet().stream().forEach((theme) -> {
            if (sb.length() > 0) {
                sb.append(";");
            }
            sb.append(themes.get(theme)).append("=").append(theme);
        });
        cbxTema.setListOptions(sb.toString());
        cbxTema.setValue(ApplicationUtil.getLastTheme());

        String dataSource;

        try {
            Map<String, String> items = SessionUtil.listarDatasources();
            sb.delete(0, sb.length());
            items.keySet().stream().forEach((key) -> {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append(items.get(key)).append("=").append(key);
            });

            txbUsuario.setValue(ApplicationUtil.getValue("CLIENTE_LOGADO"));
            FCheckBoxLembrarSenha.setValue(ApplicationUtil.getValue(LEMBRAR_SENHA));
            if (FCheckBoxLembrarSenha.isChecked()) {
                txbSenha.setValue(ApplicationUtil.getValue("SENHA_CLIENTE"));
            }

            dataSource = wl.sysget("CLIENTE_DATASOURCE").asString();
            wl.sysget("CLIENTE_USER");

            if (!dataSource.equals("")
                    && !wl.sysget("CLIENTE_USER").asString().trim().isEmpty()) {
                wl.put(IWorkList.DATASOURCE_DEFAULT, dataSource);
                ApplicationUtil.setValue(DATASOURCE_SELECIONADO, dataSource);
                DSCliente = dataSource;
            } else {
                Dialog.create()
                        .title("Falta paramentros")
                        .message("CLIENTE_DATASOURCE ou CLIENTE_USER não informado no fserver.xml")
                        .showError();
            }
        } catch (Exception e) {
            ExceptionEngine.register(e);
        }

    }

    @Override
    public void FCheckBoxLembrarSenhaCheck(final Event<Object> event) {
        lembrarSenha();
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        if (txbUsuario.getValue().isEmpty()) {
            txbUsuario.setFocus();
        } else if (txbSenha.getValue().isEmpty()) {
            txbSenha.setFocus();
        }
        String msgErro = wl.remove("ACCESS_ERROR").asString();
        if (!msgErro.isEmpty()) {
            Dialog.create()
                    .title("Validação de acesso")
                    .message(msgErro)
                    .showError();
        }
        /*txbUsuario.setValue("<EMAIL>");
        txbSenha.setValue("<EMAIL> senha: TXKEDH16");*/
    }

    @Override
    public void btnLoginClick(final Event<Object> event) {
        //super.btnLoginClick(event);

        if (txbUsuario.getValue().asString().trim().isEmpty()
                || txbSenha.getValue().asString().trim().isEmpty()) {
            Dialog.create()
                    .title("Erro ao acessar sistema")
                    .message("Favor informar: usuário e senha para acessar")
                    .showError();

        } else {
            try {
                wl.put(IWorkList.DATASOURCE_DEFAULT, DSCliente);

                String dataSource = ApplicationUtil.getValue(DATASOURCE_SELECIONADO);

                String validou = rn.validarLogin(txbUsuario.getValue().asString(),
                        txbSenha.getValue().asString(),
                        wl.sysget("CLIENTE_USER").asString().toUpperCase(),
                        wl.sysget("CLIENTE_USER_PASS").asString(),
                        dataSource, true);

                //Trocar Senha
                if (!validou.isEmpty()) {
                    Dialog.create()
                            .title("Erro ao acessar sistema")
                            .message(validou)
                            .showError();
                } else {
                    wl.put(IWorkList.USUARIO_LOGADO, wl.sysget("CLIENTE_USER").asString().toUpperCase());
                    wl.put("CLIENTE_LOGADO", txbUsuario.getValue().asString().trim());
                    String formName = ApplicationUtil.getValue("LAST_FORM_REQUEST");
                    if (formName == null || formName.trim().isEmpty()) {
                        formName = "frmhomecliente";
                    }

                    lembrarSenha();

                    try {
                        FormUtil.redirect("app?" + formName);
                    } finally {
                        ApplicationUtil.setValue("LAST_FORM_REQUEST", null);
                    }
                }
            } catch (Exception e) {
                Dialog.create()
                        .title("Erro ao acessar sistema")
                        .message(e.getMessage())
                        .showException(e);
            }
        }
    }

    @Override
    public void cbxTemaChange(final Event<Object> event) {
        applyTheme();
    }

    private void applyTheme() {
        ApplicationUtil.applyTheme(cbxTema.getValue().asString());
    }

    @Override
    public void txbSenhaEnter(final Event<Object> event) {
        btnLoginClick(event);
    }

    @Override
    public void txbUsuarioEnter(final Event<Object> event) {
        btnLoginClick(event);
    }

    @Override
    public void txbUsuarioExit(final Event<Object> event) {
        ApplicationUtil.setValue("CLIENTE_LOGADO", txbUsuario.getValue().asString());
        ApplicationUtil.setValue(USUARIO_SELECIONADO, wl.sysget("CLIENTE_USER").asString().toUpperCase());
    }

    private void lembrarSenha() {
        ApplicationUtil.setValue(LEMBRAR_SENHA, FCheckBoxLembrarSenha.getValue().asString());

        /*String senha = txbSenha.getValue().asString();

        AesUtil cript = new AesUtil();
        String pass = cript.encrypt(PASSPHRASE, senha);*/
        if (FCheckBoxLembrarSenha.isChecked()) {
            ApplicationUtil.setValue("SENHA_CLIENTE", txbSenha.getValue().asString());
        } else {
            ApplicationUtil.setValue("SENHA_CLIENTE", "");
        }
    }

    @Override
    public void txbSenhaExit(Event<Object> event) {

    }

    @Override
    public void lblEsqueceuSenhaClick(final Event event) {
        
    }

}
