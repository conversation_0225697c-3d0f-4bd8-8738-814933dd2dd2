package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.client.controls.impl.*;
//import freedom.util.CrmPartsUtil;
import freedom.util.DateUtils;
import freedom.util.EmpresaUtil;
import freedom.util.file.Exportacao;
import org.zkoss.zk.ui.HtmlBasedComponent;
import org.zkoss.zul.Tabbox;

import java.util.Date;

public class FrmNotaFiscalEletronicaSefazA extends FrmNotaFiscalEletronicaSefazW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String CL_GRAY = "clGray";

    private long aguardarAte;

    private boolean ok = false;

    private boolean timerOcupado;

    private long horaInicial;

    private String pontos;

    private double codEmpresa;

    private double controle;

    private String serie;

    private void setFontTab(TFPageControl pageControl) {
        ((<PERSON><PERSON><PERSON>) pageControl.getImpl()).getTabs().getChildren().forEach(t -> ((HtmlBasedComponent) t).setStyle("font-size: " + 16 + "px"));
    }

    public FrmNotaFiscalEletronicaSefazA() {
        this.setFontTab(this.pgcNFe
        );
        this.tbsMensagens.setVisible(false);
        this.pgcNFe.selectTab(this.tbsAutorizarNfe);
    }

    public Boolean isOk() {
        return this.ok;
    }

    public boolean jaGerouPdfNFeAutorizada() {
        boolean tbCrmpartsNfeMovimentoEmpty = this.tbCrmpartsNfeMovimento.isEmpty();
        if (tbCrmpartsNfeMovimentoEmpty) {
            return false;
        }
        String temPDF = this.tbCrmpartsNfeMovimento.getTEM_PDF().asString();
        if (temPDF.equals("S")){
            return true;
        }
        try {
            this.rn.abrirNFEMovimento(this.codEmpresa,
                    this.controle,
                    this.serie);
        } catch (DataException dataException) {
            return false;
        }
        return (this.tbCrmpartsNfeMovimento.getTEM_PDF().asString().equals("S"));
    }

    public void downloadPdfNfeAutorizada() {
        boolean tbCrmpartsNfeMovimentoNotEmpty = !this.tbCrmpartsNfeMovimento.isEmpty();
        if (tbCrmpartsNfeMovimentoNotEmpty) {
            String temPDF = this.tbCrmpartsNfeMovimento.getTEM_PDF().asString();
            if (temPDF.equals("S")) {
                String fileName = "nfe"
                        + this.tbCrmpartsNfeMovimento.getCHAVE_NFE().asString()
                        + ".pdf";
                String anexo = this.tbCrmpartsNfeMovimento.getPDF_NOTA().asString();
                Exportacao.exportFile(anexo,
                        fileName);
            }
        }
    }

    private void atualizarStatus(boolean aguardar,
                                 String color) {
        String msgNfe = this.rn.getNFEDescricaoStatus();
        this.lblStatusNfe.setCaption("");
        if (msgNfe != null) {
            long tmp = System.currentTimeMillis() - this.horaInicial;
            this.lblStatusNfe.setFontColor(color);
            if (aguardar) {
                String s = "["
                        + DateUtils.format(new Date(tmp),
                        "mm:ss")
                        + "]     ";
                this.lblStatusNfe.setCaption(s
                        + msgNfe
                        + this.pontos);
            } else {
                this.lblStatusNfe.setCaption(msgNfe);
            }
        }
        this.hBoxStatus.invalidate();
    }

    private void atualizarStatusPdf(boolean aguardar) {
        this.lblStatusPdf.setCaption("");
        long tmp = System.currentTimeMillis()
                - this.horaInicial;
        this.lblStatusPdf.setFontColor(FrmNotaFiscalEletronicaSefazA.CL_GRAY);
        if (aguardar) {
            String s = "["
                    + DateUtils.format(new Date(tmp),
                    "mm:ss")
                    + "]     ";
            this.lblStatusPdf.setCaption(s
                    + "Aguardando gerar PDF/DANFe "
                    + this.pontos);
        } else {
            this.lblStatusPdf.setCaption("Aguardando gerar PDF/DANFe!!!");
        }
        this.hBoxStatusPdf.invalidate();
    }

    @Override
    public void btnTentarDepoisClick(final Event<Object> event) {
        this.close();
    }

    @Override
    public void timerNfeTimer(Event<Object> event) {
        if (!this.timerOcupado) {
            this.timerOcupado = true;
            try {
                this.pontos = this.pontos
                        + " . ";
                if (this.pontos.length() > 30) {
                    this.pontos = " . ";
                }
                this.aguardarAprNfe();
            } finally {
                this.timerOcupado = false;
            }
        }
    }

    @Override
    public void timerPdfTimer(Event<Object> event) {
        if (!this.timerOcupado) {
            this.timerOcupado = true;
            try {
                this.pontos = this.pontos + " . ";
                if (this.pontos.length() > 30) {
                    this.pontos = " . ";
                }
                this.aguardarGerarPdf();
            } finally {
                this.timerOcupado = false;
            }
        }
    }

    private void aguardarGerarPdf() {
        boolean aguardar = this.rn.aguardaGerarPDF(this.codEmpresa,
                this.controle,
                this.serie);
        this.atualizarStatusPdf(aguardar
        );
        if (aguardar
                && (System.currentTimeMillis() > this.aguardarAte)) {
            aguardar = false;
        }
        if (!aguardar) {
            this.setTimerPdf(false,
                    0);
            this.atualizarStatusPdf(aguardar);
            if (this.tbCrmpartsNfeMovimento.getTEM_PDF().asString().equals("S")) {
                this.ok = true;
                this.close();
            } else {
                this.btnGerarFilaPdf.setEnabled(true);
            }
        }
    }

    private void aguardarAprNfe() {
        try {
            boolean aguardar = this.rn.aguardaAprovarNFE(this.codEmpresa,
                    this.controle,
                    this.serie);
            this.atualizarStatus(aguardar,
                    CL_GRAY);
            if (aguardar
                    && (System.currentTimeMillis() > this.aguardarAte)) {
                aguardar = false;
            }
            if (!aguardar) {
                this.setTimer(false,
                        0);
                if (this.rn.isNFEFalha()) {
                    this.atualizarStatus(aguardar,
                            "clRed");
                    this.rn.aguardaAprovarNFE(this.codEmpresa,
                            this.controle,
                            this.serie);
                } else if (this.rn.isNFEAprovada()) {
                    this.rn.abrirNFEMovimento(this.codEmpresa,
                            this.controle,
                            this.serie);
                    this.atualizarStatus(aguardar,
                            "clBlue");
                    this.btnReenviar.setEnabled(false);
                    this.btnTentarDepois.setFontColor("clWhite");
                    if (this.tbCrmpartsNfeMovimento.getTEM_PDF().asString().equals("S")) {
                        this.ok = true;
                        this.close();
                    } else {
                        this.btnGerarFilaPdf.setEnabled(true);
                    }
                } else {
                    this.atualizarStatus(aguardar,
                            CL_GRAY);
                }
            }
        } catch (DataException dataException) {
            this.setTimer(false,
                    0);
        }
    }

    private void setTimer(boolean iniciar,
                          int tempoAguardar) {
        this.btnReenviar.setEnabled(!iniciar);
        this.btnTentarDepois.setEnabled(true);
        if (iniciar) {
            this.pontos = ".";
            this.horaInicial = System.currentTimeMillis();
            this.aguardarAte = this.horaInicial
                    + tempoAguardar;
            this.atualizarStatus(true,
                    CL_GRAY);
            this.timerNfe.setInterval(1000);
            this.timerNfe.setRepeats(true);
            this.timerNfe.setEnabled(true);
        } else {
            this.timerNfe.setEnabled(false);
        }
    }

    private void setTimerPdf(boolean iniciar,
                             int tempoAguardar) {
        this.btnGerarFilaPdf.setEnabled(!iniciar);
        this.btnTentarDepois.setEnabled(true);
        if (iniciar) {
            this.pontos = ".";
            this.horaInicial = System.currentTimeMillis();
            this.aguardarAte = this.horaInicial
                    + tempoAguardar;
            this.atualizarStatusPdf(true
            );
            this.timerPdf.setInterval(1000);
            this.timerPdf.setRepeats(true);
            this.timerPdf.setEnabled(true);
        } else {
            this.timerPdf.setEnabled(false);
        }
    }

    private void aprovarNfe(double codEmpresa,
                            double controle,
                            String serie) {
        try {
            this.setCaption("Emissão NF-e");
            this.rn.iniciarRn(codEmpresa,
                    controle,
                    serie);
            this.lblNotaFiscalNr.setCaption("Nota fiscal nr. "
                    + String.format("%1$.0f",
                    controle)
                    + "/"
                    + serie);
            /*
             Aguardar um minuto
             */
            this.setTimer(true,
                    60000);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Falha ao iniciar geração NF-e. ", dataException);
        }
    }

    @Override
    public void btnReenviarClick(final Event<Object> event) {
        this.rn.reenviarNFE(this.codEmpresa,
                this.controle,
                this.serie);
        this.aprovarNfe(this.codEmpresa,
                this.controle,
                this.serie);
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        this.lblStatusNfe.setCaption("");
        this.lblNotaFiscalNr.setCaption("Nota fiscal nr. "
                + String.format("%1$.0f",
                this.controle)
                + "/"
                + this.serie);
        FreedomUtilities.invokeLater(() -> this.aprovarNfe(this.codEmpresa,
                this.controle,
                this.serie));
        String xml = this.tbCrmpartsNfeMovimento.getXML_NOTA().asString();
        this.btnDownloadXML.setEnabled((xml != null)
                && (!xml.isEmpty()));
        this.imgNFe.setImageSrc(EmpresaUtil.isCrmService()?"/images/crmservice310025.png":"/images/crmparts310025.png");
    }

    private void gerarPdf(double codEmpresa,
                          double controle,
                          String serie) {
        String gerou = this.rn.gerarFilaPDF(codEmpresa,
                controle,
                serie);
        this.hBoxStatusPdf.setVisible(true);
        if (gerou.equals("S")) {
            this.setTimerPdf(true,
                    45000);
        } else {
            if (gerou.equals("N")) {
                gerou = "Fila impressao PDF nao gerada!";
            }
            this.lblStatusPdf.setCaption(gerou);
        }
    }

    @Override
    public void btnGerarFilaPdfClick(final Event<Object> event) {
        this.gerarPdf(this.codEmpresa,
                this.controle,
                this.serie);
    }

    public boolean orcMapaTemNfeEmitidaComStatusAtiva(double codEmpresa,
                                                      double codOrcMapa) {
        try {
            this.rn.abrirNFEMovimentoComOrcMapa(codEmpresa,
                    codOrcMapa);
        } catch (DataException dataException) {
            return false;
        }
        this.codEmpresa = this.tbCrmpartsNfeMovimento.getCOD_EMPRESA().asDecimal();
        this.controle = this.tbCrmpartsNfeMovimento.getCONTROLE().asDecimal();
        this.serie = this.tbCrmpartsNfeMovimento.getSERIE().asString();
        boolean tbCrmpartsNfeMovimentoEmpty = tbCrmpartsNfeMovimento.isEmpty();
        if (tbCrmpartsNfeMovimentoEmpty) {
            return false;
        }
        return this.tbCrmpartsNfeMovimento.getNFE().asString().trim().equalsIgnoreCase("S");
    }

    public boolean podeEmitirNFe(double codEmpresa,
                                 double controle,
                                 String serie) {
        try {
            this.rn.abrirNFEMovimento(codEmpresa,
                    controle,
                    serie);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao abrir cursor da NF-e", dataException);
            return false;
        }
        this.codEmpresa = this.tbCrmpartsNfeMovimento.getCOD_EMPRESA().asDecimal();
        this.controle = this.tbCrmpartsNfeMovimento.getCONTROLE().asDecimal();
        this.serie = this.tbCrmpartsNfeMovimento.getSERIE().asString();
        boolean tbCrmpartsNfeMovimentoEmpty = this.tbCrmpartsNfeMovimento.isEmpty();
        if (tbCrmpartsNfeMovimentoEmpty) {
            return false;
        }
        String status = this.tbCrmpartsNfeMovimento.getSTATUS().asString();
        return !status.equals("1");
    }

    @Override
    public void btnDownloadXMLClick(Event<Object> event) {
        boolean retornoValidacaoAcessoK0246 = EmpresaUtil.validarAcesso("K0246");
        if (!retornoValidacaoAcessoK0246) {
            return;
        }
        String xml = this.tbCrmpartsNfeMovimento.getXML_NOTA().asString();
        String chaveNFE = this.tbCrmpartsNfeMovimento.getCHAVE_NFE().asString();
        String nomeDoArquivoXMLComExtensao = "nfe"
                + chaveNFE
                + ".xml";
        Exportacao.exportFile(xml.getBytes(),
                nomeDoArquivoXMLComExtensao);
    }

}
