package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmSelecionarEmpresaW;
import freedom.bytecode.rn.enun.EnTipoAtendimento;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmSelecionarEmpresaA extends FrmSelecionarEmpresaW {

    private static final long serialVersionUID = 20130827081850L;

    private boolean ok = false;

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private final int codEmpresaLogado = EmpresaUtil.getCodEmpresaUserLogged();

    private EnTipoAtendimento tipoAtendimento = EnTipoAtendimento.ATIVO;

    private int codEmpresaEvento;

    private boolean obrigaMidia = false;

    private boolean midiaVisible = false;

    public FrmSelecionarEmpresaA() {
        this.hbSelEmpAtivo.setAttribute("SCLASS_BASE", "hbbuttonleftradius");
        //hboxMarca3.setAttribute("SCLASS_BASE", "hboxmarca3 hbbuttoncenterradius");
        this.hbSelEmpReceptivo.setAttribute("SCLASS_BASE", "hbbuttoncenterradius");
        this.hbSelEmpPassivo.setAttribute("SCLASS_BASE", "hbbuttonrightradius");
    }

    public boolean isOk() {
        return this.ok;
    }


    public void filtrarComboCidade(String usuarioLogado, int codEmpresaLogado) {
        try {
            rn.filtrarComboCidade(usuarioLogado, codEmpresaLogado);
        } catch (DataException e) {
            EmpresaUtil.showError("Não foi possivel tornar a empresa do evento ativa", e);
        }
    }

    public void filtrarComboUf(String usuarioLogado, int codEmpresaLogado) {
        try {
            rn.filtrarComboUf(usuarioLogado, codEmpresaLogado);
        } catch (DataException e) {
            EmpresaUtil.showError("Não foi possivel carregar os dados do combo cidade", e);
        }
    }

    public void filtrarLeadsEmpresasUsuario(String usuarioLogado, int codEmpresaLogado, int codEmpresaEvento) {
        try {
            rn.filtrarLeadsEmpresasUsuario(usuarioLogado, codEmpresaLogado, codEmpresaEvento);
        } catch (DataException e) {
            EmpresaUtil.showError("Não foi possivel carregar os dados Leads Empresas Usuarios", e);
        }
    }

    public void recortarCidadePorEstado(String uf) {
        try {
            rn.recortarCidadePorEstado(uf);
        } catch (DataException e) {
            EmpresaUtil.showError("Não foi possivel filtrar as cidades pela UF", e);
        }
    }

    public void recortarEmpresaPorUfCidade(String textoEmpresa, String uf, String cidade, int codEmpresaEvento) {
        try {
            rn.recortarEmpresaPorUfCidade(textoEmpresa, uf, cidade, codEmpresaEvento);
        } catch (DataException e) {
            EmpresaUtil.showError("Não foi possivel fazer recorte das empresas por UF - Cidade", e);
        }
    }

    public boolean podeSelecionarEmp(int codEmpresaEvento,
                                     String titulo) {
        this.setCaption(titulo);
        this.codEmpresaEvento = codEmpresaEvento;
        filtrarLeadsEmpresasUsuario(this.usuarioLogado, this.codEmpresaLogado, this.codEmpresaEvento);
        filtrarComboUf(this.usuarioLogado, this.codEmpresaLogado);
        filtrarComboCidade(this.usuarioLogado, this.codEmpresaLogado);
        cmbUFClearClick(null);
        if (midiaVisible) {
            midia();
        } else {
            hboxMidia.setVisible(false);
        }
        return true;
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        this.close();
    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        if (this.tbLeadsEmpresasUsuarios.isEmpty()) {
            EmpresaUtil.showWarning("Atenção", "Nenhum Registro Selecionado!");
            return;
        }
        if (obrigaMidia && midiaVisible) {
            int codMidia = cbbMidia.getValue().asInteger();
            if (codMidia == 0) {
                EmpresaUtil.showWarning("Atenção", "Obrigatório Informar a Mídia!");
                return;
            }
        }
        this.ok = true;
        this.close();
    }

    @Override
    public void hbSelEmpAtivoClick(final Event<Object> event) {
        this.configurarBackGround(EnTipoAtendimento.ATIVO);
    }

    @Override
    public void hbSelEmpReceptivoClick(final Event<Object> event) {
        this.configurarBackGround(EnTipoAtendimento.RECEPTIVO);
    }

    @Override
    public void hbSelEmpPassivoClick(final Event<Object> event) {
        this.configurarBackGround(EnTipoAtendimento.PASSIVO);
    }

    private void configurarBackGround(EnTipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
        String colorDestaque = "clSilver";
        String colorDefault = "#FFFFFF";
        this.hbSelEmpAtivo.setColor(colorDefault);
        this.hbSelEmpReceptivo.setColor(colorDefault);
        this.hbSelEmpPassivo.setColor(colorDefault);
        if (tipoAtendimento == EnTipoAtendimento.ATIVO) {
            this.hbSelEmpAtivo.setColor(colorDestaque);
        }
        if (tipoAtendimento == EnTipoAtendimento.RECEPTIVO) {
            this.hbSelEmpReceptivo.setColor(colorDestaque);
        }
        if (tipoAtendimento == EnTipoAtendimento.PASSIVO) {
            this.hbSelEmpPassivo.setColor(colorDestaque);
        }
    }

    public EnTipoAtendimento getTipoAtendimento() {
        return this.tipoAtendimento;
    }

    @Override
    public void cmbUFChange(Event<Object> event) {
        recortarCidadePorEstado(cmbUF.getValue().asString());
        recortarEmpresaPorUfCidade(edEmpresa.getValue().asString(), cmbUF.getValue().asString(), cmbCidade.getValue().asString(), codEmpresaEvento);
    }

    @Override
    public void cmbUFClearClick(Event<Object> event) {
        recortarCidadePorEstado(cmbUF.getValue().asString());
        recortarEmpresaPorUfCidade(edEmpresa.getValue().asString(), cmbUF.getValue().asString(), cmbCidade.getValue().asString(), codEmpresaEvento);
    }

    @Override
    public void cmbCidadeChange(Event<Object> event) {
        recortarEmpresaPorUfCidade(edEmpresa.getValue().asString(), cmbUF.getValue().asString(), cmbCidade.getValue().asString(), codEmpresaEvento);
    }

    @Override
    public void cmbCidadeClearClick(Event<Object> event) {
        recortarEmpresaPorUfCidade(edEmpresa.getValue().asString(), cmbUF.getValue().asString(), cmbCidade.getValue().asString(), codEmpresaEvento);
    }

    @Override
    public void edEmpresaEnter(Event<Object> event) {
        recortarEmpresaPorUfCidade(edEmpresa.getValue().asString(), cmbUF.getValue().asString(), cmbCidade.getValue().asString(), codEmpresaEvento);
    }

    @Override
    public void edEmpresaExit(Event<Object> event) {
        recortarEmpresaPorUfCidade(edEmpresa.getValue().asString(), cmbUF.getValue().asString(), cmbCidade.getValue().asString(), codEmpresaEvento);
    }

    @Override
    public void tbLeadsEmpresasUsuariosAfterScroll(Event<Object> event) {
        midia();
    }

    @Override
    public void cbbTipoMidiaChange(Event<Object> event) {
        try {
            if (cbbTipoMidia.getValue().asInteger() > 0) {
                rn.carregaMidia(cbbTipoMidia.getValue().asInteger());
                if (tbMidia.count() == 1) {
                    cbbMidia.setValue(tbMidia.getCOD_MIDIA().asInteger());
                } else {
                    if (tbMidia.count() > 1) {
                        cbbMidia.setOpen(true);
                    }
                }
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Não foi possivel carregar os dados de Mídia", e);
        }
    }

    @Override
    public void cbbTipoMidiaClearClick(Event<Object> event) {
        tbMidia.close();
    }


    private void midia() {
        try {
            if (tbTipoMidia.count() == 0) {
                rn.carregaTipoMidia();
            }
            if (tbLeadsEmpresasUsuarios.count() > 0) {
                obrigaMidia = rn.getObrigaMidia(tbLeadsEmpresasUsuarios.getCOD_EMPRESA().asDecimal());
                if (obrigaMidia) {
                    lblObrigatorio.setVisible(true);
                    lblObrigatorio1.setVisible(true);
                } else {
                    lblObrigatorio.setVisible(false);
                    lblObrigatorio1.setVisible(false);
                }
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Não foi possivel carregar os dados de Mídia", e);
        }
    }

    public Double getCodMidia() {
        return cbbMidia.getValue().asDecimal();
    }

    public void setMidiaVisible(boolean midiaVisible) {
        this.midiaVisible = midiaVisible;
    }
}
