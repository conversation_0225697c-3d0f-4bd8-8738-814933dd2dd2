package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmObservacaoW;
import freedom.client.event.Event;
import freedom.util.EmpresaUtil;
import lombok.Getter;
import lombok.Setter;

public class FrmObservacaoA extends FrmObservacaoW {

    private static final long serialVersionUID = 20130827081850L;

    @Getter
    private String observacao;

    @Setter
    private boolean validarObservacao;

    @Getter
    private boolean ok = false;

    @Setter
    private String messageObritatorio = "Preencha o texto.";

    private boolean obrigaPreenchimentoObservacao = false;

    public void setMaxLength(
            int maxLength
    ) {
        this.memObservacao.setMaxlength(
                maxLength
        );
    }

    public void setNaoPermiteEditar() {
        this.btnOk.setVisible(
                false
        );
        this.memObservacao.setEnabled(
                false
        );
    }

    public FrmObservacaoA(
            boolean obrigaPreenchimentoObservacao
            ,int memObservacaoMaxlength
            ,String formcCaption
    ) {
        this.obrigaPreenchimentoObservacao = obrigaPreenchimentoObservacao;
        this.memObservacao.setMaxlength(
                memObservacaoMaxlength
        );
        if ((formcCaption != null)
                && (!formcCaption.isEmpty())) {
            this.setCaption(
                    formcCaption
            );
        }
    }

    public FrmObservacaoA() {
        this.memObservacao.setColor(
                "#faf9fa"
        );
    }
  
    public FrmObservacaoA titulo(String titulo) {
        this.setCaption(titulo);
        return this;
    }

    public FrmObservacaoA obrigarPreecherObservacao(boolean obrigar) {
            this.validarObservacao = obrigar;
        return this;
    }

    public FrmObservacaoA textoObservacao(String observacao) {
        this.observacao = observacao;
        this.memObservacao.setValue(observacao);
        return this;
    }

    public FrmObservacaoA tamanhoMaximoTexto(int maxLength) {
        this.memObservacao.setMaxlength(maxLength);
        return this;
    }

    public FrmObservacaoA permitirAlterar(boolean simOuNao) {
        this.btnOk.setVisible(simOuNao);
        this.memObservacao.setReadOnly(!simOuNao);
        return this;
    }
  
    public FrmObservacaoA corFundo(String corFundo) {
        this.memObservacao.setColor(corFundo);
        return this;
    }
  
    public void setObservacao(
            String observacao
    ) {
        this.observacao = observacao;
        this.memObservacao.setValue(
                observacao
        );
    }
  
    public FrmObservacaoA fonteTexto(String nomeFonte) {
        this.memObservacao.setFontName(nomeFonte);
        return this;
    }
  
    @Override
    public void btnOkClick(final Event<Object> event) {
        this.observacao = this.memObservacao.getValue().asString();
        if (this.validarObservacao
                && (this.observacao.trim().isEmpty())) {
            EmpresaUtil.showInformationMessage(
                    this.messageObritatorio
            );
            this.ok = false;
            return;
        }
        this.ok = true;
        this.close();
    }

    @Override
    public void btnCancelClick(final Event<Object> event) {
        this.observacao = "";
        this.ok = false;
        this.close();
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.memObservacao.setColor(
                "#faf9fa"
        );
        this.memObservacao.setFocus();
    }

}