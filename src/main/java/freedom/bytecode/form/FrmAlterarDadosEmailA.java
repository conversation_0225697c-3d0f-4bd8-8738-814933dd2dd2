package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmAlterarDadosEmailA extends FrmAlterarDadosEmailW {

    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;

    public boolean isOk() {
        return ok;
    }

    public void setOk(boolean ok) {
        this.ok = ok;
    }

    @Override
    public void btnSalvarClick(final Event event) {
        String ret = rn.validarDadosEmail(edtSenha.getValue().asString(),
                edtConfimarSenha.getValue().asString(), edtSenhaAntiga.getValue().asString());

        if (!ret.equals("")) {
            EmpresaUtil.showWarning("Atenção", ret);
            return;
        }
        salvar();
        ok = true;
        close();
    }

    private void salvar() {
        try {
            rn.salvarDadosEmail(edtSenha.getValue().asString());
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro ao salvar.", ex);
        }
    }

    @Override
    public void btnCancelarClick(final Event event) {
        ok = false;
        close();
    }

    public void filtrarEmpresasUsuarios(String nome) {
        try {
            rn.filtrarEmpresasUsuarios(nome);
            edtSenhaAntiga.setEnabled(!tbEmpresasUsuarios.getEMAIL_SENHA().asString().trim().equals(""));
            grbUsuario.setCaption("Usuário: " + tbEmpresasUsuarios.getNOME_COMPLETO().asString().trim());
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro filtrar empresas usuários", ex);
        }
    }

}
