package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAlterarSenhaW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;

public class FrmAlterarSenhaA extends FrmAlterarSenhaW {

    private static final long serialVersionUID = 20130827081850L;

    private final String usuario = EmpresaUtil.getUserLogged();

    private void salvar() {
        String senhaAtual = this.edtSenhaAtual.getValue().asString().trim();
        if (senhaAtual.isEmpty()) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblSenhaAtual.getCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da <PERSON>nco<PERSON>, alinhada ao centro.
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtSenhaAtual       // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtSenhaAtual.setFocus();
            return;
        }
        String novaSenha = this.edtNovaSenha.getValue().asString().trim();
        if (novaSenha.isEmpty()) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblNovaSenha.getCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro.
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtNovaSenha        // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtNovaSenha.setFocus();
            return;
        }
        String confirmaSenha = this.edtConfirmarSenha.getValue().asString().trim();
        if (confirmaSenha.isEmpty()) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblConfirmarSenha.getCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro.
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtConfirmarSenha   // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtConfirmarSenha.setFocus();
            return;
        }
        if (senhaAtual.trim().equals(novaSenha)) {
            String mensagem = "A senha atual não pode ser a mesma que a nova senha.";
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro.
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtNovaSenha        // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtNovaSenha.setFocus();
            return;
        }
        if (!novaSenha.trim().equals(confirmaSenha)) {
            String mensagem = "A nova senha não confere com a confirmação da senha.";
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro.
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtNovaSenha        // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtNovaSenha.setFocus();
            return;
        }
        try {
            this.rn.alterarSenha(
                    this.usuario
                    ,senhaAtual
                    ,novaSenha
                    ,confirmaSenha
            );
            this.edtNovaSenha.clear();
            this.edtConfirmarSenha.clear();
            this.edtSenhaAtual.clear();
            Dialog.create()
                    .showToastInfo(
                            "A senha foi alterada com sucesso." // Texto da mensagem
                            ,Constantes.MIDDLE_CENTER           // A mensagem será alinhada no centro-meio
                            ,10000                              // Milissegundos de exibição da mensagem
                            ,true                               // Exibe X para fechá-la antes de encerrar o tempo de exibição
                    );
            this.registrarATrocaDeSenhaDoUsuario(
                    this.usuario
            );
            FormUtil.closeTab(
                    this
            );
            this.close();
        } catch (
                Exception exception
        ) {
            Dialog.create()
                    .showNotificationAviso(
                            exception.getMessage()
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro.
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtSenhaAtual       // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtSenhaAtual.setFocus();
        }
    }

    private void registrarATrocaDeSenhaDoUsuario(
            String loginUsuario
    ) {
        try {
            this.rn.registrarATrocaDeSenhaDoUsuario(
                    loginUsuario
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao registrar a troca de senha do usuário."
                    ,dataException
            );
        }
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtConfirmarSenhaEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtNovaSenhaEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtSenhaAtualEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.edtSenhaAtual.setFocus();
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        FormUtil.closeTab(
                this
        );
        this.close();
    }

}