package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmCadastroAcessoFuncaoW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmCadastroAcessoFuncaoA extends FrmCadastroAcessoFuncaoW {
    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;
    private int idPainel = 0;

    public FrmCadastroAcessoFuncaoA(int idPainel) {
        this.idPainel = idPainel;
        initForm();
    }

    private void initForm() {
        try {
            rn.carregaGridAcessoFuncao(idPainel);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void gridAcessoFuncaogridFuncaoUnChecked(final Event event) {
        selIndicadores();
    }

    @Override
    public void gridAcessoFuncaogridFuncaoChecked(final Event event) {
        selIndicadores();
    }

    private void selIndicadores() {
        try {
            tbGridCadAcessoFunc.edit();
            if (tbGridCadAcessoFunc.getSEL().asString().equals("S")) {
                tbGridCadAcessoFunc.setSEL("N");
            } else {
                tbGridCadAcessoFunc.setSEL("S");
            }
            tbGridCadAcessoFunc.post();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnCancelarClick(final Event event) {
        ok = false;
        close();
    }

    @Override
    public void btnSalvarClick(final Event event) {
        if (validaAcessoFuncao()) {
            gravaAcessoFuncao();
            ok = true;
            close();
        } else {
            EmpresaUtil.showMessage("Atenção", "Nenhum item na grid foi selecionado!");
            return;
        }
    }

    private boolean validaAcessoFuncao() {
        boolean valida = false;
        try {
            tbGridCadAcessoFunc.first();
            while (!tbGridCadAcessoFunc.eof()) {
                if (tbGridCadAcessoFunc.getSEL().asString().equals("S")) {
                    valida = true;
                    break;
                }
                tbGridCadAcessoFunc.next();
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
        return valida;
    }

    private void gravaAcessoFuncao() {
        try {
            tbGridCadAcessoFunc.first();
            while (!tbGridCadAcessoFunc.eof()) {
                if (tbGridCadAcessoFunc.getSEL().asString().equals("S")) {
                    tbPainelAcessoFuncao.append();
                    tbPainelAcessoFuncao.setCOD_FUNCAO(tbGridCadAcessoFunc.getID().asInteger());
                    tbPainelAcessoFuncao.setID_PAINEL(idPainel);
                    tbPainelAcessoFuncao.post();
                }

                tbGridCadAcessoFunc.next();
            }
            tbPainelAcessoFuncao.applyUpdates();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    public boolean isOk() {
        return ok;
    }
}
