package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmGerencialPainelPreferenciaW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;

public class FrmGerencialPainelPreferenciaA extends FrmGerencialPainelPreferenciaW {
    private static final long serialVersionUID = 20130827081850L;
    private int idGrupoClasse;
    private boolean ok = false;

    public FrmGerencialPainelPreferenciaA(int idGrupoClasse) {
        this.idGrupoClasse = idGrupoClasse;
        initForm();
    }

    private void initForm() {
        try {
            rn.carregaDados();
            cbbPainel.setValue(tbPainelGerencialUsuario.getID().asInteger());
            atualizaImagePainel();
            carregaIndicadores();
            carregaQuebraIndicadores();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void carregaIndicadores() {
        try {
            rn.carregaIndicadores(cbbPainel.getValue().asInteger());
            tbGridIndicadores.locate("ID", tbGridIndicadores.getID().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void carregaQuebraIndicadores() {
        try {
            rn.carregaQuebraIndicadores(tbGridIndicadores.getID().asInteger(), idGrupoClasse);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnVoltarClick(final Event event) {
        ok = false;
        close();
    }

    @Override
    public void cbbPainelChange(final Event event) {
        atualizaImagePainel();
        carregaIndicadores();
    }

    @Override
    public void tbGridIndicadoresAfterScroll(final Event event) {
        carregaQuebraIndicadores();
    }

    @Override
    public void imgPainelClick(final Event event) {
        if (cbbPainel.getValue().asInteger().equals(tbEmpresasUsuarios.getID_PAINEL().asInteger())) {
            Dialog.create()
                    .title("Confirma")
                    .message("Confirma a retirada do painel {" + cbbPainel.getText() + "} como preferência do usuário?")
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            try {
                                rn.atualizaPreferenciaUsuario(0);
                                atualizaImagePainel();
                            } catch (DataException ex) {
                                EmpresaUtil.showError("Erro", ex);
                            }
                        }
                    });
        } else {
            Dialog.create()
                    .title("Confirma")
                    .message("Confirma colocar painel {" + cbbPainel.getText() + "} como preferência do usuário?\n" +
                            "Usuário somente poderá ter um unico painel de preferência!")
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            try {
                                rn.atualizaPreferenciaUsuario(cbbPainel.getValue().asInteger());
                                atualizaImagePainel();
                            } catch (DataException ex) {
                                EmpresaUtil.showError("Erro", ex);
                            }
                        }
                    });
        }
    }

    @Override
    public void btnAceitarClick(final Event event) {
        ok = true;
        close();
    }

    public boolean isOk() {
        return ok;
    }

    @Override
    public void gridIndicadoresfavGridIndicadoresClick(final Event event) {
        Dialog.create()
                .title("Confirma")
                .message("Confirma colocar o Indicador \n{" + tbGridIndicadores.getDESCRICAO().asString() + "} \ncomo favorito  do painel \n{" + cbbPainel.getText() + "}  ?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            rn.atualizaPreferencia(cbbPainel.getValue().asInteger(), tbGridIndicadores.getID().asInteger(), 0);
                            carregaIndicadores();
                        } catch (DataException ex) {
                            EmpresaUtil.showError("Erro", ex);
                        }
                    }
                });
    }

    @Override
    public void gridQuebrafavGridQuebrasClick(final Event event) {
        Dialog.create()
                .title("Confirma")
                .message("Confirma adiconar quebra  \n{" + tbGridQuebraInd.getDESCRICAO_QUEBRA().asString() + "} \n" +
                        " como favorito do Indicador {" + tbGridIndicadores.getDESCRICAO().asString() + "} ?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            rn.atualizaPreferencia(cbbPainel.getValue().asInteger(), tbGridQuebraInd.getID_INDICADOR().asInteger(),
                                    tbGridQuebraInd.getID_QUEBRA().asInteger());
                            carregaQuebraIndicadores();
                        } catch (DataException ex) {
                            EmpresaUtil.showError("Erro", ex);
                        }
                    }
                });
    }

    private void atualizaImagePainel() {
        try {
            rn.carregaEmpresasUsuarios();
            if (cbbPainel.getValue().asInteger().equals(tbEmpresasUsuarios.getID_PAINEL().asInteger())) {
                imgPainel.setImageSrc("/images/crmservice4300107.png");
            } else {
                imgPainel.setImageSrc("/images/crmservice4300106.png");
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }
}
