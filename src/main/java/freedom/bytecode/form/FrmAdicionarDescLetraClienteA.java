package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAdicionarDescLetraClienteW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmAdicionarDescLetraClienteA extends FrmAdicionarDescLetraClienteW {

    private static final long serialVersionUID = 20130827081850L;

    private String codigoDaLetraDeDesconto;

    private double descontoParaOCliente;

    private boolean fechadoAoSalvar = false;

    public boolean isFechadoAoSalvar() {
        return this.fechadoAoSalvar;
    }

    public FrmAdicionarDescLetraClienteA() {
        try {
            this.rn.carregarLetrasDeDeconto();
            this.codigoDaLetraDeDesconto = "";
            this.descontoParaOCliente = 0.0;
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar letras de desconto"
                    ,dataException
            );
        }
    }

    public String getCodigoDaLetraDeDesconto() {
        return this.codigoDaLetraDeDesconto;
    }

    public double getDescontoParaOCliente() {
        return this.descontoParaOCliente;
    }

    public void setCodigoDaLetraDeDesconto(
            String codigoDaLetraDeDesconto
    ) {
        this.codigoDaLetraDeDesconto = codigoDaLetraDeDesconto;
        this.cboLetraDeDesconto.setValue(
                this.codigoDaLetraDeDesconto
        );
    }

    public void setDescontoParaOCliente(
            double descontoParaOCliente
    ) {
        this.descontoParaOCliente = descontoParaOCliente;
        this.edtDescontoParaOCliente.setValue(
                this.descontoParaOCliente
        );
    }

    private void salvar() {
        this.codigoDaLetraDeDesconto = this.cboLetraDeDesconto.getValue().asString();
        boolean codigoDaLetraDeDescontoEmpty = this.codigoDaLetraDeDesconto.isEmpty();
        if (codigoDaLetraDeDescontoEmpty) {
            String mensagem = (
                    "O campo \""
                            + this.lblLetraDeDesconto.getCaption()
                            + "\" deve ser preenchido."
            );
            Dialog.create()
                    .title("Informação")
                    .message(mensagem)
                    .showInformation((Event event1) -> FreedomUtilities.invokeLater(() -> {
                        this.cboLetraDeDesconto.setFocus();
                        this.cboLetraDeDesconto.setOpen(true);
                    }));
            return;
        }
        this.descontoParaOCliente = this.edtDescontoParaOCliente.getValue().asDecimal();
        if ((this.descontoParaOCliente > 99.99)
                || (this.descontoParaOCliente < 0.01)) {
            String mensagem = (
                    "O campo \""
                            + this.lblDescontoParaOCliente.getCaption()
                            + "\" deve ser preenchido com o valor entre \"0,01\" e \"99,99\"."
            );
            Dialog.create()
                    .title("Informação")
                    .message(mensagem)
                    .showInformation((Event event1) -> FreedomUtilities.invokeLater(() -> this.edtDescontoParaOCliente.setFocus()));
            return;
        }
        this.fechadoAoSalvar = true;
        this.close();
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        this.salvar();
    }

    @Override
    public void cboLetraDeDescontoChange(final Event<Object> event) {
        double percentualDeDescontoDaLetra = this.tbItensPerDescFlag.getDESCONTO().asDecimal();
        double descontoParaOCliente = this.edtDescontoParaOCliente.getValue().asDecimal();
        if (descontoParaOCliente == 0.0) {
            this.edtDescontoParaOCliente.setValue(
                    percentualDeDescontoDaLetra
            );
            this.edtDescontoParaOCliente.select();
        }
        this.edtDescontoParaOCliente.setFocus();
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        String codigoDaLetraDeDesconto = this.cboLetraDeDesconto.getText().trim();
        if (codigoDaLetraDeDesconto.isEmpty()) {
            FreedomUtilities.invokeLater(() -> {
                this.cboLetraDeDesconto.setFocus();
                this.cboLetraDeDesconto.setOpen(
                        true
                );
            });
        } else {
            this.edtDescontoParaOCliente.setFocus();
            this.edtDescontoParaOCliente.setSelectionRange(
                    0
                    ,(this.edtDescontoParaOCliente.getValue().asString().length() + 1)
            );
        }
    }

    @Override
    public void edtDescontoParaOClienteEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void cboLetraDeDescontoEnter(Event<Object> event) {
        this.salvar();
    }

}
