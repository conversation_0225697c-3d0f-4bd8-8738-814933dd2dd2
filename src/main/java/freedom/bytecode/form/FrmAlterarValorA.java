package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.util.EmpresaUtil;

public class FrmAlterarValorA extends FrmAlterarValorW {

    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;
    private final double valorAltOriginal;

    public FrmAlterarValorA(double valorAlt) {
        valorAltOriginal = valorAlt;
        edtValor.setValue(valorAlt);
    }

    @Override
    public void btnOkClick(final Event event) {
        if (edtValor.getValue().asDecimal() < 0) {
            EmpresaUtil.showWarning("Atenção", "Valor não pode ser inferior a 0.00(Zero)");
            return;
        }

        ok = true;
        close();
    }

    @Override
    public void btnCancelarClick(final Event event) {
        ok = false;
        close();
    }

    public boolean isOk() {
        return ok;
    }

    public double getValorOriginal() {
        return valorAltOriginal;
    }

    public double getValor() {
        return edtValor.getValue().asDecimal();
    }

}
