package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmPesquisarEmpresasUsuariosA extends FrmPesquisarEmpresasUsuariosW {

    private static final long serialVersionUID = 20130827081850L;

    private boolean ok = false;

    public boolean isOk() {
        return ok;
    }

    public FrmPesquisarEmpresasUsuariosA() {
        abrirTabAux();
        cmbFiltroEmpresa.setValue(EmpresaUtil.getCodEmpresaUserLogged());
    }

    public void iniciarConsulta() {
        consultar();
    }

    public void abrirTabAux() {
        try {
            rn.abrirTabelas();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnVoltarClick(final Event event) {
        close();
    }

    @Override
    public void btnAceitarClick(final Event event) {
        if (tbEmpresasUsuarios.isEmpty()) {
            EmpresaUtil.showMessage("Atenção", "Nenhum Registro Encontrado.");
            return;
        }
        ok = true;
        close();
    }

    @Override
    public void btnPesquisarClick(final Event event) {
        consultar();
    }

    @Override
    public void gridEmpresaUsuarioselecionarConsultor(Event<Object> event) {
        ok = true;
        close();
    }

    private void consultar() {
        try {
            String nomeConsultor = edtNomeUsuario.getValue().asString();
            int codEmpresa = cmbFiltroEmpresa.getValue().asInteger();
            rn.pesquisar(nomeConsultor, codEmpresa);
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao pesquisar ", e);
        }
    }

    @Override
    public void cmbFiltroEmpresaChange(Event<Object> event) {
        consultar();
    }

    @Override
    public void cmbFiltroEmpresaClearClick(Event<Object> event) {
        consultar();
    }

    @Override
    public void edtNomeUsuarioEnter(Event<Object> event) {
        consultar();
    }

}
