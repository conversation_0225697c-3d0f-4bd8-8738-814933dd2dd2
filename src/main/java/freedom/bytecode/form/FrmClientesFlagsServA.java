package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmClientesFlagsServW;
import freedom.client.controls.IInputComponent;
import freedom.client.controls.impl.TFCombo;
import freedom.client.controls.impl.TFImage;
import freedom.client.controls.impl.TFString;
import freedom.client.controls.impl.TFTable;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmServiceRNA;

import java.util.logging.Level;
import java.util.logging.Logger;

public class FrmClientesFlagsServA extends FrmClientesFlagsServW {

    private static final long serialVersionUID = 20130827081850L;
    private final PkgCrmServiceRNA pkCrmServiceRna = new PkgCrmServiceRNA();
    private double codCliente;
    private Value valor = new Value(null);
    private boolean ok = false;

    public FrmClientesFlagsServA() {
        carregaCombo();
        hboxAltera.invalidate();
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        filtraGrid(0, "");
        hboxAltera.invalidate();
        edtPesquisaDescricao.setPrompt("Pesquisar por Descrição");
    }

    public boolean consultaCliente(Double codCliente) {
        try {
            this.codCliente = codCliente;
            rn.consultaClienteDiverso(codCliente);
            return true;
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Cliente", ex);
            return false;
        }
    }

    private void carregaCombo() {
        try {
            rn.carregaCombo();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados", ex);
        }
    }

    private void filtraGrid(int idGrupo, String Descricao) {
        try {
            rn.filtraGrid(idGrupo, Descricao);
            rn.filtraValor();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados", ex);
        }
    }

    @Override
    public void btnPesquisarClick(final Event event) {
        filtraGrid(cbbClienteFlagGrupo.getValue().asInteger(), edtPesquisaDescricao.getValue().asString());
    }

    @Override
    public void tbClienteFlagAfterScroll(Event<Object> event) {
        modifyFieldAlterarValor();
    }

    private void filtrarValor(int codEmpresa) {
        try {
            rn.filtraValor();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados", ex);
        }
    }

    private void modifyFieldAlterarValor() {
        try {
            setVisibleComponent(Boolean.FALSE);

            if (tbClienteFlag.getPOR_EMPRESA().equals("S")) {
                hboxAltera.setVisible(Boolean.FALSE);
            } else {
                hboxAltera.setVisible(Boolean.TRUE);
            }

            if (!tbClienteFlag.getOBJETO().isNull()) {
                String objeto = tbClienteFlag.getOBJETO().asString();
//                String showButtonFind = tbClienteFlag.getSHOW_BUTTON_FIND().asString();

//                vBoxImageButtonFind.setVisible(showButtonFind.equals("S"));
                switch (objeto) {
                    case "S": // String
                        setFieldTable(edtValorString, objeto);
                        break;
                    case "B": // CheckBox
                        if (!tbClienteFlag.getLIST_OPTION().asString().equals("")) {
                            setFieldTable(cbbValor, objeto);
                        } else {
                            setFieldTable(chkValor, objeto);
                        }
                        break;
                    case "C": // ComboBox
                        setFieldTable(cbbValor, objeto);
                        break;
                    case "L": // List Options ComboBox
                        setFieldTable(cbbValor, objeto);
                        break;
                    case "N": // Númerico
                        setFieldTable(edtValorDecimal, objeto);
                        break;
                    case "I": // Inteiro
                        setFieldTable(edtValorInteger, objeto);
                        break;
                    case "D": // Data
                        setFieldTable(edtValorDate, objeto);
                        break;
                    case "H": // hora
                        setFieldTable(edtValorHora, objeto);
                        break;
                    case "P": // Password
                        setFieldTable(edtValorString, objeto);
                        break;
                    case "A":
                        FImageParam.setValue(valor);
                        setFieldImage(FImageParam);
                        break;
                }

            }
        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao consultar parâmetros.")
                    .message(ex.getMessage())
                    .showException(ex);
        }
    }

    private void setVisibleComponent(Boolean visible) {
        edtValorString.setVisible(visible);
        edtValorDecimal.setVisible(visible);
        edtValorDate.setVisible(visible);
        edtValorInteger.setVisible(visible);
        cbbValor.setVisible(visible);
        chkValor.setVisible(visible);
        edtValorHora.setVisible(visible);
        FImageParam.setVisible(visible);
    }

    private void setFieldTable(IInputComponent component, String tipoObj) throws DataException {
        component.setFieldName("VALOR");

        if (component instanceof TFCombo) {

            if (tbClienteFlag.getLIST_OPTION().asString().equals("")) {
                setComboLookup(((TFCombo) component), tbClienteFlag, tbClienteFlag);
            } else {
                ((TFCombo) component).setListOptions(tbClienteFlag.getLIST_OPTION().asString());
            }

        } else if (component instanceof TFString) {
            if ("P".equals(tipoObj)) {
                ((TFString) component).setPwd(Boolean.TRUE);
            } else {
                ((TFString) component).setPwd(Boolean.FALSE);
            }
        }

        component.setVisible(Boolean.TRUE);
    }

    private void setComboLookup(TFCombo comboBox, TFTable tableParmSys, TFTable tableParmEmpValues) throws DataException {
        rn.setComboLookup(comboBox, tableParmSys, tableParmEmpValues);
    }

    private void setFieldImage(TFImage imageParam) throws DataException {
        // Arquivo Imagem
        imageParam.setVisible(true);
    }

    @Override
    public void btnAlterarClick(final Event event) {
        try {
            String retFuncao = pkCrmServiceRna.validarAcessoClienteFlag(EmpresaUtil.getUserLogged(), tbClienteFlag.getID_GRUPO().asDecimal());
            if (retFuncao.equals("N")) {
                int idClienteFlag = tbClienteFlag.getID().asInteger();
                EmpresaUtil.showMessage("Atenção", "Usuário não tem acesso para alterar a Flag! Verifique Acessos Clientes Flag.");
                filtrarValor(0);
                tbClienteFlag.locate("ID", idClienteFlag);
                return;
            }
            alterarValor();
        } catch (DataException ex) {
            Logger.getLogger(FrmClientesFlagsServA.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void alterarValor() {
        try {
            int idClienteFlag = tbClienteFlag.getID().asInteger();
            String tipoAlteracao = "E";

            Value value = tbClienteFlag.getField("VALOR");
            boolean excessao = validaCampo();

            if (!excessao) {

                tbClienteFlag.edit();
                tbClienteFlag.setField("VALOR", value);
                tbClienteFlag.post();

                Boolean sucesso = rn.salvarAlteracoes(tipoAlteracao, 0, codCliente, value);
            }
            filtrarValor(0);
            tbClienteFlag.locate("ID", idClienteFlag);
        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro Salvar Alteração.")
                    .message(ex.getMessage())
                    .showException(ex);
        }
    }

    public void gridClienteFlagclientesFlagsEmpClick(Event<Object> event) {

        if ((tbClienteFlag.getTABELA_DELPHI().asString().toUpperCase().equals("CLIENTE_ISS_EMPRESA")
                || tbClienteFlag.getTABELA_DELPHI().asString().toUpperCase().equals("CLIENTE_ISENTO_ISS_EMPRESA")
                || tbClienteFlag.getTABELA_DELPHI().asString().toUpperCase().equals("CLIENTE_IGNORA_PJ_RETER_ISS"))
                && tbClienteDiverso.getCLASSE().asString().equals("F")) {

            EmpresaUtil.showMessage("Atenção", "Cliente não pode ser Pessoa Física");

        } else {
            FrmClientesFlagsServEmpA form = new FrmClientesFlagsServEmpA();
            form.setCaption(tbClienteFlag.getDESCRICAO().asString());
            form.setMode("UPD");
            form.iniciarValores(tbClienteFlag.getTABELA_DELPHI().asString(),
                    tbClienteFlag.getCAMPO_DELPHI().asString(),
                    tbClienteFlag.getOBJETO().asString(),
                    codCliente);
            FormUtil.doShow(form, (EventListener) t -> {
            });
        }
    }

    private boolean validaCampo() throws DataException {
        boolean valida = false;

        Value value = tbClienteFlag.getField("VALOR");
        String tipoCampo = tbClienteFlag.getOBJETO().asString().trim();
        String campo = tbClienteFlag.getCAMPO_DELPHI().asString().trim();
        String msg = "";

        if ((tipoCampo.trim().equals("N")) || (tipoCampo.trim().equals("I")) || (tipoCampo.trim().equals("C"))) {
            switch (tipoCampo.trim()) {
                case "N":
                    if (value.asDecimal() < 0) {
                        msg = "Valor não pode ser inferior a 0.00 (Zero).";
                    }
                    break;
                case "I":
                    if (value.asInteger() < 0) {
                        msg = "Valor não pode ser inferior a 0 (Zero).";
                    }
                    break;
                case "C":
                    if (value.asString().trim().equals("")) {
                        msg = "Informe o valor.";
                    }
                    break;

            }
        }

        if (msg.trim().equals("")) {
            switch (campo.toUpperCase()) {
                case "CLIENTE_REIDI":
                    if (value.toString().equals("S") && tbClienteDiverso.getOPTANTE_SIMPLES().asString().equals("S")) {
                        msg = "Cliente Beneficiário do REIDI poderá ser marcado somente para cliente NÃO seja Optante do Simples";
                    }
                    if (value.toString().equals("S") && tbClienteDiverso.getCLASSE().asString().equals("F")) {
                        msg = "Cliente Beneficiário do REIDI poderá ser marcado somente para cliente que seja Pessoa Jurídica";
                    }
                    break;
                case "ALIQ_RED_ICMS_TRANSP":
                    if (tbClienteDiverso.getCLIENTE_TRANSPORTADORA().asString().equals("N") || tbClienteDiverso.getCLIENTE_TRANSPORTADORA().isNull()) {
                        msg = "Cliente não marcado como transportadora";
                    }
                    break;
                case "OPTANTE_SIMPLES":
                    if (tbClienteDiverso.getCLIENTE_REIDI().asString().equals("S")) {
                        msg = "Cliente é Beneficiário do REIDI não pode ser marcado como Optante do Simples";
                    }
                    break;
                case "ISENTO_ISS":
                    if (value.toString().equals("S") && tbClienteDiverso.getCLASSE().asString().equals("F")) {
                        rn.consultaEndCliIE(codCliente);
                        if (tbClientesEnderecoIe.getINSCRICAO_ESTADUAL().isNull()) {
                            msg = "Apenas pessoas Juridicas e Pessoa Física com Inscrição Estadual, podem habilitar este parâmetro.";
                        }
                    }
                    break;
                case "CLIENTE_ESTRANG_CONSUMIDOR":
                    if (value.toString().equals("S")) {
                        rn.consultaClientes(codCliente);
                        if (!tbClientes.getUF_RES().toString().equals("EX")
                                || !tbClientes.getUF_COM().toString().equals("EX")
                                || !tbClientes.getUF_COBRANCA().toString().equals("EX")
                                || !tbClienteDiverso.getUF().toString().equals("EX")) {
                            msg = "Para marcar o parâmetro, o cliente deve ser estrangeiro 'Sigla da UF (UF) = EX '";
                        }
                    }
                    break;
            }
        }

        if (!msg.trim().equals("")) {
            Dialog.create()
                    .title("Atenção")
                    .message(msg)
                    .showWarning();
            valida = true;
        }
        return valida;
    }

    @Override
    public void lblClienteClick(final Event event) {
        FrmCadastroRapidoClienteU form = new FrmCadastroRapidoClienteU();
        if (form.buscarCliente(codCliente)) {
            FormUtil.doModal(form, (EventListener) t -> {
                filtraGrid(0, "");
            });
        }
    }
}
