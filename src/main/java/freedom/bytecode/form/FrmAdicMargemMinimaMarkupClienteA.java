package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAdicMargemMinimaMarkupClienteW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;

public class FrmAdicMargemMinimaMarkupClienteA extends FrmAdicMargemMinimaMarkupClienteW {

    private static final long serialVersionUID = 20130827081850L;

    private double margemMinima;

    public FrmAdicMargemMinimaMarkupClienteA(
            double margemMinima
    ) {
        this.margemMinima = margemMinima;
    }

    private void salvar() {
        this.margemMinima = this.edtMargemMinima.getValue().asDecimal();
        if ((this.margemMinima <= 0.0)
                || (this.margemMinima >= 100.0)) {
            Dialog.create()
                    .title("Informação")
                    .message("A margem mínima deve ser maior que \"0\" (zero) e menor que \"100\" (cem).")
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                        this.edtMargemMinima.setFocus();
                        this.edtMargemMinima.setSelectionRange(
                                0
                                ,(this.edtMargemMinima.getValue().asString().length() + 1)
                        );
                    }));
        } else {
            this.margemMinima = this.edtMargemMinima.getValue().asDecimal();
            this.close();
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtMargemMinimaEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.edtMargemMinima.setValue(
                this.margemMinima
        );
        this.edtMargemMinima.setFocus();
        this.edtMargemMinima.setSelectionRange(
                0
                ,(this.edtMargemMinima.getValue().asString().length() + 1)
        );
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

}
