package freedom.bytecode.form;

import freedom.bytecode.cursor.LISTA_FORMAS_PGTO;
import freedom.bytecode.form.wizard.FrmPesquisaFormaPgtoW;
import freedom.client.controls.impl.TFGrid;
import freedom.client.controls.impl.TFTable;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.data.impl.View;
import freedom.util.EmpresaUtil;
import freedom.util.GridUtil;
import lombok.Getter;

public class FrmPesquisaFormaPgtoA extends FrmPesquisaFormaPgtoW {

    private double codEmpresaFiltrada;

    private double codDepartamentoFiltrado;

    private double codFormaPagamentoFiltrada;

    private String codTipoFormaPagamentoFiltrada;

    private String exclusivaFiltrada;

    @Getter
    private boolean fechadoAoSalvar;

    @Getter
    private LISTA_FORMAS_PGTO tabelaDeRegistrosSelecionadosNaGrade;

    private final GridUtil gridUtil = new GridUtil();

    private static final long serialVersionUID = 20130827081850L;

    public FrmPesquisaFormaPgtoA(
            long codClienteFiltrado
    ) {
        try {
            this.codEmpresaFiltrada = 0.0;
            this.codDepartamentoFiltrado = 0.0;
            this.codTipoFormaPagamentoFiltrada = "";
            this.codFormaPagamentoFiltrada = 0.0;
            this.tabelaDeRegistrosSelecionadosNaGrade = new LISTA_FORMAS_PGTO("tabelaDeRegistrosSelecionadosNaGrade");
            this.exclusivaFiltrada = "";
            this.rn.setCodClienteFiltrado(
                    codClienteFiltrado
            );
            this.rn.carregarTiposDeFormasDePagamentoDoFiltro();
            this.rn.carregarEmpresasDoFiltro();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao criar o formulário 'Selecionar formas de pagamento'."
                    ,dataException
            );
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            View vTbListaFormasPgto = this.tbListaFormasPgto.getView();
            vTbListaFormasPgto.first();
            while (Boolean.FALSE.equals(vTbListaFormasPgto.eof())) {
                String sel = vTbListaFormasPgto.getField("SEL").asString();
                if (sel.equals("S")) {
                    long empCodigo = vTbListaFormasPgto.getField("EMP_CODIGO").asLong();
                    long dptoCodigo = vTbListaFormasPgto.getField("DPTO_CODIGO").asLong();
                    long formaCodigo = vTbListaFormasPgto.getField("FORMA_CODIGO").asLong();
                    this.tabelaDeRegistrosSelecionadosNaGrade.append();
                    this.tabelaDeRegistrosSelecionadosNaGrade.setEMP_CODIGO(
                            empCodigo
                    );
                    this.tabelaDeRegistrosSelecionadosNaGrade.setDPTO_CODIGO(
                            dptoCodigo
                    );
                    this.tabelaDeRegistrosSelecionadosNaGrade.setFORMA_CODIGO(
                            formaCodigo
                    );
                    this.tabelaDeRegistrosSelecionadosNaGrade.post();
                }
                vTbListaFormasPgto.next();
            }
            boolean tabelaDeRegistrosSelecionadosNaGradeEmpty = this.tabelaDeRegistrosSelecionadosNaGrade.isEmpty();
            if (tabelaDeRegistrosSelecionadosNaGradeEmpty) {
                EmpresaUtil.showInformationMessage(
                        "Selecione algum registro."
                );
                return;
            }
            this.fechadoAoSalvar = true;
            this.close();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao clicar em 'Salvar'."
                    ,dataException
            );
        }
    }

    @Override
    public void cboEmpresaChange(final Event<Object> event) {
        try {
            this.codEmpresaFiltrada = cboEmpresa.getValue().asLong();
            this.rn.setCodEmpresaFiltrada(
                    this.codEmpresaFiltrada
            );
            this.rn.carregarDepartamentosDoFiltro();
            this.btnPesquisarClick(event);
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o valor do filtro 'Empresa'."
                    ,dataException
            );
        }
    }

    @Override
    public void cboEmpresaClearClick(final Event<Object> event) {
        try {
            this.codEmpresaFiltrada = 0.0;
            this.rn.setCodEmpresaFiltrada(
                    this.codEmpresaFiltrada
            );
            this.rn.carregarDepartamentosDoFiltro();
            this.btnPesquisarClick(event);
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o valor do filtro 'Empresa'."
                    ,dataException
            );
        }
    }

    @Override
    public void cboDepartamentoChange(final Event<Object> event) {
        try {
            this.codDepartamentoFiltrado = cboDepartamento.getValue().asLong();
            this.rn.setCodDepartamentoFiltrado(
                    this.codDepartamentoFiltrado
            );
            this.rn.carregarTiposDeFormasDePagamentoDoFiltro();
            this.rn.carregarFormasDePagamentoDoFiltro();
            this.btnPesquisarClick(event);
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o valor do filtro 'Departamento'."
                    ,dataException
            );
        }
    }

    @Override
    public void cboDepartamentoClearClick(final Event<Object> event) {
        try {
            this.codDepartamentoFiltrado = 0.0;
            this.rn.setCodDepartamentoFiltrado(
                    this.codDepartamentoFiltrado
            );
            this.codTipoFormaPagamentoFiltrada = "";
            this.rn.setCodTipoFormaPagamentoFiltrada(
                    this.codTipoFormaPagamentoFiltrada
            );
            this.codFormaPagamentoFiltrada = 0.0;
            this.rn.setCodFormaDePagamentoFiltrada(
                    this.codFormaPagamentoFiltrada
            );
            this.rn.carregarTiposDeFormasDePagamentoDoFiltro();
            this.rn.carregarFormasDePagamentoDoFiltro();
            this.btnPesquisarClick(event);
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o valor do filtro 'Departamento'."
                    ,dataException
            );
        }
    }

    @Override
    public void cboTipoFormaPagamentoChange(final Event<Object> event) {
        try {
            this.codTipoFormaPagamentoFiltrada = cboTipoFormaPagamento.getValue().asString();
            this.rn.setCodTipoFormaPagamentoFiltrada(
                    this.codTipoFormaPagamentoFiltrada
            );
            this.rn.carregarFormasDePagamentoDoFiltro();
            this.btnPesquisarClick(event);
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o valor do filtro 'Tipo da forma de pagamento'."
                    ,dataException
            );
        }
    }

    @Override
    public void cboTipoFormaPagamentoClearClick(final Event<Object> event) {
        try {
            this.codTipoFormaPagamentoFiltrada = "";
            this.rn.setCodTipoFormaPagamentoFiltrada(
                    this.codTipoFormaPagamentoFiltrada
            );
            this.rn.carregarFormasDePagamentoDoFiltro();
            this.btnPesquisarClick(event);
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o valor do filtro 'Tipo da forma de pagamento'."
                    ,dataException
            );
        }
    }

    @Override
    public void cboFormaPagamentoChange(final Event<Object> event) {
        this.codFormaPagamentoFiltrada = cboFormaPagamento.getValue().asLong();
        this.rn.setCodFormaDePagamentoFiltrada(
                this.codFormaPagamentoFiltrada
        );
        this.btnPesquisarClick(event);
    }

    @Override
    public void cboFormaPagamentoClearClick(final Event<Object> event) {
        this.codFormaPagamentoFiltrada = 0.0;
        this.rn.setCodFormaDePagamentoFiltrada(
                this.codFormaPagamentoFiltrada
        );
        this.btnPesquisarClick(event);
    }

    @Override
    public void cboExclusivaChange(final Event<Object> event) {
        try {
            this.exclusivaFiltrada = cboExclusiva.getValue().asString();
            this.rn.setExclusivaFiltrada(
                    this.exclusivaFiltrada
            );
            this.rn.carregarFormasDePagamentoDoFiltro();
            this.btnPesquisarClick(event);
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o valor do filtro 'Exclusiva'."
                    ,dataException
            );
        }
    }

    @Override
    public void cboExclusivaClearClick(final Event<Object> event) {
        try {
            this.exclusivaFiltrada = "";
            this.rn.setExclusivaFiltrada(this.exclusivaFiltrada);
            this.rn.carregarFormasDePagamentoDoFiltro();
            this.btnPesquisarClick(event);
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o valor do filtro 'Exclusiva'."
                    ,dataException
            );
        }
    }

    @Override
    public void btnPesquisarClick(final Event<Object> event) {
        try {
            this.rn.carregarFormasDePagamentoDaGrade();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao clicar em 'Pesquisar'."
                    ,dataException
            );
        }
    }

    private void exportExcel(
            TFTable table
            ,TFGrid grid
    ) {
        try {
            this.gridUtil.exportarExcel(
                    table
                    ,grid
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao exportar excel"
                    ,dataException
            );
        }
    }

    @Override
    public void mmExportarExcelGrdFormasPgtoClick(final Event<Object> event) {
        this.exportExcel(
                this.tbListaFormasPgto
                ,this.grdFormasPgto
        );
    }

    private void selecionarRegistrosNaGrade(
            String value
    ) {
        try {
            this.tbListaFormasPgto.first();
            while (Boolean.FALSE.equals(this.tbListaFormasPgto.eof())) {
                this.tbListaFormasPgto.edit();
                this.tbListaFormasPgto.setSEL(
                        value
                );
                this.tbListaFormasPgto.post();
                this.tbListaFormasPgto.next();
            }
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao selecionar"
                    ,dataException
            );
        }
    }

    @Override
    public void mmSelecionarTodosClick(final Event<Object> event) {
        this.selecionarRegistrosNaGrade("S");
    }

    @Override
    public void mmSelecionarNenhumClick(final Event<Object> event) {
        this.selecionarRegistrosNaGrade("N");
    }

    @Override
    public void grdFormasPgtomarcarRegistroDaGrade(final Event<Object> event) {
        try {
            this.tbListaFormasPgto.edit();
            this.tbListaFormasPgto.setSEL("S");
            this.tbListaFormasPgto.post();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao marcar registro da grade"
                    ,dataException
            );
        }
    }

    @Override
    public void grdFormasPgtodesmarcarRegistroDaGrade(final Event<Object> event) {
        try {
            this.tbListaFormasPgto.edit();
            this.tbListaFormasPgto.setSEL("N");
            this.tbListaFormasPgto.post();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao desmarcar registro da grade"
                    ,dataException
            );
        }
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

}
