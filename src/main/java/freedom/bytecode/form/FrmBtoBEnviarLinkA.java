package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmBtoBEnviarLinkW;
import freedom.client.controls.impl.TFPageControl;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;
import org.zkoss.zk.ui.HtmlBasedComponent;
import org.zkoss.zul.Tabbox;

import java.util.logging.Level;
import java.util.logging.Logger;

public class FrmBtoBEnviarLinkA extends FrmBtoBEnviarLinkW {

    private static final long serialVersionUID = 20130827081850L;
    boolean atualizaFilaCrmParts = false;
    boolean enableEvento = true;

    private void setFontTab(TFPageControl pag, int fontSize) {
        ((Tabbox) pag.getImpl()).getTabs().getChildren().forEach((t) -> {
            ((HtmlBasedComponent) t).setStyle("font-size: " + fontSize + "px");
        });
    }

    public FrmBtoBEnviarLinkA(long codEvento, long codCliente) throws DataException {
        tbEmpresasUsuarios.setMaxRowCount(Integer.MAX_VALUE);
        tbBtobEnderecoClienteEntrega.setMaxRowCount(Integer.MAX_VALUE);
        tbBtobClientesPequisaEmail.setMaxRowCount(Integer.MAX_VALUE);
        tbBtobEnderecoClienteFatura.setMaxRowCount(Integer.MAX_VALUE);
        tbBtobEnderecosCadastrados.setMaxRowCount(Integer.MAX_VALUE);
        tbEmpresasClienteEnxerga.setMaxRowCount(Integer.MAX_VALUE);
        tbEmpresasClientesEnxerga.setMaxRowCount(Integer.MAX_VALUE);
        tbClienteDiverso.setMaxRowCount(Integer.MAX_VALUE);
        if (codEvento > 1) {
            tbCrmFichaEventoBalcao.addFilter("COD_EVENTO");
            tbCrmFichaEventoBalcao.addParam("COD_EVENTO", codEvento);
            tbCrmFichaEventoBalcao.open();
            rn.codEvento = codEvento;
            rn.codClienteEvento = tbCrmFichaEventoBalcao.getCOD_CLIENTE().asLong();
            rn.email = tbCrmFichaEventoBalcao.getEMAIL().asString();
            rn.codEmpresa = tbCrmFichaEventoBalcao.getCOD_EMPRESA().asInteger();
        } else {
            rn.codClienteEvento = codCliente;
            tbClienteDiverso.addFilter("COD_CLIENTE");
            tbClienteDiverso.addParam("COD_CLIENTE", codCliente);
            tbClienteDiverso.open();
            rn.email = tbClienteDiverso.getENDERECO_ELETRONICO().asString();
            rn.codEmpresa = EmpresaUtil.getCodEmpresaUserLogged();
        }
        if (rn.email != null && rn.email.trim().equals("")) {
            EmpresaUtil.showMessage("CRM Parts", "Cliente sem e-mail cadastrado.");
            close();
        }
        FrmBtoBEnviarLink.setCaption("Cadastro do BtoB para o E-mail: " + rn.email);
        execute();
        setLabelVendedor();
        filtrarClientesEmail(rn.email);
        setFontTab(pagDadosCli, 16);
    }

    void execute() throws DataException {
        rn.filtrarCliente(rn.codClienteEvento);
        filtrarEnderecosCadastrados(rn.codClienteEvento);
        filtrarEmpresasClienteSelecionado(rn.codClienteEvento);
        filtrarEnderecoEntrega(rn.codClienteEvento);
        filtrarEnderecoFatura(rn.codClienteEvento);
        if (!tbBtobEnderecosCadastrados.isEmpty()
                && tbBtobEnderecosCadastrados.getENTREGA_TIPO().asInteger() > 0) {
            if (tbBtobEnderecosCadastrados.getENTREGA_TIPO().asInteger() == 1) {
                rb1.setChecked(true);
            } else {
                rb2.setChecked(true);
            }
        }
        filtrarEmpresas(rn.codClienteEvento);
    }

    private void setLabelVendedor() throws DataException {
        if (tbClienteDiverso.getVENDEDOR_RESPONSAVEL().asString().equals("")) { // Não achou o vendedor
            rn.getFilaDaVez(rn.codEmpresa > 0 ? rn.codEmpresa : EmpresaUtil.getCodEmpresaUserLogged()); //pega o da vez
        } else {
            rn.getFilaDaVez(tbClienteDiverso.getVENDEDOR_RESPONSAVEL().asString()); // PEga o nome completo
        }
        lbVendedor.setCaption(tbEmpresasUsuarios.getNOME_COMPLETO().asString());

    }

    private void filtrarEnderecosCadastrados(long codCliente) {
        try {
            if (codCliente == 0) {
                return;
            }
            try {
                rn.filtrarEnderecosCadastrados(codCliente);
            } finally {
                //enableEvento = true;
            }

        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao Filtrar Endereço")
                    .message("NBS-ERROR-P0101: " + ex.getMessage())
                    .showError();
        }
    }

    private void filtrarEmpresasClienteSelecionado(long codCliente) {
        try {
            if (codCliente == 0) {
                return;
            }
            try {
                rn.filtrarEmpresasClienteEnxergaSelecionado(codCliente);
            } finally {
                //enableEvento = true;
            }

        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao Filtrar Empresas")
                    .message("NBS-ERROR-P0101: " + ex.getMessage())
                    .showError();
        }
    }

    private void filtrarClientesEmail(String email) {
        try {
            if (email == null || email.trim().equals("")) {
                return;
            }
            rn.getClientesEmail(email);
            if (tbBtobClientesPequisaEmail.isEmpty()) {
                return;
            }
            tbBtobClientesPequisaEmail.first();
            while (!tbBtobClientesPequisaEmail.eof()) {
                setCheckedCliente("N");
                tbBtobClientesPequisaEmail.next();
            }
            if (tbBtobClientesPequisaEmail.locate("COD_CLIENTE",
                    rn.codClienteEvento)) {
                setCheckedCliente("S");
            }
            GridClientesEmail.setEnabled(tbBtobClientesPequisaEmail.count() > 0);
        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao Listar Clientes com o mesmo e-mail")
                    .message("NBS-ERROR-P0101: " + ex.getMessage())
                    .showError();
        }
    }

    private void filtrarEmpresas(long codCliente) {
        try {
            if (codCliente == 0) {
                return;
            }
            try {

                rn.filtrarEmpresasCliente(codCliente);
                if (tbEmpresasClienteEnxerga.isEmpty()) {
                    return;
                }

                tbEmpresasClienteEnxerga.first();

                while (!tbEmpresasClienteEnxerga.eof()) {
                    setSelectPricipal("N");
                    tbEmpresasClienteEnxerga.next();
                }
                tbEmpresasClienteEnxerga.first();

                // if Já tem cadastro
                if (!tbEmpresasClientesEnxerga.isEmpty()
                        && tbEmpresasClientesEnxerga.locate("PRINCIPAL", "S")) {
                    if (tbEmpresasClienteEnxerga.locate("COD_EMPRESA",
                            tbEmpresasClientesEnxerga.getCOD_EMPRESA().asInteger())) {
                        setSelectPricipal("S");
                    }
                }
                tbEmpresasClienteEnxerga.sort("COD_EMPRESA", "ASC");
            } finally {
                //enableEvento = true;
            }

        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao Filtrar Empresas")
                    .message("NBS-ERROR-P0101: " + ex.getMessage())
                    .showError();
        }
    }

    private void filtrarEnderecoEntrega(long codCliente) {
        try {
            if (codCliente == 0) {
                return;
            }
            try {

                rn.filtrarEnderecoEntrega(codCliente);
                if (tbBtobEnderecoClienteEntrega.isEmpty()) {
                    return;
                }

                tbBtobEnderecoClienteEntrega.first();

                while (!tbBtobEnderecoClienteEntrega.eof()) {
                    setCheckedEntrega("N");
                    tbBtobEnderecoClienteEntrega.next();
                }
                tbBtobEnderecoClienteEntrega.first();

                // if Já tem cadastro
                if (!tbBtobEnderecosCadastrados.isEmpty() && tbBtobEnderecosCadastrados.getTIPO_ENDERECO_ENTREGA().asInteger() > 0) {
                    if (tbBtobEnderecoClienteEntrega.locate("COD_TIPO_ENDERECO,INSCRICAO_ESTADUAL",
                            tbBtobEnderecosCadastrados.getTIPO_ENDERECO_ENTREGA().asInteger(),
                            tbBtobEnderecosCadastrados.getINSCRICAO_ENTREGA().asString().equals("") ? null
                                    : tbBtobEnderecosCadastrados.getINSCRICAO_ENTREGA().asString())) {
                        setCheckedEntrega("S");
                    } else { //Se não achar, pode ter apagado o endereco cadastrado
                        if (tbBtobEnderecoClienteEntrega.locate("TIPO_ENDERECO", "Residencial")) {
                            setCheckedEntrega("S");
                        } else if (tbBtobEnderecoClienteEntrega.locate("TIPO_ENDERECO", "Comercial")) {
                            setCheckedEntrega("S");
                        } else if (tbBtobEnderecoClienteEntrega.locate("TIPO_ENDERECO", "Cobrança")) {
                            setCheckedEntrega("S");
                        } else {
                            tbBtobEnderecoClienteEntrega.first();
                            setCheckedEntrega("S");
                        }
                    }
                } else { // Não tem cadastro
                    if (tbBtobEnderecoClienteEntrega.locate("TIPO_ENDERECO", "Residencial")) {
                        setCheckedEntrega("S");
                    } else if (tbBtobEnderecoClienteEntrega.locate("TIPO_ENDERECO", "Comercial")) {
                        setCheckedEntrega("S");
                    } else if (tbBtobEnderecoClienteEntrega.locate("TIPO_ENDERECO", "Cobrança")) {
                        setCheckedEntrega("S");
                    } else {
                        tbBtobEnderecoClienteEntrega.first();
                        setCheckedEntrega("S");
                    }
                }
            } finally {
                //enableEvento = true;
            }

        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao Filtrar Endereço")
                    .message("NBS-ERROR-P0101: " + ex.getMessage())
                    .showError();
        }
    }

    private void filtrarEnderecoFatura(long codCliente) {
        try {
            if (codCliente == 0) {
                return;
            }
            try {
                //enableEvento = false;

                rn.filtrarEnderecoFatura(codCliente);
                if (tbBtobEnderecoClienteFatura.isEmpty()) {
                    return;
                }

                tbBtobEnderecoClienteFatura.first();

                while (!tbBtobEnderecoClienteFatura.eof()) {
                    setCheckedFatura("N");
                    tbBtobEnderecoClienteFatura.next();
                }
                tbBtobEnderecoClienteFatura.first();
                if (!tbBtobEnderecosCadastrados.isEmpty() && tbBtobEnderecosCadastrados.getTIPO_ENDERECO_FATURAMENTO().asInteger() > 0) {
                    if (tbBtobEnderecoClienteFatura.locate("COD_TIPO_ENDERECO,INSCRICAO_ESTADUAL",
                            tbBtobEnderecosCadastrados.getTIPO_ENDERECO_FATURAMENTO().asInteger(),
                            tbBtobEnderecosCadastrados.getINSCRICAO_FATURAMENTO().asString().equals("") ? null
                                    : tbBtobEnderecosCadastrados.getINSCRICAO_FATURAMENTO().asString())) {
                        setCheckedFatura("S");
                    } else {
                        if (tbBtobEnderecoClienteFatura.locate("TIPO_ENDERECO", "Cobrança")) {
                            setCheckedFatura("S");
                        } else if (tbBtobEnderecoClienteFatura.locate("TIPO_ENDERECO", "Comercial")) {
                            setCheckedFatura("S");
                        } else if (tbBtobEnderecoClienteFatura.locate("TIPO_ENDERECO", "Residencial")) {
                            setCheckedFatura("S");
                        } else {
                            tbBtobEnderecoClienteFatura.first();
                            setCheckedFatura("S");
                        }
                    }
                } else {
                    if (tbBtobEnderecoClienteFatura.locate("TIPO_ENDERECO", "Cobrança")) {
                        setCheckedFatura("S");
                    } else if (tbBtobEnderecoClienteFatura.locate("TIPO_ENDERECO", "Comercial")) {
                        setCheckedFatura("S");
                    } else if (tbBtobEnderecoClienteFatura.locate("TIPO_ENDERECO", "Residencial")) {
                        setCheckedFatura("S");
                    } else {
                        tbBtobEnderecoClienteFatura.first();
                        setCheckedFatura("S");
                    }
                }

            } finally {
                //enableEvento = true;
            }

        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao Filtrar Endereço")
                    .message("NBS-ERROR-P0101: " + ex.getMessage())
                    .showError();
        }
    }

    private void setCheckedEntrega(String check) throws DataException {
        tbBtobEnderecoClienteEntrega.edit();
        tbBtobEnderecoClienteEntrega.setField("CHECKED", check);
        tbBtobEnderecoClienteEntrega.post();
    }

    private void setCheckedFatura(String check) throws DataException {
        tbBtobEnderecoClienteFatura.edit();
        tbBtobEnderecoClienteFatura.setField("CHECKED", check);
        tbBtobEnderecoClienteFatura.post();
    }

    private void setCheckedCliente(String check) throws DataException {
        tbBtobClientesPequisaEmail.edit();
        tbBtobClientesPequisaEmail.setField("CHECKED", check);
        tbBtobClientesPequisaEmail.post();
    }

    private void setSelectPricipal(String check) throws DataException {
        tbEmpresasClienteEnxerga.edit();
        tbEmpresasClienteEnxerga.setField("PRINCIPAL", check);
        tbEmpresasClienteEnxerga.post();
    }

    @Override
    public void btnVoltarClick(final Event event) {
        close();
    }

    @Override
    public void btnAceitarClick(final Event event) {
        Dialog.create()
                .title("CrmParts")
                .message("Confirma Salvar e Enviar link para acesso externo ao cliente selecionado?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            String erro = validarCampos();
                            if (!erro.isEmpty()) {
                                EmpresaUtil.showMessage("Crm Parts", erro);
                                return;
                            }
                            Value retorno = rn.salvar(rb1.isChecked() ? 1 : 2);
                            if (!retorno.asString().equals("S")) {
                                EmpresaUtil.showMessage("CRM Parts", retorno.asString());
                            }

                            rn.enviarEmail();
                            EmpresaUtil.showMessage("CrmParts", "Email enviado com Sucesso!");
                            close();

                        } catch (Exception ex) {
                            EmpresaUtil.showError("Falha ao enviar email", ex);
                        }
                    }
                });

    }

    public String validarCampos() throws Exception {
        String retorno = "";
        if (!rb1.isChecked() && !rb2.isChecked()) {
            retorno += "Tipo de entrega não selecionada\n";
        }
        if (!tbBtobEnderecoClienteFatura.locate("CHECKED", "S")) {
            retorno += "Endereço de Faturamento não selecionado\n";
        }
        if (!tbBtobEnderecoClienteEntrega.locate("CHECKED", "S")) {
            retorno += "Endereço de Entrega não selecionado\n";
        }
        if (!tbEmpresasClienteEnxerga.locate("PRINCIPAL", "S")) {
            retorno += "Empresa principal não selecionada";
        }

        return retorno;
    }

    @Override
    public void gridFatimgFatura(Event<Object> event) {
        Integer imageIndex = new Value(((Object[]) event.getValue())[0]).asInteger();
        Integer book = new Value(((Object[]) event.getValue())[1]).asInteger();
        try {
            tbBtobEnderecoClienteFatura.first();
            while (!tbBtobEnderecoClienteFatura.eof()) {
                setCheckedFatura("N");
                tbBtobEnderecoClienteFatura.next();
            }
            tbBtobEnderecoClienteFatura.gotoBookmark(book);
            setCheckedFatura("S");
        } catch (DataException ex) {
            Logger.getLogger(FrmBtoBEnviarLinkA.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    public void gridEntimgEntrega(Event<Object> event) {
        Integer imageIndex = new Value(((Object[]) event.getValue())[0]).asInteger();
        Integer book = new Value(((Object[]) event.getValue())[1]).asInteger();
        try {
            tbBtobEnderecoClienteEntrega.first();
            while (!tbBtobEnderecoClienteEntrega.eof()) {
                setCheckedEntrega("N");
                tbBtobEnderecoClienteEntrega.next();
            }
            tbBtobEnderecoClienteEntrega.gotoBookmark(book);
            setCheckedEntrega("S");
        } catch (DataException ex) {
            Logger.getLogger(FrmBtoBEnviarLinkA.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    public void gridEmpmarcar(Event<Object> event) {
        try {
            if (tbEmpresasClienteEnxerga.getPRINCIPAL().asString().equals("S")) {
                EmpresaUtil.showMessage("CRM Parts", "Principal não pode ser desmarcada");
                return;
            }
            Integer imageIndex = new Value(((Object[]) event.getValue())[0]).asInteger();
            Integer book = new Value(((Object[]) event.getValue())[1]).asInteger();
            enableEvento = false;
            tbEmpresasClienteEnxerga.edit();
            tbEmpresasClienteEnxerga.setCHECKED(imageIndex == 0 ? "N" : "S");
            tbEmpresasClienteEnxerga.post();
            enableEvento = false;
        } catch (DataException ex) {
            Logger.getLogger(FrmBtoBEnviarLinkA.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    public void gridEmpimgClick(Event<Object> event) {
        Integer imageIndex = new Value(((Object[]) event.getValue())[0]).asInteger();
        Integer book = new Value(((Object[]) event.getValue())[1]).asInteger();
        try {
            if (tbEmpresasClienteEnxerga.getCHECKED().asString().equals("N")) {
                tbEmpresasClienteEnxerga.edit();
                tbEmpresasClienteEnxerga.setCHECKED("S");
                tbEmpresasClienteEnxerga.post();
            }
            tbEmpresasClienteEnxerga.first();
            while (!tbEmpresasClienteEnxerga.eof()) {
                setSelectPricipal("N");
                tbEmpresasClienteEnxerga.next();
            }
            tbEmpresasClienteEnxerga.gotoBookmark(book);
            setSelectPricipal("S");
        } catch (DataException ex) {
            Logger.getLogger(FrmBtoBEnviarLinkA.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void abreCliente(Event<Object> event) {
        if (!GridClientesEmail.isEnabled()) {
            return;
        }
        Double codCliente = tbBtobClientesPequisaEmail.getCOD_CLIENTE().asDecimal();
        FrmCadastroRapidoClienteA form = new FrmCadastroRapidoClienteA();
        if (form.buscarCliente(codCliente)) {
            FormUtil.doModal(form, (EventListener) (Event t) -> {
            });
        }
    }

    @Override
    public void GridClientesEmailGridClientesEmailCheckDoubleClick(Event<Object> event) {
        abreCliente(event);
    }

    @Override
    public void GridClientesEmailGridClientesEmaiClienteDoubleClick(Event<Object> event) {
        abreCliente(event);
    }

    @Override
    public void GridClientesEmailGridClientesEmailCnpjDoubleClick(Event<Object> event) {
        abreCliente(event);
    }

    @Override
    public void GridClientesEmailGridClientesEmailCheckClick(Event<Object> event) {
        Integer imageIndex = new Value(((Object[]) event.getValue())[0]).asInteger();
        Integer book = new Value(((Object[]) event.getValue())[2]).asInteger();
        try {
            tbBtobClientesPequisaEmail.first();
            while (!tbBtobClientesPequisaEmail.eof()) {
                setCheckedCliente("N");
                tbBtobClientesPequisaEmail.next();
            }
            tbBtobClientesPequisaEmail.gotoBookmark(book);
            setCheckedCliente("S");
            rn.codClienteEvento = tbBtobClientesPequisaEmail.getCOD_CLIENTE().asLong();
            rn.email = tbBtobClientesPequisaEmail.getENDERECO_ELETRONICO().asString();
            execute();

        } catch (DataException ex) {
            Logger.getLogger(FrmBtoBEnviarLinkA.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    public void FVBox3Click(Event<Object> event) {

    }
}
