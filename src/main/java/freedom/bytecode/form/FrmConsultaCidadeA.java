package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.XmlUtil;
import freedom.util.pkg.PkgCrmPartsRNA;

public class FrmConsultaCidadeA extends FrmConsultaCidadeW {

    private static final long serialVersionUID = 20130827081850L;
    private final PkgCrmPartsRNA pkCrmPartsRna = new PkgCrmPartsRNA();
    private final int codEmpresa = EmpresaUtil.getCodEmpresaUserLogged();
    private boolean ok = false;
    private String strUF;

    public boolean isOk() {
        return ok;
    }

    public void setCidadeEnd(String uf, String cidade) {
        try {
            if (uf.length() > 0) {
                strUF = uf;
            } else {
                Double codEmp = new Double(codEmpresa);
                strUF = pkCrmPartsRna.getParametro(codEmp, "PARM_SYS", "UF");
            }
            edtPesquisa.setValue(XmlUtil.removeAcentos(cidade.trim().toUpperCase()));
            pesquisar();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao iniciar Consulta Cidade", e);
        }
    }

    private void pesquisar() {
        if (edtPesquisa.getValue().asString().trim().length() < 3) {
            EmpresaUtil.showWarning("Atenção", "Informe ao menos 3 caracteres para a pesquisa.");
            return;
        }
        try {
            tbCidades.close();
            tbCidades.clearFilters();
            tbCidades.clearParams();
            tbCidades.addFilter("UF");
            tbCidades.addFilter("DESCRICAO");
            tbCidades.addParam("UF", strUF.trim());
            tbCidades.addParam("DESCRICAO", XmlUtil.removeAcentos(edtPesquisa.getValue().asString().trim().toUpperCase()));
            tbCidades.open();
            if (tbCidades.isEmpty()) {
                tbCidades.close();
                tbCidades.clearFilters();
                tbCidades.clearParams();
                tbCidades.addFilter("UF");
                tbCidades.addFilter("CEP_LIKE");
                tbCidades.addParam("UF", strUF.trim());
                tbCidades.addParam("CEP_LIKE", XmlUtil.removeAcentos(edtPesquisa.getValue().asString().trim().toUpperCase()));
                tbCidades.open();
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao pesquisar", e);
        }
    }

    @Override
    public void btnPesquisarClick(final Event event) {
        pesquisar();
    }

    @Override
    public void btnAceitarClick(final Event event) {
        if (tbCidades.isEmpty()) {
            EmpresaUtil.showWarning("Atenção", "Nenhuma Cidade Selecionada!");
            return;
        }
        ok = true;
        close();
    }

    @Override
    public void btnVoltarClick(final Event event) {
        close();
    }

    @Override
    public void edtPesquisaEnter(Event<Object> event) {
        pesquisar();
    }
}
