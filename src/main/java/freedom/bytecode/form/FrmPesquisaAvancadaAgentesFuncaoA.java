package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmPesquisaAvancadaAgentesFuncaoW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;
import freedom.util.StringUtil;

enum Ativo {

    TODOS("T", "Todos"),
    SIM("S", "Sim"),
    NAO("N", "Não");

    private final String codigo;

    private final String descricao;

    Ativo(String codigo,
                  String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public String getCodigo() {
        return this.codigo;
    }

    public String getDescricao() {
        return this.descricao;
    }

}

enum CadBD {

    TODOS("T", "Todos"),
    SIM("S", "Sim"),
    NAO("N", "Não");

    private final String codigo;

    private final String descricao;

    CadBD(String codigo,
                  String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public String getCodigo() {
        return this.codigo;
    }

    public String getDescricao() {
        return this.descricao;
    }

}

public class FrmPesquisaAvancadaAgentesFuncaoA extends FrmPesquisaAvancadaAgentesFuncaoW {

    private static final long serialVersionUID = 20130827081850L;

    private final long codEmpresa;

    private final int codDepartamento;

    private final int codDivisao;

    private boolean fechadoAoPesquisar = false;

    public boolean isFechadoAoPesquisar() {
        return this.fechadoAoPesquisar;
    }

    public FrmPesquisaAvancadaAgentesFuncaoA(String login,
                                             String nomeCompleto,
                                             long codEmpresa,
                                             int codDepartamento,
                                             int codDivisao,
                                             String cpf,
                                             String ativo,
                                             String cadBD) {
        if (!login.equals("")) {
            this.edtLogin.setValue(login);
        }
        if (!nomeCompleto.equals("")) {
            this.edtNome.setValue(nomeCompleto);
        }
        this.codEmpresa = codEmpresa;
        this.codDepartamento = codDepartamento;
        this.codDivisao = codDivisao;
        if (!cpf.equals("")) {
            this.edtCPF.setValue(cpf);
        }
        if (!ativo.equals("")) {
            this.cboAtivo.setValue(ativo);
        } else {
            this.cboAtivo.setValue(Ativo.TODOS.getCodigo());
        }
        if (!cadBD.equals("")) {
            this.cboCadBD.setValue(cadBD);
        } else {
            this.cboCadBD.setValue(CadBD.TODOS.getCodigo());
        }
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.carregarCboEmpresa();
        if (this.codEmpresa > 0L) {
            this.cboEmpresa.setValue(this.codEmpresa);
            this.carregarCboDepartamento(this.codEmpresa);
            this.cboDepartamento.setEnabled(true);
            if (this.codDepartamento > 0) {
                this.cboDepartamento.setValue(this.codDepartamento);
                this.carregarCboDivisao(this.codEmpresa,
                        this.codDepartamento);
                this.cboDivisao.setEnabled(true);
                if (this.codDivisao > 0) {
                    this.cboDivisao.setValue(this.codDivisao);
                }
            }
        }
        this.edtLogin.setFocus();
        this.edtLogin.setSelectionRange(0,
                (this.edtLogin.getValue().asString().length() + 1));
    }

    @Override
    public void edtLoginEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void edtLoginExit(Event<Object> event) {
        String login = this.edtLogin.getValue().asString().trim();
        this.edtLogin.setValue(login);
    }

    @Override
    public void edtNomeEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void edtNomeExit(Event<Object> event) {
        String nome = this.edtNome.getValue().asString().trim();
        this.edtNome.setValue(nome);
    }

    @Override
    public void btnLimparClick(Event<Object> event) {
        Dialog.create()
                .title("Confirmação")
                .message("Deseja realmente limpar a pesquisa avançada?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        this.edtLogin.clear();
                        this.edtNome.clear();
                        this.cboEmpresa.clear();
                        this.cboDepartamento.clear();
                        this.cboDivisao.clear();
                        this.edtCPF.clear();
                        this.cboAtivo.setValue(Ativo.TODOS.getCodigo());
                        this.cboCadBD.setValue(CadBD.TODOS.getCodigo());
                        this.edtLogin.setFocus();
                    }
                });
    }

    @Override
    public void btnPesquisarClick(Event<Object> event) {

        this.fechadoAoPesquisar = true;
        this.close();
    }

    private void carregarCboEmpresa() {
        try {
            this.rn.carregarCboEmpresa();
            int tbEmpresasCount = this.tbEmpresas.count();
            if (tbEmpresasCount == 1) {
                this.tbEmpresas.first();
                long codEmpresa2 = this.tbEmpresas.getCOD_EMPRESA().asLong();
                this.cboEmpresa.setValue(codEmpresa2);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar as empresas",
                    dataException);
        }
    }

    @Override
    public void cboEmpresaEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void cboEmpresaClearClick(Event<Object> event) {
        this.cboDivisao.clear();
        this.cboDivisao.setEnabled(false);
        this.cboDepartamento.clear();
        this.cboDepartamento.setEnabled(false);
    }

    @Override
    public void cboDepartamentoEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void cboDepartamentoClearClick(Event<Object> event) {
        this.cboDivisao.clear();
        this.cboDivisao.setEnabled(false);
    }

    @Override
    public void cboDivisaoEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void edtCPFChange(Event<Object> event) {
        String cpf = this.edtCPF.getValue().asString();
        cpf = StringUtil.removerCaracteresNaoNumericos(cpf);
        this.edtCPF.setValue(cpf);
    }

    @Override
    public void edtCPFEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void cboAtivoEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void cboCadBDEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    private void carregarCboDepartamento(long codEmpresa) {
        try {
            this.rn.carregarCboDepartamento(codEmpresa);
            int tbDepartamentosCount = this.tbDepartamentos.count();
            if (tbDepartamentosCount == 1) {
                this.tbDepartamentos.first();
                long codDepartamento2 = this.tbDepartamentos.getDPTO_CODIGO().asInteger();
                this.cboDepartamento.setValue(codDepartamento2);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar os departamentos",
                    dataException);
        }
    }

    @Override
    public void cboEmpresaChange(Event<Object> event) {
        long codEmpresa2 = this.cboEmpresa.getValue().asLong();
        this.carregarCboDepartamento(codEmpresa2);
        this.cboDepartamento.setEnabled(true);
        this.cboDepartamento.setFocus();
        this.cboDepartamento.setOpen(true);
        this.cboDivisao.clear();
    }

    private void carregarCboDivisao(long codEmpresa,
                                    int codDepartamento) {
        try {
            this.rn.carregarCboDivisao(codEmpresa,
                    codDepartamento);
            int tbDivisoesCount = this.tbDivisoes.count();
            if (tbDivisoesCount == 1) {
                this.tbDivisoes.first();
                long codDivisao2 = this.tbDivisoes.getCOD_EMPRESA_DIVISAO().asInteger();
                this.cboDivisao.setValue(codDivisao2);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar as divisões",
                    dataException);
        }
    }

    @Override
    public void cboDepartamentoChange(Event<Object> event) {
        long codEmpresa2 = this.cboEmpresa.getValue().asLong();
        int codDepartamento2 = this.cboDepartamento.getValue().asInteger();
        this.carregarCboDivisao(codEmpresa2,
                codDepartamento2);
        this.cboDivisao.setEnabled(true);
        this.cboDivisao.setFocus();
        this.cboDivisao.setOpen(true);
    }

    @Override
    public void cboDivisaoChange(Event<Object> event) {
        this.edtCPF.setFocus();
    }

}