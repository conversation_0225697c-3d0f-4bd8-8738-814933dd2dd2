package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmLimiteFinanceiroW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import org.apache.commons.lang.StringUtils;

public class FrmLimiteFinanceiroA extends FrmLimiteFinanceiroW {

    private static final long serialVersionUID = 20130827081850L;

    private final int codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();

    private double codCliente = 0;

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        close();
    }

    public void openLimiteCliente(double codCliente) {
        this.codCliente = codCliente;
        try {
            rn.openLimiteCliente(codCliente);
        } catch (DataException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        try {
            hBoxMotivoBloqueio.setVisible(rn.podeExibirMotivoBloqueio(codEmpresaUsuarioLogado));
            exibeStatusCadastro();
        } catch (DataException e) {
            e.printStackTrace();
        }
    }

    private void exibeStatusCadastro() {
        lblCadastroIrregular.setVisible(false);
        boolean usaApiConsulta = usaPluginConsultaNBS();
        if (!usaApiConsulta) {
            return;
        }
        Value aRfbSituacao = new Value(null);
        Value aRfbSituacaoMensagem = new Value(null);
        Value aSintegraSituacao = new Value(null);
        Value aSintegraMensagem = new Value(null);
        Value aSintegraCadastroIsento = new Value(null);
        Value aSintegraMultiplasIe = new Value(null);
        String ie = "";

        try {
            String respfunc = rn.infoSituacaoCadastralIntegracao(this.codEmpresaUsuarioLogado, this.codCliente, tbLimiteCliente.getCOD_CLASSE().asString(), ie, aRfbSituacao, aRfbSituacaoMensagem, aSintegraSituacao, aSintegraMensagem, aSintegraCadastroIsento, aSintegraMultiplasIe);
            if (!respfunc.equals("S")){
                lblCadastroIrregular.setVisible(true);
                StringBuilder msg = new StringBuilder();
                msg.append("Situação Cadastral: ").append("IRREGULAR").append("\n");
                msg.append("Receita Federal (");
                if (StringUtils.isNotBlank(aRfbSituacaoMensagem.asString().trim())){
                    msg.append(aRfbSituacaoMensagem.asString().trim());
                } else{
                    msg.append("* Não Consultou *");
                }
                msg.append(")").append("\n");

                msg.append("Sintegra (");
                if (StringUtils.isNotBlank(aSintegraMultiplasIe.asString())){
                    msg.append(aSintegraMultiplasIe.asString().trim());
                }else{
                    msg.append("* Não Consultou *");
                }
                msg.append(")").append(" (-^) ");
                lblCadastroIrregular.setHint(msg.toString());
            }
        } catch (DataException e) {
            e.printStackTrace();
        }


    }
    private boolean usaPluginConsultaNBS() {
        return rn.usaPluginConsultaNBS((double) this.codEmpresaUsuarioLogado);
    }

}
