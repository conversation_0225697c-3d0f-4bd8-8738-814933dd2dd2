package freedom.bytecode.form;

import encrypt.criptografia.AesUtil;
import freedom.bytecode.form.wizard.FrmLoginW;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.ExceptionEngine;
import freedom.client.util.FormUtil;
import freedom.commons.lang.IWorkList;
import freedom.connection.SessionUtil;
import freedom.data.Value;
import freedom.util.*;
import org.zkoss.zk.ui.Executions;

import java.time.Instant;
import java.util.Map;
import java.util.Random;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static freedom.util.CastUtil.asInteger;

public class FrmLoginA extends FrmLoginW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String DATASOURCE_SELECIONADO = "DATASOURCE_SELECIONADO";

    private static final String USUARIO_SELECIONADO = "USUARIO_SELECIONADO";

    private static final String SENHA_USUARIO = "SENHA_USUARIO";

    private static final String LEMBRAR_SENHA = "LEMBRAR_SENHA";

    private static final String PWD_SELECIONADO = "PWD_SELECIONADO";

    private static final String PASSPHRASE = "NBS%OFFICE%456";

    private static final String ERRO_AO_ACESSAR_SISTEMA = "Erro ao acessar sistema";

    private final IWorkList wl = WorkListFactory.getInstance();

    private final AesUtil cript = new AesUtil();

    private final EmpresaUtil util = new EmpresaUtil();

    public FrmLoginA() {
        final StringBuilder sb = new StringBuilder();
        Map<String, String> themes = ApplicationUtil.getThemeList();
        themes.keySet().forEach(theme -> {
            if (sb.length() > 0) {
                sb.append(";");
            }
            sb.append(themes.get(theme)).append("=").append(theme);
        });
        this.cboTema.setListOptions(sb.toString());
        this.cboTema.setValue(ApplicationUtil.getLastTheme());
        String dataSource;
        try {
            Map<String, String> items = SessionUtil.listarDatasources();
            sb.delete(0,
                    sb.length());
            items.keySet().forEach(key -> {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append(items.get(key))
                        .append("=")
                        .append(key);
            });
            this.cboSchema.setListOptions(sb.toString());
            this.cboSchema.setValue(ApplicationUtil.getValue(DATASOURCE_SELECIONADO));
            this.edtStringUsuario.setValue(ApplicationUtil.getValue(USUARIO_SELECIONADO));
            this.chkLembrarSenha.setValue(ApplicationUtil.getValue(LEMBRAR_SENHA));
            if (Boolean.TRUE.equals(this.chkLembrarSenha.isChecked())) {
                String pass = this.cript.decrypt(PASSPHRASE, ApplicationUtil.getValue(SENHA_USUARIO));
                this.edtStringSenha.setValue(pass);
            }
            dataSource = ApplicationUtil.getValue(DATASOURCE_SELECIONADO);
            if (!dataSource.isEmpty()) {
                this.wl.put(IWorkList.DATASOURCE_DEFAULT,
                        dataSource);
            }
            Random r = new Random(System.currentTimeMillis());
            int version = r.nextInt();
            if (version < 0) {
                version = version * -1;
            }
            this.setBackgroundImage("images/" + wl.sysget("APPLICATION_NAME").asString() + "Login.JPG?version=" + version);
            this.logoNBS.setImageSrc("/images/" + wl.sysget("APPLICATION_NAME").asString() + "7000195.png?version=1");
            String versao = ApplicationUtil.getApplicationVersion();
            versao = versao == null ? "*******" : versao;
            this.lblVersaoSistema.setCaption("Versão: " + versao);
        } catch (Exception exception) {
            ExceptionEngine.register(exception);
        }
        if (Executions.getCurrent() != null) {
            String urlHash = Executions.getCurrent().getParameter("hash");
            if (urlHash != null && !urlHash.isEmpty()) {
                try {
                    urlHash = urlHash.replace(" ", "+");
                    loginHash(urlHash);
                } catch (Exception e) {
                    showErrorLoginHash("");
                }
            }
        }
    }

    public void loginHash(
            String urlHash
    ) {
        String hashDecrypt = this.cript.decrypt(
                this.cript.getpassphraseDefault()
                ,urlHash
        );
        /*
        String datasource = this.getPwdFromHash(
                hashDecrypt
                ,0
        );
        */
        String schema = this.getPwdFromHash(
                hashDecrypt
                ,1
        );
        String usuario = this.getPwdFromHash(
                hashDecrypt
                ,2
        );
        String expiracao = this.getPwdFromHash(
                hashDecrypt
                ,3
        );
        String senha = this.getPwdFromHash(
                hashDecrypt
                ,4
        );
        if (this.checkHashValidity(expiracao)) {
            this.executeLogin(
                    schema
                    ,usuario
                    ,senha
                    ,true
            );
        } else {
            this.showErrorLoginHash(
                    "Validade do token expirada."
                            + System.lineSeparator()
                            + "Tente logar com seu usuário e senha."
            );
        }
    }

    public boolean checkHashValidity(
            String hashText
    ) {
        long tempo = Long.parseLong(
                hashText
        );
        Instant expiracaoInstant = Instant.ofEpochMilli(
                tempo
        );
        Instant momentoAtual = Instant.now();
        return expiracaoInstant.isAfter(
                momentoAtual
        );
    }

    /**
     * Recebe o hash decriptografado
     * Realiza o split para tranformar em array e devolve o texto na posição solicitada do array
     * datasource|usuario|expiracao|senha
     * caso a posição seja a da senha esse verificará se a senha continha o caractere '|'
     * caso o tamanho do array seja maior que 4, ira concatenar o '|' onde o split removeu
     */
    public String getPwdFromHash(
            String hashText
            ,int position
    ) {
        String[] arrayString = hashText.split(
                "\\|"
        );
        if (position == 4) {
            int tamanhoHash = arrayString.length;
            StringBuilder concatSenha = new StringBuilder();
            for (int i = 4; i < tamanhoHash; i++) {
                concatSenha.append(arrayString[i]);
                if (i < tamanhoHash - 1) {
                    concatSenha.append("|");
                }
            }
            return concatSenha.toString();
        } else if (position < 4) {
            return arrayString[position];
        }
        return "";
    }

    public void showErrorLoginHash(
            String msgPersonalizada
    ) {
        String msg = (
                "Não foi possível conectar automaticamente."
                        + System.lineSeparator()
                        + "Tente logar com seu usuário e senha."
        );
        Dialog.create()
                .title(FrmLoginA.ERRO_AO_ACESSAR_SISTEMA)
                .message(((msgPersonalizada != null) && (!msgPersonalizada.isEmpty())) ? msgPersonalizada : msg)
                .showError();
    }

    public void executeLogin(
            String dataSource
            ,String usuario
            ,String senha
            ,boolean loginByHash
    ) {
        try {
            this.wl.put(
                    IWorkList.DATASOURCE_DEFAULT
                    ,dataSource
            );
            String validou = this.rn.validarLogin(
                    usuario
                    ,senha
                    ,dataSource
                    ,true
            );
            if (validou.contains("NBS-0485")) {
//                    FrmAlterarSenhaA altSenha = new FrmAlterarSenhaA(txbUsuario.getValue().asString(),
//                            txbSenha.getValue().asString());
//                    FormUtil.doModal(altSenha, (EventListener) (Event t) -> {
//                    });
            } else if (!validou.isEmpty()) {
                Dialog.create()
                        .title(FrmLoginA.ERRO_AO_ACESSAR_SISTEMA)
                        .message(validou)
                        .showError();
            } else {
                this.validarVersaoScript(t -> {
                    this.wl.put(
                            IWorkList.USUARIO_LOGADO
                            ,usuario
                    ); //Seto no worklist o usuário logado
                    ApplicationUtil.setValue(
                            FrmLoginA.USUARIO_SELECIONADO,
                            usuario
                    ); //Seto no cookie o usuário selecionado
                    String pass = this.cript.encrypt(
                            FrmLoginA.PASSPHRASE
                            ,senha
                    );
                    ApplicationUtil.setValue(
                            FrmLoginA.PWD_SELECIONADO
                            ,pass
                    ); //Seto no cookie o senha selecionado cript
                    this.wl.put(
                            "PWD_USUARIO_LOGADO"
                            ,pass
                    ); //Seto no worklist a senha logado
                    String formName = ApplicationUtil.getValue(
                            "LAST_FORM_REQUEST"
                    );
                    if (formName.trim().isEmpty()
                            || (!this.isAlphanumeric(formName))) {
                        formName = "FrmHome";
                    }
                    this.lembrarSenha();
                    try {
                        //Dialog.showBusy("Carregando");
                        FormUtil.redirect(
                                "app?"
                                        + formName
                        );
                    } finally {
                        //FreedomUtilities.invokeLater(Dialog::clearBusy);
                        ApplicationUtil.setValue(
                                "LAST_FORM_REQUEST"
                                ,null
                        );
                    }
                });
            }
        } catch (
                Exception exception
        ) {
            if (loginByHash) {
                showErrorLoginHash(
                        ""
                );
            } else {
                Dialog.create()
                        .title(FrmLoginA.ERRO_AO_ACESSAR_SISTEMA)
                        .message(exception.getMessage())
                        .showException(exception);
            }
        }
    }

    @Override
    public void chkLembrarSenhaCheck(final Event<Object> event) {
        this.lembrarSenha();
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        if (this.edtStringUsuario.getValue().isEmpty()) {
            this.edtStringUsuario.setFocus();
        } else if (this.edtStringSenha.getValue().isEmpty()) {
            this.edtStringSenha.setFocus();
        }
        String msgErro = this.wl.remove("ACCESS_ERROR").asString();
        if (!msgErro.isEmpty()) {
            Dialog.create()
                    .title("Validação de acesso")
                    .message(msgErro)
                    .showError();
        }
        TestUtil.setIdTestComponents(
                this
        );
    }

    public void getVersaoScript(
            Value versaoMajor
            ,Value versaoMinor
            ,Value versaoRelease
            ,Value versaoBuild
    ) {
        String versaoScript = ApplicationUtil.getApplicationScriptVersion();
        StringTokenizer sc = new StringTokenizer(
                versaoScript
                ,"."
        );
        if (sc.countTokens() > 3) {
            Integer nextValue;
            int i = 0;
            while (sc.hasMoreTokens()) {
                nextValue = asInteger(
                        sc.nextToken()
                );
                if (i == 0) {
                    versaoMajor.setValue(
                            nextValue
                    );
                } else if (i == 1) {
                    versaoMinor.setValue(
                            nextValue
                    );
                } else if (i == 2) {
                    versaoRelease.setValue(
                            nextValue
                    );
                } else if (i == 3) {
                    versaoBuild.setValue(
                            nextValue
                    );
                }
                i++;
            }
        }
    }

    private void validarVersaoScript(
            EventListener eventListener
    ) throws Exception {
        Value versaoMajor = new Value(
                0
        );
        Value versaoMinor = new Value(
                0
        );
        Value versaoRelease = new Value(
                0
        );
        Value versaoBuild = new Value(
                0
        );
        Value versaoDb = new Value(
                null
        );
        Value versaoApp = new Value(
                null
        );
        this.getVersaoScript(
                versaoMajor
                ,versaoMinor
                ,versaoRelease
                ,versaoBuild
        );
        if (versaoMajor.asInteger() > 0) {
            if (Boolean.FALSE.equals(this.util.validarVersaoMinimaScript(
                    versaoMajor.asInteger()
                    ,versaoMinor.asInteger()
                    ,versaoRelease.asInteger()
                    ,versaoBuild.asInteger()
                    ,versaoDb
                    ,versaoApp
            ))) {
                String mensagem = (
                        "A versão do banco de dados está desatualizada."
                                + System.lineSeparator()
                                + "Versão atual: "
                                + versaoDb.asString()
                                + System.lineSeparator()
                                + "Versão mínima: "
                                + versaoApp.asString()
                                + System.lineSeparator()
                                + "É necessário atualizar o banco de dados usando o aplicativo \"NBSScript.exe\" disponível no site da NBS."
                );
                Dialog.create()
                        .title("Atenção")
                        .message(mensagem)
                        .showInformation((eventListener));
            } else {
                eventListener.onEvent(null);
            }
        } else {
            eventListener.onEvent(null);
        }
    }

    private void logar() {
        String schema = this.cboSchema.getValue().asString().trim();
        if (schema.isEmpty()) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblSchema.getCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.cboSchema           // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.cboSchema.setFocus();
            this.cboSchema.setOpen(
                    true
            );
            return;
        }
        String usuario = this.edtStringUsuario.getValue().asString().trim();
        if (usuario.isEmpty()) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblUsuario.getCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtStringUsuario    // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtStringUsuario.setFocus();
            return;
        }
        String senha = this.edtStringSenha.getValue().asString().trim();
        if (senha.isEmpty()) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblSenha.getCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtStringSenha      // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtStringSenha.setFocus();
            return;
        }
        this.executeLogin(
                schema
                ,usuario
                ,senha
                ,false
        );
    }

    @Override
    public void btnLoginClick(final Event<Object> event) {
        this.logar();
    }

    private boolean isAlphanumeric(
            String value
    ) {
        String regex = "^[a-zA-Z0-9]*$";
        Pattern pattern = Pattern.compile(
                regex
        );
        Matcher matcher = pattern.matcher(
                value
        );
        return matcher.matches();
    }

    @Override
    public void cboSchemaChange(final Event<Object> event) {
        ApplicationUtil.setValue(
                FrmLoginA.DATASOURCE_SELECIONADO
                ,this.cboSchema.getValue().asString()
        );
        String dataSource = this.cboSchema.getValue().asString();
        if (!dataSource.isEmpty()) {
            this.wl.put(
                    IWorkList.DATASOURCE_DEFAULT
                    ,dataSource
            );
        }
    }

    @Override
    public void cboTemaChange(final Event<Object> event) {
        this.applyTheme();
    }

    private void applyTheme() {
        ApplicationUtil.applyTheme(this.cboTema.getValue().asString());
    }

    @Override
    public void edtStringSenhaEnter(final Event<Object> event) {
        this.btnLoginClick(event);
    }

    @Override
    public void edtStringUsuarioEnter(final Event<Object> event) {
        this.btnLoginClick(event);
    }

    @Override
    public void edtStringUsuarioExit(final Event<Object> event) {
        ApplicationUtil.setValue(
                FrmLoginA.USUARIO_SELECIONADO
                ,this.edtStringUsuario.getValue().asString().trim()
        );
    }

    private void lembrarSenha() {
        ApplicationUtil.setValue(
                FrmLoginA.LEMBRAR_SENHA
                ,this.chkLembrarSenha.getValue().asString()
        );
        String senha = this.edtStringSenha.getValue().asString();
        String pass = this.cript.encrypt(
                FrmLoginA.PASSPHRASE
                ,senha
        );
        boolean chkLembrarSenhaChecked = this.chkLembrarSenha.isChecked();
        if (chkLembrarSenhaChecked) {
            ApplicationUtil.setValue(
                    FrmLoginA.SENHA_USUARIO
                    ,pass
            );
        } else {
            ApplicationUtil.setValue(
                    FrmLoginA.SENHA_USUARIO
                    ,""
            );
        }
    }

    @Override
    public void cboSchemaEnter(Event<Object> event) {
        this.logar();
    }

    @Override
    public void cboTemaEnter(Event<Object> event) {
        this.logar();
    }
}