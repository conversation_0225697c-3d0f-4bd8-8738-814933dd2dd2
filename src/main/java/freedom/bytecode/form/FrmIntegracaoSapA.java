package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmIntegracaoSapW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.DateUtils;
import freedom.util.EmpresaUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

enum ConnectSap {

    CONSULTAR_CC("1"), CONSUMIR_CC("2");

    public final String acao;

    ConnectSap(String value) {
        acao = value;
    }
}


public class FrmIntegracaoSapA extends FrmIntegracaoSapW {
    private static final long serialVersionUID = 20130827081850L;

    private final Value idFila = new Value(null);
    private final Value valorCC = new Value(null);
    private final Value mensagemSap = new Value(null);
    private final Value detalharSap = new Value(null);
    private final Value statusFilaSap = new Value(null);
    private boolean ok = false;
    private long horaInicial;
    private long aguardarAte;
    private long aguardarAteCancelAuto;
    private boolean timerOcupado = false;
    private int pontos = 0;

    private boolean liberadoParaCancelar = false;
    private ConnectSap acaoConnectSap;

    @Override
    public void FFormCreate(Event<Object> event) {
        consultarStatusFila();
        if (podeFechar()) {
            this.close();
        }
        setTimer();
    }

    private void cancelar() {
        try {
            boolean timerSapNotEnabled = !this.timerSap.isEnabled();
            if (timerSapNotEnabled) {
                mensagemSap.setValue("Falha ao cancelar fila!");
                statusFilaSap.setValue("N");
                this.ok = true;
                this.close();
            }
            timerSap.setEnabled(false);
            String respCancel =  rn.cancelarFilaConsultaCC(idFila.asDecimal(), "N");
            statusFilaSap.setValue(rn.consultaStatusFila(idFila.asDecimal(), mensagemSap, detalharSap));
            if (respCancel.equals("S") || liberadoParaCancelar) {
                this.ok = true;
                this.close();
            } else {
                liberadoParaCancelar = true;
                lblStatusIntegracao.setFontColor("clRed");
                aguardarAteCancelAuto =  System.currentTimeMillis() + 2000; // 3 segundos +
                timerSap.setEnabled(true);
            }
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao cancelar"
                    ,dataException
            );
        }
    }

    @Override
    public void iconCancelarClick(Event<Object> event) {
        cancelar();
    }

    @Override
    public void vBoxBtnCancelarClick(Event<Object> event) {
        cancelar();
    }


    @Override
    public void hBoxSepCancelarClick(Event<Object> event) {
        cancelar();
    }

    @Override
    public void vBoxIconCancelarClick(Event<Object> event) {
        cancelar();
    }

    @Override
    public void lblAcaoCancelarClick(Event<Object> event) {
        cancelar();
    }

    public String getMensagemSap() {
        if (mensagemSap.isNull() || mensagemSap.isEmpty()) {
            return "";
        }
        return mensagemSap.toString().replace("#13", System.lineSeparator());
    }

    private String getStatusFilaSap() {
        if ((statusFilaSap.isNull()) || (statusFilaSap.isEmpty())) {
            return "FILA";
        }
        return statusFilaSap.toString();
    }

    public boolean isOkFila() {
        return getStatusFilaSap().equals("OK");
    }

    public boolean detalharRetornoCC() {
        return (StringUtils.isNotBlank(detalharSap.toString())) && (detalharSap.toString().equals("S"));
    }

    @Override
    public void timerSapTimer(Event<Object> event) {
        if (!timerOcupado) {
            timerOcupado = true;
            try {
                pontos = pontos + 1;

                int timeoutCancelar = 16000;
                if ((!hBoxCancelar.isVisible()) && (horaInicial > 0) && ((System.currentTimeMillis() - horaInicial) > timeoutCancelar)) {
                    hBoxCancelar.setVisible(true);
                }
                if (pontos > 20) {
                    pontos = 1;
                }
                consultarStatusFila();
                if (podeFechar()) {
                    this.close();
                }
                atualizarStatus();
            } finally {
                timerOcupado = false;
            }
        }
    }

    private void setTimer() {
        pontos = 1;
        horaInicial = System.currentTimeMillis();
        aguardarAte = horaInicial + 130000;
        aguardarAteCancelAuto = horaInicial + 199999;
        atualizarStatus();
        timerSap.setInterval(1500);
        timerSap.setRepeats(true);
        timerSap.setEnabled(true);
    }

    private boolean podeFechar() {
        if (isok()) {
            return true;
        }
        return !statusFilaSap.asString().equals("FILA") && !statusFilaSap.asString().equals("CONSULTANDO");
    }

    private boolean isok() {
        return ok;
    }

    private void consultarStatusFila() {
        try {
            if ((aguardarAteCancelAuto > 0) &&  (System.currentTimeMillis() > aguardarAteCancelAuto)){
                cancelar();
            } else {
                if ((aguardarAte > 0) && (System.currentTimeMillis() > aguardarAte)) {
                    timerSap.setEnabled(false);
                    if (acaoConnectSap == ConnectSap.CONSULTAR_CC) {
                        rn.cancelarFilaConsultaCC(idFila.asDecimal(), "S");
                    }
                    this.ok = true;
                }
                if (acaoConnectSap == ConnectSap.CONSULTAR_CC) {
                    statusFilaSap.setValue(rn.consultaStatusFila(idFila.asDecimal(), mensagemSap, detalharSap));
                    atualizarStatus();
                }
            }
        } catch (DataException e) {
            statusFilaSap.setValue(e.getMessage());
        }
    }

    private void atualizarStatus() {
        long tmp = System.currentTimeMillis() - horaInicial;
        StringBuilder msg = new StringBuilder();
        msg.append("[").append(DateUtils.format(new Date(tmp), "mm:ss")).append("]     ");
        if (acaoConnectSap == ConnectSap.CONSULTAR_CC) {
            if (statusFilaSap.asString().equals("FILA")) {
                msg.append("Aguarde, tentando conexão");
            } else  if (statusFilaSap.asString().equals("CONSULTANDO")){
                msg.append("Aguardando resposta do barramento");
                lblStatusIntegracao.setFontColor("clBlue");
            } else {
                msg.append("Aguarde, lendo retorno consulta");
                lblStatusIntegracao.setFontColor("clGreen");
            }
        } else if (acaoConnectSap == ConnectSap.CONSUMIR_CC) {
            if (statusFilaSap.asString().equals("FILA")) {
                msg.append("Aguarde, tentando conexão");
            } else {
                msg.append("Aguardando resposta do barramento");
                lblStatusIntegracao.setFontColor("clBlue");
            }
        } else {
            msg.append("Cadastro do cliente sendo expandido no SAP");
        }
        msg.append(StringUtils.rightPad("", pontos * 3, " . "));
        lblStatusIntegracao.setCaption(msg.toString());
        hBoxStatus.invalidate();
    }

    public String gerarFilaConsultaOrcamCreditoCorporativo(
            double codCliente
            ,double codEmpresa
            ,double codOrcMapa
            ,String consultaCliBloq
            ,String resultCompleto
            ,String geraMov
            ,String usuarioConsulta
    ) {
        this.acaoConnectSap = ConnectSap.CONSULTAR_CC;
        this.setCaption("Crédito Corporativo");
        try {
            return rn.gerarFilaConsultaOrcamCc(codCliente, codEmpresa, codOrcMapa, consultaCliBloq, resultCompleto, geraMov, usuarioConsulta, idFila, valorCC);
        } catch (DataException e) {
            return e.getMessage();
        }
    }

    public void aguardarConsumirCreditoCorporativo(Double idFilaConsumir) {
        /*ATENÇÂO:
        * Consumir credito, já foi criada a fila no momento da emissão da nota fiscal.
        * Aqui só vai mostrar a tela
        * */
        this.acaoConnectSap = ConnectSap.CONSUMIR_CC;
        this.setCaption("Crédito Corporativo");
        this.idFila.setValue(idFilaConsumir);
    }

    public Double getValorCC(){
        return valorCC.asDecimal();
    }

    public Double getIdFilaCC(){
        return idFila.asDecimal();
    }

}