package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmMotivoPerdasA extends FrmMotivoPerdasW {

    private static final long serialVersionUID = 20130827081850L;

    public FrmMotivoPerdasA() {
        this.efDepartamento.setListOptions("Todos=T;Veículos=V;Peças=P;Oficina=O");
        this.efDepartamento.setValue("T");
        this.efExclusivoDescarteAuto.setListOptions("Todos=T;Sim=S;Não=N");
        this.efExclusivoDescarteAuto.setValue("T");
        this.efAtivo.setListOptions("Todos=T;Sim=S;Não=N");
        this.efAtivo.setValue("T");
    }

    @Override
    public void gridPrincipalClickImageDelete(final Event<Object> event) {
        try {
            this.tbDescarteCheck.close();
            this.tbDescarteCheck.clearFilters();
            this.tbDescarteCheck.clearParams();
            this.tbDescarteCheck.addParam("COD_DESCARTE", tbDescartes.getCOD_DESCARTE());
            this.tbDescarteCheck.open();
            if (this.tbDescarteCheck.getTOTAL().asLong() > 0) {
                EmpresaUtil.showInformationMessage("Não pode excluir, existe evento usando esse motivo.");
                return;
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Falha ao validar exclusão",
                    dataException);
            return;
        }
        super.gridPrincipalClickImageDelete(event);
    }

    @Override
    public void executaFiltroPrincipal() {
        String filtroDepartamento = this.efDepartamento.getValue().asString();
        String filtroAtivo = this.efAtivo.getValue().asString();
        String filtroPodeVisualizarNaVendaPerdida = this.efExclusivoDescarteAuto.getValue().asString();
        this.executaFiltroPrincipal(filtroDepartamento,
                filtroAtivo,
                filtroPodeVisualizarNaVendaPerdida);
    }

    public void executaFiltroPrincipal(String filtroDepartamento,
                                       String filtroAtivo,
                                       String filtroPodeVisualizarNaVendaPerdida) {
        try {
            this.rn.executaFiltroPrincipal(filtroDepartamento,
                    filtroAtivo,
                    filtroPodeVisualizarNaVendaPerdida);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao executar filtro principal.",
                    dataException);
        }
    }

    //@Override
    public void iconClassHelpClick(final Event event) {
        FormUtil.redirect("http://ajuda.nbsi.com.br:84/index.php/Motivos_Venda_Perdida", true);
    }

}
