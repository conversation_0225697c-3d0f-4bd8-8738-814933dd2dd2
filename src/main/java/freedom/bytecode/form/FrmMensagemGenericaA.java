package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmMensagemGenericaW;
import freedom.client.controls.impl.TFButton;
import freedom.client.controls.impl.TFLabel;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.CSSUtil;
import freedom.client.util.FormUtil;
import lombok.Getter;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;
import org.zkoss.zhtml.I;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.HtmlBasedComponent;
import org.zkoss.zul.*;

import java.util.Stack;

@Setter
@Getter
public class FrmMensagemGenericaA extends FrmMensagemGenericaW {
    private static final long serialVersionUID = 20130827081850L;

    private Stack<Layout> stackSecoes = new Stack<>();
    private I lastIcon = null;
    private TFLabel lastLabel = null;
    private TFButton lastButton = null;
    private Component lastComponentAdd = null;


    private int idItem = 0;

    private int getNextIdItem() {
        setIdItem(getIdItem() + 1);
        return getIdItem();
    }

    public FrmMensagemGenericaA fimSecao() {
        if (this.stackSecoes.size() > 1) {
            this.stackSecoes.pop();
        }
        return this;
    }


    private String title;

    public static FrmMensagemGenericaA create() {
        return new FrmMensagemGenericaA().AddSecaoVertical().setSpace(18);
    }

    public FrmMensagemGenericaA title(String title) {
        this.title = title;
        return this;
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        Window w = (Window) ((Vlayout) this.getImpl()).getParent();
        Vlayout div = (Vlayout) this.getImpl();
        w.setTitle(this.title);
        w.setMaximizable(false);
        div.setStyle("display: grid;min-height: 120px; min-width: 480px; max-width: 600px; place-items: center;");
        w.setClass("z-messagebox-window");
        w.setHeight("auto");
        w.setWidth("auto");
        w.setMaximizable(false);
        //w.applyProperties();
    }

    public FrmMensagemGenericaA AddSecaoVertical() {
        return AddSecaoVertical("");
    }

    public FrmMensagemGenericaA AddSecaoHorizontal() {
        return AddSecaoHorizontal("");
    }
//
//    public FrmMensagemGenericaA AddSecaoHorizontal(int space, String alinhar) {
//        return AddSecaoHorizontal("display: flex; flex-direction: row; gap: " + space + "px; justify-content: " + alinhar);
//    }
//
//    public FrmMensagemGenericaA AddSecaoVertical(int space, String alinhar) {
//        return AddSecaoVertical("display: flex; flex-direction: column; gap: " + space + "px; align-items: " + alinhar);
//    }

    public FrmMensagemGenericaA setPropertieCss(String style) {
        if (getLastComponentAdd() != null) {
            if (getLastComponentAdd() instanceof I) {
                I icon = (I) getLastComponentAdd();
                (icon).setStyle(CSSUtil.add(icon.getStyle(), style));
                icon.applyProperties();
            } else {
                HtmlBasedComponent component = (HtmlBasedComponent) getLastComponentAdd();
                component.setStyle(CSSUtil.add(component.getStyle(), style));
                component.applyProperties();
            }
        }
        return this;
    }

    public FrmMensagemGenericaA setSpace(int space) {
        if (!this.stackSecoes.isEmpty()) {
            this.stackSecoes.peek().setStyle(CSSUtil.add(this.stackSecoes.peek().getStyle(), "gap: " + space + "px"));
            this.stackSecoes.peek().applyProperties();
        }
        return this;
    }

    public FrmMensagemGenericaA setAlinharEsquerda() {
        return setAlinharSecao("left");
    }

    public FrmMensagemGenericaA setAlinharCentro() {
        return setAlinharSecao("center");
    }

    public FrmMensagemGenericaA setAlinharDireita() {
        return setAlinharSecao("right");
    }

    private FrmMensagemGenericaA setAlinharSecao(String alinhar) {
        if (!this.stackSecoes.isEmpty()) {
            if (this.stackSecoes.peek().getId().contains("secao_horizontal_")) {
                this.stackSecoes.peek().setStyle(CSSUtil.add(this.stackSecoes.peek().getStyle(), "justify-content: " + alinhar));
            } else {
                String alinharVertical = "";
                switch (alinhar) {
                    case "left":
                        alinharVertical = "flex-start";
                        break;
                    case "center":
                        alinharVertical = "center";
                        break;
                    case "right":
                        alinharVertical = "flex-end";
                        break;
                }
                this.stackSecoes.peek().setStyle(CSSUtil.add(this.stackSecoes.peek().getStyle(), "align-items: " + alinharVertical));
            }
            this.stackSecoes.peek().applyProperties();
        }
        return this;
    }

    public FrmMensagemGenericaA AddSecaoHorizontal(String style) {
        Hlayout boxSecaoHorizontal = new Hlayout();
        boxSecaoHorizontal.setId("secao_horizontal_" + getNextIdItem());
        boxSecaoHorizontal.setClientAttribute("idTest", boxSecaoHorizontal.getId());
        boxSecaoHorizontal.setHeight("auto");
        boxSecaoHorizontal.setWidth("auto");
        boxSecaoHorizontal.setStyle("display: flex; flex-direction: row; justify-content: left; align-items: center; padding: 5px;");
        if (style != null) {
            boxSecaoHorizontal.setStyle(CSSUtil.add(boxSecaoHorizontal.getStyle(), style));
        }
        boxSecaoHorizontal.applyProperties();
        if (this.stackSecoes.isEmpty()) {
            ((Component) this.vboxPrincipal.getImpl()).getChildren().add(boxSecaoHorizontal);
        } else {
            stackSecoes.peek().getChildren().add(boxSecaoHorizontal);
        }
        this.vboxPrincipal.applyProperties();
        stackSecoes.push(boxSecaoHorizontal);
        setLastComponentAdd(boxSecaoHorizontal);
        return this;
    }

    public FrmMensagemGenericaA AddSecaoVertical(String style) {
        Vlayout boxSecaoVertical = new Vlayout();
        boxSecaoVertical.setId("secao_vertical_" + getNextIdItem());
        boxSecaoVertical.setClientAttribute("idTest", boxSecaoVertical.getId());
        boxSecaoVertical.setHeight("auto");
        boxSecaoVertical.setWidth("auto");
        boxSecaoVertical.setStyle("display: flex; flex-direction: column; justify-content: center; align-items: left; padding: 5px;");
        if (style != null) {
            boxSecaoVertical.setStyle(CSSUtil.add(boxSecaoVertical.getStyle(), style));
        }
        if (this.stackSecoes.isEmpty()) {
            ((Component) this.vboxPrincipal.getImpl()).getChildren().add(boxSecaoVertical);
            this.vboxPrincipal.applyProperties();
        } else {
            stackSecoes.peek().getChildren().add(boxSecaoVertical);
            boxSecaoVertical.applyProperties();
        }
        stackSecoes.push(boxSecaoVertical);
        setLastComponentAdd(boxSecaoVertical);
        return this;
    }

    public FrmMensagemGenericaA addIcon(String iconClass) {
        Hlayout box = new Hlayout();
        box.setId("box_icon_" + getNextIdItem());
        box.setStyle("padding: 3px");
        box.setClientAttribute("idTest", box.getId());

        I icon = new I();
        icon.setId("icon_" + getNextIdItem());
        icon.setClientAttribute("idTest", icon.getId());
        icon.setSclass(iconClass);
        icon.setStyle("font-size: 30px");
        box.getChildren().add(icon);
        stackSecoes.peek().getChildren().add(box);
        stackSecoes.peek().applyProperties();
        setLastIcon(icon);
        setLastComponentAdd(icon);
        return this;
    }

    public FrmMensagemGenericaA setIconColor(String colorHex) {
        if (this.lastIcon == null) {
            return this;
        }
        this.lastIcon.setStyle(CSSUtil.add(this.lastIcon.getStyle(), "color:" + colorHex));
        this.lastIcon.applyProperties();
        return this;
    }

    public FrmMensagemGenericaA setIconSize(int size) {
        if (this.lastIcon == null) {
            return this;
        }
        lastIcon.setStyle(CSSUtil.add(lastIcon.getStyle(), "font-size:" + size + "px"));
        lastIcon.applyProperties();
        return this;
    }

    @NotNull
    public FrmMensagemGenericaA addLabel(String mensagem) {
        TFLabel lblMensagemSimples = new TFLabel();
        lblMensagemSimples.setName("label_" + getNextIdItem());
        lblMensagemSimples.setCaption(mensagem);
        lblMensagemSimples.setFontColor("clWindowText");
        lblMensagemSimples.setFontSize(-17);
        lblMensagemSimples.setFontName("Tahoma");
        stackSecoes.peek().getChildren().add((Component) lblMensagemSimples.getImpl());
        HtmlBasedComponent lbl = (HtmlBasedComponent) lblMensagemSimples.getImpl();
        lbl.setStyle(CSSUtil.remove(lbl.getStyle(), "white-space"));
        lbl.setStyle(CSSUtil.add(lbl.getStyle(), "display:block;word-break:break-word;"));
        lblMensagemSimples.applyProperties();
        setLastLabel(lblMensagemSimples);
        setLastComponentAdd((Component) lblMensagemSimples.getImpl());
        return this;
    }

    public FrmMensagemGenericaA setFontNegrito() {
        if (this.lastLabel == null) {
            return this;
        }
        Label l = (Label) this.lastLabel.getImpl();
        l.setStyle(CSSUtil.add(l.getStyle(), "font-weight: bold;"));
        l.applyProperties();
        return this;
    }

    public FrmMensagemGenericaA setFontSize(int size) {
        if (this.lastLabel == null) {
            return this;
        }
        Label l = (Label) this.lastLabel.getImpl();
        l.setStyle(CSSUtil.add(l.getStyle(), "font-size: " + size + "px;"));
        l.applyProperties();
        return this;
    }

    public FrmMensagemGenericaA addBtnFechar(String caption) {
        return addBtnSimples(caption, this::close);
    }

    public FrmMensagemGenericaA addBtnSimples(String caption, Runnable runnable) {
        TFButton btnSimples = new TFButton();
        btnSimples.setName("button_" + getNextIdItem());
        btnSimples.setLeft(0);
        btnSimples.setTop(0);
        btnSimples.setHeight(38);
        btnSimples.setCaption(caption);
        btnSimples.setFontColor("clWindowText");
        btnSimples.setFontSize(-17);
        btnSimples.setFontName("Tahoma");
        btnSimples.setFontStyle("[]");
        btnSimples.addEventListener("onClick", (EventListener<Event<Object>>) (Event<Object> event) -> runnable.run());
        btnSimples.setImageId(0);
        btnSimples.setColor("clBtnFace");
        btnSimples.setAccess(false);
        btnSimples.setIconReverseDirection(false);
        ((HtmlBasedComponent) btnSimples.getImpl()).setStyle("padding-left: 10px; padding-right: 10px;");
        this.stackSecoes.peek().getChildren().add((Component) btnSimples.getImpl());
        btnSimples.applyProperties();
        setLastButton(btnSimples);
        setLastComponentAdd((Component) btnSimples.getImpl());
        return this;
    }

    public void show() {
        FormUtil.doShow(this,
                t -> {
                });
    }


}
