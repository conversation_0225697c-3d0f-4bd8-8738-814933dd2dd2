package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAlterarItemBasicoW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;
import lombok.Getter;
import lombok.Setter;

public class FrmAlterarItemBasicoA extends FrmAlterarItemBasicoW {

    private static final long serialVersionUID = 20130827081850L;

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    @Setter
    @Getter
    private boolean ok = false;

    private boolean itemBloqueadoParaCompra = false;

    public FrmAlterarItemBasicoA() {
        this.setDelCallBack();
    }

    public void filtrarItem(
            String codItem
            ,double codFornecedor
    ) {
        try {
            this.rn.filtrarItem(
                    codItem
                    ,codFornecedor
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "[183] Erro ao filtrar item"
                    ,dataException
            );
        }
    }

    private void abreTabelasAux() {
        try {
            this.rn.filtrarGrupo();
            this.rn.filtrarFabricante();
            this.rn.filtrarMarca();
            this.rn.filtrarSituacaoEspecial();
            this.rn.filtrarUnidadeMedida();
            this.rn.filtrarLetraDesconto();
            this.filtrarSubGrupo();
            this.carregarOrigem();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "[184] Erro ao abrir tabelas auxiliares"
                    ,dataException
            );
        }
    }

    private void filtrarSubGrupo() {
        try {
            int codGrupoInterno = this.tbItens.getCOD_GRUPO_INTERNO().asInteger();
            this.rn.filtrarSubGrupo(
                    codGrupoInterno
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "[185] Erro ao abrir tabelas auxiliares"
                    ,dataException
            );
        }
    }

    private void definirTextoBtnBloquearDesbloquearItemParaCompraEEdtStatus() {
        this.itemBloqueadoParaCompra = this.tbItensFornecedor.getCOMPRAS_BLOQUEADAS().asString().equals("S");
        if (this.itemBloqueadoParaCompra) {
            this.edtStatus.setValue(
                    "Bloqueado para compra"
            );
            this.btnBloquearDesbloquearItemParaCompra.setCaption(
                    "Desbloquear"
            );
            this.btnBloquearDesbloquearItemParaCompra.setHint(
                    "Desbloquear para compra"
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \"K0328\""
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "ITENS_FORNECEDOR.COMPRAS_BLOQUEADAS"
            );
        } else {
            this.edtStatus.setValue(
                    "Desbloqueado para compra"
            );
            this.btnBloquearDesbloquearItemParaCompra.setCaption(
                    "Bloquear"
            );
            this.btnBloquearDesbloquearItemParaCompra.setHint(
                    "Bloquear para compra"
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \"K0328\""
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "ITENS_FORNECEDOR.COMPRAS_BLOQUEADAS"
            );
        }
        this.edtStatus.setHint(
                "Status do bloqueio para compra"
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "Clique no botão \""
                        + this.btnBloquearDesbloquearItemParaCompra.getCaption()
                        + "\" para alterar"
        );
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.abreTabelasAux();
        //region Origem
        String codAcessoAlterarOrigemItem = "K0319";
        boolean possuiAcessoK0319AlterarOrigemItem = this.validarAcesso(
                this.usuarioLogado
                ,codAcessoAlterarOrigemItem
        ).equals("S");
        this.cboOrigem.setEnabled(
                possuiAcessoK0319AlterarOrigemItem
        );
        String hintCboOrigem = (
                "Origem"
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "Acesso \""
                        + codAcessoAlterarOrigemItem
                        + "\""
        );
        this.cboOrigem.setHint(
                hintCboOrigem
        );
        //endregion
        //region Bloquear/Desbloquear Item para Compra
        boolean possuiAcessoK0328PermiteBloquearEDesbloquearItemParaCompra = EmpresaUtil.validarAcesso(
                "K0328"
                ,false
        );
        this.btnBloquearDesbloquearItemParaCompra.setEnabled(
                possuiAcessoK0328PermiteBloquearEDesbloquearItemParaCompra
        );
        this.definirTextoBtnBloquearDesbloquearItemParaCompraEEdtStatus();
        //endregion
    }

    @Override
    public void cboGrupoChange(Event<Object> event) {
        this.filtrarSubGrupo();
    }

    private void setDelCallBack() {
        this.cboGrupo.setDelCallback(this::filtrarSubGrupo);
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        this.close();
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            long codGrupoInterno = this.cboGrupo.getValue().asLong();
            if (codGrupoInterno == 0L) {
                String mensagem = (
                        Constantes.O_CAMPO
                        + this.cboGrupo.getHelpCaption()
                        + Constantes.DEVE_SER_PREENCHIDO
                );
                Dialog.create()
                        .showNotificationInfo(
                                mensagem
                                ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                                ,10000                   // Tempo em milissegundos para exibir a mensagem
                                ,this.cboGrupo           // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
                return;
            }
            long codSubgrupoInterno = this.cboSubgrupo.getValue().asLong();
            if (codSubgrupoInterno == 0L) {
                String mensagem = (
                        Constantes.O_CAMPO
                                + this.cboSubgrupo.getHelpCaption()
                                + Constantes.DEVE_SER_PREENCHIDO
                );
                Dialog.create()
                        .showNotificationInfo(
                                mensagem
                                ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                                ,10000                   // Tempo em milissegundos para exibir a mensagem
                                ,this.cboSubgrupo        // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
                return;
            }
            String unidade = this.cboUnidade.getValue().asString().trim();
            if (unidade.isEmpty()) {
                String mensagem = (
                        Constantes.O_CAMPO
                                + this.cboUnidade.getHelpCaption()
                                + Constantes.DEVE_SER_PREENCHIDO
                );
                Dialog.create()
                        .showNotificationInfo(
                                mensagem
                                ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                                ,10000                   // Tempo em milissegundos para exibir a mensagem
                                ,this.cboUnidade         // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
                return;
            }
            String codLetraDesconto = this.cboLetraDesconto.getValue().asString().trim();
            if (codLetraDesconto.isEmpty()) {
                String mensagem = (
                        Constantes.O_CAMPO
                                + this.cboLetraDesconto.getHelpCaption()
                                + Constantes.DEVE_SER_PREENCHIDO
                );
                Dialog.create()
                        .showNotificationInfo(
                                mensagem
                                ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                                ,10000                   // Tempo em milissegundos para exibir a mensagem
                                ,this.cboLetraDesconto   // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
                return;
            }
            this.tbItens.post();
            this.tbItensFornecedor.post();
            this.rn.salvar();
            this.setOk(
                    true
            );
            this.close();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao salvar alterações"
                    ,dataException
            );
        }
    }

    private void carregarOrigem() {
        try {
             this.rn.carregarOrigem();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar origem",
                    dataException);
        }
    }

    private String validarAcesso(
            String loginUsuario
            ,String codAcesso
    ) {
        String retFuncao = "";
        try {
            retFuncao = this.rn.validarAcesso(loginUsuario,
                    codAcesso);
        } catch (DataException dataException) {
            String mensagem = "Erro ao validar o acesso \""
                    + codAcesso
                    + "\" para o usuário \""
                    + loginUsuario
                    + "\"";
            EmpresaUtil.showError(mensagem,
                    dataException);
        }
        return retFuncao;
    }

    @Override
    public void btnBloquearDesbloquearItemParaCompraClick(Event<Object> event) {
        String codItem = this.tbItens.getCOD_ITEM().asString();
        long codFornecedor = this.tbItensFornecedor.getCOD_FORNECEDOR().asLong();
        String itensFornecedorComprasBloqueadas = this.tbItensFornecedor.getCOMPRAS_BLOQUEADAS().asString();
        FrmBloquearDesbloquearItemCompraA frmBloquearDesbloquearItemCompraA = new FrmBloquearDesbloquearItemCompraA(
                this.itemBloqueadoParaCompra
                ,codItem
                ,codFornecedor
        );
        FormUtil.doShow(
                frmBloquearDesbloquearItemCompraA
                ,t -> {
                    boolean frmBloquearDesbloquearItemCompraAFechadoAoSalvar = frmBloquearDesbloquearItemCompraA.isFechadoAoSalvar();
                    if (frmBloquearDesbloquearItemCompraAFechadoAoSalvar) {
                        if (itensFornecedorComprasBloqueadas.equals("S")) {
                            this.atualizarItensFornecedorComprasBloqueadas(
                                    "N"
                            );
                        } else {
                            this.atualizarItensFornecedorComprasBloqueadas(
                                    "S"
                            );
                        }
                        this.definirTextoBtnBloquearDesbloquearItemParaCompraEEdtStatus();
                    }
                });
    }

    private void atualizarItensFornecedorComprasBloqueadas(
            String comprasBloqueadasSN
    ) {
        try {
            this.rn.atualizarItensFornecedorComprasBloqueadas(
                    comprasBloqueadasSN
            );
        } catch (
                DataException dataException
        )
        {
            EmpresaUtil.showError(
                    "Erro ao atualizar a informação de compras bloqueadas de itens fornecedor"
                    ,dataException
            );
        }
    }

}