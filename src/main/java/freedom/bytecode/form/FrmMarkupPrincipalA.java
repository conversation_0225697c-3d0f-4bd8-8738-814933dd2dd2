package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmMarkupPrincipalW;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.util.EmpresaUtil;

public class FrmMarkupPrincipalA extends FrmMarkupPrincipalW {

    private static final long serialVersionUID = 20130827081850L;

    private boolean isEdicaoMarkupModelo = false;


    private boolean isEdicaoMarkup = false;


    public FrmMarkupPrincipalA() {
        try {
            rn.carregarGridMarKupModelo("", 0);
            enabledEdicaoMarkupModelo(false);
            enabledEdicaoMarkup(false);
            tabListagemMarkupModelo.setFontSize(12);
            tabCadastroMarkupModelo.setFontSize(12);
            tabMarkup.setFontSize(12);
            rn.carregaDadosMarkupTipo();
        } catch (Exception e) {
            Dialog.create().title("Erro ao processar").message(e.getMessage()).showException(e);
        }
    }

    @Override
    public void filtrarGrid(final Event<Object> event) {
        try {
            rn.carregarGridMarKupModelo(efDescricao.getValue().asString(), efIdMarkup.getValue().asInteger());
        } catch (Exception e) {
            Dialog.create().title("Erro ao processar").message(e.getMessage()).showException(e);
        }
    }

    private void enabledEdicaoMarkupModelo(Boolean isEnabled) {
        isEdicaoMarkupModelo = isEnabled;
        btnNovo.setEnabled(!isEdicaoMarkupModelo);
        btnAlterar.setEnabled(!isEdicaoMarkupModelo);
        btnExcluir.setEnabled(!isEdicaoMarkupModelo);
        btnSalvar.setEnabled(isEdicaoMarkupModelo);
        btnCancelar.setEnabled(isEdicaoMarkupModelo);
        gridPrincipal.setEnabled(!isEdicaoMarkupModelo);
        edDescricaoMarkupModelo.setEnabled(isEdicaoMarkupModelo);
        edtipoCustoMarkupModelo.setEnabled(isEdicaoMarkupModelo);
    }

    private void enabledEdicaoMarkup(Boolean isEnabled) {
        isEdicaoMarkup = isEnabled;
        btnAlterarMarkup.setEnabled(isEdicaoMarkupModelo && !isEdicaoMarkup);
        btnSalvarMarkup.setEnabled(isEdicaoMarkupModelo && isEdicaoMarkup);
        btnCancelarMarkup.setEnabled(isEdicaoMarkupModelo && isEdicaoMarkup);
        GridMarkup.setEnabled(!(isEdicaoMarkupModelo && isEdicaoMarkup));
        edIdmarkupTipoMarkup.setEnabled(isEdicaoMarkupModelo && isEdicaoMarkup);
        edTipoComissaoMarkup.setEnabled(isEdicaoMarkupModelo && isEdicaoMarkup);
        edValorFixoMarkup.setEnabled(isEdicaoMarkupModelo && isEdicaoMarkup);
        edValorPercentualMarkup.setEnabled(isEdicaoMarkupModelo && isEdicaoMarkup);
    }

    @Override
    public void btnNovoClick(final Event event) {
        try {
            rn.novoMarkupModelo();
            enabledEdicaoMarkupModelo(true);
            enabledEdicaoMarkup(false);
            PageControlMurkup.selectTab(1);
            edDescricaoMarkupModelo.setFocus();
        } catch (Exception e) {
            EmpresaUtil.showError("Falha ao iniciar Novo Markup Modelo", e);
        }
    }

    @Override
    public void btnAlterarClick(final Event event) {
        try {
            if (rn.nenhumRegistroGridMarkupModeloSelecionado()) {
                EmpresaUtil.showWarning("Erro ao tentar editar Markup Modelo", "Obrigatório Selecionar um Registro a ser Alterado.");
                PageControlMurkup.selectTab(0);
                return;
            }
            rn.alterarMarkupModelo();
            enabledEdicaoMarkupModelo(true);
            enabledEdicaoMarkup(false);
            PageControlMurkup.selectTab(1);
            edDescricaoMarkupModelo.setFocus();
        } catch (Exception e) {
            EmpresaUtil.showError("Falha ao Editar Registro Markup Modelo", e);
        }
    }

    @Override
    public void btnSalvarClick(final Event event) {
        try {
            if (isEdicaoMarkup) {
                salvarMarkupClick();
            }
            if (isEdicaoMarkup) {
                return;
            }
            if (validaCamposMarkupModelo()) {
                rn.salvarMarkupModelo();
                enabledEdicaoMarkupModelo(false);
                enabledEdicaoMarkup(false);
                PageControlMurkup.selectTab(0);
                rn.carregarGridMarKupModelo(efDescricao.getValue().asString(), efIdMarkup.getValue().asInteger());
                rn.selecionarRegistroAnteriorGridMarkupModelo();
            }
        } catch (Exception e) {
            EmpresaUtil.showError("Falha ao tentar salvar.", e);
        }
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        try {
            if (!rn.isExisteCrmFluxoVinculado()) {
                Dialog.create().title("Exclusão Modelo Markup").message("Tem certeza que deseja excluir o Modelo Markup?").confirmSimNao((responseSimNao) -> {
                    try {
                        if (responseSimNao.equals("1")) {
                            rn.excluirMarkupModelo();
                            PageControlMurkup.selectTab(0);
                        }
                    } catch (Exception e) {
                        EmpresaUtil.showError("Falha ao tentar Excluir o registro.", e);
                    }
                });
            } else {
                Dialog.create().title("Exclusão Modelo Markup").message("Ops, Não é possivel excluir este Markup modelo pois já existe um CRM_PARM_FLUXO vinculado!").showInformation(((EventListener) event1 -> {
                }));
            }
        } catch (Exception e) {
            EmpresaUtil.showError("Falha ao tentar Excluir o registro Markup Modelo.", e);
        }
    }

    @Override
    public void btnCancelarClick(final Event event) {
        try {
            rn.cancelarAlteracaoMarkupModelo();
            enabledEdicaoMarkupModelo(false);
            enabledEdicaoMarkup(false);
            PageControlMurkup.selectTab(0);
            rn.carregarGridMarKupModelo(efDescricao.getValue().asString(), efIdMarkup.getValue().asInteger());
            rn.selecionarRegistroAnteriorGridMarkupModelo();
        } catch (Exception e) {
            EmpresaUtil.showError("Falha ao Cancelar", e);
        }
    }

    public boolean validaCamposMarkupModelo() {
        if (edDescricaoMarkupModelo.getValue().asString().equals("")) {
            Dialog.create().title("Validação").message("É obrigatório preencher o campo Descrição.").showInformation(((EventListener) event1 -> {
                PageControlMurkup.selectTab(1);
                edDescricaoMarkupModelo.setFocus();
            }));
            return false;
        }
        if (edtipoCustoMarkupModelo.getValue().asString().equals("")) {
            Dialog.create().title("Validação").message("É obrigatório selecionar um Tipo de Custo.").showInformation(((EventListener) event1 -> {
                PageControlMurkup.selectTab(1);
                edtipoCustoMarkupModelo.setFocus();
            }));
            return false;
        }
        return true;
    }

    @Override
    public void tbMarkupModeloAfterScroll(final Event<Object> event) {
        carregarDadosMarkupSelecionado();
    }

    public void carregarDadosMarkupSelecionado() {
        try {
            rn.carregarDadosMarkup();
        } catch (Exception ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados do Item: ", ex);
        }
    }

    @Override
    public void btnAlterarMarkupClick(final Event<Object> event) {
        try {
            if (rn.nenhumRegistroGridMarkupSelecionado()) {
                EmpresaUtil.showWarning("Erro ao tentar editar Markup", "Obrigatório Selecionar um Registro na tabela markup.");
                return;
            }
            rn.AlterarMarkup();
            enabledEdicaoMarkup(true);
            edIdmarkupTipoMarkup.setEnabled(false);
        } catch (Exception e) {
            EmpresaUtil.showError("Falha ao tentar cancelar Markup: ", e);
        }
    }

    @Override
    public void btnSalvarMarkupClick(final Event<Object> event) {
        salvarMarkupClick();
    }

    public void salvarMarkupClick() {
        try {
            if (validarMarkup()) {
                rn.salvarMarkup();
                enabledEdicaoMarkup(false);
            }
        } catch (Exception e) {
            EmpresaUtil.showError("Falha ao tentar salvar.", e);
        }
    }

    @Override
    public void btnCancelarMarkupClick(final Event<Object> event) {
        try {
            rn.cancelarMarkup();
            enabledEdicaoMarkup(false);
        } catch (Exception e) {
            EmpresaUtil.showError("Falha ao tentar cancelar Markup.", e);
        }
    }


    public boolean validarMarkup() {
        if (edTipoComissaoMarkup.getValue().asString().equals("")) {
            Dialog.create().title("Validação").message("É obrigatório selecionar um tipo comissão!").showInformation(((EventListener) event1 -> {
                PageControlMurkup.selectTab(2);
                edDescricaoMarkupModelo.setFocus();
            }));
            return false;
        }
        return true;
    }

}
