package freedom.bytecode.form;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.form.wizard.*;
import freedom.bytecode.rn.wizard.*;
import freedom.util.EmpresaUtil;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

public class FrmHistoricoFichaClienteA extends FrmHistoricoFichaClienteW {
    private static final long serialVersionUID = 20130827081850L;

    private Double codCliente;
    private String nomeCliente;

    public void setCLiente(double codCliente, String nomeCliente){
        this.codCliente = codCliente;
        this.nomeCliente = nomeCliente;
    }

    public void setCodCliente(double codCliente){
        this.codCliente = codCliente;
    }

    public void setNomeCliente(String nomeCliente){
        this.nomeCliente = nomeCliente;
    }

    public FrmHistoricoFichaClienteA(){

    }

    public void carregarDadosTela(double codCliente, String nomeCliente){
        this.codCliente = codCliente;
        this.nomeCliente = nomeCliente;
        this.carregarTela();
        this.pgCtrlPrincipalChange(null);
    }

    @Override
    public void pgCtrlPrincipalChange(Event <Object> event){
        try {
            if (pgCtrlPrincipal.getSelectedTab() == tabEventos && !rn.isCarregadoDadosEvento()){
                rn.carregarDadosEvento(this.codCliente);
            }else if(pgCtrlPrincipal.getSelectedTab() == tabVeiculos && !rn.isCarregadoDadosVeiculos()){
                rn.carregarDadosVeiculos(this.codCliente);
            } else if(pgCtrlPrincipal.getSelectedTab() == tabOsEOrc && !rn.isCarregadoDadosOsVeiculosCombo()){
                rn.carregarDadosOsVeiculosCombo(this.codCliente);
                carregarDadosOsOrc();
            } else if(pgCtrlPrincipal.getSelectedTab() == tabServicosEPecas && !rn.isCarregadoVeiculosTabServicosCombo2()){
                rn.carregarVeiculosTabServicosCombo2(codCliente);
                rn.carregarDadosOsOrcCombo(codCliente, null,null,null);
                carregarDadosServicosPecas();
            }
        } catch (DataException e) {
            EmpresaUtil.showMessage("erro ao carregar dados","Não foi possibel carregar os dados do cliente");
        }
    }

    public void carregarTela(){
        labelNomeCliente.setValue(this.nomeCliente);
    }

    @Override
    public void comboTabOsOrcVeiculosChange(Event <Object> event){
        carregarDadosOsOrc();
    }

    @Override
    public void comboTabOsOrcVeiculosClearClick(Event <Object> event){
        carregarDadosOsOrc();
    }

    @Override
    public void comboTabOsOrcVeiculosEnter(Event <Object> event){
        carregarDadosOsOrc();
    }


    public void carregarDadosOsOrc(){
        try{
            String chassi = null;
            Double  codModelo = null;
            Double codProduto = null;
            if (!comboTabOsOrcVeiculos.getValue().isNull()) {
                chassi = rn.getChassi();
                codModelo = rn.getModelo();
                codProduto = rn.getProduto();
            }
            rn.carregarDadosOsOrc(this.codCliente, codModelo, codProduto, chassi);

        }catch (DataException e){
            EmpresaUtil.showMessage("erro ao carregar dados","Não foi possivel carregar os dados da OS");
        }
    }

    @Override
    public void comboTabServicosPecasVeiculoChange(Event <Object> event){
        this.carregarDadosOsOrcCombo();
        this.carregarDadosServicosPecas();
    }

    @Override
    public void comboTabServicosPecasVeiculoClearClick(Event <Object> event){
        this.carregarDadosOsOrcCombo();
        this.carregarDadosServicosPecas();
    }

    @Override
    public void comboTabServicosPecasVeiculoEnter(Event <Object> event){
        this.carregarDadosOsOrcCombo();
        this.carregarDadosServicosPecas();
    }

    public void carregarDadosOsOrcCombo(){
        try{
            String chassi = null;
            Double  codModelo = null;
            Double codProduto = null;
            if (!comboTabServicosPecasVeiculo.getValue().isNull()) {
                chassi = rn.getChassiCombo2();
                codModelo = rn.getModeloCombo2();
                codProduto = rn.getProdutoCombo2();
            }
            rn.carregarDadosOsOrcCombo(this.codCliente, codModelo, codProduto, chassi);

        }catch (DataException e){
            EmpresaUtil.showMessage("erro ao carregar dados","Não foi possivel carregar os dados da OS");
        }
    }

    @Override
    public void comboTabServicosPecasOSChange(Event <Object> event){
        this.carregarDadosServicosPecas();
    }
    @Override
    public void comboTabServicosPecasOSClearClick(Event <Object> event){
        this.carregarDadosServicosPecas();
    }
    @Override
    public void comboTabServicosPecasOSEnter(Event <Object> event){
        this.carregarDadosServicosPecas();
    }



    public void carregarDadosServicosPecas(){
        try{
            String chassi = null;
            Double  codModelo = null;
            Double codProduto = null;
            if (!comboTabServicosPecasVeiculo.getValue().isNull()) {
                chassi = rn.getChassiCombo2();
                codModelo = rn.getModeloCombo2();
                codProduto = rn.getProdutoCombo2();
            }
            Double numeroOs = null;
            Double codEmpresa = null;
            if (!comboTabServicosPecasOS.getValue().isNull()){
                numeroOs = rn.getNumeroOsCombo();
                codEmpresa = rn.getCodEmpresaCombo();
            }

            rn.carregarDadosServico(this.codCliente, codModelo, codProduto, chassi, numeroOs, codEmpresa);
            rn.carregarDadosPecas(this.codCliente, codModelo, codProduto, chassi, numeroOs, codEmpresa);

        }catch (DataException e){
            EmpresaUtil.showMessage("erro ao carregar dados","Não foi possivel carregar os dados da OS");
        }
    }

    @Override
    public void btnVoltarClick(Event <Object> event){
        close();
    }
}
