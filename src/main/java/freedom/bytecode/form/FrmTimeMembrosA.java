package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;

import freedom.client.event.EventListener;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.util.EmpresaUtil;

import freedom.client.controls.impl.grid.TFGridExporter;

import freedom.client.event.Event;
import freedom.client.controls.IFocusable;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.TableState;
import freedom.util.CastUtil;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;

public class FrmTimeMembrosA extends FrmTimeMembrosW {

    private static final long serialVersionUID = 20130827081850L;

    // INICIO DA CLASSE W

    public TableState oper = TableState.QUERYING;

    protected void habilitaComp(Boolean enabled) {
        gridPrincipal.setEnabled(!enabled);
        btnConsultar.setEnabled(!enabled);
        btnFiltroAvancado.setEnabled(!enabled);
        btnNovo.setEnabled(!enabled && !menuSelecaoMultipla.isChecked());
        btnAlterar.setEnabled(!enabled && !tbTime.isEmpty());
        btnExcluir.setEnabled(!enabled && !tbTime.isEmpty());
        if (! menuHabilitaNavegacao.isChecked()) {                                       // menu popup habilitar navegação
            btnProximo.setEnabled(!enabled && !tbTime.isEmpty());
            btnAnterior.setEnabled(!enabled && !tbTime.isEmpty());
        }
        btnAceitar.setEnabled(!enabled && !tbTime.isEmpty());
        btnCancelar.setEnabled(enabled);
        btnSalvar.setEnabled(enabled);
        btnSalvarContinuar.setEnabled(enabled && !menuSelecaoMultipla.isChecked());
        menuSelecaoMultipla.setVisible(!enabled);

        edIdTime70001.setEnabled(false);
        edDescricao70001.setEnabled(enabled && ( ! tbTime.isEmpty()  || tbTime.getState() == TableState.INSERTING));
        edGrupo70001.setEnabled(enabled && ( ! tbTime.isEmpty()  || tbTime.getState() == TableState.INSERTING));
        edAtivo70001.setEnabled(enabled && ( ! tbTime.isEmpty()  || tbTime.getState() == TableState.INSERTING));
        edMaxDescServ70001.setEnabled(false);

        cmbTemplate.setEnabled(enabled);
        gridMembrosTemplateDisponivel.setEnabled(enabled);
        gridMembrosTemplateCruzado.setEnabled(enabled);
        btnHorariosAdicionarTemplate.setEnabled(enabled);
        btnHorariosRemoverTemplate.setEnabled(enabled);
        btnAlterarTemplate.setEnabled(enabled);

    }

    @Override
    public void btnConsultarClick(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao consultar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnFiltroAvancadoClick(Event<Object> event) {
        filtroAvancado.doModal();
    }

    @Override
    public void btnNovoClick(final Event<Object> event) {
        try {
            onIncluir();
            tbTime.setID_TIME(SequenceUtil.nextVal("SEQ_ID_TIME"));
            enabledMembros(true);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao incluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnAnteriorClick(final Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao voltar para registro anterior")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnProximoClick(final Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao avançar para o próximo registro")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        try {
            onAlterar();
            enabledMembros(true);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao alterar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        try {
            onCancelar();
            enabledMembros(false);
            sincronizarTabelasTbTimeMembroTemplateCruzadoDisponivel();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao cancelar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            if (menuSelecaoMultipla.isChecked()) {
                onSalvarMultiplo();
            } else {
                onSalvar();
            }
            enabledMembros(false);
            sincronizarTabelasTbTimeMembroTemplateCruzadoDisponivel();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao salvar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnSalvarContinuarClick(final Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao salvar e continuar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    public void executaFiltroPrincipal() throws Exception {
        tbTime.clearFilters();

    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void menuHabilitaNavegacaoClick(final Event<Object> event) {
        if (menuHabilitaNavegacao.isChecked()) {
            btnProximo.setEnabled(true);
            btnAnterior.setEnabled(true);
        } else {
            btnProximo.setEnabled(btnNovo.isEnabled());
            btnAnterior.setEnabled(btnNovo.isEnabled());
        }
    }

    @Override
    public void menuSelecaoMultiplaClick(final Event<Object> event) {

        boolean checkedMenu = menuSelecaoMultipla.isChecked();
        gridPrincipal.setMultiSelection(checkedMenu);

        // tratamento das abas visto que pode mexer somente na tabela master
        for (int i = 2; i <= pgPrincipal.getPageCount()-1; i++) {
            pgPrincipal.selectTab(i);
            pgPrincipal.getSelectedTab().setVisible(!checkedMenu);
        }

        // opções da barra de ferramenta
        btnNovo.setEnabled(! checkedMenu && btnAlterar.isEnabled());
        btnSalvarContinuar.setEnabled(! checkedMenu && btnAlterar.isEnabled());


        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()+30);
            // desregistra o marter table dos componentes
            edIdTime70001.setTable(null);
            edIdTime70001.setValue(null);
            edDescricao70001.setTable(null);
            edDescricao70001.setValue(null);
            edGrupo70001.setTable(null);
            edGrupo70001.setValue(null);
            edAtivo70001.setTable(null);
            edAtivo70001.setValue(null);
            edMaxDescServ70001.setTable(null);
            edMaxDescServ70001.setValue(null);

            gridPrincipal.clearSelection();
        } else {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()-30);
            // registra o master table para os componentes
            edIdTime70001.setTable(tbTime);
            edDescricao70001.setTable(tbTime);
            edGrupo70001.setTable(tbTime);
            edAtivo70001.setTable(tbTime);
            edMaxDescServ70001.setTable(tbTime);

        }
        pgPrincipal.selectTab(0);
    }

    @Override
    public void FrmTimeMembroskeyActionPesquisar(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao consultar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmTimeMembroskeyActionIncluir(Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao incluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmTimeMembroskeyActionAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao alterar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmTimeMembroskeyActionExcluir(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmTimeMembroskeyActionSalvar(Event<Object> event) {
        try {
            onSalvar();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao salvar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmTimeMembroskeyActionSalvarContinuar(Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao salvar e continuar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmTimeMembroskeyActionCancelar(Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao cancelar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmTimeMembroskeyActionAnterior(Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao voltar para registro anterior")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmTimeMembroskeyActionProximo(Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao avançar para o próximo registro")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmTimeMembroskeyActionAceitar(Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnMaisClick(Event<Object> event) {
        popMenuPrincipal.open(this);
    }

    @Override
    public void menuItemAbreTabelaAuxClick(Event<Object> event) {
        try {
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                    .title("Aviso")
                    .message("Tabela Auxiliares Foram Reabertas")
                    .showInformation();
        }
    }

    @Override
    public void menuItemConfgGridClick(Event<Object> event) {
        gridConfig.doModal();
    }

    @Override
    public void menuItemHelpClick(Event<Object> event) {
        FormUtil.redirect("help/FrmTimeMembros.zul", true);
    }

    protected void onConsultar() throws Exception {

        tbTime.close();
        executaFiltroPrincipal();
        tbTime.setOrderBy("DESCRICAO");
        tbTime.open();
        habilitaComp(false);

        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.clearSelection();
        }

        if (tbTime.isEmpty()) {
            Dialog.create()
                    .title("Aviso")
                    .message("Registro Não Encontrado...")
                    .showInformation();
        }
    }

    protected void onAnterior() throws Exception {

        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbTime.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnAnterior);
        }

        if (tbTime.bof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o Primeiro Registro")
                    .showInformation();
        } else {
            tbTime.prior();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onProximo() throws Exception {
        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbTime.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnProximo);
        }

        if (tbTime.eof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o último registgro")
                    .showInformation();
        } else {
            tbTime.next();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onIncluir() throws Exception {
        oper = TableState.INSERTING;

        rn.incluir();


        pgPrincipal.selectTab(1);
        edDescricao70001.setFocus();
        habilitaComp(true);
        lblMensagem.setCaption("Incluindo...");
    }

    protected void onAlterar() throws Exception {
        oper = TableState.MODIFYING;

        if (menuSelecaoMultipla.isChecked()) {
            lblMensagem.setCaption("ATENÇÃO: Alterando multiplos registros. Será alterado todos os registros selecionados...");
            pgPrincipal.selectTab(1);
            habilitaComp(true);
        } else {
            if (!tbTime.isEmpty()) {
                rn.alterar();
                if (pgPrincipal.getSelectedIndex() == 0)  {
                    pgPrincipal.selectTab(1);
                }
                habilitaComp(true);
                edDescricao70001.setFocus();
                lblMensagem.setCaption("Alterando "+tbTime.getID_TIME().asString() + ", " + tbTime.getDESCRICAO().asString()+"...");
            } else {
                Dialog.create()
                        .title("Erro ao editar")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        }
    }

    protected void onExcluir() throws DataException {
        if (!tbTime.isEmpty()) {
            oper = TableState.DELETING;
            String titulo;
            String mensagem;
            if (menuSelecaoMultipla.isChecked()) {
                titulo = "Exclusão Multipla";
                mensagem = "ATENÇÃO: Serão excluido(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?";
            } else {
                titulo = "Exclusão de Registro";
                mensagem = "Confirma a exclusão do registro selecionado?";
            }

            Dialog.create()
                    .title(titulo)
                    .message(mensagem)
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            try {
                                try {
                                    tbTime.disableControls();
                                    tbTime.disableMasterTable();
                                    if (menuSelecaoMultipla.isChecked()) {
                                        for (int bm : gridPrincipal.getSelectedIndices(false)) {
                                            tbTime.gotoBookmark(bm);
                                            rn.excluiTableMaster();
                                        }
                                    } else {
                                        rn.excluiTableMaster();
                                    }

                                    try {
                                        rn.excluir();

                                        habilitaComp(false);
                                    } catch (DataException e) {
                                        throw e;
                                    }
                                } finally {
                                    tbTime.enableControls();
                                    tbTime.enableMasterTable();
                                    oper = TableState.QUERYING;
                                }
                            } catch (DataException ex) {
                                Dialog.create()
                                        .title("Erro ao excluir")
                                        .message(ex.getMessage())
                                        .showException(ex);

                                try {
                                    tbTime.cancelUpdates();
                                } catch (DataException ex1) {
                                    Dialog.create()
                                            .title("Erro no CancelUpdates ao excluir")
                                            .message(ex.getMessage())
                                            .showException(ex1);
                                }

                            }
                        }
                    });
        } else {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message("Selecione um registro antes de excluir")
                    .showError();
        }
    }

    protected void onSalvarMultiplo() throws DataException {

        Dialog.create()
                .title("Alteração Multipla")
                .message("ATENÇÃO: Serão alterado(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {

                        try {
                            tbTime.disableControls();
                            int lastBookmark = tbTime.getBookmark();
                            try {
                                for (int bm : gridPrincipal.getSelectedIndices(true)) {
                                    tbTime.gotoBookmark(bm);
                                    tbTime.edit();

                                    if ( ! edDescricao70001.getValue().isNull()) {
                                        tbTime.setDESCRICAO(edDescricao70001.getValue());
                                    }
                                    if ( ! edGrupo70001.getValue().isNull()) {
                                        tbTime.setGRUPO(edGrupo70001.getValue());
                                    }
                                    if ( ! edAtivo70001.getValue().isNull()) {
                                        tbTime.setATIVO(edAtivo70001.getValue());
                                    }

                                    tbTime.post();
                                }

                                onSalvar();

                            } finally {
                                tbTime.close();
                                tbTime.open();
                                // tbTime.gotoBookmark(lastBookmark);
                                tbTime.enableControls();
                                gridPrincipal.clearSelection();
                            }

                        } catch (DataException e) {
                            Dialog.create()
                                    .title("Erro ao salvar")
                                    .message(e.getMessage())
                                    .showException(e);
                        }
                    }
                });
    }

    protected void onSalvar() throws DataException {
        // executa a validação das constraint dos objetos edition
        check();
        if (!getErrorMap().isEmpty()) {
            StringBuilder strBuilder = new StringBuilder();

            getErrorMap().values().stream().forEach((s) -> {
                strBuilder.append(s).append("\n");
            });

            // manda o focu para o primeiro objeto que deu erro de constraint
            ((IFocusable)getErrorMap().keySet().iterator().next()).setFocus();

            Dialog.create()
                    .title("Erro ao validar")
                    .message("Existe validação(s) pendente...\n" + strBuilder.toString())
                    .showError();

            return;
        }

        // seta Calc. Update
        setCalcUpdate();

        // executar o metodo salvar na RN
        rn.salvar();

        // atualiza o registro
        tbTime.refreshRecord();

        habilitaComp(false);
        oper = TableState.QUERYING;
        lblMensagem.setCaption("");
    }

    protected void onSalvarContinuar() throws DataException {
        try {
            TableState st = tbTime.getState();
            onSalvar();
            if (st == TableState.INSERTING) {
                onIncluir();
            } else if (st == TableState.MODIFYING) {
                onAlterar();
            }
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao salvar e continuar a edição")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    protected void onCancelar() throws DataException {
        habilitaComp(false);
        rn.cancelar();

        oper = TableState.QUERYING;
        lblMensagem.setCaption("Registro Selecionado: "+tbTime.getID_TIME().asString() + ", " + tbTime.getDESCRICAO().asString());
    }

    protected void onAceitar() throws Exception {
        if (FormUtil.isExternalCall()) {
            // passa os parametros para a resposta ao VB
            FormUtil.externalCall(tbTime.getField("ID_PESSOA").asString());
        } else {
            close();
        }
    }

    protected void onAbreTabelaAux() throws DataException {
        ISession s = SessionFactory.getInstance().getSession();
        try {
            s.open();
            tbTimeMembro.setOrderBy("NOME");
            tbTime.setSession(s);
            tbTime.refreshRecord();
            tbTime.setSession(null);
        } finally {
            if (s != null) {
                s.close();
            }
        }
    }

    protected void setCalcUpdate() throws DataException {

        postTable();
    }

    private void postTable() throws DataException {
        tbTime.post();
        tbTimeMembro.post();
    }

    public void loadFormPk(Integer idTime ) throws DataException {
        tbTime.close();
        tbTime.clearFilters();

        if (idTime > 0) {
            tbTime.addFilter("ID_TIME");
            tbTime.addParam("ID_TIME", idTime);
        } else return;

        tbTime.open();
        habilitaComp(false);          // se tem registro habilita botões da barra de ferramenta
    }

    // retorna true se o master esta sendo editado, pode ser usado para verificar se o form esta
    // habilitado edição
    public boolean masterIsEditing() {
        return (tbTime.getState() != TableState.QUERYING);
    }


    @Override
    public void tbTimeAfterScroll(final Event<Object> event) {
        lblMensagem.setCaption("Selecionado: "+tbTime.getID_TIME().asString() + ", " + tbTime.getDESCRICAO().asString());
        filtrarAgentesDisponiveis();
        filtrarTimeTemplate();
    }




    @Override
    public void gridPrincipalClickImageDelete(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void gridPrincipalClickImageAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao alterar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void menuItemExportPdfClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportPdf(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void menuItemExportExcelClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportExcel(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }




    protected final void habilitaComp43001(Boolean enabled) {


    }


    public void onIncluir43001() {
        try {
            rn.incluir43001();
            habilitaComp43001(true);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao adicionar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onAlterar43001() {
        try {
            if (!tbTimeMembro.isEmpty()) {
                rn.alterar43001();

                habilitaComp(true);
            } else {
                Dialog.create()
                        .title("Erro ao editar detalhe")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao editar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onExcluir43001() {
        try {
            if (!tbTimeMembro.isEmpty()) {

                rn.excluir43001();
            } else {
                Dialog.create()
                        .title("Erro ao excluir detalhe")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao editar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onCancelar43001() {
        try {
            rn.cancelar43001();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao cancelar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    public void onConfirmar43001() {
        try {
            rn.confirmar43001();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao confirmar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    // FIM DA CLASSE W

    public FrmTimeMembrosA() {
        lblMensagem.setCaption("");
        habilitaComp(false);
        tabListagem.setFontSize(-15);
        tabCadastro.setFontSize(-15);
        tabSheetMembros.setFontSize(-15);
        tabHorarios.setFontSize(-15);

        try {
            filtrarTabelasAux();
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao Abrir Tabelas Auxiliares")
                    .message(e.getMessage())
                    .showException(e);
        }

    }


    @Override
    public void edIdTime70001Exit(final Event<Object> event) {

    }

    private void filtrarTabelasAux() {
        try {
            rn.filtrarEmpresas();
            rn.filtrarEmpresasFuncoes();

            cbbEmpresa.setValue(EmpresaUtil.getCodEmpresaUserLogged());
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro ao filtrar tabelas auxiliares.", ex);
        }
    }

    private void filtrarAgentesDisponiveis() {
        try {
            int codEmpresa = cbbEmpresa.getValue().asInteger();
            int codFuncao = cbbEmpresasFuncoes.getValue().asInteger();

            rn.filtrarAgentesDisponiveis(codEmpresa, codFuncao);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro ao filtrar agentes disponíveis.", ex);
        }
    }



    @Override
    public void cbbEmpresaChange(Event<Object> event) {
        filtrarAgentesDisponiveis();
    }

    @Override
    public void cbbEmpresaClearClick(Event<Object> event) {
        filtrarAgentesDisponiveis();
    }

    @Override
    public void cbbEmpresasFuncoesChange(Event<Object> event) {
        filtrarAgentesDisponiveis();
    }

    @Override
    public void cbbEmpresasFuncoesClearClick(Event<Object> event) {
        filtrarAgentesDisponiveis();
    }

    @Override
    public void tbTimeMembroBeforePost(Event<Object> event) {
        try {
            if (tbTimeMembro.getID_TIME().asInteger() == 0) {
                tbTimeMembro.edit();
                tbTimeMembro.setID_TIME(tbTime.getID_TIME().asInteger());
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro ao setar Id. Time.", ex);
        }
    }

    private void enabledMembros(boolean enabled) {
        cbbEmpresa.setEnabled(enabled);
        cbbEmpresasFuncoes.setEnabled(enabled);
        DualListMembros.setEnabled(enabled);
    }

    /**
     * filtra os times_templates disponíveis
     */
    private void filtrarTimeTemplate(){
        try {
            rn.filtrarTimeTemplate();
            int idTemplate = rn.getIDTemplateCombo();
            cmbTemplate.setValue(idTemplate);
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao filtrar templates horarios.", e);
        }
    }

    /**
     * Sincroniza as tabelas (TbTimeMembroTemplateDisponivel ,TbTimeMembroTemplateCruzado) com a tabela (tbTimeMembros)
     * essa logica foi aplicada para manter os dados sincronizados entre essas tabelas, tendo a tabela
     * tbTimesMembros como a principal, pois as demais são apenas para preencher a grids e ajudar no controle dos ids_templates
     */
    public void sincronizarTabelasTbTimeMembroTemplateCruzadoDisponivel(){
        try {
            rn.sincronizarTbTimeMembroTemplateCruzado();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao filtrar templates horarios cruzados.", e);
        }

        try {
            rn.sincronizarTbTimeMembroTemplateDisponivel();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao filtrar templates horarios disponiveis.", e);
        }
    }

    /**
     * sincroniza as tabelas tbTimeMembroTemplateDisponivel e tbTimeMembroTemplateCruzado
     * com a tabela tbTimeMembro, ao trocar para aba Horarios.
     * @param event
     */
    @Override
    public void pgPrincipalChange(Event<Object> event) {
        if (pgPrincipal.getSelectedTab() == tabHorarios) {
            sincronizarTabelasTbTimeMembroTemplateCruzadoDisponivel();
        }
    }

    /**
     * Adiciona um idTemplate ao time_membro
     * @param event
     */
    @Override
    public void btnHorariosAdicionarTemplateClick(Event<Object> event) {
        if(cmbTemplate.getValue().isNull()){
            Dialog.create()
                .title("Validação")
                .message("Selecione um template")
                .showInformation(t -> {
                    cmbTemplate.setFocus();
                    cmbTemplate.setOpen(true);
            });
            return;
        }
        if (rn.isEmptyTbTimeMembroTemplateDisponivel()){
            Dialog.create()
                    .title("Validação")
                    .message("Nenhum membro sem template selecionado")
                    .showInformation(t -> {
                    });
            return;
        }
        try {
            int idTemplate = cmbTemplate.getValue().asInteger();
            String descricaoTemplate = cmbTemplate.getText();
            String nomeMembro = rn.getNomeTbTimeMembroTemplateDisponivel();
            rn.setIdTemplateTbTimeMembro(idTemplate, descricaoTemplate, nomeMembro);
            sincronizarTabelasTbTimeMembroTemplateCruzadoDisponivel();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao adicionar Template ao membro.", e);
        }
    }

    /**
     * Remove um id_template de um time membro
     * @param event
     */
    @Override
    public void btnHorariosRemoverTemplateClick(Event<Object> event) {
        try {
            if (rn.isEmptyTbTimeMembroTemplateCruzado()){
                Dialog.create()
                    .title("Validação")
                    .message("Nenhum membro com template selecionado")
                    .showInformation(t -> {
                });
                return;
            }
            String nomeMembro = rn.getNomeTbTimeMembroTemplateCruzado();
            rn.setIdTemplateTbTimeMembro(null, "", nomeMembro);
            sincronizarTabelasTbTimeMembroTemplateCruzadoDisponivel();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao remover Template ao membro.", e);
        }
    }

    /**
     * Abre modal da tela de templates de horarios para incluir ou editar um template
     */
    @Override
    public void btnAlterarTemplateClick(Event<Object> event) {
        FrmTimeTemplateHorariosA frmTemplate = new FrmTimeTemplateHorariosA();
        int idTemplateCombo = cmbTemplate.getValue().asInteger();
        frmTemplate.ajustarTelaMarcarTemplateEspecifico(idTemplateCombo);
        FormUtil.doModal(frmTemplate, (EventListener) t -> {
            int idTemplateSelecionadoNoForm = frmTemplate.getIDTemplate();
            filtrarTimeTemplate();
            cmbTemplate.setValue(idTemplateSelecionadoNoForm);
        });
    }


    /**
     * Abre modal da tela de templates de horarios para vizualizar os horarios
     * @param event
     */
    @Override
    public void gridMembrosTemplateCruzadoColumns1Click(Event<Object> event) {
        FrmTimeTemplateHorariosA frmTemplate = new FrmTimeTemplateHorariosA();
        int idTemplateCruzado = tbTimeMembroTemplateCruzado.getID_TEMPLATE().asInteger();
        frmTemplate.ajustarTelaSomentevisualizarTemplateHorario(idTemplateCruzado);
        FormUtil.doModal(frmTemplate, (EventListener) t -> {
        });

    }

    /**
     * adicionar template ao membro selecionado ao dar duplo clique no nome na grid templateCruzado remove
     * @param event
     */
    @Override
    public void gridMembrosTemplateDisponivelColumns0DoubleClick(Event<Object> event) {
        btnHorariosAdicionarTemplateClick(null);
    }

    /**
     * remover template do membro ao dar duplo clique no nome na grid templateCruzado remove
     * @param event
     */
    @Override
    public void gridMembrosTemplateCruzadoColumns0DoubleClick(Event<Object> event) {
        btnHorariosRemoverTemplateClick(null);
    }
}
