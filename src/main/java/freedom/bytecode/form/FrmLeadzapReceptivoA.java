package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmLeadzapReceptivoW;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.data.impl.View;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;
import org.zkoss.zk.ui.HtmlBasedComponent;
import org.zkoss.zul.Tabbox;

import java.util.ArrayList;
import java.util.List;

public class FrmLeadzapReceptivoA extends FrmLeadzapReceptivoW {

    private static final long serialVersionUID = 20130827081850L;

    private int idMenu;

    private int idItem;

    private boolean isEdicao = false;

    private boolean obrigatorioAtendimento = true;

    private boolean obrigatorioTipoEvento = true;

    private final PkgCrmPartsRNA pkCrmPartsRna = new PkgCrmPartsRNA();

    private final int codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();

    public FrmLeadzapReceptivoA() {
        ((Tabbox) pgItem.getImpl()).getTabs().getChildren().forEach((t) -> {
            ((HtmlBasedComponent) t).setStyle("font-size: 15px");
        });
        enabledComponentes(false);
        carregaTemplateQualificado();
        carregaTemplateLead();
        carregaTemplateLeadAprov();
        carregaTemplateLeadReprov();
        carregaLeadArea();
        carregaTime();
        carregaTipoEvento();
    }

    private void enabledComponentes(Boolean isEnabled) {
        // Botoões
        isEdicao = isEnabled;
        btnConsultar.setEnabled(!isEnabled);
        btnNovo.setEnabled(!isEnabled);
        btnAlterar.setEnabled(!isEnabled);
        btnSalvar.setEnabled(isEnabled);
        btnCancelar.setEnabled(isEnabled);
        gridPrincipal.setEnabled(!isEnabled);
        // Cadastro
        edtDescricao.setEnabled(isEnabled);
        chkAtivo.setEnabled(isEnabled);
        cbbTemplateQualificado.setEnabled(isEnabled);
        chkMostraNome.setEnabled(isEnabled);
        chkMostraEmail.setEnabled(isEnabled);
        chkCadDuplo.setEnabled(isEnabled);
        cbbTemplateLead.setEnabled(isEnabled);
        cbbTemplateVendedor.setEnabled(isEnabled);
        cbbTemplateVendedorAprov.setEnabled(isEnabled);
        cbbTemplateVendedorReprov.setEnabled(isEnabled);
    }

    private void enabledComponentesItem(boolean isEnabled) {
        // cadastro itens do menu
        if (isEdicao) {
            btnNovoItem.setEnabled(!isEnabled);
            btnAlterarItem.setEnabled(!isEnabled);
            btnExcluirItem.setEnabled(!isEnabled);
            btnSalvarItem.setEnabled(isEnabled);
            btnCancelarItem.setEnabled(isEnabled);
            gridItensMenu.setEnabled(!isEnabled);
        } else {
            btnNovoItem.setEnabled(isEdicao);
            btnAlterarItem.setEnabled(isEdicao);
            btnExcluirItem.setEnabled(isEdicao);
            btnSalvarItem.setEnabled(isEdicao);
            btnCancelarItem.setEnabled(isEdicao);
            gridItensMenu.setEnabled(!isEdicao);
        }
        edtDescricaoItem.setEnabled(isEnabled);
        edtSequencia.setEnabled(isEnabled);
        cbbArea.setEnabled(isEnabled);
        cbbTime.setEnabled(isEnabled);
        cbbTipoEvento.setEnabled(isEnabled);
        chkUltimoAgenteEvento.setEnabled(isEnabled);
        chkEventoPerdido.setEnabled(isEnabled);
        chkCentralUnica.setEnabled(isEnabled);
        cbbIdTemplateBoasVIndasVendedor.setEnabled(isEnabled);
        cbbIdTemplateBoasVIndasConsultor.setEnabled(isEnabled);
        cbbIdTemplateJaExisteAtendimentoVendedor.setEnabled(isEnabled);
        cbbIdTemplateJaExisteAtendimentoConsultor.setEnabled(isEnabled);
    }

    public void carregaDados() {
        try {
            rn.carregaDados();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados: ", ex);
        }
    }

    public void carregaTemplateQualificado() {
        try {
            rn.carregaTemplateQualificado(tbLeadzapMenu.getID_TEMPLATE_QUALIFICADO().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Template Qualidicado: ", ex);
        }
    }

    public void carregaTemplateLead() {
        try {
            rn.carregaTemplateLead(tbLeadzapMenu.getID_TEMPLATE_LEAD().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Template Lead: ", ex);
        }
    }

    @Override
    public void btnConsultarClick(final Event event) {
        carregaDados();
    }

    @Override
    public void btnNovoClick(final Event event) {
        try {
            tbLeadzapMenu.append();
            tbLeadzapMenu.setID_MENU(SequenceUtil.nextVal("SEQ_CRM_LEADZAP_MENU"));
            idMenu = tbLeadzapMenu.getID_MENU().asInteger();
            tbLeadzapMenu.setATIVO("S");
            tbLeadzapMenu.setLEAD_PERGUNTA_EMAIL("N");
            tbLeadzapMenu.setLEAD_PERGUNTA_NOME("N");
            tbLeadzapMenu.setCAD_DUPLO_MOSTRA_AO_CLIENTE("N");
            enabledComponentes(true);
            pgChatbot.selectTab(1);
            edtDescricao.setFocus();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao iniciar Novo", e);
        }
    }

    @Override
    public void btnAlterarClick(final Event event) {
        try {
            if (tbLeadzapMenu.isEmpty()) {
                Dialog.create().title("CrmService").message("Obrigatório Selecionar um Registro a ser Alterado.").showInformation(((EventListener) event1 -> {
                    enabledComponentes(false);
                    pgChatbot.selectTab(0);
                    return;
                }));
            }
            idMenu = tbLeadzapMenu.getID_MENU().asInteger();
            tbLeadzapMenu.edit();
            enabledComponentes(true);
            pgChatbot.selectTab(1);
            edtDescricao.setFocus();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao Editar Registro", e);
        }
    }

    @Override
    public void btnSalvarClick(final Event event) {
        try {
            if (validaCampos()) {
                tbLeadzapMenu.post();
                tbLeadzapMenu.applyUpdates();
                tbLeadzapItem.applyUpdates();
                tbLeadzapMenu.commitUpdates();
                tbLeadzapMenu.commitUpdates();
                enabledComponentes(false);
                pgChatbot.selectTab(0);
                carregaDados();
                tbLeadzapMenu.locate("ID_MENU", idMenu);
                rn.filtrarCadastroWhatsApp(idMenu);
                if (tbCadastroWhatsapp.count() == 0) {
                    EmpresaUtil.showInformationMessage("Lembrete: Por favor não se esqueça de vincular o menu ao Cadastro de WhatsApp.");
                } else {
                    rn.filtrarWhatsAppEmpresa(tbCadastroWhatsapp.getID_CELULAR().asInteger());
                    //Aqui verificamos as empresas dos usuários que não foram vinculadas ao cadastro de whatsapp
                    tbLeadzapItem.first();
                    Integer codEmpresa;
                    String empresas = "";
                    List<String> listEmps = new ArrayList<>();
                    while (!tbLeadzapItem.eof()) {
                        if (tbLeadzapItem.getID_TIME().asInteger() > 0) {
                            rn.filtrarTimeEmpresa(tbLeadzapItem.getID_TIME().asInteger());
                            tbReceptTimesMembrosEmpUser.first();
                            while (!tbReceptTimesMembrosEmpUser.eof()) {
                                codEmpresa = tbReceptTimesMembrosEmpUser.getCOD_EMPRESA().asInteger();
                                if (!tbWhatsappEmpresa.locate("COD_EMPRESA", codEmpresa)) {
                                    if (listEmps.indexOf(String.valueOf(codEmpresa)) == -1) {
                                        if (!empresas.equals("")) {
                                            empresas = empresas + ", ";
                                        }
                                        listEmps.add(String.valueOf(codEmpresa));
                                        empresas = empresas + codEmpresa;
                                    }
                                }
                                tbReceptTimesMembrosEmpUser.next();
                            }
                        }
                        tbLeadzapItem.next();
                    }

                    String msg = "";
                    if (!empresas.equals("")) {
                        msg = "1 - Atenção verificamos que os usuários dos times possuem as seguintes empresas ("
                                + empresas + ") mas elas não estão vinculadas ao Cadastro do WhatsApp.";
                    }
                    //Aqui verificamos as empresas do cadastro whatsapp e olhamos se os times faltam alguma empresa.
                    String empresas2 = "";
                    String key = "";
                    listEmps.clear();
                    tbWhatsappEmpresa.first();
                    View v = tbTime.getView();
                    while (!tbWhatsappEmpresa.eof()) {
                        codEmpresa = tbWhatsappEmpresa.getCOD_EMPRESA().asInteger();
                        tbLeadzapItem.first();
                        while (!tbLeadzapItem.eof()) {
                            if (tbLeadzapItem.getID_TIME().asInteger() > 0) {
                                rn.filtrarTimeEmpresa(tbLeadzapItem.getID_TIME().asInteger());
                                key = String.valueOf(codEmpresa) + "." + tbLeadzapItem.getID_TIME().asString();
                                if (!tbReceptTimesMembrosEmpUser.locate("COD_EMPRESA", codEmpresa)) {
                                    if (listEmps.indexOf(key) == -1) {
                                        if (!empresas2.equals("")) {
                                            empresas2 = empresas2 + "\n";
                                        }
                                        listEmps.add(key);
                                        v.locate("ID_TIME", tbLeadzapItem.getID_TIME().asInteger());
                                        empresas2 = empresas2 + "Falta um usuário nesse Time: ["
                                                + v.getField("DESCRICAO").asString() + "] que atenda a empresa [" + codEmpresa + "]";
                                    }
                                }
                            }
                            tbLeadzapItem.next();
                        }
                        tbWhatsappEmpresa.next();
                    }

                    if (!empresas2.equals("")) {
                        if (!msg.equals("")) {
                            msg = msg + "\n\n";
                            msg = msg + "2 - ";
                        } else {
                            msg = msg + "1 - ";
                        }
                        msg = msg + "Atenção verifique os seguintes times, pois falta pelo menos um usuário " +
                                "responsável por atender as empresas listadas abaixo. \n" + empresas2 + "\n" +
                                "Essas empresas acima estão vinculadas ao Cadastro de WhatsApp.";
                    }

                    if (!msg.equals("")) {
                        EmpresaUtil.showWarning("Atenção", msg);
                    }
                }

            }
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao tentar salvar.", e);
        }
    }

    @Override
    public void btnCancelarClick(final Event event) {
        try {
            tbLeadzapMenu.cancel();
            enabledComponentes(false);
            pgChatbot.selectTab(0);
            carregaDados();
            tbLeadzapMenu.locate("ID_MENU", idMenu);
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao Cancelar", e);
        }
    }

    public boolean validaCampos() {
        boolean isValido = true;
        if (edtDescricao.getValue().asString().equals("")) {
            Dialog.create().title("CrmService").message("Campo Descrição é obrigatório.").showInformation(((EventListener) event1 -> {
                pgChatbot.selectTab(1);
                edtDescricao.setFocus();
            }));
            return false;
        }
        if (cbbTemplateQualificado.getValue().asInteger() <= 0) {
            Dialog.create().title("CrmService").message("Campo Template é obrigatório.").showInformation(((EventListener) event1 -> {
                pgChatbot.selectTab(1);
                cbbTemplateQualificado.setFocus();
            }));
            return false;
        }
        if (cbbTemplateLead.getValue().asInteger() <= 0) {
            Dialog.create().title("CrmService").message("Campo Template é obrigatório.").showInformation(((EventListener) event1 -> {
                pgChatbot.selectTab(1);
                cbbTemplateLead.setFocus();
            }));
            return false;
        }
        if (tbLeadzapItem.count() == 0) {
            Dialog.create().title("CrmService").message("É obrigatório informar pelo menos um item.").showInformation(((EventListener) event1 -> {
                pgChatbot.selectTab(2);
            }));
            return false;
        }
        return isValido;
    }

    @Override
    public void btnNovoItemClick(final Event event) {
        try {
            tbLeadzapItem.append();
            tbLeadzapItem.setID_ITEM(SequenceUtil.nextVal("SEQ_CRM_LEADZAP_ITEM"));
            idItem = tbLeadzapItem.getID_ITEM().asInteger();
            tbLeadzapItem.setID_MENU(idMenu);
            tbLeadzapItem.setPREF_ULT_AGENTE("N");
            tbLeadzapItem.setEVENTO_PERDIDO_REABRE("N");
            enabledComponentesItem(true);
            edtSequencia.setFocus();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao iniciar Novo", e);
        }
    }

    @Override
    public void btnAlterarItemClick(final Event event) {
        try {
            if (tbLeadzapItem.isEmpty()) {
                Dialog.create().title("CrmService").message("Obrigatório Selecionar um Registro a ser Alterado.").showInformation(((EventListener) event1 -> {
                    enabledComponentesItem(false);
                    return;
                }));
            }
            idItem = tbLeadzapItem.getID_ITEM().asInteger();
            tbLeadzapItem.edit();
            enabledComponentesItem(true);
            edtSequencia.setFocus();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao Editar Registro", e);
        }
    }

    @Override
    public void btnExcluirItemClick(final Event event) {
        try {
            tbLeadzapItem.delete();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao Exclur item.", e);
        }
    }

    @Override
    public void btnSalvarItemClick(final Event event) {
        try {
            if (validaCamposItem()) {
                tbLeadzapItem.post();
                enabledComponentesItem(false);
                tbLeadzapItem.locate("ID_ITEM", idItem);
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao tentar salvar.", e);
        }
    }

    @Override
    public void btnCancelarItemClick(final Event event) {
        try {
            tbLeadzapItem.cancel();
            enabledComponentesItem(false);
            carregaDadosItem();
            tbLeadzapItem.locate("ID_ITEM", idItem);
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao Cancelar", e);
        }
    }

    public boolean validaCamposItem() {
        boolean isValido = true;
        if (edtSequencia.getValue().asInteger() == 0) {
            Dialog.create().title("CrmService").message("Campo Sequência é obrigatório.").showInformation(((EventListener) event1 -> {
                edtSequencia.setFocus();
            }));
            return false;
        }
        if (edtDescricaoItem.getValue().asString().equals("")) {
            Dialog.create().title("CrmService").message("Campo Descrição é obrigatório.").showInformation(((EventListener) event1 -> {
                edtDescricaoItem.setFocus();
            }));
            return false;
        }
        if (cbbArea.getValue().asInteger() <= 0) {
            Dialog.create().title("CrmService").message("Campo Area é obrigatório.").showInformation(((EventListener) event1 -> {
                cbbArea.setFocus();
            }));
            return false;
        }
        if (cbbTipoEvento.getValue().asInteger() <= 0 && obrigatorioTipoEvento) {
            Dialog.create().title("CrmService").message("Campo Tipo Evento é obrigatório.").showInformation(((EventListener) event1 -> {
                cbbTipoEvento.setFocus();
                System.out.println("Vai caralho");
            }));
            return false;
        }
        if (cbbTime.getValue().asInteger() <= 0 && obrigatorioAtendimento) {
            Dialog.create().title("CrmService").message("Campo Time Evento é obrigatório.").showInformation(((EventListener) event1 -> {
                cbbTime.setFocus();
            }));
            return false;
        }
        if (!validaMembroTime()) {
            Dialog.create().title("CrmService").message("Time Selecionado não contém Membros, favor Verique!").showInformation(((EventListener) event1 -> {
                cbbTime.setFocus();
            }));
            return false;
        }
        if (cbbArea.getValue().asInteger() == 12 && cbbTime.getValue().asInteger() > 0) {
            // Consultar Peças
            if (!validaSetorUsuario()) {
                return false;
            }
        }
        return isValido;
    }

    public void carregaDadosItem() {
        try {
            rn.carregaDadosItem(tbLeadzapMenu.getID_MENU().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados do Item: ", ex);
        }
    }

    public void carregaLeadArea() {
        try {
            rn.carregaLeadArea();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados do Item: ", ex);
        }
    }

    public void carregaTime() {
        try {
            rn.carregaTime();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados do Item: ", ex);
        }
    }

    @Override
    public void cbbIdTemplateJaExisteAtendimentoVendedorChange(Event <Object>event){
        if (!validaMembroTime()) {
            Dialog.create().title("CrmService").message("Time Selecionado não contém Membros, favor Verique!").showInformation(((EventListener) event1 -> {
                cbbTime.setFocus();
            }));
        }
    }

    /**
     * Seta null nos combobox que não tem função de acordo com a cod_area
      * @param area
     */
    public void LimparCombosPorArea(Integer area){
        try {
            //
            if ((area >= 1 && area <= 5) || (area == 7 )){
                //1 a 5 e 7 - não possui template Atendente/Consultor
                tbLeadzapItem.setID_TEMPATE_ATEND_CONTATO(null);
                tbLeadzapItem.setID_TEMPATE_ATEND_EXIST_ATD(null);

            } else if(area == 6 || area == 10) {
                // 6 e 10 - não possui template Vendedor
                tbLeadzapItem.setID_TEMPATE_VEND_CONTATO(null);
                tbLeadzapItem.setID_TEMPATE_VEND_EXIST_ATD(null);
            } else{
                // 9, 11 e 12 - não possui template Vendedor, Atendente/Consultor
                tbLeadzapItem.setID_TEMPATE_VEND_CONTATO(null);
                tbLeadzapItem.setID_TEMPATE_VEND_EXIST_ATD(null);
                tbLeadzapItem.setID_TEMPATE_ATEND_CONTATO(null);
                tbLeadzapItem.setID_TEMPATE_ATEND_EXIST_ATD(null);
            }
            if (area == 11 || area == 9) {
                // não possui time nem tipo evento
                tbLeadzapItem.setID_TIME(null);
                tbLeadzapItem.setCOD_TIPO_EVENTO(null);
            }

        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao limpar campos", e);
        }
    }
    /**
     * Esconde os combobox e componentes de acordo com o cod_area
     * @param area
     */
    public void tornarComponentesVisivelPorArea(Integer area){
        // 9 - Consulta de Os - Atendimento e Tipo Evento off
        // 11 - //Peças - Consultar Orçamento  -- Atendimento e Tipo Evento off
        if (area == 11 || area == 9) {
            lblTipoEvento.setVisible(false);
            cbbTipoEvento.setVisible(false);
            //cbbTipoEvento.setValue(null);
            groupBoxAtendimento.setVisible(false);
            //cbbTime.setValue(null);
            obrigatorioTipoEvento = false;
            obrigatorioAtendimento = false;

        } else {
            lblTipoEvento.setVisible(true);
            cbbTipoEvento.setVisible(true);
            groupBoxAtendimento.setVisible(true);
            obrigatorioTipoEvento = true;
            obrigatorioAtendimento = true;
        }

        if ((area >= 1 && area <= 5) || (area == 7 )){
            // 1 a 5 e 7 - mostrar template boas vindas vendedor
            pgItemtabTemplates.setVisible(true);
            FVBoxPrincipalTemplatesPgItemItem1.setVisible(true);
            FVBoxPrincipalTemplatesPgItemItem3.setVisible(true);
            FVBoxPrincipalTemplatesPgItemItem2.setVisible(false);
            FVBoxPrincipalTemplatesPgItemItem4.setVisible(false);
        } else if(cbbArea.getValue().asInteger() == 6 || cbbArea.getValue().asInteger() == 10){
            // 6 e 10 - mostrar template boas vindas Atendente/Consultor
            pgItemtabTemplates.setVisible(true);
            FVBoxPrincipalTemplatesPgItemItem2.setVisible(true);
            FVBoxPrincipalTemplatesPgItemItem4.setVisible(true);
            FVBoxPrincipalTemplatesPgItemItem1.setVisible(false);
            FVBoxPrincipalTemplatesPgItemItem3.setVisible(false);
        } else {
            // 9, 11 e 12 - esconder todos os campos
            pgItem.selectTab(pgItemtabCadastro);
            pgItemtabTemplates.setVisible(false);
        }

    }

    public void carregaTipoEvento() {
        try {
            int idGrupo = 0;
            if (cbbArea.getValue().asInteger() == 7) {
                // Peças - Falar com Atendente
                idGrupo = 9;
            }
            if (cbbArea.getText().contains("Pós Vendas")) {
                idGrupo = 1;
            }
            rn.carregaTipoEvento(idGrupo);
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados do Item: ", ex);
        }
    }

    @Override
    public void pgChatbotChange(Event<Object> event) {
        if (pgChatbot.getSelectedIndex() == 2) {
            enabledComponentesItem(false);
        }
    }

    @Override
    public void tbLeadzapMenuAfterScroll(final Event<Object> event) {
        carregaDadosItem();
    }

    @Override
    public void cbbAreaChange(final Event<Object> event) {
        // 
        //carregaTipoEvento();
        Integer codArea= cbbArea.getValue().asInteger();
        LimparCombosPorArea(codArea);
        tornarComponentesVisivelPorArea(codArea);
        carregaTipoEvento();
    }

    @Override
    public void tbLeadzapItemAfterScroll(final Event<Object> event) {
        tornarComponentesVisivelPorArea(cbbArea.getValue().asInteger());
        carregaTipoEvento();
    }

    @Override
    public void cbbTimeChange(final Event<Object> event) {
        if (!validaMembroTime()) {
            Dialog.create().title("CrmService").message("Time Selecionado não contém Membros, favor Verique!").showInformation(((EventListener) event1 -> {
                cbbTime.setFocus();
            }));
        }
    }

    public boolean validaMembroTime() {
        try {
            if (cbbTime.getValue().asInteger() > 0) {
                rn.validaMembroTime(cbbTime.getValue().asInteger());
                if (tbLeadzapTemMembrosTime.getTOT().asInteger() == 0) {
                    return false;
                }
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Validar Membros do Item: ", ex);
        }
        return true;
    }

    public boolean validaSetorUsuario() {
        String usuarios = "";
        try {
            if (pkCrmPartsRna.getParametro((double) codEmpresaUsuarioLogado, "PARM_SYS", "FORCAR_SETOR_NA_VENDA").equals("S")) {
                rn.validaSetorMembroTime(cbbTime.getValue().asInteger());
                int count = 0;
                while (!tbLeadzapSetorMembroTime.eof()) {
                    if (tbLeadzapSetorMembroTime.getCOD_SETOR().isNull()) {
                        if (usuarios.equals("")) {
                            usuarios += tbLeadzapSetorMembroTime.getNOME().asString();
                        } else {
                            usuarios += ", " + tbLeadzapSetorMembroTime.getNOME().asString();
                        }
                    }
                    count += 1;
                    if (count == 30) {
                        break;
                    }
                    tbLeadzapSetorMembroTime.next();
                }
                if (!usuarios.equals("")) {
                    Dialog.create().title("CrmService").message("Seguintes Usuários Não tem Setor Parâmetrizado, verifique" + " Todos Usuários do Time! Segue Lista dos 30 Primeiros usuários. " + usuarios).showInformation(((EventListener) event1 -> {
                        cbbTime.setFocus();
                    }));
                    return false;
                }
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Validar Membros do Item: ", ex);
        }
        return true;
    }

    @Override
    public void tbLeadzapSetorMembroTimeMaxRow(final Event<Object> event) {

    }

    public void carregaTemplateLeadAprov() {
        try {
            rn.carregaTemplateLeadAprov(tbLeadzapMenu.getID_TEMPLATE_VENDEDOR_PARTS().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Template Lead Vendedor Parts Aprov: ", ex);
        }
    }

    public void carregaTemplateLeadReprov() {
        try {
            rn.carregaTemplateLeadReprov(tbLeadzapMenu.getID_TEMPLATE_VENDEDOR_PARTS_REP().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Template Lead Vendedor Parts Reprov: ", ex);
        }
    }
}
