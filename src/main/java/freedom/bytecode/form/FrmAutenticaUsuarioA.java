package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAutenticaUsuarioW;
import freedom.bytecode.rn.LoginRNA;
import freedom.client.event.Event;
import freedom.util.ApplicationUtil;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;

public class FrmAutenticaUsuarioA extends FrmAutenticaUsuarioW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String DATASOURCE_SELECIONADO = "DATASOURCE_SELECIONADO";

    private final String schema = ApplicationUtil.getValue(DATASOURCE_SELECIONADO);

    private final LoginRNA loginRNA = new LoginRNA();

    private boolean ok = false;

    private String codAcesso;

    private boolean autenticou = false;

    private boolean temMotivo = false;

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        String usuario = edtUsuario.getValue().asString().trim();
        if (usuario.equals("")) {
            EmpresaUtil.showWarning("Atenção", "Informe o usuário!");
            return;
        }
        String senha = edtSenha.getValue().asString().trim();
        if (senha.equals("")) {
            EmpresaUtil.showWarning("Atenção", "Informe a senha!");
            return;
        }
        if (temMotivo) {
            if (edtMotivo.getValue().asString().equals("")) {
                EmpresaUtil.showWarning("Atenção", "Informe o Motivo!");
                return;
            }
        }
        try {
            String validou = loginRNA.validarLogin(usuario, senha, schema, false);
            if (!validou.isEmpty()) {
                EmpresaUtil.showWarning("Atenção", validou);
            } else {
                if (codAcesso.isEmpty() || !codAcesso.equals("")) {
                    PkgCrmPartsRNA pkRna = new PkgCrmPartsRNA();
                    String retFuncao = pkRna.validarAcesso(usuario, codAcesso);
                    if (!retFuncao.equals("S")) {
                        EmpresaUtil.showWarning("Atenção", retFuncao);
                        return;
                    }
                }
                ok = true;
                autenticou = true;
                close();
            }
        } catch (Exception e) {
            EmpresaUtil.showError("Erro", e);
        }
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        ok = false;
        close();
    }

    public boolean isOk() {
        return ok;
    }

    public void setMsg(String msg) {
        lblMsg.setCaption(msg);
    }

    public FrmAutenticaUsuarioA(String codAcesso) {
        this.codAcesso = codAcesso;
    }

    public FrmAutenticaUsuarioA(String codAcesso, Boolean temMotivo) {
        this.codAcesso = codAcesso;
        this.temMotivo = temMotivo;
        if (temMotivo) {
            vBoxMotivo.setVisible(true);
            FrmAutenticaUsuario.setHeight(320);
        } else {
            vBoxMotivo.setVisible(false);
            FrmAutenticaUsuario.setHeight(255);
        }
    }

    public boolean isAutenticou() {
        return autenticou;
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        if (lblMsg.getCaption().equals("") || lblMsg.getCaption().equals("lblMsg")) {
            hboxMsg.setVisible(false);
        } else {
            hboxMsg.setVisible(true);
        }

    }

    public String getMotivo() {
        return edtMotivo.getValue().asString();
    }
}
