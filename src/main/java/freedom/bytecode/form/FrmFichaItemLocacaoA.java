package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.bytecode.cursor.*;
import freedom.client.util.Dialog;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;

public class FrmFichaItemLocacaoA extends FrmFichaItemLocacaoW {

    private static final long serialVersionUID = 20130827081850L;
    private Double codEmpresa;
    private String codItem;
    private Double codFornecedor;
    private boolean ok = false;

    public boolean isOk() {
        return ok;
    }

    public boolean selecionarLocacao(Double codEmpresa, String codItem, Double codFornecedor) {
        try {
            tbItemLocacaoDispEmp.setMaxRowCount(0);
            tbLocalEstoque.close();
            tbLocalEstoque.addFilter("COD_EMPRESA");
            tbLocalEstoque.addParam("COD_EMPRESA", codEmpresa);
            tbLocalEstoque.open();
            this.codEmpresa = codEmpresa;
            this.codItem = codItem;
            this.codFornecedor = codFornecedor;
            return true;
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao selecionar locacao", e);
            return false;
        }
    }

    public void filtrarLocacoes() {
        try {
            tbItemLocacaoDispEmp.close();
            tbItemLocacaoDispEmp.clearFilters();
            tbItemLocacaoDispEmp.clearParams();
            tbItemLocacaoDispEmp.addParam("COD_ITEM", codItem);
            tbItemLocacaoDispEmp.addParam("COD_FORNECEDOR", codFornecedor);
            tbItemLocacaoDispEmp.addParam("COD_EMPRESA", codEmpresa);
            if (comboLocalEstoque.getValue().asInteger() > 0) {
                tbItemLocacaoDispEmp.addFilter("COD_LOCAL_ESTOQUE");
                tbItemLocacaoDispEmp.addParam("COD_LOCAL_ESTOQUE", tbLocalEstoque.getCOD_LOCAL_ESTOQUE().asInteger());
            }
            if (edtLocacao.getValue().asString().length() > 0) {
                tbItemLocacaoDispEmp.addFilter(("LOCACAO"));
                tbItemLocacaoDispEmp.addParam("LOCACAO", edtLocacao.getValue().asString().trim().toUpperCase() + "%");
            }
            tbItemLocacaoDispEmp.open();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao filtrar locações", e);
        }
    }

    @Override
    public void edtLocacaoEnter(Event<Object> event) {
        filtrarLocacoes();
    }

    @Override
    public void comboLocalEstoqueChange(Event<Object> event) {
        filtrarLocacoes();
    }

    @Override
    public void btnSalvarClick(final Event event) {
        int qtde = 0;
        try {
            tbItemLocacaoDispEmp.first();
            while (!tbItemLocacaoDispEmp.eof()) {
                if (tbItemLocacaoDispEmp.getSEL().asString().equals("S")) {
                    qtde = qtde + 1;
                }
                tbItemLocacaoDispEmp.next();
            }

            if (qtde == 0) {
                EmpresaUtil.showMessage("CrmParts", "Nenhuma locação selecionada.");
                return;
            }

            Dialog.create()
                    .title("CrmParts")
                    .message("Confirma Salvar as Locações Selecionadas?")
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            try {
                                int qtdLoc = 0;
                                int qtdeEstoque = 0;
                                ESTOQUE tbEst = new ESTOQUE("tbEst");
                                tbEst.addFilter("COD_ITEM;COD_FORNECEDOR;COD_EMPRESA");
                                tbEst.addParam("COD_ITEM", codItem);
                                tbEst.addParam("COD_FORNECEDOR", codFornecedor);
                                tbEst.addParam("COD_EMPRESA", codEmpresa);
                                tbEst.open();
                                if (tbEst.isEmpty()) {
                                    qtdeEstoque = 0;
                                } else {
                                    qtdeEstoque = tbEst.getQTDE().asInteger();
                                }
                                LOCACAO incLoc = new LOCACAO("incLoc");
                                tbItemLocacaoDispEmp.first();
                                while (!tbItemLocacaoDispEmp.eof()) {
                                    if (tbItemLocacaoDispEmp.getSEL().asString().equals("S")) {
                                        qtdLoc++;
                                        incLoc.append();
                                        incLoc.setCOD_EMPRESA(codEmpresa);
                                        incLoc.setCOD_ITEM(codItem);
                                        incLoc.setCOD_FORNECEDOR(codFornecedor);
                                        incLoc.setCOD_LOCAL_ESTOQUE(tbItemLocacaoDispEmp.getCOD_LOCAL_ESTOQUE());
                                        incLoc.setLOCACAO(tbItemLocacaoDispEmp.getLOCACAO());
                                        if (qtdLoc == 1) {
                                            incLoc.setQTDE_ESTOQUE(qtdeEstoque);
                                        } else {
                                            incLoc.setQTDE_ESTOQUE(0);
                                        }
                                        incLoc.post();
                                    }
                                    tbItemLocacaoDispEmp.next();
                                    incLoc.applyUpdates();
                                    incLoc.commitUpdates();
                                }
                                ok = true;
                                close();
                            } catch (DataException e) {
                                EmpresaUtil.showError("Falha ao salvar alterações: ", e);
                            }
                        }
                    });

        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao salvar item", e);
        }
    }

    private void selecionarItem() {
        try {
            tbItemLocacaoDispEmp.edit();
            if (tbItemLocacaoDispEmp.getSEL().asString().equals("S")) {
                tbItemLocacaoDispEmp.setSEL("N");
            } else {
                tbItemLocacaoDispEmp.setSEL("S");
            }
            tbItemLocacaoDispEmp.post();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao selecionar item", e);
        }

    }

    @Override
    public void gridLocacaoselecionar(Event<Object> event) {
        selecionarItem();
    }

    @Override
    public void gridLocacaoretirarSelecao(Event<Object> event) {
        selecionarItem();
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        ok = false;
                close();
    }
}
