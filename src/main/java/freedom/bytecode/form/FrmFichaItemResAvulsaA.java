package freedom.bytecode.form;

import freedom.bytecode.cursor.ITENS_CUSTOS;
import freedom.bytecode.cursor.PARM_SYS;
import freedom.bytecode.form.wizard.FrmFichaItemResAvulsaW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.util.Constantes;
import freedom.util.EdtString;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import java.util.Date;

public class FrmFichaItemResAvulsaA extends FrmFichaItemResAvulsaW {

    private static final long serialVersionUID = 20130827081850L;

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    @Getter
    private boolean fechadoAoSalvar;

    private double codEmpresaSel;

    private boolean ehResAvulsaDireta;

    public boolean podeIncluirReserva(
            String codItem
            ,double codFornecedor
            ,boolean ehResAvulsaDireta
    ) {
        boolean podeIncluir = this.podeIncluirReservaItem(
                codItem
                ,codFornecedor
                ,ehResAvulsaDireta
        );
        if (podeIncluir){
            this.loadFornecedorItem(
                    codItem
                    ,codFornecedor
            );
        }
        return podeIncluir;
    }

    private boolean podeIncluirReservaItem(
            String codItem
            ,double codFornecedor
            ,boolean ehResAvulsaDireta
    ){
        boolean podeIncluir = false;
        double codEmpresaParam;
        try {
            this.tbEmpresasUsuarios.close();
            this.tbEmpresasUsuarios.setFilterNOME(
                    this.usuarioLogado
            );
            this.tbEmpresasUsuarios.open();
            if (!ehResAvulsaDireta) {
                codEmpresaParam = this.tbEmpresasUsuarios.getCOD_EMPRESA().asDecimal();
            } else {
                codEmpresaParam = this.codEmpresaSel;
            }
            String respFunc = this.pkgCrmPartsRNA.podeIncluirResAvulsa(
                    codEmpresaParam
                    ,codItem
                    ,codFornecedor
                    ,this.usuarioLogado
            );
            if (StringUtils.isBlank(respFunc)
                    || respFunc.equals("S")
                    || respFunc.contains("Esse item nao tem estoque disponivel.")) {
                this.tbEstoque.close();
                this.tbEstoque.setFilterCOD_ITEM(
                        codItem
                );
                this.tbEstoque.setFilterCOD_FORNECEDOR(
                        codFornecedor
                );
                this.tbEstoque.setFilterCOD_EMPRESA(
                        codEmpresaParam
                );
                this.tbEstoque.open();
                PARM_SYS tbParmSys = new PARM_SYS(
                        "tbParmSys"
                );
                tbParmSys.setFilterCOD_EMPRESA(
                        codEmpresaParam
                );
                tbParmSys.open();
                ITENS_CUSTOS tbItensCustos = new ITENS_CUSTOS(
                        "tbItensCustos"
                );
                tbItensCustos.setFilterCOD_EMPRESA(
                        codEmpresaParam
                );
                tbItensCustos.setFilterCOD_ITEM(
                        codItem
                );
                tbItensCustos.setFilterCOD_FORNECEDOR(
                        codFornecedor
                );
                tbItensCustos.open();
                long codDepartamento = this.tbEmpresasUsuarios.getCOD_EMPRESA_DEPARTAMENTO().asLong();
                long codDivisao = this.tbEmpresasUsuarios.getCOD_EMPRESA_DIVISAO().asLong();
                double precoVenda = tbItensCustos.getPRECO_VENDA().asDecimal();
                int parmSysReservaDiasItens = tbParmSys.getRESERVA_DIAS_ITENS().asInteger();
                this.tbItensReservasPendencias.close();
                this.tbItensReservasPendencias.append();
                this.tbItensReservasPendencias.setNOME(
                        this.usuarioLogado
                );
                this.tbItensReservasPendencias.setCOD_EMPRESA(
                        codEmpresaParam
                );
                this.tbItensReservasPendencias.setCOD_EMPRESA_DEPARTAMENTO(
                        codDepartamento
                );
                this.tbItensReservasPendencias.setCOD_EMPRESA_DIVISAO(
                        codDivisao
                );
                this.tbItensReservasPendencias.setCOD_ITEM(
                        codItem
                );
                this.tbItensReservasPendencias.setCOD_FORNECEDOR(
                        codFornecedor
                );
                this.tbItensReservasPendencias.setDATA_RESERVA(
                        new Date()
                );
                this.tbItensReservasPendencias.setQTDE(
                        1
                );
                this.tbItensReservasPendencias.setTIPO_RESERVA(
                        8
                );
                this.tbItensReservasPendencias.setPRIORIDADE(
                        1
                );
                this.tbItensReservasPendencias.setPRECO_UNITARIO(
                        precoVenda
                );
                this.tbItensReservasPendencias.setDATA_VALIDADE(
                        DateUtils.addDays(
                                new Date()
                                ,parmSysReservaDiasItens
                        )
                );
                podeIncluir = true;
            }
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Falha ao validar inclusao de reserva avulsa"
                    ,dataException
            );
        }
        return podeIncluir;
    }

    private void salvar() {
        String codItem = this.edtCodigo.getValue().asString().trim();
        if (codItem.isEmpty()) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.edtCodigo.getHelpCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                            ,10000                   // Tempo em milissegundos para exibir a mensagem
                            ,this.edtCodigo          // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtCodigo.setFocus();
            return;
        }
        int qtdeRes = this.edtQuantidade.getValue().asInteger();
        if (qtdeRes <= 0) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.edtQuantidade.getHelpCaption()
                            + "\" deve ser preenchido com valor maior que \"0\" (zero)."
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                            ,10000                   // Tempo em milissegundos para exibir a mensagem
                            ,this.edtQuantidade      // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtQuantidade.setFocus();
            this.edtQuantidade.setSelectionRange(
                    0
                    ,(this.edtQuantidade.getValue().asString().length() + 1)
            );
            return;
        }
        int disponivel = (
                this.tbEstoque.getQTDE().asInteger()
                        - this.tbEstoque.getRESERVADO().asInteger()
        );
        if (disponivel < qtdeRes) {
            String mensagem = (
                    "Quantidade reserva ("
                            + qtdeRes
                            + ") maior que a disponível ("
                            + disponivel
                            + ")."
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                            ,10000                   // Tempo em milissegundos para exibir a mensagem
                            ,this.edtQuantidade      // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtQuantidade.setFocus();
            this.edtQuantidade.setSelectionRange(
                    0
                    ,(this.edtQuantidade.getValue().asString().length() + 1)
            );
            return;
        }
        String clienteNome = this.edtNomeCliente.getValue().asString().trim();
        if (clienteNome.isEmpty()) {
            this.pesquisarCliente();
            return;
        }
        String clienteTelefone = this.edtTelefone.getValue().asString().trim();
        if ((clienteTelefone.length() != 10)
                && (clienteTelefone.length() != 11)) {
            String mensagem = (
                    "O campo \""
                            + this.edtTelefone.getHelpCaption()
                            + "\" deve ser preenchido com \"10\" (dez) ou \"11\" (onze) dígitos."
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                            ,10000                   // Tempo em milissegundos para exibir a mensagem
                            ,this.edtTelefone        // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtTelefone.setFocus();
            return;
        }
        try {
            long codEmpresaDestino = this.cboEmpresas.getValue().asLong();
            String retFuncao = this.rn.salvarReservaAvulsa(
                    this.usuarioLogado
                    ,codEmpresaDestino
            );
            if (!retFuncao.equals("S")) {
                Dialog.create()
                        .showToastInfo(
                                retFuncao
                                ,Constantes.MIDDLE_CENTER // A mensagem aparece no meio da tela, alinhada ao centro
                                ,10000                    // Tempo em milissegundos para exibir a mensagem
                                ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
                return;
            }
            this.fechadoAoSalvar = true;
            this.close();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Falha ao incluir reserva"
                    ,dataException
            );
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        this.salvar();
    }

    public void loadFornecedorItem(
            String coditem
            ,double codFornecedor
    ) {
        try {
            this.tbFornecedorEstoqueItem.close();
            this.tbFornecedorEstoqueItem.setFilterCOD_ITEM(
                    coditem
            );
            this.tbFornecedorEstoqueItem.open();
            if (codFornecedor > 0.0) {
                this.tbFornecedorEstoqueItem.locate(
                        "COD_FORNECEDOR"
                        ,codFornecedor
                );
                String nomeFornecedor = this.tbFornecedorEstoqueItem.getNOME_FORNECEDOR().asString();
                this.cboFornecedor.setText(nomeFornecedor);
            }
            boolean tbFornecedorEstoqueItemNotEmpty = !this.tbFornecedorEstoqueItem.isEmpty();
            this.cboFornecedor.setEnabled(
                    tbFornecedorEstoqueItemNotEmpty
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Falha ao carregar fornecedor"
                    ,dataException
            );
        }
    }

    private void pesquisarItem() {
        String codItem = this.edtCodigo.getValue().asString().trim();
        if (codItem.isEmpty()) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.edtCodigo.getHelpCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                            ,10000                   // Tempo em milissegundos para exibir a mensagem
                            ,this.edtCodigo          // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtCodigo.setFocus();
            return;
        }
        this.refresh();
        this.edtCodigo.setValue(
                codItem
        );
        this.pesquisarCodigoItem();
    }

    @Override
    public void icoPesquisarItemClick(final Event<Object> event) {
        this.pesquisarItem();
    }

    public void pesquisarCodigoItem() {
        this.edtDescricao.clear();
        String codItem = this.edtCodigo.getValue().asString().trim();
        if (codItem.isEmpty()) {
            this.edtCodigo.setFocus();
        } else {
            this.tbItens.close();
            try {
                this.tbItens.setFilterCOD_ITEM(
                        codItem
                );
            } catch (
                    DataException dataException
            ) {
                this.edtCodigo.setFocus();
            }
            try {
                this.tbItens.open();
            } catch (
                    DataException dataException
            ) {
                this.edtCodigo.setFocus();
            }
            boolean tbItensEmpty = this.tbItens.isEmpty();
            if (tbItensEmpty) {
                String mensagem = (
                        Constantes.O_ITEM
                                + codItem
                                + "\" não foi encontrado."
                );
                Dialog.create()
                        .showNotificationInfo(
                                mensagem
                                ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                                ,10000                   // Tempo em milissegundos para exibir a mensagem
                                ,this.edtCodigo          // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
                this.edtCodigo.setFocus();
                this.edtCodigo.setSelectionRange(
                        0
                        ,(this.edtCodigo.getValue().asString().length() + 1)
                );
            } else {
                if (this.tbFornecedorEstoqueItem.count() > 1) {
                    String mensagem = (
                            "O item \""
                                    + codItem
                                    + "\" foi encontrado mais de uma vez. Escolha o fornecedor."
                    );
                    Dialog.create()
                            .showNotificationInfo(
                                    mensagem
                                    ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                                    ,10000                   // Tempo em milissegundos para exibir a mensagem
                                    ,this.edtCodigo          // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                    ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                            );
                    this.cboFornecedor.setFocus();
                    this.cboFornecedor.setOpen(
                            true
                    );
                } else {
                    this.loadFornecedorItem(
                            codItem
                            ,0.0
                    );
                    boolean tbFornecedorEstoqueItemEmpty = this.tbFornecedorEstoqueItem.isEmpty();
                    if (tbFornecedorEstoqueItemEmpty) {
                        String mensagem = (
                                "O item \""
                                        + codItem
                                        + "\" nao foi encontrado com nenhum fornecedor (ITENS_FORNECEDOR)."
                        );
                        Dialog.create()
                                .showNotificationInfo(
                                        mensagem
                                        ,Constantes.AFTER_CENTER // A mensagem aparece abaixo da âncora, alinhada ao centro
                                        ,10000                   // Tempo em milissegundos para exibir a mensagem
                                        ,this.edtCodigo          // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                        ,true                    // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                                );
                        this.edtCodigo.setFocus();
                    } else {
                        String nomeFornecedor = this.tbFornecedorEstoqueItem.getNOME_FORNECEDOR().asString();
                        this.cboFornecedor.setText(
                                nomeFornecedor
                        );
                        String descricao = this.tbItens.getDESCRICAO().asString();
                        this.edtDescricao.setValue(
                                descricao
                        );
                        double codFornecedor = this.tbFornecedorEstoqueItem.getCOD_FORNECEDOR().asDecimal();
                        boolean podeIncluir = this.podeIncluirReservaItem(
                                codItem
                                ,codFornecedor
                                ,this.ehResAvulsaDireta
                        );
                        this.edtQuantidade.setEnabled(
                                podeIncluir
                        );
                        if (podeIncluir) {
                            if (this.tbFornecedorEstoqueItem.count() == 1) {
                                this.edtNomeCliente.setFocus();
                            } else {
                                this.cboFornecedor.setEnabled(true);
                                this.cboFornecedor.setFocus();
                            }
                        } else {
                            this.edtCodigo.setFocus();
                        }
                    }
                }
            }
        }
    }

    @Override
    public void cboFornecedorChange(Event<Object> event) {
        String codItem = this.edtCodigo.getValue().asString();
        double codFornecedor = tbFornecedorEstoqueItem.getCOD_FORNECEDOR().asDecimal();
        boolean podeIncluir = podeIncluirReservaItem(codItem, codFornecedor, this.ehResAvulsaDireta);
        this.edtQuantidade.setEnabled(podeIncluir);
    }

    public void openFichaItemResAvulsa(
            double codEmpresa
    ) {
        this.codEmpresaSel = codEmpresa;
        this.ehResAvulsaDireta = true;
        this.vboxEmpresas.setVisible(
                true
        );
        double codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();
        try {
            this.rn.openEmpresas(
                    this.usuarioLogado
                    ,codEmpresaUsuarioLogado
            );
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Falha ao iniciar reservas avulsas"
                    ,exception
            );
        }
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        long codEmpresaSelecionada = (long) this.codEmpresaSel;
        this.cboEmpresas.setValue(
                codEmpresaSelecionada
        );
        boolean cboEmpresasEnabled = this.cboEmpresas.isEnabled();
        int contadorEmpresas = this.tbLeadsEmpresasUsuarios.count();
        if (cboEmpresasEnabled
                && (contadorEmpresas > 1)) {
            FreedomUtilities.invokeLater(() -> {
                this.cboEmpresas.setFocus();
                this.cboEmpresas.setOpen(
                        true
                );
            });
        } else {
            this.edtQuantidade.setFocus();
            this.edtQuantidade.setSelectionRange(
                    0
                    ,(this.edtQuantidade.getValue().asString().length() + 1)
            );
        }
    }

    private void definirObrigatoriedadeDaPesquisaDoCliente() {
        long codEmpresa = this.cboEmpresas.getValue().asLong();
        boolean parmSys3CrmpartsObrPesqCliResAvul = this.rn.getParmSys3CrmpartsObrPesqCliResAvul(
                codEmpresa
        );
        if (parmSys3CrmpartsObrPesqCliResAvul) {
            this.edtNomeCliente.setEnabled(
                    false
            );
            this.edtNomeCliente.clear();
        } else {
            this.edtNomeCliente.setEnabled(
                    true
            );
        }
    }

    @Override
    public void tbLeadsEmpresasUsuariosAfterScroll(Event<Object> event) {
        this.definirObrigatoriedadeDaPesquisaDoCliente();
    }

    @Override
    public void cboEmpresasChange(final Event<Object> event) {
        this.codEmpresaSel = cboEmpresas.getValue().asDecimal();
        String codItem = this.edtCodigo.getValue().asString().trim();
        if (!codItem.isEmpty()) {
            codItem = this.edtCodigo.getValue().asString();
            this.refresh();
            this.edtCodigo.setValue(
                    codItem
            );
            this.pesquisarCodigoItem();
        }
    }

    public void refresh() {
        this.tbItensReservasPendencias.close();
        this.tbItens.close();
        this.tbEstoque.close();
        this.tbFornecedorEstoqueItem.close();
        this.edtDescricao.clear();
        this.edtNomeCliente.clear();
        this.edtTelefone.clear();
        this.edtObservacao.clear();
    }

    @Override
    public void cboEmpresasEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtCodigoEnter(Event<Object> event) {
        String descricao = this.edtDescricao.getValue().asString().trim();
        if (descricao.isEmpty()) {
            this.pesquisarItem();
            return;
        }
        this.salvar();
    }

    @Override
    public void edtCodigoExit(Event<Object> event) {
        this.pesquisarItem();
    }

    @Override
    public void cboFornecedorEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtQuantidadeEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtObservacaoEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtTelefoneEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void edtTelefoneExit(Event<Object> event) {
        EdtString.definirMascaraDeTelefone(
                this.edtTelefone
        );
    }

    @Override
    public void edtNomeClienteEnter(Event<Object> event) {
        this.salvar();
    }

    @Override
    public void btnPesquisarClienteClick(Event<Object> event) {
        this.pesquisarCliente();
    }

    @Override
    public void edtTelefoneChange(Event<Object> event) {
        EdtString.definirMascaraDeTelefone(
                this.edtTelefone
        );
    }

    private void pesquisarCliente() {
        String nomeCliente = this.edtNomeCliente.getValue().asString().trim();
        FrmPesquisaClienteA frmPesquisaClienteA = new FrmPesquisaClienteA();
        frmPesquisaClienteA.edtPesquisarCliente.setValue(
                nomeCliente
        );
        FormUtil.doShow(
                frmPesquisaClienteA
                , t -> {
                    boolean frmPesquisaClienteAFechadoAoAceitar = frmPesquisaClienteA.isFechadoAoAceitar();
                    if (frmPesquisaClienteAFechadoAoAceitar) {
                        String frmPesquisaClienteANomeCliente = frmPesquisaClienteA.tbLeadsConsultaClientes.getCLIENTE().asString();
                        String frmPesquisaClienteATelefone = frmPesquisaClienteA.getTelefone();
                        if (frmPesquisaClienteANomeCliente.length() > 100) {
                            frmPesquisaClienteANomeCliente = frmPesquisaClienteANomeCliente.substring(
                                    0
                                    ,100
                            );
                        }
                        this.edtNomeCliente.setValue(
                                frmPesquisaClienteANomeCliente
                        );
                        this.edtTelefone.setValue(
                                frmPesquisaClienteATelefone
                        );
                        boolean edtObservacaoEnabled = this.edtObservacao.isEnabled();
                        if (edtObservacaoEnabled) {
                            this.edtObservacao.setFocus();
                        }
                    }
                });
    }

}