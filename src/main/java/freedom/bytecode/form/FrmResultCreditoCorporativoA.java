package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmResultCreditoCorporativoW;
import freedom.client.event.Event;

public class FrmResultCreditoCorporativoA extends FrmResultCreditoCorporativoW {
    private static final long serialVersionUID = 20130827081850L;

    private Double codCliente;

    public FrmResultCreditoCorporativoA(Double codCliente){
          this.codCliente = codCliente;
          abreConsultaClienteCreditoCorporativo(codCliente);
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
       this.close();
    }

    private void abreConsultaClienteCreditoCorporativo(Double codCliente) {

        rn.abreConsultaClienteCreditoCorporativo(codCliente);

    }

}
