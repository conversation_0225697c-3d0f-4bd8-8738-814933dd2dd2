package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.util.EmpresaUtil;

public class FrmAlterarQtdeA extends FrmAlterarQtdeW {

    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;
    private final int qtdeOriginal;

    public FrmAlterarQtdeA(int qtde) {
        this.qtdeOriginal = qtde;
        edtQtde.setValue(qtde);
    }

    @Override
    public void btnCancelarClick(final Event event) {
        ok = false;
        close();
    }

    @Override
    public void btnOkClick(final Event event) {
        if (edtQtde.getValue().asDecimal() < 0) {
            EmpresaUtil.showWarning("Atenção", "Qtde não pode ser inferior a 0(Zero)");
            return;
        }
        ok = true;
        close();
    }

    public boolean isOk() {
        return ok;
    }

    public int getQtdeOriginal() {
        return qtdeOriginal;
    }

    public int getQtde() {
        return edtQtde.getValue().asInteger();
    }

}
