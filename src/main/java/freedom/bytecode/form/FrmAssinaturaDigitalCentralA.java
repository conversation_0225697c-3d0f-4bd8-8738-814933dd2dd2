package freedom.bytecode.form;

import freedom.client.controls.impl.grid.TFGridExporter;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.bytecode.form.wizard.*;
import freedom.bytecode.rn.wizard.*;
import freedom.util.DateUtils;
import freedom.util.EmpresaUtil;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalDocumento;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalUtils;
import freedom.util.assinaturaDigital.EnTipoAssinaturaFactory;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;
import lombok.Getter;
import lombok.Setter;
import org.json.JSONObject;
import org.zkoss.zul.Vlayout;
import org.zkoss.zul.Window;

import java.util.Date;
import java.util.List;

import freedom.client.event.*;
import freedom.data.DataException;
import freedom.client.controls.impl.*;

@Getter
@Setter
public class FrmAssinaturaDigitalCentralA extends FrmAssinaturaDigitalCentralW {
    private static final long serialVersionUID = 20130827081850L;

    private final int codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();
    private final String usuarioLogado = EmpresaUtil.getUserLogged();
    private Date dataInicial;
    private Date dataFinal;


    @Getter
    public enum EnTipoSubFiltro{
        TODAS("1 = 1"),
        AGUARDANDO("DESC_STATUS IN ('AGUARDANDO_ASSINATURAS', 'PREPARANDO_ENVIO', 'AGUARDANDO_ENVIO', 'ENVIAR_DOCUMENTOS')"),
        ASSINADAS("DESC_STATUS IN ('DOCUMENTOS_ASSINADOS','DOCS_ASSINADOS_BAIXADOS', 'ARQUIVADO')"),
        FALHAS("DESC_STATUS in ('FALHA')"),
        CANCELADAS("DESC_STATUS in ('ENVELOPE_CANCELADO','SOLICITACAO_CANCELAMENTO')"),
        EXPIRADAS("DESC_STATUS in ('EXPIRADO')");

        final String filter;

        EnTipoSubFiltro(String filter){
            this.filter = filter;
        }
    }

    EnTipoSubFiltro subFiltroStatusSelecionado;// = EnTipoSubFiltro.TODAS;

    public FrmAssinaturaDigitalCentralA() {
        FPanelButtonTodas.setAttribute("SCLASS_BASE", "vboximagestrech vboxborderpainel");
        FPanelButtonAguardando.setAttribute("SCLASS_BASE", "vboximagestrech vboxborderpainel");
        FPanelButtonAssinadas.setAttribute("SCLASS_BASE", "vboximagestrech vboxborderpainel");
        FPanelButtonFalhas.setAttribute("SCLASS_BASE", "vboximagestrech vboxborderpainel");
        FPanelButtonCanceladas.setAttribute("SCLASS_BASE", "vboximagestrech vboxborderpainel");
        FPanelButtonExpiradas.setAttribute("SCLASS_BASE", "vboximagestrech vboxborderpainel");
        setDataIniciarForm();
        carregarTabelasAuxiliares();
        ftEmpresa.setValue(codEmpresaUsuarioLogado);
    }

    public void setDataIniciarForm(){
        setDataInicial(new Date());
        setDataFinal(new Date());
        ftPeriodo.setValue("MES");
    }



    @Override
    public void FFormCreate(Event<Object> event) {

    }

    public void carregarTabelasAuxiliares(){
        try {
            rn.filtrarEmpresasUsuarios(codEmpresaUsuarioLogado, usuarioLogado);
            rn.filtrarTbAssinaturaDigital();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao abrir tabelas auxialires.", e);
        }
    }

    @Override
    public void btnPesquisarClick(Event<Object> event) {
        try {
            int filtroCodEmpresa = ftEmpresa.getValue().asInteger();
            Date filtroDataInicial =  this.dataInicial;
            Date filtroDataFinal =  this.dataFinal;
            String filtroNomeOuDocumento = ftNomeOuDocumento.getValue().asString();
            TipoAssinaturaStrategy filtroTipoAssinatura = EnTipoAssinaturaFactory.getTipoAssinatura(ftTipoAssinatura.getValue().asString());
            String tipoPeriodo = ftPeriodo.getValue().asString();
            rn.filtrarTbCentralAssinaturaGrid(filtroCodEmpresa, filtroTipoAssinatura, filtroNomeOuDocumento, tipoPeriodo, filtroDataInicial, filtroDataFinal);
            calcularTotais();
            aplicarSubFiltro(getSubFiltroStatusSelecionado());
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao pesquisar.", e);
        }
    }

    public void calcularTotais(){
        try {
            TFPanelButtonItem FPanelButtonTodasLblTotal = (TFPanelButtonItem) FPanelButtonTodas.findItemByName("FPanelButtonTodasLblTotal");
            int totalTodas = rn.calcularTotal(EnTipoSubFiltro.TODAS.getFilter());
            if (FPanelButtonTodasLblTotal != null) {
                FPanelButtonTodasLblTotal.setCaption(Integer.toString(totalTodas));
                FPanelButtonTodas.rebuild();
            }

            TFPanelButtonItem FPanelButtonAguardandoLblTotal = (TFPanelButtonItem) FPanelButtonAguardando.findItemByName("FPanelButtonAguardandoLblTotal");
            int totalAguardando = rn.calcularTotal(EnTipoSubFiltro.AGUARDANDO.getFilter());
            if (FPanelButtonAguardandoLblTotal != null) {
                FPanelButtonAguardandoLblTotal.setCaption(Integer.toString(totalAguardando));
                FPanelButtonAguardando.rebuild();
            }

            TFPanelButtonItem FPanelButtonAssinadasLblTotal = (TFPanelButtonItem) FPanelButtonAssinadas.findItemByName("FPanelButtonAssinadasLblTotal");
            int totalAssinadas = rn.calcularTotal(EnTipoSubFiltro.ASSINADAS.getFilter());
            if (FPanelButtonAssinadasLblTotal != null) {
                FPanelButtonAssinadasLblTotal.setCaption(Integer.toString(totalAssinadas));
                FPanelButtonAssinadas.rebuild();
            }

            TFPanelButtonItem FPanelButtonFalhasLblTotal = (TFPanelButtonItem) FPanelButtonFalhas.findItemByName("FPanelButtonFalhasLblTotal");
            int totalFalhas = rn.calcularTotal(EnTipoSubFiltro.FALHAS.getFilter());
            if (FPanelButtonFalhasLblTotal != null) {
                FPanelButtonFalhasLblTotal.setCaption(Integer.toString(totalFalhas));
                FPanelButtonFalhas.rebuild();
            }

            TFPanelButtonItem FPanelButtonCanceladasLblTotal = (TFPanelButtonItem) FPanelButtonCanceladas.findItemByName("FPanelButtonCanceladasLblTotal");
            int totalCanceladas = rn.calcularTotal(EnTipoSubFiltro.CANCELADAS.getFilter());
            if (FPanelButtonCanceladasLblTotal != null) {
                FPanelButtonCanceladasLblTotal.setCaption(Integer.toString(totalCanceladas));
                FPanelButtonCanceladas.rebuild();
            }

            TFPanelButtonItem FPanelButtonExpiradasLblTotal = (TFPanelButtonItem) FPanelButtonExpiradas.findItemByName("FPanelButtonExpiradasLblTotal");
            int totalExpiradas = rn.calcularTotal(EnTipoSubFiltro.EXPIRADAS.getFilter());
            if (FPanelButtonExpiradasLblTotal != null) {
                FPanelButtonExpiradasLblTotal.setCaption(Integer.toString(totalExpiradas));
                FPanelButtonExpiradas.rebuild();
            }

        } catch (DataException e) {
            EmpresaUtil.showError("Erro a calcular totais de assinaturas.", e);
        }
    }

    @Override
    public void FPanelButtonExpiradasClick(Event<Object> event) {
        aplicarSubFiltro(EnTipoSubFiltro.EXPIRADAS);
    }

    @Override
    public void FPanelButtonCanceladasClick(Event<Object> event) {
        aplicarSubFiltro(EnTipoSubFiltro.CANCELADAS);
    }

    @Override
    public void FPanelButtonFalhasClick(Event<Object> event) {
        aplicarSubFiltro(EnTipoSubFiltro.FALHAS);
    }

    @Override
    public void FPanelButtonAssinadasClick(Event<Object> event) {
        aplicarSubFiltro(EnTipoSubFiltro.ASSINADAS);
    }

    @Override
    public void FPanelButtonAguardandoClick(Event<Object> event) {
        aplicarSubFiltro(EnTipoSubFiltro.AGUARDANDO);
    }

    @Override
    public void FPanelButtonTodasClick(Event<Object> event) {
        aplicarSubFiltro(EnTipoSubFiltro.TODAS);
    }

    public void aplicarSubFiltro(EnTipoSubFiltro subFiltro) {
        try {
            if (subFiltro== null){
                return;
            }
            setSubFiltroStatusSelecionado(subFiltro);
            hailitarTodosSubFiltrosPorStatus();
            rn.filtrarAssinaturaPorStatus(this.getSubFiltroStatusSelecionado().getFilter());
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao filtrar por status", e);
        }
    }

    public void hailitarTodosSubFiltrosPorStatus(){
        EnTipoSubFiltro subFiltro = getSubFiltroStatusSelecionado();
        FPanelButtonTodas.setToggle(false);
        FPanelButtonAguardando.setToggle(false);
        FPanelButtonAssinadas.setToggle(false);
        FPanelButtonFalhas.setToggle(false);
        FPanelButtonCanceladas.setToggle(false);
        FPanelButtonExpiradas.setToggle(false);
        
        if (!subFiltro.equals(EnTipoSubFiltro.TODAS)){
            FPanelButtonTodas.setToggle(true);
            FPanelButtonTodas.clearToggle();
            FPanelButtonTodas.rebuild();
        }
        if (!subFiltro.equals(EnTipoSubFiltro.AGUARDANDO)){
            FPanelButtonAguardando.setToggle(true);
            FPanelButtonAguardando.clearToggle();
            FPanelButtonAguardando.rebuild();
        }
        if (!subFiltro.equals(EnTipoSubFiltro.ASSINADAS)){
            FPanelButtonAssinadas.setToggle(true);
            FPanelButtonAssinadas.clearToggle();
            FPanelButtonAssinadas.rebuild();
        }
        if (!subFiltro.equals(EnTipoSubFiltro.FALHAS)){
            FPanelButtonFalhas.setToggle(true);
            FPanelButtonFalhas.clearToggle();
            FPanelButtonFalhas.rebuild();
        }
        if (!subFiltro.equals(EnTipoSubFiltro.CANCELADAS)){
            FPanelButtonCanceladas.setToggle(true);
            FPanelButtonCanceladas.clearToggle();
            FPanelButtonCanceladas.rebuild();
        }
        if (!subFiltro.equals(EnTipoSubFiltro.EXPIRADAS)){
            FPanelButtonExpiradas.setToggle(true);
            FPanelButtonExpiradas.clearToggle();
            FPanelButtonExpiradas.rebuild();
        }
    }

    @Override
    public void ftPeriodoChange(Event<Object> event) {
        if (ftPeriodo.getValue().toString().equals("PERIODO")) {
            //Date dataInicial = this.dataInicial == null ? new Date() : this.dataInicial;
            //Date dataFinal = this.dataFinal == null ? new Date() : this.dataFinal;
            FrmSelecionarPeriodoA frmSelecionarPeriodo = new FrmSelecionarPeriodoA(getDataInicial(), getDataFinal());
            frmSelecionarPeriodo.vBoxPrincipal.setPaddingTop(20);
            frmSelecionarPeriodo.vBoxPrincipal.setPaddingBottom(20);
            frmSelecionarPeriodo.vBoxPrincipal.setPaddingLeft(20);
            frmSelecionarPeriodo.vBoxPrincipal.setPaddingRight(20);
            FormUtil.doShow(frmSelecionarPeriodo, t -> {
                if (frmSelecionarPeriodo.getDataInicial() != null && frmSelecionarPeriodo.getDataFinal() != null){
                    setDataInicial(frmSelecionarPeriodo.getDataInicial());
                    setDataFinal(frmSelecionarPeriodo.getDataFinal());
                    String newOptionData = "[" + DateUtils.format(getDataInicial(), "dd/MM/yyyy") + " - " + DateUtils.format(getDataFinal(), "dd/MM/yyyy") + "]";
                    ftPeriodo.setListOptions("Enviadas Hoje=HOJE;Enviadas Ontem=ONTEM;Esta Semana=SEMANA;Este M�s=MES;M�s Anterior=MES_ANTERIOR;" + newOptionData + "=PERIODO");
                    ftPeriodo.setValue("PERIODO");
                }
            });
            Window w = (Window) ((Vlayout) frmSelecionarPeriodo.getImpl()).getParent();
            w.setHeight("auto");
            w.setWidth("auto");
        }
    }

    public void abrirAssinatura() throws DataException {
        String codigoValor = tbCentralAssinaturaGrid.getVALOR_CODIGO().asString();
        TipoAssinaturaStrategy tipoAssinatura = EnTipoAssinaturaFactory.getTipoAssinatura(tbCentralAssinaturaGrid.getTIPO_ASSINATURA().asString());
        Double idEnvelope = tbCentralAssinaturaGrid.getID().asDecimal();
        CrmAssinaturaDigitalUtils.abrirAssinatura(tipoAssinatura, codigoValor, idEnvelope, ()-> {});
    }


    @Override
    public void gridAssinaturasAbrirAssinatura(Event<Object> event) {
        try {
            abrirAssinatura();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro Ao tentar abrir assinatura", e);
        }
    }

    @Override
    public void btnExportarExcelClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportExcel(gridAssinaturas);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }
}
