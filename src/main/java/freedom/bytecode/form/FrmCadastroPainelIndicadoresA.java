package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmCadastroPainelIndicadoresW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmCadastroPainelIndicadoresA extends FrmCadastroPainelIndicadoresW {
    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;
    private String mode = "";
    private Integer idPainel = 0;
    private Integer idGrupoClasse = 0;

    public FrmCadastroPainelIndicadoresA(String mode, Integer idPainel, Integer idGrupoClasse) {
        this.mode = mode;
        this.idPainel = idPainel;
        this.idGrupoClasse = idGrupoClasse;
        initForm();
    }

    private void initForm() {
        try {
            rn.carregaComboGrupo(idGrupoClasse);
            if (mode.equals("INS")) {
                FrmCadastroPainelIndicadores.setCaption("Novo Painel");
                tbPainel.append();
                idPainel = rn.getIdPainel();
                chkboxAtivo.setChecked(true);
                chkboxAtivo.setEnabled(false);
                cbbGrupo.setValue(tbComboGrupo.getID().asInteger());
            } else if (mode.equals("UPD")) {
                FrmCadastroPainelIndicadores.setCaption("Alterar Painel");
                rn.carregaPainel(idPainel);
                tbPainel.edit();
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnSalvarClick(final Event event) {
        try {
            if (validaSalve()) {
                if (mode.equals("INS")) {
                    tbPainel.setID_PAINEL(idPainel);
                }
                tbPainel.post();
                tbPainel.applyUpdates();
                ok = true;
                close();
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnCancelarClick(final Event event) {
        ok = false;
        close();
    }

    public boolean isOk() {
        return ok;
    }

    private boolean validaSalve() {
        boolean ret = true;
        if (edtNomePainel.getValue().asString().equals("")) {
            EmpresaUtil.showMessage("Atenção", "Obrigatório informar nome do painel!");
            ret = false;
            return ret;
        }
        if (cbbGrupo.getValue().asInteger() == 0) {
            EmpresaUtil.showMessage("Atenção", "Obrigatório informar o grupo do painel!");
            ret = false;
            return ret;
        }
        return ret;
    }

}
