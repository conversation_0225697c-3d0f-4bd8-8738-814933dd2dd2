package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmCadastroLeadzapW;
import freedom.bytecode.rn.LoginRNA;
import freedom.client.controls.IFocusable;
import freedom.client.controls.impl.TFString;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.event.UploadEvent;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.FreedomUtilities;
import freedom.client.util.IDialog;
import freedom.commons.lang.IWorkList;
import freedom.connection.ISession;
import encrypt.criptografia.Cript;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.TableState;
import freedom.util.*;
import org.apache.commons.lang.StringUtils;

import java.util.Base64;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FrmCadastroLeadzapA extends FrmCadastroLeadzapW {

    public TableState oper = TableState.QUERYING;

    private static final long serialVersionUID = 20130827081850L;

    private String celularOld = "";

    private Boolean incluindo = false;

    private final Cript cript = new Cript();

    private final LoginRNA loginRna = new LoginRNA();

    private static final String DATASOURCE_SELECIONADO = "DATASOURCE_SELECIONADO";

    private String ufSel = "AC";

    private boolean carregouCidades = false;

    private final IWorkList wl = WorkListFactory.getInstance();

    public FrmCadastroLeadzapA() {
        lblMensagem.setCaption("");
        habilitaComp(false);
        tabListagem.setFontSize(-20);
        tabCadastro.setFontSize(-20);
        try {
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao Abrir Tabelas Auxiliares")
                    .message(e.getMessage())
                    .showException(e);
        }
        init();
    }

    private void init() {
        edCelular.setMaxlength(14);
        grpBoxPrincipal.setVisible(Boolean.FALSE);
        carregarLeadzapMenu();
        edtUsuario.setCharCase("ccUpper");
    }

//--------------- classe W


    @Override
    public void btnFiltroAvancadoClick(Event<Object> event) {
        filtroAvancado.doModal();
    }

    @Override
    public void btnAnteriorClick(final Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao voltar para registro anterior")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnProximoClick(final Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao avançar para o próximo registro")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    @Override
    public void btnSalvarContinuarClick(final Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao salvar e continuar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    public void executaFiltroPrincipal() throws Exception {
        tbCadastroWhatsapp.clearFilters();
        if (! efDescricao.getValue().asString().isEmpty()) {
            tbCadastroWhatsapp.setFilterDESCRICAO(efDescricao.getValue());
        }

    }

    @Override
    public void menuHabilitaNavegacaoClick(final Event<Object> event) {
        if (menuHabilitaNavegacao.isChecked()) {
            btnProximo.setEnabled(true);
            btnAnterior.setEnabled(true);
        } else {
            btnProximo.setEnabled(btnNovo.isEnabled());
            btnAnterior.setEnabled(btnNovo.isEnabled());
        }
    }

    @Override
    public void menuSelecaoMultiplaClick(final Event<Object> event) {

        boolean checkedMenu = menuSelecaoMultipla.isChecked();
        gridPrincipal.setMultiSelection(checkedMenu);

        // tratamento das abas visto que pode mexer somente na tabela master
        for (int i = 2; i <= pgPrincipal.getPageCount()-1; i++) {
            pgPrincipal.selectTab(i);
            pgPrincipal.getSelectedTab().setVisible(!checkedMenu);
        }

        // opções da barra de ferramenta
        btnNovo.setEnabled(! checkedMenu && btnAlterar.isEnabled());
        btnSalvarContinuar.setEnabled(! checkedMenu && btnAlterar.isEnabled());


        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()+30);
            // desregistra o marter table dos componentes
            edIdMenu44001.setTable(null);
            edIdMenu44001.setValue(null);

            gridPrincipal.clearSelection();
        } else {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()-30);
            // registra o master table para os componentes
            edIdMenu44001.setTable(tbCadastroWhatsapp);

        }
        pgPrincipal.selectTab(0);
    }

    @Override
    public void FrmCadastroLeadzapkeyActionIncluir(Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao incluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmCadastroLeadzapkeyActionAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao alterar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmCadastroLeadzapkeyActionExcluir(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmCadastroLeadzapkeyActionSalvar(Event<Object> event) {
        try {
            onSalvar();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao salvar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmCadastroLeadzapkeyActionSalvarContinuar(Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao salvar e continuar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmCadastroLeadzapkeyActionAnterior(Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao voltar para registro anterior")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmCadastroLeadzapkeyActionProximo(Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao avançar para o próximo registro")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnMaisClick(Event<Object> event) {
        popMenuPrincipal.open(this);
    }

    @Override
    public void menuItemAbreTabelaAuxClick(Event<Object> event) {
        try {
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                    .title("Aviso")
                    .message("Tabela Auxiliares Foram Reabertas")
                    .showInformation();
        }
    }

    @Override
    public void menuItemConfgGridClick(Event<Object> event) {
        gridConfig.doModal();
    }

    @Override
    public void menuItemHelpClick(Event<Object> event) {
        FormUtil.redirect("help/FrmCadastroLeadzap.zul", true);
    }

    protected void onConsultar() throws Exception {

        tbCadastroWhatsapp.close();
        executaFiltroPrincipal();
        tbCadastroWhatsapp.setOrderBy("DESCRICAO");
        tbCadastroWhatsapp.open();
        habilitaComp(false);

        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.clearSelection();
        }

        if (tbCadastroWhatsapp.isEmpty()) {
            Dialog.create()
                    .title("Aviso")
                    .message("Registro Não Encontrado...")
                    .showInformation();
        }
    }

    protected void onAnterior() throws Exception {

        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbCadastroWhatsapp.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnAnterior);
        }

        if (tbCadastroWhatsapp.bof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o Primeiro Registro")
                    .showInformation();
        } else {
            tbCadastroWhatsapp.prior();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onProximo() throws Exception {
        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbCadastroWhatsapp.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnProximo);
        }

        if (tbCadastroWhatsapp.eof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o último registgro")
                    .showInformation();
        } else {
            tbCadastroWhatsapp.next();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onIncluir() throws Exception {
        oper = TableState.INSERTING;

        rn.incluir();


        pgPrincipal.selectTab(1);
        edIdMenu44001.setFocus();
        habilitaComp(true);
        lblMensagem.setCaption("Incluindo...");
    }

    protected void onAlterar() throws Exception {
        oper = TableState.MODIFYING;

        if (menuSelecaoMultipla.isChecked()) {
            lblMensagem.setCaption("ATENÇÃO: Alterando multiplos registros. Será alterado todos os registros selecionados...");
            pgPrincipal.selectTab(1);
            habilitaComp(true);
        } else {
            if (!tbCadastroWhatsapp.isEmpty()) {
                rn.alterar();
                if (pgPrincipal.getSelectedIndex() == 0)  {
                    pgPrincipal.selectTab(1);
                }
                habilitaComp(true);
                edIdMenu44001.setFocus();
                lblMensagem.setCaption("Alterando "+tbCadastroWhatsapp.getDESCRICAO().asString()+"...");
            } else {
                Dialog.create()
                        .title("Erro ao editar")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        }
    }

    protected void onSalvarMultiplo() throws DataException {

        Dialog.create()
                .title("Alteração Multipla")
                .message("ATENÇÃO: Serão alterado(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {

                        try {
                            tbCadastroWhatsapp.disableControls();
                            int lastBookmark = tbCadastroWhatsapp.getBookmark();
                            try {
                                for (int bm : gridPrincipal.getSelectedIndices(true)) {
                                    tbCadastroWhatsapp.gotoBookmark(bm);
                                    tbCadastroWhatsapp.edit();

                                    if ( ! edIdMenu44001.getValue().isNull()) {
                                        tbCadastroWhatsapp.setID_MENU(edIdMenu44001.getValue());
                                    }

                                    tbCadastroWhatsapp.post();
                                }

                                onSalvar();

                            } finally {
                                tbCadastroWhatsapp.close();
                                tbCadastroWhatsapp.open();
                                // tbCadastroWhatsapp.gotoBookmark(lastBookmark);
                                tbCadastroWhatsapp.enableControls();
                                gridPrincipal.clearSelection();
                            }

                        } catch (DataException e) {
                            Dialog.create()
                                    .title("Erro ao salvar")
                                    .message(e.getMessage())
                                    .showException(e);
                        }
                    }
                });
    }

    protected void onSalvarContinuar() throws DataException {
        try {
            TableState st = tbCadastroWhatsapp.getState();
            onSalvar();
            if (st == TableState.INSERTING) {
                onIncluir();
            } else if (st == TableState.MODIFYING) {
                onAlterar();
            }
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao salvar e continuar a edição")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    protected void onCancelar() throws DataException {
        habilitaComp(false);
        rn.cancelar();

        oper = TableState.QUERYING;
        lblMensagem.setCaption("Registro Selecionado: "+tbCadastroWhatsapp.getDESCRICAO().asString());
    }

    protected void onAceitar() throws Exception {
        if (FormUtil.isExternalCall()) {
            // passa os parametros para a resposta ao VB
            FormUtil.externalCall(tbCadastroWhatsapp.getField("ID_PESSOA").asString());
        } else {
            close();
        }
    }

    protected void onAbreTabelaAux() throws DataException {
        ISession s = SessionFactory.getInstance().getSession();
        try {
            s.open();
            tbEmpresas.setOrderBy("NOME");
            tbEmpresas.close();
            tbEmpresas.open();
            tbCadastroWhatsapp.setSession(s);
            tbCadastroWhatsapp.refreshRecord();
            tbCadastroWhatsapp.setSession(null);
        } finally {
            if (s != null) {
                s.close();
            }
        }
    }

    protected void setCalcUpdate() throws DataException {

        postTable();
    }

    private void postTable() throws DataException {
        tbCadastroWhatsapp.post();
        tbWhatsappEmpresa.post();
    }

    public void loadFormPk(Integer idCelular ) throws DataException {
        tbCadastroWhatsapp.close();
        tbCadastroWhatsapp.clearFilters();

        if (idCelular > 0) {
            tbCadastroWhatsapp.addFilter("ID_CELULAR");
            tbCadastroWhatsapp.addParam("ID_CELULAR", idCelular);
        } else return;

        tbCadastroWhatsapp.open();
        habilitaComp(false);          // se tem registro habilita botões da barra de ferramenta
    }

    // retorna true se o master esta sendo editado, pode ser usado para verificar se o form esta
    // habilitado edição
    public boolean masterIsEditing() {
        return (tbCadastroWhatsapp.getState() != TableState.QUERYING);
    }


    @Override
    public void gridPrincipalClickImageDelete(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void gridPrincipalClickImageAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao alterar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void menuItemExportPdfClick(Event<Object> event) {
    }

    @Override
    public void menuItemExportExcelClick(Event<Object> event) {
    }




    protected final void habilitaComp46001(Boolean enabled) {


    }


    public void onIncluir46001() {
        try {
            rn.incluir46001();
            habilitaComp46001(true);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao adicionar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onAlterar46001() {
        try {
            if (!tbWhatsappEmpresa.isEmpty()) {
                rn.alterar46001();

                habilitaComp(true);
            } else {
                Dialog.create()
                        .title("Erro ao editar detalhe")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao editar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onExcluir46001() {
        try {
            if (!tbWhatsappEmpresa.isEmpty()) {

                rn.excluir46001();
            } else {
                Dialog.create()
                        .title("Erro ao excluir detalhe")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao editar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onCancelar46001() {
        try {
            rn.cancelar46001();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao cancelar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    public void onConfirmar46001() {
        try {
            rn.confirmar46001();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao confirmar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }
/////////////---- W

    @Override
    public void FrmCadastroLeadzapkeyActionAceitar(final Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmCadastroLeadzapkeyActionCancelar(final Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao cancelar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void FrmCadastroLeadzapkeyActionPesquisar(final Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao consultar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao cancelar")
                    .message(e.getMessage())
                    .showException(e);
        }
        incluindo = false;
    }

    @Override
    public void btnConsultarClick(final Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao consultar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void efDescricaoEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    public void habilitaComp(Boolean enabled) {
        gridPrincipal.setEnabled(!enabled);
        btnConsultar.setEnabled(!enabled);
        btnFiltroAvancado.setEnabled(!enabled);
        btnNovo.setEnabled(!enabled && !menuSelecaoMultipla.isChecked());
        btnAlterar.setEnabled(!enabled && !tbCadastroWhatsapp.isEmpty());
        btnExcluir.setEnabled(!enabled && !tbCadastroWhatsapp.isEmpty());
        if (!menuHabilitaNavegacao.isChecked()) {
            // menu popup habilitar navegação
            btnProximo.setEnabled(!enabled && !tbCadastroWhatsapp.isEmpty());
            btnAnterior.setEnabled(!enabled && !tbCadastroWhatsapp.isEmpty());
        }
        btnAceitar.setEnabled(!enabled && !tbCadastroWhatsapp.isEmpty());
        btnCancelar.setEnabled(enabled);
        btnSalvar.setEnabled(enabled);
        btnSalvarContinuar.setEnabled(enabled && !menuSelecaoMultipla.isChecked());
        menuSelecaoMultipla.setVisible(!enabled);
        strXApiToken.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        strUrlApi.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        strTokenApi.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        chkAtivo.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        edtUsuario.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        edtSenha.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        edCelular.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        edCodEmpresa.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        edDescricao.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        cbbLeadzapReceptivo.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        edtEmailNotificacaoApi.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        btnUploadImage.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        btnLimparImagem.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        btnCadastrar.setEnabled(!enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.QUERYING));
        btnSincronizar.setEnabled(!enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.QUERYING));
        btnRefreshSinc.setEnabled(!enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.QUERYING));
        btnAtualizarLog.setEnabled(!enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.QUERYING));
        cbbTipoApi.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        edtInstancia.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        edtTokenInstancia.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        edtClienteToken.setEnabled(enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.INSERTING));
        btnTestarConexaoZAPI.setEnabled(!enabled && (!tbCadastroWhatsapp.isEmpty() || tbCadastroWhatsapp.getState() == TableState.QUERYING));
        // empresas
        dualListEmpresasVinc.setEnabled(enabled);
        // clientes
        cbbUf.setEnabled(enabled);
        edtCidades.setEnabled(enabled);
        btnPesquisarCid.setEnabled(enabled);
        gridCidDisp.setEnabled(enabled);
        gridCidSel.setEnabled(enabled);
        btnAddCidades.setEnabled(enabled);
        btnDelCidades.setEnabled(enabled);
    }

    @Override
    public void btnUploadImageClick(final Event event) {
        if (tbCadastroWhatsapp.getState() != TableState.INSERTING && tbCadastroWhatsapp.getState() != TableState.MODIFYING) {
            Dialog.showMessage("Não esta em inclusão/edição");
            return;
        }
        FileUpload.get("Upload Imagem Perfil", "Upload", (UploadEvent t) -> {
            byte[] imageBytes = ImageUtil.streamToByteArray(t.getStreamData());
            tbCadastroWhatsapp.setIMAGEM(imageBytes);
        });
    }

    @Override
    public void tbCadastroWhatsappAfterScroll(final Event event) {
        btnCadastrar.setVisible(tbCadastroWhatsapp.getPHONE_NUMBER_TOKEN().isNull());
        // btnExcluir.setVisible(tbCadastroWhatsapp.getPHONE_NUMBER_TOKEN().isNull());
        btnExcluir.setVisible(false);
        btnSincronizar.setVisible(!tbCadastroWhatsapp.getPHONE_NUMBER_TOKEN().isNull());
        btnRefreshSinc.setVisible(!tbCadastroWhatsapp.getPHONE_NUMBER_TOKEN().isNull());
        dadosZapiVisivel();
        lblMensagem.setCaption("Selecionado: "+tbCadastroWhatsapp.getDESCRICAO().asString());
        atualizarStatus();
        atualizarLog();
        FreedomUtilities.invokeLater(() -> {
            invoke();
        });
    }

    private void invoke() {
        FHBox11.invalidate();
        //FVBox3.invalidate();
    }

    @Override
    public void btnLimparImagemClick(final Event event) {
        if (tbCadastroWhatsapp.getState() != TableState.INSERTING && tbCadastroWhatsapp.getState() != TableState.MODIFYING) {
            Dialog.showMessage("Não esta em edição");
            return;
        }
        try {
            tbCadastroWhatsapp.setIMAGEM(null);
        } catch (DataException ex) {
            Logger.getLogger(FrmCadastroLeadzapA.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    public void btnCadastrarClick(final Event event) {
        try {
            if (tbCadastroWhatsapp.getState() != TableState.QUERYING) {
                Dialog.showMessage("Ação não diponível em modo de edição/inclusão.\nSalve para efetuar o cadastro.");
                return;
            }
            String mensValidade = validadeLeadZap(edCodEmpresa.getValue().asInteger());
            if (!mensValidade.trim().isEmpty()) {
                if (mensValidade.contains("expirou") || mensValidade.contains("Erro")) {
                    Dialog.showMessage(mensValidade);
                    return;
                } else {
                    Dialog.showMessage(mensValidade);
                }
            }
            if (tbCadastroWhatsapp.getPHONE_NUMBER_TOKEN().isNull()) {
                Dialog.create().title("Cadastrar").message("Após o cadastro efetuado não é mais permitido alterar o número da linha. Continuar com o cadastramento? ").confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        if (tbCadastroWhatsapp.getState() == TableState.QUERYING) {
                            try {
                                cadastrarLinha();
                            } catch (Exception ex) {
                                EmpresaUtil.showError("Erro Cadastrar Telefone", ex);
                            }
                        } else {
                            Dialog.showMessage("Ação não diponível em modo de edição/inclusão.\nSalve para efetuar o cadastro.");
                        }
                    }
                });
            } else {
                // validarWhats();
            }
        } catch (Exception ex) {
            EmpresaUtil.showError("Erro Cadastrar Telefone", ex);
        }
    }

    @Override
    public void btnSincronizarClick(final Event event) {
        try {
            // validarWhats();
        } catch (Exception ex) {
            EmpresaUtil.showError("Erro ao Sincorinizar Telefone", ex);
        }
    }

    private void atualizar() {
        try {
            tbCadastroWhatsapp.refreshRecord();
            atualizarStatus();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro ao atualizar", ex);
        }
    }


    String validadeLeadZap(int i_cod_empresa) throws Exception {
        ISession i = null;
        int dias = -1;
        try {
            i = rn.getSession();
            spValidadeLeadZap.setValue("I_COD_EMPRESA", i_cod_empresa);
            spValidadeLeadZap.setValue("O_RESULT", "");
            spValidadeLeadZap.setValue("O_ID_CELULAR", 0);
            spValidadeLeadZap.call(i);
            if (!spValidadeLeadZap.getValue("O_RESULT").isNull() || spValidadeLeadZap.getValue("O_RESULT").isEmpty()) {
                try {
                    dias = Integer.parseInt(spValidadeLeadZap.getValue("O_RESULT").asString());
                } catch (Exception ex) {
                    dias = -1;
                }
            }
            i.commit();
        } catch (Exception ex) {
            if (i != null) {
                i.rollback();
                throw ex;
            }
        } finally {
            if (i != null) {
                i.close();
                i = null;
            }
        }
        if (dias > 10) {
            return "";
        } else if (dias == 0) {
            System.out.println("Usando Validade PKG;");
            return "A validade da integração LeadZap expira hoje.";
        } else if (dias <= 10) {
            System.out.println("Usando Validade PKG;");
            return "Faltam " + dias + " dias para expirar a validade da integração com a LeadZap.";
        } else {
            System.out.println("Usando Validade PKG;");
            return "A validade da integração com a LeadZap expirou, por favor entre em contato com a NBS para mais informações.";
        }
    }

    void cadastrarLinha() throws Exception {
        if (edCodEmpresa.getValue().asInteger() < 1) {
            Dialog.showMessage("Selecione a empresa que vai usar essa linha.");
            return;
        }
        tbWhatsappParametrosEmpresa.close();
        tbWhatsappParametrosEmpresa.addFilter("COD_EMPRESA");
        tbWhatsappParametrosEmpresa.addParam("COD_EMPRESA", edCodEmpresa.getValue());
        tbWhatsappParametrosEmpresa.open();
        // leadzap.actions.Telefones tel = new leadzap.actions.Telefones();
        String encodedfile = "";
        if (!tbCadastroWhatsapp.getIMAGEM().isNull()) {
            encodedfile = "data:image/jpeg;base64," + Base64.getEncoder().encodeToString(tbCadastroWhatsapp.getIMAGEM().asString().getBytes());
        }
        String retorno = "";
        //
        // try {
        // retorno = tel.adicionarNumero(
        // IntegracaoLeadZap.getUrlV1(),
        // tbCadastroWhatsapp.getCELULAR().asString(),
        // tbCadastroWhatsapp.getCELULAR_AVISO().asString(),
        // tbCadastroWhatsapp.getDESCRICAO().asString(),
        // "",
        // tbWhatsappParametrosEmpresa.getCOD_CLIENTE().asString(),
        // encodedfile);
        // } catch (Exception e) {
        // if (e instanceof leadzap.LeadzapException && e.getMessage() != null
        // && e.getMessage().contains("453")) {
        // if (!processarTelefonesCadastrados(
        // tel.listarNumero(IntegracaoLeadZap.getUrlV1(),
        // tbWhatsappParametrosEmpresa.getCOD_CLIENTE().asString()))) {
        // throw e;
        // }
        // } else {
        //
        // }
        // throw e;
        // }
        System.out.println("retorno:" + retorno);
        if (retorno != null && retorno.contains("phone_number_token")) {
            tbCadastroWhatsapp.edit();
            tbCadastroWhatsapp.setPHONE_NUMBER_TOKEN(new org.json.JSONObject(retorno).getString("phone_number_token"));
            tbCadastroWhatsapp.setSICRONIZADO("N");
            tbCadastroWhatsapp.post();
            tbCadastroWhatsapp.applyUpdates();
        }
        onConsultar();
    }

    // boolean processarTelefonesCadastrados(ArrayList telefonesCadastrados) throws Exception {
    // boolean sucesso = Boolean.FALSE;
    //
    // if (telefonesCadastrados == null) {
    // return sucesso;
    // }
    // for (Object a : telefonesCadastrados) {
    // leadzap.vo.PhoneNumbers telefonesCadastrado = (leadzap.vo.PhoneNumbers) a;
    //
    // if (!telefonesCadastrado.getPhone_number().equals(
    // "+55"
    // + tbCadastroWhatsapp.getCELULAR().asString())) {
    // continue;
    // }
    // if (tbCadastroWhatsapp.getPHONE_NUMBER_TOKEN().asString().equals(
    // "")) {
    // tbCadastroWhatsapp.edit();
    // tbCadastroWhatsapp.setPHONE_NUMBER_TOKEN(
    // telefonesCadastrado.getPhone_number_token());
    // tbCadastroWhatsapp.post();
    // }
    // if (tbCadastroWhatsapp.getWHATSAPP_ID().asString().equals(
    // "")) {
    // tbCadastroWhatsapp.edit();
    // tbCadastroWhatsapp.setWHATSAPP_ID(
    // telefonesCadastrado.getWhatsapp_id());
    // tbCadastroWhatsapp.post();
    // }
    // if (tbCadastroWhatsapp.getTEMPLATE_WHATS_DEFAULT().asString().equals("")) {
    // gerarTemplateDefault();
    // tbCadastroWhatsapp.edit();
    // tbCadastroWhatsapp.setTEMPLATE_WHATS_DEFAULT("999999");
    // tbCadastroWhatsapp.post();
    // }
    // onSalvar();
    // return true;
    // }
    // return sucesso;
    // }

    protected void onSalvar() throws DataException {
        if (!isCelularValid()) {
            return;
        }
        // executa a validação das constraint dos objetos edition
        check();
        if (!getErrorMap().isEmpty()) {
            StringBuilder strBuilder = new StringBuilder();

            getErrorMap().values().stream().forEach((s) -> {
                strBuilder.append(s).append("\n");
            });

            // manda o focu para o primeiro objeto que deu erro de constraint
            ((IFocusable)getErrorMap().keySet().iterator().next()).setFocus();

            Dialog.create()
                    .title("Erro ao validar")
                    .message("Existe validação(s) pendente...\n" + strBuilder.toString())
                    .showError();

            return;
        }

        // seta Calc. Update
        setCalcUpdate();

        // executar o metodo salvar na RN
        rn.salvar();

        // atualiza o registro
        tbCadastroWhatsapp.refreshRecord();

        habilitaComp(false);
        oper = TableState.QUERYING;
        lblMensagem.setCaption("");
    }

    protected void onExcluir() throws DataException {
        if (tbCadastroWhatsapp.getPHONE_NUMBER_TOKEN().isNull()) {
            if (!tbCadastroWhatsapp.isEmpty()) {
                oper = TableState.DELETING;
                String titulo;
                String mensagem;
                if (menuSelecaoMultipla.isChecked()) {
                    titulo = "Exclusão Multipla";
                    mensagem = "ATENÇÃO: Serão excluido(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?";
                } else {
                    titulo = "Exclusão de Registro";
                    mensagem = "Confirma a exclusão do registro selecionado?";
                }

                Dialog.create()
                        .title(titulo)
                        .message(mensagem)
                        .confirmSimNao((String dialogResult) -> {
                            if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                                try {
                                    try {
                                        tbCadastroWhatsapp.disableControls();
                                        tbCadastroWhatsapp.disableMasterTable();
                                        if (menuSelecaoMultipla.isChecked()) {
                                            for (int bm : gridPrincipal.getSelectedIndices(false)) {
                                                tbCadastroWhatsapp.gotoBookmark(bm);
                                                rn.excluiTableMaster();
                                            }
                                        } else {
                                            rn.excluiTableMaster();
                                        }

                                        try {
                                            rn.excluir();

                                            habilitaComp(false);
                                        } catch (DataException e) {
                                            throw e;
                                        }
                                    } finally {
                                        tbCadastroWhatsapp.enableControls();
                                        tbCadastroWhatsapp.enableMasterTable();
                                        oper = TableState.QUERYING;
                                    }
                                } catch (DataException ex) {
                                    Dialog.create()
                                            .title("Erro ao excluir")
                                            .message(ex.getMessage())
                                            .showException(ex);

                                    try {
                                        tbCadastroWhatsapp.cancelUpdates();
                                    } catch (DataException ex1) {
                                        Dialog.create()
                                                .title("Erro no CancelUpdates ao excluir")
                                                .message(ex.getMessage())
                                                .showException(ex1);
                                    }

                                }
                            }
                        });
            } else {
                Dialog.create()
                        .title("Erro ao excluir")
                        .message("Selecione um registro antes de excluir")
                        .showError();
            }
        } else {
            Dialog.showMessage("Aviso", "Telefone já foi cadastrado no leadzap e não pode ser excluido!");
        }
    }

    boolean isCelularValid() throws DataException {
        if (!edCelular.getValue().isNull()) {
            if (edCelular.getValue().asString().length() > 14 || edCelular.getValue().asString().length() < 10) {
                Dialog.showMessage("Aviso", "Telefone inválido!");
                return false;
            }
            String vCelular = "";
            if (edCelular.getValue().asString().length() > 10) {
                vCelular = edCelular.getValue().asString().substring(edCelular.getValue().asString().length() - 11, edCelular.getValue().asString().length());
            } else {
                vCelular = edCelular.getValue().asString();
            }
            tbCadastroWhatsappValidar.close();
            tbCadastroWhatsappValidar.clearFilters();
            tbCadastroWhatsappValidar.clearParams();
            tbCadastroWhatsappValidar.addFilter("CELULAR_LIKE");
            tbCadastroWhatsappValidar.addParam("CELULAR", vCelular);
            tbCadastroWhatsappValidar.open();
            if (!tbCadastroWhatsappValidar.isEmpty()) {
                if (!tbCadastroWhatsappValidar.getID_CELULAR().asInteger().equals(tbCadastroWhatsapp.getID_CELULAR().asInteger())) {
                    Dialog.showMessage("Aviso", "Telefone já cadastrado!");
                    return false;
                }
            }
        }
        if (!edCodEmpresa.getValue().isNull()) {
            tbCadastroWhatsappValidar.close();
            tbCadastroWhatsappValidar.clearFilters();
            tbCadastroWhatsappValidar.clearParams();
            tbCadastroWhatsappValidar.addFilter("COD_EMPRESA");
            tbCadastroWhatsappValidar.addParam("COD_EMPRESA", edCodEmpresa.getValue());
            tbCadastroWhatsappValidar.open();
            tbCadastroWhatsappValidar.first();
            tbCadWhatsappAtivo.close();
            tbCadWhatsappAtivo.addParam("COD_EMPRESA", tbCadastroWhatsapp.getCOD_EMPRESA().asInteger());
            tbCadWhatsappAtivo.open();
            if (!tbCadWhatsappAtivo.isEmpty()) {
                if (tbCadWhatsappAtivo.getQTDE().asInteger() >= 1) {
                    if (!tbCadastroWhatsappValidar.isEmpty()) {
                        while (!tbCadastroWhatsappValidar.eof()) {
                            if (!tbCadastroWhatsappValidar.getID_CELULAR().asInteger().equals(tbCadastroWhatsapp.getID_CELULAR().asInteger())) {
                                Dialog.showMessage("Aviso", "Essa empresa já tem um telefone cadastrado!");
                                return false;
                            }
                            tbCadastroWhatsappValidar.next();
                        }
                    }
                }
            }
        }
        return true;
    }

    /// @Override
    public void tbEmpresasBeforeOpen(Event<Object> event) {
        tbEmpresas.clearFilters();
        tbEmpresas.clearParams();
        tbEmpresas.addFilter("STATUS");
        tbEmpresas.addParam("STATUS", "S");
        tbEmpresas.addFilter("COD_MATRIZ");
        tbEmpresas.setOrderBy("NOME");
    }

    private void atualizarStatus() {
        if (tbCadastroWhatsapp.getSICRONIZADO().asString().equals("S")) {
            lblStatusSinc.setCaption("Sincronizado");
            lblStatusSinc.setFontColor("clGreen");
        } else {
            lblStatusSinc.setCaption("Não Sincronizado");
            lblStatusSinc.setFontColor("clRed");
        }
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        try {
            if (incluindo) {
                if (!tbCadastroWhatsapp.getCOD_EMPRESA().isNull()) {
                    tbCadWhatsappAtivo.close();
                    tbCadWhatsappAtivo.addParam("COD_EMPRESA", tbCadastroWhatsapp.getCOD_EMPRESA().asInteger());
                    tbCadWhatsappAtivo.open();
                    if (!tbCadWhatsappAtivo.isEmpty()) {
                        if (tbCadWhatsappAtivo.getQTDE().asInteger() >= 1) {
                            pgPrincipal.selectTab(tabCadastro);
                            EmpresaUtil.showMessage("Atenção", "Empresa já tem um numero Cadastrado e Ativo.");
                            return;
                        }
                    }
                }
            }
            tbCadastroWhatsapp.post();
            if (strXApiToken.getValue().asString().equals("")) {
                FreedomUtilities.invokeLater(() -> {
                    strXApiToken.setFocus();
                });
                pgPrincipal.selectTab(tabCadastro);
                EmpresaUtil.showWarning("Atenção", "Obrigatório Informar o X-API-TOKEN.");
                return;
            }
            if (strUrlApi.getValue().asString().equals("")) {
                FreedomUtilities.invokeLater(() -> {
                    strUrlApi.setFocus();
                });
                pgPrincipal.selectTab(tabCadastro);
                EmpresaUtil.showWarning("Atenção", "Obrigatório Informar a URL API.");
                return;
            }
            if (strTokenApi.getValue().asString().equals("")) {
                FreedomUtilities.invokeLater(() -> {
                    strTokenApi.setFocus();
                });
                pgPrincipal.selectTab(tabCadastro);
                EmpresaUtil.showWarning("Atenção", "Obrigatório Informar a URL Painel web.");
                return;
            }
            if (tbWhatsappEmpresa.count() == 0) {
                filtrarTabAuxCruzamento();
                pgPrincipal.selectTab(tabCruzaEmpresa);
                EmpresaUtil.showWarning("Atenção", "Obrigatório ter pelo menos uma empresa vinculado.");
                return;
            }
            if (edtUsuario.getValue().asString().equals("")) {
                FreedomUtilities.invokeLater(() -> {
                    edtUsuario.setFocus();
                });
                pgPrincipal.selectTab(tabCadastro);
                EmpresaUtil.showWarning("Atenção", "Obrigatório Informar o Usuário.");
                return;
            }
            if (edtSenha.getValue().asString().equals("")) {
                FreedomUtilities.invokeLater(() -> {
                    edtSenha.setFocus();
                });
                pgPrincipal.selectTab(tabCadastro);
                EmpresaUtil.showWarning("Atenção", "Obrigatório Informar a senha.");
                return;
            }

            if (cbbTipoApi.getValue().asString().equals("Z-API")){
                if (edtInstancia.getValue().asString().isEmpty()){
                    FreedomUtilities.invokeLater(() -> {
                        edtInstancia.setFocus();
                    });
                    pgPrincipal.selectTab(tabCadastro);
                    EmpresaUtil.showWarning("Atenção", "Obrigatório Informar instância.");
                    return;
                }
                if (edtTokenInstancia.getValue().asString().isEmpty()){
                    FreedomUtilities.invokeLater(() -> {
                        edtTokenInstancia.setFocus();
                    });
                    pgPrincipal.selectTab(tabCadastro);
                    EmpresaUtil.showWarning("Atenção", "Obrigatório Informar a o Token Instância.");
                    return;
                }
                if (edtClienteToken.getValue().asString().isEmpty()){
                    FreedomUtilities.invokeLater(() -> {
                        edtClienteToken.setFocus();
                    });
                    pgPrincipal.selectTab(tabCadastro);
                    EmpresaUtil.showWarning("Atenção", "Obrigatório Informar a Cliente Token");
                    return;
                }
            }

            tbCadastroWhatsapp.edit();
            tbCadastroWhatsapp.setAPI_SENHA(cript.cript(tbCadastroWhatsapp.getAPI_SENHA().asString()));
            if (!validarUsuarioSenha(true)) {
                return;
            }

            if (tbCadastroWhatsappCidades.count() > 0) {
                EmpresaUtil.showWarning("Atenção.", "Ao vincular determinada cidade, os disparos serão gerados somente para as cidades informadas na aba \"Cidades\".");
            }

            if(StringUtils.isNotBlank(tbCadastroWhatsapp.getAPI_USUARIO().asString())){
                String userTemp = tbCadastroWhatsapp.getAPI_USUARIO().asString().toUpperCase();
                tbCadastroWhatsapp.setAPI_USUARIO(userTemp);
            }

        } catch (DataException ex) {
            EmpresaUtil.showError("Erro ao Salvar", ex);
        }
        try {
            if (menuSelecaoMultipla.isChecked()) {
                onSalvarMultiplo();
            } else {
                onSalvar();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao salvar")
                    .message(e.getMessage())
                    .showException(e);
        }
        atualizar();
        incluindo = false;
    }

    @Override
    public void btnRefreshSincClick(final Event<Object> event) {
        // getStatusLeadZap();
    }

    @Override
    public void btnAlterarClick(Event<Object> event) {
        // To change body of generated methods, choose Tools | Templates.
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao alterar")
                    .message(e.getMessage())
                    .showException(e);
        }
        celularOld = tbCadastroWhatsapp.getCELULAR().asString();

        // edCelular.setEnabled(false);
        incluindo = false;
        carregouCidades = false;
        if (!tbCadastroWhatsapp.getAPI_SENHA().asString().equals("")) {
            try {
                tbCadastroWhatsapp.setAPI_SENHA(cript.decript(tbCadastroWhatsapp.getAPI_SENHA().asString()));
            } catch (DataException ignore) {
            }
        }
    }

    @Override
    public void pgPrincipalChange(Event<Object> event) {
        if (pgPrincipal.getSelectedTab().equals(tabCruzaEmpresa)) {
            if (tbCadastroWhatsapp.getID_CELULAR().asInteger() > 0) {
                filtrarTabAuxCruzamento();
            }
        }
        if (pgPrincipal.getSelectedTab().equals(tabCidades)) {
            if (tbCadastroWhatsapp.getID_CELULAR().asInteger() > 0) {
                carregarComboUf();
                cbbUf.setValue(ufSel);
                if (!carregouCidades) {
                    carregarGridCidades();
                    carregaCidadesCruzadas();
                    carregouCidades = true;
                }
            }
        }
    }

    private void filtrarTabAuxCruzamento() {
        try {
            if (tbEmpresasCruzaLeadZap.count() == 0) {
                rn.filtrarEmpresasFiltrarCruzamento();
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("OPS. Ocorreu um erro inesperado", ex);
        }
    }

    private void atualizarLog() {
        try {
            rn.filtrarLog(tbCadastroWhatsapp.getID_CELULAR().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("OPS. Ocorreu um erro inesperado LOG", ex);
        }
    }

    @Override
    public void btnAtualizarLogClick(final Event event) {
        atualizarLog();
    }

    @Override
    public void btnNovoClick(final Event event) {
        // a chamada do super garante a correta utilizacao da validacao de acesso
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao incluir")
                    .message(e.getMessage())
                    .showException(e);
        }
        edCelular.setEnabled(true);
        incluindo = true;
        carregouCidades = false;
    }

    @Override
    public void btnTestarClick(final Event event) {
        if (validarUsuarioSenha(btnAlterar.isEnabled())) {
            EmpresaUtil.showMessage("CRMService", "Usuário e Senha validado com sucesso.");
        }
    }

    private boolean validarUsuarioSenha(boolean criptografado) {
        try {
            String pwd = "";
            if (criptografado) {
                pwd = cript.decript(tbCadastroWhatsapp.getAPI_SENHA().asString());
            } else {
                pwd = tbCadastroWhatsapp.getAPI_SENHA().asString();
            }
            String xSchemas = wl.get("DATASOURCE_DEFAULT").asString();
//            String validou = loginRna.validarLogin(edtUsuario.getValue().asString(), pwd, ApplicationUtil.getValue(DATASOURCE_SELECIONADO), true);
            String validou = loginRna.validarLogin(edtUsuario.getValue().asString(), pwd, xSchemas, true);

            if (validou.equals("")) {
                return true;
            } else {
                EmpresaUtil.showMessage("CRMService", validou.toString());
                return false;
            }
        } catch (Exception ex) {
            EmpresaUtil.showError("Ocorreu erro ao validar usuário!", ex);
            return false;
        }
    }

    private void carregarComboUf() {
        try {
            rn.carregarComboUf();
        } catch (DataException ex) {
            EmpresaUtil.showError("OPS. Ocorreu um erro ao carregar UF", ex);
        }
    }

    private void carregarGridCidades() {
        try {
            rn.carregarGridCidades(cbbUf.getValue().asString(), edtCidades.getValue().asString(), tbCadastroWhatsapp.getID_CELULAR().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("OPS. Ocorreu um erro ao pesquisar Cidades", ex);
        }
    }

    @Override
    public void btnPesquisarCidClick(final Event event) {
        carregarGridCidades();
    }

    @Override
    public void cbbUfChange(Event<Object> event) {
        ufSel = cbbUf.getValue().asString();
        carregarGridCidades();
        carregarGridCidades();
    }

    @Override
    public void edtCidadesEnter(Event<Object> event) {
        carregarGridCidades();
    }

    @Override
    public void btnAddCidadesClick(final Event event) {
        try {
            if (tbCadastroWhatsappCidades.getCOD_CIDADES().asInteger() > 0) {
                tbCadastroWhatsappCidades.first();
                while (!tbCadastroWhatsappCidades.eof()) {
                    if (tbCidades.getCOD_CIDADES().asInteger().equals(tbCadastroWhatsappCidades.getCOD_CIDADES().asInteger()) && tbCidades.getUF().asString().equals(tbCadastroWhatsappCidades.getUF().asString())) {
                        EmpresaUtil.showWarning("Atenção", "UF/Cidade já adicionado as tabela Selecionada");
                        return;
                    }
                    tbCadastroWhatsappCidades.next();
                }
            }
            addCidadesCruzadas();
        } catch (DataException ex) {
            EmpresaUtil.showError("OPS. Ocorreu um erro ao Adicionar Cidades", ex);
        }
    }

    @Override
    public void btnDelCidadesClick(final Event event) {
        delCidadesCruzadas();
    }

    private void addCidadesCruzadas() {
        try {
            rn.addCidadesCruzadas(tbCadastroWhatsapp.getID_CELULAR().asInteger(), tbCidades.getUF().asString(), tbCidades.getCOD_CIDADES().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("OPS. Ocorreu um erro ao Adicionar Cidades", ex);
        }
    }

    private void delCidadesCruzadas() {
        try {
            rn.delCidadesCruzadas();
        } catch (DataException ex) {
            EmpresaUtil.showError("OPS. Ocorreu um erro ao Remover Cidades", ex);
        }
    }

    private void carregaCidadesCruzadas() {
        try {
            rn.carregaCidadesCruzadas(tbCadastroWhatsapp.getID_CELULAR().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("OPS. Ocorreu um erro ao Carregar Cidades Cruzadas", ex);
        }
    }

    @Override
    public void tbCidadesMaxRow(Event<Object> event) {
    }

    @Override
    public void tbCadastroWhatsappCidadesMaxRow(Event<Object> event) {
    }

    private void carregarLeadzapMenu() {
        try {
            rn.carregarLeadzapMenu();
        } catch (DataException ex) {
            EmpresaUtil.showError("OPS. Ocorreu um erro ao carregar Menu", ex);
        }
    }

    @Override
    public void cbbLeadzapReceptivoClearClick(final Event<Object> event) {
        cbbLeadzapReceptivo.clear();
    }

    @Override
    public void btnTestarConexaoZAPIClick(Event<Object> event) {
        int idCelular = tbCadastroWhatsapp.getID_CELULAR().asInteger();
        if (idCelular == 0){
            EmpresaUtil.showInformationMessage("Numero de celular Ainda não cadastrado");
            return;
        }
        callChatQrCodeZApi(idCelular);
    }


    public void callChatQrCodeZApi(int idCelular) {
        boolean reponsive = !wl.get("MOBILE_RESPONSIVE").isNull();
        FrmFrameChatQRA chat = new FrmFrameChatQRA();
        if (!reponsive) {
            chat.setWidth(400);
            chat.setHeight(450);
        } else {
            chat.setWidth(400);
            chat.setHeight(450);
        }
        if (chat.prepareChat(idCelular)) {
            FormUtil.doShow(chat, (EventListener) t -> {
            });
        }
    }

    @Override
    public void cbbTipoApiChange(Event<Object> event) {
        dadosZapiVisivel();
        FreedomUtilities.invokeLater(() -> {
            FVBox2.invalidate();
        });
    }

    public void dadosZapiVisivel(){
        hboxDadosZapi.setVisible(tbCadastroWhatsapp.getAPI_TIPO().asString().equals("Z-API"));
    }

}
