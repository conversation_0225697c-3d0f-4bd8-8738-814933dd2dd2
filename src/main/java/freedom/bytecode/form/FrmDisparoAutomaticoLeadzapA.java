package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmDisparoAutomaticoLeadzapW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmDisparoAutomaticoLeadzapA extends FrmDisparoAutomaticoLeadzapW {

    private static final long serialVersionUID = 20130827081850L;

    public FrmDisparoAutomaticoLeadzapA() {
        tbDisparoChatbot.setOrderBy("A.ID_ITEM");
        vBoxChatbot.setEnabled(false);
    }

    @Override
    public void tbDisparoBeforeOpen(Event<Object> event) {
        tbDisparo.addFilter("SISTEMA");
        tbDisparo.addParam("SISTEMA", "S");
    }

    @Override
    public void tbDisparoAfterOpen(Event<Object> event) {

    }

    @Override
    public void tbDisparoAfterScroll(Event<Object> event) {

    }

    @Override
    public void tbDisparoBeforeScroll(Event<Object> event) {

    }

    @Override
    public void btnSalvarChatbotClick(final Event event) {
        try {
            habilarCompChatbot(false);
            btnSalvarChatbot.setEnabled(false);
            btnCancelarChatbot.setEnabled(false);
            btnAlterarChatbot.setEnabled(true);
            gridChatbot.setEnabled(true);
            gbDetalhe46001.setEnabled(true);
            tbDisparoChatbot.post();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnAlterarChatbotClick(final Event event) {
        try {
            habilarCompChatbot(true);
            btnSalvarChatbot.setEnabled(true);
            btnCancelarChatbot.setEnabled(true);
            btnAlterarChatbot.setEnabled(false);
            gridChatbot.setEnabled(false);
            gbDetalhe46001.setEnabled(false);
            tbDisparoChatbot.edit();

            //Regra passada por Orione - 27/04/2020 [Fábio Ramires]
            //Link só pro 1
            //Motivo de Perda pro 2,3,4 e 5
            switch (tbDisparoChatbot.getID_ITEM().asInteger()) {
                case 1:
                    edtLink.setEnabled(true);
                    cbbMotivoPerda.setEnabled(false);
                    break;
                case 2:
                    edtLink.setEnabled(false);
                    cbbMotivoPerda.setEnabled(true);
                    break;
                case 3:
                    edtLink.setEnabled(false);
                    cbbMotivoPerda.setEnabled(true);
                    break;
                case 4:
                    edtLink.setEnabled(false);
                    cbbMotivoPerda.setEnabled(true);
                    break;
                case 5:
                    edtLink.setEnabled(false);
                    cbbMotivoPerda.setEnabled(true);
                    break;
                case 99:
                    edtLink.setEnabled(false);
                    cbbMotivoPerda.setEnabled(false);
                    edtMensagem.setEnabled(false);
                    break;
                default:
                    break;
            }
            edtDescOpcional.setFocus();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnCancelarChatbotClick(final Event event) {
        try {
            habilarCompChatbot(false);
            btnSalvarChatbot.setEnabled(false);
            btnCancelarChatbot.setEnabled(false);
            btnAlterarChatbot.setEnabled(true);
            tbDisparoChatbot.cancel();
            gridChatbot.setEnabled(true);
            gbDetalhe46001.setEnabled(true);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void habilarCompChatbot(boolean habilitar) {
        edtDescOpcional.setEnabled(habilitar);
        edtLink.setEnabled(habilitar);
        cbbMotivoPerda.setEnabled(habilitar);
        edtMensagem.setEnabled(habilitar);
        edtMascara.setEnabled(habilitar);
    }

    @Override
    public void btnAlterarClick(final Event event) {
        super.btnAlterarClick(event);
        btnAlterarChatbot.setEnabled(true);
        edDescricao44001.setEnabled(false);
        vBoxChatbot.setEnabled(true);
    }

    @Override
    protected void onAbreTabelaAux() throws DataException {
        super.onAbreTabelaAux();
        rn.filtrarDescartes();
        rn.filtrarCadastroWhatsApp();
    }

    @Override
    public void btnCancelarClick(Event<Object> event) {
        btnCancelarChatbotClick(event);
        super.btnCancelarClick(event);
        btnAlterarChatbot.setEnabled(false);
        vBoxChatbot.setEnabled(false);
        pgPrincipal.selectTab(0);
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        try {
            if (!rn.validarMascara()) {
                EmpresaUtil.showWarning("Atenção", "Não pode existir sequência com o mesmo número duplicado verifique.");
                return;
            }
        } catch (DataException e) {
            EmpresaUtil.showError("OPS! Ocorreu um erro inesperado", e);
        }

        btnSalvarChatbotClick(event);
        super.btnSalvarClick(event);
        btnAlterarChatbot.setEnabled(false);
        pgPrincipal.selectTab(0);
    }

    @Override
    public void btnNovoClick(final Event event) {
        //a chamada do super garante a correta utilizacao da validacao de acesso
        super.btnNovoClick(event);
    }

    @Override
    public void btnConsultarClick(final Event event) {
        //a chamada do super garante a correta utilizacao da validacao de acesso
        super.btnConsultarClick(event);
        if (!tbDisparo.isEmpty()) {
            lblMensagem.setVisible(true);
            lblMensagem.setValue("Registro Selecionado: " + tbDisparo.getDESCRICAO().asString().trim());
        }
    }


}
