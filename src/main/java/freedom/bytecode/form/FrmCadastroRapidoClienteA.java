package freedom.bytecode.form;

import freedom.bytecode.cursor.CIDADES;
import freedom.bytecode.form.wizard.FrmCadastroRapidoClienteW;
import freedom.bytecode.rn.CampoFoco;
import freedom.client.controls.ITFTabsheet;
import freedom.client.controls.impl.*;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.FreedomUtilities;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.report.ReportException;
import freedom.util.*;
import freedom.util.assinaturaDigital.CrmAssinaturaDigital;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalUtils;
import freedom.util.assinaturaDigital.strategy.OsTermoLgpdStrategy;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;
import org.zkoss.zk.ui.Component;
import org.zkoss.zul.Vlayout;
import org.zkoss.zul.Window;
import java.io.File;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FrmCadastroRapidoClienteA extends FrmCadastroRapidoClienteW {

    private static final String FALHA_AO_BUSCAR_CLIENTE_MOTIVO = "Falha ao buscarCliente. Motivo: ";

    private static final String UF_RES = "UF_RES";

    private static final String COD_CID_RES = "COD_CID_RES";

    private static final String CEP_RES = "CEP_RES";

    private static final String RUA_RES = "RUA_RES";

    private static final String BAIRRO_RES = "BAIRRO_RES";

    private static final String COMPLEMENTO_RES = "COMPLEMENTO_RES";

    private static final String FACHADA_RES = "FACHADA_RES";

    private static final String UF_COM = "UF_COM";

    private static final String COD_CID_COM = "COD_CID_COM";

    private static final String CEP_COM = "CEP_COM";

    private static final String RUA_COM = "RUA_COM";

    private static final String BAIRRO_COM = "BAIRRO_COM";

    private static final String COMPLEMENTO_COM = "COMPLEMENTO_COM";

    private static final String FACHADA_COM = "FACHADA_COM";

    private static final String UF_COBRANCA = "UF_COBRANCA";

    private static final String COD_CID_COBRANCA = "COD_CID_COBRANCA";

    private static final String CEP_COBRANCA = "CEP_COBRANCA";

    private static final String RUA_COBRANCA = "RUA_COBRANCA";

    private static final String BAIRRO_COBRANCA = "BAIRRO_COBRANCA";

    private static final String COMPLEMENTO_COBRANCA = "COMPLEMENTO_COBRANCA";

    private static final String FACHADA_COBRANCA = "FACHADA_COBRANCA";

    private static final String DADOS_FISICOS_CPF = "DADOS_FISICOS.CPF";

    private static final String CLIENTES_UF_RES = "CLIENTES.UF_RES";

    private static final String CLIENTES_COD_CID_RES = "CLIENTES.COD_CID_RES";

    private static final String CLIENTES_CEP_RES = "CLIENTES.CEP_RES";

    private static final String CLIENTES_RUA_RES = "CLIENTES.RUA_RES";

    private static final String CLIENTES_BAIRRO_RES = "CLIENTES.BAIRRO_RES";

    private static final String CLIENTES_FACHADA_RES = "CLIENTES.FACHADA_RES";

    private static final String CLIENTES_UF_COM = "CLIENTES.UF_COM";

    private static final String CLIENTES_COD_CID_COM = "CLIENTES.COD_CID_COM";

    private static final String CLIENTES_CEP_COM = "CLIENTES.CEP_COM";

    private static final String CLIENTES_RUA_COM = "CLIENTES.RUA_COM";

    private static final String CLIENTES_BAIRRO_COM = "CLIENTES.BAIRRO_COM";

    private static final String CLIENTES_FACHADA_COM = "CLIENTES.FACHADA_COM";

    private static final String CLIENTES_UF_COBRANCA = "CLIENTES.UF_COBRANCA";

    private static final String CLIENTES_COD_CID_COBRANCA = "CLIENTES.COD_CID_COBRANCA";

    private static final String CLIENTES_CEP_COBRANCA = "CLIENTES.CEP_COBRANCA";

    private static final String CLIENTES_RUA_COBRANCA = "CLIENTES.RUA_COBRANCA";

    private static final String CLIENTES_BAIRRO_COBRANCA = "CLIENTES.BAIRRO_COBRANCA";

    private static final String CLIENTES_FACHADA_COBRANCA = "CLIENTES.FACHADA_COBRANCA";

    private static final String DESCRICAO = "DESCRICAO";

    private static final String CIDADE_NAO_USA_CEP_POR_RUA = "Cidade não usa CEP por Rua.";

    private static final String COPIADO = "Copiado";

    private static final String COPIAR = "Copiar";

    private static final String NENHUM_CAMPO_DE_ENDERECO_FOI_PREENCHIDO_VERIFIQUE = "Nenhum campo de Endereço foi preenchido. Verifique!";

    private static final String NENHUM_CAMPO_DE_ENDERECO_FOI_COPIADO_VERIFIQUE = "Nenhum campo de Endereço foi copiado. Verifique!";

    private static final String APPLICATION_NAME = "APPLICATION_NAME";

    public static final String INFORMACAO = "Informação";

    private double clienteCpf = 0.0;

    private double clienteCnpj = 0.0;

    private final boolean ehCRMParts = EmpresaUtil.isCrmParts();

    @Getter
    private boolean fechadoAoSalvar = false;

    @Override
    public void lblSintegraMultiIeClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    private void exibirSintegraMultiplasIe() {
        if (StringUtils.isNotBlank(this.lblSintegraMultiIe.getHint())) {
            EmpresaUtil.showInformationMessage("Inscrições Estaduais "
                    + System.lineSeparator()
                    + this.lblSintegraMultiIe.getHint().replace(",",
                    ("," + System.lineSeparator())));
        }
    }

    private static final long serialVersionUID = 20130827081850L;

    private final IWorkList wl = WorkListFactory.getInstance();

    private final boolean isResponsive = !wl.get("MOBILE_RESPONSIVE").isNull();

    private Double codCliente = -1.0;

    private boolean incluirEndClick = false;

    private final List<CampoFoco> edits = new ArrayList<>();

    @Getter
    private boolean fechou = false;

    @Getter
    private int prefixoCel = 0;

    @Getter
    private int telefoneCel = 0;

    public boolean isSalvar = true;

    private String tipoEnderecoCopy;

    private String ufCopy;

    private Integer cidadeCopy;

    private String cepCopy;

    private String ruaCopy;

    private String bairroCopy;

    private String complementoCopy;

    private String numeroCopy;

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private final int codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();

    private boolean usaApiConsulta = false;

    private String schemaAtual;

    private double codClienteConsultadoApi = 0.0;

    private boolean ehRodobens = false;

    private static final String DATA_INVALIDA_BEFORE = "01/01/1900";

    public FrmCadastroRapidoClienteA() {
        this.lblTempoConsultaCEP.setVisible(false);
        this.lblDicaAPIViaCEP.setVisible(false);
        this.lblDicaAPIViaCEP02.setVisible(false);
        this.hBoxDicaAPIViaCEP.setVisible(false);
        /*
        this.hBoxChecksCRM.setHeight(115);
        */
        this.hBoxChecksCRM.invalidate();
        this.btnConsultaIntegracaoCnpj.setVisible(false);
        this.btnConsultaIntegracaoCpf.setVisible(false);
        this.btnExibirDadosConsultaAPIIntegracao.setVisible(false);
        this.preencherEditsValidacao();
        this.reiniciarMensagemValidacao();
    }



    private void marcarEditsValidacao() {
        String ehAlteracao;
        if (this.codCliente <= 0.0) {
            ehAlteracao = "N";
        } else {
            ehAlteracao = "S";
        }
        try {
            String campos = this.rn.camposParaValidacao(this.codEmpresaUsuarioLogado,
                    ehAlteracao);
            if (StringUtils.isBlank(campos)) {
                return;
            }
            CampoFoco obj = null;
            String[] camposFoco = campos.split(";");
            for (String field : camposFoco) {
                boolean achouCampo = false;
                for (CampoFoco edit : edits) {
                    obj = edit;
                    if (obj.getField().equals((field.toUpperCase()))) {
                        achouCampo = true;
                        break;
                    }
                }
                if (achouCampo) {
                    if (obj.getEdit() instanceof TFDecimal) {
                        ((TFDecimal) obj.getEdit()).setRequired(true);
                    }
                    if (obj.getEdit() instanceof TFInteger) {
                        ((TFInteger) obj.getEdit()).setRequired(true);
                    }
                    if (obj.getEdit() instanceof TFString) {
                        ((TFString) obj.getEdit()).setRequired(true);
                    }
                    if (obj.getEdit() instanceof TFCombo) {
                        ((TFCombo) obj.getEdit()).setRequired(true);
                    }
                    if (obj.getEdit() instanceof TFDate) {
                        ((TFDate) obj.getEdit()).setRequired(true);
                    }
                }
            }
        } catch (DataException e) {
            // Nao conseguiu marcar os campos
        }
    }

    public Double getCodigoNovoCliente() {
        return this.codCliente;
    }

    private boolean ehSituacaoValidaCadastroSintegra() {
        boolean tbConsultaNbsSintegraDadosEmpty = this.tbConsultaNbsSintegraDados.isEmpty();
        if (tbConsultaNbsSintegraDadosEmpty) {
            return false;
        }
        String situacaoCadastral = this.tbConsultaNbsSintegraDados.getSITUACAO_CADASTRAL().asString();
        if (StringUtils.isBlank(situacaoCadastral)) {
            return false;
        }
        try {
            return this.rn.ehSituacaoValidaCadastroSintegra(situacaoCadastral,
                    "novo");
        } catch (DataException dataException) {
            return false;
        }
    }

    public boolean buscarCliente(Double codCliente) {
        boolean result = false;
        try {
            Date dataAtual = new Date();
            String strHora;
            String strData;
            String strDataHora;
            Date dataHoraFormat;
            SimpleDateFormat dataFormat = new SimpleDateFormat("dd/MM/yyyy");
            DateFormat dhFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            this.btnPesquisaCep1.setEnabled(this.isSalvar);
            this.btnPesquisaCep2.setEnabled(this.isSalvar);
            this.btnPesquisaCep3.setEnabled(this.isSalvar);
            this.btnSalvar.setEnabled(this.isSalvar);
            if (codCliente <= 0) {
                codCliente = -1.0;
            }
            result = this.rn.buscarCliente(codCliente);
            if (result) {
                this.codCliente = codCliente;
                marcarEditsValidacao();
                if (!this.tbClientesProfissaoInclusao.isActive()) {
                    this.rn.abrirTabelaAux(this.cboTipo.getValue().asString());
                }
                this.cboTipo.setEnabled(false);
                this.edtCPF.setEnabled(false);
                this.edtCNPJ.setEnabled(false);
                if ((!this.tbClienteDiverso.getTELE_HORARIO_CONTATO().isNull())
                        && (!this.tbClienteDiverso.getTELE_HORARIO_CONTATO().isEmpty())) {
                    if (this.tbClienteDiverso.getTELE_HORARIO_CONTATO().asString().trim().length() < 4) {
                        strHora = "00:00:00";
                    } else {
                        strHora = this.tbClienteDiverso.getTELE_HORARIO_CONTATO().asString() + ":00";
                    }
                    strData = dataFormat.format(dataAtual);
                    strDataHora = strData
                            + " "
                            + strHora;
                    dataHoraFormat = dhFormat.parse(strDataHora);
                    this.tbClienteDiverso.edit();
                    this.timeCrmMelhorHorario.setValue(dataHoraFormat);
                }
                this.incluirEndClick = false;
                if (this.cboTipo.getValue().asString().equals("F")) {
                    this.vBoxPessoaFisica.setVisible(Boolean.TRUE);
                    this.vBoxPessoaJuridica.setVisible(Boolean.FALSE);
                    boolean ehProdutorRural = "S".equals(this.tbClienteDiverso.getPRODUTOR_RURAL().asString());
                    this.vBoxFoneContatoPJ.setVisible(ehProdutorRural);
                } else {
                    this.vBoxPessoaFisica.setVisible(Boolean.FALSE);
                    this.vBoxPessoaJuridica.setVisible(Boolean.TRUE);
                    this.vBoxFoneContatoPJ.setVisible(true);
                }
                this.rn.abrirTabelaAux(this.cboTipo.getValue().asString());
                this.btnPesquisaCep1.setEnabled(this.isSalvar);
                this.btnPesquisaCep2.setEnabled(this.isSalvar);
                this.btnPesquisaCep3.setEnabled(this.isSalvar);
                this.btnSalvar.setEnabled(this.isSalvar);
                this.preencherEndereco();
                if (!this.cboTipo.getValue().asString().equals("F")
                        && (this.tbDadosJuridicos.isActive())
                        && (this.tbDadosJuridicos.getNOME_FANTASIA().isEmpty()
                        || this.tbDadosJuridicos.getNOME_FANTASIA().isNull())) {
                    this.tbDadosJuridicos.edit();
                    if (this.tbClientes.getNOME().asString().trim().length() > 30) {
                        this.tbDadosJuridicos.setNOME_FANTASIA(this.tbClientes.getNOME().asString().trim().substring(0,
                                30));
                    } else {
                        this.tbDadosJuridicos.setNOME_FANTASIA(this.tbClientes.getNOME().asString().trim());
                    }
                    this.tbDadosJuridicos.post();
                }
            } else {
                this.marcarEditsValidacao();
                this.edtCPF.setEnabled(true);
                this.edtCNPJ.setEnabled(true);
                this.cboTipo.setEnabled(true);
                if (this.tbClientes.isActive()) {
                    boolean tbDadosJuridicosEmpty = this.tbDadosJuridicos.isEmpty();
                    if (tbDadosJuridicosEmpty) {
                        this.tbDadosJuridicos.append();
                    } else {
                        this.tbDadosJuridicos.edit();
                    }
                    boolean tbClientesEmpty = this.tbClientes.isEmpty();
                    if (tbClientesEmpty) {
                        this.tbClientes.append();
                    } else {
                        this.tbClientes.edit();
                    }
                    this.cboTipo.setValue("F");
                    this.tbClientes.setCOD_CLASSE("F");
                    this.rn.abrirTabelaAux(this.cboTipo.getValue().asString());
                    boolean tbClienteDiversoEmpty = this.tbClienteDiverso.isEmpty();
                    if (tbClienteDiversoEmpty) {
                        this.tbClienteDiverso.append();
                    } else {
                        this.tbClienteDiverso.edit();
                    }
                    boolean tbDadosFisicosEmpty = this.tbDadosFisicos.isEmpty();
                    if (tbDadosFisicosEmpty) {
                        this.tbDadosFisicos.append();
                    } else {
                        this.tbDadosFisicos.edit();
                    }
                    tbDadosJuridicosEmpty = this.tbDadosJuridicos.isEmpty();
                    if (tbDadosJuridicosEmpty) {
                        this.tbDadosJuridicos.append();
                    } else {
                        this.tbDadosJuridicos.edit();
                    }
                    this.incluirEndereco();
                    this.edUF1.setValue(this.tbParmSys.getUF().asString());
                    this.edtCidade1.setValue(this.tbParmSys.getCOD_CIDADES().asInteger());
                    this.cboNacionalidade.setValue(this.tbParmSys.getCOD_NACIONALIDADE().asInteger());
                    this.edTpEnd1.setValue(1);
                    result = true;
                }
            }
            // Acrescentado por conta do Tablet não abre campo data se estiver null
            if (this.isResponsive
                    && (this.dtNasc.getValue().isNull())) {
                this.dtNasc.setValue(new Date());
            }
        } catch (DataException | ParseException exception) {
            EmpresaUtil.showError(FALHA_AO_BUSCAR_CLIENTE_MOTIVO,
                    exception);
        }
        return result;
    }

    public String infoSituacaoCadastralIntegracao(double codEmpresa,
                                                  double codClienteApi,
                                                  String tipoPessoa,
                                                  String ie,
                                                  Value rfbSituacao,
                                                  Value rfbCadastroIrregular,
                                                  Value sintegraSituacao,
                                                  Value sintegraCadastroIsento,
                                                  Value sintegraCadastroIrregular,
                                                  Value sintegraMultiplasIe) {
        String retFuncao = "";
        try {
            retFuncao = this.rn.infoSituacaoCadastralIntegracao(codEmpresa,
                    codClienteApi,
                    tipoPessoa,
                    ie,
                    rfbSituacao,
                    rfbCadastroIrregular,
                    sintegraSituacao,
                    sintegraCadastroIsento,
                    sintegraCadastroIrregular,
                    sintegraMultiplasIe);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao obter a informação da situção cadastral da integração",
                    dataException);
        }
        return retFuncao;
    }

    private void exibirStatusCadastro() {
        this.vBoxStatusCadastro.setVisible(false);
        if (!this.usaApiConsulta) {
            return;
        }
        double codClienteApi;
        String ie = null;
        if (this.cboTipo.getValue().asString().equals("F")) {
            String cpf = this.edtCPF.getValue().asString();
            if (StringUtils.isBlank(cpf)) {
                return;
            }
            codClienteApi = Double.parseDouble(cpf.replaceAll("\\D", ""));
        } else {
            String cnpj = this.edtCNPJ.getValue().asString();
            this.hBoxSituacaoCadSintegra.setVisible(true);
            if (StringUtils.isBlank(cnpj)) {
                return;
            }
            codClienteApi = Double.parseDouble(cnpj.replaceAll("\\D", ""));
            if (StringUtils.isNotBlank(this.edtINSC_ESTADUAL.getValue().asString())) {
                ie = this.edtINSC_ESTADUAL.getValue().asString().replaceAll("\\D", "");
            }
        }
        if (codClienteApi == 0.0) {
            return;
        }
        this.vBoxStatusCadastro.setVisible(true);
        this.hBoxSituacaoCadReceitaFederal.setVisible(true);
        this.hBoxSituacaoCadSintegra.setVisible(true);
        this.lblSintegraMultiIe.setVisible(false);
        this.lblSituacaoCadReceitaFederal.setVisible(true);
        this.lblCadastroIrregular.setVisible(false);
        this.hBoxSituacaoCadSintegra.setVisible(false);
        this.lblSituacaoSintegra.setCaption(" -- ");
        this.lblSituacaoSintegra.setHint("");
        this.lblSituacaoReceitaFederal.setCaption(" -- ");
        this.lblSituacaoReceitaFederal.setHint("");
        this.lblSintegraMultiIe.setHint("");
        this.lblSituacaoCadSintegra.setHint("");
        Value rfbSituacao = new Value(null);
        Value rfbSituacaoMensagem = new Value(null);
        Value sintegraSituacao = new Value(null);
        Value sintegraMensagem = new Value(null);
        Value sintegraCadastroIsento = new Value(null);
        Value sintegraMultiplasIe = new Value(null);
        String respFunc = this.infoSituacaoCadastralIntegracao(this.codEmpresaUsuarioLogado,
                codClienteApi,
                this.cboTipo.getValue().asString(),
                ie,
                rfbSituacao,
                rfbSituacaoMensagem,
                sintegraSituacao,
                sintegraMensagem,
                sintegraCadastroIsento,
                sintegraMultiplasIe);
        if (!respFunc.equals("S")) {
            this.lblCadastroIrregular.setVisible(true);
        }
        if (StringUtils.isNotBlank(ie)) {
            this.hBoxSituacaoCadSintegra.setVisible(true);
        }
        this.lblSituacaoReceitaFederal.setHint(rfbSituacaoMensagem.asString());
        this.lblSituacaoSintegra.setHint(sintegraMensagem.asString());
        if (StringUtils.isNotBlank(sintegraMultiplasIe.asString())) {
            boolean hBoxSituacaoCadSintegraNotVisible = !this.hBoxSituacaoCadSintegra.isVisible();
            if (hBoxSituacaoCadSintegraNotVisible) {
                this.hBoxSituacaoCadSintegra.setVisible(true);
            }
            this.lblSintegraMultiIe.setVisible(true);
            this.lblSintegraMultiIe.setHint(sintegraMultiplasIe.asString());
            this.lblSituacaoCadSintegra.setHint("Multiplas Ie"
                    + System.lineSeparator()
                    + sintegraMultiplasIe.asString());
        }
        this.lblSituacaoSintegra.setCaption(sintegraSituacao.asString());
        this.lblSituacaoReceitaFederal.setCaption(rfbSituacao.asString());
    }

    private void preencherEndereco() {
        if (!this.incluirEndClick) {
            if (!this.tbClientes.getUF_RES().isNull()) {
                this.incluirEndereco();
            }
            if (!this.tbClientes.getUF_COM().isNull()) {
                this.incluirEndereco();
            }
            if (!this.tbClientes.getUF_COBRANCA().isNull()) {
                this.incluirEndereco();
            }
        }
        boolean tbClientesUFResNotNull = !this.tbClientes.getUF_RES().isNull();
        boolean boxEnd1Visible = this.hBoxEnd01.isVisible();
        boolean boxEnd2Visible = this.hBoxEnd02.isVisible();
        boolean boxEnd3Visible = this.hBoxEnd03.isVisible();
        if (tbClientesUFResNotNull
                && boxEnd1Visible
                && (this.edTpEnd1.getValue().asString().isEmpty())) {
            edTpEnd1.setValue(1);
            edTpEnd1.applyProperties();
            edUF1.setTable(tbClientes);
            edUF1.setFieldName(UF_RES);
            edUF1.setValue(tbClientes.getUF_RES().asString());
            edUF1.applyProperties();
            edtCidade1.setTable(tbClientes);
            edtCidade1.setFieldName(COD_CID_RES);
            edtCidade1.setValue(tbClientes.getCOD_CID_RES().asInteger());
            edtCidade1.applyProperties();
            edtCEP1.setTable(tbClientes);
            edtCEP1.setFieldName(CEP_RES);
            edtCEP1.setValue(tbClientes.getCEP_RES().asString());
            edtCEP1.applyProperties();
            edtRua1.setTable(tbClientes);
            edtRua1.setFieldName(RUA_RES);
            edtRua1.setValue(tbClientes.getRUA_RES().asString());
            edtRua1.applyProperties();
            edtBairro1.setTable(tbClientes);
            edtBairro1.setFieldName(BAIRRO_RES);
            edtBairro1.setValue(tbClientes.getBAIRRO_RES().asString());
            edtBairro1.applyProperties();
            edtComplemento1.setTable(tbClientes);
            edtComplemento1.setFieldName(COMPLEMENTO_RES);
            edtComplemento1.setValue(tbClientes.getCOMPLEMENTO_RES().asString());
            edtComplemento1.applyProperties();
            edtNumero1.setTable(tbClientes);
            edtNumero1.setFieldName(FACHADA_RES);
            edtNumero1.setValue(tbClientes.getFACHADA_RES().asString());
            edtNumero1.applyProperties();
        } else if (tbClientesUFResNotNull
                && boxEnd2Visible
                && (this.edTpEnd2.getValue().asString().isEmpty())) {
            edTpEnd2.setValue(1);
            edTpEnd2.applyProperties();
            edUF2.setTable(tbClientes);
            edUF2.setFieldName(UF_RES);
            edUF2.setValue(tbClientes.getUF_RES().asString());
            edUF2.applyProperties();
            edtCidade2.setTable(tbClientes);
            edtCidade2.setFieldName(COD_CID_RES);
            edtCidade2.setValue(tbClientes.getCOD_CID_RES().asInteger());
            edtCidade2.applyProperties();
            edtCEP2.setTable(tbClientes);
            edtCEP2.setFieldName(CEP_RES);
            edtCEP2.setValue(tbClientes.getCEP_RES().asString());
            edtCEP2.applyProperties();
            edtRua2.setTable(tbClientes);
            edtRua2.setFieldName(RUA_RES);
            edtRua2.setValue(tbClientes.getRUA_RES().asString());
            edtRua2.applyProperties();
            edtBairro2.setTable(tbClientes);
            edtBairro2.setFieldName(BAIRRO_RES);
            edtBairro2.setValue(tbClientes.getBAIRRO_RES().asString());
            edtBairro2.applyProperties();
            edtComplemento2.setTable(tbClientes);
            edtComplemento2.setFieldName(COMPLEMENTO_RES);
            edtComplemento2.setValue(tbClientes.getCOMPLEMENTO_RES().asString());
            edtComplemento2.applyProperties();
            edtNumero2.setTable(tbClientes);
            edtNumero2.setFieldName(FACHADA_RES);
            edtNumero2.setValue(tbClientes.getFACHADA_RES().asString());
            edtNumero2.applyProperties();
        } else if (tbClientesUFResNotNull
                && boxEnd3Visible
                && (edTpEnd3.getValue().asString().isEmpty())) {
            edTpEnd3.setValue(1);
            edTpEnd3.applyProperties();
            edUF3.setTable(tbClientes);
            edUF3.setFieldName(UF_RES);
            edUF3.setValue(tbClientes.getUF_RES().asString());
            edUF3.applyProperties();
            edtCidade3.setTable(tbClientes);
            edtCidade3.setFieldName(COD_CID_RES);
            edtCidade3.setValue(tbClientes.getCOD_CID_RES().asInteger());
            edtCidade3.applyProperties();
            edtCEP3.setTable(tbClientes);
            edtCEP3.setFieldName(CEP_RES);
            edtCEP3.setValue(tbClientes.getCEP_RES().asString());
            edtCEP3.applyProperties();
            edtRua3.setTable(tbClientes);
            edtRua3.setFieldName(RUA_RES);
            edtRua3.setValue(tbClientes.getRUA_RES().asString());
            edtRua3.applyProperties();
            edtBairro3.setTable(tbClientes);
            edtBairro3.setFieldName(BAIRRO_RES);
            edtBairro3.setValue(tbClientes.getBAIRRO_RES().asString());
            edtBairro3.applyProperties();
            edtComplemento3.setTable(tbClientes);
            edtComplemento3.setFieldName(COMPLEMENTO_RES);
            edtComplemento3.setValue(tbClientes.getCOMPLEMENTO_RES().asString());
            edtComplemento3.applyProperties();
            edtNumero3.setTable(tbClientes);
            edtNumero3.setFieldName(FACHADA_RES);
            edtNumero3.setValue(tbClientes.getFACHADA_RES().asString());
            edtNumero3.applyProperties();
        }
        boolean tbClientesUFComNotNull = !this.tbClientes.getUF_COM().isNull();
        boxEnd1Visible = this.hBoxEnd01.isVisible();
        boxEnd2Visible = this.hBoxEnd02.isVisible();
        boxEnd3Visible = this.hBoxEnd03.isVisible();
        if (tbClientesUFComNotNull
                && boxEnd1Visible
                && (this.edTpEnd1.getValue().asString().isEmpty())) {
            edTpEnd1.setValue(2);
            edTpEnd1.applyProperties();
            edUF1.setTable(tbClientes);
            edUF1.setFieldName(UF_COM);
            edUF1.setValue(tbClientes.getUF_COM().asString());
            edUF1.applyProperties();
            edtCidade1.setTable(tbClientes);
            edtCidade1.setFieldName(COD_CID_COM);
            edtCidade1.setValue(tbClientes.getCOD_CID_COM().asInteger());
            edtCidade1.applyProperties();
            edtCEP1.setTable(tbClientes);
            edtCEP1.setFieldName(CEP_COM);
            edtCEP1.setValue(tbClientes.getCEP_COM().asString());
            edtCEP1.applyProperties();
            edtRua1.setTable(tbClientes);
            edtRua1.setFieldName(RUA_COM);
            edtRua1.setValue(tbClientes.getRUA_COM().asString());
            edtRua1.applyProperties();
            edtBairro1.setTable(tbClientes);
            edtBairro1.setFieldName(BAIRRO_COM);
            edtBairro1.setValue(tbClientes.getBAIRRO_COM().asString());
            edtBairro1.applyProperties();
            edtComplemento1.setTable(tbClientes);
            edtComplemento1.setFieldName(COMPLEMENTO_COM);
            edtComplemento1.setValue(tbClientes.getCOMPLEMENTO_COM().asString());
            edtComplemento1.applyProperties();
            edtNumero1.setTable(tbClientes);
            edtNumero1.setFieldName(FACHADA_COM);
            edtNumero1.setValue(tbClientes.getFACHADA_COM().asString());
            edtNumero1.applyProperties();
        } else if (tbClientesUFComNotNull
                && boxEnd2Visible
                && (this.edTpEnd2.getValue().asString().isEmpty())) {
            edTpEnd2.setValue(2);
            edTpEnd2.applyProperties();
            edUF2.setTable(tbClientes);
            edUF2.setFieldName(UF_COM);
            edUF2.setValue(tbClientes.getUF_COM().asString());
            edUF2.applyProperties();
            edtCidade2.setTable(tbClientes);
            edtCidade2.setFieldName(COD_CID_COM);
            edtCidade2.setValue(tbClientes.getCOD_CID_COM().asInteger());
            edtCidade2.applyProperties();
            edtCEP2.setTable(tbClientes);
            edtCEP2.setFieldName(CEP_COM);
            edtCEP2.setValue(tbClientes.getCEP_COM().asString());
            edtCEP2.applyProperties();
            edtRua2.setTable(tbClientes);
            edtRua2.setFieldName(RUA_COM);
            edtRua2.setValue(tbClientes.getRUA_COM().asString());
            edtRua2.applyProperties();
            edtBairro2.setTable(tbClientes);
            edtBairro2.setFieldName(BAIRRO_COM);
            edtBairro2.setValue(tbClientes.getBAIRRO_COM().asString());
            edtBairro2.applyProperties();
            edtComplemento2.setTable(tbClientes);
            edtComplemento2.setFieldName(COMPLEMENTO_COM);
            edtComplemento2.setValue(tbClientes.getCOMPLEMENTO_COM().asString());
            edtComplemento2.applyProperties();
            edtNumero2.setTable(tbClientes);
            edtNumero2.setFieldName(FACHADA_COM);
            edtNumero2.setValue(tbClientes.getFACHADA_COM().asString());
            edtNumero2.applyProperties();
        } else if (tbClientesUFComNotNull
                && boxEnd3Visible
                && (edTpEnd3.getValue().asString().isEmpty())) {
            edTpEnd3.setValue(2);
            edTpEnd3.applyProperties();
            edUF3.setTable(tbClientes);
            edUF3.setFieldName(UF_COM);
            edUF3.setValue(tbClientes.getUF_COM().asString());
            edUF3.applyProperties();
            edtCidade3.setTable(tbClientes);
            edtCidade3.setFieldName(COD_CID_COM);
            edtCidade3.setValue(tbClientes.getCOD_CID_COM().asInteger());
            edtCidade3.applyProperties();
            edtCEP3.setTable(tbClientes);
            edtCEP3.setFieldName(CEP_COM);
            edtCEP3.setValue(tbClientes.getCEP_COM().asString());
            edtCEP3.applyProperties();
            edtRua3.setTable(tbClientes);
            edtRua3.setFieldName(RUA_COM);
            edtRua3.setValue(tbClientes.getRUA_COM().asString());
            edtRua3.applyProperties();
            edtBairro3.setTable(tbClientes);
            edtBairro3.setFieldName(BAIRRO_COM);
            edtBairro3.setValue(tbClientes.getBAIRRO_COM().asString());
            edtBairro3.applyProperties();
            edtComplemento3.setTable(tbClientes);
            edtComplemento3.setFieldName(COMPLEMENTO_COM);
            edtComplemento3.setValue(tbClientes.getCOMPLEMENTO_COM().asString());
            edtComplemento3.applyProperties();
            edtNumero3.setTable(tbClientes);
            edtNumero3.setFieldName(FACHADA_COM);
            edtNumero3.setValue(tbClientes.getFACHADA_COM().asString());
            edtNumero3.applyProperties();
        }
        boolean tbClientesUFCobrancaNotNull = !this.tbClientes.getUF_COBRANCA().isNull();
        boxEnd1Visible = this.hBoxEnd01.isVisible();
        boxEnd2Visible = this.hBoxEnd02.isVisible();
        boxEnd3Visible = this.hBoxEnd03.isVisible();
        if (tbClientesUFCobrancaNotNull
                && boxEnd1Visible
                && (edTpEnd1.getValue().asString().isEmpty())) {
            edTpEnd1.setValue(3);
            edTpEnd1.applyProperties();
            edUF1.setTable(tbClientes);
            edUF1.setFieldName(UF_COBRANCA);
            edUF1.setValue(tbClientes.getUF_COBRANCA().asString());
            edUF1.applyProperties();
            edtCidade1.setTable(tbClientes);
            edtCidade1.setFieldName(COD_CID_COBRANCA);
            edtCidade1.setValue(tbClientes.getCOD_CID_COBRANCA().asInteger());
            edtCidade1.applyProperties();
            edtCEP1.setTable(tbClientes);
            edtCEP1.setFieldName(CEP_COBRANCA);
            edtCEP1.setValue(tbClientes.getCEP_COBRANCA().asString());
            edtCEP1.applyProperties();
            edtRua1.setTable(tbClientes);
            edtRua1.setFieldName(RUA_COBRANCA);
            edtRua1.setValue(tbClientes.getRUA_COBRANCA().asString());
            edtRua1.applyProperties();
            edtBairro1.setTable(tbClientes);
            edtBairro1.setFieldName(BAIRRO_COBRANCA);
            edtBairro1.setValue(tbClientes.getBAIRRO_COBRANCA().asString());
            edtBairro1.applyProperties();
            edtComplemento1.setTable(tbClientes);
            edtComplemento1.setFieldName(COMPLEMENTO_COBRANCA);
            edtComplemento1.setValue(tbClientes.getCOMPLEMENTO_COBRANCA().asString());
            edtComplemento1.applyProperties();
            edtNumero1.setTable(tbClientes);
            edtNumero1.setFieldName(FACHADA_COBRANCA);
            edtNumero1.setValue(tbClientes.getFACHADA_COBRANCA().asString());
            edtNumero1.applyProperties();
        } else if (tbClientesUFCobrancaNotNull
                && boxEnd2Visible
                && (edTpEnd2.getValue().asString().isEmpty())) {
            edTpEnd2.setValue(3);
            edTpEnd2.applyProperties();
            edUF2.setTable(tbClientes);
            edUF2.setFieldName(UF_COBRANCA);
            edUF2.setValue(tbClientes.getUF_COBRANCA().asString());
            edUF2.applyProperties();
            edtCidade2.setTable(tbClientes);
            edtCidade2.setFieldName(COD_CID_COBRANCA);
            edtCidade2.setValue(tbClientes.getCOD_CID_COBRANCA().asInteger());
            edtCidade2.applyProperties();
            edtCEP2.setTable(tbClientes);
            edtCEP2.setFieldName(CEP_COBRANCA);
            edtCEP2.setValue(tbClientes.getCEP_COBRANCA().asString());
            edtCEP2.applyProperties();
            edtRua2.setTable(tbClientes);
            edtRua2.setFieldName(RUA_COBRANCA);
            edtRua2.setValue(tbClientes.getRUA_COBRANCA().asString());
            edtRua2.applyProperties();
            edtBairro2.setTable(tbClientes);
            edtBairro2.setFieldName(BAIRRO_COBRANCA);
            edtBairro2.setValue(tbClientes.getBAIRRO_COBRANCA().asString());
            edtBairro2.applyProperties();
            edtComplemento2.setTable(tbClientes);
            edtComplemento2.setFieldName(COMPLEMENTO_COBRANCA);
            edtComplemento2.setValue(tbClientes.getCOMPLEMENTO_COBRANCA().asString());
            edtComplemento2.applyProperties();
            edtNumero2.setTable(tbClientes);
            edtNumero2.setFieldName(FACHADA_COBRANCA);
            edtNumero2.setValue(tbClientes.getFACHADA_COBRANCA().asString());
            edtNumero2.applyProperties();
        } else if (tbClientesUFCobrancaNotNull
                && boxEnd3Visible
                && (edTpEnd3.getValue().asString().isEmpty())) {
            edTpEnd3.setValue(3);
            edTpEnd3.applyProperties();
            edUF3.setTable(tbClientes);
            edUF3.setFieldName(UF_COBRANCA);
            edUF3.setValue(tbClientes.getUF_COBRANCA().asString());
            edUF3.applyProperties();
            edtCidade3.setTable(tbClientes);
            edtCidade3.setFieldName(COD_CID_COBRANCA);
            edtCidade3.setValue(tbClientes.getCOD_CID_COBRANCA().asInteger());
            edtCidade3.applyProperties();
            edtCEP3.setTable(tbClientes);
            edtCEP3.setFieldName(CEP_COBRANCA);
            edtCEP3.setValue(tbClientes.getCEP_COBRANCA().asString());
            edtCEP3.applyProperties();
            edtRua3.setTable(tbClientes);
            edtRua3.setFieldName(RUA_COBRANCA);
            edtRua3.setValue(tbClientes.getRUA_COBRANCA().asString());
            edtRua3.applyProperties();
            edtBairro3.setTable(tbClientes);
            edtBairro3.setFieldName(BAIRRO_COBRANCA);
            edtBairro3.setValue(tbClientes.getBAIRRO_COBRANCA().asString());
            edtBairro3.applyProperties();
            edtComplemento3.setTable(tbClientes);
            edtComplemento3.setFieldName(COMPLEMENTO_COBRANCA);
            edtComplemento3.setValue(tbClientes.getCOMPLEMENTO_COBRANCA().asString());
            edtComplemento3.applyProperties();
            edtNumero3.setTable(tbClientes);
            edtNumero3.setFieldName(FACHADA_COBRANCA);
            edtNumero3.setValue(tbClientes.getFACHADA_COBRANCA().asString());
            edtNumero3.applyProperties();
        }
    }

    private void setCampos() {
        try {
            boolean tbClientesEmpty = this.tbClientes.isEmpty();
            if (tbClientesEmpty) {
                this.tbClientes.append();
                if (!((this.cboTipo.getValue().asString().equals("F"))
                        && (cboTipo.getValue().asString().equals("J")))) {
                    this.cboTipo.setValue("F");
                    this.tbClientes.setCOD_CLASSE("F");
                    this.vBoxPessoaFisica.setVisible(Boolean.TRUE);
                    this.vBoxPessoaJuridica.setVisible(Boolean.FALSE);
                    this.vBoxFoneContatoPJ.setVisible(false);
                    this.rn.abrirTabelaAux(this.cboTipo.getValue().asString());
                }
            } else {
                this.tbClientes.edit();
            }
            boolean tbClienteDiversoEmpty = this.tbClienteDiverso.isEmpty();
            if (tbClienteDiversoEmpty) {
                this.tbClienteDiverso.append();
            } else {
                this.tbClienteDiverso.edit();
            }
            this.tbClientes.setNOME(this.edtNomeCliente.getValue());
            this.tbClientes.setCOD_NACIONALIDADE(this.cboNacionalidade.getValue());
            if (!this.cboProfissao.getValue().asString().trim().isEmpty()) {
                this.tbClientes.setCOD_PROFISSAO(this.cboProfissao.getValue());
            }
            switch (this.edTpEnd1.getValue().asInteger()) {
                case 1:
                    this.tbClientes.setUF_RES(this.edUF1.getValue());
                    this.tbClientes.setCOD_CID_RES(this.edtCidade1.getValue());
                    this.tbClientes.setBAIRRO_RES(this.edtBairro1.getValue());
                    this.tbClientes.setRUA_RES(this.edtRua1.getValue());
                    this.tbClientes.setCEP_RES(this.edtCEP1.getValue().asString());
                    this.tbClientes.setCOMPLEMENTO_RES(this.edtComplemento1.getValue());
                    this.tbClientes.setFACHADA_RES(this.edtNumero1.getValue());
                    break;
                case 2:
                    this.tbClientes.setUF_COM(this.edUF1.getValue());
                    this.tbClientes.setCOD_CID_COM(this.edtCidade1.getValue());
                    this.tbClientes.setBAIRRO_COM(this.edtBairro1.getValue());
                    this.tbClientes.setRUA_COM(this.edtRua1.getValue());
                    this.tbClientes.setCEP_COM(this.edtCEP1.getValue().asString());
                    this.tbClientes.setCOMPLEMENTO_COM(this.edtComplemento1.getValue());
                    this.tbClientes.setFACHADA_COM(this.edtNumero1.getValue());
                    break;
                case 3:
                    this.tbClientes.setUF_COBRANCA(this.edUF1.getValue());
                    this.tbClientes.setCOD_CID_COBRANCA(this.edtCidade1.getValue());
                    this.tbClientes.setBAIRRO_COBRANCA(this.edtBairro1.getValue());
                    this.tbClientes.setRUA_COBRANCA(this.edtRua1.getValue());
                    this.tbClientes.setCEP_COBRANCA(this.edtCEP1.getValue().asString());
                    this.tbClientes.setCOMPLEMENTO_COBRANCA(this.edtComplemento1.getValue());
                    this.tbClientes.setFACHADA_COBRANCA(this.edtNumero1.getValue());
                    break;
                default:
                    break;
            }
            switch (edTpEnd2.getValue().asInteger()) {
                case 1:
                    tbClientes.setUF_RES(edUF2.getValue());
                    tbClientes.setCOD_CID_RES(edtCidade2.getValue());
                    tbClientes.setBAIRRO_RES(edtBairro2.getValue());
                    tbClientes.setRUA_RES(edtRua2.getValue());
                    tbClientes.setCEP_RES(edtCEP2.getValue().asString());
                    tbClientes.setCOMPLEMENTO_RES(edtComplemento2.getValue());
                    tbClientes.setFACHADA_RES(edtNumero2.getValue());
                    break;
                case 2:
                    tbClientes.setUF_COM(edUF2.getValue());
                    tbClientes.setCOD_CID_COM(edtCidade2.getValue());
                    tbClientes.setBAIRRO_COM(edtBairro2.getValue());
                    tbClientes.setRUA_COM(edtRua2.getValue());
                    tbClientes.setCEP_COM(edtCEP2.getValue().asString());
                    tbClientes.setCOMPLEMENTO_COM(edtComplemento2.getValue());
                    tbClientes.setFACHADA_COM(edtNumero2.getValue());
                    break;
                case 3:
                    tbClientes.setUF_COBRANCA(edUF2.getValue());
                    tbClientes.setCOD_CID_COBRANCA(edtCidade2.getValue());
                    tbClientes.setBAIRRO_COBRANCA(edtBairro2.getValue());
                    tbClientes.setRUA_COBRANCA(edtRua2.getValue());
                    tbClientes.setCEP_COBRANCA(edtCEP2.getValue().asString());
                    tbClientes.setCOMPLEMENTO_COBRANCA(edtComplemento2.getValue());
                    tbClientes.setFACHADA_COBRANCA(edtNumero2.getValue());
                    break;
                default:
                    break;
            }
            switch (edTpEnd3.getValue().asInteger()) {
                case 1:
                    tbClientes.setUF_RES(edUF3.getValue());
                    tbClientes.setCOD_CID_RES(edtCidade3.getValue());
                    tbClientes.setBAIRRO_RES(edtBairro3.getValue());
                    tbClientes.setRUA_RES(edtRua3.getValue());
                    tbClientes.setCEP_RES(edtCEP3.getValue().asString());
                    tbClientes.setCOMPLEMENTO_RES(edtComplemento3.getValue());
                    tbClientes.setFACHADA_RES(edtNumero3.getValue());
                    break;
                case 2:
                    tbClientes.setUF_COM(edUF3.getValue());
                    tbClientes.setCOD_CID_COM(edtCidade3.getValue());
                    tbClientes.setBAIRRO_COM(edtBairro3.getValue());
                    tbClientes.setRUA_COM(edtRua3.getValue());
                    tbClientes.setCEP_COM(edtCEP3.getValue().asString());
                    tbClientes.setCOMPLEMENTO_COM(edtComplemento3.getValue());
                    tbClientes.setFACHADA_COM(edtNumero3.getValue());
                    break;
                case 3:
                    tbClientes.setUF_COBRANCA(edUF3.getValue());
                    tbClientes.setCOD_CID_COBRANCA(edtCidade3.getValue());
                    tbClientes.setBAIRRO_COBRANCA(edtBairro3.getValue());
                    tbClientes.setRUA_COBRANCA(edtRua3.getValue());
                    tbClientes.setCEP_COBRANCA(edtCEP3.getValue().asString());
                    tbClientes.setCOMPLEMENTO_COBRANCA(edtComplemento3.getValue());
                    tbClientes.setFACHADA_COBRANCA(edtNumero3.getValue());
                    break;
                default:
                    break;
            }
            tbClientes.setPREFIXO_CEL(edtDddCel.getValue());
            tbClientes.setTELEFONE_CEL(edtTelefoneCel.getValue());
            tbClientes.setPREFIXO_COM(edtDddCom.getValue());
            tbClientes.setTELEFONE_COM(edtTelefoneCom.getValue());
            tbClientes.setPREFIXO_RES(edtDDDTelRes.getValue());
            tbClientes.setTELEFONE_RES(edtTelRes.getValue());
            tbClientes.setPREFIXO_FAX(edtDDDTelCob.getValue());
            tbClientes.setTELEFONE_FAX(edtTelCob.getValue());
            tbClientes.setENDERECO_ELETRONICO(edtEmail.getValue());
            String email2 = edtEmail.getValue().asString();
            tbClientes.setEMAIL2(email2.substring(0,
                    Math.min(email2.length(),
                            50))
            );
            tbClientes.setEMAIL_NFE(edtEmailNFE.getValue());
            this.prefixoCel = tbClientes.getPREFIXO_CEL().asInteger();
            this.telefoneCel = tbClientes.getTELEFONE_CEL().asInteger();
            tbClientes.post();
            tbClienteDiverso.post();
            if (cboTipo.getValue().asString().equals("F")) {
                boolean tbDadosFisicosEmpty = this.tbDadosFisicos.isEmpty();
                if (tbDadosFisicosEmpty) {
                    tbDadosFisicos.append();
                } else {
                    tbDadosFisicos.edit();
                }
                tbDadosFisicos.setCOD_SEXO(cboSexo.getValue());
                tbDadosFisicos.setCOD_ESTADO_CIVIL(cboEstadoCivil.getValue());
                tbDadosFisicos.setANIVERSARIO(this.dtNasc.getValue());
                tbDadosFisicos.setRG_NUMERO(edtRG.getValue());
                tbDadosFisicos.setRG_EMISSOR(edtEmissorRG.getValue());
                tbDadosFisicos.setRG_DATA_EMISSAO(dtEmissaoRG.getValue());
                tbDadosFisicos.setESCOLARIDADE(cboEscolaridade.getValue());
                tbDadosFisicos.post();
            } else {
                boolean tbDadosJuridicosEmpty = this.tbDadosJuridicos.isEmpty();
                if (tbDadosJuridicosEmpty) {
                    tbDadosJuridicos.append();
                } else {
                    tbDadosJuridicos.edit();
                }
                tbDadosJuridicos.setINSC_ESTADUAL(edtINSC_ESTADUAL.getValue().asString());
                tbDadosJuridicos.setINSC_MUNICIPAL(edtINSC_MUNICIPAL.getValue().asString());
                tbDadosJuridicos.setDATA_FUNDACAO(edtDataFundacao.getValue());
                tbDadosJuridicos.post();
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Salvar Cadastro. Motivo: ", ex);
        }
    }

    private void preencherEditsValidacao() {
        this.edits.add(new CampoFoco("CLIENTES.COD_CLASSE", this.cboTipo, this.lblValidarTipoPessoa, this.vBoxTipo, this.hBoxClienteTipo));
        this.edits.add(new CampoFoco("CLIENTES.NOME", this.edtNomeCliente, this.lblValidarNomeCliente, this.vBoxNomeCliente, this.hBoxCliente));
        //region Telefones
        this.edits.add(new CampoFoco("CLIENTES.TELEFONE_CEL", this.edtTelefoneCel, this.lblValidarCelular, this.vBoxCelular, this.hBoxFone1));
        this.edits.add(new CampoFoco("CLIENTES.TELEFONE_COM", this.edtTelefoneCom, this.lblValidarFoneCom, this.vBoxFoneCom, this.hBoxFone1));
        this.edits.add(new CampoFoco("CLIENTES.TELEFONE_RES", this.edtTelRes, this.lblValidarFoneRes, this.vBoxFoneRes, this.hBoxFone2));
        this.cboProfissao.setEnabled(true);
        //endregion
        //region Validação CRMFlags
        this.edits.add(new CampoFoco("CLIENTE_DIVERSO.TELE_CONTATO", this.cboTeleContato, this.lblValidaTeleContato, this.vBoxTeleContato, this.hBoxTeleContato));
        this.edits.add(new CampoFoco("CLIENTE_DIVERSO.CRM_FONE", this.cboCrmFone, this.lblValidaCrmFone, this.vBoxCrmFone, this.hBoxCrmFone));
        this.edits.add(new CampoFoco("CLIENTE_DIVERSO.CRM_EMAIL", this.cboCrmEmail, this.lblValidaCrmEmail, this.vBoxCrmEmail, this.hBoxCrmEmail));
        this.edits.add(new CampoFoco("CLIENTE_DIVERSO.CRM_SMS", this.cboCrmSMS, this.lblValidaCrmSms, this.vBoxCrmSms, this.hBoxCrmSms));
        this.edits.add(new CampoFoco("CLIENTE_DIVERSO.CRM_MALA", this.cboCrmMalaDireta, this.lblValidaCrmMalaDireta, this.vBoxMalaDireta, this.hBoxCrmMalaDireta));
        //endregion
        //region Validação Servidor Público
        this.edits.add(new CampoFoco("CLIENTE_DIVERSO.SERVIDOR_PUBLICO", this.cboServidorPublico, this.lblvalidaServidorPublico, this.vBoxServidorPublico, this.hBoxServidorPublicoContainer));
        this.edits.add(new CampoFoco("CLIENTE_DIVERSO.POLITICAMENTE_EXPOSTO", this.cboPoliticamenteExposto, this.lblValidaPoliticamenteExposto, this.vBoxPoliticamenteExposto, this.hBoxServidorPublicoContainer));
        //endregion
        //region Dados Físicos
        this.edits.add(new CampoFoco("DADOS_FISICOS.ANIVERSARIO", this.dtNasc, this.lblValidarDataNascimento, this.vBoxDataNascimento, this.hboxPessoaFisica1));
        this.edits.add(new CampoFoco("CLIENTES.COD_NACIONALIDADE", this.cboNacionalidade, this.lblValidarNacionalidade, this.vBoxNacionalidade, this.hboxPessoaFisica1));
        this.edits.add(new CampoFoco("DADOS_FISICOS.COD_PROFISSAO", this.cboProfissao, this.lblValidarProfissao, this.vBoxProfissao, this.hboxPessoaFisica1));
        this.edits.add(new CampoFoco(DADOS_FISICOS_CPF, this.edtCPF, this.lblValidarCPF, this.vBoxCPF, this.hboxPessoaFisica2));
        this.edits.add(new CampoFoco("DADOS_FISICOS.RG_NUMERO", this.edtRG, this.lblValidarRG, this.vBoxRG, this.hboxPessoaFisica2));
        this.edits.add(new CampoFoco("DADOS_FISICOS.RG_DATA_EMISSAO", this.dtEmissaoRG, this.lblValidarRGEmissao, this.vBoxDTEmissaoRG, this.hboxPessoaFisica2));
        this.edits.add(new CampoFoco("DADOS_FISICOS.RG_EMISSOR", this.edtEmissorRG, this.lblValidarRGEmissor, this.vBoxEmissorRG, this.hboxPessoaFisica2));
        this.edits.add(new CampoFoco("DADOS_FISICOS.COD_SEXO", this.cboSexo, this.lblValidarSexo, this.vBoxSexo, this.hboxPessoaFisica3));
        this.edits.add(new CampoFoco("DADOS_FISICOS.COD_ESTADO_CIVIL", this.cboEstadoCivil, this.lblValidarEstadoCivil, this.vBoxEstadoCivil, this.hboxPessoaFisica3));
        this.edits.add(new CampoFoco("DADOS_FISICOS.ESCOLARIDADE", this.cboEscolaridade, this.lblValidarEscolaridade, this.vBoxEscolaridade, this.hboxPessoaFisica1));
        //endregion
        //region Dados Jurídicos
        this.edits.add(new CampoFoco("DADOS_JURIDICOS.COD_ATIVIDADE", this.cboRamo, this.lblValidarRamo, this.vBoxRamo, this.hboxPessoaJuridica2));
        this.cboRamo.setEnabled(true);
        this.edits.add(new CampoFoco("DADOS_JURIDICOS.CGC", this.edtCNPJ, this.lblValidarCNPJ, this.vBoxCNPJ, this.hboxPessoaJuridica1));
        this.edits.add(new CampoFoco("DADOS_JURIDICOS.COD_RAMO", this.cboDistribuicao, this.lblValidarDistribuicao, this.vBoxDistribuicao, this.hboxPessoaJuridica2));
        this.edits.add(new CampoFoco("DADOS_JURIDICOS.NOME_FANTASIA", this.edtNomeFantasia, this.lblValidarDJNomeFantasia, this.vBoxDJNomeFantasia, this.hboxPessoaJuridica3));
        this.edits.add(new CampoFoco("DADOS_JURIDICOS.DATA_FUNDACAO", this.edtDataFundacao, this.lblValidaDataFundacao, this.vBoxDataFundacao, this.hBoxDataFundacao));
        this.edits.add(new CampoFoco("DADOS_JURIDICOS.NRO_SUFRAMA", this.edtNroSuframa, this.lblValidaNroSuframa, this.vBoxNroSuframa, this.hBoxNroSuframa));
        this.edits.add(new CampoFoco("DADOS_JURIDICOS.CONCESSIONARIA", this.cboConcessionaria, this.lblValidaConcessionaria, this.vBoxConcessionaria, this.hBoxConcessionaria));
        this.edits.add(new CampoFoco("CLIENTES.EMAIL_NFE", this.edtEmailNFE, this.lblValidarEmailNFe, this.vBoxEmailNFeCadRapCli, this.hBoxEmailNFeCadRapCliente));
        this.edits.add(new CampoFoco("CLIENTES.ENDERECO_ELETRONICO", this.edtEmail, this.lblValidarEmail, this.vBoxEmailCadRapCli, this.hBoxEmailCadRapCliente));
        //endregion
    }

    private boolean possuiContato(double codCliente) {
        boolean retFuncao;
        try {
            retFuncao = this.rn.possuiContato(codCliente);
        } catch (DataException dataException) {
            retFuncao = false;
        }
        return retFuncao;
    }

    private void prencherEditsValidacaoEndereco() {
        // Endereços
        switch (edTpEnd1.getValue().asInteger()) {
            case 1:
                edits.add(new CampoFoco(CLIENTES_UF_RES, edUF1, lblValidarUF1, vBoxUF1, hBoxEnd2));
                edits.add(new CampoFoco(CLIENTES_COD_CID_RES, edtCidade1, lblValidarCidade1, vBoxCidade1, hBoxEnd2));
                edits.add(new CampoFoco(CLIENTES_CEP_RES, edtCEP1, lblValidarCEP1, vBoxCEP1, hBoxEnd1));
                edits.add(new CampoFoco(CLIENTES_RUA_RES, edtRua1, lblValidarRua1, vBoxRua1, hBoxEnd3));
                edits.add(new CampoFoco(CLIENTES_BAIRRO_RES, edtBairro1, lblValidarBairro1, vBoxBairro1, hBoxEnd3));
                edits.add(new CampoFoco(CLIENTES_FACHADA_RES, edtNumero1, lblValidarNumero1, vBoxNumero1, FHBox2));
                break;
            case 2:
                edits.add(new CampoFoco(CLIENTES_UF_COM, edUF1, lblValidarUF1, vBoxUF1, hBoxEnd2));
                edits.add(new CampoFoco(CLIENTES_COD_CID_COM, edtCidade1, lblValidarCidade1, vBoxCidade1, hBoxEnd2));
                edits.add(new CampoFoco(CLIENTES_CEP_COM, edtCEP1, lblValidarCEP1, vBoxCEP1, hBoxEnd1));
                edits.add(new CampoFoco(CLIENTES_RUA_COM, edtRua1, lblValidarRua1, vBoxRua1, hBoxEnd3));
                edits.add(new CampoFoco(CLIENTES_BAIRRO_COM, edtBairro1, lblValidarBairro1, vBoxBairro1, hBoxEnd3));
                edits.add(new CampoFoco(CLIENTES_FACHADA_COM, edtNumero1, lblValidarNumero1, vBoxNumero1, FHBox2));
                break;
            case 3:
                edits.add(new CampoFoco(CLIENTES_UF_COBRANCA, edUF1, lblValidarUF1, vBoxUF1, hBoxEnd2));
                edits.add(new CampoFoco(CLIENTES_COD_CID_COBRANCA, edtCidade1, lblValidarCidade1, vBoxCidade1, hBoxEnd2));
                edits.add(new CampoFoco(CLIENTES_CEP_COBRANCA, edtCEP1, lblValidarCEP1, vBoxCEP1, hBoxEnd1));
                edits.add(new CampoFoco(CLIENTES_RUA_COBRANCA, edtRua1, lblValidarRua1, vBoxRua1, hBoxEnd3));
                edits.add(new CampoFoco(CLIENTES_BAIRRO_COBRANCA, edtBairro1, lblValidarBairro1, vBoxBairro1, hBoxEnd3));
                edits.add(new CampoFoco(CLIENTES_FACHADA_COBRANCA, edtNumero1, lblValidarNumero1, vBoxNumero1, FHBox2));
                break;
            default:
                break;
        }
        switch (edTpEnd2.getValue().asInteger()) {
            case 1:
                edits.add(new CampoFoco(CLIENTES_UF_RES, edUF2, lblValidarUF2, vBoxUF2, hBoxEnd5));
                edits.add(new CampoFoco(CLIENTES_COD_CID_RES, edtCidade2, lblValidarCidade2, vBoxCidade2, hBoxEnd5));
                edits.add(new CampoFoco(CLIENTES_CEP_RES, edtCEP2, lblValidarCEP2, vBoxCEP2, hBoxEnd4));
                edits.add(new CampoFoco(CLIENTES_RUA_RES, edtRua2, lblValidarRua2, vBoxRua2, hBoxEnd6));
                edits.add(new CampoFoco(CLIENTES_BAIRRO_RES, edtBairro2, lblValidarBairro2, vBoxBairro2, hBoxEnd6));
                edits.add(new CampoFoco(CLIENTES_FACHADA_RES, edtNumero2, lblValidarNumero2, vBoxNumero2, FHBox4));
                break;
            case 2:
                edits.add(new CampoFoco(CLIENTES_UF_COM, edUF2, lblValidarUF2, vBoxUF2, hBoxEnd5));
                edits.add(new CampoFoco(CLIENTES_COD_CID_COM, edtCidade2, lblValidarCidade2, vBoxCidade2, hBoxEnd5));
                edits.add(new CampoFoco(CLIENTES_CEP_COM, edtCEP2, lblValidarCEP2, vBoxCEP2, hBoxEnd4));
                edits.add(new CampoFoco(CLIENTES_RUA_COM, edtRua2, lblValidarRua2, vBoxRua2, hBoxEnd6));
                edits.add(new CampoFoco(CLIENTES_BAIRRO_COM, edtBairro2, lblValidarBairro2, vBoxBairro2, hBoxEnd6));
                edits.add(new CampoFoco(CLIENTES_FACHADA_COM, edtNumero2, lblValidarNumero2, vBoxNumero2, FHBox4));
                break;
            case 3:
                edits.add(new CampoFoco(CLIENTES_UF_COBRANCA, edUF2, lblValidarUF2, vBoxUF2, hBoxEnd5));
                edits.add(new CampoFoco(CLIENTES_COD_CID_COBRANCA, edtCidade2, lblValidarCidade2, vBoxCidade2, hBoxEnd5));
                edits.add(new CampoFoco(CLIENTES_CEP_COBRANCA, edtCEP2, lblValidarCEP2, vBoxCEP2, hBoxEnd4));
                edits.add(new CampoFoco(CLIENTES_RUA_COBRANCA, edtRua2, lblValidarRua2, vBoxRua2, hBoxEnd6));
                edits.add(new CampoFoco(CLIENTES_BAIRRO_COBRANCA, edtBairro2, lblValidarBairro2, vBoxBairro2, hBoxEnd6));
                edits.add(new CampoFoco(CLIENTES_FACHADA_COBRANCA, edtNumero2, lblValidarNumero2, vBoxNumero2, FHBox4));
                break;
            default:
                break;
        }
        switch (edTpEnd3.getValue().asInteger()) {
            case 1:
                edits.add(new CampoFoco(CLIENTES_UF_RES, edUF3, lblValidarUF3, vBoxUF3, hBoxEnd8));
                edits.add(new CampoFoco(CLIENTES_COD_CID_RES, edtCidade3, lblValidarCidade3, vBoxCidade3, hBoxEnd8));
                edits.add(new CampoFoco(CLIENTES_CEP_RES, edtCEP3, lblValidarCEP3, vBoxCEP3, hBoxEnd7));
                edits.add(new CampoFoco(CLIENTES_RUA_RES, edtRua3, lblValidarRua3, vBoxRua3, hBoxEnd9));
                edits.add(new CampoFoco(CLIENTES_BAIRRO_RES, edtBairro3, lblValidarBairro3, vBoxBairro3, hBoxEnd9));
                edits.add(new CampoFoco(CLIENTES_FACHADA_RES, edtNumero3, lblValidarNumero3, vBoxNumero3, FHBox6));
                break;
            case 2:
                edits.add(new CampoFoco(CLIENTES_UF_COM, edUF3, lblValidarUF3, vBoxUF3, hBoxEnd8));
                edits.add(new CampoFoco(CLIENTES_COD_CID_COM, edtCidade3, lblValidarCidade3, vBoxCidade3, hBoxEnd8));
                edits.add(new CampoFoco(CLIENTES_CEP_COM, edtCEP3, lblValidarCEP3, vBoxCEP3, hBoxEnd7));
                edits.add(new CampoFoco(CLIENTES_RUA_COM, edtRua3, lblValidarRua3, vBoxRua3, hBoxEnd9));
                edits.add(new CampoFoco(CLIENTES_BAIRRO_COM, edtBairro3, lblValidarBairro3, vBoxBairro3, hBoxEnd9));
                edits.add(new CampoFoco(CLIENTES_FACHADA_COM, edtNumero3, lblValidarNumero3, vBoxNumero3, FHBox6));
                break;
            case 3:
                edits.add(new CampoFoco(CLIENTES_UF_COBRANCA, edUF3, lblValidarUF3, vBoxUF3, hBoxEnd8));
                edits.add(new CampoFoco(CLIENTES_COD_CID_COBRANCA, edtCidade3, lblValidarCidade3, vBoxCidade3, hBoxEnd8));
                edits.add(new CampoFoco(CLIENTES_CEP_COBRANCA, edtCEP3, lblValidarCEP3, vBoxCEP3, hBoxEnd7));
                edits.add(new CampoFoco(CLIENTES_RUA_COBRANCA, edtRua3, lblValidarRua3, vBoxRua3, hBoxEnd9));
                edits.add(new CampoFoco(CLIENTES_BAIRRO_COBRANCA, edtBairro3, lblValidarBairro3, vBoxBairro3, hBoxEnd9));
                edits.add(new CampoFoco(CLIENTES_FACHADA_COBRANCA, edtNumero3, lblValidarNumero3, vBoxNumero3, FHBox6));
                break;
            default:
                break;
        }
    }

    private void reiniciarMensagemValidacao() {
        CampoFoco obj;
        for (CampoFoco edit : edits) {
            obj = edit;
            obj.getLblMensagem().setVisible(false);
            if (obj.getEdit() instanceof TFDecimal) {
                ((TFDecimal) obj.getEdit()).setRequired(false);
            }
            if (obj.getEdit() instanceof TFInteger) {
                ((TFInteger) obj.getEdit()).setRequired(false);
            }
            if (obj.getEdit() instanceof TFString) {
                ((TFString) obj.getEdit()).setRequired(false);
            }
            if (obj.getEdit() instanceof TFCombo) {
                ((TFCombo) obj.getEdit()).setRequired(false);
            }
            if (obj.getEdit() instanceof TFDate) {
                ((TFDate) obj.getEdit()).setRequired(false);
            }
            /*
            if (obj.getBox() != null) {
                obj.getBox().setHeight(70);
            }
            if (obj.getHbox() != null) {
                obj.getHbox().setHeight(70);
                obj.getHbox().setFlexVflex("ftFalse");
            }
            */
        }
    }

    private void setFocusCampos(Value aCampoFoco,
                                Value aMensagem) {
        boolean validouAlguem = false;
        CampoFoco obj = null;
        String[] camposFoco = aCampoFoco.asString().split(";");
        String[] mensagensFoco = aMensagem.asString().split(";");
        reiniciarMensagemValidacao();
        if (camposFoco.length != mensagensFoco.length) {
            EmpresaUtil.showWarning("Falha", aMensagem.asString());
            return;
        }
        boolean ehSoUmCampo = (camposFoco.length == 1);
        for (int i = 0; i < camposFoco.length; i++) {
            String field = camposFoco[i];
            String mensagem = mensagensFoco[i];
            boolean achouCampo = false;
            for (CampoFoco edit : edits) {
                obj = edit;
                if (obj.getField().equals(field.toUpperCase())) {
                    achouCampo = true;
                    break;
                }
            }
            if (achouCampo) {
                validouAlguem = true;
                if (obj.getEdit() instanceof TFDecimal) {
                    ((TFDecimal) obj.getEdit()).setRequired(true);
                }
                if (obj.getEdit() instanceof TFInteger) {
                    ((TFInteger) obj.getEdit()).setRequired(true);
                }
                if (obj.getEdit() instanceof TFString) {
                    ((TFString) obj.getEdit()).setRequired(true);
                }
                if (obj.getEdit() instanceof TFCombo) {
                    if (ehSoUmCampo) {
                        ((TFCombo) obj.getEdit()).setOpen(true);
                    }
                    ((TFCombo) obj.getEdit()).setRequired(true);
                }
                if (obj.getEdit() instanceof TFDate) {
                    ((TFDate) obj.getEdit()).setRequired(true);
                }
                obj.getLblMensagem().setHint(mensagem);
                obj.getLblMensagem().setCaption(mensagem);
                obj.getLblMensagem().setVisible(true);
                /*
                if (obj.getBox() != null) {
                    obj.getBox().setHeight(80);
                }
                if (obj.getHbox() != null) {
                    obj.getHbox().setHeight(80);
                }
                */
                if (i == 0) {
                    obj.getEdit().setFocus();
                }
            }
        }
        if (validouAlguem) {
            vBoxPrincipal.invalidate();
        } else {
            EmpresaUtil.showWarning("Falha", aMensagem.asString());
        }
    }

    private String incluirCliente(Double xCodCliente,
                                  Double xCodEmpresa,
                                  String xCPFCNPJ,
                                  String horaFormatada,
                                  String soValidar,
                                  Value oCampoFoco) {
        try {
            tbClientes.edit();
            tbClientes.post();
            String crmFone = getValorCombo(cboCrmFone);
            String crmEmail = getValorCombo(cboCrmEmail);
            String crmSMS = getValorCombo(cboCrmSMS);
            String crmMalaDireta = getValorCombo(cboCrmMalaDireta);
            String crmCartaoDotz = getValorCombo(cboCartaoDotz);
            Double pServidorPublico = Double.valueOf(getValorComboNumero(cboServidorPublico));
            Double pPoliticamenteExposto = Double.valueOf(getValorComboNumero(cboPoliticamenteExposto));
            Double pEscolaridade = Double.valueOf(getValorComboNumero(cboEscolaridade));
            String pConcessionaria = getValorCombo(cboConcessionaria);
            double codMidia = this.cboMidia.getValue().asDecimal();
            return rn.incluirCliente(cboTipo.getValue().asString(),
                    xCodCliente,
                    tbClientes.getNOME().asString(),
                    xCodEmpresa,
                    xCPFCNPJ,
                    cboTeleContato.getValue().asString(),
                    crmFone,
                    crmEmail,
                    crmSMS,
                    crmMalaDireta,
                    crmCartaoDotz,
                    tbDadosFisicos.getCOD_SEXO().asString(),
                    tbDadosFisicos.getCOD_ESTADO_CIVIL().asDecimal(),
                    tbDadosFisicos.getANIVERSARIO().asDate(),
                    tbDadosFisicos.getRG_NUMERO().asString(),
                    tbDadosFisicos.getRG_EMISSOR().asString(),
                    tbDadosFisicos.getRG_DATA_EMISSAO().asDate(),
                    tbDadosJuridicos.getCOD_RAMO().asString(),
                    tbDadosJuridicos.getINSC_ESTADUAL().asString(),
                    tbDadosJuridicos.getINSC_MUNICIPAL().asString(),
                    tbClientes.getCOD_NACIONALIDADE().asDecimal(),
                    tbClientes.getCOD_PROFISSAO().asDecimal(),
                    tbClientes.getUF_RES().asString(),
                    tbClientes.getCOD_CID_RES().asDecimal(),
                    tbClientes.getBAIRRO_RES().asString(),
                    tbClientes.getRUA_RES().asString(),
                    tbClientes.getCEP_RES().asString(),
                    tbClientes.getCOMPLEMENTO_RES().asString(),
                    tbClientes.getUF_COM().asString(),
                    tbClientes.getCOD_CID_COM().asDecimal(),
                    tbClientes.getBAIRRO_COM().asString(),
                    tbClientes.getRUA_COM().asString(),
                    tbClientes.getCEP_COM().asString(),
                    tbClientes.getCOMPLEMENTO_COM().asString(),
                    tbClientes.getUF_COBRANCA().asString(),
                    tbClientes.getCOD_CID_COBRANCA().asDecimal(),
                    tbClientes.getBAIRRO_COBRANCA().asString(),
                    tbClientes.getRUA_COBRANCA().asString(),
                    tbClientes.getCEP_COBRANCA().asString(),
                    tbClientes.getCOMPLEMENTO_COBRANCA().asString(),
                    tbClientes.getPREFIXO_CEL().asString(),
                    tbClientes.getTELEFONE_CEL().asString(),
                    tbClientes.getPREFIXO_COM().asString(),
                    tbClientes.getTELEFONE_COM().asString(),
                    tbClientes.getPREFIXO_RES().asString(),
                    tbClientes.getTELEFONE_RES().asString(),
                    tbClientes.getPREFIXO_FAX().asString(),
                    tbClientes.getTELEFONE_FAX().asString(),
                    tbClientes.getENDERECO_ELETRONICO().asString(),
                    tbClientes.getEMAIL_NFE().asString(),
                    horaFormatada,
                    tbClientes.getFACHADA_RES().asString(),
                    tbClientes.getFACHADA_COM().asString(),
                    tbClientes.getFACHADA_COBRANCA().asString(),
                    edTpEnd1.getValue().asDecimal(),
                    edTpEnd2.getValue().asDecimal(),
                    edTpEnd3.getValue().asDecimal(),
                    tbClientes.getFACEBOOK().asString().trim(),
                    tbClientes.getTWITTER().asString().trim(),
                    tbClienteDiverso.getEMPRESA_SITE().asString().trim(),
                    tbClienteDiverso.getNUMERO_CNO().asString().trim(),
                    tbDadosJuridicos.getNOME_FANTASIA().asString().trim(),
                    tbClienteDiverso.getCOD_SUB_TRIB_ISS().asString().trim(),
                    tbClienteDiverso.getCOD_REG_ESPECIAL_TRIBUT().asDecimal(),
                    tbDadosJuridicos.getCNAE().asString().trim(),
                    soValidar,
                    tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString(),
                    tbClienteEnderecoInscricao.getNOME_PROPRIEDADE().asString(),
                    tbClienteEnderecoInscricao.getUF().asString(),
                    tbClienteEnderecoInscricao.getCOD_CIDADES().asDecimal(),
                    tbClienteEnderecoInscricao.getBAIRRO().asString(),
                    tbClienteEnderecoInscricao.getRUA().asString(),
                    tbClienteEnderecoInscricao.getCEP().asString(),
                    tbClienteEnderecoInscricao.getCOMPLEMENTO().asString(),
                    tbClienteEnderecoInscricao.getFACHADA().asString(),
                    tbClienteEnderecoInscricao.getCX_POSTAL().asString(),
                    tbClienteEnderecoInscricao.getCONTATO().asString(),
                    tbClienteEnderecoInscricao.getATIVO().asString(),
                    pServidorPublico,
                    pPoliticamenteExposto,
                    pEscolaridade,
                    tbClienteDiverso.getNRO_SUFRAMA().asDecimal(),
                    tbDadosJuridicos.getDATA_FUNDACAO().asDate(),
                    pConcessionaria,
                    this.usuarioLogado,
                    codMidia,
                    oCampoFoco);
        } catch (DataException ex) {
            return ex.getMessage();
        }
    }


    @Override
    public void btnSalvarClick(final Event<Object> event) {
        String tipo = this.cboTipo.getValue().asString();
        if (tipo.equals("F")) {
            String cpf = this.edtCPF.getValue().asString();
            if (cpf.isEmpty()) {
                String mensagem = "O campo \""
                        + this.lblCPF.getCaption()
                        + "\" deve ser preenchido.";
                Dialog.create()
                        .title(FrmCadastroRapidoClienteA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtCPF.setFocus()));
                return;
            }
        } else {
            String cnpj = this.edtCNPJ.getValue().asString();
            if (cnpj.isEmpty()) {
                String mensagem = "O campo \""
                        + this.lblCNPJ.getCaption()
                        + "\" deve ser preenchido.";
                Dialog.create()
                        .title(FrmCadastroRapidoClienteA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtCNPJ.setFocus()));
                return;
            }
            if (this.ehCRMParts) {
                boolean obrigatorioInformarContatoCRMPartsParmSys2ForcarContato = this.rn.isObrigatorioInformarContatoCRMPartsParmSys2ForcarContato(this.codEmpresaUsuarioLogado);
                if (obrigatorioInformarContatoCRMPartsParmSys2ForcarContato) {
                    String codClientePreenchido = this.edtCNPJ.getValue().asString().trim();
                    String cnpjSemPontos = codClientePreenchido.replaceAll(
                            "\\D"
                            ,""
                    );
                    boolean possuiContato = this.possuiContato(Double.parseDouble(cnpjSemPontos));
                    if (!possuiContato) {
                        String mensagem = "O cliente do tipo pessoa jurídica deve ter, no mínimo, um contato cadastrado, conforme definido no parâmetro \"PARM_SYS2.FORCAR_CONTATO\".";
                        Dialog.create()
                                .title(FrmCadastroRapidoClienteA.INFORMACAO)
                                .message(mensagem)
                                .showInformation(t -> FreedomUtilities.invokeLater(() -> this.btnContatoClick(event)));
                        return;
                    }
                }
            }
        }
        this.salvar();
    }

    public String formatarCpfCnpj(String value) {
        return FormatChange.formataCpfCnpj(value);
    }

    private void salvar() throws NumberFormatException {
        String xCPFCNPJ = "";
        String auxCpfCnpj;
        double xCodCliente = 0.0;
        String retFuncao;
        Value campoFoco = new Value(null);
        Value mensagemFoco = new Value(null);
        this.exibirStatusCadastro();
        try {
            SimpleDateFormat sdfHora = new SimpleDateFormat("HH:mm");
            Date hora = timeCrmMelhorHorario.getValue().asDate();
            String horaFormatada = "00:00";
            if (hora != null) {
                horaFormatada = sdfHora.format(hora);
            }
            if (!consistirDados()) {
                return;
            }
            this.prencherEditsValidacaoEndereco();
            if (cboTipo.getValue().asString().equals("F")) {
                if (!edtCPF.getValue().isEmpty()) {
                    auxCpfCnpj = edtCPF.getValue().asString().replaceAll("\\D", "");
                    xCodCliente = Double.parseDouble(auxCpfCnpj);
                    xCPFCNPJ = this.formatarCpfCnpj(auxCpfCnpj);
                }
            } else {
                if (!edtCNPJ.getValue().isEmpty()) {
                    auxCpfCnpj = edtCNPJ.getValue().asString().replaceAll("\\D", "");
                    xCodCliente = Double.parseDouble(auxCpfCnpj);
                    xCPFCNPJ = this.formatarCpfCnpj(auxCpfCnpj);
                }
            }
            if (this.codCliente <= 0) {
                this.setCampos();
                if (xCodCliente > 0) {
                    retFuncao = this.incluirCliente(xCodCliente,
                            (double) this.codEmpresaUsuarioLogado,
                            xCPFCNPJ,
                            horaFormatada,
                            "N",
                            campoFoco);
                    mensagemFoco.setValue(retFuncao);
                } else {
                    // Acrescentado por conta do Tablet não abre campo data se estiver null
                    if (this.isResponsive
                            && (this.dtNasc.getValue().isNull())) {
                        this.dtNasc.setValue(new Date());
                    }
                    if (this.cboTipo.getValue().asString().equals("F")
                            && ((this.cboNacionalidade.getValue().isEmpty())
                            || (this.cboNacionalidade.getValue().asInteger() == 0))) {
                        this.cboNacionalidade.setValue(this.tbParmSys.getCOD_NACIONALIDADE().asInteger());
                    }
                    return;
                }
                if (!retFuncao.equals("S")) {
                    FreedomUtilities.invokeLater(() -> setFocusCampos(campoFoco,
                            mensagemFoco));
                    return;
                } else {
                    this.reiniciarMensagemValidacao();
                }
                this.codCliente = xCodCliente;
                this.rn.incluirClienteLog(this.codCliente,
                        this.usuarioLogado);
                if (this.ehRodobens) {
                    this.rn.gerarSapCliente((double) this.codEmpresaUsuarioLogado,
                            this.codCliente);
                }
                if (this.podeAtualizarCadastroCliente(xCodCliente)) {
                    this.atualizarCadastroCliente(xCodCliente,
                            this.tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString(),
                            this.codEmpresaUsuarioLogado);
                }
                EmpresaUtil.showInformationMessage("Cadastro incluído com Sucesso!");
            } else {
                this.setCampos();
                retFuncao = this.incluirCliente(xCodCliente,
                        (double) this.codEmpresaUsuarioLogado,
                        xCPFCNPJ,
                        horaFormatada,
                        "S",
                        campoFoco);
                mensagemFoco.setValue(retFuncao);
                if (!retFuncao.equals("S")) {
                    FreedomUtilities.invokeLater(() -> setFocusCampos(campoFoco,
                            mensagemFoco));
                    return;
                } else {
                    this.rn.incluirClienteLog(this.codCliente,
                            this.usuarioLogado);
                    if (this.ehRodobens) {
                        this.rn.gerarSapCliente((double) this.codEmpresaUsuarioLogado,
                                this.codCliente);
                    }
                    this.reiniciarMensagemValidacao();
                }
                this.tbClientes.edit();
                this.prefixoCel = this.tbClientes.getPREFIXO_CEL().asInteger();
                this.telefoneCel = this.tbClientes.getTELEFONE_CEL().asInteger();
                if (this.tbClientes.getUF_COBRANCA().asString().isEmpty()) {
                    if (this.cboTipo.getValue().asString().equals("F")) {
                        this.tbClientes.setUF_COBRANCA(this.tbClientes.getUF_RES().asString());
                        this.tbClientes.setCOD_CID_COBRANCA(this.tbClientes.getCOD_CID_RES().asInteger());
                        this.tbClientes.setCEP_COBRANCA(this.tbClientes.getCEP_RES().asString());
                        this.tbClientes.setRUA_COBRANCA(this.tbClientes.getRUA_RES().asString());
                        this.tbClientes.setBAIRRO_COBRANCA(this.tbClientes.getBAIRRO_RES().asString());
                        this.tbClientes.setCOMPLEMENTO_COBRANCA(this.tbClientes.getCOMPLEMENTO_RES().asString());
                        this.tbClientes.setFACHADA_COBRANCA(this.tbClientes.getFACHADA_RES().asString());
                    } else {
                        this.tbClientes.setUF_COBRANCA(this.tbClientes.getUF_COM().asString());
                        this.tbClientes.setCOD_CID_COBRANCA(this.tbClientes.getCOD_CID_COM().asInteger());
                        this.tbClientes.setCEP_COBRANCA(this.tbClientes.getCEP_COM().asString());
                        this.tbClientes.setRUA_COBRANCA(this.tbClientes.getRUA_COM().asString());
                        this.tbClientes.setBAIRRO_COBRANCA(this.tbClientes.getBAIRRO_COM().asString());
                        this.tbClientes.setCOMPLEMENTO_COBRANCA(this.tbClientes.getCOMPLEMENTO_COM().asString());
                        this.tbClientes.setFACHADA_COBRANCA(this.tbClientes.getFACHADA_COM().asString());
                    }
                }
                this.tbClientes.post();
                this.tbClienteDiverso.edit();
                this.tbClienteDiverso.setTELE_HORARIO_CONTATO(horaFormatada);
                this.tbClienteDiverso.setNOME(this.edtNomeCliente.getValue());
                if (!this.cboTipo.getValue().asString().equals("F")) {
                    this.tbClienteDiverso.setINSCRICAO_ESTADUAL(this.edtINSC_ESTADUAL.getValue());
                }
                this.tbClienteDiverso.setDATA_ULTIMA_ATUALIZACAO(DateUtils.truncDay(new Date()));
                this.tbClienteDiverso.post();
                if (this.cboTipo.getValue().asString().equals("F")) {
                    this.tbDadosFisicos.edit();
                    this.tbDadosFisicos.post();
                } else {
                    this.tbDadosJuridicos.edit();
                    this.tbDadosJuridicos.post();
                }
                this.tbClienteEnderecoInscricao.post();
                this.rn.applyUpdatesCadastroCliente(this.cboTipo.getValue().asString().equals("F"),
                        this.usuarioLogado);
                EmpresaUtil.showInformationMessage("Cadastro alterado com Sucesso!");
            }
            this.fechou = true;
            // Acrescentado por conta do Tablet não abre campo data se estiver null
            if (this.isResponsive
                    && (this.dtNasc.getValue().isNull())) {
                this.dtNasc.setValue(new Date());
            }
            this.fechadoAoSalvar = true;
            if (!this.abriuComoTabSheet()) {
                this.close();
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Falha ao Salvar Cadastro. Motivo: ",
                    dataException);
        }
    }

    private boolean abriuComoTabSheet() {
        boolean ehTabSheet = false;
        TFPageControl pg = FormUtil.getPgCtrl();
        for (int i = 0; i < pg.getPageCount(); i++) {
            ITFTabsheet<Component> tab = pg.getTabSheet(i);
            if (tab.getCaption().equals("Cadastro Rápido de Cliente")) {
                if (!this.isResponsive) {
                    tab.close();
                    ehTabSheet = true;
                }
                break;
            }
        }
        return ehTabSheet;
    }

    private boolean podeAtualizarCadastroCliente(double codCliente) {
        return (codCliente == this.codClienteConsultadoApi);
    }

    private void atualizarCadastroCliente(double codCliente,
                                          String inscricaoEstadual,
                                          double codEmpresa) {
        try {
            this.rn.atualizarCadastroCliente(codCliente,
                    inscricaoEstadual,
                    codEmpresa);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao atualizar o cadastro do cliente",
                    dataException);
        }
    }

    @Override
    public void cboTipoChange(Event<Object> event) {
        try {
            if (cboTipo.getValue().asString().equals("F")) {
                vBoxPessoaFisica.setVisible(Boolean.TRUE);
                vBoxPessoaJuridica.setVisible(Boolean.FALSE);
                vBoxFoneContatoPJ.setVisible(false);
            } else {
                vBoxPessoaFisica.setVisible(Boolean.FALSE);
                vBoxPessoaJuridica.setVisible(Boolean.TRUE);
                vBoxFoneContatoPJ.setVisible(true);
                cboConcessionaria.setValue("N");
            }
            this.rn.abrirTabelaAux(cboTipo.getValue().asString());
            salvar();
        } catch (Exception ex) {
            EmpresaUtil.showError("Falha ao Alterar Tipo. Motivo: ", ex);
        }
    }

    @Override
    public void btnExcluirEnd3Click(final Event<Object> event) {
        excluirEndereco(3);
    }

    @Override
    public void btnExcluirEnd2Click(final Event<Object> event) {
        excluirEndereco(2);
    }

    @Override
    public void btnExcluirEnd1Click(final Event<Object> event) {
        excluirEndereco(1);
    }

    void excluirEndereco(int index) {
        switch (index) {
            case 1:
                if (hBoxEnd03.isVisible() || hBoxEnd02.isVisible()) {
                    hBoxEnd01.setVisible(false);
                }
                edTpEnd1.clear();
                edUF1.clear();
                edtCidade1.clear();
                edtCEP1.clear();
                edtRua1.clear();
                edtBairro1.clear();
                edtComplemento1.clear();
                edtNumero1.clear();
                edTpEnd1.setValue(null);
                edUF1.setValue(null);
                edtCidade1.setValue(null);
                break;
            case 2:
                if (hBoxEnd01.isVisible() || hBoxEnd03.isVisible()) {
                    hBoxEnd02.setVisible(false);
                    edTpEnd2.clear();
                    edUF2.clear();
                    edtCidade2.clear();
                    edtCEP2.clear();
                    edtRua2.clear();
                    edtBairro2.clear();
                    edtComplemento2.clear();
                    edtNumero2.clear();
                    edTpEnd2.setValue(null);
                    edUF2.setValue(null);
                    edtCidade2.setValue(null);
                }
                break;
            case 3:
                if (hBoxEnd01.isVisible() || hBoxEnd02.isVisible()) {
                    hBoxEnd03.setVisible(false);
                    edTpEnd3.clear();
                    edUF3.clear();
                    edtCidade3.clear();
                    edtCEP3.clear();
                    edtRua3.clear();
                    edtBairro3.clear();
                    edtComplemento3.clear();
                    edtNumero3.clear();
                    edTpEnd3.setValue(null);
                    edUF3.setValue(null);
                    edtCidade3.setValue(null);
                }
                break;
            default:
                break;
        }
    }

    void incluirEndereco() {
        boolean boxEnd1NotVisible = !this.hBoxEnd01.isVisible();
        if (boxEnd1NotVisible) {
            hBoxEnd01.setVisible(true);
            edTpEnd1.clear();
            edUF1.clear();
            edtCidade1.clear();
            edtCEP1.clear();
            edtRua1.clear();
            edtBairro1.clear();
            edtComplemento1.clear();
            edtNumero1.clear();
            edUF1.setValue(tbParmSys.getUF().asString());
            edtCidade1.setValue(tbParmSys.getCOD_CIDADES().asInteger());
            return;
        }
        boolean boxEnd2NotVisible = !this.hBoxEnd02.isVisible();
        if (boxEnd2NotVisible) {
            hBoxEnd02.setVisible(true);
            edTpEnd2.clear();
            edUF2.clear();
            edtCidade2.clear();
            edtCEP2.clear();
            edtRua2.clear();
            edtBairro2.clear();
            edtComplemento2.clear();
            edtNumero2.clear();
            edUF2.setValue(tbParmSys.getUF().asString());
            edtCidade2.setValue(tbParmSys.getCOD_CIDADES().asInteger());
            return;
        }
        boolean boxEnd3NotVisible = !hBoxEnd03.isVisible();
        if (boxEnd3NotVisible) {
            hBoxEnd03.setVisible(true);
            edTpEnd3.clear();
            edUF3.clear();
            edtCidade3.clear();
            edtCEP3.clear();
            edtRua3.clear();
            edtBairro3.clear();
            edtComplemento3.clear();
            edtNumero3.clear();
            edUF3.setValue(tbParmSys.getUF().asString());
            edtCidade3.setValue(tbParmSys.getCOD_CIDADES().asInteger());
            return;
        }
        EmpresaUtil.showInformationMessage("Número máximo de endereço obtido.");
    }

    @Override
    public void btnIncluirClick(final Event<Object> event) {
        this.incluirEndClick = true;
        this.incluirEndereco();
        this.incluirEndClick = false;
    }

    boolean consistirDados() {
        String mensagem = "";
        double xQtdeEndRes = 0.0;
        double xQtdeEndCom = 0.0;
        double xQtdeEndCob = 0.0;
        boolean edtCPFEnabled = edtCPF.isEnabled();
        if ((!edtCPF.getValue().isEmpty())
                && edtCPFEnabled
                && (!ValidarUtil.isValidCPF(edtCPF.getValue().asString().replaceAll("\\D", "")))) {
            mensagem += "CPF Inválido." + System.lineSeparator();
        }
        boolean edtCNPJEnabled = edtCNPJ.isEnabled();
        if ((!edtCNPJ.getValue().isEmpty())
                && edtCNPJEnabled
                && (!ValidarUtil.isValidCNPJ(edtCNPJ.getValue().asString().replaceAll("\\D", "")))) {
            mensagem += "CNPJ Inválido." + System.lineSeparator();
        }
        if (!edtEmail.getValue().isEmpty()
                && (!ValidarUtil.isEmailValid(edtEmail.getValue().asString()))) {
            mensagem += "E-mail Inválido." + System.lineSeparator();
        }
        if (!edtEmailNFE.getValue().isEmpty()
                && (!ValidarUtil.isEmailValid(edtEmailNFE.getValue().asString()))) {
            mensagem += "E-mail NF-e Inválido." + System.lineSeparator();
        }
        if ((edTpEnd1.getValue().isEmpty()) && (edTpEnd2.getValue().isEmpty()) && (edTpEnd3.getValue().isEmpty())) {
            mensagem += "Deve ser preenchido algum endereço (Residencial/Comercial/Cobrança). Caso contrário, não será possível emitir NF-e!"
                    + System.lineSeparator();
        }
        if (edTpEnd1.getValue().asInteger().equals(1)) {
            xQtdeEndRes++;
        }
        if (edTpEnd2.getValue().asInteger().equals(1)) {
            xQtdeEndRes++;
        }
        if (edTpEnd3.getValue().asInteger().equals(1)) {
            xQtdeEndRes++;
        }
        if (edTpEnd1.getValue().asInteger().equals(2)) {
            xQtdeEndCom++;
        }
        if (edTpEnd2.getValue().asInteger().equals(2)) {
            xQtdeEndCom++;
        }
        if (edTpEnd3.getValue().asInteger().equals(2)) {
            xQtdeEndCom++;
        }
        if (edTpEnd1.getValue().asInteger().equals(3)) {
            xQtdeEndCob++;
        }
        if (edTpEnd2.getValue().asInteger().equals(3)) {
            xQtdeEndCob++;
        }
        if (edTpEnd3.getValue().asInteger().equals(3)) {
            xQtdeEndCob++;
        }
        if ((xQtdeEndRes > 1) || (xQtdeEndCom > 1) || (xQtdeEndCob > 1)) {
            mensagem += "Não é permitido informar o tipo de endereço repetido."
                    + System.lineSeparator();
        }


        if (mensagem.isEmpty()) {
            return true;
        } else {
            EmpresaUtil.showInformationMessage(mensagem);
        }
        return false;
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        this.fechou = true;
        if (!this.abriuComoTabSheet()) {
            close();
        }
    }

    @Override
    public void icoApagarCelularClick(final Event<Object> event) {
        edtDddCel.clear();
        edtTelefoneCel.clear();
        edtDddCel.setFocus();
    }

    @Override
    public void icoApagarTelResClick(final Event<Object> event) {
        edtDDDTelRes.clear();
        edtTelRes.clear();
        edtDDDTelRes.setFocus();
    }

    @Override
    public void icoApagarTelComClick(final Event<Object> event) {
        edtDddCom.clear();
        edtTelefoneCom.clear();
        edtDddCom.setFocus();
    }

    @Override
    public void icoApagarTelCobClick(final Event<Object> event) {
        edtDDDTelCob.clear();
        edtTelCob.clear();
        edtDDDTelCob.setFocus();
    }

    @Override
    public void icoApagarEmailNFeClick(final Event<Object> event) {
        edtEmailNFE.clear();
        edtEmailNFE.setFocus();
    }

    @Override
    public void icoApagarEmailClick(final Event<Object> event) {
        edtEmail.clear();
        edtEmail.setFocus();
    }

    @Override
    public void icoApagarNomeClick(final Event<Object> event) {
        edtNomeCliente.clear();
        edtNomeCliente.setFocus();
    }

    /*
    public boolean servicoOk(String strCEP, Boolean allEnd, Value xMensagem) throws Exception {
        FrmCEPClienteOnLineA formCEPClienteOnLine = new FrmCEPClienteOnLineA();
        boolean xReturn = formCEPClienteOnLine.servicoOk(strCEP, allEnd, xMensagem);
        this.jsonCepDados = formCEPClienteOnLine.retJsonCepDados();
        return xReturn;
    }
    */

    public boolean temIbge(CIDADES tbCidade, String ibge) throws DataException {
        return tbCidade.locate("CODCIDADEIBGE", ibge);
    }

    @Override
    public void btnPesquisaCep1Click(final Event<Object> event) {
        String cep = this.edtCEP1.getValue().asString().trim();
        FrmPesquisaCEPOnlineA frmPesquisaCEPOnlineA = new FrmPesquisaCEPOnlineA();
        if (!cep.isEmpty()) {
            frmPesquisaCEPOnlineA.edtCEP.setValue(
                    cep
            );
            frmPesquisaCEPOnlineA.pesquisarCEP();
        }
        FormUtil.doShow(
                frmPesquisaCEPOnlineA
                ,t -> {
                    boolean frmPesquisaCEPOnlineAFechadoAoAceitar = frmPesquisaCEPOnlineA.isFechadoAoAceitar();
                    if (frmPesquisaCEPOnlineAFechadoAoAceitar) {
                        //region CEP
                        String cepPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                StringUtil.removerCaracteresNaoNumericos(
                                        frmPesquisaCEPOnlineA.lblValorCEP.getCaption().trim().toUpperCase()
                                )
                                ,this.edtCEP1
                        );
                        this.edtCEP1.setValue(
                                cepPesquisado
                        );
                        //endregion
                        //region UF
                        String ufDoCEPPesquisado = frmPesquisaCEPOnlineA.lblValorUF.getCaption().trim().toUpperCase();
                        this.edUF1.setValue(
                                ufDoCEPPesquisado
                        );
                        //endregion
                        //region Cidade
                        String cidadeDoCEPPesquisado = frmPesquisaCEPOnlineA.lblValorLocalidade.getCaption().trim().toUpperCase();
                        this.edtCidade1.setText(
                                cidadeDoCEPPesquisado
                        );
                        //endregion
                        //region Rua
                        String ruaLogradouroDoCEPPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                frmPesquisaCEPOnlineA.lblValorLogradouro.getCaption().trim().toUpperCase()
                                ,this.edtRua1
                        );
                        this.edtRua1.setValue(
                                ruaLogradouroDoCEPPesquisado
                        );
                        //endregion
                        //region Bairro
                        String bairroDoCEPPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                frmPesquisaCEPOnlineA.lblValorBairro.getCaption().trim().toUpperCase()
                                ,this.edtBairro1
                        );
                        this.edtBairro1.setValue(
                                bairroDoCEPPesquisado
                        );
                        //endregion
                        //region Complemento
                        String complementoDoCEPPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                frmPesquisaCEPOnlineA.lblValorComplemento.getCaption().trim().toUpperCase()
                                ,this.edtComplemento1
                        );
                        this.edtComplemento1.setValue(
                                complementoDoCEPPesquisado
                        );
                        //endregion
                    }
                });
        /*
        if (edtCEP1.getValue().asString().trim().length() < 8) {
            lblTempoConsultaCEP.setVisible(false);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            lblDicaAPIViaCEP.setCaption(CEP_INVALIDO);
            hBoxDicaAPIViaCEP.setVisible(true);
            return;
        }
        if (edtCEP1.getValue().asString().trim().length() > 8) {
            lblTempoConsultaCEP.setVisible(false);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            lblDicaAPIViaCEP.setCaption(CEP_INVALIDO);
            hBoxDicaAPIViaCEP.setVisible(true);
            return;
        }
        lblTempoConsultaCEP.setVisible(false);
        lblDicaAPIViaCEP.setVisible(false);
        lblDicaAPIViaCEP02.setVisible(false);
        hBoxDicaAPIViaCEP.setVisible(false);
        lblSemDivergencias.setVisible(false);
        Value xMensagem = new Value(null);
        boolean tbCidades1NotEmpty = !this.tbCidades1.isEmpty();
        if (!edtCidade1.getValue().isNull()
                && tbCidades1NotEmpty
                && (tbCidades1.getCEP_POR_LOG_NVL().asString().trim().equals("N"))) {
            lblDicaAPIViaCEP.setCaption(CIDADE_NAO_USA_CEP_POR_RUA);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            hBoxDicaAPIViaCEP.setVisible(true);
            return;
        }
        if (!edtCEP1.getValue().asString().isEmpty()) {
            lblTempoConsultaCEP.setVisible(false);
            try {
                startTime = 0;
                endTime = 0;
                if (servicoOk(edtCEP1.getValue().asString().trim(), false, xMensagem)) {
                    getCEPOnLineService(1);
                } else {
                    if (endTime == 0) {
                        endTime = System.currentTimeMillis();
                    }
                    if (!xMensagem.isNull()) {
                        xAchouDivergencias = false;
                        if (xMensagem.asString().trim().equals(DICA_SERVIDOR_DE_APLICACAO_CRM_SERVICE_NAO_CONSEGUIU_CONECTAR_SE_A_API_HTTPS_VIACEP_COM_BR_VERIFIQUE_COM_SEU_DEPARTAMENTO_DE_TI)) {
                            lblDicaAPIViaCEP.setVisible(true);
                            lblDicaAPIViaCEP02.setVisible(true);
                            lblDicaAPIViaCEP.setCaption(xMensagem.asString().trim());
                        } else {
                            lblDicaAPIViaCEP.setVisible(true);
                            lblDicaAPIViaCEP02.setVisible(false);
                            lblDicaAPIViaCEP.setCaption(xMensagem.asString().trim());
                        }
                        hBoxDicaAPIViaCEP.setVisible(true);
                    }
                }
            } catch (Exception ex) {
                if (endTime == 0) {
                    endTime = System.currentTimeMillis();
                }
                lblDicaAPIViaCEP.setVisible(true);
                hBoxDicaAPIViaCEP.setVisible(true);
                EmpresaUtil.showError(FALHA_AO_CONSULTAR_CEP_MOTIVO, ex);
            }
            boolean hBoxDicaAPIViaCEPNotVisible = !this.hBoxDicaAPIViaCEP.isVisible();
            if (hBoxDicaAPIViaCEPNotVisible) {
                hBoxDicaAPIViaCEP.setVisible(true);
            }
            if (xAchouDivergencias) {
                lblTempoConsultaCEP.setCaption(CONSULTA_E_RETORNO_ON_LINE_DO_CEP_REALIZADA_EM + (endTime - startTime) + "ms");
            } else {
                lblTempoConsultaCEP.setCaption(CONSULTA_ON_LINE_DO_CEP_REALIZADA_EM + (endTime - startTime) + "ms");
            }
        }
        if (!(!xAchouDivergencias
                && lblDicaAPIViaCEP.isVisible()
                && !lblDicaAPIViaCEP02.isVisible())) {
            lblSemDivergencias.setVisible(!xAchouDivergencias && !lblDicaAPIViaCEP.isVisible() && !lblDicaAPIViaCEP02.isVisible());
        }
        */
    }

    @Override
    public void btnPesquisaCep2Click(final Event<Object> event) {
        String cep = this.edtCEP2.getValue().asString().trim();
        FrmPesquisaCEPOnlineA frmPesquisaCEPOnlineA = new FrmPesquisaCEPOnlineA();
        if (!cep.isEmpty()) {
            frmPesquisaCEPOnlineA.edtCEP.setValue(
                    cep
            );
            frmPesquisaCEPOnlineA.pesquisarCEP();
        }
        FormUtil.doShow(
                frmPesquisaCEPOnlineA
                ,t -> {
                    boolean frmPesquisaCEPOnlineAFechadoAoAceitar = frmPesquisaCEPOnlineA.isFechadoAoAceitar();
                    if (frmPesquisaCEPOnlineAFechadoAoAceitar) {
                        //region CEP
                        String cepPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                StringUtil.removerCaracteresNaoNumericos(
                                        frmPesquisaCEPOnlineA.lblValorCEP.getCaption().trim().toUpperCase()
                                )
                                ,this.edtCEP2
                        );
                        this.edtCEP2.setValue(
                                cepPesquisado
                        );
                        //endregion
                        //region UF
                        String ufDoCEPPesquisado = frmPesquisaCEPOnlineA.lblValorUF.getCaption().trim().toUpperCase();
                        this.edUF2.setValue(
                                ufDoCEPPesquisado
                        );
                        //endregion
                        //region Cidade
                        String cidadeDoCEPPesquisado = frmPesquisaCEPOnlineA.lblValorLocalidade.getCaption().trim().toUpperCase();
                        this.edtCidade2.setText(
                                cidadeDoCEPPesquisado
                        );
                        //endregion
                        //region Rua
                        String ruaLogradouroDoCEPPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                frmPesquisaCEPOnlineA.lblValorLogradouro.getCaption().trim().toUpperCase()
                                ,this.edtRua2
                        );
                        this.edtRua2.setValue(
                                ruaLogradouroDoCEPPesquisado
                        );
                        //endregion
                        //region Bairro
                        String bairroDoCEPPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                frmPesquisaCEPOnlineA.lblValorBairro.getCaption().trim().toUpperCase()
                                ,this.edtBairro2
                        );
                        this.edtBairro2.setValue(
                                bairroDoCEPPesquisado
                        );
                        //endregion
                        //region Complemento
                        String complementoDoCEPPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                frmPesquisaCEPOnlineA.lblValorComplemento.getCaption().trim().toUpperCase()
                                ,this.edtComplemento2
                        );
                        this.edtComplemento2.setValue(
                                complementoDoCEPPesquisado
                        );
                        //endregion
                    }
                });
        /*
        if (edtCEP2.getValue().asString().trim().length() < 8) {
            lblTempoConsultaCEP.setVisible(false);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            lblDicaAPIViaCEP.setCaption(CEP_INVALIDO);
            hBoxDicaAPIViaCEP.setVisible(true);
            return;
        }
        if (edtCEP2.getValue().asString().trim().length() > 8) {
            lblTempoConsultaCEP.setVisible(false);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            lblDicaAPIViaCEP.setCaption(CEP_INVALIDO);
            hBoxDicaAPIViaCEP.setVisible(true);
            return;
        }
        lblTempoConsultaCEP.setVisible(false);
        lblDicaAPIViaCEP.setVisible(false);
        lblDicaAPIViaCEP02.setVisible(false);
        hBoxDicaAPIViaCEP.setVisible(false);
        lblSemDivergencias.setVisible(false);
        Value xMensagem = new Value(null);
        boolean tbCidades2NotEmpty = !this.tbCidades2.isEmpty();
        if (!edtCidade2.getValue().isNull()
                && tbCidades2NotEmpty
                && (tbCidades2.getCEP_POR_LOG_NVL().asString().trim().equals("N"))) {
            lblDicaAPIViaCEP.setCaption(CIDADE_NAO_USA_CEP_POR_RUA);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            hBoxDicaAPIViaCEP.setVisible(true);
            return;
        }
        if (!edtCEP2.getValue().asString().isEmpty()) {
            lblTempoConsultaCEP.setVisible(false);
            try {
                startTime = 0;
                endTime = 0;
                if (servicoOk(edtCEP2.getValue().asString().trim(), false, xMensagem)) {
                    getCEPOnLineService(2);
                } else {
                    if (endTime == 0) {
                        endTime = System.currentTimeMillis();
                    }
                    if (!xMensagem.isNull()) {
                        xAchouDivergencias = false;
                        if (xMensagem.asString().trim().equals(DICA_SERVIDOR_DE_APLICACAO_CRM_SERVICE_NAO_CONSEGUIU_CONECTAR_SE_A_API_HTTPS_VIACEP_COM_BR_VERIFIQUE_COM_SEU_DEPARTAMENTO_DE_TI)) {
                            lblDicaAPIViaCEP.setVisible(true);
                            lblDicaAPIViaCEP02.setVisible(true);
                        } else {
                            lblDicaAPIViaCEP.setVisible(true);
                            lblDicaAPIViaCEP02.setVisible(false);
                            lblDicaAPIViaCEP.setCaption(xMensagem.asString().trim());
                        }
                        hBoxDicaAPIViaCEP.setVisible(true);
                    }
                }
            } catch (Exception ex) {
                if (endTime == 0) {
                    endTime = System.currentTimeMillis();
                }
                lblDicaAPIViaCEP.setVisible(true);
                hBoxDicaAPIViaCEP.setVisible(true);
                EmpresaUtil.showError(FALHA_AO_CONSULTAR_CEP_MOTIVO, ex);
            }
            boolean hBoxDicaAPIViaCEPNotVisible = !this.hBoxDicaAPIViaCEP.isVisible();
            if (hBoxDicaAPIViaCEPNotVisible) {
                hBoxDicaAPIViaCEP.setVisible(true);
            }
            if (xAchouDivergencias) {
                lblTempoConsultaCEP.setCaption(CONSULTA_E_RETORNO_ON_LINE_DO_CEP_REALIZADA_EM + (endTime - startTime) + "ms");
            } else {
                lblTempoConsultaCEP.setCaption(CONSULTA_ON_LINE_DO_CEP_REALIZADA_EM + (endTime - startTime) + "ms");
            }
        }
        if (!(!xAchouDivergencias
                && lblDicaAPIViaCEP.isVisible()
                && !lblDicaAPIViaCEP02.isVisible())) {
            lblSemDivergencias.setVisible(!xAchouDivergencias && !lblDicaAPIViaCEP.isVisible() && !lblDicaAPIViaCEP02.isVisible());
        }
        */
    }

    @Override
    public void btnPesquisaCep3Click(final Event<Object> event) {
        String cep = this.edtCEP3.getValue().asString().trim();
        FrmPesquisaCEPOnlineA frmPesquisaCEPOnlineA = new FrmPesquisaCEPOnlineA();
        if (!cep.isEmpty()) {
            frmPesquisaCEPOnlineA.edtCEP.setValue(
                    cep
            );
            frmPesquisaCEPOnlineA.pesquisarCEP();
        }
        FormUtil.doShow(
                frmPesquisaCEPOnlineA
                ,t -> {
                    boolean frmPesquisaCEPOnlineAFechadoAoAceitar = frmPesquisaCEPOnlineA.isFechadoAoAceitar();
                    if (frmPesquisaCEPOnlineAFechadoAoAceitar) {
                        //region CEP
                        String cepPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                StringUtil.removerCaracteresNaoNumericos(
                                        frmPesquisaCEPOnlineA.lblValorCEP.getCaption().trim().toUpperCase()
                                )
                                ,this.edtCEP3
                        );
                        this.edtCEP3.setValue(
                                cepPesquisado
                        );
                        //endregion
                        //region UF
                        String ufDoCEPPesquisado = frmPesquisaCEPOnlineA.lblValorUF.getCaption().trim().toUpperCase();
                        this.edUF3.setValue(
                                ufDoCEPPesquisado
                        );
                        //endregion
                        //region Cidade
                        String cidadeDoCEPPesquisado = frmPesquisaCEPOnlineA.lblValorLocalidade.getCaption().trim().toUpperCase();
                        this.edtCidade3.setText(
                                cidadeDoCEPPesquisado
                        );
                        //endregion
                        //region Rua
                        String ruaLogradouroDoCEPPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                frmPesquisaCEPOnlineA.lblValorLogradouro.getCaption().trim().toUpperCase()
                                ,this.edtRua3
                        );
                        this.edtRua3.setValue(
                                ruaLogradouroDoCEPPesquisado
                        );
                        //endregion
                        //region Bairro
                        String bairroDoCEPPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                frmPesquisaCEPOnlineA.lblValorBairro.getCaption().trim().toUpperCase()
                                ,this.edtBairro3
                        );
                        this.edtBairro3.setValue(
                                bairroDoCEPPesquisado
                        );
                        //endregion
                        //region Complemento
                        String complementoDoCEPPesquisado = this.getValorDoCampoReferenteAoCEPPesquisado(
                                frmPesquisaCEPOnlineA.lblValorComplemento.getCaption().trim().toUpperCase()
                                ,this.edtComplemento3
                        );
                        this.edtComplemento3.setValue(
                                complementoDoCEPPesquisado
                        );
                        //endregion
                    }
                });
        /*
        if (edtCEP3.getValue().asString().trim().length() < 8) {
            lblTempoConsultaCEP.setVisible(false);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            lblDicaAPIViaCEP.setCaption(CEP_INVALIDO);
            hBoxDicaAPIViaCEP.setVisible(true);
            return;
        }
        if (edtCEP3.getValue().asString().trim().length() > 8) {
            lblTempoConsultaCEP.setVisible(false);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            lblDicaAPIViaCEP.setCaption(CEP_INVALIDO);
            hBoxDicaAPIViaCEP.setVisible(true);
            return;
        }
        lblTempoConsultaCEP.setVisible(false);
        lblDicaAPIViaCEP.setVisible(false);
        lblDicaAPIViaCEP02.setVisible(false);
        hBoxDicaAPIViaCEP.setVisible(false);
        Value xMensagem = new Value(null);
        boolean tbCidades3NotEmpty = !this.tbCidades3.isEmpty();
        if (!edtCidade3.getValue().isNull()
                && tbCidades3NotEmpty
                && (tbCidades3.getCEP_POR_LOG_NVL().asString().trim().equals("N"))) {
            lblDicaAPIViaCEP.setCaption(CIDADE_NAO_USA_CEP_POR_RUA);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            hBoxDicaAPIViaCEP.setVisible(true);
            return;
        }
        if (!edtCEP3.getValue().asString().isEmpty()) {
            lblTempoConsultaCEP.setVisible(false);
            try {
                startTime = 0;
                endTime = 0;
                if (servicoOk(edtCEP3.getValue().asString().trim(), false, xMensagem)) {
                    getCEPOnLineService(3);
                } else {
                    if (endTime == 0) {
                        endTime = System.currentTimeMillis();
                    }
                    if (!xMensagem.isNull()) {
                        xAchouDivergencias = false;
                        if (xMensagem.asString().trim().equals(DICA_SERVIDOR_DE_APLICACAO_CRM_SERVICE_NAO_CONSEGUIU_CONECTAR_SE_A_API_HTTPS_VIACEP_COM_BR_VERIFIQUE_COM_SEU_DEPARTAMENTO_DE_TI)) {
                            lblDicaAPIViaCEP.setVisible(true);
                            lblDicaAPIViaCEP02.setVisible(true);
                        } else {
                            lblDicaAPIViaCEP.setVisible(true);
                            lblDicaAPIViaCEP02.setVisible(false);
                            lblDicaAPIViaCEP.setCaption(xMensagem.asString().trim());
                        }
                        hBoxDicaAPIViaCEP.setVisible(true);
                    }
                }
            } catch (Exception ex) {
                if (endTime == 0) {
                    endTime = System.currentTimeMillis();
                }
                lblDicaAPIViaCEP.setVisible(true);
                hBoxDicaAPIViaCEP.setVisible(true);
                EmpresaUtil.showError(FALHA_AO_CONSULTAR_CEP_MOTIVO, ex);
            }
            boolean hBoxDicaAPIViaCEPNotVisible = !this.hBoxDicaAPIViaCEP.isVisible();
            if (hBoxDicaAPIViaCEPNotVisible) {
                hBoxDicaAPIViaCEP.setVisible(true);
            }
            if (xAchouDivergencias) {
                lblTempoConsultaCEP.setCaption(CONSULTA_E_RETORNO_ON_LINE_DO_CEP_REALIZADA_EM + (endTime - startTime) + "ms");
            } else {
                lblTempoConsultaCEP.setCaption(CONSULTA_ON_LINE_DO_CEP_REALIZADA_EM + (endTime - startTime) + "ms");
            }
        }
        if (!(!xAchouDivergencias
                && lblDicaAPIViaCEP.isVisible()
                && !lblDicaAPIViaCEP02.isVisible())) {
            lblSemDivergencias.setVisible(!xAchouDivergencias && !lblDicaAPIViaCEP.isVisible() && !lblDicaAPIViaCEP02.isVisible());
        }
        */
    }

    @NotNull
    private String getValorDoCampoReferenteAoCEPPesquisado(
            String lblValorCEP
            ,TFString edtCEP
    ) {
        String cepPesquisado = lblValorCEP;
        if (cepPesquisado.length() > edtCEP.getMaxlength()) {
            cepPesquisado = cepPesquisado.substring(
                    0
                    , edtCEP.getMaxlength()
            );
        }
        return cepPesquisado;
    }

    @Override
    public void edtEmailExit(Event<Object> event) {

        if ((!edtEmail.getValue().isEmpty()) && (edtEmailNFE.getValue().isEmpty())) {
            edtEmailNFE.setValue(edtEmail.getValue().asString());
        }
    }

    @Override
    public void edtEmailNFEExit(Event<Object> event) {
        if ((!edtEmailNFE.getValue().isEmpty()) && (edtEmail.getValue().isEmpty())) {
            edtEmail.setValue(edtEmailNFE.getValue().asString());
        }
    }

    @Override
    public void edtDddCelFocus(Event<Object> event) {
        if (tbClientes.getPREFIXO_CEL().asInteger() == 0) {
            edtDddCel.clear();
        }
    }

    @Override
    public void edtTelefoneCelFocus(Event<Object> event) {
        if (tbClientes.getTELEFONE_CEL().asInteger() == 0) {
            edtTelefoneCel.clear();
        }
    }

    @Override
    public void edtDddComFocus(Event<Object> event) {
        if (tbClientes.getPREFIXO_COM().asInteger() == 0) {
            edtDddCom.clear();
        }
    }

    @Override
    public void edtTelefoneComFocus(Event<Object> event) {
        if (tbClientes.getTELEFONE_COM().asInteger() == 0) {
            edtTelefoneCom.clear();
        }
    }

    @Override
    public void edtDDDTelResFocus(Event<Object> event) {
        if (tbClientes.getPREFIXO_RES().asInteger() == 0) {
            edtDDDTelRes.clear();
        }
    }

    @Override
    public void edtTelResFocus(Event<Object> event) {
        if (tbClientes.getTELEFONE_RES().asInteger() == 0) {
            edtTelRes.clear();
        }
    }

    @Override
    public void edtDDDTelCobFocus(Event<Object> event) {
        if (tbClientes.getPREFIXO_FAX().asInteger() == 0) {
            edtDDDTelCob.clear();
        }
    }

    @Override
    public void edtTelCobFocus(Event<Object> event) {
        if (tbClientes.getTELEFONE_FAX().asInteger() == 0) {
            edtTelCob.clear();
        }
    }

    @Override
    public void vBoxCelularLimparClick(final Event<Object> event) {
        icoApagarCelularClick(event);
    }

    @Override
    public void vBocCobrancaLimparClick(final Event<Object> event) {
        icoApagarTelComClick(event);
    }

    @Override
    public void FVBox14Click(final Event<Object> event) {
        icoApagarTelCobClick(event);
    }

    @Override
    public void FVBox12Click(final Event<Object> event) {
        icoApagarTelResClick(event);
    }

    @Override
    public void btnPesquisaProfissaoClick(final Event<Object> event) {
        boolean tbClientesProfissaoInclusaoEmpty = this.tbClientesProfissaoInclusao.isEmpty();
        if (tbClientesProfissaoInclusaoEmpty) {
            return;
        }
        FrmClientesProfissaoA frmClientesProfissaoA = new FrmClientesProfissaoA();
        String prof = cboProfissao.getText().trim();
        if (!prof.isEmpty()) {
            frmClientesProfissaoA.edtDescricaoProfissao.setValue(prof);
            frmClientesProfissaoA.btnPesquisarClick(event);
        }
        FormUtil.doModal(frmClientesProfissaoA,
                t -> {
                    if (frmClientesProfissaoA.isOk()
                            && (tbClientesProfissaoInclusao.locate("COD_PROFISSAO",
                            frmClientesProfissaoA.tbClientesProfissao.getCOD_PROFISSAO().asInteger()))) {
                        cboProfissao.setText(frmClientesProfissaoA.tbClientesProfissao.getDESCRICAO().asString());
                    }
                });
    }

    @Override
    public void icoClassHelpClick(final Event<Object> event) {
        FormUtil.redirect("http://ajuda.nbsi.com.br:84/index.php/Cadastro_R%C3%A1pido", true);
    }

    @Override
    public void vBoxLblFacebookClick(final Event<Object> event) {
        if (!edtFacebook.getValue().asString().isEmpty()) {
            FormUtil.redirect(edtFacebook.getValue().asString().trim(), true);
        }
    }

    @Override
    public void vBoxLblTwitterClick(final Event<Object> event) {
        if (!edtTwitter.getValue().asString().isEmpty()) {
            FormUtil.redirect(edtTwitter.getValue().asString().trim(), true);
        }
    }

    @Override
    public void vBoxCliRapidoLogoFacebookClick(final Event<Object> event) {
        if (!edtFacebook.getValue().asString().isEmpty()) {
            FormUtil.redirect(edtFacebook.getValue().asString().trim(), true);
        }
    }

    @Override
    public void vBoxCliRapidoLogoTwitterClick(final Event<Object> event) {
        if (!edtTwitter.getValue().asString().isEmpty()) {
            FormUtil.redirect(edtTwitter.getValue().asString().trim(), true);
        }
    }

    @Override
    public void vBoxLblSiteClick(final Event<Object> event) {
        if (!edtSite.getValue().asString().isEmpty()) {
            FormUtil.redirect(edtSite.getValue().asString().trim(), true);
        }
    }

    @Override
    public void vBoxCliRapidoLogoSiteClick(final Event<Object> event) {
        if (!edtSite.getValue().asString().isEmpty()) {
            FormUtil.redirect(edtSite.getValue().asString().trim(), true);
        }
    }

    @Override
    public void vBoxApagarNomeFantasiaClick(final Event<Object> event) {
        edtNomeFantasia.clear();
        edtNomeFantasia.setFocus();
    }

    private void exibirContatosDoCliente() {
        String vCnpj = this.edtCNPJ.getValue().asString().replaceAll(
                "\\D"
                ,""
        );
        String vCpf = this.edtCPF.getValue().asString().replaceAll(
                "\\D"
                ,""
        );
        boolean ehProdutorRural = false;
        if (!vCpf.isEmpty()) {
            ehProdutorRural = this.rn.isProdutorRural(
                    vCpf
            );
            this.clienteCpf = Double.parseDouble(
                    vCpf
            );
        }
        if ((StringUtils.isBlank(vCnpj))
                && (!ehProdutorRural)) {
            EmpresaUtil.showInformationMessage(
                    "Precisa ser cliente Pessoa Jurídica ou Pessoa Fisíca Produtor Rural!"
            );
            return;
        }
        if (!vCnpj.isEmpty()) {
            this.clienteCnpj = Double.parseDouble(vCnpj);
        }
        FrmCadClienteContatoA frmCadClienteContato = new FrmCadClienteContatoA();
        if (this.clienteCpf > 0.0) {
            frmCadClienteContato.setCodCliente(
                    (long) this.clienteCpf
            );
        }
        if (this.clienteCnpj > 0.0) {
            frmCadClienteContato.setCodCliente(
                    (long) this.clienteCnpj
            );
        }
        frmCadClienteContato.pesquisar();
        FormUtil.doShow(frmCadClienteContato,
                t -> {
                    if (this.clienteCpf > 0.0) {
                        boolean frmCadClienteContatoFechadoAoVoltar = frmCadClienteContato.isFechadoAoVoltar();
                        if (frmCadClienteContatoFechadoAoVoltar) {
                            this.rn.abrirRapCliContato(this.clienteCpf);
                        }
                    }
                    if (this.clienteCnpj > 0.0) {
                        boolean frmCadClienteContatoFechadoAoVoltar = frmCadClienteContato.isFechadoAoVoltar();
                        if (frmCadClienteContatoFechadoAoVoltar) {
                            this.rn.abrirRapCliContato(this.clienteCnpj);
                        }
                    }

                });
    }

    @Override
    public void btnContatoClick(final Event<Object> event) {
        this.exibirContatosDoCliente();
    }

    @Override
    public void edtCidade1Change(Event<Object> event) {
        lblDicaAPIViaCEP.setCaption("");
        lblDicaAPIViaCEP.setVisible(false);
        lblDicaAPIViaCEP02.setVisible(false);
        hBoxDicaAPIViaCEP.setVisible(false);
        boolean tbCidades1NotEmpty = !tbCidades1.isEmpty();
        if (!edtCidade1.getValue().isNull()
                && tbCidades1NotEmpty
                && (tbCidades1.getCEP_POR_LOG_NVL().asString().trim().equals("N"))) {
            lblDicaAPIViaCEP.setCaption(CIDADE_NAO_USA_CEP_POR_RUA);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            hBoxDicaAPIViaCEP.setVisible(true);
        }
    }

    @Override
    public void edtCidade1ClearClick(Event<Object> event) {
        lblDicaAPIViaCEP.setCaption("");
        lblDicaAPIViaCEP.setVisible(false);
        lblDicaAPIViaCEP02.setVisible(false);
        hBoxDicaAPIViaCEP.setVisible(false);
    }

    @Override
    public void edtCidade2Change(Event<Object> event) {
        lblDicaAPIViaCEP.setCaption("");
        lblDicaAPIViaCEP.setVisible(false);
        lblDicaAPIViaCEP02.setVisible(false);
        hBoxDicaAPIViaCEP.setVisible(false);
        boolean tbCidades2NotEmpty = !this.tbCidades2.isEmpty();
        if (!edtCidade2.getValue().isNull()
                && tbCidades2NotEmpty
                && (tbCidades2.getCEP_POR_LOG_NVL().asString().trim().equals("N"))) {
            lblDicaAPIViaCEP.setCaption(CIDADE_NAO_USA_CEP_POR_RUA);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            hBoxDicaAPIViaCEP.setVisible(true);
        }
    }

    @Override
    public void edtCidade2ClearClick(Event<Object> event) {
        this.edtCidade1ClearClick(event);
    }

    @Override
    public void edtCidade3Change(Event<Object> event) {
        lblDicaAPIViaCEP.setCaption("");
        lblDicaAPIViaCEP.setVisible(false);
        lblDicaAPIViaCEP02.setVisible(false);
        hBoxDicaAPIViaCEP.setVisible(false);
        boolean tbCidades3NotEmpty = !tbCidades3.isEmpty();
        if (!edtCidade3.getValue().isNull()
                && tbCidades3NotEmpty
                && (tbCidades3.getCEP_POR_LOG_NVL().asString().trim().equals("N"))) {
            lblDicaAPIViaCEP.setCaption(CIDADE_NAO_USA_CEP_POR_RUA);
            lblDicaAPIViaCEP.setVisible(true);
            lblDicaAPIViaCEP02.setVisible(false);
            hBoxDicaAPIViaCEP.setVisible(true);
        }
    }

    @Override
    public void edtCidade3ClearClick(Event<Object> event) {
        this.edtCidade1ClearClick(event);
    }

    @Override
    public void cboCrmEmailChange(Event<Object> event) {
        // Implementado em FrmCadastroRapidoClienteU
    }

    @Override
    public void cboCrmSMSChange(Event<Object> event) {
        // Implementado em FrmCadastroRapidoClienteU
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        FRLogger.log(
                ("Formulário: " + this.getName())
                ,this.getClass()
        );
        if (this.codCliente <= 0.00) {
            FreedomUtilities.invokeLater(
                    this::salvar
            );
        }
        this.validaUrlConsultaNbs();
        this.btnFlags.setVisible(
                this.codCliente > 0.00
        );
        //this.vBoxSepFlags.setVisible(this.btnFlags.isVisible());
        this.btnDadosFinanceiros.setVisible(this.codCliente > 0.00);
        //this.vBoxSepComFinanceiro.setVisible(this.btnDadosFinanceiros.isVisible());
        this.ehRodobens = this.ehRodobensParam();
        this.vBoxCodigoBP.setVisible(
                this.ehRodobens
        );
        this.btnLogs.setVisible(
                this.ehRodobens
                && (this.codCliente > 0.00)
        );
        this.btnCreditoCorporativo.setVisible(
                this.ehRodobens
                && (this.codCliente > 0.00)
        );
        this.btnHistoricoFichaCliente.setVisible(
                this.codCliente > 0.00
        );
        this.btnImpressaoConsentimentoLGPD.setVisible(
                this.codCliente > 0.00
        );
        if (this.codCliente > 0.00) {
            this.setIconAbrirAssinaturaDigital();
            boolean podeAlterarCadastro = this.rn.podeAlterarCadastro(
                    this.ehCRMParts
                    ,this.usuarioLogado
            );
            if (!podeAlterarCadastro) {
                this.btnSalvar.setEnabled(false);
                this.btnSalvar.setHint("Acesso \"K0234\"");
                this.edtNomeCliente.setEnabled(false);
                this.icoApagarNome.setVisible(false);
                this.edtTelefoneCel.setEnabled(false);
                this.edtDddCel.setEnabled(false);
                this.edtNumeroCNO.setEnabled(false);
                this.icoApagarCelular.setVisible(false);
                this.edtDDDTelRes.setEnabled(false);
                this.edtTelRes.setEnabled(false);
                this.icoApagarTelRes.setVisible(false);
                this.edtDddCom.setEnabled(false);
                this.edtTelefoneCom.setEnabled(false);
                this.icoApagarTelCom.setVisible(false);
                this.edtEmail.setEnabled(false);
                this.edtEmailNFE.setEnabled(false);
                this.icoApagarEmail.setVisible(false);
                this.icoApagarEmailNFe.setVisible(false);
                this.edtCNAE.setEnabled(false);
                this.cboRegimeEspecialTrib.setEnabled(false);
                this.edtCodSubTribISS.setEnabled(false);
                this.edtDataFundacao.setEnabled(false);
                this.edtNomeFantasia.setEnabled(false);
                this.icoApagarNomeFantasia.setVisible(false);
                this.cboConcessionaria.setEnabled(false);
                this.cboDistribuicao.setEnabled(false);
                this.cboRamo.setEnabled(false);
                this.edtNroSuframa.setEnabled(false);
                this.edtINSC_MUNICIPAL.setEnabled(false);
                this.edtINSC_ESTADUAL.setEnabled(false);
                this.cboEstadoCivil.setEnabled(false);
                this.cboSexo.setEnabled(false);
                this.edtEmissorRG.setEnabled(false);
                this.dtEmissaoRG.setEnabled(false);
                this.edtRG.setEnabled(false);
                this.cboEscolaridade.setEnabled(false);
                this.btnPesquisaProfissao.setVisible(false);
                this.cboProfissao.setEnabled(false);
                this.cboNacionalidade.setEnabled(false);
                this.dtNasc.setEnabled(false);
                this.edtSite.setEnabled(false);
                this.edtTwitter.setEnabled(false);
                this.edtFacebook.setEnabled(false);
                this.timeCrmMelhorHorario.setEnabled(false);
                this.cboPoliticamenteExposto.setEnabled(false);
                this.cboServidorPublico.setEnabled(false);
                this.cboCartaoDotz.setEnabled(false);
                this.cboCrmSMS.setEnabled(false);
                this.cboCrmFone.setEnabled(false);
                this.cboTeleContato.setEnabled(false);
                this.cboCrmMalaDireta.setEnabled(false);
                this.cboCrmEmail.setEnabled(false);
                this.btnIncluir.setVisible(false);
                this.edTpEnd1.setEnabled(false);
                this.edtNumero1.setEnabled(false);
                this.edtComplemento1.setEnabled(false);
                this.edtBairro1.setEnabled(false);
                this.edtRua1.setEnabled(false);
                this.edtCidade1.setEnabled(false);
                this.edUF1.setEnabled(false);
                this.edtCEP1.setEnabled(false);
                this.edTpEnd2.setEnabled(false);
                this.edtNumero2.setEnabled(false);
                this.edtComplemento2.setEnabled(false);
                this.edtBairro2.setEnabled(false);
                this.edtRua2.setEnabled(false);
                this.edtCidade2.setEnabled(false);
                this.edUF2.setEnabled(false);
                this.edtCEP2.setEnabled(false);
                this.edTpEnd3.setEnabled(false);
                this.edtNumero3.setEnabled(false);
                this.edtComplemento3.setEnabled(false);
                this.edtBairro3.setEnabled(false);
                this.edtRua3.setEnabled(false);
                this.edtCidade3.setEnabled(false);
                this.edUF3.setEnabled(false);
                this.edtCEP3.setEnabled(false);
                this.btnPesquisaCep1.setEnabled(false);
                this.btnPesquisaCep2.setEnabled(false);
                this.btnPesquisaCep3.setEnabled(false);
                this.btnColarEnd1.setEnabled(false);
                this.btnCopiarEnd1.setEnabled(false);
                this.btnExcluirEnd1.setEnabled(false);
                this.btnColarEnd2.setEnabled(false);
                this.btnCopiarEnd2.setEnabled(false);
                this.btnExcluirEnd2.setEnabled(false);
                this.btnColarEnd3.setEnabled(false);
                this.btnCopiarEnd3.setEnabled(false);
                this.btnExcluirEnd3.setEnabled(false);
            } else if (!this.rn.podeAlterarNomeRazSocCadastro(this.ehCRMParts,
                    this.usuarioLogado)) {
                this.edtNomeCliente.setEnabled(false);
                this.icoApagarNome.setVisible(false);
                this.edtNomeCliente.setHint("Não pode alterar Razão Social/Nome do fornecedor/Cliente - Acesso: K0282");
            }
        }
        this.hBoxSepAjudaNofinal.setVisible(true);
        this.hBoxAjuda.setVisible(true);
        this.carregarSchemaAtual();
        this.carregarCboMidia();
        TestUtil.setIdTestComponents(
                this
        );
    }

    private void carregarSchemaAtual() {
        try {
            this.schemaAtual = (this.rn.getSchemaAtual()).toUpperCase();
        } catch (DataException dataException) {
            EmpresaUtil.showInformationMessage("Não carregou o schemma do usuário."
                    + System.lineSeparator()
                    + "Motivo: "
                    + dataException.getMessage());
        }
    }

    public void validaUrlConsultaNbs() {
        this.usaApiConsulta = usaPluginConsultaNBS();
        this.btnConsultaIntegracaoCnpj.setVisible((this.usaApiConsulta) && (codCliente <= 0.0)); // Pode exibir botãoo ApiConsulta ao Cadastrar Novo
        this.btnConsultaIntegracaoCpf.setVisible((this.usaApiConsulta) && (codCliente <= 0.0)); // Pode exibir botão  ApiConsulta ao Cadastrar Novo
        this.btnExibirDadosConsultaAPIIntegracao.setVisible((this.usaApiConsulta) && (codCliente > 0.0)); // Pode exibir botão integração API
        //this.vBoxSepSalvarToConsulta.setVisible(btnExibirDadosConsultaAPIIntegracao.isVisible());
//        if (this.ehCRMParts) {
            this.btnEnderecosPorInscricao.setVisible(this.codCliente > 0.0);
//        } else {
//            this.btnEnderecosPorInscricao.setVisible(false);
//        }
        //this.vBoxSepEnderecosIE.setVisible(this.btnEnderecosPorInscricao.isVisible());
        this.exibirStatusCadastro();
    }

    private boolean usaPluginConsultaNBS() {
        return rn.usaPluginConsultaNBS((double) this.codEmpresaUsuarioLogado);
    }

    private boolean ehRodobensParam() {
        return rn.ehRodobensParam((double) this.codEmpresaUsuarioLogado);
    }

    @Override
    public void btnCopiarEnd1Click(final Event<Object> event) {
        copiarDadosEnderecos(1);
    }

    @Override
    public void btnCopiarEnd2Click(final Event<Object> event) {
        copiarDadosEnderecos(2);
    }

    @Override
    public void btnCopiarEnd3Click(final Event<Object> event) {
        copiarDadosEnderecos(3);
    }

    public void copiarDadosEnderecos(int acao) {
        this.limparVariaveis();
        if (acao == 1) {
            this.tipoEnderecoCopy = edTpEnd1.getValue().asString();
            this.ufCopy = edUF1.getValue().asString();
            this.cidadeCopy = edtCidade1.getValue().asInteger();
            this.cepCopy = edtCEP1.getValue().asString();
            this.ruaCopy = edtRua1.getValue().asString();
            this.bairroCopy = edtBairro1.getValue().asString();
            this.complementoCopy = edtComplemento1.getValue().asString();
            this.numeroCopy = edtNumero1.getValue().asString();
            btnCopiarEnd1.setCaption(COPIADO);
        }
        if (acao == 2) {
            this.tipoEnderecoCopy = edTpEnd2.getValue().asString();
            this.ufCopy = edUF2.getValue().asString();
            this.cidadeCopy = edtCidade2.getValue().asInteger();
            this.cepCopy = edtCEP2.getValue().asString();
            this.ruaCopy = edtRua2.getValue().asString();
            this.bairroCopy = edtBairro2.getValue().asString();
            this.complementoCopy = edtComplemento2.getValue().asString();
            this.numeroCopy = edtNumero2.getValue().asString();
            btnCopiarEnd2.setCaption(COPIADO);
        }
        if (acao == 3) {
            this.tipoEnderecoCopy = edTpEnd3.getValue().asString();
            this.ufCopy = edUF3.getValue().asString();
            this.cidadeCopy = edtCidade3.getValue().asInteger();
            this.cepCopy = edtCEP3.getValue().asString();
            this.ruaCopy = edtRua3.getValue().asString();
            this.bairroCopy = edtBairro3.getValue().asString();
            this.complementoCopy = edtComplemento3.getValue().asString();
            this.numeroCopy = edtNumero3.getValue().asString();
            btnCopiarEnd3.setCaption(COPIADO);
        }
        if (this.validarDadosEndereco()) {
            EmpresaUtil.showInformationMessage(NENHUM_CAMPO_DE_ENDERECO_FOI_PREENCHIDO_VERIFIQUE);
            btnCopiarEnd1.setCaption(COPIAR);
            btnCopiarEnd2.setCaption(COPIAR);
            btnCopiarEnd3.setCaption(COPIAR);
            return;
        }
        btnColarEnd1.setEnabled(true);
        btnColarEnd2.setEnabled(true);
        btnColarEnd3.setEnabled(true);
    }

    @Override
    public void btnColarEnd1Click(final Event<Object> event) {
        if (this.validarDadosEndereco()) {
            EmpresaUtil.showInformationMessage(NENHUM_CAMPO_DE_ENDERECO_FOI_COPIADO_VERIFIQUE);
            return;
        }
        edUF1.setValue(ufCopy);
        edtCidade1.setValue(cidadeCopy);
        edtCEP1.setValue(cepCopy);
        edtRua1.setValue(ruaCopy);
        edtBairro1.setValue(bairroCopy);
        edtComplemento1.setValue(complementoCopy);
        edtNumero1.setValue(numeroCopy);
        btnColarEnd1.setEnabled(false);
        btnColarEnd2.setEnabled(false);
        btnColarEnd3.setEnabled(false);
        btnCopiarEnd1.setCaption(COPIAR);
        btnCopiarEnd2.setCaption(COPIAR);
        btnCopiarEnd3.setCaption(COPIAR);
    }

    @Override
    public void btnColarEnd2Click(final Event<Object> event) {
        if (this.validarDadosEndereco()) {
            EmpresaUtil.showInformationMessage(NENHUM_CAMPO_DE_ENDERECO_FOI_COPIADO_VERIFIQUE);
            return;
        }
        edUF2.setValue(ufCopy);
        edtCidade2.setValue(cidadeCopy);
        edtCEP2.setValue(cepCopy);
        edtRua2.setValue(ruaCopy);
        edtBairro2.setValue(bairroCopy);
        edtComplemento2.setValue(complementoCopy);
        edtNumero2.setValue(numeroCopy);
        btnColarEnd1.setEnabled(false);
        btnColarEnd2.setEnabled(false);
        btnColarEnd3.setEnabled(false);
        btnCopiarEnd1.setCaption(COPIAR);
        btnCopiarEnd2.setCaption(COPIAR);
        btnCopiarEnd3.setCaption(COPIAR);
    }

    @Override
    public void btnColarEnd3Click(final Event<Object> event) {
        if (this.validarDadosEndereco()) {
            EmpresaUtil.showInformationMessage(NENHUM_CAMPO_DE_ENDERECO_FOI_COPIADO_VERIFIQUE);
            return;
        }
        edUF3.setValue(ufCopy);
        edtCidade3.setValue(cidadeCopy);
        edtCEP3.setValue(cepCopy);
        edtRua3.setValue(ruaCopy);
        edtBairro3.setValue(bairroCopy);
        edtComplemento3.setValue(complementoCopy);
        edtNumero3.setValue(numeroCopy);
        btnColarEnd1.setEnabled(false);
        btnColarEnd2.setEnabled(false);
        btnColarEnd3.setEnabled(false);
        btnCopiarEnd1.setCaption(COPIAR);
        btnCopiarEnd2.setCaption(COPIAR);
        btnCopiarEnd3.setCaption(COPIAR);
    }

    public void limparVariaveis() {
        tipoEnderecoCopy = null;
        ufCopy = null;
        cidadeCopy = null;
        cepCopy = null;
        ruaCopy = null;
        bairroCopy = null;
        complementoCopy = null;
        numeroCopy = null;
    }

    public boolean validarDadosEndereco() {
        boolean validaDadosEnd = false;
        StringBuilder temDadosEndereco = new StringBuilder();
        Object[] campos = {tipoEnderecoCopy, ufCopy, cidadeCopy, cepCopy, ruaCopy, bairroCopy, complementoCopy, numeroCopy};
        for (Object campo : campos) {
            if (campo == null) {
                temDadosEndereco = new StringBuilder();
            } else if (!campo.equals("") && !campo.equals(0)) {
                temDadosEndereco.append(campo);
            }
        }
        if (temDadosEndereco.toString().isEmpty()) {
            validaDadosEnd = true;
        }
        return validaDadosEnd;
    }

    public static String removerAcentos(String str) {
        return XmlUtil.removeAcentos(str);
    }

    private void exibirFrmResultCreditoCorporativoA() {
        FrmResultCreditoCorporativoA frmResultCreditoCorporativoA = new FrmResultCreditoCorporativoA(this.codCliente);
        FormUtil.doShow(frmResultCreditoCorporativoA,
                t2 -> {

                });
    }

    @Override
    public void btnDadosFinanceirosClick(final Event<Object> event) {
        if (codCliente <= 0) {
            return;
        }
        if (rn.usaCreditoCorporativo(this.codEmpresaUsuarioLogado)) {
            FrmIntegracaoSapA sap = new FrmIntegracaoSapA();
            String respFunc = sap.gerarFilaConsultaOrcamCreditoCorporativo(codCliente,
                    this.codEmpresaUsuarioLogado,
                    0.0,
                    "N",
                    "N",
                    "N",
                    this.usuarioLogado);
            if (respFunc.equals("S")) {
                FormUtil.doShow(sap, t -> {
                    sap.timerSap.setEnabled(false);
                    String msgSap = sap.getMensagemSap();

                    if (sap.detalharRetornoCC()) {
                        this.exibirFrmResultCreditoCorporativoA();
                    } else if (StringUtils.isNotBlank(msgSap)) {
                        Dialog.create()
                                .title("Informação - Crédito Corporativo")
                                .message(msgSap)
                                .showInformation();
                    } else {
                        this.exibirFrmResultCreditoCorporativoA();
                    }
                });
                Window w = (Window) ((Vlayout) sap.getImpl()).getParent();
                w.setClosable(false);
                w.setMaximizable(false);
            } else {
                EmpresaUtil.showInformationMessage("Não foi possível gerar fila de consulta crédito corporativo. " + respFunc);
            }
        } else if (this.ehCRMParts) {
            FrmLimiteFinanceiroA frmLimiteFinanceiroA = new FrmLimiteFinanceiroA();
            frmLimiteFinanceiroA.openLimiteCliente(this.codCliente);
            FormUtil.doShow(frmLimiteFinanceiroA,
                    t -> {

                    });
        } else {
            try {
                if (!EmpresaUtil.validarAcesso("B2203")) {
                    return;
                }
                FrmDadosFinanceiroA frmDadosFinanceiroA = new FrmDadosFinanceiroA();
                String isOk = frmDadosFinanceiroA.carregarDadosFinanceiro(this.codCliente);
                if (!isOk.isEmpty()) {
                    EmpresaUtil.showWarning("Dados Financeiro",
                            isOk);
                    return;
                }
                FormUtil.doShow(frmDadosFinanceiroA,
                        t -> {

                        });
            } catch (DataException dataException) {
                EmpresaUtil.showError("Erro ao carregar",
                        dataException);
            }
        }
    }

    @Override
    public void btnConsultaIntegracaoCpfClick(final Event<Object> event) {
        String urlConsultaNbs = rn.getUrlConsultaNbs((double) this.codEmpresaUsuarioLogado);
        if (StringUtils.isBlank(urlConsultaNbs)) {
            EmpresaUtil.showInformationMessage("É obrigatório configurar a URL de consulta plugin-consulta-nbs em  [CRMParts] Tabelas > Parâmetros > Clientes.");
            return;
        }
        SimpleDateFormat str = new SimpleDateFormat("dd/MM/yyyy");
        if (cboTipo.getValue().asString().equals("F")) {
            if (edtCPF.getValue().isEmpty()) {
                Value campoFoco = new Value(null);
                Value mensagemFoco = new Value(null);
                campoFoco.setValue(DADOS_FISICOS_CPF);
                mensagemFoco.setValue("CPF não informado!");
                FreedomUtilities.invokeLater(() -> setFocusCampos(campoFoco, mensagemFoco));
                return;
            }
            boolean edtCPFEnabled = this.edtCPF.isEnabled();
            if ((!edtCPF.getValue().isEmpty())
                    && edtCPFEnabled
                    && (!ValidarUtil.isValidCPF(edtCPF.getValue().asString()))) {
                Value campoFoco = new Value(null);
                Value mensagemFoco = new Value(null);
                campoFoco.setValue(DADOS_FISICOS_CPF);
                mensagemFoco.setValue("CPF informado para consulta é inválido");
                FreedomUtilities.invokeLater(() -> setFocusCampos(campoFoco, mensagemFoco));
                return;
            }
            lblValidarCPF.setVisible(false);
            String documento = edtCPF.getValue().asString();
            double codCliente1 = Double.parseDouble(documento);
            if (existeCliente(codCliente1)) {
                EmpresaUtil.showInformationMessage("Cliente já Cadastrado!");
                return;
            }
            FrmCadastroRapidoClienteProdRuralA frm = new FrmCadastroRapidoClienteProdRuralA();
            frm.setCpfCnpj(edtCPF.getValue().asString());
            if (StringUtils.isNotBlank(tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString())) {
                frm.setUFInscricao(tbClienteEnderecoInscricao.getUF().asString(), tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString());
            } else {
                frm.setUFInscricao(tbParmSys.getUF().asString());
            }
            frm.setDataNascimento(this.dtNasc.getValue().asDate());
            FormUtil.doShow(frm, t -> {
                if (frm.isOk()) {
                    String nascimento;
                    this.dtNasc.setValue(frm.edtDataNasc.getValue().asDate());
                    String uf = frm.getUfInscricao();
                    String inscricao = frm.getInscricao();
                    if (!this.dtNasc.getValue().isNull()) {
                        nascimento = str.format(this.dtNasc.getValue().asDate());
                    } else {
                        nascimento = "";
                    }
                    if (ValidarUtil.validarDataNascimento(nascimento,
                            FrmCadastroRapidoClienteA.DATA_INVALIDA_BEFORE)) {
                        EmpresaUtil.showInformationMessage("Data de nascimento informada [" + nascimento + "] é Inválida para consulta na Receita Federal. Verifique!");
                        return;
                    }
                    String retFuncao = this.consultarDadosPessoaFisicaClienteNovo(urlConsultaNbs, documento, nascimento, uf, inscricao);
                    if (retFuncao.equals("N")) {
                        return;
                    } else if (!retFuncao.equals("S")) {
                        EmpresaUtil.showInformationMessage("Consulta de dados retornou - " + retFuncao);
                        return;
                    }
                    this.codClienteConsultadoApi = Double.parseDouble(documento);
                    salvar();
                }
            });
        }
    }

    private String consultarDadosPessoaFisicaClienteNovo(String urlConsultaNbs, String documento, String nascimento, String uf, String inscricao) {
        String retFuncao;
        if (!this.buscarDadosClientePf(documento, inscricao)) {
            StringBuilder url = new StringBuilder();
            url.append(urlConsultaNbs);
            url.append("/plugin-nbs-consulta/api/find");
            url.append("?empresa=").append(this.codEmpresaUsuarioLogado);
            url.append("&usuario=").append(this.usuarioLogado.toLowerCase());
            url.append("&sistema=").append(this.wl.sysget(APPLICATION_NAME).asString());
            url.append("&doc=").append(documento);
            if (StringUtils.isNotBlank(nascimento)) {
                url.append("&nascimento=").append(nascimento);
            }
            if (StringUtils.isNotBlank(inscricao)) {
                url.append("&uf=").append(uf);
                url.append("&ie=").append(inscricao);
            }

            IntegracaoPluginNbsConsulta plugin = new IntegracaoPluginNbsConsulta();
            try {
                Double idCns = plugin.consultaCadastro(urlConsultaNbs, url.toString(), this.usuarioLogado, this.schemaAtual);

                retFuncao = rn.validarRetornoApiConsulta(idCns);
                if (!retFuncao.equals("S")) {
                    return rn.validarMensagemConsultaIntegracao((double) this.codEmpresaUsuarioLogado
                            , Double.parseDouble(documento.replaceAll("\\D", ""))
                            , inscricao, idCns, retFuncao);
                }
                this.rn.openConsultaDadosRetornoApi(idCns, "F");
            } catch (Exception e) {
                /*
                 * Verificar se informada inscrição e não retornou dados. O que é pra fazer.
                 */
                return rn.validarMensagemConsultaIntegracao((double) this.codEmpresaUsuarioLogado, Double.parseDouble(documento.replaceAll("\\D", "")), inscricao, 0.0, e.getMessage());
            }
        }
        retFuncao = preencherDadosPf();
        btnAlterarEnderecoInscricao.setVisible(!tbClienteEnderecoInscricao.isEmpty());
        return retFuncao;
    }

    public String consultarPessoaJuridica(String urlConsultaNbs,
                                          String documento,
                                          String inscricao) {
        String retFuncao = null;
        StringBuilder url = new StringBuilder();
        url.append(urlConsultaNbs);
        url.append("/plugin-nbs-consulta/api/find");
        url.append("?empresa=").append(this.codEmpresaUsuarioLogado);
        url.append("&usuario=").append(this.usuarioLogado.toLowerCase());
        url.append("&sistema=").append(this.wl.sysget(APPLICATION_NAME).asString());
        url.append("&doc=").append(documento);
        if (StringUtils.isNotBlank(inscricao)) {
            url.append("&ie=").append(inscricao);
        }
        IntegracaoPluginNbsConsulta plugin = new IntegracaoPluginNbsConsulta();
        try {
            Double idCns = plugin.consultaCadastro(urlConsultaNbs,
                    url.toString(),
                    this.usuarioLogado,
                    this.schemaAtual);
            //Validar Retorno Api Consulta
            retFuncao = this.rn.validarRetornoApiConsulta(idCns);
            if (!retFuncao.equals("S")) {
                return this.rn.validarMensagemConsultaIntegracao((double) this.codEmpresaUsuarioLogado,
                        Double.parseDouble(documento.replaceAll("\\D",
                                "")),
                        inscricao,
                        idCns,
                        retFuncao);
            }
            this.rn.openConsultaDadosRetornoApi(idCns, "J");
        } catch (Exception e) {
            String mensagemIntegracao = this.rn.validarMensagemConsultaIntegracao((double) this.codEmpresaUsuarioLogado,
                    Double.parseDouble(documento.replaceAll("\\D",
                            "")),
                    inscricao,
                    0.0,
                    e.getMessage());
            EmpresaUtil.showErrorMessage(mensagemIntegracao);
        }
        return retFuncao;
    }

    public boolean existeCliente(Double codCliente) {
        boolean result = false;
        try {
            result = rn.buscaCliente(codCliente);
        } catch (DataException ex) {
            EmpresaUtil.showError(FALHA_AO_BUSCAR_CLIENTE_MOTIVO, ex);
        }
        return result;
    }

    public boolean buscarDadosCliente(String documento) {
        try {
            return rn.buscaDadosCliente(documento);
        } catch (DataException ex) {
            EmpresaUtil.showError(FALHA_AO_BUSCAR_CLIENTE_MOTIVO, ex);
        }
        return false;
    }

    public boolean buscarDadosClientePf(String documento,
                                        String inscricao) {
        boolean result = false;
        try {
            result = rn.buscaDadosClientePf(documento, inscricao);
        } catch (DataException ex) {
            EmpresaUtil.showError(FALHA_AO_BUSCAR_CLIENTE_MOTIVO, ex);
        }
        return result;
    }

    public void preencherDadosPj() {
        this.incluirEndClick = true;
        this.limparEnderecos();
        this.incluirEndereco();
        this.incluirEndClick = false;
        this.edtNomeCliente.setValue(this.prepararNomeRazaoSocial(this.tbConsultaNbsPessoaJuridica.getRAZAO_SOCIAL().asString()));
        String nomeFantasia = (this.prepararFantasia(this.tbConsultaNbsPessoaJuridica.getNOME_FANTASIA().asString()));
        String cnae = this.tbConsultaNbsPessoaJuridica.getCNAE_PRINCIPAL().asString();
        try {
            if (StringUtils.isNotBlank(cnae)) {
                this.tbDadosJuridicos.setCNAE(cnae.substring(0,
                        Math.min(cnae.length(),
                                10)).replaceAll("\\D+",
                        ""));
            }
            if (this.edtNomeFantasia.getValue().isEmpty()) {
                this.tbDadosJuridicos.setNOME_FANTASIA(nomeFantasia);
            }
        } catch (DataException dataException) {
            dataException.printStackTrace();
        }
        if (this.ehSituacaoValidaCadastroSintegra()) {
            this.edTpEnd1.setValue(2);
            String ie = this.tbConsultaNbsSintegraDados.getINSCRICAO_ESTADUAL().asString();
            String bairro = this.tbConsultaNbsSintegraDados.getBAIRRO().asString();
            String rua = this.tbConsultaNbsSintegraDados.getLOGRADOURO().asString();
            String numero = this.tbConsultaNbsSintegraDados.getNUMERO().asString();
            String complemento = this.tbConsultaNbsSintegraDados.getCOMPLEMENTO().asString();
            String cep = this.tbConsultaNbsSintegraDados.getCEP().asString();
            String uf = this.tbConsultaNbsSintegraDados.getUF().asString();
            String cidade = this.tbConsultaNbsSintegraDados.getMUNICIPIO_IBGE().asString();
            if (StringUtils.isNotBlank(cidade)) {
                cidade = this.tbConsultaNbsSintegraDados.getMUNICIPIO().asString();
            }
            this.edtINSC_ESTADUAL.setValue(ie);
            this.edtBairro1.setValue(this.prepararEnderBairro(bairro));
            this.edtRua1.setValue(this.prepararEnderRua(rua));
            this.edtNumero1.setValue(this.prepararEnderNumero(numero));
            this.edtComplemento1.setValue(this.prepararEnderComplemento(complemento));
            this.edtCEP1.setValue(cep);
            this.edUF1.setValue(uf.toUpperCase());
            String ibgeCorreios = this.tbConsultaNbsSintegraDados.getCODIGO_IBGE().asString();
            boolean tbUf1NotEmpty = !this.tbUf1.isEmpty();
            if (tbUf1NotEmpty) {
                try {
                    if (this.temIbge(this.tbCidades1,
                            ibgeCorreios)) {
                        String cidadeAux = this.tbCidades1.getDESCRICAO().asString();
                        this.edtCidade1.setText(cidadeAux);
                    } else {
                        if (StringUtils.isNotBlank(cidade)) {
                            String cidadeAux = removerAcentos(cidade.toUpperCase().trim().toUpperCase());
                            if (this.tbCidades1.locate(FrmCadastroRapidoClienteA.DESCRICAO,
                                    cidadeAux)) {
                                cidadeAux = this.tbCidades1.getDESCRICAO().asString();
                                this.edtCidade1.setText(cidadeAux);
                            }
                        }
                    }
                } catch (DataException dataException) {
                    dataException.printStackTrace();
                }
            }
        } else {
            // EnderecoRetornadoReceitaFederal
            this.edTpEnd1.setValue(2);
            this.edtBairro1.setValue(this.prepararEnderBairro(this.tbConsultaNbsPessoaJuridica.getBAIRRO().asString()));
            this.edtRua1.setValue(this.prepararEnderRua(this.tbConsultaNbsPessoaJuridica.getLOGRADOURO().asString()));
            this.edtNumero1.setValue(this.prepararEnderNumero(this.tbConsultaNbsPessoaJuridica.getNUMERO().asString()));
            this.edtComplemento1.setValue(this.prepararEnderComplemento(this.tbConsultaNbsPessoaJuridica.getCOMPLEMENTO().asString()));
            this.edtCEP1.setValue(this.tbConsultaNbsPessoaJuridica.getCEP().asString());
            this.edUF1.setValue(this.tbConsultaNbsPessoaJuridica.getUF().asString());
            String cidade = this.tbConsultaNbsPessoaJuridica.getMUNICIPIO_IBGE().asString();
            if (StringUtils.isNotBlank(cidade)) {
                cidade = this.tbConsultaNbsPessoaJuridica.getMUNICIPIO().asString();
            }
            String ibgeCorreios = this.tbConsultaNbsPessoaJuridica.getCODIGO_IBGE().asString();
            boolean tbUf1Empty = !this.tbUf1.isEmpty();
            if (tbUf1Empty) {
                try {
                    if (temIbge(this.tbCidades1, ibgeCorreios)) {
                        String cidadeAux = this.tbCidades1.getDESCRICAO().asString();
                        this.edtCidade1.setText(cidadeAux);
                    } else {
                        if (StringUtils.isNotBlank(cidade)) {
                            String cidadeAux = removerAcentos(cidade.toUpperCase().trim().toUpperCase());
                            if (this.tbCidades1.locate(FrmCadastroRapidoClienteA.DESCRICAO,
                                    cidadeAux)) {
                                cidadeAux = this.tbCidades1.getDESCRICAO().asString();
                                this.edtCidade1.setText(cidadeAux);
                            }
                        }
                    }
                } catch (DataException dataException) {
                    dataException.printStackTrace();
                }
            }
        }
        this.salvar();
    }

    private String prepararNomeRazaoSocial(String nome) {
        return limitarString(removerAcentos(nome.trim().toUpperCase()), 100);
    }

    private String prepararFantasia(String nome) {
        return limitarString(removerAcentos(nome.trim().toUpperCase()), 30);
    }

    private String prepararEnderNumero(String numero) {
        return limitarString(numero, 5);
    }

    private String prepararEnderRua(String rua) {
        return limitarString(removerAcentos(rua.trim().toUpperCase()), 50);
    }

    private String prepararEnderBairro(String bairro) {
        return limitarString(removerAcentos(bairro.trim().toUpperCase()), 30);
    }

    private String prepararEnderComplemento(String complemento) {
        return limitarString(complemento.trim(), 30);
    }

    public String preencherDadosPf() {
        boolean tbConsultaNbsPessoaFisicaNotEmpty = !this.tbConsultaNbsPessoaFisica.isEmpty();
        if (tbConsultaNbsPessoaFisicaNotEmpty) {
            edtNomeCliente.setValue(this.prepararNomeRazaoSocial(tbConsultaNbsPessoaFisica.getNOME().asString()));
        }
        boolean tbConsultaNbsSintegraDadosNotEmpty = !tbConsultaNbsSintegraDados.isEmpty();
        if (tbConsultaNbsSintegraDadosNotEmpty) {
            try {
                boolean tbConsultaNbsPessoaFisicaEmpty = tbConsultaNbsPessoaFisica.isEmpty();
                if (tbConsultaNbsPessoaFisicaEmpty) {
                    edtNomeCliente.setValue(this.prepararNomeRazaoSocial(tbConsultaNbsSintegraDados.getNOME_RAZAO_SOCIAL().asString()));
                }
                tbClientes.edit();
                tbClientes.setNOME(edtNomeCliente.getValue().asString());
                tbClientes.post();
                if (tbClienteEnderecoInscricao.locate("INSCRICAO_ESTADUAL", tbConsultaNbsSintegraDados.getINSCRICAO_ESTADUAL().asString())) {
                    tbClienteEnderecoInscricao.edit();
                } else {
                    tbClienteEnderecoInscricao.append();
                    tbClienteEnderecoInscricao.setCOD_CLIENTE(tbConsultaNbsSintegraDados.getCPF_CNPJ().asDecimal());
                    tbClienteEnderecoInscricao.setINSCRICAO_ESTADUAL(tbConsultaNbsSintegraDados.getINSCRICAO_ESTADUAL().asString());
                }
                tbClienteEnderecoInscricao.setUF(tbConsultaNbsSintegraDados.getUF().asString());
                tbClienteEnderecoInscricao.setATIVO(inscricaoSituacaoAtivo(tbConsultaNbsSintegraDados.getSITUACAO_CADASTRAL().asString()));
                tbClienteEnderecoInscricao.setBAIRRO(this.prepararEnderBairro(tbConsultaNbsSintegraDados.getBAIRRO().asString()));
                tbClienteEnderecoInscricao.setRUA(this.prepararEnderRua(tbConsultaNbsSintegraDados.getLOGRADOURO().asString()));
                tbClienteEnderecoInscricao.setFACHADA(this.prepararEnderNumero(tbConsultaNbsSintegraDados.getNUMERO().asString()));
                tbClienteEnderecoInscricao.setCEP(tbConsultaNbsSintegraDados.getCEP().asString());
                tbClienteEnderecoInscricao.setCIDADE(this.prepararEnderCidade(tbConsultaNbsSintegraDados.getMUNICIPIO().asString(), tbConsultaNbsSintegraDados.getUF().asString()));
                tbClienteEnderecoInscricao.setCOMPLEMENTO(this.prepararEnderComplemento(tbConsultaNbsSintegraDados.getCOMPLEMENTO().asString()));
                if (tbConsultaNbsSintegraDados.getNOME_FANTASIA().asString().isEmpty()) {
                    tbClienteEnderecoInscricao.setNOME_PROPRIEDADE(this.prepararNomeRazaoSocial(tbConsultaNbsSintegraDados.getNOME_RAZAO_SOCIAL().asString()));
                } else {
                    tbClienteEnderecoInscricao.setNOME_PROPRIEDADE(this.prepararNomeRazaoSocial(tbConsultaNbsSintegraDados.getNOME_FANTASIA().asString()));
                }
                /*Buscar cidade IBGE*/
                rn.pesquisarCidadesUf(tbCidadesInscricao, tbConsultaNbsSintegraDados.getUF().asString());
                if (!temIbge(tbCidadesInscricao, tbConsultaNbsSintegraDados.getCODIGO_IBGE().asString().trim())) {
                    tbCidadesInscricao.locate(DESCRICAO, tbConsultaNbsSintegraDados.getMUNICIPIO().asString());
                }
                tbClienteEnderecoInscricao.setCOD_CIDADES(tbCidadesInscricao.getCOD_CIDADES().asDecimal());
                tbClienteEnderecoInscricao.post();
                this.preencherEnderecoResidencialPessoaFisica();
            } catch (DataException e) {
                return e.getMessage();
            }
        }
        return "S";
    }

    private String inscricaoSituacaoAtivo(String situacao) {
        if (StringUtils.isBlank(situacao)) {
            return "N";
        }
        if (situacao.equalsIgnoreCase("ATIVO")) {
            return "S";
        }
        if (situacao.equalsIgnoreCase("ATIVA")) {
            return "S";
        }
        if (situacao.equalsIgnoreCase("HABILITADO")) {
            return "S";
        }
        if (situacao.equalsIgnoreCase("HABILITADA")) {
            return "S";
        }
        return "N";
    }

    private String prepararEnderCidade(String cidade, String uf) {
        if (StringUtils.isBlank(cidade)) {
            return "";
        }
        return limitarString(cidade, 56) + "/" + uf;  //Limitar 60 Caracteres
    }

    private void pesquisarCidadesUf(CIDADES tbCidade,
                                    String uf) {
        try {
            this.rn.pesquisarCidadesUf(tbCidade,
                    uf);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao pesquisar Cidades UF",
                    dataException);
        }
    }

    private void preencherEnderecoResidencialPessoaFisica() {
        try {
            this.incluirEndClick = true;
            /*1o.Endereco*/
            boolean boxEnd1NotVisible = !this.hBoxEnd01.isVisible();
            if (boxEnd1NotVisible) {
                this.hBoxEnd01.setVisible(true);
                this.edTpEnd1.clear();
                this.edUF1.clear();
                this.edtCidade1.clear();
                this.edtCEP1.clear();
                this.edtRua1.clear();
                this.edtBairro1.clear();
                this.edtComplemento1.clear();
                this.edtNumero1.clear();
            }
            this.incluirEndClick = false;
            this.tbClientes.edit();
            this.edtBairro1.setValue(this.tbClienteEnderecoInscricao.getBAIRRO().asString());
            this.edtRua1.setValue(this.tbClienteEnderecoInscricao.getRUA().asString());
            this.edtNumero1.setValue(this.tbClienteEnderecoInscricao.getFACHADA().asString());
            this.edtComplemento1.setValue(this.tbClienteEnderecoInscricao.getCOMPLEMENTO().asString());
            this.edtCEP1.setValue(this.tbClienteEnderecoInscricao.getCEP().asString());
            this.edTpEnd1.setValue(1);
            this.edUF1.setValue(this.tbClienteEnderecoInscricao.getUF().asString());
            this.pesquisarCidadesUf(this.tbCidades1,
                    this.tbClienteEnderecoInscricao.getUF().asString());
            this.edtCidade1.setValue(this.tbClienteEnderecoInscricao.getCOD_CIDADES());
            this.tbClientes.post();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao preencher o endereço residencial da pessoa física",
                    dataException);
        }
    }

    private void editarEnderecoPorInscricao() {
        FrmCadastroRapidoClienteEnderecoA frmCadastroRapidoClienteEnderecoA = new FrmCadastroRapidoClienteEnderecoA();
        frmCadastroRapidoClienteEnderecoA.setCaption("Endereço por Inscrição");
        frmCadastroRapidoClienteEnderecoA.lblTipoDoEndereco.setValue("Inscrição Estadual");
        frmCadastroRapidoClienteEnderecoA.edtTipoDoEndereco.setValue(tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString());
        frmCadastroRapidoClienteEnderecoA.edtNomePropriedade.setValue(tbClienteEnderecoInscricao.getNOME_PROPRIEDADE().asString());
        frmCadastroRapidoClienteEnderecoA.edtRua.setValue(tbClienteEnderecoInscricao.getRUA().asString());
        frmCadastroRapidoClienteEnderecoA.edtNumero.setValue(tbClienteEnderecoInscricao.getFACHADA().asString());
        frmCadastroRapidoClienteEnderecoA.edtBairro.setValue(tbClienteEnderecoInscricao.getBAIRRO().asString());
        frmCadastroRapidoClienteEnderecoA.edtCEP.setValue(tbClienteEnderecoInscricao.getCEP().asString());
        frmCadastroRapidoClienteEnderecoA.edtComplemento.setValue(tbClienteEnderecoInscricao.getCOMPLEMENTO().asString());
        frmCadastroRapidoClienteEnderecoA.edtCxPostal.setValue(tbClienteEnderecoInscricao.getCX_POSTAL().asString());
        frmCadastroRapidoClienteEnderecoA.edtContato.setValue(tbClienteEnderecoInscricao.getCONTATO().asString());
        frmCadastroRapidoClienteEnderecoA.iniciarAlterarEnderecoPorInscricao(this.codEmpresaUsuarioLogado,
                this.tbClienteEnderecoInscricao.getCOD_CLIENTE().asDecimal(),
                this.cboTipo.getValue().asString(),
                this.tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString(),
                this.tbClienteEnderecoInscricao.getUF().asString(),
                this.tbClienteEnderecoInscricao.getCOD_CIDADES().asDecimal(),
                this.usaApiConsulta);
        FormUtil.doShow(frmCadastroRapidoClienteEnderecoA,
                t -> {
                    boolean frmCadastroRapidoClienteEnderecoAOk = frmCadastroRapidoClienteEnderecoA.isOk();
                    if (frmCadastroRapidoClienteEnderecoAOk) {
                        this.tbClienteEnderecoInscricao.edit();
                        this.tbClienteEnderecoInscricao.setRUA(frmCadastroRapidoClienteEnderecoA.edtRua.getValue().asString());
                        this.tbClienteEnderecoInscricao.setFACHADA(frmCadastroRapidoClienteEnderecoA.edtNumero.getValue().asString());
                        this.tbClienteEnderecoInscricao.setBAIRRO(frmCadastroRapidoClienteEnderecoA.edtBairro.getValue().asString());
                        this.tbClienteEnderecoInscricao.setCEP(frmCadastroRapidoClienteEnderecoA.edtCEP.getValue().asString());
                        this.tbClienteEnderecoInscricao.setUF(frmCadastroRapidoClienteEnderecoA.cboUF.getValue().asString());
                        this.tbClienteEnderecoInscricao.setCOD_CIDADES(frmCadastroRapidoClienteEnderecoA.cboCidade.getValue().asDecimal());
                        this.tbClienteEnderecoInscricao.setCOMPLEMENTO(frmCadastroRapidoClienteEnderecoA.edtComplemento.getValue().asString());
                        this.tbClienteEnderecoInscricao.setNOME_PROPRIEDADE(frmCadastroRapidoClienteEnderecoA.edtNomePropriedade.getValue().asString());
                        this.tbClienteEnderecoInscricao.setCX_POSTAL(frmCadastroRapidoClienteEnderecoA.edtCxPostal.getValue().asString());
                        this.tbClienteEnderecoInscricao.setCONTATO(frmCadastroRapidoClienteEnderecoA.edtContato.getValue().asString());
                        this.tbClienteEnderecoInscricao.post();
                        this.preencherEnderecoResidencialPessoaFisica();
                    }
                });
    }

    private String limitarString(String campo, int tamanhoMaximo) {
        if (campo == null) {
            return "";
        }
        if (campo.trim().length() > tamanhoMaximo) {
            return campo.trim().substring(0, tamanhoMaximo);
        } else {
            return campo.trim();
        }
    }

    @Override
    public void btnConsultaIntegracaoCnpjClick(final Event<Object> event) {
        String urlConsultaNbs = rn.getUrlConsultaNbs((double) this.codEmpresaUsuarioLogado);
        if (StringUtils.isBlank(urlConsultaNbs)) {
            EmpresaUtil.showInformationMessage("É obrigatório configurar a URL de consulta plugin-consulta-nbs em  [CRMParts] Tabelas > Parâmetros > Clientes.");
            return;
        }

        if (this.cboTipo.getValue().asString().equals("J")) {
            if (this.edtCNPJ.getValue().isEmpty()) {
                EmpresaUtil.showInformationMessage("É obrigatório informar o CNPJ para consulta na Receita Federal.");
                return;
            }
            String documento = edtCNPJ.getValue().asString().replaceAll("\\D", "");
            String inscricao = edtINSC_ESTADUAL.getValue().asString().replaceAll("\\D", "");
            double codCliente2 = Double.parseDouble(documento);
            if (existeCliente(codCliente2)) {
                Dialog.create().title("Aviso!").message("Cliente já cadastrado!").showWarning();
            } else if (this.buscarDadosCliente(documento)) {
                preencherDadosPj();
                this.codClienteConsultadoApi = codCliente2;
            } else {
                String retFuncao = this.consultarPessoaJuridica(urlConsultaNbs, documento, inscricao);
                if (!retFuncao.equals("S")) {
                    EmpresaUtil.showInformationMessage("Consulta de dados retornou " + retFuncao);
                    return;
                }
                preencherDadosPj();
                this.codClienteConsultadoApi = codCliente2;
            }
        }
    }

    /*
    private void getCEPOnLineService(
            Integer xEndereco
    ) {
        String xCPFCNPJ = "";
        String strCEP = "";
        String strTipoEnd = "";
        double xCodCliente = 0.0;
        Integer tpEndereco = xEndereco;
        if (cboTipo.getValue().asString().equals("F")) {
            if (!edtCPF.getValue().isEmpty()) {
                xCPFCNPJ = edtCPF.getValue().asString().replaceAll("\\D", "");
                xCodCliente = Double.parseDouble(xCPFCNPJ);
            }
        } else {
            if (!edtCNPJ.getValue().isEmpty()) {
                xCPFCNPJ = edtCNPJ.getValue().asString().replaceAll("\\D", "");
                xCodCliente = Double.parseDouble(xCPFCNPJ);
            }
        }
        switch (xEndereco) {
            case 1:
                strCEP = edtCEP1.getValue().asString().trim();
                break;
            case 2:
                strCEP = edtCEP2.getValue().asString().trim();
                break;
            case 3:
                strCEP = edtCEP3.getValue().asString().trim();
                break;
            default:
                break;
        }
        if (xEndereco.equals(1) && edTpEnd1.getText().trim().equals(RESIDENCIAL)) {
            strTipoEnd = edTpEnd1.getText().trim();
            tpEndereco = 1;
        } else if (xEndereco.equals(1) && edTpEnd1.getText().trim().equals(COMERCIAL)) {
            strTipoEnd = edTpEnd1.getText().trim();
            tpEndereco = 2;
        } else if (xEndereco.equals(1) && edTpEnd1.getText().trim().equals(COBRANCA)) {
            strTipoEnd = edTpEnd1.getText().trim();
            tpEndereco = 3;
        } else if (xEndereco.equals(2) && edTpEnd2.getText().trim().equals(RESIDENCIAL)) {
            strTipoEnd = edTpEnd2.getText().trim();
            tpEndereco = 1;
        } else if (xEndereco.equals(2) && edTpEnd2.getText().trim().equals(COMERCIAL)) {
            strTipoEnd = edTpEnd2.getText().trim();
            tpEndereco = 2;
        } else if (xEndereco.equals(2) && edTpEnd2.getText().trim().equals(COBRANCA)) {
            strTipoEnd = edTpEnd2.getText().trim();
            tpEndereco = 3;
        } else if (xEndereco.equals(3) && edTpEnd3.getText().trim().equals(RESIDENCIAL)) {
            strTipoEnd = edTpEnd3.getText().trim();
            tpEndereco = 1;
        } else if (xEndereco.equals(3) && edTpEnd3.getText().trim().equals(COMERCIAL)) {
            strTipoEnd = edTpEnd3.getText().trim();
            tpEndereco = 2;
        } else if (xEndereco.equals(3) && edTpEnd3.getText().trim().equals(COBRANCA)) {
            strTipoEnd = edTpEnd3.getText().trim();
            tpEndereco = 3;
        }
        if (!xCPFCNPJ.isEmpty()) {
            String ufCorreios = "";
            String cidadeCorreios;
            String ibgeCorreios = "";
            String cidadeAux;
            // para validar se o registro é novo, verifico se já passou um fieldName para o edCEP, pois caso ainda não tenha passado é um registro novo
            boolean isRegistroNovoEndereco1 = (edtCEP1.getFieldName() != null) && (edtCEP1.getFieldName().isEmpty());
            boolean isRegistroNovoEndereco2 = (edtCEP2.getFieldName() != null) && (edtCEP2.getFieldName().isEmpty());
            boolean isRegistroNovoEndereco3 = (edtCEP3.getFieldName() != null) && (edtCEP3.getFieldName().isEmpty());
            if ((xEndereco.equals(1)) && isRegistroNovoEndereco1) {
                try {
                    if (jsonCepDados.getString(LOGRADOURO).trim().length() > 50) {
                        edtRua1.setValue(removerAcentos(jsonCepDados.getString(LOGRADOURO).substring(0, 50).trim().toUpperCase()));
                    } else {
                        edtRua1.setValue(removerAcentos(jsonCepDados.getString(LOGRADOURO).trim().toUpperCase()));
                    }
                    ufCorreios = removerAcentos(jsonCepDados.getString("uf").toUpperCase().trim().toUpperCase());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                    if (tbUf1.locate("UF", ufCorreios)) {
                        String uf1 = tbUf1.getUF().asString();
                        edUF1.setValue(uf1);
                    }
                } catch (DataException e) {
                    e.printStackTrace();
                }
                try {
                    ibgeCorreios = removerAcentos(jsonCepDados.getString("ibge").toUpperCase().trim().toUpperCase());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                    boolean tbUf1NotEmpty = !this.tbUf1.isEmpty();
                    if (tbUf1NotEmpty) {
                        if (temIbge(tbCidades1, ibgeCorreios)) {
                            cidadeAux = tbCidades1.getDESCRICAO().asString();
                            edtCidade1.setText(cidadeAux);
                        } else {
                            cidadeCorreios = removerAcentos(jsonCepDados.getString(LOCALIDADE).toUpperCase().trim().toUpperCase());
                            if (tbCidades1.locate(DESCRICAO, cidadeCorreios)) {
                                cidadeAux = tbCidades1.getDESCRICAO().asString();
                                edtCidade1.setText(cidadeAux);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    if (jsonCepDados.getString(BAIRRO).trim().length() > 30) {
                        edtBairro1.setValue(removerAcentos(jsonCepDados.getString(BAIRRO).substring(0, 30).trim().toUpperCase()));
                    } else {
                        edtBairro1.setValue(removerAcentos(jsonCepDados.getString(BAIRRO).trim().toUpperCase()));
                    }
                    edtComplemento1.setValue(removerAcentos(jsonCepDados.getString(COMPLEMENTO).trim().toUpperCase()));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            if ((xEndereco.equals(2)) && isRegistroNovoEndereco2) {
                try {
                    if (jsonCepDados.getString(LOGRADOURO).trim().length() > 50) {
                        edtRua2.setValue(removerAcentos(jsonCepDados.getString(LOGRADOURO).substring(0, 50).trim().toUpperCase()));
                    } else {
                        edtRua2.setValue(removerAcentos(jsonCepDados.getString(LOGRADOURO).trim().toUpperCase()));
                    }
                    ufCorreios = removerAcentos(jsonCepDados.getString("uf").toUpperCase().trim().toUpperCase());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                    if (tbUf2.locate("UF", ufCorreios)) {
                        String uf2 = tbUf2.getUF().asString();
                        edUF2.setValue(uf2);
                    }
                } catch (DataException e) {
                    e.printStackTrace();
                }
                try {
                    ibgeCorreios = removerAcentos(jsonCepDados.getString("ibge").toUpperCase().trim().toUpperCase());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                    boolean tbUf2NotEmpty = !this.tbUf2.isEmpty();
                    if (tbUf2NotEmpty) {
                        if (temIbge(tbCidades2, ibgeCorreios)) {
                            cidadeAux = tbCidades2.getDESCRICAO().asString();
                            edtCidade2.setText(cidadeAux);
                        } else {
                            cidadeCorreios = removerAcentos(jsonCepDados.getString(LOCALIDADE).toUpperCase().trim().toUpperCase());
                            if (tbCidades2.locate(DESCRICAO, cidadeCorreios)) {
                                cidadeAux = tbCidades2.getDESCRICAO().asString();
                                edtCidade1.setText(cidadeAux);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    if (jsonCepDados.getString(BAIRRO).trim().length() > 30) {
                        edtBairro2.setValue(removerAcentos(jsonCepDados.getString(BAIRRO).substring(0, 30).trim().toUpperCase()));
                    } else {
                        edtBairro2.setValue(removerAcentos(jsonCepDados.getString(BAIRRO).trim().toUpperCase()));
                    }
                    edtComplemento2.setValue(removerAcentos(jsonCepDados.getString(COMPLEMENTO).trim().toUpperCase()));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            if ((xEndereco.equals(3)) && isRegistroNovoEndereco3) {
                try {
                    if (jsonCepDados.getString(LOGRADOURO).trim().length() > 50) {
                        edtRua3.setValue(removerAcentos(jsonCepDados.getString(LOGRADOURO).substring(0, 50).trim().toUpperCase()));
                    } else {
                        edtRua3.setValue(removerAcentos(jsonCepDados.getString(LOGRADOURO).trim().toUpperCase()));
                    }
                    ufCorreios = removerAcentos(jsonCepDados.getString("uf").toUpperCase().trim().toUpperCase());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                    if (tbUf3.locate("UF", ufCorreios)) {
                        String uf3 = tbUf3.getUF().asString();
                        edUF3.setValue(uf3);
                    }
                } catch (DataException e) {
                    e.printStackTrace();
                }
                try {
                    ibgeCorreios = removerAcentos(jsonCepDados.getString("ibge").toUpperCase().trim().toUpperCase());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                    boolean tbUf3NotEmpty = !tbUf3.isEmpty();
                    if (tbUf3NotEmpty) {
                        if (temIbge(tbCidades3, ibgeCorreios)) {
                            cidadeAux = tbCidades3.getDESCRICAO().asString();
                            edtCidade3.setText(cidadeAux);
                        } else {
                            cidadeCorreios = removerAcentos(jsonCepDados.getString(LOCALIDADE).toUpperCase().trim().toUpperCase());
                            if (tbCidades3.locate(DESCRICAO, cidadeCorreios)) {
                                cidadeAux = tbCidades3.getDESCRICAO().asString();
                                edtCidade3.setText(cidadeAux);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    if (jsonCepDados.getString(BAIRRO).trim().length() > 30) {
                        edtBairro3.setValue(removerAcentos(jsonCepDados.getString(BAIRRO).substring(0, 30).trim().toUpperCase()));
                    } else {
                        edtBairro3.setValue(removerAcentos(jsonCepDados.getString(BAIRRO).trim().toUpperCase()));
                    }
                    edtComplemento3.setValue(removerAcentos(jsonCepDados.getString(COMPLEMENTO).trim().toUpperCase()));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            try {
                if (!jsonCepDados.toString().isEmpty()) {
                    jsonCepDados = null;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            FrmCepClienteOnLineServiceA formCEPClienteOnLine = new FrmCepClienteOnLineServiceA();
            String tipoEnd = strTipoEnd;
            int xtpEndereco;
            if (tpEndereco == 1) {
                xtpEndereco = 2;
            } else {
                if (tpEndereco == 2) {
                    xtpEndereco = 3;
                } else {
                    xtpEndereco = 4;
                }
            }
            if (formCEPClienteOnLine.getEnderecos(xCodCliente, strCEP, xtpEndereco)) {
                xAchouDivergencias = true;
                FormUtil.doShow(formCEPClienteOnLine, t -> {
                    boolean statusRes = false;
                    boolean statusCom = false;
                    boolean statusCob = false;
                    if (formCEPClienteOnLine.getpTpEndereco() == 2) {
                        statusRes = true;
                    } else {
                        if (formCEPClienteOnLine.getpTpEndereco() == 3) {
                            statusCom = true;
                        } else {
                            statusCob = true;
                        }
                    }

                    boolean setouFieldName1 = (edtCEP1.getFieldName() != null) && (!edtCEP1.getFieldName().isEmpty());
                    boolean setouFieldName2 = (edtCEP2.getFieldName() != null) && (!edtCEP2.getFieldName().isEmpty());
                    boolean setouFieldName3 = (edtCEP3.getFieldName() != null) && (!edtCEP3.getFieldName().isEmpty());
                    if (formCEPClienteOnLine.isOk()) {
                        if ((statusRes) && (tipoEnd.equals(RESIDENCIAL))) {
                            switch (xEndereco) {
                                case 1:
                                    if (setouFieldName1) {
                                        tbClientes.edit();
                                        String cep1 = formCEPClienteOnLine.tbClientesEndereco.getCEP_RES().asString();
                                        edtCEP1.getTable().setField(edtCEP1.getFieldName(), cep1);
                                        edUF1.getTable().setField(edUF1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getUF_RES().asString().trim());
                                        edtCidade1.getTable().setField(edtCidade1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOD_CID_RES().asInteger());
                                        edtRua1.getTable().setField(edtRua1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getRUA_RES().asString().trim());
                                        edtBairro1.getTable().setField(edtBairro1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getBAIRRO_RES().asString().trim());
                                        edtComplemento1.getTable().setField(edtComplemento1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOMPLEMENTO_RES().asString().trim());
                                        tbClientes.post();
                                    }
                                    break;
                                case 2:
                                    if (setouFieldName2) {
                                        tbClientes.edit();
                                        String cep2 = formCEPClienteOnLine.tbClientesEndereco.getCEP_RES().asString();
                                        edtCEP2.getTable().setField(edtCEP2.getFieldName(), cep2);
                                        edUF2.getTable().setField(edUF2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getUF_RES().asString().trim());
                                        edtCidade2.getTable().setField(edtCidade2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOD_CID_RES().asInteger());
                                        edtRua2.getTable().setField(edtRua2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getRUA_RES().asString().trim());
                                        edtBairro2.getTable().setField(edtBairro2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getBAIRRO_RES().asString().trim());
                                        edtComplemento2.getTable().setField(edtComplemento2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOMPLEMENTO_RES().asString().trim());
                                        tbClientes.post();
                                    }
                                    break;
                                case 3:
                                    if (setouFieldName3) {
                                        tbClientes.edit();
                                        String cep3 = formCEPClienteOnLine.tbClientesEndereco.getCEP_RES().asString();
                                        edtCEP3.getTable().setField(edtCEP3.getFieldName(), cep3);
                                        edUF3.getTable().setField(edUF3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getUF_RES().asString().trim());
                                        edtCidade3.getTable().setField(edtCidade3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOD_CID_RES().asInteger());
                                        edtRua3.getTable().setField(edtRua3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getRUA_RES().asString().trim());
                                        edtBairro3.getTable().setField(edtBairro3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getBAIRRO_RES().asString().trim());
                                        edtComplemento3.getTable().setField(edtComplemento3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOMPLEMENTO_RES().asString().trim());
                                        tbClientes.post();
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                        if ((statusCom) && (tipoEnd.equals(COMERCIAL))) {
                            switch (xEndereco) {
                                case 1:
                                    if (setouFieldName1) {
                                        tbClientes.edit();
                                        String cep1 = formCEPClienteOnLine.tbClientesEndereco.getCEP_COM().asString();
                                        edtCEP1.getTable().setField(edtCEP1.getFieldName(), cep1);
                                        edUF1.getTable().setField(edUF1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getUF_COM().asString().trim());
                                        edtCidade1.getTable().setField(edtCidade1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOD_CID_COM().asInteger());
                                        edtRua1.getTable().setField(edtRua1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getRUA_COM().asString().trim());
                                        edtBairro1.getTable().setField(edtBairro1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getBAIRRO_COM().asString().trim());
                                        edtComplemento1.getTable().setField(edtComplemento1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOMPLEMENTO_COM().asString().trim());
                                        tbClientes.post();
                                    }
                                    break;
                                case 2:
                                    if (setouFieldName2) {
                                        tbClientes.edit();
                                        String cep2 = formCEPClienteOnLine.tbClientesEndereco.getCEP_COM().asString();
                                        edtCEP2.getTable().setField(edtCEP2.getFieldName(), cep2);
                                        edUF2.getTable().setField(edUF2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getUF_COM().asString().trim());
                                        edtCidade2.getTable().setField(edtCidade2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOD_CID_COM().asInteger());
                                        edtRua2.getTable().setField(edtRua2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getRUA_COM().asString().trim());
                                        edtBairro2.getTable().setField(edtBairro2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getBAIRRO_COM().asString().trim());
                                        edtComplemento2.getTable().setField(edtComplemento2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOMPLEMENTO_COM().asString().trim());
                                        tbClientes.post();
                                    }
                                    break;
                                case 3:
                                    if (setouFieldName3) {
                                        tbClientes.edit();
                                        String cep3 = formCEPClienteOnLine.tbClientesEndereco.getCEP_COM().asString();
                                        edtCEP3.getTable().setField(edtCEP3.getFieldName(), cep3);
                                        edUF3.getTable().setField(edUF3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getUF_COM().asString().trim());
                                        edtCidade3.getTable().setField(edtCidade3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOD_CID_COM().asInteger());
                                        edtRua3.getTable().setField(edtRua3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getRUA_COM().asString().trim());
                                        edtBairro3.getTable().setField(edtBairro3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getBAIRRO_COM().asString().trim());
                                        edtComplemento3.getTable().setField(edtComplemento3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOMPLEMENTO_COM().asString().trim());
                                        tbClientes.post();
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                        if ((statusCob) && (tipoEnd.equals(COBRANCA))) {
                            switch (xEndereco) {
                                case 1:
                                    if (setouFieldName1) {
                                        tbClientes.edit();
                                        String cep1 = formCEPClienteOnLine.tbClientesEndereco.getCEP_COBRANCA().asString();
                                        edtCEP1.getTable().setField(edtCEP1.getFieldName(), cep1);
                                        edUF1.getTable().setField(edUF1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getUF_COBRANCA().asString().trim());
                                        edtCidade1.getTable().setField(edtCidade1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOD_CID_COBRANCA().asInteger());
                                        edtRua1.getTable().setField(edtRua1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getRUA_COBRANCA().asString().trim());
                                        edtBairro1.getTable().setField(edtBairro1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getBAIRRO_COBRANCA().asString().trim());
                                        edtComplemento1.getTable().setField(edtComplemento1.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOMPLEMENTO_COBRANCA().asString().trim());
                                        tbClientes.post();
                                    }
                                    break;
                                case 2:
                                    if (setouFieldName2) {
                                        tbClientes.edit();
                                        String cep2 = formCEPClienteOnLine.tbClientesEndereco.getCEP_COBRANCA().asString();
                                        edtCEP2.getTable().setField(edtCEP2.getFieldName(), cep2);
                                        edUF2.getTable().setField(edUF2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getUF_COBRANCA().asString().trim());
                                        edtCidade2.getTable().setField(edtCidade2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOD_CID_COBRANCA().asInteger());
                                        edtRua2.getTable().setField(edtRua2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getRUA_COBRANCA().asString().trim());
                                        edtBairro2.getTable().setField(edtBairro2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getBAIRRO_COBRANCA().asString().trim());
                                        edtComplemento2.getTable().setField(edtComplemento2.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOMPLEMENTO_COBRANCA().asString().trim());
                                        tbClientes.post();
                                    }
                                    break;
                                case 3:
                                    if (setouFieldName3) {
                                        tbClientes.edit();
                                        String cep3 = formCEPClienteOnLine.tbClientesEndereco.getCEP_COBRANCA().asString();
                                        edtCEP3.getTable().setField(edtCEP3.getFieldName(), cep3);
                                        edUF3.getTable().setField(edUF3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getUF_COBRANCA().asString().trim());
                                        edtCidade3.getTable().setField(edtCidade3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOD_CID_COBRANCA().asInteger());
                                        edtRua3.getTable().setField(edtRua3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getRUA_COBRANCA().asString().trim());
                                        edtBairro3.getTable().setField(edtBairro3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getBAIRRO_COBRANCA().asString().trim());
                                        edtComplemento3.getTable().setField(edtComplemento3.getFieldName(), formCEPClienteOnLine.tbClientesEndereco.getCOMPLEMENTO_COBRANCA().asString().trim());
                                        tbClientes.post();
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                    } else {
                        xAchouDivergencias = false;
                    }
                });
            } else {
                xAchouDivergencias = false;
            }
        } else {
            xAchouDivergencias = false;
        }
        endTime = System.currentTimeMillis();
    }
    */

    @Override
    public void btnExibirDadosConsultaAPIIntegracaoClick(final Event<Object> event) {
        this.consultarApiIntegracao();
    }

    private void consultarApiIntegracao() {
        String cpfCnpj = this.tbClienteDiverso.getCNPJ_CPF().asString().replaceAll("\\D", "");
        String ie = this.tbClienteDiverso.getINSCRICAO_ESTADUAL().asString();
        String uf = this.ufComercialInformada();
        FrmDadosSintegraA frmDadosSintegraA = new FrmDadosSintegraA();
        frmDadosSintegraA.abreDadosConsultados(
                cpfCnpj
                , ie
                , uf
        );
        FormUtil.doShow(frmDadosSintegraA,
                t -> {
                    if (frmDadosSintegraA.isAtualizouCadastro()) {
                        this.limparEnderecos();
                        this.buscarCliente(Double.parseDouble(cpfCnpj));
                    }
                    this.exibirStatusCadastro();
                });
    }

    private void retirarVinculoComTabela(TFString campo) {
        campo.setFieldName(null);
        campo.setTable(null);
        campo.applyProperties();
    }

    private void retirarVinculoComTabela(TFCombo campo) {
        campo.setFieldName(null);
        campo.setTable(null);
        campo.applyProperties();
    }

    private void limparEnderecos() {
        /* Os Edits estão vinculados a tabela, primeiro tem que retirar todos os vínculos
         * */
        retirarVinculoComTabela(edUF1);
        retirarVinculoComTabela(edtCidade1);
        retirarVinculoComTabela(edtCEP1);
        retirarVinculoComTabela(edtRua1);
        retirarVinculoComTabela(edtNumero1);
        retirarVinculoComTabela(edtComplemento1);
        retirarVinculoComTabela(edtBairro1);
        retirarVinculoComTabela(edtBairro1);
        retirarVinculoComTabela(edUF2);
        retirarVinculoComTabela(edtCidade2);
        retirarVinculoComTabela(edtCEP2);
        retirarVinculoComTabela(edtRua2);
        retirarVinculoComTabela(edtNumero2);
        retirarVinculoComTabela(edtComplemento2);
        retirarVinculoComTabela(edtBairro2);
        retirarVinculoComTabela(edtBairro2);
        retirarVinculoComTabela(edUF3);
        retirarVinculoComTabela(edtCidade3);
        retirarVinculoComTabela(edtCEP3);
        retirarVinculoComTabela(edtRua3);
        retirarVinculoComTabela(edtNumero3);
        retirarVinculoComTabela(edtComplemento3);
        retirarVinculoComTabela(edtBairro3);
        retirarVinculoComTabela(edtBairro3);
        excluirEndereco(3);
        excluirEndereco(2);
        excluirEndereco(1);
        hBoxEnd01.setVisible(false);
    }

    @Override
    public void edUF1Exit(final Event<Object> event) {
        if (edUF1.getValue().asString().isEmpty()) {
            edUF1.setText("");
        }
    }

    @Override
    public void edtCidade1Exit(final Event<Object> event) {
        if (edtCidade1.getValue().asString().isEmpty()) {
            edtCidade1.setText("");
        }
    }

    @Override
    public void edUF2Exit(final Event<Object> event) {
        if (edUF2.getValue().asString().isEmpty()) {
            edUF2.setText("");
        }
    }

    @Override
    public void edtCidade2Exit(final Event<Object> event) {
        if (edtCidade2.getValue().asString().isEmpty()) {
            edtCidade2.setText("");
        }
    }

    @Override
    public void edUF3Exit(final Event<Object> event) {
        if (edUF3.getValue().asString().isEmpty()) {
            edUF3.setText("");
        }
    }

    @Override
    public void edtCidade3Exit(final Event<Object> event) {
        if (edtCidade3.getValue().asString().isEmpty()) {
            edtCidade3.setText("");
        }
    }

    @Override
    public void cboNacionalidadeExit(final Event<Object> event) {
        if (cboNacionalidade.getValue().asString().isEmpty()) {
            cboNacionalidade.setText("");
        }
    }

    @Override
    public void btnAlterarEnderecoInscricaoClick(final Event<Object> event) {
        editarEnderecoPorInscricao();
    }

    @Override
    public void edtINSC_ESTADUALExit(final Event<Object> event) {
        if (!edtINSC_ESTADUAL.getValue().isEmpty()) {
            if (this.usaApiConsulta) {
                String uf = ufComercialInformada();
                String auxIe;
                try {
                    auxIe = rn.padronizarInscricaoEstadual(uf, edtINSC_ESTADUAL.getValue().asString());
                } catch (DataException e) {
                    auxIe = edtINSC_ESTADUAL.getValue().asString().replaceAll("\\D", "");
                }
                edtINSC_ESTADUAL.setValue(auxIe);
            } else {
                String auxIe = edtINSC_ESTADUAL.getValue().asString().replaceAll("\\D", "");
                edtINSC_ESTADUAL.setValue(auxIe);
            }
        }
    }

    private String ufComercialInformada() {
        if ((!edUF1.getFieldName().isEmpty()) && (edUF1.getFieldName().equals(UF_COM))) {
            return edUF1.getValue().asString();
        }
        if ((!edUF2.getFieldName().isEmpty()) && (edUF2.getFieldName().equals(UF_COM))) {
            return edUF2.getValue().asString();
        }
        if ((!edUF3.getFieldName().isEmpty()) && (edUF3.getFieldName().equals(UF_COM))) {
            return edUF3.getValue().asString();
        }
        return "";
    }

    @Override
    public void btnFlagsClick(final Event<Object> event) {
        if (this.ehCRMParts) {
            if (!EmpresaUtil.validarAcesso("K0625")) {
                return;
            }
        } else {
            if (!EmpresaUtil.validarAcesso("B9106")) {
                return;
            }
        }
        if (codCliente > 0) {
            FrmClientesFlagsServA formFlag = new FrmClientesFlagsServA();
            if (formFlag.consultaCliente(codCliente)) {
                if (this.abriuComoTabSheet()) {
                    FormUtil.createTab(formFlag, t -> {
                    });
                } else {
                    FormUtil.doShow(formFlag, t -> {
                    });
                }
            }
        }
    }

    @Override
    public void lblSituacaoCadSintegraClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    @Override
    public void lblSituacaoSintegraClick(Event<Object> event) {
        this.exibirSintegraMultiplasIe();
    }

    @Override
    public void btnEnderecosPorInscricaoClick(final Event<Object> event) {
        if (codCliente > 0) {
            FrmClienteEnderecoPorInscricaoA frmCad = new FrmClienteEnderecoPorInscricaoA();
            if (frmCad.consultaCliente(codCliente)) {
                FormUtil.doShow(frmCad, t -> {
                    if (cboTipo.getValue().asString().equals("F")) {
                        boolean ehProdutorRural = "S".equals(frmCad.tbClienteDiverso.getPRODUTOR_RURAL().asString());
                        this.vBoxFoneContatoPJ.setVisible(ehProdutorRural);
                    }
                });
            }
        }
    }

    public String getValorCombo(TFCombo combo) {
        String value = combo.getValue().asString().trim();
        value = StringUtils.isNotBlank(value) ? value : "";
        return value;
    }

    public String getValorComboNumero(TFCombo combo) {
        String value;

        if (combo.getValue().asString().trim().isEmpty()) {
            value = "-1";
        } else {
            value = combo.getValue().asString().trim();
        }
        return value;
    }

    @Override
    public void btnLogsClick(final Event<Object> event) {
        if (!rn.podeAcessarLogsCliente(this.usuarioLogado)) {
            EmpresaUtil.showInformationMessage("Sem permissão de acesso [Logs Cliente] - Acesso: K1015");
            return;
        }
        FrmLogsA frmLogs = new FrmLogsA();
        frmLogs.openFrmLogs(this.codCliente, this.codEmpresaUsuarioLogado, this.ehRodobens);
        FormUtil.doShow(frmLogs, t -> {
        });
    }

    @Override
    public void btnCreditoCorporativoClick(final Event<Object> event) {
        if (!rn.podeAcessarCreditoCorporativo(this.usuarioLogado)) {
            EmpresaUtil.showInformationMessage("Sem permissão de acesso [Crédito Corporativo] - Acesso: K1016");
            return;
        }

        FrmCreditoCorporativoA frmCreditoCorporativo = new FrmCreditoCorporativoA();
        frmCreditoCorporativo.openFrmCreditoCorporativo(this.codCliente, this.codEmpresaUsuarioLogado, this.ehRodobens);
        FormUtil.doShow(frmCreditoCorporativo, t -> {
        });
    }

    @Override
    public void btnHistoricoFichaClienteClick(Event<Object> event) {
        FrmHistoricoFichaClienteA frmHistoricoFichaClienteA = new FrmHistoricoFichaClienteA();
        frmHistoricoFichaClienteA.carregarDadosTela(tbClientes.getCOD_CLIENTE().asDecimal(), tbClientes.getNOME().asString());
        if (!this.ehCRMParts) {
            FormUtil.doModal(frmHistoricoFichaClienteA,
                    t -> {
                    });
        } else {
            FormUtil.doShow(frmHistoricoFichaClienteA,
                    t -> {
                    });
        }
    }

    @Override
    public void btnImpressaoConsentimentoLGPDClick(final Event<Object> event) {
        FPopupMenuTermoLGPD.open(btnImpressaoConsentimentoLGPD);
    }

    private void setIconAbrirAssinaturaDigital() {
        try {
            TipoAssinaturaStrategy tipoAssinatura = new OsTermoLgpdStrategy();
            JSONObject parametrosAssinatura = tipoAssinatura.getJsonParametros(this.codCliente, this.codEmpresaUsuarioLogado);
            CrmAssinaturaDigitalUtils.EnStatusAssinatura statusAssinaturaDigital = CrmAssinaturaDigitalUtils.getStatusAssinatura(tipoAssinatura, parametrosAssinatura);
            abrirAssinaturaDigital.setIconClass(statusAssinaturaDigital.getClasseIcone() + " " + statusAssinaturaDigital.getClasseIconeColor());
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao montar menu de opções para termo lgpd", e);
        }
    }

    @Override
    public void imprimirTermoClick(Event<Object> event) {
        CrmAssinaturaDigital assinaturaDigitalTermoLgpd = new CrmAssinaturaDigital(
                new OsTermoLgpdStrategy()
                , this.codCliente
                , this.codEmpresaUsuarioLogado
                        );
        CrmAssinaturaDigitalUtils.imprimirDigitalOuNormal(assinaturaDigitalTermoLgpd.getTipoAssinaturaStrategy(), assinaturaDigitalTermoLgpd.getJsonParametros(), this::imprimirTermoLGPD, this::setIconAbrirAssinaturaDigital);
            }

    private void abrirAssinaturaDigital() {
        CrmAssinaturaDigital assinaturaDigitalTermoLGPD = new CrmAssinaturaDigital(
                new OsTermoLgpdStrategy()
                , this.codCliente
                ,this.codEmpresaUsuarioLogado
        );
        assinaturaDigitalTermoLGPD.abrirAssinatura(this::setIconAbrirAssinaturaDigital);
        }

    @Override
    public void abrirAssinaturaDigitalClick(Event<Object> event) {
        this.abrirAssinaturaDigital();
    }

//    public void imprimirTermoLGPDAssinado() {
//        try {
//            TipoAssinaturaStrategy tipoAssinaturaEmpresas = new OsTermoLgpdStrategy();
//            JSONObject parametrosJson = tipoAssinaturaEmpresas.getJsonParametros(this.codCliente, this.codEmpresaUsuarioLogado);
//            CrmAssinaturaDigitalUtils.abrirListaDocumentos(tipoAssinaturaEmpresas, parametrosJson);
//        } catch (DataException e) {
//            EmpresaUtil.showError("Erro ao imprimir termo LGPD Assinado", e);
//        }
//    }

    public void imprimirTermoLGPD() {
        String nomeRel = File.separator + "crmservice" + File.separator + "TermoConsentimentoLGPD.jasper";

        boolean ambienteDesenvolvimento = wl.sysget("SPRINGBOOT").asString().equals("S");
        if (ambienteDesenvolvimento) {
            nomeRel = nomeRel.replace(File.separator + "crmservice", "");
        }

        TFReport report = new TFReport();
        report.clearParams();
        report.setName("TermoConsentimentoLGPD");
        report.setReportFile(nomeRel);
        report.addParam("COD_EMPRESA", (double) this.codEmpresaUsuarioLogado);
        String logMonitor = "Relatório \"TermoConsentimentoLGPD\"" + System.lineSeparator()
                + System.lineSeparator()
                + "Parâmetro \"COD_EMPRESA\" Valor \"" + this.codEmpresaUsuarioLogado + "\"";
        freedom.util.FRLogger.log(logMonitor, this.getClass());
        try {
            report.runToPdf();
        } catch (ReportException reportException) {
            EmpresaUtil.showError("Erro ao tentar imprimir relatório de consentimento LGPD",
                    reportException);
        }
    }

    @Override
    public void tbClientesAfterScroll(Event<Object> event) {
        try {
            String telefoneCelularGravadoNoBanco = this.tbClientes.getTELEFONE_CEL().asString();
            boolean telefoneCelularContemCaracteresNaoNumericos = StringUtil.contemCaracteresNaoNumericos(telefoneCelularGravadoNoBanco);
            if (telefoneCelularContemCaracteresNaoNumericos) {
                String telefoneCelularSomenteNumeros = StringUtil.removerCaracteresNaoNumericos(telefoneCelularGravadoNoBanco);
                this.tbClientes.edit();
                this.tbClientes.setTELEFONE_CEL(telefoneCelularSomenteNumeros);
                this.tbClientes.post();
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao remover caracteres não numéricos do telefone celular",
                    dataException);
        }
        try {
            String telefoneResidencialGravadoNoBanco = this.tbClientes.getTELEFONE_RES().asString();
            boolean telefoneResidencialContemCaracteresNaoNumericos = StringUtil.contemCaracteresNaoNumericos(telefoneResidencialGravadoNoBanco);
            if (telefoneResidencialContemCaracteresNaoNumericos) {
                String telefoneResidencialSomenteNumeros = StringUtil.removerCaracteresNaoNumericos(telefoneResidencialGravadoNoBanco);
                this.tbClientes.edit();
                this.tbClientes.setTELEFONE_RES(telefoneResidencialSomenteNumeros);
                this.tbClientes.post();
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao remover caracteres não numéricos do telefone residencial",
                    dataException);
        }
        try {
            String telefoneComercialGravadoNoBanco = this.tbClientes.getTELEFONE_COM().asString();
            boolean telefoneComercialContemCaracteresNaoNumericos = StringUtil.contemCaracteresNaoNumericos(telefoneComercialGravadoNoBanco);
            if (telefoneComercialContemCaracteresNaoNumericos) {
                String telefoneComercialSomenteNumeros = StringUtil.removerCaracteresNaoNumericos(telefoneComercialGravadoNoBanco);
                this.tbClientes.edit();
                this.tbClientes.setTELEFONE_COM(telefoneComercialSomenteNumeros);
                this.tbClientes.post();
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao remover caracteres não numéricos do telefone comercial",
                    dataException);
        }
    }

    @Override
    public void edtDddCelChange(Event<Object> event) {
        // Implementado em FrmCadastroRapidoClienteU
    }

    @Override
    public void edtTelefoneCelChange(Event<Object> event) {
        // Implementado em FrmCadastroRapidoClienteU
    }

    @Override
    public void edtEmailChange(Event<Object> event) {
        // Implementado em FrmCadastroRapidoClienteU
    }

    private void carregarCboMidia() {
        try {
            this.rn.carregarCboMidia();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a mídia"
                    ,dataException
            );
        }
    }

}