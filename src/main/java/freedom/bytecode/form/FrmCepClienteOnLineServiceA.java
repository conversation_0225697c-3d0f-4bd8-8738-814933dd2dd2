package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmCepClienteOnLineServiceW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;

public class FrmCepClienteOnLineServiceA extends FrmCepClienteOnLineServiceW {

    private static final long serialVersionUID = 20130827081850L;

    private double pCodCliente = 0.0;

    private String pStrCep = "";

    private int pTpEndereco = 0;

    private boolean ok = false;

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        ok= false;
        close();
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            if (edtCep.getValue().asString().equals("")) {
                FCoachmark.getItem("btnSalvar").setMessage("Informe um CEP!");
                FCoachmark.open("btnSalvar");
                return;
            }
            if (tbClientesEnderecoTemp.getField("UF").asString().equals("") || edtCep.getValue().isEmpty()) {
                FCoachmark.getItem("btnSalvar").setMessage("CEP não encontrado!");
                FCoachmark.open("btnSalvar");
                return;
            }
            if (!tbClientesEnderecoTemp.getField("CEP").asString().trim().equals(edtCep.getValue().asString().trim())) {
                FCoachmark.getItem("btnSalvar").setMessage("CEP informado diferente do pesquisado!");
                FCoachmark.open("btnSalvar");
                return;
            }
            Value qtdeReg = new Value(0);
            int codCidadeIbge = 0;
            int codCid = 0;
            int valor = cbbTipoEndereco.getValue().asInteger();
            switch (valor) {
                case 1:
                    // break;
                case // Residencial
                        2:
                    codCid = rn.getCodCidade(lblUfCorreio.getCaption(), lblCidadeCorreio.getCaption(), lblCepCorreio.getCaption(), tbClientesEnderecoTemp.getField("CODCIDADEIBGE").asInteger(), qtdeReg);
                    if (qtdeReg.asInteger() > 0 && codCid > 0) {
                        tbClientesEndereco.edit();
                        tbClientesEndereco.setCEP_RES(lblCepCorreio.getCaption());
                        tbClientesEndereco.setUF_RES(lblUfCorreio.getCaption());
                        tbClientesEndereco.setCIDADE_RES(lblCidadeCorreio.getCaption());
                        tbClientesEndereco.setRUA_RES(lblLogradouroCorreio.getCaption());
                        tbClientesEndereco.setBAIRRO_RES(lblBairroCorreio.getCaption());
                        codCidadeIbge = tbClientesEnderecoTemp.getField("CODCIDADEIBGE").asInteger();
                        tbClientesEndereco.setCODCIDADEIBGE_RES(codCidadeIbge);
                        String complEnd = lblComplementoCorreio.getCaption().trim();
                        if (complEnd.length() > 30) {
                            complEnd = complEnd.substring(0, 30);
                        }
                        tbClientesEndereco.setCOMPLEMENTO_RES(complEnd);
                        tbClientesEndereco.setCOD_CID_RES(codCid);
                        tbClientesEndereco.post();
                    }
                    tbClientesEndereco.applyUpdates();
                    tbClientesEndereco.commitUpdates();
                    break;
                case // Comercial
                        3:
                    codCid = rn.getCodCidade(lblUfCorreio.getCaption(), lblCidadeCorreio.getCaption(), lblCepCorreio.getCaption(), tbClientesEnderecoTemp.getField("CODCIDADEIBGE").asInteger(), qtdeReg);
                    if (qtdeReg.asInteger() > 0 && codCid > 0) {
                        tbClientesEndereco.edit();
                        tbClientesEndereco.setCEP_COM(lblCepCorreio.getCaption());
                        tbClientesEndereco.setUF_COM(lblUfCorreio.getCaption());
                        tbClientesEndereco.setCIDADE_COM(lblCidadeCorreio.getCaption());
                        tbClientesEndereco.setRUA_COM(lblLogradouroCorreio.getCaption());
                        tbClientesEndereco.setBAIRRO_COM(lblBairroCorreio.getCaption());
                        codCidadeIbge = tbClientesEnderecoTemp.getField("CODCIDADEIBGE").asInteger();
                        tbClientesEndereco.setCODCIDADEIBGE_COM(codCidadeIbge);
                        String complEnd = lblComplementoCorreio.getCaption().trim();
                        if (complEnd.length() > 30) {
                            complEnd = complEnd.substring(0, 30);
                        }
                        tbClientesEndereco.setCOMPLEMENTO_COM(complEnd);
                        tbClientesEndereco.setCOD_CID_COM(codCid);
                        tbClientesEndereco.post();
                    }
                    tbClientesEndereco.applyUpdates();
                    tbClientesEndereco.commitUpdates();
                    break;
                case // Cobrança
                        4:
                    codCid = rn.getCodCidade(lblUfCorreio.getCaption(), lblCidadeCorreio.getCaption(), lblCepCorreio.getCaption(), tbClientesEnderecoTemp.getField("CODCIDADEIBGE").asInteger(), qtdeReg);
                    if (qtdeReg.asInteger() > 0 && codCid > 0) {
                        tbClientesEndereco.edit();
                        tbClientesEndereco.setCEP_COBRANCA(lblCepCorreio.getCaption());
                        tbClientesEndereco.setUF_COBRANCA(lblUfCorreio.getCaption());
                        tbClientesEndereco.setCIDADE_COB(lblCidadeCorreio.getCaption());
                        tbClientesEndereco.setRUA_COBRANCA(lblLogradouroCorreio.getCaption());
                        tbClientesEndereco.setBAIRRO_COBRANCA(lblBairroCorreio.getCaption());
                        codCidadeIbge = tbClientesEnderecoTemp.getField("CODCIDADEIBGE").asInteger();
                        tbClientesEndereco.setCODCIDADEIBGE_COB(codCidadeIbge);
                        String complEnd = lblComplementoCorreio.getCaption().trim();
                        if (complEnd.length() > 30) {
                            complEnd = complEnd.substring(0, 30);
                        }
                        tbClientesEndereco.setCOMPLEMENTO_COBRANCA(complEnd);
                        tbClientesEndereco.setCOD_CID_COBRANCA(codCid);
                        tbClientesEndereco.post();
                    }
                    tbClientesEndereco.applyUpdates();
                    tbClientesEndereco.commitUpdates();
                    break;
            }
            if ((codCidadeIbge > 0) && (codCid > 0)) {
                if (tbCidades.locate("COD_CIDADES", codCid)) {
                    if (codCidadeIbge > 0) {
                        tbCidades.edit();
                        tbCidades.setCODCIDADEIBGE(codCidadeIbge);
                        tbCidades.post();
                        tbCidades.applyUpdates();
                        tbCidades.commitUpdates();
                    }
                }
            }
            if (qtdeReg.asInteger() > 1) {
                EmpresaUtil.showMessage("Validação","Não foi possivel alterar o endereço, pois foi encontrada dois ou mais cidades com esse mesmo mesmo numero IBGE [" + tbClientesEnderecoTemp.getField("CODCIDADEIBGE").asInteger() +  "]" );
            } else{
                ok = true;
                close();
            }

        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Salvar Alterações. Motivo: ", ex);
        }
    }

    @Override
    public void vBoxApagarNomeCliClick(final Event<Object> event) {
        limpaCepCorreios();
        edtCep.clear();
        edtCep.setFocus();
    }

    public void pesquisarTipoEndCliente() {
        try {
            rn.pesquisarTipoEndCliente(pCodCliente);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    public boolean getEnderecos(Double codCliente, String strCEP, Integer tpEndereco) {
        boolean ret;
        try {
            this.pCodCliente = codCliente;
            this.pStrCep = strCEP;
            this.pTpEndereco = tpEndereco;
            rn.filtrarClienteEndereco(codCliente);
            //ret = validaCEPOnLine(codCliente, strCEP, tpEndereco);
            ret = true;
        } catch (Exception ex) {
            EmpresaUtil.showError("Falha ao Buscar Endereços", ex);
            ret = false;
        }
        return ret;
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        pesquisarTipoEndCliente();
        cbbTipoEndereco.setValue(pTpEndereco);
        edtCep.setValue(pStrCep);
        atribuiValores();
        consultaCepOnLine();
        FHBox4.setColor("#EEE9E9");
        FHBox14.setColor("#EEE9E9");
        FHBox22.setColor("#EEE9E9");
        FHBox30.setColor("#EEE9E9");
        edtCep.setMaxlength(8);
    }

    private void atribuiValores() {
        int valor = cbbTipoEndereco.getValue().asInteger();
        switch (valor) {
            case 1:
                // lblCepAtual.setCaption(tbClientesEndereco.getCEP_RES().asString());
                // lblUfAtual.setCaption(tbClientesEndereco.getUF_RES().asString());
                // lblCidadeAtual.setCaption(tbClientesEndereco.getCIDADE_RES().asString());
                // lblLogradouroAtual.setCaption(tbClientesEndereco.getRUA_RES().asString());
                // lblBairroAtual.setCaption(tbClientesEndereco.getBAIRRO_RES().asString());
                // lblComplementoAtual.setCaption(tbClientesEndereco.getCOMPLEMENTO_RES().asString());
                // break;
            case // Residencial
                    2:
                lblCepAtual.setCaption(tbClientesEndereco.getCEP_RES().asString());
                lblUfAtual.setCaption(tbClientesEndereco.getUF_RES().asString().toUpperCase());
                lblCidadeAtual.setCaption(tbClientesEndereco.getCIDADE_RES().asString().toUpperCase());
                lblLogradouroAtual.setCaption(tbClientesEndereco.getRUA_RES().asString().toUpperCase());
                lblBairroAtual.setCaption(tbClientesEndereco.getBAIRRO_RES().asString().toUpperCase());
                lblComplementoAtual.setCaption(tbClientesEndereco.getCOMPLEMENTO_RES().asString().toUpperCase());
                break;
            case // Comercial
                    3:
                lblCepAtual.setCaption(tbClientesEndereco.getCEP_COM().asString());
                lblUfAtual.setCaption(tbClientesEndereco.getUF_COM().asString().toUpperCase());
                lblCidadeAtual.setCaption(tbClientesEndereco.getCIDADE_COM().asString().toUpperCase());
                lblLogradouroAtual.setCaption(tbClientesEndereco.getRUA_COM().asString().toUpperCase());
                lblBairroAtual.setCaption(tbClientesEndereco.getBAIRRO_COM().asString().toUpperCase());
                lblComplementoAtual.setCaption(tbClientesEndereco.getCOMPLEMENTO_COM().asString().toUpperCase());
                break;
            case // Cobrança
                    4:
                lblCepAtual.setCaption(tbClientesEndereco.getCEP_COBRANCA().asString());
                lblUfAtual.setCaption(tbClientesEndereco.getUF_COBRANCA().asString().toUpperCase());
                lblCidadeAtual.setCaption(tbClientesEndereco.getCIDADE_COB().asString().toUpperCase());
                lblLogradouroAtual.setCaption(tbClientesEndereco.getRUA_COBRANCA().asString().toUpperCase());
                lblBairroAtual.setCaption(tbClientesEndereco.getBAIRRO_COBRANCA().asString().toUpperCase());
                lblComplementoAtual.setCaption(tbClientesEndereco.getCOMPLEMENTO_COBRANCA().asString().toUpperCase());
                break;
        }
    }

    @Override
    public void cbbTipoEnderecoChange(final Event<Object> event) {
        atribuiValores();
        pTpEndereco = cbbTipoEndereco.getValue().asInteger();
    }

    @Override
    public void btnCepClick(final Event<Object> event) {
        consultaCepOnLine();
    }

    public void consultaCepOnLine() {
        try {
            boolean ret = rn.getDadosCEPOnLine(pCodCliente, edtCep.getValue().asString().trim().toUpperCase(), "", 0, cbbTipoEndereco.getValue().asInteger());
            if (!ret) {
                limpaCepCorreios();
                FCoachmark.getItem("edtCep").setMessage("CEP não encontrado, verifique!");
                FCoachmark.open("edtCep");
            }
            if (!tbClientesEnderecoIeTemp.isEmpty()) {
                lblCepCorreio.setCaption(tbClientesEnderecoTemp.getField("CEP").asString());
                lblUfCorreio.setCaption(tbClientesEnderecoTemp.getField("UF").asString());
                lblCidadeCorreio.setCaption(tbClientesEnderecoTemp.getField("CIDADE").asString());
                lblLogradouroCorreio.setCaption(tbClientesEnderecoTemp.getField("RUA").asString());
                lblBairroCorreio.setCaption(tbClientesEnderecoTemp.getField("BAIRRO").asString());
                lblComplementoCorreio.setCaption(tbClientesEnderecoTemp.getField("COMPLEMENTO").asString());
            }
        } catch (Exception ex) {
            EmpresaUtil.showError("Erro ao Consultar CEP: ", ex);
        }
    }

    public boolean isOk() {
        return ok;
    }

    public void limpaCepCorreios() {
        try {
            if (tbClientesEnderecoTemp.isEmpty()) {
                tbClientesEnderecoTemp.append();
            } else {
                tbClientesEnderecoTemp.edit();
            }
            tbClientesEnderecoTemp.setField("COD_CLIENTE", pCodCliente);
            tbClientesEnderecoTemp.setField("CEP", "");
            tbClientesEnderecoTemp.setField("UF", "");
            tbClientesEnderecoTemp.setField("RUA", "");
            tbClientesEnderecoTemp.setField("CIDADE", "");
            tbClientesEnderecoTemp.setField("BAIRRO", "");
            tbClientesEnderecoTemp.setField("COMPLEMENTO", "");
            tbClientesEnderecoTemp.setField("CODCIDADEIBGE", 0);
            tbClientesEnderecoTemp.post();
            if (!tbClientesEnderecoIeTemp.isEmpty()) {
                lblCepCorreio.setCaption("");
                lblUfCorreio.setCaption(tbClientesEnderecoTemp.getField("UF").asString());
                lblCidadeCorreio.setCaption(tbClientesEnderecoTemp.getField("CIDADE").asString());
                lblLogradouroCorreio.setCaption(tbClientesEnderecoTemp.getField("RUA").asString());
                lblBairroCorreio.setCaption(tbClientesEnderecoTemp.getField("BAIRRO").asString());
                lblComplementoCorreio.setCaption(tbClientesEnderecoTemp.getField("COMPLEMENTO").asString());
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Limpar CEP: ", ex);
        }
    }

    public int getpTpEndereco() {
        return pTpEndereco;
    }

    @Override
    public void edtCepEnter(final Event<Object> event) {
        consultaCepOnLine();
    }

    public boolean servicoOk(String strCEP, Boolean allEnd, Value oRetMensagem) throws Exception {
        return rn.servicoOk(strCEP, allEnd, oRetMensagem);
    }

}
