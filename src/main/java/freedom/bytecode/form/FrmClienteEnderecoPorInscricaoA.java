package freedom.bytecode.form;

import freedom.bytecode.cursor.CIDADES;
import freedom.bytecode.form.wizard.FrmClienteEnderecoPorInscricaoW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.*;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class FrmClienteEnderecoPorInscricaoA extends FrmClienteEnderecoPorInscricaoW {

    private static final long serialVersionUID = 20130827081850L;

    public static final String INFORMACAO = "Informação";

    private final IWorkList wl = WorkListFactory.getInstance();

    private Double codCliente;

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private final int codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();

    private boolean usaApiConsulta = false;

    private String schemaAtual;

    private boolean podeAlterar;

    public boolean consultaCliente(double codCliente) {
        boolean result = false;
        this.codCliente = codCliente;
        try {
            result = this.rn.consultaCliente(this.codCliente);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao consultar o cliente",
                    dataException);
        }
        return result;
    }

    private void excluirEnderecoPorInscricao() {
        String podeExcluir = rn.podeExcluirEnderecoPorInscricao(tbClienteEnderecoInscricao.getCOD_CLIENTE().asDecimal(), tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString());
        if (podeExcluir.equals("S")) {
            Dialog.create().title("Confirma").message("Deseja excluir a Inscrição Estadual \"" + tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString() + "\" ?").confirmSimNao((String dialogResult) -> {
                if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                    String respFunc;
                    try {
                        respFunc = rn.excluirEnderecoPorInscricao(tbClienteEnderecoInscricao.getCOD_CLIENTE().asDecimal(), tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString());
                    } catch (DataException e) {
                        respFunc = "Não foi possível excluir o endereço! ";
                    }
                    if (!respFunc.equals("S")) {
                        Dialog.create()
                                .title(FrmClienteEnderecoPorInscricaoA.INFORMACAO)
                                .message(respFunc)
                                .showInformation();
                        return;
                    }
                    try {
                        rn.refreshEnderecosPorInscricao(this.codCliente);
                        habilitaBotoes();
                    } catch (DataException e) {
                        habilitaBotoes();
                    }
                }
            });
        } else {
            Dialog.create()
                    .title(FrmClienteEnderecoPorInscricaoA.INFORMACAO)
                    .message(podeExcluir)
                    .showInformation();
        }
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        FRLogger.log("Formulário: " + this.getName(),
                this.getClass());
        this.usaApiConsulta = usaPluginConsultaNBS();
        this.btnExibirDadosConsultaAPIIntegracao.setVisible(this.usaApiConsulta);
        this.carregarSchemaAtual();
        this.podeAlterar = this.rn.podeAlterarCadastro(this.usuarioLogado);
        boolean flagProdutorRural = this.rn.podeAlterarFlagProdutorRural(this.usuarioLogado);
        this.cboClienteEhProdutorRural.setEnabled(flagProdutorRural);
        this.habilitaBotoes();
        this.exibirStatusCadastro();
        String cboClienteEhProdutorRuralHint = "Se marcado, isentará o ICMS na venda."
                + System.lineSeparator()
                + System.lineSeparator()
                + "Acesso: K0283";
        this.cboClienteEhProdutorRural.setHint(cboClienteEhProdutorRuralHint);
    }

    private void carregarSchemaAtual() {
        try {
            this.schemaAtual = (this.rn.getSchemaAtual()).toUpperCase();
        } catch (DataException dataException) {
            EmpresaUtil.showInformationMessage("Não carregou o esquema do usuário."
                    + System.lineSeparator()
                    + "Motivo: "
                    + dataException.getMessage());
        }
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        this.close();
    }

    @Override
    public void btnIncluirClick(final Event<Object> event) {
        String urlConsultaNbs = this.rn.getUrlConsultaNbs(this.codEmpresaUsuarioLogado);
        if (this.usaApiConsulta
                && StringUtils.isBlank(urlConsultaNbs)) {
            EmpresaUtil.showInformationMessage("É obrigatório configurar a URL de consulta plugin-consulta-nbs em [CrmParts] Tabelas > Parâmetros > Clientes.");
            return;
        }
        FrmCadastroRapidoClienteProdRuralA frmCadastroRapidoClienteProdRuralA = new FrmCadastroRapidoClienteProdRuralA();
        String cnpjCPF = this.tbClienteDiverso.getCNPJ_CPF().asString();
        String docCPFCNPJ = cnpjCPF.replaceAll("\\D",
                "");
        frmCadastroRapidoClienteProdRuralA.setCpfCnpj(docCPFCNPJ);
        String ufCad;
        double codCidadeCad;
        String classe = this.tbClienteDiverso.getCLASSE().asString();
        if (classe.equals("F")) {
            ufCad = this.tbClientes.getUF_RES().asString();
            codCidadeCad = this.tbClientes.getCOD_CID_RES().asDecimal();
            Date aniversario = this.tbDadosFisicos.getANIVERSARIO().asDate();
            frmCadastroRapidoClienteProdRuralA.edtDataNasc.setValue(aniversario);
        } else {
            ufCad = this.tbClientes.getUF_COM().asString();
            codCidadeCad = this.tbClientes.getCOD_CID_COM().asDecimal();
        }
        frmCadastroRapidoClienteProdRuralA.setUFCadEnderecoPorInscricao(ufCad,
                null);
        FormUtil.doShow(frmCadastroRapidoClienteProdRuralA,
                t -> {
            if (frmCadastroRapidoClienteProdRuralA.isOk()) {
                String ie = frmCadastroRapidoClienteProdRuralA.edtInscricaoEstadual.getValue().asString();
                if (this.tbClienteEnderecoInscricao.locate("INSCRICAO_ESTADUAL",
                        ie)) {
                    EmpresaUtil.showInformationMessage("Já há um endereço cadastrado para a inscrição estadual \""
                            + ie
                            + "\".");
                } else if ((this.usaApiConsulta)
                        && (classe.equals("F"))) {
                    String nascimento;
                    String uf = frmCadastroRapidoClienteProdRuralA.getUfInscricao();
                    SimpleDateFormat str = new SimpleDateFormat("dd/MM/yyyy");
                    if (!frmCadastroRapidoClienteProdRuralA.edtDataNasc.getValue().isNull()) {
                        nascimento = str.format(frmCadastroRapidoClienteProdRuralA.edtDataNasc.getValue().asDate());
                    } else {
                        nascimento = "";
                    }
                    String retFuncao = this.consultaDadosPessoaFisicaNovoCadIe(urlConsultaNbs,
                            docCPFCNPJ,
                            nascimento,
                            uf,
                            ie);
                    if (!retFuncao.equals("S")) {
                        EmpresaUtil.showErrorMessage("Consulta de dados retornou "
                                + retFuncao);
                    }
                    this.habilitaBotoes();
                } else if ((this.usaApiConsulta)
                        && (classe.equals("J"))) {
                    String retFuncao = this.consultaDadosPessoaJuridicaNovoCadIe();
                    if (!retFuncao.equals("S")) {
                        EmpresaUtil.showErrorMessage("Consulta de dados retornou "
                                + retFuncao);
                    }
                    this.habilitaBotoes();
                } else {
                    FrmCadastroRapidoClienteEnderecoA frmCadastroRapidoClienteEnderecoA = new FrmCadastroRapidoClienteEnderecoA();
                    frmCadastroRapidoClienteEnderecoA.setCaption("Endereço por Inscrição");
                    frmCadastroRapidoClienteEnderecoA.lblTipoDoEndereco.setValue("Inscrição Estadual");
                    frmCadastroRapidoClienteEnderecoA.edtTipoDoEndereco.setValue(ie);
                    frmCadastroRapidoClienteEnderecoA.cboEnderecoAtivo.setValue("S");
                    frmCadastroRapidoClienteEnderecoA.iniciarUfCidades(ufCad,
                            codCidadeCad);
                    FormUtil.doShow(frmCadastroRapidoClienteEnderecoA,
                            t2 -> {
                        boolean frmCadastroRapidoClienteEnderecoAOk = frmCadastroRapidoClienteEnderecoA.isOk();
                        if (frmCadastroRapidoClienteEnderecoAOk) {
                            this.tbClienteEnderecoInscricao.append();
                            this.tbClienteEnderecoInscricao.setCOD_CLIENTE(this.tbClientes.getCOD_CLIENTE().asDecimal());
                            this.tbClienteEnderecoInscricao.setINSCRICAO_ESTADUAL(ie);
                            this.tbClienteEnderecoInscricao.setRUA(frmCadastroRapidoClienteEnderecoA.edtRua.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.setFACHADA(frmCadastroRapidoClienteEnderecoA.edtNumero.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.setBAIRRO(frmCadastroRapidoClienteEnderecoA.edtBairro.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.setCEP(frmCadastroRapidoClienteEnderecoA.edtCEP.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.setUF(frmCadastroRapidoClienteEnderecoA.cboUF.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.setCOD_CIDADES(frmCadastroRapidoClienteEnderecoA.cboCidade.getValue().asDecimal());
                            this.tbClienteEnderecoInscricao.setCOMPLEMENTO(frmCadastroRapidoClienteEnderecoA.edtComplemento.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.setNOME_PROPRIEDADE(frmCadastroRapidoClienteEnderecoA.edtNomePropriedade.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.setCX_POSTAL(frmCadastroRapidoClienteEnderecoA.edtCxPostal.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.setCONTATO(frmCadastroRapidoClienteEnderecoA.edtContato.getValue().asString().trim());
                            String cidade = frmCadastroRapidoClienteEnderecoA.cboCidade.getText().trim();
                            String primeiros57CaracteresDaCidade = cidade.substring(0, Math.min(cidade.length(), 57));
                            this.tbClienteEnderecoInscricao.setCIDADE(primeiros57CaracteresDaCidade
                                    + "/"
                                    + frmCadastroRapidoClienteEnderecoA.cboUF.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.setATIVO(frmCadastroRapidoClienteEnderecoA.cboEnderecoAtivo.getValue().asString().trim());
                            this.tbClienteEnderecoInscricao.post();
                            try {
                                this.tbClienteEnderecoInscricao.applyUpdates();
                                this.tbClienteEnderecoInscricao.commitUpdates();
                            } catch (DataException dataException) {
                                this.tbClienteEnderecoInscricao.cancelUpdates();
                                EmpresaUtil.showError("Falha ao salvar Motivo: ",
                                        dataException);
                            }
                            this.habilitaBotoes();
                        }
                    });
                }
            }
        });
    }

    @Override
    public void btnAlterarClick(Event<Object> event) {
        this.alterarEnderecoPorInscricao();
    }

    @Override
    public void btnExcluirClick(Event<Object> event) {
        this.excluirEnderecoPorInscricao();
    }

    private String consultaDadosPessoaJuridicaNovoCadIe() {
        this.preencherDadosInscricao();
        return "S";
    }

    private String consultaDadosPessoaFisicaNovoCadIe(String urlConsultaNbs, String docCpf, String nascimento, String uf, String inscricao) {
        if (!buscaDadosClientePf(docCpf, inscricao)) {
            StringBuilder url = new StringBuilder();
            url.append(urlConsultaNbs);
            url.append("/plugin-nbs-consulta/api/find");
            url.append("?empresa=").append(this.codEmpresaUsuarioLogado);
            url.append("&usuario=").append(this.usuarioLogado);
            url.append("&sistema=").append(this.wl.sysget("APPLICATION_NAME").asString());
            url.append("&doc=").append(docCpf);
            if (StringUtils.isNotBlank(nascimento)) {
                url.append("&nascimento=").append(nascimento);
            }
            if (StringUtils.isNotBlank(inscricao)) {
                url.append("&uf=").append(uf);
                url.append("&ie=").append(inscricao);
            }

            IntegracaoPluginNbsConsulta plugin = new IntegracaoPluginNbsConsulta();
            try {
                Double idCns = plugin.consultaCadastro(urlConsultaNbs, url.toString(), this.usuarioLogado, this.schemaAtual);
                this.rn.openConsultaDadosRetornoApi(idCns,
                        "F");
            } catch (Exception e) {
                EmpresaUtil.showErrorMessage(e.getMessage());
            }
            /*
             * Verificar se informada inscrição e não retornou dados. O que é pra fazer.
             */
        }
        preencherDadosInscricao();
        return "S";
    }

    public void preencherDadosInscricao() {
        boolean tbConsultaNbsSintegraDadosEmpty = this.tbConsultaNbsSintegraDados.isEmpty();
        if (!tbConsultaNbsSintegraDadosEmpty) {
            try {
                if (this.tbClienteEnderecoInscricao.locate("INSCRICAO_ESTADUAL", this.tbConsultaNbsSintegraDados.getINSCRICAO_ESTADUAL().asString())) {
                    this.tbClienteEnderecoInscricao.edit();
                } else {
                    this.tbClienteEnderecoInscricao.append();
                    this.tbClienteEnderecoInscricao.setCOD_CLIENTE(this.tbConsultaNbsSintegraDados.getCPF_CNPJ().asDecimal());
                    this.tbClienteEnderecoInscricao.setINSCRICAO_ESTADUAL(this.tbConsultaNbsSintegraDados.getINSCRICAO_ESTADUAL().asString());
                }
                this.tbClienteEnderecoInscricao.setUF(this.tbConsultaNbsSintegraDados.getUF().asString());
                String statusAtivos = "HABILITADO - HABILITADA - ATIVO - ATIVA";
                if (statusAtivos.contains(this.tbConsultaNbsSintegraDados.getSITUACAO_CADASTRAL().asString().trim().toUpperCase())) {
                    this.tbClienteEnderecoInscricao.setATIVO("S");
                } else {
                    this.tbClienteEnderecoInscricao.setATIVO("N");
                }
                this.tbClienteEnderecoInscricao.setBAIRRO(this.limitarString(removerAcentos(this.tbConsultaNbsSintegraDados.getBAIRRO().asString().trim().toUpperCase()),
                        30));
                this.tbClienteEnderecoInscricao.setRUA(this.limitarString(removerAcentos(this.tbConsultaNbsSintegraDados.getLOGRADOURO().asString().trim().toUpperCase()),
                        50));
                this.tbClienteEnderecoInscricao.setFACHADA(this.limitarString(this.tbConsultaNbsSintegraDados.getNUMERO().asString(),
                        5));
                this.tbClienteEnderecoInscricao.setCEP(this.tbConsultaNbsSintegraDados.getCEP().asString());
                this.tbClienteEnderecoInscricao.setCIDADE(this.limitarString(this.tbConsultaNbsSintegraDados.getMUNICIPIO().asString() + "/" + tbConsultaNbsSintegraDados.getUF().asString(),
                        60));
                this.tbClienteEnderecoInscricao.setCOMPLEMENTO(this.limitarString(this.tbConsultaNbsSintegraDados.getCOMPLEMENTO().asString(),
                        30));
                if (this.tbConsultaNbsSintegraDados.getNOME_FANTASIA().asString().isEmpty()) {
                    this.tbClienteEnderecoInscricao.setNOME_PROPRIEDADE(this.limitarString(this.tbConsultaNbsSintegraDados.getNOME_RAZAO_SOCIAL().asString(),
                            100));
                } else {
                    this.tbClienteEnderecoInscricao.setNOME_PROPRIEDADE(this.limitarString(this.tbConsultaNbsSintegraDados.getNOME_FANTASIA().asString(),
                            100));
                }
                /*Buscar cidade IBGE*/
                this.rn.pesquisarCidadesUf(this.tbCidadesInscricao,
                        this.tbConsultaNbsSintegraDados.getUF().asString());
                if (!this.temIbge(this.tbCidadesInscricao,
                        this.tbConsultaNbsSintegraDados.getCODIGO_IBGE().asString().trim())) {
                    this.tbCidadesInscricao.locate("DESCRICAO",
                            this.tbConsultaNbsSintegraDados.getMUNICIPIO().asString());
                }
                this.tbClienteEnderecoInscricao.setCOD_CIDADES(this.tbCidadesInscricao.getCOD_CIDADES().asDecimal());
                this.tbClienteEnderecoInscricao.post();
                this.tbClienteEnderecoInscricao.applyUpdates();
                this.tbClienteEnderecoInscricao.commitUpdates();
                this.alterarEnderecoPorInscricao();
            } catch (DataException dataException) {
                EmpresaUtil.showError("Erro ao preencher dados da inscrição estadual",
                        dataException);
            }
        }
    }

    public boolean temIbge(CIDADES tbCidade, String ibge) throws DataException {
        return tbCidade.locate("CODCIDADEIBGE", ibge);
    }


    public static String removerAcentos(String str) {
        return XmlUtil.removeAcentos(str);
    }

    private String limitarString(String campo, int tamanhoMaximo) {
        if (campo == null) {
            return "";
        }
        if (campo.length() > tamanhoMaximo) {
            return campo.substring(0, tamanhoMaximo);
        }
        return campo;
    }

    private boolean usaPluginConsultaNBS() {
        return rn.usaPluginConsultaNBS(this.codEmpresaUsuarioLogado);
    }

    private void habilitaBotoes() {
        this.btnAlterar.setEnabled(!this.tbClienteEnderecoInscricao.isEmpty());
        this.btnExcluir.setEnabled((!this.tbClienteEnderecoInscricao.isEmpty())
                && this.podeAlterar);
        boolean tbClienteEnderecoInscricaoEmpty = this.tbClienteEnderecoInscricao.isEmpty();
        if (tbClienteEnderecoInscricaoEmpty) {
            this.vBoxStatusCadastro.setVisible(false);
        }
    }

    private void alterarEnderecoPorInscricao() {
        FrmCadastroRapidoClienteEnderecoA frmCadastroRapidoClienteEnderecoA = new FrmCadastroRapidoClienteEnderecoA();
        frmCadastroRapidoClienteEnderecoA.setCaption("Endereço por Inscrição");
        frmCadastroRapidoClienteEnderecoA.lblTipoDoEndereco.setValue("Inscrição Estadual");
        frmCadastroRapidoClienteEnderecoA.edtTipoDoEndereco.setValue(this.tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString());
        frmCadastroRapidoClienteEnderecoA.edtNomePropriedade.setValue(this.tbClienteEnderecoInscricao.getNOME_PROPRIEDADE().asString());
        frmCadastroRapidoClienteEnderecoA.edtRua.setValue(this.tbClienteEnderecoInscricao.getRUA().asString());
        frmCadastroRapidoClienteEnderecoA.edtNumero.setValue(this.tbClienteEnderecoInscricao.getFACHADA().asString());
        frmCadastroRapidoClienteEnderecoA.edtBairro.setValue(this.tbClienteEnderecoInscricao.getBAIRRO().asString());
        frmCadastroRapidoClienteEnderecoA.edtCEP.setValue(this.tbClienteEnderecoInscricao.getCEP().asString());
        frmCadastroRapidoClienteEnderecoA.edtComplemento.setValue(this.tbClienteEnderecoInscricao.getCOMPLEMENTO().asString());
        frmCadastroRapidoClienteEnderecoA.edtCxPostal.setValue(this.tbClienteEnderecoInscricao.getCX_POSTAL().asString());
        frmCadastroRapidoClienteEnderecoA.edtContato.setValue(this.tbClienteEnderecoInscricao.getCONTATO().asString());
        frmCadastroRapidoClienteEnderecoA.iniciarAlterarEnderecoPorInscricao(this.codEmpresaUsuarioLogado,
                this.tbClienteEnderecoInscricao.getCOD_CLIENTE().asDecimal(),
                this.tbClienteDiverso.getCLASSE().asString(),
                this.tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString(),
                this.tbClienteEnderecoInscricao.getUF().asString(),
                this.tbClienteEnderecoInscricao.getCOD_CIDADES().asDecimal(),
                this.usaApiConsulta);
        if (this.tbClienteEnderecoInscricao.getATIVO().asString().equals("S")) {
            frmCadastroRapidoClienteEnderecoA.cboEnderecoAtivo.setValue("S");
        } else {
            frmCadastroRapidoClienteEnderecoA.cboEnderecoAtivo.setValue("N");
        }
        frmCadastroRapidoClienteEnderecoA.edtTipoDoEndereco.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.edtNomePropriedade.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.edtRua.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.edtNumero.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.edtBairro.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.edtCEP.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.edtComplemento.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.edtCxPostal.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.edtContato.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.cboEnderecoAtivo.setEnabled(this.podeAlterar);
        frmCadastroRapidoClienteEnderecoA.btnConfirmar.setEnabled(this.podeAlterar);
        if (!this.podeAlterar){
            frmCadastroRapidoClienteEnderecoA.btnConfirmar.setHint("Pode alterar cadastro. Acesso K0234");
        }
        FormUtil.doShow(frmCadastroRapidoClienteEnderecoA,
                t -> {
            if (frmCadastroRapidoClienteEnderecoA.isOk()) {
                this.tbClienteEnderecoInscricao.edit();
                this.tbClienteEnderecoInscricao.setRUA(frmCadastroRapidoClienteEnderecoA.edtRua.getValue().asString());
                this.tbClienteEnderecoInscricao.setFACHADA(frmCadastroRapidoClienteEnderecoA.edtNumero.getValue().asString());
                this.tbClienteEnderecoInscricao.setBAIRRO(frmCadastroRapidoClienteEnderecoA.edtBairro.getValue().asString());
                this.tbClienteEnderecoInscricao.setCEP(frmCadastroRapidoClienteEnderecoA.edtCEP.getValue().asString());
                this.tbClienteEnderecoInscricao.setUF(frmCadastroRapidoClienteEnderecoA.cboUF.getValue().asString());
                this.tbClienteEnderecoInscricao.setCOD_CIDADES(frmCadastroRapidoClienteEnderecoA.cboCidade.getValue().asDecimal());
                this.tbClienteEnderecoInscricao.setCOMPLEMENTO(frmCadastroRapidoClienteEnderecoA.edtComplemento.getValue().asString());
                this.tbClienteEnderecoInscricao.setNOME_PROPRIEDADE(frmCadastroRapidoClienteEnderecoA.edtNomePropriedade.getValue().asString());
                this.tbClienteEnderecoInscricao.setCX_POSTAL(frmCadastroRapidoClienteEnderecoA.edtCxPostal.getValue().asString());
                this.tbClienteEnderecoInscricao.setCONTATO(frmCadastroRapidoClienteEnderecoA.edtContato.getValue().asString());
                this.tbClienteEnderecoInscricao.setCIDADE(frmCadastroRapidoClienteEnderecoA.cboCidade.getText()
                        + "/"
                        + frmCadastroRapidoClienteEnderecoA.cboUF.getValue().asString());
                this.tbClienteEnderecoInscricao.setATIVO(frmCadastroRapidoClienteEnderecoA.cboEnderecoAtivo.getValue().asString());
                this.tbClienteEnderecoInscricao.post();
                try {
                    this.tbClienteEnderecoInscricao.applyUpdates();
                    this.tbClienteEnderecoInscricao.commitUpdates();
                } catch (DataException dataException) {
                    this.tbClienteEnderecoInscricao.cancelUpdates();
                    EmpresaUtil.showError("Falha ao salvar Motivo:",
                            dataException);
                }
            }
        });
    }


    private boolean buscaDadosClientePf(String documento, String inscricao) {
        boolean result = false;
        try {
            result = rn.buscaDadosClientePf(documento, inscricao);
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao buscarCliente. Motivo: ", ex);
        }
        return result;
    }

    @Override
    public void btnExibirDadosConsultaAPIIntegracaoClick(Event<Object> event) {
        this.consultarApiIntegracao();
    }

    @Override
    public void lblSituacaoCadSintegraClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    private void exibirSintegraMultiplasIe() {
        if (StringUtils.isNotBlank(this.lblSintegraMultiIe.getHint())) {
            EmpresaUtil.showInformationMessage("Inscrições Estaduais"
                    + System.lineSeparator()
                    + this.lblSintegraMultiIe.getHint().replace(",",
                    ("," + System.lineSeparator())));
        }
    }

    @Override
    public void lblSituacaoSintegraClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    @Override
    public void lblSintegraMultiIeClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    @Override
    public void tbClienteEnderecoInscricaoAfterScroll(Event<Object> event) {
        this.exibirStatusCadastro();
    }

    @Override
    public void cboClienteEhProdutorRuralChange(Event<Object> event) {
       String respFunc = this.rn.aplicarFlagProdutorRural();
       if (!respFunc.equals("S")){
           EmpresaUtil.showInformationMessage(respFunc);
       }
    }

    private void exibirStatusCadastro() {
        this.vBoxStatusCadastro.setVisible(false);
        if (!this.usaApiConsulta) {
            return;
        }
        double codClienteApi = this.tbClienteEnderecoInscricao.getCOD_CLIENTE().asDecimal();
        String tipoPessoa = this.tbClienteDiverso.getCLASSE().asString();
        if (codClienteApi == 0.0) {
            return;
        }
        this.vBoxStatusCadastro.setVisible(true);
        this.hBoxSituacaoCadReceitaFederal.setVisible(true);
        this.hBoxSituacaoCadSintegra.setVisible(true);
        this.lblSintegraMultiIe.setVisible(false);
        this.lblSituacaoCadReceitaFederal.setVisible(true);
        this.lblCadastroIrregular.setVisible(false);
        this.hBoxSituacaoCadSintegra.setVisible(false);
        this.lblSituacaoSintegra.setCaption(" -- ");
        this.lblSituacaoSintegra.setHint("");
        this.lblSituacaoReceitaFederal.setCaption(" -- ");
        this.lblSituacaoReceitaFederal.setHint("");
        this.lblSintegraMultiIe.setHint("");
        this.lblSituacaoCadSintegra.setHint("");
        Value aRfbSituacao = new Value(null);
        Value aRfbSituacaoMensagem = new Value(null);
        Value aSintegraSituacao = new Value(null);
        Value aSintegraMensagem = new Value(null);
        Value aSintegraCadastroIsento = new Value(null);
        Value aSintegraMultiplasIe = new Value(null);
        try {
            String respFunc = this.rn.infoSituacaoCadastralIntegracao(
                    this.codEmpresaUsuarioLogado
                    ,codClienteApi
                    ,tipoPessoa
                    ,this.tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString()
                    ,aRfbSituacao
                    ,aRfbSituacaoMensagem
                    ,aSintegraSituacao
                    ,aSintegraMensagem
                    ,aSintegraCadastroIsento
                    ,aSintegraMultiplasIe
            );
            if (!respFunc.equals("S")) {
                this.lblCadastroIrregular.setVisible(true);
            }
            this.lblSituacaoReceitaFederal.setHint(aRfbSituacaoMensagem.asString());
            this.lblSituacaoSintegra.setHint(aSintegraMensagem.asString());
            if (StringUtils.isNotBlank(aSintegraMultiplasIe.asString())) {
                boolean hBoxSituacaoCadSintegraVisible = this.hBoxSituacaoCadSintegra.isVisible();
                if (!hBoxSituacaoCadSintegraVisible) {
                    this.hBoxSituacaoCadSintegra.setVisible(true);
                }
                this.lblSintegraMultiIe.setVisible(true);
                this.lblSintegraMultiIe.setHint(aSintegraMultiplasIe.asString());
                this.lblSituacaoCadSintegra.setHint(
                        "Multiplas Ie"
                        + System.lineSeparator()
                        + aSintegraMultiplasIe.asString()
                );
            }
            this.lblSituacaoSintegra.setCaption(aSintegraSituacao.asString());
            this.lblSituacaoReceitaFederal.setCaption(aRfbSituacao.asString());
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao exibir o status do cadastro",
                    dataException);
        }
    }

    private void consultarApiIntegracao() {
        String cpfCnpj = this.tbClienteDiverso.getCNPJ_CPF().asString().replaceAll("\\D", "");
        String ie = this.tbClienteEnderecoInscricao.getINSCRICAO_ESTADUAL().asString();
        String uf = this.tbClienteEnderecoInscricao.getUF().asString();
        FrmDadosSintegraA frmDadosSintegraA = new FrmDadosSintegraA();
        frmDadosSintegraA.abreDadosConsultados(
                cpfCnpj
                ,ie
                ,uf
        );
        FormUtil.doShow(
                frmDadosSintegraA
                ,t -> this.exibirStatusCadastro()
        );
    }

    @Override
    public void btnReloadClick(Event<Object> event) {
        try {
            this.rn.refreshEnderecosPorInscricao(this.codCliente);
            boolean flagProdutorRural = this.rn.podeAlterarFlagProdutorRural(this.usuarioLogado);
            this.cboClienteEhProdutorRural.setEnabled(flagProdutorRural);
            this.habilitaBotoes();
        } catch (DataException dataException) {
            this.habilitaBotoes();
        }
    }
}
