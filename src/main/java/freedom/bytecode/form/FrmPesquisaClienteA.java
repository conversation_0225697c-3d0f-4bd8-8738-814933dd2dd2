package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmPesquisaClienteW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.zkoss.zul.Vlayout;
import org.zkoss.zul.Window;

public class FrmPesquisaClienteA extends FrmPesquisaClienteW {

    private static final long serialVersionUID = 20130827081850L;

    @Getter
    private String tipoEndereco = "1";

    @Getter
    private boolean fechadoAoAceitar = false;

    @Getter
    private String telefone = "";

    @Getter
    private String email = "";

    @Getter
    private long codMidia = 0L;

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private final int codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();

    private boolean enableEvento = true;

    @Setter
    private boolean checkExistEvento = true;

    private boolean validarLetra = false;

    private String letraCliente = "";

    private boolean verificaExistenciaEvento = true;

    @Setter
    private boolean aceitarSemValidarLeads = false;

    public void setValidarLetra(String letra) {
        this.validarLetra = true;
        this.letraCliente = letra;
        this.checkExistEvento = false;
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        this.fechadoAoAceitar = false;
        this.close();
    }

    public FrmPesquisaClienteA() {
        this.edtPesquisarCliente.setFocus();
    }

    private void carregarGrdEndereco(
            long codCliente
    ) {
        try {
            this.rn.carregarGrdEndereco(
                    codCliente
            );
            boolean tbLeadsEnderecoClienteEmpty = this.tbLeadsEnderecoCliente.isEmpty();
            if (tbLeadsEnderecoClienteEmpty) {
                String mensagem = (
                        "Nenhum endereço foi encontrado."
                );
                Dialog.create()
                        .showNotificationInfo(
                                mensagem
                                ,Constantes.MIDDLE_CENTER // A mensagem se sobrepõe à âncora, com a âncora e a mensagem alinhadas no centro-meio.
                                ,10000                    // Tempo em milissegundos para exibir a mensagem
                                ,this.grdEndereco         // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
            }
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de endereços do cliente"
                    ,dataException
            );
        }
    }

    private void filtrarEndereco(
            long codCliente
    ) {
        try {
            if (codCliente == 0.0) {
                return;
            }
            try {
                this.enableEvento = false;
                this.carregarGrdEndereco(
                        codCliente
                );
                this.tbLeadsEnderecoCliente.first();
                while (Boolean.FALSE.equals(this.tbLeadsEnderecoCliente.eof())) {
                    this.setChecked("N");
                    this.tbLeadsEnderecoCliente.next();
                }
                this.tbLeadsEnderecoCliente.first();
                if (this.tbLeadsConsultaClientes.getCOD_CLASSE().asString().equals("F")) {
                    if (this.tbLeadsEnderecoCliente.locate(Constantes.TIPO_ENDERECO, "Inscrição Estadual")) {
                        this.setChecked("S");
                    } else if (this.tbLeadsEnderecoCliente.locate(Constantes.TIPO_ENDERECO, Constantes.RESIDENCIAL)) {
                        this.setChecked("S");
                    } else if (this.tbLeadsEnderecoCliente.locate(Constantes.TIPO_ENDERECO, Constantes.COMERCIAL)) {
                        this.setChecked("S");
                    } else if (this.tbLeadsEnderecoCliente.locate(Constantes.TIPO_ENDERECO, "Cobrança")) {
                        this.setChecked("S");
                    } else {
                        this.tbLeadsEnderecoCliente.first();
                        this.setChecked("S");
                    }
                } else {
                    if (this.tbLeadsEnderecoCliente.locate(Constantes.TIPO_ENDERECO, Constantes.COMERCIAL)) {
                        this.setChecked("S");
                    } else if (this.tbLeadsEnderecoCliente.locate(Constantes.TIPO_ENDERECO, "Cobrança")) {
                        this.setChecked("S");
                    } else if (this.tbLeadsEnderecoCliente.locate(Constantes.TIPO_ENDERECO, Constantes.RESIDENCIAL)) {
                        this.setChecked("S");
                    } else {
                        this.tbLeadsEnderecoCliente.first();
                        this.setChecked("S");
                    }
                }
            } finally {
                this.enableEvento = true;
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao Filtrar Endereço",
                    dataException);
        }
    }

    private boolean informouTelefone(String telefone) {
        if (StringUtils.isBlank(telefone)) {
            return false;
        }
        if (telefone.equals("-")) {
            return false;
        }
        return (telefone.length() >= 8);
    }

    private void setChecked(String check) throws DataException {
        boolean tbLeadsEnderecoClienteNotEmpty = !this.tbLeadsEnderecoCliente.isEmpty();
        if (tbLeadsEnderecoClienteNotEmpty) {
            this.tbLeadsEnderecoCliente.edit();
            this.tbLeadsEnderecoCliente.setField("CHECKED", check);
            this.tbLeadsEnderecoCliente.post();
        }
    }

    private boolean existeEvento(double codCliente,
                                 double codEmpresa,
                                 String usuarioRespEvent) {
        boolean retFuncao = false;
        try {
            retFuncao = this.rn.existeEvento(codCliente,
                    codEmpresa,
                    usuarioRespEvent);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao verificar a existência de evento",
                    dataException);
        }
        return retFuncao;
    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        try {
            String lgpdExibeOptInOmiteOptOut = this.tbLeadsConsultaClientes.getLGPD_EXIBE_OPTIN_OMITE_OPTOUT().asString();
            if (lgpdExibeOptInOmiteOptOut.equals(Constantes.OPT_OUT)) {
                EmpresaUtil.showInformationMessage(Constantes.PROTECAO_LGPD_ESTE_CLIENTE_BLOQUEOU_O_ACESSO_AOS_SEUS_DADOS);
            } else {
                boolean tbLeadsConsultaClientesEmpty = this.tbLeadsConsultaClientes.isEmpty();
                if (tbLeadsConsultaClientesEmpty) {
                    EmpresaUtil.showInformationMessage("Selecione um cliente antes de aceitar.");
                    this.edtPesquisarCliente.setFocus();
                    return;
                }
                long codCliente = this.tbLeadsConsultaClientes.getCOD_CLIENTE().asLong();
                String observacaoVendaCliente = this.getObservacaoVendaCliente(codCliente);
                if (!observacaoVendaCliente.trim().isEmpty()) {
                    String mensagem = (
                            "Atenção à observação de venda do cliente:"
                                    + System.lineSeparator()
                                    + System.lineSeparator()
                                    + observacaoVendaCliente
                    );
                    EmpresaUtil.showInformationMessage(
                            mensagem
                    );
                }
                boolean tbLeadsEnderecoClienteEmpty = this.tbLeadsEnderecoCliente.isEmpty();
                if (tbLeadsEnderecoClienteEmpty) {
                    EmpresaUtil.showInformationMessage(
                            "Endereço não encontrado para o cliente selecionado."
                    );
                    this.edtPesquisarCliente.setFocus();
                    return;
                }
                this.tipoEndereco = this.tbLeadsEnderecoCliente.getCOD_TIPO_ENDERECO().asString();
                this.telefone = this.tbListFoneCliente.getField("FONE").asString().trim();
                this.email = this.tbLeadsConsultaClientes.getEMAIL().asString().trim();
                this.codMidia = this.tbLeadsConsultaClientes.getCOD_MIDIA().asLong();
                if (this.aceitarSemValidarLeads){
                    this.fechadoAoAceitar = true;
                    this.close();
                    return;
                }
                String foneAux = this.telefone;
                String dddAux = "";
                if (this.telefone.contains("-")) {
                    dddAux = this.telefone.substring(0, this.telefone.indexOf("-"));
                    foneAux = this.telefone.substring(this.telefone.indexOf("-") + 1);
                }
                if (dddAux.isEmpty()) {
                    EmpresaUtil.showInformationMessage("Telefone selecionado inválido. DDD não informado.");
                    this.edtPesquisarCliente.setFocus();
                    return;
                }
                if (dddAux.trim().length() < 2) {
                    EmpresaUtil.showInformationMessage("Telefone selecionado inválido. DDD inválido.");
                    this.edtPesquisarCliente.setFocus();
                    return;
                }
                if (foneAux.trim().length() < 8) {
                    EmpresaUtil.showInformationMessage("Telefone selecionado inválido.");
                    this.edtPesquisarCliente.setFocus();
                    return;
                }
                if (!this.telefone.isEmpty()) {
                    this.telefone = this.telefone.replaceAll("\\D", "");
                }
                if (this.telefone.trim().length() < 8) {
                    EmpresaUtil.showInformationMessage("O Telefone selecionado deve conter, no mínimo, 8 dígitos.");
                    this.edtPesquisarCliente.setFocus();
                    return;
                }
                if (this.validarLetra) {
                    this.tbClientesDescontos.close();
                    this.tbClientesDescontos.clearFilters();
                    this.tbClientesDescontos.clearParams();
                    this.tbClientesDescontos.setFilterCOD_CLIENTE(codCliente);
                    this.tbClientesDescontos.setFilterLETRA(this.letraCliente);
                    this.tbClientesDescontos.open();
                    boolean tbClientesDescontosNotEmpty = !this.tbClientesDescontos.isEmpty();
                    if (tbClientesDescontosNotEmpty) {
                        String mensagem = "A letra de desconto \""
                                + this.letraCliente
                                + "\" já está cadastrada para esse cliente.";
                        EmpresaUtil.showInformationMessage(mensagem);
                        return;
                    }
                }
                this.fechadoAoAceitar = true;
                this.close();
                boolean existeEvento = this.existeEvento(codCliente,
                        this.codEmpresaUsuarioLogado,
                        this.usuarioLogado);
                if (this.verificaExistenciaEvento
                        && existeEvento
                        && this.checkExistEvento) {
                    this.fechadoAoAceitar = false;
                    this.email = this.tbLeadsConsultaClientes.getEMAIL().asString();
                } else {
                    this.close();
                }
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao aceitar",
                    dataException);
        }
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        try {
            String textoPesquisado = this.edtPesquisarCliente.getValue().asString().trim();
            if (!textoPesquisado.isEmpty()) {
                this.pesquisar();
            } else {
                this.edtPesquisarCliente.setFocus();
            }
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao criar o formulário Pesquisa Cliente"
                    ,exception
            );
        }
    }

    @Override
    public void tbLeadsConsultaClientesAfterScroll(Event<Object> event) {
        try {
            long codClienteSelecionadoGridClientes = this.tbLeadsConsultaClientes.getCOD_CLIENTE().asLong();
            this.filtrarEndereco(
                    codClienteSelecionadoGridClientes
            );
            this.tbListFoneCliente.close();
            String telCel = this.tbLeadsConsultaClientes.getTEL_CEL_EXIBIR().asString().trim();
            if (informouTelefone(telCel)) {
                this.tbListFoneCliente.append();
                this.tbListFoneCliente.setField(Constantes.TIPO_FONE, "Celular");
                this.tbListFoneCliente.setField("FONE", telCel);
                this.tbListFoneCliente.post();
            }
            String telRes = this.tbLeadsConsultaClientes.getTEL_RES_EXIBIR().asString().trim();
            if (informouTelefone(telRes)) {
                this.tbListFoneCliente.append();
                this.tbListFoneCliente.setField(Constantes.TIPO_FONE, Constantes.RESIDENCIAL);
                this.tbListFoneCliente.setField("FONE", telRes);
                this.tbListFoneCliente.post();
            }
            String telCom = this.tbLeadsConsultaClientes.getTEL_COM_EXIBIR().asString().trim();
            if (informouTelefone(telCom)) {
                this.tbListFoneCliente.append();
                this.tbListFoneCliente.setField(Constantes.TIPO_FONE, Constantes.COMERCIAL);
                this.tbListFoneCliente.setField("FONE", telCom);
                this.tbListFoneCliente.post();
            }
            this.tbListFoneCliente.first();
            boolean tbListFoneClienteEmpty = this.tbListFoneCliente.isEmpty();
            if (tbListFoneClienteEmpty) {
                String mensagem = (
                        "Nenhum telefone foi encontrado."
                );
                Dialog.create()
                        .showNotificationInfo(
                                mensagem
                                ,Constantes.MIDDLE_CENTER // A mensagem se sobrepõe à âncora, com a âncora e a mensagem alinhadas no centro-meio.
                                ,10000                    // Tempo em milissegundos para exibir a mensagem
                                ,this.grdFoneCliente      // Nome do componente do formulário em frente ao qual a mensagem será exibida
                                ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                        );
            }
            boolean ehClienteEspecial = EmpresaUtil.isClienteEspecial(
                    codClienteSelecionadoGridClientes
            );
            boolean possuiAcessoK0235 = EmpresaUtil.validarAcesso(
                    "K0235"
                    ,false
            );
            this.btnFlagsEspeciais.setVisible(
                    ehClienteEspecial
                            && possuiAcessoK0235
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao navegar pelos registros da grade Cliente"
                    ,dataException
            );
        }
    }

    @Override
    public void edtPesquisarClienteEnter(Event<Object> event) {
        this.pesquisar();
    }

    private void carregarGrdClientes(
            String filtrarPor
    ) {
        try {
            this.rn.carregarGrdClientes(
                    filtrarPor
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de clientes"
                    ,dataException
            );
        }
    }

    public void pesquisar() {
        String nomeTelefoneEmail = this.edtPesquisarCliente.getValue().asString().trim();
        if (nomeTelefoneEmail.isEmpty()) {
            String mensagem = (
                    "O campo \""
                            + this.edtPesquisarCliente.getHelpCaption()
                            + "\" deve ser preenchido."
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // a mensagem aparece abaixo da âncora, alinhada ao centro
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtPesquisarCliente // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtPesquisarCliente.setFocus();
            return;
        }
        String docCPFOuCNPJ = StringUtil.removerCaracteresNaoNumericos(
                nomeTelefoneEmail
        );
        
        if ((docCPFOuCNPJ.length() == 11)
                || (docCPFOuCNPJ.length() == 14)) {
            this.carregarGrdClientes(
                    docCPFOuCNPJ
            );
            this.btnAceitar.setFocus();
        } else {
            this.carregarGrdClientes(
                    nomeTelefoneEmail
            );
            this.btnAceitar.setFocus();
        }
        boolean tbLeadsConsultaClientesEmpty = this.tbLeadsConsultaClientes.isEmpty();
        if (tbLeadsConsultaClientesEmpty) {
            Dialog.create()
                    .showNotificationInfo(
                            "Nenhum cliente foi encontrado."
                            ,Constantes.MIDDLE_CENTER // A mensagem se sobrepõe à âncora, com a âncora e a mensagem alinhadas no centro-meio
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.grdClientes         // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            Dialog.create()
                    .showNotificationInfo(
                            "Nenhum telefone foi encontrado."
                            ,Constantes.MIDDLE_CENTER // A mensagem se sobrepõe à âncora, com a âncora e a mensagem alinhadas no centro-meio
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.grdFoneCliente      // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            Dialog.create()
                    .showNotificationInfo(
                            "Nenhum endereço foi encontrado."
                            ,Constantes.MIDDLE_CENTER // A mensagem se sobrepõe à âncora, com a âncora e a mensagem alinhadas no centro-meio
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.grdEndereco         // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
        }
    }

    @Override
    public void FIconClassEditarClienteClick(Event<Object> event) {
        this.alterarFoneEmailCliente();
    }

    @Override
    public void btnPesquisarClick(final Event<Object> event) {
        this.pesquisar();
    }

    @Override
    public void tbLeadsEnderecoClienteAfterScroll(Event<Object> event) {
        try {
            if (this.enableEvento) {
                this.setChecked("S");
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao definir radioButton",
                    dataException);
        }
    }

    @Override
    public void tbLeadsEnderecoClienteBeforeScroll(Event<Object> event) {
        try {
            if (this.enableEvento) {
                this.setChecked("N");
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao definir radioButton",
                    dataException);
        }
    }

    @Override
    public void btnAlterarFoneEmailClick(Event<Object> event) {
        this.alterarFoneEmailCliente();
    }

    private void alterarFoneEmailCliente() {
        String lgpdExibeOptinOmiteOptout = this.tbLeadsConsultaClientes.getLGPD_EXIBE_OPTIN_OMITE_OPTOUT().asString();
        if (lgpdExibeOptinOmiteOptout.equals(Constantes.OPT_OUT)) {
            EmpresaUtil.showInformationMessage(Constantes.PROTECAO_LGPD_ESTE_CLIENTE_BLOQUEOU_O_ACESSO_AOS_SEUS_DADOS);
        } else {
            long codCliente = this.tbLeadsConsultaClientes.getCOD_CLIENTE().asLong();
            if (codCliente == 0) {
                EmpresaUtil.showInformationMessage(
                        Constantes.SELECIONE_UM_CLIENTE
                );
            } else {
                FrmAlterarClienteBasicoA frmAlterarClienteBasicoA = new FrmAlterarClienteBasicoA();
                frmAlterarClienteBasicoA.filtrarCliente(codCliente);
                FormUtil.doShow(frmAlterarClienteBasicoA,
                        t -> {
                            this.pesquisar();
                            long codCliente1 = frmAlterarClienteBasicoA.tbClientes.getCOD_CLIENTE().asLong();
                            this.tbLeadsConsultaClientes.locate(
                                    Constantes.COD_CLIENTE
                                    ,codCliente1
                            );
                        });
            }
        }
    }

    @Override
    public void iconClassEditarEnderecoClick(final Event<Object> event) {
        String lgpdExibeOptInOmiteOptOut = this.tbLeadsConsultaClientes.getLGPD_EXIBE_OPTIN_OMITE_OPTOUT().asString();
        if (lgpdExibeOptInOmiteOptOut.equals(Constantes.OPT_OUT)) {
            EmpresaUtil.showInformationMessage(Constantes.PROTECAO_LGPD_ESTE_CLIENTE_BLOQUEOU_O_ACESSO_AOS_SEUS_DADOS);
        } else {
            long codCliente = this.tbLeadsConsultaClientes.getCOD_CLIENTE().asLong();
            long tpEndereco = this.tbLeadsEnderecoCliente.getCOD_TIPO_ENDERECO().asLong();
            if (tpEndereco == 0) {
                EmpresaUtil.showInformationMessage("Nenhum endereço foi selecionado.");
            } else {
                FrmAlterarEnderecoClienteA frmAlterarEnderecoClienteA = new FrmAlterarEnderecoClienteA();
                frmAlterarEnderecoClienteA.filtrarEnderecoCliente(codCliente,
                        tpEndereco);
                FormUtil.doShow(frmAlterarEnderecoClienteA,
                        t -> {
                            this.filtrarEndereco(codCliente);
                            long codClienteL = frmAlterarEnderecoClienteA.tbAlteraEnderecoCliente.getCOD_CLIENTE().asLong();
                            this.tbLeadsConsultaClientes.locate(
                                    Constantes.COD_CLIENTE
                                    ,codClienteL
                            );
                            long tpEnderecoL = frmAlterarEnderecoClienteA.tbAlteraEnderecoCliente.getCOD_TIPO_ENDERECO().asLong();
                            this.tbLeadsEnderecoCliente.locate("COD_TIPO_ENDERECO", tpEnderecoL);
                        });
            }
        }
    }

    @Override
    public void btnLimiteClick(Event<Object> event) {
        long codCliente = this.tbLeadsConsultaClientes.getCOD_CLIENTE().asLong();
        boolean usaCreditoCorporativo = this.rn.usaCreditoCorporativo(this.codEmpresaUsuarioLogado);
        if (codCliente == 0) {
            EmpresaUtil.showInformationMessage(
                    Constantes.SELECIONE_UM_CLIENTE
            );
        } else if (usaCreditoCorporativo) {
            FrmIntegracaoSapA frmIntegracaoSapA = new FrmIntegracaoSapA();
            String respFunc = frmIntegracaoSapA.gerarFilaConsultaOrcamCreditoCorporativo(
                    codCliente
                    ,this.codEmpresaUsuarioLogado
                    ,0.0
                    ,"N"
                    ,"N"
                    ,"N"
                    ,this.usuarioLogado
            );
            if (respFunc.equals("S")) {
                FormUtil.doShow(frmIntegracaoSapA,
                        t -> {
                            frmIntegracaoSapA.timerSap.setEnabled(false);
                            String msgSap = frmIntegracaoSapA.getMensagemSap();
                            if (frmIntegracaoSapA.detalharRetornoCC()) {
                                this.exibirFrmResultCreditoCorporativoA(codCliente);
                            } else if (StringUtils.isNotBlank(msgSap)) {
                                Dialog.create()
                                        .title("Informação - Crédito Corporativo")
                                        .message(msgSap)
                                        .showInformation();
                            } else {
                                this.exibirFrmResultCreditoCorporativoA(codCliente);
                            }
                        });
                Window window = (Window) ((Vlayout) frmIntegracaoSapA.getImpl()).getParent();
                window.setClosable(false);
                window.setMaximizable(false);
            } else {
                EmpresaUtil.showInformationMessage("Não foi possível gerar fila de consulta Crédito Corporativo. " + respFunc);
            }
        } else {
            FrmLimiteFinanceiroA frmLimiteFinanceiroA = new FrmLimiteFinanceiroA();
            frmLimiteFinanceiroA.openLimiteCliente(codCliente);
            FormUtil.doShow(frmLimiteFinanceiroA,
                    t -> {
                    });
        }
    }

    private void exibirFrmResultCreditoCorporativoA(long codCliente) {
        FrmResultCreditoCorporativoA frmResultCreditoCorporativoA = new FrmResultCreditoCorporativoA((double) codCliente);
        FormUtil.doShow(frmResultCreditoCorporativoA,
                t2 -> {});
    }

    @Override
    public void btnFlagsEspeciaisClick(Event<Object> event) {
        long codCliente = this.tbLeadsConsultaClientes.getCOD_CLIENTE().asLong();
        if (codCliente == 0L) {
            EmpresaUtil.showInformationMessage(
                    Constantes.SELECIONE_UM_CLIENTE
            );
            return;
        }
        if (this.tbLeadsConsultaClientes.getLGPD_EXIBE_OPTIN_OMITE_OPTOUT().asString().equals(Constantes.OPT_OUT)) {
            EmpresaUtil.showInformationMessage(Constantes.PROTECAO_LGPD_ESTE_CLIENTE_BLOQUEOU_O_ACESSO_AOS_SEUS_DADOS);
        } else {
            FrmClientesFlagsA frmClientesFlagsA = new FrmClientesFlagsA();
            long codClienteSelecionadoNaGradeGridClientes = this.tbLeadsConsultaClientes.getCOD_CLIENTE().asLong();
            frmClientesFlagsA.setCodCliente(
                    codClienteSelecionadoNaGradeGridClientes
            );
            FormUtil.doShow(
                    frmClientesFlagsA
                    ,t -> {

                    });
        }
    }

    @Override
    public void btnNovoClienteClick(Event<Object> event) {
        FrmCadastroRapidoClienteU frmCadastroRapidoClienteU = new FrmCadastroRapidoClienteU();
        boolean frmCadastroRapidoClienteUBuscarCliente = frmCadastroRapidoClienteU.buscarCliente(
                0.0
        );
        if (frmCadastroRapidoClienteUBuscarCliente) {
            FormUtil.doShow(
                    frmCadastroRapidoClienteU
                    , t -> {
                        double codCliente = frmCadastroRapidoClienteU.getCodigoNovoCliente();
                        if (codCliente > 0.0) {
                            this.edtPesquisarCliente.setValue(
                                    String.format(
                                            "%.0f"
                                            ,codCliente
                                    )
                            );
                            this.edtPesquisarCliente.setFocus();
                            this.pesquisar();
                        }
                    });
        }
    }

    @Override
    public void btnAlterarClienteClick(Event<Object> event) {
        double codCliente = this.tbLeadsConsultaClientes.getCOD_CLIENTE().asDecimal();
        if (codCliente == 0.0) {
            EmpresaUtil.showInformationMessage(
                    Constantes.SELECIONE_UM_CLIENTE
            );
            return;
        }
        boolean possuiAcessoK0233 = EmpresaUtil.validarAcesso(
                "K0233"
                ,true
        );
        if (!possuiAcessoK0233) {
            return;
        }
        if (this.tbLeadsConsultaClientes.getLGPD_EXIBE_OPTIN_OMITE_OPTOUT().asString().equals(Constantes.OPT_OUT)) {
            EmpresaUtil.showInformationMessage(
                    Constantes.PROTECAO_LGPD_ESTE_CLIENTE_BLOQUEOU_O_ACESSO_AOS_SEUS_DADOS
            );
            return;
        }
        FrmCadastroRapidoClienteU frmCadastroRapidoClienteU = new FrmCadastroRapidoClienteU();
        boolean frmCadastroRapidoClienteUBuscaCliente = frmCadastroRapidoClienteU.buscarCliente(
                codCliente
        );
        if (frmCadastroRapidoClienteUBuscaCliente) {
            FormUtil.doShow(
                    frmCadastroRapidoClienteU
                    ,t -> {
                        boolean frmCadastroRapidoClienteUFechadoAoSalvar = frmCadastroRapidoClienteU.isFechadoAoSalvar();
                        if (frmCadastroRapidoClienteUFechadoAoSalvar) {
                            String textoPesquisado = this.edtPesquisarCliente.getValue().asString().trim();
                            this.carregarGrdClientes(
                                    textoPesquisado
                            );
                            String columnName = this.tbLeadsConsultaClientes.getCOD_CLIENTE().getFieldName();
                            this.tbLeadsConsultaClientes.locate(
                                    columnName
                                    ,(long) codCliente
                            );
                        }
                    });
        }
    }

    public void setVerificarExistenciaDeEvento(boolean verificaExistenciaEvento) {
        this.verificaExistenciaEvento = verificaExistenciaEvento;
    }

    @Override
    public void btnExcluirClienteClick(Event<Object> event) {
        long codCliente = this.tbLeadsConsultaClientes.getCOD_CLIENTE().asLong();
        String nomeCliente = this.tbLeadsConsultaClientes.getCLIENTE().asString();
        if (codCliente == 0L) {
            EmpresaUtil.showInformationMessage(
                    Constantes.SELECIONE_UM_CLIENTE
            );
            return;
        }
        boolean possuiAcessoK1038 = EmpresaUtil.validarAcesso(
                "K1038"
                ,true
        );
        if (!possuiAcessoK1038) {
            return;
        }
        if (codCliente > 0L) {
            String mensagem = "Deseja realmente excluir o cliente \""
                    + nomeCliente
                    + " ["
                    + codCliente
                    + "]\"?";
            Dialog.create()
                    .title("Confirmação")
                    // .message("Deseja realmente excluir o cliente "+nomeCliente + " [" + codCliente +"]? ")
                    .message(mensagem)
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES)
                        {
                            String retorno = this.excluirClienteSelecionado(codCliente);
                            if (retorno.equals("S"))
                            {
                                EmpresaUtil.showInformationMessage("Cliente excluído com sucesso.");
                                this.btnPesquisarClick(event);
                            }else{
                                EmpresaUtil.showInformationMessage(retorno);
                            }
                        }
                    });
        }
    }

    public String excluirClienteSelecionado(long codCliente) {
        String retFuncao = "";
        try {
            retFuncao = this.rn.excluirClienteSelecionado(codCliente);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao excluir o cliente selecionado",
                    dataException);
        }
        return retFuncao;
    }

    private String getObservacaoVendaCliente(double codCliente) {
        String retFuncao = "";
        try {
            retFuncao = this.rn.getObservacaoVendaCliente(codCliente);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao obter a observação da venda do cliente",
                    dataException);
        }
        return retFuncao;
    }

}