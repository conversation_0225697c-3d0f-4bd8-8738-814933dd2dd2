package freedom.bytecode.form;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.client.util.FormUtil;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.form.wizard.*;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

public class FrmLogsA extends FrmLogsW {
    private static final long serialVersionUID = 20130827081850L;

    SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss");

    public void openFrmLogs(double codCliente, int codEmpresaUser, boolean ehRodobens){
        try {
            rn.buscaLogsCliente(codCliente, codEmpresaUser);

            pgControlLogsCliente.selectTab(0);
            tabLogs.setVisible(true);
            tabOutros.setVisible(false);

            hBoxLogsDataConsultaCrivo.setVisible(ehRodobens);
            hBoxLogsValidadeConsultaCrivo.setVisible(ehRodobens);

            if(!tbCadRapClienteLogs.isEmpty()){
                Date ultimaAteracao = tbCadRapClienteLogs.getULTIMA_ALTERACAO().asDate();
                edtLogsUltimaAlteracao.setValue(dateFormat.format(ultimaAteracao));
            }
        } catch (DataException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        close();
    }

}
