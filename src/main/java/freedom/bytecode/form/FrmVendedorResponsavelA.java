package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmVendedorResponsavelW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.FreedomUtilities;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.*;

public class FrmVendedorResponsavelA extends FrmVendedorResponsavelW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String O_CAMPO = "O campo \"";

    private static final String DEVE_SER_PREENCHIDO = "\" deve ser preenchido.";

    private static final String INFORMACAO = "Informação";

    private final long codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private final long codCliente;

    private final String curva;

    private char modoInclusaoAlteracaoVisualizacao = 'V'; // Inclusão [I] / Alteração [A]

    private String codigosDasEmpresasDoUsuario;

    public FrmVendedorResponsavelA(long codCliente,
                                   String curva){
        this.codCliente = codCliente;
        this.curva = curva;
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        FRLogger.log("Formulário: "
                        + this.getName(),
                this.getClass());
        this.carregarCboEmpresa(this.usuarioLogado,
                this.codEmpresaUsuarioLogado);
        this.codigosDasEmpresasDoUsuario = this.getCodigosDasEmpresasDoUsuario();
        this.carregarCboVendedor(0L,
                0,
                codigosDasEmpresasDoUsuario);
        boolean ehCrmParts = EmpresaUtil.isCrmParts();
        if (ehCrmParts) {
            this.cboSistema.setValue(2); // Gold [1] / Parts [2] / Service [3]
            this.cboSistema.setEnabled(false);
            this.cboAltSistema.setEnabled(false);
        }
        this.carregarCboAltProcesso(2);
        int temperatura = this.getTemperatura(this.curva); // Super Quente [1] / Quente [2] / Morno [3] / Frio [4] / Bolsão [5]
        this.cboTemperatura.setValue(temperatura);
        String captionFormulario = "Vendedor responsável pelo cliente \""
                + ClienteDiversoUtil.getNomeCliente(this.codCliente)
                + " ["
                + this.codCliente
                + "]\"";
        this.setCaption(captionFormulario);
    }

    private int getTemperatura(String curva){
        int temperatura = 0;
        switch (curva){
            case "A":
                temperatura = 1;
                break;
            case "B":
                temperatura = 2;
                break;
            case "C":
                temperatura = 3;
                break;
            case "D":
                temperatura = 4;
                break;
            default:
                break;
        }
        return temperatura;
    }

    private void carregarCboVendedor(long codEmpresa,
                                     int processo,
                                     String codigosDasEmpresasDoUsuario) {
        try {
            this.rn.carregarCboVendedor(codEmpresa,
                    processo,
                    codigosDasEmpresasDoUsuario);
            int tbVendedoresListagemCount = this.tbVendedoresListagem.count();
            if (tbVendedoresListagemCount == 1) {
                String vendedor = this.tbVendedoresListagem.getNOME().asString();
                this.cboVendedor.setValue(vendedor);
            }
        } catch(DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar os vendedores da guia Listagem",
                    dataException);
        }
    }

    private void carregarCboAltVendedor(long codEmpresa,
                                        int processo,
                                        String codigosDasEmpresasDoUsuario) {
        try {
            this.cboAltVendedor.setEnabled((codEmpresa > 0L)
                    && (processo > 0));
            this.rn.carregarCboAltVendedor(codEmpresa,
                    processo,
                    codigosDasEmpresasDoUsuario);
            int tbVendedoresCadastroCount = this.tbVendedoresCadastro.count();
            if (tbVendedoresCadastroCount == 1) {
                String vendedor = this.tbVendedoresCadastro.getNOME().asString();
                this.cboAltVendedor.setValue(vendedor);
            }
        } catch(DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar os vendedores da guia Cadastro",
                    dataException);
        }
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

    @Override
    public void btnNovoClick(Event<Object> event) {
        this.modoInclusaoAlteracaoVisualizacao = 'I';
        this.tbsListagem.setVisible(false);
        this.tbsCadastro.setVisible(true);
        this.pgcVendedorResponsavel.selectTab(this.tbsCadastro);
        this.carregarCboAltEmpresa(this.usuarioLogado,
                this.codEmpresaUsuarioLogado);
        this.cboAltEmpresa.setFocus();
        this.cboAltEmpresa.setOpen(true);
        //region Definir enabled botões
        this.btnVoltar.setEnabled(true);
        this.btnPesquisar.setEnabled(false);
        this.btnNovo.setEnabled(false);
        this.btnAlterar.setEnabled(false);
        this.btnExcluir.setEnabled(false);
        this.btnSalvar.setEnabled(true);
        this.btnCancelar.setEnabled(true);
        //endregion
        int temperatura = this.getTemperatura(this.curva);
        this.cboAltTemperatura.setValue(temperatura); // Super Quente [1] / Quente [2] / Morno [3] / Frio [4] / Bolsão [5]
        boolean ehCrmParts = EmpresaUtil.isCrmParts();
        if (ehCrmParts) {
            this.cboAltSistema.setEnabled(false);
            this.cboAltSistema.setValue(2); // Gold [1] / Parts [2] / Service [3]
        }
    }

    @Override
    public void btnExcluirClick(Event<Object> event) {
        long codEmpresa2 = this.tbListaClienteResponsavel.getCOD_EMPRESA().asInteger();
        int sistema = this.tbListaClienteResponsavel.getSISTEMA().asInteger();
        int processo = this.tbListaClienteResponsavel.getPROCESSO().asInteger();
        String mensagem = "Deseja realmente excluir o registro selecionado?"
                + System.lineSeparator()
                + System.lineSeparator()
                + "Empresa: "
                + this.tbListaClienteResponsavel.getEMPRESAS_NOME_INITCAP_COD().asString()
                + System.lineSeparator()
                + System.lineSeparator()
                + "Sistema: "
                + this.tbListaClienteResponsavel.getSISTEMA_STR().asString()
                + System.lineSeparator()
                + System.lineSeparator()
                + "Processo: "
                + this.tbListaClienteResponsavel.getPROCESSO_STR().asString()
                + System.lineSeparator()
                + System.lineSeparator()
                + "Temperatura: "
                + this.tbListaClienteResponsavel.getTEMPERATURA_STR().asString()
                + System.lineSeparator()
                + System.lineSeparator()
                + "Vendedor: "
                + this.tbListaClienteResponsavel.getVENDEDOR().asString();
        Dialog.create()
                .title("Confirmação")
                .message(mensagem)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        this.excluirClienteResponsavel(codEmpresa2,
                                this.codCliente,
                                sistema,
                                processo);
                        this.btnPesquisarClick(event);
                    }
                });
    }

    public void excluirClienteResponsavel(long codEmpresa,
                                          long codCliente,
                                          int sistema,
                                          int processo){
        try {
            this.rn.excluirClienteResponsavel(codEmpresa,
                    codCliente,
                    sistema,
                    processo);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Falha ao deletar registro.",
                    dataException);
        }
    }

    private boolean temVendedores(long codEmpresa,
                                  long codCliente,
                                  int sistema,
                                  int processo) {
        boolean retFuncao = false;
        try {
            retFuncao = this.rn.temVendedores(codEmpresa,
                    codCliente,
                    sistema,
                    processo);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao verificar se tem vendedores",
                    dataException);
        }
        return retFuncao;
    }

    public void inserirClienteResponsavel(long codEmpresa,
                                          long codCliente,
                                          int sistema,
                                          int processo,
                                          int temperatura,
                                          String responsavel) {
        try {
            this.rn.inserirClienteResponsavel(codEmpresa,
                    codCliente,
                    sistema,
                    processo,
                    temperatura,
                    responsavel);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao inserir cliente responsável",
                    dataException);
        }
    }

    public void alterarClienteResponsavel(long codEmpresa,
                                          long codCliente,
                                          int sistema,
                                          int processo,
                                          int temperatura,
                                          String responsavel) {
        try {
            this.rn.alterarClienteResponsavel(codEmpresa,
                    codCliente,
                    sistema,
                    processo,
                    temperatura,
                    responsavel);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao alterar cliente responsável",
                    dataException);
        }
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        if (this.modoInclusaoAlteracaoVisualizacao == 'I') {
            //region Validar preenchimento
            //region Empresa
            long codEmpresa2 = this.cboAltEmpresa.getValue().asLong();
            if (codEmpresa2 == 0L) {
                String mensagem = FrmVendedorResponsavelA.O_CAMPO
                        + this.lblAltEmpresa.getCaption()
                        + FrmVendedorResponsavelA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .title(FrmVendedorResponsavelA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                            this.cboAltEmpresa.setFocus();
                            this.cboAltEmpresa.setOpen(true);
                        }));
                return;
            }
            //endregion
            //region Sistema
            int sistema = this.cboAltSistema.getValue().asInteger();
            if (sistema == 0) {
                String mensagem = FrmVendedorResponsavelA.O_CAMPO
                        + this.lblAltSistema.getCaption()
                        + FrmVendedorResponsavelA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .title(FrmVendedorResponsavelA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                            this.cboAltSistema.setFocus();
                            this.cboAltSistema.setOpen(true);
                        }));
                return;
            }
            //endregion
            //region Processo
            int processo = this.cboAltProcesso.getValue().asInteger();
            if (processo == 0) {
                String mensagem = FrmVendedorResponsavelA.O_CAMPO
                        + this.lblAltProcesso.getCaption()
                        + FrmVendedorResponsavelA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .title(FrmVendedorResponsavelA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                            this.cboAltProcesso.setFocus();
                            this.cboAltProcesso.setOpen(true);
                        }));
                return;
            }
            //endregion
            //region Temperatura
            int temperatura = this.cboAltTemperatura.getValue().asInteger();
            if (temperatura == 0) {
                String mensagem = FrmVendedorResponsavelA.O_CAMPO
                        + this.lblAltTemperatura.getCaption()
                        + FrmVendedorResponsavelA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .title(FrmVendedorResponsavelA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                            this.cboAltTemperatura.setFocus();
                            this.cboAltTemperatura.setOpen(true);
                        }));
                return;
            }
            //endregion
            //region Vendedor
            String vendedor = this.cboAltVendedor.getValue().asString();
            if (vendedor.isEmpty()) {
                String mensagem = FrmVendedorResponsavelA.O_CAMPO
                        + this.lblAltvendedor.getCaption()
                        + FrmVendedorResponsavelA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .title(FrmVendedorResponsavelA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                            this.cboAltVendedor.setFocus();
                            this.cboAltVendedor.setOpen(true);
                        }));
                return;
            }
            //endregion
            //endregion
            boolean temVendedor = this.temVendedores(codEmpresa2,
                    this.codCliente,
                    sistema,
                    processo);
            if (temVendedor) {
                String mensagem = "Já há registro para:"
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "Empresa: "
                        + this.cboAltEmpresa.getText()
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "Sistema: "
                        + this.cboAltSistema.getText()
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "Processo: "
                        + this.cboAltProcesso.getText();
                EmpresaUtil.showInformationMessage(mensagem);
                return;
            }
            this.inserirClienteResponsavel(codEmpresa2,
                    this.codCliente,
                    sistema,
                    processo,
                    temperatura,
                    vendedor);
            EmpresaUtil.showInformationMessage("Cadastro realizado com sucesso.");
        }
        if (this.modoInclusaoAlteracaoVisualizacao == 'A') {
            long codEmpresa2 = this.cboAltEmpresa.getValue().asInteger();
            int sistema = this.cboAltSistema.getValue().asInteger();
            int processo = this.cboAltProcesso.getValue().asInteger();
            int temperatura = this.cboAltTemperatura.getValue().asInteger();
            String responsavel = this.cboAltVendedor.getValue().asString();
            this.alterarClienteResponsavel(codEmpresa2,
                    this.codCliente,
                    sistema,
                    processo,
                    temperatura,
                    responsavel);
            EmpresaUtil.showInformationMessage("Alteração realizada com sucesso.");
        }
        this.tbsListagem.setVisible(true);
        this.tbsCadastro.setVisible(false);
        this.pgcVendedorResponsavel.selectTab(this.tbsListagem);
        //region Definir enabled botões
        this.btnVoltar.setEnabled(true);
        this.btnPesquisar.setEnabled(true);
        this.btnNovo.setEnabled(true);
        this.btnAlterar.setEnabled(true);
        this.btnExcluir.setEnabled(true);
        this.btnSalvar.setEnabled(false);
        this.btnCancelar.setEnabled(false);
        //endregion
        this.modoInclusaoAlteracaoVisualizacao = 'V';
        this.btnPesquisarClick(event);
    }

    private void carregarGridVendedores(long codCliente,
                                        String codigosDasEmpresasDoUsuario,
                                        long codEmpresaFiltrada,
                                        int codSistemaFiltrado,
                                        int codProcessoFiltrado,
                                        int codTemperaturaFiltrada,
                                        String loginVendedorFiltrado) {
        try {
            this.rn.carregarGridVendedores(codCliente,
                    codigosDasEmpresasDoUsuario,
                    codEmpresaFiltrada,
                    codSistemaFiltrado,
                    codProcessoFiltrado,
                    codTemperaturaFiltrada,
                    loginVendedorFiltrado);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Falha ao listar os vendedores.",
                    dataException);
        }
    }

    @Override
    public void btnCancelarClick(Event<Object> event) {
        this.tbsListagem.setVisible(true);
        this.tbsCadastro.setVisible(false);
        this.pgcVendedorResponsavel.selectTab(this.tbsListagem);
        //region Definir enabled botões
        this.btnVoltar.setEnabled(true);
        this.btnPesquisar.setEnabled(true);
        this.btnNovo.setEnabled(true);
        this.btnAlterar.setEnabled(true);
        this.btnExcluir.setEnabled(true);
        this.btnSalvar.setEnabled(false);
        this.btnCancelar.setEnabled(false);
        //endregion
        this.modoInclusaoAlteracaoVisualizacao = 'V';
    }

    @Override
    public void cboEmpresaChange(Event<Object> event) {
       long codEmpresa2 = this.cboEmpresa.getValue().asLong();
       int processo = this.cboProcesso.getValue().asInteger();
       this.carregarCboVendedor(codEmpresa2,
               processo,
               this.codigosDasEmpresasDoUsuario);
    }

    @Override
    public void cboSistemaChange(Event<Object> event) {
        this.cboProcesso.clear();
        int sistema = this.cboSistema.getValue().asInteger();
        this.carregarCboAltProcesso(sistema);
        this.cboProcesso.setFocus();
        this.cboProcesso.setOpen(true);
        this.cboEmpresaChange(event);
    }

    private void carregarCboAltProcesso(int sistema){
        String listOptionsParts = "Vendedor Interno [4]=4;Vendedor Externo [5]=5;Vendedor Pneu [6]=6";
        String listOptionsGoldService = "Caminhão [1]=1;Vans [2]=2;Ônibus [3]=3;Vendedor Interno [4]=4;Vendedor Externo [5]=5";
        switch (sistema) {
            case 1:
            case 3:
                this.cboAltProcesso.setListOptions(listOptionsGoldService);
                break;
            case 2:
                this.cboAltProcesso.setListOptions(listOptionsParts);
                break;
            default:
                break;
        }
    }

    private void carregarCboEmpresa(String loginUsuario,
                                    long codEmpresa) {
        try {
            this.rn.carregarCboEmpresa(loginUsuario,
                    codEmpresa);
            int tbEmpresasListagemCount = this.tbEmpresasListagem.count();
            if (tbEmpresasListagemCount == 1) {
                long codEmpresaPrimeiroRegistro = this.tbEmpresasListagem.getCOD_EMPRESA().asLong();
                this.cboEmpresa.setValue(codEmpresaPrimeiroRegistro);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar as empresas da guia Listagem",
                    dataException);
        }
    }

    private void carregarCboAltEmpresa(String loginUsuario,
                                       long codEmpresa) {
        try {
            this.rn.carregarCboAltEmpresa(loginUsuario,
                    codEmpresa);
            int tbEmpresasCadastroCount = this.tbEmpresasCadastro.count();
            if (tbEmpresasCadastroCount == 1) {
                long codEmpresaPrimeiroRegistro = this.tbEmpresasCadastro.getCOD_EMPRESA().asLong();
                this.cboAltEmpresa.setValue(codEmpresaPrimeiroRegistro);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao carregar as empresas da guia Cadastro",
                    dataException);
        }
    }

    private String getCodigosDasEmpresasDoUsuario() {
        String retFuncao = "";
        try {
            retFuncao = this.rn.getCodigosDasEmpresasDoUsuario();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao obter os códigos das empresas do usuário",
                    dataException);
        }
        return retFuncao;
    }

    @Override
    public void btnPesquisarClick(Event<Object> event) {
        long codEmpresaFiltrada = this.cboEmpresa.getValue().asLong();
        int codSistemaFiltrado =  this.cboSistema.getValue().asInteger();
        int codProcessoFiltrado = this.cboProcesso.getValue().asInteger();
        int codTemperaturaFiltrada = this.cboTemperatura.getValue().asInteger();
        String loginVendedorFiltrado = this.cboVendedor.getValue().asString();
        this.carregarGridVendedores(this.codCliente,
                this.codigosDasEmpresasDoUsuario,
                codEmpresaFiltrada,
                codSistemaFiltrado,
                codProcessoFiltrado,
                codTemperaturaFiltrada,
                loginVendedorFiltrado);
        boolean tbListaClienteResponsavelNotEmpty = !this.tbListaClienteResponsavel.isEmpty();
        this.btnAlterar.setEnabled(tbListaClienteResponsavelNotEmpty);
        this.btnExcluir.setEnabled(tbListaClienteResponsavelNotEmpty);
    }

    @Override
    public void tbEmpresasCadastroAfterScroll(Event<Object> event) {
        long codEmpresa2 = this.cboAltEmpresa.getValue().asLong();
        int processo = this.cboAltProcesso.getValue().asInteger();
        this.carregarCboAltVendedor(codEmpresa2,
                processo,
                this.codigosDasEmpresasDoUsuario);
    }

    @Override
    public void btnAlterarClick(Event<Object> event) {
        this.tbsListagem.setVisible(false);
        this.tbsCadastro.setVisible(true);
        this.pgcVendedorResponsavel.selectTab(this.tbsCadastro);
        this.modoInclusaoAlteracaoVisualizacao = 'A';
        //region Definir enabled botões
        this.btnVoltar.setEnabled(true);
        this.btnPesquisar.setEnabled(false);
        this.btnNovo.setEnabled(false);
        this.btnAlterar.setEnabled(false);
        this.btnExcluir.setEnabled(false);
        this.btnSalvar.setEnabled(true);
        this.btnCancelar.setEnabled(true);
        //endregion
        boolean ehCrmParts = EmpresaUtil.isCrmParts();
        this.cboSistema.setEnabled(!ehCrmParts);
        //region Empresa
        this.carregarCboAltEmpresa(this.usuarioLogado,
                this.codEmpresaUsuarioLogado);
        long codEmpresaSelecionada = this.tbListaClienteResponsavel.getCOD_EMPRESA().asLong();
        this.cboAltEmpresa.setValue(codEmpresaSelecionada);
        this.cboAltEmpresa.setEnabled(false);
        //endregion
        //region Sistema
        int codSistemaSelecionado = this.tbListaClienteResponsavel.getSISTEMA().asInteger();
        this.cboAltSistema.setValue(codSistemaSelecionado);
        //endregion
        //region Processo
        int codProcessoSelecionado = this.tbListaClienteResponsavel.getPROCESSO().asInteger();
        this.cboAltProcesso.setValue(codProcessoSelecionado);
        this.cboAltProcesso.setEnabled(false);
        //endregion
        //region Temperatura
        int codTemperaturaSelecionada = this.tbListaClienteResponsavel.getTEMPERATURA().asInteger();
        this.cboAltTemperatura.setValue(codTemperaturaSelecionada);
        //endregion
        //region Vendedor
        this.carregarCboAltVendedor(codEmpresaSelecionada,
                codProcessoSelecionado,
                this.codigosDasEmpresasDoUsuario);
        String loginVendedorSelecionado = this.tbListaClienteResponsavel.getVENDEDOR_LOGIN().asString();
        this.cboAltVendedor.setValue(loginVendedorSelecionado);
        //endregion
    }

    @Override
    public void iconClassHelpClick(Event<Object> event) {
        FormUtil.redirect("https://ajuda.nbsi.com.br:84/index.php/CRM_Parts_-_Clientes_-_Vendedor_Respons%C3%A1vel",
                true);
    }

}