package freedom.bytecode.form;

import freedom.bytecode.cursor.EMPRESAS_USUARIOS;
import freedom.bytecode.form.wizard.FrmPerfilW;
import freedom.client.event.Event;
import freedom.client.event.UploadEvent;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.FileUpload;
import freedom.util.ImageUtil;
import freedom.util.file.ImagesUtil;

public class FrmPerfilA extends FrmPerfilW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String ERRO_AO_SALVAR_A_ASSINATURA = "Erro ao salvar a assinatura";

    private final String loginUsuario;

    private final String loginUsuarioLogado = EmpresaUtil.getUserLogged();

    private byte[] imageBytes;

    public FrmPerfilA() {
        this.loginUsuario = EmpresaUtil.getUserLogged();
        this.filtrarPerfilUsuario();
        this.controlAssinatura();
        this.initForm(this.loginUsuarioLogado);
    }

    public FrmPerfilA(String loginUsuario) {
        this.loginUsuario = loginUsuario;
        this.filtrarPerfilUsuario();
        this.controlAssinatura();
        this.initForm(loginUsuario);
    }

    private void filtrarPerfilUsuario() {
        try {
            this.rn.filtrarUsuarioPerfil(this.loginUsuario);
            this.cmbSetorVenda.setValue(this.tbPerfil.getCOD_SETOR());
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao consultar Perfil",
                    dataException);
        }
    }

    @Override
    public void lblImagePerfilClick(final Event<Object> event) {
        FileUpload.get("Upload Imagem Perfil",
                "Upload",
                (UploadEvent t) -> {
            byte[] imageBytes2 = ImageUtil.streamToByteArray(t.getStreamData());
            this.tbPerfil.edit();
            this.tbPerfil.setFOTO(imageBytes2);
            this.tbPerfil.setFOTO_ICONE(
                    this.converterMiniatura(
                            imageBytes2
                            ,t.getFileName()
                    )
            );
            this.tbPerfil.post();
            this.rn.salvarFoto(this.loginUsuario);
        });
    }

    @Override
    public void lblNomeCompletoClick(final Event<Object> event) {
        try {
            this.tbPerfil.edit();
            this.tbPerfil.setFOTO(null);
            this.tbPerfil.setFOTO_ICONE(null);
            this.tbPerfil.post();
            this.rn.removerFoto(this.loginUsuario);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao salvar o perfil",
                    dataException);
        }
    }

    private byte[] converterMiniatura(
            byte[] imageBytes
            ,String fileName
            ) throws Exception {
        ImagesUtil imagesUtil = new ImagesUtil();
        return imagesUtil.converterMiniatura(
                imageBytes
                ,fileName
                ,200
        );
    }

    @Override
    public void iconAltTabPrecoClick(final Event<Object> event) {
        if (!EmpresaUtil.validarAcesso("K0203")) {
            return;
        }
        FrmPerfilTabPrecoA frmPerfilTabPrecoA = new FrmPerfilTabPrecoA();
        if (frmPerfilTabPrecoA.podeAlterarTabPreco()) {
            FormUtil.doShow(frmPerfilTabPrecoA,
                    t -> this.filtrarPerfilUsuario());
        }
    }

    @Override
    public void cmbSetorVendaChange(Event<Object> event) {
        try {
            EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("eu");
            tbEmpresasUsuarios.setFilterNOME(this.loginUsuario);
            tbEmpresasUsuarios.open();
            tbEmpresasUsuarios.edit();
            tbEmpresasUsuarios.setCOD_SETOR(this.tbSetorVenda.getCOD_SETOR().asInteger());
            tbEmpresasUsuarios.post();
            tbEmpresasUsuarios.applyUpdates();
            tbEmpresasUsuarios.commitUpdates();
        } catch (Exception exception) {
            EmpresaUtil.showError("Erro ao alterar o setor da venda",
                    exception);
        }
    }

    @Override
    public void icoEditarEmailClick(final Event<Object> event) {
        if (EmpresaUtil.isCrmService()) {
            if (!EmpresaUtil.validarAcesso("B3106")) {
                return;
            }
        } else {
            if (!EmpresaUtil.validarAcesso("K0204")) {
                return;
            }
        }
        FrmAlterarDadosEmailA frmAlterarDadosEmailA = new FrmAlterarDadosEmailA();
        frmAlterarDadosEmailA.filtrarEmpresasUsuarios(this.loginUsuario);
        FormUtil.doModal(frmAlterarDadosEmailA,
                t -> {
            boolean frmAlterarDadosEmailAOk = frmAlterarDadosEmailA.isOk();
            if (frmAlterarDadosEmailAOk) {
                this.filtrarPerfilUsuario();
            }
        });
    }

    @Override
    public void signatureSave(UploadEvent event) {
        try {
            this.imageBytes = ImageUtil.streamToByteArray(event.getStreamData());
            this.salvarAssinaturaPerfil();
            this.controlAssinatura();
        } catch (Exception exception) {
            EmpresaUtil.showError(ERRO_AO_SALVAR_A_ASSINATURA,
                    exception);
        }
    }

    @Override
    public void signatureClear(Event<Object> event) {
        this.imageBytes = null;
    }

    @Override
    public void btnDesfazerClick(final Event<Object> event) {
        this.signature.undo();
    }

    @Override
    public void btnLimparClick(final Event<Object> event) {
        boolean tbEmpresasUsuariosAssinaturaNotNull = !this.tbEmpresasUsuarios.getASSINATURA().isNull();
        if (tbEmpresasUsuariosAssinaturaNotNull) {
            this.imageBytes = null;
            this.salvarAssinaturaPerfil();
            this.controlAssinatura();
        } else {
            this.signature.clear();
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        this.signature.save();
    }

    private void salvarAssinaturaPerfil() {
        try {
            this.rn.salvarAssinaturaPerfil(this.imageBytes);
        } catch (Exception exception) {
            EmpresaUtil.showError(ERRO_AO_SALVAR_A_ASSINATURA,
                    exception);
        }
    }

    private void controlAssinatura() {
        this.carregarAssinatura();
        boolean tbEmpresasUsuariosASSINATURANull = this.tbEmpresasUsuarios.getASSINATURA().isNull();
        if (tbEmpresasUsuariosASSINATURANull) {
            this.signature.setVisible(true);
            this.vBoxImgSignatrueChk.setVisible(false);
            this.btnDesfazer.setVisible(true);
            this.btnSalvar.setVisible(true);
        } else {
            this.signature.setVisible(false);
            this.vBoxImgSignatrueChk.setVisible(true);
            this.btnDesfazer.setVisible(false);
            this.btnSalvar.setVisible(false);
        }
    }

    private void carregarAssinatura() {
        try {
            this.rn.carregarAssinatura(this.loginUsuario);
        } catch (Exception exception) {
            EmpresaUtil.showError(ERRO_AO_SALVAR_A_ASSINATURA,
                    exception);
        }
    }

    @Override
    public void cmbLocalEstoqueChange(final Event<Object> event) {
        try {
            this.tbEmpresasUsuarios.edit();
            this.tbEmpresasUsuarios.post();
            this.tbEmpresasUsuarios.applyUpdates();
            this.tbEmpresasUsuarios.commitUpdates();
        } catch (DataException dataException) {
            EmpresaUtil.showError("OPS! Ocorreu um erro inesperado",
                    dataException);
        }
    }

    public void buscarLocalEstoque(double codEmpresa) {
        try {
            this.rn.openLocalEstoque(codEmpresa);
            if (!this.tbEmpresasUsuarios.getCOD_LOCAL_ESTOQUE().isEmpty()) {
                int codLocalEstoque = this.tbEmpresasUsuarios.getCOD_LOCAL_ESTOQUE().asInteger();
                this.cmbLocalEstoque.setValue(codLocalEstoque);
            } else {
                this.cmbLocalEstoque.setValue(null);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao consultar o local de estoque",
                    dataException);
        }
    }

    @Override
    public void cmbSegmentoChange(final Event<Object> event) {
        try {
            EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("tbEmpresasUsuarios");
            tbEmpresasUsuarios.setFilterNOME(this.loginUsuario);
            tbEmpresasUsuarios.open();
            tbEmpresasUsuarios.edit();
            tbEmpresasUsuarios.setCOD_SEGMENTO(this.tbProdutoSegmento.getCOD_SEGMENTO().asInteger());
            tbEmpresasUsuarios.post();
            tbEmpresasUsuarios.applyUpdates();
            tbEmpresasUsuarios.commitUpdates();
        } catch (Exception exception) {
            EmpresaUtil.showError("Erro ao alterar o segmento",
                    exception);
        }
    }

    public void initForm(String loginUsuario){
        this.filtrarPerfilUsuario();
        this.controlAssinatura();
        this.buscarLocalEstoque(tbEmpresasUsuarios.getCOD_EMPRESA().asDecimal());
        this.vBoxAssinaturaUsadaRecepcao.setVisible(this.loginUsuarioLogado.equals(loginUsuario));
        this.btnDesfazer.setVisible(this.loginUsuarioLogado.equals(loginUsuario));
        this.btnLimpar.setVisible(this.loginUsuarioLogado.equals(loginUsuario));
        this.btnSalvar.setVisible(this.loginUsuarioLogado.equals(loginUsuario));
        boolean possuiAcessoQuePermiteAlterarDadosGeral;
        String hintAcessoPermiteAlterarDadosGeral;
        if (EmpresaUtil.isCrmService()) {
            possuiAcessoQuePermiteAlterarDadosGeral = EmpresaUtil.validarAcesso(
                    "B3103"
                    ,false
            );
            hintAcessoPermiteAlterarDadosGeral = "Acesso B3103";
        } else {
            possuiAcessoQuePermiteAlterarDadosGeral = EmpresaUtil.validarAcesso(
                    "K0203"
                    ,false
            );
            hintAcessoPermiteAlterarDadosGeral = "Acesso K0203";
        }
        this.cmbSetorVenda.setEnabled(possuiAcessoQuePermiteAlterarDadosGeral);
        this.cmbSegmento.setEnabled(possuiAcessoQuePermiteAlterarDadosGeral);
        this.cmbLocalEstoque.setEnabled(possuiAcessoQuePermiteAlterarDadosGeral);
        this.cmbSetorVenda.setHint(hintAcessoPermiteAlterarDadosGeral);
        this.cmbSegmento.setHint(hintAcessoPermiteAlterarDadosGeral);
        this.cmbLocalEstoque.setHint(hintAcessoPermiteAlterarDadosGeral);
    }
}