package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmSelecaoMultiplaW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class
FrmSelecaoMultiplaA extends FrmSelecaoMultiplaW {

    private static final long serialVersionUID = 20130827081850L;

    private String[] vetorDeCodigosDosRegistrosSelecionadosNaGrade;

    private String codigosDosRegistrosSelecionadosNaGradeSeparadosPorVirgulaEEspaco;

    private boolean fechadoAoAceitar = false;

    public boolean isFechadoAoAceitar() {
        return this.fechadoAoAceitar;
    }

    public String[] getVetorDeCodigosDosRegistrosSelecionadosNaGrade() {
        return this.vetorDeCodigosDosRegistrosSelecionadosNaGrade;
    }

    public String getCodigosDosRegistrosSelecionadosNaGradeSeparadosPorVirgulaEEspaco() {
        return this.codigosDosRegistrosSelecionadosNaGradeSeparadosPorVirgulaEEspaco;
    }

    public FrmSelecaoMultiplaA(String captionFormulario) {
        this.setCaption(captionFormulario);
    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        try {
            //region Contar registros selecionados na grade para declarar o vetor de retorno de dados com esse tamanho
            int numeroDeRegistrosSelecionadosNaGrade = this.rn.getNumeroDeRegistrosSelecionadosNaGrade();
            if (numeroDeRegistrosSelecionadosNaGrade == 0) {
                EmpresaUtil.showInformationMessage("Selecione algum registro.");
                return;
            }
            //endregion
            //region Declarar o vetor de retorno
            this.vetorDeCodigosDosRegistrosSelecionadosNaGrade = new String[numeroDeRegistrosSelecionadosNaGrade];
            //endregion
            //region Carregar o vetor com os registros selecionados na grade
            this.vetorDeCodigosDosRegistrosSelecionadosNaGrade = this.rn.getVetorDeStringContendoOsCodigosDosRegistrosSelecionadosNaGrade();
            this.codigosDosRegistrosSelecionadosNaGradeSeparadosPorVirgulaEEspaco = this.rn.getStringContendoOsCodigosDosRegistrosSelecionadosNaGradeSeparadosPorVirgulaEEspaco();
            //endregion
            this.fechadoAoAceitar = true;
            this.close();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao clicar em Aceitar",
                    dataException);
        }
    }

    @Override
    public void gridSelecaoMultipladesmarcarRegistroNaGridSelecaoMultipla(Event<Object> event) {
        try {
            this.rn.desmarcarRegistroDaGrade();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao desmarcar registro na grade Seleção Múltipla",
                    dataException);
        }
    }

    @Override
    public void gridSelecaoMultiplamarcarRegistroNaGridSelecaoMultipla(Event<Object> event) {
        try {
            this.rn.marcarRegistroDaGrade();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao marcar registro na grade Seleção Múltipla",
                            dataException);
        }
    }

    @Override
    public void mmSelecionarTodosOsRegistrosGridSelecaoMultiplaClick(Event<Object> event) {
        try {
            this.rn.selecionarTodosOsRegistrosDaGrade();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao selecionar todos os registros na grade Seleção Múltipla",
                    dataException);
        }
    }

    @Override
    public void mmSelecionarNenhumRegistroGridSelecaoMultiplaClick(Event<Object> event) {
        try {
            this.rn.selecionarNenhumRegistroDaGrade();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao selecionar todos os registros na grade Seleção Múltipla",
                    dataException);
        }
    }

    @Override
    public void btnFecharClick(Event<Object> event) {
        this.close();
    }

}
