package freedom.bytecode.form;
import freedom.bytecode.form.wizard.*;
import freedom.util.EmpresaUtil;
import freedom.util.ImageUtil;
import org.zkoss.zkmax.zul.Video;

import freedom.client.event.*;
import freedom.client.controls.impl.*;
import org.zkoss.zul.Vlayout;

import java.io.IOException;

public class FrmVideoSnapShotA extends FrmVideoSnapShotW {
    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;
    private byte[] videoBytes;
    private String nameFile;
    private int contador =0;
    private Video player = new Video();

    public String getNameFile() {
        return nameFile;
    }

    public byte[] getImageBytes() {
        return videoBytes;
    }

    public boolean isOk() {
        return ok;
    }

    @Override
    public void videoSnapshotUpload(UploadEvent event) {
        video.setVisible(false);

        try {
            videoBytes = ImageUtil.streamToByteArray(event.getStreamData());
            nameFile = event.getFileName();


            videoPreview.setVisible(true);
            loadVideo(videoBytes);
            vBoxCancel.setVisible(true);
            iconCancel.setVisible(true);
            iconOk.setVisible(true);
            buttonCapturarVideo.setVisible(false);
            iconStop.setVisible(false);
            vboxStop.setVisible(false);
            vBoxCapturarVideo.setVisible(false);

            hBoxOpcoes.invalidate();
        } catch (Exception ex) {
            EmpresaUtil.showError("Erro ao capturar Video.", ex);
        }
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        video.setLengthLimit(25);
        video.setMaxSize(2048);
        video.requestCamera();
        vBoxCancel.setVisible(false);
        configVideoPlayer(videoPreview);
    }

    @Override
    public void iconOkClick(final Event event) {
        ok = true;
        close();
    }

    @Override
    public void vBoxCancelClick(Event<Object> event) {
        video.setVisible(true);

        vBoxCancel.setVisible(false);
        videoPreview.setVisible(false);
        iconOk.setVisible(false);
        iconStop.setVisible(false);
        vboxStop.setVisible(true);
        buttonCapturarVideo.setVisible(true);
        vBoxCapturarVideo.setVisible(true);
        hBoxOpcoes.invalidate();
    }

    @Override
    public void hBoxCloseClick(Event<Object> event) {
        close();
        ok = false;
    }

    @Override
    public void vBoxCapturarVideoClick(final Event event) {
        iconStop.setVisible(true);
        buttonCapturarVideo.setVisible(false);
        video.setRecording(true);
    }

    @Override
    public void vBoxStopClick(Event<Object> event) {
        video.setRecording(false);
        video.setVisible(true);

        vBoxCancel.setVisible(false);
        iconOk.setVisible(false);
        iconStop.setVisible(false);
        buttonCapturarVideo.setVisible(true);
        hBoxOpcoes.invalidate();
    }

    private void configVideoPlayer(TFVBox videoContainer) {
        player.setControls(true);
        player.setVflex("true");
        player.setHflex("true");
        player.setParent((Vlayout) videoContainer.getImpl());
        player.setDimBackground(false);
    }

    private void loadVideo(byte[] dadosVideo) {
        try{
        org.zkoss.video.Video content = new org.zkoss.video.AVideo("zk.mp4", dadosVideo);
        player.setContent(content);
    } catch (IOException e) {
        throw new RuntimeException(e);
    }
    }

}
