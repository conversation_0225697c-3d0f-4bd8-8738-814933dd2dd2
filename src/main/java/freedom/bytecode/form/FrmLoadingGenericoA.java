package freedom.bytecode.form;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.client.util.FreedomUtilities;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.form.wizard.*;
import freedom.bytecode.rn.wizard.*;
import freedom.util.EmpresaUtil;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import lombok.Getter;
import lombok.Setter;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Messagebox;
import org.zkoss.zul.Vlayout;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;


/**
 * Cria uma tela de loading genérica, que pode ser utilizada para exibir uma mensagem de
 * espera enquanto uma operação de longa duração é realizada. A tela de loading pode ser
 * finalizada automaticamente após um tempo específico ou de acordo com uma condição
 * personalizada definida pelo callback.
 *
 * <p><b>Exemplo de uso:</b></p>
 * <pre>{@code
 * // Define o callback para finalizar a tela de loading
 * FrmLoadingGenericoA.CallBackLoading callbackLoading = new FrmLoadingGenericoA.CallBackLoading() {
 *     @Override
 *     public boolean isFinalizarLoading() {
 *         return true; // Retorna true para indicar o término do loading
 *     }
 * };
 *
 * // Inicia a tela de loading com uma mensagem e configurações de tempo
 * FrmLoadingGenericoA frmLoadingGenericoA = new FrmLoadingGenericoA();
 * if (frmLoadingGenericoA.iniciarTelaLoading("Aguardando...", callbackLoading, 10, 3000)) {
 *     FormUtil.doShow(frmLoadingGenericoA, t -> {
 *         if (frmLoadingGenericoA.isOk()) {
 *             Dialog.create().showToastInfo("Processado com sucesso!", Constantes.MIDDLE_CENTER, 3000, true);
 *         } else {
 *             Dialog.create().showToastError("Processado com falha!", Constantes.MIDDLE_CENTER, 3000, true);
 *         }
 *     });
 * }
 * }</pre>
 *
 * <p><b>Parâmetros:</b></p>
 * <ul>
 *     <li><b>Mensagem:</b> Texto a ser exibido durante o loading (ex.: "Aguardando...").</li>
 *     <li><b>Callback:</b> Função de callback que determina quando o loading deve ser finalizado.</li>
 *     <li><b>Tentativas:</b> Quantidade de tentativas para verificar o estado do callback.</li>
 *     <li><b>Tempo de intervalo:</b> Intervalo (em milissegundos) para verificar o estado do callback.</li>
 * </ul>
 *
 * <p><b>Nota:</b> Utilize o callback {@code CallBackLoading} para definir a lógica de término da tela de loading.</p>
 *
 * @see FrmLoadingGenericoA.CallBackLoading
 * <AUTHOR>
 * @version 1.0
 */

@Setter
@Getter
public class FrmLoadingGenericoA extends FrmLoadingGenericoW {
    private static final long serialVersionUID = 20130827081850L;

    public interface CallBackLoading {
        boolean isFinalizarLoading();
    }


    public FrmLoadingGenericoA() {
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        Window w = (Window) ((Vlayout) this.getImpl()).getParent();
        Vlayout div = (Vlayout) this.getImpl();
        w.setTitle("");
        div.setStyle("display: grid; place-items: center");
        w.setHeight("auto");
        w.setWidth("auto");
    }

    CallBackLoading callbackLoading;
    int tentativas;
    boolean ok = false;

    @Override
    public void timerValidarEncerramentoTimer(Event<Object> event) {
        setTentativas(getTentativas() - 1);
        if (callbackLoading.isFinalizarLoading()) {
            setOk(true);
            encerrarLoading();
        }
        if (getTentativas() == 0) {
            setOk(false);
            encerrarLoading();
        }
    }

    public void encerrarLoading() {
        timerValidarEncerramento.setEnabled(false);
        close();
    }



    public boolean iniciarTelaLoading(String mensagem, CallBackLoading callbackLoading, int tentativas, int millissecondsInterval) {

        lblMensagem.setCaption(mensagem);
        this.timerValidarEncerramento.setInterval(millissecondsInterval);
        setTentativas(tentativas);

        /* caso não seja passado nada, vai esperar um unico intervalo e finalizar a tela de Loading */
        if (callbackLoading == null) {
            setCallbackLoading(new CallBackLoading() {
                @Override
                public boolean isFinalizarLoading() {
                    return true;
                }
            });
        }else{
            setCallbackLoading(callbackLoading);
        }
        timerValidarEncerramento.setEnabled(true);
        return true;
    }

}
