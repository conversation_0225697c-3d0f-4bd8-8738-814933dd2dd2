package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmClienteAlterarFrameExternoW;
import freedom.client.util.Dialog;
import static freedom.util.CastUtil.asString;
import freedom.util.EmpresaUtil;
import freedom.util.IntegracaoWebEasy;

public class FrmClienteAlterarFrameExternoA extends FrmClienteAlterarFrameExternoW {

    private static final long serialVersionUID = 20130827081850L;
    private IntegracaoWebEasy key;
    private String usuarioLogged = "";
    private Integer codEmpresaUsuario = 0;

    public FrmClienteAlterarFrameExternoA(long codCliente, long codEvento) {

        try {
            key = new IntegracaoWebEasy();

            usuarioLogged = EmpresaUtil.getUserLogged();
            codEmpresaUsuario = EmpresaUtil.getCodEmpresaUserLogged();

            String chave = key.criarChave(EmpresaUtil.getNomeModuloCrmGold(), usuarioLogged);

            String url = EmpresaUtil.getUrlWebEasy().trim();

            String codStrCliente = asString(codCliente);

            url = url + "TfrmAlteraCadastroCliente.zul?URLNBS=" + chave + "&COD_CLIENTE=" + codStrCliente
                    + "&USUARIO_LOGADO=" + usuarioLogged + "&EMPRESA_USUARIO=" + codEmpresaUsuario
                    + "&COD_EVENTO=" + codEvento;

            FrameAlterarCadastroCliente.setUrl(url);
        } catch (Exception ex) {
            Dialog.create()
                    .title("Erro")
                    .message("NBS-ERROR-H0101: " + ex.getMessage())
                    .showError();
        }
    }

}
