package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.util.EmpresaUtil;

public class FrmInputBoxFixedA extends FrmInputBoxFixedW {

    private static final long serialVersionUID = 20130827081850L;

    private boolean ok = false;

    @Override
    public void btnVoltarClick(final Event event) {
        ok = false;
        close();
    }

    @Override
    public void btnAceitarClick(final Event event) {
        ok = true;

        if (edtDescricao.getValue().asString().equals("")) {
            EmpresaUtil.showWarning("Atenção", "Obrigatório informar uma descrição.");
            return;
        }

        close();
    }

    public boolean isOk() {
        return ok;
    }

    public String getDescricao() {
        return edtDescricao.getValue().asString().trim();
    }

}
