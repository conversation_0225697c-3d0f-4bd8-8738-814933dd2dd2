package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmTrocarImagemLoginW;
import freedom.client.event.Event;
import freedom.client.event.UploadEvent;
import freedom.client.util.Dialog;
import freedom.commons.lang.IWorkList;
import freedom.util.FileUpload;
import freedom.util.FileUtils;
import freedom.util.ImageUtil;
import freedom.util.WorkListFactory;
import java.util.Random;

public class FrmTrocarImagemLoginA extends FrmTrocarImagemLoginW {

    private static final long serialVersionUID = 20130827081850L;
    private final IWorkList wl = WorkListFactory.getInstance();

    public FrmTrocarImagemLoginA() {
        setImageBackGround();
    }

    @Override
    public void btnUploadImagemClick(final Event event) {
        FileUpload.get("Upload Imagem Perfil", "Upload", (UploadEvent t) -> {
            byte[] imageBytes = ImageUtil.streamToByteArray(t.getStreamData());

            if (FileUtils.extractFileExtension(t.getFileName()).toUpperCase().equals("JPG")) {

                ImageUtil.saveImageLocal(imageBytes, "Login", "JPG");

                setImageBackGround();

            } else {
                Dialog.create()
                        .title("Atenção")
                        .message("Permitido somente imagens com extensão .JPG")
                        .showError();
            }
        });
    }

    private void setImageBackGround() {
        Random r = new Random(System.currentTimeMillis());
        int version = r.nextInt();
        if (version < 0) {
            version = version * -1;
        }
        vBoxImage.setBackgroundImage("/images/" + wl.sysget("APPLICATION_NAME").asString() + "Login.JPG?version=" + version);
    }

    @Override
    public void btnLimparImagemClick(final Event event) {
        //setImageBackGround();
    }
}
