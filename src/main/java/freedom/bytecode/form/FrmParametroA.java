package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmParametroW;
import freedom.bytecode.rn.EnTipoParm;
import freedom.client.controls.IInputComponent;
import freedom.client.controls.impl.TFCombo;
import freedom.client.controls.impl.TFHBox;
import freedom.client.controls.impl.TFString;
import freedom.client.controls.util.GridConfigCallback;
import freedom.client.event.Event;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.data.impl.View;
import freedom.util.EmpresaUtil;
import java.util.ArrayList;
import java.util.List;

public class FrmParametroA extends FrmParametroW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String VALOR = "VALOR";

    private static final String CHECK = "CHECK";

    private static final String COD_EMPRESA = "COD_EMPRESA";

    private static final String ERRO_AO_MARCAR_CHECK = "Erro ao marcar check.";

    private int codEmpresaSel;

    public FrmParametroA() {
        this.callBackCombo();
    }

    private void setGridCallBack() {

        this.gridParametro.setGridConfigCallback(new GridConfigCallback() {

            @Override
            public String getCellHint(int bookMark,
                                      String fieldName) {
                String hint = null;
                if (fieldName.equals("DESCRICAO")) {
                    try {
                        View vParametroSys = tbParametroSys.getView();
                        if (vParametroSys.gotoBookmark(bookMark)) {
                            hint = "Tabela: "
                                    + vParametroSys.getField("TABELA_DELPHI").asString().trim()
                                    + System.lineSeparator()
                                    + "Campo: "
                                    + vParametroSys.getField("CAMPO_DELPHI").asString().trim();
                        }
                    } catch (DataException dataException) {
                        EmpresaUtil.showError("Erro ao atribuir hint [107]",
                                dataException);
                    }
                }
                return hint;
            }

            @Override
            public String getCellCSS(int bookMark,
                                     String fieldName,
                                     boolean isLast) {
                return null;
            }

            @Override
            public String getFooter(String fieldName) {
                return null;
            }

        });
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.tbParametroSys.setAttribute("TAG",
                0);
        this.openTabelasAux();
        this.pesquisarParametros("");
        this.setGridCallBack();
    }

    private void pesquisarParametros(String descricao) {
        try {
            int idSistema = this.cbbParmSistema.getValue().asInteger();
            int idGrupo = this.cbbParmGrupo.getValue().asInteger();
            boolean chkSmtParSemValorDefChecked = this.chkSmtParSemValorDef.isChecked();
            this.rn.filtrarParametroSys(idSistema,
                    idGrupo,
                    descricao,
                    chkSmtParSemValorDefChecked);
            if (chkSmtParSemValorDefChecked) {
                this.tbParametroSysAfterScroll(null);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro filtrar Grupo - NBS-ERROR-P0103",
                    dataException);
        }
    }

    private void openTabelasAux() {
        try {
            this.rn.filtrarSistema();
            //CrmParts
            int initCodGrupo = 1;
            this.rn.filtrarGrupo(initCodGrupo);
            this.rn.filtrarEmpresas();
            this.cbbParmSistema.setValue(initCodGrupo);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro - NBS-ERROR-P0101:",
                    dataException);
        }
    }

    @Override
    public void cbbParmSistemaChange(Event<Object> event) {
        try {
            int idSistema = this.cbbParmSistema.getValue().asInteger();
            this.rn.filtrarGrupo(idSistema);
            String descricao = this.edtDescricao.getValue().asString().trim();
            this.pesquisarParametros(descricao);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao filtrar Grupo - NBS-ERROR-P0102:",
                    dataException);
        }
    }

    @Override
    public void tbParametroSysAfterScroll(Event<Object> event) {
        if (this.tbParametroSys.getAttribute("TAG").equals(0)) {
            this.filtrarParametroValor();
            this.modifyFieldAlterarValor();
        }
    }

    private void filtrarParametroValor() {
        try {
            this.rn.filtrarParametroValor(0);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro Valor Parâmetros - NBS-ERROR-P0104:",
                    dataException);
        }
    }

    private void modifyFieldAlterarValor() {
        try {
            this.edtValorString.setVisible(Boolean.FALSE);
            this.edtValorDecimal.setVisible(Boolean.FALSE);
            this.edtValorDate.setVisible(Boolean.FALSE);
            this.edtValorInteger.setVisible(Boolean.FALSE);
            this.cboValor.setVisible(Boolean.FALSE);
            this.chkValor.setVisible(Boolean.FALSE);
            this.edtValorHora.setVisible(Boolean.FALSE);
            this.hBoxFImageParam.setVisible(Boolean.FALSE);
            this.memoValorString.setVisible(Boolean.FALSE);
            this.memObs.setReadOnly(Boolean.TRUE);
            if (!tbParametroSys.getOBJETO().isNull()) {

                String objeto = tbParametroSys.getOBJETO().asString();
                String showButtonFind = tbParametroSys.getSHOW_BUTTON_FIND().asString();
                vBoxImageButtonFind.setVisible(showButtonFind.equals("S"));
                btnAlterar.setVisible(true);
                btnAlterar.setCaption("Alterar");
                hboxSelecaoGenerica.setVisible(false);
                hboxSelecaoGenericaLimpar.setVisible(false);
                tbParamEmpresaValues.disableControls();
                switch (objeto) {
                    case "S": // String
                        setFieldTable(edtValorString, objeto);
                        break;
                    case "B": // CheckBox
                        setFieldTable(chkValor, objeto);
                        break;
                    case "C": // ComboBox
                        setFieldTable(cboValor, objeto);
                        break;
                    case "L": // List Options ComboBox
                        setFieldTable(cboValor, objeto);
                        break;
                    case "N": // Númerico
                        setFieldTable(edtValorDecimal, objeto);
                        break;
                    case "I": // Inteiro
                        setFieldTable(edtValorInteger, objeto);
                        break;
                    case "D": // Data
                        setFieldTable(edtValorDate, objeto);
                        break;
                    case "H": // hora
                        setFieldTable(edtValorHora, objeto);
                        break;
                    case "P": // Password
                        setFieldTable(edtValorString, objeto);
                        break;
                    case "A":
                        hboxSelecaoGenericaLimpar.setVisible(true);
                        setFieldImage(hBoxFImageParam);
                        break;
                    case "M": // Memo para CLob
                        btnAlterar.setCaption("Editar");
                        this.setFieldTable(this.memoValorString, objeto);
                        break;
                    case "F": // editFind = true;
                        btnAlterar.setCaption("Buscar");
                        hboxSelecaoGenericaLimpar.setVisible(true);
                        setFieldTable(edtValorString, objeto);
                        break;
                    default:
                        break;
                }
                tbParamEmpresaValues.enableControls();
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao consultar parâmetros.",
                    dataException);
        }
    }

    private void setFieldImage(TFHBox imageParam) {
        // Arquivo Imagem
        imageParam.setVisible(true);
    }

    private void setFieldTable(IInputComponent component,
                               String tipoObj) throws DataException {
        component.setFieldName(VALOR);
        if (component instanceof TFCombo) {
            String tbParametroSysListOption = this.tbParametroSys.getLIST_OPTION().asString();
            if (!tbParametroSysListOption.isEmpty()) {
            //    this.setComboLookup(((TFCombo) component));
            //} else {
                ((TFCombo) component).setListOptions(this.tbParametroSys.getLIST_OPTION().asString());
            }
            component.setValue(this.tbParamEmpresaValues.getField(VALOR));
        } else if (component instanceof TFString) {
            if ("P".equals(tipoObj)) {
                ((TFString) component).setPwd(Boolean.TRUE);
            } else {
                ((TFString) component).setPwd(Boolean.FALSE);
            }
        }
        component.setVisible(Boolean.TRUE);
    }

    private void setComboLookup(TFCombo comboBox) {
        try {
            this.rn.setComboLookup(comboBox);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao definir o lookup combo",
                    dataException);
        }
    }

    @Override
    public void gridValuesParmClickCheck(Event<Object> event) {
        this.setChecked();
    }

    @Override
    public void gridValuesParmClickUnCheck(Event<Object> event) {
        this.setChecked();
    }

    private void setChecked() {
        try {
            this.tbParamEmpresaValues.edit();
            String tbParamEmpresaValuesCHECK = this.tbParamEmpresaValues.getField(CHECK).asString();
            if (tbParamEmpresaValuesCHECK.equals("S")) {
                this.tbParamEmpresaValues.setField(CHECK,
                        "N");
            } else {
                this.tbParamEmpresaValues.setField(CHECK,
                        "S");
                this.codEmpresaSel = this.tbParamEmpresaValues.getField(COD_EMPRESA).asInteger();
            }
            this.tbParamEmpresaValues.post();
            boolean exibeMensagemAoMarcarSegundaEmpresaParametroParmSysVpMotivoExclusaoOrc = this.exibirMensagemAoMarcarSegundaEmpresaParametroParmSysVpMotivoExclusaoOrc();
            if (exibeMensagemAoMarcarSegundaEmpresaParametroParmSysVpMotivoExclusaoOrc) {
                String mensagem = "Não é permitido selecionar mais de uma empresa para a tabela \"PARM_SYS\" e o campo \"VP_MOTIVO_EXCLUSAO_ORC\".";
                EmpresaUtil.showInformationMessage(mensagem);
                this.setChecked();
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError(FrmParametroA.ERRO_AO_MARCAR_CHECK,
                    dataException);
        }
    }

    private void setFilterEmpresasSelecionadas(String codEmpSel) {
        try {
            this.rn.setFilterEmpresasSelecionadas(codEmpSel);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao definir o filtro das empresas selecionadas",
                    dataException);
        }
    }

    @Override
    public void chkFixarEmpresaCheck(Event<Object> event) {
        boolean chkFixarEmpresaNotChecked = !this.chkFixarEmpresa.isChecked();
        if (chkFixarEmpresaNotChecked) {
            this.chkFixarEmpresa.setHint("");
        } else {
            boolean retFuncao = validaEmpresasSelecionadas();

            if (!retFuncao) {
                EmpresaUtil.showInformationMessage("Nenhuma Empresa foi selecionada!");
                this.chkFixarEmpresa.setChecked(false);
                return;
            }

            String strEmpSel = this.getEmpresasSelecionadas();
            this.chkFixarEmpresa.setHint(strEmpSel);
        }

        this.setFilterEmpresasSelecionadas(this.chkFixarEmpresa.getHint());
        this.pesquisarParametros();
    }

    public boolean validaEmpresasSelecionadas() {
        boolean retFuncao = false;
        try {
            View vParamEmpresaValues = this.tbParamEmpresaValues.getView();

            vParamEmpresaValues.first();
            while (Boolean.FALSE.equals(vParamEmpresaValues.eof())) {
                String check = vParamEmpresaValues.getField(CHECK).asString();
                if (check.equals("S")) {
                    retFuncao = true;
                }
                vParamEmpresaValues.next();
            }

            return retFuncao;
        } catch (Exception exception) {
            EmpresaUtil.showError("Erro validar empresas checadas", exception);
        }
        return retFuncao;
    }

    private String getEmpresasSelecionadas() {
        try {
            List<Value> listCodEmpresa = new ArrayList<>();
            View vParamEmpresaValues = this.tbParamEmpresaValues.getView();
            StringBuilder empresasSelecionadas = new StringBuilder();
            vParamEmpresaValues.first();
            while (Boolean.FALSE.equals(vParamEmpresaValues.eof())) {
                String vParamEmpresaValuesCHECK = vParamEmpresaValues.getField(CHECK).asString();
                if ((vParamEmpresaValuesCHECK.equals("S") &&
                        (this.tbParamEmpresaValues.locate(COD_EMPRESA, vParamEmpresaValues.getField(COD_EMPRESA))))) {
                    Value vCodEmpresa = vParamEmpresaValues.getField(COD_EMPRESA);
                    listCodEmpresa.add(vCodEmpresa);
                }
                vParamEmpresaValues.next();
            }
            for (Value codEmpresaSel2 : listCodEmpresa) {
                empresasSelecionadas.append(codEmpresaSel2).append(",");
            }
            return empresasSelecionadas.substring(0, empresasSelecionadas.length() - 1);
        } catch (DataException e) {
            EmpresaUtil.showError(FrmParametroA.ERRO_AO_MARCAR_CHECK, e);
        }
        return "";
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        boolean tbParamEmpresaValuesEmpty = this.tbParamEmpresaValues.isEmpty();
        if (tbParamEmpresaValuesEmpty) {
            EmpresaUtil.showInformationMessage("Não há dados para realizar esta ação.");
            return;
        }
        int contador = 0;
        try {
            View vParamEmpresaValues = this.tbParamEmpresaValues.getView();
            vParamEmpresaValues.first();
            while (Boolean.FALSE.equals(vParamEmpresaValues.eof())) {
                String check = vParamEmpresaValues.getField(CHECK).asString();
                if (check.equals("S")) {
                    contador++;
                }
                vParamEmpresaValues.next();
            }
        } catch (Exception exception) {
            EmpresaUtil.showError("Erro validar empresas checadas",
                    exception);
        }
        if (contador == 0) {
            EmpresaUtil.showInformationMessage("Nenhuma empresa selecionada");
            return;
        }
        this.alterarValorParametro();
    }

    private void alterarValorParametro() {
        try {
            String tipoAlteracao = "E";
            this.tbParamEmpresaValues.post();
            Value value = this.tbParamEmpresaValues.getField(VALOR);
            String tipoCampo = this.tbParametroSys.getOBJETO().asString().trim();
            if ((tipoCampo.equals("N"))
                    || (tipoCampo.equals("I"))
                    || (tipoCampo.equals("C"))) {
                String msg = "";
                switch (tipoCampo) {
                    case "N":
                        if (value.asDecimal() < 0) {
                            msg = "Valor não pode ser inferior a 0.00 (Zero).";
                        }
                        break;
                    case "I":
                        if (value.asInteger() < 0) {
                            msg = "Valor não pode ser inferior a 0 (Zero).";
                        }
                        break;
                    case "C":
                        /*
                        Estava assim incorretamente porque nem todos os parâmetros são de preenchimento obrigatório.
                        Validar isso no método cboValorClearClick
                        if (value.asString().trim().equals("")) {
                            msg = "Informe o valor.";
                        }
                        */
                        break;
                    default:
                        break;
                }
                if (!msg.trim().isEmpty()) {
                    EmpresaUtil.showInformationMessage(msg);
                    return;
                }
            }
            //Não mexo no ponteiro da tabela caso não tenha nenhum selecionado
            View vParamEmpresaValues = this.tbParamEmpresaValues.getView();
            vParamEmpresaValues.first();
            while (Boolean.FALSE.equals(vParamEmpresaValues.eof())) {
                String vParamEmpresaValuesCHECK = vParamEmpresaValues.getField(CHECK).asString();
                if ((vParamEmpresaValuesCHECK.equals("S")
                        && (this.tbParamEmpresaValues.locate(COD_EMPRESA,
                        vParamEmpresaValues.getField(COD_EMPRESA))))) {
                    this.tbParamEmpresaValues.edit();
                    this.tbParamEmpresaValues.setField(VALOR,
                            value);
                    this.tbParamEmpresaValues.post();
                    tipoAlteracao = "T";
                }
                vParamEmpresaValues.next();
            }
            this.rn.salvarAlteracoesParametros(tipoAlteracao);
            this.filtrarParametroValor();
            this.tbParamEmpresaValues.locate(COD_EMPRESA,
                    this.codEmpresaSel);
        } catch (DataException dataException) {
            EmpresaUtil.showError(FrmParametroA.ERRO_AO_MARCAR_CHECK,
                    dataException);
        }
    }

    @Override
    public void menuItemNenhumClick(final Event<Object> event) {
        this.selecionarTodos("N");
    }

    @Override
    public void menuItemSelTodasClick(final Event<Object> event) {
        this.selecionarTodos("S");
    }

    private void selecionarTodos(String check) {
        try {
            this.tbParamEmpresaValues.first();
            while (Boolean.FALSE.equals(this.tbParamEmpresaValues.eof())) {
                this.tbParamEmpresaValues.edit();
                this.tbParamEmpresaValues.setField(CHECK,
                        check);
                this.tbParamEmpresaValues.post();
                this.tbParamEmpresaValues.next();
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao Marcar Check",
                    dataException);
        }
    }

    @Override
    public void icoAtualizarClick(final Event<Object> event) {
        this.pesquisarParametros();
    }

    private void pesquisarParametros() {
        String descricao = this.edtDescricao.getValue().asString().trim();
        this.pesquisarParametros(descricao);
    }

    @Override
    public void cbbParmGrupoChange(Event<Object> event) {
        this.pesquisarParametros();
    }

    @Override
    public void edtDescricaoEnter(Event<Object> event) {
        this.pesquisarParametros();
    }

    private void callBackCombo() {
        String descricao = this.edtDescricao.getValue().asString().trim();
        this.cbbParmSistema.setDelCallback(() -> this.pesquisarParametros(descricao));
        this.cbbParmGrupo.setDelCallback(() -> this.pesquisarParametros(descricao));
    }

    @Override
    public void iconClassHelpClick(final Event<Object> event) {
        FormUtil.redirect("http://ajuda.nbsi.com.br:84/index.php/Par%C3%A2metros",
                true);
    }

    @Override
    public void edtDescricaoChanging(Event<Object> event) {
        if (((Value) event.getValue()).isEmpty()
                || ((Value) event.getValue()).asString().trim().isEmpty()) {
            this.pesquisarParametros("");
        }
    }

    @Override
    public void chkSmtParSemValorDefCheck(Event<Object> event) {
        this.pesquisarParametros();
    }

    @Override
    public void btnFindClick(final Event<Object> event) {
        //Implementado código na classe FrmParametroU de cada projeto
    }

    @Override
    public void cbbParmSistemaClearClick(Event<Object> event) {

    }

    @Override
    public void cbbParmGrupoClearClick(Event<Object> event) {

    }

    private Value getParametro(int codEmpresa) {
        Value retFuncao = null;
        try {
            retFuncao = this.rn.getParametro(EnTipoParm.PARM_SYS3,
                    codEmpresa,
                    "PESQ_ITEM_PADRAO_PARTS");
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao obter o parâmetro",
                    dataException);
        }
        return retFuncao;
    }

    @Override
    public void cboValorClearClick(Event<Object> event) {
        String tabela = this.tbParametroSys.getTABELA_DELPHI().asString();
        String campo = this.tbParametroSys.getCAMPO_DELPHI().asString();
        long codEmpresaSelecionada = this.tbParamEmpresaValues.getCOD_EMPRESA().asLong();
        if (tabela.equals("PARM_SYS3")
                && campo.equals("PESQ_ITEM_PADRAO_PARTS")) {
            String valorDoParametro = ((this.getParametro((int) codEmpresaSelecionada).asString() == null) ? ("") : (this.getParametro((int) codEmpresaSelecionada).asString()));
            String mensagem = "O parâmetro abaixo não pode ficar vazio:"
                    + System.lineSeparator()
                    + System.lineSeparator()
                    + "Grupo: "
                    + this.tbParametroSys.getGRUPO().asString()
                    + System.lineSeparator()
                    + System.lineSeparator()
                    + "Descrição: "
                    + this.tbParametroSys.getDESCRICAO().asString()
                    + System.lineSeparator()
                    + System.lineSeparator()
                    + "Tabela: "
                    + this.tbParametroSys.getTABELA_DELPHI().asString()
                    + System.lineSeparator()
                    + System.lineSeparator()
                    + "Campo: "
                    + this.tbParametroSys.getCAMPO_DELPHI().asString()
                    + System.lineSeparator()
                    + System.lineSeparator()
                    + this.tbParametroSys.getOBSERVACAO().asString();
            EmpresaUtil.showInformationMessage(mensagem);
            this.cboValor.setValue(valorDoParametro);
        }
    }

    public boolean permiteAlterarValorParametro(){
        if (this.tbParamEmpresaValues.count() == 0) {
            EmpresaUtil.showInformationMessage("Não há dados para realizar esta ação.");
            return false;
        }
        int conta = 0;
        try {
            View vParamEmpresaValues = this.tbParamEmpresaValues.getView();
            vParamEmpresaValues.first();
            while (Boolean.FALSE.equals(vParamEmpresaValues.eof())) {
                if (vParamEmpresaValues.getField(FrmParametroA.CHECK).asString().equals("S")) {
                    conta++;
                }
                vParamEmpresaValues.next();
            }
        } catch (DataException dataException) {
            EmpresaUtil.showInformationMessage("Erro ao validar as empresas checadas");
            return false;
        }
        if (conta == 0) {
            EmpresaUtil.showInformationMessage("Selecione alguma empresa.");
            return false;
        }
        return true;
    }

    @Override
    public void btnSelecaoGenericaClick(Event<Object> event) {
        boolean naoPermiteAlterarValorParametro = !this.permiteAlterarValorParametro();
        if (naoPermiteAlterarValorParametro) {
            return;
        }
        this.abrirEdicaoFind();
    }

    public void abrirEdicaoFind(){
        String filterKey;
        try {
            filterKey = tbParamEmpresaValues.getField(FrmParametroA.VALOR).asString();
        } catch (DataException e) {
            filterKey = "";
        }
        FrmSelecaoGenericaA frmSelecaoGenerica = new FrmSelecaoGenericaA();
        frmSelecaoGenerica.setTitulo("Selecionar Item");
        frmSelecaoGenerica.edFiltroKey.setValue(filterKey);
        if (frmSelecaoGenerica.carregarTabela(tbParametroSys.getLOOKUP_TABLE().asString(), tbParametroSys.getLOOKUP_KEY_FIELD().asString(), tbParametroSys.getLOOKUP_DISPLAY_FIELD().asString(), tbParametroSys.getLOOKUP_FILTER().asString(),100)) {
            FormUtil.doShow(frmSelecaoGenerica,
                    t -> {
                if (frmSelecaoGenerica.isOk()) {
                    //this.valorFind = frmSelecaoGenerica.getLookupKey();
                    tbParamEmpresaValues.setField(FrmParametroA.VALOR,frmSelecaoGenerica.getLookupKey());
                    alterarValorParametro();
                }
            });
        }
    }

    @Override
    public void btnSelecaoGenericaLimparClick(Event<Object> event) {
        try {
            boolean naoPermiteAlterarValorParametro = !this.permiteAlterarValorParametro();
            if (naoPermiteAlterarValorParametro) {
                return;
            }
            this.tbParamEmpresaValues.setField(
                    FrmParametroA.VALOR
                    ,null
            );
            this.alterarValorParametro();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Não foi possivel limpar o parametro"
                    ,dataException
            );
        }
    }

    private boolean exibirMensagemAoMarcarSegundaEmpresaParametroParmSysVpMotivoExclusaoOrc() {
        boolean retFuncao = false;
        try {
            retFuncao = this.rn.exibirMensagemAoMarcarSegundaEmpresaParametroParmSysVpMotivoExclusaoOrc();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao verificar se marcou a segunda empresa para o parâmetro PARM_SYS.VP_MOTIVO_EXCLUSAO_ORC",
                    dataException);
        }
        return retFuncao;
    }

    @Override
    public void tbParamEmpresaValuesAfterScroll(Event<Object> event) {
        String objeto = this.tbParametroSys.getOBJETO().asString();
        if (objeto.equals("C")){
            this.setComboLookup(this.cboValor);
        }
    }

}