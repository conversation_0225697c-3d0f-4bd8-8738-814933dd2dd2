package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FrmEnviarEmailA extends FrmEnviarEmailW {

    private static final long serialVersionUID = 20130827081850L;

    public void iniciarEmailCliente(Double codEvento, Double codEmpresa) {
        try {
            tbEmailModelo.close();
            tbEmailModelo.addFilter("APLICACAO;DEPARTAMENTO");
            tbEmailModelo.addParam("APLICACAO", "E");
            tbEmailModelo.setOrderBy("MODELO");
            tbEmailModelo.open();

            tbEmailModeloTag.close();
            tbEmailModeloTag.open();

            rn.setCodEmpresa(codEmpresa);
            rn.setCodEvento(codEvento);
            tbCrmpartsDadosEmail.close();
            tbCrmpartsDadosEmail.addFilter("COD_EMPRESA;COD_EVENTO");
            tbCrmpartsDadosEmail.addParam("COD_EMPRESA", codEmpresa);
            tbCrmpartsDadosEmail.addParam("COD_EVENTO", codEvento);
            tbCrmpartsDadosEmail.open();
            lblMensagem.setCaption("Enviar E-mail para: ");
            lblMensagem.setCaption(lblMensagem.getCaption() + tbCrmpartsDadosEmail.getNOME_DO_CLIENTE().asString().trim());
            lblMensagem.setCaption(lblMensagem.getCaption() + " - " + tbCrmpartsDadosEmail.getE_MAIL_DO_CLIENTE().asString().toLowerCase().trim());
        } catch (DataException ex) {
            EmpresaUtil.showError("Crm Parts", ex);
        }
    }

    public void iniciarEmailOrcamento(Double codEvento,
                                      Double codEmpresa,
                                      Double codOrcMapa) {
        try {
            this.iniciarEmailCliente(codEvento,
                    codEmpresa);
            this.ckSolicitarAprCliente.setVisible(this.rn.faltaPedirAprovacaoCliente());
            this.rn.setCodOrcMapa(codOrcMapa);
            this.tbParmFluxo.close();
            this.tbParmFluxo.setFilterCOD_EMPRESA(codEmpresa);
            this.tbParmFluxo.open();
            if (this.tbParmFluxo.getTEMPLATE_EMAIL_ORCAMENTO_EMAIL().asInteger() > 0) {
                this.tbEmailModelo.locate("ID_EMAIL_MODELO", this.tbParmFluxo.getTEMPLATE_EMAIL_ORCAMENTO_EMAIL().asInteger());
                this.cmbTemplate.setText(this.tbEmailModelo.getMODELO().asString());
                this.cmbTemplateChange(null);
            }
        } catch (Exception exception) {
            EmpresaUtil.showError("falha ao iniciar email orçamento",
                    exception);
        }
    }

    public void responderEmailCliente(Double codEvento,
                                      Double codEmpresa,
                                      Double codOrcMapa,
                                      String assuntoEmail) {
        try {
            this.iniciarEmailCliente(codEvento,
                    codEmpresa);
            this.edAssunto.setValue(assuntoEmail);
            this.ckSolicitarAprCliente.setVisible(false);
            this.rn.setCodOrcMapa(codOrcMapa);
        } catch (Exception exception) {
            EmpresaUtil.showError("falha ao iniciar email orçamento",
                    exception);
        }
    }

    String validaDados() throws Exception {
        String result = "";

        if (edAssunto.getValue().asString().isEmpty()) {
            result += "\nCampo Assunto do email está vazio.";
        }

        if (edMailBody.getValue().asString().isEmpty()) {
            result += "\nO Conteudo do email está vazio.";
        }

        if (this.tbCrmpartsDadosEmail.getE_MAIL_DO_CLIENTE().asString().equals("")) {
            result += "\nO Email é inválido.";
        }

        return result;
    }

    void gerarEmail() throws Exception {
        this.edAssunto.setValue("");
        if (!this.cmbTemplate.getValue().isEmpty()) {
            this.edAssunto.setValue(this.tbEmailModelo.getASSUNTO().asString());
            String mensagem = this.tbEmailModelo.getMENSAGEM().asString();
            Pattern pattern = Pattern.compile("\\[.*?\\]");
            Matcher matcher = pattern.matcher(mensagem);
            while (matcher.find()) {
                try {
                    mensagem = mensagem.replace(matcher.group(),
                            this.fomaterTag(matcher.group(),
                                    this.tbCrmpartsDadosEmail.getField(
                                            matcher.group()
                                                    .replaceAll("[\\[\\]]", "")
                                                    .replace("/", "_"))));
                } catch (Exception exception) {
                }
            }
            this.edMailBody.setValue(mensagem);
        }
    }

    String fomaterTag(String tag,
                      Value valor) throws Exception {
        if (this.tbEmailModeloTag.locate("TAG",
                tag)) {
            String tipo = this.tbEmailModeloTag.getTIPO().asString();
            switch (tipo) {
                case "DE":
                    return valor.asString(",##0.00");
                case "DD":
                    return valor.asString("dd/MM/yyyy");
                case "DT":
                    return valor.asString("dd/MM/yyyy HH:mm:ss");
                case "IN":
                    return String.valueOf(valor.asInteger());
                default:
                    return valor.asString();
            }
        }
        return valor.asString();
    }

    @Override
    public void cmbTemplateChange(Event<Object> event) {
        try {
            this.gerarEmail();
        } catch (Exception exception) {
            EmpresaUtil.showError("Erro",
                    exception);
        }
    }

    void onEnviarEmail() throws Exception {
        String msgErro;
        msgErro = this.validaDados();
        if (msgErro.trim().length() > 0) {
            EmpresaUtil.showMessage("",
                    msgErro);
            return;
        }
        try {
            String emailPara = this.tbCrmpartsDadosEmail.getE_MAIL_DO_CLIENTE().asString();
            String emailAssunto = this.edAssunto.getValue().asString();
            String emailMsg = this.edMailBody.getValue().asString();
            String emailMsgPure = this.removeHTMLTags(this.edMailBody.getValue().asString().replace("&nbsp;",
                    " "));
            this.rn.enviarEmail(emailPara,
                    emailAssunto,
                    emailMsg,
                    emailMsgPure,
                    this.ckSolicitarAprCliente.isChecked());
            EmpresaUtil.showMessage("",
                    "E-mail enviado com sucesso.");
            this.close();
        } catch (Exception exception) {
            EmpresaUtil.showError("Falha ao enviar email",
                    exception);
        }
    }

    public String removeHTMLTags(String html) {
        return org.jsoup.Jsoup.parse(html).text();
    }

    @Override
    public void btnEnviarEmailClick(final Event<Object> event) {
        try {
            this.onEnviarEmail();
        } catch (Exception exception) {
            EmpresaUtil.showError("Erro ao Enviar e-mail",
                    exception);
        }
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        this.close();
    }

}