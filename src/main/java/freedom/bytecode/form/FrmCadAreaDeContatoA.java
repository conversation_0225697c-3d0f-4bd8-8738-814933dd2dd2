package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.Event;

public class FrmCadAreaDeContatoA extends FrmCadAreaDeContatoW {

    private static final long serialVersionUID = 20130827081850L;

    private boolean fechadoAoAceitar = false;

    public boolean isFechadoAoAceitar() {
        return this.fechadoAoAceitar;
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.btnFiltroAvancado.setVisible(false);
        //region Omitir opções de exclusão - Solicitado assim por Alam e Sione
        this.btnExcluir.setVisible(false);
        this.gridPrincipal.getColumns().forEach(coluna -> {
            if (coluna.getTitleCaption().equals("Exc.")) {
                coluna.setVisible(false);
            }
        });
        //endregion
        this.btnSalvarContinuar.setVisible(false);
        this.btnAnterior.setVisible(false);
        this.btnProximo.setVisible(false);
        this.FHBox3.setVisible(false);
        this.FHBox8.setVisible(false);
        this.menuItemAbreTabelaAux.setVisible(false);
        this.menuHabilitaNavegacao.setVisible(false);
        this.menuSelecaoMultipla.setVisible(false);
        this.menuItemHelp.setVisible(false);
        this.menuItemConfgGrid.setVisible(false);
        this.menuItemExportPdf.setVisible(false);
        this.btnConsultar.setHeight(55);
        this.btnConsultar.setWidth(65);
        this.btnNovo.setHeight(55);
        this.btnNovo.setWidth(65);
        this.btnAlterar.setHeight(55);
        this.btnAlterar.setWidth(65);
        this.btnSalvar.setHeight(55);
        this.btnSalvar.setWidth(65);
        this.btnCancelar.setHeight(55);
        this.btnCancelar.setWidth(65);
        this.btnAceitar.setHeight(55);
        this.btnAceitar.setWidth(65);
        this.btnMais.setHeight(55);
        this.btnMais.setWidth(65);
        this.FHBox5.setHeight(70);
        this.FHBox5.setWidth(140);
        this.FHBox1.setHeight(70);
        this.FHBox6.setHeight(70);
        this.btnConsultar.setCaption("Pesquisar");
        this.btnNovo.setCaption("Novo");
        this.btnAlterar.setCaption("Alterar");
        this.btnSalvar.setCaption("Salvar");
        this.btnCancelar.setCaption("Cancelar");
        this.btnAceitar.setCaption("Aceitar");
    }

    @Override
    public void btnAceitarClick(Event<Object> event) {
        this.fechadoAoAceitar = true;
        super.btnAceitarClick(event);
    }

}
