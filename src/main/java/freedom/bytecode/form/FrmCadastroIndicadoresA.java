package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmCadastroIndicadoresW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmCadastroIndicadoresA extends FrmCadastroIndicadoresW {
    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;
    private int idPainel = 0;
    private int idOrder = 0;
    private int idGrupoClasse = 0;

    public FrmCadastroIndicadoresA(int idPainel, int idGrupoClasse) {
        this.idPainel = idPainel;
        this.idGrupoClasse = idGrupoClasse;
        initForm();
    }

    private void initForm() {
        try {
            rn.carregaComboGrupoInd(idGrupoClasse);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void carregaGridIndicadores() {
        try {
            rn.carregaGridIndicadores(idPainel, cbbGrupoIndicadores.getValue().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }


    @Override
    public void gridIndicadoresgridIndUnChecked(final Event event) {
        selIndicadores();
    }

    @Override
    public void gridIndicadoresgridIndChecked(final Event event) {
        selIndicadores();
    }

    private void selIndicadores() {
        try {
            tbGridGrupoIndicadores.edit();
            if (tbGridGrupoIndicadores.getSEL().asString().equals("S")) {
                tbGridGrupoIndicadores.setSEL("N");
            } else {
                tbGridGrupoIndicadores.setSEL("S");
            }
            tbGridGrupoIndicadores.post();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnCancelarClick(final Event event) {
        ok = false;
        close();
    }

    @Override
    public void btnSalvarClick(final Event event) {
        if (validaIndicadores()) {
            gravaIndicadores();
            ok = true;
            close();
        } else {
            EmpresaUtil.showMessage("Atenção", "Nenhum item na grid foi selecionado!");
            return;
        }
    }

    @Override
    public void cbbGrupoIndicadoresChange(final Event event) {
        carregaGridIndicadores();
    }

    public boolean isOk() {
        return ok;
    }

    private boolean validaIndicadores() {
        boolean valida = false;
        try {
            tbGridGrupoIndicadores.first();
            while (!tbGridGrupoIndicadores.eof()) {
                if (tbGridGrupoIndicadores.getSEL().asString().equals("S")) {
                    valida = true;
                    break;
                }
                tbGridGrupoIndicadores.next();
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
        return valida;
    }

    private void gravaIndicadores() {
        try {
            idOrder = rn.displayOrder(idPainel, tbGridGrupoIndicadores.getID_GRUPO().asInteger());
            tbGridGrupoIndicadores.first();
            while (!tbGridGrupoIndicadores.eof()) {
                if (tbGridGrupoIndicadores.getSEL().asString().equals("S")) {
                    idOrder += 1;
                    tbPainelIndicador.append();
                    tbPainelIndicador.setID_INDICADOR(tbGridGrupoIndicadores.getID().asInteger());
                    tbPainelIndicador.setID_PAINEL(idPainel);
                    tbPainelIndicador.setID_GRUPO(tbGridGrupoIndicadores.getID_GRUPO().asInteger());
                    tbPainelIndicador.setDISPLAY_ORDER(idOrder);
                    tbPainelIndicador.post();
                }

                tbGridGrupoIndicadores.next();
            }
            tbPainelIndicador.applyUpdates();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

}
