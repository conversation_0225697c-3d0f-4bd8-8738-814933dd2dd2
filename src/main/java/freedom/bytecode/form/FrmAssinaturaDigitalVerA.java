package freedom.bytecode.form;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.bytecode.form.wizard.*;
import freedom.client.util.FormUtil;
import freedom.client.util.FreedomUtilities;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;
import lombok.Getter;
import lombok.Setter;
import org.json.JSONObject;

import freedom.data.DataException;
import org.zkoss.zul.Div;

@Setter
@Getter
public class FrmAssinaturaDigitalVerA extends FrmAssinaturaDigitalVerW {
    private static final long serialVersionUID = 20130827081850L;
    TipoAssinaturaStrategy tipoAssinatura;
    JSONObject jsonParametros;
    String tagSignatario;

    public FrmAssinaturaDigitalVerA() {

    }

    /**
     * Carrega o link de assinatura Geral
     * @param tipoAssinatura tipo da assinatura
     * @param jsonParametros tag do signatario
     * @return true caso a assinatura seja carregada
     */
    public boolean carregarAssinaturaDigitalPresencial(TipoAssinaturaStrategy tipoAssinatura, JSONObject jsonParametros) {
        return carregarAssinaturaDigitalRemota(tipoAssinatura, jsonParametros, "");
    }

    /**
     * Carrega o link de assinatura Especifico de um signatario (tagSignatario)
     * @param tipoAssinatura tipo da assinatura
     * @param jsonParametros parametros da assinatura
     * @param tagSignatario tag do signatario
     * @return true caso a assinatura seja carregada
     */
    public boolean carregarAssinaturaDigitalRemota(TipoAssinaturaStrategy tipoAssinatura, JSONObject jsonParametros, String tagSignatario) {
        try {
            setTipoAssinatura(tipoAssinatura);
            setJsonParametros(jsonParametros);
            setTagSignatario(tagSignatario);
            adicionarFuncaoCopiarClipBoardJS();
            imgQrCode.setAttribute("SCLASS_BASE", "vboxborderpainel");
            initForm();
            return true;
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao carregar QRCode da Assinatura", e);
            return false;
        }
    }

    public void initForm() throws DataException {
        rn.filtrarTbSolicitacoesAssinaturas(getTipoAssinatura(), getJsonParametros(), getTagSignatario());
        if (rn.tbSolicitacoesAssinaturasIsEmpty()) {
            throw new DataException("Nenhum Signatario associado: " + getTipoAssinatura().getTipo());
        }
        if (!getTagSignatario().isEmpty() && !tbSolicitacoesAssinaturas.locate("TAG_DOCUMENTO", tagSignatario)){
            throw new DataException("Nenhum Signatario com a tag: " + getTagSignatario());
        }
    }

    @Override
    public void iconStatusAguardandoAssinaturaClick(Event<Object> event) {
        Dialog.create().showToastInfo("Copiado", Constantes.MIDDLE_CENTER,2000,true);
    }

    void adicionarFuncaoCopiarClipBoardJS() {
        FreedomUtilities.invokeLater(() -> {
            String linkAssinatura = tbSolicitacoesAssinaturas.getURL_ASSINATURA().asString();
            String btnCopiarId = ((Div) iconAssinar.getImpl()).getUuid();
            String sb = "\n" +
                    "(()=>{\n" +
                    "const copyDiv = document.querySelector('#" + btnCopiarId + "');\n" +
                    "const handleCopy = (content) => {\n" +
                    " const textarea = document.createElement(\"textArea\");\n" +
                    " textarea.textContent = content;\n" +
                    " document.body.appendChild(textarea);\n" +
                    " textarea.select();\n" +
                    " document.execCommand(\"copy\");\n" +
                    " document.body.removeChild(textarea);\n" +
                    " console.log(content)\n" +
                    "};\n" +
                    "copyDiv.addEventListener('click',() => {handleCopy('" + linkAssinatura + "')});\n" +
                    "})();";

            org.zkoss.zk.ui.util.Clients.evalJavaScript(sb);
        });
    }

    @Override
    public void lblAssinarClick(Event<Object> event) {
        String linkAssinatura = tbSolicitacoesAssinaturas.getURL_ASSINATURA().asString();
        FormUtil.redirect(linkAssinatura, true);
    }
}
