package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.data.impl.View;
import freedom.util.EmpresaUtil;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FrmAcessoEmpresasA extends FrmAcessoEmpresasW {

    private static final long serialVersionUID = 20130827081850L;

    public static final String A_NOME = "A.NOME";

    private boolean editando = false;

    @Override
    public void FFormCreate(Event<Object> event) {
        try {
            this.habilitarComp(false);
            this.tbCrmEmpresaFuncao.setMaxRowCount(0);
            this.cmbEmpresa.setDelCallback(() -> {
                try {
                    this.tbCrmEmpresaFuncao.open();
                } catch (DataException dataException) {
                    this.tbCrmEmpresaFuncao.close();
                }
            });
            this.cmbFuncao.setDelCallback(() -> {
                try {
                    this.tbCrmEmpresaFuncao.open();
                } catch (DataException dataException) {
                    this.tbCrmEmpresaFuncao.close();
                }
            });
            this.cboEmpresaSelecionada.setDelCallback(() -> {
            });
            this.tbEmpresasFuncoes.setOrderBy(" DESCRICAO");
            this.tbEmpresasFuncoesLkp.setOrderBy(" DESCRICAO");
            this.tbEmpresasSelecionada.close();
            this.tbEmpresasSelecionada.clearFilters();
            this.tbEmpresasSelecionada.clearParams();
            this.tbEmpresasSelecionada.setOrderBy(FrmAcessoEmpresasA.A_NOME);
            this.tbEmpresasSelecionada.setFilterCOD_MATRIZ_MAIOR(" ");
            this.tbEmpresasSelecionada.setFilterSTATUS("S");
            this.tbEmpresasSelecionada.setMaxRowCount(0);
            this.tbEmpresasSelecionada.open();
            this.tbEmpresasSelecionada.first();
            this.cboEmpresaSelecionada.setValue(this.tbEmpresasSelecionada.getCOD_EMPRESA().asLong());
            this.tbEmpresas.setMaxRowCount(0);
            this.tbEmpresas.open();
            this.tbEmpresasLkp.setOrderBy(FrmAcessoEmpresasA.A_NOME);
            this.tbEmpresasLkp.setMaxRowCount(0);
            this.tbEmpresasLkp.open();
            this.tbEmpresasFuncoes.setMaxRowCount(0);
            this.tbEmpresasFuncoes.open();
            this.tbEmpresasFuncoesLkp.setMaxRowCount(0);
            this.tbEmpresasFuncoesLkp.open();
            this.tbCrmEmpresaFuncao.open();
        } catch (Exception exception) {
            EmpresaUtil.showError("Erro",
                    exception);
        }
    }

    @Override
    public void tbEmpresasBeforeOpen(Event<Object> event) {
        try {
            this.tbEmpresas.setFilterCOD_MATRIZ(" ");
            this.tbEmpresas.setFilterSTATUS("S");
            long codEmpresaSelecionada = this.cboEmpresaSelecionada.getValue().asLong();
            this.tbEmpresas.setFilterCOD_EMPRESA_DIFERENTE(codEmpresaSelecionada);
        } catch (DataException dataException) {
            throw new RuntimeException(dataException);
        }
        this.tbEmpresas.setOrderBy("NOME_EMPRESA");
    }

    @Override
    public void cboEmpresaSelecionadaChange(Event<Object> event) {
        try {
            this.tbCrmEmpresaFuncao.open();
            this.tbEmpresas.open();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao listar funções cruzadas",
                    dataException);
        }
    }

    @Override
    public void tbCrmEmpresaFuncaoBeforeOpen(Event<Object> event) {
        boolean tbEmpresasSelecionadaNotEmpty = !this.tbEmpresasSelecionada.isEmpty();
        if (tbEmpresasSelecionadaNotEmpty
                && !this.cboEmpresaSelecionada.getValue().isNull()) {
            try {
                this.fazFiltro();
            } catch (
                    DataException dataException
            ) {
                throw new RuntimeException(
                        dataException
                );
            }
        }
    }

    @Override
    public void btnIncluirFuncaoEmpresaClick(final Event<Object> event) {
        if (this.gridFuncao.getSelectionCount() > 0) {
            if (this.gridEmpresa.getSelectionCount() > 0) {
                List<Integer> listaDeFuncoesSelecionadas = this.gridFuncao.getSelectedIndices(true);
                List<Integer> listaDeEmpresasSelecionadas = this.gridEmpresa.getSelectedIndices(true);
                try {
                    for (Integer elementoDaListaDeFuncoesSelecionadas : listaDeFuncoesSelecionadas) {
                        View vEmpresasFuncoes = this.tbEmpresasFuncoes.getView();
                        vEmpresasFuncoes.gotoBookmark(elementoDaListaDeFuncoesSelecionadas);
                        for (Integer elementoDaListaDeEmpresasSelecionadas : listaDeEmpresasSelecionadas) {
                            View vEmpresas = this.tbEmpresas.getView();
                            vEmpresas.gotoBookmark(elementoDaListaDeEmpresasSelecionadas);
                            if (!this.tbCrmEmpresaFuncao.locate("COD_EMPRESA_PARM,COD_FUNCAO,COD_EMPRESA_ACESSO",
                                    this.cboEmpresaSelecionada.getValue().asLong(),
                                    vEmpresasFuncoes.getField("COD_FUNCAO").asInteger(),
                                    vEmpresas.getField("COD_EMPRESA").asInteger())) {
                                this.tbCrmEmpresaFuncao.append();
                                this.tbCrmEmpresaFuncao.setCOD_EMPRESA_PARM(this.cboEmpresaSelecionada.getValue().asLong());
                                this.tbCrmEmpresaFuncao.setCOD_EMPRESA_ACESSO(vEmpresas.getField("COD_EMPRESA").asLong());
                                this.tbCrmEmpresaFuncao.setCOD_FUNCAO(vEmpresasFuncoes.getField("COD_FUNCAO").asLong());
                                this.tbCrmEmpresaFuncao.setNOME_EMPRESA(vEmpresas.getField("NOME").asString());
                                this.tbCrmEmpresaFuncao.setNOME_FUNCAO(vEmpresasFuncoes.getField("DESCRICAO").asString());
                                this.tbCrmEmpresaFuncao.setNOME_INITCAP_COD_EMPRESA(vEmpresas.getField("NOME_INITCAP_COD_EMPRESA").asString());
                                this.tbCrmEmpresaFuncao.setNOME_COD_FUNCAO(vEmpresasFuncoes.getField("DESCRICAO_CODFUNCAO").asString());
                                this.tbCrmEmpresaFuncao.post();
                            }
                        }
                    }
                } catch (DataException dataException) {
                    EmpresaUtil.showError("Erro ao incluir",
                            dataException);
                }
            } else {
                EmpresaUtil.showInformationMessage("Selecione alguma empresa.");
            }
        } else {
            EmpresaUtil.showInformationMessage("Selecione alguma função.");
        }
    }

    @Override
    public void btnExcluirFuncaoEmpresaClick(final Event<Object> event) {
        //a chamada do super garante a correta utilização da validação de acesso
        if (this.gridEmpresaFuncao.getSelectionCount() > 0) {
            Object[] a = this.gridEmpresaFuncao.getSelectedIndices(false).toArray();
            for (Object obj : a) {
                try {
                    this.tbCrmEmpresaFuncao.gotoBookmark((Integer) obj);
                    this.tbCrmEmpresaFuncao.delete();
                } catch (DataException dataException) {
                    EmpresaUtil.showError("Erro ao remover",
                            dataException);
                }
            }
        } else {
            EmpresaUtil.showInformationMessage("Selecione alguma função cruzada.");
        }
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        //a chamada do super garante a correta utilização da validação de acesso
        if (this.cboEmpresaSelecionada.getValue().isNull()) {
            EmpresaUtil.showInformationMessage("Selecione alguma empresa.");
            return;
        }
        this.habilitarComp(true);
    }

    void habilitarComp(boolean value) {
        this.btnAlterar.setEnabled(!value);
        this.cboEmpresaSelecionada.setEnabled(!value);
        this.btnSalvar.setEnabled(value);
        this.btnCancelar.setEnabled(value);
        this.gridEmpresa.setEnabled(value);
        this.gridEmpresaFuncao.setEnabled(value);
        this.gridFuncao.setEnabled(value);
        this.btnIncluirFuncaoEmpresa.setEnabled(value);
        this.btnExcluirFuncaoEmpresa.setEnabled(value);
        this.editando = value;
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            this.tbCrmEmpresaFuncao.applyUpdates();
            this.tbCrmEmpresaFuncao.commitUpdates();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao Salvar",
                    dataException);
        }
        this.habilitarComp(false);
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        //a chamada do super garante a correta utilização da validação de acesso
        this.habilitarComp(false);
        try {
            this.tbEmpresas.open();
            this.tbEmpresasSelecionada.open();
            this.tbCrmEmpresaFuncao.close();
            this.cboEmpresaSelecionada.setValue(null);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao cancelar",
                    dataException);
        }
    }

    @Override
    public void cmbFuncaoChange(Event<Object> event) {
        if (this.cboEmpresaSelecionada.getValue().isNull()) {
            return;
        }
        if (this.editando) {
            this.cmbFuncao.setValue(event.getOldValue());
            EmpresaUtil.showInformationMessage("Termine a edição antes de filtrar.");
            return;
        }
        try {
            this.tbCrmEmpresaFuncao.open();
        } catch (DataException dataException) {
            Logger.getLogger(FrmAcessoEmpresasA.class.getName()).log(Level.SEVERE,
                    null,
                    dataException);
        }

    }

    @Override
    public void cmbEmpresaChange(Event<Object> event) {
        if (this.cboEmpresaSelecionada.getValue().isNull()) {
            return;
        }
        if (this.editando) {
            this.cmbEmpresa.setValue(event.getOldValue());
            EmpresaUtil.showInformationMessage("Termine a edição antes de filtrar.");
            return;
        }
        try {
            this.tbCrmEmpresaFuncao.open();
        } catch (DataException dataException) {
            Logger.getLogger(FrmAcessoEmpresasA.class.getName()).log(Level.SEVERE,
                    null,
                    dataException);
        }

    }

    void fazFiltro() throws DataException {
        this.tbCrmEmpresaFuncao.clearFilters();
        this.tbCrmEmpresaFuncao.clearParams();
        this.tbCrmEmpresaFuncao.removeFilter("COD_EMPRESA_ACESSO");
        if (!this.cmbEmpresa.getValue().isNull()) {
            this.tbCrmEmpresaFuncao.setFilterCOD_EMPRESA_ACESSO(this.cmbEmpresa.getValue().asLong());
        }
        if (!this.cmbFuncao.getValue().isNull()) {
            this.tbCrmEmpresaFuncao.setFilterCOD_FUNCAO(this.cmbFuncao.getValue().asLong());
        }
        this.tbCrmEmpresaFuncao.setFilterCOD_EMPRESA_PARM(this.cboEmpresaSelecionada.getValue().asLong());
        this.tbCrmEmpresaFuncao.setOrderBy("NOME_EMPRESA");
    }

    @Override
    public void tbEmpresasLkpBeforeOpen(Event<Object> event) {
        try {
            this.tbEmpresasLkp.setFilterCOD_MATRIZ(" ");
            this.tbEmpresasLkp.setFilterSTATUS("S");
        } catch (DataException dataException) {
            throw new RuntimeException(dataException);
        }
        this.tbEmpresasLkp.setOrderBy(FrmAcessoEmpresasA.A_NOME);
    }

    @Override
    public void iconClassHelpClick(final Event<Object> event) {
        FormUtil.redirect(
                "http://ajuda.nbsi.com.br:84/index.php/Acesso_a_Empresa"
                ,true
        );
    }

}