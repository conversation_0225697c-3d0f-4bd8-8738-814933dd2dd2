package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAdicMargMinMarkupCliEmpW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import lombok.Getter;

public class FrmAdicMargMinMarkupCliEmpA extends FrmAdicMargMinMarkupCliEmpW {

    private static final long serialVersionUID = 20130827081850L;

    private final double codCliente;

    private double codEmpresa;

    private double margemMinima;

    @Getter
    private boolean fechadoAoClicarEmSalvar = false;

    public FrmAdicMargMinMarkupCliEmpA(
            double codCliente
            ,double codEmpresa
            ,double margemMinima
    ) {
        this.codCliente = codCliente;
        this.codEmpresa = codEmpresa;
        this.margemMinima = margemMinima;
        this.carregarEmpresas();
        this.cboEmpresa.setValue(
                this.codEmpresa
        );
        this.edtMargemMinima.setValue(
                margemMinima
        );
    }

    private void incluirAlterarMargemMinimaMarkupClienteEmpresa(
            double codCliente
            ,double codEmpresa
            ,double margemMinima
    ) {
        try {
            this.rn.incluirAlterarMargemMinimaMarkupClienteEmpresa(
                    codCliente
                    ,codEmpresa
                    ,margemMinima
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao incluir/alterar margem mínima markup cliente/empresa"
                    ,dataException
            );
        }
    }

    private void carregarEmpresas() {
        try {
            this.rn.carregarEmpresas();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar empresas"
                    ,dataException
            );
        }
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        this.codEmpresa = this.cboEmpresa.getValue().asDecimal();
        String mensagem;
        if (this.codEmpresa == 0.0) {
            mensagem = (
                    "O campo \""
                            + this.lblEmpresaLetraDesconto.getCaption()
                            + "\" deve ser preenchido."
            );
            Dialog.create()
                    .title("Informação")
                    .message(mensagem)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                        this.cboEmpresa.setFocus();
                        this.cboEmpresa.setOpen(true);
                    }));
            return;
        }
        this.margemMinima = this.edtMargemMinima.getValue().asDecimal();
        if ((this.margemMinima <= 0.0)
                || (this.margemMinima >= 100.0)) {
            mensagem = (
                    "O campo \""
                            + this.lblMargemMinima.getCaption()
                            + "\" deve ser preenchido com valor maior que \"0\" (zero) e menor que \"100\" (cem)."
            );
            Dialog.create()
                    .title("Informação")
                    .message(mensagem)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                        this.edtMargemMinima.setFocus();
                        this.edtMargemMinima.setSelectionRange(0,
                                (this.edtMargemMinima.getValue().asString().length() + 1));
                    }));
            return;
        }
        this.incluirAlterarMargemMinimaMarkupClienteEmpresa(
                this.codCliente
                ,this.codEmpresa
                ,this.margemMinima
        );
        this.fechadoAoClicarEmSalvar = true;
        this.close();
    }

    @Override
    public void cboEmpresaEnter(Event<Object> event) {
        this.btnSalvarClick(event);
    }

    @Override
    public void edtMargemMinimaEnter(Event<Object> event) {
        this.btnSalvarClick(event);
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        boolean cboEmpresaEnabled = this.cboEmpresa.isEnabled();
        String empresa = this.cboEmpresa.getText().trim();
        boolean edtMargemMinimaEnabled = this.edtMargemMinima.isEnabled();
        if (cboEmpresaEnabled
                && empresa.isEmpty()) {
            FreedomUtilities.invokeLater(() -> {
                this.cboEmpresa.setFocus();
                this.cboEmpresa.setOpen(true);
            });
        } else if (edtMargemMinimaEnabled) {
            this.edtMargemMinima.setFocus();
            this.edtMargemMinima.setSelectionRange(0,
                    (this.edtMargemMinima.getValue().asString().length() + 1)
            );
        }
    }

}
