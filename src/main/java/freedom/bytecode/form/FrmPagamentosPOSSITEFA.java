package freedom.bytecode.form;

import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.bytecode.form.wizard.*;
import freedom.util.*;
import java.util.Date;
import freedom.client.event.*;
import freedom.data.DataException;

public class FrmPagamentosPOSSITEFA extends FrmPagamentosPOSSITEFW {

    private static final long serialVersionUID = 20130827081850L;

    @Override
    public void FFormCreate(Event<Object> event) {
        String loginUsuarioLogado = EmpresaUtil.getUserLogged();
        long codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();
        this.carregarCboEmpresa(
                loginUsuarioLogado
                ,codEmpresaUsuarioLogado
        );
        int tbEmpresasCount = this.tbEmpresas.count();
        if (tbEmpresasCount > 1) {
            this.cboEmpresa.setFocus();
        } else {
            this.edtCPFCNPJCliente.setFocus();
        }
    }

    @Override
    public void edtCPFCNPJClienteExit(Event<Object> event) {
        String cpfCNPJ = StringUtil.removerCaracteresNaoNumericos(
                this.edtCPFCNPJCliente.getValue().asString()
        );
        this.edtCPFCNPJCliente.setValue(
                cpfCNPJ
        );
    }

    @Override
    public void edtCPFCNPJClienteEnter(Event<Object> event) {
        this.pesquisar();
    }

    private void pesquisar() {
        long codEmpresa = this.cboEmpresa.getValue().asLong();
        if (codEmpresa == 0L) {
            String mensagem = (
                    Constantes.O_CAMPO
                            + this.lblEmpresa.getCaption()
                            + Constantes.DEVE_SER_PREENCHIDO
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.cboEmpresa          // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.cboEmpresa.setFocus();
            this.cboEmpresa.setOpen(
                    true
            );
            return;
        }
        long codCliente = this.edtCPFCNPJCliente.getValue().asLong();
        long codOrcMapa = this.edtOrcamentoPreNota.getValue().asLong();
        Date dataEmissao = this.dtEmissaoLead.getValue().asDate();
        if ((codCliente == 0L)
                && (codOrcMapa == 0L)
                && (dataEmissao == null)) {
            String mensagem = (
                    "Preencha o campo \""
                            + this.lblCPFCNPJCliente.getCaption()
                            + "\" ou \""
                            + this.lblOrcamentoPreNota.getCaption()
                            + "\" ou \""
                            + this.lblEmissao.getCaption()
                            + "\"."
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtCPFCNPJCliente   // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.edtCPFCNPJCliente.setFocus();
            return;
        }
        this.carregarGrdPagamentos(
                codEmpresa
                ,codCliente
                ,codOrcMapa
                ,dataEmissao
        );
        boolean tbPagamentosPosSitefEmpty = this.tbPagamentosPosSitef.isEmpty();
        if (tbPagamentosPosSitefEmpty) {
            Dialog.create()
                    .showNotificationInfo(
                            "Nenhum registro foi encontrado."
                            ,Constantes.MIDDLE_CENTER // A mensagem se sobrepõe à âncora, com a âncora e a mensagem alinhadas no centro-meio
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.grdPagamentos       // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
        }
        long idPagamento = this.tbPagamentosPosSitef.getID_PAGAMENTO().asLong();
        this.carregarGrdParcelas(
                idPagamento
        );
        boolean possuiAcessoK0630 = EmpresaUtil.validarAcesso(
                "K0630"
                ,false
        );
        this.btnExcluir.setVisible(
                possuiAcessoK0630
                && (idPagamento > 0L)
        );
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        FormUtil.closeTab(
                this
        );
        this.close();
    }

    @Override
    public void btnPesquisarClick(Event<Object> event) {
        this.pesquisar();
    }

    @Override
    public void edtOrcamentoPreNotaEnter(Event<Object> event) {
        this.pesquisar();
    }

    @Override
    public void dtEmissaoLeadEnter(Event<Object> event) {
        this.pesquisar();
    }

    private void carregarCboEmpresa(
            String usuario
            ,long codEmpresa
    ) {
        try {
            this.rn.carregarCboEmpresa(
                    usuario
                    ,codEmpresa
            );
            int tbLeadsEmpresasUsuariosCount = this.tbEmpresas.count();
            if (tbLeadsEmpresasUsuariosCount == 1) {
                long tbLeadsEmpresasUsuariosCodEmpresa = this.tbEmpresas.getCOD_EMPRESA().asLong();
                this.cboEmpresa.setValue(
                        tbLeadsEmpresasUsuariosCodEmpresa
                );
            }
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar as empresas"
                    ,dataException
            );
        }
    }

    private void carregarGrdPagamentos(
            long codEmpresa
            , long codCliente
            , long codOrcMapa
            , Date dataEmissao
    ) {
        try {
            this.rn.carregarGrdPagamentos(
                    codEmpresa
                    ,codCliente
                    ,codOrcMapa
                    ,dataEmissao
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de pagamentos confirmados sem nota fiscal"
                    ,dataException
            );
        }
    }

    private void carregarGrdParcelas(
            long idPagamento
    ) {
        try {
            this.rn.carregarGrdParcelas(
                    idPagamento
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de parcelas"
                    ,dataException
            );
        }
    }

    @Override
    public void btnExcluirClick(Event<Object> event) {
        boolean possuiAcessoK0630 = EmpresaUtil.validarAcesso(
                "K0630"
                ,true
        );
        if (!possuiAcessoK0630) {
            return;
        }
        String mensagem = (
                "Deseja realmente excluir o pagamento \""
                        + this.tbPagamentosPosSitef.getID_PAGAMENTO().asString()
                        + "\"?"
        );
        Dialog.create()
                .title(Constantes.CONFIRMACAO)
                .message(mensagem)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        long idPagamento = this.tbPagamentosPosSitef.getID_PAGAMENTO().asLong();
                        String mensagemAoExcluirPagamentoPOSSITEF = this.apagarPagamentoPOSSITEF(
                                idPagamento
                        );
                        if (!mensagemAoExcluirPagamentoPOSSITEF.equals("S")) {
                            EmpresaUtil.showInformationMessage(
                                    "Erro ao excluir o pagamento POS SITEF:"
                                            + System.lineSeparator()
                                            + System.lineSeparator()
                                            + mensagemAoExcluirPagamentoPOSSITEF
                            );
                            return;
                        }
                        this.pesquisar();
                    }
                });
    }

    @Override
    public void tbPagamentosPosSitefAfterScroll(Event<Object> event) {
        long idPagamento = this.tbPagamentosPosSitef.getID_PAGAMENTO().asLong();
        this.carregarGrdParcelas(
                idPagamento
        );
    }

    @Override
    public void btnPesquisarClienteClick(Event<Object> event) {
        FrmPesquisaClienteA frmPesquisaClienteA = new FrmPesquisaClienteA();
        long codCliente = this.edtCPFCNPJCliente.getValue().asLong();
        if (codCliente > 0L) {
            frmPesquisaClienteA.edtPesquisarCliente.setValue(
                    codCliente
            );
        }
        FormUtil.doShow(
                frmPesquisaClienteA
                ,t -> {
                    boolean frmPesquisaClienteAFechadoAoAceitar = frmPesquisaClienteA.isFechadoAoAceitar();
                    if (frmPesquisaClienteAFechadoAoAceitar) {
                        long frmPesquisaClienteACodCliente = frmPesquisaClienteA.tbLeadsConsultaClientes.getCOD_CLIENTE().asLong();
                        this.edtCPFCNPJCliente.setValue(
                                frmPesquisaClienteACodCliente
                        );
                        this.edtOrcamentoPreNota.setFocus();
                    }
                });
    }

    private String apagarPagamentoPOSSITEF(
            long idPagamento
    ) {
        String retFuncao = "";
        try {
            retFuncao = this.rn.apagarPagamentoPOSSITEF(
                    idPagamento
            );
        } catch (
                DataException dataException
        ) {
            String mensagem = (
                    "Erro ao apagar pagamento POS SITEF \""
                            + idPagamento
                            + "\""
            );
            EmpresaUtil.showError(
                    mensagem
                    ,dataException
            );
        }
        return retFuncao;
    }

}