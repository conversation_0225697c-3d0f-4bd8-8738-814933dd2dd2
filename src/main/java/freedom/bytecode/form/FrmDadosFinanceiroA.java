package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmDadosFinanceiroW;
import freedom.client.event.Event;
import freedom.data.DataException;

public class FrmDadosFinanceiroA extends FrmDadosFinanceiroW {

    private static final long serialVersionUID = 20130827081850L;

    public FrmDadosFinanceiroA() {

    }

    public String carregarDadosFinanceiro(Double codCliente) throws DataException {
        String SucessoCarregamento = "";
        if (!codCliente.equals(0.0)) {
            SucessoCarregamento = rn.buscarDadosFinanceirosClientes(codCliente);
            if (rn.ClienteEstaBloqueado()){
                //FHBox2.setFlexHflex("ftFalse");
                hbMotivoBloqueio.setVisible(true);
                FrmDadosFinanceiro.setHeight((int) (FrmDadosFinanceiro.getHeight() + hbMotivoBloqueio.getHeight()));
                FrmDadosFinanceiro.applyProperties();
            }
            //FVBox1.invalidate();
            return SucessoCarregamento;
        }
        SucessoCarregamento = "Codigo de Cliente inválido";
        return SucessoCarregamento;
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        close();
    }
}
