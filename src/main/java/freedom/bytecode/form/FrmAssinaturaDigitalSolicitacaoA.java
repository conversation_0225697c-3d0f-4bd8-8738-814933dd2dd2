package freedom.bytecode.form;
import freedom.client.controls.impl.*;
import freedom.client.util.Dialog;
import freedom.bytecode.form.wizard.*;
import freedom.client.util.FormUtil;
import freedom.client.util.FreedomUtilities;
import freedom.client.util.IDialog;
import freedom.commons.lang.IWorkList;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.SequenceUtil;
import freedom.data.Value;
import freedom.data.impl.View;
import freedom.util.*;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalUtils;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalDocumento;
import freedom.util.assinaturaDigital.NaoEncontrouDocumentoException;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;
import freedom.util.file.FolderUtils;
import freedom.util.file.PdfUtils;
import freedom.util.pkg.PkgAssinaturaDigitalA;
import lombok.Getter;
import lombok.Setter;
import org.json.JSONObject;
import freedom.client.event.*;
import freedom.data.DataException;
import org.zkoss.zul.Hlayout;
import org.zkoss.zul.Vlayout;
import org.zkoss.zul.Window;

import java.io.IOException;
import java.util.*;

@Getter
@Setter
public class FrmAssinaturaDigitalSolicitacaoA extends FrmAssinaturaDigitalSolicitacaoW {
    private static final long serialVersionUID = 20130827081850L;
    private final PkgAssinaturaDigitalA pkgAssinaturaDigitalA = new PkgAssinaturaDigitalA();
    private static final IWorkList wl = WorkListFactory.getInstance();
    private final List<TFMenuItem> listaOpcoesMenuDocumentosABaixar = new ArrayList<>();
    private static final double COD_PACOTE_FORSIGN = 19.0; /* atualmente so existe o pacote 19, que é o FORSIGN */
    private List<CrmAssinaturaDigitalDocumento> listaDocumentosAvulso = new ArrayList<>();
    String bookemarkTAG;
    TipoAssinaturaStrategy tipoAssinatura;
    JSONObject jsonParametros;
    CrmAssinaturaDigitalUtils.EnTipoEnvio tipoEnvioSelecionado;
    Boolean isPreview;
    Double idEnvelopeGerado;
    Double idEnvelopeBuscado;

    private final double codEmpresaLogada = EmpresaUtil.getCodEmpresaUserLogged();
    private final String usuarioLogado = EmpresaUtil.getUserLogged().trim();

    public static boolean validarAcessoBloqueioAssinaturaPresencial() {
        String codAcesso = EmpresaUtil.isCrmService() ? "B0283" : "KXXXX";
        if (EmpresaUtil.validarAcesso(codAcesso,false)){
            EmpresaUtil.showMessage("Permissão", "Sem permissão para realizar a assinatura presencial. ACESSO {"+ codAcesso +"}");
            return true;
        }
        return false;
    }


    private CrmAssinaturaDigitalUtils.EnStatusAssinaturaSignatario getStatusAssinaturaDestinatario(){
        return CrmAssinaturaDigitalUtils.EnStatusAssinaturaSignatario.fromString(tbSolicitacoesAssinaturas.getSTATUS_ANDAMENTO_ASSINATURA().isNull() ? "PENDENTE" : tbSolicitacoesAssinaturas.getSTATUS_ANDAMENTO_ASSINATURA().toString());
    }

    private CrmAssinaturaDigitalUtils.EnStatusAssinatura getStatusAssinatura(){
        return CrmAssinaturaDigitalUtils.EnStatusAssinatura.fromString(tbNbsapiEnvelopeFilaResumo.getDESC_STATUS().isNull() ? "PENDENTE" : tbNbsapiEnvelopeFilaResumo.getDESC_STATUS().toString());
    }

    private CrmAssinaturaDigitalUtils.EnTipoEnvio getTipoEnvio(){
        return tbNbsapiEnvelopeFilaResumo.getPRESENCIAL().asString().equals("S") ? CrmAssinaturaDigitalUtils.EnTipoEnvio.PRESENCIAL : CrmAssinaturaDigitalUtils.EnTipoEnvio.REMOTO;
    }


    public FrmAssinaturaDigitalSolicitacaoA() {
        hboxSelcaoRemoto.setAttribute("SCLASS_BASE", "hbbuttonleftradius");
        hboxSelcaoPresencial.setAttribute("SCLASS_BASE", "hbbuttonrightradius");
        ((Hlayout)hboxIrParaVersaoAtual.getImpl()).setStyle("box-shadow: 3px 3px 2px #888888;background:#FFD58D;border-style:outset;border-width:1px;border-color:#EDEDED;padding-left:3px;margin-left:4px;margin-right:3px;");
        this.setHeight(350);
    }


    public boolean carregarAssinaturaDigital(TipoAssinaturaStrategy tipoAssinatura, JSONObject jsonParametros){
        return carregarAssinaturaDigital(tipoAssinatura, jsonParametros, null);
    }

    /**
     * valida se pode abrir a assinatura digital
     * @param tipoAssinatura
     * @param jsonParametros
     * @param idEnvelopeBuscado
     * @return
     */
    public boolean carregarAssinaturaDigital(TipoAssinaturaStrategy tipoAssinatura, JSONObject jsonParametros, Double idEnvelopeBuscado){
        setTipoAssinatura(tipoAssinatura);
        setJsonParametros(jsonParametros);
        setIdEnvelopeBuscado(idEnvelopeBuscado);
        try {
            /* valida se utiliza assinatura digital */
            if (!CrmAssinaturaDigitalUtils.usaAssinaturaDigital(codEmpresaLogada)){
                Dialog.create().title("Assinatura Digital").message("Recurso não habilitado!\n\nDeseja obter mais informações sobre as vantagens e como contratar?").showConfirm((String dialogResult)-> {
                    switch (dialogResult){
                        case "1": {
                            FormUtil.redirect("https://ajuda.nbsi.com.br:84/index.php?title=Assinatura_Digital", true);
                            break;
                        }
                        case "0":{
                            break;
                        }
                    }
                }, "Quero", "Agora não");
                return false;
            }
            /* valida o acesso a assinatura digital */
            if (!CrmAssinaturaDigitalUtils.validarAcessoAssinaturaDigital()){
                return false;
            }
            
            /* valida se tem acesso para o tipo de assinatura especifico*/
            if (!getTipoAssinatura().validarAcessoTipoAssinatura()){
                return false;
            }

            initForm();
            return true;
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao carregar assinatura digital", e);
            return false;
        }
    }

    public void initForm() throws DataException {
        rn.filtrarTbAssinaturaDigital(getTipoAssinatura());
        validarCrmAssinaturaDigital();
        rn.filtrarTbAssinaturaDigitalDocumentos();
        /* caso getIdEnvelopeBuscado seja null, então abre o ultimo envelope assinatura enviado, caso não exista abre como pendente */
        rn.filtrarTbNbsapiEnvelopeFilaResumo(getTipoAssinatura(), getJsonParametros(), getIdEnvelopeBuscado());
        rn.filtrarTbSolicitacoesAssinaturas(getTipoAssinatura(), getJsonParametros(),  rn.getIdEnvelope());
        validarCrmSolicitacaoAssinatura();
        rn.filtrarTbNbsapiDocumentosAssinados();
        atualizarTelaDeAcordoComStatusAssinatura();
    }

    /**
     * atualiza a tela conforme os status da assinatura
     */
    public void atualizarTelaDeAcordoComStatusAssinatura(){

        CrmAssinaturaDigitalUtils.EnStatusAssinatura statusAssinatura = this.getStatusAssinatura();
        setTipoEnvio(getTipoEnvio());

        /* label do form */
        this.setCaption("Assinatura Digital: " + tbAssinaturaDigital.getDESCRICAO().asString());

        /* valida se é somente para vizualização */
        //boolean isPreview =  statusAssinatura.equals(CrmAssinaturaDigital.EnStatusAssinatura.ENVELOPE_CANCELADO) || statusAssinatura.equals(CrmAssinaturaDigital.EnStatusAssinatura.ARQUIVADO);
        setIsPreview(statusAssinatura.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.ENVELOPE_CANCELADO) || statusAssinatura.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.ARQUIVADO) || statusAssinatura.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.EXPIRADO));
        /* label ir para versão mais recente */
        hboxIrParaVersaoAtual.setVisible(getIsPreview());
        FreedomUtilities.invokeLater(() -> {
            Window window = (Window)((Vlayout) this.getImpl()).getParent();
            window.setHeight(getIsPreview() ? "375px" : "350px");
        });

        /* botões de ação do topo */
        btnCancelar.setVisible(!getIsPreview() && (statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.PREPARANDO_ENVIO
                || statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.AGUARDANDO_ENVIO
                || statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.ENVIAR_DOCUMENTOS
                || statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.AGUARDANDO_ASSINATURAS)
        );
        btnArquivar.setVisible(!getIsPreview() &&statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.DOCS_ASSINADOS_BAIXADOS);
        btnReenviar.setVisible(!getIsPreview() && statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.FALHA);
        btnEnviar.setVisible(!getIsPreview() && statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE);


        /* campo de observador */
        vboxObservador.setVisible(statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE || (!tbNbsapiEnvelopeFilaResumo.getEMAIL_OUVINTE_ASS_DIG().asString().isEmpty()));
        edObservador.setEnabled(statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE);
        edObservador.setValue(tbNbsapiEnvelopeFilaResumo.getEMAIL_OUVINTE_ASS_DIG().asString());

        /* labels e icones de status */
        TFPanelButtonItem pnlStatusAssinatura = (TFPanelButtonItem) FPanelStatusAssinatura.findItemByName("FPanelStatusAssinaturaLbl2");
        pnlStatusAssinatura.setFontColor(statusAssinatura.getFontColor());
        pnlStatusAssinatura.setCaption(statusAssinatura.getCaption());
        FPanelStatusAssinatura.rebuild();

        /* QR somente se for presencial e estiver aguardando assinatura */
        vBoxBtnAcaoLerQRCodePrincipal.setVisible(statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.AGUARDANDO_ASSINATURAS && getTipoEnvio() == CrmAssinaturaDigitalUtils.EnTipoEnvio.PRESENCIAL);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setVisible(statusAssinatura == CrmAssinaturaDigitalUtils.EnStatusAssinatura.AGUARDANDO_ASSINATURAS && getTipoEnvio() == CrmAssinaturaDigitalUtils.EnTipoEnvio.REMOTO);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setVisible(statusAssinatura.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.DOCS_ASSINADOS_BAIXADOS) || statusAssinatura.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.ARQUIVADO));
        //hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setVisible(!getIsPreview() && (statusAssinatura != CrmAssinaturaDigital.EnStatusAssinatura.DOCUMENTOS_ASSINADOS && statusAssinatura != CrmAssinaturaDigital.EnStatusAssinatura.DOCS_ASSINADOS_BAIXADOS && statusAssinatura != CrmAssinaturaDigital.EnStatusAssinatura.SOLICITACAO_CANCELAMENTO && statusAssinatura != CrmAssinaturaDigital.EnStatusAssinatura.ENVELOPE_CANCELADO));

        montarMenuVerDocumentos();

        vBoxGridSolicitacaoAssinatura.applyProperties();
        vBoxGridSolicitacaoAssinatura.invalidate();

    }

    public void validarCrmAssinaturaDigital() throws DataException {
        if (rn.tbAssinaturaDigitalIsEmpty()) {
            throw new DataException("Assinatura digital não cadastrada em CRM_ASSINATURA_DIGITAL: " + getTipoAssinatura().getTipo());
        }
        String chavesFaltantes = CrmAssinaturaDigitalUtils.validarJsonChavesFaltantesNecessariasAssinaturaDigital(tbAssinaturaDigital.getTIPO_CODIGO().asString(), getJsonParametros());
        if (!chavesFaltantes.isEmpty()) {
            throw new DataException("Parametros pendentes para a assinatura digital: " + getTipoAssinatura().getTipo() + " os seguintes campos devem ser informados nos Parametros Json {" + chavesFaltantes + "}");
        }
    }

    public void validarCrmSolicitacaoAssinatura() throws DataException {
        if (rn.tbSolicitacoesAssinaturasIsEmpty()) {
            throw new DataException("Nenhum Signatario cadastrado em CRM_ASSINATURA_DIGITAL_SIGN, associado a assinaura digital: " + getTipoAssinatura().getTipo());
        }
    }

    public void isInseridoDocumentoEnvelope(ISession session) throws DataException, NaoEncontrouDocumentoException {
        tbNbsapiDocumentos.close();
        tbNbsapiDocumentos.clearFilters();
        tbNbsapiDocumentos.clearParams();
        tbNbsapiDocumentos.setFilterID_ENVELOPE(idEnvelopeGerado);
        tbNbsapiDocumentos.setSession(session);
        tbNbsapiDocumentos.open();
        if (tbNbsapiDocumentos.count() <= 0) {
            throw new NaoEncontrouDocumentoException("Nenhum documento foi anexado ao envelope: cadastre em CRM_ASSINATURA_DIGITAL_DOC, ou adicione algum documento avulso (addDocumentosAvulso), ou via tipoAssinaturaStrategy (getDocumentosParaAssinar)");
        }
    }

    private void setTipoEnvio(CrmAssinaturaDigitalUtils.EnTipoEnvio tipoEnvio) {
        String colorDestaque = "clSilver";
        String colorDefault = "#FFFFFF";
        hboxSelcaoRemoto.setColor(colorDefault);
        hboxSelcaoPresencial.setColor(colorDefault);

        switch (tipoEnvio) {
            case PRESENCIAL:
                hboxSelcaoPresencial.setColor(colorDestaque);
                this.setTipoEnvioSelecionado(CrmAssinaturaDigitalUtils.EnTipoEnvio.PRESENCIAL);
                break;
            case REMOTO:
                hboxSelcaoRemoto.setColor(colorDestaque);
                this.setTipoEnvioSelecionado(CrmAssinaturaDigitalUtils.EnTipoEnvio.REMOTO);
                break;
        }
        hboxSelcaoRemoto.invalidate();
        hboxSelcaoPresencial.invalidate();
    }


    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

    @Override
    public void timerAtualizarStatusAssinaturaTimer(Event<Object> event) {
        try {
            boolean AssinaturaJaEnviada = !getStatusAssinatura().equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE) && !getStatusAssinatura().equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.DOCS_ASSINADOS_BAIXADOS);
            if (AssinaturaJaEnviada){
                atualizarStatusAssinatura();
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao atualizar", e);
        }
    }

    public void atualizarStatusAssinatura() throws DataException {
        tbSolicitacoesAssinaturas.disableControls();
        tbSolicitacoesAssinaturas.disableMasterTable();
        setBookemarkTAG(tbSolicitacoesAssinaturas.getTAG_DOCUMENTO().asString());
        this.initForm();
        tbSolicitacoesAssinaturas.locate("TAG_DOCUMENTO",getBookemarkTAG());
        tbSolicitacoesAssinaturas.enableControls();
        tbSolicitacoesAssinaturas.enableMasterTable();
    }


    @Override
    public void btnAceitarClick(Event<Object> event) {
        try {
            solicitarAssinatura();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao solicitar assinatura", e);
        } catch (NaoEncontrouDocumentoException e) {
            EmpresaUtil.showInformationMessage(e.getMessage());
        }
    }

    @Override
    public void btnReenviarClick(Event<Object> event) {
        try {
            solicitarReenvio();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao solicitar assinatura", e);
        }
    }

    public void solicitarReenvio() throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            pkgAssinaturaDigitalA.reprocessarEnvioAssinaturas(session, rn.getIdEnvelope(), COD_PACOTE_FORSIGN);
            session.commit();
            this.initForm();
        } catch (DataException e) {
            session.rollback();
            throw new DataException(e);
        } finally {
            session.close();
        }
    }

    @Override
    public void btnCancelarClick(Event<Object> event) {
        Dialog.create()
                .title("Cancelamento")
                .message("Tem certeza que deseja solicitar o cancelamento da assinatura?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            solicitarCancelamento(rn.getIdEnvelope());
                        } catch (DataException e) {
                            EmpresaUtil.showError("Erro ao solicitar cancelamento da assinatura", e);
                        }
                    }});
    }

    private void solicitarCancelamento(Double idEnvelopeParaCancelar) throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            pkgAssinaturaDigitalA.deletarOuCancelar(session, idEnvelopeParaCancelar, COD_PACOTE_FORSIGN);
            session.commit();
            abrirTelaLoadingAguardarCancelamentoAssinatura();
            //this.initForm();
        } catch (DataException e) {
            session.rollback();
            throw new DataException(e);
        } finally {
            session.close();
        }
    }

    public void solicitarAtualizacaoStatus() throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            pkgAssinaturaDigitalA.atualizarStatusEnvelope(session, rn.getIdEnvelope(), COD_PACOTE_FORSIGN);
            session.commit();
        } catch (DataException e) {
            session.rollback();
            throw new DataException(e);
        } finally {
            session.close();
        }
    }

    @Override
    public void btnArquivarClick(Event<Object> event) {
        Dialog.create()
                .title("Arquivar")
                .message("Tem certeza que deseja arquivar a assinatura?\nUma nova assinatura Pendente será gerada")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            solicitarArquivamento();
                        } catch (DataException e) {
                            EmpresaUtil.showError("Erro ao solicitar cancelamento da assinatura", e);
                        }
                    }});
    }

    private void solicitarArquivamento() throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            pkgAssinaturaDigitalA.arquivarEnvelope(session, rn.getIdEnvelope(), COD_PACOTE_FORSIGN);
            session.commit();
            abrirTelaLoadingAguardarArquivamentoAssinatura();
            //this.initForm();
        } catch (DataException e) {
            session.rollback();
            throw new DataException(e);
        } finally {
            session.close();
        }
    }

    @Override
    public void hboxSelcaoRemotoClick(Event<Object> event) {
        if (getStatusAssinatura() == CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE) {
            setTipoEnvio(CrmAssinaturaDigitalUtils.EnTipoEnvio.REMOTO);
        }
    }

    @Override
    public void hboxSelcaoPresencialClick(Event<Object> event) {
        if (getStatusAssinatura() == CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE) {
            if (validarAcessoBloqueioAssinaturaPresencial()) {
                setTipoEnvio(CrmAssinaturaDigitalUtils.EnTipoEnvio.REMOTO);
                return;
            }
            setTipoEnvio(CrmAssinaturaDigitalUtils.EnTipoEnvio.PRESENCIAL);
        }
    }

    public void solicitarAssinatura() throws DataException, NaoEncontrouDocumentoException {
        /* criar a sessão */
        ISession session;
        session = SessionFactory.getInstance().getSession();
        if (isSignatariosInvalidos()) {
            return;
        }
        try {
            session.open();
            adicionarEnvelope(session);
            adicionarSignatarios(session);
            adicionarDocumentosAvulso(session);
            adicionarDocumentosTipoStrategy(session);
            adicionarDocumentosCadastradosCrmAssinaturaDigitalDoc(session);
            isInseridoDocumentoEnvelope(session);
            solicitarEnvioEnvelope(session);
            //session.rollback();
            session.commit();
            abrirTelaLoadingAguardarEnvioAssinatura();
        } catch (DataException | NaoEncontrouDocumentoException e) {
            session.rollback();
            throw e;
        }finally{
            session.close();
        }
    }

    /**
     * Valida todos os signatarios da assinatura
     * @return return true caso o signatario seja valido
     */
    public boolean isSignatariosInvalidos() throws DataException {
        View viewSolicitacaoAssinaturas = tbSolicitacoesAssinaturas.getView();
        viewSolicitacaoAssinaturas.first();
        while (!viewSolicitacaoAssinaturas.eof()){
            if (CrmAssinaturaDigitalUtils.isSignatarioInvalido(viewSolicitacaoAssinaturas.getField("NOME").asString(), viewSolicitacaoAssinaturas.getField("EMAIL").asString(), viewSolicitacaoAssinaturas.getField("TELEFONE").asString(), getTipoEnvioSelecionado())){
                return true;
            }
            viewSolicitacaoAssinaturas.next();
        }
        return false;
    }



    private void adicionarEnvelope(ISession session) throws DataException {
        Double retFuncao;
        String tipoCodigo = tbAssinaturaDigital.getTIPO_CODIGO().asString();
        String codigo = CrmAssinaturaDigitalUtils.getValorCodigo(tbAssinaturaDigital.getTIPO_CODIGO().asString(), getJsonParametros());
        String tipoAssinatura = tbAssinaturaDigital.getTIPO_ASSINATURA().asString();
        String descricaoAssinatura = tbAssinaturaDigital.getDESCRICAO().asString();
        String observadores = edObservador.getValue().asString().replace(";", ",");
        String presencial = getTipoEnvioSelecionado() == CrmAssinaturaDigitalUtils.EnTipoEnvio.PRESENCIAL ? "S" : "N";
        double idSistema = tbAssinaturaDigital.getID_SISTEMA().asDecimal();
        retFuncao = pkgAssinaturaDigitalA.criarEnvelope(session, tipoCodigo, codigo, codEmpresaLogada, descricaoAssinatura, presencial, COD_PACOTE_FORSIGN, observadores, tipoAssinatura, idSistema);
        setIdEnvelopeGerado(retFuncao);
        if (getIdEnvelopeGerado() == null) {
            throw new DataException("N?o foi possivel gerar o envelope");
        }
    }

    private void adicionarSignatarios(ISession session) throws DataException {
        View viewSolicitacaoAssinaturas = tbSolicitacoesAssinaturas.getView();
        viewSolicitacaoAssinaturas.first();
        while (!viewSolicitacaoAssinaturas.eof()){
            String email = viewSolicitacaoAssinaturas.getField("EMAIL").asString();
            String nome = viewSolicitacaoAssinaturas.getField("NOME").asString();
            String telefone = viewSolicitacaoAssinaturas.getField("TELEFONE").asString();
            String metodoEnvio ="email"; /* por hora somente email */
            String tagDocumento = viewSolicitacaoAssinaturas.getField("TAG_DOCUMENTO").asString();
            pkgAssinaturaDigitalA.inserirAssinantesECopias(session, getIdEnvelopeGerado(), email, null,  nome, null, telefone, null, metodoEnvio, tagDocumento, COD_PACOTE_FORSIGN);
            viewSolicitacaoAssinaturas.next();
        }
    }

    /**
     * Função para adicionar documentos avulsos
     * @param nome nome do documento
     * @param conteudoPDF pdf, deve estar no formato base64. caso não esteja converter:
     *                    conversão: <pre>{@code Base64.getEncoder().encodeToString(conteudoPDF)}</pre>
     *
     *
     */
    public void addDocumento(String nome, String conteudoPDF, String tagsNecessarias) throws DataException {
        CrmAssinaturaDigitalDocumento documento = new CrmAssinaturaDigitalDocumento(nome, conteudoPDF, tagsNecessarias);
        String tagsPendentesNoPDF = CrmAssinaturaDigitalUtils.validarPdfTagsNecessariasFaltantes(conteudoPDF, tagsNecessarias);
        if (!tagsPendentesNoPDF.isEmpty()){
            throw new DataException("O PDF " + nome + " não é valido para assinatura digital, Não possui em seu corpo as tags: " + tagsPendentesNoPDF);
        }
        listaDocumentosAvulso.add(documento);
    }

    // Função para adicionar os documentos passados da lista no banco
    private void adicionarDocumentosAvulso(ISession session) throws DataException {
        tbNbsapiDocumentos.close();
        tbNbsapiDocumentos.clearFilters();
        tbNbsapiDocumentos.clearParams();
        //tbNbsapiDocumentos.setFilterID_ENVELOPE(getIdEnvelopeGerado());
        tbNbsapiDocumentos.setSession(session);

        for (CrmAssinaturaDigitalDocumento documento : listaDocumentosAvulso) {
            String nomeDocumento = documento.getNome();
            String conteudoPdf = documento.getPdfBase64();
            String tagsNecessariasPdf = documento.getTagsNecessariasPdf();

            // insere documento a ser assinado na tabela tbNbsapiDocumentos
            tbNbsapiDocumentos.append();
            tbNbsapiDocumentos.setID(SequenceUtil.nextVal("SEQ_NBSAPI_DOCUMENTOS"));
            tbNbsapiDocumentos.setID_ENVELOPE(getIdEnvelopeGerado());
            tbNbsapiDocumentos.setDOC_BASE64(conteudoPdf);
            tbNbsapiDocumentos.setNOME_DOCUMENTO(nomeDocumento);
            tbNbsapiDocumentos.setEXTENSAO_ARQ_DOC("pdf");
            tbNbsapiDocumentos.setSEQUENCIA(listaDocumentosAvulso.indexOf(documento) + 1);
            tbNbsapiDocumentos.setPROCESSADO("N");
            tbNbsapiDocumentos.post();
        }
        tbNbsapiDocumentos.applyUpdates();
        tbNbsapiDocumentos.commitUpdates();
    }

    // Função para adicionar os documentos que vem da strategia TipoAssinaturaStrategy
    private void adicionarDocumentosTipoStrategy(ISession session) throws DataException, NaoEncontrouDocumentoException {
        tbNbsapiDocumentos.close();
        tbNbsapiDocumentos.clearFilters();
        tbNbsapiDocumentos.clearParams();
        tbNbsapiDocumentos.setSession(session);

        String valorCodigo = CrmAssinaturaDigitalUtils.getValorCodigo(tbAssinaturaDigital.getTIPO_CODIGO().asString(), getJsonParametros());

        for (CrmAssinaturaDigitalDocumento documento : getTipoAssinatura().getDocumentosParaAssinar(valorCodigo)) {
            String nomeDocumento = documento.getNome();
            String conteudoPdf = documento.getPdfBase64();
            String tagsNecessariasPdf = documento.getTagsNecessariasPdf();

            String tagsPendentesNoPDF = CrmAssinaturaDigitalUtils.validarPdfTagsNecessariasFaltantes(conteudoPdf, tagsNecessariasPdf);
            if (!tagsPendentesNoPDF.isEmpty()){
                throw new DataException("O PDF " + nomeDocumento + " não é valido para assinatura digital, Não possui em seu corpo as tags: " + tagsPendentesNoPDF);
            }

            // insere documento a ser assinado na tabela tbNbsapiDocumentos
            tbNbsapiDocumentos.append();
            tbNbsapiDocumentos.setID(SequenceUtil.nextVal("SEQ_NBSAPI_DOCUMENTOS"));
            tbNbsapiDocumentos.setID_ENVELOPE(getIdEnvelopeGerado());
            tbNbsapiDocumentos.setDOC_BASE64(conteudoPdf);
            tbNbsapiDocumentos.setNOME_DOCUMENTO(nomeDocumento);
            tbNbsapiDocumentos.setEXTENSAO_ARQ_DOC("pdf");
            tbNbsapiDocumentos.setSEQUENCIA(listaDocumentosAvulso.indexOf(documento) + 1);
            tbNbsapiDocumentos.setPROCESSADO("N");
            tbNbsapiDocumentos.post();
        }
        tbNbsapiDocumentos.applyUpdates();
        tbNbsapiDocumentos.commitUpdates();
    }


    private void adicionarDocumentosCadastradosCrmAssinaturaDigitalDoc(ISession session) throws DataException {
        String chavesFaltantes;
        tbNbsapiDocumentos.close();
        tbNbsapiDocumentos.clearFilters();
        tbNbsapiDocumentos.clearParams();
        //tbNbsapiDocumentos.setFilterID_ENVELOPE(getIdEnvelopeGerado());
        tbNbsapiDocumentos.setSession(session);
        tbAssinaturaDigitalDoc.first();
        while (!tbAssinaturaDigitalDoc.eof()){
            /*chavesFaltantes = JasperReportUtil.validarJsonChavesFaltantesNecessariasRelatorioJasper(tbAssinaturaDigitalDoc.getRELATORIO_NOME().asString(), false, getJsonParametros());
            if (!chavesFaltantes.isEmpty()){
                throw new DataException("Parametros pendentes para o relatorio: " + tbAssinaturaDigitalDoc.getRELATORIO_NOME().asString() + " os seguintes campos devem ser informados nos Parametros Json {" + chavesFaltantes + "}");
            }*/
            byte[] pdfGerado = JasperReportUtil.gerarRelatorioJasper(tbAssinaturaDigitalDoc.getRELATORIO_NOME().asString(), false, getJsonParametros());
            String pdfGeradoBase64 = Base64.getEncoder().encodeToString(pdfGerado);
            int lastSeparatorIndex = tbAssinaturaDigitalDoc.getRELATORIO_NOME().asString().lastIndexOf("\\");
            String nomeDocumento = tbAssinaturaDigitalDoc.getRELATORIO_NOME().asString().substring(lastSeparatorIndex+1).split("\\.")[0];
            tbNbsapiDocumentos.append();
            tbNbsapiDocumentos.setID(SequenceUtil.nextVal("SEQ_NBSAPI_DOCUMENTOS"));
            tbNbsapiDocumentos.setID_ENVELOPE(getIdEnvelopeGerado());
            tbNbsapiDocumentos.setDOC_BASE64(pdfGeradoBase64);
            tbNbsapiDocumentos.setNOME_DOCUMENTO(nomeDocumento);
            tbNbsapiDocumentos.setEXTENSAO_ARQ_DOC("pdf");
            tbNbsapiDocumentos.setSEQUENCIA(tbAssinaturaDigitalDoc.getDOCUMENTO_ITEM());
            tbNbsapiDocumentos.setPROCESSADO("N");
            tbNbsapiDocumentos.post();
            tbAssinaturaDigitalDoc.next();
        }
        tbNbsapiDocumentos.applyUpdates();
        tbNbsapiDocumentos.commitUpdates();
    }

    private void solicitarEnvioEnvelope(ISession session) throws DataException {
        pkgAssinaturaDigitalA.enviarParaAssinatura(session, getIdEnvelopeGerado(), COD_PACOTE_FORSIGN);
    }

    public void alterarTbSolicitacoesAssinaturas(String nome, String email, String telefone) throws DataException {
        tbSolicitacoesAssinaturas.edit();
        tbSolicitacoesAssinaturas.setNOME(nome);
        tbSolicitacoesAssinaturas.setEMAIL(email);
        tbSolicitacoesAssinaturas.setTELEFONE(telefone);
        tbSolicitacoesAssinaturas.post();
    }

    public void solicitarAlteracao(String nome, String email, String telefone) throws DataException {
        if (getStatusAssinatura() == CrmAssinaturaDigitalUtils.EnStatusAssinatura.DOCUMENTOS_ASSINADOS || getStatusAssinatura() == CrmAssinaturaDigitalUtils.EnStatusAssinatura.DOCS_ASSINADOS_BAIXADOS) {
            return;
        }
        if (CrmAssinaturaDigitalUtils.isSignatarioInvalido(nome, email, telefone, getTipoEnvioSelecionado())) {
            return;
        }
        if (rn.tbNbsapiEnvelopeFilaResumoIsEmpty()) {/* caso assinatura ainda não tenha sido enviada */
            alterarTbSolicitacoesAssinaturas(nome, email, telefone);
        }else{ /* caso assinatura já tenha sido enviada */
            ISession session;
            session = SessionFactory.getInstance().getSession();
            try {
                session.open();
                pkgAssinaturaDigitalA.filaAtualizarDestinatario(session, rn.getIdEnvelope(), rn.getIdDestinatario(), nome, email, telefone, COD_PACOTE_FORSIGN);
                session.commit();
                initForm();
            } catch (DataException e) {
                session.rollback();
                throw new DataException(e);
            }finally{
                session.close();
            }
        }
    }

    /**
     * Monta o popup menu com as opções de documentos assinados disponiveis para visualização
     */
    public void montarMenuVerDocumentos() {
        try {
            limparMenuVerDocumentos();
            tbNbsapiDocumentosAssinados.first();
            while(!tbNbsapiDocumentosAssinados.eof()){
                TFMenuItem ppmMenuItem = new TFMenuItem();
                ppmMenuItem.setName("itemDoc_" + tbNbsapiDocumentosAssinados.getID().asInteger());
                ppmMenuItem.setCaption(tbNbsapiDocumentosAssinados.getNOME_DOCUMENTO().asString());
                ppmMenuItem.setImageIndex(4600353);
                ppmMenuItem.addEventListener("onClick", t -> {
                    byte[] documentoABaixar;
                    String nomeDocumento;
                    
                    // Verifica a origem do documento (Banco de dados ou Disco)
                    String gravadoEm = tbNbsapiDocumentosAssinados.getGRAVADO_EM().asString();
                    if ("P".equals(gravadoEm)) {
                        // Documento está no disco
                        String caminhoArquivo = tbNbsapiDocumentosAssinados.getCAMINHO_ARQUIVO().asString();
                        try {
                            documentoABaixar = FolderUtils.getFileAsBytes(caminhoArquivo);
                        } catch (IOException e) {
                            EmpresaUtil.showError("Erro ao ler arquivo do disco", e);
                            return;
                        }
                    } else {
                        // Documento está no banco de dados (padrão)
                        documentoABaixar = (byte[]) tbNbsapiDocumentosAssinados.getDOCUMENTO_ASSINADO().value();
                    }
                    
                    nomeDocumento = tbNbsapiDocumentosAssinados.getNOME_DOCUMENTO().asString();
                    PdfUtils.abrirPdf(documentoABaixar, nomeDocumento);
                });
                menuVerDocumentos.addChildren(ppmMenuItem);
                listaOpcoesMenuDocumentosABaixar.add(ppmMenuItem);
                tbNbsapiDocumentosAssinados.next();
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao montar o menu de opções de documentos assinados disponivel para visualização", e);
        }
    }

    /**
     * Limpa o popup menu de documentos assinados disponíveis para o usuário ver
     */
    public void limparMenuVerDocumentos(){
        listaOpcoesMenuDocumentosABaixar.forEach(menuVerDocumentos::removeChildren);
        listaOpcoesMenuDocumentosABaixar.clear();
    }

    public void alterarDadosSignatario(){
        FrmAssinaturaDigitalSignatarioA frmAssinaturaDigitalSignatarioA = new FrmAssinaturaDigitalSignatarioA();
        String nomeAtual = tbSolicitacoesAssinaturas.getNOME().asString();
        String emailAtual = tbSolicitacoesAssinaturas.getEMAIL().asString();
        String telefoneAtual = tbSolicitacoesAssinaturas.getTELEFONE().asString();

        if (frmAssinaturaDigitalSignatarioA.carregarAssinaturaDigitalSignatario(nomeAtual, emailAtual, telefoneAtual, getTipoEnvioSelecionado())) {
            FormUtil.doShow(frmAssinaturaDigitalSignatarioA, t -> {
                String novoNome = frmAssinaturaDigitalSignatarioA.getNome();
                String novoEmail = frmAssinaturaDigitalSignatarioA.getEmail();
                String novoTelefone = frmAssinaturaDigitalSignatarioA.getTelefone();
                if (frmAssinaturaDigitalSignatarioA.isOk() && verificarHouveMudanca(novoNome, novoEmail, novoTelefone)) {
                    try {
                        solicitarAlteracao(novoNome, novoEmail, novoTelefone);
                    } catch (DataException e) {
                        EmpresaUtil.showError("Erro ao Salvar dados Signatario", e);
                    }
                }
            });
        }
    }

    public boolean verificarHouveMudanca(String nome, String email, String telefone){
        return (nome.equals(tbSolicitacoesAssinaturas.getNOME().asString()) || email.equals(tbSolicitacoesAssinaturas.getEMAIL().asString()) || telefone.equals(tbSolicitacoesAssinaturas.getTELEFONE().asString()));
    }

    @Override
    public void hboxTemplateSolicitacaoAssinaturaLblEditarClick(Event<Object> event) {
        alterarDadosSignatario();
    }

    @Override
    public void FGrid1Columns1RenderTemplate(RenderTemplateEvent<Value> event) {
        try {
            View dados = tbSolicitacoesAssinaturas.getView();
            dados.gotoBookmark(event.getBookmark());
            // Status Assinatura
            String nomeComponent = event.getTarget().getName();
            if (nomeComponent != null) {
                if (nomeComponent.equals(hboxTemplateSolicitacaoAssinaturaLblStatus.getName())) {
                    CrmAssinaturaDigitalUtils.EnStatusAssinaturaSignatario status = CrmAssinaturaDigitalUtils.EnStatusAssinaturaSignatario.fromString(dados.getField("STATUS_ANDAMENTO_ASSINATURA").isNull() ? "PENDENTE" : dados.getField("STATUS_ANDAMENTO_ASSINATURA").toString());
                    ((TFLabel) event.getTarget()).setCaption(status.getCaption());
                    ((TFLabel) event.getTarget()).setFontColor(status.getColor());
                }
                if (nomeComponent.equals(FPanelBtnQrCode.getName())){
                    FPanelBtnQrCode.setAttribute("SCLASS_BASE", "vboximagestrech");
                }
                if (nomeComponent.equals(hboxTemplateSolicitacaoAssinaturaHboxLblEditar.getName())){
                    CrmAssinaturaDigitalUtils.EnStatusAssinatura statusAssinatura = this.getStatusAssinatura();
                    CrmAssinaturaDigitalUtils.EnStatusAssinaturaSignatario status = CrmAssinaturaDigitalUtils.EnStatusAssinaturaSignatario.fromString(dados.getField("STATUS_ANDAMENTO_ASSINATURA").isNull() ? "PENDENTE" : dados.getField("STATUS_ANDAMENTO_ASSINATURA").toString());
                    ((TFHBox) event.getTarget()).setVisible(!getIsPreview() && (!status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinaturaSignatario.ASSINATURA_CONCLUIDA) && statusAssinatura != CrmAssinaturaDigitalUtils.EnStatusAssinatura.DOCUMENTOS_ASSINADOS && statusAssinatura != CrmAssinaturaDigitalUtils.EnStatusAssinatura.DOCS_ASSINADOS_BAIXADOS && statusAssinatura != CrmAssinaturaDigitalUtils.EnStatusAssinatura.SOLICITACAO_CANCELAMENTO && statusAssinatura != CrmAssinaturaDigitalUtils.EnStatusAssinatura.ENVELOPE_CANCELADO));
                    //((TFHBox) event.getTarget()).setVisible(!status.equals(CrmAssinaturaDigital.EnStatusAssinaturaSignatario.ASSINATURA_CONCLUIDA));
                }
            }

        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao renderizar o template da grid de solicitacoes de assinatura", e);
        }
    }

    @Override
    public void btnHistoricoClick(Event<Object> event) {
        FrmAssinaturaDigitaHistoricoA frmAssinaturaDigitaHistoricoA = new FrmAssinaturaDigitaHistoricoA();
        if (frmAssinaturaDigitaHistoricoA.carregarAssinaturaDigitalHistorico(getTipoAssinatura(), getJsonParametros())){
            FormUtil.doShow(frmAssinaturaDigitaHistoricoA, t -> {
                //(inTheFuture) futuramente procurar um local melhor para solicitar a atualização do status (isso para evitar o loop infinito (cancelando...) ao cancelar uma assinatura expirada na forsign)
                if (getStatusAssinatura().equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.SOLICITACAO_CANCELAMENTO)) {
                    initForm();
                }
            });
        }
    }


    @Override
    public void FPanelBtnAcaoBaixarDocumentoClick(Event<Object> event) {
        if (tbNbsapiDocumentosAssinados.count() > 1){
            // abre o popup menu de documentos assinados disponíveis para visualização
            menuVerDocumentos.open(FPanelBtnAcaoBaixarDocumento);
        } else if (tbNbsapiDocumentosAssinados.count() == 1) {
            // caso só tenha um documento já abre direto o documento
            byte[] documentoABaixar;
            String nomeDocumento;
            
            // Verifica a origem do documento (Banco de dados ou Disco)
            String gravadoEm = tbNbsapiDocumentosAssinados.getGRAVADO_EM().asString();
            if ("P".equals(gravadoEm)) {
                // Documento está no disco
                String caminhoArquivo = tbNbsapiDocumentosAssinados.getCAMINHO_ARQUIVO().asString();
                try {
                    documentoABaixar = FolderUtils.getFileAsBytes(caminhoArquivo);
                } catch (IOException e) {
                    EmpresaUtil.showError("Erro ao ler arquivo do disco", e);
                    return;
                }
            } else {
                // Documento está no banco de dados (padrão)
                documentoABaixar = (byte[]) tbNbsapiDocumentosAssinados.getDOCUMENTO_ASSINADO().value();
            }
            
            nomeDocumento = tbNbsapiDocumentosAssinados.getNOME_DOCUMENTO().asString();
            PdfUtils.abrirPdf(documentoABaixar, nomeDocumento);
        } else {
            EmpresaUtil.showMessage("Abrir Documentos Assinados","Nenhum Documento Foi Encontrado!");
        }
    }

    @Override
    public void FPanelBtnAcaoLerQRCodeClick(Event<Object> event) {
        abrirLinkAssinaturaPresencial();
    }

    @Override
    public void FPanelBtnQrCodeClick(Event<Object> event) {
        abrirLinkAssinaturaRemota();
    }

    public void abrirLinkAssinaturaPresencial(){
        if (validarAcessoBloqueioAssinaturaPresencial()){
            return;
        }
        FrmAssinaturaDigitalVerA frmAssinaturaDigitalVerA = new FrmAssinaturaDigitalVerA();
        if (frmAssinaturaDigitalVerA.carregarAssinaturaDigitalPresencial(getTipoAssinatura(), getJsonParametros())){
            FormUtil.doShow(frmAssinaturaDigitalVerA, t -> {
                initForm();
            });
        }
    }

    public void abrirLinkAssinaturaRemota(){
        if (validarAcessoBloqueioAssinaturaPresencial()){
            return;
        }
        FrmAssinaturaDigitalVerA frmAssinaturaDigitalVerA = new FrmAssinaturaDigitalVerA();
        if (frmAssinaturaDigitalVerA.carregarAssinaturaDigitalRemota(getTipoAssinatura(), getJsonParametros(), tbSolicitacoesAssinaturas.getTAG_DOCUMENTO().asString())){
            FormUtil.doShow(frmAssinaturaDigitalVerA, t -> {
                initForm();
            });
        }
    }

    public void abrirTelaLoadingAguardarCancelamentoAssinatura() {
        FrmLoadingGenericoA.CallBackLoading callbackLoading = new FrmLoadingGenericoA.CallBackLoading() {
            @Override
            public boolean isFinalizarLoading() {
                try {
                    CrmAssinaturaDigitalUtils.EnStatusAssinatura status = CrmAssinaturaDigitalUtils.getStatusAssinatura(getTipoAssinatura(), getJsonParametros());
                    return status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE) || status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.FALHA);
                } catch (DataException e) {
                    throw new RuntimeException(e);
                }
            }
        };

        FrmLoadingGenericoA frmLoadingGenericoA = new FrmLoadingGenericoA();
        if (frmLoadingGenericoA.iniciarTelaLoading("Cancelando...", callbackLoading, 10, 3000)){
            FormUtil.doShow(frmLoadingGenericoA, t -> {
                if (frmLoadingGenericoA.isOk()){
                    CrmAssinaturaDigitalUtils.EnStatusAssinatura status = CrmAssinaturaDigitalUtils.getStatusAssinatura(getTipoAssinatura(), getJsonParametros());
                    if (status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE)){
                        Dialog.create().showToastInfo("Assinaturas Canceladas Com Sucesso!",Constantes.MIDDLE_CENTER,3000,true);
                    }else{
                        Dialog.create().showToastError("Processado com falha, tente novamente",Constantes.MIDDLE_CENTER,3000,true);
                    }
                    initForm();
                }else{
                    Dialog.create().title("Confirmar").message("Tempo de espera maior que o normal...").showConfirm((String dialogResult)-> {
                        switch (dialogResult){
                            case "1": {
                                try {
                                    solicitarAtualizacaoStatus();
                                    abrirTelaLoadingAguardarCancelamentoAssinatura();
                                } catch (DataException e) {
                                    EmpresaUtil.showError("Erro ao Atualizar status da assinatura", e);
                                }
                                break;
                            }
                            case "0":{
                                try {
                                    initForm();
                                } catch (DataException e) {
                                    EmpresaUtil.showError("Erro ao recarregar tela", e);
                                }
                                break;
                            }
                        }
                    }, "Aguardar", "Aguardar Em segundo plano");
                }
            });
        }

    }

    public void abrirTelaLoadingAguardarEnvioAssinatura() {
        FrmLoadingGenericoA.CallBackLoading callbackLoading = new FrmLoadingGenericoA.CallBackLoading() {
            @Override
            public boolean isFinalizarLoading() {
                try {
                    CrmAssinaturaDigitalUtils.EnStatusAssinatura status = CrmAssinaturaDigitalUtils.getStatusAssinatura(getTipoAssinatura(), getJsonParametros());
                    return status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.AGUARDANDO_ASSINATURAS) || status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.FALHA);
                } catch (DataException e) {
                    throw new RuntimeException(e);
                }
            }
        };

        FrmLoadingGenericoA frmLoadingGenericoA = new FrmLoadingGenericoA();
        if (frmLoadingGenericoA.iniciarTelaLoading("Enviando...", callbackLoading, 10, 3000)){
            FormUtil.doShow(frmLoadingGenericoA, t -> {
                if (frmLoadingGenericoA.isOk()){
                    CrmAssinaturaDigitalUtils.EnStatusAssinatura status = CrmAssinaturaDigitalUtils.getStatusAssinatura(getTipoAssinatura(), getJsonParametros());
                    if (status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.AGUARDANDO_ASSINATURAS)){
                        Dialog.create().showToastInfo("Assinaturas Enviadas Com Sucesso!",Constantes.MIDDLE_CENTER,3000,true);
                    }else{
                        Dialog.create().showToastError("Processado com falha, tente novamente",Constantes.MIDDLE_CENTER,3000,true);
                    }
                    initForm();
                }else{
                    Dialog.create().title("Confirmar").message("Tempo de espera maior que o normal...").showConfirm((String dialogResult)-> {
                        switch (dialogResult){
                            case "1": {
                                abrirTelaLoadingAguardarEnvioAssinatura();
                                break;
                            }
                            case "0":{
                                try {
                                    solicitarCancelamento(getIdEnvelopeGerado());
                                } catch (DataException e) {
                                    EmpresaUtil.showError("Erro ao solicitar cancelamento da assinatura", e);
                                }
                                break;
                            }
                        }
                    }, "Aguardar", "Cancelar");
                }
            });
        }
    }

    public void abrirTelaLoadingAguardarArquivamentoAssinatura() {
        FrmLoadingGenericoA.CallBackLoading callbackLoading = new FrmLoadingGenericoA.CallBackLoading() {
            @Override
            public boolean isFinalizarLoading() {
                try {
                    CrmAssinaturaDigitalUtils.EnStatusAssinatura status = CrmAssinaturaDigitalUtils.getStatusAssinatura(getTipoAssinatura(), getJsonParametros());
                    return status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE) || status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.FALHA);
                } catch (DataException e) {
                    throw new RuntimeException(e);
                }
            }
        };

        FrmLoadingGenericoA frmLoadingGenericoA = new FrmLoadingGenericoA();
        if (frmLoadingGenericoA.iniciarTelaLoading("Arquivando...", callbackLoading, 10, 3000)){
            FormUtil.doShow(frmLoadingGenericoA, t -> {
                if (frmLoadingGenericoA.isOk()){
                    CrmAssinaturaDigitalUtils.EnStatusAssinatura status = CrmAssinaturaDigitalUtils.getStatusAssinatura(getTipoAssinatura(), getJsonParametros());
                    if (status.equals(CrmAssinaturaDigitalUtils.EnStatusAssinatura.PENDENTE)){
                        Dialog.create().showToastInfo("Assinaturas Arquivadas Com Sucesso!",Constantes.MIDDLE_CENTER,3000,true);
                    }else{
                        Dialog.create().showToastError("Processado com falha, tente novamente",Constantes.MIDDLE_CENTER,3000,true);
                    }
                    initForm();
                }else{
                    Dialog.create().title("Confirmar").message("Tempo de espera maior que o normal...").showConfirm((String dialogResult)-> {
                        switch (dialogResult){
                            case "1": {
                                    abrirTelaLoadingAguardarCancelamentoAssinatura();
                                break;
                            }
                            case "0":{
                                try {
                                    initForm();
                                } catch (DataException e) {
                                    EmpresaUtil.showError("Erro ao recarregar tela", e);
                                }
                                break;
                            }
                        }
                    }, "Aguardar", "Aguardar Em segundo plano");
                }
            });
        }

    }

    @Override
    public void lblIrParaVersaoAtualClick(Event<Object> event) {
        try {
            setIdEnvelopeBuscado(null);
            initForm();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao ir para versão atual", e);
        }
    }

    @Override
    public void btnTesteLoadingClick(Event<Object> event) {
        FrmLoadingGenericoA. CallBackLoading callbackLoading = new FrmLoadingGenericoA. CallBackLoading() {
            @Override     public boolean isFinalizarLoading() {
                return false; // Retorna true para indicar o término do loading
               } };


        FrmLoadingGenericoA frmLoadingGenericoA = new FrmLoadingGenericoA();
        if (frmLoadingGenericoA.iniciarTelaLoading("Testando...", callbackLoading, 5, 2000)) {
            FormUtil. doShow(frmLoadingGenericoA, t -> {
                if (frmLoadingGenericoA. isOk()) {
                    Dialog. create().showToastInfo("Processado com sucesso!", Constantes.MIDDLE_CENTER, 3000, true);
                } else {
                    Dialog. create().showToastError("Processado com falha!", Constantes.MIDDLE_CENTER, 3000, true);
                }
            });
        }
    }

}
