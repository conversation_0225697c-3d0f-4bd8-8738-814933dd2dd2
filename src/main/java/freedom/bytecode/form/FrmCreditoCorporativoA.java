package freedom.bytecode.form;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.form.wizard.*;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.apache.commons.lang3.StringUtils;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

public class FrmCreditoCorporativoA extends FrmCreditoCorporativoW {
    private static final long serialVersionUID = 20130827081850L;

    public void openFrmCreditoCorporativo(double codCliente, int codEmpresaUser, boolean ehRodobens){
        cbTipo.setEnabled(false);
        edtNomeCliente.setEnabled(false);

        try {
            rn.buscaLogsCliente(codCliente, codEmpresaUser);
            String documento = tbRapClienteLogsCc.getCPF().asString().trim();
            if(StringUtils.isNotBlank(documento)){
                cbTipo.setValue("F");
            }else{
                cbTipo.setValue("J");
            }
            edtNomeCliente.setValue(tbRapClienteLogsCc.getNOME().asString().trim());

        } catch (DataException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        close();
    }

}
