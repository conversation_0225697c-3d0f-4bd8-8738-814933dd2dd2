package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmClientesFlagsPgtoW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.data.impl.View;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;
import lombok.Getter;

public class FrmClientesFlagsPgtoA extends FrmClientesFlagsPgtoW {

    private static final long serialVersionUID = 20130827081850L;

    private double codEmpresaFiltrada;

    private double codDepartamentoFiltrado;

    private String codTipoDaFormaDePagamentoFiltrada;

    private String formaDePagamentoExclusivaFiltrada;

    private double codFormaDePagamentoFiltrada;

    private String condicaoDePagamentoExclusivaFiltrada;

    private double codCondicaoDePagamentoFiltrada;

    @Getter
    private boolean fechadoAoSalvar =  false;

    public FrmClientesFlagsPgtoA(
            double codClienteFiltrado
    ) {
        try {
            this.codEmpresaFiltrada = 0.0;
            this.codDepartamentoFiltrado = 0.0;
            this.codTipoDaFormaDePagamentoFiltrada = "";
            this.formaDePagamentoExclusivaFiltrada = "";
            this.codFormaDePagamentoFiltrada = 0.0;
            this.condicaoDePagamentoExclusivaFiltrada = "";
            this.codCondicaoDePagamentoFiltrada = 0.0;
            this.rn.setCodClienteFiltrado(
                    codClienteFiltrado
            );
            this.rn.carregarEmpresasDoFiltro();
            this.rn.carregarTiposDasFormasDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar tipos das formas de pagamentos do filtro"
                    ,exception
            );
        }
    }

    @Override
    public void cboEmpresaChange(Event<Object> event) {
        try {
            this.codEmpresaFiltrada = this.cboEmpresa.getValue().asLong();
            this.rn.setCodEmpresaFiltrada(
                    this.codEmpresaFiltrada
            );
            this.rn.carregarDepartamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o filtro de empresa"
                    ,exception
            );
        }
    }

    @Override
    public void cboEmpresaClearClick(final Event<Object> event) {
        try {
            this.codEmpresaFiltrada = 0.0;
            this.rn.setCodEmpresaFiltrada(
                    this.codEmpresaFiltrada
            );
            this.codDepartamentoFiltrado = 0.0;
            this.rn.setCodDepartamentoFiltrado(
                    this.codDepartamentoFiltrado
            );
            this.codFormaDePagamentoFiltrada = 0.0;
            this.rn.setCodFormaDePagamentoFiltrada(
                    this.codFormaDePagamentoFiltrada
            );
            this.codCondicaoDePagamentoFiltrada = 0.0;
            this.rn.setCodCondicaoDePagamentoFiltrada(
                    this.codCondicaoDePagamentoFiltrada
            );
            this.rn.carregarDepartamentosDoFiltro();
            this.rn.carregarFormasDePagamentosDoFiltro();
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o filtro de empresa"
                    ,exception
            );
        }
    }

    @Override
    public void cboDepartamentoChange(Event<Object> event) {
        try {
            this.codDepartamentoFiltrado = cboDepartamento.getValue().asLong();
            this.rn.setCodDepartamentoFiltrado(
                    this.codDepartamentoFiltrado
            );
            this.rn.carregarFormasDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o filtro de departamento"
                    ,exception
            );
        }
    }

    @Override
    public void cboDepartamentoClearClick(final Event<Object> event) {
        try {
            this.codDepartamentoFiltrado = 0.0;
            this.rn.setCodDepartamentoFiltrado(
                    this.codDepartamentoFiltrado
            );
            this.codFormaDePagamentoFiltrada = 0.0;
            this.rn.setCodFormaDePagamentoFiltrada(
                    this.codFormaDePagamentoFiltrada
            );
            this.codCondicaoDePagamentoFiltrada = 0.0;
            this.rn.setCodCondicaoDePagamentoFiltrada(
                    this.codCondicaoDePagamentoFiltrada
            );
            this.rn.carregarFormasDePagamentosDoFiltro();
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o filtro de departamento"
                    ,exception
            );
        }
    }

    @Override
    public void btnPesquisarClick(final Event<Object> event) {
        try {
            this.rn.carregarGradeDeCondicoesDePagamento();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar grade de condições de pagamento"
                    ,dataException
            );
        }
    }

    private void selecionarRegistrosNaGradeGridPgtos(
            String value
    ) {
        try {
            this.tbClienteFormaPgtoDisp.first();
            while (Boolean.FALSE.equals(this.tbClienteFormaPgtoDisp.eof())) {
                this.tbClienteFormaPgtoDisp.edit();
                this.tbClienteFormaPgtoDisp.setSEL(
                        value
                );
                this.tbClienteFormaPgtoDisp.post();
                this.tbClienteFormaPgtoDisp.next();
            }
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao selecionar registro na grade condições de pagamento"
                    ,dataException
            );
        }
    }

    @Override
    public void mmSelecionarTodosOsRegistrosDaGradeGridPgtosClick(final Event<Object> event) {
        this.selecionarRegistrosNaGradeGridPgtos(
                "S"
        );
    }

    @Override
    public void mmSelecionarNenhumRegistroDaGradeGridPgtosClick(final Event<Object> event) {
        this.selecionarRegistrosNaGradeGridPgtos(
                "N"
        );
    }

    @Override
    public void gridPgtosmarcarRegistroNaGradeGridPgtos(final Event<Object> event) {
        try {
            this.tbClienteFormaPgtoDisp.edit();
            this.tbClienteFormaPgtoDisp.setSEL(
                    "S"
            );
            this.tbClienteFormaPgtoDisp.post();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao marcar registro na grade condições de pagamento"
                    ,exception
            );
        }
    }

    @Override
    public void gridPgtosdesmarcarRegistroNaGradeGridPgtos(final Event<Object> event) {
        try {
            this.tbClienteFormaPgtoDisp.edit();
            this.tbClienteFormaPgtoDisp.setSEL(
                    "N"
            );
            this.tbClienteFormaPgtoDisp.post();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao desmarcar registro na grade condições de pagamento"
                    ,exception
            );
        }
    }

    @Override
    public void cboFormaDePagamentoChange(final Event<Object> event) {
        try {
            this.codFormaDePagamentoFiltrada = cboFormaDePagamento.getValue().asLong();
            this.rn.setCodFormaDePagamentoFiltrada(
                    this.codFormaDePagamentoFiltrada
            );
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o filtro de forma de pagamento"
                    ,exception
            );
        }
    }

    @Override
    public void cboFormaDePagamentoClearClick(final Event<Object> event) {
        try {
            this.codFormaDePagamentoFiltrada = 0.0;
            this.rn.setCodFormaDePagamentoFiltrada(
                    this.codFormaDePagamentoFiltrada
            );
            this.codCondicaoDePagamentoFiltrada = 0.0;
            this.rn.setCodCondicaoDePagamentoFiltrada(
                    this.codCondicaoDePagamentoFiltrada
            );
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o filtro de forma de pagamento"
                    ,exception
            );
        }
    }

    @Override
    public void cboTipoDaFormaDePagamentoChange(final Event<Object> event) {
        try {
            this.codTipoDaFormaDePagamentoFiltrada = cboTipoDaFormaDePagamento.getValue().asString();
            this.rn.setCodTipoDaFormaDePagamentoFiltrada(
                    this.codTipoDaFormaDePagamentoFiltrada
            );
            this.rn.carregarFormasDePagamentosDoFiltro();
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o filtro de tipo da forma de pagamento"
                    ,exception
            );
        }
    }

    @Override
    public void cboTipoDaFormaDePagamentoClearClick(final Event<Object> event) {
        try {
            this.codTipoDaFormaDePagamentoFiltrada = "";
            this.rn.setCodTipoDaFormaDePagamentoFiltrada(
                    this.codTipoDaFormaDePagamentoFiltrada
            );
            this.codFormaDePagamentoFiltrada = 0.0;
            this.rn.setCodFormaDePagamentoFiltrada(
                    this.codFormaDePagamentoFiltrada
            );
            this.codCondicaoDePagamentoFiltrada = 0.0;
            this.rn.setCodCondicaoDePagamentoFiltrada(
                    this.codCondicaoDePagamentoFiltrada
            );
            this.rn.carregarFormasDePagamentosDoFiltro();
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o filtro de tipo da forma de pagamento"
                    ,exception
            );
        }
    }

    @Override
    public void cboCondicaoDePagamentoChange(final Event<Object> event) {
        this.codCondicaoDePagamentoFiltrada = this.cboCondicaoDePagamento.getValue().asLong();
        this.rn.setCodCondicaoDePagamentoFiltrada(
                this.codCondicaoDePagamentoFiltrada
        );
    }

    @Override
    public void cboCondicaoDePagamentoClearClick(final Event<Object> event) {
        this.codCondicaoDePagamentoFiltrada = 0.0;
        this.rn.setCodCondicaoDePagamentoFiltrada(
                this.codCondicaoDePagamentoFiltrada
        );
    }

    @Override
    public void cboCondicaoDePagamentoExclusivaChange(final Event<Object> event) {
        try {
            this.condicaoDePagamentoExclusivaFiltrada = this.cboCondicaoDePagamentoExclusiva.getValue().asString();
            this.rn.setCondicaoDePagamentoExclusivaFiltrada(
                    this.condicaoDePagamentoExclusivaFiltrada
            );
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o filtro de condição de pagamento exclusiva"
                    ,exception
            );
        }
    }

    @Override
    public void cboCondicaoDePagamentoExclusivaClearClick(final Event<Object> event) {
        try {
            this.condicaoDePagamentoExclusivaFiltrada = "";
            this.rn.setCondicaoDePagamentoExclusivaFiltrada(
                    this.condicaoDePagamentoExclusivaFiltrada
            );
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o filtro de condição de pagamento exclusiva"
                    ,exception
            );
        }
    }

    @Override
    public void cboFormaDePagamentoExclusivaChange(final Event<Object> event) {
        try {
            this.formaDePagamentoExclusivaFiltrada = this.cboFormaDePagamentoExclusiva.getValue().asString();
            this.rn.setFormaDePagamentoExclusivaFiltrada(
                    this.formaDePagamentoExclusivaFiltrada
            );
            this.rn.carregarFormasDePagamentosDoFiltro();
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o filtro de forma de pagamento exclusiva"
                    ,exception
            );
        }
    }

    @Override
    public void cboFormaDePagamentoExclusivaClearClick(final Event<Object> event) {
        try {
            this.formaDePagamentoExclusivaFiltrada = "";
            this.rn.setFormaDePagamentoExclusivaFiltrada(
                    this.formaDePagamentoExclusivaFiltrada
            );
            this.rn.carregarFormasDePagamentosDoFiltro();
            this.rn.carregarCondicoesDePagamentosDoFiltro();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao limpar o filtro de forma de pagamento exclusiva"
                    ,exception
            );
        }
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        try {
            View vTbClienteFormaPgtoDisp = this.tbClienteFormaPgtoDisp.getView();
            boolean encontrouRegistroSelecionado = vTbClienteFormaPgtoDisp.locate(
                    "SEL"
                    ,"S"
            );
            if (!encontrouRegistroSelecionado) {
                EmpresaUtil.showInformationMessage(
                        "Selecione algum registro."
                );
                return;
            }
            Dialog.create()
                    .title("Salvar")
                    .message("Deseja salvar todas as condições selecionadas?")
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            this.fechadoAoSalvar = true;
                            this.close();
                        }
                    });
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao clicar no botão Salvar"
                    ,exception
            );
        }
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

}
