package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmSnapShotCameraW;
import freedom.client.event.Event;
import freedom.client.event.UploadEvent;
import freedom.util.EmpresaUtil;
import freedom.util.ImageUtil;
import org.zkoss.zk.ui.util.Clients;
import org.zkoss.zkmax.zul.Camera;

public class FrmSnapShotCameraA extends FrmSnapShotCameraW {

    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;
    private byte[] imageBytes;
    private String nameFile;

    public String getNameFile() {
        return nameFile;
    }

    public byte[] getImageBytes() {
        return imageBytes;
    }

    public boolean isOk() {
        return ok;
    }

    private boolean flashOn = false;

    private Double zoom = 1.0;

    @Override
    public void cameraSnapshotUpload(UploadEvent event) {
        camera.setVisible(false);
        imagePreview.setVisible(true);
        imagePreview.invalidate();

        try {
            imageBytes = ImageUtil.streamToByteArray(event.getStreamData());
            imagePreview.setImageContent(ImageUtil.getRenderedImage(imageBytes));
            nameFile = event.getFileName();

            iconCancel.setVisible(true);
            iconOk.setVisible(true);
            buttonCapturarFoto.setVisible(false);

            hBoxOpcoes.invalidate();
        } catch (Exception ex) {
            EmpresaUtil.showError("Erro ao capturar foto.", ex);
        }
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        camera.requestCamera();
        camera.setPreviewRecord(true);
        createFlashZoom();
    }

    @Override
    public void iconOkClick(final Event event) {
        ok = true;
        close();
    }

    @Override
    public void vBoxCancelClick(Event<Object> event) {
        camera.setVisible(true);
        imagePreview.setVisible(false);
        imagePreview.setValue(null);

        iconCancel.setVisible(false);
        iconOk.setVisible(false);
        buttonCapturarFoto.setVisible(true);
        hBoxOpcoes.invalidate();
    }

    @Override
    public void hBoxCloseClick(Event<Object> event) {
        close();
        ok = false;
    }

    @Override
    public void vBoxCapturarFotoClick(final Event event) {
        camera.snapshot();
    }


    @Override
    public void vboxFlashClick(final Event event) {
        flash();
    }

    @Override
    public void vboxZoomClick(final Event event) {
        zoom();
    }

    public void createFlashZoom() {
        String js = "// Exemplo conceitual do JavaScript necessário\n" +
                "var zkCameraUtil = {};" +
                "zkCameraUtil.updateSettings = function(uuid, settings) {\n" +
                "    var cmp = zk.Widget.$(uuid);\n" +
                "    var video = jq(cmp.$n()).find('video')[0];\n" +
                "    var stream = video.srcObject;\n" +
                "    \n" +
                "    if (stream)\n" +
                " {\n" +
                "        var tracks = stream.getVideoTracks();\n" +
                "        if (tracks.length > 0) {\n" +
                "            var capabilities = tracks[0].getCapabilities();\n" +
                "            var constraints = {};\n" +
                "            \n" +
                "            // Verificar se o flash é suportado\n" +
                "            if ('torch' in capabilities) {\n" +
                "                constraints.advanced = [{torch: settings.flash}];\n" +
                "            }\n" +
                "            \n" +
                "            // Verificar se o zoom é suportado\n" +
                "            if ('zoom' in capabilities) {\n" +
                "                constraints.advanced = constraints.advanced || [];\n" +
                "                constraints.advanced.push({zoom: settings.zoom});\n" +
                "            }\n" +
                "            \n" +
                "            // Aplicar as configurações\n" +
                "            if (Object.keys(constraints).length > 0) {\n" +
                "                tracks[0].applyConstraints(constraints);\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "};";
        Clients.evalJavaScript(js);
    }

    private void flash() {
        if (flashOn) {
            flashOn = false;
        } else {
            flashOn = true;
        }
        String script = "zkCameraUtil.updateSettings(" +
                "'" + ((Camera) camera.getImpl()).getUuid() + "', " +
                "{flash: " + flashOn + " });";
        Clients.evalJavaScript(script);
    }

    private void zoom() {
        if (zoom == 1.0) {
            zoom = 2.0;
            iconClassZoom.setIconClass("search-minus");
        } else {
            zoom = 1.0;
            iconClassZoom.setIconClass("search-plus");
        }
        String script = "zkCameraUtil.updateSettings(" +
                "'" + ((Camera) camera.getImpl()).getUuid() + "', " +
                "{zoom: " + zoom + "});";
        Clients.evalJavaScript(script);
    }
}
