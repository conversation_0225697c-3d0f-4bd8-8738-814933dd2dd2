package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmPgtoVendaInfW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.CastUtil;
import org.apache.commons.lang.StringUtils;
import org.zkoss.zk.ui.HtmlBasedComponent;
import org.zkoss.zul.Tabbox;

public class FrmPgtoVendaInfA extends FrmPgtoVendaInfW {
    private static final long serialVersionUID = 20130827081850L;
    private static final String INFORMACAO = "Informação";
    private boolean ok = false;

    public void informarPgtoOS(Double codEmpresa, Double numeroOS) {
        rn.validarInfoPgtoVendaOS(codEmpresa, numeroOS);
    }

    public void informarPgtoOrcMapa(Double codEmpresa, Double codOrcMapa) {
        rn.validarinfoPgtoVendaOrcMapa(codEmpresa, codOrcMapa);
        rn.validarInfoPgtoVenda();
        //this.openInfPgtoVenda(codEmpresa, codOrcMapa);
    }

    public void iniciarVendaComCartao(Double codEmpresa, Double codOrcMapa) {
        rn.validarinfoPgtoVendaOrcMapa(codEmpresa, codOrcMapa);
        rn.validarInfoPgtoVenda();

        /*
         *  Verificar se UsaPosSitef
         *  Se tem tem cartao para digitar
         *  Se tem Pos e pergunta digitar cartao.
         * */

    }

    @Override
    public void frmShow(Event<Object> event) {
        ((Tabbox) this.pgcInfoPgto.getImpl()).getTabs().getChildren().forEach(t -> ((HtmlBasedComponent) t).setStyle("font-size: 12px"));
        hBoxCartoesTopAlterar.setHeight(16);
        hBoxCartoesTopSalvarAlterar.setHeight(13);
        abrePagamentoInfVenda();
        atualizaTotaisTela();
        edtCartaoValor.setValue(0.0);
        edtCartaoQtdParc.setValue(1);
        filtrarListaCartoes(edtCartaoQtdParc.getValue().asInteger());
        if (rn.isIncluindoCartoes()) {
            this.pgcInfoPgto.selectTab(tbsCartoes);
            edtCartaoValor.setValue(rn.getValorFaltaCartao());
            edtCartoesObservacao.setFocus();
        } else if (rn.isIncluindoCheques()) {
            this.pgcInfoPgto.selectTab(tbsCheques);
            edtChequesObservacao.setFocus();
        } else if (rn.getValorCartao() > 0.0) {
            this.pgcInfoPgto.selectTab(tbsCartoes);
        } else if (rn.getValorBoleto() > 0.0) {
            this.pgcInfoPgto.selectTab(tbsBoleto);
        } else if (rn.getValorCheque() > 0.0) {
            this.pgcInfoPgto.selectTab(tbsCheques);
        }
    }

    private void filtrarListaCartoes(Integer nrParcelas) {
        try {
            rn.filtrarListaCartoes(nrParcelas);
        } catch (DataException e) {
            e.printStackTrace();
        }
    }

    private void atualizaTotaisTela() {
        tbsCartoes.setVisible(rn.getValorCartao() > 0.0);
        tbsCheques.setVisible(rn.getValorCheque() > 0.0);
        tbsBoleto.setVisible(rn.getValorBoleto() > 0.0);

        edtCartaoValorTotal.setValue(rn.getValorCartao());
        edtCartaoValorTotal.setEnabled(false);
        edtCartaoValorFaltaLanc.setValue(rn.getValorFaltaCartao());
        edtCartaoValorFaltaLanc.setEnabled(false);
        btnIncluirCartoes.setEnabled(rn.isIncluindoCartoes() && rn.getValorFaltaCartao() > 0.0);
        edtChequeValorTotal.setValue((rn.getValorCheque()));
        edtChequeValorTotal.setEnabled(false);
        edtChequeValorFaltaLancar.setValue(rn.getValorFaltaCheque());
        edtChequeValorFaltaLancar.setEnabled(false);
        btnIncluirCheques.setEnabled(rn.isIncluindoCheques() && rn.getValorFaltaCheque() > 0.0);

        this.filtrarListaCartoes(edtCartaoQtdParc.getValue().asInteger());
        this.btnConfirmar.setEnabled(rn.podeConfirmar());

        habilitarCheque(false);
        habilitarCartao(false);
    }

    private void habilitarCartao(boolean enabled) {
        grdCartoesInformados.setEnabled(!enabled);
        if (tbLeadsPgtoVendaInfCartao.isEmpty()) {
            hBoxEditarCartoes.setVisible(false);
            btnAlterarCartao.setEnabled(false);
            btnCartaoCancelarAlteracao.setEnabled(false);
            btnCartaoSalvarAlteracao.setEnabled(false);
            edtCartaoAlterarNrAut.setEnabled(false);
            btnExcluirCartoes.setEnabled(false);
        } else {
            hBoxEditarCartoes.setVisible(true);
            vBoxCartaoAlterar.setVisible(!enabled);
            vBoxCartaoSalvarAlteracao.setVisible(enabled);
            btnAlterarCartao.setEnabled(!enabled);
            btnCartaoCancelarAlteracao.setEnabled(enabled);
            btnCartaoSalvarAlteracao.setEnabled(enabled);
            edtCartaoAlterarNrAut.setEnabled(enabled);
            btnExcluirCartoes.setEnabled(rn.isIncluindoCartoes() && (!enabled));
        }
    }

    private void abrePagamentoInfVenda() {
        try {
            rn.abreCartoesInformados();
            rn.abreBoletosInformados();
            rn.abreChequesInformados();
        } catch (DataException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void btnConfirmarClick(Event<Object> event) {
        boolean faltaPreencherAut = faltaPreencherNrAutorizacao();
        if (faltaPreencherAut) {
            return;
        }
        this.ok = true;
        this.close();
    }

    private boolean faltaPreencherNrAutorizacao() {
        if (rn.getValorCartao() <= 0.0) {
            return false;
        }
        if (rn.isIncluindoCartoes()&& (!rn.isIncluindoCartaoSitef())) {
            return false;
        }
        if (rn.getValorCartao() > 0.0) {
            if (rn.faltaPreencherNrAutorizacao()) {
                String mensagem = "Falta informar Nr. Autorização para os cartões";
                Dialog.create().title(INFORMACAO).message(mensagem).showInformation(t -> FreedomUtilities.invokeLater(() -> {
                    this.pgcInfoPgto.selectTab(this.tbsCartoes);
                }));
                return true;
            }
        }
        return false;
    }

    @Override
    public void btnCancelarClick(Event<Object> event) {
        if (rn.isIncluindoCartoes() && rn.getValorCartao() > rn.getValorFaltaCartao()) {
            try {
                rn.excluirCartoes();
            } catch (DataException ignored) {
            }
        }
        this.close();
    }

    @Override
    public void edtCartaoQtdParcExit(Event<Object> event) {
        this.filtrarListaCartoes(edtCartaoQtdParc.getValue().asInteger());
    }

    public boolean isOk() {
        return ok;
    }

    @Override
    public void btnIncluirCartoesClick(Event<Object> event) {

        boolean faltaPreencherValor = this.faltaValorCartao();
        if (faltaPreencherValor) {
            return;
        }
        boolean faltaPreencherParcelasCartao = this.faltaParcelasCartao();
        if (faltaPreencherParcelasCartao) {
            return;
        }

        boolean faltaPreecherNrAutCartao = this.faltaNrAutCartao();
        if (faltaPreecherNrAutCartao) {
            return;
        }
        boolean faltaPreencherCartao = this.faltaCartao();
        if (faltaPreencherCartao) {
            return;
        }
        Double codCartaoCredito = this.tbLeadsCartoes.getCOD_CARTAO_CREDITO().asDecimal();
        Double valorCartao = edtCartaoValor.getValue().asDecimal();
        Double nrParcelas = edtCartaoQtdParc.getValue().asDecimal();
        String nrAut = StringUtils.deleteWhitespace(edtCartaoNrAut.getValue().asString());
        try {
            String retFuncao = rn.incluirCartao(codCartaoCredito,
                    valorCartao,
                    nrParcelas,
                    nrAut);
            if (retFuncao.equals("S")) {
                edtCartaoValor.setValue(0.0);
                edtCartaoQtdParc.setValue(1);
            } else {
                Dialog.create()
                        .title(INFORMACAO)
                        .message(retFuncao)
                        .showInformation();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title(INFORMACAO)
                    .message("Falha ao incluir dados do cartão. " + "\n" + e.getMessage())
                    .showInformation();
        }
        atualizarValoresCartoes();
    }

    private void atualizarValoresCartoes() {
        try {
            rn.atualizaValoresInformadosCartao();
            habilitarBoleto(false);
            atualizaCamposBoleto();
        } catch (DataException e) {
            try {
                rn.atualizaValoresInformadosCartao();
            } catch (DataException ex) {
                Dialog.create()
                        .title(INFORMACAO)
                        .message("Falha ao atualizar tela com dados incluidos. " + "\n" + ex.getMessage())
                        .showInformation();
            }
        }
        atualizaTotaisTela();
    }

    private boolean faltaNrAutCartao() {
        if (StringUtils.isBlank(edtCartaoNrAut.getValue().asString())) {
            if (rn.isIncluindoCartoes()) {
                return false;
            }
            Dialog.create()
                    .title(INFORMACAO)
                    .message("Informe o número da autorização.")
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtCartaoNrAut.setFocus()));
            return true;
        }
        if (!tbLeadsPgtoVendaInfCartao.isEmpty()) {
            String nrAut = StringUtils.deleteWhitespace(edtCartaoNrAut.getValue().asString());
            Integer codCartao = tbLeadsCartoes.getCOD_CARTAO_CREDITO().asInteger();
            if (rn.cartaoJaLancado(codCartao, nrAut)) {
                Dialog.create()
                        .title(INFORMACAO)
                        .message("Cartão " + tbLeadsCartoes.getName() + "já lançado!.")
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtCartaoNrAut.setFocus()));
                return true;
            }
        }
        return false;
    }

    private boolean faltaParcelasCartao() {
        boolean retFuncao = false;
        if (edtCartaoQtdParc.getValue().asInteger() <= 0) {
            Dialog.create()
                    .title(INFORMACAO)
                    .message("Informe a quantidade de parcelas.")
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtCartaoQtdParc.setFocus()));
            retFuncao = true;
        }
        return retFuncao;
    }

    private boolean faltaValorCartao() {
        boolean retFuncao = false;
        if (edtCartaoValor.getValue().asDecimal() <= 0.0) {
            Dialog.create()
                    .title(INFORMACAO)
                    .message("Informe o valor do cartão.")
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                        edtCartaoValor.setValue(edtCartaoValorFaltaLanc.getValue().asDecimal());
                        this.edtCartaoValor.select();
                        this.edtCartaoValor.setFocus();
                    }));
            retFuncao = true;
        } else if (edtCartaoValor.getValue().asDecimal() > edtCartaoValorFaltaLanc.getValue().asDecimal()) {
            Dialog.create()
                    .title(INFORMACAO)
                    .message("O valor do cartão informado ultrapassa o valor do pagamento tipo cartão.")
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                        this.edtCartaoValor.select();
                        this.edtCartaoValor.setFocus();
                    }));
            retFuncao = true;
        }
        return retFuncao;
    }

    private boolean faltaCartao() {
        boolean retFuncao = false;
        if (this.cbbCartao.getText().isEmpty()) {
            Dialog.create()
                    .title(INFORMACAO)
                    .message("Informe o cartão crédito.")
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> {
                        this.cbbCartao.setFocus();
                        this.cbbCartao.setOpen(true);
                    }));
            retFuncao = true;
        }
        return retFuncao;
    }

    @Override
    public void btnExcluirCartoesClick(Event<Object> event) {
        try {
            rn.excluirCartoes();
        } catch (DataException e) {
            Dialog.create()
                    .title(INFORMACAO)
                    .message("Falha ao excluir dados do cartão. " + "\n" + e.getMessage())
                    .showInformation();
        }
        atualizarValoresCartoes();
    }

    public String validarInfPgtoVenda() {
        return rn.validarInfPgtoVenda();
    }

    @Override
    public void btnEditDetalheBoletoClick(Event<Object> event) {
        habilitarBoleto(true);
        edtBoletoNossoNumero.setFocus();
    }

    @Override
    public void btnConfirmarBoletoClick(Event<Object> event) {
        String respFunc;
        try {
            respFunc = rn.atualizaBoletoNossoNumero(tbLeadsPgtoVendaInfBoleto.getCOD_FORMA_PGTO().asDecimal(),
                    tbLeadsPgtoVendaInfBoleto.getPARCELA().asDecimal(),
                    edtBoletoNossoNumero.getValue().asString());
        } catch (DataException e) {
            respFunc = "Falha ao confirmar nosso número \n " + e.getMessage();
        }
        if (respFunc.equals("S")) {
            Integer codFormaPgto = tbLeadsPgtoVendaInfBoleto.getCOD_FORMA_PGTO().asInteger();
            Integer parcela = tbLeadsPgtoVendaInfBoleto.getPARCELA().asInteger();
            boolean ehAlteracao = StringUtils.isNotBlank(tbLeadsPgtoVendaInfBoleto.getNOSSO_NUMERO().asString());
            habilitarBoleto(false);
            try {
                rn.atualizaValoresInformadosBoleto();
                tbLeadsPgtoVendaInfBoleto.locate("COD_FORMA_PGTO, PARCELA", codFormaPgto, parcela);
                if (!ehAlteracao) tbLeadsPgtoVendaInfBoleto.next();
            } catch (DataException e) {
                e.printStackTrace();
            }
        } else {
            Dialog.create()
                    .title(INFORMACAO)
                    .message(respFunc)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtBoletoNossoNumero.setFocus()));
        }
        atualizaTotaisTela();
    }

    @Override
    public void btnCancelarBoletoClick(Event<Object> event) {
        habilitarBoleto(false);
        atualizaCamposBoleto();
    }

    @Override
    public void tbLeadsPgtoVendaInfCartaoAfterScroll(Event<Object> event) {
        atualizaCamposCartao();
    }

    @Override
    public void tbLeadsPgtoVendaInfBoletoAfterScroll(Event<Object> event) {
        atualizaCamposBoleto();
    }

    @Override
    public void tbLeadsPgtoVendaInfChequeAfterScroll(Event<Object> event) {
        atualizaCamposCheque();
    }

    private void atualizaCamposCheque() {
        edtChequeNumero.setValue(tbLeadsPgtoVendaInfCheque.getNR_CHEQUE().asString());
        edtChequeValor.setValue(tbLeadsPgtoVendaInfCheque.getVALOR().asDecimal());
        edtChequeDtVencimento.setValue(tbLeadsPgtoVendaInfCheque.getDATA_VENCIMENTO());
    }

    private void atualizaCamposCartao() {
        edtCartaoAlterarNrAut.setValue(tbLeadsPgtoVendaInfCartao.getNR_AUTORIZACAO_VISA().asString());
        edtCartaoNomeCartao.setValue(tbLeadsPgtoVendaInfCartao.getNOME().asString());
        edtCartaoParcCartao.setValue(tbLeadsPgtoVendaInfCartao.getPARCELA().asString());
    }

    private void atualizaCamposBoleto() {
        edtBoletoValor.setValue(tbLeadsPgtoVendaInfBoleto.getVALOR());
        edtBoletoParc.setValue(tbLeadsPgtoVendaInfBoleto.getPARCELA());
        edtBoletoVencimento.setValue(tbLeadsPgtoVendaInfBoleto.getVENCIMENTO());
        edtBoletoNossoNumero.setValue(tbLeadsPgtoVendaInfBoleto.getNOSSO_NUMERO());
    }

    public void habilitarBoleto(Boolean enabled) {
        grdBoletos.setEnabled(!enabled);
        btnEditDetalheBoleto.setEnabled(!enabled);
        btnConfirmarBoleto.setEnabled(enabled);
        btnCancelarBoleto.setEnabled(enabled);
        edtBoletoNossoNumero.setEnabled(enabled);
    }

    public void habilitarCheque(Boolean enabled) {
        grdChequesInformados.setEnabled(!enabled);
        if (tbLeadsPgtoVendaInfCheque.isEmpty()) {
            btnChequeAlterar.setEnabled(false);
            btnChequeExcluir.setEnabled(false);
            btnExcluirCheques.setEnabled(false);
            btnChequeConfirmar.setEnabled(false);
            btnChequeCancelar.setEnabled(false);
            edtChequeValor.setEnabled(false);
            edtChequeNumero.setEnabled(false);
            edtChequeDtVencimento.setEnabled(false);
        } else {
            vBoxChequeAlterar.setVisible(!enabled);
            vBoxChequesSalvarAlteracoes.setVisible(enabled);
            btnChequeAlterar.setEnabled(!enabled);
            btnChequeExcluir.setEnabled(!enabled);
            btnExcluirCheques.setEnabled(rn.isIncluindoCheques() && (!enabled));
            btnChequeCancelar.setEnabled(enabled);
            btnChequeConfirmar.setEnabled(enabled);
            edtChequeValor.setEnabled(enabled);
            edtChequeNumero.setEnabled(enabled);
            edtChequeDtVencimento.setEnabled(enabled);
        }
    }

    @Override
    public void btnIncluirChequesClick(Event<Object> event) {
        try {
            String retFuncao = rn.incluirCheques();
            if (!retFuncao.equals("S")) {
                Dialog.create()
                        .title(INFORMACAO)
                        .message(retFuncao)
                        .showInformation();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title(INFORMACAO)
                    .message("Falha ao incluir dados cheque. " + "\n" + e.getMessage())
                    .showInformation();
        }
        atualizarValoresCheques();
    }

    private void atualizarValoresCheques() {
        try {
            rn.atualizaValoresInformadosCheque();

        } catch (DataException e) {
            try {
                rn.atualizaValoresInformadosCheque();
            } catch (DataException ex) {
                Dialog.create()
                        .title(INFORMACAO)
                        .message("Falha ao atualizar tela com dados incluidos. " + "\n" + ex.getMessage())
                        .showInformation();
            }
        }
        atualizaTotaisTela();
    }

    @Override
    public void btnExcluirChequesClick(Event<Object> event) {
        Dialog.create().title("Informação").message("Confirma apagar todos os cheques informados?").confirmSimNao((String dialogResult) -> {
            if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                excluirCheque(-99.0);
            }
        });
    }

    private void excluirCheque(Double codPagamentoInf) {
        try {
            rn.excluirCheque(codPagamentoInf);
        } catch (DataException e) {
            e.printStackTrace();
        }
        atualizarValoresCheques();
        if (tbLeadsPgtoVendaInfCheque.isEmpty()) {
            habilitarCheque(true);
            atualizaCamposCheque();
        }
    }

    @Override
    public void btnChequeAlterarClick(Event<Object> event) {
        habilitarCheque(true);
        atualizaCamposCheque();
        edtChequeNumero.setFocus();
    }

    @Override
    public void btnChequeExcluirClick(Event<Object> event) {
        Dialog.create().title("Informação").message("Confirma apagar o cheque " + tbLeadsPgtoVendaInfCheque.getNR_CHEQUE().asString() + " informado?").confirmSimNao((String dialogResult) -> {
            if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                excluirCheque(tbLeadsPgtoVendaInfCheque.getCOD_PAGAMENTO_INF().asDecimal());
            }
        });
    }

    @Override
    public void btnChequeConfirmarClick(Event<Object> event) {
        String respFunc;
        try {
            respFunc = rn.atualizaInfoCheque(edtChequeNumero.getValue().asString(), edtChequeValor.getValue().asDecimal(), edtChequeDtVencimento.getValue().asDate());
        } catch (DataException e) {
            respFunc = "Falha ao confirmar alterações dados cheque!\n " + e.getMessage();
        }
        if (respFunc.equals("S")) {
            Integer codPgtoInf = tbLeadsPgtoVendaInfCheque.getCOD_PAGAMENTO_INF().asInteger();
            try {
                atualizarValoresCheques();
                tbLeadsPgtoVendaInfCheque.locate("COD_PAGAMENTO_INF", codPgtoInf);
                atualizaTotaisTela();
                return;
            } catch (DataException e) {
                e.printStackTrace();
            }
        } else {
            Dialog.create()
                    .title(INFORMACAO)
                    .message(respFunc)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> this.edtChequeNumero.setFocus()));
        }
        atualizaTotaisTela();
    }

    @Override
    public void btnChequeCancelarClick(Event<Object> event) {
        habilitarCheque(false);
        atualizaCamposCheque();
    }

    @Override
    public void edtChequeNumeroExit(Event<Object> event) {
        String soNumero = edtChequeNumero.getValue().asString().replaceAll("[^0-9]", "");
        edtChequeNumero.setValue(soNumero);
    }

    public void gerarInfPgtoCartao(double valorPgto, double codFormaPgto) {
        rn.setValorCartaoIncluir(valorPgto, codFormaPgto);
        edtCartoesObservacao.setEnabled(true);
    }

    public void gerarInfPgtoCheque(double valorPgto, double codFormaPgto, double codCondPgto) {
        rn.setValorChequeIncluir(valorPgto, codFormaPgto, codCondPgto);
        edtChequesObservacao.setEnabled(true);
    }

    @Override
    public void btnAlterarCartaoClick(Event<Object> event) {
        habilitarCartao(true);
        atualizaCamposCartao();
        edtCartaoAlterarNrAut.setFocus();
    }

    @Override
    public void btnCartaoSalvarAlteracaoClick(Event<Object> event) {
        String respFunc = rn.alterarNrAutorizacaoCartao(tbLeadsPgtoVendaInfCartao.getCOD_EMPRESA().asDecimal(),
                tbLeadsPgtoVendaInfCartao.getCOD_ORC_MAPA().asDecimal(),
                tbLeadsPgtoVendaInfCartao.getCOD_FORMA_PGTO().asDecimal(),
                tbLeadsPgtoVendaInfCartao.getCOD_CARTAO_CREDITO().asDecimal(),
                edtCartaoAlterarNrAut.getValue().asString());
        if (!respFunc.equals("S")) {
            Dialog.create()
                    .title(INFORMACAO)
                    .message("Falha ao atualizar nr.Autorização. " + "\n" + respFunc)
                    .showInformation();
        }
        habilitarCartao(false);
        atualizarValoresCartoes();
    }

    @Override
    public void btnCartaoCancelarAlteracaoClick(Event<Object> event) {
        habilitarCartao(false);
        atualizaCamposCartao();
    }

    public Boolean temPagamentoPosSitef() {
        return rn.temPagamentoPosSitef();
    }

    public boolean perguntaDigitarCartao() {
        return rn.perguntaDigitarCartao();
    }

    public void digitarCartaoPosSitef() {
        //rn.setValorCartaoIncluir(this.valorj   valorPgto, codFormaPgto);
        //edtCartaoValorTotal.setValue(rn.getValorCartao());
        rn.setValorDigitarCartaoIncluir();
        edtCartoesObservacao.setEnabled(true);
    }

    public boolean emitirNFComPosSitefAprovado() {
        return rn.emitirNFComPosSitefAprovado();
    }
}
