package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;

public class FrmTabelaNBSA extends FrmTabelaNBSW {

    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;

    public boolean isOk() {
        return ok;
    }

    @Override
    public void FrmTabelaNBSkeyActionAceitar(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionAceitar(event);
    }

    @Override
    public void FrmTabelaNBSkeyActionAlterar(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionAlterar(event);
    }

    @Override
    public void FrmTabelaNBSkeyActionAnterior(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionAnterior(event);
    }

    @Override
    public void FrmTabelaNBSkeyActionCancelar(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionCancelar(event);
    }

    @Override
    public void FrmTabelaNBSkeyActionExcluir(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionExcluir(event);
    }

    @Override
    public void FrmTabelaNBSkeyActionIncluir(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionIncluir(event);
    }

    @Override
    public void FrmTabelaNBSkeyActionPesquisar(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionPesquisar(event);
    }

    @Override
    public void FrmTabelaNBSkeyActionProximo(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionProximo(event);
    }

    @Override
    public void FrmTabelaNBSkeyActionSalvar(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionSalvar(event);
    }

    @Override
    public void FrmTabelaNBSkeyActionSalvarContinuar(final Event<Object> event) {
        super.FrmTabelaNBSkeyActionSalvarContinuar(event);
    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        selecionar();
        super.btnAceitarClick(event);
    }

    private void selecionar() {
        if (tbNbs.isEmpty()) {
            //CrmServiceUtil.showWarning("Nenhum Cod. NBS Selecionado");
            return;
        }
        ok = true;
        close();
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        super.btnAlterarClick(event);
    }

    @Override
    public void btnAnteriorClick(final Event<Object> event) {
        super.btnAnteriorClick(event);
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        super.btnCancelarClick(event);
    }

    @Override
    public void btnConsultarClick(final Event<Object> event) {
        super.btnConsultarClick(event);
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        super.btnExcluirClick(event);
    }

    @Override
    public void btnFiltroAvancadoClick(final Event<Object> event) {
        super.btnFiltroAvancadoClick(event);
    }

    @Override
    public void btnMaisClick(final Event<Object> event) {
        super.btnMaisClick(event);
    }

    @Override
    public void btnNovoClick(final Event<Object> event) {
        super.btnNovoClick(event);
    }

    @Override
    public void btnProximoClick(final Event<Object> event) {
        super.btnProximoClick(event);
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        super.btnSalvarClick(event);
    }

    @Override
    public void btnSalvarContinuarClick(final Event<Object> event) {
        super.btnSalvarContinuarClick(event);
    }

    @Override
    public void efDescricaoEnter(final Event<Object> event) {
        super.efDescricaoEnter(event);
    }

    @Override
    public void gridPrincipalClickImageAlterar(final Event<Object> event) {
        super.gridPrincipalClickImageAlterar(event);
    }

    @Override
    public void gridPrincipalClickImageDelete(final Event<Object> event) {
        super.gridPrincipalClickImageDelete(event);
    }

    @Override
    public void menuHabilitaNavegacaoClick(final Event<Object> event) {
        super.menuHabilitaNavegacaoClick(event);
    }

    @Override
    public void menuItemAbreTabelaAuxClick(final Event<Object> event) {
        super.menuItemAbreTabelaAuxClick(event);
    }

    @Override
    public void menuItemConfgGridClick(final Event<Object> event) {
        super.menuItemConfgGridClick(event);
    }

    @Override
    public void menuItemExportExcelClick(final Event<Object> event) {
        super.menuItemExportExcelClick(event);
    }

    @Override
    public void menuItemExportPdfClick(final Event<Object> event) {
        super.menuItemExportPdfClick(event);
    }

    @Override
    public void menuItemHelpClick(final Event<Object> event) {
        super.menuItemHelpClick(event);
    }

    @Override
    public void menuSelecaoMultiplaClick(final Event<Object> event) {
        super.menuSelecaoMultiplaClick(event);
    }

    @Override
    public void tbNbsAfterScroll(final Event<Object> event) {
        super.tbNbsAfterScroll(event);
    }

}
