package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmOportunidadeVendasW;
import freedom.client.controls.impl.TFHBox;
import freedom.client.controls.impl.TFLabel;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.util.CrmServiceUtil;
import freedom.util.DateUtils;
import freedom.util.EmpresaUtil;
import freedom.util.UiUtils;
import freedom.util.pkg.PkgCrmPartsRNA;

import java.util.Date;

import static freedom.util.CastUtil.asInteger;

public class FrmOportunidadeVendasA extends FrmOportunidadeVendasW {

    private static final long serialVersionUID = 20130827081850L;

    //Vazio - Não Selecionado S - Sim N - Não
    private PkgCrmPartsRNA pkCrmPartsRna = new PkgCrmPartsRNA();
    private String clientePretenteTrocarVeiculo = "";
    private String veiculoAtualdaMarca = "";
    private String pretendeTrocarMesmoModelo = "";
    private String desejaFazerAvalicao = "";
    private String tipoVeiculoDeseja = ""; //N - Novo S - Seminovo
    private boolean ok = false;


    @Override
    public void FFormCreate(Event<Object> event) {
        ajustarAlturaFormulario();
    }

    public void continuarOs(int codEmpresa, int codOsAgenda) {
        try {
            rn.filtrarOsAgenda(codEmpresa, codOsAgenda);

            String collaboration = tbAgendaPersiste.getCOLLABORATION().asString();

            if (collaboration.equals("S")) {
                setColorSimNao(hBoxSimPretVender, hBoxNaoPretVender, true);
                setVisibleComp(true);
                clientePretenteTrocarVeiculo = "S"; //Sim
                setVisibleComp(true);
            }

        } catch (DataException ex) {
            CrmServiceUtil.showError("OPS! Ocorreu um erro inesperado", ex);
        }
    }

    @Override
    public void hBoxNaoPretVenderClick(final Event event) {
        Dialog.showBusy("Processando....");
        setColorSimNao(hBoxSimPretVender, hBoxNaoPretVender, false);
        setVisibleComp(false);
        clientePretenteTrocarVeiculo = "N"; //Não
        setValidacao(false, hBoxPretLabelTrocarVeic, lblValPretendeTrocarVeic);
        FreedomUtilities.invokeLater(() -> {
            proximaEtapa();
            Dialog.clearBusy();
        });
    }

    @Override
    public void hBoxSimPretVenderClick(final Event event) {
        setColorSimNao(hBoxSimPretVender, hBoxNaoPretVender, true);
        setVisibleComp(true);
        clientePretenteTrocarVeiculo = "S"; //Sim
        setValidacao(false, hBoxPretLabelTrocarVeic, lblValPretendeTrocarVeic);
        if (edtDataPretendeTrocar.getValue().isNull()) {
            edtDataPretendeTrocar.setValue(new Date());
        }
    }

    @Override
    public void hBoxNaoVeicAtMarcaClick(final Event event) {
        setColorSimNao(hBoxSimVeicAtMarca, hBoxNaoVeicAtMarca, false);
        veiculoAtualdaMarca = "N"; //Nao
        setValidacao(false, hBoxLabelVeicDaMarca, lblValVeiculodaMarca);
    }

    @Override
    public void hBoxSimVeicAtMarcaClick(final Event event) {
        setColorSimNao(hBoxSimVeicAtMarca, hBoxNaoVeicAtMarca, true);
        veiculoAtualdaMarca = "S"; //Sim
        setValidacao(false, hBoxLabelVeicDaMarca, lblValVeiculodaMarca);
    }

    @Override
    public void hBoxSimTrocarModClick(final Event event) {
        setColorSimNao(hBoxSimTrocarMod, hBoxNaoTrocarMod, true);
        pretendeTrocarMesmoModelo = "S"; //Sim
        setValidacao(false, hBoxPretTrocMsmModel, lblValPretendeTrocarMsmModel);
    }

    @Override
    public void hBoxNaoTrocarModClick(final Event event) {
        setColorSimNao(hBoxSimTrocarMod, hBoxNaoTrocarMod, false);
        pretendeTrocarMesmoModelo = "N"; //Nao
        setValidacao(false, hBoxPretTrocMsmModel, lblValPretendeTrocarMsmModel);
    }

    @Override
    public void hBoxSimAvaliacaoClick(final Event event) {
        setColorSimNao(hBoxSimAvaliacao, hBoxNaoAvaliacao, true);
        desejaFazerAvalicao = "S"; //Sim
        setValidacao(false, hBoxLabelFazerAval, lblValDesejaFazerAval);
    }

    @Override
    public void hBoxNaoAvaliacaoClick(final Event event) {
        setColorSimNao(hBoxSimAvaliacao, hBoxNaoAvaliacao, false);
        desejaFazerAvalicao = "N"; //Não
        setValidacao(false, hBoxLabelFazerAval, lblValDesejaFazerAval);
    }

    @Override
    public void hBoxVeicSemiNovoClick(final Event event) {
        setColorSimNao(hBoxVeicSemiNovo, hBoxVeicNovo, true);
        tipoVeiculoDeseja = "S"; //Seminovo
        setValidacao(false, hBoxLblTipoVeicDeseja, lblValTipoVecDeseja);
    }

    @Override
    public void hBoxVeicNovoClick(final Event event) {
        setColorSimNao(hBoxVeicSemiNovo, hBoxVeicNovo, false);
        tipoVeiculoDeseja = "N"; //Novo
        setValidacao(false, hBoxLblTipoVeicDeseja, lblValTipoVecDeseja);
    }

    private void setColorSimNao(TFHBox hBoxSim, TFHBox hBoxNao, boolean status) {
        String colorDestaque = "16752448";
        String colorDefault = "clSilver";
        hBoxSim.setColor(colorDefault);
        hBoxNao.setColor(colorDefault);

        if (status) {
            hBoxSim.setColor(colorDestaque);
        } else {
            hBoxNao.setColor(colorDestaque);
        }
        hBoxSim.invalidate();
        hBoxNao.invalidate();
    }

    private void setVisibleComp(boolean visible) {
        hBoxDataTrocar.setVisible(visible);
        hBoxVeicAtualMarca.setVisible(visible);
        hBoxPretendTrocarMsmModel.setVisible(visible);
        hBoxFazerAvaliacao.setVisible(visible);
        hBoxTipoVeicDeseja.setVisible(visible);
        hBoxObsLabel.setVisible(visible);
        memOportVendas.setVisible(visible);
        ajustarAlturaFormulario();
    }

    /**
     * Ajusta automaticamente a altura do formulário baseada na visibilidade dos componentes
     */
    private void ajustarAlturaFormulario() {
        UiUtils.fixFormHeight(this);
    }

    @Override
    public void btnProximoClick(final Event event) {
        proximaEtapa();
    }

    private void proximaEtapa() {
        setValidacao(false, hBoxPretLabelTrocarVeic, lblValPretendeTrocarVeic);
        setValidacao(false, hbLabelDataPretendeTrocarVeic, lblValidDataTrocarVeic);
        setValidacao(false, hBoxLabelVeicDaMarca, lblValVeiculodaMarca);
        setValidacao(false, hBoxPretTrocMsmModel, lblValPretendeTrocarMsmModel);
        setValidacao(false, hBoxLabelFazerAval, lblValDesejaFazerAval);
        setValidacao(false, hBoxLblTipoVeicDeseja, lblValTipoVecDeseja);

        Boolean validou = true;

        if (clientePretenteTrocarVeiculo.equals("")) {
            setValidacao(true, hBoxPretLabelTrocarVeic, lblValPretendeTrocarVeic);
        } else {
            if (clientePretenteTrocarVeiculo.equals("S")) {

                if (edtDataPretendeTrocar.getValue().isNull()) {
                    lblValidDataTrocarVeic.setCaption("Informe a Data que Pretende Trocar o Veiculo");
                    setValidacao(true, hbLabelDataPretendeTrocarVeic, lblValidDataTrocarVeic);
                    validou = false;
                } else {
                    Date dataAtual = DateUtils.truncDay(new Date());
                    Date dataValid = DateUtils.truncDay(edtDataPretendeTrocar.getValue().asDate());
                    if (DateUtils.lessThan(dataValid, dataAtual)) {
                        lblValidDataTrocarVeic.setCaption("Data tem que maior ou igual à hoje");
                        setValidacao(true, hbLabelDataPretendeTrocarVeic, lblValidDataTrocarVeic);
                        validou = false;
                    }
                }
                if (veiculoAtualdaMarca.equals("")) {
                    setValidacao(true, hBoxLabelVeicDaMarca, lblValVeiculodaMarca);
                    validou = false;
                }

                if (pretendeTrocarMesmoModelo.equals("")) {
                    setValidacao(true, hBoxPretTrocMsmModel, lblValPretendeTrocarMsmModel);
                    validou = false;
                }

                if (desejaFazerAvalicao.equals("")) {
                    setValidacao(true, hBoxLabelFazerAval, lblValDesejaFazerAval);
                    validou = false;
                }

                if (tipoVeiculoDeseja.equals("")) {
                    setValidacao(true, hBoxLblTipoVeicDeseja, lblValTipoVecDeseja);
                    validou = false;
                }

                int tipoOs = 0;
                try {
                    tipoOs = asInteger(pkCrmPartsRna.getParametro(new Double(EmpresaUtil.getCodEmpresaUserLogged()), "CRM_PARM_FLUXO", "COD_TIPO_EVENTO_LEAD_OS"));
                } catch (DataException e) {
                    CrmServiceUtil.showError("Falha ao consultar parâmetro", e);
                    validou = false;
                }

                if (tipoOs == 0) {
                    CrmServiceUtil.showWarning("Não foi configurado parãmtero de tipo de Evento para novos leads. Favor ir em Parâmetros, grupo Abertura de O.S. Parâmetro: (Cod_Tipo_Evento_Lead_Os)");
                    validou = false;
                }
            }

            if (validou) {
                try {
                    Date dataTrocar = edtDataPretendeTrocar.getValue().asDate();
                    String obsVend = memOportVendas.getValue().asString();
                    rn.salvarOportunidade(clientePretenteTrocarVeiculo, dataTrocar, veiculoAtualdaMarca, pretendeTrocarMesmoModelo,
                            desejaFazerAvalicao, tipoVeiculoDeseja, obsVend);
                    ok = true;
                    close();
                } catch (DataException ex) {
                    ok = false;
                    CrmServiceUtil.showError("OPS! Ocorreu um erro inesperado", ex);
                }
            }

        }
    }

    private void setValidacao(boolean mostrar, TFHBox hBox, TFLabel label) {
        if (mostrar) {
            hBox.setPaddingTop(0);
            label.setVisible(true);
        } else {
            hBox.setPaddingTop(8);
            label.setVisible(false);
        }
        hBox.invalidate();
    }

    @Override
    public void edtDataPretendeTrocarChange(Event<Object> event) {
        setValidacao(false, hbLabelDataPretendeTrocarVeic, lblValidDataTrocarVeic);
    }

    @Override
    public void btnVoltarOsClick(final Event event) {
        ok = false;
        close();
    }

    public boolean isOk() {
        return ok;
    }

    public boolean getExistLeadOpt(double codCliente) {
        boolean result = false;
        try {
            result = rn.getExistLeadOpt(codCliente);
        } catch (DataException ex) {
            CrmServiceUtil.showError("OPS! Ocorreu um erro inesprado", ex);
        }
        return result;
    }

    public boolean getExistPkgEventoGold() throws DataException {
        boolean result = false;
        try {
            result = rn.getExistPkgEventoGold();
        } catch (DataException ex) {
            CrmServiceUtil.showError("OPS! Ocorreu um erro inesprado", ex);
        }
        return result;

    }

}
