package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmTimeTemplateHorariosW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.CastUtil;
import freedom.util.DateUtil;
import freedom.util.DateUtils;
import freedom.util.EmpresaUtil;

import java.util.Calendar;
import java.util.Date;

public class FrmTimeTemplateHorariosA extends FrmTimeTemplateHorariosW {
    private static final long serialVersionUID = 20130827081850L;

    public FrmTimeTemplateHorariosA(){
        this.filtrarDadosInicio();
        this.modoEdicao(false);
        this.modoEdicaoHorario(false);
    }

    public int getIDTemplate(){
        return rn.getIDTemplate();
    }

    /**
     * Habilita o modo edição, habilitando e desabilitando os botões
     * @param edicao caso true habilita o modo edição, do contrario desabilita o modo edição
     */
    public void modoEdicao(boolean edicao){
        this.btnNovo.setEnabled(!edicao);
        this.btnAlterar.setEnabled(!edicao);
        this.btnCancelar.setEnabled(edicao);
        this.btnSalvar.setEnabled(edicao);
        this.gridTemplates.setEnabled(!edicao);
        this.gridTemplatesHorariosAmostra.setEnabled(!edicao);
        this.edDescricao.setEnabled(edicao);
        this.vboxEditarHoarario.setVisible(edicao);
        if (edicao){
            this.pgControlTemplates.selectTab(tabCadastro);
        }else{
            //this.pgControlTemplates.selectTab(tabListagem);
        }
        vboxDetalheTemplate.invalidate();
    }

    /**
     * Habilita o modo edição dos horarios dos templates
     * @param edicao caso true, habilita a edição, caso false desabilita o modo edição
     */
    public void modoEdicaoHorario(boolean edicao){
        this.btnAlterarHorario.setEnabled(!edicao);
        this.gridTemplatesHorarios.setEnabled(!edicao);
        this.btnSalvarHorario.setEnabled(edicao);
        this.btnCancelarHorario.setEnabled(edicao);
        btnLimparHorario.setEnabled(edicao);
        this.edInicial.setEnabled(edicao);
        this.edFinal.setEnabled(edicao);
    }

    public void filtrarDadosInicio(){
        try {
            this.rn.filtrarTbTimeTemplate();
        } catch (DataException e) {
            EmpresaUtil.showError("Não foi filtrar o time template", e);
        }
    }

    @Override
    public void tbTimeTemplateAfterScroll(Event<Object> event) {
        try {
            this.rn.filtrarTbTimeTemplateHorario();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao filtrar o time template horario", e);
        }
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        boolean estaAlterandoTemplete =btnSalvar.isEnabled();
        if (estaAlterandoTemplete){
            Dialog.create()
                .title("Edição")
                .message("Cancelar Edições sem salvar")
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                               close();
                        }else{
                            return;
                        }
                    });
        }else{
            close();
        }

    }


    @Override
    public void btnNovoClick(Event<Object> event) {
        try {
            this.modoEdicao(true);
            this.rn.novoTbTimeTemplate();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao criar novo Time Template",e);
        }
    }

    @Override
    public void btnAlterarClick(Event<Object> event) {
        try {
            if (this.rn.tbTimeTemplateIsEmpty()){
                EmpresaUtil.showMessage("Validação", "Nenhum Registro selecionado");
                return;
            }
            this.modoEdicao(true);
            this.rn.editarTbTimeTemplate();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao Alterar Time Template", e);
        }
    }

    @Override
    public void btnExcluirClick(Event<Object> event) {
        super.btnExcluirClick(event);
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        try {
            if (this.validarTemplate()){
                this.rn.salvarDados();
                this.modoEdicao(false);
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao Salvar time template", e);
        }
    }

    public boolean validarTemplate(){
        boolean estaAlterandoAtendimentoHorario =btnSalvarHorario.isEnabled();
        if (estaAlterandoAtendimentoHorario){
            Dialog.create()
                    .title("Validação")
                    .message("Salve primeiro as alterações do periodo de atendimento!")
                    .showInformation(t -> {

                    });
            return false;
        }

        String descricao = edDescricao.getValue().asString();
        if (descricao == null || descricao.trim().length() == 0){
            Dialog.create()
                .title("Validação")
                .message("O campo descrição não pode ser vazio")
                .showInformation(t -> {
                edDescricao.setFocus();
                edDescricao.setSelectionRange(0, edDescricao.getValue().asString().length()+1);
            });
            return false;
        }
        return true;
    }

    @Override
    public void btnCancelarClick(Event<Object> event) {
        try {

            boolean estaAlterandoAtendimentoHorario =btnSalvarHorario.isEnabled();
            if (estaAlterandoAtendimentoHorario){
                btnCancelarHorarioClick(null);
            }

            this.rn.cancelarAlteracaoTimeTemplate();
            this.modoEdicao(false);




        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao Cancelar Alteração Time Template", e);
        }
    }

    @Override
    public void btnAlterarHorarioClick(Event<Object> event) {
        try {
            if (this.rn.tbTimeTemplateHorarioIsEmpty()){
                EmpresaUtil.showMessage("Validação", "Nenhum Registro selecionado");
                return;
            }
            this.modoEdicaoHorario(true);
            this.rn.editarHorarioTimeTemplate();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao tentar alterar horario", e);
        }
    }

    @Override
    public void btnSalvarHorarioClick(Event<Object> event) {
        try {
            //tornarDataInicialeDataFinalMesmoDia();
            if (this.validarHorario()){
                this.modoEdicaoHorario(false);
                this.rn.salvarHorarioTimeTemplate();
            }
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao salvar horario", e);
        }
    }

    @Override
    public void btnCancelarHorarioClick(Event<Object> event) {
        try {
            this.modoEdicaoHorario(false);
            this.rn.cancelarAlteracaoHorarioTimeTemplate();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao salvar horario", e);
        }
    }

    /**
     * valida se horario é valido
     * @return true caso o horario seja valido e false caso o horario seja inválido
     */
    public boolean validarHorario(){
        Date dataInicial = this.edInicial.getValue().asDate();
        Date dataFinal = this.edFinal.getValue().asDate();


        /* caso uma seja nula ambas devem ser nulas */
        if (!((dataInicial == null && dataFinal == null) || (dataInicial != null && dataFinal != null))){
            Dialog.create()
                    .title("Validação")
                    .message("informe a hora de início e a hora de término, ou deixe ambas em branco caso não tinha expediente definido para o dia.")
                    .showInformation(t -> {
                        if (dataInicial == null){
                            this.edInicial.setFocus();
                            this.edInicial.setSelectionRange(0, edInicial.getValue().asString().length() + 1);
                        }else{
                            this.edFinal.setFocus();
                            this.edFinal.setSelectionRange(0, edFinal.getValue().asString().length() + 1);
                        }
                    });
            return false;
        }


        /* valido se segunda data é maior que a primeira */
        if (DateUtil.primeiraDataMenorSegundaData(dataFinal,dataInicial)){
            Dialog.create()
                .title("Validação")
                .message("Informe a Hora final maior que hora inicial")
                .showInformation(t -> {
                    this.edFinal.setFocus();
            });
            return false;
        }
        return true;
    }

    /**
     * prepara tela, marcando o template com id passado no parametro
     * @param idTemplate
     */
    public void ajustarTelaMarcarTemplateEspecifico(int idTemplate){
        try {
            rn.selecionarTbTimeTemplate(idTemplate);
            pgControlTemplates.selectTab(tabCadastro);
        } catch (DataException e) {
            EmpresaUtil.showError("erro selecionar um template!", e);
        }
    }

    /**
     * prepara tela, marcando o template com id passado no parametro e escondendo todas as funções e abas exceto a aba de template horarios
     * @param idTemplate
     */
    public void ajustarTelaSomentevisualizarTemplateHorario(int idTemplate){
        try {
            rn.selecionarTbTimeTemplate(idTemplate);
            btnNovo.setVisible(false);
            btnExcluir.setVisible(false);
            btnAlterar.setVisible(false);
            btnSalvar.setVisible(false);
            btnCancelar.setVisible(false);
            tabListagem.setVisible(false);
            pgControlTemplates.selectTab(tabCadastro);
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao tentar ajustar tela apenas para visualização de template horario", e);
        }
    }

    @Override
    public void btnLimparHorarioClick(Event<Object> event) {
        try {
            rn.tbTimeTemplatelimparCamposHorarios();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao tentar limpar horarios!",e);
        }
    }

    /**
     * Função deixa o dd/mm/yyyy da data inicial e final iguais, mudando apenas as horas
     * para não permitir reservar se extender para dias diferentes
     */
    public void tornarDataInicialeDataFinalMesmoDia(){
        if (edInicial.getValue().isNull() || edFinal.getValue().isNull()){
            //edFinal.setValue(edInicial.getValue());
            return;
        }

        /* preciso deixar a data inicial e data final com o mesmo dia 01/01/2000 */
        /* salvo a hora anterior */
        Date dataEdFinal = edFinal.getValue().asDate();
        Calendar calendarEdFinal = Calendar.getInstance();
        calendarEdFinal.setTime(dataEdFinal);
        calendarEdFinal.set(2000,1,1);
        calendarEdFinal.set(Calendar.SECOND, 0);
        //int horaFinal = calendarEdFinal.get(Calendar.HOUR_OF_DAY);
        //int minutoFinal = calendarEdFinal.get(Calendar.MINUTE);
        edFinal.setValue(calendarEdFinal.getTime());

        Date dataEdInicial = edInicial.getValue().asDate();
        Calendar calendarEdInicial = Calendar.getInstance();
        calendarEdInicial.setTime(dataEdInicial);
        calendarEdInicial.set(2000,1,1);
        calendarEdInicial.set(Calendar.SECOND, 0);
        //int horaInicial = calendarEdInicial.get(Calendar.HOUR_OF_DAY);
        //int minutoInicial = calendarEdInicial.get(Calendar.MINUTE);
        edInicial.setValue(calendarEdInicial.getTime());


        /* pego a nova data e adiciono a hora anterior */
        //Date dataPadrao = DateUtil.getDataHoraDoTexto("01/01/2000");
        //calendarEdFinal.setTime(dataPadrao);
        //calendarEdFinal.set(2000,1,1);
        //calendarEdFinal.set(Calendar.HOUR_OF_DAY, horaFinal);
        //calendarEdFinal.set(Calendar.MINUTE, minutoFinal);
        //edFinal.setValue(calendarEdFinal.getTime());
    }

}
