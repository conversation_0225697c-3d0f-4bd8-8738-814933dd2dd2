package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAlterarEnderecoClienteW;
import freedom.client.controls.IComponent;
import freedom.client.controls.IFocusable;
import freedom.client.controls.impl.TFCombo;
import freedom.client.controls.impl.TFDecimal;
import freedom.client.controls.impl.TFGridPanel;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.lang.Trictionary;
import freedom.util.EmpresaUtil;
import freedom.util.FRLogger;

public class FrmAlterarEnderecoClienteA extends FrmAlterarEnderecoClienteW {

    private static final long serialVersionUID = 20130827081850L;

    private final Trictionary<String, IFocusable, TFGridPanel> edits = new Trictionary<>();

    public FrmAlterarEnderecoClienteA() {
        this.tbCidades.setMaxRowCount(0);
        this.tbUf.setMaxRowCount(0);
        this.prencherEditsValidacao();
        FreedomUtilities.invokeLater(() -> this.edtBairro.setFocus());
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        FRLogger.log("Formulário: " + this.getName(),
                this.getClass());
    }

    public void filtrarEnderecoCliente(long codCliente, long tpEndereco) {
        try {
            rn.filtrarEnderecoCliente(codCliente, tpEndereco);
        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao Filtrar Cliente")
                    .message("[35] " + ex.getMessage())
                    .showError();
        }
    }

    private void filtrarCidade(String uf) {
        try {
            rn.filtrarCidade(uf);
        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao Filtrar Cidade")
                    .message("[35] " + ex.getMessage())
                    .showError();
        }
    }

    private void prencherEditsValidacao() {
        this.edits.put("CLIENTES.UF",
                this.cbUF,
                this.gridAlterarDadosBasicos);
        this.edits.put("CLIENTES.CIDADE",
                this.cbCidade,
                this.gridAlterarDadosBasicos);
    }

    private void setFocus(Value aCampoFoco) {
        if (this.edits.containsKey(aCampoFoco.asString())) {
            TFGridPanel panel = this.edits.getSecondValue(aCampoFoco.asString());
            boolean panelVisible = panel.isVisible();
            if (panelVisible) {
                panel.setVisible(true);
            }
            IComponent edt = (IComponent) this.edits.getFirstValue(aCampoFoco.asString());
            if (edt instanceof TFDecimal) {
                ((TFDecimal) edt).select();
            }
            if (edt instanceof TFCombo) {
                ((TFCombo) edt).setOpen(true);
            }
            ((IFocusable) edt).setFocus();
        }
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        try {
            close();
        } catch (Exception ex) {
            EmpresaUtil.showError("CRMParts - Falha ao Cancelar", ex);
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        String retFuncao;
        Value aCampoFoco = new Value(null);
        try {
            retFuncao = this.rn.alterarEndCliente(aCampoFoco);
            if (!retFuncao.equals("S")) {
                Dialog.create()
                        .title("Erro")
                        .message(retFuncao)
                        .showError(t -> FreedomUtilities.invokeLater(() -> this.setFocus(aCampoFoco)));
                return;
            }
            this.close();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Falha ao Salvar",
                    dataException);
        }
    }

    @Override
    public void cbUFChange(Event<Object> event) {
        try {
            if (!this.tbAlteraEnderecoCliente.getUF().asString().isEmpty()) {
                this.filtrarCidade(this.tbAlteraEnderecoCliente.getUF().asString());
                this.tbAlteraEnderecoCliente.setUF(this.cbUF.getValue().asString());
            } else {
                this.tbAlteraEnderecoCliente.setUF("");
                this.tbAlteraEnderecoCliente.setCOD_CIDADES(0);
                this.tbAlteraEnderecoCliente.setCEP("");
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Falha",
                    dataException);
        }
    }

    @Override
    public void cbCidadeChange(Event<Object> event) {
        try {
            if (!this.tbAlteraEnderecoCliente.getCIDADE().asString().isEmpty()) {
                this.tbAlteraEnderecoCliente.setCEP(this.tbCidades.getCEP().asString());
            } else {
                this.tbAlteraEnderecoCliente.setCEP("");
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Falha",
                    dataException);
        }
    }

    @Override
    public void cbUFClearClick(Event<Object> event) {
        try {
            tbAlteraEnderecoCliente.setCOD_CIDADES(0);
            tbAlteraEnderecoCliente.setCEP("");
            tbCidades.close();
        } catch (DataException ex) {
            EmpresaUtil.showError("CRMParts - Falha", ex);
        }
    }

    @Override
    public void cbCidadeClearClick(Event<Object> event) {
        try {
            tbAlteraEnderecoCliente.setCEP("");
        } catch (DataException ex) {
            EmpresaUtil.showError("CRMParts - Falha", ex);
        }
    }
}
