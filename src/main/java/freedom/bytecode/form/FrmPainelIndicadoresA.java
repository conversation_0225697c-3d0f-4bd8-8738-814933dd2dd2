package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmPainelIndicadoresW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;
import org.zkoss.zk.ui.Executions;
import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;

public class FrmPainelIndicadoresA extends FrmPainelIndicadoresW {

    private static final long serialVersionUID = 20130827081850L;

    private String filtroAtivo = "S";

    private Integer idGrupoClasse = 0;

    public FrmPainelIndicadoresA() {
        this.initForm();
    }

    private void initForm() {
        try {
            // Orione quer o idGrupoClasse fixo por aplicação
            // identificar o idGrupoClasse a ser fixado na tabela bsc_grupo_classe
            // Alterar aqui FrmPainelIndicadoresA e FrmGerencialPainelA

            URL url = new URL(((HttpServletRequest) Executions.getCurrent().getNativeRequest()).getRequestURL().toString());
            if (url.toString().toLowerCase().contains("crmservice")) {
                idGrupoClasse = 3;
            } else if (url.toString().toLowerCase().contains("crmparts")) {
                idGrupoClasse = 6;
            } else if (url.toString().toLowerCase().contains("crmgold")) {
                idGrupoClasse = 1;
            }
            hboxAtivo.setAttribute("SCLASS_BASE", "hbbuttonleftradius");
            hboxInativo.setAttribute("SCLASS_BASE", "hbbuttonrightradius");
            rn.carregaComboPainel(idGrupoClasse);
            if (tbComboGrupo.count() > 0) {
                cbbGrupoLista.setValue(tbComboGrupo.getID().asInteger());
            }
            setFiltroAtivo("A");
        } catch (DataException | MalformedURLException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void pesquisarPainel() {
        try {
            rn.carregaGridPainel(filtroAtivo, cbbGrupoLista.getValue().asDecimal());
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void cbbGrupoListaChange(final Event<Object> event) {
        pesquisarPainel();
    }

    @Override
    public void cbbGrupoListaClearClick(final Event<Object> event) {
        pesquisarPainel();
    }

    private void setFiltroAtivo(String tipo) {
        String colorDefault = "clWhite";
        String colorDestaque = "clSilver";
        hboxAtivo.setColor(colorDefault);
        hboxInativo.setColor(colorDefault);
        if (tipo.equals("A")) {
            hboxAtivo.setColor(colorDestaque);
            filtroAtivo = "S";
        } else {
            hboxInativo.setColor(colorDestaque);
            filtroAtivo = "N";
        }
        pesquisarPainel();
    }

    @Override
    public void hboxAtivoClick(final Event<Object> event) {
        setFiltroAtivo("A");
    }

    @Override
    public void hboxInativoClick(final Event<Object> event) {
        setFiltroAtivo("I");
    }

    @Override
    public void btnNovoClick(final Event<Object> event) {
        FrmCadastroPainelIndicadoresA frm = new FrmCadastroPainelIndicadoresA("INS", 0, idGrupoClasse);
        FormUtil.doShow(frm, t -> {
            if (frm.isOk()) {
                pesquisarPainel();
            }
        });
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        if (tbListaPainel.getID().asInteger() == 0) {
            EmpresaUtil.showInformationMessage("Selecione algum registro na grade.");
            return;
        }

        FrmCadastroPainelIndicadoresA frm = new FrmCadastroPainelIndicadoresA("UPD", tbListaPainel.getID().asInteger(), idGrupoClasse);
        FormUtil.doShow(frm, t -> {
            if (frm.isOk()) {
                pesquisarPainel();
            }
        });
    }

    @Override
    public void pgIndicadoresChange(Event<Object> event) {
        btnNovoIndicadores.setEnabled(true);
        btnExcluirIndicadores.setEnabled(true);
        btnNovoAcessoFunc.setEnabled(true);
        btnExcluirAcessoFunc.setEnabled(true);
        if (pgIndicadores.getSelectedTab() == tabIndicadores) {
            pesquisarIndicadores();
            if (tbListaPainel.getID().asInteger() == 0) {
                btnNovoIndicadores.setEnabled(false);
                btnExcluirIndicadores.setEnabled(false);
            }
        } else if (pgIndicadores.getSelectedTab() == tabAcessoFuncao) {
            pesquisarAcessoFuncao();
            if (tbListaPainel.getID().asInteger() == 0) {
                btnNovoAcessoFunc.setEnabled(false);
                btnExcluirAcessoFunc.setEnabled(false);
            }
        }
    }

    private void pesquisarIndicadores() {
        try {
            rn.pesquisarIndicadores(tbListaPainel.getID().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    private void pesquisarAcessoFuncao() {
        try {
            rn.pesquisarAcessoFuncao(tbListaPainel.getID().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnNovoIndicadoresClick(final Event<Object> event) {
        FrmCadastroIndicadoresA frm = new FrmCadastroIndicadoresA(tbListaPainel.getID().asInteger(), idGrupoClasse);
        FormUtil.doShow(frm, t -> {
            if (frm.isOk()) {
                pgIndicadores.selectTab(1);
                pesquisarIndicadores();
            }
        });
    }

    @Override
    public void btnExcluirIndicadoresClick(final Event<Object> event) {
        if (tbGridIndicadores.getID().asInteger() == 0) {
            EmpresaUtil.showInformationMessage("Selecione algum registro na grade.");
            return;
        }
        Dialog.create()
                .title("Confirmação")
                .message("Confirma a Exclusão do Indicador {" + tbGridIndicadores.getDESCRICAO().asString() + "} ?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            rn.pesquisaPainelIndicador(tbGridIndicadores.getID_PAINEL().asInteger(),
                                    tbGridIndicadores.getID_GRUPO().asInteger(),
                                    tbGridIndicadores.getID().asInteger(),
                                    false,
                                    0);
                            if (tbPainelIndicador.count() > 0) {
                                tbPainelIndicador.delete();
                                tbPainelIndicador.applyUpdates();
                            }
                            pgIndicadores.selectTab(1);
                            pesquisarIndicadores();
                        } catch (DataException ex) {
                            EmpresaUtil.showError("Erro", ex);
                        }
                    }
                });
    }

    @Override
    public void btnNovoAcessoFuncClick(final Event<Object> event) {
        FrmCadastroAcessoFuncaoA frm = new FrmCadastroAcessoFuncaoA(tbListaPainel.getID().asInteger());
        FormUtil.doShow(frm, t -> {
            if (frm.isOk()) {
                pgIndicadores.selectTab(2);
                pesquisarAcessoFuncao();
            }
        });
    }

    @Override
    public void btnExcluirAcessoFuncClick(final Event<Object> event) {
        if (tbGridAcessoFuncao.getID().asInteger() == 0) {
            EmpresaUtil.showMessage("Atenção", "Necessário selecionar um registro na grid!");
            return;
        }

        Dialog.create()
                .title("Confirma")
                .message("Confirma a Exclusão da Função {" + tbGridAcessoFuncao.getFUNCAO().asString() + "}?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            rn.pesquisaPainelAcessoFuncao(tbListaPainel.getID().asInteger(), tbGridAcessoFuncao.getID().asInteger());
                            if (tbPainelAcessoFuncao.count() > 0) {
                                tbPainelAcessoFuncao.delete();
                                tbPainelAcessoFuncao.applyUpdates();
                            }
                            pgIndicadores.selectTab(2);
                            pesquisarAcessoFuncao();
                        } catch (DataException ex) {
                            EmpresaUtil.showError("Erro", ex);
                        }
                    }
                });
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        if (tbListaPainel.getID().asInteger() == 0) {
            EmpresaUtil.showMessage("Atenção", "Necessário selecionar um registro na grid!");
            return;
        }

        Dialog.create()
                .title("Confirma")
                .message("Confirma a Exclusão Painel {" + tbListaPainel.getDESCRICAO().asString() + "}?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            rn.excluirPainel(tbListaPainel.getID().asInteger());
                            pgIndicadores.selectTab(0);
                            pesquisarPainel();
                        } catch (DataException ex) {
                            EmpresaUtil.showError("Erro", ex);
                        }
                    }
                });
    }

    @Override
    public void btnOrderAcimaClick(final Event<Object> event) {
        try {
            int order = tbGridIndicadores.getDISPLAY_ORDER().asInteger();
            rn.alterarOrdemIndicadores(tbGridIndicadores.getID_PAINEL().asInteger(), tbGridIndicadores.getID().asInteger(), order, "Acima");
            pgIndicadores.selectTab(1);
            pesquisarIndicadores();
            tbGridIndicadores.locate("DISPLAY_ORDER", order - 1);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void btnOrderAbaixoClick(final Event<Object> event) {
        try {
            int order = tbGridIndicadores.getDISPLAY_ORDER().asInteger();
            rn.alterarOrdemIndicadores(tbGridIndicadores.getID_PAINEL().asInteger(), tbGridIndicadores.getID().asInteger(), order, "Abaixo");
            pgIndicadores.selectTab(1);
            pesquisarIndicadores();
            tbGridIndicadores.locate("DISPLAY_ORDER", order + 1);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }


}
