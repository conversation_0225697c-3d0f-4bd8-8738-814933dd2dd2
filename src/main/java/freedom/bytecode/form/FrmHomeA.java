package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmHomeW;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.ExceptionEngine;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.*;
import lombok.Getter;
import lombok.Setter;

import static freedom.util.CastUtil.asString;

public class FrmHomeA extends FrmHomeW {

    private static final long serialVersionUID = 20180503081850L;

    private static final String USUARIO_COD_FUNCAO = "USUARIO_COD_FUNCAO";

    private static final String USUARIO_COD_EMPRESA = "USUARIO_COD_EMPRESA";

    private static final String USUARIO_COD_DEPARTAMENTO = "USUARIO_COD_DEPARTAMENTO";

    private static final String USUARIO_COD_SEGMENTO = "USUARIO_COD_SEGMENTO";

    private static final String IMAGES = "/images/";

    private static final String APPLICATION_NAME = "APPLICATION_NAME";

    private final IWorkList iWorkList = WorkListFactory.getInstance();

    @Getter
    private final String usuarioLogado = this.iWorkList.get("USUARIO_LOGADO").asString();

    @Getter
    private int codFuncao = 0;

    @Getter
    private int codEmpresa = 0;

    @Setter
    private boolean imageFormDefault = true;

    private boolean isExibiuMensagemFiltroPesquisaAvancada = false;

    public FrmHomeA() {
        // registrar o pageControl principal da aplicacao
        FormUtil.registerPgCtrl(
                this.pgctrlPrincipal
        );
        // inicializar table para últimos acessos
        this.setInfoUser(this.usuarioLogado);
        this.imgLogo.setImageSrc(IMAGES + this.iWorkList.sysget(APPLICATION_NAME).asString() + "310038.png?version=1");
        this.imgBuscar.setImageSrc(IMAGES + this.iWorkList.sysget(APPLICATION_NAME).asString() + "700089.png?version=1");
        this.imgBuscarItem.setImageSrc(IMAGES + this.iWorkList.sysget(APPLICATION_NAME).asString() + "700089.png?version=1");
        this.imageUsuario.setImageSrc(IMAGES + this.iWorkList.sysget(APPLICATION_NAME).asString() + "700090.png?version=1");
        String versao = ApplicationUtil.getApplicationVersion();
        if (versao != null) {
            this.lblVersaoSistema.setCaption(
                    "Versão: "
                            + versao
                            + " - Usuário: "
                            + this.usuarioLogado
            );
            String nomeCompletoELoginDoUsuario = EmpresaUtil.getNomeCompletoELoginDoUsuario(
                    this.usuarioLogado
            );
            this.lblUsuarioLogadoTopoEsquerdo.setCaption(
                    "Usuário: "
                            + nomeCompletoELoginDoUsuario
            );
            this.lblVersaoSistemaTopoDireito.setCaption(
                    "Versão: "
                            + versao
            );
        }
        this.trocarSenhaSeNecessario();
    }

    private void trocarSenhaSeNecessario() {
        boolean usuarioObrigadoATrocarSenhaAoLogar = this.rn.isUsuarioObrigadoATrocarSenhaAoLogar(
                this.usuarioLogado
        );
        if (usuarioObrigadoATrocarSenhaAoLogar) {
            FrmAlterarSenhaA frmAlterarSenhaA = new FrmAlterarSenhaA();
            FormUtil.doShow(
                    frmAlterarSenhaA
                    ,t -> this.trocarSenhaSeNecessario());
        }
    }

    private void setInfoUser(String user) {
        try {
            this.tbUserInformation.close();
            this.tbUserInformation.clearFilters();
            this.tbUserInformation.clearParams();
            this.tbUserInformation.addParam("USUARIO", user);
            this.tbUserInformation.open();
            this.codFuncao = this.tbUserInformation.getCOD_FUNCAO().asInteger();
            this.codEmpresa = this.tbUserInformation.getCOD_EMPRESA().asInteger();
            this.iWorkList.put(USUARIO_COD_FUNCAO, asString(this.codFuncao));
            this.iWorkList.put(USUARIO_COD_EMPRESA, asString(this.codEmpresa));
            String codEmpresaDepartamento = this.tbUserInformation.getCOD_EMPRESA_DEPARTAMENTO().asString();
            this.iWorkList.put(USUARIO_COD_DEPARTAMENTO, codEmpresaDepartamento);
            String codSegmento = this.tbUserInformation.getCOD_SEGMENTO().asString();
            this.iWorkList.put(USUARIO_COD_SEGMENTO, codSegmento);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao consultar informações do usuário", dataException);
        }
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        FRLogger.log("Formulário: " + this.getName(),
                this.getClass());
        this.accordion.setFocus();
        this.loadMenu();
        this.loadPromptBuscarCodigo();
    }

    private void loadPromptBuscarCodigo() {
        String buscarPor = rn.getPadraoPesquisaitem();
        String promptBuscaCodigo;
        switch (buscarPor){
            case "C":  promptBuscaCodigo = "Buscar por Código";
            break;
            case "B": promptBuscaCodigo = "Buscar por Código de Barras";
            break;
            case "P": promptBuscaCodigo = "Buscar por Part-number/Código item";
            break;
            case "D": promptBuscaCodigo = "Buscar por Descrição";
            break;
            default:
                promptBuscaCodigo = "Buscar por Código/Descrição Item";
        }
        edtBuscarCodigo.setPrompt(promptBuscaCodigo);
    }

    private void loadMenu() {
        try {
            String sistema = SystemUtil.getSistema();
            this.rn.loadMenu(this.mnuPrincipal, 20001, this.codFuncao, sistema, event -> {
                // Posição 0 = formName
                // Posição 1 = título
                // Posição 2 = Id. Imagem
                // Posição 3 = idForm/IdMenu
                // Posição 4 = Tipo: F-Form/M-Menu
            }, true, this.imageFormDefault);
            this.accordion.setMenu(this.mnuPrincipal);
        } catch (Exception exception) {
            ExceptionEngine.register(exception);
        }
        // sempre que for adicionada uma aba ao pagecontrol, testar para ver se
        // precisa fechar o borderPanel do menu
        this.pgctrlPrincipal.addEventListener("onAppendTab", (EventListener<Event<Object>>) (Event<Object> event) -> this.borderPanel.setOpen("W", this.pgctrlPrincipal.getPageCount() == 1));
    }

    @Override
    public void imageUsuarioClick(final Event<Object> event) {
        this.popMenuLogin.open(this.imageUsuario);
    }

    @Override
    public void mmAlterarSenhaClick(final Event<Object> event) {
        FrmAlterarSenhaA frmAlterarSenhaA = new FrmAlterarSenhaA();
        FormUtil.createTab(frmAlterarSenhaA,
                t -> {
                },
                true);
    }

    @Override
    public void mmSairClick(final Event<Object> event) {
        Dialog.create().title("Confirmação").message("Deseja realmente sair do sistema?").confirmSimNao((String dialogResult) -> {
            if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                if (this.iWorkList.get("CLIENTE_LOGADO").asString().isEmpty()) {
                    this.iWorkList.remove(IWorkList.USUARIO_LOGADO);
                    FormUtil.redirect("app?FrmLogin");
                } else {
                    this.iWorkList.remove("CLIENTE_LOGADO");
                    this.iWorkList.remove(IWorkList.USUARIO_LOGADO);
                    FormUtil.redirect("client?FrmLoginCliente");
                }
            }
        });
    }

    @Override
    public void pgctrlPrincipalChange(final Event<Object> event) {
        // sempre que for removido uma aba ao pagecontrol, testar pra ver se
        // precisa fechar o borderPanel do menu
        this.borderPanel.setOpen("W", (this.pgctrlPrincipal.getPageCount() == 1));
    }

    @Override
    public void tmrRelogioTimer(final Event<Object> event) {
        //
    }

    @Override
    public void edtBuscarChanging(Event<Object> event) {
        this.btnSearch.setVisible(!((Value) event.getValue()).isEmpty()
                && ((Value) event.getValue()).asString().length() > 3);
        this.btnPesquisaAvancadaCliente.setVisible(!this.btnSearch.isVisible());
        if (!this.isExibiuMensagemFiltroPesquisaAvancada) {
            Dialog.create()
                    .showNotificationInfo("Pesquisa mais eficiente com utilização de filtros.",
                    "end_center",
                    10000, // Tempo em milissegundos para exibir a mensagem
                    this.btnPesquisaAvancadaCliente,// Nome do componente do formulário em frente ao qual a mensagem será exibida
                    true);
        }
        this.isExibiuMensagemFiltroPesquisaAvancada = true;
    }

    @Override
    public void btnSearchClick(final Event<Object> event) {
        this.rn.buscarPor(this.edtBuscar.getValue().asString().trim());
    }

    @Override
    public void edtBuscarEnter(Event<Object> event) {
        this.btnSearchClick(event);
    }

    @Override
    public void mmPerfilClick(final Event<Object> event) {
        FrmPerfilA frmPerfilA = new FrmPerfilA(this.usuarioLogado);
        FormUtil.createTab(frmPerfilA,
                t -> {},
                true);
    }

    @Override
    public void imgLogoClick(final Event<Object> event) {
        FormUtil.redirect("http://www.nbsi.com.br",
                true);
    }

    @Override
    public void mmHelpClick(final Event<Object> event) {
        // Método implementado em FrmHomeU
    }

    @Override
    public void edtBuscarCodigoEnter(Event<Object> event) {
        this.rn.buscarCodigoPor(this.edtBuscarCodigo.getValue().asString().trim());
    }

    @Override
    public void edtBuscarCodigoChanging(Event<Object> event) {
        this.btnSearchCodigo.setVisible(!((Value) event.getValue()).isEmpty() && ((Value) event.getValue()).asString().length() > 3);
    }

    @Override
    public void btnSearchCodigoClick(final Event<Object> event) {
        this.rn.buscarCodigoPor(this.edtBuscarCodigo.getValue().asString().trim());
    }

    @Override
    public void iconClassNovoClick(final Event<Object> event) {
        // Método implementado em FrmHomeU
    }

    @Override
    public void iconItensOrcClick(final Event<Object> event) {
        // Método implementado em FrmHomeU
    }

    @Override
    public void FrmHomepesquisarItensEmBranco(Event<Object> event) {
        // FUNCAO IMPLEMENTADA NO MODULO CRMPARTS
    }

    @Override
    public void tmrMensagemTimer(Event<Object> event) {
        // FUNCAO IMPLEMENTADA NO MODULO CRMPARTS
    }

    @Override
    public void btnPesquisaAvancadaClienteClick(final Event<Object> event) {
        // Função implementada no módulo CRMParts (FrmHomeU)
    }

    @Override
    public void lblVersaoSistemaClick(Event<Object> event) {
        FormUtil.redirect(
                "http://ajuda.nbsi.com.br:84/index.php?title=Vers%C3%B5es_CRMPARTS&redirect=no"
                ,true
        );
    }

    @Override
    public void lblVersaoSistemaTopoDireitoClick(Event<Object> event) {
        FormUtil.redirect(
                "http://ajuda.nbsi.com.br:84/index.php?title=Vers%C3%B5es_CRMPARTS&redirect=no"
                ,true
        );
    }

}
