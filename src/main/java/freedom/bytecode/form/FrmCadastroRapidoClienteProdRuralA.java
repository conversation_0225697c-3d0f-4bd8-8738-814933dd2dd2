package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmCadastroRapidoClienteProdRuralW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.FRLogger;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

import static freedom.util.FormatChange.formataCpfCnpj;

public class FrmCadastroRapidoClienteProdRuralA extends FrmCadastroRapidoClienteProdRuralW {

    private static final long serialVersionUID = 20130827081850L;

    public static final String INFORMACAO = "Informação";

    public static final String O_CAMPO = "O campo \"";

    public static final String DEVE_SER_PREENCHIDO = "\" deve ser preenchido.";

    @Getter
    private boolean ok;

    private String ufInscricao;

    private String inscricao;

    private boolean ehPessoaFisica = true;

    public String getUfInscricao() {
        String inscricaoEstadual = this.edtInscricaoEstadual.getValue().asString().trim();
        if (inscricaoEstadual.isEmpty()) {
            return "";
        }
        return this.edtUF.getValue().asString();
    }

    public String getInscricao() {
        return this.edtInscricaoEstadual.getValue().asString();
    }

    @Override
    public void cboProdutorRuralChange(final Event<Object> event) {
        String produtorRural = this.cboProdutorRural.getValue().asString();
        if (produtorRural.equals("S")) {
            this.habilitaCamposInscricao();
            this.edtInscricaoEstadual.setFocus();
        } else {
            this.edtInscricaoEstadual.setValue("");
            String uf = this.edtUF.getValue().asString();
            if (!uf.isEmpty()) {
                this.ufInscricao = this.edtUF.getValue().asString();
            }
            this.edtUF.setValue("");
            this.edtInscricaoEstadual.setEnabled(false);
            this.edtUF.setEnabled(false);
        }
    }

    @Override
    public void edtUFChange(Event<Object> event) {
        this.edtInscricaoEstadual.setFocus();
    }

    private void habilitaCamposInscricao() {
        try {
            this.rn.abreTabelas();
            if (this.tbUf.locate("UF",
                    this.ufInscricao)) {
                String uf = this.tbUf.getUF().asString();
                this.edtUF.setValue(uf);
            }
            this.edtUF.setEnabled(true);
            this.edtInscricaoEstadual.setEnabled(true);
            if (StringUtils.isNotBlank(this.inscricao)) {
                this.edtInscricaoEstadual.setValue(this.inscricao);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao habilitar campos inscrição",
                    dataException);
        }
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        this.close();
    }

    private String padronizarInscricaoEstadual(String uf,
                                               String inscricaoEstadual) {
        String retFuncao = "";
        try {
            retFuncao = this.rn.padronizarInscricaoEstadual(uf,
                    inscricaoEstadual);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao padronizar a inscrição estadual",
                    dataException);
        }
        return retFuncao;
    }

    @Override
    public void btnConfirmarClick(final Event<Object> event) {
        String produtorRural = this.cboProdutorRural.getValue().asString();
        if (produtorRural.equals("S")) {
            String inscricaoEstadual = this.edtInscricaoEstadual.getValue().asString().trim();
            if (inscricaoEstadual.isEmpty()) {
                String mensagem = FrmCadastroRapidoClienteProdRuralA.O_CAMPO
                        + this.lblInscricaoEstadual.getCaption()
                        + FrmCadastroRapidoClienteProdRuralA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .title(FrmCadastroRapidoClienteProdRuralA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(event1 -> FreedomUtilities.invokeLater(() -> this.edtInscricaoEstadual.setFocus()));
                return;
            }
            String uf = this.edtUF.getValue().asString().trim();
            if (uf.isEmpty()) {
                String mensagem = FrmCadastroRapidoClienteProdRuralA.O_CAMPO
                        + this.lblUF.getCaption()
                        + "\" deve ser preenchido para consultar dados Sintegra quando selecionado produtor rural.";
                Dialog.create()
                        .title(FrmCadastroRapidoClienteProdRuralA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(event1 -> FreedomUtilities.invokeLater(() -> this.edtUF.setFocus()));
                return;
            }
            if ((this.ehPessoaFisica)
                    && (this.edtDataNasc.getValue().isNull())) {
                String mensagem = FrmCadastroRapidoClienteProdRuralA.O_CAMPO
                        + this.lblDataNascimento.getCaption()
                        + FrmCadastroRapidoClienteProdRuralA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .title(FrmCadastroRapidoClienteProdRuralA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(event1 -> FreedomUtilities.invokeLater(() -> this.edtDataNasc.setFocus()));
                return;
            }
            String ie = this.padronizarInscricaoEstadual(this.edtUF.getValue().asString().trim(),
                    this.edtInscricaoEstadual.getValue().asString().trim());
            this.edtInscricaoEstadual.setValue(ie);
            inscricaoEstadual = this.edtInscricaoEstadual.getValue().asString().trim();
            if (inscricaoEstadual.isEmpty()) {
                String mensagem = FrmCadastroRapidoClienteProdRuralA.O_CAMPO
                        + this.lblInscricaoEstadual.getCaption()
                        + FrmCadastroRapidoClienteProdRuralA.DEVE_SER_PREENCHIDO;
                Dialog.create()
                        .title(FrmCadastroRapidoClienteProdRuralA.INFORMACAO)
                        .message(mensagem)
                        .showInformation(event1 -> FreedomUtilities.invokeLater(() -> this.edtInscricaoEstadual.setFocus()));
                return;
            }
        } else if (this.edtDataNasc.getValue().isNull()) {
            String mensagem = FrmCadastroRapidoClienteProdRuralA.O_CAMPO
                    + this.lblDataNascimento.getCaption()
                    + FrmCadastroRapidoClienteProdRuralA.DEVE_SER_PREENCHIDO;
            Dialog.create()
                    .title(FrmCadastroRapidoClienteProdRuralA.INFORMACAO)
                    .message(mensagem)
                    .showInformation(event1 -> FreedomUtilities.invokeLater(() -> this.edtDataNasc.setFocus()));
            return;
        }
        this.ok = true;
        this.close();
    }

    @Override
    public void edtUFExit(final Event<Object> event) {
        String uf = this.edtUF.getValue().asString().trim();
        if (uf.isEmpty()) {
            this.edtUF.setText("");
        }
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.ehPessoaFisica = (cpfCnpj.length() == 11);
        this.edtCPFCNPJ.setValue(formataCpfCnpj(cpfCnpj));
        this.edtDataNasc.setVisible(this.ehPessoaFisica);
        this.lblDataNascimento.setVisible(this.ehPessoaFisica);
    }

    private void abreTabelas() {
        try {
            this.rn.abreTabelas();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao abrir tabelas",
                    dataException);
        }
    }

    public void setUFInscricao(String ufInscricao) {
        this.ufInscricao = ufInscricao;
        this.abreTabelas();
    }

    public void setUFInscricao(String ufInscricao,
                               String inscricao) {
        this.ufInscricao = ufInscricao;
        this.inscricao = inscricao;
        if (StringUtils.isNotBlank(this.inscricao)) {
            this.cboProdutorRural.setValue("S");
            this.habilitaCamposInscricao();
        }
    }

    public void setDataNascimento(Date dataNascimento) {
        if (dataNascimento != null) {
            this.edtDataNasc.setValue(dataNascimento);
        }
    }

    public void setUFCadEnderecoPorInscricao(String ufInscricao,
                                             String inscricao) {
        this.ufInscricao = ufInscricao;
        this.inscricao = inscricao;
        this.habilitaCamposInscricao();
        this.cboProdutorRural.setValue("S");
        this.cboProdutorRural.setEnabled(false);
        this.setCaption("Cadastro de Endereço por Inscrição");
        if (!this.ehPessoaFisica){
           this.lblProdutorRural.setCaption("Adicionar nova Inscrição?");
        } else {
           edtDataNasc.setEnabled(false);
        }
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        FRLogger.log("Formulário: " + this.getName(),
                this.getClass());
        FreedomUtilities.invokeLater(() -> this.edtInscricaoEstadual.setFocus());
    }

}
