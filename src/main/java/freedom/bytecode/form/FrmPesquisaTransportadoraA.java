package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmPesquisaTransportadoraW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.StringUtil;
import lombok.Getter;

@Getter
public class FrmPesquisaTransportadoraA extends FrmPesquisaTransportadoraW {

    private static final long serialVersionUID = 20130827081850L;

    @Getter
    private boolean fechadaAoAceitar = false;

    @Override
    public void edtDescricaoEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void edtCNPJCPFEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void edtCodigoEnter(Event<Object> event) {
        this.btnPesquisarClick(event);
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

    private void carregarGridTransportadora(
            long codTransportadora
            ,String descricaoTransportadora
            ,String cNPJCPF
    ) {
        try {
            this.rn.carregarGridTransportadora(
                    codTransportadora
                    ,descricaoTransportadora
                    ,cNPJCPF
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de transportadoras"
                    ,dataException
            );
        }
    }

    @Override
    public void btnPesquisarClick(Event<Object> event) {
        String descricao = this.edtDescricao.getValue().asString().trim();
        String cNPJCPF = this.edtCNPJCPF.getValue().asString().trim();
        long codigo = this.edtCodigo.getValue().asLong();
        this.carregarGridTransportadora(
                codigo
                ,descricao
                ,cNPJCPF
        );
    }

    @Override
    public void btnAceitarClick(Event<Object> event) {
        boolean tbTransportadorasEmpty = this.tbTransportadoras.isEmpty();
        if (tbTransportadorasEmpty) {
            EmpresaUtil.showInformationMessage("Selecione algum registro.");
            return;
        }
        this.fechadaAoAceitar = true;
        this.close();
    }

    @Override
    public void edtCNPJCPFChange(Event<Object> event) {
        String cNPJCPF = this.edtCNPJCPF.getValue().asString().trim();
        cNPJCPF = StringUtil.removerCaracteresNaoNumericos(cNPJCPF);
        this.edtCNPJCPF.setValue(cNPJCPF);
    }

    @Override
    public void edtCodigoChange(Event<Object> event) {
        String codigo = this.edtCodigo.getValue().asString().trim();
        codigo = StringUtil.removerCaracteresNaoNumericos(codigo);
        this.edtCodigo.setValue(codigo);
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.edtDescricao.setFocus();
    }

}
