package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.data.DataException;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class FrmSelecionarEmpresasUsuarioA extends FrmSelecionarEmpresasUsuarioW {

    private static final long serialVersionUID = 20130827081850L;

    @Getter
    private boolean fechadoAoaceitar = false;

    private final List<Long> listaDeCodigosDeEmpresasParaSelecionarAutomaticamente;

    public FrmSelecionarEmpresasUsuarioA(
            List<Long> listaDeCodigosDeEmpresasParaSelecionarAutomaticamente
    ) {
        this.adicionarEventoOnClickAColunaSELDaGrdEmpresas();
        this.listaDeCodigosDeEmpresasParaSelecionarAutomaticamente = listaDeCodigosDeEmpresasParaSelecionarAutomaticamente;
    }

    private void carregarGrdEmpresas(
            String usuario
            ,long codEmpresaUsuario
    ) {
        try {
            this.rn.carregarGrdEmpresas(
                    usuario
                    ,codEmpresaUsuario
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar as empresas"
                    ,dataException
            );
        }

    }

    @Override
    public void FFormCreate(Event<Object> event) {
        String usuario = EmpresaUtil.getUserLogged();
        long codEmpresaUsuario = EmpresaUtil.getCodEmpresaUserLogged();
        this.carregarGrdEmpresas(
                usuario
                ,codEmpresaUsuario
        );
        if (this.listaDeCodigosDeEmpresasParaSelecionarAutomaticamente != null) {
            this.selecionarEmpresasNaGrdEmpresas(
                    this.listaDeCodigosDeEmpresasParaSelecionarAutomaticamente
            );
        }
    }

    private void selecionarEmpresasNaGrdEmpresas(
            List<Long> listaDeCodigosDeEmpresas
    ) {
        try {
            this.rn.selecionarEmpresasNaGrdEmpresas(
                    listaDeCodigosDeEmpresas
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao selecionar as empresas na grade"
                    ,dataException
            );
        }
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

    @Override
    public void btnAceitarClick(Event<Object> event) {
        List<Long> listaDeCodigosDeEmpresasSelecionadas = this.getListaDeCodigosDeEmpresasSelecionadas();
        if (listaDeCodigosDeEmpresasSelecionadas.isEmpty()) {
            Dialog.create()
                    .showNotificationInfo(
                            "Selecione alguma empresa"
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.grdEmpresas         // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            return;
        }
        this.fechadoAoaceitar = true;
        this.close();
    }

    public List<Long> getListaDeCodigosDeEmpresasSelecionadas() {
        List<Long> retFuncao = new ArrayList<>();
        try {
            retFuncao = this.rn.getListaDeCodigosDeEmpresasSelecionadas();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao obter a lista com os códigos das empresas selecionadas"
                    ,dataException
            );
        }
        return retFuncao;
    }

    private void marcarDesmarcarGrdEmpresas() {
        try {
            this.rn.marcarDesmarcarGrdEmpresas();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao marcar/desmarcar a grade de empresas"
                    ,dataException
            );
        }
    }

    public void grdEmpresasSelClick() {
        this.marcarDesmarcarGrdEmpresas();
    }

    private void adicionarEventoOnClickAColunaSELDaGrdEmpresas() {
        this.grdEmpresas.getColumns().forEach(coluna -> {
            if (coluna.getFieldName().equals("SEL")) {
                coluna.addEventListener(
                        "onClick"
                        ,(Event<Object> event) -> {
                            this.grdEmpresasSelClick();
                            processarFlow(
                                    "FrmSelecionarEmpresasUsuario"
                                    ,"SEL"
                                    ,"OnClick"
                            );
                        });
            }
        });
    }

    private void definirValorColunaSelTodosRegistrosGrdEmpresas(char valorSN) {
        try {
            long codEmpresaComFoco = this.tbEmpresas.getCOD_EMPRESA().asLong();
            this.tbEmpresas.disableControls();
            try {
                this.tbEmpresas.first();
                while (Boolean.FALSE.equals(this.tbEmpresas.eof())) {
                    this.tbEmpresas.edit();
                    if (valorSN == 'S') {
                        this.tbEmpresas.setSEL("S");
                    } else {
                        this.tbEmpresas.setSEL("N");
                    }
                    this.tbEmpresas.post();
                    this.tbEmpresas.next();
                }
                this.tbEmpresas.first();
                while (Boolean.FALSE.equals(this.tbEmpresas.eof())) {
                    long codEmpresaLoop = this.tbEmpresas.getEMPRESA().asLong();
                    if (codEmpresaComFoco == codEmpresaLoop) {
                        break;
                    }
                    this.tbEmpresas.next();
                }
            } finally {
                this.tbEmpresas.enableControls();
            }
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao definir o valor da coluna SEL para todos os registros da grade de empresas"
                    ,dataException
            );
        }
    }

    @Override
    public void mmSelecionarTodosOsRegistrosGrdEmpresasClick(Event<Object> event) {
        this.definirValorColunaSelTodosRegistrosGrdEmpresas('S');
    }

    @Override
    public void mmSelecionarNenhumRegistroGrdEmpresasClick(Event<Object> event) {
        this.definirValorColunaSelTodosRegistrosGrdEmpresas('N');
    }

}
