package freedom.bytecode.form;

import freedom.bytecode.cursor.OS;
import freedom.bytecode.form.wizard.FrmAssinaturaOsW;
import freedom.bytecode.rn.enun.EnTipoAbertura;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.event.UploadEvent;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.FreedomUtilities;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.*;

import java.io.IOException;

public class FrmAssinaturaOsA extends FrmAssinaturaOsW {

    private static final long serialVersionUID = 20130827081850L;

    private boolean ok = false;
    private boolean cancel = false;
    private byte[] imageBytes;
    private int tipoAcaoFrota;
    private String strEmail;
    private String strDDDCel;
    private String strCelular;
    boolean assinaturaObrigatoria = true;
    private String tipoEnvio = "F";
    private String emailFone = "";
    private EnTipoAbertura tipoAbertura;

    public boolean isOk() {
        return ok;
    }

    public boolean isCancel() {
        return cancel;
    }

    public boolean continuarOs(Double codEmpresa, Double numeroOs, Double codOsAgenda, int tipoAcaoFrota, boolean avisoLgpd,
                               EnTipoAbertura tipoAbertura) {
        this.tipoAbertura = tipoAbertura;
        if (avisoLgpd) {
            abreCienciaLgpd(codEmpresa, numeroOs);
        }
        boolean ret = true;
        this.tipoAcaoFrota = tipoAcaoFrota;
        this.setScrollable(true);
        try {
            rn.continuarOs(codEmpresa, numeroOs, codOsAgenda);
            hBoxWhatsApp.setVisible(rn.leadZapSinc(codEmpresa));
            strEmail = tbAgenda.getEMAIL().asString();
            strDDDCel = tbAgenda.getCLIENTE_DDD_CEL().asString().trim();
            strCelular = tbAgenda.getCLIENTE_FONE_CEL().asString().trim();
            edtComprovanteEmail.setValue(strEmail);

            if (strDDDCel.length() > 2) {
                strDDDCel = strDDDCel.substring(0, 2);
            }

            edtDDDCel.setValue(strDDDCel);
            if (strCelular.length() > 9) {
                strCelular = strCelular.substring(0, 9);
            }
            edtNumeroCelular.setValue(strCelular);
        } catch (DataException e) {
            CrmServiceUtil.showError("Falha ao iniciar Os Agenda", e);
            ret = false;
        }
        return ret;
    }

    public void setBooGerarNfCompra(boolean booGerarNfCompra) {
        rn.setBooGerarNfCompra(booGerarNfCompra);
    }

    @Override
    public void btnSalvarOsClick(final Event event) {
        signature.save();
    }

    private void validarEquipeCheckList() {
        try {
            if (!rn.checkExistEquipeChecklist()) {
                CrmServiceUtil.showWarning("Time que atende o checklist para eventos futuros não encontrado. Não será gerado eventos futuros. Parâmetro [TIME_ATENDE_CHECKLIST]");
            }
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao consultar equipe checklist", ex);
        }
    }

    public String continuarGeracaoOs(boolean testarGeracao, String acaoPecasDisp, String acaoPecasIndisp, int tipoAcaoFrota,
                                     EnTipoAbertura tipoAbertura) throws Exception {
        this.tipoAbertura = tipoAbertura;
        Value campoFoco = new Value(null);
        Value mensagemFoco = new Value(null);
        Value numeroOs = new Value(null);
        Value osGeradas = new Value(null);
        Value osGeradasNum = new Value(null);
        Value tipoOsOrc = new Value(null);

        lblValidarDDD.setVisible(false);
        lblValidarCelular.setVisible(false);
        if (rbEnviarComprovante.isChecked()) {
            tipoEnvio = "F";
            emailFone = tbAgenda.getEMAIL().asString().trim();
        } else if (rbNaoEnviar.isChecked()) {
            tipoEnvio = "N";
        } else if (rbOutroEmail.isChecked()) {
            tipoEnvio = "O";
            emailFone = edtEmailInf.getValue().asString().trim();
        } else if (rbWhatsApp.isChecked()) {
            tipoEnvio = "W";
            emailFone = edtDDDCel.getValue().asString().trim() + edtNumeroCelular.getValue().asString().trim();
        }

        String retFunc = validarEnvido(tipoEnvio, emailFone);
        if (!retFunc.equals("S")) {
            return retFunc;
        }

        if ((edtDDDCel.getValue().asString().length() > 0) && (edtNumeroCelular.getValue().asString().length() > 0)) {
            tbClientes.edit();
            tbClientes.setPREFIXO_CEL(edtDDDCel.getValue().asString());
            tbClientes.setTELEFONE_CEL(edtNumeroCelular.getValue().asString());
        }

        if (edtComprovanteEmail.getValue().asString().length() > 0) {
            tbClientes.edit();
            tbClientes.setEMAIL2(edtComprovanteEmail.getValue().asString());
            tbClientes.setENDERECO_ELETRONICO(edtComprovanteEmail.getValue().asString());
//            Solicitado remoção update email cliente Ev: 1369958
//        } else if (edtEmailInf.getValue().asString().length() > 0) {
//            tbClientes.edit();
//            tbClientes.setEMAIL2(edtEmailInf.getValue().asString());
//            tbClientes.setENDERECO_ELETRONICO(edtEmailInf.getValue().asString());
        }

        boolean isOrcamento = rn.getIsOrcamento();
        if ((rn.gerarNfcompra()) && (testarGeracao) && (!isOrcamento)) {
            FrmAberturaOsCompraA frmCompra = new FrmAberturaOsCompraA();
            if (frmCompra.continuarOs(rn.getCodEmpresa(), rn.getNumeroOs(), rn.getCodOsAgenda())) {
                FormUtil.doShow(frmCompra, (EventListener) t -> {
                    if (frmCompra.isOk()) {
                        TableUtil.copyCurrentRecord(frmCompra.tbCompra, tbCompra, true);

                        String retFuncao = gerarOsAgenda(testarGeracao, acaoPecasDisp, acaoPecasIndisp, tipoAcaoFrota, campoFoco, mensagemFoco,
                                numeroOs, osGeradas, tipoOsOrc, tipoEnvio, emailFone, tipoAbertura, osGeradasNum);

                        salvarAgenda(retFuncao);
                        ok = true;
                    } else if (frmCompra.isCancel()) {
                        ok = false;
                        cancel = true;
                    }
                });
            }
        } else {
            String retFuncao = gerarOsAgenda(testarGeracao, acaoPecasDisp, acaoPecasIndisp, tipoAcaoFrota, campoFoco, mensagemFoco,
                    numeroOs, osGeradas, tipoOsOrc, tipoEnvio, emailFone, tipoAbertura, osGeradasNum);
            return retFuncao;
        }
        return "FAZER_NADA";
    }

    private String gerarOsAgenda(boolean testarGeracao, String acaoPecasDisp, String acaoPecasIndisp, int tipoAcaoFrota,
                                 Value campoFoco, Value mensagemFoco, Value numeroOs, Value osGeradas, Value tipoOsOrc,
                                 String tipoEnvio, String emailFone, EnTipoAbertura tipoAbertura, Value osGeradasNum) throws Exception {
        String retFuncao = rn.gerarOsAgenda(testarGeracao, acaoPecasDisp, acaoPecasIndisp,
                numeroOs, osGeradas, campoFoco, tipoOsOrc, tipoEnvio, emailFone, tipoAcaoFrota, tipoAbertura, osGeradasNum);
        mensagemFoco.setValue(retFuncao);
        if (!retFuncao.equals("S")) {
            FreedomUtilities.invokeLater(() -> {
                CrmServiceUtil.showWarning(mensagemFoco.asString());
            });
        } else {
            String[] aux = osGeradas.asString().split(";");
            String osAux = "";
            String strBtnOs = "";
            int addLinha = 0;
            for (String s : aux) {
                osAux += "   " + s + "\n";
                addLinha += 15;
            }

            if (!testarGeracao) {

                String titulo = "Abertura de O.S Realizada com Sucesso!\n\n"
                        + "O.S / ID Aberta(s):\n\n"
                        + osAux;
                strBtnOs = "Abrir O.S " + numeroOs.asInteger();
                if (tipoOsOrc.asString().equals("ORC")) {
                    titulo = "Abertura de Orçamento Realizada com Sucesso!\n\n"
                            + "Orçamento Aberto(s):\n\n"
                            + osAux;
                    strBtnOs = "Abrir ORC " + Math.abs(numeroOs.asInteger());
                }

                //Vai fazer check se tem arquivo em disco e demais tramitacoes
                CrmServiceDocumento doc = new CrmServiceDocumento();
                doc.efetivaArquivosOsTemp(rn.getCodEmpresa(), osGeradasNum);

//                CrmServiceUtil.showMessage(titulo);
                FrmDialogA frm = new FrmDialogA("CrmService", titulo, strBtnOs, "OK");
                frm.setHeight(220 + addLinha);
                frm.hboxMessage.setHeight(140 + addLinha);
                frm.FButton1.setWidth(155);
                FormUtil.doShow(frm, (EventListener) t -> {
                    if (frm.getRet() == 1) {
                        CrmServiceUtil.abrirConsultaOsOrc(rn.getCodEmpresa(), numeroOs.asDecimal());
                    }
                });
                ok = true;
                close();
            }
        }
        return retFuncao;
    }

    private String validarEnvido(String tipoEnvio, String emailFone) {
        switch (tipoEnvio) {
            case "O":
                if (emailFone.trim().equals("")) {
                    CrmServiceUtil.showWarning("Informe um e-mail antes de enviar");
                    FreedomUtilities.invokeLater(() -> {
                        edtEmailInf.setFocus();
                    });
                    return "FAZER_NADA";
                }
                break;
            case "F":
                if (emailFone.trim().equals("")) {
                    CrmServiceUtil.showWarning("Informe um e-mail antes de enviar");
                    rbOutroEmail.setChecked(true);
                    FreedomUtilities.invokeLater(() -> {
                        edtEmailInf.setFocus();
                    });
                    return "FAZER_NADA";
                }
                break;
            case "W":
                if (edtDDDCel.equals("")) {
                    CrmServiceUtil.showWarning("Informe o DDD do Numero antes de Enviar");
                    lblValidarDDD.setVisible(true);
                    if (edtNumeroCelular.equals("")) {
                        lblValidarCelular.setVisible(true);
                    }
                    if (edtNumeroCelular.getValue().asString().length() < 8) {
                        lblValidarCelular.setVisible(true);
                    }
                    rbWhatsApp.setChecked(true);
                    FreedomUtilities.invokeLater(() -> {
                        edtDDDCel.setFocus();
                    });
                    return "FAZER_NADA";
                } else {
                    if (edtDDDCel.getValue().asString().length() < 2) {
                        CrmServiceUtil.showWarning("DDD Inválido. Necessário 2 dígitos.");
                        lblValidarDDD.setVisible(true);
                        if (edtNumeroCelular.equals("")) {
                            lblValidarCelular.setVisible(true);
                        }
                        if (edtNumeroCelular.getValue().asString().length() < 8) {
                            lblValidarCelular.setVisible(true);
                        }
                        rbWhatsApp.setChecked(true);
                        FreedomUtilities.invokeLater(() -> {
                            edtDDDCel.setFocus();
                        });
                        return "FAZER_NADA";
                    }
                }
                if (edtNumeroCelular.equals("")) {
                    CrmServiceUtil.showWarning("Informe o Numero antes de Enviar");
                    lblValidarCelular.setVisible(true);
                    rbWhatsApp.setChecked(true);
                    if (edtDDDCel.equals("")) {
                        lblValidarDDD.setVisible(true);
                    }
                    if (edtDDDCel.getValue().asString().length() < 2) {
                        lblValidarDDD.setVisible(true);
                    }
                    FreedomUtilities.invokeLater(() -> {
                        edtNumeroCelular.setFocus();
                    });
                    return "FAZER_NADA";
                } else {
                    if (edtNumeroCelular.getValue().asString().length() < 8) {
                        CrmServiceUtil.showWarning("Telefone Inválido (menor que 8 dígitos)");
                        lblValidarCelular.setVisible(true);
                        if (edtDDDCel.equals("")) {
                            lblValidarDDD.setVisible(true);
                        }
                        if (edtDDDCel.getValue().asString().length() < 2) {
                            lblValidarDDD.setVisible(true);
                        }
                        rbWhatsApp.setChecked(true);
                        FreedomUtilities.invokeLater(() -> {
                            edtNumeroCelular.setFocus();
                        });
                        return "FAZER_NADA";
                    }
                }
                break;
            default:
                break;
        }
        return "S";
    }

    public void salvarAgenda(String acaoExterna) {
        String acaoPecasDisp = "FAZER_NADA";
        String acaoPecasIndisp = "FAZER_NADA";
        String retFuncao;
        try {
            if (acaoExterna == null) {
                retFuncao = continuarGeracaoOs(true, acaoPecasDisp, acaoPecasIndisp, tipoAcaoFrota, tipoAbertura);
            } else {
                retFuncao = acaoExterna;
            }
            if (retFuncao.equals("S")) {
                FrmAcaoAberturaOsA form = new FrmAcaoAberturaOsA();
                Double codEmpresa = tbOs.getCOD_EMPRESA().asDecimal();
                Double nrOs = tbOs.getNUMERO_OS().asDecimal();
                if (tbOs.getORCAMENTO().asString().equals("S")) {
                    form.setCaption("Ações Abertura Orc");
                }
                if (form.validarAcoes(codEmpresa, nrOs)) {
                    FormUtil.doShow(form, (EventListener) t -> {
                        String acaoPecasEstoque = "FAZER_NADA";
                        if (form.isReservar()) {
                            acaoPecasEstoque = "RESERVAR_ITENS";
                        } else if (form.isRequisitar()) {
                            acaoPecasEstoque = "REQUISITAR_ITENS";
                        }
                        String acaoPecasSemEstoque = "FAZER_NADA";
                        if (form.isGerarVendaPendente()) {
                            acaoPecasSemEstoque = "GERAR_VENDA_PENDENTE";
                        }
                        if (form.isReservaEstoqueFilial()) {
                            acaoPecasSemEstoque = "ESTOQUE_FILIAL";
                        }
                        if (form.isOk()) {
                            continuarGeracaoOs(false, acaoPecasEstoque, acaoPecasSemEstoque, tipoAcaoFrota, tipoAbertura);
                        }
                    });
                } else {
                    continuarGeracaoOs(false, acaoPecasDisp, acaoPecasIndisp, tipoAcaoFrota, tipoAbertura);
                }
            }
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao gerar os Agenda", e);
        }
    }

    private void assinarOs() {
        try {
            String retFuncao = rn.osSessaoExpirou();
            if (!retFuncao.equals("N")) {
                CrmServiceUtil.showWarning(retFuncao);
                cancel = true;
                close();
                return;
            }

            if (tbOs.getORCAMENTO().asString().equals("S")) {
                if (!ImgUtil.isImageValid(imageBytes)) {
                    Dialog.create()
                            .title("Atenção")
                            .message("Deseja Prosseguir com Abertura de Orçamento Sem Assinatura?")
                            .confirmSimNao((String dialogResult) -> {
                                if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                                    validarEquipeCheckList();
                                    salvarAgenda(null);
                                }
                            });
                } else {
                    validarEquipeCheckList();
                    rn.salvarAssinaturaCliente(imageBytes);
                    salvarAgenda(null);
                }
            } else {
                if (!ImgUtil.isImageValid(imageBytes)) {
                    CrmServiceUtil.showMessage("Assinatura do cliente obrigatória.");
                    return;
                }

                validarEquipeCheckList();
                rn.salvarAssinaturaCliente(imageBytes);
                salvarAgenda(null);
            }

        } catch (DataException | IOException | InterruptedException ex) {
            CrmServiceUtil.showError("Falha ao gerar os Agenda", ex);
        }
    }

    @Override
    public void signatureSave(UploadEvent event) {
        try {
            imageBytes = ImageUtil.streamToByteArray(event.getStreamData());
            assinarOs();
        } catch (Exception ex) {
            CrmServiceUtil.showError("Falha ao gerar assinatura", ex);
        }
    }

    @Override
    public void signatureClear(Event<Object> event) {
        imageBytes = null;
    }

    @Override
    public void btnDesfazerClick(final Event event) {
        signature.undo();
    }

    @Override
    public void btnLimparClick(final Event event) {
        signature.clear();
    }

    @Override
    public void btnVoltarClick(final Event event) {
        close();
    }

    @Override
    public void rbWhatsAppCheck(Event<Object> event) {
        edtDDDCel.setEnabled(rbWhatsApp.isChecked());
        edtNumeroCelular.setEnabled(rbWhatsApp.isChecked());
        edtComprovanteEmail.clear();
        edtComprovanteEmail.setValue(strEmail);
        edtDDDCel.setValue(strDDDCel);
        edtNumeroCelular.setValue(strCelular);
        edtEmailInf.clear();
        edtEmailInf.setEnabled(false);
        edtComprovanteEmail.setEnabled(false);
        FreedomUtilities.invokeLater(() -> {
            edtDDDCel.setFocus();
        });
    }

    @Override
    public void rbEnviarComprovanteCheck(Event<Object> event) {
        edtComprovanteEmail.setEnabled(rbEnviarComprovante.isChecked());
        edtEmailInf.clear();
        edtDDDCel.clear();
        edtNumeroCelular.clear();
        edtEmailInf.setEnabled(false);
        edtDDDCel.setEnabled(false);
        edtNumeroCelular.setEnabled(false);
        edtComprovanteEmail.setValue(strEmail);
        FreedomUtilities.invokeLater(() -> {
            edtComprovanteEmail.setFocus();
        });
    }

    @Override
    public void rbOutroEmailCheck(Event<Object> event) {
        edtEmailInf.setEnabled(rbOutroEmail.isChecked());
        edtComprovanteEmail.clear();
        edtDDDCel.clear();
        edtNumeroCelular.clear();
        edtComprovanteEmail.setEnabled(false);
        edtDDDCel.setEnabled(false);
        edtNumeroCelular.setEnabled(false);
        FreedomUtilities.invokeLater(() -> {
            edtEmailInf.setFocus();
        });
    }

    @Override
    public void edtDDDCelChanging(Event<Object> event) {
        if (edtDDDCel.getValue().asString().length() == 2) {
            FreedomUtilities.invokeLater(() -> {
                edtNumeroCelular.setFocus();
            });
        }
    }

    @Override
    public void edtDDDCelChange(Event<Object> event) {
        if (edtDDDCel.getValue().asString().length() == 2) {
            FreedomUtilities.invokeLater(() -> {
                edtNumeroCelular.setFocus();
            });
        }
    }

    @Override
    public void rbNaoEnviarCheck(Event<Object> event) {
        edtComprovanteEmail.clear();
        edtEmailInf.clear();
        edtDDDCel.clear();
        edtNumeroCelular.clear();
        edtComprovanteEmail.setValue(strEmail);
        edtDDDCel.setValue(strDDDCel);
        edtNumeroCelular.setValue(strCelular);
        edtComprovanteEmail.setEnabled(false);
        edtEmailInf.setEnabled(false);
        edtDDDCel.setEnabled(false);
        edtNumeroCelular.setEnabled(false);
    }

    public void abreCienciaLgpd(Double codEmpresa, Double numeroOs) {
        FrmCienciaLgpdA frm = new FrmCienciaLgpdA();
        Double codCliente;
        try {
            codCliente = getCodCliente(codEmpresa, numeroOs);
            frm.carregaDados(codEmpresa, codCliente);
        if (frm.isOk()) {
            FormUtil.doShow(frm, t -> {
            });
        }
        } catch (DataException e) {
            CrmServiceUtil.showError("Erro ao abrir termo LGPD", e);
        }
    }

    private Double getCodCliente(double codEmpresa, double numeroOs) throws DataException {
        OS tbOsTemp = new OS("tbOsTemp");
        tbOsTemp.clearFilters();
        tbOsTemp.clearParams();
        tbOsTemp.addFilter("COD_EMPRESA");
        tbOsTemp.setFilterCOD_EMPRESA(codEmpresa);
        tbOsTemp.setFilterNUMERO_OS(numeroOs);
        tbOsTemp.open();
        return tbOsTemp.getCOD_CLIENTE().asDecimal();
    }
}
