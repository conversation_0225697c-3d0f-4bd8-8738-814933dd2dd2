package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmClientesProfissaoA extends FrmClientesProfissaoW {

    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;

    public boolean isOk() {
        return ok;
    }

    @Override
    public void btnVoltarClick(final Event event) {
        ok = false;
        close();
    }

    @Override
    public void btnPesquisarClick(final Event event) {
        pesquisarProfissao();
    }

    private void pesquisarProfissao() {
        try {
            String desc = edtDescricaoProfissao.getValue().asString().trim();

            if (desc.trim().equals("")) {
                EmpresaUtil.showWarning("Atenção", "Informe a descrição antes de pesquisar");
                return;
            }

            rn.filtrarClientesProfissao(desc);
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro ao pesquisar", ex);
        }
    }

    @Override
    public void btnAceitarClick(final Event event) {
        if (tbClientesProfissao.count() == 0) {
            ok = false;
            EmpresaUtil.showWarning("Atenção", "Selecione uma profissão");
        } else {
            ok = true;
            close();
        }
    }

    @Override
    public void edtDescricaoProfissaoEnter(Event<Object> event) {
        pesquisarProfissao();
    }
}
