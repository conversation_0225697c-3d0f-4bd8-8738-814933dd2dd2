package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmTemplatesCelularAprovarW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmTemplatesCelularAprovarA extends FrmTemplatesCelularAprovarW {

    private static final long serialVersionUID = 20130827081850L;
    private boolean ok = false;
    private int idCelular = 0;

    public FrmTemplatesCelularAprovarA() {
        try {
            rn.filtrarCelular();
        } catch (DataException e) {
            EmpresaUtil.showError("OPS! Ocorreu um erro inesperado", e);
        }
    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        if (tbCelularAtivosDisparos.count() == 0) {
            EmpresaUtil.showWarning("Atenção", "Selecione um celular antes de aceitar.");
            ok = false;
            return;
        }
        idCelular = tbCelularAtivosDisparos.getID_CELULAR().asInteger();
        ok = true;
        close();
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        ok = false;
        close();
    }

    public boolean isOk() {
        return ok;
    }

    public int getIdCelular() {
        return idCelular;
    }

    public void setIdCelular(int idCelular) {
        this.idCelular = idCelular;
    }
}
