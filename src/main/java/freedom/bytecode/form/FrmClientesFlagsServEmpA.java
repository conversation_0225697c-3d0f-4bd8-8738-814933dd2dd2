package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmClientesFlagsServEmpW;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;
import freedom.util.GridUtil;

public class FrmClientesFlagsServEmpA extends FrmClientesFlagsServEmpW {

    private static final long serialVersionUID = 20130827081850L;
    private String tabelaNome;
    private String campo;
    private String objeto;
    private Double codCliente;
    private String mode;
    private Value valor = new Value(null);

    public FrmClientesFlagsServEmpA() {
    }

    public void setMode(String Mode) {
        this.mode = Mode;
    }

    public void iniciarValores(String tabelaNome, String campo, String objeto, Double codCliente) {
        this.tabelaNome = tabelaNome;
        this.codCliente = codCliente;
        this.campo = campo;
        this.objeto = objeto;

        carregaDados();

        GridUtil.getColumnGrid(gridClienteFlagEmpresa, "VALOR").setVisible(false);
        if (campo.equals("")) {
            GridUtil.getColumnGrid(gridClienteFlagEmpresa, "VALOR_GRID").setVisible(false);
            hboxAltera.setVisible(false);
        } else {
            if (objeto.equals("B")) {
                chkValor.setVisible(true);
                edtValorDecimal.setVisible(false);
            } else {
                chkValor.setVisible(false);
                edtValorDecimal.setVisible(true);
                GridUtil.getColumnGrid(gridClienteFlagEmpresa, "VALOR_GRID").setTextAlign("taRight");
            }
        }

        //tela em display somente visualização dos dados
        if (mode.equals("DSP")) {
            GridUtil.getColumnGrid(gridClienteFlagEmpresa, " ").setVisible(false);
            hboxAltera.setVisible(false);
        }
    }

    private void carregaDados() {
        try {
            rn.carregaDados(tabelaNome, campo, objeto, codCliente, mode);
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados", ex);
        }
    }

    @Override
    public void gridClienteFlagEmpresaExcluirEmpresaGrid(Event<Object> event) {

        Dialog.create()
                .title("Excluir")
                .message("Confirma Excluir a Empresa Selecionado?")
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            rn.salvarAlteracoes("DLT", tabelaNome, campo, objeto,
                                    tbClienteflagempresa.getCOD_EMPRESA().asInteger(), codCliente,
                                    valor);
                            carregaDados();
                        } catch (DataException ex) {
                            EmpresaUtil.showError("Falha ao Excluir Empresa", ex);
                        }
                    }
                });
    }

    @Override
    public void gridClienteFlagEmpresaSelEmpresaClick(Event<Object> event) {
        FrmSelecionarEmpresaA frmEmpSel = new FrmSelecionarEmpresaA();

        if (frmEmpSel.podeSelecionarEmp(0, "Selecionar Empresa para Incluir")) {
            FormUtil.doShow(frmEmpSel, (EventListener) t -> {
                if (frmEmpSel.isOk()) {
                    int codEmp = frmEmpSel.tbLeadsEmpresasUsuarios.getCOD_EMPRESA().asInteger();

                    tbClienteflagempresa.first();
                    boolean existeEmpresa = false;
                    while (!tbClienteflagempresa.eof()) {
                        if (codEmp == tbClienteflagempresa.getCOD_EMPRESA().asInteger()) {
                            existeEmpresa = true;
                        }
                        tbClienteflagempresa.next();
                    }
                    if (existeEmpresa) {
                        EmpresaUtil.showMessage("Atenção", "Já existe empresa cadastrada para este cliente");
                    } else {
                        rn.salvarAlteracoes("INS", tabelaNome, campo, objeto,
                                codEmp, codCliente, valor);
                        carregaDados();
                    }
                    ;
                }
            });
        }
    }

    @Override
    public void btnAlterarClick(final Event event) {
        try {
            int codEmpSel = tbClienteflagempresa.getCOD_EMPRESA().asInteger();
            if (objeto.equals("B")) {
                valor = chkValor.getValue();
            } else {
                valor = edtValorDecimal.getValue();
            }
            rn.salvarAlteracoes("UPD", tabelaNome, campo, objeto,
                    tbClienteflagempresa.getCOD_EMPRESA().asInteger(), codCliente,
                    valor);
            carregaDados();
            tbClienteflagempresa.locate("COD_EMPRESA", codEmpSel);
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Alterar Valor Empresa", ex);
        }
    }

}
