package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAdicMargemMinimaMarkupCliente extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AdicMargemMinimaMarkupClienteRNA rn = null;

    public FrmAdicMargemMinimaMarkupCliente() {
        try {
            rn = (freedom.bytecode.rn.AdicMargemMinimaMarkupClienteRNA) getRN(freedom.bytecode.rn.wizard.AdicMargemMinimaMarkupClienteRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_vBoxPrincipal();
        init_FHBox1();
        init_hBoxLinha01();
        init_FHBox7();
        init_btnVoltar();
        init_FHBox6();
        init_btnSalvar();
        init_FHBox8();
        init_FHBox2();
        init_hBoxCampos();
        init_FHBox3();
        init_vBoxDescontoParaOCliente();
        init_lblMargemMinima();
        init_edtMargemMinima();
        init_FHBox5();
        init_FHBox4();
        init_FrmAdicMargemMinimaMarkupCliente();
    }

    protected TFForm FrmAdicMargemMinimaMarkupCliente = this;
    private void init_FrmAdicMargemMinimaMarkupCliente() {
        FrmAdicMargemMinimaMarkupCliente.setName("FrmAdicMargemMinimaMarkupCliente");
        FrmAdicMargemMinimaMarkupCliente.setCaption("Adicionar margem m\u00EDnima markup cliente");
        FrmAdicMargemMinimaMarkupCliente.setClientHeight(152);
        FrmAdicMargemMinimaMarkupCliente.setClientWidth(354);
        FrmAdicMargemMinimaMarkupCliente.setColor("clBtnFace");
        FrmAdicMargemMinimaMarkupCliente.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmAdicMargemMinimaMarkupCliente", "FrmAdicMargemMinimaMarkupCliente", "OnCreate");
        });
        FrmAdicMargemMinimaMarkupCliente.setWOrigem("EhMain");
        FrmAdicMargemMinimaMarkupCliente.setWKey("5300719");
        FrmAdicMargemMinimaMarkupCliente.setSpacing(0);
        FrmAdicMargemMinimaMarkupCliente.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(354);
        vBoxPrincipal.setHeight(152);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftFalse");
        vBoxPrincipal.setFlexHflex("ftFalse");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmAdicMargemMinimaMarkupCliente.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(340);
        FHBox1.setHeight(5);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox hBoxLinha01 = new TFHBox();

    private void init_hBoxLinha01() {
        hBoxLinha01.setName("hBoxLinha01");
        hBoxLinha01.setLeft(0);
        hBoxLinha01.setTop(6);
        hBoxLinha01.setWidth(340);
        hBoxLinha01.setHeight(63);
        hBoxLinha01.setBorderStyle("stNone");
        hBoxLinha01.setPaddingTop(0);
        hBoxLinha01.setPaddingLeft(0);
        hBoxLinha01.setPaddingRight(0);
        hBoxLinha01.setPaddingBottom(0);
        hBoxLinha01.setMarginTop(0);
        hBoxLinha01.setMarginLeft(0);
        hBoxLinha01.setMarginRight(0);
        hBoxLinha01.setMarginBottom(0);
        hBoxLinha01.setSpacing(1);
        hBoxLinha01.setFlexVflex("ftMin");
        hBoxLinha01.setFlexHflex("ftTrue");
        hBoxLinha01.setScrollable(false);
        hBoxLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha01.setBoxShadowConfigOpacity(75);
        hBoxLinha01.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha01);
        hBoxLinha01.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(5);
        FHBox7.setHeight(20);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(5);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(53);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmAdicMargemMinimaMarkupCliente", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(70);
        FHBox6.setTop(0);
        FHBox6.setWidth(5);
        FHBox6.setHeight(20);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(75);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmAdicMargemMinimaMarkupCliente", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(140);
        FHBox8.setTop(0);
        FHBox8.setWidth(5);
        FHBox8.setHeight(20);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(70);
        FHBox2.setWidth(340);
        FHBox2.setHeight(5);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox hBoxCampos = new TFHBox();

    private void init_hBoxCampos() {
        hBoxCampos.setName("hBoxCampos");
        hBoxCampos.setLeft(0);
        hBoxCampos.setTop(76);
        hBoxCampos.setWidth(340);
        hBoxCampos.setHeight(60);
        hBoxCampos.setBorderStyle("stNone");
        hBoxCampos.setPaddingTop(0);
        hBoxCampos.setPaddingLeft(0);
        hBoxCampos.setPaddingRight(0);
        hBoxCampos.setPaddingBottom(0);
        hBoxCampos.setMarginTop(0);
        hBoxCampos.setMarginLeft(0);
        hBoxCampos.setMarginRight(0);
        hBoxCampos.setMarginBottom(0);
        hBoxCampos.setSpacing(1);
        hBoxCampos.setFlexVflex("ftMin");
        hBoxCampos.setFlexHflex("ftTrue");
        hBoxCampos.setScrollable(false);
        hBoxCampos.setBoxShadowConfigHorizontalLength(10);
        hBoxCampos.setBoxShadowConfigVerticalLength(10);
        hBoxCampos.setBoxShadowConfigBlurRadius(5);
        hBoxCampos.setBoxShadowConfigSpreadRadius(0);
        hBoxCampos.setBoxShadowConfigShadowColor("clBlack");
        hBoxCampos.setBoxShadowConfigOpacity(75);
        hBoxCampos.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxCampos);
        hBoxCampos.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(5);
        FHBox3.setHeight(20);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        hBoxCampos.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFVBox vBoxDescontoParaOCliente = new TFVBox();

    private void init_vBoxDescontoParaOCliente() {
        vBoxDescontoParaOCliente.setName("vBoxDescontoParaOCliente");
        vBoxDescontoParaOCliente.setLeft(5);
        vBoxDescontoParaOCliente.setTop(0);
        vBoxDescontoParaOCliente.setWidth(150);
        vBoxDescontoParaOCliente.setHeight(50);
        vBoxDescontoParaOCliente.setBorderStyle("stNone");
        vBoxDescontoParaOCliente.setPaddingTop(0);
        vBoxDescontoParaOCliente.setPaddingLeft(0);
        vBoxDescontoParaOCliente.setPaddingRight(0);
        vBoxDescontoParaOCliente.setPaddingBottom(0);
        vBoxDescontoParaOCliente.setMarginTop(0);
        vBoxDescontoParaOCliente.setMarginLeft(0);
        vBoxDescontoParaOCliente.setMarginRight(0);
        vBoxDescontoParaOCliente.setMarginBottom(0);
        vBoxDescontoParaOCliente.setSpacing(1);
        vBoxDescontoParaOCliente.setFlexVflex("ftMin");
        vBoxDescontoParaOCliente.setFlexHflex("ftTrue");
        vBoxDescontoParaOCliente.setScrollable(false);
        vBoxDescontoParaOCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxDescontoParaOCliente.setBoxShadowConfigVerticalLength(10);
        vBoxDescontoParaOCliente.setBoxShadowConfigBlurRadius(5);
        vBoxDescontoParaOCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxDescontoParaOCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxDescontoParaOCliente.setBoxShadowConfigOpacity(75);
        hBoxCampos.addChildren(vBoxDescontoParaOCliente);
        vBoxDescontoParaOCliente.applyProperties();
    }

    public TFLabel lblMargemMinima = new TFLabel();

    private void init_lblMargemMinima() {
        lblMargemMinima.setName("lblMargemMinima");
        lblMargemMinima.setLeft(0);
        lblMargemMinima.setTop(0);
        lblMargemMinima.setWidth(92);
        lblMargemMinima.setHeight(13);
        lblMargemMinima.setCaption("Margem m\u00EDnima");
        lblMargemMinima.setFontColor("clWindowText");
        lblMargemMinima.setFontSize(-11);
        lblMargemMinima.setFontName("Tahoma");
        lblMargemMinima.setFontStyle("[fsBold]");
        lblMargemMinima.setVerticalAlignment("taVerticalCenter");
        lblMargemMinima.setWordBreak(false);
        vBoxDescontoParaOCliente.addChildren(lblMargemMinima);
        lblMargemMinima.applyProperties();
    }

    public TFDecimal edtMargemMinima = new TFDecimal();

    private void init_edtMargemMinima() {
        edtMargemMinima.setName("edtMargemMinima");
        edtMargemMinima.setLeft(0);
        edtMargemMinima.setTop(14);
        edtMargemMinima.setWidth(121);
        edtMargemMinima.setHeight(24);
        edtMargemMinima.setHint("Margem m\u00EDnima");
        edtMargemMinima.setHelpCaption("Margem m\u00EDnima");
        edtMargemMinima.setFlex(false);
        edtMargemMinima.setRequired(true);
        edtMargemMinima.setPrompt("Margem m\u00EDnima");
        edtMargemMinima.setConstraintCheckWhen("cwImmediate");
        edtMargemMinima.setConstraintCheckType("ctExpression");
        edtMargemMinima.setConstraintFocusOnError(false);
        edtMargemMinima.setConstraintEnableUI(true);
        edtMargemMinima.setConstraintEnabled(false);
        edtMargemMinima.setConstraintFormCheck(true);
        edtMargemMinima.setMaxlength(0);
        edtMargemMinima.setPrecision(0);
        edtMargemMinima.setFontColor("clWindowText");
        edtMargemMinima.setFontSize(-13);
        edtMargemMinima.setFontName("Tahoma");
        edtMargemMinima.setFontStyle("[]");
        edtMargemMinima.setAlignment("taRightJustify");
        edtMargemMinima.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtMargemMinimaEnter(event);
            processarFlow("FrmAdicMargemMinimaMarkupCliente", "edtMargemMinima", "OnEnter");
        });
        vBoxDescontoParaOCliente.addChildren(edtMargemMinima);
        edtMargemMinima.applyProperties();
        addValidatable(edtMargemMinima);
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(155);
        FHBox5.setTop(0);
        FHBox5.setWidth(5);
        FHBox5.setHeight(20);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        hBoxCampos.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(137);
        FHBox4.setWidth(340);
        FHBox4.setHeight(5);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtMargemMinimaEnter(final Event<Object> event);

}