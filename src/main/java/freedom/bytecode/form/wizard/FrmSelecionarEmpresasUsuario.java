package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmSelecionarEmpresasUsuario extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.SelecionarEmpresasUsuarioRNA rn = null;

    public FrmSelecionarEmpresasUsuario() {
        try {
            rn = (freedom.bytecode.rn.SelecionarEmpresasUsuarioRNA) getRN(freedom.bytecode.rn.wizard.SelecionarEmpresasUsuarioRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmpresas();
        init_popGrdEmpresas();
        init_mmSelecionarTodosOsRegistrosGrdEmpresas();
        init_mmSelecionarNenhumRegistroGrdEmpresas();
        init_vBoxPrincipal();
        init_hBoxBotoes();
        init_btnVoltar();
        init_btnAceitar();
        init_grdEmpresas();
        init_FrmSelecionarEmpresasUsuario();
    }

    public LEADS_EMPRESAS_USUARIOS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("51201449;51201");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public TFPopupMenu popGrdEmpresas = new TFPopupMenu();

    private void init_popGrdEmpresas() {
        popGrdEmpresas.setName("popGrdEmpresas");
        FrmSelecionarEmpresasUsuario.addChildren(popGrdEmpresas);
        popGrdEmpresas.applyProperties();
    }

    public TFMenuItem mmSelecionarTodosOsRegistrosGrdEmpresas = new TFMenuItem();

    private void init_mmSelecionarTodosOsRegistrosGrdEmpresas() {
        mmSelecionarTodosOsRegistrosGrdEmpresas.setName("mmSelecionarTodosOsRegistrosGrdEmpresas");
        mmSelecionarTodosOsRegistrosGrdEmpresas.setCaption("Selecionar todos os registros");
        mmSelecionarTodosOsRegistrosGrdEmpresas.setHint("Selecionar todos os registros");
        mmSelecionarTodosOsRegistrosGrdEmpresas.setImageIndex(310010);
        mmSelecionarTodosOsRegistrosGrdEmpresas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarTodosOsRegistrosGrdEmpresasClick(event);
            processarFlow("FrmSelecionarEmpresasUsuario", "mmSelecionarTodosOsRegistrosGrdEmpresas", "OnClick");
        });
        mmSelecionarTodosOsRegistrosGrdEmpresas.setAccess(false);
        mmSelecionarTodosOsRegistrosGrdEmpresas.setCheckmark(false);
        mmSelecionarTodosOsRegistrosGrdEmpresas.setIconClass("hashtag");
        popGrdEmpresas.addChildren(mmSelecionarTodosOsRegistrosGrdEmpresas);
        mmSelecionarTodosOsRegistrosGrdEmpresas.applyProperties();
    }

    public TFMenuItem mmSelecionarNenhumRegistroGrdEmpresas = new TFMenuItem();

    private void init_mmSelecionarNenhumRegistroGrdEmpresas() {
        mmSelecionarNenhumRegistroGrdEmpresas.setName("mmSelecionarNenhumRegistroGrdEmpresas");
        mmSelecionarNenhumRegistroGrdEmpresas.setCaption("Selecionar nenhum registro");
        mmSelecionarNenhumRegistroGrdEmpresas.setHint("Selecionar nenhum registro");
        mmSelecionarNenhumRegistroGrdEmpresas.setImageIndex(310011);
        mmSelecionarNenhumRegistroGrdEmpresas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarNenhumRegistroGrdEmpresasClick(event);
            processarFlow("FrmSelecionarEmpresasUsuario", "mmSelecionarNenhumRegistroGrdEmpresas", "OnClick");
        });
        mmSelecionarNenhumRegistroGrdEmpresas.setAccess(false);
        mmSelecionarNenhumRegistroGrdEmpresas.setCheckmark(false);
        mmSelecionarNenhumRegistroGrdEmpresas.setIconClass("hashtag");
        popGrdEmpresas.addChildren(mmSelecionarNenhumRegistroGrdEmpresas);
        mmSelecionarNenhumRegistroGrdEmpresas.applyProperties();
    }

    protected TFForm FrmSelecionarEmpresasUsuario = this;
    private void init_FrmSelecionarEmpresasUsuario() {
        FrmSelecionarEmpresasUsuario.setName("FrmSelecionarEmpresasUsuario");
        FrmSelecionarEmpresasUsuario.setCaption("Selecionar empresas do usu\u00E1rio");
        FrmSelecionarEmpresasUsuario.setClientHeight(261);
        FrmSelecionarEmpresasUsuario.setClientWidth(584);
        FrmSelecionarEmpresasUsuario.setColor("clBtnFace");
        FrmSelecionarEmpresasUsuario.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmSelecionarEmpresasUsuario", "FrmSelecionarEmpresasUsuario", "OnCreate");
        });
        FrmSelecionarEmpresasUsuario.setWOrigem("EhMain");
        FrmSelecionarEmpresasUsuario.setWKey("51201449");
        FrmSelecionarEmpresasUsuario.setSpacing(0);
        FrmSelecionarEmpresasUsuario.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(584);
        vBoxPrincipal.setHeight(261);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(5);
        vBoxPrincipal.setMarginLeft(5);
        vBoxPrincipal.setMarginRight(5);
        vBoxPrincipal.setMarginBottom(5);
        vBoxPrincipal.setSpacing(5);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmSelecionarEmpresasUsuario.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(570);
        hBoxBotoes.setHeight(60);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(5);
        hBoxBotoes.setFlexVflex("ftMin");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(55);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmSelecionarEmpresasUsuario", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(60);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(55);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmSelecionarEmpresasUsuario", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFGrid grdEmpresas = new TFGrid();

    private void init_grdEmpresas() {
        grdEmpresas.setName("grdEmpresas");
        grdEmpresas.setLeft(0);
        grdEmpresas.setTop(61);
        grdEmpresas.setWidth(400);
        grdEmpresas.setHeight(120);
        grdEmpresas.setTable(tbEmpresas);
        grdEmpresas.setFlexVflex("ftTrue");
        grdEmpresas.setFlexHflex("ftTrue");
        grdEmpresas.setPagingEnabled(false);
        grdEmpresas.setFrozenColumns(0);
        grdEmpresas.setShowFooter(false);
        grdEmpresas.setShowHeader(true);
        grdEmpresas.setMultiSelection(false);
        grdEmpresas.setGroupingEnabled(false);
        grdEmpresas.setGroupingExpanded(false);
        grdEmpresas.setGroupingShowFooter(false);
        grdEmpresas.setCrosstabEnabled(false);
        grdEmpresas.setCrosstabGroupType("cgtConcat");
        grdEmpresas.setEditionEnabled(false);
        grdEmpresas.setContextMenu(popGrdEmpresas);
        grdEmpresas.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("SEL");
        item0.setTitleCaption("#");
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL = 'S'");
        item1.setHint("Registro selecionado");
        item1.setEvalType("etExpression");
        item1.setImageId(310010);
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL = 'N'");
        item2.setHint("Registro n\u00E3o selecionado");
        item2.setEvalType("etExpression");
        item2.setImageId(310011);
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdEmpresas.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("EMPRESA");
        item3.setTitleCaption("Empresa");
        item3.setWidth(200);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdEmpresas.getColumns().add(item3);
        vBoxPrincipal.addChildren(grdEmpresas);
        grdEmpresas.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void mmSelecionarTodosOsRegistrosGrdEmpresasClick(final Event<Object> event);

    public abstract void mmSelecionarNenhumRegistroGrdEmpresasClick(final Event<Object> event);

}