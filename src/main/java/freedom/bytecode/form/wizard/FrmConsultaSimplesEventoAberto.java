package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmConsultaSimplesEventoAberto extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ConsultaSimplesEventoAbertoRNA rn = null;

    public FrmConsultaSimplesEventoAberto() {
        try {
            rn = (freedom.bytecode.rn.ConsultaSimplesEventoAbertoRNA) getRN(freedom.bytecode.rn.wizard.ConsultaSimplesEventoAbertoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbCrmpartsConsultaEvento();
        init_vBoxConsultaEvento();
        init_FHBox1();
        init_btnContinuar();
        init_FVBox1();
        init_btnSelecionar();
        init_gridConsultaEvento();
        init_FrmConsultaSimplesEventoAberto();
    }

    public CRMPARTS_CONSULTA_EVENTO tbCrmpartsConsultaEvento;

    private void init_tbCrmpartsConsultaEvento() {
        tbCrmpartsConsultaEvento = rn.tbCrmpartsConsultaEvento;
        tbCrmpartsConsultaEvento.setName("tbCrmpartsConsultaEvento");
        tbCrmpartsConsultaEvento.setMaxRowCount(200);
        tbCrmpartsConsultaEvento.setWKey("7000149;70001");
        tbCrmpartsConsultaEvento.setRatioBatchSize(20);
        getTables().put(tbCrmpartsConsultaEvento, "tbCrmpartsConsultaEvento");
        tbCrmpartsConsultaEvento.applyProperties();
    }

    protected TFForm FrmConsultaSimplesEventoAberto = this;
    private void init_FrmConsultaSimplesEventoAberto() {
        FrmConsultaSimplesEventoAberto.setName("FrmConsultaSimplesEventoAberto");
        FrmConsultaSimplesEventoAberto.setCaption("Existe Or\u00E7amentos em aberto para este cliente");
        FrmConsultaSimplesEventoAberto.setClientHeight(295);
        FrmConsultaSimplesEventoAberto.setClientWidth(533);
        FrmConsultaSimplesEventoAberto.setColor("clBtnFace");
        FrmConsultaSimplesEventoAberto.setWOrigem("EhMain");
        FrmConsultaSimplesEventoAberto.setWKey("7000149");
        FrmConsultaSimplesEventoAberto.setSpacing(0);
        FrmConsultaSimplesEventoAberto.applyProperties();
    }

    public TFVBox vBoxConsultaEvento = new TFVBox();

    private void init_vBoxConsultaEvento() {
        vBoxConsultaEvento.setName("vBoxConsultaEvento");
        vBoxConsultaEvento.setLeft(0);
        vBoxConsultaEvento.setTop(0);
        vBoxConsultaEvento.setWidth(533);
        vBoxConsultaEvento.setHeight(295);
        vBoxConsultaEvento.setAlign("alClient");
        vBoxConsultaEvento.setBorderStyle("stNone");
        vBoxConsultaEvento.setPaddingTop(0);
        vBoxConsultaEvento.setPaddingLeft(0);
        vBoxConsultaEvento.setPaddingRight(0);
        vBoxConsultaEvento.setPaddingBottom(0);
        vBoxConsultaEvento.setMarginTop(0);
        vBoxConsultaEvento.setMarginLeft(0);
        vBoxConsultaEvento.setMarginRight(0);
        vBoxConsultaEvento.setMarginBottom(0);
        vBoxConsultaEvento.setSpacing(1);
        vBoxConsultaEvento.setFlexVflex("ftTrue");
        vBoxConsultaEvento.setFlexHflex("ftTrue");
        vBoxConsultaEvento.setScrollable(false);
        vBoxConsultaEvento.setBoxShadowConfigHorizontalLength(10);
        vBoxConsultaEvento.setBoxShadowConfigVerticalLength(10);
        vBoxConsultaEvento.setBoxShadowConfigBlurRadius(5);
        vBoxConsultaEvento.setBoxShadowConfigSpreadRadius(0);
        vBoxConsultaEvento.setBoxShadowConfigShadowColor("clBlack");
        vBoxConsultaEvento.setBoxShadowConfigOpacity(75);
        FrmConsultaSimplesEventoAberto.addChildren(vBoxConsultaEvento);
        vBoxConsultaEvento.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(583);
        FHBox1.setHeight(64);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(2);
        FHBox1.setPaddingLeft(5);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxConsultaEvento.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnContinuar = new TFButton();

    private void init_btnContinuar() {
        btnContinuar.setName("btnContinuar");
        btnContinuar.setLeft(0);
        btnContinuar.setTop(0);
        btnContinuar.setWidth(60);
        btnContinuar.setHeight(56);
        btnContinuar.setHint("Continuar abertura evento");
        btnContinuar.setAlign("alLeft");
        btnContinuar.setCaption("Continuar");
        btnContinuar.setFontColor("clWindowText");
        btnContinuar.setFontSize(-11);
        btnContinuar.setFontName("Tahoma");
        btnContinuar.setFontStyle("[]");
        btnContinuar.setLayout("blGlyphTop");
        btnContinuar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnContinuarClick(event);
            processarFlow("FrmConsultaSimplesEventoAberto", "btnContinuar", "OnClick");
        });
        btnContinuar.setImageId(7000100);
        btnContinuar.setColor("clBtnFace");
        btnContinuar.setAccess(false);
        btnContinuar.setIconReverseDirection(false);
        FHBox1.addChildren(btnContinuar);
        btnContinuar.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(60);
        FVBox1.setTop(0);
        FVBox1.setWidth(8);
        FVBox1.setHeight(52);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFButton btnSelecionar = new TFButton();

    private void init_btnSelecionar() {
        btnSelecionar.setName("btnSelecionar");
        btnSelecionar.setLeft(68);
        btnSelecionar.setTop(0);
        btnSelecionar.setWidth(60);
        btnSelecionar.setHeight(56);
        btnSelecionar.setHint("Escolher evento abaixo");
        btnSelecionar.setAlign("alLeft");
        btnSelecionar.setCaption("Selecionar");
        btnSelecionar.setFontColor("clWindowText");
        btnSelecionar.setFontSize(-11);
        btnSelecionar.setFontName("Tahoma");
        btnSelecionar.setFontStyle("[]");
        btnSelecionar.setLayout("blGlyphTop");
        btnSelecionar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSelecionarClick(event);
            processarFlow("FrmConsultaSimplesEventoAberto", "btnSelecionar", "OnClick");
        });
        btnSelecionar.setImageId(700088);
        btnSelecionar.setColor("clBtnFace");
        btnSelecionar.setAccess(false);
        btnSelecionar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSelecionar);
        btnSelecionar.applyProperties();
    }

    public TFGrid gridConsultaEvento = new TFGrid();

    private void init_gridConsultaEvento() {
        gridConsultaEvento.setName("gridConsultaEvento");
        gridConsultaEvento.setLeft(0);
        gridConsultaEvento.setTop(65);
        gridConsultaEvento.setWidth(582);
        gridConsultaEvento.setHeight(224);
        gridConsultaEvento.setAlign("alClient");
        gridConsultaEvento.setTable(tbCrmpartsConsultaEvento);
        gridConsultaEvento.setFlexVflex("ftTrue");
        gridConsultaEvento.setFlexHflex("ftTrue");
        gridConsultaEvento.setPagingEnabled(true);
        gridConsultaEvento.setFrozenColumns(0);
        gridConsultaEvento.setShowFooter(false);
        gridConsultaEvento.setShowHeader(true);
        gridConsultaEvento.setMultiSelection(false);
        gridConsultaEvento.setGroupingEnabled(false);
        gridConsultaEvento.setGroupingExpanded(false);
        gridConsultaEvento.setGroupingShowFooter(false);
        gridConsultaEvento.setCrosstabEnabled(false);
        gridConsultaEvento.setCrosstabGroupType("cgtConcat");
        gridConsultaEvento.setEditionEnabled(false);
        gridConsultaEvento.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_ORC_MAPA");
        item0.setTitleCaption("Nr. Or\u00E7amento");
        item0.setWidth(178);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftInteger");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridConsultaEvento.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DATA_EVENTO");
        item1.setTitleCaption("Data Evento");
        item1.setWidth(192);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftDate");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridConsultaEvento.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("TOTAL_LIQUIDO");
        item2.setTitleCaption("Valor");
        item2.setWidth(139);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taRight");
        item2.setFieldType("ftDecimal");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFMaskExpression item3 = new TFMaskExpression();
        item3.setExpression("*");
        item3.setEvalType("etExpression");
        item3.setMask("R$ ,##0.00");
        item3.setPadLength(0);
        item3.setPadDirection("pdNone");
        item3.setMaskType("mtDecimal");
        item2.getMasks().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridConsultaEvento.getColumns().add(item2);
        vBoxConsultaEvento.addChildren(gridConsultaEvento);
        gridConsultaEvento.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnContinuarClick(final Event<Object> event) {
        if (btnContinuar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnContinuar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSelecionarClick(final Event<Object> event) {
        if (btnSelecionar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSelecionar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}