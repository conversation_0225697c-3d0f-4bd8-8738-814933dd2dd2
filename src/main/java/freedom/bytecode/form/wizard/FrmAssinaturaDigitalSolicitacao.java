package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAssinaturaDigitalSolicitacao extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AssinaturaDigitalSolicitacaoRNA rn = null;

    public FrmAssinaturaDigitalSolicitacao() {
        try {
            rn = (freedom.bytecode.rn.AssinaturaDigitalSolicitacaoRNA) getRN(freedom.bytecode.rn.wizard.AssinaturaDigitalSolicitacaoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbSolicitacoesAssinaturas();
        init_tbNbsapiEnvelopeFilaResumo();
        init_tbAssinaturaDigital();
        init_tbNbsapiDocumentos();
        init_tbAssinaturaDigitalDoc();
        init_tbNbsapiDocumentosAssinados();
        init_tbNbsapiEnvelopeFilaRank();
        init_menuVerDocumentos();
        init_timerAtualizarStatusAssinatura();
        init_FPopupMenu1();
        init_imgAnexo();
        init_imgQrCode();
        init_boxPrinciapl();
        init_hboxBotoes();
        init_btnVoltar();
        init_btnEnviar();
        init_btnReenviar();
        init_btnCancelar();
        init_btnArquivar();
        init_hboxControleRemotoPresencial();
        init_hboxControleRemotoPresencialEspaco1();
        init_hboxControleRemotoPresencialInterno();
        init_hboxSelcaoRemoto();
        init_hboxSelcaoAtivoEspaco1();
        init_lblSelecaoRemoto();
        init_hboxSelcaoAtivoEspaco2();
        init_hboxSelcaoPresencial();
        init_hboxSelcaoPresencialEspaco1();
        init_lblSelecaoPresencial();
        init_hboxSelcaoPresencialEspaco2();
        init_hboxControleRemotoPresencialEspaco2();
        init_hboxStatusAssinaturaEspaco1();
        init_vBoxBtnAcaoLerQRCodePrincipal();
        init_vBoxBtnAcaoLerQRCodePrincipalEspaco1();
        init_FPanelBtnAcaoLerQRCode();
        init_vBoxBtnAcaoLerQRCodePrincipalEspaco2();
        init_hboxStatusAssinaturaEspaco2();
        init_vBoxBtnAcaoBaixarDocumentoPrincipal();
        init_vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1();
        init_FPanelBtnAcaoBaixarDocumento();
        init_vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2();
        init_FHBox3();
        init_vBoxlStatusAssinaturaPrincipal();
        init_vBoxlStatusAssinaturaPrincipal1();
        init_FPanelStatusAssinatura();
        init_vBoxlStatusAssinaturaPrincipal2();
        init_VboxTemplateSolicitacaoAssinaturaPrincipal();
        init_hboxTemplateSolicitacaoAssinatura();
        init_vboxTemplateSolicitacaoAssinaturaCol1();
        init_hboxTemplateSolicitacaoAssinaturaLinha1();
        init_hboxTemplateSolicitacaoAssinaturaHboxLblTipo();
        init_hboxTemplateSolicitacaoAssinaturaLblTipo();
        init_hboxTemplateSolicitacaoAssinaturaHboxLblEditar();
        init_hboxTemplateSolicitacaoAssinaturaIconEditar();
        init_hboxTemplateSolicitacaoAssinaturaLblEditar();
        init_hboxTemplateSolicitacaoAssinaturaHboxLblStatus();
        init_hboxTemplateSolicitacaoAssinaturaLblStatus();
        init_hboxTemplateSolicitacaoAssinaturaLinha2();
        init_hboxTemplateSolicitacaoAssinaturaHboxLblNome();
        init_hboxTemplateSolicitacaoAssinaturaLblNome();
        init_hboxTemplateSolicitacaoAssinaturaLinha3();
        init_hboxTemplateSolicitacaoAssinaturaHboxLblEmail();
        init_hboxTemplateSolicitacaoAssinaturaLblEmail();
        init_hboxTemplateSolicitacaoAssinaturaHboxLblTelefone();
        init_hboxTemplateSolicitacaoAssinaturaLblTelefone();
        init_vBoxBtnAcaoLerQRCodeSignatarioPrincipal();
        init_vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2();
        init_FPanelBtnQrCode();
        init_vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1();
        init_hboxIrParaVersaoAtual();
        init_lblIrParaVersaoAtualVisualizacao();
        init_lblIrParaVersaoAtual();
        init_vBoxGridSolicitacaoAssinatura();
        init_FGrid1();
        init_pgCtrlDados();
        init_tabDadosAssinatura();
        init_hboxTabDadosAssinaturaMain();
        init_FHBox1();
        init_vboxObservador();
        init_lblObservador();
        init_edObservador();
        init_vboxBtnHistorico();
        init_vboxBtnHistoricoEspaco1();
        init_btnHistorico();
        init_FVBox1();
        init_FHBox2();
        init_btnTesteLoading();
        init_vBoxGridSolicitacaoAssinaturaEspacoRodape();
        init_FrmAssinaturaDigitalSolicitacao();
    }

    public SOLICITACOES_ASSINATURAS tbSolicitacoesAssinaturas;

    private void init_tbSolicitacoesAssinaturas() {
        tbSolicitacoesAssinaturas = rn.tbSolicitacoesAssinaturas;
        tbSolicitacoesAssinaturas.setName("tbSolicitacoesAssinaturas");
        TFTableField item15 = new TFTableField();
        item15.setName("EDITAR");
        item15.setCalculated(true);
        item15.setUpdatable(false);
        item15.setPrimaryKey(false);
        item15.setFieldType("ftString");
        item15.setJSONConfigNullOnEmpty(false);
        item15.setCaption("EDITAR");
        tbSolicitacoesAssinaturas.getFieldDefs().add(item15);
        TFTableField item16 = new TFTableField();
        item16.setName("VER");
        item16.setCalculated(true);
        item16.setUpdatable(false);
        item16.setPrimaryKey(false);
        item16.setFieldType("ftString");
        item16.setJSONConfigNullOnEmpty(false);
        item16.setCaption("VER");
        tbSolicitacoesAssinaturas.getFieldDefs().add(item16);
        TFTableField item17 = new TFTableField();
        item17.setName("ASSINAR");
        item17.setCalculated(true);
        item17.setUpdatable(false);
        item17.setPrimaryKey(false);
        item17.setFieldType("ftString");
        item17.setJSONConfigNullOnEmpty(false);
        item17.setCaption("ASSINAR");
        tbSolicitacoesAssinaturas.getFieldDefs().add(item17);
        tbSolicitacoesAssinaturas.setMaxRowCount(200);
        tbSolicitacoesAssinaturas.setWKey("45506;45501");
        tbSolicitacoesAssinaturas.setRatioBatchSize(20);
        getTables().put(tbSolicitacoesAssinaturas, "tbSolicitacoesAssinaturas");
        tbSolicitacoesAssinaturas.applyProperties();
    }

    public NBSAPI_ENVELOPE_FILA_RESUMO tbNbsapiEnvelopeFilaResumo;

    private void init_tbNbsapiEnvelopeFilaResumo() {
        tbNbsapiEnvelopeFilaResumo = rn.tbNbsapiEnvelopeFilaResumo;
        tbNbsapiEnvelopeFilaResumo.setName("tbNbsapiEnvelopeFilaResumo");
        tbNbsapiEnvelopeFilaResumo.setMaxRowCount(200);
        tbNbsapiEnvelopeFilaResumo.setWKey("45506;45503");
        tbNbsapiEnvelopeFilaResumo.setRatioBatchSize(20);
        getTables().put(tbNbsapiEnvelopeFilaResumo, "tbNbsapiEnvelopeFilaResumo");
        tbNbsapiEnvelopeFilaResumo.applyProperties();
    }

    public CRM_ASSINATURA_DIGITAL tbAssinaturaDigital;

    private void init_tbAssinaturaDigital() {
        tbAssinaturaDigital = rn.tbAssinaturaDigital;
        tbAssinaturaDigital.setName("tbAssinaturaDigital");
        tbAssinaturaDigital.setMaxRowCount(200);
        tbAssinaturaDigital.setWKey("45506;45504");
        tbAssinaturaDigital.setRatioBatchSize(20);
        getTables().put(tbAssinaturaDigital, "tbAssinaturaDigital");
        tbAssinaturaDigital.applyProperties();
    }

    public NBSAPI_DOCUMENTOS tbNbsapiDocumentos;

    private void init_tbNbsapiDocumentos() {
        tbNbsapiDocumentos = rn.tbNbsapiDocumentos;
        tbNbsapiDocumentos.setName("tbNbsapiDocumentos");
        tbNbsapiDocumentos.setMaxRowCount(200);
        tbNbsapiDocumentos.setWKey("45506;45505");
        tbNbsapiDocumentos.setRatioBatchSize(20);
        getTables().put(tbNbsapiDocumentos, "tbNbsapiDocumentos");
        tbNbsapiDocumentos.applyProperties();
    }

    public CRM_ASSINATURA_DIGITAL_DOC tbAssinaturaDigitalDoc;

    private void init_tbAssinaturaDigitalDoc() {
        tbAssinaturaDigitalDoc = rn.tbAssinaturaDigitalDoc;
        tbAssinaturaDigitalDoc.setName("tbAssinaturaDigitalDoc");
        tbAssinaturaDigitalDoc.setMaxRowCount(200);
        tbAssinaturaDigitalDoc.setWKey("45506;45506");
        tbAssinaturaDigitalDoc.setRatioBatchSize(20);
        getTables().put(tbAssinaturaDigitalDoc, "tbAssinaturaDigitalDoc");
        tbAssinaturaDigitalDoc.applyProperties();
    }

    public NBSAPI_DOCUMENTOS_ASSINADOS tbNbsapiDocumentosAssinados;

    private void init_tbNbsapiDocumentosAssinados() {
        tbNbsapiDocumentosAssinados = rn.tbNbsapiDocumentosAssinados;
        tbNbsapiDocumentosAssinados.setName("tbNbsapiDocumentosAssinados");
        tbNbsapiDocumentosAssinados.setMaxRowCount(200);
        tbNbsapiDocumentosAssinados.setWKey("45506;45507");
        tbNbsapiDocumentosAssinados.setRatioBatchSize(20);
        getTables().put(tbNbsapiDocumentosAssinados, "tbNbsapiDocumentosAssinados");
        tbNbsapiDocumentosAssinados.applyProperties();
    }

    public NBSAPI_ENVELOPE_FILA_RANK tbNbsapiEnvelopeFilaRank;

    private void init_tbNbsapiEnvelopeFilaRank() {
        tbNbsapiEnvelopeFilaRank = rn.tbNbsapiEnvelopeFilaRank;
        tbNbsapiEnvelopeFilaRank.setName("tbNbsapiEnvelopeFilaRank");
        tbNbsapiEnvelopeFilaRank.setMaxRowCount(200);
        tbNbsapiEnvelopeFilaRank.setWKey("45506;47408");
        tbNbsapiEnvelopeFilaRank.setRatioBatchSize(20);
        getTables().put(tbNbsapiEnvelopeFilaRank, "tbNbsapiEnvelopeFilaRank");
        tbNbsapiEnvelopeFilaRank.applyProperties();
    }

    public TFPopupMenu menuVerDocumentos = new TFPopupMenu();

    private void init_menuVerDocumentos() {
        menuVerDocumentos.setName("menuVerDocumentos");
        FrmAssinaturaDigitalSolicitacao.addChildren(menuVerDocumentos);
        menuVerDocumentos.applyProperties();
    }

    public TFTimer timerAtualizarStatusAssinatura = new TFTimer();

    private void init_timerAtualizarStatusAssinatura() {
        timerAtualizarStatusAssinatura.setName("timerAtualizarStatusAssinatura");
        timerAtualizarStatusAssinatura.setEnabled(false);
        timerAtualizarStatusAssinatura.setInterval(5000);
        timerAtualizarStatusAssinatura.addEventListener("onTimer", (EventListener<Event<Object>>)(Event<Object> event) -> {
            timerAtualizarStatusAssinaturaTimer(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "timerAtualizarStatusAssinatura", "OnTimer");
        });
        timerAtualizarStatusAssinatura.setRepeats(true);
        FrmAssinaturaDigitalSolicitacao.addChildren(timerAtualizarStatusAssinatura);
        timerAtualizarStatusAssinatura.applyProperties();
    }

    public TFPopupMenu FPopupMenu1 = new TFPopupMenu();

    private void init_FPopupMenu1() {
        FPopupMenu1.setName("FPopupMenu1");
        FrmAssinaturaDigitalSolicitacao.addChildren(FPopupMenu1);
        FPopupMenu1.applyProperties();
    }

    public TFMenuItem imgAnexo = new TFMenuItem();

    private void init_imgAnexo() {
        imgAnexo.setName("imgAnexo");
        imgAnexo.setCaption("imgAnexo");
        imgAnexo.setImageIndex(4600365);
        imgAnexo.setAccess(false);
        imgAnexo.setCheckmark(false);
        FPopupMenu1.addChildren(imgAnexo);
        imgAnexo.applyProperties();
    }

    public TFMenuItem imgQrCode = new TFMenuItem();

    private void init_imgQrCode() {
        imgQrCode.setName("imgQrCode");
        imgQrCode.setCaption("imgQrCode");
        imgQrCode.setImageIndex(4600365);
        imgQrCode.setAccess(false);
        imgQrCode.setCheckmark(false);
        FPopupMenu1.addChildren(imgQrCode);
        imgQrCode.applyProperties();
    }

    protected TFForm FrmAssinaturaDigitalSolicitacao = this;
    private void init_FrmAssinaturaDigitalSolicitacao() {
        FrmAssinaturaDigitalSolicitacao.setName("FrmAssinaturaDigitalSolicitacao");
        FrmAssinaturaDigitalSolicitacao.setCaption("Assinatura Digital Solicita\u00E7\u00E3o");
        FrmAssinaturaDigitalSolicitacao.setClientHeight(502);
        FrmAssinaturaDigitalSolicitacao.setClientWidth(584);
        FrmAssinaturaDigitalSolicitacao.setColor("clBtnFace");
        FrmAssinaturaDigitalSolicitacao.setWOrigem("EhMain");
        FrmAssinaturaDigitalSolicitacao.setWKey("45506");
        FrmAssinaturaDigitalSolicitacao.setSpacing(0);
        FrmAssinaturaDigitalSolicitacao.applyProperties();
    }

    public TFVBox boxPrinciapl = new TFVBox();

    private void init_boxPrinciapl() {
        boxPrinciapl.setName("boxPrinciapl");
        boxPrinciapl.setLeft(0);
        boxPrinciapl.setTop(0);
        boxPrinciapl.setWidth(584);
        boxPrinciapl.setHeight(502);
        boxPrinciapl.setAlign("alClient");
        boxPrinciapl.setBorderStyle("stNone");
        boxPrinciapl.setPaddingTop(5);
        boxPrinciapl.setPaddingLeft(5);
        boxPrinciapl.setPaddingRight(5);
        boxPrinciapl.setPaddingBottom(5);
        boxPrinciapl.setMarginTop(0);
        boxPrinciapl.setMarginLeft(0);
        boxPrinciapl.setMarginRight(0);
        boxPrinciapl.setMarginBottom(0);
        boxPrinciapl.setSpacing(1);
        boxPrinciapl.setFlexVflex("ftTrue");
        boxPrinciapl.setFlexHflex("ftTrue");
        boxPrinciapl.setScrollable(false);
        boxPrinciapl.setBoxShadowConfigHorizontalLength(10);
        boxPrinciapl.setBoxShadowConfigVerticalLength(10);
        boxPrinciapl.setBoxShadowConfigBlurRadius(5);
        boxPrinciapl.setBoxShadowConfigSpreadRadius(0);
        boxPrinciapl.setBoxShadowConfigShadowColor("clBlack");
        boxPrinciapl.setBoxShadowConfigOpacity(75);
        FrmAssinaturaDigitalSolicitacao.addChildren(boxPrinciapl);
        boxPrinciapl.applyProperties();
    }

    public TFHBox hboxBotoes = new TFHBox();

    private void init_hboxBotoes() {
        hboxBotoes.setName("hboxBotoes");
        hboxBotoes.setLeft(0);
        hboxBotoes.setTop(0);
        hboxBotoes.setWidth(680);
        hboxBotoes.setHeight(57);
        hboxBotoes.setAlign("alTop");
        hboxBotoes.setBorderStyle("stNone");
        hboxBotoes.setPaddingTop(0);
        hboxBotoes.setPaddingLeft(0);
        hboxBotoes.setPaddingRight(5);
        hboxBotoes.setPaddingBottom(0);
        hboxBotoes.setMarginTop(0);
        hboxBotoes.setMarginLeft(0);
        hboxBotoes.setMarginRight(0);
        hboxBotoes.setMarginBottom(0);
        hboxBotoes.setSpacing(5);
        hboxBotoes.setFlexVflex("ftFalse");
        hboxBotoes.setFlexHflex("ftTrue");
        hboxBotoes.setScrollable(false);
        hboxBotoes.setBoxShadowConfigHorizontalLength(10);
        hboxBotoes.setBoxShadowConfigVerticalLength(10);
        hboxBotoes.setBoxShadowConfigBlurRadius(5);
        hboxBotoes.setBoxShadowConfigSpreadRadius(0);
        hboxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hboxBotoes.setBoxShadowConfigOpacity(75);
        hboxBotoes.setVAlign("tvTop");
        boxPrinciapl.addChildren(hboxBotoes);
        hboxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(50);
        btnVoltar.setHeight(50);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(0);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconClass("fas fa-reply  icon-color-black icon-24px");
        btnVoltar.setIconReverseDirection(false);
        hboxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnEnviar = new TFButton();

    private void init_btnEnviar() {
        btnEnviar.setName("btnEnviar");
        btnEnviar.setLeft(50);
        btnEnviar.setTop(0);
        btnEnviar.setWidth(50);
        btnEnviar.setHeight(50);
        btnEnviar.setHint("Enviar assinatura");
        btnEnviar.setAlign("alLeft");
        btnEnviar.setCaption("Enviar");
        btnEnviar.setFontColor("clWindowText");
        btnEnviar.setFontSize(-11);
        btnEnviar.setFontName("Tahoma");
        btnEnviar.setFontStyle("[]");
        btnEnviar.setLayout("blGlyphTop");
        btnEnviar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "btnEnviar", "OnClick");
        });
        btnEnviar.setImageId(0);
        btnEnviar.setColor("clBtnFace");
        btnEnviar.setAccess(false);
        btnEnviar.setIconClass("fas fa-paper-plane icon-color-black icon-24px");
        btnEnviar.setIconReverseDirection(false);
        hboxBotoes.addChildren(btnEnviar);
        btnEnviar.applyProperties();
    }

    public TFButton btnReenviar = new TFButton();

    private void init_btnReenviar() {
        btnReenviar.setName("btnReenviar");
        btnReenviar.setLeft(100);
        btnReenviar.setTop(0);
        btnReenviar.setWidth(50);
        btnReenviar.setHeight(50);
        btnReenviar.setHint("Reenviar Assinatura");
        btnReenviar.setAlign("alLeft");
        btnReenviar.setCaption("Reenviar");
        btnReenviar.setFontColor("clWindowText");
        btnReenviar.setFontSize(-11);
        btnReenviar.setFontName("Tahoma");
        btnReenviar.setFontStyle("[]");
        btnReenviar.setLayout("blGlyphTop");
        btnReenviar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnReenviarClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "btnReenviar", "OnClick");
        });
        btnReenviar.setImageId(0);
        btnReenviar.setColor("clBtnFace");
        btnReenviar.setAccess(false);
        btnReenviar.setIconClass("fas fa-paper-plane icon-color-black icon-24px");
        btnReenviar.setIconReverseDirection(false);
        hboxBotoes.addChildren(btnReenviar);
        btnReenviar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(150);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(50);
        btnCancelar.setHeight(50);
        btnCancelar.setHint("Cancelar Assinatura");
        btnCancelar.setAlign("alLeft");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(0);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconClass("fas fa-ban icon-color-black icon-24px");
        btnCancelar.setIconReverseDirection(false);
        hboxBotoes.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFButton btnArquivar = new TFButton();

    private void init_btnArquivar() {
        btnArquivar.setName("btnArquivar");
        btnArquivar.setLeft(200);
        btnArquivar.setTop(0);
        btnArquivar.setWidth(50);
        btnArquivar.setHeight(50);
        btnArquivar.setHint("Cancelar Assinatura");
        btnArquivar.setAlign("alLeft");
        btnArquivar.setCaption("Arquivar");
        btnArquivar.setFontColor("clWindowText");
        btnArquivar.setFontSize(-11);
        btnArquivar.setFontName("Tahoma");
        btnArquivar.setFontStyle("[]");
        btnArquivar.setLayout("blGlyphTop");
        btnArquivar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnArquivarClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "btnArquivar", "OnClick");
        });
        btnArquivar.setImageId(0);
        btnArquivar.setColor("clBtnFace");
        btnArquivar.setAccess(false);
        btnArquivar.setIconClass("fas fa-archive icon-color-black icon-24px");
        btnArquivar.setIconReverseDirection(false);
        hboxBotoes.addChildren(btnArquivar);
        btnArquivar.applyProperties();
    }

    public TFVBox hboxControleRemotoPresencial = new TFVBox();

    private void init_hboxControleRemotoPresencial() {
        hboxControleRemotoPresencial.setName("hboxControleRemotoPresencial");
        hboxControleRemotoPresencial.setLeft(250);
        hboxControleRemotoPresencial.setTop(0);
        hboxControleRemotoPresencial.setWidth(170);
        hboxControleRemotoPresencial.setHeight(50);
        hboxControleRemotoPresencial.setBorderStyle("stNone");
        hboxControleRemotoPresencial.setPaddingTop(0);
        hboxControleRemotoPresencial.setPaddingLeft(0);
        hboxControleRemotoPresencial.setPaddingRight(0);
        hboxControleRemotoPresencial.setPaddingBottom(0);
        hboxControleRemotoPresencial.setMarginTop(0);
        hboxControleRemotoPresencial.setMarginLeft(0);
        hboxControleRemotoPresencial.setMarginRight(0);
        hboxControleRemotoPresencial.setMarginBottom(0);
        hboxControleRemotoPresencial.setSpacing(1);
        hboxControleRemotoPresencial.setFlexVflex("ftTrue");
        hboxControleRemotoPresencial.setFlexHflex("ftFalse");
        hboxControleRemotoPresencial.setScrollable(false);
        hboxControleRemotoPresencial.setBoxShadowConfigHorizontalLength(10);
        hboxControleRemotoPresencial.setBoxShadowConfigVerticalLength(10);
        hboxControleRemotoPresencial.setBoxShadowConfigBlurRadius(5);
        hboxControleRemotoPresencial.setBoxShadowConfigSpreadRadius(0);
        hboxControleRemotoPresencial.setBoxShadowConfigShadowColor("clBlack");
        hboxControleRemotoPresencial.setBoxShadowConfigOpacity(75);
        hboxBotoes.addChildren(hboxControleRemotoPresencial);
        hboxControleRemotoPresencial.applyProperties();
    }

    public TFHBox hboxControleRemotoPresencialEspaco1 = new TFHBox();

    private void init_hboxControleRemotoPresencialEspaco1() {
        hboxControleRemotoPresencialEspaco1.setName("hboxControleRemotoPresencialEspaco1");
        hboxControleRemotoPresencialEspaco1.setLeft(0);
        hboxControleRemotoPresencialEspaco1.setTop(0);
        hboxControleRemotoPresencialEspaco1.setWidth(76);
        hboxControleRemotoPresencialEspaco1.setHeight(8);
        hboxControleRemotoPresencialEspaco1.setBorderStyle("stNone");
        hboxControleRemotoPresencialEspaco1.setPaddingTop(0);
        hboxControleRemotoPresencialEspaco1.setPaddingLeft(0);
        hboxControleRemotoPresencialEspaco1.setPaddingRight(0);
        hboxControleRemotoPresencialEspaco1.setPaddingBottom(0);
        hboxControleRemotoPresencialEspaco1.setMarginTop(0);
        hboxControleRemotoPresencialEspaco1.setMarginLeft(0);
        hboxControleRemotoPresencialEspaco1.setMarginRight(0);
        hboxControleRemotoPresencialEspaco1.setMarginBottom(0);
        hboxControleRemotoPresencialEspaco1.setSpacing(1);
        hboxControleRemotoPresencialEspaco1.setFlexVflex("ftTrue");
        hboxControleRemotoPresencialEspaco1.setFlexHflex("ftTrue");
        hboxControleRemotoPresencialEspaco1.setScrollable(false);
        hboxControleRemotoPresencialEspaco1.setBoxShadowConfigHorizontalLength(10);
        hboxControleRemotoPresencialEspaco1.setBoxShadowConfigVerticalLength(10);
        hboxControleRemotoPresencialEspaco1.setBoxShadowConfigBlurRadius(5);
        hboxControleRemotoPresencialEspaco1.setBoxShadowConfigSpreadRadius(0);
        hboxControleRemotoPresencialEspaco1.setBoxShadowConfigShadowColor("clBlack");
        hboxControleRemotoPresencialEspaco1.setBoxShadowConfigOpacity(75);
        hboxControleRemotoPresencialEspaco1.setVAlign("tvTop");
        hboxControleRemotoPresencial.addChildren(hboxControleRemotoPresencialEspaco1);
        hboxControleRemotoPresencialEspaco1.applyProperties();
    }

    public TFHBox hboxControleRemotoPresencialInterno = new TFHBox();

    private void init_hboxControleRemotoPresencialInterno() {
        hboxControleRemotoPresencialInterno.setName("hboxControleRemotoPresencialInterno");
        hboxControleRemotoPresencialInterno.setLeft(0);
        hboxControleRemotoPresencialInterno.setTop(9);
        hboxControleRemotoPresencialInterno.setWidth(170);
        hboxControleRemotoPresencialInterno.setHeight(27);
        hboxControleRemotoPresencialInterno.setBorderStyle("stNone");
        hboxControleRemotoPresencialInterno.setPaddingTop(0);
        hboxControleRemotoPresencialInterno.setPaddingLeft(0);
        hboxControleRemotoPresencialInterno.setPaddingRight(0);
        hboxControleRemotoPresencialInterno.setPaddingBottom(0);
        hboxControleRemotoPresencialInterno.setMarginTop(0);
        hboxControleRemotoPresencialInterno.setMarginLeft(0);
        hboxControleRemotoPresencialInterno.setMarginRight(0);
        hboxControleRemotoPresencialInterno.setMarginBottom(0);
        hboxControleRemotoPresencialInterno.setSpacing(0);
        hboxControleRemotoPresencialInterno.setFlexVflex("ftFalse");
        hboxControleRemotoPresencialInterno.setFlexHflex("ftTrue");
        hboxControleRemotoPresencialInterno.setScrollable(false);
        hboxControleRemotoPresencialInterno.setBoxShadowConfigHorizontalLength(10);
        hboxControleRemotoPresencialInterno.setBoxShadowConfigVerticalLength(10);
        hboxControleRemotoPresencialInterno.setBoxShadowConfigBlurRadius(5);
        hboxControleRemotoPresencialInterno.setBoxShadowConfigSpreadRadius(0);
        hboxControleRemotoPresencialInterno.setBoxShadowConfigShadowColor("clBlack");
        hboxControleRemotoPresencialInterno.setBoxShadowConfigOpacity(75);
        hboxControleRemotoPresencialInterno.setVAlign("tvTop");
        hboxControleRemotoPresencial.addChildren(hboxControleRemotoPresencialInterno);
        hboxControleRemotoPresencialInterno.applyProperties();
    }

    public TFHBox hboxSelcaoRemoto = new TFHBox();

    private void init_hboxSelcaoRemoto() {
        hboxSelcaoRemoto.setName("hboxSelcaoRemoto");
        hboxSelcaoRemoto.setLeft(0);
        hboxSelcaoRemoto.setTop(0);
        hboxSelcaoRemoto.setWidth(80);
        hboxSelcaoRemoto.setHeight(23);
        hboxSelcaoRemoto.setBorderStyle("stNone");
        hboxSelcaoRemoto.setColor("clSilver");
        hboxSelcaoRemoto.setPaddingTop(2);
        hboxSelcaoRemoto.setPaddingLeft(0);
        hboxSelcaoRemoto.setPaddingRight(0);
        hboxSelcaoRemoto.setPaddingBottom(0);
        hboxSelcaoRemoto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxSelcaoRemotoClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "hboxSelcaoRemoto", "OnClick");
        });
        hboxSelcaoRemoto.setMarginTop(0);
        hboxSelcaoRemoto.setMarginLeft(0);
        hboxSelcaoRemoto.setMarginRight(0);
        hboxSelcaoRemoto.setMarginBottom(0);
        hboxSelcaoRemoto.setSpacing(0);
        hboxSelcaoRemoto.setFlexVflex("ftTrue");
        hboxSelcaoRemoto.setFlexHflex("ftFalse");
        hboxSelcaoRemoto.setScrollable(false);
        hboxSelcaoRemoto.setBoxShadowConfigHorizontalLength(10);
        hboxSelcaoRemoto.setBoxShadowConfigVerticalLength(10);
        hboxSelcaoRemoto.setBoxShadowConfigBlurRadius(5);
        hboxSelcaoRemoto.setBoxShadowConfigSpreadRadius(0);
        hboxSelcaoRemoto.setBoxShadowConfigShadowColor("clBlack");
        hboxSelcaoRemoto.setBoxShadowConfigOpacity(75);
        hboxSelcaoRemoto.setVAlign("tvTop");
        hboxControleRemotoPresencialInterno.addChildren(hboxSelcaoRemoto);
        hboxSelcaoRemoto.applyProperties();
    }

    public TFVBox hboxSelcaoAtivoEspaco1 = new TFVBox();

    private void init_hboxSelcaoAtivoEspaco1() {
        hboxSelcaoAtivoEspaco1.setName("hboxSelcaoAtivoEspaco1");
        hboxSelcaoAtivoEspaco1.setLeft(0);
        hboxSelcaoAtivoEspaco1.setTop(0);
        hboxSelcaoAtivoEspaco1.setWidth(16);
        hboxSelcaoAtivoEspaco1.setHeight(20);
        hboxSelcaoAtivoEspaco1.setBorderStyle("stNone");
        hboxSelcaoAtivoEspaco1.setPaddingTop(0);
        hboxSelcaoAtivoEspaco1.setPaddingLeft(0);
        hboxSelcaoAtivoEspaco1.setPaddingRight(0);
        hboxSelcaoAtivoEspaco1.setPaddingBottom(0);
        hboxSelcaoAtivoEspaco1.setMarginTop(0);
        hboxSelcaoAtivoEspaco1.setMarginLeft(0);
        hboxSelcaoAtivoEspaco1.setMarginRight(0);
        hboxSelcaoAtivoEspaco1.setMarginBottom(0);
        hboxSelcaoAtivoEspaco1.setSpacing(1);
        hboxSelcaoAtivoEspaco1.setFlexVflex("ftTrue");
        hboxSelcaoAtivoEspaco1.setFlexHflex("ftTrue");
        hboxSelcaoAtivoEspaco1.setScrollable(false);
        hboxSelcaoAtivoEspaco1.setBoxShadowConfigHorizontalLength(10);
        hboxSelcaoAtivoEspaco1.setBoxShadowConfigVerticalLength(10);
        hboxSelcaoAtivoEspaco1.setBoxShadowConfigBlurRadius(5);
        hboxSelcaoAtivoEspaco1.setBoxShadowConfigSpreadRadius(0);
        hboxSelcaoAtivoEspaco1.setBoxShadowConfigShadowColor("clBlack");
        hboxSelcaoAtivoEspaco1.setBoxShadowConfigOpacity(75);
        hboxSelcaoRemoto.addChildren(hboxSelcaoAtivoEspaco1);
        hboxSelcaoAtivoEspaco1.applyProperties();
    }

    public TFLabel lblSelecaoRemoto = new TFLabel();

    private void init_lblSelecaoRemoto() {
        lblSelecaoRemoto.setName("lblSelecaoRemoto");
        lblSelecaoRemoto.setLeft(16);
        lblSelecaoRemoto.setTop(0);
        lblSelecaoRemoto.setWidth(43);
        lblSelecaoRemoto.setHeight(14);
        lblSelecaoRemoto.setCaption("Remoto");
        lblSelecaoRemoto.setFontColor("clWindowText");
        lblSelecaoRemoto.setFontSize(-12);
        lblSelecaoRemoto.setFontName("Tahoma");
        lblSelecaoRemoto.setFontStyle("[]");
        lblSelecaoRemoto.setVerticalAlignment("taVerticalCenter");
        lblSelecaoRemoto.setWordBreak(false);
        hboxSelcaoRemoto.addChildren(lblSelecaoRemoto);
        lblSelecaoRemoto.applyProperties();
    }

    public TFVBox hboxSelcaoAtivoEspaco2 = new TFVBox();

    private void init_hboxSelcaoAtivoEspaco2() {
        hboxSelcaoAtivoEspaco2.setName("hboxSelcaoAtivoEspaco2");
        hboxSelcaoAtivoEspaco2.setLeft(59);
        hboxSelcaoAtivoEspaco2.setTop(0);
        hboxSelcaoAtivoEspaco2.setWidth(12);
        hboxSelcaoAtivoEspaco2.setHeight(19);
        hboxSelcaoAtivoEspaco2.setBorderStyle("stNone");
        hboxSelcaoAtivoEspaco2.setPaddingTop(0);
        hboxSelcaoAtivoEspaco2.setPaddingLeft(0);
        hboxSelcaoAtivoEspaco2.setPaddingRight(0);
        hboxSelcaoAtivoEspaco2.setPaddingBottom(0);
        hboxSelcaoAtivoEspaco2.setMarginTop(0);
        hboxSelcaoAtivoEspaco2.setMarginLeft(0);
        hboxSelcaoAtivoEspaco2.setMarginRight(0);
        hboxSelcaoAtivoEspaco2.setMarginBottom(0);
        hboxSelcaoAtivoEspaco2.setSpacing(1);
        hboxSelcaoAtivoEspaco2.setFlexVflex("ftTrue");
        hboxSelcaoAtivoEspaco2.setFlexHflex("ftTrue");
        hboxSelcaoAtivoEspaco2.setScrollable(false);
        hboxSelcaoAtivoEspaco2.setBoxShadowConfigHorizontalLength(10);
        hboxSelcaoAtivoEspaco2.setBoxShadowConfigVerticalLength(10);
        hboxSelcaoAtivoEspaco2.setBoxShadowConfigBlurRadius(5);
        hboxSelcaoAtivoEspaco2.setBoxShadowConfigSpreadRadius(0);
        hboxSelcaoAtivoEspaco2.setBoxShadowConfigShadowColor("clBlack");
        hboxSelcaoAtivoEspaco2.setBoxShadowConfigOpacity(75);
        hboxSelcaoRemoto.addChildren(hboxSelcaoAtivoEspaco2);
        hboxSelcaoAtivoEspaco2.applyProperties();
    }

    public TFHBox hboxSelcaoPresencial = new TFHBox();

    private void init_hboxSelcaoPresencial() {
        hboxSelcaoPresencial.setName("hboxSelcaoPresencial");
        hboxSelcaoPresencial.setLeft(80);
        hboxSelcaoPresencial.setTop(0);
        hboxSelcaoPresencial.setWidth(80);
        hboxSelcaoPresencial.setHeight(23);
        hboxSelcaoPresencial.setBorderStyle("stNone");
        hboxSelcaoPresencial.setPaddingTop(2);
        hboxSelcaoPresencial.setPaddingLeft(0);
        hboxSelcaoPresencial.setPaddingRight(0);
        hboxSelcaoPresencial.setPaddingBottom(0);
        hboxSelcaoPresencial.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxSelcaoPresencialClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "hboxSelcaoPresencial", "OnClick");
        });
        hboxSelcaoPresencial.setMarginTop(0);
        hboxSelcaoPresencial.setMarginLeft(0);
        hboxSelcaoPresencial.setMarginRight(0);
        hboxSelcaoPresencial.setMarginBottom(0);
        hboxSelcaoPresencial.setSpacing(0);
        hboxSelcaoPresencial.setFlexVflex("ftTrue");
        hboxSelcaoPresencial.setFlexHflex("ftFalse");
        hboxSelcaoPresencial.setScrollable(false);
        hboxSelcaoPresencial.setBoxShadowConfigHorizontalLength(10);
        hboxSelcaoPresencial.setBoxShadowConfigVerticalLength(10);
        hboxSelcaoPresencial.setBoxShadowConfigBlurRadius(5);
        hboxSelcaoPresencial.setBoxShadowConfigSpreadRadius(0);
        hboxSelcaoPresencial.setBoxShadowConfigShadowColor("clBlack");
        hboxSelcaoPresencial.setBoxShadowConfigOpacity(75);
        hboxSelcaoPresencial.setVAlign("tvTop");
        hboxControleRemotoPresencialInterno.addChildren(hboxSelcaoPresencial);
        hboxSelcaoPresencial.applyProperties();
    }

    public TFVBox hboxSelcaoPresencialEspaco1 = new TFVBox();

    private void init_hboxSelcaoPresencialEspaco1() {
        hboxSelcaoPresencialEspaco1.setName("hboxSelcaoPresencialEspaco1");
        hboxSelcaoPresencialEspaco1.setLeft(0);
        hboxSelcaoPresencialEspaco1.setTop(0);
        hboxSelcaoPresencialEspaco1.setWidth(12);
        hboxSelcaoPresencialEspaco1.setHeight(20);
        hboxSelcaoPresencialEspaco1.setBorderStyle("stNone");
        hboxSelcaoPresencialEspaco1.setPaddingTop(0);
        hboxSelcaoPresencialEspaco1.setPaddingLeft(0);
        hboxSelcaoPresencialEspaco1.setPaddingRight(0);
        hboxSelcaoPresencialEspaco1.setPaddingBottom(0);
        hboxSelcaoPresencialEspaco1.setMarginTop(0);
        hboxSelcaoPresencialEspaco1.setMarginLeft(0);
        hboxSelcaoPresencialEspaco1.setMarginRight(0);
        hboxSelcaoPresencialEspaco1.setMarginBottom(0);
        hboxSelcaoPresencialEspaco1.setSpacing(1);
        hboxSelcaoPresencialEspaco1.setFlexVflex("ftTrue");
        hboxSelcaoPresencialEspaco1.setFlexHflex("ftTrue");
        hboxSelcaoPresencialEspaco1.setScrollable(false);
        hboxSelcaoPresencialEspaco1.setBoxShadowConfigHorizontalLength(10);
        hboxSelcaoPresencialEspaco1.setBoxShadowConfigVerticalLength(10);
        hboxSelcaoPresencialEspaco1.setBoxShadowConfigBlurRadius(5);
        hboxSelcaoPresencialEspaco1.setBoxShadowConfigSpreadRadius(0);
        hboxSelcaoPresencialEspaco1.setBoxShadowConfigShadowColor("clBlack");
        hboxSelcaoPresencialEspaco1.setBoxShadowConfigOpacity(75);
        hboxSelcaoPresencial.addChildren(hboxSelcaoPresencialEspaco1);
        hboxSelcaoPresencialEspaco1.applyProperties();
    }

    public TFLabel lblSelecaoPresencial = new TFLabel();

    private void init_lblSelecaoPresencial() {
        lblSelecaoPresencial.setName("lblSelecaoPresencial");
        lblSelecaoPresencial.setLeft(12);
        lblSelecaoPresencial.setTop(0);
        lblSelecaoPresencial.setWidth(53);
        lblSelecaoPresencial.setHeight(14);
        lblSelecaoPresencial.setCaption("Presencial");
        lblSelecaoPresencial.setFontColor("clWindowText");
        lblSelecaoPresencial.setFontSize(-12);
        lblSelecaoPresencial.setFontName("Tahoma");
        lblSelecaoPresencial.setFontStyle("[]");
        lblSelecaoPresencial.setVerticalAlignment("taVerticalCenter");
        lblSelecaoPresencial.setWordBreak(false);
        hboxSelcaoPresencial.addChildren(lblSelecaoPresencial);
        lblSelecaoPresencial.applyProperties();
    }

    public TFVBox hboxSelcaoPresencialEspaco2 = new TFVBox();

    private void init_hboxSelcaoPresencialEspaco2() {
        hboxSelcaoPresencialEspaco2.setName("hboxSelcaoPresencialEspaco2");
        hboxSelcaoPresencialEspaco2.setLeft(65);
        hboxSelcaoPresencialEspaco2.setTop(0);
        hboxSelcaoPresencialEspaco2.setWidth(12);
        hboxSelcaoPresencialEspaco2.setHeight(19);
        hboxSelcaoPresencialEspaco2.setBorderStyle("stNone");
        hboxSelcaoPresencialEspaco2.setPaddingTop(0);
        hboxSelcaoPresencialEspaco2.setPaddingLeft(0);
        hboxSelcaoPresencialEspaco2.setPaddingRight(0);
        hboxSelcaoPresencialEspaco2.setPaddingBottom(0);
        hboxSelcaoPresencialEspaco2.setMarginTop(0);
        hboxSelcaoPresencialEspaco2.setMarginLeft(0);
        hboxSelcaoPresencialEspaco2.setMarginRight(0);
        hboxSelcaoPresencialEspaco2.setMarginBottom(0);
        hboxSelcaoPresencialEspaco2.setSpacing(1);
        hboxSelcaoPresencialEspaco2.setFlexVflex("ftTrue");
        hboxSelcaoPresencialEspaco2.setFlexHflex("ftTrue");
        hboxSelcaoPresencialEspaco2.setScrollable(false);
        hboxSelcaoPresencialEspaco2.setBoxShadowConfigHorizontalLength(10);
        hboxSelcaoPresencialEspaco2.setBoxShadowConfigVerticalLength(10);
        hboxSelcaoPresencialEspaco2.setBoxShadowConfigBlurRadius(5);
        hboxSelcaoPresencialEspaco2.setBoxShadowConfigSpreadRadius(0);
        hboxSelcaoPresencialEspaco2.setBoxShadowConfigShadowColor("clBlack");
        hboxSelcaoPresencialEspaco2.setBoxShadowConfigOpacity(75);
        hboxSelcaoPresencial.addChildren(hboxSelcaoPresencialEspaco2);
        hboxSelcaoPresencialEspaco2.applyProperties();
    }

    public TFHBox hboxControleRemotoPresencialEspaco2 = new TFHBox();

    private void init_hboxControleRemotoPresencialEspaco2() {
        hboxControleRemotoPresencialEspaco2.setName("hboxControleRemotoPresencialEspaco2");
        hboxControleRemotoPresencialEspaco2.setLeft(0);
        hboxControleRemotoPresencialEspaco2.setTop(37);
        hboxControleRemotoPresencialEspaco2.setWidth(76);
        hboxControleRemotoPresencialEspaco2.setHeight(8);
        hboxControleRemotoPresencialEspaco2.setBorderStyle("stNone");
        hboxControleRemotoPresencialEspaco2.setPaddingTop(0);
        hboxControleRemotoPresencialEspaco2.setPaddingLeft(0);
        hboxControleRemotoPresencialEspaco2.setPaddingRight(0);
        hboxControleRemotoPresencialEspaco2.setPaddingBottom(0);
        hboxControleRemotoPresencialEspaco2.setMarginTop(0);
        hboxControleRemotoPresencialEspaco2.setMarginLeft(0);
        hboxControleRemotoPresencialEspaco2.setMarginRight(0);
        hboxControleRemotoPresencialEspaco2.setMarginBottom(0);
        hboxControleRemotoPresencialEspaco2.setSpacing(1);
        hboxControleRemotoPresencialEspaco2.setFlexVflex("ftTrue");
        hboxControleRemotoPresencialEspaco2.setFlexHflex("ftTrue");
        hboxControleRemotoPresencialEspaco2.setScrollable(false);
        hboxControleRemotoPresencialEspaco2.setBoxShadowConfigHorizontalLength(10);
        hboxControleRemotoPresencialEspaco2.setBoxShadowConfigVerticalLength(10);
        hboxControleRemotoPresencialEspaco2.setBoxShadowConfigBlurRadius(5);
        hboxControleRemotoPresencialEspaco2.setBoxShadowConfigSpreadRadius(0);
        hboxControleRemotoPresencialEspaco2.setBoxShadowConfigShadowColor("clBlack");
        hboxControleRemotoPresencialEspaco2.setBoxShadowConfigOpacity(75);
        hboxControleRemotoPresencialEspaco2.setVAlign("tvTop");
        hboxControleRemotoPresencial.addChildren(hboxControleRemotoPresencialEspaco2);
        hboxControleRemotoPresencialEspaco2.applyProperties();
    }

    public TFHBox hboxStatusAssinaturaEspaco1 = new TFHBox();

    private void init_hboxStatusAssinaturaEspaco1() {
        hboxStatusAssinaturaEspaco1.setName("hboxStatusAssinaturaEspaco1");
        hboxStatusAssinaturaEspaco1.setLeft(420);
        hboxStatusAssinaturaEspaco1.setTop(0);
        hboxStatusAssinaturaEspaco1.setWidth(13);
        hboxStatusAssinaturaEspaco1.setHeight(24);
        hboxStatusAssinaturaEspaco1.setBorderStyle("stNone");
        hboxStatusAssinaturaEspaco1.setPaddingTop(0);
        hboxStatusAssinaturaEspaco1.setPaddingLeft(0);
        hboxStatusAssinaturaEspaco1.setPaddingRight(0);
        hboxStatusAssinaturaEspaco1.setPaddingBottom(0);
        hboxStatusAssinaturaEspaco1.setMarginTop(0);
        hboxStatusAssinaturaEspaco1.setMarginLeft(0);
        hboxStatusAssinaturaEspaco1.setMarginRight(0);
        hboxStatusAssinaturaEspaco1.setMarginBottom(0);
        hboxStatusAssinaturaEspaco1.setSpacing(1);
        hboxStatusAssinaturaEspaco1.setFlexVflex("ftFalse");
        hboxStatusAssinaturaEspaco1.setFlexHflex("ftTrue");
        hboxStatusAssinaturaEspaco1.setScrollable(false);
        hboxStatusAssinaturaEspaco1.setBoxShadowConfigHorizontalLength(10);
        hboxStatusAssinaturaEspaco1.setBoxShadowConfigVerticalLength(10);
        hboxStatusAssinaturaEspaco1.setBoxShadowConfigBlurRadius(5);
        hboxStatusAssinaturaEspaco1.setBoxShadowConfigSpreadRadius(0);
        hboxStatusAssinaturaEspaco1.setBoxShadowConfigShadowColor("clBlack");
        hboxStatusAssinaturaEspaco1.setBoxShadowConfigOpacity(75);
        hboxStatusAssinaturaEspaco1.setVAlign("tvTop");
        hboxBotoes.addChildren(hboxStatusAssinaturaEspaco1);
        hboxStatusAssinaturaEspaco1.applyProperties();
    }

    public TFVBox vBoxBtnAcaoLerQRCodePrincipal = new TFVBox();

    private void init_vBoxBtnAcaoLerQRCodePrincipal() {
        vBoxBtnAcaoLerQRCodePrincipal.setName("vBoxBtnAcaoLerQRCodePrincipal");
        vBoxBtnAcaoLerQRCodePrincipal.setLeft(433);
        vBoxBtnAcaoLerQRCodePrincipal.setTop(0);
        vBoxBtnAcaoLerQRCodePrincipal.setWidth(88);
        vBoxBtnAcaoLerQRCodePrincipal.setHeight(55);
        vBoxBtnAcaoLerQRCodePrincipal.setBorderStyle("stNone");
        vBoxBtnAcaoLerQRCodePrincipal.setPaddingTop(0);
        vBoxBtnAcaoLerQRCodePrincipal.setPaddingLeft(0);
        vBoxBtnAcaoLerQRCodePrincipal.setPaddingRight(0);
        vBoxBtnAcaoLerQRCodePrincipal.setPaddingBottom(0);
        vBoxBtnAcaoLerQRCodePrincipal.setMarginTop(0);
        vBoxBtnAcaoLerQRCodePrincipal.setMarginLeft(0);
        vBoxBtnAcaoLerQRCodePrincipal.setMarginRight(0);
        vBoxBtnAcaoLerQRCodePrincipal.setMarginBottom(0);
        vBoxBtnAcaoLerQRCodePrincipal.setSpacing(0);
        vBoxBtnAcaoLerQRCodePrincipal.setFlexVflex("ftTrue");
        vBoxBtnAcaoLerQRCodePrincipal.setFlexHflex("ftFalse");
        vBoxBtnAcaoLerQRCodePrincipal.setScrollable(false);
        vBoxBtnAcaoLerQRCodePrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnAcaoLerQRCodePrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxBtnAcaoLerQRCodePrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxBtnAcaoLerQRCodePrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnAcaoLerQRCodePrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnAcaoLerQRCodePrincipal.setBoxShadowConfigOpacity(75);
        hboxBotoes.addChildren(vBoxBtnAcaoLerQRCodePrincipal);
        vBoxBtnAcaoLerQRCodePrincipal.applyProperties();
    }

    public TFHBox vBoxBtnAcaoLerQRCodePrincipalEspaco1 = new TFHBox();

    private void init_vBoxBtnAcaoLerQRCodePrincipalEspaco1() {
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setName("vBoxBtnAcaoLerQRCodePrincipalEspaco1");
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setLeft(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setTop(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setWidth(76);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setHeight(8);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setBorderStyle("stNone");
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setPaddingTop(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setPaddingLeft(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setPaddingRight(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setPaddingBottom(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setVisible(false);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setMarginTop(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setMarginLeft(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setMarginRight(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setMarginBottom(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setSpacing(1);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setFlexVflex("ftTrue");
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setFlexHflex("ftTrue");
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setScrollable(false);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setBoxShadowConfigVerticalLength(10);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setBoxShadowConfigBlurRadius(5);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setBoxShadowConfigOpacity(75);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.setVAlign("tvTop");
        vBoxBtnAcaoLerQRCodePrincipal.addChildren(vBoxBtnAcaoLerQRCodePrincipalEspaco1);
        vBoxBtnAcaoLerQRCodePrincipalEspaco1.applyProperties();
    }

    public TFPanelButton FPanelBtnAcaoLerQRCode = new TFPanelButton();

    private void init_FPanelBtnAcaoLerQRCode() {
        FPanelBtnAcaoLerQRCode.setName("FPanelBtnAcaoLerQRCode");
        FPanelBtnAcaoLerQRCode.setLeft(0);
        FPanelBtnAcaoLerQRCode.setTop(9);
        FPanelBtnAcaoLerQRCode.setWidth(80);
        FPanelBtnAcaoLerQRCode.setHeight(45);
        FPanelBtnAcaoLerQRCode.setBorderStyle("stNone");
        FPanelBtnAcaoLerQRCode.setPaddingTop(0);
        FPanelBtnAcaoLerQRCode.setPaddingLeft(0);
        FPanelBtnAcaoLerQRCode.setPaddingRight(0);
        FPanelBtnAcaoLerQRCode.setPaddingBottom(0);
        FPanelBtnAcaoLerQRCode.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FPanelBtnAcaoLerQRCodeClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "FPanelBtnAcaoLerQRCode", "OnClick");
        });
        FPanelBtnAcaoLerQRCode.setMarginTop(0);
        FPanelBtnAcaoLerQRCode.setMarginLeft(0);
        FPanelBtnAcaoLerQRCode.setMarginRight(0);
        FPanelBtnAcaoLerQRCode.setMarginBottom(0);
        FPanelBtnAcaoLerQRCode.setBoxShadowConfigHorizontalLength(0);
        FPanelBtnAcaoLerQRCode.setBoxShadowConfigVerticalLength(7);
        FPanelBtnAcaoLerQRCode.setBoxShadowConfigBlurRadius(10);
        FPanelBtnAcaoLerQRCode.setBoxShadowConfigSpreadRadius(-5);
        FPanelBtnAcaoLerQRCode.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnAcaoLerQRCode.setBoxShadowConfigOpacity(75);
        FPanelBtnAcaoLerQRCode.setBorderRadiusTopLeft(0);
        FPanelBtnAcaoLerQRCode.setBorderRadiusTopRight(0);
        FPanelBtnAcaoLerQRCode.setBorderRadiusBottomRight(0);
        FPanelBtnAcaoLerQRCode.setBorderRadiusBottomLeft(0);
        FPanelBtnAcaoLerQRCode.setToggle(true);
        FPanelBtnAcaoLerQRCode.setToggleColor("clBtnFace");
        vBoxBtnAcaoLerQRCodePrincipal.addChildren(FPanelBtnAcaoLerQRCode);

        TFFastDesignCmpItems FPanelBtnAcaoLerQRCodeItems = new TFFastDesignCmpItems();
        FPanelBtnAcaoLerQRCodeItems.setName("FPanelBtnAcaoLerQRCodeItems");
        FPanelBtnAcaoLerQRCode.addChildren(FPanelBtnAcaoLerQRCodeItems);
        FPanelBtnAcaoLerQRCodeItems.applyProperties();

        TFPanelButtonItem FPanelBtnAcaoLerQRCodeVbox = new TFPanelButtonItem();
        FPanelBtnAcaoLerQRCodeVbox.setName("FPanelBtnAcaoLerQRCodeVbox");
        FPanelBtnAcaoLerQRCodeVbox.setCaption("FPanelButtonItem1");
        FPanelBtnAcaoLerQRCodeVbox.setItemType("itVBox");
        FPanelBtnAcaoLerQRCodeVbox.setFontColor("clWindowText");
        FPanelBtnAcaoLerQRCodeVbox.setFontSize(-13);
        FPanelBtnAcaoLerQRCodeVbox.setFontName("Tahoma");
        FPanelBtnAcaoLerQRCodeVbox.setFontStyle("[]");
        FPanelBtnAcaoLerQRCodeVbox.setItemAlign("iaCenter");
        FPanelBtnAcaoLerQRCodeVbox.setFlex(true);
        FPanelBtnAcaoLerQRCodeVbox.setItemVAlign("ivaTop");
        FPanelBtnAcaoLerQRCodeVbox.setPaddingTop(0);
        FPanelBtnAcaoLerQRCodeVbox.setPaddingLeft(0);
        FPanelBtnAcaoLerQRCodeVbox.setPaddingRight(0);
        FPanelBtnAcaoLerQRCodeVbox.setPaddingBottom(0);
        FPanelBtnAcaoLerQRCodeVbox.setMarginTop(0);
        FPanelBtnAcaoLerQRCodeVbox.setMarginLeft(0);
        FPanelBtnAcaoLerQRCodeVbox.setMarginRight(0);
        FPanelBtnAcaoLerQRCodeVbox.setMarginBottom(0);
        FPanelBtnAcaoLerQRCodeVbox.setBorderStyle("stNone");
        FPanelBtnAcaoLerQRCodeVbox.setBoxShadowConfigHorizontalLength(10);
        FPanelBtnAcaoLerQRCodeVbox.setBoxShadowConfigVerticalLength(10);
        FPanelBtnAcaoLerQRCodeVbox.setBoxShadowConfigBlurRadius(5);
        FPanelBtnAcaoLerQRCodeVbox.setBoxShadowConfigSpreadRadius(0);
        FPanelBtnAcaoLerQRCodeVbox.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnAcaoLerQRCodeVbox.setBoxShadowConfigOpacity(75);
        FPanelBtnAcaoLerQRCodeVbox.setBorderRadiusTopLeft(0);
        FPanelBtnAcaoLerQRCodeVbox.setBorderRadiusTopRight(0);
        FPanelBtnAcaoLerQRCodeVbox.setBorderRadiusBottomRight(0);
        FPanelBtnAcaoLerQRCodeVbox.setBorderRadiusBottomLeft(0);
        FPanelBtnAcaoLerQRCodeVbox.setColor("clBtnFace");
        FPanelBtnAcaoLerQRCodeVbox.setWordBreak(false);
        FPanelBtnAcaoLerQRCodeItems.addChildren(FPanelBtnAcaoLerQRCodeVbox);
        FPanelBtnAcaoLerQRCodeVbox.applyProperties();

        TFPanelButtonItem FPanelBtnAcaoLerQRCodeLbl = new TFPanelButtonItem();
        FPanelBtnAcaoLerQRCodeLbl.setName("FPanelBtnAcaoLerQRCodeLbl");
        FPanelBtnAcaoLerQRCodeLbl.setCaption("Ler QRCode");
        FPanelBtnAcaoLerQRCodeLbl.setItemType("itLabel");
        FPanelBtnAcaoLerQRCodeLbl.setFontColor("clGray");
        FPanelBtnAcaoLerQRCodeLbl.setFontSize(-12);
        FPanelBtnAcaoLerQRCodeLbl.setFontName("Tahoma");
        FPanelBtnAcaoLerQRCodeLbl.setFontStyle("[fsBold]");
        FPanelBtnAcaoLerQRCodeLbl.setItemAlign("iaCenter");
        FPanelBtnAcaoLerQRCodeLbl.setFlex(false);
        FPanelBtnAcaoLerQRCodeLbl.setItemVAlign("ivaTop");
        FPanelBtnAcaoLerQRCodeLbl.setPaddingTop(0);
        FPanelBtnAcaoLerQRCodeLbl.setPaddingLeft(0);
        FPanelBtnAcaoLerQRCodeLbl.setPaddingRight(0);
        FPanelBtnAcaoLerQRCodeLbl.setPaddingBottom(0);
        FPanelBtnAcaoLerQRCodeLbl.setMarginTop(0);
        FPanelBtnAcaoLerQRCodeLbl.setMarginLeft(0);
        FPanelBtnAcaoLerQRCodeLbl.setMarginRight(0);
        FPanelBtnAcaoLerQRCodeLbl.setMarginBottom(0);
        FPanelBtnAcaoLerQRCodeLbl.setBorderStyle("stNone");
        FPanelBtnAcaoLerQRCodeLbl.setBoxShadowConfigHorizontalLength(10);
        FPanelBtnAcaoLerQRCodeLbl.setBoxShadowConfigVerticalLength(10);
        FPanelBtnAcaoLerQRCodeLbl.setBoxShadowConfigBlurRadius(5);
        FPanelBtnAcaoLerQRCodeLbl.setBoxShadowConfigSpreadRadius(0);
        FPanelBtnAcaoLerQRCodeLbl.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnAcaoLerQRCodeLbl.setBoxShadowConfigOpacity(75);
        FPanelBtnAcaoLerQRCodeLbl.setBorderRadiusTopLeft(0);
        FPanelBtnAcaoLerQRCodeLbl.setBorderRadiusTopRight(0);
        FPanelBtnAcaoLerQRCodeLbl.setBorderRadiusBottomRight(0);
        FPanelBtnAcaoLerQRCodeLbl.setBorderRadiusBottomLeft(0);
        FPanelBtnAcaoLerQRCodeLbl.setColor("clBtnFace");
        FPanelBtnAcaoLerQRCodeLbl.setWordBreak(false);
        FPanelBtnAcaoLerQRCodeVbox.addChildren(FPanelBtnAcaoLerQRCodeLbl);
        FPanelBtnAcaoLerQRCodeLbl.applyProperties();

        TFPanelButtonItem FPanelBtnAcaoLerQRCodeImg = new TFPanelButtonItem();
        FPanelBtnAcaoLerQRCodeImg.setName("FPanelBtnAcaoLerQRCodeImg");
        FPanelBtnAcaoLerQRCodeImg.setCaption("FPanelButtonItem2");
        FPanelBtnAcaoLerQRCodeImg.setItemType("itImage");
        FPanelBtnAcaoLerQRCodeImg.setFontColor("clWindowText");
        FPanelBtnAcaoLerQRCodeImg.setFontSize(-13);
        FPanelBtnAcaoLerQRCodeImg.setFontName("Tahoma");
        FPanelBtnAcaoLerQRCodeImg.setFontStyle("[]");
        FPanelBtnAcaoLerQRCodeImg.setItemAlign("iaCenter");
        FPanelBtnAcaoLerQRCodeImg.setImageSrc("/images/crmservice386027.png");
        FPanelBtnAcaoLerQRCodeImg.setFlex(false);
        FPanelBtnAcaoLerQRCodeImg.setWidth(27);
        FPanelBtnAcaoLerQRCodeImg.setHeight(27);
        FPanelBtnAcaoLerQRCodeImg.setItemVAlign("ivaTop");
        FPanelBtnAcaoLerQRCodeImg.setPaddingTop(0);
        FPanelBtnAcaoLerQRCodeImg.setPaddingLeft(0);
        FPanelBtnAcaoLerQRCodeImg.setPaddingRight(0);
        FPanelBtnAcaoLerQRCodeImg.setPaddingBottom(0);
        FPanelBtnAcaoLerQRCodeImg.setMarginTop(0);
        FPanelBtnAcaoLerQRCodeImg.setMarginLeft(0);
        FPanelBtnAcaoLerQRCodeImg.setMarginRight(0);
        FPanelBtnAcaoLerQRCodeImg.setMarginBottom(0);
        FPanelBtnAcaoLerQRCodeImg.setBorderStyle("stNone");
        FPanelBtnAcaoLerQRCodeImg.setBoxShadowConfigHorizontalLength(10);
        FPanelBtnAcaoLerQRCodeImg.setBoxShadowConfigVerticalLength(10);
        FPanelBtnAcaoLerQRCodeImg.setBoxShadowConfigBlurRadius(5);
        FPanelBtnAcaoLerQRCodeImg.setBoxShadowConfigSpreadRadius(0);
        FPanelBtnAcaoLerQRCodeImg.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnAcaoLerQRCodeImg.setBoxShadowConfigOpacity(75);
        FPanelBtnAcaoLerQRCodeImg.setBorderRadiusTopLeft(0);
        FPanelBtnAcaoLerQRCodeImg.setBorderRadiusTopRight(0);
        FPanelBtnAcaoLerQRCodeImg.setBorderRadiusBottomRight(0);
        FPanelBtnAcaoLerQRCodeImg.setBorderRadiusBottomLeft(0);
        FPanelBtnAcaoLerQRCodeImg.setColor("clBtnFace");
        FPanelBtnAcaoLerQRCodeImg.setWordBreak(false);
        FPanelBtnAcaoLerQRCodeVbox.addChildren(FPanelBtnAcaoLerQRCodeImg);
        FPanelBtnAcaoLerQRCodeImg.applyProperties();
        FPanelBtnAcaoLerQRCode.applyProperties();
    }

    public TFHBox vBoxBtnAcaoLerQRCodePrincipalEspaco2 = new TFHBox();

    private void init_vBoxBtnAcaoLerQRCodePrincipalEspaco2() {
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setName("vBoxBtnAcaoLerQRCodePrincipalEspaco2");
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setLeft(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setTop(55);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setWidth(76);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setHeight(8);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setBorderStyle("stNone");
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setPaddingTop(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setPaddingLeft(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setPaddingRight(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setPaddingBottom(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setVisible(false);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setMarginTop(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setMarginLeft(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setMarginRight(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setMarginBottom(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setSpacing(1);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setFlexVflex("ftTrue");
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setFlexHflex("ftTrue");
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setScrollable(false);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setBoxShadowConfigVerticalLength(10);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setBoxShadowConfigBlurRadius(5);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setBoxShadowConfigOpacity(75);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.setVAlign("tvTop");
        vBoxBtnAcaoLerQRCodePrincipal.addChildren(vBoxBtnAcaoLerQRCodePrincipalEspaco2);
        vBoxBtnAcaoLerQRCodePrincipalEspaco2.applyProperties();
    }

    public TFHBox hboxStatusAssinaturaEspaco2 = new TFHBox();

    private void init_hboxStatusAssinaturaEspaco2() {
        hboxStatusAssinaturaEspaco2.setName("hboxStatusAssinaturaEspaco2");
        hboxStatusAssinaturaEspaco2.setLeft(521);
        hboxStatusAssinaturaEspaco2.setTop(0);
        hboxStatusAssinaturaEspaco2.setWidth(13);
        hboxStatusAssinaturaEspaco2.setHeight(24);
        hboxStatusAssinaturaEspaco2.setBorderStyle("stNone");
        hboxStatusAssinaturaEspaco2.setPaddingTop(0);
        hboxStatusAssinaturaEspaco2.setPaddingLeft(0);
        hboxStatusAssinaturaEspaco2.setPaddingRight(0);
        hboxStatusAssinaturaEspaco2.setPaddingBottom(0);
        hboxStatusAssinaturaEspaco2.setMarginTop(0);
        hboxStatusAssinaturaEspaco2.setMarginLeft(0);
        hboxStatusAssinaturaEspaco2.setMarginRight(0);
        hboxStatusAssinaturaEspaco2.setMarginBottom(0);
        hboxStatusAssinaturaEspaco2.setSpacing(1);
        hboxStatusAssinaturaEspaco2.setFlexVflex("ftFalse");
        hboxStatusAssinaturaEspaco2.setFlexHflex("ftTrue");
        hboxStatusAssinaturaEspaco2.setScrollable(false);
        hboxStatusAssinaturaEspaco2.setBoxShadowConfigHorizontalLength(10);
        hboxStatusAssinaturaEspaco2.setBoxShadowConfigVerticalLength(10);
        hboxStatusAssinaturaEspaco2.setBoxShadowConfigBlurRadius(5);
        hboxStatusAssinaturaEspaco2.setBoxShadowConfigSpreadRadius(0);
        hboxStatusAssinaturaEspaco2.setBoxShadowConfigShadowColor("clBlack");
        hboxStatusAssinaturaEspaco2.setBoxShadowConfigOpacity(75);
        hboxStatusAssinaturaEspaco2.setVAlign("tvTop");
        hboxBotoes.addChildren(hboxStatusAssinaturaEspaco2);
        hboxStatusAssinaturaEspaco2.applyProperties();
    }

    public TFVBox vBoxBtnAcaoBaixarDocumentoPrincipal = new TFVBox();

    private void init_vBoxBtnAcaoBaixarDocumentoPrincipal() {
        vBoxBtnAcaoBaixarDocumentoPrincipal.setName("vBoxBtnAcaoBaixarDocumentoPrincipal");
        vBoxBtnAcaoBaixarDocumentoPrincipal.setLeft(534);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setTop(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setWidth(88);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setHeight(55);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setBorderStyle("stNone");
        vBoxBtnAcaoBaixarDocumentoPrincipal.setPaddingTop(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setPaddingLeft(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setPaddingRight(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setPaddingBottom(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setMarginTop(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setMarginLeft(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setMarginRight(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setMarginBottom(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setSpacing(4);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setFlexVflex("ftTrue");
        vBoxBtnAcaoBaixarDocumentoPrincipal.setFlexHflex("ftFalse");
        vBoxBtnAcaoBaixarDocumentoPrincipal.setScrollable(false);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnAcaoBaixarDocumentoPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnAcaoBaixarDocumentoPrincipal.setBoxShadowConfigOpacity(75);
        hboxBotoes.addChildren(vBoxBtnAcaoBaixarDocumentoPrincipal);
        vBoxBtnAcaoBaixarDocumentoPrincipal.applyProperties();
    }

    public TFHBox vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1 = new TFHBox();

    private void init_vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1() {
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setName("vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setLeft(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setTop(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setWidth(76);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setHeight(8);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setBorderStyle("stNone");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setPaddingTop(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setPaddingLeft(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setPaddingRight(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setPaddingBottom(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setMarginTop(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setMarginLeft(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setMarginRight(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setMarginBottom(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setSpacing(1);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setFlexVflex("ftTrue");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setFlexHflex("ftTrue");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setScrollable(false);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setBoxShadowConfigVerticalLength(10);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setBoxShadowConfigBlurRadius(5);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setBoxShadowConfigOpacity(75);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.setVAlign("tvTop");
        vBoxBtnAcaoBaixarDocumentoPrincipal.addChildren(vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1.applyProperties();
    }

    public TFPanelButton FPanelBtnAcaoBaixarDocumento = new TFPanelButton();

    private void init_FPanelBtnAcaoBaixarDocumento() {
        FPanelBtnAcaoBaixarDocumento.setName("FPanelBtnAcaoBaixarDocumento");
        FPanelBtnAcaoBaixarDocumento.setLeft(0);
        FPanelBtnAcaoBaixarDocumento.setTop(9);
        FPanelBtnAcaoBaixarDocumento.setWidth(73);
        FPanelBtnAcaoBaixarDocumento.setHeight(45);
        FPanelBtnAcaoBaixarDocumento.setBorderStyle("stNone");
        FPanelBtnAcaoBaixarDocumento.setPaddingTop(0);
        FPanelBtnAcaoBaixarDocumento.setPaddingLeft(0);
        FPanelBtnAcaoBaixarDocumento.setPaddingRight(0);
        FPanelBtnAcaoBaixarDocumento.setPaddingBottom(0);
        FPanelBtnAcaoBaixarDocumento.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FPanelBtnAcaoBaixarDocumentoClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "FPanelBtnAcaoBaixarDocumento", "OnClick");
        });
        FPanelBtnAcaoBaixarDocumento.setMarginTop(0);
        FPanelBtnAcaoBaixarDocumento.setMarginLeft(0);
        FPanelBtnAcaoBaixarDocumento.setMarginRight(0);
        FPanelBtnAcaoBaixarDocumento.setMarginBottom(0);
        FPanelBtnAcaoBaixarDocumento.setBoxShadowConfigHorizontalLength(0);
        FPanelBtnAcaoBaixarDocumento.setBoxShadowConfigVerticalLength(7);
        FPanelBtnAcaoBaixarDocumento.setBoxShadowConfigBlurRadius(10);
        FPanelBtnAcaoBaixarDocumento.setBoxShadowConfigSpreadRadius(-5);
        FPanelBtnAcaoBaixarDocumento.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnAcaoBaixarDocumento.setBoxShadowConfigOpacity(75);
        FPanelBtnAcaoBaixarDocumento.setBorderRadiusTopLeft(0);
        FPanelBtnAcaoBaixarDocumento.setBorderRadiusTopRight(0);
        FPanelBtnAcaoBaixarDocumento.setBorderRadiusBottomRight(0);
        FPanelBtnAcaoBaixarDocumento.setBorderRadiusBottomLeft(0);
        FPanelBtnAcaoBaixarDocumento.setToggle(true);
        FPanelBtnAcaoBaixarDocumento.setToggleColor("clBtnFace");
        vBoxBtnAcaoBaixarDocumentoPrincipal.addChildren(FPanelBtnAcaoBaixarDocumento);

        TFFastDesignCmpItems FPanelBtnAcaoBaixarDocumentoItems = new TFFastDesignCmpItems();
        FPanelBtnAcaoBaixarDocumentoItems.setName("FPanelBtnAcaoBaixarDocumentoItems");
        FPanelBtnAcaoBaixarDocumento.addChildren(FPanelBtnAcaoBaixarDocumentoItems);
        FPanelBtnAcaoBaixarDocumentoItems.applyProperties();

        TFPanelButtonItem FPanelBtnAcaoBaixarDocumentoVbox = new TFPanelButtonItem();
        FPanelBtnAcaoBaixarDocumentoVbox.setName("FPanelBtnAcaoBaixarDocumentoVbox");
        FPanelBtnAcaoBaixarDocumentoVbox.setCaption("FPanelButtonItem1");
        FPanelBtnAcaoBaixarDocumentoVbox.setItemType("itVBox");
        FPanelBtnAcaoBaixarDocumentoVbox.setFontColor("clWindowText");
        FPanelBtnAcaoBaixarDocumentoVbox.setFontSize(-13);
        FPanelBtnAcaoBaixarDocumentoVbox.setFontName("Tahoma");
        FPanelBtnAcaoBaixarDocumentoVbox.setFontStyle("[]");
        FPanelBtnAcaoBaixarDocumentoVbox.setItemAlign("iaLeft");
        FPanelBtnAcaoBaixarDocumentoVbox.setFlex(true);
        FPanelBtnAcaoBaixarDocumentoVbox.setItemVAlign("ivaTop");
        FPanelBtnAcaoBaixarDocumentoVbox.setPaddingTop(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setPaddingLeft(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setPaddingRight(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setPaddingBottom(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setMarginTop(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setMarginLeft(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setMarginRight(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setMarginBottom(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setBorderStyle("stNone");
        FPanelBtnAcaoBaixarDocumentoVbox.setBoxShadowConfigHorizontalLength(10);
        FPanelBtnAcaoBaixarDocumentoVbox.setBoxShadowConfigVerticalLength(10);
        FPanelBtnAcaoBaixarDocumentoVbox.setBoxShadowConfigBlurRadius(5);
        FPanelBtnAcaoBaixarDocumentoVbox.setBoxShadowConfigSpreadRadius(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnAcaoBaixarDocumentoVbox.setBoxShadowConfigOpacity(75);
        FPanelBtnAcaoBaixarDocumentoVbox.setBorderRadiusTopLeft(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setBorderRadiusTopRight(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setBorderRadiusBottomRight(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setBorderRadiusBottomLeft(0);
        FPanelBtnAcaoBaixarDocumentoVbox.setColor("clBtnFace");
        FPanelBtnAcaoBaixarDocumentoVbox.setWordBreak(false);
        FPanelBtnAcaoBaixarDocumentoItems.addChildren(FPanelBtnAcaoBaixarDocumentoVbox);
        FPanelBtnAcaoBaixarDocumentoVbox.applyProperties();

        TFPanelButtonItem FPanelBtnAcaoBaixarDocumentoLbl = new TFPanelButtonItem();
        FPanelBtnAcaoBaixarDocumentoLbl.setName("FPanelBtnAcaoBaixarDocumentoLbl");
        FPanelBtnAcaoBaixarDocumentoLbl.setCaption("Abrir Doc.");
        FPanelBtnAcaoBaixarDocumentoLbl.setItemType("itLabel");
        FPanelBtnAcaoBaixarDocumentoLbl.setFontColor("clGray");
        FPanelBtnAcaoBaixarDocumentoLbl.setFontSize(-12);
        FPanelBtnAcaoBaixarDocumentoLbl.setFontName("Tahoma");
        FPanelBtnAcaoBaixarDocumentoLbl.setFontStyle("[fsBold]");
        FPanelBtnAcaoBaixarDocumentoLbl.setItemAlign("iaCenter");
        FPanelBtnAcaoBaixarDocumentoLbl.setFlex(false);
        FPanelBtnAcaoBaixarDocumentoLbl.setItemVAlign("ivaTop");
        FPanelBtnAcaoBaixarDocumentoLbl.setPaddingTop(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setPaddingLeft(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setPaddingRight(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setPaddingBottom(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setMarginTop(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setMarginLeft(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setMarginRight(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setMarginBottom(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setBorderStyle("stNone");
        FPanelBtnAcaoBaixarDocumentoLbl.setBoxShadowConfigHorizontalLength(10);
        FPanelBtnAcaoBaixarDocumentoLbl.setBoxShadowConfigVerticalLength(10);
        FPanelBtnAcaoBaixarDocumentoLbl.setBoxShadowConfigBlurRadius(5);
        FPanelBtnAcaoBaixarDocumentoLbl.setBoxShadowConfigSpreadRadius(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnAcaoBaixarDocumentoLbl.setBoxShadowConfigOpacity(75);
        FPanelBtnAcaoBaixarDocumentoLbl.setBorderRadiusTopLeft(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setBorderRadiusTopRight(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setBorderRadiusBottomRight(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setBorderRadiusBottomLeft(0);
        FPanelBtnAcaoBaixarDocumentoLbl.setColor("clBtnFace");
        FPanelBtnAcaoBaixarDocumentoLbl.setWordBreak(false);
        FPanelBtnAcaoBaixarDocumentoVbox.addChildren(FPanelBtnAcaoBaixarDocumentoLbl);
        FPanelBtnAcaoBaixarDocumentoLbl.applyProperties();

        TFPanelButtonItem FPanelBtnAcaoBaixarDocumentoImg = new TFPanelButtonItem();
        FPanelBtnAcaoBaixarDocumentoImg.setName("FPanelBtnAcaoBaixarDocumentoImg");
        FPanelBtnAcaoBaixarDocumentoImg.setCaption("FPanelButtonItem2");
        FPanelBtnAcaoBaixarDocumentoImg.setItemType("itImage");
        FPanelBtnAcaoBaixarDocumentoImg.setFontColor("clWindowText");
        FPanelBtnAcaoBaixarDocumentoImg.setFontSize(-13);
        FPanelBtnAcaoBaixarDocumentoImg.setFontName("Tahoma");
        FPanelBtnAcaoBaixarDocumentoImg.setFontStyle("[]");
        FPanelBtnAcaoBaixarDocumentoImg.setItemAlign("iaCenter");
        FPanelBtnAcaoBaixarDocumentoImg.setImageSrc("/images/crmservice4600365.png");
        FPanelBtnAcaoBaixarDocumentoImg.setFlex(false);
        FPanelBtnAcaoBaixarDocumentoImg.setWidth(27);
        FPanelBtnAcaoBaixarDocumentoImg.setHeight(27);
        FPanelBtnAcaoBaixarDocumentoImg.setItemVAlign("ivaTop");
        FPanelBtnAcaoBaixarDocumentoImg.setPaddingTop(0);
        FPanelBtnAcaoBaixarDocumentoImg.setPaddingLeft(0);
        FPanelBtnAcaoBaixarDocumentoImg.setPaddingRight(0);
        FPanelBtnAcaoBaixarDocumentoImg.setPaddingBottom(0);
        FPanelBtnAcaoBaixarDocumentoImg.setMarginTop(0);
        FPanelBtnAcaoBaixarDocumentoImg.setMarginLeft(0);
        FPanelBtnAcaoBaixarDocumentoImg.setMarginRight(0);
        FPanelBtnAcaoBaixarDocumentoImg.setMarginBottom(0);
        FPanelBtnAcaoBaixarDocumentoImg.setBorderStyle("stNone");
        FPanelBtnAcaoBaixarDocumentoImg.setBoxShadowConfigHorizontalLength(10);
        FPanelBtnAcaoBaixarDocumentoImg.setBoxShadowConfigVerticalLength(10);
        FPanelBtnAcaoBaixarDocumentoImg.setBoxShadowConfigBlurRadius(5);
        FPanelBtnAcaoBaixarDocumentoImg.setBoxShadowConfigSpreadRadius(0);
        FPanelBtnAcaoBaixarDocumentoImg.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnAcaoBaixarDocumentoImg.setBoxShadowConfigOpacity(75);
        FPanelBtnAcaoBaixarDocumentoImg.setBorderRadiusTopLeft(0);
        FPanelBtnAcaoBaixarDocumentoImg.setBorderRadiusTopRight(0);
        FPanelBtnAcaoBaixarDocumentoImg.setBorderRadiusBottomRight(0);
        FPanelBtnAcaoBaixarDocumentoImg.setBorderRadiusBottomLeft(0);
        FPanelBtnAcaoBaixarDocumentoImg.setColor("clBtnFace");
        FPanelBtnAcaoBaixarDocumentoImg.setWordBreak(false);
        FPanelBtnAcaoBaixarDocumentoVbox.addChildren(FPanelBtnAcaoBaixarDocumentoImg);
        FPanelBtnAcaoBaixarDocumentoImg.applyProperties();
        FPanelBtnAcaoBaixarDocumento.applyProperties();
    }

    public TFHBox vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2 = new TFHBox();

    private void init_vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2() {
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setName("vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setLeft(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setTop(55);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setWidth(76);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setHeight(8);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setBorderStyle("stNone");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setPaddingTop(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setPaddingLeft(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setPaddingRight(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setPaddingBottom(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setMarginTop(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setMarginLeft(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setMarginRight(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setMarginBottom(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setSpacing(1);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setFlexVflex("ftTrue");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setFlexHflex("ftTrue");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setScrollable(false);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setBoxShadowConfigVerticalLength(10);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setBoxShadowConfigBlurRadius(5);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setBoxShadowConfigOpacity(75);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.setVAlign("tvTop");
        vBoxBtnAcaoBaixarDocumentoPrincipal.addChildren(vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2);
        vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(622);
        FHBox3.setTop(0);
        FHBox3.setWidth(13);
        FHBox3.setHeight(24);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        hboxBotoes.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFVBox vBoxlStatusAssinaturaPrincipal = new TFVBox();

    private void init_vBoxlStatusAssinaturaPrincipal() {
        vBoxlStatusAssinaturaPrincipal.setName("vBoxlStatusAssinaturaPrincipal");
        vBoxlStatusAssinaturaPrincipal.setLeft(635);
        vBoxlStatusAssinaturaPrincipal.setTop(0);
        vBoxlStatusAssinaturaPrincipal.setWidth(86);
        vBoxlStatusAssinaturaPrincipal.setHeight(55);
        vBoxlStatusAssinaturaPrincipal.setBorderStyle("stNone");
        vBoxlStatusAssinaturaPrincipal.setPaddingTop(0);
        vBoxlStatusAssinaturaPrincipal.setPaddingLeft(0);
        vBoxlStatusAssinaturaPrincipal.setPaddingRight(0);
        vBoxlStatusAssinaturaPrincipal.setPaddingBottom(0);
        vBoxlStatusAssinaturaPrincipal.setMarginTop(0);
        vBoxlStatusAssinaturaPrincipal.setMarginLeft(0);
        vBoxlStatusAssinaturaPrincipal.setMarginRight(0);
        vBoxlStatusAssinaturaPrincipal.setMarginBottom(0);
        vBoxlStatusAssinaturaPrincipal.setSpacing(4);
        vBoxlStatusAssinaturaPrincipal.setFlexVflex("ftTrue");
        vBoxlStatusAssinaturaPrincipal.setFlexHflex("ftMin");
        vBoxlStatusAssinaturaPrincipal.setScrollable(false);
        vBoxlStatusAssinaturaPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxlStatusAssinaturaPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxlStatusAssinaturaPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxlStatusAssinaturaPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxlStatusAssinaturaPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxlStatusAssinaturaPrincipal.setBoxShadowConfigOpacity(75);
        hboxBotoes.addChildren(vBoxlStatusAssinaturaPrincipal);
        vBoxlStatusAssinaturaPrincipal.applyProperties();
    }

    public TFHBox vBoxlStatusAssinaturaPrincipal1 = new TFHBox();

    private void init_vBoxlStatusAssinaturaPrincipal1() {
        vBoxlStatusAssinaturaPrincipal1.setName("vBoxlStatusAssinaturaPrincipal1");
        vBoxlStatusAssinaturaPrincipal1.setLeft(0);
        vBoxlStatusAssinaturaPrincipal1.setTop(0);
        vBoxlStatusAssinaturaPrincipal1.setWidth(76);
        vBoxlStatusAssinaturaPrincipal1.setHeight(8);
        vBoxlStatusAssinaturaPrincipal1.setBorderStyle("stNone");
        vBoxlStatusAssinaturaPrincipal1.setPaddingTop(0);
        vBoxlStatusAssinaturaPrincipal1.setPaddingLeft(0);
        vBoxlStatusAssinaturaPrincipal1.setPaddingRight(0);
        vBoxlStatusAssinaturaPrincipal1.setPaddingBottom(0);
        vBoxlStatusAssinaturaPrincipal1.setMarginTop(0);
        vBoxlStatusAssinaturaPrincipal1.setMarginLeft(0);
        vBoxlStatusAssinaturaPrincipal1.setMarginRight(0);
        vBoxlStatusAssinaturaPrincipal1.setMarginBottom(0);
        vBoxlStatusAssinaturaPrincipal1.setSpacing(1);
        vBoxlStatusAssinaturaPrincipal1.setFlexVflex("ftTrue");
        vBoxlStatusAssinaturaPrincipal1.setFlexHflex("ftTrue");
        vBoxlStatusAssinaturaPrincipal1.setScrollable(false);
        vBoxlStatusAssinaturaPrincipal1.setBoxShadowConfigHorizontalLength(10);
        vBoxlStatusAssinaturaPrincipal1.setBoxShadowConfigVerticalLength(10);
        vBoxlStatusAssinaturaPrincipal1.setBoxShadowConfigBlurRadius(5);
        vBoxlStatusAssinaturaPrincipal1.setBoxShadowConfigSpreadRadius(0);
        vBoxlStatusAssinaturaPrincipal1.setBoxShadowConfigShadowColor("clBlack");
        vBoxlStatusAssinaturaPrincipal1.setBoxShadowConfigOpacity(75);
        vBoxlStatusAssinaturaPrincipal1.setVAlign("tvTop");
        vBoxlStatusAssinaturaPrincipal.addChildren(vBoxlStatusAssinaturaPrincipal1);
        vBoxlStatusAssinaturaPrincipal1.applyProperties();
    }

    public TFPanelButton FPanelStatusAssinatura = new TFPanelButton();

    private void init_FPanelStatusAssinatura() {
        FPanelStatusAssinatura.setName("FPanelStatusAssinatura");
        FPanelStatusAssinatura.setLeft(0);
        FPanelStatusAssinatura.setTop(9);
        FPanelStatusAssinatura.setWidth(164);
        FPanelStatusAssinatura.setHeight(45);
        FPanelStatusAssinatura.setBorderStyle("stNone");
        FPanelStatusAssinatura.setPaddingTop(0);
        FPanelStatusAssinatura.setPaddingLeft(0);
        FPanelStatusAssinatura.setPaddingRight(0);
        FPanelStatusAssinatura.setPaddingBottom(0);
        FPanelStatusAssinatura.setMarginTop(0);
        FPanelStatusAssinatura.setMarginLeft(0);
        FPanelStatusAssinatura.setMarginRight(0);
        FPanelStatusAssinatura.setMarginBottom(0);
        FPanelStatusAssinatura.setBoxShadowConfigHorizontalLength(0);
        FPanelStatusAssinatura.setBoxShadowConfigVerticalLength(7);
        FPanelStatusAssinatura.setBoxShadowConfigBlurRadius(10);
        FPanelStatusAssinatura.setBoxShadowConfigSpreadRadius(-5);
        FPanelStatusAssinatura.setBoxShadowConfigShadowColor("clBlack");
        FPanelStatusAssinatura.setBoxShadowConfigOpacity(75);
        FPanelStatusAssinatura.setBorderRadiusTopLeft(0);
        FPanelStatusAssinatura.setBorderRadiusTopRight(0);
        FPanelStatusAssinatura.setBorderRadiusBottomRight(0);
        FPanelStatusAssinatura.setBorderRadiusBottomLeft(0);
        FPanelStatusAssinatura.setToggle(true);
        FPanelStatusAssinatura.setToggleColor("clBtnFace");
        vBoxlStatusAssinaturaPrincipal.addChildren(FPanelStatusAssinatura);

        TFFastDesignCmpItems FPanelStatusAssinaturaItems = new TFFastDesignCmpItems();
        FPanelStatusAssinaturaItems.setName("FPanelStatusAssinaturaItems");
        FPanelStatusAssinatura.addChildren(FPanelStatusAssinaturaItems);
        FPanelStatusAssinaturaItems.applyProperties();

        TFPanelButtonItem FPanelStatusAssinaturaVbox = new TFPanelButtonItem();
        FPanelStatusAssinaturaVbox.setName("FPanelStatusAssinaturaVbox");
        FPanelStatusAssinaturaVbox.setCaption("FPanelButtonItem1");
        FPanelStatusAssinaturaVbox.setItemType("itVBox");
        FPanelStatusAssinaturaVbox.setFontColor("clWindowText");
        FPanelStatusAssinaturaVbox.setFontSize(-13);
        FPanelStatusAssinaturaVbox.setFontName("Tahoma");
        FPanelStatusAssinaturaVbox.setFontStyle("[]");
        FPanelStatusAssinaturaVbox.setItemAlign("iaLeft");
        FPanelStatusAssinaturaVbox.setFlex(false);
        FPanelStatusAssinaturaVbox.setItemVAlign("ivaTop");
        FPanelStatusAssinaturaVbox.setPaddingTop(0);
        FPanelStatusAssinaturaVbox.setPaddingLeft(0);
        FPanelStatusAssinaturaVbox.setPaddingRight(0);
        FPanelStatusAssinaturaVbox.setPaddingBottom(0);
        FPanelStatusAssinaturaVbox.setMarginTop(0);
        FPanelStatusAssinaturaVbox.setMarginLeft(0);
        FPanelStatusAssinaturaVbox.setMarginRight(0);
        FPanelStatusAssinaturaVbox.setMarginBottom(0);
        FPanelStatusAssinaturaVbox.setBorderStyle("stNone");
        FPanelStatusAssinaturaVbox.setBoxShadowConfigHorizontalLength(10);
        FPanelStatusAssinaturaVbox.setBoxShadowConfigVerticalLength(10);
        FPanelStatusAssinaturaVbox.setBoxShadowConfigBlurRadius(5);
        FPanelStatusAssinaturaVbox.setBoxShadowConfigSpreadRadius(0);
        FPanelStatusAssinaturaVbox.setBoxShadowConfigShadowColor("clBlack");
        FPanelStatusAssinaturaVbox.setBoxShadowConfigOpacity(75);
        FPanelStatusAssinaturaVbox.setBorderRadiusTopLeft(0);
        FPanelStatusAssinaturaVbox.setBorderRadiusTopRight(0);
        FPanelStatusAssinaturaVbox.setBorderRadiusBottomRight(0);
        FPanelStatusAssinaturaVbox.setBorderRadiusBottomLeft(0);
        FPanelStatusAssinaturaVbox.setColor("clBtnFace");
        FPanelStatusAssinaturaVbox.setWordBreak(false);
        FPanelStatusAssinaturaItems.addChildren(FPanelStatusAssinaturaVbox);
        FPanelStatusAssinaturaVbox.applyProperties();

        TFPanelButtonItem FPanelStatusAssinaturaLbl1 = new TFPanelButtonItem();
        FPanelStatusAssinaturaLbl1.setName("FPanelStatusAssinaturaLbl1");
        FPanelStatusAssinaturaLbl1.setCaption("Status da Assinatura");
        FPanelStatusAssinaturaLbl1.setItemType("itLabel");
        FPanelStatusAssinaturaLbl1.setFontColor("clBlack");
        FPanelStatusAssinaturaLbl1.setFontSize(-15);
        FPanelStatusAssinaturaLbl1.setFontName("Tahoma");
        FPanelStatusAssinaturaLbl1.setFontStyle("[fsBold]");
        FPanelStatusAssinaturaLbl1.setItemAlign("iaLeft");
        FPanelStatusAssinaturaLbl1.setFlex(false);
        FPanelStatusAssinaturaLbl1.setItemVAlign("ivaBottom");
        FPanelStatusAssinaturaLbl1.setPaddingTop(0);
        FPanelStatusAssinaturaLbl1.setPaddingLeft(0);
        FPanelStatusAssinaturaLbl1.setPaddingRight(0);
        FPanelStatusAssinaturaLbl1.setPaddingBottom(0);
        FPanelStatusAssinaturaLbl1.setMarginTop(0);
        FPanelStatusAssinaturaLbl1.setMarginLeft(0);
        FPanelStatusAssinaturaLbl1.setMarginRight(0);
        FPanelStatusAssinaturaLbl1.setMarginBottom(0);
        FPanelStatusAssinaturaLbl1.setBorderStyle("stNone");
        FPanelStatusAssinaturaLbl1.setBoxShadowConfigHorizontalLength(10);
        FPanelStatusAssinaturaLbl1.setBoxShadowConfigVerticalLength(10);
        FPanelStatusAssinaturaLbl1.setBoxShadowConfigBlurRadius(5);
        FPanelStatusAssinaturaLbl1.setBoxShadowConfigSpreadRadius(0);
        FPanelStatusAssinaturaLbl1.setBoxShadowConfigShadowColor("clBlack");
        FPanelStatusAssinaturaLbl1.setBoxShadowConfigOpacity(75);
        FPanelStatusAssinaturaLbl1.setBorderRadiusTopLeft(0);
        FPanelStatusAssinaturaLbl1.setBorderRadiusTopRight(0);
        FPanelStatusAssinaturaLbl1.setBorderRadiusBottomRight(0);
        FPanelStatusAssinaturaLbl1.setBorderRadiusBottomLeft(0);
        FPanelStatusAssinaturaLbl1.setColor("clBtnFace");
        FPanelStatusAssinaturaLbl1.setWordBreak(false);
        FPanelStatusAssinaturaVbox.addChildren(FPanelStatusAssinaturaLbl1);
        FPanelStatusAssinaturaLbl1.applyProperties();

        TFPanelButtonItem FPanelStatusAssinaturaLbl2 = new TFPanelButtonItem();
        FPanelStatusAssinaturaLbl2.setName("FPanelStatusAssinaturaLbl2");
        FPanelStatusAssinaturaLbl2.setCaption("Status");
        FPanelStatusAssinaturaLbl2.setItemType("itLabel");
        FPanelStatusAssinaturaLbl2.setFontColor("clWindowText");
        FPanelStatusAssinaturaLbl2.setFontSize(-13);
        FPanelStatusAssinaturaLbl2.setFontName("Tahoma");
        FPanelStatusAssinaturaLbl2.setFontStyle("[fsBold]");
        FPanelStatusAssinaturaLbl2.setItemAlign("iaLeft");
        FPanelStatusAssinaturaLbl2.setFlex(false);
        FPanelStatusAssinaturaLbl2.setItemVAlign("ivaTop");
        FPanelStatusAssinaturaLbl2.setPaddingTop(0);
        FPanelStatusAssinaturaLbl2.setPaddingLeft(0);
        FPanelStatusAssinaturaLbl2.setPaddingRight(0);
        FPanelStatusAssinaturaLbl2.setPaddingBottom(0);
        FPanelStatusAssinaturaLbl2.setMarginTop(0);
        FPanelStatusAssinaturaLbl2.setMarginLeft(0);
        FPanelStatusAssinaturaLbl2.setMarginRight(0);
        FPanelStatusAssinaturaLbl2.setMarginBottom(0);
        FPanelStatusAssinaturaLbl2.setBorderStyle("stNone");
        FPanelStatusAssinaturaLbl2.setBoxShadowConfigHorizontalLength(10);
        FPanelStatusAssinaturaLbl2.setBoxShadowConfigVerticalLength(10);
        FPanelStatusAssinaturaLbl2.setBoxShadowConfigBlurRadius(5);
        FPanelStatusAssinaturaLbl2.setBoxShadowConfigSpreadRadius(0);
        FPanelStatusAssinaturaLbl2.setBoxShadowConfigShadowColor("clBlack");
        FPanelStatusAssinaturaLbl2.setBoxShadowConfigOpacity(75);
        FPanelStatusAssinaturaLbl2.setBorderRadiusTopLeft(0);
        FPanelStatusAssinaturaLbl2.setBorderRadiusTopRight(0);
        FPanelStatusAssinaturaLbl2.setBorderRadiusBottomRight(0);
        FPanelStatusAssinaturaLbl2.setBorderRadiusBottomLeft(0);
        FPanelStatusAssinaturaLbl2.setColor("clBtnFace");
        FPanelStatusAssinaturaLbl2.setWordBreak(false);
        FPanelStatusAssinaturaVbox.addChildren(FPanelStatusAssinaturaLbl2);
        FPanelStatusAssinaturaLbl2.applyProperties();
        FPanelStatusAssinatura.applyProperties();
    }

    public TFHBox vBoxlStatusAssinaturaPrincipal2 = new TFHBox();

    private void init_vBoxlStatusAssinaturaPrincipal2() {
        vBoxlStatusAssinaturaPrincipal2.setName("vBoxlStatusAssinaturaPrincipal2");
        vBoxlStatusAssinaturaPrincipal2.setLeft(0);
        vBoxlStatusAssinaturaPrincipal2.setTop(55);
        vBoxlStatusAssinaturaPrincipal2.setWidth(76);
        vBoxlStatusAssinaturaPrincipal2.setHeight(8);
        vBoxlStatusAssinaturaPrincipal2.setBorderStyle("stNone");
        vBoxlStatusAssinaturaPrincipal2.setPaddingTop(0);
        vBoxlStatusAssinaturaPrincipal2.setPaddingLeft(0);
        vBoxlStatusAssinaturaPrincipal2.setPaddingRight(0);
        vBoxlStatusAssinaturaPrincipal2.setPaddingBottom(0);
        vBoxlStatusAssinaturaPrincipal2.setMarginTop(0);
        vBoxlStatusAssinaturaPrincipal2.setMarginLeft(0);
        vBoxlStatusAssinaturaPrincipal2.setMarginRight(0);
        vBoxlStatusAssinaturaPrincipal2.setMarginBottom(0);
        vBoxlStatusAssinaturaPrincipal2.setSpacing(1);
        vBoxlStatusAssinaturaPrincipal2.setFlexVflex("ftTrue");
        vBoxlStatusAssinaturaPrincipal2.setFlexHflex("ftTrue");
        vBoxlStatusAssinaturaPrincipal2.setScrollable(false);
        vBoxlStatusAssinaturaPrincipal2.setBoxShadowConfigHorizontalLength(10);
        vBoxlStatusAssinaturaPrincipal2.setBoxShadowConfigVerticalLength(10);
        vBoxlStatusAssinaturaPrincipal2.setBoxShadowConfigBlurRadius(5);
        vBoxlStatusAssinaturaPrincipal2.setBoxShadowConfigSpreadRadius(0);
        vBoxlStatusAssinaturaPrincipal2.setBoxShadowConfigShadowColor("clBlack");
        vBoxlStatusAssinaturaPrincipal2.setBoxShadowConfigOpacity(75);
        vBoxlStatusAssinaturaPrincipal2.setVAlign("tvTop");
        vBoxlStatusAssinaturaPrincipal.addChildren(vBoxlStatusAssinaturaPrincipal2);
        vBoxlStatusAssinaturaPrincipal2.applyProperties();
    }

    public TFVBox VboxTemplateSolicitacaoAssinaturaPrincipal = new TFVBox();

    private void init_VboxTemplateSolicitacaoAssinaturaPrincipal() {
        VboxTemplateSolicitacaoAssinaturaPrincipal.setName("VboxTemplateSolicitacaoAssinaturaPrincipal");
        VboxTemplateSolicitacaoAssinaturaPrincipal.setLeft(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setTop(58);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setWidth(577);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setHeight(106);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setBorderStyle("stNone");
        VboxTemplateSolicitacaoAssinaturaPrincipal.setPaddingTop(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setPaddingLeft(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setPaddingRight(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setPaddingBottom(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setVisible(false);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setMarginTop(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setMarginLeft(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setMarginRight(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setMarginBottom(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setSpacing(8);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setFlexVflex("ftMin");
        VboxTemplateSolicitacaoAssinaturaPrincipal.setFlexHflex("ftTrue");
        VboxTemplateSolicitacaoAssinaturaPrincipal.setScrollable(false);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setBoxShadowConfigHorizontalLength(10);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setBoxShadowConfigVerticalLength(10);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setBoxShadowConfigBlurRadius(5);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setBoxShadowConfigSpreadRadius(0);
        VboxTemplateSolicitacaoAssinaturaPrincipal.setBoxShadowConfigShadowColor("clBlack");
        VboxTemplateSolicitacaoAssinaturaPrincipal.setBoxShadowConfigOpacity(75);
        boxPrinciapl.addChildren(VboxTemplateSolicitacaoAssinaturaPrincipal);
        VboxTemplateSolicitacaoAssinaturaPrincipal.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinatura = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinatura() {
        hboxTemplateSolicitacaoAssinatura.setName("hboxTemplateSolicitacaoAssinatura");
        hboxTemplateSolicitacaoAssinatura.setLeft(0);
        hboxTemplateSolicitacaoAssinatura.setTop(0);
        hboxTemplateSolicitacaoAssinatura.setWidth(576);
        hboxTemplateSolicitacaoAssinatura.setHeight(101);
        hboxTemplateSolicitacaoAssinatura.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinatura.setPaddingTop(0);
        hboxTemplateSolicitacaoAssinatura.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinatura.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinatura.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinatura.setMarginTop(0);
        hboxTemplateSolicitacaoAssinatura.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinatura.setMarginRight(0);
        hboxTemplateSolicitacaoAssinatura.setMarginBottom(0);
        hboxTemplateSolicitacaoAssinatura.setSpacing(8);
        hboxTemplateSolicitacaoAssinatura.setFlexVflex("ftMin");
        hboxTemplateSolicitacaoAssinatura.setFlexHflex("ftTrue");
        hboxTemplateSolicitacaoAssinatura.setScrollable(false);
        hboxTemplateSolicitacaoAssinatura.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinatura.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinatura.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinatura.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinatura.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinatura.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinatura.setVAlign("tvTop");
        VboxTemplateSolicitacaoAssinaturaPrincipal.addChildren(hboxTemplateSolicitacaoAssinatura);
        hboxTemplateSolicitacaoAssinatura.applyProperties();
    }

    public TFVBox vboxTemplateSolicitacaoAssinaturaCol1 = new TFVBox();

    private void init_vboxTemplateSolicitacaoAssinaturaCol1() {
        vboxTemplateSolicitacaoAssinaturaCol1.setName("vboxTemplateSolicitacaoAssinaturaCol1");
        vboxTemplateSolicitacaoAssinaturaCol1.setLeft(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setTop(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setWidth(452);
        vboxTemplateSolicitacaoAssinaturaCol1.setHeight(87);
        vboxTemplateSolicitacaoAssinaturaCol1.setBorderStyle("stNone");
        vboxTemplateSolicitacaoAssinaturaCol1.setPaddingTop(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setPaddingLeft(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setPaddingRight(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setPaddingBottom(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setMarginTop(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setMarginLeft(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setMarginRight(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setMarginBottom(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setSpacing(1);
        vboxTemplateSolicitacaoAssinaturaCol1.setFlexVflex("ftMin");
        vboxTemplateSolicitacaoAssinaturaCol1.setFlexHflex("ftTrue");
        vboxTemplateSolicitacaoAssinaturaCol1.setScrollable(false);
        vboxTemplateSolicitacaoAssinaturaCol1.setBoxShadowConfigHorizontalLength(10);
        vboxTemplateSolicitacaoAssinaturaCol1.setBoxShadowConfigVerticalLength(10);
        vboxTemplateSolicitacaoAssinaturaCol1.setBoxShadowConfigBlurRadius(5);
        vboxTemplateSolicitacaoAssinaturaCol1.setBoxShadowConfigSpreadRadius(0);
        vboxTemplateSolicitacaoAssinaturaCol1.setBoxShadowConfigShadowColor("clBlack");
        vboxTemplateSolicitacaoAssinaturaCol1.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinatura.addChildren(vboxTemplateSolicitacaoAssinaturaCol1);
        vboxTemplateSolicitacaoAssinaturaCol1.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinaturaLinha1 = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinaturaLinha1() {
        hboxTemplateSolicitacaoAssinaturaLinha1.setName("hboxTemplateSolicitacaoAssinaturaLinha1");
        hboxTemplateSolicitacaoAssinaturaLinha1.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setTop(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setWidth(422);
        hboxTemplateSolicitacaoAssinaturaLinha1.setHeight(30);
        hboxTemplateSolicitacaoAssinaturaLinha1.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinaturaLinha1.setPaddingTop(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setMarginTop(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setMarginRight(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setMarginBottom(3);
        hboxTemplateSolicitacaoAssinaturaLinha1.setSpacing(10);
        hboxTemplateSolicitacaoAssinaturaLinha1.setFlexVflex("ftMin");
        hboxTemplateSolicitacaoAssinaturaLinha1.setFlexHflex("ftTrue");
        hboxTemplateSolicitacaoAssinaturaLinha1.setScrollable(false);
        hboxTemplateSolicitacaoAssinaturaLinha1.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinaturaLinha1.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinaturaLinha1.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinaturaLinha1.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinaturaLinha1.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaLinha1.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinaturaLinha1.setVAlign("tvTop");
        vboxTemplateSolicitacaoAssinaturaCol1.addChildren(hboxTemplateSolicitacaoAssinaturaLinha1);
        hboxTemplateSolicitacaoAssinaturaLinha1.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinaturaHboxLblTipo = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinaturaHboxLblTipo() {
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setName("hboxTemplateSolicitacaoAssinaturaHboxLblTipo");
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setWidth(35);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setHeight(26);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setAlign("alLeft");
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setPaddingTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setMarginTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setMarginRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setMarginBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setSpacing(1);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setFlexVflex("ftMin");
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setFlexHflex("ftMin");
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setScrollable(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.setVAlign("tvTop");
        hboxTemplateSolicitacaoAssinaturaLinha1.addChildren(hboxTemplateSolicitacaoAssinaturaHboxLblTipo);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.applyProperties();
    }

    public TFLabel hboxTemplateSolicitacaoAssinaturaLblTipo = new TFLabel();

    private void init_hboxTemplateSolicitacaoAssinaturaLblTipo() {
        hboxTemplateSolicitacaoAssinaturaLblTipo.setName("hboxTemplateSolicitacaoAssinaturaLblTipo");
        hboxTemplateSolicitacaoAssinaturaLblTipo.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaLblTipo.setTop(0);
        hboxTemplateSolicitacaoAssinaturaLblTipo.setWidth(42);
        hboxTemplateSolicitacaoAssinaturaLblTipo.setHeight(23);
        hboxTemplateSolicitacaoAssinaturaLblTipo.setHint("Signatario");
        hboxTemplateSolicitacaoAssinaturaLblTipo.setAlign("alRight");
        hboxTemplateSolicitacaoAssinaturaLblTipo.setCaption("Tipo");
        hboxTemplateSolicitacaoAssinaturaLblTipo.setFontColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaLblTipo.setFontSize(-19);
        hboxTemplateSolicitacaoAssinaturaLblTipo.setFontName("Tahoma");
        hboxTemplateSolicitacaoAssinaturaLblTipo.setFontStyle("[fsBold]");
        hboxTemplateSolicitacaoAssinaturaLblTipo.setFieldName("DESCRICAO");
        hboxTemplateSolicitacaoAssinaturaLblTipo.setTable(tbSolicitacoesAssinaturas);
        hboxTemplateSolicitacaoAssinaturaLblTipo.setVerticalAlignment("taVerticalCenter");
        hboxTemplateSolicitacaoAssinaturaLblTipo.setWordBreak(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblTipo.addChildren(hboxTemplateSolicitacaoAssinaturaLblTipo);
        hboxTemplateSolicitacaoAssinaturaLblTipo.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinaturaHboxLblEditar = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinaturaHboxLblEditar() {
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setName("hboxTemplateSolicitacaoAssinaturaHboxLblEditar");
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setLeft(35);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setWidth(60);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setHeight(26);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setAlign("alLeft");
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setPaddingTop(4);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setMarginTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setMarginRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setMarginBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setSpacing(1);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setFlexVflex("ftTrue");
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setFlexHflex("ftFalse");
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setScrollable(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.setVAlign("tvTop");
        hboxTemplateSolicitacaoAssinaturaLinha1.addChildren(hboxTemplateSolicitacaoAssinaturaHboxLblEditar);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.applyProperties();
    }

    public TFIconClass hboxTemplateSolicitacaoAssinaturaIconEditar = new TFIconClass();

    private void init_hboxTemplateSolicitacaoAssinaturaIconEditar() {
        hboxTemplateSolicitacaoAssinaturaIconEditar.setName("hboxTemplateSolicitacaoAssinaturaIconEditar");
        hboxTemplateSolicitacaoAssinaturaIconEditar.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaIconEditar.setTop(0);
        hboxTemplateSolicitacaoAssinaturaIconEditar.setIconClass("pencil");
        hboxTemplateSolicitacaoAssinaturaIconEditar.setSize(13);
        hboxTemplateSolicitacaoAssinaturaIconEditar.setColor("clBlue");
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.addChildren(hboxTemplateSolicitacaoAssinaturaIconEditar);
        hboxTemplateSolicitacaoAssinaturaIconEditar.applyProperties();
    }

    public TFLabel hboxTemplateSolicitacaoAssinaturaLblEditar = new TFLabel();

    private void init_hboxTemplateSolicitacaoAssinaturaLblEditar() {
        hboxTemplateSolicitacaoAssinaturaLblEditar.setName("hboxTemplateSolicitacaoAssinaturaLblEditar");
        hboxTemplateSolicitacaoAssinaturaLblEditar.setLeft(16);
        hboxTemplateSolicitacaoAssinaturaLblEditar.setTop(0);
        hboxTemplateSolicitacaoAssinaturaLblEditar.setWidth(41);
        hboxTemplateSolicitacaoAssinaturaLblEditar.setHeight(18);
        hboxTemplateSolicitacaoAssinaturaLblEditar.setHint("mudar dados signatario");
        hboxTemplateSolicitacaoAssinaturaLblEditar.setAlign("alRight");
        hboxTemplateSolicitacaoAssinaturaLblEditar.setCaption("Mudar");
        hboxTemplateSolicitacaoAssinaturaLblEditar.setFontColor("clWindowText");
        hboxTemplateSolicitacaoAssinaturaLblEditar.setFontSize(-15);
        hboxTemplateSolicitacaoAssinaturaLblEditar.setFontName("Tahoma");
        hboxTemplateSolicitacaoAssinaturaLblEditar.setFontStyle("[]");
        hboxTemplateSolicitacaoAssinaturaLblEditar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxTemplateSolicitacaoAssinaturaLblEditarClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "hboxTemplateSolicitacaoAssinaturaLblEditar", "OnClick");
        });
        hboxTemplateSolicitacaoAssinaturaLblEditar.setHiperLink(true);
        hboxTemplateSolicitacaoAssinaturaLblEditar.setVerticalAlignment("taAlignBottom");
        hboxTemplateSolicitacaoAssinaturaLblEditar.setWordBreak(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblEditar.addChildren(hboxTemplateSolicitacaoAssinaturaLblEditar);
        hboxTemplateSolicitacaoAssinaturaLblEditar.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinaturaHboxLblStatus = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinaturaHboxLblStatus() {
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setName("hboxTemplateSolicitacaoAssinaturaHboxLblStatus");
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setLeft(95);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setWidth(135);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setHeight(26);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setAlign("alLeft");
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setPaddingTop(4);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setMarginTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setMarginRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setMarginBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setSpacing(1);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setFlexVflex("ftTrue");
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setFlexHflex("ftTrue");
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setScrollable(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.setVAlign("tvTop");
        hboxTemplateSolicitacaoAssinaturaLinha1.addChildren(hboxTemplateSolicitacaoAssinaturaHboxLblStatus);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.applyProperties();
    }

    public TFLabel hboxTemplateSolicitacaoAssinaturaLblStatus = new TFLabel();

    private void init_hboxTemplateSolicitacaoAssinaturaLblStatus() {
        hboxTemplateSolicitacaoAssinaturaLblStatus.setName("hboxTemplateSolicitacaoAssinaturaLblStatus");
        hboxTemplateSolicitacaoAssinaturaLblStatus.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaLblStatus.setTop(0);
        hboxTemplateSolicitacaoAssinaturaLblStatus.setWidth(41);
        hboxTemplateSolicitacaoAssinaturaLblStatus.setHeight(18);
        hboxTemplateSolicitacaoAssinaturaLblStatus.setHint("Status");
        hboxTemplateSolicitacaoAssinaturaLblStatus.setAlign("alRight");
        hboxTemplateSolicitacaoAssinaturaLblStatus.setCaption("Status");
        hboxTemplateSolicitacaoAssinaturaLblStatus.setFontColor("clWindowText");
        hboxTemplateSolicitacaoAssinaturaLblStatus.setFontSize(-15);
        hboxTemplateSolicitacaoAssinaturaLblStatus.setFontName("Tahoma");
        hboxTemplateSolicitacaoAssinaturaLblStatus.setFontStyle("[]");
        hboxTemplateSolicitacaoAssinaturaLblStatus.setVerticalAlignment("taVerticalCenter");
        hboxTemplateSolicitacaoAssinaturaLblStatus.setWordBreak(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblStatus.addChildren(hboxTemplateSolicitacaoAssinaturaLblStatus);
        hboxTemplateSolicitacaoAssinaturaLblStatus.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinaturaLinha2 = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinaturaLinha2() {
        hboxTemplateSolicitacaoAssinaturaLinha2.setName("hboxTemplateSolicitacaoAssinaturaLinha2");
        hboxTemplateSolicitacaoAssinaturaLinha2.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaLinha2.setTop(31);
        hboxTemplateSolicitacaoAssinaturaLinha2.setWidth(422);
        hboxTemplateSolicitacaoAssinaturaLinha2.setHeight(25);
        hboxTemplateSolicitacaoAssinaturaLinha2.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinaturaLinha2.setPaddingTop(0);
        hboxTemplateSolicitacaoAssinaturaLinha2.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinaturaLinha2.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinaturaLinha2.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinaturaLinha2.setMarginTop(0);
        hboxTemplateSolicitacaoAssinaturaLinha2.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinaturaLinha2.setMarginRight(0);
        hboxTemplateSolicitacaoAssinaturaLinha2.setMarginBottom(3);
        hboxTemplateSolicitacaoAssinaturaLinha2.setSpacing(8);
        hboxTemplateSolicitacaoAssinaturaLinha2.setFlexVflex("ftMin");
        hboxTemplateSolicitacaoAssinaturaLinha2.setFlexHflex("ftTrue");
        hboxTemplateSolicitacaoAssinaturaLinha2.setScrollable(false);
        hboxTemplateSolicitacaoAssinaturaLinha2.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinaturaLinha2.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinaturaLinha2.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinaturaLinha2.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinaturaLinha2.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaLinha2.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinaturaLinha2.setVAlign("tvTop");
        vboxTemplateSolicitacaoAssinaturaCol1.addChildren(hboxTemplateSolicitacaoAssinaturaLinha2);
        hboxTemplateSolicitacaoAssinaturaLinha2.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinaturaHboxLblNome = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinaturaHboxLblNome() {
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setName("hboxTemplateSolicitacaoAssinaturaHboxLblNome");
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setWidth(252);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setHeight(22);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setPaddingTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setMarginTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setMarginRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setMarginBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setSpacing(1);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setFlexVflex("ftMin");
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setFlexHflex("ftTrue");
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setScrollable(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.setVAlign("tvTop");
        hboxTemplateSolicitacaoAssinaturaLinha2.addChildren(hboxTemplateSolicitacaoAssinaturaHboxLblNome);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.applyProperties();
    }

    public TFLabel hboxTemplateSolicitacaoAssinaturaLblNome = new TFLabel();

    private void init_hboxTemplateSolicitacaoAssinaturaLblNome() {
        hboxTemplateSolicitacaoAssinaturaLblNome.setName("hboxTemplateSolicitacaoAssinaturaLblNome");
        hboxTemplateSolicitacaoAssinaturaLblNome.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaLblNome.setTop(0);
        hboxTemplateSolicitacaoAssinaturaLblNome.setWidth(39);
        hboxTemplateSolicitacaoAssinaturaLblNome.setHeight(18);
        hboxTemplateSolicitacaoAssinaturaLblNome.setHint("Nome");
        hboxTemplateSolicitacaoAssinaturaLblNome.setAlign("alRight");
        hboxTemplateSolicitacaoAssinaturaLblNome.setCaption("Nome");
        hboxTemplateSolicitacaoAssinaturaLblNome.setFontColor("clWindowText");
        hboxTemplateSolicitacaoAssinaturaLblNome.setFontSize(-15);
        hboxTemplateSolicitacaoAssinaturaLblNome.setFontName("Tahoma");
        hboxTemplateSolicitacaoAssinaturaLblNome.setFontStyle("[]");
        hboxTemplateSolicitacaoAssinaturaLblNome.setFieldName("NOME");
        hboxTemplateSolicitacaoAssinaturaLblNome.setTable(tbSolicitacoesAssinaturas);
        hboxTemplateSolicitacaoAssinaturaLblNome.setVerticalAlignment("taVerticalCenter");
        hboxTemplateSolicitacaoAssinaturaLblNome.setWordBreak(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblNome.addChildren(hboxTemplateSolicitacaoAssinaturaLblNome);
        hboxTemplateSolicitacaoAssinaturaLblNome.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinaturaLinha3 = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinaturaLinha3() {
        hboxTemplateSolicitacaoAssinaturaLinha3.setName("hboxTemplateSolicitacaoAssinaturaLinha3");
        hboxTemplateSolicitacaoAssinaturaLinha3.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaLinha3.setTop(57);
        hboxTemplateSolicitacaoAssinaturaLinha3.setWidth(422);
        hboxTemplateSolicitacaoAssinaturaLinha3.setHeight(25);
        hboxTemplateSolicitacaoAssinaturaLinha3.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinaturaLinha3.setPaddingTop(0);
        hboxTemplateSolicitacaoAssinaturaLinha3.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinaturaLinha3.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinaturaLinha3.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinaturaLinha3.setMarginTop(0);
        hboxTemplateSolicitacaoAssinaturaLinha3.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinaturaLinha3.setMarginRight(0);
        hboxTemplateSolicitacaoAssinaturaLinha3.setMarginBottom(3);
        hboxTemplateSolicitacaoAssinaturaLinha3.setSpacing(8);
        hboxTemplateSolicitacaoAssinaturaLinha3.setFlexVflex("ftMin");
        hboxTemplateSolicitacaoAssinaturaLinha3.setFlexHflex("ftTrue");
        hboxTemplateSolicitacaoAssinaturaLinha3.setScrollable(false);
        hboxTemplateSolicitacaoAssinaturaLinha3.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinaturaLinha3.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinaturaLinha3.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinaturaLinha3.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinaturaLinha3.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaLinha3.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinaturaLinha3.setVAlign("tvTop");
        vboxTemplateSolicitacaoAssinaturaCol1.addChildren(hboxTemplateSolicitacaoAssinaturaLinha3);
        hboxTemplateSolicitacaoAssinaturaLinha3.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinaturaHboxLblEmail = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinaturaHboxLblEmail() {
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setName("hboxTemplateSolicitacaoAssinaturaHboxLblEmail");
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setWidth(193);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setHeight(22);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setPaddingTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setMarginTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setMarginRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setMarginBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setSpacing(1);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setFlexVflex("ftFalse");
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setFlexHflex("ftTrue");
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setScrollable(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.setVAlign("tvTop");
        hboxTemplateSolicitacaoAssinaturaLinha3.addChildren(hboxTemplateSolicitacaoAssinaturaHboxLblEmail);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.applyProperties();
    }

    public TFLabel hboxTemplateSolicitacaoAssinaturaLblEmail = new TFLabel();

    private void init_hboxTemplateSolicitacaoAssinaturaLblEmail() {
        hboxTemplateSolicitacaoAssinaturaLblEmail.setName("hboxTemplateSolicitacaoAssinaturaLblEmail");
        hboxTemplateSolicitacaoAssinaturaLblEmail.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaLblEmail.setTop(0);
        hboxTemplateSolicitacaoAssinaturaLblEmail.setWidth(33);
        hboxTemplateSolicitacaoAssinaturaLblEmail.setHeight(18);
        hboxTemplateSolicitacaoAssinaturaLblEmail.setHint("Email");
        hboxTemplateSolicitacaoAssinaturaLblEmail.setAlign("alRight");
        hboxTemplateSolicitacaoAssinaturaLblEmail.setCaption("Email");
        hboxTemplateSolicitacaoAssinaturaLblEmail.setFontColor("clWindowText");
        hboxTemplateSolicitacaoAssinaturaLblEmail.setFontSize(-15);
        hboxTemplateSolicitacaoAssinaturaLblEmail.setFontName("Tahoma");
        hboxTemplateSolicitacaoAssinaturaLblEmail.setFontStyle("[]");
        hboxTemplateSolicitacaoAssinaturaLblEmail.setFieldName("EMAIL");
        hboxTemplateSolicitacaoAssinaturaLblEmail.setTable(tbSolicitacoesAssinaturas);
        hboxTemplateSolicitacaoAssinaturaLblEmail.setVerticalAlignment("taVerticalCenter");
        hboxTemplateSolicitacaoAssinaturaLblEmail.setWordBreak(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblEmail.addChildren(hboxTemplateSolicitacaoAssinaturaLblEmail);
        hboxTemplateSolicitacaoAssinaturaLblEmail.applyProperties();
    }

    public TFHBox hboxTemplateSolicitacaoAssinaturaHboxLblTelefone = new TFHBox();

    private void init_hboxTemplateSolicitacaoAssinaturaHboxLblTelefone() {
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setName("hboxTemplateSolicitacaoAssinaturaHboxLblTelefone");
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setLeft(193);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setWidth(97);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setHeight(22);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setBorderStyle("stNone");
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setPaddingTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setPaddingLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setPaddingRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setPaddingBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setMarginTop(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setMarginLeft(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setMarginRight(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setMarginBottom(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setSpacing(1);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setFlexVflex("ftFalse");
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setFlexHflex("ftFalse");
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setScrollable(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setBoxShadowConfigHorizontalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setBoxShadowConfigVerticalLength(10);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setBoxShadowConfigBlurRadius(5);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setBoxShadowConfigSpreadRadius(0);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setBoxShadowConfigShadowColor("clBlack");
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.setVAlign("tvTop");
        hboxTemplateSolicitacaoAssinaturaLinha3.addChildren(hboxTemplateSolicitacaoAssinaturaHboxLblTelefone);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.applyProperties();
    }

    public TFLabel hboxTemplateSolicitacaoAssinaturaLblTelefone = new TFLabel();

    private void init_hboxTemplateSolicitacaoAssinaturaLblTelefone() {
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setName("hboxTemplateSolicitacaoAssinaturaLblTelefone");
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setLeft(0);
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setTop(0);
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setWidth(57);
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setHeight(18);
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setHint("Telefone");
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setAlign("alRight");
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setCaption("Telefone");
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setFontColor("clWindowText");
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setFontSize(-15);
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setFontName("Tahoma");
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setFontStyle("[]");
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setFieldName("TELEFONE");
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setTable(tbSolicitacoesAssinaturas);
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setVerticalAlignment("taVerticalCenter");
        hboxTemplateSolicitacaoAssinaturaLblTelefone.setWordBreak(false);
        hboxTemplateSolicitacaoAssinaturaHboxLblTelefone.addChildren(hboxTemplateSolicitacaoAssinaturaLblTelefone);
        hboxTemplateSolicitacaoAssinaturaLblTelefone.applyProperties();
    }

    public TFVBox vBoxBtnAcaoLerQRCodeSignatarioPrincipal = new TFVBox();

    private void init_vBoxBtnAcaoLerQRCodeSignatarioPrincipal() {
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setName("vBoxBtnAcaoLerQRCodeSignatarioPrincipal");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setLeft(452);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setTop(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setWidth(92);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setHeight(81);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setBorderStyle("stNone");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setPaddingTop(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setPaddingLeft(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setPaddingRight(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setPaddingBottom(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setMarginTop(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setMarginLeft(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setMarginRight(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setMarginBottom(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setSpacing(4);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setFlexVflex("ftTrue");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setFlexHflex("ftFalse");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setScrollable(false);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.setBoxShadowConfigOpacity(75);
        hboxTemplateSolicitacaoAssinatura.addChildren(vBoxBtnAcaoLerQRCodeSignatarioPrincipal);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.applyProperties();
    }

    public TFHBox vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2 = new TFHBox();

    private void init_vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2() {
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setName("vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setLeft(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setTop(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setWidth(76);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setHeight(8);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setBorderStyle("stNone");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setPaddingTop(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setPaddingLeft(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setPaddingRight(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setPaddingBottom(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setMarginTop(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setMarginLeft(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setMarginRight(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setMarginBottom(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setSpacing(1);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setFlexVflex("ftTrue");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setFlexHflex("ftTrue");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setScrollable(false);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setBoxShadowConfigVerticalLength(10);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setBoxShadowConfigBlurRadius(5);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setBoxShadowConfigOpacity(75);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.setVAlign("tvTop");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.addChildren(vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2.applyProperties();
    }

    public TFPanelButton FPanelBtnQrCode = new TFPanelButton();

    private void init_FPanelBtnQrCode() {
        FPanelBtnQrCode.setName("FPanelBtnQrCode");
        FPanelBtnQrCode.setLeft(0);
        FPanelBtnQrCode.setTop(9);
        FPanelBtnQrCode.setWidth(83);
        FPanelBtnQrCode.setHeight(57);
        FPanelBtnQrCode.setBorderStyle("stNone");
        FPanelBtnQrCode.setPaddingTop(0);
        FPanelBtnQrCode.setPaddingLeft(0);
        FPanelBtnQrCode.setPaddingRight(0);
        FPanelBtnQrCode.setPaddingBottom(0);
        FPanelBtnQrCode.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FPanelBtnQrCodeClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "FPanelBtnQrCode", "OnClick");
        });
        FPanelBtnQrCode.setMarginTop(0);
        FPanelBtnQrCode.setMarginLeft(0);
        FPanelBtnQrCode.setMarginRight(0);
        FPanelBtnQrCode.setMarginBottom(0);
        FPanelBtnQrCode.setBoxShadowConfigHorizontalLength(0);
        FPanelBtnQrCode.setBoxShadowConfigVerticalLength(7);
        FPanelBtnQrCode.setBoxShadowConfigBlurRadius(10);
        FPanelBtnQrCode.setBoxShadowConfigSpreadRadius(-5);
        FPanelBtnQrCode.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnQrCode.setBoxShadowConfigOpacity(75);
        FPanelBtnQrCode.setBorderRadiusTopLeft(0);
        FPanelBtnQrCode.setBorderRadiusTopRight(0);
        FPanelBtnQrCode.setBorderRadiusBottomRight(0);
        FPanelBtnQrCode.setBorderRadiusBottomLeft(0);
        FPanelBtnQrCode.setToggle(true);
        FPanelBtnQrCode.setToggleColor("clBtnFace");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.addChildren(FPanelBtnQrCode);

        TFFastDesignCmpItems FPanelBtnQrCodeItems = new TFFastDesignCmpItems();
        FPanelBtnQrCodeItems.setName("FPanelBtnQrCodeItems");
        FPanelBtnQrCode.addChildren(FPanelBtnQrCodeItems);
        FPanelBtnQrCodeItems.applyProperties();

        TFPanelButtonItem FPanelBtnQrCodeVbox = new TFPanelButtonItem();
        FPanelBtnQrCodeVbox.setName("FPanelBtnQrCodeVbox");
        FPanelBtnQrCodeVbox.setCaption("FPanelButtonItem1");
        FPanelBtnQrCodeVbox.setItemType("itVBox");
        FPanelBtnQrCodeVbox.setFontColor("clWindowText");
        FPanelBtnQrCodeVbox.setFontSize(-13);
        FPanelBtnQrCodeVbox.setFontName("Tahoma");
        FPanelBtnQrCodeVbox.setFontStyle("[]");
        FPanelBtnQrCodeVbox.setItemAlign("iaLeft");
        FPanelBtnQrCodeVbox.setFlex(false);
        FPanelBtnQrCodeVbox.setItemVAlign("ivaTop");
        FPanelBtnQrCodeVbox.setPaddingTop(0);
        FPanelBtnQrCodeVbox.setPaddingLeft(0);
        FPanelBtnQrCodeVbox.setPaddingRight(0);
        FPanelBtnQrCodeVbox.setPaddingBottom(0);
        FPanelBtnQrCodeVbox.setMarginTop(0);
        FPanelBtnQrCodeVbox.setMarginLeft(0);
        FPanelBtnQrCodeVbox.setMarginRight(0);
        FPanelBtnQrCodeVbox.setMarginBottom(0);
        FPanelBtnQrCodeVbox.setBorderStyle("stNone");
        FPanelBtnQrCodeVbox.setBoxShadowConfigHorizontalLength(10);
        FPanelBtnQrCodeVbox.setBoxShadowConfigVerticalLength(10);
        FPanelBtnQrCodeVbox.setBoxShadowConfigBlurRadius(5);
        FPanelBtnQrCodeVbox.setBoxShadowConfigSpreadRadius(0);
        FPanelBtnQrCodeVbox.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnQrCodeVbox.setBoxShadowConfigOpacity(75);
        FPanelBtnQrCodeVbox.setBorderRadiusTopLeft(0);
        FPanelBtnQrCodeVbox.setBorderRadiusTopRight(0);
        FPanelBtnQrCodeVbox.setBorderRadiusBottomRight(0);
        FPanelBtnQrCodeVbox.setBorderRadiusBottomLeft(0);
        FPanelBtnQrCodeVbox.setColor("clBtnFace");
        FPanelBtnQrCodeVbox.setWordBreak(false);
        FPanelBtnQrCodeItems.addChildren(FPanelBtnQrCodeVbox);
        FPanelBtnQrCodeVbox.applyProperties();

        TFPanelButtonItem FPanelBtnQrCodeLbl = new TFPanelButtonItem();
        FPanelBtnQrCodeLbl.setName("FPanelBtnQrCodeLbl");
        FPanelBtnQrCodeLbl.setCaption("Ler QRCode");
        FPanelBtnQrCodeLbl.setItemType("itLabel");
        FPanelBtnQrCodeLbl.setFontColor("clGray");
        FPanelBtnQrCodeLbl.setFontSize(-12);
        FPanelBtnQrCodeLbl.setFontName("Tahoma");
        FPanelBtnQrCodeLbl.setFontStyle("[fsBold]");
        FPanelBtnQrCodeLbl.setItemAlign("iaCenter");
        FPanelBtnQrCodeLbl.setFlex(false);
        FPanelBtnQrCodeLbl.setItemVAlign("ivaTop");
        FPanelBtnQrCodeLbl.setPaddingTop(0);
        FPanelBtnQrCodeLbl.setPaddingLeft(0);
        FPanelBtnQrCodeLbl.setPaddingRight(0);
        FPanelBtnQrCodeLbl.setPaddingBottom(0);
        FPanelBtnQrCodeLbl.setMarginTop(0);
        FPanelBtnQrCodeLbl.setMarginLeft(0);
        FPanelBtnQrCodeLbl.setMarginRight(0);
        FPanelBtnQrCodeLbl.setMarginBottom(0);
        FPanelBtnQrCodeLbl.setBorderStyle("stNone");
        FPanelBtnQrCodeLbl.setBoxShadowConfigHorizontalLength(10);
        FPanelBtnQrCodeLbl.setBoxShadowConfigVerticalLength(10);
        FPanelBtnQrCodeLbl.setBoxShadowConfigBlurRadius(5);
        FPanelBtnQrCodeLbl.setBoxShadowConfigSpreadRadius(0);
        FPanelBtnQrCodeLbl.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnQrCodeLbl.setBoxShadowConfigOpacity(75);
        FPanelBtnQrCodeLbl.setBorderRadiusTopLeft(0);
        FPanelBtnQrCodeLbl.setBorderRadiusTopRight(0);
        FPanelBtnQrCodeLbl.setBorderRadiusBottomRight(0);
        FPanelBtnQrCodeLbl.setBorderRadiusBottomLeft(0);
        FPanelBtnQrCodeLbl.setColor("clBtnFace");
        FPanelBtnQrCodeLbl.setWordBreak(false);
        FPanelBtnQrCodeVbox.addChildren(FPanelBtnQrCodeLbl);
        FPanelBtnQrCodeLbl.applyProperties();

        TFPanelButtonItem FPanelBtnQrCodeImg = new TFPanelButtonItem();
        FPanelBtnQrCodeImg.setName("FPanelBtnQrCodeImg");
        FPanelBtnQrCodeImg.setCaption("FPanelButtonItem2");
        FPanelBtnQrCodeImg.setItemType("itImage");
        FPanelBtnQrCodeImg.setFontColor("clWindowText");
        FPanelBtnQrCodeImg.setFontSize(-13);
        FPanelBtnQrCodeImg.setFontName("Tahoma");
        FPanelBtnQrCodeImg.setFontStyle("[]");
        FPanelBtnQrCodeImg.setItemAlign("iaCenter");
        FPanelBtnQrCodeImg.setImageSrc("/images/crmservice386027.png");
        FPanelBtnQrCodeImg.setFlex(false);
        FPanelBtnQrCodeImg.setWidth(30);
        FPanelBtnQrCodeImg.setHeight(30);
        FPanelBtnQrCodeImg.setItemVAlign("ivaTop");
        FPanelBtnQrCodeImg.setPaddingTop(0);
        FPanelBtnQrCodeImg.setPaddingLeft(0);
        FPanelBtnQrCodeImg.setPaddingRight(0);
        FPanelBtnQrCodeImg.setPaddingBottom(0);
        FPanelBtnQrCodeImg.setMarginTop(0);
        FPanelBtnQrCodeImg.setMarginLeft(0);
        FPanelBtnQrCodeImg.setMarginRight(0);
        FPanelBtnQrCodeImg.setMarginBottom(0);
        FPanelBtnQrCodeImg.setBorderStyle("stNone");
        FPanelBtnQrCodeImg.setBoxShadowConfigHorizontalLength(10);
        FPanelBtnQrCodeImg.setBoxShadowConfigVerticalLength(10);
        FPanelBtnQrCodeImg.setBoxShadowConfigBlurRadius(5);
        FPanelBtnQrCodeImg.setBoxShadowConfigSpreadRadius(0);
        FPanelBtnQrCodeImg.setBoxShadowConfigShadowColor("clBlack");
        FPanelBtnQrCodeImg.setBoxShadowConfigOpacity(75);
        FPanelBtnQrCodeImg.setBorderRadiusTopLeft(0);
        FPanelBtnQrCodeImg.setBorderRadiusTopRight(0);
        FPanelBtnQrCodeImg.setBorderRadiusBottomRight(0);
        FPanelBtnQrCodeImg.setBorderRadiusBottomLeft(0);
        FPanelBtnQrCodeImg.setColor("clBtnFace");
        FPanelBtnQrCodeImg.setWordBreak(false);
        FPanelBtnQrCodeVbox.addChildren(FPanelBtnQrCodeImg);
        FPanelBtnQrCodeImg.applyProperties();
        FPanelBtnQrCode.applyProperties();
    }

    public TFHBox vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1 = new TFHBox();

    private void init_vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1() {
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setName("vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setLeft(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setTop(67);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setWidth(76);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setHeight(8);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setBorderStyle("stNone");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setPaddingTop(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setPaddingLeft(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setPaddingRight(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setPaddingBottom(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setMarginTop(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setMarginLeft(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setMarginRight(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setMarginBottom(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setSpacing(1);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setFlexVflex("ftTrue");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setFlexHflex("ftTrue");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setScrollable(false);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setBoxShadowConfigVerticalLength(10);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setBoxShadowConfigBlurRadius(5);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setBoxShadowConfigOpacity(75);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.setVAlign("tvTop");
        vBoxBtnAcaoLerQRCodeSignatarioPrincipal.addChildren(vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1);
        vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1.applyProperties();
    }

    public TFHBox hboxIrParaVersaoAtual = new TFHBox();

    private void init_hboxIrParaVersaoAtual() {
        hboxIrParaVersaoAtual.setName("hboxIrParaVersaoAtual");
        hboxIrParaVersaoAtual.setLeft(0);
        hboxIrParaVersaoAtual.setTop(165);
        hboxIrParaVersaoAtual.setWidth(576);
        hboxIrParaVersaoAtual.setHeight(22);
        hboxIrParaVersaoAtual.setBorderStyle("stNone");
        hboxIrParaVersaoAtual.setPaddingTop(4);
        hboxIrParaVersaoAtual.setPaddingLeft(0);
        hboxIrParaVersaoAtual.setPaddingRight(0);
        hboxIrParaVersaoAtual.setPaddingBottom(0);
        hboxIrParaVersaoAtual.setMarginTop(0);
        hboxIrParaVersaoAtual.setMarginLeft(0);
        hboxIrParaVersaoAtual.setMarginRight(0);
        hboxIrParaVersaoAtual.setMarginBottom(0);
        hboxIrParaVersaoAtual.setSpacing(1);
        hboxIrParaVersaoAtual.setFlexVflex("ftFalse");
        hboxIrParaVersaoAtual.setFlexHflex("ftTrue");
        hboxIrParaVersaoAtual.setScrollable(false);
        hboxIrParaVersaoAtual.setBoxShadowConfigHorizontalLength(10);
        hboxIrParaVersaoAtual.setBoxShadowConfigVerticalLength(10);
        hboxIrParaVersaoAtual.setBoxShadowConfigBlurRadius(5);
        hboxIrParaVersaoAtual.setBoxShadowConfigSpreadRadius(0);
        hboxIrParaVersaoAtual.setBoxShadowConfigShadowColor("clBlack");
        hboxIrParaVersaoAtual.setBoxShadowConfigOpacity(75);
        hboxIrParaVersaoAtual.setVAlign("tvTop");
        boxPrinciapl.addChildren(hboxIrParaVersaoAtual);
        hboxIrParaVersaoAtual.applyProperties();
    }

    public TFLabel lblIrParaVersaoAtualVisualizacao = new TFLabel();

    private void init_lblIrParaVersaoAtualVisualizacao() {
        lblIrParaVersaoAtualVisualizacao.setName("lblIrParaVersaoAtualVisualizacao");
        lblIrParaVersaoAtualVisualizacao.setLeft(0);
        lblIrParaVersaoAtualVisualizacao.setTop(0);
        lblIrParaVersaoAtualVisualizacao.setWidth(158);
        lblIrParaVersaoAtualVisualizacao.setHeight(19);
        lblIrParaVersaoAtualVisualizacao.setAlign("alRight");
        lblIrParaVersaoAtualVisualizacao.setCaption("Apenas Vizualiza\u00E7\u00E3o - ");
        lblIrParaVersaoAtualVisualizacao.setFontColor("clWindowText");
        lblIrParaVersaoAtualVisualizacao.setFontSize(-16);
        lblIrParaVersaoAtualVisualizacao.setFontName("Tahoma");
        lblIrParaVersaoAtualVisualizacao.setFontStyle("[]");
        lblIrParaVersaoAtualVisualizacao.setVerticalAlignment("taVerticalCenter");
        lblIrParaVersaoAtualVisualizacao.setWordBreak(false);
        hboxIrParaVersaoAtual.addChildren(lblIrParaVersaoAtualVisualizacao);
        lblIrParaVersaoAtualVisualizacao.applyProperties();
    }

    public TFLabel lblIrParaVersaoAtual = new TFLabel();

    private void init_lblIrParaVersaoAtual() {
        lblIrParaVersaoAtual.setName("lblIrParaVersaoAtual");
        lblIrParaVersaoAtual.setLeft(158);
        lblIrParaVersaoAtual.setTop(0);
        lblIrParaVersaoAtual.setWidth(143);
        lblIrParaVersaoAtual.setHeight(19);
        lblIrParaVersaoAtual.setAlign("alRight");
        lblIrParaVersaoAtual.setCaption("Ir para Vers\u00E3o Atual");
        lblIrParaVersaoAtual.setFontColor("clWindowText");
        lblIrParaVersaoAtual.setFontSize(-16);
        lblIrParaVersaoAtual.setFontName("Tahoma");
        lblIrParaVersaoAtual.setFontStyle("[]");
        lblIrParaVersaoAtual.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblIrParaVersaoAtualClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "lblIrParaVersaoAtual", "OnClick");
        });
        lblIrParaVersaoAtual.setHiperLink(true);
        lblIrParaVersaoAtual.setVerticalAlignment("taVerticalCenter");
        lblIrParaVersaoAtual.setWordBreak(false);
        hboxIrParaVersaoAtual.addChildren(lblIrParaVersaoAtual);
        lblIrParaVersaoAtual.applyProperties();
    }

    public TFVBox vBoxGridSolicitacaoAssinatura = new TFVBox();

    private void init_vBoxGridSolicitacaoAssinatura() {
        vBoxGridSolicitacaoAssinatura.setName("vBoxGridSolicitacaoAssinatura");
        vBoxGridSolicitacaoAssinatura.setLeft(0);
        vBoxGridSolicitacaoAssinatura.setTop(188);
        vBoxGridSolicitacaoAssinatura.setWidth(576);
        vBoxGridSolicitacaoAssinatura.setHeight(308);
        vBoxGridSolicitacaoAssinatura.setAlign("alClient");
        vBoxGridSolicitacaoAssinatura.setBorderStyle("stNone");
        vBoxGridSolicitacaoAssinatura.setPaddingTop(0);
        vBoxGridSolicitacaoAssinatura.setPaddingLeft(0);
        vBoxGridSolicitacaoAssinatura.setPaddingRight(0);
        vBoxGridSolicitacaoAssinatura.setPaddingBottom(0);
        vBoxGridSolicitacaoAssinatura.setMarginTop(0);
        vBoxGridSolicitacaoAssinatura.setMarginLeft(0);
        vBoxGridSolicitacaoAssinatura.setMarginRight(0);
        vBoxGridSolicitacaoAssinatura.setMarginBottom(0);
        vBoxGridSolicitacaoAssinatura.setSpacing(5);
        vBoxGridSolicitacaoAssinatura.setFlexVflex("ftTrue");
        vBoxGridSolicitacaoAssinatura.setFlexHflex("ftTrue");
        vBoxGridSolicitacaoAssinatura.setScrollable(false);
        vBoxGridSolicitacaoAssinatura.setBoxShadowConfigHorizontalLength(10);
        vBoxGridSolicitacaoAssinatura.setBoxShadowConfigVerticalLength(10);
        vBoxGridSolicitacaoAssinatura.setBoxShadowConfigBlurRadius(5);
        vBoxGridSolicitacaoAssinatura.setBoxShadowConfigSpreadRadius(0);
        vBoxGridSolicitacaoAssinatura.setBoxShadowConfigShadowColor("clBlack");
        vBoxGridSolicitacaoAssinatura.setBoxShadowConfigOpacity(75);
        boxPrinciapl.addChildren(vBoxGridSolicitacaoAssinatura);
        vBoxGridSolicitacaoAssinatura.applyProperties();
    }

    public TFGrid FGrid1 = new TFGrid();

    private void init_FGrid1() {
        FGrid1.setName("FGrid1");
        FGrid1.setLeft(0);
        FGrid1.setTop(0);
        FGrid1.setWidth(571);
        FGrid1.setHeight(184);
        FGrid1.setTable(tbSolicitacoesAssinaturas);
        FGrid1.setFlexVflex("ftFalse");
        FGrid1.setFlexHflex("ftTrue");
        FGrid1.setPagingEnabled(false);
        FGrid1.setFrozenColumns(0);
        FGrid1.setShowFooter(false);
        FGrid1.setShowHeader(false);
        FGrid1.setMultiSelection(false);
        FGrid1.setGroupingEnabled(false);
        FGrid1.setGroupingExpanded(false);
        FGrid1.setGroupingShowFooter(false);
        FGrid1.setCrosstabEnabled(false);
        FGrid1.setCrosstabGroupType("cgtConcat");
        FGrid1.setEditionEnabled(false);
        FGrid1.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("VER");
        item0.setWidth(40);
        item0.setVisible(false);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        FGrid1.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(false);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setTemplate(VboxTemplateSolicitacaoAssinaturaPrincipal);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        item1.addEventListener("onRenderTemplate", (EventListener<RenderTemplateEvent<Value>>) (RenderTemplateEvent<Value> event) -> {
            FGrid1Columns1RenderTemplate(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "item1", "OnRenderTemplate");
        });
        FGrid1.getColumns().add(item1);
        vBoxGridSolicitacaoAssinatura.addChildren(FGrid1);
        FGrid1.applyProperties();
    }

    public TFPageControl pgCtrlDados = new TFPageControl();

    private void init_pgCtrlDados() {
        pgCtrlDados.setName("pgCtrlDados");
        pgCtrlDados.setLeft(0);
        pgCtrlDados.setTop(185);
        pgCtrlDados.setWidth(568);
        pgCtrlDados.setHeight(118);
        pgCtrlDados.setAlign("alClient");
        pgCtrlDados.setTabPosition("tpTop");
        pgCtrlDados.setFlexVflex("ftTrue");
        pgCtrlDados.setFlexHflex("ftTrue");
        pgCtrlDados.setRenderStyle("rsCard");
        pgCtrlDados.applyProperties();
        vBoxGridSolicitacaoAssinatura.addChildren(pgCtrlDados);
    }

    public TFTabsheet tabDadosAssinatura = new TFTabsheet();

    private void init_tabDadosAssinatura() {
        tabDadosAssinatura.setName("tabDadosAssinatura");
        tabDadosAssinatura.setCaption("Dados Assinatura");
        tabDadosAssinatura.setVisible(true);
        tabDadosAssinatura.setClosable(false);
        pgCtrlDados.addChildren(tabDadosAssinatura);
        tabDadosAssinatura.applyProperties();
    }

    public TFVBox hboxTabDadosAssinaturaMain = new TFVBox();

    private void init_hboxTabDadosAssinaturaMain() {
        hboxTabDadosAssinaturaMain.setName("hboxTabDadosAssinaturaMain");
        hboxTabDadosAssinaturaMain.setLeft(0);
        hboxTabDadosAssinaturaMain.setTop(0);
        hboxTabDadosAssinaturaMain.setWidth(560);
        hboxTabDadosAssinaturaMain.setHeight(90);
        hboxTabDadosAssinaturaMain.setAlign("alClient");
        hboxTabDadosAssinaturaMain.setBorderStyle("stSingleLine");
        hboxTabDadosAssinaturaMain.setPaddingTop(0);
        hboxTabDadosAssinaturaMain.setPaddingLeft(5);
        hboxTabDadosAssinaturaMain.setPaddingRight(0);
        hboxTabDadosAssinaturaMain.setPaddingBottom(0);
        hboxTabDadosAssinaturaMain.setMarginTop(0);
        hboxTabDadosAssinaturaMain.setMarginLeft(0);
        hboxTabDadosAssinaturaMain.setMarginRight(0);
        hboxTabDadosAssinaturaMain.setMarginBottom(0);
        hboxTabDadosAssinaturaMain.setSpacing(0);
        hboxTabDadosAssinaturaMain.setFlexVflex("ftTrue");
        hboxTabDadosAssinaturaMain.setFlexHflex("ftTrue");
        hboxTabDadosAssinaturaMain.setScrollable(false);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigHorizontalLength(10);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigVerticalLength(10);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigBlurRadius(5);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigSpreadRadius(0);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigShadowColor("clBlack");
        hboxTabDadosAssinaturaMain.setBoxShadowConfigOpacity(75);
        tabDadosAssinatura.addChildren(hboxTabDadosAssinaturaMain);
        hboxTabDadosAssinaturaMain.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(533);
        FHBox1.setHeight(65);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        hboxTabDadosAssinaturaMain.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox vboxObservador = new TFVBox();

    private void init_vboxObservador() {
        vboxObservador.setName("vboxObservador");
        vboxObservador.setLeft(0);
        vboxObservador.setTop(0);
        vboxObservador.setWidth(403);
        vboxObservador.setHeight(60);
        vboxObservador.setBorderStyle("stNone");
        vboxObservador.setPaddingTop(3);
        vboxObservador.setPaddingLeft(3);
        vboxObservador.setPaddingRight(3);
        vboxObservador.setPaddingBottom(3);
        vboxObservador.setMarginTop(0);
        vboxObservador.setMarginLeft(0);
        vboxObservador.setMarginRight(0);
        vboxObservador.setMarginBottom(0);
        vboxObservador.setSpacing(1);
        vboxObservador.setFlexVflex("ftFalse");
        vboxObservador.setFlexHflex("ftTrue");
        vboxObservador.setScrollable(false);
        vboxObservador.setBoxShadowConfigHorizontalLength(10);
        vboxObservador.setBoxShadowConfigVerticalLength(10);
        vboxObservador.setBoxShadowConfigBlurRadius(5);
        vboxObservador.setBoxShadowConfigSpreadRadius(0);
        vboxObservador.setBoxShadowConfigShadowColor("clBlack");
        vboxObservador.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(vboxObservador);
        vboxObservador.applyProperties();
    }

    public TFLabel lblObservador = new TFLabel();

    private void init_lblObservador() {
        lblObservador.setName("lblObservador");
        lblObservador.setLeft(0);
        lblObservador.setTop(0);
        lblObservador.setWidth(107);
        lblObservador.setHeight(13);
        lblObservador.setCaption("Observador (opcional)");
        lblObservador.setFontColor("clWindowText");
        lblObservador.setFontSize(-11);
        lblObservador.setFontName("Tahoma");
        lblObservador.setFontStyle("[]");
        lblObservador.setVerticalAlignment("taVerticalCenter");
        lblObservador.setWordBreak(false);
        vboxObservador.addChildren(lblObservador);
        lblObservador.applyProperties();
    }

    public TFString edObservador = new TFString();

    private void init_edObservador() {
        edObservador.setName("edObservador");
        edObservador.setLeft(0);
        edObservador.setTop(14);
        edObservador.setWidth(330);
        edObservador.setHeight(24);
        edObservador.setFlex(true);
        edObservador.setRequired(false);
        edObservador.setPrompt("ex: <EMAIL>, <EMAIL>");
        edObservador.setConstraintCheckWhen("cwImmediate");
        edObservador.setConstraintCheckType("ctExpression");
        edObservador.setConstraintFocusOnError(false);
        edObservador.setConstraintEnableUI(true);
        edObservador.setConstraintEnabled(false);
        edObservador.setConstraintFormCheck(true);
        edObservador.setCharCase("ccNormal");
        edObservador.setPwd(false);
        edObservador.setMaxlength(0);
        edObservador.setFontColor("clWindowText");
        edObservador.setFontSize(-13);
        edObservador.setFontName("Tahoma");
        edObservador.setFontStyle("[]");
        edObservador.setSaveLiteralCharacter(false);
        edObservador.applyProperties();
        vboxObservador.addChildren(edObservador);
        addValidatable(edObservador);
    }

    public TFVBox vboxBtnHistorico = new TFVBox();

    private void init_vboxBtnHistorico() {
        vboxBtnHistorico.setName("vboxBtnHistorico");
        vboxBtnHistorico.setLeft(403);
        vboxBtnHistorico.setTop(0);
        vboxBtnHistorico.setWidth(56);
        vboxBtnHistorico.setHeight(62);
        vboxBtnHistorico.setBorderStyle("stNone");
        vboxBtnHistorico.setPaddingTop(0);
        vboxBtnHistorico.setPaddingLeft(0);
        vboxBtnHistorico.setPaddingRight(0);
        vboxBtnHistorico.setPaddingBottom(0);
        vboxBtnHistorico.setMarginTop(0);
        vboxBtnHistorico.setMarginLeft(0);
        vboxBtnHistorico.setMarginRight(0);
        vboxBtnHistorico.setMarginBottom(0);
        vboxBtnHistorico.setSpacing(1);
        vboxBtnHistorico.setFlexVflex("ftFalse");
        vboxBtnHistorico.setFlexHflex("ftFalse");
        vboxBtnHistorico.setScrollable(false);
        vboxBtnHistorico.setBoxShadowConfigHorizontalLength(10);
        vboxBtnHistorico.setBoxShadowConfigVerticalLength(10);
        vboxBtnHistorico.setBoxShadowConfigBlurRadius(5);
        vboxBtnHistorico.setBoxShadowConfigSpreadRadius(0);
        vboxBtnHistorico.setBoxShadowConfigShadowColor("clBlack");
        vboxBtnHistorico.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(vboxBtnHistorico);
        vboxBtnHistorico.applyProperties();
    }

    public TFHBox vboxBtnHistoricoEspaco1 = new TFHBox();

    private void init_vboxBtnHistoricoEspaco1() {
        vboxBtnHistoricoEspaco1.setName("vboxBtnHistoricoEspaco1");
        vboxBtnHistoricoEspaco1.setLeft(0);
        vboxBtnHistoricoEspaco1.setTop(0);
        vboxBtnHistoricoEspaco1.setWidth(49);
        vboxBtnHistoricoEspaco1.setHeight(6);
        vboxBtnHistoricoEspaco1.setBorderStyle("stNone");
        vboxBtnHistoricoEspaco1.setPaddingTop(0);
        vboxBtnHistoricoEspaco1.setPaddingLeft(0);
        vboxBtnHistoricoEspaco1.setPaddingRight(0);
        vboxBtnHistoricoEspaco1.setPaddingBottom(0);
        vboxBtnHistoricoEspaco1.setMarginTop(0);
        vboxBtnHistoricoEspaco1.setMarginLeft(0);
        vboxBtnHistoricoEspaco1.setMarginRight(0);
        vboxBtnHistoricoEspaco1.setMarginBottom(0);
        vboxBtnHistoricoEspaco1.setSpacing(1);
        vboxBtnHistoricoEspaco1.setFlexVflex("ftFalse");
        vboxBtnHistoricoEspaco1.setFlexHflex("ftFalse");
        vboxBtnHistoricoEspaco1.setScrollable(false);
        vboxBtnHistoricoEspaco1.setBoxShadowConfigHorizontalLength(10);
        vboxBtnHistoricoEspaco1.setBoxShadowConfigVerticalLength(10);
        vboxBtnHistoricoEspaco1.setBoxShadowConfigBlurRadius(5);
        vboxBtnHistoricoEspaco1.setBoxShadowConfigSpreadRadius(0);
        vboxBtnHistoricoEspaco1.setBoxShadowConfigShadowColor("clBlack");
        vboxBtnHistoricoEspaco1.setBoxShadowConfigOpacity(75);
        vboxBtnHistoricoEspaco1.setVAlign("tvTop");
        vboxBtnHistorico.addChildren(vboxBtnHistoricoEspaco1);
        vboxBtnHistoricoEspaco1.applyProperties();
    }

    public TFButton btnHistorico = new TFButton();

    private void init_btnHistorico() {
        btnHistorico.setName("btnHistorico");
        btnHistorico.setLeft(0);
        btnHistorico.setTop(7);
        btnHistorico.setWidth(50);
        btnHistorico.setHeight(50);
        btnHistorico.setAlign("alLeft");
        btnHistorico.setCaption("Hist\u00F3rico");
        btnHistorico.setFontColor("clWindowText");
        btnHistorico.setFontSize(-11);
        btnHistorico.setFontName("Tahoma");
        btnHistorico.setFontStyle("[]");
        btnHistorico.setLayout("blGlyphTop");
        btnHistorico.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnHistoricoClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "btnHistorico", "OnClick");
        });
        btnHistorico.setImageId(0);
        btnHistorico.setColor("clBtnFace");
        btnHistorico.setAccess(false);
        btnHistorico.setIconClass("fas fa-history icon-color-black icon-24px");
        btnHistorico.setIconReverseDirection(false);
        vboxBtnHistorico.addChildren(btnHistorico);
        btnHistorico.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(459);
        FVBox1.setTop(0);
        FVBox1.setWidth(56);
        FVBox1.setHeight(62);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setVisible(false);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(49);
        FHBox2.setHeight(5);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnTesteLoading = new TFButton();

    private void init_btnTesteLoading() {
        btnTesteLoading.setName("btnTesteLoading");
        btnTesteLoading.setLeft(0);
        btnTesteLoading.setTop(6);
        btnTesteLoading.setWidth(50);
        btnTesteLoading.setHeight(50);
        btnTesteLoading.setAlign("alLeft");
        btnTesteLoading.setCaption("Teste Loading");
        btnTesteLoading.setFontColor("clWindowText");
        btnTesteLoading.setFontSize(-11);
        btnTesteLoading.setFontName("Tahoma");
        btnTesteLoading.setFontStyle("[]");
        btnTesteLoading.setLayout("blGlyphTop");
        btnTesteLoading.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnTesteLoadingClick(event);
            processarFlow("FrmAssinaturaDigitalSolicitacao", "btnTesteLoading", "OnClick");
        });
        btnTesteLoading.setImageId(0);
        btnTesteLoading.setColor("clBtnFace");
        btnTesteLoading.setAccess(false);
        btnTesteLoading.setIconClass("history");
        btnTesteLoading.setIconReverseDirection(true);
        FVBox1.addChildren(btnTesteLoading);
        btnTesteLoading.applyProperties();
    }

    public TFHBox vBoxGridSolicitacaoAssinaturaEspacoRodape = new TFHBox();

    private void init_vBoxGridSolicitacaoAssinaturaEspacoRodape() {
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setName("vBoxGridSolicitacaoAssinaturaEspacoRodape");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setLeft(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setTop(66);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setWidth(185);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setHeight(9);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBorderStyle("stNone");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingTop(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingLeft(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingRight(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingBottom(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginTop(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginLeft(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginRight(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginBottom(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setSpacing(1);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setFlexVflex("ftFalse");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setFlexHflex("ftFalse");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setScrollable(false);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigHorizontalLength(10);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigVerticalLength(10);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigBlurRadius(5);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigSpreadRadius(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigShadowColor("clBlack");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigOpacity(75);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setVAlign("tvTop");
        hboxTabDadosAssinaturaMain.addChildren(vBoxGridSolicitacaoAssinaturaEspacoRodape);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnEnviar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnEnviar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnReenviarClick(final Event<Object> event) {
        if (btnReenviar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnReenviar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnArquivarClick(final Event<Object> event) {
        if (btnArquivar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnArquivar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void hboxSelcaoRemotoClick(final Event<Object> event);

    public abstract void hboxSelcaoPresencialClick(final Event<Object> event);

    public abstract void FPanelBtnAcaoLerQRCodeClick(final Event<Object> event);

    public abstract void FPanelBtnAcaoBaixarDocumentoClick(final Event<Object> event);

    public abstract void hboxTemplateSolicitacaoAssinaturaLblEditarClick(final Event<Object> event);

    public abstract void FPanelBtnQrCodeClick(final Event<Object> event);

    public abstract void lblIrParaVersaoAtualClick(final Event<Object> event);

    public abstract void FGrid1Columns1RenderTemplate(final RenderTemplateEvent<Value> event);

    public void btnHistoricoClick(final Event<Object> event) {
        if (btnHistorico.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnHistorico");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnTesteLoadingClick(final Event<Object> event) {
        if (btnTesteLoading.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnTesteLoading");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void timerAtualizarStatusAssinaturaTimer(final Event<Object> event);

}