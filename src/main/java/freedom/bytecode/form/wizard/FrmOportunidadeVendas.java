package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmOportunidadeVendas extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.OportunidadeVendasRNA rn = null;

    public FrmOportunidadeVendas() {
        try {
            rn = (freedom.bytecode.rn.OportunidadeVendasRNA) getRN(freedom.bytecode.rn.wizard.OportunidadeVendasRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbAgendaPersiste();
        init_tbExistTipoOsLeadOpt();
        init_tbCheckExistePkg();
        init_FVBox1();
        init_hBoxTopoTelaAbr();
        init_btnVoltarOs();
        init_FHBox4();
        init_btnProximo();
        init_FHBox72();
        init_FHBox2();
        init_hBoxPretLabelTrocarVeic();
        init_FVBox2();
        init_FLabel1();
        init_lblValPretendeTrocarVeic();
        init_FHBox97();
        init_hBoxSimPretVender();
        init_FHBox21();
        init_FHBox26();
        init_lblOrdemServico();
        init_FHBox23();
        init_hBoxNaoPretVender();
        init_FHBox24();
        init_FHBox27();
        init_lblOrcamento();
        init_FHBox28();
        init_hBoxDataTrocar();
        init_hbLabelDataPretendeTrocarVeic();
        init_FVBox3();
        init_FLabel2();
        init_lblValidDataTrocarVeic();
        init_FHBox6();
        init_edtDataPretendeTrocar();
        init_hBoxVeicAtualMarca();
        init_hBoxLabelVeicDaMarca();
        init_FVBox4();
        init_FLabel5();
        init_lblValVeiculodaMarca();
        init_FHBox17();
        init_hBoxSimVeicAtMarca();
        init_FHBox19();
        init_FHBox20();
        init_FLabel6();
        init_FHBox22();
        init_hBoxNaoVeicAtMarca();
        init_FHBox29();
        init_FHBox30();
        init_FLabel7();
        init_FHBox31();
        init_hBoxPretendTrocarMsmModel();
        init_hBoxPretTrocMsmModel();
        init_FVBox5();
        init_FLabel8();
        init_lblValPretendeTrocarMsmModel();
        init_FHBox34();
        init_hBoxSimTrocarMod();
        init_FHBox36();
        init_FHBox37();
        init_FLabel9();
        init_FHBox38();
        init_hBoxNaoTrocarMod();
        init_FHBox40();
        init_FHBox41();
        init_FLabel10();
        init_FHBox42();
        init_hBoxFazerAvaliacao();
        init_hBoxLabelFazerAval();
        init_FVBox6();
        init_FLabel11();
        init_lblValDesejaFazerAval();
        init_FHBox45();
        init_hBoxSimAvaliacao();
        init_FHBox47();
        init_FHBox48();
        init_FLabel12();
        init_FHBox49();
        init_hBoxNaoAvaliacao();
        init_FHBox51();
        init_FHBox52();
        init_FLabel13();
        init_FHBox53();
        init_hBoxTipoVeicDeseja();
        init_hBoxLblTipoVeicDeseja();
        init_FVBox7();
        init_FLabel14();
        init_lblValTipoVecDeseja();
        init_FHBox56();
        init_hBoxVeicNovo();
        init_FHBox58();
        init_FHBox59();
        init_FLabel15();
        init_FHBox60();
        init_hBoxVeicSemiNovo();
        init_FHBox62();
        init_FHBox63();
        init_FLabel16();
        init_FHBox64();
        init_hBoxObsLabel();
        init_hbLblObsVend();
        init_FVBox8();
        init_FLabel17();
        init_memOportVendas();
        init_hboxespacofinal();
        init_sc();
        init_FrmOportunidadeVendas();
    }

    public OS_AGENDA_PERSISTE tbAgendaPersiste;

    private void init_tbAgendaPersiste() {
        tbAgendaPersiste = rn.tbAgendaPersiste;
        tbAgendaPersiste.setName("tbAgendaPersiste");
        tbAgendaPersiste.setMaxRowCount(200);
        tbAgendaPersiste.setWKey("4600296;46001");
        tbAgendaPersiste.setRatioBatchSize(20);
        getTables().put(tbAgendaPersiste, "tbAgendaPersiste");
        tbAgendaPersiste.applyProperties();
    }

    public EXIST_TIPO_OS_LEAD_OPT tbExistTipoOsLeadOpt;

    private void init_tbExistTipoOsLeadOpt() {
        tbExistTipoOsLeadOpt = rn.tbExistTipoOsLeadOpt;
        tbExistTipoOsLeadOpt.setName("tbExistTipoOsLeadOpt");
        tbExistTipoOsLeadOpt.setMaxRowCount(200);
        tbExistTipoOsLeadOpt.setWKey("4600296;46002");
        tbExistTipoOsLeadOpt.setRatioBatchSize(20);
        getTables().put(tbExistTipoOsLeadOpt, "tbExistTipoOsLeadOpt");
        tbExistTipoOsLeadOpt.applyProperties();
    }

    public CHECK_EXISTE_PKG tbCheckExistePkg;

    private void init_tbCheckExistePkg() {
        tbCheckExistePkg = rn.tbCheckExistePkg;
        tbCheckExistePkg.setName("tbCheckExistePkg");
        tbCheckExistePkg.setMaxRowCount(200);
        tbCheckExistePkg.setWKey("4600296;46003");
        tbCheckExistePkg.setRatioBatchSize(20);
        getTables().put(tbCheckExistePkg, "tbCheckExistePkg");
        tbCheckExistePkg.applyProperties();
    }

    protected TFForm FrmOportunidadeVendas = this;
    private void init_FrmOportunidadeVendas() {
        FrmOportunidadeVendas.setName("FrmOportunidadeVendas");
        FrmOportunidadeVendas.setCaption("Oportunidade de Vendas");
        FrmOportunidadeVendas.setClientHeight(549);
        FrmOportunidadeVendas.setClientWidth(479);
        FrmOportunidadeVendas.setColor("clBtnFace");
        FrmOportunidadeVendas.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmOportunidadeVendas", "FrmOportunidadeVendas", "OnCreate");
        });
        FrmOportunidadeVendas.setWOrigem("EhMain");
        FrmOportunidadeVendas.setWKey("4600296");
        FrmOportunidadeVendas.setSpacing(0);
        FrmOportunidadeVendas.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(479);
        FVBox1.setHeight(549);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(10);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftMin");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmOportunidadeVendas.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox hBoxTopoTelaAbr = new TFHBox();

    private void init_hBoxTopoTelaAbr() {
        hBoxTopoTelaAbr.setName("hBoxTopoTelaAbr");
        hBoxTopoTelaAbr.setLeft(0);
        hBoxTopoTelaAbr.setTop(0);
        hBoxTopoTelaAbr.setWidth(469);
        hBoxTopoTelaAbr.setHeight(61);
        hBoxTopoTelaAbr.setAlign("alTop");
        hBoxTopoTelaAbr.setBorderStyle("stSingleLine");
        hBoxTopoTelaAbr.setPaddingTop(0);
        hBoxTopoTelaAbr.setPaddingLeft(0);
        hBoxTopoTelaAbr.setPaddingRight(0);
        hBoxTopoTelaAbr.setPaddingBottom(0);
        hBoxTopoTelaAbr.setMarginTop(0);
        hBoxTopoTelaAbr.setMarginLeft(0);
        hBoxTopoTelaAbr.setMarginRight(0);
        hBoxTopoTelaAbr.setMarginBottom(0);
        hBoxTopoTelaAbr.setSpacing(1);
        hBoxTopoTelaAbr.setFlexVflex("ftFalse");
        hBoxTopoTelaAbr.setFlexHflex("ftTrue");
        hBoxTopoTelaAbr.setScrollable(false);
        hBoxTopoTelaAbr.setBoxShadowConfigHorizontalLength(10);
        hBoxTopoTelaAbr.setBoxShadowConfigVerticalLength(10);
        hBoxTopoTelaAbr.setBoxShadowConfigBlurRadius(5);
        hBoxTopoTelaAbr.setBoxShadowConfigSpreadRadius(0);
        hBoxTopoTelaAbr.setBoxShadowConfigShadowColor("clBlack");
        hBoxTopoTelaAbr.setBoxShadowConfigOpacity(75);
        hBoxTopoTelaAbr.setVAlign("tvTop");
        FVBox1.addChildren(hBoxTopoTelaAbr);
        hBoxTopoTelaAbr.applyProperties();
    }

    public TFButton btnVoltarOs = new TFButton();

    private void init_btnVoltarOs() {
        btnVoltarOs.setName("btnVoltarOs");
        btnVoltarOs.setUploadMime("image/*");
        btnVoltarOs.setLeft(0);
        btnVoltarOs.setTop(0);
        btnVoltarOs.setWidth(76);
        btnVoltarOs.setHeight(56);
        btnVoltarOs.setHint("Voltar");
        btnVoltarOs.setCaption("Cancelar");
        btnVoltarOs.setFontColor("clWindowText");
        btnVoltarOs.setFontSize(-16);
        btnVoltarOs.setFontName("Tahoma");
        btnVoltarOs.setFontStyle("[]");
        btnVoltarOs.setLayout("blGlyphTop");
        btnVoltarOs.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarOsClick(event);
            processarFlow("FrmOportunidadeVendas", "btnVoltarOs", "OnClick");
        });
        btnVoltarOs.setImageId(700081);
        btnVoltarOs.setColor("clBtnFace");
        btnVoltarOs.setAccess(false);
        btnVoltarOs.setIconReverseDirection(false);        hBoxTopoTelaAbr.addChildren(btnVoltarOs);
        btnVoltarOs.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(76);
        FHBox4.setTop(0);
        FHBox4.setWidth(7);
        FHBox4.setHeight(50);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        hBoxTopoTelaAbr.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFButton btnProximo = new TFButton();

    private void init_btnProximo() {
        btnProximo.setName("btnProximo");
        btnProximo.setUploadMime("image/*");
        btnProximo.setLeft(83);
        btnProximo.setTop(0);
        btnProximo.setWidth(76);
        btnProximo.setHeight(56);
        btnProximo.setHint("Proximo, Capa da O.S");
        btnProximo.setCaption("Continuar");
        btnProximo.setFontColor("clWindowText");
        btnProximo.setFontSize(-16);
        btnProximo.setFontName("Tahoma");
        btnProximo.setFontStyle("[]");
        btnProximo.setLayout("blGlyphTop");
        btnProximo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnProximoClick(event);
            processarFlow("FrmOportunidadeVendas", "btnProximo", "OnClick");
        });
        btnProximo.setImageId(700086);
        btnProximo.setColor("clBtnFace");
        btnProximo.setAccess(false);
        btnProximo.setIconReverseDirection(false);        hBoxTopoTelaAbr.addChildren(btnProximo);
        btnProximo.applyProperties();
    }

    public TFHBox FHBox72 = new TFHBox();

    private void init_FHBox72() {
        FHBox72.setName("FHBox72");
        FHBox72.setLeft(159);
        FHBox72.setTop(0);
        FHBox72.setWidth(7);
        FHBox72.setHeight(50);
        FHBox72.setBorderStyle("stNone");
        FHBox72.setPaddingTop(0);
        FHBox72.setPaddingLeft(0);
        FHBox72.setPaddingRight(0);
        FHBox72.setPaddingBottom(0);
        FHBox72.setMarginTop(0);
        FHBox72.setMarginLeft(0);
        FHBox72.setMarginRight(0);
        FHBox72.setMarginBottom(0);
        FHBox72.setSpacing(1);
        FHBox72.setFlexVflex("ftFalse");
        FHBox72.setFlexHflex("ftFalse");
        FHBox72.setScrollable(false);
        FHBox72.setBoxShadowConfigHorizontalLength(10);
        FHBox72.setBoxShadowConfigVerticalLength(10);
        FHBox72.setBoxShadowConfigBlurRadius(5);
        FHBox72.setBoxShadowConfigSpreadRadius(0);
        FHBox72.setBoxShadowConfigShadowColor("clBlack");
        FHBox72.setBoxShadowConfigOpacity(75);
        FHBox72.setVAlign("tvTop");
        hBoxTopoTelaAbr.addChildren(FHBox72);
        FHBox72.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(62);
        FHBox2.setWidth(470);
        FHBox2.setHeight(46);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(10);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox hBoxPretLabelTrocarVeic = new TFHBox();

    private void init_hBoxPretLabelTrocarVeic() {
        hBoxPretLabelTrocarVeic.setName("hBoxPretLabelTrocarVeic");
        hBoxPretLabelTrocarVeic.setLeft(0);
        hBoxPretLabelTrocarVeic.setTop(0);
        hBoxPretLabelTrocarVeic.setWidth(276);
        hBoxPretLabelTrocarVeic.setHeight(37);
        hBoxPretLabelTrocarVeic.setBorderStyle("stNone");
        hBoxPretLabelTrocarVeic.setPaddingTop(8);
        hBoxPretLabelTrocarVeic.setPaddingLeft(0);
        hBoxPretLabelTrocarVeic.setPaddingRight(0);
        hBoxPretLabelTrocarVeic.setPaddingBottom(0);
        hBoxPretLabelTrocarVeic.setMarginTop(0);
        hBoxPretLabelTrocarVeic.setMarginLeft(0);
        hBoxPretLabelTrocarVeic.setMarginRight(0);
        hBoxPretLabelTrocarVeic.setMarginBottom(0);
        hBoxPretLabelTrocarVeic.setSpacing(1);
        hBoxPretLabelTrocarVeic.setFlexVflex("ftFalse");
        hBoxPretLabelTrocarVeic.setFlexHflex("ftFalse");
        hBoxPretLabelTrocarVeic.setScrollable(false);
        hBoxPretLabelTrocarVeic.setBoxShadowConfigHorizontalLength(10);
        hBoxPretLabelTrocarVeic.setBoxShadowConfigVerticalLength(10);
        hBoxPretLabelTrocarVeic.setBoxShadowConfigBlurRadius(5);
        hBoxPretLabelTrocarVeic.setBoxShadowConfigSpreadRadius(0);
        hBoxPretLabelTrocarVeic.setBoxShadowConfigShadowColor("clBlack");
        hBoxPretLabelTrocarVeic.setBoxShadowConfigOpacity(75);
        hBoxPretLabelTrocarVeic.setVAlign("tvTop");
        FHBox2.addChildren(hBoxPretLabelTrocarVeic);
        hBoxPretLabelTrocarVeic.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(268);
        FVBox2.setHeight(32);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        hBoxPretLabelTrocarVeic.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(164);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Cliente Pretende Trocar o Ve\u00EDculo?");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox2.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFLabel lblValPretendeTrocarVeic = new TFLabel();

    private void init_lblValPretendeTrocarVeic() {
        lblValPretendeTrocarVeic.setName("lblValPretendeTrocarVeic");
        lblValPretendeTrocarVeic.setLeft(0);
        lblValPretendeTrocarVeic.setTop(14);
        lblValPretendeTrocarVeic.setWidth(227);
        lblValPretendeTrocarVeic.setHeight(13);
        lblValPretendeTrocarVeic.setCaption("Informe se o Cliente pretende Trocar o Veiculo.");
        lblValPretendeTrocarVeic.setFontColor("clRed");
        lblValPretendeTrocarVeic.setFontSize(-11);
        lblValPretendeTrocarVeic.setFontName("Tahoma");
        lblValPretendeTrocarVeic.setFontStyle("[]");
        lblValPretendeTrocarVeic.setVisible(false);
        lblValPretendeTrocarVeic.setVerticalAlignment("taVerticalCenter");
        lblValPretendeTrocarVeic.setWordBreak(false);
        FVBox2.addChildren(lblValPretendeTrocarVeic);
        lblValPretendeTrocarVeic.applyProperties();
    }

    public TFHBox FHBox97 = new TFHBox();

    private void init_FHBox97() {
        FHBox97.setName("FHBox97");
        FHBox97.setLeft(276);
        FHBox97.setTop(0);
        FHBox97.setWidth(180);
        FHBox97.setHeight(37);
        FHBox97.setBorderStyle("stNone");
        FHBox97.setPaddingTop(0);
        FHBox97.setPaddingLeft(0);
        FHBox97.setPaddingRight(0);
        FHBox97.setPaddingBottom(0);
        FHBox97.setMarginTop(0);
        FHBox97.setMarginLeft(0);
        FHBox97.setMarginRight(0);
        FHBox97.setMarginBottom(0);
        FHBox97.setSpacing(0);
        FHBox97.setFlexVflex("ftFalse");
        FHBox97.setFlexHflex("ftFalse");
        FHBox97.setScrollable(false);
        FHBox97.setBoxShadowConfigHorizontalLength(10);
        FHBox97.setBoxShadowConfigVerticalLength(10);
        FHBox97.setBoxShadowConfigBlurRadius(5);
        FHBox97.setBoxShadowConfigSpreadRadius(0);
        FHBox97.setBoxShadowConfigShadowColor("clBlack");
        FHBox97.setBoxShadowConfigOpacity(75);
        FHBox97.setVAlign("tvTop");
        FHBox2.addChildren(FHBox97);
        FHBox97.applyProperties();
    }

    public TFHBox hBoxSimPretVender = new TFHBox();

    private void init_hBoxSimPretVender() {
        hBoxSimPretVender.setName("hBoxSimPretVender");
        hBoxSimPretVender.setLeft(0);
        hBoxSimPretVender.setTop(0);
        hBoxSimPretVender.setWidth(90);
        hBoxSimPretVender.setHeight(35);
        hBoxSimPretVender.setAlign("alClient");
        hBoxSimPretVender.setBorderStyle("stNone");
        hBoxSimPretVender.setColor("clSilver");
        hBoxSimPretVender.setPaddingTop(0);
        hBoxSimPretVender.setPaddingLeft(0);
        hBoxSimPretVender.setPaddingRight(0);
        hBoxSimPretVender.setPaddingBottom(0);
        hBoxSimPretVender.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSimPretVenderClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxSimPretVender", "OnClick");
        });
        hBoxSimPretVender.setMarginTop(0);
        hBoxSimPretVender.setMarginLeft(0);
        hBoxSimPretVender.setMarginRight(0);
        hBoxSimPretVender.setMarginBottom(0);
        hBoxSimPretVender.setSpacing(1);
        hBoxSimPretVender.setFlexVflex("ftTrue");
        hBoxSimPretVender.setFlexHflex("ftTrue");
        hBoxSimPretVender.setScrollable(false);
        hBoxSimPretVender.setBoxShadowConfigHorizontalLength(10);
        hBoxSimPretVender.setBoxShadowConfigVerticalLength(10);
        hBoxSimPretVender.setBoxShadowConfigBlurRadius(5);
        hBoxSimPretVender.setBoxShadowConfigSpreadRadius(0);
        hBoxSimPretVender.setBoxShadowConfigShadowColor("clBlack");
        hBoxSimPretVender.setBoxShadowConfigOpacity(75);
        hBoxSimPretVender.setVAlign("tvTop");
        FHBox97.addChildren(hBoxSimPretVender);
        hBoxSimPretVender.applyProperties();
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(0);
        FHBox21.setTop(0);
        FHBox21.setWidth(21);
        FHBox21.setHeight(18);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(1);
        FHBox21.setFlexVflex("ftFalse");
        FHBox21.setFlexHflex("ftTrue");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        hBoxSimPretVender.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFHBox FHBox26 = new TFHBox();

    private void init_FHBox26() {
        FHBox26.setName("FHBox26");
        FHBox26.setLeft(21);
        FHBox26.setTop(0);
        FHBox26.setWidth(44);
        FHBox26.setHeight(31);
        FHBox26.setBorderStyle("stNone");
        FHBox26.setPaddingTop(10);
        FHBox26.setPaddingLeft(0);
        FHBox26.setPaddingRight(0);
        FHBox26.setPaddingBottom(0);
        FHBox26.setMarginTop(0);
        FHBox26.setMarginLeft(0);
        FHBox26.setMarginRight(0);
        FHBox26.setMarginBottom(0);
        FHBox26.setSpacing(1);
        FHBox26.setFlexVflex("ftFalse");
        FHBox26.setFlexHflex("ftMin");
        FHBox26.setScrollable(false);
        FHBox26.setBoxShadowConfigHorizontalLength(10);
        FHBox26.setBoxShadowConfigVerticalLength(10);
        FHBox26.setBoxShadowConfigBlurRadius(5);
        FHBox26.setBoxShadowConfigSpreadRadius(0);
        FHBox26.setBoxShadowConfigShadowColor("clBlack");
        FHBox26.setBoxShadowConfigOpacity(75);
        FHBox26.setVAlign("tvTop");
        hBoxSimPretVender.addChildren(FHBox26);
        FHBox26.applyProperties();
    }

    public TFLabel lblOrdemServico = new TFLabel();

    private void init_lblOrdemServico() {
        lblOrdemServico.setName("lblOrdemServico");
        lblOrdemServico.setLeft(0);
        lblOrdemServico.setTop(0);
        lblOrdemServico.setWidth(28);
        lblOrdemServico.setHeight(18);
        lblOrdemServico.setCaption("Sim");
        lblOrdemServico.setFontColor("clBlack");
        lblOrdemServico.setFontSize(-15);
        lblOrdemServico.setFontName("Tahoma");
        lblOrdemServico.setFontStyle("[fsBold]");
        lblOrdemServico.setVerticalAlignment("taVerticalCenter");
        lblOrdemServico.setWordBreak(false);
        FHBox26.addChildren(lblOrdemServico);
        lblOrdemServico.applyProperties();
    }

    public TFHBox FHBox23 = new TFHBox();

    private void init_FHBox23() {
        FHBox23.setName("FHBox23");
        FHBox23.setLeft(65);
        FHBox23.setTop(0);
        FHBox23.setWidth(19);
        FHBox23.setHeight(18);
        FHBox23.setBorderStyle("stNone");
        FHBox23.setPaddingTop(0);
        FHBox23.setPaddingLeft(0);
        FHBox23.setPaddingRight(0);
        FHBox23.setPaddingBottom(0);
        FHBox23.setMarginTop(0);
        FHBox23.setMarginLeft(0);
        FHBox23.setMarginRight(0);
        FHBox23.setMarginBottom(0);
        FHBox23.setSpacing(1);
        FHBox23.setFlexVflex("ftFalse");
        FHBox23.setFlexHflex("ftTrue");
        FHBox23.setScrollable(false);
        FHBox23.setBoxShadowConfigHorizontalLength(10);
        FHBox23.setBoxShadowConfigVerticalLength(10);
        FHBox23.setBoxShadowConfigBlurRadius(5);
        FHBox23.setBoxShadowConfigSpreadRadius(0);
        FHBox23.setBoxShadowConfigShadowColor("clBlack");
        FHBox23.setBoxShadowConfigOpacity(75);
        FHBox23.setVAlign("tvTop");
        hBoxSimPretVender.addChildren(FHBox23);
        FHBox23.applyProperties();
    }

    public TFHBox hBoxNaoPretVender = new TFHBox();

    private void init_hBoxNaoPretVender() {
        hBoxNaoPretVender.setName("hBoxNaoPretVender");
        hBoxNaoPretVender.setLeft(90);
        hBoxNaoPretVender.setTop(0);
        hBoxNaoPretVender.setWidth(94);
        hBoxNaoPretVender.setHeight(35);
        hBoxNaoPretVender.setAlign("alClient");
        hBoxNaoPretVender.setBorderStyle("stNone");
        hBoxNaoPretVender.setColor("clSilver");
        hBoxNaoPretVender.setPaddingTop(0);
        hBoxNaoPretVender.setPaddingLeft(0);
        hBoxNaoPretVender.setPaddingRight(0);
        hBoxNaoPretVender.setPaddingBottom(0);
        hBoxNaoPretVender.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxNaoPretVenderClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxNaoPretVender", "OnClick");
        });
        hBoxNaoPretVender.setMarginTop(0);
        hBoxNaoPretVender.setMarginLeft(0);
        hBoxNaoPretVender.setMarginRight(0);
        hBoxNaoPretVender.setMarginBottom(0);
        hBoxNaoPretVender.setSpacing(1);
        hBoxNaoPretVender.setFlexVflex("ftTrue");
        hBoxNaoPretVender.setFlexHflex("ftTrue");
        hBoxNaoPretVender.setScrollable(false);
        hBoxNaoPretVender.setBoxShadowConfigHorizontalLength(10);
        hBoxNaoPretVender.setBoxShadowConfigVerticalLength(10);
        hBoxNaoPretVender.setBoxShadowConfigBlurRadius(5);
        hBoxNaoPretVender.setBoxShadowConfigSpreadRadius(0);
        hBoxNaoPretVender.setBoxShadowConfigShadowColor("clBlack");
        hBoxNaoPretVender.setBoxShadowConfigOpacity(75);
        hBoxNaoPretVender.setVAlign("tvTop");
        FHBox97.addChildren(hBoxNaoPretVender);
        hBoxNaoPretVender.applyProperties();
    }

    public TFHBox FHBox24 = new TFHBox();

    private void init_FHBox24() {
        FHBox24.setName("FHBox24");
        FHBox24.setLeft(0);
        FHBox24.setTop(0);
        FHBox24.setWidth(15);
        FHBox24.setHeight(18);
        FHBox24.setBorderStyle("stNone");
        FHBox24.setPaddingTop(0);
        FHBox24.setPaddingLeft(0);
        FHBox24.setPaddingRight(0);
        FHBox24.setPaddingBottom(0);
        FHBox24.setMarginTop(0);
        FHBox24.setMarginLeft(0);
        FHBox24.setMarginRight(0);
        FHBox24.setMarginBottom(0);
        FHBox24.setSpacing(1);
        FHBox24.setFlexVflex("ftFalse");
        FHBox24.setFlexHflex("ftTrue");
        FHBox24.setScrollable(false);
        FHBox24.setBoxShadowConfigHorizontalLength(10);
        FHBox24.setBoxShadowConfigVerticalLength(10);
        FHBox24.setBoxShadowConfigBlurRadius(5);
        FHBox24.setBoxShadowConfigSpreadRadius(0);
        FHBox24.setBoxShadowConfigShadowColor("clBlack");
        FHBox24.setBoxShadowConfigOpacity(75);
        FHBox24.setVAlign("tvTop");
        hBoxNaoPretVender.addChildren(FHBox24);
        FHBox24.applyProperties();
    }

    public TFHBox FHBox27 = new TFHBox();

    private void init_FHBox27() {
        FHBox27.setName("FHBox27");
        FHBox27.setLeft(15);
        FHBox27.setTop(0);
        FHBox27.setWidth(50);
        FHBox27.setHeight(33);
        FHBox27.setBorderStyle("stNone");
        FHBox27.setPaddingTop(10);
        FHBox27.setPaddingLeft(0);
        FHBox27.setPaddingRight(0);
        FHBox27.setPaddingBottom(0);
        FHBox27.setMarginTop(0);
        FHBox27.setMarginLeft(0);
        FHBox27.setMarginRight(0);
        FHBox27.setMarginBottom(0);
        FHBox27.setSpacing(1);
        FHBox27.setFlexVflex("ftFalse");
        FHBox27.setFlexHflex("ftMin");
        FHBox27.setScrollable(false);
        FHBox27.setBoxShadowConfigHorizontalLength(10);
        FHBox27.setBoxShadowConfigVerticalLength(10);
        FHBox27.setBoxShadowConfigBlurRadius(5);
        FHBox27.setBoxShadowConfigSpreadRadius(0);
        FHBox27.setBoxShadowConfigShadowColor("clBlack");
        FHBox27.setBoxShadowConfigOpacity(75);
        FHBox27.setVAlign("tvTop");
        hBoxNaoPretVender.addChildren(FHBox27);
        FHBox27.applyProperties();
    }

    public TFLabel lblOrcamento = new TFLabel();

    private void init_lblOrcamento() {
        lblOrcamento.setName("lblOrcamento");
        lblOrcamento.setLeft(0);
        lblOrcamento.setTop(0);
        lblOrcamento.setWidth(30);
        lblOrcamento.setHeight(18);
        lblOrcamento.setCaption("N\u00E3o");
        lblOrcamento.setFontColor("clBlack");
        lblOrcamento.setFontSize(-15);
        lblOrcamento.setFontName("Tahoma");
        lblOrcamento.setFontStyle("[fsBold]");
        lblOrcamento.setVerticalAlignment("taVerticalCenter");
        lblOrcamento.setWordBreak(false);
        FHBox27.addChildren(lblOrcamento);
        lblOrcamento.applyProperties();
    }

    public TFHBox FHBox28 = new TFHBox();

    private void init_FHBox28() {
        FHBox28.setName("FHBox28");
        FHBox28.setLeft(65);
        FHBox28.setTop(0);
        FHBox28.setWidth(15);
        FHBox28.setHeight(18);
        FHBox28.setBorderStyle("stNone");
        FHBox28.setPaddingTop(0);
        FHBox28.setPaddingLeft(0);
        FHBox28.setPaddingRight(0);
        FHBox28.setPaddingBottom(0);
        FHBox28.setMarginTop(0);
        FHBox28.setMarginLeft(0);
        FHBox28.setMarginRight(0);
        FHBox28.setMarginBottom(0);
        FHBox28.setSpacing(1);
        FHBox28.setFlexVflex("ftFalse");
        FHBox28.setFlexHflex("ftTrue");
        FHBox28.setScrollable(false);
        FHBox28.setBoxShadowConfigHorizontalLength(10);
        FHBox28.setBoxShadowConfigVerticalLength(10);
        FHBox28.setBoxShadowConfigBlurRadius(5);
        FHBox28.setBoxShadowConfigSpreadRadius(0);
        FHBox28.setBoxShadowConfigShadowColor("clBlack");
        FHBox28.setBoxShadowConfigOpacity(75);
        FHBox28.setVAlign("tvTop");
        hBoxNaoPretVender.addChildren(FHBox28);
        FHBox28.applyProperties();
    }

    public TFHBox hBoxDataTrocar = new TFHBox();

    private void init_hBoxDataTrocar() {
        hBoxDataTrocar.setName("hBoxDataTrocar");
        hBoxDataTrocar.setLeft(0);
        hBoxDataTrocar.setTop(109);
        hBoxDataTrocar.setWidth(470);
        hBoxDataTrocar.setHeight(46);
        hBoxDataTrocar.setBorderStyle("stNone");
        hBoxDataTrocar.setPaddingTop(10);
        hBoxDataTrocar.setPaddingLeft(0);
        hBoxDataTrocar.setPaddingRight(0);
        hBoxDataTrocar.setPaddingBottom(0);
        hBoxDataTrocar.setVisible(false);
        hBoxDataTrocar.setMarginTop(0);
        hBoxDataTrocar.setMarginLeft(0);
        hBoxDataTrocar.setMarginRight(0);
        hBoxDataTrocar.setMarginBottom(0);
        hBoxDataTrocar.setSpacing(1);
        hBoxDataTrocar.setFlexVflex("ftFalse");
        hBoxDataTrocar.setFlexHflex("ftTrue");
        hBoxDataTrocar.setScrollable(false);
        hBoxDataTrocar.setBoxShadowConfigHorizontalLength(10);
        hBoxDataTrocar.setBoxShadowConfigVerticalLength(10);
        hBoxDataTrocar.setBoxShadowConfigBlurRadius(5);
        hBoxDataTrocar.setBoxShadowConfigSpreadRadius(0);
        hBoxDataTrocar.setBoxShadowConfigShadowColor("clBlack");
        hBoxDataTrocar.setBoxShadowConfigOpacity(75);
        hBoxDataTrocar.setVAlign("tvTop");
        FVBox1.addChildren(hBoxDataTrocar);
        hBoxDataTrocar.applyProperties();
    }

    public TFHBox hbLabelDataPretendeTrocarVeic = new TFHBox();

    private void init_hbLabelDataPretendeTrocarVeic() {
        hbLabelDataPretendeTrocarVeic.setName("hbLabelDataPretendeTrocarVeic");
        hbLabelDataPretendeTrocarVeic.setLeft(0);
        hbLabelDataPretendeTrocarVeic.setTop(0);
        hbLabelDataPretendeTrocarVeic.setWidth(276);
        hbLabelDataPretendeTrocarVeic.setHeight(37);
        hbLabelDataPretendeTrocarVeic.setBorderStyle("stNone");
        hbLabelDataPretendeTrocarVeic.setPaddingTop(8);
        hbLabelDataPretendeTrocarVeic.setPaddingLeft(0);
        hbLabelDataPretendeTrocarVeic.setPaddingRight(0);
        hbLabelDataPretendeTrocarVeic.setPaddingBottom(0);
        hbLabelDataPretendeTrocarVeic.setMarginTop(0);
        hbLabelDataPretendeTrocarVeic.setMarginLeft(0);
        hbLabelDataPretendeTrocarVeic.setMarginRight(0);
        hbLabelDataPretendeTrocarVeic.setMarginBottom(0);
        hbLabelDataPretendeTrocarVeic.setSpacing(1);
        hbLabelDataPretendeTrocarVeic.setFlexVflex("ftFalse");
        hbLabelDataPretendeTrocarVeic.setFlexHflex("ftFalse");
        hbLabelDataPretendeTrocarVeic.setScrollable(false);
        hbLabelDataPretendeTrocarVeic.setBoxShadowConfigHorizontalLength(10);
        hbLabelDataPretendeTrocarVeic.setBoxShadowConfigVerticalLength(10);
        hbLabelDataPretendeTrocarVeic.setBoxShadowConfigBlurRadius(5);
        hbLabelDataPretendeTrocarVeic.setBoxShadowConfigSpreadRadius(0);
        hbLabelDataPretendeTrocarVeic.setBoxShadowConfigShadowColor("clBlack");
        hbLabelDataPretendeTrocarVeic.setBoxShadowConfigOpacity(75);
        hbLabelDataPretendeTrocarVeic.setVAlign("tvTop");
        hBoxDataTrocar.addChildren(hbLabelDataPretendeTrocarVeic);
        hbLabelDataPretendeTrocarVeic.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(267);
        FVBox3.setHeight(35);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        hbLabelDataPretendeTrocarVeic.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(130);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Data que Pretende Trocar?");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox3.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFLabel lblValidDataTrocarVeic = new TFLabel();

    private void init_lblValidDataTrocarVeic() {
        lblValidDataTrocarVeic.setName("lblValidDataTrocarVeic");
        lblValidDataTrocarVeic.setLeft(0);
        lblValidDataTrocarVeic.setTop(14);
        lblValidDataTrocarVeic.setWidth(220);
        lblValidDataTrocarVeic.setHeight(13);
        lblValidDataTrocarVeic.setCaption("Informe a Data que pretende Trocar o Veiculo");
        lblValidDataTrocarVeic.setFontColor("clRed");
        lblValidDataTrocarVeic.setFontSize(-11);
        lblValidDataTrocarVeic.setFontName("Tahoma");
        lblValidDataTrocarVeic.setFontStyle("[]");
        lblValidDataTrocarVeic.setVisible(false);
        lblValidDataTrocarVeic.setVerticalAlignment("taVerticalCenter");
        lblValidDataTrocarVeic.setWordBreak(false);
        FVBox3.addChildren(lblValidDataTrocarVeic);
        lblValidDataTrocarVeic.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(276);
        FHBox6.setTop(0);
        FHBox6.setWidth(180);
        FHBox6.setHeight(37);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        hBoxDataTrocar.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFDate edtDataPretendeTrocar = new TFDate();

    private void init_edtDataPretendeTrocar() {
        edtDataPretendeTrocar.setName("edtDataPretendeTrocar");
        edtDataPretendeTrocar.setLeft(0);
        edtDataPretendeTrocar.setTop(0);
        edtDataPretendeTrocar.setWidth(178);
        edtDataPretendeTrocar.setHeight(24);
        edtDataPretendeTrocar.setFlex(false);
        edtDataPretendeTrocar.setRequired(false);
        edtDataPretendeTrocar.setConstraintCheckWhen("cwImmediate");
        edtDataPretendeTrocar.setConstraintCheckType("ctExpression");
        edtDataPretendeTrocar.setConstraintFocusOnError(false);
        edtDataPretendeTrocar.setConstraintEnableUI(true);
        edtDataPretendeTrocar.setConstraintEnabled(false);
        edtDataPretendeTrocar.setConstraintFormCheck(true);
        edtDataPretendeTrocar.setFormat("dd/MM/yyyy");
        edtDataPretendeTrocar.setShowCheckBox(false);
        edtDataPretendeTrocar.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtDataPretendeTrocarChange(event);
            processarFlow("FrmOportunidadeVendas", "edtDataPretendeTrocar", "OnChange");
        });
        FHBox6.addChildren(edtDataPretendeTrocar);
        edtDataPretendeTrocar.applyProperties();
        addValidatable(edtDataPretendeTrocar);
    }

    public TFHBox hBoxVeicAtualMarca = new TFHBox();

    private void init_hBoxVeicAtualMarca() {
        hBoxVeicAtualMarca.setName("hBoxVeicAtualMarca");
        hBoxVeicAtualMarca.setLeft(0);
        hBoxVeicAtualMarca.setTop(156);
        hBoxVeicAtualMarca.setWidth(470);
        hBoxVeicAtualMarca.setHeight(46);
        hBoxVeicAtualMarca.setBorderStyle("stNone");
        hBoxVeicAtualMarca.setPaddingTop(10);
        hBoxVeicAtualMarca.setPaddingLeft(0);
        hBoxVeicAtualMarca.setPaddingRight(0);
        hBoxVeicAtualMarca.setPaddingBottom(0);
        hBoxVeicAtualMarca.setVisible(false);
        hBoxVeicAtualMarca.setMarginTop(0);
        hBoxVeicAtualMarca.setMarginLeft(0);
        hBoxVeicAtualMarca.setMarginRight(0);
        hBoxVeicAtualMarca.setMarginBottom(0);
        hBoxVeicAtualMarca.setSpacing(1);
        hBoxVeicAtualMarca.setFlexVflex("ftFalse");
        hBoxVeicAtualMarca.setFlexHflex("ftTrue");
        hBoxVeicAtualMarca.setScrollable(false);
        hBoxVeicAtualMarca.setBoxShadowConfigHorizontalLength(10);
        hBoxVeicAtualMarca.setBoxShadowConfigVerticalLength(10);
        hBoxVeicAtualMarca.setBoxShadowConfigBlurRadius(5);
        hBoxVeicAtualMarca.setBoxShadowConfigSpreadRadius(0);
        hBoxVeicAtualMarca.setBoxShadowConfigShadowColor("clBlack");
        hBoxVeicAtualMarca.setBoxShadowConfigOpacity(75);
        hBoxVeicAtualMarca.setVAlign("tvTop");
        FVBox1.addChildren(hBoxVeicAtualMarca);
        hBoxVeicAtualMarca.applyProperties();
    }

    public TFHBox hBoxLabelVeicDaMarca = new TFHBox();

    private void init_hBoxLabelVeicDaMarca() {
        hBoxLabelVeicDaMarca.setName("hBoxLabelVeicDaMarca");
        hBoxLabelVeicDaMarca.setLeft(0);
        hBoxLabelVeicDaMarca.setTop(0);
        hBoxLabelVeicDaMarca.setWidth(276);
        hBoxLabelVeicDaMarca.setHeight(37);
        hBoxLabelVeicDaMarca.setBorderStyle("stNone");
        hBoxLabelVeicDaMarca.setPaddingTop(8);
        hBoxLabelVeicDaMarca.setPaddingLeft(0);
        hBoxLabelVeicDaMarca.setPaddingRight(0);
        hBoxLabelVeicDaMarca.setPaddingBottom(0);
        hBoxLabelVeicDaMarca.setMarginTop(0);
        hBoxLabelVeicDaMarca.setMarginLeft(0);
        hBoxLabelVeicDaMarca.setMarginRight(0);
        hBoxLabelVeicDaMarca.setMarginBottom(0);
        hBoxLabelVeicDaMarca.setSpacing(1);
        hBoxLabelVeicDaMarca.setFlexVflex("ftFalse");
        hBoxLabelVeicDaMarca.setFlexHflex("ftFalse");
        hBoxLabelVeicDaMarca.setScrollable(false);
        hBoxLabelVeicDaMarca.setBoxShadowConfigHorizontalLength(10);
        hBoxLabelVeicDaMarca.setBoxShadowConfigVerticalLength(10);
        hBoxLabelVeicDaMarca.setBoxShadowConfigBlurRadius(5);
        hBoxLabelVeicDaMarca.setBoxShadowConfigSpreadRadius(0);
        hBoxLabelVeicDaMarca.setBoxShadowConfigShadowColor("clBlack");
        hBoxLabelVeicDaMarca.setBoxShadowConfigOpacity(75);
        hBoxLabelVeicDaMarca.setVAlign("tvTop");
        hBoxVeicAtualMarca.addChildren(hBoxLabelVeicDaMarca);
        hBoxLabelVeicDaMarca.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(0);
        FVBox4.setWidth(266);
        FVBox4.setHeight(34);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hBoxLabelVeicDaMarca.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(0);
        FLabel5.setWidth(121);
        FLabel5.setHeight(13);
        FLabel5.setCaption("Veiculo atual \u00E9 da Marca?");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FVBox4.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFLabel lblValVeiculodaMarca = new TFLabel();

    private void init_lblValVeiculodaMarca() {
        lblValVeiculodaMarca.setName("lblValVeiculodaMarca");
        lblValVeiculodaMarca.setLeft(0);
        lblValVeiculodaMarca.setTop(14);
        lblValVeiculodaMarca.setWidth(181);
        lblValVeiculodaMarca.setHeight(13);
        lblValVeiculodaMarca.setCaption("Informe se o Ve\u00EDculo Atual \u00E9 da Marca");
        lblValVeiculodaMarca.setFontColor("clRed");
        lblValVeiculodaMarca.setFontSize(-11);
        lblValVeiculodaMarca.setFontName("Tahoma");
        lblValVeiculodaMarca.setFontStyle("[]");
        lblValVeiculodaMarca.setVisible(false);
        lblValVeiculodaMarca.setVerticalAlignment("taVerticalCenter");
        lblValVeiculodaMarca.setWordBreak(false);
        FVBox4.addChildren(lblValVeiculodaMarca);
        lblValVeiculodaMarca.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(276);
        FHBox17.setTop(0);
        FHBox17.setWidth(180);
        FHBox17.setHeight(37);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(0);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(0);
        FHBox17.setFlexVflex("ftFalse");
        FHBox17.setFlexHflex("ftFalse");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        hBoxVeicAtualMarca.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFHBox hBoxSimVeicAtMarca = new TFHBox();

    private void init_hBoxSimVeicAtMarca() {
        hBoxSimVeicAtMarca.setName("hBoxSimVeicAtMarca");
        hBoxSimVeicAtMarca.setLeft(0);
        hBoxSimVeicAtMarca.setTop(0);
        hBoxSimVeicAtMarca.setWidth(90);
        hBoxSimVeicAtMarca.setHeight(35);
        hBoxSimVeicAtMarca.setAlign("alClient");
        hBoxSimVeicAtMarca.setBorderStyle("stNone");
        hBoxSimVeicAtMarca.setColor("clSilver");
        hBoxSimVeicAtMarca.setPaddingTop(0);
        hBoxSimVeicAtMarca.setPaddingLeft(0);
        hBoxSimVeicAtMarca.setPaddingRight(0);
        hBoxSimVeicAtMarca.setPaddingBottom(0);
        hBoxSimVeicAtMarca.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSimVeicAtMarcaClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxSimVeicAtMarca", "OnClick");
        });
        hBoxSimVeicAtMarca.setMarginTop(0);
        hBoxSimVeicAtMarca.setMarginLeft(0);
        hBoxSimVeicAtMarca.setMarginRight(0);
        hBoxSimVeicAtMarca.setMarginBottom(0);
        hBoxSimVeicAtMarca.setSpacing(1);
        hBoxSimVeicAtMarca.setFlexVflex("ftTrue");
        hBoxSimVeicAtMarca.setFlexHflex("ftTrue");
        hBoxSimVeicAtMarca.setScrollable(false);
        hBoxSimVeicAtMarca.setBoxShadowConfigHorizontalLength(10);
        hBoxSimVeicAtMarca.setBoxShadowConfigVerticalLength(10);
        hBoxSimVeicAtMarca.setBoxShadowConfigBlurRadius(5);
        hBoxSimVeicAtMarca.setBoxShadowConfigSpreadRadius(0);
        hBoxSimVeicAtMarca.setBoxShadowConfigShadowColor("clBlack");
        hBoxSimVeicAtMarca.setBoxShadowConfigOpacity(75);
        hBoxSimVeicAtMarca.setVAlign("tvTop");
        FHBox17.addChildren(hBoxSimVeicAtMarca);
        hBoxSimVeicAtMarca.applyProperties();
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(0);
        FHBox19.setWidth(21);
        FHBox19.setHeight(18);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftFalse");
        FHBox19.setFlexHflex("ftTrue");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        hBoxSimVeicAtMarca.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(21);
        FHBox20.setTop(0);
        FHBox20.setWidth(44);
        FHBox20.setHeight(31);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(10);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftMin");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        hBoxSimVeicAtMarca.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(0);
        FLabel6.setTop(0);
        FLabel6.setWidth(28);
        FLabel6.setHeight(18);
        FLabel6.setCaption("Sim");
        FLabel6.setFontColor("clBlack");
        FLabel6.setFontSize(-15);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[fsBold]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FHBox20.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFHBox FHBox22 = new TFHBox();

    private void init_FHBox22() {
        FHBox22.setName("FHBox22");
        FHBox22.setLeft(65);
        FHBox22.setTop(0);
        FHBox22.setWidth(19);
        FHBox22.setHeight(18);
        FHBox22.setBorderStyle("stNone");
        FHBox22.setPaddingTop(0);
        FHBox22.setPaddingLeft(0);
        FHBox22.setPaddingRight(0);
        FHBox22.setPaddingBottom(0);
        FHBox22.setMarginTop(0);
        FHBox22.setMarginLeft(0);
        FHBox22.setMarginRight(0);
        FHBox22.setMarginBottom(0);
        FHBox22.setSpacing(1);
        FHBox22.setFlexVflex("ftFalse");
        FHBox22.setFlexHflex("ftTrue");
        FHBox22.setScrollable(false);
        FHBox22.setBoxShadowConfigHorizontalLength(10);
        FHBox22.setBoxShadowConfigVerticalLength(10);
        FHBox22.setBoxShadowConfigBlurRadius(5);
        FHBox22.setBoxShadowConfigSpreadRadius(0);
        FHBox22.setBoxShadowConfigShadowColor("clBlack");
        FHBox22.setBoxShadowConfigOpacity(75);
        FHBox22.setVAlign("tvTop");
        hBoxSimVeicAtMarca.addChildren(FHBox22);
        FHBox22.applyProperties();
    }

    public TFHBox hBoxNaoVeicAtMarca = new TFHBox();

    private void init_hBoxNaoVeicAtMarca() {
        hBoxNaoVeicAtMarca.setName("hBoxNaoVeicAtMarca");
        hBoxNaoVeicAtMarca.setLeft(90);
        hBoxNaoVeicAtMarca.setTop(0);
        hBoxNaoVeicAtMarca.setWidth(94);
        hBoxNaoVeicAtMarca.setHeight(35);
        hBoxNaoVeicAtMarca.setAlign("alClient");
        hBoxNaoVeicAtMarca.setBorderStyle("stNone");
        hBoxNaoVeicAtMarca.setColor("clSilver");
        hBoxNaoVeicAtMarca.setPaddingTop(0);
        hBoxNaoVeicAtMarca.setPaddingLeft(0);
        hBoxNaoVeicAtMarca.setPaddingRight(0);
        hBoxNaoVeicAtMarca.setPaddingBottom(0);
        hBoxNaoVeicAtMarca.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxNaoVeicAtMarcaClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxNaoVeicAtMarca", "OnClick");
        });
        hBoxNaoVeicAtMarca.setMarginTop(0);
        hBoxNaoVeicAtMarca.setMarginLeft(0);
        hBoxNaoVeicAtMarca.setMarginRight(0);
        hBoxNaoVeicAtMarca.setMarginBottom(0);
        hBoxNaoVeicAtMarca.setSpacing(1);
        hBoxNaoVeicAtMarca.setFlexVflex("ftTrue");
        hBoxNaoVeicAtMarca.setFlexHflex("ftTrue");
        hBoxNaoVeicAtMarca.setScrollable(false);
        hBoxNaoVeicAtMarca.setBoxShadowConfigHorizontalLength(10);
        hBoxNaoVeicAtMarca.setBoxShadowConfigVerticalLength(10);
        hBoxNaoVeicAtMarca.setBoxShadowConfigBlurRadius(5);
        hBoxNaoVeicAtMarca.setBoxShadowConfigSpreadRadius(0);
        hBoxNaoVeicAtMarca.setBoxShadowConfigShadowColor("clBlack");
        hBoxNaoVeicAtMarca.setBoxShadowConfigOpacity(75);
        hBoxNaoVeicAtMarca.setVAlign("tvTop");
        FHBox17.addChildren(hBoxNaoVeicAtMarca);
        hBoxNaoVeicAtMarca.applyProperties();
    }

    public TFHBox FHBox29 = new TFHBox();

    private void init_FHBox29() {
        FHBox29.setName("FHBox29");
        FHBox29.setLeft(0);
        FHBox29.setTop(0);
        FHBox29.setWidth(15);
        FHBox29.setHeight(18);
        FHBox29.setBorderStyle("stNone");
        FHBox29.setPaddingTop(0);
        FHBox29.setPaddingLeft(0);
        FHBox29.setPaddingRight(0);
        FHBox29.setPaddingBottom(0);
        FHBox29.setMarginTop(0);
        FHBox29.setMarginLeft(0);
        FHBox29.setMarginRight(0);
        FHBox29.setMarginBottom(0);
        FHBox29.setSpacing(1);
        FHBox29.setFlexVflex("ftFalse");
        FHBox29.setFlexHflex("ftTrue");
        FHBox29.setScrollable(false);
        FHBox29.setBoxShadowConfigHorizontalLength(10);
        FHBox29.setBoxShadowConfigVerticalLength(10);
        FHBox29.setBoxShadowConfigBlurRadius(5);
        FHBox29.setBoxShadowConfigSpreadRadius(0);
        FHBox29.setBoxShadowConfigShadowColor("clBlack");
        FHBox29.setBoxShadowConfigOpacity(75);
        FHBox29.setVAlign("tvTop");
        hBoxNaoVeicAtMarca.addChildren(FHBox29);
        FHBox29.applyProperties();
    }

    public TFHBox FHBox30 = new TFHBox();

    private void init_FHBox30() {
        FHBox30.setName("FHBox30");
        FHBox30.setLeft(15);
        FHBox30.setTop(0);
        FHBox30.setWidth(50);
        FHBox30.setHeight(33);
        FHBox30.setBorderStyle("stNone");
        FHBox30.setPaddingTop(10);
        FHBox30.setPaddingLeft(0);
        FHBox30.setPaddingRight(0);
        FHBox30.setPaddingBottom(0);
        FHBox30.setMarginTop(0);
        FHBox30.setMarginLeft(0);
        FHBox30.setMarginRight(0);
        FHBox30.setMarginBottom(0);
        FHBox30.setSpacing(1);
        FHBox30.setFlexVflex("ftFalse");
        FHBox30.setFlexHflex("ftMin");
        FHBox30.setScrollable(false);
        FHBox30.setBoxShadowConfigHorizontalLength(10);
        FHBox30.setBoxShadowConfigVerticalLength(10);
        FHBox30.setBoxShadowConfigBlurRadius(5);
        FHBox30.setBoxShadowConfigSpreadRadius(0);
        FHBox30.setBoxShadowConfigShadowColor("clBlack");
        FHBox30.setBoxShadowConfigOpacity(75);
        FHBox30.setVAlign("tvTop");
        hBoxNaoVeicAtMarca.addChildren(FHBox30);
        FHBox30.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(0);
        FLabel7.setTop(0);
        FLabel7.setWidth(30);
        FLabel7.setHeight(18);
        FLabel7.setCaption("N\u00E3o");
        FLabel7.setFontColor("clBlack");
        FLabel7.setFontSize(-15);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[fsBold]");
        FLabel7.setVerticalAlignment("taVerticalCenter");
        FLabel7.setWordBreak(false);
        FHBox30.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFHBox FHBox31 = new TFHBox();

    private void init_FHBox31() {
        FHBox31.setName("FHBox31");
        FHBox31.setLeft(65);
        FHBox31.setTop(0);
        FHBox31.setWidth(15);
        FHBox31.setHeight(18);
        FHBox31.setBorderStyle("stNone");
        FHBox31.setPaddingTop(0);
        FHBox31.setPaddingLeft(0);
        FHBox31.setPaddingRight(0);
        FHBox31.setPaddingBottom(0);
        FHBox31.setMarginTop(0);
        FHBox31.setMarginLeft(0);
        FHBox31.setMarginRight(0);
        FHBox31.setMarginBottom(0);
        FHBox31.setSpacing(1);
        FHBox31.setFlexVflex("ftFalse");
        FHBox31.setFlexHflex("ftTrue");
        FHBox31.setScrollable(false);
        FHBox31.setBoxShadowConfigHorizontalLength(10);
        FHBox31.setBoxShadowConfigVerticalLength(10);
        FHBox31.setBoxShadowConfigBlurRadius(5);
        FHBox31.setBoxShadowConfigSpreadRadius(0);
        FHBox31.setBoxShadowConfigShadowColor("clBlack");
        FHBox31.setBoxShadowConfigOpacity(75);
        FHBox31.setVAlign("tvTop");
        hBoxNaoVeicAtMarca.addChildren(FHBox31);
        FHBox31.applyProperties();
    }

    public TFHBox hBoxPretendTrocarMsmModel = new TFHBox();

    private void init_hBoxPretendTrocarMsmModel() {
        hBoxPretendTrocarMsmModel.setName("hBoxPretendTrocarMsmModel");
        hBoxPretendTrocarMsmModel.setLeft(0);
        hBoxPretendTrocarMsmModel.setTop(203);
        hBoxPretendTrocarMsmModel.setWidth(470);
        hBoxPretendTrocarMsmModel.setHeight(46);
        hBoxPretendTrocarMsmModel.setBorderStyle("stNone");
        hBoxPretendTrocarMsmModel.setPaddingTop(10);
        hBoxPretendTrocarMsmModel.setPaddingLeft(0);
        hBoxPretendTrocarMsmModel.setPaddingRight(0);
        hBoxPretendTrocarMsmModel.setPaddingBottom(0);
        hBoxPretendTrocarMsmModel.setVisible(false);
        hBoxPretendTrocarMsmModel.setMarginTop(0);
        hBoxPretendTrocarMsmModel.setMarginLeft(0);
        hBoxPretendTrocarMsmModel.setMarginRight(0);
        hBoxPretendTrocarMsmModel.setMarginBottom(0);
        hBoxPretendTrocarMsmModel.setSpacing(1);
        hBoxPretendTrocarMsmModel.setFlexVflex("ftFalse");
        hBoxPretendTrocarMsmModel.setFlexHflex("ftTrue");
        hBoxPretendTrocarMsmModel.setScrollable(false);
        hBoxPretendTrocarMsmModel.setBoxShadowConfigHorizontalLength(10);
        hBoxPretendTrocarMsmModel.setBoxShadowConfigVerticalLength(10);
        hBoxPretendTrocarMsmModel.setBoxShadowConfigBlurRadius(5);
        hBoxPretendTrocarMsmModel.setBoxShadowConfigSpreadRadius(0);
        hBoxPretendTrocarMsmModel.setBoxShadowConfigShadowColor("clBlack");
        hBoxPretendTrocarMsmModel.setBoxShadowConfigOpacity(75);
        hBoxPretendTrocarMsmModel.setVAlign("tvTop");
        FVBox1.addChildren(hBoxPretendTrocarMsmModel);
        hBoxPretendTrocarMsmModel.applyProperties();
    }

    public TFHBox hBoxPretTrocMsmModel = new TFHBox();

    private void init_hBoxPretTrocMsmModel() {
        hBoxPretTrocMsmModel.setName("hBoxPretTrocMsmModel");
        hBoxPretTrocMsmModel.setLeft(0);
        hBoxPretTrocMsmModel.setTop(0);
        hBoxPretTrocMsmModel.setWidth(276);
        hBoxPretTrocMsmModel.setHeight(37);
        hBoxPretTrocMsmModel.setBorderStyle("stNone");
        hBoxPretTrocMsmModel.setPaddingTop(8);
        hBoxPretTrocMsmModel.setPaddingLeft(0);
        hBoxPretTrocMsmModel.setPaddingRight(0);
        hBoxPretTrocMsmModel.setPaddingBottom(0);
        hBoxPretTrocMsmModel.setMarginTop(0);
        hBoxPretTrocMsmModel.setMarginLeft(0);
        hBoxPretTrocMsmModel.setMarginRight(0);
        hBoxPretTrocMsmModel.setMarginBottom(0);
        hBoxPretTrocMsmModel.setSpacing(1);
        hBoxPretTrocMsmModel.setFlexVflex("ftFalse");
        hBoxPretTrocMsmModel.setFlexHflex("ftFalse");
        hBoxPretTrocMsmModel.setScrollable(false);
        hBoxPretTrocMsmModel.setBoxShadowConfigHorizontalLength(10);
        hBoxPretTrocMsmModel.setBoxShadowConfigVerticalLength(10);
        hBoxPretTrocMsmModel.setBoxShadowConfigBlurRadius(5);
        hBoxPretTrocMsmModel.setBoxShadowConfigSpreadRadius(0);
        hBoxPretTrocMsmModel.setBoxShadowConfigShadowColor("clBlack");
        hBoxPretTrocMsmModel.setBoxShadowConfigOpacity(75);
        hBoxPretTrocMsmModel.setVAlign("tvTop");
        hBoxPretendTrocarMsmModel.addChildren(hBoxPretTrocMsmModel);
        hBoxPretTrocMsmModel.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(0);
        FVBox5.setTop(0);
        FVBox5.setWidth(264);
        FVBox5.setHeight(32);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftTrue");
        FVBox5.setFlexHflex("ftTrue");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        hBoxPretTrocMsmModel.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(0);
        FLabel8.setTop(0);
        FLabel8.setWidth(156);
        FLabel8.setHeight(13);
        FLabel8.setCaption("Pretende Trocar mesmo Modelo?");
        FLabel8.setFontColor("clWindowText");
        FLabel8.setFontSize(-11);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[]");
        FLabel8.setVerticalAlignment("taVerticalCenter");
        FLabel8.setWordBreak(false);
        FVBox5.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFLabel lblValPretendeTrocarMsmModel = new TFLabel();

    private void init_lblValPretendeTrocarMsmModel() {
        lblValPretendeTrocarMsmModel.setName("lblValPretendeTrocarMsmModel");
        lblValPretendeTrocarMsmModel.setLeft(0);
        lblValPretendeTrocarMsmModel.setTop(14);
        lblValPretendeTrocarMsmModel.setWidth(229);
        lblValPretendeTrocarMsmModel.setHeight(13);
        lblValPretendeTrocarMsmModel.setCaption("Informe se Pretende Trocar pelo Mesmo Modelo");
        lblValPretendeTrocarMsmModel.setFontColor("clRed");
        lblValPretendeTrocarMsmModel.setFontSize(-11);
        lblValPretendeTrocarMsmModel.setFontName("Tahoma");
        lblValPretendeTrocarMsmModel.setFontStyle("[]");
        lblValPretendeTrocarMsmModel.setVisible(false);
        lblValPretendeTrocarMsmModel.setVerticalAlignment("taVerticalCenter");
        lblValPretendeTrocarMsmModel.setWordBreak(false);
        FVBox5.addChildren(lblValPretendeTrocarMsmModel);
        lblValPretendeTrocarMsmModel.applyProperties();
    }

    public TFHBox FHBox34 = new TFHBox();

    private void init_FHBox34() {
        FHBox34.setName("FHBox34");
        FHBox34.setLeft(276);
        FHBox34.setTop(0);
        FHBox34.setWidth(180);
        FHBox34.setHeight(37);
        FHBox34.setBorderStyle("stNone");
        FHBox34.setPaddingTop(0);
        FHBox34.setPaddingLeft(0);
        FHBox34.setPaddingRight(0);
        FHBox34.setPaddingBottom(0);
        FHBox34.setMarginTop(0);
        FHBox34.setMarginLeft(0);
        FHBox34.setMarginRight(0);
        FHBox34.setMarginBottom(0);
        FHBox34.setSpacing(0);
        FHBox34.setFlexVflex("ftFalse");
        FHBox34.setFlexHflex("ftFalse");
        FHBox34.setScrollable(false);
        FHBox34.setBoxShadowConfigHorizontalLength(10);
        FHBox34.setBoxShadowConfigVerticalLength(10);
        FHBox34.setBoxShadowConfigBlurRadius(5);
        FHBox34.setBoxShadowConfigSpreadRadius(0);
        FHBox34.setBoxShadowConfigShadowColor("clBlack");
        FHBox34.setBoxShadowConfigOpacity(75);
        FHBox34.setVAlign("tvTop");
        hBoxPretendTrocarMsmModel.addChildren(FHBox34);
        FHBox34.applyProperties();
    }

    public TFHBox hBoxSimTrocarMod = new TFHBox();

    private void init_hBoxSimTrocarMod() {
        hBoxSimTrocarMod.setName("hBoxSimTrocarMod");
        hBoxSimTrocarMod.setLeft(0);
        hBoxSimTrocarMod.setTop(0);
        hBoxSimTrocarMod.setWidth(90);
        hBoxSimTrocarMod.setHeight(35);
        hBoxSimTrocarMod.setAlign("alClient");
        hBoxSimTrocarMod.setBorderStyle("stNone");
        hBoxSimTrocarMod.setColor("clSilver");
        hBoxSimTrocarMod.setPaddingTop(0);
        hBoxSimTrocarMod.setPaddingLeft(0);
        hBoxSimTrocarMod.setPaddingRight(0);
        hBoxSimTrocarMod.setPaddingBottom(0);
        hBoxSimTrocarMod.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSimTrocarModClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxSimTrocarMod", "OnClick");
        });
        hBoxSimTrocarMod.setMarginTop(0);
        hBoxSimTrocarMod.setMarginLeft(0);
        hBoxSimTrocarMod.setMarginRight(0);
        hBoxSimTrocarMod.setMarginBottom(0);
        hBoxSimTrocarMod.setSpacing(1);
        hBoxSimTrocarMod.setFlexVflex("ftTrue");
        hBoxSimTrocarMod.setFlexHflex("ftTrue");
        hBoxSimTrocarMod.setScrollable(false);
        hBoxSimTrocarMod.setBoxShadowConfigHorizontalLength(10);
        hBoxSimTrocarMod.setBoxShadowConfigVerticalLength(10);
        hBoxSimTrocarMod.setBoxShadowConfigBlurRadius(5);
        hBoxSimTrocarMod.setBoxShadowConfigSpreadRadius(0);
        hBoxSimTrocarMod.setBoxShadowConfigShadowColor("clBlack");
        hBoxSimTrocarMod.setBoxShadowConfigOpacity(75);
        hBoxSimTrocarMod.setVAlign("tvTop");
        FHBox34.addChildren(hBoxSimTrocarMod);
        hBoxSimTrocarMod.applyProperties();
    }

    public TFHBox FHBox36 = new TFHBox();

    private void init_FHBox36() {
        FHBox36.setName("FHBox36");
        FHBox36.setLeft(0);
        FHBox36.setTop(0);
        FHBox36.setWidth(21);
        FHBox36.setHeight(18);
        FHBox36.setBorderStyle("stNone");
        FHBox36.setPaddingTop(0);
        FHBox36.setPaddingLeft(0);
        FHBox36.setPaddingRight(0);
        FHBox36.setPaddingBottom(0);
        FHBox36.setMarginTop(0);
        FHBox36.setMarginLeft(0);
        FHBox36.setMarginRight(0);
        FHBox36.setMarginBottom(0);
        FHBox36.setSpacing(1);
        FHBox36.setFlexVflex("ftFalse");
        FHBox36.setFlexHflex("ftTrue");
        FHBox36.setScrollable(false);
        FHBox36.setBoxShadowConfigHorizontalLength(10);
        FHBox36.setBoxShadowConfigVerticalLength(10);
        FHBox36.setBoxShadowConfigBlurRadius(5);
        FHBox36.setBoxShadowConfigSpreadRadius(0);
        FHBox36.setBoxShadowConfigShadowColor("clBlack");
        FHBox36.setBoxShadowConfigOpacity(75);
        FHBox36.setVAlign("tvTop");
        hBoxSimTrocarMod.addChildren(FHBox36);
        FHBox36.applyProperties();
    }

    public TFHBox FHBox37 = new TFHBox();

    private void init_FHBox37() {
        FHBox37.setName("FHBox37");
        FHBox37.setLeft(21);
        FHBox37.setTop(0);
        FHBox37.setWidth(44);
        FHBox37.setHeight(31);
        FHBox37.setBorderStyle("stNone");
        FHBox37.setPaddingTop(10);
        FHBox37.setPaddingLeft(0);
        FHBox37.setPaddingRight(0);
        FHBox37.setPaddingBottom(0);
        FHBox37.setMarginTop(0);
        FHBox37.setMarginLeft(0);
        FHBox37.setMarginRight(0);
        FHBox37.setMarginBottom(0);
        FHBox37.setSpacing(1);
        FHBox37.setFlexVflex("ftFalse");
        FHBox37.setFlexHflex("ftMin");
        FHBox37.setScrollable(false);
        FHBox37.setBoxShadowConfigHorizontalLength(10);
        FHBox37.setBoxShadowConfigVerticalLength(10);
        FHBox37.setBoxShadowConfigBlurRadius(5);
        FHBox37.setBoxShadowConfigSpreadRadius(0);
        FHBox37.setBoxShadowConfigShadowColor("clBlack");
        FHBox37.setBoxShadowConfigOpacity(75);
        FHBox37.setVAlign("tvTop");
        hBoxSimTrocarMod.addChildren(FHBox37);
        FHBox37.applyProperties();
    }

    public TFLabel FLabel9 = new TFLabel();

    private void init_FLabel9() {
        FLabel9.setName("FLabel9");
        FLabel9.setLeft(0);
        FLabel9.setTop(0);
        FLabel9.setWidth(28);
        FLabel9.setHeight(18);
        FLabel9.setCaption("Sim");
        FLabel9.setFontColor("clBlack");
        FLabel9.setFontSize(-15);
        FLabel9.setFontName("Tahoma");
        FLabel9.setFontStyle("[fsBold]");
        FLabel9.setVerticalAlignment("taVerticalCenter");
        FLabel9.setWordBreak(false);
        FHBox37.addChildren(FLabel9);
        FLabel9.applyProperties();
    }

    public TFHBox FHBox38 = new TFHBox();

    private void init_FHBox38() {
        FHBox38.setName("FHBox38");
        FHBox38.setLeft(65);
        FHBox38.setTop(0);
        FHBox38.setWidth(19);
        FHBox38.setHeight(18);
        FHBox38.setBorderStyle("stNone");
        FHBox38.setPaddingTop(0);
        FHBox38.setPaddingLeft(0);
        FHBox38.setPaddingRight(0);
        FHBox38.setPaddingBottom(0);
        FHBox38.setMarginTop(0);
        FHBox38.setMarginLeft(0);
        FHBox38.setMarginRight(0);
        FHBox38.setMarginBottom(0);
        FHBox38.setSpacing(1);
        FHBox38.setFlexVflex("ftFalse");
        FHBox38.setFlexHflex("ftTrue");
        FHBox38.setScrollable(false);
        FHBox38.setBoxShadowConfigHorizontalLength(10);
        FHBox38.setBoxShadowConfigVerticalLength(10);
        FHBox38.setBoxShadowConfigBlurRadius(5);
        FHBox38.setBoxShadowConfigSpreadRadius(0);
        FHBox38.setBoxShadowConfigShadowColor("clBlack");
        FHBox38.setBoxShadowConfigOpacity(75);
        FHBox38.setVAlign("tvTop");
        hBoxSimTrocarMod.addChildren(FHBox38);
        FHBox38.applyProperties();
    }

    public TFHBox hBoxNaoTrocarMod = new TFHBox();

    private void init_hBoxNaoTrocarMod() {
        hBoxNaoTrocarMod.setName("hBoxNaoTrocarMod");
        hBoxNaoTrocarMod.setLeft(90);
        hBoxNaoTrocarMod.setTop(0);
        hBoxNaoTrocarMod.setWidth(94);
        hBoxNaoTrocarMod.setHeight(35);
        hBoxNaoTrocarMod.setAlign("alClient");
        hBoxNaoTrocarMod.setBorderStyle("stNone");
        hBoxNaoTrocarMod.setColor("clSilver");
        hBoxNaoTrocarMod.setPaddingTop(0);
        hBoxNaoTrocarMod.setPaddingLeft(0);
        hBoxNaoTrocarMod.setPaddingRight(0);
        hBoxNaoTrocarMod.setPaddingBottom(0);
        hBoxNaoTrocarMod.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxNaoTrocarModClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxNaoTrocarMod", "OnClick");
        });
        hBoxNaoTrocarMod.setMarginTop(0);
        hBoxNaoTrocarMod.setMarginLeft(0);
        hBoxNaoTrocarMod.setMarginRight(0);
        hBoxNaoTrocarMod.setMarginBottom(0);
        hBoxNaoTrocarMod.setSpacing(1);
        hBoxNaoTrocarMod.setFlexVflex("ftTrue");
        hBoxNaoTrocarMod.setFlexHflex("ftTrue");
        hBoxNaoTrocarMod.setScrollable(false);
        hBoxNaoTrocarMod.setBoxShadowConfigHorizontalLength(10);
        hBoxNaoTrocarMod.setBoxShadowConfigVerticalLength(10);
        hBoxNaoTrocarMod.setBoxShadowConfigBlurRadius(5);
        hBoxNaoTrocarMod.setBoxShadowConfigSpreadRadius(0);
        hBoxNaoTrocarMod.setBoxShadowConfigShadowColor("clBlack");
        hBoxNaoTrocarMod.setBoxShadowConfigOpacity(75);
        hBoxNaoTrocarMod.setVAlign("tvTop");
        FHBox34.addChildren(hBoxNaoTrocarMod);
        hBoxNaoTrocarMod.applyProperties();
    }

    public TFHBox FHBox40 = new TFHBox();

    private void init_FHBox40() {
        FHBox40.setName("FHBox40");
        FHBox40.setLeft(0);
        FHBox40.setTop(0);
        FHBox40.setWidth(15);
        FHBox40.setHeight(18);
        FHBox40.setBorderStyle("stNone");
        FHBox40.setPaddingTop(0);
        FHBox40.setPaddingLeft(0);
        FHBox40.setPaddingRight(0);
        FHBox40.setPaddingBottom(0);
        FHBox40.setMarginTop(0);
        FHBox40.setMarginLeft(0);
        FHBox40.setMarginRight(0);
        FHBox40.setMarginBottom(0);
        FHBox40.setSpacing(1);
        FHBox40.setFlexVflex("ftFalse");
        FHBox40.setFlexHflex("ftTrue");
        FHBox40.setScrollable(false);
        FHBox40.setBoxShadowConfigHorizontalLength(10);
        FHBox40.setBoxShadowConfigVerticalLength(10);
        FHBox40.setBoxShadowConfigBlurRadius(5);
        FHBox40.setBoxShadowConfigSpreadRadius(0);
        FHBox40.setBoxShadowConfigShadowColor("clBlack");
        FHBox40.setBoxShadowConfigOpacity(75);
        FHBox40.setVAlign("tvTop");
        hBoxNaoTrocarMod.addChildren(FHBox40);
        FHBox40.applyProperties();
    }

    public TFHBox FHBox41 = new TFHBox();

    private void init_FHBox41() {
        FHBox41.setName("FHBox41");
        FHBox41.setLeft(15);
        FHBox41.setTop(0);
        FHBox41.setWidth(50);
        FHBox41.setHeight(33);
        FHBox41.setBorderStyle("stNone");
        FHBox41.setPaddingTop(10);
        FHBox41.setPaddingLeft(0);
        FHBox41.setPaddingRight(0);
        FHBox41.setPaddingBottom(0);
        FHBox41.setMarginTop(0);
        FHBox41.setMarginLeft(0);
        FHBox41.setMarginRight(0);
        FHBox41.setMarginBottom(0);
        FHBox41.setSpacing(1);
        FHBox41.setFlexVflex("ftFalse");
        FHBox41.setFlexHflex("ftMin");
        FHBox41.setScrollable(false);
        FHBox41.setBoxShadowConfigHorizontalLength(10);
        FHBox41.setBoxShadowConfigVerticalLength(10);
        FHBox41.setBoxShadowConfigBlurRadius(5);
        FHBox41.setBoxShadowConfigSpreadRadius(0);
        FHBox41.setBoxShadowConfigShadowColor("clBlack");
        FHBox41.setBoxShadowConfigOpacity(75);
        FHBox41.setVAlign("tvTop");
        hBoxNaoTrocarMod.addChildren(FHBox41);
        FHBox41.applyProperties();
    }

    public TFLabel FLabel10 = new TFLabel();

    private void init_FLabel10() {
        FLabel10.setName("FLabel10");
        FLabel10.setLeft(0);
        FLabel10.setTop(0);
        FLabel10.setWidth(30);
        FLabel10.setHeight(18);
        FLabel10.setCaption("N\u00E3o");
        FLabel10.setFontColor("clBlack");
        FLabel10.setFontSize(-15);
        FLabel10.setFontName("Tahoma");
        FLabel10.setFontStyle("[fsBold]");
        FLabel10.setVerticalAlignment("taVerticalCenter");
        FLabel10.setWordBreak(false);
        FHBox41.addChildren(FLabel10);
        FLabel10.applyProperties();
    }

    public TFHBox FHBox42 = new TFHBox();

    private void init_FHBox42() {
        FHBox42.setName("FHBox42");
        FHBox42.setLeft(65);
        FHBox42.setTop(0);
        FHBox42.setWidth(15);
        FHBox42.setHeight(18);
        FHBox42.setBorderStyle("stNone");
        FHBox42.setPaddingTop(0);
        FHBox42.setPaddingLeft(0);
        FHBox42.setPaddingRight(0);
        FHBox42.setPaddingBottom(0);
        FHBox42.setMarginTop(0);
        FHBox42.setMarginLeft(0);
        FHBox42.setMarginRight(0);
        FHBox42.setMarginBottom(0);
        FHBox42.setSpacing(1);
        FHBox42.setFlexVflex("ftFalse");
        FHBox42.setFlexHflex("ftTrue");
        FHBox42.setScrollable(false);
        FHBox42.setBoxShadowConfigHorizontalLength(10);
        FHBox42.setBoxShadowConfigVerticalLength(10);
        FHBox42.setBoxShadowConfigBlurRadius(5);
        FHBox42.setBoxShadowConfigSpreadRadius(0);
        FHBox42.setBoxShadowConfigShadowColor("clBlack");
        FHBox42.setBoxShadowConfigOpacity(75);
        FHBox42.setVAlign("tvTop");
        hBoxNaoTrocarMod.addChildren(FHBox42);
        FHBox42.applyProperties();
    }

    public TFHBox hBoxFazerAvaliacao = new TFHBox();

    private void init_hBoxFazerAvaliacao() {
        hBoxFazerAvaliacao.setName("hBoxFazerAvaliacao");
        hBoxFazerAvaliacao.setLeft(0);
        hBoxFazerAvaliacao.setTop(250);
        hBoxFazerAvaliacao.setWidth(470);
        hBoxFazerAvaliacao.setHeight(46);
        hBoxFazerAvaliacao.setBorderStyle("stNone");
        hBoxFazerAvaliacao.setPaddingTop(10);
        hBoxFazerAvaliacao.setPaddingLeft(0);
        hBoxFazerAvaliacao.setPaddingRight(0);
        hBoxFazerAvaliacao.setPaddingBottom(0);
        hBoxFazerAvaliacao.setVisible(false);
        hBoxFazerAvaliacao.setMarginTop(0);
        hBoxFazerAvaliacao.setMarginLeft(0);
        hBoxFazerAvaliacao.setMarginRight(0);
        hBoxFazerAvaliacao.setMarginBottom(0);
        hBoxFazerAvaliacao.setSpacing(1);
        hBoxFazerAvaliacao.setFlexVflex("ftFalse");
        hBoxFazerAvaliacao.setFlexHflex("ftTrue");
        hBoxFazerAvaliacao.setScrollable(false);
        hBoxFazerAvaliacao.setBoxShadowConfigHorizontalLength(10);
        hBoxFazerAvaliacao.setBoxShadowConfigVerticalLength(10);
        hBoxFazerAvaliacao.setBoxShadowConfigBlurRadius(5);
        hBoxFazerAvaliacao.setBoxShadowConfigSpreadRadius(0);
        hBoxFazerAvaliacao.setBoxShadowConfigShadowColor("clBlack");
        hBoxFazerAvaliacao.setBoxShadowConfigOpacity(75);
        hBoxFazerAvaliacao.setVAlign("tvTop");
        FVBox1.addChildren(hBoxFazerAvaliacao);
        hBoxFazerAvaliacao.applyProperties();
    }

    public TFHBox hBoxLabelFazerAval = new TFHBox();

    private void init_hBoxLabelFazerAval() {
        hBoxLabelFazerAval.setName("hBoxLabelFazerAval");
        hBoxLabelFazerAval.setLeft(0);
        hBoxLabelFazerAval.setTop(0);
        hBoxLabelFazerAval.setWidth(276);
        hBoxLabelFazerAval.setHeight(37);
        hBoxLabelFazerAval.setBorderStyle("stNone");
        hBoxLabelFazerAval.setPaddingTop(8);
        hBoxLabelFazerAval.setPaddingLeft(0);
        hBoxLabelFazerAval.setPaddingRight(0);
        hBoxLabelFazerAval.setPaddingBottom(0);
        hBoxLabelFazerAval.setMarginTop(0);
        hBoxLabelFazerAval.setMarginLeft(0);
        hBoxLabelFazerAval.setMarginRight(0);
        hBoxLabelFazerAval.setMarginBottom(0);
        hBoxLabelFazerAval.setSpacing(1);
        hBoxLabelFazerAval.setFlexVflex("ftFalse");
        hBoxLabelFazerAval.setFlexHflex("ftFalse");
        hBoxLabelFazerAval.setScrollable(false);
        hBoxLabelFazerAval.setBoxShadowConfigHorizontalLength(10);
        hBoxLabelFazerAval.setBoxShadowConfigVerticalLength(10);
        hBoxLabelFazerAval.setBoxShadowConfigBlurRadius(5);
        hBoxLabelFazerAval.setBoxShadowConfigSpreadRadius(0);
        hBoxLabelFazerAval.setBoxShadowConfigShadowColor("clBlack");
        hBoxLabelFazerAval.setBoxShadowConfigOpacity(75);
        hBoxLabelFazerAval.setVAlign("tvTop");
        hBoxFazerAvaliacao.addChildren(hBoxLabelFazerAval);
        hBoxLabelFazerAval.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(0);
        FVBox6.setWidth(264);
        FVBox6.setHeight(32);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftTrue");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        hBoxLabelFazerAval.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFLabel FLabel11 = new TFLabel();

    private void init_FLabel11() {
        FLabel11.setName("FLabel11");
        FLabel11.setLeft(0);
        FLabel11.setTop(0);
        FLabel11.setWidth(140);
        FLabel11.setHeight(13);
        FLabel11.setCaption("Deseja Fazer uma Avalia\u00E7\u00E3o?");
        FLabel11.setFontColor("clWindowText");
        FLabel11.setFontSize(-11);
        FLabel11.setFontName("Tahoma");
        FLabel11.setFontStyle("[]");
        FLabel11.setVerticalAlignment("taVerticalCenter");
        FLabel11.setWordBreak(false);
        FVBox6.addChildren(FLabel11);
        FLabel11.applyProperties();
    }

    public TFLabel lblValDesejaFazerAval = new TFLabel();

    private void init_lblValDesejaFazerAval() {
        lblValDesejaFazerAval.setName("lblValDesejaFazerAval");
        lblValDesejaFazerAval.setLeft(0);
        lblValDesejaFazerAval.setTop(14);
        lblValDesejaFazerAval.setWidth(190);
        lblValDesejaFazerAval.setHeight(13);
        lblValDesejaFazerAval.setCaption("Informe se Deseja Fazer uma Avalia\u00E7\u00E3o");
        lblValDesejaFazerAval.setFontColor("clRed");
        lblValDesejaFazerAval.setFontSize(-11);
        lblValDesejaFazerAval.setFontName("Tahoma");
        lblValDesejaFazerAval.setFontStyle("[]");
        lblValDesejaFazerAval.setVisible(false);
        lblValDesejaFazerAval.setVerticalAlignment("taVerticalCenter");
        lblValDesejaFazerAval.setWordBreak(false);
        FVBox6.addChildren(lblValDesejaFazerAval);
        lblValDesejaFazerAval.applyProperties();
    }

    public TFHBox FHBox45 = new TFHBox();

    private void init_FHBox45() {
        FHBox45.setName("FHBox45");
        FHBox45.setLeft(276);
        FHBox45.setTop(0);
        FHBox45.setWidth(180);
        FHBox45.setHeight(37);
        FHBox45.setBorderStyle("stNone");
        FHBox45.setPaddingTop(0);
        FHBox45.setPaddingLeft(0);
        FHBox45.setPaddingRight(0);
        FHBox45.setPaddingBottom(0);
        FHBox45.setMarginTop(0);
        FHBox45.setMarginLeft(0);
        FHBox45.setMarginRight(0);
        FHBox45.setMarginBottom(0);
        FHBox45.setSpacing(0);
        FHBox45.setFlexVflex("ftFalse");
        FHBox45.setFlexHflex("ftFalse");
        FHBox45.setScrollable(false);
        FHBox45.setBoxShadowConfigHorizontalLength(10);
        FHBox45.setBoxShadowConfigVerticalLength(10);
        FHBox45.setBoxShadowConfigBlurRadius(5);
        FHBox45.setBoxShadowConfigSpreadRadius(0);
        FHBox45.setBoxShadowConfigShadowColor("clBlack");
        FHBox45.setBoxShadowConfigOpacity(75);
        FHBox45.setVAlign("tvTop");
        hBoxFazerAvaliacao.addChildren(FHBox45);
        FHBox45.applyProperties();
    }

    public TFHBox hBoxSimAvaliacao = new TFHBox();

    private void init_hBoxSimAvaliacao() {
        hBoxSimAvaliacao.setName("hBoxSimAvaliacao");
        hBoxSimAvaliacao.setLeft(0);
        hBoxSimAvaliacao.setTop(0);
        hBoxSimAvaliacao.setWidth(90);
        hBoxSimAvaliacao.setHeight(35);
        hBoxSimAvaliacao.setAlign("alClient");
        hBoxSimAvaliacao.setBorderStyle("stNone");
        hBoxSimAvaliacao.setColor("clSilver");
        hBoxSimAvaliacao.setPaddingTop(0);
        hBoxSimAvaliacao.setPaddingLeft(0);
        hBoxSimAvaliacao.setPaddingRight(0);
        hBoxSimAvaliacao.setPaddingBottom(0);
        hBoxSimAvaliacao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSimAvaliacaoClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxSimAvaliacao", "OnClick");
        });
        hBoxSimAvaliacao.setMarginTop(0);
        hBoxSimAvaliacao.setMarginLeft(0);
        hBoxSimAvaliacao.setMarginRight(0);
        hBoxSimAvaliacao.setMarginBottom(0);
        hBoxSimAvaliacao.setSpacing(1);
        hBoxSimAvaliacao.setFlexVflex("ftTrue");
        hBoxSimAvaliacao.setFlexHflex("ftTrue");
        hBoxSimAvaliacao.setScrollable(false);
        hBoxSimAvaliacao.setBoxShadowConfigHorizontalLength(10);
        hBoxSimAvaliacao.setBoxShadowConfigVerticalLength(10);
        hBoxSimAvaliacao.setBoxShadowConfigBlurRadius(5);
        hBoxSimAvaliacao.setBoxShadowConfigSpreadRadius(0);
        hBoxSimAvaliacao.setBoxShadowConfigShadowColor("clBlack");
        hBoxSimAvaliacao.setBoxShadowConfigOpacity(75);
        hBoxSimAvaliacao.setVAlign("tvTop");
        FHBox45.addChildren(hBoxSimAvaliacao);
        hBoxSimAvaliacao.applyProperties();
    }

    public TFHBox FHBox47 = new TFHBox();

    private void init_FHBox47() {
        FHBox47.setName("FHBox47");
        FHBox47.setLeft(0);
        FHBox47.setTop(0);
        FHBox47.setWidth(21);
        FHBox47.setHeight(18);
        FHBox47.setBorderStyle("stNone");
        FHBox47.setPaddingTop(0);
        FHBox47.setPaddingLeft(0);
        FHBox47.setPaddingRight(0);
        FHBox47.setPaddingBottom(0);
        FHBox47.setMarginTop(0);
        FHBox47.setMarginLeft(0);
        FHBox47.setMarginRight(0);
        FHBox47.setMarginBottom(0);
        FHBox47.setSpacing(1);
        FHBox47.setFlexVflex("ftFalse");
        FHBox47.setFlexHflex("ftTrue");
        FHBox47.setScrollable(false);
        FHBox47.setBoxShadowConfigHorizontalLength(10);
        FHBox47.setBoxShadowConfigVerticalLength(10);
        FHBox47.setBoxShadowConfigBlurRadius(5);
        FHBox47.setBoxShadowConfigSpreadRadius(0);
        FHBox47.setBoxShadowConfigShadowColor("clBlack");
        FHBox47.setBoxShadowConfigOpacity(75);
        FHBox47.setVAlign("tvTop");
        hBoxSimAvaliacao.addChildren(FHBox47);
        FHBox47.applyProperties();
    }

    public TFHBox FHBox48 = new TFHBox();

    private void init_FHBox48() {
        FHBox48.setName("FHBox48");
        FHBox48.setLeft(21);
        FHBox48.setTop(0);
        FHBox48.setWidth(44);
        FHBox48.setHeight(31);
        FHBox48.setBorderStyle("stNone");
        FHBox48.setPaddingTop(10);
        FHBox48.setPaddingLeft(0);
        FHBox48.setPaddingRight(0);
        FHBox48.setPaddingBottom(0);
        FHBox48.setMarginTop(0);
        FHBox48.setMarginLeft(0);
        FHBox48.setMarginRight(0);
        FHBox48.setMarginBottom(0);
        FHBox48.setSpacing(1);
        FHBox48.setFlexVflex("ftFalse");
        FHBox48.setFlexHflex("ftMin");
        FHBox48.setScrollable(false);
        FHBox48.setBoxShadowConfigHorizontalLength(10);
        FHBox48.setBoxShadowConfigVerticalLength(10);
        FHBox48.setBoxShadowConfigBlurRadius(5);
        FHBox48.setBoxShadowConfigSpreadRadius(0);
        FHBox48.setBoxShadowConfigShadowColor("clBlack");
        FHBox48.setBoxShadowConfigOpacity(75);
        FHBox48.setVAlign("tvTop");
        hBoxSimAvaliacao.addChildren(FHBox48);
        FHBox48.applyProperties();
    }

    public TFLabel FLabel12 = new TFLabel();

    private void init_FLabel12() {
        FLabel12.setName("FLabel12");
        FLabel12.setLeft(0);
        FLabel12.setTop(0);
        FLabel12.setWidth(28);
        FLabel12.setHeight(18);
        FLabel12.setCaption("Sim");
        FLabel12.setFontColor("clBlack");
        FLabel12.setFontSize(-15);
        FLabel12.setFontName("Tahoma");
        FLabel12.setFontStyle("[fsBold]");
        FLabel12.setVerticalAlignment("taVerticalCenter");
        FLabel12.setWordBreak(false);
        FHBox48.addChildren(FLabel12);
        FLabel12.applyProperties();
    }

    public TFHBox FHBox49 = new TFHBox();

    private void init_FHBox49() {
        FHBox49.setName("FHBox49");
        FHBox49.setLeft(65);
        FHBox49.setTop(0);
        FHBox49.setWidth(19);
        FHBox49.setHeight(18);
        FHBox49.setBorderStyle("stNone");
        FHBox49.setPaddingTop(0);
        FHBox49.setPaddingLeft(0);
        FHBox49.setPaddingRight(0);
        FHBox49.setPaddingBottom(0);
        FHBox49.setMarginTop(0);
        FHBox49.setMarginLeft(0);
        FHBox49.setMarginRight(0);
        FHBox49.setMarginBottom(0);
        FHBox49.setSpacing(1);
        FHBox49.setFlexVflex("ftFalse");
        FHBox49.setFlexHflex("ftTrue");
        FHBox49.setScrollable(false);
        FHBox49.setBoxShadowConfigHorizontalLength(10);
        FHBox49.setBoxShadowConfigVerticalLength(10);
        FHBox49.setBoxShadowConfigBlurRadius(5);
        FHBox49.setBoxShadowConfigSpreadRadius(0);
        FHBox49.setBoxShadowConfigShadowColor("clBlack");
        FHBox49.setBoxShadowConfigOpacity(75);
        FHBox49.setVAlign("tvTop");
        hBoxSimAvaliacao.addChildren(FHBox49);
        FHBox49.applyProperties();
    }

    public TFHBox hBoxNaoAvaliacao = new TFHBox();

    private void init_hBoxNaoAvaliacao() {
        hBoxNaoAvaliacao.setName("hBoxNaoAvaliacao");
        hBoxNaoAvaliacao.setLeft(90);
        hBoxNaoAvaliacao.setTop(0);
        hBoxNaoAvaliacao.setWidth(94);
        hBoxNaoAvaliacao.setHeight(35);
        hBoxNaoAvaliacao.setAlign("alClient");
        hBoxNaoAvaliacao.setBorderStyle("stNone");
        hBoxNaoAvaliacao.setColor("clSilver");
        hBoxNaoAvaliacao.setPaddingTop(0);
        hBoxNaoAvaliacao.setPaddingLeft(0);
        hBoxNaoAvaliacao.setPaddingRight(0);
        hBoxNaoAvaliacao.setPaddingBottom(0);
        hBoxNaoAvaliacao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxNaoAvaliacaoClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxNaoAvaliacao", "OnClick");
        });
        hBoxNaoAvaliacao.setMarginTop(0);
        hBoxNaoAvaliacao.setMarginLeft(0);
        hBoxNaoAvaliacao.setMarginRight(0);
        hBoxNaoAvaliacao.setMarginBottom(0);
        hBoxNaoAvaliacao.setSpacing(1);
        hBoxNaoAvaliacao.setFlexVflex("ftTrue");
        hBoxNaoAvaliacao.setFlexHflex("ftTrue");
        hBoxNaoAvaliacao.setScrollable(false);
        hBoxNaoAvaliacao.setBoxShadowConfigHorizontalLength(10);
        hBoxNaoAvaliacao.setBoxShadowConfigVerticalLength(10);
        hBoxNaoAvaliacao.setBoxShadowConfigBlurRadius(5);
        hBoxNaoAvaliacao.setBoxShadowConfigSpreadRadius(0);
        hBoxNaoAvaliacao.setBoxShadowConfigShadowColor("clBlack");
        hBoxNaoAvaliacao.setBoxShadowConfigOpacity(75);
        hBoxNaoAvaliacao.setVAlign("tvTop");
        FHBox45.addChildren(hBoxNaoAvaliacao);
        hBoxNaoAvaliacao.applyProperties();
    }

    public TFHBox FHBox51 = new TFHBox();

    private void init_FHBox51() {
        FHBox51.setName("FHBox51");
        FHBox51.setLeft(0);
        FHBox51.setTop(0);
        FHBox51.setWidth(15);
        FHBox51.setHeight(18);
        FHBox51.setBorderStyle("stNone");
        FHBox51.setPaddingTop(0);
        FHBox51.setPaddingLeft(0);
        FHBox51.setPaddingRight(0);
        FHBox51.setPaddingBottom(0);
        FHBox51.setMarginTop(0);
        FHBox51.setMarginLeft(0);
        FHBox51.setMarginRight(0);
        FHBox51.setMarginBottom(0);
        FHBox51.setSpacing(1);
        FHBox51.setFlexVflex("ftFalse");
        FHBox51.setFlexHflex("ftTrue");
        FHBox51.setScrollable(false);
        FHBox51.setBoxShadowConfigHorizontalLength(10);
        FHBox51.setBoxShadowConfigVerticalLength(10);
        FHBox51.setBoxShadowConfigBlurRadius(5);
        FHBox51.setBoxShadowConfigSpreadRadius(0);
        FHBox51.setBoxShadowConfigShadowColor("clBlack");
        FHBox51.setBoxShadowConfigOpacity(75);
        FHBox51.setVAlign("tvTop");
        hBoxNaoAvaliacao.addChildren(FHBox51);
        FHBox51.applyProperties();
    }

    public TFHBox FHBox52 = new TFHBox();

    private void init_FHBox52() {
        FHBox52.setName("FHBox52");
        FHBox52.setLeft(15);
        FHBox52.setTop(0);
        FHBox52.setWidth(50);
        FHBox52.setHeight(33);
        FHBox52.setBorderStyle("stNone");
        FHBox52.setPaddingTop(10);
        FHBox52.setPaddingLeft(0);
        FHBox52.setPaddingRight(0);
        FHBox52.setPaddingBottom(0);
        FHBox52.setMarginTop(0);
        FHBox52.setMarginLeft(0);
        FHBox52.setMarginRight(0);
        FHBox52.setMarginBottom(0);
        FHBox52.setSpacing(1);
        FHBox52.setFlexVflex("ftFalse");
        FHBox52.setFlexHflex("ftMin");
        FHBox52.setScrollable(false);
        FHBox52.setBoxShadowConfigHorizontalLength(10);
        FHBox52.setBoxShadowConfigVerticalLength(10);
        FHBox52.setBoxShadowConfigBlurRadius(5);
        FHBox52.setBoxShadowConfigSpreadRadius(0);
        FHBox52.setBoxShadowConfigShadowColor("clBlack");
        FHBox52.setBoxShadowConfigOpacity(75);
        FHBox52.setVAlign("tvTop");
        hBoxNaoAvaliacao.addChildren(FHBox52);
        FHBox52.applyProperties();
    }

    public TFLabel FLabel13 = new TFLabel();

    private void init_FLabel13() {
        FLabel13.setName("FLabel13");
        FLabel13.setLeft(0);
        FLabel13.setTop(0);
        FLabel13.setWidth(30);
        FLabel13.setHeight(18);
        FLabel13.setCaption("N\u00E3o");
        FLabel13.setFontColor("clBlack");
        FLabel13.setFontSize(-15);
        FLabel13.setFontName("Tahoma");
        FLabel13.setFontStyle("[fsBold]");
        FLabel13.setVerticalAlignment("taVerticalCenter");
        FLabel13.setWordBreak(false);
        FHBox52.addChildren(FLabel13);
        FLabel13.applyProperties();
    }

    public TFHBox FHBox53 = new TFHBox();

    private void init_FHBox53() {
        FHBox53.setName("FHBox53");
        FHBox53.setLeft(65);
        FHBox53.setTop(0);
        FHBox53.setWidth(15);
        FHBox53.setHeight(18);
        FHBox53.setBorderStyle("stNone");
        FHBox53.setPaddingTop(0);
        FHBox53.setPaddingLeft(0);
        FHBox53.setPaddingRight(0);
        FHBox53.setPaddingBottom(0);
        FHBox53.setMarginTop(0);
        FHBox53.setMarginLeft(0);
        FHBox53.setMarginRight(0);
        FHBox53.setMarginBottom(0);
        FHBox53.setSpacing(1);
        FHBox53.setFlexVflex("ftFalse");
        FHBox53.setFlexHflex("ftTrue");
        FHBox53.setScrollable(false);
        FHBox53.setBoxShadowConfigHorizontalLength(10);
        FHBox53.setBoxShadowConfigVerticalLength(10);
        FHBox53.setBoxShadowConfigBlurRadius(5);
        FHBox53.setBoxShadowConfigSpreadRadius(0);
        FHBox53.setBoxShadowConfigShadowColor("clBlack");
        FHBox53.setBoxShadowConfigOpacity(75);
        FHBox53.setVAlign("tvTop");
        hBoxNaoAvaliacao.addChildren(FHBox53);
        FHBox53.applyProperties();
    }

    public TFHBox hBoxTipoVeicDeseja = new TFHBox();

    private void init_hBoxTipoVeicDeseja() {
        hBoxTipoVeicDeseja.setName("hBoxTipoVeicDeseja");
        hBoxTipoVeicDeseja.setLeft(0);
        hBoxTipoVeicDeseja.setTop(297);
        hBoxTipoVeicDeseja.setWidth(470);
        hBoxTipoVeicDeseja.setHeight(46);
        hBoxTipoVeicDeseja.setBorderStyle("stNone");
        hBoxTipoVeicDeseja.setPaddingTop(10);
        hBoxTipoVeicDeseja.setPaddingLeft(0);
        hBoxTipoVeicDeseja.setPaddingRight(0);
        hBoxTipoVeicDeseja.setPaddingBottom(0);
        hBoxTipoVeicDeseja.setVisible(false);
        hBoxTipoVeicDeseja.setMarginTop(0);
        hBoxTipoVeicDeseja.setMarginLeft(0);
        hBoxTipoVeicDeseja.setMarginRight(0);
        hBoxTipoVeicDeseja.setMarginBottom(0);
        hBoxTipoVeicDeseja.setSpacing(1);
        hBoxTipoVeicDeseja.setFlexVflex("ftFalse");
        hBoxTipoVeicDeseja.setFlexHflex("ftTrue");
        hBoxTipoVeicDeseja.setScrollable(false);
        hBoxTipoVeicDeseja.setBoxShadowConfigHorizontalLength(10);
        hBoxTipoVeicDeseja.setBoxShadowConfigVerticalLength(10);
        hBoxTipoVeicDeseja.setBoxShadowConfigBlurRadius(5);
        hBoxTipoVeicDeseja.setBoxShadowConfigSpreadRadius(0);
        hBoxTipoVeicDeseja.setBoxShadowConfigShadowColor("clBlack");
        hBoxTipoVeicDeseja.setBoxShadowConfigOpacity(75);
        hBoxTipoVeicDeseja.setVAlign("tvTop");
        FVBox1.addChildren(hBoxTipoVeicDeseja);
        hBoxTipoVeicDeseja.applyProperties();
    }

    public TFHBox hBoxLblTipoVeicDeseja = new TFHBox();

    private void init_hBoxLblTipoVeicDeseja() {
        hBoxLblTipoVeicDeseja.setName("hBoxLblTipoVeicDeseja");
        hBoxLblTipoVeicDeseja.setLeft(0);
        hBoxLblTipoVeicDeseja.setTop(0);
        hBoxLblTipoVeicDeseja.setWidth(276);
        hBoxLblTipoVeicDeseja.setHeight(37);
        hBoxLblTipoVeicDeseja.setBorderStyle("stNone");
        hBoxLblTipoVeicDeseja.setPaddingTop(8);
        hBoxLblTipoVeicDeseja.setPaddingLeft(0);
        hBoxLblTipoVeicDeseja.setPaddingRight(0);
        hBoxLblTipoVeicDeseja.setPaddingBottom(0);
        hBoxLblTipoVeicDeseja.setMarginTop(0);
        hBoxLblTipoVeicDeseja.setMarginLeft(0);
        hBoxLblTipoVeicDeseja.setMarginRight(0);
        hBoxLblTipoVeicDeseja.setMarginBottom(0);
        hBoxLblTipoVeicDeseja.setSpacing(1);
        hBoxLblTipoVeicDeseja.setFlexVflex("ftFalse");
        hBoxLblTipoVeicDeseja.setFlexHflex("ftFalse");
        hBoxLblTipoVeicDeseja.setScrollable(false);
        hBoxLblTipoVeicDeseja.setBoxShadowConfigHorizontalLength(10);
        hBoxLblTipoVeicDeseja.setBoxShadowConfigVerticalLength(10);
        hBoxLblTipoVeicDeseja.setBoxShadowConfigBlurRadius(5);
        hBoxLblTipoVeicDeseja.setBoxShadowConfigSpreadRadius(0);
        hBoxLblTipoVeicDeseja.setBoxShadowConfigShadowColor("clBlack");
        hBoxLblTipoVeicDeseja.setBoxShadowConfigOpacity(75);
        hBoxLblTipoVeicDeseja.setVAlign("tvTop");
        hBoxTipoVeicDeseja.addChildren(hBoxLblTipoVeicDeseja);
        hBoxLblTipoVeicDeseja.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(0);
        FVBox7.setWidth(264);
        FVBox7.setHeight(33);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftTrue");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        hBoxLblTipoVeicDeseja.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFLabel FLabel14 = new TFLabel();

    private void init_FLabel14() {
        FLabel14.setName("FLabel14");
        FLabel14.setLeft(0);
        FLabel14.setTop(0);
        FLabel14.setWidth(133);
        FLabel14.setHeight(13);
        FLabel14.setCaption("Tipo de Ve\u00EDculo que Deseja?");
        FLabel14.setFontColor("clWindowText");
        FLabel14.setFontSize(-11);
        FLabel14.setFontName("Tahoma");
        FLabel14.setFontStyle("[]");
        FLabel14.setVerticalAlignment("taVerticalCenter");
        FLabel14.setWordBreak(false);
        FVBox7.addChildren(FLabel14);
        FLabel14.applyProperties();
    }

    public TFLabel lblValTipoVecDeseja = new TFLabel();

    private void init_lblValTipoVecDeseja() {
        lblValTipoVecDeseja.setName("lblValTipoVecDeseja");
        lblValTipoVecDeseja.setLeft(0);
        lblValTipoVecDeseja.setTop(14);
        lblValTipoVecDeseja.setWidth(178);
        lblValTipoVecDeseja.setHeight(13);
        lblValTipoVecDeseja.setCaption("Informe o Tipo de Ve\u00EDculo que Deseja");
        lblValTipoVecDeseja.setFontColor("clRed");
        lblValTipoVecDeseja.setFontSize(-11);
        lblValTipoVecDeseja.setFontName("Tahoma");
        lblValTipoVecDeseja.setFontStyle("[]");
        lblValTipoVecDeseja.setVisible(false);
        lblValTipoVecDeseja.setVerticalAlignment("taVerticalCenter");
        lblValTipoVecDeseja.setWordBreak(false);
        FVBox7.addChildren(lblValTipoVecDeseja);
        lblValTipoVecDeseja.applyProperties();
    }

    public TFHBox FHBox56 = new TFHBox();

    private void init_FHBox56() {
        FHBox56.setName("FHBox56");
        FHBox56.setLeft(276);
        FHBox56.setTop(0);
        FHBox56.setWidth(180);
        FHBox56.setHeight(37);
        FHBox56.setBorderStyle("stNone");
        FHBox56.setPaddingTop(0);
        FHBox56.setPaddingLeft(0);
        FHBox56.setPaddingRight(0);
        FHBox56.setPaddingBottom(0);
        FHBox56.setMarginTop(0);
        FHBox56.setMarginLeft(0);
        FHBox56.setMarginRight(0);
        FHBox56.setMarginBottom(0);
        FHBox56.setSpacing(0);
        FHBox56.setFlexVflex("ftFalse");
        FHBox56.setFlexHflex("ftFalse");
        FHBox56.setScrollable(false);
        FHBox56.setBoxShadowConfigHorizontalLength(10);
        FHBox56.setBoxShadowConfigVerticalLength(10);
        FHBox56.setBoxShadowConfigBlurRadius(5);
        FHBox56.setBoxShadowConfigSpreadRadius(0);
        FHBox56.setBoxShadowConfigShadowColor("clBlack");
        FHBox56.setBoxShadowConfigOpacity(75);
        FHBox56.setVAlign("tvTop");
        hBoxTipoVeicDeseja.addChildren(FHBox56);
        FHBox56.applyProperties();
    }

    public TFHBox hBoxVeicNovo = new TFHBox();

    private void init_hBoxVeicNovo() {
        hBoxVeicNovo.setName("hBoxVeicNovo");
        hBoxVeicNovo.setLeft(0);
        hBoxVeicNovo.setTop(0);
        hBoxVeicNovo.setWidth(90);
        hBoxVeicNovo.setHeight(35);
        hBoxVeicNovo.setAlign("alClient");
        hBoxVeicNovo.setBorderStyle("stNone");
        hBoxVeicNovo.setColor("clSilver");
        hBoxVeicNovo.setPaddingTop(0);
        hBoxVeicNovo.setPaddingLeft(0);
        hBoxVeicNovo.setPaddingRight(0);
        hBoxVeicNovo.setPaddingBottom(0);
        hBoxVeicNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxVeicNovoClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxVeicNovo", "OnClick");
        });
        hBoxVeicNovo.setMarginTop(0);
        hBoxVeicNovo.setMarginLeft(0);
        hBoxVeicNovo.setMarginRight(0);
        hBoxVeicNovo.setMarginBottom(0);
        hBoxVeicNovo.setSpacing(1);
        hBoxVeicNovo.setFlexVflex("ftTrue");
        hBoxVeicNovo.setFlexHflex("ftTrue");
        hBoxVeicNovo.setScrollable(false);
        hBoxVeicNovo.setBoxShadowConfigHorizontalLength(10);
        hBoxVeicNovo.setBoxShadowConfigVerticalLength(10);
        hBoxVeicNovo.setBoxShadowConfigBlurRadius(5);
        hBoxVeicNovo.setBoxShadowConfigSpreadRadius(0);
        hBoxVeicNovo.setBoxShadowConfigShadowColor("clBlack");
        hBoxVeicNovo.setBoxShadowConfigOpacity(75);
        hBoxVeicNovo.setVAlign("tvTop");
        FHBox56.addChildren(hBoxVeicNovo);
        hBoxVeicNovo.applyProperties();
    }

    public TFHBox FHBox58 = new TFHBox();

    private void init_FHBox58() {
        FHBox58.setName("FHBox58");
        FHBox58.setLeft(0);
        FHBox58.setTop(0);
        FHBox58.setWidth(21);
        FHBox58.setHeight(18);
        FHBox58.setBorderStyle("stNone");
        FHBox58.setPaddingTop(0);
        FHBox58.setPaddingLeft(0);
        FHBox58.setPaddingRight(0);
        FHBox58.setPaddingBottom(0);
        FHBox58.setMarginTop(0);
        FHBox58.setMarginLeft(0);
        FHBox58.setMarginRight(0);
        FHBox58.setMarginBottom(0);
        FHBox58.setSpacing(1);
        FHBox58.setFlexVflex("ftFalse");
        FHBox58.setFlexHflex("ftTrue");
        FHBox58.setScrollable(false);
        FHBox58.setBoxShadowConfigHorizontalLength(10);
        FHBox58.setBoxShadowConfigVerticalLength(10);
        FHBox58.setBoxShadowConfigBlurRadius(5);
        FHBox58.setBoxShadowConfigSpreadRadius(0);
        FHBox58.setBoxShadowConfigShadowColor("clBlack");
        FHBox58.setBoxShadowConfigOpacity(75);
        FHBox58.setVAlign("tvTop");
        hBoxVeicNovo.addChildren(FHBox58);
        FHBox58.applyProperties();
    }

    public TFHBox FHBox59 = new TFHBox();

    private void init_FHBox59() {
        FHBox59.setName("FHBox59");
        FHBox59.setLeft(21);
        FHBox59.setTop(0);
        FHBox59.setWidth(44);
        FHBox59.setHeight(31);
        FHBox59.setBorderStyle("stNone");
        FHBox59.setPaddingTop(10);
        FHBox59.setPaddingLeft(0);
        FHBox59.setPaddingRight(0);
        FHBox59.setPaddingBottom(0);
        FHBox59.setMarginTop(0);
        FHBox59.setMarginLeft(0);
        FHBox59.setMarginRight(0);
        FHBox59.setMarginBottom(0);
        FHBox59.setSpacing(1);
        FHBox59.setFlexVflex("ftFalse");
        FHBox59.setFlexHflex("ftMin");
        FHBox59.setScrollable(false);
        FHBox59.setBoxShadowConfigHorizontalLength(10);
        FHBox59.setBoxShadowConfigVerticalLength(10);
        FHBox59.setBoxShadowConfigBlurRadius(5);
        FHBox59.setBoxShadowConfigSpreadRadius(0);
        FHBox59.setBoxShadowConfigShadowColor("clBlack");
        FHBox59.setBoxShadowConfigOpacity(75);
        FHBox59.setVAlign("tvTop");
        hBoxVeicNovo.addChildren(FHBox59);
        FHBox59.applyProperties();
    }

    public TFLabel FLabel15 = new TFLabel();

    private void init_FLabel15() {
        FLabel15.setName("FLabel15");
        FLabel15.setLeft(0);
        FLabel15.setTop(0);
        FLabel15.setWidth(39);
        FLabel15.setHeight(18);
        FLabel15.setCaption("Novo");
        FLabel15.setFontColor("clBlack");
        FLabel15.setFontSize(-15);
        FLabel15.setFontName("Tahoma");
        FLabel15.setFontStyle("[fsBold]");
        FLabel15.setVerticalAlignment("taVerticalCenter");
        FLabel15.setWordBreak(false);
        FHBox59.addChildren(FLabel15);
        FLabel15.applyProperties();
    }

    public TFHBox FHBox60 = new TFHBox();

    private void init_FHBox60() {
        FHBox60.setName("FHBox60");
        FHBox60.setLeft(65);
        FHBox60.setTop(0);
        FHBox60.setWidth(19);
        FHBox60.setHeight(18);
        FHBox60.setBorderStyle("stNone");
        FHBox60.setPaddingTop(0);
        FHBox60.setPaddingLeft(0);
        FHBox60.setPaddingRight(0);
        FHBox60.setPaddingBottom(0);
        FHBox60.setMarginTop(0);
        FHBox60.setMarginLeft(0);
        FHBox60.setMarginRight(0);
        FHBox60.setMarginBottom(0);
        FHBox60.setSpacing(1);
        FHBox60.setFlexVflex("ftFalse");
        FHBox60.setFlexHflex("ftTrue");
        FHBox60.setScrollable(false);
        FHBox60.setBoxShadowConfigHorizontalLength(10);
        FHBox60.setBoxShadowConfigVerticalLength(10);
        FHBox60.setBoxShadowConfigBlurRadius(5);
        FHBox60.setBoxShadowConfigSpreadRadius(0);
        FHBox60.setBoxShadowConfigShadowColor("clBlack");
        FHBox60.setBoxShadowConfigOpacity(75);
        FHBox60.setVAlign("tvTop");
        hBoxVeicNovo.addChildren(FHBox60);
        FHBox60.applyProperties();
    }

    public TFHBox hBoxVeicSemiNovo = new TFHBox();

    private void init_hBoxVeicSemiNovo() {
        hBoxVeicSemiNovo.setName("hBoxVeicSemiNovo");
        hBoxVeicSemiNovo.setLeft(90);
        hBoxVeicSemiNovo.setTop(0);
        hBoxVeicSemiNovo.setWidth(94);
        hBoxVeicSemiNovo.setHeight(35);
        hBoxVeicSemiNovo.setAlign("alClient");
        hBoxVeicSemiNovo.setBorderStyle("stNone");
        hBoxVeicSemiNovo.setColor("clSilver");
        hBoxVeicSemiNovo.setPaddingTop(0);
        hBoxVeicSemiNovo.setPaddingLeft(0);
        hBoxVeicSemiNovo.setPaddingRight(0);
        hBoxVeicSemiNovo.setPaddingBottom(0);
        hBoxVeicSemiNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxVeicSemiNovoClick(event);
            processarFlow("FrmOportunidadeVendas", "hBoxVeicSemiNovo", "OnClick");
        });
        hBoxVeicSemiNovo.setMarginTop(0);
        hBoxVeicSemiNovo.setMarginLeft(0);
        hBoxVeicSemiNovo.setMarginRight(0);
        hBoxVeicSemiNovo.setMarginBottom(0);
        hBoxVeicSemiNovo.setSpacing(1);
        hBoxVeicSemiNovo.setFlexVflex("ftTrue");
        hBoxVeicSemiNovo.setFlexHflex("ftTrue");
        hBoxVeicSemiNovo.setScrollable(false);
        hBoxVeicSemiNovo.setBoxShadowConfigHorizontalLength(10);
        hBoxVeicSemiNovo.setBoxShadowConfigVerticalLength(10);
        hBoxVeicSemiNovo.setBoxShadowConfigBlurRadius(5);
        hBoxVeicSemiNovo.setBoxShadowConfigSpreadRadius(0);
        hBoxVeicSemiNovo.setBoxShadowConfigShadowColor("clBlack");
        hBoxVeicSemiNovo.setBoxShadowConfigOpacity(75);
        hBoxVeicSemiNovo.setVAlign("tvTop");
        FHBox56.addChildren(hBoxVeicSemiNovo);
        hBoxVeicSemiNovo.applyProperties();
    }

    public TFHBox FHBox62 = new TFHBox();

    private void init_FHBox62() {
        FHBox62.setName("FHBox62");
        FHBox62.setLeft(0);
        FHBox62.setTop(0);
        FHBox62.setWidth(15);
        FHBox62.setHeight(18);
        FHBox62.setBorderStyle("stNone");
        FHBox62.setPaddingTop(0);
        FHBox62.setPaddingLeft(0);
        FHBox62.setPaddingRight(0);
        FHBox62.setPaddingBottom(0);
        FHBox62.setMarginTop(0);
        FHBox62.setMarginLeft(0);
        FHBox62.setMarginRight(0);
        FHBox62.setMarginBottom(0);
        FHBox62.setSpacing(1);
        FHBox62.setFlexVflex("ftFalse");
        FHBox62.setFlexHflex("ftTrue");
        FHBox62.setScrollable(false);
        FHBox62.setBoxShadowConfigHorizontalLength(10);
        FHBox62.setBoxShadowConfigVerticalLength(10);
        FHBox62.setBoxShadowConfigBlurRadius(5);
        FHBox62.setBoxShadowConfigSpreadRadius(0);
        FHBox62.setBoxShadowConfigShadowColor("clBlack");
        FHBox62.setBoxShadowConfigOpacity(75);
        FHBox62.setVAlign("tvTop");
        hBoxVeicSemiNovo.addChildren(FHBox62);
        FHBox62.applyProperties();
    }

    public TFHBox FHBox63 = new TFHBox();

    private void init_FHBox63() {
        FHBox63.setName("FHBox63");
        FHBox63.setLeft(15);
        FHBox63.setTop(0);
        FHBox63.setWidth(50);
        FHBox63.setHeight(33);
        FHBox63.setBorderStyle("stNone");
        FHBox63.setPaddingTop(10);
        FHBox63.setPaddingLeft(0);
        FHBox63.setPaddingRight(0);
        FHBox63.setPaddingBottom(0);
        FHBox63.setMarginTop(0);
        FHBox63.setMarginLeft(0);
        FHBox63.setMarginRight(0);
        FHBox63.setMarginBottom(0);
        FHBox63.setSpacing(1);
        FHBox63.setFlexVflex("ftFalse");
        FHBox63.setFlexHflex("ftMin");
        FHBox63.setScrollable(false);
        FHBox63.setBoxShadowConfigHorizontalLength(10);
        FHBox63.setBoxShadowConfigVerticalLength(10);
        FHBox63.setBoxShadowConfigBlurRadius(5);
        FHBox63.setBoxShadowConfigSpreadRadius(0);
        FHBox63.setBoxShadowConfigShadowColor("clBlack");
        FHBox63.setBoxShadowConfigOpacity(75);
        FHBox63.setVAlign("tvTop");
        hBoxVeicSemiNovo.addChildren(FHBox63);
        FHBox63.applyProperties();
    }

    public TFLabel FLabel16 = new TFLabel();

    private void init_FLabel16() {
        FLabel16.setName("FLabel16");
        FLabel16.setLeft(0);
        FLabel16.setTop(0);
        FLabel16.setWidth(73);
        FLabel16.setHeight(18);
        FLabel16.setCaption("Seminovo");
        FLabel16.setFontColor("clBlack");
        FLabel16.setFontSize(-15);
        FLabel16.setFontName("Tahoma");
        FLabel16.setFontStyle("[fsBold]");
        FLabel16.setVerticalAlignment("taVerticalCenter");
        FLabel16.setWordBreak(false);
        FHBox63.addChildren(FLabel16);
        FLabel16.applyProperties();
    }

    public TFHBox FHBox64 = new TFHBox();

    private void init_FHBox64() {
        FHBox64.setName("FHBox64");
        FHBox64.setLeft(65);
        FHBox64.setTop(0);
        FHBox64.setWidth(15);
        FHBox64.setHeight(18);
        FHBox64.setBorderStyle("stNone");
        FHBox64.setPaddingTop(0);
        FHBox64.setPaddingLeft(0);
        FHBox64.setPaddingRight(0);
        FHBox64.setPaddingBottom(0);
        FHBox64.setMarginTop(0);
        FHBox64.setMarginLeft(0);
        FHBox64.setMarginRight(0);
        FHBox64.setMarginBottom(0);
        FHBox64.setSpacing(1);
        FHBox64.setFlexVflex("ftFalse");
        FHBox64.setFlexHflex("ftTrue");
        FHBox64.setScrollable(false);
        FHBox64.setBoxShadowConfigHorizontalLength(10);
        FHBox64.setBoxShadowConfigVerticalLength(10);
        FHBox64.setBoxShadowConfigBlurRadius(5);
        FHBox64.setBoxShadowConfigSpreadRadius(0);
        FHBox64.setBoxShadowConfigShadowColor("clBlack");
        FHBox64.setBoxShadowConfigOpacity(75);
        FHBox64.setVAlign("tvTop");
        hBoxVeicSemiNovo.addChildren(FHBox64);
        FHBox64.applyProperties();
    }

    public TFHBox hBoxObsLabel = new TFHBox();

    private void init_hBoxObsLabel() {
        hBoxObsLabel.setName("hBoxObsLabel");
        hBoxObsLabel.setLeft(0);
        hBoxObsLabel.setTop(344);
        hBoxObsLabel.setWidth(470);
        hBoxObsLabel.setHeight(46);
        hBoxObsLabel.setBorderStyle("stNone");
        hBoxObsLabel.setPaddingTop(10);
        hBoxObsLabel.setPaddingLeft(0);
        hBoxObsLabel.setPaddingRight(0);
        hBoxObsLabel.setPaddingBottom(0);
        hBoxObsLabel.setVisible(false);
        hBoxObsLabel.setMarginTop(0);
        hBoxObsLabel.setMarginLeft(0);
        hBoxObsLabel.setMarginRight(0);
        hBoxObsLabel.setMarginBottom(0);
        hBoxObsLabel.setSpacing(1);
        hBoxObsLabel.setFlexVflex("ftFalse");
        hBoxObsLabel.setFlexHflex("ftTrue");
        hBoxObsLabel.setScrollable(false);
        hBoxObsLabel.setBoxShadowConfigHorizontalLength(10);
        hBoxObsLabel.setBoxShadowConfigVerticalLength(10);
        hBoxObsLabel.setBoxShadowConfigBlurRadius(5);
        hBoxObsLabel.setBoxShadowConfigSpreadRadius(0);
        hBoxObsLabel.setBoxShadowConfigShadowColor("clBlack");
        hBoxObsLabel.setBoxShadowConfigOpacity(75);
        hBoxObsLabel.setVAlign("tvTop");
        FVBox1.addChildren(hBoxObsLabel);
        hBoxObsLabel.applyProperties();
    }

    public TFHBox hbLblObsVend = new TFHBox();

    private void init_hbLblObsVend() {
        hbLblObsVend.setName("hbLblObsVend");
        hbLblObsVend.setLeft(0);
        hbLblObsVend.setTop(0);
        hbLblObsVend.setWidth(276);
        hbLblObsVend.setHeight(37);
        hbLblObsVend.setBorderStyle("stNone");
        hbLblObsVend.setPaddingTop(8);
        hbLblObsVend.setPaddingLeft(0);
        hbLblObsVend.setPaddingRight(0);
        hbLblObsVend.setPaddingBottom(0);
        hbLblObsVend.setMarginTop(0);
        hbLblObsVend.setMarginLeft(0);
        hbLblObsVend.setMarginRight(0);
        hbLblObsVend.setMarginBottom(0);
        hbLblObsVend.setSpacing(1);
        hbLblObsVend.setFlexVflex("ftFalse");
        hbLblObsVend.setFlexHflex("ftFalse");
        hbLblObsVend.setScrollable(false);
        hbLblObsVend.setBoxShadowConfigHorizontalLength(10);
        hbLblObsVend.setBoxShadowConfigVerticalLength(10);
        hbLblObsVend.setBoxShadowConfigBlurRadius(5);
        hbLblObsVend.setBoxShadowConfigSpreadRadius(0);
        hbLblObsVend.setBoxShadowConfigShadowColor("clBlack");
        hbLblObsVend.setBoxShadowConfigOpacity(75);
        hbLblObsVend.setVAlign("tvTop");
        hBoxObsLabel.addChildren(hbLblObsVend);
        hbLblObsVend.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(0);
        FVBox8.setTop(0);
        FVBox8.setWidth(262);
        FVBox8.setHeight(32);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftTrue");
        FVBox8.setFlexHflex("ftTrue");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        hbLblObsVend.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel FLabel17 = new TFLabel();

    private void init_FLabel17() {
        FLabel17.setName("FLabel17");
        FLabel17.setLeft(0);
        FLabel17.setTop(0);
        FLabel17.setWidth(141);
        FLabel17.setHeight(13);
        FLabel17.setCaption("Observa\u00E7\u00E3o para o Vendedor");
        FLabel17.setFontColor("clWindowText");
        FLabel17.setFontSize(-11);
        FLabel17.setFontName("Tahoma");
        FLabel17.setFontStyle("[]");
        FLabel17.setVerticalAlignment("taVerticalCenter");
        FLabel17.setWordBreak(false);
        FVBox8.addChildren(FLabel17);
        FLabel17.applyProperties();
    }

    public TFMemo memOportVendas = new TFMemo();

    private void init_memOportVendas() {
        memOportVendas.setName("memOportVendas");
        memOportVendas.setLeft(0);
        memOportVendas.setTop(391);
        memOportVendas.setWidth(469);
        memOportVendas.setHeight(125);
        memOportVendas.setCharCase("ccNormal");
        memOportVendas.setFontColor("clWindowText");
        memOportVendas.setFontSize(-11);
        memOportVendas.setFontName("Tahoma");
        memOportVendas.setFontStyle("[]");
        memOportVendas.setMaxlength(1000);
        memOportVendas.setVisible(false);
        memOportVendas.setFlexVflex("ftFalse");
        memOportVendas.setFlexHflex("ftTrue");
        memOportVendas.setConstraintCheckWhen("cwImmediate");
        memOportVendas.setConstraintCheckType("ctExpression");
        memOportVendas.setConstraintFocusOnError(false);
        memOportVendas.setConstraintEnableUI(true);
        memOportVendas.setConstraintEnabled(false);
        memOportVendas.setConstraintFormCheck(true);
        memOportVendas.setRequired(false);
        FVBox1.addChildren(memOportVendas);
        memOportVendas.applyProperties();
        addValidatable(memOportVendas);
    }

    public TFVBox hboxespacofinal = new TFVBox();

    private void init_hboxespacofinal() {
        hboxespacofinal.setName("hboxespacofinal");
        hboxespacofinal.setLeft(0);
        hboxespacofinal.setTop(517);
        hboxespacofinal.setWidth(185);
        hboxespacofinal.setHeight(17);
        hboxespacofinal.setBorderStyle("stNone");
        hboxespacofinal.setPaddingTop(0);
        hboxespacofinal.setPaddingLeft(0);
        hboxespacofinal.setPaddingRight(0);
        hboxespacofinal.setPaddingBottom(0);
        hboxespacofinal.setMarginTop(0);
        hboxespacofinal.setMarginLeft(0);
        hboxespacofinal.setMarginRight(0);
        hboxespacofinal.setMarginBottom(0);
        hboxespacofinal.setSpacing(1);
        hboxespacofinal.setFlexVflex("ftFalse");
        hboxespacofinal.setFlexHflex("ftFalse");
        hboxespacofinal.setScrollable(false);
        hboxespacofinal.setBoxShadowConfigHorizontalLength(10);
        hboxespacofinal.setBoxShadowConfigVerticalLength(10);
        hboxespacofinal.setBoxShadowConfigBlurRadius(5);
        hboxespacofinal.setBoxShadowConfigSpreadRadius(0);
        hboxespacofinal.setBoxShadowConfigShadowColor("clBlack");
        hboxespacofinal.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(hboxespacofinal);
        hboxespacofinal.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbAgendaPersiste);
        sc.getTables().add(item0);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarOsClick(final Event<Object> event) {
        if (btnVoltarOs.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltarOs");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnProximoClick(final Event<Object> event) {
        if (btnProximo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnProximo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void hBoxSimPretVenderClick(final Event<Object> event);

    public abstract void hBoxNaoPretVenderClick(final Event<Object> event);

    public abstract void edtDataPretendeTrocarChange(final Event<Object> event);

    public abstract void hBoxSimVeicAtMarcaClick(final Event<Object> event);

    public abstract void hBoxNaoVeicAtMarcaClick(final Event<Object> event);

    public abstract void hBoxSimTrocarModClick(final Event<Object> event);

    public abstract void hBoxNaoTrocarModClick(final Event<Object> event);

    public abstract void hBoxSimAvaliacaoClick(final Event<Object> event);

    public abstract void hBoxNaoAvaliacaoClick(final Event<Object> event);

    public abstract void hBoxVeicNovoClick(final Event<Object> event);

    public abstract void hBoxVeicSemiNovoClick(final Event<Object> event);

}