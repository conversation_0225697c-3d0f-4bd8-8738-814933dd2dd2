package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAssinaturaDigitalSignatario extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AssinaturaDigitalSignatarioRNA rn = null;

    public FrmAssinaturaDigitalSignatario() {
        try {
            rn = (freedom.bytecode.rn.AssinaturaDigitalSignatarioRNA) getRN(freedom.bytecode.rn.wizard.AssinaturaDigitalSignatarioRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_hboxTabDadosAssinaturaMain();
        init_hboxBotoes();
        init_btnVoltar();
        init_btnAceitar();
        init_hboxTabDadosSignatarioMain();
        init_hboxLinhatbTbSolicitacoesAssinaturas1();
        init_HboxlblNome();
        init_lblNome();
        init_edNome();
        init_hboxLinhatbTbSolicitacoesAssinaturas2();
        init_HboxlblEmail();
        init_lblEmail();
        init_edEmail();
        init_hboxLinhatbTbSolicitacoesAssinaturas3();
        init_hBoxlblTelefone();
        init_lblTelefone();
        init_edTelefone();
        init_hboxTabDadosSignatarioMainEspaco1();
        init_vBoxGridSolicitacaoAssinaturaEspacoRodape();
        init_FrmAssinaturaDigitalSignatario();
    }

    protected TFForm FrmAssinaturaDigitalSignatario = this;
    private void init_FrmAssinaturaDigitalSignatario() {
        FrmAssinaturaDigitalSignatario.setName("FrmAssinaturaDigitalSignatario");
        FrmAssinaturaDigitalSignatario.setCaption("Editar Signat\u00E1rio");
        FrmAssinaturaDigitalSignatario.setClientHeight(238);
        FrmAssinaturaDigitalSignatario.setClientWidth(456);
        FrmAssinaturaDigitalSignatario.setColor("clBtnFace");
        FrmAssinaturaDigitalSignatario.setWOrigem("EhMain");
        FrmAssinaturaDigitalSignatario.setWKey("474013");
        FrmAssinaturaDigitalSignatario.setSpacing(0);
        FrmAssinaturaDigitalSignatario.applyProperties();
    }

    public TFVBox hboxTabDadosAssinaturaMain = new TFVBox();

    private void init_hboxTabDadosAssinaturaMain() {
        hboxTabDadosAssinaturaMain.setName("hboxTabDadosAssinaturaMain");
        hboxTabDadosAssinaturaMain.setLeft(0);
        hboxTabDadosAssinaturaMain.setTop(0);
        hboxTabDadosAssinaturaMain.setWidth(456);
        hboxTabDadosAssinaturaMain.setHeight(238);
        hboxTabDadosAssinaturaMain.setAlign("alClient");
        hboxTabDadosAssinaturaMain.setBorderStyle("stNone");
        hboxTabDadosAssinaturaMain.setPaddingTop(5);
        hboxTabDadosAssinaturaMain.setPaddingLeft(5);
        hboxTabDadosAssinaturaMain.setPaddingRight(5);
        hboxTabDadosAssinaturaMain.setPaddingBottom(5);
        hboxTabDadosAssinaturaMain.setMarginTop(0);
        hboxTabDadosAssinaturaMain.setMarginLeft(0);
        hboxTabDadosAssinaturaMain.setMarginRight(0);
        hboxTabDadosAssinaturaMain.setMarginBottom(0);
        hboxTabDadosAssinaturaMain.setSpacing(5);
        hboxTabDadosAssinaturaMain.setFlexVflex("ftTrue");
        hboxTabDadosAssinaturaMain.setFlexHflex("ftTrue");
        hboxTabDadosAssinaturaMain.setScrollable(false);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigHorizontalLength(10);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigVerticalLength(10);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigBlurRadius(5);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigSpreadRadius(0);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigShadowColor("clBlack");
        hboxTabDadosAssinaturaMain.setBoxShadowConfigOpacity(75);
        FrmAssinaturaDigitalSignatario.addChildren(hboxTabDadosAssinaturaMain);
        hboxTabDadosAssinaturaMain.applyProperties();
    }

    public TFHBox hboxBotoes = new TFHBox();

    private void init_hboxBotoes() {
        hboxBotoes.setName("hboxBotoes");
        hboxBotoes.setLeft(0);
        hboxBotoes.setTop(0);
        hboxBotoes.setWidth(450);
        hboxBotoes.setHeight(57);
        hboxBotoes.setAlign("alTop");
        hboxBotoes.setBorderStyle("stNone");
        hboxBotoes.setPaddingTop(0);
        hboxBotoes.setPaddingLeft(0);
        hboxBotoes.setPaddingRight(5);
        hboxBotoes.setPaddingBottom(0);
        hboxBotoes.setMarginTop(0);
        hboxBotoes.setMarginLeft(0);
        hboxBotoes.setMarginRight(0);
        hboxBotoes.setMarginBottom(0);
        hboxBotoes.setSpacing(5);
        hboxBotoes.setFlexVflex("ftFalse");
        hboxBotoes.setFlexHflex("ftTrue");
        hboxBotoes.setScrollable(false);
        hboxBotoes.setBoxShadowConfigHorizontalLength(10);
        hboxBotoes.setBoxShadowConfigVerticalLength(10);
        hboxBotoes.setBoxShadowConfigBlurRadius(5);
        hboxBotoes.setBoxShadowConfigSpreadRadius(0);
        hboxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hboxBotoes.setBoxShadowConfigOpacity(75);
        hboxBotoes.setVAlign("tvTop");
        hboxTabDadosAssinaturaMain.addChildren(hboxBotoes);
        hboxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(50);
        btnVoltar.setHeight(50);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmAssinaturaDigitalSignatario", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hboxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(50);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(50);
        btnAceitar.setHeight(50);
        btnAceitar.setHint("Salvar dados");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Salvar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmAssinaturaDigitalSignatario", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(700080);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        hboxBotoes.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFVBox hboxTabDadosSignatarioMain = new TFVBox();

    private void init_hboxTabDadosSignatarioMain() {
        hboxTabDadosSignatarioMain.setName("hboxTabDadosSignatarioMain");
        hboxTabDadosSignatarioMain.setLeft(0);
        hboxTabDadosSignatarioMain.setTop(58);
        hboxTabDadosSignatarioMain.setWidth(450);
        hboxTabDadosSignatarioMain.setHeight(110);
        hboxTabDadosSignatarioMain.setAlign("alClient");
        hboxTabDadosSignatarioMain.setBorderStyle("stNone");
        hboxTabDadosSignatarioMain.setPaddingTop(5);
        hboxTabDadosSignatarioMain.setPaddingLeft(5);
        hboxTabDadosSignatarioMain.setPaddingRight(20);
        hboxTabDadosSignatarioMain.setPaddingBottom(5);
        hboxTabDadosSignatarioMain.setMarginTop(0);
        hboxTabDadosSignatarioMain.setMarginLeft(0);
        hboxTabDadosSignatarioMain.setMarginRight(0);
        hboxTabDadosSignatarioMain.setMarginBottom(0);
        hboxTabDadosSignatarioMain.setSpacing(5);
        hboxTabDadosSignatarioMain.setFlexVflex("ftTrue");
        hboxTabDadosSignatarioMain.setFlexHflex("ftTrue");
        hboxTabDadosSignatarioMain.setScrollable(false);
        hboxTabDadosSignatarioMain.setBoxShadowConfigHorizontalLength(10);
        hboxTabDadosSignatarioMain.setBoxShadowConfigVerticalLength(10);
        hboxTabDadosSignatarioMain.setBoxShadowConfigBlurRadius(5);
        hboxTabDadosSignatarioMain.setBoxShadowConfigSpreadRadius(0);
        hboxTabDadosSignatarioMain.setBoxShadowConfigShadowColor("clBlack");
        hboxTabDadosSignatarioMain.setBoxShadowConfigOpacity(75);
        hboxTabDadosAssinaturaMain.addChildren(hboxTabDadosSignatarioMain);
        hboxTabDadosSignatarioMain.applyProperties();
    }

    public TFHBox hboxLinhatbTbSolicitacoesAssinaturas1 = new TFHBox();

    private void init_hboxLinhatbTbSolicitacoesAssinaturas1() {
        hboxLinhatbTbSolicitacoesAssinaturas1.setName("hboxLinhatbTbSolicitacoesAssinaturas1");
        hboxLinhatbTbSolicitacoesAssinaturas1.setLeft(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setTop(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setWidth(430);
        hboxLinhatbTbSolicitacoesAssinaturas1.setHeight(30);
        hboxLinhatbTbSolicitacoesAssinaturas1.setBorderStyle("stNone");
        hboxLinhatbTbSolicitacoesAssinaturas1.setPaddingTop(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setPaddingLeft(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setPaddingRight(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setPaddingBottom(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setMarginTop(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setMarginLeft(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setMarginRight(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setMarginBottom(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setSpacing(4);
        hboxLinhatbTbSolicitacoesAssinaturas1.setFlexVflex("ftMin");
        hboxLinhatbTbSolicitacoesAssinaturas1.setFlexHflex("ftTrue");
        hboxLinhatbTbSolicitacoesAssinaturas1.setScrollable(false);
        hboxLinhatbTbSolicitacoesAssinaturas1.setBoxShadowConfigHorizontalLength(10);
        hboxLinhatbTbSolicitacoesAssinaturas1.setBoxShadowConfigVerticalLength(10);
        hboxLinhatbTbSolicitacoesAssinaturas1.setBoxShadowConfigBlurRadius(5);
        hboxLinhatbTbSolicitacoesAssinaturas1.setBoxShadowConfigSpreadRadius(0);
        hboxLinhatbTbSolicitacoesAssinaturas1.setBoxShadowConfigShadowColor("clBlack");
        hboxLinhatbTbSolicitacoesAssinaturas1.setBoxShadowConfigOpacity(75);
        hboxLinhatbTbSolicitacoesAssinaturas1.setVAlign("tvTop");
        hboxTabDadosSignatarioMain.addChildren(hboxLinhatbTbSolicitacoesAssinaturas1);
        hboxLinhatbTbSolicitacoesAssinaturas1.applyProperties();
    }

    public TFHBox HboxlblNome = new TFHBox();

    private void init_HboxlblNome() {
        HboxlblNome.setName("HboxlblNome");
        HboxlblNome.setLeft(0);
        HboxlblNome.setTop(0);
        HboxlblNome.setWidth(67);
        HboxlblNome.setHeight(27);
        HboxlblNome.setBorderStyle("stNone");
        HboxlblNome.setPaddingTop(10);
        HboxlblNome.setPaddingLeft(0);
        HboxlblNome.setPaddingRight(0);
        HboxlblNome.setPaddingBottom(0);
        HboxlblNome.setMarginTop(0);
        HboxlblNome.setMarginLeft(0);
        HboxlblNome.setMarginRight(0);
        HboxlblNome.setMarginBottom(0);
        HboxlblNome.setSpacing(1);
        HboxlblNome.setFlexVflex("ftFalse");
        HboxlblNome.setFlexHflex("ftFalse");
        HboxlblNome.setScrollable(false);
        HboxlblNome.setBoxShadowConfigHorizontalLength(10);
        HboxlblNome.setBoxShadowConfigVerticalLength(10);
        HboxlblNome.setBoxShadowConfigBlurRadius(5);
        HboxlblNome.setBoxShadowConfigSpreadRadius(0);
        HboxlblNome.setBoxShadowConfigShadowColor("clBlack");
        HboxlblNome.setBoxShadowConfigOpacity(75);
        HboxlblNome.setVAlign("tvTop");
        hboxLinhatbTbSolicitacoesAssinaturas1.addChildren(HboxlblNome);
        HboxlblNome.applyProperties();
    }

    public TFLabel lblNome = new TFLabel();

    private void init_lblNome() {
        lblNome.setName("lblNome");
        lblNome.setLeft(0);
        lblNome.setTop(0);
        lblNome.setWidth(39);
        lblNome.setHeight(18);
        lblNome.setHint("Resumo pergunta");
        lblNome.setAlign("alRight");
        lblNome.setCaption("Nome");
        lblNome.setFontColor("clWindowText");
        lblNome.setFontSize(-15);
        lblNome.setFontName("Tahoma");
        lblNome.setFontStyle("[]");
        lblNome.setVerticalAlignment("taVerticalCenter");
        lblNome.setWordBreak(false);
        HboxlblNome.addChildren(lblNome);
        lblNome.applyProperties();
    }

    public TFString edNome = new TFString();

    private void init_edNome() {
        edNome.setName("edNome");
        edNome.setLeft(67);
        edNome.setTop(0);
        edNome.setWidth(300);
        edNome.setHeight(24);
        edNome.setFieldName("NOME");
        edNome.setFlex(true);
        edNome.setRequired(false);
        edNome.setPrompt("Nome signat\u00E1rio");
        edNome.setConstraintExpression("value is null or trim(value) = ''");
        edNome.setConstraintMessage("Campo Resumo pergunta, preenchimento \u00E9 obrigat\u00F3rio");
        edNome.setConstraintCheckWhen("cwImmediate");
        edNome.setConstraintCheckType("ctExpression");
        edNome.setConstraintFocusOnError(false);
        edNome.setConstraintGroupName("tbPergunta");
        edNome.setConstraintEnableUI(true);
        edNome.setConstraintEnabled(false);
        edNome.setConstraintFormCheck(false);
        edNome.setCharCase("ccNormal");
        edNome.setPwd(false);
        edNome.setMaxlength(100);
        edNome.setAlign("alLeft");
        edNome.setFontColor("clWindowText");
        edNome.setFontSize(-13);
        edNome.setFontName("Tahoma");
        edNome.setFontStyle("[]");
        edNome.setSaveLiteralCharacter(false);
        edNome.applyProperties();
        hboxLinhatbTbSolicitacoesAssinaturas1.addChildren(edNome);
        addValidatable(edNome);
    }

    public TFHBox hboxLinhatbTbSolicitacoesAssinaturas2 = new TFHBox();

    private void init_hboxLinhatbTbSolicitacoesAssinaturas2() {
        hboxLinhatbTbSolicitacoesAssinaturas2.setName("hboxLinhatbTbSolicitacoesAssinaturas2");
        hboxLinhatbTbSolicitacoesAssinaturas2.setLeft(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setTop(31);
        hboxLinhatbTbSolicitacoesAssinaturas2.setWidth(430);
        hboxLinhatbTbSolicitacoesAssinaturas2.setHeight(30);
        hboxLinhatbTbSolicitacoesAssinaturas2.setBorderStyle("stNone");
        hboxLinhatbTbSolicitacoesAssinaturas2.setPaddingTop(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setPaddingLeft(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setPaddingRight(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setPaddingBottom(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setMarginTop(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setMarginLeft(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setMarginRight(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setMarginBottom(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setSpacing(4);
        hboxLinhatbTbSolicitacoesAssinaturas2.setFlexVflex("ftMin");
        hboxLinhatbTbSolicitacoesAssinaturas2.setFlexHflex("ftTrue");
        hboxLinhatbTbSolicitacoesAssinaturas2.setScrollable(false);
        hboxLinhatbTbSolicitacoesAssinaturas2.setBoxShadowConfigHorizontalLength(10);
        hboxLinhatbTbSolicitacoesAssinaturas2.setBoxShadowConfigVerticalLength(10);
        hboxLinhatbTbSolicitacoesAssinaturas2.setBoxShadowConfigBlurRadius(5);
        hboxLinhatbTbSolicitacoesAssinaturas2.setBoxShadowConfigSpreadRadius(0);
        hboxLinhatbTbSolicitacoesAssinaturas2.setBoxShadowConfigShadowColor("clBlack");
        hboxLinhatbTbSolicitacoesAssinaturas2.setBoxShadowConfigOpacity(75);
        hboxLinhatbTbSolicitacoesAssinaturas2.setVAlign("tvTop");
        hboxTabDadosSignatarioMain.addChildren(hboxLinhatbTbSolicitacoesAssinaturas2);
        hboxLinhatbTbSolicitacoesAssinaturas2.applyProperties();
    }

    public TFHBox HboxlblEmail = new TFHBox();

    private void init_HboxlblEmail() {
        HboxlblEmail.setName("HboxlblEmail");
        HboxlblEmail.setLeft(0);
        HboxlblEmail.setTop(0);
        HboxlblEmail.setWidth(67);
        HboxlblEmail.setHeight(27);
        HboxlblEmail.setBorderStyle("stNone");
        HboxlblEmail.setPaddingTop(10);
        HboxlblEmail.setPaddingLeft(0);
        HboxlblEmail.setPaddingRight(0);
        HboxlblEmail.setPaddingBottom(0);
        HboxlblEmail.setMarginTop(0);
        HboxlblEmail.setMarginLeft(0);
        HboxlblEmail.setMarginRight(0);
        HboxlblEmail.setMarginBottom(0);
        HboxlblEmail.setSpacing(1);
        HboxlblEmail.setFlexVflex("ftFalse");
        HboxlblEmail.setFlexHflex("ftFalse");
        HboxlblEmail.setScrollable(false);
        HboxlblEmail.setBoxShadowConfigHorizontalLength(10);
        HboxlblEmail.setBoxShadowConfigVerticalLength(10);
        HboxlblEmail.setBoxShadowConfigBlurRadius(5);
        HboxlblEmail.setBoxShadowConfigSpreadRadius(0);
        HboxlblEmail.setBoxShadowConfigShadowColor("clBlack");
        HboxlblEmail.setBoxShadowConfigOpacity(75);
        HboxlblEmail.setVAlign("tvTop");
        hboxLinhatbTbSolicitacoesAssinaturas2.addChildren(HboxlblEmail);
        HboxlblEmail.applyProperties();
    }

    public TFLabel lblEmail = new TFLabel();

    private void init_lblEmail() {
        lblEmail.setName("lblEmail");
        lblEmail.setLeft(0);
        lblEmail.setTop(0);
        lblEmail.setWidth(33);
        lblEmail.setHeight(18);
        lblEmail.setHint("Resumo pergunta");
        lblEmail.setAlign("alRight");
        lblEmail.setCaption("Email");
        lblEmail.setFontColor("clWindowText");
        lblEmail.setFontSize(-15);
        lblEmail.setFontName("Tahoma");
        lblEmail.setFontStyle("[]");
        lblEmail.setVerticalAlignment("taVerticalCenter");
        lblEmail.setWordBreak(false);
        HboxlblEmail.addChildren(lblEmail);
        lblEmail.applyProperties();
    }

    public TFString edEmail = new TFString();

    private void init_edEmail() {
        edEmail.setName("edEmail");
        edEmail.setLeft(67);
        edEmail.setTop(0);
        edEmail.setWidth(300);
        edEmail.setHeight(24);
        edEmail.setFieldName("EMAIL");
        edEmail.setFlex(true);
        edEmail.setRequired(false);
        edEmail.setPrompt("Email signat\u00E1rio");
        edEmail.setConstraintExpression("value is null or trim(value) = ''");
        edEmail.setConstraintMessage("Campo Resumo pergunta, preenchimento \u00E9 obrigat\u00F3rio");
        edEmail.setConstraintCheckWhen("cwImmediate");
        edEmail.setConstraintCheckType("ctExpression");
        edEmail.setConstraintFocusOnError(false);
        edEmail.setConstraintGroupName("tbPergunta");
        edEmail.setConstraintEnableUI(true);
        edEmail.setConstraintEnabled(false);
        edEmail.setConstraintFormCheck(false);
        edEmail.setCharCase("ccNormal");
        edEmail.setPwd(false);
        edEmail.setMaxlength(100);
        edEmail.setAlign("alLeft");
        edEmail.setFontColor("clWindowText");
        edEmail.setFontSize(-13);
        edEmail.setFontName("Tahoma");
        edEmail.setFontStyle("[]");
        edEmail.setSaveLiteralCharacter(false);
        edEmail.applyProperties();
        hboxLinhatbTbSolicitacoesAssinaturas2.addChildren(edEmail);
        addValidatable(edEmail);
    }

    public TFHBox hboxLinhatbTbSolicitacoesAssinaturas3 = new TFHBox();

    private void init_hboxLinhatbTbSolicitacoesAssinaturas3() {
        hboxLinhatbTbSolicitacoesAssinaturas3.setName("hboxLinhatbTbSolicitacoesAssinaturas3");
        hboxLinhatbTbSolicitacoesAssinaturas3.setLeft(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setTop(62);
        hboxLinhatbTbSolicitacoesAssinaturas3.setWidth(430);
        hboxLinhatbTbSolicitacoesAssinaturas3.setHeight(30);
        hboxLinhatbTbSolicitacoesAssinaturas3.setBorderStyle("stNone");
        hboxLinhatbTbSolicitacoesAssinaturas3.setPaddingTop(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setPaddingLeft(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setPaddingRight(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setPaddingBottom(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setMarginTop(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setMarginLeft(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setMarginRight(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setMarginBottom(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setSpacing(4);
        hboxLinhatbTbSolicitacoesAssinaturas3.setFlexVflex("ftMin");
        hboxLinhatbTbSolicitacoesAssinaturas3.setFlexHflex("ftTrue");
        hboxLinhatbTbSolicitacoesAssinaturas3.setScrollable(false);
        hboxLinhatbTbSolicitacoesAssinaturas3.setBoxShadowConfigHorizontalLength(10);
        hboxLinhatbTbSolicitacoesAssinaturas3.setBoxShadowConfigVerticalLength(10);
        hboxLinhatbTbSolicitacoesAssinaturas3.setBoxShadowConfigBlurRadius(5);
        hboxLinhatbTbSolicitacoesAssinaturas3.setBoxShadowConfigSpreadRadius(0);
        hboxLinhatbTbSolicitacoesAssinaturas3.setBoxShadowConfigShadowColor("clBlack");
        hboxLinhatbTbSolicitacoesAssinaturas3.setBoxShadowConfigOpacity(75);
        hboxLinhatbTbSolicitacoesAssinaturas3.setVAlign("tvTop");
        hboxTabDadosSignatarioMain.addChildren(hboxLinhatbTbSolicitacoesAssinaturas3);
        hboxLinhatbTbSolicitacoesAssinaturas3.applyProperties();
    }

    public TFHBox hBoxlblTelefone = new TFHBox();

    private void init_hBoxlblTelefone() {
        hBoxlblTelefone.setName("hBoxlblTelefone");
        hBoxlblTelefone.setLeft(0);
        hBoxlblTelefone.setTop(0);
        hBoxlblTelefone.setWidth(67);
        hBoxlblTelefone.setHeight(27);
        hBoxlblTelefone.setBorderStyle("stNone");
        hBoxlblTelefone.setPaddingTop(10);
        hBoxlblTelefone.setPaddingLeft(0);
        hBoxlblTelefone.setPaddingRight(0);
        hBoxlblTelefone.setPaddingBottom(0);
        hBoxlblTelefone.setMarginTop(0);
        hBoxlblTelefone.setMarginLeft(0);
        hBoxlblTelefone.setMarginRight(0);
        hBoxlblTelefone.setMarginBottom(0);
        hBoxlblTelefone.setSpacing(1);
        hBoxlblTelefone.setFlexVflex("ftFalse");
        hBoxlblTelefone.setFlexHflex("ftFalse");
        hBoxlblTelefone.setScrollable(false);
        hBoxlblTelefone.setBoxShadowConfigHorizontalLength(10);
        hBoxlblTelefone.setBoxShadowConfigVerticalLength(10);
        hBoxlblTelefone.setBoxShadowConfigBlurRadius(5);
        hBoxlblTelefone.setBoxShadowConfigSpreadRadius(0);
        hBoxlblTelefone.setBoxShadowConfigShadowColor("clBlack");
        hBoxlblTelefone.setBoxShadowConfigOpacity(75);
        hBoxlblTelefone.setVAlign("tvTop");
        hboxLinhatbTbSolicitacoesAssinaturas3.addChildren(hBoxlblTelefone);
        hBoxlblTelefone.applyProperties();
    }

    public TFLabel lblTelefone = new TFLabel();

    private void init_lblTelefone() {
        lblTelefone.setName("lblTelefone");
        lblTelefone.setLeft(0);
        lblTelefone.setTop(0);
        lblTelefone.setWidth(57);
        lblTelefone.setHeight(18);
        lblTelefone.setHint("Resumo pergunta");
        lblTelefone.setAlign("alRight");
        lblTelefone.setCaption("Telefone");
        lblTelefone.setFontColor("clWindowText");
        lblTelefone.setFontSize(-15);
        lblTelefone.setFontName("Tahoma");
        lblTelefone.setFontStyle("[]");
        lblTelefone.setVerticalAlignment("taVerticalCenter");
        lblTelefone.setWordBreak(false);
        hBoxlblTelefone.addChildren(lblTelefone);
        lblTelefone.applyProperties();
    }

    public TFString edTelefone = new TFString();

    private void init_edTelefone() {
        edTelefone.setName("edTelefone");
        edTelefone.setLeft(67);
        edTelefone.setTop(0);
        edTelefone.setWidth(300);
        edTelefone.setHeight(24);
        edTelefone.setFieldName("TELEFONE");
        edTelefone.setFlex(true);
        edTelefone.setRequired(false);
        edTelefone.setPrompt("Fone signat\u00E1rio");
        edTelefone.setConstraintExpression("value is null or trim(value) = ''");
        edTelefone.setConstraintMessage("Campo Resumo pergunta, preenchimento \u00E9 obrigat\u00F3rio");
        edTelefone.setConstraintCheckWhen("cwImmediate");
        edTelefone.setConstraintCheckType("ctExpression");
        edTelefone.setConstraintFocusOnError(false);
        edTelefone.setConstraintGroupName("tbPergunta");
        edTelefone.setConstraintEnableUI(true);
        edTelefone.setConstraintEnabled(false);
        edTelefone.setConstraintFormCheck(false);
        edTelefone.setCharCase("ccNormal");
        edTelefone.setPwd(false);
        edTelefone.setMaxlength(100);
        edTelefone.setAlign("alLeft");
        edTelefone.setFontColor("clWindowText");
        edTelefone.setFontSize(-13);
        edTelefone.setFontName("Tahoma");
        edTelefone.setFontStyle("[]");
        edTelefone.setSaveLiteralCharacter(false);
        edTelefone.applyProperties();
        hboxLinhatbTbSolicitacoesAssinaturas3.addChildren(edTelefone);
        addValidatable(edTelefone);
    }

    public TFVBox hboxTabDadosSignatarioMainEspaco1 = new TFVBox();

    private void init_hboxTabDadosSignatarioMainEspaco1() {
        hboxTabDadosSignatarioMainEspaco1.setName("hboxTabDadosSignatarioMainEspaco1");
        hboxTabDadosSignatarioMainEspaco1.setLeft(0);
        hboxTabDadosSignatarioMainEspaco1.setTop(93);
        hboxTabDadosSignatarioMainEspaco1.setWidth(185);
        hboxTabDadosSignatarioMainEspaco1.setHeight(9);
        hboxTabDadosSignatarioMainEspaco1.setBorderStyle("stNone");
        hboxTabDadosSignatarioMainEspaco1.setPaddingTop(0);
        hboxTabDadosSignatarioMainEspaco1.setPaddingLeft(0);
        hboxTabDadosSignatarioMainEspaco1.setPaddingRight(0);
        hboxTabDadosSignatarioMainEspaco1.setPaddingBottom(0);
        hboxTabDadosSignatarioMainEspaco1.setMarginTop(0);
        hboxTabDadosSignatarioMainEspaco1.setMarginLeft(0);
        hboxTabDadosSignatarioMainEspaco1.setMarginRight(0);
        hboxTabDadosSignatarioMainEspaco1.setMarginBottom(0);
        hboxTabDadosSignatarioMainEspaco1.setSpacing(1);
        hboxTabDadosSignatarioMainEspaco1.setFlexVflex("ftFalse");
        hboxTabDadosSignatarioMainEspaco1.setFlexHflex("ftFalse");
        hboxTabDadosSignatarioMainEspaco1.setScrollable(false);
        hboxTabDadosSignatarioMainEspaco1.setBoxShadowConfigHorizontalLength(10);
        hboxTabDadosSignatarioMainEspaco1.setBoxShadowConfigVerticalLength(10);
        hboxTabDadosSignatarioMainEspaco1.setBoxShadowConfigBlurRadius(5);
        hboxTabDadosSignatarioMainEspaco1.setBoxShadowConfigSpreadRadius(0);
        hboxTabDadosSignatarioMainEspaco1.setBoxShadowConfigShadowColor("clBlack");
        hboxTabDadosSignatarioMainEspaco1.setBoxShadowConfigOpacity(75);
        hboxTabDadosSignatarioMain.addChildren(hboxTabDadosSignatarioMainEspaco1);
        hboxTabDadosSignatarioMainEspaco1.applyProperties();
    }

    public TFHBox vBoxGridSolicitacaoAssinaturaEspacoRodape = new TFHBox();

    private void init_vBoxGridSolicitacaoAssinaturaEspacoRodape() {
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setName("vBoxGridSolicitacaoAssinaturaEspacoRodape");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setLeft(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setTop(169);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setWidth(185);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setHeight(13);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBorderStyle("stNone");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingTop(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingLeft(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingRight(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingBottom(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginTop(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginLeft(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginRight(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginBottom(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setSpacing(1);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setFlexVflex("ftFalse");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setFlexHflex("ftFalse");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setScrollable(false);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigHorizontalLength(10);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigVerticalLength(10);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigBlurRadius(5);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigSpreadRadius(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigShadowColor("clBlack");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigOpacity(75);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setVAlign("tvTop");
        hboxTabDadosAssinaturaMain.addChildren(vBoxGridSolicitacaoAssinaturaEspacoRodape);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}