package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmBloquearDesbloquearItemCompra extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.BloquearDesbloquearItemCompraRNA rn = null;

    public FrmBloquearDesbloquearItemCompra() {
        try {
            rn = (freedom.bytecode.rn.BloquearDesbloquearItemCompraRNA) getRN(freedom.bytecode.rn.wizard.BloquearDesbloquearItemCompraRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbItensCustosCompraBloq();
        init_popGrdEmpresas();
        init_mmSelecionarTodosOsRegistrosGrdEmpresas();
        init_mmSelecionarNenhumRegistroGrdEmpresas();
        init_vBoxPrincipal();
        init_hBoxBotoes();
        init_btnVoltar();
        init_btnSalvar();
        init_lblItem();
        init_lblFornecedor();
        init_lblObservacao();
        init_memoObservacao();
        init_lblItensCustos();
        init_grdEmpresas();
        init_FrmBloquearDesbloquearItemCompra();
    }

    public BUSCA_ITENS_CUSTOS_COMPRA_BLOQ tbItensCustosCompraBloq;

    private void init_tbItensCustosCompraBloq() {
        tbItensCustosCompraBloq = rn.tbItensCustosCompraBloq;
        tbItensCustosCompraBloq.setName("tbItensCustosCompraBloq");
        tbItensCustosCompraBloq.setMaxRowCount(200);
        tbItensCustosCompraBloq.setWKey("51201450;51202");
        tbItensCustosCompraBloq.setRatioBatchSize(20);
        getTables().put(tbItensCustosCompraBloq, "tbItensCustosCompraBloq");
        tbItensCustosCompraBloq.applyProperties();
    }

    public TFPopupMenu popGrdEmpresas = new TFPopupMenu();

    private void init_popGrdEmpresas() {
        popGrdEmpresas.setName("popGrdEmpresas");
        FrmBloquearDesbloquearItemCompra.addChildren(popGrdEmpresas);
        popGrdEmpresas.applyProperties();
    }

    public TFMenuItem mmSelecionarTodosOsRegistrosGrdEmpresas = new TFMenuItem();

    private void init_mmSelecionarTodosOsRegistrosGrdEmpresas() {
        mmSelecionarTodosOsRegistrosGrdEmpresas.setName("mmSelecionarTodosOsRegistrosGrdEmpresas");
        mmSelecionarTodosOsRegistrosGrdEmpresas.setCaption("Selecionar todos os registros");
        mmSelecionarTodosOsRegistrosGrdEmpresas.setHint("Selecionar todos os registros");
        mmSelecionarTodosOsRegistrosGrdEmpresas.setImageIndex(310010);
        mmSelecionarTodosOsRegistrosGrdEmpresas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarTodosOsRegistrosGrdEmpresasClick(event);
            processarFlow("FrmBloquearDesbloquearItemCompra", "mmSelecionarTodosOsRegistrosGrdEmpresas", "OnClick");
        });
        mmSelecionarTodosOsRegistrosGrdEmpresas.setAccess(false);
        mmSelecionarTodosOsRegistrosGrdEmpresas.setCheckmark(false);
        mmSelecionarTodosOsRegistrosGrdEmpresas.setIconClass("hashtag");
        popGrdEmpresas.addChildren(mmSelecionarTodosOsRegistrosGrdEmpresas);
        mmSelecionarTodosOsRegistrosGrdEmpresas.applyProperties();
    }

    public TFMenuItem mmSelecionarNenhumRegistroGrdEmpresas = new TFMenuItem();

    private void init_mmSelecionarNenhumRegistroGrdEmpresas() {
        mmSelecionarNenhumRegistroGrdEmpresas.setName("mmSelecionarNenhumRegistroGrdEmpresas");
        mmSelecionarNenhumRegistroGrdEmpresas.setCaption("Selecionar nenhum registro");
        mmSelecionarNenhumRegistroGrdEmpresas.setHint("Selecionar nenhum registro");
        mmSelecionarNenhumRegistroGrdEmpresas.setImageIndex(310011);
        mmSelecionarNenhumRegistroGrdEmpresas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarNenhumRegistroGrdEmpresasClick(event);
            processarFlow("FrmBloquearDesbloquearItemCompra", "mmSelecionarNenhumRegistroGrdEmpresas", "OnClick");
        });
        mmSelecionarNenhumRegistroGrdEmpresas.setAccess(false);
        mmSelecionarNenhumRegistroGrdEmpresas.setCheckmark(false);
        mmSelecionarNenhumRegistroGrdEmpresas.setIconClass("hashtag");
        popGrdEmpresas.addChildren(mmSelecionarNenhumRegistroGrdEmpresas);
        mmSelecionarNenhumRegistroGrdEmpresas.applyProperties();
    }

    protected TFForm FrmBloquearDesbloquearItemCompra = this;
    private void init_FrmBloquearDesbloquearItemCompra() {
        FrmBloquearDesbloquearItemCompra.setName("FrmBloquearDesbloquearItemCompra");
        FrmBloquearDesbloquearItemCompra.setCaption("Bloquar / Desbloquear item para compra");
        FrmBloquearDesbloquearItemCompra.setClientHeight(461);
        FrmBloquearDesbloquearItemCompra.setClientWidth(584);
        FrmBloquearDesbloquearItemCompra.setColor("clBtnFace");
        FrmBloquearDesbloquearItemCompra.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmBloquearDesbloquearItemCompra", "FrmBloquearDesbloquearItemCompra", "OnCreate");
        });
        FrmBloquearDesbloquearItemCompra.setWOrigem("EhMain");
        FrmBloquearDesbloquearItemCompra.setWKey("51201450");
        FrmBloquearDesbloquearItemCompra.setSpacing(0);
        FrmBloquearDesbloquearItemCompra.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(584);
        vBoxPrincipal.setHeight(461);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(5);
        vBoxPrincipal.setMarginLeft(5);
        vBoxPrincipal.setMarginRight(5);
        vBoxPrincipal.setMarginBottom(5);
        vBoxPrincipal.setSpacing(5);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmBloquearDesbloquearItemCompra.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(570);
        hBoxBotoes.setHeight(60);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(5);
        hBoxBotoes.setFlexVflex("ftMin");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(55);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmBloquearDesbloquearItemCompra", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(60);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(60);
        btnSalvar.setHeight(55);
        btnSalvar.setHint("Salvar");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmBloquearDesbloquearItemCompra", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(700080);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFLabel lblItem = new TFLabel();

    private void init_lblItem() {
        lblItem.setName("lblItem");
        lblItem.setLeft(0);
        lblItem.setTop(61);
        lblItem.setWidth(119);
        lblItem.setHeight(13);
        lblItem.setCaption("Item: Descri\u00E7\u00E3o [C\u00F3digo]");
        lblItem.setFontColor("clWindowText");
        lblItem.setFontSize(-11);
        lblItem.setFontName("Tahoma");
        lblItem.setFontStyle("[]");
        lblItem.setVerticalAlignment("taVerticalCenter");
        lblItem.setWordBreak(false);
        vBoxPrincipal.addChildren(lblItem);
        lblItem.applyProperties();
    }

    public TFLabel lblFornecedor = new TFLabel();

    private void init_lblFornecedor() {
        lblFornecedor.setName("lblFornecedor");
        lblFornecedor.setLeft(0);
        lblFornecedor.setTop(75);
        lblFornecedor.setWidth(152);
        lblFornecedor.setHeight(13);
        lblFornecedor.setCaption("Fornecedor: Descri\u00E7\u00E3o [C\u00F3digo]");
        lblFornecedor.setFontColor("clWindowText");
        lblFornecedor.setFontSize(-11);
        lblFornecedor.setFontName("Tahoma");
        lblFornecedor.setFontStyle("[]");
        lblFornecedor.setVerticalAlignment("taVerticalCenter");
        lblFornecedor.setWordBreak(false);
        vBoxPrincipal.addChildren(lblFornecedor);
        lblFornecedor.applyProperties();
    }

    public TFLabel lblObservacao = new TFLabel();

    private void init_lblObservacao() {
        lblObservacao.setName("lblObservacao");
        lblObservacao.setLeft(0);
        lblObservacao.setTop(89);
        lblObservacao.setWidth(58);
        lblObservacao.setHeight(13);
        lblObservacao.setCaption("Observa\u00E7\u00E3o");
        lblObservacao.setFontColor("clWindowText");
        lblObservacao.setFontSize(-11);
        lblObservacao.setFontName("Tahoma");
        lblObservacao.setFontStyle("[]");
        lblObservacao.setVerticalAlignment("taVerticalCenter");
        lblObservacao.setWordBreak(false);
        vBoxPrincipal.addChildren(lblObservacao);
        lblObservacao.applyProperties();
    }

    public TFMemo memoObservacao = new TFMemo();

    private void init_memoObservacao() {
        memoObservacao.setName("memoObservacao");
        memoObservacao.setLeft(0);
        memoObservacao.setTop(103);
        memoObservacao.setWidth(200);
        memoObservacao.setHeight(100);
        memoObservacao.setHint("Observa\u00E7\u00E3o");
        memoObservacao.setCharCase("ccNormal");
        memoObservacao.setFontColor("clWindowText");
        memoObservacao.setFontSize(-11);
        memoObservacao.setFontName("Tahoma");
        memoObservacao.setFontStyle("[]");
        memoObservacao.setMaxlength(0);
        memoObservacao.setFlexVflex("ftTrue");
        memoObservacao.setFlexHflex("ftTrue");
        memoObservacao.setHelpCaption("Observa\u00E7\u00E3o");
        memoObservacao.setConstraintCheckWhen("cwImmediate");
        memoObservacao.setConstraintCheckType("ctExpression");
        memoObservacao.setConstraintFocusOnError(false);
        memoObservacao.setConstraintEnableUI(true);
        memoObservacao.setConstraintEnabled(false);
        memoObservacao.setConstraintFormCheck(true);
        memoObservacao.setRequired(true);
        vBoxPrincipal.addChildren(memoObservacao);
        memoObservacao.applyProperties();
        addValidatable(memoObservacao);
    }

    public TFLabel lblItensCustos = new TFLabel();

    private void init_lblItensCustos() {
        lblItensCustos.setName("lblItensCustos");
        lblItensCustos.setLeft(0);
        lblItensCustos.setTop(204);
        lblItensCustos.setWidth(75);
        lblItensCustos.setHeight(13);
        lblItensCustos.setCaption("ITENS_CUSTOS");
        lblItensCustos.setFontColor("clWindowText");
        lblItensCustos.setFontSize(-11);
        lblItensCustos.setFontName("Tahoma");
        lblItensCustos.setFontStyle("[]");
        lblItensCustos.setVerticalAlignment("taVerticalCenter");
        lblItensCustos.setWordBreak(false);
        vBoxPrincipal.addChildren(lblItensCustos);
        lblItensCustos.applyProperties();
    }

    public TFGrid grdEmpresas = new TFGrid();

    private void init_grdEmpresas() {
        grdEmpresas.setName("grdEmpresas");
        grdEmpresas.setLeft(0);
        grdEmpresas.setTop(218);
        grdEmpresas.setWidth(320);
        grdEmpresas.setHeight(120);
        grdEmpresas.setTable(tbItensCustosCompraBloq);
        grdEmpresas.setFlexVflex("ftTrue");
        grdEmpresas.setFlexHflex("ftTrue");
        grdEmpresas.setPagingEnabled(false);
        grdEmpresas.setFrozenColumns(0);
        grdEmpresas.setShowFooter(false);
        grdEmpresas.setShowHeader(true);
        grdEmpresas.setMultiSelection(false);
        grdEmpresas.setGroupingEnabled(false);
        grdEmpresas.setGroupingExpanded(false);
        grdEmpresas.setGroupingShowFooter(false);
        grdEmpresas.setCrosstabEnabled(false);
        grdEmpresas.setCrosstabGroupType("cgtConcat");
        grdEmpresas.setEditionEnabled(false);
        grdEmpresas.setContextMenu(popGrdEmpresas);
        grdEmpresas.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("SEL");
        item0.setTitleCaption("#");
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL = 'S'");
        item1.setHint("Registro selecionado");
        item1.setEvalType("etExpression");
        item1.setImageId(310010);
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL = 'N'");
        item2.setHint("Registro n\u00E3o selecionado");
        item2.setEvalType("etExpression");
        item2.setImageId(310011);
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdEmpresas.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("EMPRESA_NOME_CODIGO");
        item3.setTitleCaption("Empresa");
        item3.setWidth(200);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdEmpresas.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("COMPRAS_BLOQUEADAS_DESCRICAO");
        item4.setTitleCaption("Status");
        item4.setWidth(200);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdEmpresas.getColumns().add(item4);
        vBoxPrincipal.addChildren(grdEmpresas);
        grdEmpresas.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void mmSelecionarTodosOsRegistrosGrdEmpresasClick(final Event<Object> event);

    public abstract void mmSelecionarNenhumRegistroGrdEmpresasClick(final Event<Object> event);

}