package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmClienteEnderecoPorInscricao extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ClienteEnderecoPorInscricaoRNA rn = null;

    public FrmClienteEnderecoPorInscricao() {
        try {
            rn = (freedom.bytecode.rn.ClienteEnderecoPorInscricaoRNA) getRN(freedom.bytecode.rn.wizard.ClienteEnderecoPorInscricaoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClientes();
        init_tbClienteDiverso();
        init_tbClienteEnderecoInscricao();
        init_tbDadosFisicos();
        init_tbConsultaNbsPessoaFisica();
        init_tbConsultaNbsPessoaJuridica();
        init_tbConsultaNbsSintegraDados();
        init_tbConsultaNbsSintegraSimples();
        init_tbCidadesInscricao();
        init_tbSchemaAtual();
        init_vBoxPrincipal();
        init_hBoxPrincipalSeparador01();
        init_hBoxBotoes();
        init_vBoxBotoesSeparador07();
        init_btnVoltar();
        init_vBoxBotoesSeparador06();
        init_btnReload();
        init_vBoxBotoesSeparador05();
        init_btnIncluir();
        init_vBoxBotoesSeparador04();
        init_btnExcluir();
        init_vBoxBotoesSeparador03();
        init_btnAlterar();
        init_vBoxBotoesSeparador02();
        init_btnExibirDadosConsultaAPIIntegracao();
        init_hBoxBotoesSeparador01();
        init_vBoxStatusCadastro();
        init_hBoxSituacaoCadastral();
        init_lblSituacaoCadastral();
        init_********************************();
        init_lblCadastroIrregular();
        init_hBoxSituacaoCadReceitaFederal();
        init_lblSituacaoCadReceitaFederal();
        init_lblSituacaoReceitaFederal();
        init_lblSituacaoCadReceitaFederalFim();
        init_hBoxSituacaoCadSintegra();
        init_lblSituacaoCadSintegra();
        init_lblSituacaoSintegra();
        init_lblSituacaoCadSintegraFim();
        init_lblSintegraMultiIe();
        init_vBoxBotoesSeparador01();
        init_hBoxNomeCliente();
        init_vBoxNomeClienteSeparador03();
        init_vBoxNomeCliente();
        init_lblNomeCliente();
        init_edtNomeCliente();
        init_vBoxNomeClienteSeparador02();
        init_vBoxClienteProdutorRural();
        init_lblProdutorRural();
        init_cboClienteEhProdutorRural();
        init_vBoxNomeClienteSeparador01();
        init_hBoxNomePropriedade();
        init_vBoxNomePropriedadeSeparador01();
        init_vBoxPropriedade();
        init_lblNomePropriedade();
        init_edtNomePropriedade();
        init_vBoxNomePropriedadeSeparador02();
        init_hBoxPrincipalSeparador02();
        init_hBoxGrade();
        init_vBoxGradeSeparador01();
        init_gridClienteEnderecoPorInscricao();
        init_vBoxGradeSeparador02();
        init_hBoxPrincipalSeparador03();
        init_FrmClienteEnderecoPorInscricao();
    }

    public CLIENTES tbClientes;

    private void init_tbClientes() {
        tbClientes = rn.tbClientes;
        tbClientes.setName("tbClientes");
        tbClientes.setMaxRowCount(200);
        tbClientes.setWKey("5300786;53001");
        tbClientes.setRatioBatchSize(20);
        getTables().put(tbClientes, "tbClientes");
        tbClientes.applyProperties();
    }

    public CLIENTE_DIVERSO tbClienteDiverso;

    private void init_tbClienteDiverso() {
        tbClienteDiverso = rn.tbClienteDiverso;
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("5300786;53002");
        tbClienteDiverso.setRatioBatchSize(20);
        getTables().put(tbClienteDiverso, "tbClienteDiverso");
        tbClienteDiverso.applyProperties();
    }

    public CLIENTE_ENDERECO_INSCRICAO tbClienteEnderecoInscricao;

    private void init_tbClienteEnderecoInscricao() {
        tbClienteEnderecoInscricao = rn.tbClienteEnderecoInscricao;
        tbClienteEnderecoInscricao.setName("tbClienteEnderecoInscricao");
        tbClienteEnderecoInscricao.setMaxRowCount(200);
        tbClienteEnderecoInscricao.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbClienteEnderecoInscricaoAfterScroll(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "tbClienteEnderecoInscricao", "OnAfterScroll");
        });
        tbClienteEnderecoInscricao.setWKey("5300786;53003");
        tbClienteEnderecoInscricao.setRatioBatchSize(20);
        getTables().put(tbClienteEnderecoInscricao, "tbClienteEnderecoInscricao");
        tbClienteEnderecoInscricao.applyProperties();
    }

    public DADOS_FISICOS tbDadosFisicos;

    private void init_tbDadosFisicos() {
        tbDadosFisicos = rn.tbDadosFisicos;
        tbDadosFisicos.setName("tbDadosFisicos");
        tbDadosFisicos.setMaxRowCount(200);
        tbDadosFisicos.setWKey("5300786;10401");
        tbDadosFisicos.setRatioBatchSize(20);
        getTables().put(tbDadosFisicos, "tbDadosFisicos");
        tbDadosFisicos.applyProperties();
    }

    public CONSULTA_NBS_PESSOA_FISICA tbConsultaNbsPessoaFisica;

    private void init_tbConsultaNbsPessoaFisica() {
        tbConsultaNbsPessoaFisica = rn.tbConsultaNbsPessoaFisica;
        tbConsultaNbsPessoaFisica.setName("tbConsultaNbsPessoaFisica");
        tbConsultaNbsPessoaFisica.setMaxRowCount(200);
        tbConsultaNbsPessoaFisica.setWKey("5300786;10402");
        tbConsultaNbsPessoaFisica.setRatioBatchSize(20);
        getTables().put(tbConsultaNbsPessoaFisica, "tbConsultaNbsPessoaFisica");
        tbConsultaNbsPessoaFisica.applyProperties();
    }

    public CONSULTA_NBS_PESSOA_JURIDICA tbConsultaNbsPessoaJuridica;

    private void init_tbConsultaNbsPessoaJuridica() {
        tbConsultaNbsPessoaJuridica = rn.tbConsultaNbsPessoaJuridica;
        tbConsultaNbsPessoaJuridica.setName("tbConsultaNbsPessoaJuridica");
        tbConsultaNbsPessoaJuridica.setMaxRowCount(200);
        tbConsultaNbsPessoaJuridica.setWKey("5300786;10403");
        tbConsultaNbsPessoaJuridica.setRatioBatchSize(20);
        getTables().put(tbConsultaNbsPessoaJuridica, "tbConsultaNbsPessoaJuridica");
        tbConsultaNbsPessoaJuridica.applyProperties();
    }

    public CONSULTA_NBS_SINTEGRA_DADOS tbConsultaNbsSintegraDados;

    private void init_tbConsultaNbsSintegraDados() {
        tbConsultaNbsSintegraDados = rn.tbConsultaNbsSintegraDados;
        tbConsultaNbsSintegraDados.setName("tbConsultaNbsSintegraDados");
        tbConsultaNbsSintegraDados.setMaxRowCount(200);
        tbConsultaNbsSintegraDados.setWKey("5300786;10404");
        tbConsultaNbsSintegraDados.setRatioBatchSize(20);
        getTables().put(tbConsultaNbsSintegraDados, "tbConsultaNbsSintegraDados");
        tbConsultaNbsSintegraDados.applyProperties();
    }

    public CONSULTA_NBS_SINTEGRA_SIMPLES tbConsultaNbsSintegraSimples;

    private void init_tbConsultaNbsSintegraSimples() {
        tbConsultaNbsSintegraSimples = rn.tbConsultaNbsSintegraSimples;
        tbConsultaNbsSintegraSimples.setName("tbConsultaNbsSintegraSimples");
        tbConsultaNbsSintegraSimples.setMaxRowCount(200);
        tbConsultaNbsSintegraSimples.setWKey("5300786;10405");
        tbConsultaNbsSintegraSimples.setRatioBatchSize(20);
        getTables().put(tbConsultaNbsSintegraSimples, "tbConsultaNbsSintegraSimples");
        tbConsultaNbsSintegraSimples.applyProperties();
    }

    public CIDADES tbCidadesInscricao;

    private void init_tbCidadesInscricao() {
        tbCidadesInscricao = rn.tbCidadesInscricao;
        tbCidadesInscricao.setName("tbCidadesInscricao");
        tbCidadesInscricao.setMaxRowCount(200);
        tbCidadesInscricao.setWKey("5300786;10406");
        tbCidadesInscricao.setRatioBatchSize(20);
        getTables().put(tbCidadesInscricao, "tbCidadesInscricao");
        tbCidadesInscricao.applyProperties();
    }

    public SCHEMA_ATUAL tbSchemaAtual;

    private void init_tbSchemaAtual() {
        tbSchemaAtual = rn.tbSchemaAtual;
        tbSchemaAtual.setName("tbSchemaAtual");
        tbSchemaAtual.setMaxRowCount(200);
        tbSchemaAtual.setWKey("5300786;10407");
        tbSchemaAtual.setRatioBatchSize(20);
        getTables().put(tbSchemaAtual, "tbSchemaAtual");
        tbSchemaAtual.applyProperties();
    }

    protected TFForm FrmClienteEnderecoPorInscricao = this;
    private void init_FrmClienteEnderecoPorInscricao() {
        FrmClienteEnderecoPorInscricao.setName("FrmClienteEnderecoPorInscricao");
        FrmClienteEnderecoPorInscricao.setCaption("Cadastro de Clientes - Endere\u00E7os por Incri\u00E7\u00E3o");
        FrmClienteEnderecoPorInscricao.setClientHeight(462);
        FrmClienteEnderecoPorInscricao.setClientWidth(534);
        FrmClienteEnderecoPorInscricao.setColor("clBtnFace");
        FrmClienteEnderecoPorInscricao.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "FrmClienteEnderecoPorInscricao", "OnCreate");
        });
        FrmClienteEnderecoPorInscricao.setWOrigem("EhMain");
        FrmClienteEnderecoPorInscricao.setWKey("5300786");
        FrmClienteEnderecoPorInscricao.setSpacing(0);
        FrmClienteEnderecoPorInscricao.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(534);
        vBoxPrincipal.setHeight(462);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmClienteEnderecoPorInscricao.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador01 = new TFHBox();

    private void init_hBoxPrincipalSeparador01() {
        hBoxPrincipalSeparador01.setName("hBoxPrincipalSeparador01");
        hBoxPrincipalSeparador01.setLeft(0);
        hBoxPrincipalSeparador01.setTop(0);
        hBoxPrincipalSeparador01.setWidth(670);
        hBoxPrincipalSeparador01.setHeight(5);
        hBoxPrincipalSeparador01.setBorderStyle("stNone");
        hBoxPrincipalSeparador01.setPaddingTop(0);
        hBoxPrincipalSeparador01.setPaddingLeft(0);
        hBoxPrincipalSeparador01.setPaddingRight(0);
        hBoxPrincipalSeparador01.setPaddingBottom(0);
        hBoxPrincipalSeparador01.setMarginTop(0);
        hBoxPrincipalSeparador01.setMarginLeft(0);
        hBoxPrincipalSeparador01.setMarginRight(0);
        hBoxPrincipalSeparador01.setMarginBottom(0);
        hBoxPrincipalSeparador01.setSpacing(1);
        hBoxPrincipalSeparador01.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador01.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador01.setScrollable(false);
        hBoxPrincipalSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador01.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador01.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador01);
        hBoxPrincipalSeparador01.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(6);
        hBoxBotoes.setWidth(520);
        hBoxBotoes.setHeight(68);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(1);
        hBoxBotoes.setFlexVflex("ftMin");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFVBox vBoxBotoesSeparador07 = new TFVBox();

    private void init_vBoxBotoesSeparador07() {
        vBoxBotoesSeparador07.setName("vBoxBotoesSeparador07");
        vBoxBotoesSeparador07.setLeft(0);
        vBoxBotoesSeparador07.setTop(0);
        vBoxBotoesSeparador07.setWidth(5);
        vBoxBotoesSeparador07.setHeight(55);
        vBoxBotoesSeparador07.setBorderStyle("stNone");
        vBoxBotoesSeparador07.setPaddingTop(0);
        vBoxBotoesSeparador07.setPaddingLeft(0);
        vBoxBotoesSeparador07.setPaddingRight(0);
        vBoxBotoesSeparador07.setPaddingBottom(0);
        vBoxBotoesSeparador07.setMarginTop(0);
        vBoxBotoesSeparador07.setMarginLeft(0);
        vBoxBotoesSeparador07.setMarginRight(0);
        vBoxBotoesSeparador07.setMarginBottom(0);
        vBoxBotoesSeparador07.setSpacing(1);
        vBoxBotoesSeparador07.setFlexVflex("ftFalse");
        vBoxBotoesSeparador07.setFlexHflex("ftFalse");
        vBoxBotoesSeparador07.setScrollable(false);
        vBoxBotoesSeparador07.setBoxShadowConfigHorizontalLength(10);
        vBoxBotoesSeparador07.setBoxShadowConfigVerticalLength(10);
        vBoxBotoesSeparador07.setBoxShadowConfigBlurRadius(5);
        vBoxBotoesSeparador07.setBoxShadowConfigSpreadRadius(0);
        vBoxBotoesSeparador07.setBoxShadowConfigShadowColor("clBlack");
        vBoxBotoesSeparador07.setBoxShadowConfigOpacity(75);
        hBoxBotoes.addChildren(vBoxBotoesSeparador07);
        vBoxBotoesSeparador07.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(5);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(55);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFVBox vBoxBotoesSeparador06 = new TFVBox();

    private void init_vBoxBotoesSeparador06() {
        vBoxBotoesSeparador06.setName("vBoxBotoesSeparador06");
        vBoxBotoesSeparador06.setLeft(65);
        vBoxBotoesSeparador06.setTop(0);
        vBoxBotoesSeparador06.setWidth(5);
        vBoxBotoesSeparador06.setHeight(55);
        vBoxBotoesSeparador06.setBorderStyle("stNone");
        vBoxBotoesSeparador06.setPaddingTop(0);
        vBoxBotoesSeparador06.setPaddingLeft(0);
        vBoxBotoesSeparador06.setPaddingRight(0);
        vBoxBotoesSeparador06.setPaddingBottom(0);
        vBoxBotoesSeparador06.setMarginTop(0);
        vBoxBotoesSeparador06.setMarginLeft(0);
        vBoxBotoesSeparador06.setMarginRight(0);
        vBoxBotoesSeparador06.setMarginBottom(0);
        vBoxBotoesSeparador06.setSpacing(1);
        vBoxBotoesSeparador06.setFlexVflex("ftFalse");
        vBoxBotoesSeparador06.setFlexHflex("ftFalse");
        vBoxBotoesSeparador06.setScrollable(false);
        vBoxBotoesSeparador06.setBoxShadowConfigHorizontalLength(10);
        vBoxBotoesSeparador06.setBoxShadowConfigVerticalLength(10);
        vBoxBotoesSeparador06.setBoxShadowConfigBlurRadius(5);
        vBoxBotoesSeparador06.setBoxShadowConfigSpreadRadius(0);
        vBoxBotoesSeparador06.setBoxShadowConfigShadowColor("clBlack");
        vBoxBotoesSeparador06.setBoxShadowConfigOpacity(75);
        hBoxBotoes.addChildren(vBoxBotoesSeparador06);
        vBoxBotoesSeparador06.applyProperties();
    }

    public TFButton btnReload = new TFButton();

    private void init_btnReload() {
        btnReload.setName("btnReload");
        btnReload.setLeft(70);
        btnReload.setTop(0);
        btnReload.setWidth(60);
        btnReload.setHeight(55);
        btnReload.setHint("Atualizar consulta");
        btnReload.setAlign("alLeft");
        btnReload.setCaption("Atualizar");
        btnReload.setFontColor("clWindowText");
        btnReload.setFontSize(-11);
        btnReload.setFontName("Tahoma");
        btnReload.setFontStyle("[]");
        btnReload.setLayout("blGlyphTop");
        btnReload.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnReloadClick(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "btnReload", "OnClick");
        });
        btnReload.setImageId(0);
        btnReload.setColor("clBtnFace");
        btnReload.setAccess(false);
        btnReload.setIconClass("refresh");
        btnReload.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnReload);
        btnReload.applyProperties();
    }

    public TFVBox vBoxBotoesSeparador05 = new TFVBox();

    private void init_vBoxBotoesSeparador05() {
        vBoxBotoesSeparador05.setName("vBoxBotoesSeparador05");
        vBoxBotoesSeparador05.setLeft(130);
        vBoxBotoesSeparador05.setTop(0);
        vBoxBotoesSeparador05.setWidth(5);
        vBoxBotoesSeparador05.setHeight(55);
        vBoxBotoesSeparador05.setBorderStyle("stNone");
        vBoxBotoesSeparador05.setPaddingTop(0);
        vBoxBotoesSeparador05.setPaddingLeft(0);
        vBoxBotoesSeparador05.setPaddingRight(0);
        vBoxBotoesSeparador05.setPaddingBottom(0);
        vBoxBotoesSeparador05.setMarginTop(0);
        vBoxBotoesSeparador05.setMarginLeft(0);
        vBoxBotoesSeparador05.setMarginRight(0);
        vBoxBotoesSeparador05.setMarginBottom(0);
        vBoxBotoesSeparador05.setSpacing(1);
        vBoxBotoesSeparador05.setFlexVflex("ftFalse");
        vBoxBotoesSeparador05.setFlexHflex("ftFalse");
        vBoxBotoesSeparador05.setScrollable(false);
        vBoxBotoesSeparador05.setBoxShadowConfigHorizontalLength(10);
        vBoxBotoesSeparador05.setBoxShadowConfigVerticalLength(10);
        vBoxBotoesSeparador05.setBoxShadowConfigBlurRadius(5);
        vBoxBotoesSeparador05.setBoxShadowConfigSpreadRadius(0);
        vBoxBotoesSeparador05.setBoxShadowConfigShadowColor("clBlack");
        vBoxBotoesSeparador05.setBoxShadowConfigOpacity(75);
        hBoxBotoes.addChildren(vBoxBotoesSeparador05);
        vBoxBotoesSeparador05.applyProperties();
    }

    public TFButton btnIncluir = new TFButton();

    private void init_btnIncluir() {
        btnIncluir.setName("btnIncluir");
        btnIncluir.setLeft(135);
        btnIncluir.setTop(0);
        btnIncluir.setWidth(60);
        btnIncluir.setHeight(55);
        btnIncluir.setHint("Incluir");
        btnIncluir.setAlign("alLeft");
        btnIncluir.setCaption("Incluir");
        btnIncluir.setFontColor("clWindowText");
        btnIncluir.setFontSize(-13);
        btnIncluir.setFontName("Tahoma");
        btnIncluir.setFontStyle("[]");
        btnIncluir.setLayout("blGlyphTop");
        btnIncluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIncluirClick(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "btnIncluir", "OnClick");
        });
        btnIncluir.setImageId(7000128);
        btnIncluir.setColor("clBtnFace");
        btnIncluir.setAccess(false);
        btnIncluir.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnIncluir);
        btnIncluir.applyProperties();
    }

    public TFVBox vBoxBotoesSeparador04 = new TFVBox();

    private void init_vBoxBotoesSeparador04() {
        vBoxBotoesSeparador04.setName("vBoxBotoesSeparador04");
        vBoxBotoesSeparador04.setLeft(195);
        vBoxBotoesSeparador04.setTop(0);
        vBoxBotoesSeparador04.setWidth(5);
        vBoxBotoesSeparador04.setHeight(55);
        vBoxBotoesSeparador04.setBorderStyle("stNone");
        vBoxBotoesSeparador04.setPaddingTop(0);
        vBoxBotoesSeparador04.setPaddingLeft(0);
        vBoxBotoesSeparador04.setPaddingRight(0);
        vBoxBotoesSeparador04.setPaddingBottom(0);
        vBoxBotoesSeparador04.setMarginTop(0);
        vBoxBotoesSeparador04.setMarginLeft(0);
        vBoxBotoesSeparador04.setMarginRight(0);
        vBoxBotoesSeparador04.setMarginBottom(0);
        vBoxBotoesSeparador04.setSpacing(1);
        vBoxBotoesSeparador04.setFlexVflex("ftFalse");
        vBoxBotoesSeparador04.setFlexHflex("ftFalse");
        vBoxBotoesSeparador04.setScrollable(false);
        vBoxBotoesSeparador04.setBoxShadowConfigHorizontalLength(10);
        vBoxBotoesSeparador04.setBoxShadowConfigVerticalLength(10);
        vBoxBotoesSeparador04.setBoxShadowConfigBlurRadius(5);
        vBoxBotoesSeparador04.setBoxShadowConfigSpreadRadius(0);
        vBoxBotoesSeparador04.setBoxShadowConfigShadowColor("clBlack");
        vBoxBotoesSeparador04.setBoxShadowConfigOpacity(75);
        hBoxBotoes.addChildren(vBoxBotoesSeparador04);
        vBoxBotoesSeparador04.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(200);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(60);
        btnExcluir.setHeight(55);
        btnExcluir.setHint("Excluir");
        btnExcluir.setAlign("alLeft");
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-13);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(310030);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(false);
        btnExcluir.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFVBox vBoxBotoesSeparador03 = new TFVBox();

    private void init_vBoxBotoesSeparador03() {
        vBoxBotoesSeparador03.setName("vBoxBotoesSeparador03");
        vBoxBotoesSeparador03.setLeft(260);
        vBoxBotoesSeparador03.setTop(0);
        vBoxBotoesSeparador03.setWidth(5);
        vBoxBotoesSeparador03.setHeight(55);
        vBoxBotoesSeparador03.setBorderStyle("stNone");
        vBoxBotoesSeparador03.setPaddingTop(0);
        vBoxBotoesSeparador03.setPaddingLeft(0);
        vBoxBotoesSeparador03.setPaddingRight(0);
        vBoxBotoesSeparador03.setPaddingBottom(0);
        vBoxBotoesSeparador03.setMarginTop(0);
        vBoxBotoesSeparador03.setMarginLeft(0);
        vBoxBotoesSeparador03.setMarginRight(0);
        vBoxBotoesSeparador03.setMarginBottom(0);
        vBoxBotoesSeparador03.setSpacing(1);
        vBoxBotoesSeparador03.setFlexVflex("ftFalse");
        vBoxBotoesSeparador03.setFlexHflex("ftFalse");
        vBoxBotoesSeparador03.setScrollable(false);
        vBoxBotoesSeparador03.setBoxShadowConfigHorizontalLength(10);
        vBoxBotoesSeparador03.setBoxShadowConfigVerticalLength(10);
        vBoxBotoesSeparador03.setBoxShadowConfigBlurRadius(5);
        vBoxBotoesSeparador03.setBoxShadowConfigSpreadRadius(0);
        vBoxBotoesSeparador03.setBoxShadowConfigShadowColor("clBlack");
        vBoxBotoesSeparador03.setBoxShadowConfigOpacity(75);
        hBoxBotoes.addChildren(vBoxBotoesSeparador03);
        vBoxBotoesSeparador03.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(265);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(60);
        btnAlterar.setHeight(55);
        btnAlterar.setHint("Alterar");
        btnAlterar.setAlign("alLeft");
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-13);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(0);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(false);
        btnAlterar.setIconClass("edit");
        btnAlterar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFVBox vBoxBotoesSeparador02 = new TFVBox();

    private void init_vBoxBotoesSeparador02() {
        vBoxBotoesSeparador02.setName("vBoxBotoesSeparador02");
        vBoxBotoesSeparador02.setLeft(325);
        vBoxBotoesSeparador02.setTop(0);
        vBoxBotoesSeparador02.setWidth(5);
        vBoxBotoesSeparador02.setHeight(55);
        vBoxBotoesSeparador02.setBorderStyle("stNone");
        vBoxBotoesSeparador02.setPaddingTop(0);
        vBoxBotoesSeparador02.setPaddingLeft(0);
        vBoxBotoesSeparador02.setPaddingRight(0);
        vBoxBotoesSeparador02.setPaddingBottom(0);
        vBoxBotoesSeparador02.setMarginTop(0);
        vBoxBotoesSeparador02.setMarginLeft(0);
        vBoxBotoesSeparador02.setMarginRight(0);
        vBoxBotoesSeparador02.setMarginBottom(0);
        vBoxBotoesSeparador02.setSpacing(1);
        vBoxBotoesSeparador02.setFlexVflex("ftFalse");
        vBoxBotoesSeparador02.setFlexHflex("ftFalse");
        vBoxBotoesSeparador02.setScrollable(false);
        vBoxBotoesSeparador02.setBoxShadowConfigHorizontalLength(10);
        vBoxBotoesSeparador02.setBoxShadowConfigVerticalLength(10);
        vBoxBotoesSeparador02.setBoxShadowConfigBlurRadius(5);
        vBoxBotoesSeparador02.setBoxShadowConfigSpreadRadius(0);
        vBoxBotoesSeparador02.setBoxShadowConfigShadowColor("clBlack");
        vBoxBotoesSeparador02.setBoxShadowConfigOpacity(75);
        hBoxBotoes.addChildren(vBoxBotoesSeparador02);
        vBoxBotoesSeparador02.applyProperties();
    }

    public TFButton btnExibirDadosConsultaAPIIntegracao = new TFButton();

    private void init_btnExibirDadosConsultaAPIIntegracao() {
        btnExibirDadosConsultaAPIIntegracao.setName("btnExibirDadosConsultaAPIIntegracao");
        btnExibirDadosConsultaAPIIntegracao.setLeft(330);
        btnExibirDadosConsultaAPIIntegracao.setTop(0);
        btnExibirDadosConsultaAPIIntegracao.setWidth(60);
        btnExibirDadosConsultaAPIIntegracao.setHeight(55);
        btnExibirDadosConsultaAPIIntegracao.setHint("Exibe dados cadastrais receita, sintegra");
        btnExibirDadosConsultaAPIIntegracao.setAlign("alLeft");
        btnExibirDadosConsultaAPIIntegracao.setCaption("Integra\u00E7\u00E3o");
        btnExibirDadosConsultaAPIIntegracao.setFontColor("clWindowText");
        btnExibirDadosConsultaAPIIntegracao.setFontSize(-11);
        btnExibirDadosConsultaAPIIntegracao.setFontName("Tahoma");
        btnExibirDadosConsultaAPIIntegracao.setFontStyle("[]");
        btnExibirDadosConsultaAPIIntegracao.setLayout("blGlyphTop");
        btnExibirDadosConsultaAPIIntegracao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExibirDadosConsultaAPIIntegracaoClick(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "btnExibirDadosConsultaAPIIntegracao", "OnClick");
        });
        btnExibirDadosConsultaAPIIntegracao.setImageId(7000112);
        btnExibirDadosConsultaAPIIntegracao.setColor("clBtnFace");
        btnExibirDadosConsultaAPIIntegracao.setAccess(false);
        btnExibirDadosConsultaAPIIntegracao.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnExibirDadosConsultaAPIIntegracao);
        btnExibirDadosConsultaAPIIntegracao.applyProperties();
    }

    public TFHBox hBoxBotoesSeparador01 = new TFHBox();

    private void init_hBoxBotoesSeparador01() {
        hBoxBotoesSeparador01.setName("hBoxBotoesSeparador01");
        hBoxBotoesSeparador01.setLeft(390);
        hBoxBotoesSeparador01.setTop(0);
        hBoxBotoesSeparador01.setWidth(57);
        hBoxBotoesSeparador01.setHeight(41);
        hBoxBotoesSeparador01.setBorderStyle("stNone");
        hBoxBotoesSeparador01.setPaddingTop(0);
        hBoxBotoesSeparador01.setPaddingLeft(0);
        hBoxBotoesSeparador01.setPaddingRight(0);
        hBoxBotoesSeparador01.setPaddingBottom(0);
        hBoxBotoesSeparador01.setMarginTop(0);
        hBoxBotoesSeparador01.setMarginLeft(0);
        hBoxBotoesSeparador01.setMarginRight(0);
        hBoxBotoesSeparador01.setMarginBottom(0);
        hBoxBotoesSeparador01.setSpacing(1);
        hBoxBotoesSeparador01.setFlexVflex("ftFalse");
        hBoxBotoesSeparador01.setFlexHflex("ftTrue");
        hBoxBotoesSeparador01.setScrollable(false);
        hBoxBotoesSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesSeparador01.setBoxShadowConfigOpacity(75);
        hBoxBotoesSeparador01.setVAlign("tvTop");
        hBoxBotoes.addChildren(hBoxBotoesSeparador01);
        hBoxBotoesSeparador01.applyProperties();
    }

    public TFVBox vBoxStatusCadastro = new TFVBox();

    private void init_vBoxStatusCadastro() {
        vBoxStatusCadastro.setName("vBoxStatusCadastro");
        vBoxStatusCadastro.setLeft(447);
        vBoxStatusCadastro.setTop(0);
        vBoxStatusCadastro.setWidth(207);
        vBoxStatusCadastro.setHeight(62);
        vBoxStatusCadastro.setBorderStyle("stNone");
        vBoxStatusCadastro.setPaddingTop(0);
        vBoxStatusCadastro.setPaddingLeft(0);
        vBoxStatusCadastro.setPaddingRight(0);
        vBoxStatusCadastro.setPaddingBottom(0);
        vBoxStatusCadastro.setVisible(false);
        vBoxStatusCadastro.setMarginTop(0);
        vBoxStatusCadastro.setMarginLeft(0);
        vBoxStatusCadastro.setMarginRight(0);
        vBoxStatusCadastro.setMarginBottom(0);
        vBoxStatusCadastro.setSpacing(1);
        vBoxStatusCadastro.setFlexVflex("ftFalse");
        vBoxStatusCadastro.setFlexHflex("ftFalse");
        vBoxStatusCadastro.setScrollable(false);
        vBoxStatusCadastro.setBoxShadowConfigHorizontalLength(10);
        vBoxStatusCadastro.setBoxShadowConfigVerticalLength(10);
        vBoxStatusCadastro.setBoxShadowConfigBlurRadius(5);
        vBoxStatusCadastro.setBoxShadowConfigSpreadRadius(0);
        vBoxStatusCadastro.setBoxShadowConfigShadowColor("clBlack");
        vBoxStatusCadastro.setBoxShadowConfigOpacity(75);
        hBoxBotoes.addChildren(vBoxStatusCadastro);
        vBoxStatusCadastro.applyProperties();
    }

    public TFHBox hBoxSituacaoCadastral = new TFHBox();

    private void init_hBoxSituacaoCadastral() {
        hBoxSituacaoCadastral.setName("hBoxSituacaoCadastral");
        hBoxSituacaoCadastral.setLeft(0);
        hBoxSituacaoCadastral.setTop(0);
        hBoxSituacaoCadastral.setWidth(203);
        hBoxSituacaoCadastral.setHeight(20);
        hBoxSituacaoCadastral.setBorderStyle("stNone");
        hBoxSituacaoCadastral.setPaddingTop(0);
        hBoxSituacaoCadastral.setPaddingLeft(0);
        hBoxSituacaoCadastral.setPaddingRight(0);
        hBoxSituacaoCadastral.setPaddingBottom(0);
        hBoxSituacaoCadastral.setMarginTop(0);
        hBoxSituacaoCadastral.setMarginLeft(0);
        hBoxSituacaoCadastral.setMarginRight(0);
        hBoxSituacaoCadastral.setMarginBottom(0);
        hBoxSituacaoCadastral.setSpacing(1);
        hBoxSituacaoCadastral.setFlexVflex("ftFalse");
        hBoxSituacaoCadastral.setFlexHflex("ftFalse");
        hBoxSituacaoCadastral.setScrollable(false);
        hBoxSituacaoCadastral.setBoxShadowConfigHorizontalLength(10);
        hBoxSituacaoCadastral.setBoxShadowConfigVerticalLength(10);
        hBoxSituacaoCadastral.setBoxShadowConfigBlurRadius(5);
        hBoxSituacaoCadastral.setBoxShadowConfigSpreadRadius(0);
        hBoxSituacaoCadastral.setBoxShadowConfigShadowColor("clBlack");
        hBoxSituacaoCadastral.setBoxShadowConfigOpacity(75);
        hBoxSituacaoCadastral.setVAlign("tvTop");
        vBoxStatusCadastro.addChildren(hBoxSituacaoCadastral);
        hBoxSituacaoCadastral.applyProperties();
    }

    public TFLabel lblSituacaoCadastral = new TFLabel();

    private void init_lblSituacaoCadastral() {
        lblSituacaoCadastral.setName("lblSituacaoCadastral");
        lblSituacaoCadastral.setLeft(0);
        lblSituacaoCadastral.setTop(0);
        lblSituacaoCadastral.setWidth(90);
        lblSituacaoCadastral.setHeight(13);
        lblSituacaoCadastral.setCaption("Situa\u00E7\u00E3o Cadastral");
        lblSituacaoCadastral.setFontColor("clWindowText");
        lblSituacaoCadastral.setFontSize(-11);
        lblSituacaoCadastral.setFontName("Tahoma");
        lblSituacaoCadastral.setFontStyle("[]");
        lblSituacaoCadastral.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadastral.setWordBreak(false);
        hBoxSituacaoCadastral.addChildren(lblSituacaoCadastral);
        lblSituacaoCadastral.applyProperties();
    }

    public TFHBox ******************************** = new TFHBox();

    private void init_********************************() {
        ********************************.setName("********************************");
        ********************************.setLeft(90);
        ********************************.setTop(0);
        ********************************.setWidth(5);
        ********************************.setHeight(15);
        ********************************.setBorderStyle("stNone");
        ********************************.setPaddingTop(0);
        ********************************.setPaddingLeft(0);
        ********************************.setPaddingRight(0);
        ********************************.setPaddingBottom(0);
        ********************************.setMarginTop(0);
        ********************************.setMarginLeft(0);
        ********************************.setMarginRight(0);
        ********************************.setMarginBottom(0);
        ********************************.setSpacing(1);
        ********************************.setFlexVflex("ftFalse");
        ********************************.setFlexHflex("ftFalse");
        ********************************.setScrollable(false);
        ********************************.setBoxShadowConfigHorizontalLength(10);
        ********************************.setBoxShadowConfigVerticalLength(10);
        ********************************.setBoxShadowConfigBlurRadius(5);
        ********************************.setBoxShadowConfigSpreadRadius(0);
        ********************************.setBoxShadowConfigShadowColor("clBlack");
        ********************************.setBoxShadowConfigOpacity(75);
        ********************************.setVAlign("tvTop");
        hBoxSituacaoCadastral.addChildren(********************************);
        ********************************.applyProperties();
    }

    public TFLabel lblCadastroIrregular = new TFLabel();

    private void init_lblCadastroIrregular() {
        lblCadastroIrregular.setName("lblCadastroIrregular");
        lblCadastroIrregular.setLeft(95);
        lblCadastroIrregular.setTop(0);
        lblCadastroIrregular.setWidth(71);
        lblCadastroIrregular.setHeight(14);
        lblCadastroIrregular.setCaption("IRREGULAR");
        lblCadastroIrregular.setFontColor("clRed");
        lblCadastroIrregular.setFontSize(-12);
        lblCadastroIrregular.setFontName("Tahoma");
        lblCadastroIrregular.setFontStyle("[fsBold]");
        lblCadastroIrregular.setVisible(false);
        lblCadastroIrregular.setVerticalAlignment("taVerticalCenter");
        lblCadastroIrregular.setWordBreak(false);
        hBoxSituacaoCadastral.addChildren(lblCadastroIrregular);
        lblCadastroIrregular.applyProperties();
    }

    public TFHBox hBoxSituacaoCadReceitaFederal = new TFHBox();

    private void init_hBoxSituacaoCadReceitaFederal() {
        hBoxSituacaoCadReceitaFederal.setName("hBoxSituacaoCadReceitaFederal");
        hBoxSituacaoCadReceitaFederal.setLeft(0);
        hBoxSituacaoCadReceitaFederal.setTop(21);
        hBoxSituacaoCadReceitaFederal.setWidth(203);
        hBoxSituacaoCadReceitaFederal.setHeight(18);
        hBoxSituacaoCadReceitaFederal.setBorderStyle("stNone");
        hBoxSituacaoCadReceitaFederal.setPaddingTop(-1);
        hBoxSituacaoCadReceitaFederal.setPaddingLeft(0);
        hBoxSituacaoCadReceitaFederal.setPaddingRight(0);
        hBoxSituacaoCadReceitaFederal.setPaddingBottom(0);
        hBoxSituacaoCadReceitaFederal.setVisible(false);
        hBoxSituacaoCadReceitaFederal.setMarginTop(0);
        hBoxSituacaoCadReceitaFederal.setMarginLeft(0);
        hBoxSituacaoCadReceitaFederal.setMarginRight(0);
        hBoxSituacaoCadReceitaFederal.setMarginBottom(0);
        hBoxSituacaoCadReceitaFederal.setSpacing(1);
        hBoxSituacaoCadReceitaFederal.setFlexVflex("ftFalse");
        hBoxSituacaoCadReceitaFederal.setFlexHflex("ftFalse");
        hBoxSituacaoCadReceitaFederal.setScrollable(false);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigHorizontalLength(10);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigVerticalLength(10);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigBlurRadius(5);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigSpreadRadius(0);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigShadowColor("clBlack");
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigOpacity(75);
        hBoxSituacaoCadReceitaFederal.setVAlign("tvTop");
        vBoxStatusCadastro.addChildren(hBoxSituacaoCadReceitaFederal);
        hBoxSituacaoCadReceitaFederal.applyProperties();
    }

    public TFLabel lblSituacaoCadReceitaFederal = new TFLabel();

    private void init_lblSituacaoCadReceitaFederal() {
        lblSituacaoCadReceitaFederal.setName("lblSituacaoCadReceitaFederal");
        lblSituacaoCadReceitaFederal.setLeft(0);
        lblSituacaoCadReceitaFederal.setTop(0);
        lblSituacaoCadReceitaFederal.setWidth(82);
        lblSituacaoCadReceitaFederal.setHeight(13);
        lblSituacaoCadReceitaFederal.setCaption("Receita Federal (");
        lblSituacaoCadReceitaFederal.setFontColor("clWindowText");
        lblSituacaoCadReceitaFederal.setFontSize(-11);
        lblSituacaoCadReceitaFederal.setFontName("Tahoma");
        lblSituacaoCadReceitaFederal.setFontStyle("[]");
        lblSituacaoCadReceitaFederal.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadReceitaFederal.setWordBreak(false);
        hBoxSituacaoCadReceitaFederal.addChildren(lblSituacaoCadReceitaFederal);
        lblSituacaoCadReceitaFederal.applyProperties();
    }

    public TFLabel lblSituacaoReceitaFederal = new TFLabel();

    private void init_lblSituacaoReceitaFederal() {
        lblSituacaoReceitaFederal.setName("lblSituacaoReceitaFederal");
        lblSituacaoReceitaFederal.setLeft(82);
        lblSituacaoReceitaFederal.setTop(0);
        lblSituacaoReceitaFederal.setWidth(35);
        lblSituacaoReceitaFederal.setHeight(13);
        lblSituacaoReceitaFederal.setCaption("ATIVO");
        lblSituacaoReceitaFederal.setFontColor("clWindowText");
        lblSituacaoReceitaFederal.setFontSize(-11);
        lblSituacaoReceitaFederal.setFontName("Tahoma");
        lblSituacaoReceitaFederal.setFontStyle("[fsBold]");
        lblSituacaoReceitaFederal.setVerticalAlignment("taVerticalCenter");
        lblSituacaoReceitaFederal.setWordBreak(false);
        hBoxSituacaoCadReceitaFederal.addChildren(lblSituacaoReceitaFederal);
        lblSituacaoReceitaFederal.applyProperties();
    }

    public TFLabel lblSituacaoCadReceitaFederalFim = new TFLabel();

    private void init_lblSituacaoCadReceitaFederalFim() {
        lblSituacaoCadReceitaFederalFim.setName("lblSituacaoCadReceitaFederalFim");
        lblSituacaoCadReceitaFederalFim.setLeft(117);
        lblSituacaoCadReceitaFederalFim.setTop(0);
        lblSituacaoCadReceitaFederalFim.setWidth(4);
        lblSituacaoCadReceitaFederalFim.setHeight(13);
        lblSituacaoCadReceitaFederalFim.setCaption(")");
        lblSituacaoCadReceitaFederalFim.setFontColor("clWindowText");
        lblSituacaoCadReceitaFederalFim.setFontSize(-11);
        lblSituacaoCadReceitaFederalFim.setFontName("Tahoma");
        lblSituacaoCadReceitaFederalFim.setFontStyle("[]");
        lblSituacaoCadReceitaFederalFim.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadReceitaFederalFim.setWordBreak(false);
        hBoxSituacaoCadReceitaFederal.addChildren(lblSituacaoCadReceitaFederalFim);
        lblSituacaoCadReceitaFederalFim.applyProperties();
    }

    public TFHBox hBoxSituacaoCadSintegra = new TFHBox();

    private void init_hBoxSituacaoCadSintegra() {
        hBoxSituacaoCadSintegra.setName("hBoxSituacaoCadSintegra");
        hBoxSituacaoCadSintegra.setLeft(0);
        hBoxSituacaoCadSintegra.setTop(40);
        hBoxSituacaoCadSintegra.setWidth(201);
        hBoxSituacaoCadSintegra.setHeight(18);
        hBoxSituacaoCadSintegra.setBorderStyle("stNone");
        hBoxSituacaoCadSintegra.setPaddingTop(-1);
        hBoxSituacaoCadSintegra.setPaddingLeft(0);
        hBoxSituacaoCadSintegra.setPaddingRight(0);
        hBoxSituacaoCadSintegra.setPaddingBottom(0);
        hBoxSituacaoCadSintegra.setVisible(false);
        hBoxSituacaoCadSintegra.setMarginTop(0);
        hBoxSituacaoCadSintegra.setMarginLeft(0);
        hBoxSituacaoCadSintegra.setMarginRight(0);
        hBoxSituacaoCadSintegra.setMarginBottom(0);
        hBoxSituacaoCadSintegra.setSpacing(1);
        hBoxSituacaoCadSintegra.setFlexVflex("ftFalse");
        hBoxSituacaoCadSintegra.setFlexHflex("ftFalse");
        hBoxSituacaoCadSintegra.setScrollable(false);
        hBoxSituacaoCadSintegra.setBoxShadowConfigHorizontalLength(10);
        hBoxSituacaoCadSintegra.setBoxShadowConfigVerticalLength(10);
        hBoxSituacaoCadSintegra.setBoxShadowConfigBlurRadius(5);
        hBoxSituacaoCadSintegra.setBoxShadowConfigSpreadRadius(0);
        hBoxSituacaoCadSintegra.setBoxShadowConfigShadowColor("clBlack");
        hBoxSituacaoCadSintegra.setBoxShadowConfigOpacity(75);
        hBoxSituacaoCadSintegra.setVAlign("tvTop");
        vBoxStatusCadastro.addChildren(hBoxSituacaoCadSintegra);
        hBoxSituacaoCadSintegra.applyProperties();
    }

    public TFLabel lblSituacaoCadSintegra = new TFLabel();

    private void init_lblSituacaoCadSintegra() {
        lblSituacaoCadSintegra.setName("lblSituacaoCadSintegra");
        lblSituacaoCadSintegra.setLeft(0);
        lblSituacaoCadSintegra.setTop(0);
        lblSituacaoCadSintegra.setWidth(47);
        lblSituacaoCadSintegra.setHeight(13);
        lblSituacaoCadSintegra.setCaption("Sintegra (");
        lblSituacaoCadSintegra.setFontColor("clWindowText");
        lblSituacaoCadSintegra.setFontSize(-11);
        lblSituacaoCadSintegra.setFontName("Tahoma");
        lblSituacaoCadSintegra.setFontStyle("[]");
        lblSituacaoCadSintegra.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSituacaoCadSintegraClick(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "lblSituacaoCadSintegra", "OnClick");
        });
        lblSituacaoCadSintegra.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadSintegra.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSituacaoCadSintegra);
        lblSituacaoCadSintegra.applyProperties();
    }

    public TFLabel lblSituacaoSintegra = new TFLabel();

    private void init_lblSituacaoSintegra() {
        lblSituacaoSintegra.setName("lblSituacaoSintegra");
        lblSituacaoSintegra.setLeft(47);
        lblSituacaoSintegra.setTop(0);
        lblSituacaoSintegra.setWidth(57);
        lblSituacaoSintegra.setHeight(13);
        lblSituacaoSintegra.setCaption("Habilitada");
        lblSituacaoSintegra.setFontColor("clWindowText");
        lblSituacaoSintegra.setFontSize(-11);
        lblSituacaoSintegra.setFontName("Tahoma");
        lblSituacaoSintegra.setFontStyle("[fsBold]");
        lblSituacaoSintegra.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSituacaoSintegraClick(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "lblSituacaoSintegra", "OnClick");
        });
        lblSituacaoSintegra.setVerticalAlignment("taVerticalCenter");
        lblSituacaoSintegra.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSituacaoSintegra);
        lblSituacaoSintegra.applyProperties();
    }

    public TFLabel lblSituacaoCadSintegraFim = new TFLabel();

    private void init_lblSituacaoCadSintegraFim() {
        lblSituacaoCadSintegraFim.setName("lblSituacaoCadSintegraFim");
        lblSituacaoCadSintegraFim.setLeft(104);
        lblSituacaoCadSintegraFim.setTop(0);
        lblSituacaoCadSintegraFim.setWidth(4);
        lblSituacaoCadSintegraFim.setHeight(13);
        lblSituacaoCadSintegraFim.setCaption(")");
        lblSituacaoCadSintegraFim.setFontColor("clWindowText");
        lblSituacaoCadSintegraFim.setFontSize(-11);
        lblSituacaoCadSintegraFim.setFontName("Tahoma");
        lblSituacaoCadSintegraFim.setFontStyle("[]");
        lblSituacaoCadSintegraFim.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadSintegraFim.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSituacaoCadSintegraFim);
        lblSituacaoCadSintegraFim.applyProperties();
    }

    public TFLabel lblSintegraMultiIe = new TFLabel();

    private void init_lblSintegraMultiIe() {
        lblSintegraMultiIe.setName("lblSintegraMultiIe");
        lblSintegraMultiIe.setLeft(108);
        lblSintegraMultiIe.setTop(0);
        lblSintegraMultiIe.setWidth(37);
        lblSintegraMultiIe.setHeight(13);
        lblSintegraMultiIe.setCaption(" [+... ] ");
        lblSintegraMultiIe.setFontColor("clWindowText");
        lblSintegraMultiIe.setFontSize(-11);
        lblSintegraMultiIe.setFontName("Tahoma");
        lblSintegraMultiIe.setFontStyle("[fsBold]");
        lblSintegraMultiIe.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSintegraMultiIeClick(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "lblSintegraMultiIe", "OnClick");
        });
        lblSintegraMultiIe.setVerticalAlignment("taVerticalCenter");
        lblSintegraMultiIe.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSintegraMultiIe);
        lblSintegraMultiIe.applyProperties();
    }

    public TFVBox vBoxBotoesSeparador01 = new TFVBox();

    private void init_vBoxBotoesSeparador01() {
        vBoxBotoesSeparador01.setName("vBoxBotoesSeparador01");
        vBoxBotoesSeparador01.setLeft(654);
        vBoxBotoesSeparador01.setTop(0);
        vBoxBotoesSeparador01.setWidth(5);
        vBoxBotoesSeparador01.setHeight(55);
        vBoxBotoesSeparador01.setBorderStyle("stNone");
        vBoxBotoesSeparador01.setPaddingTop(0);
        vBoxBotoesSeparador01.setPaddingLeft(0);
        vBoxBotoesSeparador01.setPaddingRight(0);
        vBoxBotoesSeparador01.setPaddingBottom(0);
        vBoxBotoesSeparador01.setMarginTop(0);
        vBoxBotoesSeparador01.setMarginLeft(0);
        vBoxBotoesSeparador01.setMarginRight(0);
        vBoxBotoesSeparador01.setMarginBottom(0);
        vBoxBotoesSeparador01.setSpacing(1);
        vBoxBotoesSeparador01.setFlexVflex("ftFalse");
        vBoxBotoesSeparador01.setFlexHflex("ftFalse");
        vBoxBotoesSeparador01.setScrollable(false);
        vBoxBotoesSeparador01.setBoxShadowConfigHorizontalLength(10);
        vBoxBotoesSeparador01.setBoxShadowConfigVerticalLength(10);
        vBoxBotoesSeparador01.setBoxShadowConfigBlurRadius(5);
        vBoxBotoesSeparador01.setBoxShadowConfigSpreadRadius(0);
        vBoxBotoesSeparador01.setBoxShadowConfigShadowColor("clBlack");
        vBoxBotoesSeparador01.setBoxShadowConfigOpacity(75);
        hBoxBotoes.addChildren(vBoxBotoesSeparador01);
        vBoxBotoesSeparador01.applyProperties();
    }

    public TFHBox hBoxNomeCliente = new TFHBox();

    private void init_hBoxNomeCliente() {
        hBoxNomeCliente.setName("hBoxNomeCliente");
        hBoxNomeCliente.setLeft(0);
        hBoxNomeCliente.setTop(75);
        hBoxNomeCliente.setWidth(260);
        hBoxNomeCliente.setHeight(50);
        hBoxNomeCliente.setBorderStyle("stNone");
        hBoxNomeCliente.setPaddingTop(0);
        hBoxNomeCliente.setPaddingLeft(0);
        hBoxNomeCliente.setPaddingRight(0);
        hBoxNomeCliente.setPaddingBottom(0);
        hBoxNomeCliente.setMarginTop(0);
        hBoxNomeCliente.setMarginLeft(0);
        hBoxNomeCliente.setMarginRight(0);
        hBoxNomeCliente.setMarginBottom(0);
        hBoxNomeCliente.setSpacing(1);
        hBoxNomeCliente.setFlexVflex("ftMin");
        hBoxNomeCliente.setFlexHflex("ftTrue");
        hBoxNomeCliente.setScrollable(false);
        hBoxNomeCliente.setBoxShadowConfigHorizontalLength(10);
        hBoxNomeCliente.setBoxShadowConfigVerticalLength(10);
        hBoxNomeCliente.setBoxShadowConfigBlurRadius(5);
        hBoxNomeCliente.setBoxShadowConfigSpreadRadius(0);
        hBoxNomeCliente.setBoxShadowConfigShadowColor("clBlack");
        hBoxNomeCliente.setBoxShadowConfigOpacity(75);
        hBoxNomeCliente.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxNomeCliente);
        hBoxNomeCliente.applyProperties();
    }

    public TFVBox vBoxNomeClienteSeparador03 = new TFVBox();

    private void init_vBoxNomeClienteSeparador03() {
        vBoxNomeClienteSeparador03.setName("vBoxNomeClienteSeparador03");
        vBoxNomeClienteSeparador03.setLeft(0);
        vBoxNomeClienteSeparador03.setTop(0);
        vBoxNomeClienteSeparador03.setWidth(5);
        vBoxNomeClienteSeparador03.setHeight(25);
        vBoxNomeClienteSeparador03.setBorderStyle("stNone");
        vBoxNomeClienteSeparador03.setPaddingTop(0);
        vBoxNomeClienteSeparador03.setPaddingLeft(0);
        vBoxNomeClienteSeparador03.setPaddingRight(0);
        vBoxNomeClienteSeparador03.setPaddingBottom(0);
        vBoxNomeClienteSeparador03.setMarginTop(0);
        vBoxNomeClienteSeparador03.setMarginLeft(0);
        vBoxNomeClienteSeparador03.setMarginRight(0);
        vBoxNomeClienteSeparador03.setMarginBottom(0);
        vBoxNomeClienteSeparador03.setSpacing(1);
        vBoxNomeClienteSeparador03.setFlexVflex("ftFalse");
        vBoxNomeClienteSeparador03.setFlexHflex("ftFalse");
        vBoxNomeClienteSeparador03.setScrollable(false);
        vBoxNomeClienteSeparador03.setBoxShadowConfigHorizontalLength(10);
        vBoxNomeClienteSeparador03.setBoxShadowConfigVerticalLength(10);
        vBoxNomeClienteSeparador03.setBoxShadowConfigBlurRadius(5);
        vBoxNomeClienteSeparador03.setBoxShadowConfigSpreadRadius(0);
        vBoxNomeClienteSeparador03.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomeClienteSeparador03.setBoxShadowConfigOpacity(75);
        hBoxNomeCliente.addChildren(vBoxNomeClienteSeparador03);
        vBoxNomeClienteSeparador03.applyProperties();
    }

    public TFVBox vBoxNomeCliente = new TFVBox();

    private void init_vBoxNomeCliente() {
        vBoxNomeCliente.setName("vBoxNomeCliente");
        vBoxNomeCliente.setLeft(5);
        vBoxNomeCliente.setTop(0);
        vBoxNomeCliente.setWidth(110);
        vBoxNomeCliente.setHeight(45);
        vBoxNomeCliente.setBorderStyle("stNone");
        vBoxNomeCliente.setPaddingTop(0);
        vBoxNomeCliente.setPaddingLeft(0);
        vBoxNomeCliente.setPaddingRight(0);
        vBoxNomeCliente.setPaddingBottom(0);
        vBoxNomeCliente.setMarginTop(0);
        vBoxNomeCliente.setMarginLeft(0);
        vBoxNomeCliente.setMarginRight(0);
        vBoxNomeCliente.setMarginBottom(0);
        vBoxNomeCliente.setSpacing(1);
        vBoxNomeCliente.setFlexVflex("ftMin");
        vBoxNomeCliente.setFlexHflex("ftTrue");
        vBoxNomeCliente.setScrollable(false);
        vBoxNomeCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxNomeCliente.setBoxShadowConfigVerticalLength(10);
        vBoxNomeCliente.setBoxShadowConfigBlurRadius(5);
        vBoxNomeCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxNomeCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomeCliente.setBoxShadowConfigOpacity(75);
        hBoxNomeCliente.addChildren(vBoxNomeCliente);
        vBoxNomeCliente.applyProperties();
    }

    public TFLabel lblNomeCliente = new TFLabel();

    private void init_lblNomeCliente() {
        lblNomeCliente.setName("lblNomeCliente");
        lblNomeCliente.setLeft(0);
        lblNomeCliente.setTop(0);
        lblNomeCliente.setWidth(27);
        lblNomeCliente.setHeight(13);
        lblNomeCliente.setCaption("Nome");
        lblNomeCliente.setFontColor("clWindowText");
        lblNomeCliente.setFontSize(-11);
        lblNomeCliente.setFontName("Tahoma");
        lblNomeCliente.setFontStyle("[]");
        lblNomeCliente.setVerticalAlignment("taVerticalCenter");
        lblNomeCliente.setWordBreak(false);
        vBoxNomeCliente.addChildren(lblNomeCliente);
        lblNomeCliente.applyProperties();
    }

    public TFString edtNomeCliente = new TFString();

    private void init_edtNomeCliente() {
        edtNomeCliente.setName("edtNomeCliente");
        edtNomeCliente.setLeft(0);
        edtNomeCliente.setTop(14);
        edtNomeCliente.setWidth(100);
        edtNomeCliente.setHeight(24);
        edtNomeCliente.setTable(tbClienteDiverso);
        edtNomeCliente.setFieldName("NOME");
        edtNomeCliente.setFlex(true);
        edtNomeCliente.setRequired(false);
        edtNomeCliente.setConstraintCheckWhen("cwImmediate");
        edtNomeCliente.setConstraintCheckType("ctExpression");
        edtNomeCliente.setConstraintFocusOnError(false);
        edtNomeCliente.setConstraintEnableUI(true);
        edtNomeCliente.setConstraintEnabled(false);
        edtNomeCliente.setConstraintFormCheck(true);
        edtNomeCliente.setCharCase("ccNormal");
        edtNomeCliente.setPwd(false);
        edtNomeCliente.setMaxlength(0);
        edtNomeCliente.setEnabled(false);
        edtNomeCliente.setFontColor("clWindowText");
        edtNomeCliente.setFontSize(-13);
        edtNomeCliente.setFontName("Tahoma");
        edtNomeCliente.setFontStyle("[]");
        edtNomeCliente.setSaveLiteralCharacter(false);
        edtNomeCliente.applyProperties();
        vBoxNomeCliente.addChildren(edtNomeCliente);
        addValidatable(edtNomeCliente);
    }

    public TFVBox vBoxNomeClienteSeparador02 = new TFVBox();

    private void init_vBoxNomeClienteSeparador02() {
        vBoxNomeClienteSeparador02.setName("vBoxNomeClienteSeparador02");
        vBoxNomeClienteSeparador02.setLeft(115);
        vBoxNomeClienteSeparador02.setTop(0);
        vBoxNomeClienteSeparador02.setWidth(5);
        vBoxNomeClienteSeparador02.setHeight(25);
        vBoxNomeClienteSeparador02.setBorderStyle("stNone");
        vBoxNomeClienteSeparador02.setPaddingTop(0);
        vBoxNomeClienteSeparador02.setPaddingLeft(0);
        vBoxNomeClienteSeparador02.setPaddingRight(0);
        vBoxNomeClienteSeparador02.setPaddingBottom(0);
        vBoxNomeClienteSeparador02.setMarginTop(0);
        vBoxNomeClienteSeparador02.setMarginLeft(0);
        vBoxNomeClienteSeparador02.setMarginRight(0);
        vBoxNomeClienteSeparador02.setMarginBottom(0);
        vBoxNomeClienteSeparador02.setSpacing(1);
        vBoxNomeClienteSeparador02.setFlexVflex("ftFalse");
        vBoxNomeClienteSeparador02.setFlexHflex("ftFalse");
        vBoxNomeClienteSeparador02.setScrollable(false);
        vBoxNomeClienteSeparador02.setBoxShadowConfigHorizontalLength(10);
        vBoxNomeClienteSeparador02.setBoxShadowConfigVerticalLength(10);
        vBoxNomeClienteSeparador02.setBoxShadowConfigBlurRadius(5);
        vBoxNomeClienteSeparador02.setBoxShadowConfigSpreadRadius(0);
        vBoxNomeClienteSeparador02.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomeClienteSeparador02.setBoxShadowConfigOpacity(75);
        hBoxNomeCliente.addChildren(vBoxNomeClienteSeparador02);
        vBoxNomeClienteSeparador02.applyProperties();
    }

    public TFVBox vBoxClienteProdutorRural = new TFVBox();

    private void init_vBoxClienteProdutorRural() {
        vBoxClienteProdutorRural.setName("vBoxClienteProdutorRural");
        vBoxClienteProdutorRural.setLeft(120);
        vBoxClienteProdutorRural.setTop(0);
        vBoxClienteProdutorRural.setWidth(130);
        vBoxClienteProdutorRural.setHeight(45);
        vBoxClienteProdutorRural.setBorderStyle("stNone");
        vBoxClienteProdutorRural.setPaddingTop(0);
        vBoxClienteProdutorRural.setPaddingLeft(0);
        vBoxClienteProdutorRural.setPaddingRight(0);
        vBoxClienteProdutorRural.setPaddingBottom(0);
        vBoxClienteProdutorRural.setMarginTop(0);
        vBoxClienteProdutorRural.setMarginLeft(0);
        vBoxClienteProdutorRural.setMarginRight(0);
        vBoxClienteProdutorRural.setMarginBottom(0);
        vBoxClienteProdutorRural.setSpacing(1);
        vBoxClienteProdutorRural.setFlexVflex("ftMin");
        vBoxClienteProdutorRural.setFlexHflex("ftMin");
        vBoxClienteProdutorRural.setScrollable(false);
        vBoxClienteProdutorRural.setBoxShadowConfigHorizontalLength(10);
        vBoxClienteProdutorRural.setBoxShadowConfigVerticalLength(10);
        vBoxClienteProdutorRural.setBoxShadowConfigBlurRadius(5);
        vBoxClienteProdutorRural.setBoxShadowConfigSpreadRadius(0);
        vBoxClienteProdutorRural.setBoxShadowConfigShadowColor("clBlack");
        vBoxClienteProdutorRural.setBoxShadowConfigOpacity(75);
        hBoxNomeCliente.addChildren(vBoxClienteProdutorRural);
        vBoxClienteProdutorRural.applyProperties();
    }

    public TFLabel lblProdutorRural = new TFLabel();

    private void init_lblProdutorRural() {
        lblProdutorRural.setName("lblProdutorRural");
        lblProdutorRural.setLeft(0);
        lblProdutorRural.setTop(0);
        lblProdutorRural.setWidth(115);
        lblProdutorRural.setHeight(13);
        lblProdutorRural.setCaption("Cliente \u00E9 Produtor Rural");
        lblProdutorRural.setFontColor("clWindowText");
        lblProdutorRural.setFontSize(-11);
        lblProdutorRural.setFontName("Tahoma");
        lblProdutorRural.setFontStyle("[]");
        lblProdutorRural.setVerticalAlignment("taVerticalCenter");
        lblProdutorRural.setWordBreak(false);
        vBoxClienteProdutorRural.addChildren(lblProdutorRural);
        lblProdutorRural.applyProperties();
    }

    public TFCombo cboClienteEhProdutorRural = new TFCombo();

    private void init_cboClienteEhProdutorRural() {
        cboClienteEhProdutorRural.setName("cboClienteEhProdutorRural");
        cboClienteEhProdutorRural.setLeft(0);
        cboClienteEhProdutorRural.setTop(14);
        cboClienteEhProdutorRural.setWidth(120);
        cboClienteEhProdutorRural.setHeight(21);
        cboClienteEhProdutorRural.setHint("Cliente \u00E9 produtor rural");
        cboClienteEhProdutorRural.setTable(tbClienteDiverso);
        cboClienteEhProdutorRural.setFieldName("PRODUTOR_RURAL");
        cboClienteEhProdutorRural.setFlex(false);
        cboClienteEhProdutorRural.setListOptions("Sim=S;N\u00E3o=N");
        cboClienteEhProdutorRural.setHelpCaption("Cliente \u00E9 produtor rural");
        cboClienteEhProdutorRural.setReadOnly(true);
        cboClienteEhProdutorRural.setRequired(false);
        cboClienteEhProdutorRural.setPrompt("Cliente \u00E9 produtor rural");
        cboClienteEhProdutorRural.setConstraintCheckWhen("cwImmediate");
        cboClienteEhProdutorRural.setConstraintCheckType("ctExpression");
        cboClienteEhProdutorRural.setConstraintFocusOnError(false);
        cboClienteEhProdutorRural.setConstraintEnableUI(true);
        cboClienteEhProdutorRural.setConstraintEnabled(false);
        cboClienteEhProdutorRural.setConstraintFormCheck(true);
        cboClienteEhProdutorRural.setClearOnDelKey(true);
        cboClienteEhProdutorRural.setUseClearButton(false);
        cboClienteEhProdutorRural.setHideClearButtonOnNullValue(false);
        cboClienteEhProdutorRural.setEnabled(false);
        cboClienteEhProdutorRural.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboClienteEhProdutorRuralChange(event);
            processarFlow("FrmClienteEnderecoPorInscricao", "cboClienteEhProdutorRural", "OnChange");
        });
        vBoxClienteProdutorRural.addChildren(cboClienteEhProdutorRural);
        cboClienteEhProdutorRural.applyProperties();
        addValidatable(cboClienteEhProdutorRural);
    }

    public TFVBox vBoxNomeClienteSeparador01 = new TFVBox();

    private void init_vBoxNomeClienteSeparador01() {
        vBoxNomeClienteSeparador01.setName("vBoxNomeClienteSeparador01");
        vBoxNomeClienteSeparador01.setLeft(250);
        vBoxNomeClienteSeparador01.setTop(0);
        vBoxNomeClienteSeparador01.setWidth(5);
        vBoxNomeClienteSeparador01.setHeight(25);
        vBoxNomeClienteSeparador01.setBorderStyle("stNone");
        vBoxNomeClienteSeparador01.setPaddingTop(0);
        vBoxNomeClienteSeparador01.setPaddingLeft(0);
        vBoxNomeClienteSeparador01.setPaddingRight(0);
        vBoxNomeClienteSeparador01.setPaddingBottom(0);
        vBoxNomeClienteSeparador01.setMarginTop(0);
        vBoxNomeClienteSeparador01.setMarginLeft(0);
        vBoxNomeClienteSeparador01.setMarginRight(0);
        vBoxNomeClienteSeparador01.setMarginBottom(0);
        vBoxNomeClienteSeparador01.setSpacing(1);
        vBoxNomeClienteSeparador01.setFlexVflex("ftFalse");
        vBoxNomeClienteSeparador01.setFlexHflex("ftFalse");
        vBoxNomeClienteSeparador01.setScrollable(false);
        vBoxNomeClienteSeparador01.setBoxShadowConfigHorizontalLength(10);
        vBoxNomeClienteSeparador01.setBoxShadowConfigVerticalLength(10);
        vBoxNomeClienteSeparador01.setBoxShadowConfigBlurRadius(5);
        vBoxNomeClienteSeparador01.setBoxShadowConfigSpreadRadius(0);
        vBoxNomeClienteSeparador01.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomeClienteSeparador01.setBoxShadowConfigOpacity(75);
        hBoxNomeCliente.addChildren(vBoxNomeClienteSeparador01);
        vBoxNomeClienteSeparador01.applyProperties();
    }

    public TFHBox hBoxNomePropriedade = new TFHBox();

    private void init_hBoxNomePropriedade() {
        hBoxNomePropriedade.setName("hBoxNomePropriedade");
        hBoxNomePropriedade.setLeft(0);
        hBoxNomePropriedade.setTop(126);
        hBoxNomePropriedade.setWidth(130);
        hBoxNomePropriedade.setHeight(55);
        hBoxNomePropriedade.setBorderStyle("stNone");
        hBoxNomePropriedade.setPaddingTop(0);
        hBoxNomePropriedade.setPaddingLeft(0);
        hBoxNomePropriedade.setPaddingRight(0);
        hBoxNomePropriedade.setPaddingBottom(0);
        hBoxNomePropriedade.setMarginTop(0);
        hBoxNomePropriedade.setMarginLeft(0);
        hBoxNomePropriedade.setMarginRight(0);
        hBoxNomePropriedade.setMarginBottom(0);
        hBoxNomePropriedade.setSpacing(1);
        hBoxNomePropriedade.setFlexVflex("ftMin");
        hBoxNomePropriedade.setFlexHflex("ftTrue");
        hBoxNomePropriedade.setScrollable(false);
        hBoxNomePropriedade.setBoxShadowConfigHorizontalLength(10);
        hBoxNomePropriedade.setBoxShadowConfigVerticalLength(10);
        hBoxNomePropriedade.setBoxShadowConfigBlurRadius(5);
        hBoxNomePropriedade.setBoxShadowConfigSpreadRadius(0);
        hBoxNomePropriedade.setBoxShadowConfigShadowColor("clBlack");
        hBoxNomePropriedade.setBoxShadowConfigOpacity(75);
        hBoxNomePropriedade.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxNomePropriedade);
        hBoxNomePropriedade.applyProperties();
    }

    public TFVBox vBoxNomePropriedadeSeparador01 = new TFVBox();

    private void init_vBoxNomePropriedadeSeparador01() {
        vBoxNomePropriedadeSeparador01.setName("vBoxNomePropriedadeSeparador01");
        vBoxNomePropriedadeSeparador01.setLeft(0);
        vBoxNomePropriedadeSeparador01.setTop(0);
        vBoxNomePropriedadeSeparador01.setWidth(5);
        vBoxNomePropriedadeSeparador01.setHeight(25);
        vBoxNomePropriedadeSeparador01.setBorderStyle("stNone");
        vBoxNomePropriedadeSeparador01.setPaddingTop(0);
        vBoxNomePropriedadeSeparador01.setPaddingLeft(0);
        vBoxNomePropriedadeSeparador01.setPaddingRight(0);
        vBoxNomePropriedadeSeparador01.setPaddingBottom(0);
        vBoxNomePropriedadeSeparador01.setMarginTop(0);
        vBoxNomePropriedadeSeparador01.setMarginLeft(0);
        vBoxNomePropriedadeSeparador01.setMarginRight(0);
        vBoxNomePropriedadeSeparador01.setMarginBottom(0);
        vBoxNomePropriedadeSeparador01.setSpacing(1);
        vBoxNomePropriedadeSeparador01.setFlexVflex("ftFalse");
        vBoxNomePropriedadeSeparador01.setFlexHflex("ftFalse");
        vBoxNomePropriedadeSeparador01.setScrollable(false);
        vBoxNomePropriedadeSeparador01.setBoxShadowConfigHorizontalLength(10);
        vBoxNomePropriedadeSeparador01.setBoxShadowConfigVerticalLength(10);
        vBoxNomePropriedadeSeparador01.setBoxShadowConfigBlurRadius(5);
        vBoxNomePropriedadeSeparador01.setBoxShadowConfigSpreadRadius(0);
        vBoxNomePropriedadeSeparador01.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomePropriedadeSeparador01.setBoxShadowConfigOpacity(75);
        hBoxNomePropriedade.addChildren(vBoxNomePropriedadeSeparador01);
        vBoxNomePropriedadeSeparador01.applyProperties();
    }

    public TFVBox vBoxPropriedade = new TFVBox();

    private void init_vBoxPropriedade() {
        vBoxPropriedade.setName("vBoxPropriedade");
        vBoxPropriedade.setLeft(5);
        vBoxPropriedade.setTop(0);
        vBoxPropriedade.setWidth(110);
        vBoxPropriedade.setHeight(50);
        vBoxPropriedade.setBorderStyle("stNone");
        vBoxPropriedade.setPaddingTop(0);
        vBoxPropriedade.setPaddingLeft(0);
        vBoxPropriedade.setPaddingRight(0);
        vBoxPropriedade.setPaddingBottom(0);
        vBoxPropriedade.setMarginTop(0);
        vBoxPropriedade.setMarginLeft(0);
        vBoxPropriedade.setMarginRight(0);
        vBoxPropriedade.setMarginBottom(0);
        vBoxPropriedade.setSpacing(1);
        vBoxPropriedade.setFlexVflex("ftMin");
        vBoxPropriedade.setFlexHflex("ftTrue");
        vBoxPropriedade.setScrollable(false);
        vBoxPropriedade.setBoxShadowConfigHorizontalLength(10);
        vBoxPropriedade.setBoxShadowConfigVerticalLength(10);
        vBoxPropriedade.setBoxShadowConfigBlurRadius(5);
        vBoxPropriedade.setBoxShadowConfigSpreadRadius(0);
        vBoxPropriedade.setBoxShadowConfigShadowColor("clBlack");
        vBoxPropriedade.setBoxShadowConfigOpacity(75);
        hBoxNomePropriedade.addChildren(vBoxPropriedade);
        vBoxPropriedade.applyProperties();
    }

    public TFLabel lblNomePropriedade = new TFLabel();

    private void init_lblNomePropriedade() {
        lblNomePropriedade.setName("lblNomePropriedade");
        lblNomePropriedade.setLeft(0);
        lblNomePropriedade.setTop(0);
        lblNomePropriedade.setWidth(103);
        lblNomePropriedade.setHeight(13);
        lblNomePropriedade.setCaption("Nome da propriedade");
        lblNomePropriedade.setFontColor("clWindowText");
        lblNomePropriedade.setFontSize(-11);
        lblNomePropriedade.setFontName("Tahoma");
        lblNomePropriedade.setFontStyle("[]");
        lblNomePropriedade.setVerticalAlignment("taVerticalCenter");
        lblNomePropriedade.setWordBreak(false);
        vBoxPropriedade.addChildren(lblNomePropriedade);
        lblNomePropriedade.applyProperties();
    }

    public TFString edtNomePropriedade = new TFString();

    private void init_edtNomePropriedade() {
        edtNomePropriedade.setName("edtNomePropriedade");
        edtNomePropriedade.setLeft(0);
        edtNomePropriedade.setTop(14);
        edtNomePropriedade.setWidth(100);
        edtNomePropriedade.setHeight(24);
        edtNomePropriedade.setTable(tbClienteEnderecoInscricao);
        edtNomePropriedade.setFieldName("NOME_PROPRIEDADE");
        edtNomePropriedade.setFlex(true);
        edtNomePropriedade.setRequired(false);
        edtNomePropriedade.setConstraintCheckWhen("cwImmediate");
        edtNomePropriedade.setConstraintCheckType("ctExpression");
        edtNomePropriedade.setConstraintFocusOnError(false);
        edtNomePropriedade.setConstraintEnableUI(true);
        edtNomePropriedade.setConstraintEnabled(false);
        edtNomePropriedade.setConstraintFormCheck(true);
        edtNomePropriedade.setCharCase("ccNormal");
        edtNomePropriedade.setPwd(false);
        edtNomePropriedade.setMaxlength(0);
        edtNomePropriedade.setEnabled(false);
        edtNomePropriedade.setFontColor("clWindowText");
        edtNomePropriedade.setFontSize(-13);
        edtNomePropriedade.setFontName("Tahoma");
        edtNomePropriedade.setFontStyle("[]");
        edtNomePropriedade.setSaveLiteralCharacter(false);
        edtNomePropriedade.applyProperties();
        vBoxPropriedade.addChildren(edtNomePropriedade);
        addValidatable(edtNomePropriedade);
    }

    public TFVBox vBoxNomePropriedadeSeparador02 = new TFVBox();

    private void init_vBoxNomePropriedadeSeparador02() {
        vBoxNomePropriedadeSeparador02.setName("vBoxNomePropriedadeSeparador02");
        vBoxNomePropriedadeSeparador02.setLeft(115);
        vBoxNomePropriedadeSeparador02.setTop(0);
        vBoxNomePropriedadeSeparador02.setWidth(5);
        vBoxNomePropriedadeSeparador02.setHeight(25);
        vBoxNomePropriedadeSeparador02.setBorderStyle("stNone");
        vBoxNomePropriedadeSeparador02.setPaddingTop(0);
        vBoxNomePropriedadeSeparador02.setPaddingLeft(0);
        vBoxNomePropriedadeSeparador02.setPaddingRight(0);
        vBoxNomePropriedadeSeparador02.setPaddingBottom(0);
        vBoxNomePropriedadeSeparador02.setMarginTop(0);
        vBoxNomePropriedadeSeparador02.setMarginLeft(0);
        vBoxNomePropriedadeSeparador02.setMarginRight(0);
        vBoxNomePropriedadeSeparador02.setMarginBottom(0);
        vBoxNomePropriedadeSeparador02.setSpacing(1);
        vBoxNomePropriedadeSeparador02.setFlexVflex("ftFalse");
        vBoxNomePropriedadeSeparador02.setFlexHflex("ftFalse");
        vBoxNomePropriedadeSeparador02.setScrollable(false);
        vBoxNomePropriedadeSeparador02.setBoxShadowConfigHorizontalLength(10);
        vBoxNomePropriedadeSeparador02.setBoxShadowConfigVerticalLength(10);
        vBoxNomePropriedadeSeparador02.setBoxShadowConfigBlurRadius(5);
        vBoxNomePropriedadeSeparador02.setBoxShadowConfigSpreadRadius(0);
        vBoxNomePropriedadeSeparador02.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomePropriedadeSeparador02.setBoxShadowConfigOpacity(75);
        hBoxNomePropriedade.addChildren(vBoxNomePropriedadeSeparador02);
        vBoxNomePropriedadeSeparador02.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador02 = new TFHBox();

    private void init_hBoxPrincipalSeparador02() {
        hBoxPrincipalSeparador02.setName("hBoxPrincipalSeparador02");
        hBoxPrincipalSeparador02.setLeft(0);
        hBoxPrincipalSeparador02.setTop(182);
        hBoxPrincipalSeparador02.setWidth(130);
        hBoxPrincipalSeparador02.setHeight(5);
        hBoxPrincipalSeparador02.setBorderStyle("stNone");
        hBoxPrincipalSeparador02.setPaddingTop(0);
        hBoxPrincipalSeparador02.setPaddingLeft(0);
        hBoxPrincipalSeparador02.setPaddingRight(0);
        hBoxPrincipalSeparador02.setPaddingBottom(0);
        hBoxPrincipalSeparador02.setMarginTop(0);
        hBoxPrincipalSeparador02.setMarginLeft(0);
        hBoxPrincipalSeparador02.setMarginRight(0);
        hBoxPrincipalSeparador02.setMarginBottom(0);
        hBoxPrincipalSeparador02.setSpacing(1);
        hBoxPrincipalSeparador02.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador02.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador02.setScrollable(false);
        hBoxPrincipalSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador02.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador02.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador02);
        hBoxPrincipalSeparador02.applyProperties();
    }

    public TFHBox hBoxGrade = new TFHBox();

    private void init_hBoxGrade() {
        hBoxGrade.setName("hBoxGrade");
        hBoxGrade.setLeft(0);
        hBoxGrade.setTop(188);
        hBoxGrade.setWidth(498);
        hBoxGrade.setHeight(110);
        hBoxGrade.setAlign("alTop");
        hBoxGrade.setBorderStyle("stNone");
        hBoxGrade.setPaddingTop(0);
        hBoxGrade.setPaddingLeft(0);
        hBoxGrade.setPaddingRight(0);
        hBoxGrade.setPaddingBottom(0);
        hBoxGrade.setMarginTop(0);
        hBoxGrade.setMarginLeft(0);
        hBoxGrade.setMarginRight(0);
        hBoxGrade.setMarginBottom(0);
        hBoxGrade.setSpacing(1);
        hBoxGrade.setFlexVflex("ftTrue");
        hBoxGrade.setFlexHflex("ftTrue");
        hBoxGrade.setScrollable(false);
        hBoxGrade.setBoxShadowConfigHorizontalLength(10);
        hBoxGrade.setBoxShadowConfigVerticalLength(10);
        hBoxGrade.setBoxShadowConfigBlurRadius(5);
        hBoxGrade.setBoxShadowConfigSpreadRadius(0);
        hBoxGrade.setBoxShadowConfigShadowColor("clBlack");
        hBoxGrade.setBoxShadowConfigOpacity(75);
        hBoxGrade.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxGrade);
        hBoxGrade.applyProperties();
    }

    public TFVBox vBoxGradeSeparador01 = new TFVBox();

    private void init_vBoxGradeSeparador01() {
        vBoxGradeSeparador01.setName("vBoxGradeSeparador01");
        vBoxGradeSeparador01.setLeft(0);
        vBoxGradeSeparador01.setTop(0);
        vBoxGradeSeparador01.setWidth(5);
        vBoxGradeSeparador01.setHeight(25);
        vBoxGradeSeparador01.setBorderStyle("stNone");
        vBoxGradeSeparador01.setPaddingTop(0);
        vBoxGradeSeparador01.setPaddingLeft(0);
        vBoxGradeSeparador01.setPaddingRight(0);
        vBoxGradeSeparador01.setPaddingBottom(0);
        vBoxGradeSeparador01.setMarginTop(0);
        vBoxGradeSeparador01.setMarginLeft(0);
        vBoxGradeSeparador01.setMarginRight(0);
        vBoxGradeSeparador01.setMarginBottom(0);
        vBoxGradeSeparador01.setSpacing(1);
        vBoxGradeSeparador01.setFlexVflex("ftFalse");
        vBoxGradeSeparador01.setFlexHflex("ftFalse");
        vBoxGradeSeparador01.setScrollable(false);
        vBoxGradeSeparador01.setBoxShadowConfigHorizontalLength(10);
        vBoxGradeSeparador01.setBoxShadowConfigVerticalLength(10);
        vBoxGradeSeparador01.setBoxShadowConfigBlurRadius(5);
        vBoxGradeSeparador01.setBoxShadowConfigSpreadRadius(0);
        vBoxGradeSeparador01.setBoxShadowConfigShadowColor("clBlack");
        vBoxGradeSeparador01.setBoxShadowConfigOpacity(75);
        hBoxGrade.addChildren(vBoxGradeSeparador01);
        vBoxGradeSeparador01.applyProperties();
    }

    public TFGrid gridClienteEnderecoPorInscricao = new TFGrid();

    private void init_gridClienteEnderecoPorInscricao() {
        gridClienteEnderecoPorInscricao.setName("gridClienteEnderecoPorInscricao");
        gridClienteEnderecoPorInscricao.setLeft(5);
        gridClienteEnderecoPorInscricao.setTop(0);
        gridClienteEnderecoPorInscricao.setWidth(480);
        gridClienteEnderecoPorInscricao.setHeight(76);
        gridClienteEnderecoPorInscricao.setAlign("alClient");
        gridClienteEnderecoPorInscricao.setTable(tbClienteEnderecoInscricao);
        gridClienteEnderecoPorInscricao.setFlexVflex("ftTrue");
        gridClienteEnderecoPorInscricao.setFlexHflex("ftTrue");
        gridClienteEnderecoPorInscricao.setPagingEnabled(false);
        gridClienteEnderecoPorInscricao.setFrozenColumns(0);
        gridClienteEnderecoPorInscricao.setShowFooter(false);
        gridClienteEnderecoPorInscricao.setShowHeader(true);
        gridClienteEnderecoPorInscricao.setMultiSelection(false);
        gridClienteEnderecoPorInscricao.setGroupingEnabled(false);
        gridClienteEnderecoPorInscricao.setGroupingExpanded(false);
        gridClienteEnderecoPorInscricao.setGroupingShowFooter(false);
        gridClienteEnderecoPorInscricao.setCrosstabEnabled(false);
        gridClienteEnderecoPorInscricao.setCrosstabGroupType("cgtConcat");
        gridClienteEnderecoPorInscricao.setEditionEnabled(false);
        gridClienteEnderecoPorInscricao.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_CLIENTE");
        item0.setTitleCaption("C\u00F3d. Cliente");
        item0.setWidth(76);
        item0.setVisible(false);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("INSCRICAO_ESTADUAL");
        item1.setTitleCaption("Ins. Est.");
        item1.setWidth(135);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("UF");
        item2.setWidth(40);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("NOME_PROPRIEDADE");
        item3.setTitleCaption("Nome Propriedade");
        item3.setWidth(10);
        item3.setVisible(false);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("CEP");
        item4.setTitleCaption("Cep");
        item4.setWidth(70);
        item4.setVisible(false);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("RUA");
        item5.setTitleCaption("Rua");
        item5.setWidth(200);
        item5.setVisible(false);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(true);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("FACHADA");
        item6.setTitleCaption("N\u00FAmero");
        item6.setWidth(68);
        item6.setVisible(false);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("BAIRRO");
        item7.setTitleCaption("Bairro");
        item7.setWidth(200);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(true);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("COD_CIDADES");
        item8.setTitleCaption("C\u00F3d. Cidades");
        item8.setWidth(140);
        item8.setVisible(false);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("CIDADE");
        item9.setTitleCaption("Cidade");
        item9.setWidth(150);
        item9.setVisible(true);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item9);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("COMPLEMENTO");
        item10.setTitleCaption("Complemento");
        item10.setWidth(149);
        item10.setVisible(false);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setCheckedValue("S");
        item10.setUncheckedValue("N");
        item10.setHiperLink(false);
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        item10.setBoxSize(0);
        item10.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item10);
        TFGridColumn item11 = new TFGridColumn();
        item11.setFieldName("ATIVO");
        item11.setTitleCaption("Ativo");
        item11.setWidth(50);
        item11.setVisible(true);
        item11.setPrecision(0);
        item11.setTextAlign("taCenter");
        item11.setFieldType("ftCheckBox");
        item11.setFlexRatio(0);
        item11.setSort(false);
        item11.setImageHeader(0);
        item11.setWrap(false);
        item11.setFlex(false);
        item11.setCharCase("ccNormal");
        item11.setBlobConfigMimeType("bmtText");
        item11.setBlobConfigShowType("btImageViewer");
        item11.setShowLabel(true);
        item11.setEditorEditType("etTFString");
        item11.setEditorPrecision(0);
        item11.setEditorMaxLength(100);
        item11.setEditorLookupFilterKey(0);
        item11.setEditorLookupFilterDesc(0);
        item11.setEditorPopupHeight(400);
        item11.setEditorPopupWidth(400);
        item11.setEditorCharCase("ccNormal");
        item11.setEditorEnabled(false);
        item11.setEditorReadOnly(false);
        item11.setCheckedValue("S");
        item11.setUncheckedValue("N");
        item11.setHiperLink(false);
        item11.setEditorConstraintCheckWhen("cwImmediate");
        item11.setEditorConstraintCheckType("ctExpression");
        item11.setEditorConstraintFocusOnError(false);
        item11.setEditorConstraintEnableUI(true);
        item11.setEditorConstraintEnabled(false);
        item11.setEmpty(false);
        item11.setMobileOptsShowMobile(false);
        item11.setMobileOptsOrder(0);
        item11.setBoxSize(0);
        item11.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item11);
        TFGridColumn item12 = new TFGridColumn();
        item12.setFieldName("CONTATO");
        item12.setTitleCaption("Contato");
        item12.setWidth(40);
        item12.setVisible(false);
        item12.setPrecision(0);
        item12.setTextAlign("taLeft");
        item12.setFieldType("ftString");
        item12.setFlexRatio(0);
        item12.setSort(false);
        item12.setImageHeader(0);
        item12.setWrap(false);
        item12.setFlex(false);
        item12.setCharCase("ccNormal");
        item12.setBlobConfigMimeType("bmtText");
        item12.setBlobConfigShowType("btImageViewer");
        item12.setShowLabel(true);
        item12.setEditorEditType("etTFString");
        item12.setEditorPrecision(0);
        item12.setEditorMaxLength(100);
        item12.setEditorLookupFilterKey(0);
        item12.setEditorLookupFilterDesc(0);
        item12.setEditorPopupHeight(400);
        item12.setEditorPopupWidth(400);
        item12.setEditorCharCase("ccNormal");
        item12.setEditorEnabled(false);
        item12.setEditorReadOnly(false);
        item12.setCheckedValue("S");
        item12.setUncheckedValue("N");
        item12.setHiperLink(false);
        item12.setEditorConstraintCheckWhen("cwImmediate");
        item12.setEditorConstraintCheckType("ctExpression");
        item12.setEditorConstraintFocusOnError(false);
        item12.setEditorConstraintEnableUI(true);
        item12.setEditorConstraintEnabled(false);
        item12.setEmpty(false);
        item12.setMobileOptsShowMobile(false);
        item12.setMobileOptsOrder(0);
        item12.setBoxSize(0);
        item12.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item12);
        TFGridColumn item13 = new TFGridColumn();
        item13.setFieldName("PREFIXO_TELEFONE_CONTATO");
        item13.setTitleCaption("Prefixo Telefone Contato");
        item13.setWidth(40);
        item13.setVisible(false);
        item13.setPrecision(0);
        item13.setTextAlign("taLeft");
        item13.setFieldType("ftString");
        item13.setFlexRatio(0);
        item13.setSort(false);
        item13.setImageHeader(0);
        item13.setWrap(false);
        item13.setFlex(false);
        item13.setCharCase("ccNormal");
        item13.setBlobConfigMimeType("bmtText");
        item13.setBlobConfigShowType("btImageViewer");
        item13.setShowLabel(true);
        item13.setEditorEditType("etTFString");
        item13.setEditorPrecision(0);
        item13.setEditorMaxLength(100);
        item13.setEditorLookupFilterKey(0);
        item13.setEditorLookupFilterDesc(0);
        item13.setEditorPopupHeight(400);
        item13.setEditorPopupWidth(400);
        item13.setEditorCharCase("ccNormal");
        item13.setEditorEnabled(false);
        item13.setEditorReadOnly(false);
        item13.setCheckedValue("S");
        item13.setUncheckedValue("N");
        item13.setHiperLink(false);
        item13.setEditorConstraintCheckWhen("cwImmediate");
        item13.setEditorConstraintCheckType("ctExpression");
        item13.setEditorConstraintFocusOnError(false);
        item13.setEditorConstraintEnableUI(true);
        item13.setEditorConstraintEnabled(false);
        item13.setEmpty(false);
        item13.setMobileOptsShowMobile(false);
        item13.setMobileOptsOrder(0);
        item13.setBoxSize(0);
        item13.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item13);
        TFGridColumn item14 = new TFGridColumn();
        item14.setFieldName("TELEFONE_CONTATO");
        item14.setTitleCaption("Telefone Contato");
        item14.setWidth(40);
        item14.setVisible(false);
        item14.setPrecision(0);
        item14.setTextAlign("taLeft");
        item14.setFieldType("ftString");
        item14.setFlexRatio(0);
        item14.setSort(false);
        item14.setImageHeader(0);
        item14.setWrap(false);
        item14.setFlex(false);
        item14.setCharCase("ccNormal");
        item14.setBlobConfigMimeType("bmtText");
        item14.setBlobConfigShowType("btImageViewer");
        item14.setShowLabel(true);
        item14.setEditorEditType("etTFString");
        item14.setEditorPrecision(0);
        item14.setEditorMaxLength(100);
        item14.setEditorLookupFilterKey(0);
        item14.setEditorLookupFilterDesc(0);
        item14.setEditorPopupHeight(400);
        item14.setEditorPopupWidth(400);
        item14.setEditorCharCase("ccNormal");
        item14.setEditorEnabled(false);
        item14.setEditorReadOnly(false);
        item14.setCheckedValue("S");
        item14.setUncheckedValue("N");
        item14.setHiperLink(false);
        item14.setEditorConstraintCheckWhen("cwImmediate");
        item14.setEditorConstraintCheckType("ctExpression");
        item14.setEditorConstraintFocusOnError(false);
        item14.setEditorConstraintEnableUI(true);
        item14.setEditorConstraintEnabled(false);
        item14.setEmpty(false);
        item14.setMobileOptsShowMobile(false);
        item14.setMobileOptsOrder(0);
        item14.setBoxSize(0);
        item14.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item14);
        TFGridColumn item15 = new TFGridColumn();
        item15.setFieldName("CX_POSTAL");
        item15.setTitleCaption("Cx Postal");
        item15.setWidth(40);
        item15.setVisible(false);
        item15.setPrecision(0);
        item15.setTextAlign("taLeft");
        item15.setFieldType("ftString");
        item15.setFlexRatio(0);
        item15.setSort(false);
        item15.setImageHeader(0);
        item15.setWrap(false);
        item15.setFlex(false);
        item15.setCharCase("ccNormal");
        item15.setBlobConfigMimeType("bmtText");
        item15.setBlobConfigShowType("btImageViewer");
        item15.setShowLabel(true);
        item15.setEditorEditType("etTFString");
        item15.setEditorPrecision(0);
        item15.setEditorMaxLength(100);
        item15.setEditorLookupFilterKey(0);
        item15.setEditorLookupFilterDesc(0);
        item15.setEditorPopupHeight(400);
        item15.setEditorPopupWidth(400);
        item15.setEditorCharCase("ccNormal");
        item15.setEditorEnabled(false);
        item15.setEditorReadOnly(false);
        item15.setCheckedValue("S");
        item15.setUncheckedValue("N");
        item15.setHiperLink(false);
        item15.setEditorConstraintCheckWhen("cwImmediate");
        item15.setEditorConstraintCheckType("ctExpression");
        item15.setEditorConstraintFocusOnError(false);
        item15.setEditorConstraintEnableUI(true);
        item15.setEditorConstraintEnabled(false);
        item15.setEmpty(false);
        item15.setMobileOptsShowMobile(false);
        item15.setMobileOptsOrder(0);
        item15.setBoxSize(0);
        item15.setImageSrcType("istSource");
        gridClienteEnderecoPorInscricao.getColumns().add(item15);
        hBoxGrade.addChildren(gridClienteEnderecoPorInscricao);
        gridClienteEnderecoPorInscricao.applyProperties();
    }

    public TFVBox vBoxGradeSeparador02 = new TFVBox();

    private void init_vBoxGradeSeparador02() {
        vBoxGradeSeparador02.setName("vBoxGradeSeparador02");
        vBoxGradeSeparador02.setLeft(485);
        vBoxGradeSeparador02.setTop(0);
        vBoxGradeSeparador02.setWidth(5);
        vBoxGradeSeparador02.setHeight(25);
        vBoxGradeSeparador02.setBorderStyle("stNone");
        vBoxGradeSeparador02.setPaddingTop(0);
        vBoxGradeSeparador02.setPaddingLeft(0);
        vBoxGradeSeparador02.setPaddingRight(0);
        vBoxGradeSeparador02.setPaddingBottom(0);
        vBoxGradeSeparador02.setMarginTop(0);
        vBoxGradeSeparador02.setMarginLeft(0);
        vBoxGradeSeparador02.setMarginRight(0);
        vBoxGradeSeparador02.setMarginBottom(0);
        vBoxGradeSeparador02.setSpacing(1);
        vBoxGradeSeparador02.setFlexVflex("ftFalse");
        vBoxGradeSeparador02.setFlexHflex("ftFalse");
        vBoxGradeSeparador02.setScrollable(false);
        vBoxGradeSeparador02.setBoxShadowConfigHorizontalLength(10);
        vBoxGradeSeparador02.setBoxShadowConfigVerticalLength(10);
        vBoxGradeSeparador02.setBoxShadowConfigBlurRadius(5);
        vBoxGradeSeparador02.setBoxShadowConfigSpreadRadius(0);
        vBoxGradeSeparador02.setBoxShadowConfigShadowColor("clBlack");
        vBoxGradeSeparador02.setBoxShadowConfigOpacity(75);
        hBoxGrade.addChildren(vBoxGradeSeparador02);
        vBoxGradeSeparador02.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador03 = new TFHBox();

    private void init_hBoxPrincipalSeparador03() {
        hBoxPrincipalSeparador03.setName("hBoxPrincipalSeparador03");
        hBoxPrincipalSeparador03.setLeft(0);
        hBoxPrincipalSeparador03.setTop(299);
        hBoxPrincipalSeparador03.setWidth(185);
        hBoxPrincipalSeparador03.setHeight(5);
        hBoxPrincipalSeparador03.setBorderStyle("stNone");
        hBoxPrincipalSeparador03.setPaddingTop(0);
        hBoxPrincipalSeparador03.setPaddingLeft(0);
        hBoxPrincipalSeparador03.setPaddingRight(0);
        hBoxPrincipalSeparador03.setPaddingBottom(0);
        hBoxPrincipalSeparador03.setMarginTop(0);
        hBoxPrincipalSeparador03.setMarginLeft(0);
        hBoxPrincipalSeparador03.setMarginRight(0);
        hBoxPrincipalSeparador03.setMarginBottom(0);
        hBoxPrincipalSeparador03.setSpacing(1);
        hBoxPrincipalSeparador03.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador03.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador03.setScrollable(false);
        hBoxPrincipalSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador03.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador03.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador03);
        hBoxPrincipalSeparador03.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnReloadClick(final Event<Object> event) {
        if (btnReload.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnReload");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnIncluirClick(final Event<Object> event) {
        if (btnIncluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIncluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExibirDadosConsultaAPIIntegracaoClick(final Event<Object> event) {
        if (btnExibirDadosConsultaAPIIntegracao.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExibirDadosConsultaAPIIntegracao");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void lblSituacaoCadSintegraClick(final Event<Object> event);

    public abstract void lblSituacaoSintegraClick(final Event<Object> event);

    public abstract void lblSintegraMultiIeClick(final Event<Object> event);

    public abstract void cboClienteEhProdutorRuralChange(final Event<Object> event);

    public abstract void tbClienteEnderecoInscricaoAfterScroll(final Event<Object> event);

}