package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCadastroRapidoClienteEndereco extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CadastroRapidoClienteEnderecoRNA rn = null;

    public FrmCadastroRapidoClienteEndereco() {
        try {
            rn = (freedom.bytecode.rn.CadastroRapidoClienteEnderecoRNA) getRN(freedom.bytecode.rn.wizard.CadastroRapidoClienteEnderecoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbUf();
        init_tbCidades();
        init_vBoxPrincipal();
        init_hBoxPrincipalSeparador01();
        init_hBoxEditarLead();
        init_hBoxEditarLeadSeparador04();
        init_btnCancelar();
        init_hBoxEditarLeadSeparador02();
        init_btnConfirmar();
        init_hBoxEditarLeadSeparador01();
        init_vBoxStatusCadastro();
        init_hBoxSituacaoCadastral();
        init_lblSituacaoCadastral();
        init_********************************();
        init_lblCadastroIrregular();
        init_hBoxSituacaoCadReceitaFederal();
        init_lblSituacaoCadReceitaFederal();
        init_lblSituacaoReceitaFederal();
        init_lblSituacaoCadReceitaFederalFim();
        init_hBoxSituacaoCadSintegra();
        init_lblSituacaoCadSintegra();
        init_lblSituacaoSintegra();
        init_lblSituacaoCadSintegraFim();
        init_lblSintegraMultiIe();
        init_hBoxEditarLeadSeparador03();
        init_hBoxPrincipalSeparador02();
        init_hBoxTipoDoEndereco();
        init_hBoxTipoDoEnderecoSeparador01();
        init_vBoxTipoDoEndereco();
        init_lblTipoDoEndereco();
        init_edtTipoDoEndereco();
        init_hBoxTipoDoEnderecoSeparador02();
        init_hBoxPrincipalSeparador04();
        init_hBoxNomeDaPropriedade();
        init_********************************();
        init_vBoxNomeDaPropriedade();
        init_lblNomeDaPropriedade();
        init_edtNomePropriedade();
        init_********************************();
        init_hBoxPrincipalSeparador05();
        init_hBoxUFCidadeCEP();
        init_hBoxUFCidadeCEPSeparador01();
        init_vBoxUF();
        init_lblUF();
        init_cboUF();
        init_hBoxUFCidadeCEPSeparador02();
        init_vBoxCidade();
        init_lblCidade();
        init_cboCidade();
        init_hBoxUFCidadeCEPSeparador03();
        init_hBoxPrincipalSeparador06();
        init_hBoxBairro();
        init_hBoxBairroSeparador01();
        init_vBoxBairro();
        init_lblBairro();
        init_edtBairro();
        init_hBoxBairroSeparador02();
        init_vBoxCEP();
        init_lblCEP();
        init_edtCEP();
        init_hBoxUFCidadeCEPSeparador04();
        init_hBoxPrincipalSeparador07();
        init_hBoxRuaNumero();
        init_hBoxRuaNumeroSeparador01();
        init_vBoxRua();
        init_lblRua();
        init_edtRua();
        init_hBoxRuaNumeroSeparador02();
        init_vBoxNumero();
        init_lblNumero();
        init_edtNumero();
        init_hBoxRuaNumeroSeparador03();
        init_hBoxPrincipalSeparador08();
        init_hBoxComplementoCaixaPostal();
        init_hBoxComplementoCaixaPostalSeparador01();
        init_vBoxComplemento();
        init_lblComplemento();
        init_edtComplemento();
        init_hBoxComplementoCaixaPostalSeparador02();
        init_vBoxCaixaPostal();
        init_lblCaixaPostal();
        init_edtCxPostal();
        init_hBoxComplementoCaixaPostalSeparador03();
        init_hBoxPrincipalSeparador09();
        init_hBoxContato();
        init_hBoxContatoSeparador01();
        init_vBoxContato();
        init_lblContato();
        init_edtContato();
        init_hBoxContatoSeparador02();
        init_hBoxPrincipalSeparador10();
        init_hBoxEnderecoAtivo();
        init_hBoxEnderecoAtivoSeparador01();
        init_vBoxEnderecoAtivo();
        init_lblEnderecoAtio();
        init_cboEnderecoAtivo();
        init_hBoxEnderecoAtivoSeparador02();
        init_FrmCadastroRapidoClienteEndereco();
    }

    public UF tbUf;

    private void init_tbUf() {
        tbUf = rn.tbUf;
        tbUf.setName("tbUf");
        tbUf.setMaxRowCount(200);
        tbUf.setWKey("5300739;53001");
        tbUf.setRatioBatchSize(20);
        getTables().put(tbUf, "tbUf");
        tbUf.applyProperties();
    }

    public CIDADES tbCidades;

    private void init_tbCidades() {
        tbCidades = rn.tbCidades;
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("5300739;53002");
        tbCidades.setRatioBatchSize(20);
        getTables().put(tbCidades, "tbCidades");
        tbCidades.applyProperties();
    }

    protected TFForm FrmCadastroRapidoClienteEndereco = this;
    private void init_FrmCadastroRapidoClienteEndereco() {
        FrmCadastroRapidoClienteEndereco.setName("FrmCadastroRapidoClienteEndereco");
        FrmCadastroRapidoClienteEndereco.setCaption("Cadastro R\u00E1pido Endere\u00E7o de Cliente ");
        FrmCadastroRapidoClienteEndereco.setClientHeight(571);
        FrmCadastroRapidoClienteEndereco.setClientWidth(524);
        FrmCadastroRapidoClienteEndereco.setColor("clBtnFace");
        FrmCadastroRapidoClienteEndereco.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "FrmCadastroRapidoClienteEndereco", "OnCreate");
        });
        FrmCadastroRapidoClienteEndereco.setWOrigem("EhMain");
        FrmCadastroRapidoClienteEndereco.setWKey("5300739");
        FrmCadastroRapidoClienteEndereco.setSpacing(0);
        FrmCadastroRapidoClienteEndereco.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(524);
        vBoxPrincipal.setHeight(571);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmCadastroRapidoClienteEndereco.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador01 = new TFHBox();

    private void init_hBoxPrincipalSeparador01() {
        hBoxPrincipalSeparador01.setName("hBoxPrincipalSeparador01");
        hBoxPrincipalSeparador01.setLeft(0);
        hBoxPrincipalSeparador01.setTop(0);
        hBoxPrincipalSeparador01.setWidth(100);
        hBoxPrincipalSeparador01.setHeight(5);
        hBoxPrincipalSeparador01.setBorderStyle("stNone");
        hBoxPrincipalSeparador01.setPaddingTop(0);
        hBoxPrincipalSeparador01.setPaddingLeft(0);
        hBoxPrincipalSeparador01.setPaddingRight(0);
        hBoxPrincipalSeparador01.setPaddingBottom(0);
        hBoxPrincipalSeparador01.setMarginTop(0);
        hBoxPrincipalSeparador01.setMarginLeft(0);
        hBoxPrincipalSeparador01.setMarginRight(0);
        hBoxPrincipalSeparador01.setMarginBottom(0);
        hBoxPrincipalSeparador01.setSpacing(1);
        hBoxPrincipalSeparador01.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador01.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador01.setScrollable(false);
        hBoxPrincipalSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador01.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador01.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador01);
        hBoxPrincipalSeparador01.applyProperties();
    }

    public TFHBox hBoxEditarLead = new TFHBox();

    private void init_hBoxEditarLead() {
        hBoxEditarLead.setName("hBoxEditarLead");
        hBoxEditarLead.setLeft(0);
        hBoxEditarLead.setTop(6);
        hBoxEditarLead.setWidth(440);
        hBoxEditarLead.setHeight(70);
        hBoxEditarLead.setAlign("alTop");
        hBoxEditarLead.setBorderStyle("stNone");
        hBoxEditarLead.setPaddingTop(0);
        hBoxEditarLead.setPaddingLeft(0);
        hBoxEditarLead.setPaddingRight(0);
        hBoxEditarLead.setPaddingBottom(0);
        hBoxEditarLead.setMarginTop(0);
        hBoxEditarLead.setMarginLeft(0);
        hBoxEditarLead.setMarginRight(0);
        hBoxEditarLead.setMarginBottom(0);
        hBoxEditarLead.setSpacing(1);
        hBoxEditarLead.setFlexVflex("ftMin");
        hBoxEditarLead.setFlexHflex("ftTrue");
        hBoxEditarLead.setScrollable(false);
        hBoxEditarLead.setBoxShadowConfigHorizontalLength(10);
        hBoxEditarLead.setBoxShadowConfigVerticalLength(10);
        hBoxEditarLead.setBoxShadowConfigBlurRadius(5);
        hBoxEditarLead.setBoxShadowConfigSpreadRadius(0);
        hBoxEditarLead.setBoxShadowConfigShadowColor("clBlack");
        hBoxEditarLead.setBoxShadowConfigOpacity(75);
        hBoxEditarLead.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxEditarLead);
        hBoxEditarLead.applyProperties();
    }

    public TFHBox hBoxEditarLeadSeparador04 = new TFHBox();

    private void init_hBoxEditarLeadSeparador04() {
        hBoxEditarLeadSeparador04.setName("hBoxEditarLeadSeparador04");
        hBoxEditarLeadSeparador04.setLeft(0);
        hBoxEditarLeadSeparador04.setTop(0);
        hBoxEditarLeadSeparador04.setWidth(5);
        hBoxEditarLeadSeparador04.setHeight(20);
        hBoxEditarLeadSeparador04.setBorderStyle("stNone");
        hBoxEditarLeadSeparador04.setPaddingTop(0);
        hBoxEditarLeadSeparador04.setPaddingLeft(0);
        hBoxEditarLeadSeparador04.setPaddingRight(0);
        hBoxEditarLeadSeparador04.setPaddingBottom(0);
        hBoxEditarLeadSeparador04.setMarginTop(0);
        hBoxEditarLeadSeparador04.setMarginLeft(0);
        hBoxEditarLeadSeparador04.setMarginRight(0);
        hBoxEditarLeadSeparador04.setMarginBottom(0);
        hBoxEditarLeadSeparador04.setSpacing(1);
        hBoxEditarLeadSeparador04.setFlexVflex("ftFalse");
        hBoxEditarLeadSeparador04.setFlexHflex("ftFalse");
        hBoxEditarLeadSeparador04.setScrollable(false);
        hBoxEditarLeadSeparador04.setBoxShadowConfigHorizontalLength(10);
        hBoxEditarLeadSeparador04.setBoxShadowConfigVerticalLength(10);
        hBoxEditarLeadSeparador04.setBoxShadowConfigBlurRadius(5);
        hBoxEditarLeadSeparador04.setBoxShadowConfigSpreadRadius(0);
        hBoxEditarLeadSeparador04.setBoxShadowConfigShadowColor("clBlack");
        hBoxEditarLeadSeparador04.setBoxShadowConfigOpacity(75);
        hBoxEditarLeadSeparador04.setVAlign("tvTop");
        hBoxEditarLead.addChildren(hBoxEditarLeadSeparador04);
        hBoxEditarLeadSeparador04.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(5);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(60);
        btnCancelar.setHeight(55);
        btnCancelar.setHint("Cancelar Altera\u00E7\u00E3o de Endere\u00E7o do Cliente");
        btnCancelar.setAlign("alLeft");
        btnCancelar.setCaption("Voltar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(700081);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        hBoxEditarLead.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox hBoxEditarLeadSeparador02 = new TFHBox();

    private void init_hBoxEditarLeadSeparador02() {
        hBoxEditarLeadSeparador02.setName("hBoxEditarLeadSeparador02");
        hBoxEditarLeadSeparador02.setLeft(65);
        hBoxEditarLeadSeparador02.setTop(0);
        hBoxEditarLeadSeparador02.setWidth(5);
        hBoxEditarLeadSeparador02.setHeight(20);
        hBoxEditarLeadSeparador02.setBorderStyle("stNone");
        hBoxEditarLeadSeparador02.setPaddingTop(0);
        hBoxEditarLeadSeparador02.setPaddingLeft(0);
        hBoxEditarLeadSeparador02.setPaddingRight(0);
        hBoxEditarLeadSeparador02.setPaddingBottom(0);
        hBoxEditarLeadSeparador02.setMarginTop(0);
        hBoxEditarLeadSeparador02.setMarginLeft(0);
        hBoxEditarLeadSeparador02.setMarginRight(0);
        hBoxEditarLeadSeparador02.setMarginBottom(0);
        hBoxEditarLeadSeparador02.setSpacing(1);
        hBoxEditarLeadSeparador02.setFlexVflex("ftFalse");
        hBoxEditarLeadSeparador02.setFlexHflex("ftFalse");
        hBoxEditarLeadSeparador02.setScrollable(false);
        hBoxEditarLeadSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxEditarLeadSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxEditarLeadSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxEditarLeadSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxEditarLeadSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxEditarLeadSeparador02.setBoxShadowConfigOpacity(75);
        hBoxEditarLeadSeparador02.setVAlign("tvTop");
        hBoxEditarLead.addChildren(hBoxEditarLeadSeparador02);
        hBoxEditarLeadSeparador02.applyProperties();
    }

    public TFButton btnConfirmar = new TFButton();

    private void init_btnConfirmar() {
        btnConfirmar.setName("btnConfirmar");
        btnConfirmar.setLeft(70);
        btnConfirmar.setTop(0);
        btnConfirmar.setWidth(60);
        btnConfirmar.setHeight(55);
        btnConfirmar.setHint("Salvar Altera\u00E7\u00F5es no Endere\u00E7o do Cliente");
        btnConfirmar.setAlign("alLeft");
        btnConfirmar.setCaption("Confirmar");
        btnConfirmar.setFontColor("clWindowText");
        btnConfirmar.setFontSize(-11);
        btnConfirmar.setFontName("Tahoma");
        btnConfirmar.setFontStyle("[]");
        btnConfirmar.setLayout("blGlyphTop");
        btnConfirmar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "btnConfirmar", "OnClick");
        });
        btnConfirmar.setImageId(700075);
        btnConfirmar.setColor("clBtnFace");
        btnConfirmar.setAccess(false);
        btnConfirmar.setIconReverseDirection(false);
        hBoxEditarLead.addChildren(btnConfirmar);
        btnConfirmar.applyProperties();
    }

    public TFHBox hBoxEditarLeadSeparador01 = new TFHBox();

    private void init_hBoxEditarLeadSeparador01() {
        hBoxEditarLeadSeparador01.setName("hBoxEditarLeadSeparador01");
        hBoxEditarLeadSeparador01.setLeft(130);
        hBoxEditarLeadSeparador01.setTop(0);
        hBoxEditarLeadSeparador01.setWidth(89);
        hBoxEditarLeadSeparador01.setHeight(41);
        hBoxEditarLeadSeparador01.setBorderStyle("stNone");
        hBoxEditarLeadSeparador01.setPaddingTop(0);
        hBoxEditarLeadSeparador01.setPaddingLeft(0);
        hBoxEditarLeadSeparador01.setPaddingRight(0);
        hBoxEditarLeadSeparador01.setPaddingBottom(0);
        hBoxEditarLeadSeparador01.setMarginTop(0);
        hBoxEditarLeadSeparador01.setMarginLeft(0);
        hBoxEditarLeadSeparador01.setMarginRight(0);
        hBoxEditarLeadSeparador01.setMarginBottom(0);
        hBoxEditarLeadSeparador01.setSpacing(1);
        hBoxEditarLeadSeparador01.setFlexVflex("ftFalse");
        hBoxEditarLeadSeparador01.setFlexHflex("ftTrue");
        hBoxEditarLeadSeparador01.setScrollable(false);
        hBoxEditarLeadSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxEditarLeadSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxEditarLeadSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxEditarLeadSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxEditarLeadSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxEditarLeadSeparador01.setBoxShadowConfigOpacity(75);
        hBoxEditarLeadSeparador01.setVAlign("tvTop");
        hBoxEditarLead.addChildren(hBoxEditarLeadSeparador01);
        hBoxEditarLeadSeparador01.applyProperties();
    }

    public TFVBox vBoxStatusCadastro = new TFVBox();

    private void init_vBoxStatusCadastro() {
        vBoxStatusCadastro.setName("vBoxStatusCadastro");
        vBoxStatusCadastro.setLeft(219);
        vBoxStatusCadastro.setTop(0);
        vBoxStatusCadastro.setWidth(207);
        vBoxStatusCadastro.setHeight(62);
        vBoxStatusCadastro.setBorderStyle("stNone");
        vBoxStatusCadastro.setPaddingTop(0);
        vBoxStatusCadastro.setPaddingLeft(0);
        vBoxStatusCadastro.setPaddingRight(0);
        vBoxStatusCadastro.setPaddingBottom(0);
        vBoxStatusCadastro.setVisible(false);
        vBoxStatusCadastro.setMarginTop(0);
        vBoxStatusCadastro.setMarginLeft(0);
        vBoxStatusCadastro.setMarginRight(0);
        vBoxStatusCadastro.setMarginBottom(0);
        vBoxStatusCadastro.setSpacing(1);
        vBoxStatusCadastro.setFlexVflex("ftFalse");
        vBoxStatusCadastro.setFlexHflex("ftFalse");
        vBoxStatusCadastro.setScrollable(false);
        vBoxStatusCadastro.setBoxShadowConfigHorizontalLength(10);
        vBoxStatusCadastro.setBoxShadowConfigVerticalLength(10);
        vBoxStatusCadastro.setBoxShadowConfigBlurRadius(5);
        vBoxStatusCadastro.setBoxShadowConfigSpreadRadius(0);
        vBoxStatusCadastro.setBoxShadowConfigShadowColor("clBlack");
        vBoxStatusCadastro.setBoxShadowConfigOpacity(75);
        hBoxEditarLead.addChildren(vBoxStatusCadastro);
        vBoxStatusCadastro.applyProperties();
    }

    public TFHBox hBoxSituacaoCadastral = new TFHBox();

    private void init_hBoxSituacaoCadastral() {
        hBoxSituacaoCadastral.setName("hBoxSituacaoCadastral");
        hBoxSituacaoCadastral.setLeft(0);
        hBoxSituacaoCadastral.setTop(0);
        hBoxSituacaoCadastral.setWidth(203);
        hBoxSituacaoCadastral.setHeight(20);
        hBoxSituacaoCadastral.setBorderStyle("stNone");
        hBoxSituacaoCadastral.setPaddingTop(0);
        hBoxSituacaoCadastral.setPaddingLeft(0);
        hBoxSituacaoCadastral.setPaddingRight(0);
        hBoxSituacaoCadastral.setPaddingBottom(0);
        hBoxSituacaoCadastral.setMarginTop(0);
        hBoxSituacaoCadastral.setMarginLeft(0);
        hBoxSituacaoCadastral.setMarginRight(0);
        hBoxSituacaoCadastral.setMarginBottom(0);
        hBoxSituacaoCadastral.setSpacing(1);
        hBoxSituacaoCadastral.setFlexVflex("ftFalse");
        hBoxSituacaoCadastral.setFlexHflex("ftFalse");
        hBoxSituacaoCadastral.setScrollable(false);
        hBoxSituacaoCadastral.setBoxShadowConfigHorizontalLength(10);
        hBoxSituacaoCadastral.setBoxShadowConfigVerticalLength(10);
        hBoxSituacaoCadastral.setBoxShadowConfigBlurRadius(5);
        hBoxSituacaoCadastral.setBoxShadowConfigSpreadRadius(0);
        hBoxSituacaoCadastral.setBoxShadowConfigShadowColor("clBlack");
        hBoxSituacaoCadastral.setBoxShadowConfigOpacity(75);
        hBoxSituacaoCadastral.setVAlign("tvTop");
        vBoxStatusCadastro.addChildren(hBoxSituacaoCadastral);
        hBoxSituacaoCadastral.applyProperties();
    }

    public TFLabel lblSituacaoCadastral = new TFLabel();

    private void init_lblSituacaoCadastral() {
        lblSituacaoCadastral.setName("lblSituacaoCadastral");
        lblSituacaoCadastral.setLeft(0);
        lblSituacaoCadastral.setTop(0);
        lblSituacaoCadastral.setWidth(90);
        lblSituacaoCadastral.setHeight(13);
        lblSituacaoCadastral.setCaption("Situa\u00E7\u00E3o Cadastral");
        lblSituacaoCadastral.setFontColor("clWindowText");
        lblSituacaoCadastral.setFontSize(-11);
        lblSituacaoCadastral.setFontName("Tahoma");
        lblSituacaoCadastral.setFontStyle("[]");
        lblSituacaoCadastral.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadastral.setWordBreak(false);
        hBoxSituacaoCadastral.addChildren(lblSituacaoCadastral);
        lblSituacaoCadastral.applyProperties();
    }

    public TFHBox ******************************** = new TFHBox();

    private void init_********************************() {
        ********************************.setName("********************************");
        ********************************.setLeft(90);
        ********************************.setTop(0);
        ********************************.setWidth(5);
        ********************************.setHeight(15);
        ********************************.setBorderStyle("stNone");
        ********************************.setPaddingTop(0);
        ********************************.setPaddingLeft(0);
        ********************************.setPaddingRight(0);
        ********************************.setPaddingBottom(0);
        ********************************.setMarginTop(0);
        ********************************.setMarginLeft(0);
        ********************************.setMarginRight(0);
        ********************************.setMarginBottom(0);
        ********************************.setSpacing(1);
        ********************************.setFlexVflex("ftFalse");
        ********************************.setFlexHflex("ftFalse");
        ********************************.setScrollable(false);
        ********************************.setBoxShadowConfigHorizontalLength(10);
        ********************************.setBoxShadowConfigVerticalLength(10);
        ********************************.setBoxShadowConfigBlurRadius(5);
        ********************************.setBoxShadowConfigSpreadRadius(0);
        ********************************.setBoxShadowConfigShadowColor("clBlack");
        ********************************.setBoxShadowConfigOpacity(75);
        ********************************.setVAlign("tvTop");
        hBoxSituacaoCadastral.addChildren(********************************);
        ********************************.applyProperties();
    }

    public TFLabel lblCadastroIrregular = new TFLabel();

    private void init_lblCadastroIrregular() {
        lblCadastroIrregular.setName("lblCadastroIrregular");
        lblCadastroIrregular.setLeft(95);
        lblCadastroIrregular.setTop(0);
        lblCadastroIrregular.setWidth(71);
        lblCadastroIrregular.setHeight(14);
        lblCadastroIrregular.setCaption("IRREGULAR");
        lblCadastroIrregular.setFontColor("clRed");
        lblCadastroIrregular.setFontSize(-12);
        lblCadastroIrregular.setFontName("Tahoma");
        lblCadastroIrregular.setFontStyle("[fsBold]");
        lblCadastroIrregular.setVisible(false);
        lblCadastroIrregular.setVerticalAlignment("taVerticalCenter");
        lblCadastroIrregular.setWordBreak(false);
        hBoxSituacaoCadastral.addChildren(lblCadastroIrregular);
        lblCadastroIrregular.applyProperties();
    }

    public TFHBox hBoxSituacaoCadReceitaFederal = new TFHBox();

    private void init_hBoxSituacaoCadReceitaFederal() {
        hBoxSituacaoCadReceitaFederal.setName("hBoxSituacaoCadReceitaFederal");
        hBoxSituacaoCadReceitaFederal.setLeft(0);
        hBoxSituacaoCadReceitaFederal.setTop(21);
        hBoxSituacaoCadReceitaFederal.setWidth(203);
        hBoxSituacaoCadReceitaFederal.setHeight(18);
        hBoxSituacaoCadReceitaFederal.setBorderStyle("stNone");
        hBoxSituacaoCadReceitaFederal.setPaddingTop(-1);
        hBoxSituacaoCadReceitaFederal.setPaddingLeft(0);
        hBoxSituacaoCadReceitaFederal.setPaddingRight(0);
        hBoxSituacaoCadReceitaFederal.setPaddingBottom(0);
        hBoxSituacaoCadReceitaFederal.setVisible(false);
        hBoxSituacaoCadReceitaFederal.setMarginTop(0);
        hBoxSituacaoCadReceitaFederal.setMarginLeft(0);
        hBoxSituacaoCadReceitaFederal.setMarginRight(0);
        hBoxSituacaoCadReceitaFederal.setMarginBottom(0);
        hBoxSituacaoCadReceitaFederal.setSpacing(1);
        hBoxSituacaoCadReceitaFederal.setFlexVflex("ftFalse");
        hBoxSituacaoCadReceitaFederal.setFlexHflex("ftFalse");
        hBoxSituacaoCadReceitaFederal.setScrollable(false);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigHorizontalLength(10);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigVerticalLength(10);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigBlurRadius(5);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigSpreadRadius(0);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigShadowColor("clBlack");
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigOpacity(75);
        hBoxSituacaoCadReceitaFederal.setVAlign("tvTop");
        vBoxStatusCadastro.addChildren(hBoxSituacaoCadReceitaFederal);
        hBoxSituacaoCadReceitaFederal.applyProperties();
    }

    public TFLabel lblSituacaoCadReceitaFederal = new TFLabel();

    private void init_lblSituacaoCadReceitaFederal() {
        lblSituacaoCadReceitaFederal.setName("lblSituacaoCadReceitaFederal");
        lblSituacaoCadReceitaFederal.setLeft(0);
        lblSituacaoCadReceitaFederal.setTop(0);
        lblSituacaoCadReceitaFederal.setWidth(82);
        lblSituacaoCadReceitaFederal.setHeight(13);
        lblSituacaoCadReceitaFederal.setCaption("Receita Federal (");
        lblSituacaoCadReceitaFederal.setFontColor("clWindowText");
        lblSituacaoCadReceitaFederal.setFontSize(-11);
        lblSituacaoCadReceitaFederal.setFontName("Tahoma");
        lblSituacaoCadReceitaFederal.setFontStyle("[]");
        lblSituacaoCadReceitaFederal.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadReceitaFederal.setWordBreak(false);
        hBoxSituacaoCadReceitaFederal.addChildren(lblSituacaoCadReceitaFederal);
        lblSituacaoCadReceitaFederal.applyProperties();
    }

    public TFLabel lblSituacaoReceitaFederal = new TFLabel();

    private void init_lblSituacaoReceitaFederal() {
        lblSituacaoReceitaFederal.setName("lblSituacaoReceitaFederal");
        lblSituacaoReceitaFederal.setLeft(82);
        lblSituacaoReceitaFederal.setTop(0);
        lblSituacaoReceitaFederal.setWidth(35);
        lblSituacaoReceitaFederal.setHeight(13);
        lblSituacaoReceitaFederal.setCaption("ATIVO");
        lblSituacaoReceitaFederal.setFontColor("clWindowText");
        lblSituacaoReceitaFederal.setFontSize(-11);
        lblSituacaoReceitaFederal.setFontName("Tahoma");
        lblSituacaoReceitaFederal.setFontStyle("[fsBold]");
        lblSituacaoReceitaFederal.setVerticalAlignment("taVerticalCenter");
        lblSituacaoReceitaFederal.setWordBreak(false);
        hBoxSituacaoCadReceitaFederal.addChildren(lblSituacaoReceitaFederal);
        lblSituacaoReceitaFederal.applyProperties();
    }

    public TFLabel lblSituacaoCadReceitaFederalFim = new TFLabel();

    private void init_lblSituacaoCadReceitaFederalFim() {
        lblSituacaoCadReceitaFederalFim.setName("lblSituacaoCadReceitaFederalFim");
        lblSituacaoCadReceitaFederalFim.setLeft(117);
        lblSituacaoCadReceitaFederalFim.setTop(0);
        lblSituacaoCadReceitaFederalFim.setWidth(4);
        lblSituacaoCadReceitaFederalFim.setHeight(13);
        lblSituacaoCadReceitaFederalFim.setCaption(")");
        lblSituacaoCadReceitaFederalFim.setFontColor("clWindowText");
        lblSituacaoCadReceitaFederalFim.setFontSize(-11);
        lblSituacaoCadReceitaFederalFim.setFontName("Tahoma");
        lblSituacaoCadReceitaFederalFim.setFontStyle("[]");
        lblSituacaoCadReceitaFederalFim.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadReceitaFederalFim.setWordBreak(false);
        hBoxSituacaoCadReceitaFederal.addChildren(lblSituacaoCadReceitaFederalFim);
        lblSituacaoCadReceitaFederalFim.applyProperties();
    }

    public TFHBox hBoxSituacaoCadSintegra = new TFHBox();

    private void init_hBoxSituacaoCadSintegra() {
        hBoxSituacaoCadSintegra.setName("hBoxSituacaoCadSintegra");
        hBoxSituacaoCadSintegra.setLeft(0);
        hBoxSituacaoCadSintegra.setTop(40);
        hBoxSituacaoCadSintegra.setWidth(201);
        hBoxSituacaoCadSintegra.setHeight(18);
        hBoxSituacaoCadSintegra.setBorderStyle("stNone");
        hBoxSituacaoCadSintegra.setPaddingTop(-1);
        hBoxSituacaoCadSintegra.setPaddingLeft(0);
        hBoxSituacaoCadSintegra.setPaddingRight(0);
        hBoxSituacaoCadSintegra.setPaddingBottom(0);
        hBoxSituacaoCadSintegra.setVisible(false);
        hBoxSituacaoCadSintegra.setMarginTop(0);
        hBoxSituacaoCadSintegra.setMarginLeft(0);
        hBoxSituacaoCadSintegra.setMarginRight(0);
        hBoxSituacaoCadSintegra.setMarginBottom(0);
        hBoxSituacaoCadSintegra.setSpacing(1);
        hBoxSituacaoCadSintegra.setFlexVflex("ftFalse");
        hBoxSituacaoCadSintegra.setFlexHflex("ftFalse");
        hBoxSituacaoCadSintegra.setScrollable(false);
        hBoxSituacaoCadSintegra.setBoxShadowConfigHorizontalLength(10);
        hBoxSituacaoCadSintegra.setBoxShadowConfigVerticalLength(10);
        hBoxSituacaoCadSintegra.setBoxShadowConfigBlurRadius(5);
        hBoxSituacaoCadSintegra.setBoxShadowConfigSpreadRadius(0);
        hBoxSituacaoCadSintegra.setBoxShadowConfigShadowColor("clBlack");
        hBoxSituacaoCadSintegra.setBoxShadowConfigOpacity(75);
        hBoxSituacaoCadSintegra.setVAlign("tvTop");
        vBoxStatusCadastro.addChildren(hBoxSituacaoCadSintegra);
        hBoxSituacaoCadSintegra.applyProperties();
    }

    public TFLabel lblSituacaoCadSintegra = new TFLabel();

    private void init_lblSituacaoCadSintegra() {
        lblSituacaoCadSintegra.setName("lblSituacaoCadSintegra");
        lblSituacaoCadSintegra.setLeft(0);
        lblSituacaoCadSintegra.setTop(0);
        lblSituacaoCadSintegra.setWidth(47);
        lblSituacaoCadSintegra.setHeight(13);
        lblSituacaoCadSintegra.setCaption("Sintegra (");
        lblSituacaoCadSintegra.setFontColor("clWindowText");
        lblSituacaoCadSintegra.setFontSize(-11);
        lblSituacaoCadSintegra.setFontName("Tahoma");
        lblSituacaoCadSintegra.setFontStyle("[]");
        lblSituacaoCadSintegra.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSituacaoCadSintegraClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "lblSituacaoCadSintegra", "OnClick");
        });
        lblSituacaoCadSintegra.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadSintegra.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSituacaoCadSintegra);
        lblSituacaoCadSintegra.applyProperties();
    }

    public TFLabel lblSituacaoSintegra = new TFLabel();

    private void init_lblSituacaoSintegra() {
        lblSituacaoSintegra.setName("lblSituacaoSintegra");
        lblSituacaoSintegra.setLeft(47);
        lblSituacaoSintegra.setTop(0);
        lblSituacaoSintegra.setWidth(57);
        lblSituacaoSintegra.setHeight(13);
        lblSituacaoSintegra.setCaption("Habilitada");
        lblSituacaoSintegra.setFontColor("clWindowText");
        lblSituacaoSintegra.setFontSize(-11);
        lblSituacaoSintegra.setFontName("Tahoma");
        lblSituacaoSintegra.setFontStyle("[fsBold]");
        lblSituacaoSintegra.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSituacaoSintegraClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "lblSituacaoSintegra", "OnClick");
        });
        lblSituacaoSintegra.setVerticalAlignment("taVerticalCenter");
        lblSituacaoSintegra.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSituacaoSintegra);
        lblSituacaoSintegra.applyProperties();
    }

    public TFLabel lblSituacaoCadSintegraFim = new TFLabel();

    private void init_lblSituacaoCadSintegraFim() {
        lblSituacaoCadSintegraFim.setName("lblSituacaoCadSintegraFim");
        lblSituacaoCadSintegraFim.setLeft(104);
        lblSituacaoCadSintegraFim.setTop(0);
        lblSituacaoCadSintegraFim.setWidth(4);
        lblSituacaoCadSintegraFim.setHeight(13);
        lblSituacaoCadSintegraFim.setCaption(")");
        lblSituacaoCadSintegraFim.setFontColor("clWindowText");
        lblSituacaoCadSintegraFim.setFontSize(-11);
        lblSituacaoCadSintegraFim.setFontName("Tahoma");
        lblSituacaoCadSintegraFim.setFontStyle("[]");
        lblSituacaoCadSintegraFim.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadSintegraFim.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSituacaoCadSintegraFim);
        lblSituacaoCadSintegraFim.applyProperties();
    }

    public TFLabel lblSintegraMultiIe = new TFLabel();

    private void init_lblSintegraMultiIe() {
        lblSintegraMultiIe.setName("lblSintegraMultiIe");
        lblSintegraMultiIe.setLeft(108);
        lblSintegraMultiIe.setTop(0);
        lblSintegraMultiIe.setWidth(37);
        lblSintegraMultiIe.setHeight(13);
        lblSintegraMultiIe.setCaption(" [+... ] ");
        lblSintegraMultiIe.setFontColor("clWindowText");
        lblSintegraMultiIe.setFontSize(-11);
        lblSintegraMultiIe.setFontName("Tahoma");
        lblSintegraMultiIe.setFontStyle("[fsBold]");
        lblSintegraMultiIe.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSintegraMultiIeClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "lblSintegraMultiIe", "OnClick");
        });
        lblSintegraMultiIe.setVerticalAlignment("taVerticalCenter");
        lblSintegraMultiIe.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSintegraMultiIe);
        lblSintegraMultiIe.applyProperties();
    }

    public TFHBox hBoxEditarLeadSeparador03 = new TFHBox();

    private void init_hBoxEditarLeadSeparador03() {
        hBoxEditarLeadSeparador03.setName("hBoxEditarLeadSeparador03");
        hBoxEditarLeadSeparador03.setLeft(426);
        hBoxEditarLeadSeparador03.setTop(0);
        hBoxEditarLeadSeparador03.setWidth(5);
        hBoxEditarLeadSeparador03.setHeight(20);
        hBoxEditarLeadSeparador03.setBorderStyle("stNone");
        hBoxEditarLeadSeparador03.setPaddingTop(0);
        hBoxEditarLeadSeparador03.setPaddingLeft(0);
        hBoxEditarLeadSeparador03.setPaddingRight(0);
        hBoxEditarLeadSeparador03.setPaddingBottom(0);
        hBoxEditarLeadSeparador03.setMarginTop(0);
        hBoxEditarLeadSeparador03.setMarginLeft(0);
        hBoxEditarLeadSeparador03.setMarginRight(0);
        hBoxEditarLeadSeparador03.setMarginBottom(0);
        hBoxEditarLeadSeparador03.setSpacing(1);
        hBoxEditarLeadSeparador03.setFlexVflex("ftFalse");
        hBoxEditarLeadSeparador03.setFlexHflex("ftFalse");
        hBoxEditarLeadSeparador03.setScrollable(false);
        hBoxEditarLeadSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxEditarLeadSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxEditarLeadSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxEditarLeadSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxEditarLeadSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxEditarLeadSeparador03.setBoxShadowConfigOpacity(75);
        hBoxEditarLeadSeparador03.setVAlign("tvTop");
        hBoxEditarLead.addChildren(hBoxEditarLeadSeparador03);
        hBoxEditarLeadSeparador03.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador02 = new TFHBox();

    private void init_hBoxPrincipalSeparador02() {
        hBoxPrincipalSeparador02.setName("hBoxPrincipalSeparador02");
        hBoxPrincipalSeparador02.setLeft(0);
        hBoxPrincipalSeparador02.setTop(77);
        hBoxPrincipalSeparador02.setWidth(150);
        hBoxPrincipalSeparador02.setHeight(5);
        hBoxPrincipalSeparador02.setBorderStyle("stNone");
        hBoxPrincipalSeparador02.setPaddingTop(0);
        hBoxPrincipalSeparador02.setPaddingLeft(0);
        hBoxPrincipalSeparador02.setPaddingRight(0);
        hBoxPrincipalSeparador02.setPaddingBottom(0);
        hBoxPrincipalSeparador02.setMarginTop(0);
        hBoxPrincipalSeparador02.setMarginLeft(0);
        hBoxPrincipalSeparador02.setMarginRight(0);
        hBoxPrincipalSeparador02.setMarginBottom(0);
        hBoxPrincipalSeparador02.setSpacing(1);
        hBoxPrincipalSeparador02.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador02.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador02.setScrollable(false);
        hBoxPrincipalSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador02.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador02.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador02);
        hBoxPrincipalSeparador02.applyProperties();
    }

    public TFHBox hBoxTipoDoEndereco = new TFHBox();

    private void init_hBoxTipoDoEndereco() {
        hBoxTipoDoEndereco.setName("hBoxTipoDoEndereco");
        hBoxTipoDoEndereco.setLeft(0);
        hBoxTipoDoEndereco.setTop(83);
        hBoxTipoDoEndereco.setWidth(150);
        hBoxTipoDoEndereco.setHeight(50);
        hBoxTipoDoEndereco.setBorderStyle("stNone");
        hBoxTipoDoEndereco.setPaddingTop(0);
        hBoxTipoDoEndereco.setPaddingLeft(0);
        hBoxTipoDoEndereco.setPaddingRight(0);
        hBoxTipoDoEndereco.setPaddingBottom(0);
        hBoxTipoDoEndereco.setMarginTop(0);
        hBoxTipoDoEndereco.setMarginLeft(0);
        hBoxTipoDoEndereco.setMarginRight(0);
        hBoxTipoDoEndereco.setMarginBottom(0);
        hBoxTipoDoEndereco.setSpacing(1);
        hBoxTipoDoEndereco.setFlexVflex("ftMin");
        hBoxTipoDoEndereco.setFlexHflex("ftTrue");
        hBoxTipoDoEndereco.setScrollable(false);
        hBoxTipoDoEndereco.setBoxShadowConfigHorizontalLength(10);
        hBoxTipoDoEndereco.setBoxShadowConfigVerticalLength(10);
        hBoxTipoDoEndereco.setBoxShadowConfigBlurRadius(5);
        hBoxTipoDoEndereco.setBoxShadowConfigSpreadRadius(0);
        hBoxTipoDoEndereco.setBoxShadowConfigShadowColor("clBlack");
        hBoxTipoDoEndereco.setBoxShadowConfigOpacity(75);
        hBoxTipoDoEndereco.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxTipoDoEndereco);
        hBoxTipoDoEndereco.applyProperties();
    }

    public TFHBox hBoxTipoDoEnderecoSeparador01 = new TFHBox();

    private void init_hBoxTipoDoEnderecoSeparador01() {
        hBoxTipoDoEnderecoSeparador01.setName("hBoxTipoDoEnderecoSeparador01");
        hBoxTipoDoEnderecoSeparador01.setLeft(0);
        hBoxTipoDoEnderecoSeparador01.setTop(0);
        hBoxTipoDoEnderecoSeparador01.setWidth(5);
        hBoxTipoDoEnderecoSeparador01.setHeight(20);
        hBoxTipoDoEnderecoSeparador01.setBorderStyle("stNone");
        hBoxTipoDoEnderecoSeparador01.setPaddingTop(0);
        hBoxTipoDoEnderecoSeparador01.setPaddingLeft(0);
        hBoxTipoDoEnderecoSeparador01.setPaddingRight(0);
        hBoxTipoDoEnderecoSeparador01.setPaddingBottom(0);
        hBoxTipoDoEnderecoSeparador01.setMarginTop(0);
        hBoxTipoDoEnderecoSeparador01.setMarginLeft(0);
        hBoxTipoDoEnderecoSeparador01.setMarginRight(0);
        hBoxTipoDoEnderecoSeparador01.setMarginBottom(0);
        hBoxTipoDoEnderecoSeparador01.setSpacing(1);
        hBoxTipoDoEnderecoSeparador01.setFlexVflex("ftFalse");
        hBoxTipoDoEnderecoSeparador01.setFlexHflex("ftFalse");
        hBoxTipoDoEnderecoSeparador01.setScrollable(false);
        hBoxTipoDoEnderecoSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxTipoDoEnderecoSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxTipoDoEnderecoSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxTipoDoEnderecoSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxTipoDoEnderecoSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxTipoDoEnderecoSeparador01.setBoxShadowConfigOpacity(75);
        hBoxTipoDoEnderecoSeparador01.setVAlign("tvTop");
        hBoxTipoDoEndereco.addChildren(hBoxTipoDoEnderecoSeparador01);
        hBoxTipoDoEnderecoSeparador01.applyProperties();
    }

    public TFVBox vBoxTipoDoEndereco = new TFVBox();

    private void init_vBoxTipoDoEndereco() {
        vBoxTipoDoEndereco.setName("vBoxTipoDoEndereco");
        vBoxTipoDoEndereco.setLeft(5);
        vBoxTipoDoEndereco.setTop(0);
        vBoxTipoDoEndereco.setWidth(130);
        vBoxTipoDoEndereco.setHeight(45);
        vBoxTipoDoEndereco.setBorderStyle("stNone");
        vBoxTipoDoEndereco.setPaddingTop(0);
        vBoxTipoDoEndereco.setPaddingLeft(0);
        vBoxTipoDoEndereco.setPaddingRight(0);
        vBoxTipoDoEndereco.setPaddingBottom(0);
        vBoxTipoDoEndereco.setMarginTop(0);
        vBoxTipoDoEndereco.setMarginLeft(0);
        vBoxTipoDoEndereco.setMarginRight(0);
        vBoxTipoDoEndereco.setMarginBottom(0);
        vBoxTipoDoEndereco.setSpacing(1);
        vBoxTipoDoEndereco.setFlexVflex("ftMin");
        vBoxTipoDoEndereco.setFlexHflex("ftTrue");
        vBoxTipoDoEndereco.setScrollable(false);
        vBoxTipoDoEndereco.setBoxShadowConfigHorizontalLength(10);
        vBoxTipoDoEndereco.setBoxShadowConfigVerticalLength(10);
        vBoxTipoDoEndereco.setBoxShadowConfigBlurRadius(5);
        vBoxTipoDoEndereco.setBoxShadowConfigSpreadRadius(0);
        vBoxTipoDoEndereco.setBoxShadowConfigShadowColor("clBlack");
        vBoxTipoDoEndereco.setBoxShadowConfigOpacity(75);
        hBoxTipoDoEndereco.addChildren(vBoxTipoDoEndereco);
        vBoxTipoDoEndereco.applyProperties();
    }

    public TFLabel lblTipoDoEndereco = new TFLabel();

    private void init_lblTipoDoEndereco() {
        lblTipoDoEndereco.setName("lblTipoDoEndereco");
        lblTipoDoEndereco.setLeft(0);
        lblTipoDoEndereco.setTop(0);
        lblTipoDoEndereco.setWidth(97);
        lblTipoDoEndereco.setHeight(13);
        lblTipoDoEndereco.setCaption("Tipo do endere\u00E7o");
        lblTipoDoEndereco.setFontColor("clWindowText");
        lblTipoDoEndereco.setFontSize(-11);
        lblTipoDoEndereco.setFontName("Tahoma");
        lblTipoDoEndereco.setFontStyle("[fsBold]");
        lblTipoDoEndereco.setVerticalAlignment("taVerticalCenter");
        lblTipoDoEndereco.setWordBreak(false);
        vBoxTipoDoEndereco.addChildren(lblTipoDoEndereco);
        lblTipoDoEndereco.applyProperties();
    }

    public TFString edtTipoDoEndereco = new TFString();

    private void init_edtTipoDoEndereco() {
        edtTipoDoEndereco.setName("edtTipoDoEndereco");
        edtTipoDoEndereco.setLeft(0);
        edtTipoDoEndereco.setTop(14);
        edtTipoDoEndereco.setWidth(120);
        edtTipoDoEndereco.setHeight(27);
        edtTipoDoEndereco.setHint("Tipo do endere\u00E7o");
        edtTipoDoEndereco.setHelpCaption("Tipo do endere\u00E7o");
        edtTipoDoEndereco.setFlex(true);
        edtTipoDoEndereco.setRequired(false);
        edtTipoDoEndereco.setPrompt("Tipo do endere\u00E7o");
        edtTipoDoEndereco.setConstraintCheckWhen("cwImmediate");
        edtTipoDoEndereco.setConstraintCheckType("ctExpression");
        edtTipoDoEndereco.setConstraintFocusOnError(false);
        edtTipoDoEndereco.setConstraintEnableUI(true);
        edtTipoDoEndereco.setConstraintEnabled(false);
        edtTipoDoEndereco.setConstraintFormCheck(true);
        edtTipoDoEndereco.setCharCase("ccNormal");
        edtTipoDoEndereco.setPwd(false);
        edtTipoDoEndereco.setMaxlength(0);
        edtTipoDoEndereco.setAlign("alLeft");
        edtTipoDoEndereco.setEnabled(false);
        edtTipoDoEndereco.setFontColor("clWindowText");
        edtTipoDoEndereco.setFontSize(-16);
        edtTipoDoEndereco.setFontName("Tahoma");
        edtTipoDoEndereco.setFontStyle("[]");
        edtTipoDoEndereco.setSaveLiteralCharacter(false);
        edtTipoDoEndereco.applyProperties();
        vBoxTipoDoEndereco.addChildren(edtTipoDoEndereco);
        addValidatable(edtTipoDoEndereco);
    }

    public TFHBox hBoxTipoDoEnderecoSeparador02 = new TFHBox();

    private void init_hBoxTipoDoEnderecoSeparador02() {
        hBoxTipoDoEnderecoSeparador02.setName("hBoxTipoDoEnderecoSeparador02");
        hBoxTipoDoEnderecoSeparador02.setLeft(135);
        hBoxTipoDoEnderecoSeparador02.setTop(0);
        hBoxTipoDoEnderecoSeparador02.setWidth(5);
        hBoxTipoDoEnderecoSeparador02.setHeight(20);
        hBoxTipoDoEnderecoSeparador02.setBorderStyle("stNone");
        hBoxTipoDoEnderecoSeparador02.setPaddingTop(0);
        hBoxTipoDoEnderecoSeparador02.setPaddingLeft(0);
        hBoxTipoDoEnderecoSeparador02.setPaddingRight(0);
        hBoxTipoDoEnderecoSeparador02.setPaddingBottom(0);
        hBoxTipoDoEnderecoSeparador02.setMarginTop(0);
        hBoxTipoDoEnderecoSeparador02.setMarginLeft(0);
        hBoxTipoDoEnderecoSeparador02.setMarginRight(0);
        hBoxTipoDoEnderecoSeparador02.setMarginBottom(0);
        hBoxTipoDoEnderecoSeparador02.setSpacing(1);
        hBoxTipoDoEnderecoSeparador02.setFlexVflex("ftFalse");
        hBoxTipoDoEnderecoSeparador02.setFlexHflex("ftFalse");
        hBoxTipoDoEnderecoSeparador02.setScrollable(false);
        hBoxTipoDoEnderecoSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxTipoDoEnderecoSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxTipoDoEnderecoSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxTipoDoEnderecoSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxTipoDoEnderecoSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxTipoDoEnderecoSeparador02.setBoxShadowConfigOpacity(75);
        hBoxTipoDoEnderecoSeparador02.setVAlign("tvTop");
        hBoxTipoDoEndereco.addChildren(hBoxTipoDoEnderecoSeparador02);
        hBoxTipoDoEnderecoSeparador02.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador04 = new TFHBox();

    private void init_hBoxPrincipalSeparador04() {
        hBoxPrincipalSeparador04.setName("hBoxPrincipalSeparador04");
        hBoxPrincipalSeparador04.setLeft(0);
        hBoxPrincipalSeparador04.setTop(134);
        hBoxPrincipalSeparador04.setWidth(150);
        hBoxPrincipalSeparador04.setHeight(5);
        hBoxPrincipalSeparador04.setBorderStyle("stNone");
        hBoxPrincipalSeparador04.setPaddingTop(0);
        hBoxPrincipalSeparador04.setPaddingLeft(0);
        hBoxPrincipalSeparador04.setPaddingRight(0);
        hBoxPrincipalSeparador04.setPaddingBottom(0);
        hBoxPrincipalSeparador04.setMarginTop(0);
        hBoxPrincipalSeparador04.setMarginLeft(0);
        hBoxPrincipalSeparador04.setMarginRight(0);
        hBoxPrincipalSeparador04.setMarginBottom(0);
        hBoxPrincipalSeparador04.setSpacing(1);
        hBoxPrincipalSeparador04.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador04.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador04.setScrollable(false);
        hBoxPrincipalSeparador04.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador04.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador04.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador04.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador04.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador04.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador04.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador04);
        hBoxPrincipalSeparador04.applyProperties();
    }

    public TFHBox hBoxNomeDaPropriedade = new TFHBox();

    private void init_hBoxNomeDaPropriedade() {
        hBoxNomeDaPropriedade.setName("hBoxNomeDaPropriedade");
        hBoxNomeDaPropriedade.setLeft(0);
        hBoxNomeDaPropriedade.setTop(140);
        hBoxNomeDaPropriedade.setWidth(150);
        hBoxNomeDaPropriedade.setHeight(50);
        hBoxNomeDaPropriedade.setBorderStyle("stNone");
        hBoxNomeDaPropriedade.setPaddingTop(0);
        hBoxNomeDaPropriedade.setPaddingLeft(0);
        hBoxNomeDaPropriedade.setPaddingRight(0);
        hBoxNomeDaPropriedade.setPaddingBottom(0);
        hBoxNomeDaPropriedade.setMarginTop(0);
        hBoxNomeDaPropriedade.setMarginLeft(0);
        hBoxNomeDaPropriedade.setMarginRight(0);
        hBoxNomeDaPropriedade.setMarginBottom(0);
        hBoxNomeDaPropriedade.setSpacing(1);
        hBoxNomeDaPropriedade.setFlexVflex("ftMin");
        hBoxNomeDaPropriedade.setFlexHflex("ftTrue");
        hBoxNomeDaPropriedade.setScrollable(false);
        hBoxNomeDaPropriedade.setBoxShadowConfigHorizontalLength(10);
        hBoxNomeDaPropriedade.setBoxShadowConfigVerticalLength(10);
        hBoxNomeDaPropriedade.setBoxShadowConfigBlurRadius(5);
        hBoxNomeDaPropriedade.setBoxShadowConfigSpreadRadius(0);
        hBoxNomeDaPropriedade.setBoxShadowConfigShadowColor("clBlack");
        hBoxNomeDaPropriedade.setBoxShadowConfigOpacity(75);
        hBoxNomeDaPropriedade.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxNomeDaPropriedade);
        hBoxNomeDaPropriedade.applyProperties();
    }

    public TFHBox ******************************** = new TFHBox();

    private void init_********************************() {
        ********************************.setName("********************************");
        ********************************.setLeft(0);
        ********************************.setTop(0);
        ********************************.setWidth(5);
        ********************************.setHeight(20);
        ********************************.setBorderStyle("stNone");
        ********************************.setPaddingTop(0);
        ********************************.setPaddingLeft(0);
        ********************************.setPaddingRight(0);
        ********************************.setPaddingBottom(0);
        ********************************.setMarginTop(0);
        ********************************.setMarginLeft(0);
        ********************************.setMarginRight(0);
        ********************************.setMarginBottom(0);
        ********************************.setSpacing(1);
        ********************************.setFlexVflex("ftFalse");
        ********************************.setFlexHflex("ftFalse");
        ********************************.setScrollable(false);
        ********************************.setBoxShadowConfigHorizontalLength(10);
        ********************************.setBoxShadowConfigVerticalLength(10);
        ********************************.setBoxShadowConfigBlurRadius(5);
        ********************************.setBoxShadowConfigSpreadRadius(0);
        ********************************.setBoxShadowConfigShadowColor("clBlack");
        ********************************.setBoxShadowConfigOpacity(75);
        ********************************.setVAlign("tvTop");
        hBoxNomeDaPropriedade.addChildren(********************************);
        ********************************.applyProperties();
    }

    public TFVBox vBoxNomeDaPropriedade = new TFVBox();

    private void init_vBoxNomeDaPropriedade() {
        vBoxNomeDaPropriedade.setName("vBoxNomeDaPropriedade");
        vBoxNomeDaPropriedade.setLeft(5);
        vBoxNomeDaPropriedade.setTop(0);
        vBoxNomeDaPropriedade.setWidth(130);
        vBoxNomeDaPropriedade.setHeight(45);
        vBoxNomeDaPropriedade.setBorderStyle("stNone");
        vBoxNomeDaPropriedade.setPaddingTop(0);
        vBoxNomeDaPropriedade.setPaddingLeft(0);
        vBoxNomeDaPropriedade.setPaddingRight(0);
        vBoxNomeDaPropriedade.setPaddingBottom(0);
        vBoxNomeDaPropriedade.setMarginTop(0);
        vBoxNomeDaPropriedade.setMarginLeft(0);
        vBoxNomeDaPropriedade.setMarginRight(0);
        vBoxNomeDaPropriedade.setMarginBottom(0);
        vBoxNomeDaPropriedade.setSpacing(1);
        vBoxNomeDaPropriedade.setFlexVflex("ftMin");
        vBoxNomeDaPropriedade.setFlexHflex("ftTrue");
        vBoxNomeDaPropriedade.setScrollable(false);
        vBoxNomeDaPropriedade.setBoxShadowConfigHorizontalLength(10);
        vBoxNomeDaPropriedade.setBoxShadowConfigVerticalLength(10);
        vBoxNomeDaPropriedade.setBoxShadowConfigBlurRadius(5);
        vBoxNomeDaPropriedade.setBoxShadowConfigSpreadRadius(0);
        vBoxNomeDaPropriedade.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomeDaPropriedade.setBoxShadowConfigOpacity(75);
        hBoxNomeDaPropriedade.addChildren(vBoxNomeDaPropriedade);
        vBoxNomeDaPropriedade.applyProperties();
    }

    public TFLabel lblNomeDaPropriedade = new TFLabel();

    private void init_lblNomeDaPropriedade() {
        lblNomeDaPropriedade.setName("lblNomeDaPropriedade");
        lblNomeDaPropriedade.setLeft(0);
        lblNomeDaPropriedade.setTop(0);
        lblNomeDaPropriedade.setWidth(121);
        lblNomeDaPropriedade.setHeight(13);
        lblNomeDaPropriedade.setCaption("Nome da propriedade");
        lblNomeDaPropriedade.setFontColor("clWindowText");
        lblNomeDaPropriedade.setFontSize(-11);
        lblNomeDaPropriedade.setFontName("Tahoma");
        lblNomeDaPropriedade.setFontStyle("[fsBold]");
        lblNomeDaPropriedade.setVerticalAlignment("taVerticalCenter");
        lblNomeDaPropriedade.setWordBreak(false);
        vBoxNomeDaPropriedade.addChildren(lblNomeDaPropriedade);
        lblNomeDaPropriedade.applyProperties();
    }

    public TFString edtNomePropriedade = new TFString();

    private void init_edtNomePropriedade() {
        edtNomePropriedade.setName("edtNomePropriedade");
        edtNomePropriedade.setLeft(0);
        edtNomePropriedade.setTop(14);
        edtNomePropriedade.setWidth(120);
        edtNomePropriedade.setHeight(24);
        edtNomePropriedade.setHint("Nome da propriedade");
        edtNomePropriedade.setHelpCaption("Nome da propriedade");
        edtNomePropriedade.setFlex(true);
        edtNomePropriedade.setRequired(false);
        edtNomePropriedade.setPrompt("Nome da propriedade");
        edtNomePropriedade.setConstraintCheckWhen("cwImmediate");
        edtNomePropriedade.setConstraintCheckType("ctExpression");
        edtNomePropriedade.setConstraintFocusOnError(false);
        edtNomePropriedade.setConstraintEnableUI(true);
        edtNomePropriedade.setConstraintEnabled(false);
        edtNomePropriedade.setConstraintFormCheck(true);
        edtNomePropriedade.setCharCase("ccNormal");
        edtNomePropriedade.setPwd(false);
        edtNomePropriedade.setMaxlength(100);
        edtNomePropriedade.setAlign("alLeft");
        edtNomePropriedade.setFontColor("clWindowText");
        edtNomePropriedade.setFontSize(-13);
        edtNomePropriedade.setFontName("Tahoma");
        edtNomePropriedade.setFontStyle("[]");
        edtNomePropriedade.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "edtNomePropriedade", "OnEnter");
        });
        edtNomePropriedade.setSaveLiteralCharacter(false);
        edtNomePropriedade.applyProperties();
        vBoxNomeDaPropriedade.addChildren(edtNomePropriedade);
        addValidatable(edtNomePropriedade);
    }

    public TFHBox ******************************** = new TFHBox();

    private void init_********************************() {
        ********************************.setName("********************************");
        ********************************.setLeft(135);
        ********************************.setTop(0);
        ********************************.setWidth(5);
        ********************************.setHeight(20);
        ********************************.setBorderStyle("stNone");
        ********************************.setPaddingTop(0);
        ********************************.setPaddingLeft(0);
        ********************************.setPaddingRight(0);
        ********************************.setPaddingBottom(0);
        ********************************.setMarginTop(0);
        ********************************.setMarginLeft(0);
        ********************************.setMarginRight(0);
        ********************************.setMarginBottom(0);
        ********************************.setSpacing(1);
        ********************************.setFlexVflex("ftFalse");
        ********************************.setFlexHflex("ftFalse");
        ********************************.setScrollable(false);
        ********************************.setBoxShadowConfigHorizontalLength(10);
        ********************************.setBoxShadowConfigVerticalLength(10);
        ********************************.setBoxShadowConfigBlurRadius(5);
        ********************************.setBoxShadowConfigSpreadRadius(0);
        ********************************.setBoxShadowConfigShadowColor("clBlack");
        ********************************.setBoxShadowConfigOpacity(75);
        ********************************.setVAlign("tvTop");
        hBoxNomeDaPropriedade.addChildren(********************************);
        ********************************.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador05 = new TFHBox();

    private void init_hBoxPrincipalSeparador05() {
        hBoxPrincipalSeparador05.setName("hBoxPrincipalSeparador05");
        hBoxPrincipalSeparador05.setLeft(0);
        hBoxPrincipalSeparador05.setTop(191);
        hBoxPrincipalSeparador05.setWidth(150);
        hBoxPrincipalSeparador05.setHeight(5);
        hBoxPrincipalSeparador05.setBorderStyle("stNone");
        hBoxPrincipalSeparador05.setPaddingTop(0);
        hBoxPrincipalSeparador05.setPaddingLeft(0);
        hBoxPrincipalSeparador05.setPaddingRight(0);
        hBoxPrincipalSeparador05.setPaddingBottom(0);
        hBoxPrincipalSeparador05.setMarginTop(0);
        hBoxPrincipalSeparador05.setMarginLeft(0);
        hBoxPrincipalSeparador05.setMarginRight(0);
        hBoxPrincipalSeparador05.setMarginBottom(0);
        hBoxPrincipalSeparador05.setSpacing(1);
        hBoxPrincipalSeparador05.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador05.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador05.setScrollable(false);
        hBoxPrincipalSeparador05.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador05.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador05.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador05.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador05.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador05.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador05.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador05);
        hBoxPrincipalSeparador05.applyProperties();
    }

    public TFHBox hBoxUFCidadeCEP = new TFHBox();

    private void init_hBoxUFCidadeCEP() {
        hBoxUFCidadeCEP.setName("hBoxUFCidadeCEP");
        hBoxUFCidadeCEP.setLeft(0);
        hBoxUFCidadeCEP.setTop(197);
        hBoxUFCidadeCEP.setWidth(380);
        hBoxUFCidadeCEP.setHeight(50);
        hBoxUFCidadeCEP.setBorderStyle("stNone");
        hBoxUFCidadeCEP.setPaddingTop(0);
        hBoxUFCidadeCEP.setPaddingLeft(0);
        hBoxUFCidadeCEP.setPaddingRight(0);
        hBoxUFCidadeCEP.setPaddingBottom(0);
        hBoxUFCidadeCEP.setMarginTop(0);
        hBoxUFCidadeCEP.setMarginLeft(0);
        hBoxUFCidadeCEP.setMarginRight(0);
        hBoxUFCidadeCEP.setMarginBottom(0);
        hBoxUFCidadeCEP.setSpacing(1);
        hBoxUFCidadeCEP.setFlexVflex("ftMin");
        hBoxUFCidadeCEP.setFlexHflex("ftTrue");
        hBoxUFCidadeCEP.setScrollable(false);
        hBoxUFCidadeCEP.setBoxShadowConfigHorizontalLength(10);
        hBoxUFCidadeCEP.setBoxShadowConfigVerticalLength(10);
        hBoxUFCidadeCEP.setBoxShadowConfigBlurRadius(5);
        hBoxUFCidadeCEP.setBoxShadowConfigSpreadRadius(0);
        hBoxUFCidadeCEP.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFCidadeCEP.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEP.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxUFCidadeCEP);
        hBoxUFCidadeCEP.applyProperties();
    }

    public TFHBox hBoxUFCidadeCEPSeparador01 = new TFHBox();

    private void init_hBoxUFCidadeCEPSeparador01() {
        hBoxUFCidadeCEPSeparador01.setName("hBoxUFCidadeCEPSeparador01");
        hBoxUFCidadeCEPSeparador01.setLeft(0);
        hBoxUFCidadeCEPSeparador01.setTop(0);
        hBoxUFCidadeCEPSeparador01.setWidth(5);
        hBoxUFCidadeCEPSeparador01.setHeight(20);
        hBoxUFCidadeCEPSeparador01.setBorderStyle("stNone");
        hBoxUFCidadeCEPSeparador01.setPaddingTop(0);
        hBoxUFCidadeCEPSeparador01.setPaddingLeft(0);
        hBoxUFCidadeCEPSeparador01.setPaddingRight(0);
        hBoxUFCidadeCEPSeparador01.setPaddingBottom(0);
        hBoxUFCidadeCEPSeparador01.setMarginTop(0);
        hBoxUFCidadeCEPSeparador01.setMarginLeft(0);
        hBoxUFCidadeCEPSeparador01.setMarginRight(0);
        hBoxUFCidadeCEPSeparador01.setMarginBottom(0);
        hBoxUFCidadeCEPSeparador01.setSpacing(1);
        hBoxUFCidadeCEPSeparador01.setFlexVflex("ftFalse");
        hBoxUFCidadeCEPSeparador01.setFlexHflex("ftFalse");
        hBoxUFCidadeCEPSeparador01.setScrollable(false);
        hBoxUFCidadeCEPSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxUFCidadeCEPSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxUFCidadeCEPSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxUFCidadeCEPSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxUFCidadeCEPSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFCidadeCEPSeparador01.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEPSeparador01.setVAlign("tvTop");
        hBoxUFCidadeCEP.addChildren(hBoxUFCidadeCEPSeparador01);
        hBoxUFCidadeCEPSeparador01.applyProperties();
    }

    public TFVBox vBoxUF = new TFVBox();

    private void init_vBoxUF() {
        vBoxUF.setName("vBoxUF");
        vBoxUF.setLeft(5);
        vBoxUF.setTop(0);
        vBoxUF.setWidth(140);
        vBoxUF.setHeight(40);
        vBoxUF.setBorderStyle("stNone");
        vBoxUF.setPaddingTop(0);
        vBoxUF.setPaddingLeft(0);
        vBoxUF.setPaddingRight(0);
        vBoxUF.setPaddingBottom(0);
        vBoxUF.setMarginTop(0);
        vBoxUF.setMarginLeft(0);
        vBoxUF.setMarginRight(0);
        vBoxUF.setMarginBottom(0);
        vBoxUF.setSpacing(1);
        vBoxUF.setFlexVflex("ftMin");
        vBoxUF.setFlexHflex("ftMin");
        vBoxUF.setScrollable(false);
        vBoxUF.setBoxShadowConfigHorizontalLength(10);
        vBoxUF.setBoxShadowConfigVerticalLength(10);
        vBoxUF.setBoxShadowConfigBlurRadius(5);
        vBoxUF.setBoxShadowConfigSpreadRadius(0);
        vBoxUF.setBoxShadowConfigShadowColor("clBlack");
        vBoxUF.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEP.addChildren(vBoxUF);
        vBoxUF.applyProperties();
    }

    public TFLabel lblUF = new TFLabel();

    private void init_lblUF() {
        lblUF.setName("lblUF");
        lblUF.setLeft(0);
        lblUF.setTop(0);
        lblUF.setWidth(14);
        lblUF.setHeight(13);
        lblUF.setCaption("UF");
        lblUF.setFontColor("clWindowText");
        lblUF.setFontSize(-11);
        lblUF.setFontName("Tahoma");
        lblUF.setFontStyle("[fsBold]");
        lblUF.setVerticalAlignment("taVerticalCenter");
        lblUF.setWordBreak(false);
        vBoxUF.addChildren(lblUF);
        lblUF.applyProperties();
    }

    public TFCombo cboUF = new TFCombo();

    private void init_cboUF() {
        cboUF.setName("cboUF");
        cboUF.setLeft(0);
        cboUF.setTop(14);
        cboUF.setWidth(130);
        cboUF.setHeight(21);
        cboUF.setHint("UF");
        cboUF.setLookupTable(tbUf);
        cboUF.setLookupKey("UF");
        cboUF.setLookupDesc("UF");
        cboUF.setFlex(false);
        cboUF.setHelpCaption("UF");
        cboUF.setReadOnly(false);
        cboUF.setRequired(false);
        cboUF.setPrompt("UF");
        cboUF.setConstraintCheckWhen("cwImmediate");
        cboUF.setConstraintCheckType("ctExpression");
        cboUF.setConstraintFocusOnError(false);
        cboUF.setConstraintEnableUI(true);
        cboUF.setConstraintEnabled(false);
        cboUF.setConstraintFormCheck(true);
        cboUF.setClearOnDelKey(false);
        cboUF.setUseClearButton(true);
        cboUF.setHideClearButtonOnNullValue(true);
        cboUF.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboUFChange(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "cboUF", "OnChange");
        });
        cboUF.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "cboUF", "OnEnter");
        });
        cboUF.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboUFExit(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "cboUF", "OnExit");
        });
        cboUF.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboUFClearClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "cboUF", "OnClearClick");
        });
        vBoxUF.addChildren(cboUF);
        cboUF.applyProperties();
        addValidatable(cboUF);
    }

    public TFHBox hBoxUFCidadeCEPSeparador02 = new TFHBox();

    private void init_hBoxUFCidadeCEPSeparador02() {
        hBoxUFCidadeCEPSeparador02.setName("hBoxUFCidadeCEPSeparador02");
        hBoxUFCidadeCEPSeparador02.setLeft(145);
        hBoxUFCidadeCEPSeparador02.setTop(0);
        hBoxUFCidadeCEPSeparador02.setWidth(5);
        hBoxUFCidadeCEPSeparador02.setHeight(20);
        hBoxUFCidadeCEPSeparador02.setBorderStyle("stNone");
        hBoxUFCidadeCEPSeparador02.setPaddingTop(0);
        hBoxUFCidadeCEPSeparador02.setPaddingLeft(0);
        hBoxUFCidadeCEPSeparador02.setPaddingRight(0);
        hBoxUFCidadeCEPSeparador02.setPaddingBottom(0);
        hBoxUFCidadeCEPSeparador02.setMarginTop(0);
        hBoxUFCidadeCEPSeparador02.setMarginLeft(0);
        hBoxUFCidadeCEPSeparador02.setMarginRight(0);
        hBoxUFCidadeCEPSeparador02.setMarginBottom(0);
        hBoxUFCidadeCEPSeparador02.setSpacing(1);
        hBoxUFCidadeCEPSeparador02.setFlexVflex("ftFalse");
        hBoxUFCidadeCEPSeparador02.setFlexHflex("ftFalse");
        hBoxUFCidadeCEPSeparador02.setScrollable(false);
        hBoxUFCidadeCEPSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxUFCidadeCEPSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxUFCidadeCEPSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxUFCidadeCEPSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxUFCidadeCEPSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFCidadeCEPSeparador02.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEPSeparador02.setVAlign("tvTop");
        hBoxUFCidadeCEP.addChildren(hBoxUFCidadeCEPSeparador02);
        hBoxUFCidadeCEPSeparador02.applyProperties();
    }

    public TFVBox vBoxCidade = new TFVBox();

    private void init_vBoxCidade() {
        vBoxCidade.setName("vBoxCidade");
        vBoxCidade.setLeft(150);
        vBoxCidade.setTop(0);
        vBoxCidade.setWidth(130);
        vBoxCidade.setHeight(40);
        vBoxCidade.setBorderStyle("stNone");
        vBoxCidade.setPaddingTop(0);
        vBoxCidade.setPaddingLeft(0);
        vBoxCidade.setPaddingRight(0);
        vBoxCidade.setPaddingBottom(0);
        vBoxCidade.setMarginTop(0);
        vBoxCidade.setMarginLeft(0);
        vBoxCidade.setMarginRight(0);
        vBoxCidade.setMarginBottom(0);
        vBoxCidade.setSpacing(1);
        vBoxCidade.setFlexVflex("ftMin");
        vBoxCidade.setFlexHflex("ftTrue");
        vBoxCidade.setScrollable(false);
        vBoxCidade.setBoxShadowConfigHorizontalLength(10);
        vBoxCidade.setBoxShadowConfigVerticalLength(10);
        vBoxCidade.setBoxShadowConfigBlurRadius(5);
        vBoxCidade.setBoxShadowConfigSpreadRadius(0);
        vBoxCidade.setBoxShadowConfigShadowColor("clBlack");
        vBoxCidade.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEP.addChildren(vBoxCidade);
        vBoxCidade.applyProperties();
    }

    public TFLabel lblCidade = new TFLabel();

    private void init_lblCidade() {
        lblCidade.setName("lblCidade");
        lblCidade.setLeft(0);
        lblCidade.setTop(0);
        lblCidade.setWidth(38);
        lblCidade.setHeight(13);
        lblCidade.setCaption("Cidade");
        lblCidade.setFontColor("clWindowText");
        lblCidade.setFontSize(-11);
        lblCidade.setFontName("Tahoma");
        lblCidade.setFontStyle("[fsBold]");
        lblCidade.setVerticalAlignment("taVerticalCenter");
        lblCidade.setWordBreak(false);
        vBoxCidade.addChildren(lblCidade);
        lblCidade.applyProperties();
    }

    public TFCombo cboCidade = new TFCombo();

    private void init_cboCidade() {
        cboCidade.setName("cboCidade");
        cboCidade.setLeft(0);
        cboCidade.setTop(14);
        cboCidade.setWidth(120);
        cboCidade.setHeight(21);
        cboCidade.setHint("Cidade");
        cboCidade.setLookupTable(tbCidades);
        cboCidade.setLookupKey("COD_CIDADES");
        cboCidade.setLookupDesc("DESCRICAO");
        cboCidade.setFlex(true);
        cboCidade.setHelpCaption("Cidade");
        cboCidade.setReadOnly(false);
        cboCidade.setRequired(false);
        cboCidade.setPrompt("Cidade");
        cboCidade.setConstraintCheckWhen("cwImmediate");
        cboCidade.setConstraintCheckType("ctExpression");
        cboCidade.setConstraintFocusOnError(false);
        cboCidade.setConstraintEnableUI(true);
        cboCidade.setConstraintEnabled(false);
        cboCidade.setConstraintFormCheck(true);
        cboCidade.setClearOnDelKey(false);
        cboCidade.setUseClearButton(true);
        cboCidade.setHideClearButtonOnNullValue(true);
        cboCidade.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboCidadeChange(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "cboCidade", "OnChange");
        });
        cboCidade.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "cboCidade", "OnEnter");
        });
        cboCidade.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbCidadeExit(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "cboCidade", "OnExit");
        });
        vBoxCidade.addChildren(cboCidade);
        cboCidade.applyProperties();
        addValidatable(cboCidade);
    }

    public TFHBox hBoxUFCidadeCEPSeparador03 = new TFHBox();

    private void init_hBoxUFCidadeCEPSeparador03() {
        hBoxUFCidadeCEPSeparador03.setName("hBoxUFCidadeCEPSeparador03");
        hBoxUFCidadeCEPSeparador03.setLeft(280);
        hBoxUFCidadeCEPSeparador03.setTop(0);
        hBoxUFCidadeCEPSeparador03.setWidth(5);
        hBoxUFCidadeCEPSeparador03.setHeight(20);
        hBoxUFCidadeCEPSeparador03.setBorderStyle("stNone");
        hBoxUFCidadeCEPSeparador03.setPaddingTop(0);
        hBoxUFCidadeCEPSeparador03.setPaddingLeft(0);
        hBoxUFCidadeCEPSeparador03.setPaddingRight(0);
        hBoxUFCidadeCEPSeparador03.setPaddingBottom(0);
        hBoxUFCidadeCEPSeparador03.setMarginTop(0);
        hBoxUFCidadeCEPSeparador03.setMarginLeft(0);
        hBoxUFCidadeCEPSeparador03.setMarginRight(0);
        hBoxUFCidadeCEPSeparador03.setMarginBottom(0);
        hBoxUFCidadeCEPSeparador03.setSpacing(1);
        hBoxUFCidadeCEPSeparador03.setFlexVflex("ftFalse");
        hBoxUFCidadeCEPSeparador03.setFlexHflex("ftFalse");
        hBoxUFCidadeCEPSeparador03.setScrollable(false);
        hBoxUFCidadeCEPSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxUFCidadeCEPSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxUFCidadeCEPSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxUFCidadeCEPSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxUFCidadeCEPSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFCidadeCEPSeparador03.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEPSeparador03.setVAlign("tvTop");
        hBoxUFCidadeCEP.addChildren(hBoxUFCidadeCEPSeparador03);
        hBoxUFCidadeCEPSeparador03.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador06 = new TFHBox();

    private void init_hBoxPrincipalSeparador06() {
        hBoxPrincipalSeparador06.setName("hBoxPrincipalSeparador06");
        hBoxPrincipalSeparador06.setLeft(0);
        hBoxPrincipalSeparador06.setTop(248);
        hBoxPrincipalSeparador06.setWidth(130);
        hBoxPrincipalSeparador06.setHeight(5);
        hBoxPrincipalSeparador06.setBorderStyle("stNone");
        hBoxPrincipalSeparador06.setPaddingTop(0);
        hBoxPrincipalSeparador06.setPaddingLeft(0);
        hBoxPrincipalSeparador06.setPaddingRight(0);
        hBoxPrincipalSeparador06.setPaddingBottom(0);
        hBoxPrincipalSeparador06.setMarginTop(0);
        hBoxPrincipalSeparador06.setMarginLeft(0);
        hBoxPrincipalSeparador06.setMarginRight(0);
        hBoxPrincipalSeparador06.setMarginBottom(0);
        hBoxPrincipalSeparador06.setSpacing(1);
        hBoxPrincipalSeparador06.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador06.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador06.setScrollable(false);
        hBoxPrincipalSeparador06.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador06.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador06.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador06.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador06.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador06.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador06.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador06);
        hBoxPrincipalSeparador06.applyProperties();
    }

    public TFHBox hBoxBairro = new TFHBox();

    private void init_hBoxBairro() {
        hBoxBairro.setName("hBoxBairro");
        hBoxBairro.setLeft(0);
        hBoxBairro.setTop(254);
        hBoxBairro.setWidth(230);
        hBoxBairro.setHeight(50);
        hBoxBairro.setBorderStyle("stNone");
        hBoxBairro.setPaddingTop(0);
        hBoxBairro.setPaddingLeft(0);
        hBoxBairro.setPaddingRight(0);
        hBoxBairro.setPaddingBottom(0);
        hBoxBairro.setMarginTop(0);
        hBoxBairro.setMarginLeft(0);
        hBoxBairro.setMarginRight(0);
        hBoxBairro.setMarginBottom(0);
        hBoxBairro.setSpacing(1);
        hBoxBairro.setFlexVflex("ftMin");
        hBoxBairro.setFlexHflex("ftTrue");
        hBoxBairro.setScrollable(false);
        hBoxBairro.setBoxShadowConfigHorizontalLength(10);
        hBoxBairro.setBoxShadowConfigVerticalLength(10);
        hBoxBairro.setBoxShadowConfigBlurRadius(5);
        hBoxBairro.setBoxShadowConfigSpreadRadius(0);
        hBoxBairro.setBoxShadowConfigShadowColor("clBlack");
        hBoxBairro.setBoxShadowConfigOpacity(75);
        hBoxBairro.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBairro);
        hBoxBairro.applyProperties();
    }

    public TFHBox hBoxBairroSeparador01 = new TFHBox();

    private void init_hBoxBairroSeparador01() {
        hBoxBairroSeparador01.setName("hBoxBairroSeparador01");
        hBoxBairroSeparador01.setLeft(0);
        hBoxBairroSeparador01.setTop(0);
        hBoxBairroSeparador01.setWidth(5);
        hBoxBairroSeparador01.setHeight(20);
        hBoxBairroSeparador01.setBorderStyle("stNone");
        hBoxBairroSeparador01.setPaddingTop(0);
        hBoxBairroSeparador01.setPaddingLeft(0);
        hBoxBairroSeparador01.setPaddingRight(0);
        hBoxBairroSeparador01.setPaddingBottom(0);
        hBoxBairroSeparador01.setMarginTop(0);
        hBoxBairroSeparador01.setMarginLeft(0);
        hBoxBairroSeparador01.setMarginRight(0);
        hBoxBairroSeparador01.setMarginBottom(0);
        hBoxBairroSeparador01.setSpacing(1);
        hBoxBairroSeparador01.setFlexVflex("ftFalse");
        hBoxBairroSeparador01.setFlexHflex("ftFalse");
        hBoxBairroSeparador01.setScrollable(false);
        hBoxBairroSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxBairroSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxBairroSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxBairroSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxBairroSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxBairroSeparador01.setBoxShadowConfigOpacity(75);
        hBoxBairroSeparador01.setVAlign("tvTop");
        hBoxBairro.addChildren(hBoxBairroSeparador01);
        hBoxBairroSeparador01.applyProperties();
    }

    public TFVBox vBoxBairro = new TFVBox();

    private void init_vBoxBairro() {
        vBoxBairro.setName("vBoxBairro");
        vBoxBairro.setLeft(5);
        vBoxBairro.setTop(0);
        vBoxBairro.setWidth(110);
        vBoxBairro.setHeight(45);
        vBoxBairro.setBorderStyle("stNone");
        vBoxBairro.setPaddingTop(0);
        vBoxBairro.setPaddingLeft(0);
        vBoxBairro.setPaddingRight(0);
        vBoxBairro.setPaddingBottom(0);
        vBoxBairro.setMarginTop(0);
        vBoxBairro.setMarginLeft(0);
        vBoxBairro.setMarginRight(0);
        vBoxBairro.setMarginBottom(0);
        vBoxBairro.setSpacing(1);
        vBoxBairro.setFlexVflex("ftMin");
        vBoxBairro.setFlexHflex("ftTrue");
        vBoxBairro.setScrollable(false);
        vBoxBairro.setBoxShadowConfigHorizontalLength(10);
        vBoxBairro.setBoxShadowConfigVerticalLength(10);
        vBoxBairro.setBoxShadowConfigBlurRadius(5);
        vBoxBairro.setBoxShadowConfigSpreadRadius(0);
        vBoxBairro.setBoxShadowConfigShadowColor("clBlack");
        vBoxBairro.setBoxShadowConfigOpacity(75);
        hBoxBairro.addChildren(vBoxBairro);
        vBoxBairro.applyProperties();
    }

    public TFLabel lblBairro = new TFLabel();

    private void init_lblBairro() {
        lblBairro.setName("lblBairro");
        lblBairro.setLeft(0);
        lblBairro.setTop(0);
        lblBairro.setWidth(34);
        lblBairro.setHeight(13);
        lblBairro.setCaption("Bairro");
        lblBairro.setFontColor("clWindowText");
        lblBairro.setFontSize(-11);
        lblBairro.setFontName("Tahoma");
        lblBairro.setFontStyle("[fsBold]");
        lblBairro.setVerticalAlignment("taVerticalCenter");
        lblBairro.setWordBreak(false);
        vBoxBairro.addChildren(lblBairro);
        lblBairro.applyProperties();
    }

    public TFString edtBairro = new TFString();

    private void init_edtBairro() {
        edtBairro.setName("edtBairro");
        edtBairro.setLeft(0);
        edtBairro.setTop(14);
        edtBairro.setWidth(100);
        edtBairro.setHeight(24);
        edtBairro.setHint("Bairro");
        edtBairro.setHelpCaption("Bairro");
        edtBairro.setFlex(true);
        edtBairro.setRequired(false);
        edtBairro.setPrompt("Bairro");
        edtBairro.setConstraintCheckWhen("cwImmediate");
        edtBairro.setConstraintCheckType("ctExpression");
        edtBairro.setConstraintFocusOnError(false);
        edtBairro.setConstraintEnableUI(true);
        edtBairro.setConstraintEnabled(false);
        edtBairro.setConstraintFormCheck(true);
        edtBairro.setCharCase("ccNormal");
        edtBairro.setPwd(false);
        edtBairro.setMaxlength(30);
        edtBairro.setFontColor("clWindowText");
        edtBairro.setFontSize(-13);
        edtBairro.setFontName("Tahoma");
        edtBairro.setFontStyle("[]");
        edtBairro.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "edtBairro", "OnEnter");
        });
        edtBairro.setSaveLiteralCharacter(false);
        edtBairro.applyProperties();
        vBoxBairro.addChildren(edtBairro);
        addValidatable(edtBairro);
    }

    public TFHBox hBoxBairroSeparador02 = new TFHBox();

    private void init_hBoxBairroSeparador02() {
        hBoxBairroSeparador02.setName("hBoxBairroSeparador02");
        hBoxBairroSeparador02.setLeft(115);
        hBoxBairroSeparador02.setTop(0);
        hBoxBairroSeparador02.setWidth(5);
        hBoxBairroSeparador02.setHeight(20);
        hBoxBairroSeparador02.setBorderStyle("stNone");
        hBoxBairroSeparador02.setPaddingTop(0);
        hBoxBairroSeparador02.setPaddingLeft(0);
        hBoxBairroSeparador02.setPaddingRight(0);
        hBoxBairroSeparador02.setPaddingBottom(0);
        hBoxBairroSeparador02.setMarginTop(0);
        hBoxBairroSeparador02.setMarginLeft(0);
        hBoxBairroSeparador02.setMarginRight(0);
        hBoxBairroSeparador02.setMarginBottom(0);
        hBoxBairroSeparador02.setSpacing(1);
        hBoxBairroSeparador02.setFlexVflex("ftFalse");
        hBoxBairroSeparador02.setFlexHflex("ftFalse");
        hBoxBairroSeparador02.setScrollable(false);
        hBoxBairroSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxBairroSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxBairroSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxBairroSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxBairroSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxBairroSeparador02.setBoxShadowConfigOpacity(75);
        hBoxBairroSeparador02.setVAlign("tvTop");
        hBoxBairro.addChildren(hBoxBairroSeparador02);
        hBoxBairroSeparador02.applyProperties();
    }

    public TFVBox vBoxCEP = new TFVBox();

    private void init_vBoxCEP() {
        vBoxCEP.setName("vBoxCEP");
        vBoxCEP.setLeft(120);
        vBoxCEP.setTop(0);
        vBoxCEP.setWidth(90);
        vBoxCEP.setHeight(40);
        vBoxCEP.setBorderStyle("stNone");
        vBoxCEP.setPaddingTop(0);
        vBoxCEP.setPaddingLeft(0);
        vBoxCEP.setPaddingRight(0);
        vBoxCEP.setPaddingBottom(0);
        vBoxCEP.setMarginTop(0);
        vBoxCEP.setMarginLeft(0);
        vBoxCEP.setMarginRight(0);
        vBoxCEP.setMarginBottom(0);
        vBoxCEP.setSpacing(1);
        vBoxCEP.setFlexVflex("ftMin");
        vBoxCEP.setFlexHflex("ftMin");
        vBoxCEP.setScrollable(false);
        vBoxCEP.setBoxShadowConfigHorizontalLength(10);
        vBoxCEP.setBoxShadowConfigVerticalLength(10);
        vBoxCEP.setBoxShadowConfigBlurRadius(5);
        vBoxCEP.setBoxShadowConfigSpreadRadius(0);
        vBoxCEP.setBoxShadowConfigShadowColor("clBlack");
        vBoxCEP.setBoxShadowConfigOpacity(75);
        hBoxBairro.addChildren(vBoxCEP);
        vBoxCEP.applyProperties();
    }

    public TFLabel lblCEP = new TFLabel();

    private void init_lblCEP() {
        lblCEP.setName("lblCEP");
        lblCEP.setLeft(0);
        lblCEP.setTop(0);
        lblCEP.setWidth(20);
        lblCEP.setHeight(13);
        lblCEP.setCaption("CEP");
        lblCEP.setFontColor("clWindowText");
        lblCEP.setFontSize(-11);
        lblCEP.setFontName("Tahoma");
        lblCEP.setFontStyle("[fsBold]");
        lblCEP.setVerticalAlignment("taVerticalCenter");
        lblCEP.setWordBreak(false);
        vBoxCEP.addChildren(lblCEP);
        lblCEP.applyProperties();
    }

    public TFString edtCEP = new TFString();

    private void init_edtCEP() {
        edtCEP.setName("edtCEP");
        edtCEP.setLeft(0);
        edtCEP.setTop(14);
        edtCEP.setWidth(95);
        edtCEP.setHeight(24);
        edtCEP.setHint("CEP");
        edtCEP.setHelpCaption("CEP");
        edtCEP.setFlex(false);
        edtCEP.setRequired(false);
        edtCEP.setPrompt("CEP");
        edtCEP.setConstraintCheckWhen("cwImmediate");
        edtCEP.setConstraintCheckType("ctExpression");
        edtCEP.setConstraintFocusOnError(false);
        edtCEP.setConstraintEnableUI(true);
        edtCEP.setConstraintEnabled(false);
        edtCEP.setConstraintFormCheck(true);
        edtCEP.setCharCase("ccNormal");
        edtCEP.setPwd(false);
        edtCEP.setMaxlength(8);
        edtCEP.setFontColor("clWindowText");
        edtCEP.setFontSize(-13);
        edtCEP.setFontName("Tahoma");
        edtCEP.setFontStyle("[]");
        edtCEP.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "edtCEP", "OnEnter");
        });
        edtCEP.setSaveLiteralCharacter(false);
        edtCEP.applyProperties();
        vBoxCEP.addChildren(edtCEP);
        addValidatable(edtCEP);
    }

    public TFHBox hBoxUFCidadeCEPSeparador04 = new TFHBox();

    private void init_hBoxUFCidadeCEPSeparador04() {
        hBoxUFCidadeCEPSeparador04.setName("hBoxUFCidadeCEPSeparador04");
        hBoxUFCidadeCEPSeparador04.setLeft(210);
        hBoxUFCidadeCEPSeparador04.setTop(0);
        hBoxUFCidadeCEPSeparador04.setWidth(5);
        hBoxUFCidadeCEPSeparador04.setHeight(20);
        hBoxUFCidadeCEPSeparador04.setBorderStyle("stNone");
        hBoxUFCidadeCEPSeparador04.setPaddingTop(0);
        hBoxUFCidadeCEPSeparador04.setPaddingLeft(0);
        hBoxUFCidadeCEPSeparador04.setPaddingRight(0);
        hBoxUFCidadeCEPSeparador04.setPaddingBottom(0);
        hBoxUFCidadeCEPSeparador04.setMarginTop(0);
        hBoxUFCidadeCEPSeparador04.setMarginLeft(0);
        hBoxUFCidadeCEPSeparador04.setMarginRight(0);
        hBoxUFCidadeCEPSeparador04.setMarginBottom(0);
        hBoxUFCidadeCEPSeparador04.setSpacing(1);
        hBoxUFCidadeCEPSeparador04.setFlexVflex("ftFalse");
        hBoxUFCidadeCEPSeparador04.setFlexHflex("ftFalse");
        hBoxUFCidadeCEPSeparador04.setScrollable(false);
        hBoxUFCidadeCEPSeparador04.setBoxShadowConfigHorizontalLength(10);
        hBoxUFCidadeCEPSeparador04.setBoxShadowConfigVerticalLength(10);
        hBoxUFCidadeCEPSeparador04.setBoxShadowConfigBlurRadius(5);
        hBoxUFCidadeCEPSeparador04.setBoxShadowConfigSpreadRadius(0);
        hBoxUFCidadeCEPSeparador04.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFCidadeCEPSeparador04.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEPSeparador04.setVAlign("tvTop");
        hBoxBairro.addChildren(hBoxUFCidadeCEPSeparador04);
        hBoxUFCidadeCEPSeparador04.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador07 = new TFHBox();

    private void init_hBoxPrincipalSeparador07() {
        hBoxPrincipalSeparador07.setName("hBoxPrincipalSeparador07");
        hBoxPrincipalSeparador07.setLeft(0);
        hBoxPrincipalSeparador07.setTop(305);
        hBoxPrincipalSeparador07.setWidth(130);
        hBoxPrincipalSeparador07.setHeight(5);
        hBoxPrincipalSeparador07.setBorderStyle("stNone");
        hBoxPrincipalSeparador07.setPaddingTop(0);
        hBoxPrincipalSeparador07.setPaddingLeft(0);
        hBoxPrincipalSeparador07.setPaddingRight(0);
        hBoxPrincipalSeparador07.setPaddingBottom(0);
        hBoxPrincipalSeparador07.setMarginTop(0);
        hBoxPrincipalSeparador07.setMarginLeft(0);
        hBoxPrincipalSeparador07.setMarginRight(0);
        hBoxPrincipalSeparador07.setMarginBottom(0);
        hBoxPrincipalSeparador07.setSpacing(1);
        hBoxPrincipalSeparador07.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador07.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador07.setScrollable(false);
        hBoxPrincipalSeparador07.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador07.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador07.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador07.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador07.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador07.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador07.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador07);
        hBoxPrincipalSeparador07.applyProperties();
    }

    public TFHBox hBoxRuaNumero = new TFHBox();

    private void init_hBoxRuaNumero() {
        hBoxRuaNumero.setName("hBoxRuaNumero");
        hBoxRuaNumero.setLeft(0);
        hBoxRuaNumero.setTop(311);
        hBoxRuaNumero.setWidth(240);
        hBoxRuaNumero.setHeight(45);
        hBoxRuaNumero.setBorderStyle("stNone");
        hBoxRuaNumero.setPaddingTop(0);
        hBoxRuaNumero.setPaddingLeft(0);
        hBoxRuaNumero.setPaddingRight(0);
        hBoxRuaNumero.setPaddingBottom(0);
        hBoxRuaNumero.setMarginTop(0);
        hBoxRuaNumero.setMarginLeft(0);
        hBoxRuaNumero.setMarginRight(0);
        hBoxRuaNumero.setMarginBottom(0);
        hBoxRuaNumero.setSpacing(1);
        hBoxRuaNumero.setFlexVflex("ftMin");
        hBoxRuaNumero.setFlexHflex("ftTrue");
        hBoxRuaNumero.setScrollable(false);
        hBoxRuaNumero.setBoxShadowConfigHorizontalLength(10);
        hBoxRuaNumero.setBoxShadowConfigVerticalLength(10);
        hBoxRuaNumero.setBoxShadowConfigBlurRadius(5);
        hBoxRuaNumero.setBoxShadowConfigSpreadRadius(0);
        hBoxRuaNumero.setBoxShadowConfigShadowColor("clBlack");
        hBoxRuaNumero.setBoxShadowConfigOpacity(75);
        hBoxRuaNumero.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxRuaNumero);
        hBoxRuaNumero.applyProperties();
    }

    public TFHBox hBoxRuaNumeroSeparador01 = new TFHBox();

    private void init_hBoxRuaNumeroSeparador01() {
        hBoxRuaNumeroSeparador01.setName("hBoxRuaNumeroSeparador01");
        hBoxRuaNumeroSeparador01.setLeft(0);
        hBoxRuaNumeroSeparador01.setTop(0);
        hBoxRuaNumeroSeparador01.setWidth(5);
        hBoxRuaNumeroSeparador01.setHeight(20);
        hBoxRuaNumeroSeparador01.setBorderStyle("stNone");
        hBoxRuaNumeroSeparador01.setPaddingTop(0);
        hBoxRuaNumeroSeparador01.setPaddingLeft(0);
        hBoxRuaNumeroSeparador01.setPaddingRight(0);
        hBoxRuaNumeroSeparador01.setPaddingBottom(0);
        hBoxRuaNumeroSeparador01.setMarginTop(0);
        hBoxRuaNumeroSeparador01.setMarginLeft(0);
        hBoxRuaNumeroSeparador01.setMarginRight(0);
        hBoxRuaNumeroSeparador01.setMarginBottom(0);
        hBoxRuaNumeroSeparador01.setSpacing(1);
        hBoxRuaNumeroSeparador01.setFlexVflex("ftFalse");
        hBoxRuaNumeroSeparador01.setFlexHflex("ftFalse");
        hBoxRuaNumeroSeparador01.setScrollable(false);
        hBoxRuaNumeroSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxRuaNumeroSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxRuaNumeroSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxRuaNumeroSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxRuaNumeroSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxRuaNumeroSeparador01.setBoxShadowConfigOpacity(75);
        hBoxRuaNumeroSeparador01.setVAlign("tvTop");
        hBoxRuaNumero.addChildren(hBoxRuaNumeroSeparador01);
        hBoxRuaNumeroSeparador01.applyProperties();
    }

    public TFVBox vBoxRua = new TFVBox();

    private void init_vBoxRua() {
        vBoxRua.setName("vBoxRua");
        vBoxRua.setLeft(5);
        vBoxRua.setTop(0);
        vBoxRua.setWidth(130);
        vBoxRua.setHeight(40);
        vBoxRua.setBorderStyle("stNone");
        vBoxRua.setPaddingTop(0);
        vBoxRua.setPaddingLeft(0);
        vBoxRua.setPaddingRight(0);
        vBoxRua.setPaddingBottom(0);
        vBoxRua.setMarginTop(0);
        vBoxRua.setMarginLeft(0);
        vBoxRua.setMarginRight(0);
        vBoxRua.setMarginBottom(0);
        vBoxRua.setSpacing(1);
        vBoxRua.setFlexVflex("ftMin");
        vBoxRua.setFlexHflex("ftTrue");
        vBoxRua.setScrollable(false);
        vBoxRua.setBoxShadowConfigHorizontalLength(10);
        vBoxRua.setBoxShadowConfigVerticalLength(10);
        vBoxRua.setBoxShadowConfigBlurRadius(5);
        vBoxRua.setBoxShadowConfigSpreadRadius(0);
        vBoxRua.setBoxShadowConfigShadowColor("clBlack");
        vBoxRua.setBoxShadowConfigOpacity(75);
        hBoxRuaNumero.addChildren(vBoxRua);
        vBoxRua.applyProperties();
    }

    public TFLabel lblRua = new TFLabel();

    private void init_lblRua() {
        lblRua.setName("lblRua");
        lblRua.setLeft(0);
        lblRua.setTop(0);
        lblRua.setWidth(22);
        lblRua.setHeight(13);
        lblRua.setCaption("Rua");
        lblRua.setFontColor("clWindowText");
        lblRua.setFontSize(-11);
        lblRua.setFontName("Tahoma");
        lblRua.setFontStyle("[fsBold]");
        lblRua.setVerticalAlignment("taVerticalCenter");
        lblRua.setWordBreak(false);
        vBoxRua.addChildren(lblRua);
        lblRua.applyProperties();
    }

    public TFString edtRua = new TFString();

    private void init_edtRua() {
        edtRua.setName("edtRua");
        edtRua.setLeft(0);
        edtRua.setTop(14);
        edtRua.setWidth(120);
        edtRua.setHeight(24);
        edtRua.setHint("Rua");
        edtRua.setHelpCaption("Rua");
        edtRua.setFlex(true);
        edtRua.setRequired(false);
        edtRua.setPrompt("Rua");
        edtRua.setConstraintCheckWhen("cwImmediate");
        edtRua.setConstraintCheckType("ctExpression");
        edtRua.setConstraintFocusOnError(false);
        edtRua.setConstraintEnableUI(true);
        edtRua.setConstraintEnabled(false);
        edtRua.setConstraintFormCheck(true);
        edtRua.setCharCase("ccNormal");
        edtRua.setPwd(false);
        edtRua.setMaxlength(50);
        edtRua.setFontColor("clWindowText");
        edtRua.setFontSize(-13);
        edtRua.setFontName("Tahoma");
        edtRua.setFontStyle("[]");
        edtRua.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "edtRua", "OnEnter");
        });
        edtRua.setSaveLiteralCharacter(false);
        edtRua.applyProperties();
        vBoxRua.addChildren(edtRua);
        addValidatable(edtRua);
    }

    public TFHBox hBoxRuaNumeroSeparador02 = new TFHBox();

    private void init_hBoxRuaNumeroSeparador02() {
        hBoxRuaNumeroSeparador02.setName("hBoxRuaNumeroSeparador02");
        hBoxRuaNumeroSeparador02.setLeft(135);
        hBoxRuaNumeroSeparador02.setTop(0);
        hBoxRuaNumeroSeparador02.setWidth(5);
        hBoxRuaNumeroSeparador02.setHeight(20);
        hBoxRuaNumeroSeparador02.setBorderStyle("stNone");
        hBoxRuaNumeroSeparador02.setPaddingTop(0);
        hBoxRuaNumeroSeparador02.setPaddingLeft(0);
        hBoxRuaNumeroSeparador02.setPaddingRight(0);
        hBoxRuaNumeroSeparador02.setPaddingBottom(0);
        hBoxRuaNumeroSeparador02.setMarginTop(0);
        hBoxRuaNumeroSeparador02.setMarginLeft(0);
        hBoxRuaNumeroSeparador02.setMarginRight(0);
        hBoxRuaNumeroSeparador02.setMarginBottom(0);
        hBoxRuaNumeroSeparador02.setSpacing(1);
        hBoxRuaNumeroSeparador02.setFlexVflex("ftFalse");
        hBoxRuaNumeroSeparador02.setFlexHflex("ftFalse");
        hBoxRuaNumeroSeparador02.setScrollable(false);
        hBoxRuaNumeroSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxRuaNumeroSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxRuaNumeroSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxRuaNumeroSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxRuaNumeroSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxRuaNumeroSeparador02.setBoxShadowConfigOpacity(75);
        hBoxRuaNumeroSeparador02.setVAlign("tvTop");
        hBoxRuaNumero.addChildren(hBoxRuaNumeroSeparador02);
        hBoxRuaNumeroSeparador02.applyProperties();
    }

    public TFVBox vBoxNumero = new TFVBox();

    private void init_vBoxNumero() {
        vBoxNumero.setName("vBoxNumero");
        vBoxNumero.setLeft(140);
        vBoxNumero.setTop(0);
        vBoxNumero.setWidth(90);
        vBoxNumero.setHeight(40);
        vBoxNumero.setBorderStyle("stNone");
        vBoxNumero.setPaddingTop(0);
        vBoxNumero.setPaddingLeft(0);
        vBoxNumero.setPaddingRight(0);
        vBoxNumero.setPaddingBottom(0);
        vBoxNumero.setMarginTop(0);
        vBoxNumero.setMarginLeft(0);
        vBoxNumero.setMarginRight(0);
        vBoxNumero.setMarginBottom(0);
        vBoxNumero.setSpacing(1);
        vBoxNumero.setFlexVflex("ftMin");
        vBoxNumero.setFlexHflex("ftMin");
        vBoxNumero.setScrollable(false);
        vBoxNumero.setBoxShadowConfigHorizontalLength(10);
        vBoxNumero.setBoxShadowConfigVerticalLength(10);
        vBoxNumero.setBoxShadowConfigBlurRadius(5);
        vBoxNumero.setBoxShadowConfigSpreadRadius(0);
        vBoxNumero.setBoxShadowConfigShadowColor("clBlack");
        vBoxNumero.setBoxShadowConfigOpacity(75);
        hBoxRuaNumero.addChildren(vBoxNumero);
        vBoxNumero.applyProperties();
    }

    public TFLabel lblNumero = new TFLabel();

    private void init_lblNumero() {
        lblNumero.setName("lblNumero");
        lblNumero.setLeft(0);
        lblNumero.setTop(0);
        lblNumero.setWidth(44);
        lblNumero.setHeight(13);
        lblNumero.setCaption("N\u00FAmero");
        lblNumero.setFontColor("clWindowText");
        lblNumero.setFontSize(-11);
        lblNumero.setFontName("Tahoma");
        lblNumero.setFontStyle("[fsBold]");
        lblNumero.setVerticalAlignment("taVerticalCenter");
        lblNumero.setWordBreak(false);
        vBoxNumero.addChildren(lblNumero);
        lblNumero.applyProperties();
    }

    public TFString edtNumero = new TFString();

    private void init_edtNumero() {
        edtNumero.setName("edtNumero");
        edtNumero.setLeft(0);
        edtNumero.setTop(14);
        edtNumero.setWidth(95);
        edtNumero.setHeight(24);
        edtNumero.setHint("N\u00FAmero");
        edtNumero.setHelpCaption("N\u00FAmero");
        edtNumero.setFlex(false);
        edtNumero.setRequired(false);
        edtNumero.setPrompt("N\u00FAmero");
        edtNumero.setConstraintCheckWhen("cwImmediate");
        edtNumero.setConstraintCheckType("ctExpression");
        edtNumero.setConstraintFocusOnError(false);
        edtNumero.setConstraintEnableUI(true);
        edtNumero.setConstraintEnabled(false);
        edtNumero.setConstraintFormCheck(true);
        edtNumero.setCharCase("ccUpper");
        edtNumero.setPwd(false);
        edtNumero.setMaxlength(5);
        edtNumero.setFontColor("clWindowText");
        edtNumero.setFontSize(-13);
        edtNumero.setFontName("Tahoma");
        edtNumero.setFontStyle("[]");
        edtNumero.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "edtNumero", "OnEnter");
        });
        edtNumero.setSaveLiteralCharacter(false);
        edtNumero.applyProperties();
        vBoxNumero.addChildren(edtNumero);
        addValidatable(edtNumero);
    }

    public TFHBox hBoxRuaNumeroSeparador03 = new TFHBox();

    private void init_hBoxRuaNumeroSeparador03() {
        hBoxRuaNumeroSeparador03.setName("hBoxRuaNumeroSeparador03");
        hBoxRuaNumeroSeparador03.setLeft(230);
        hBoxRuaNumeroSeparador03.setTop(0);
        hBoxRuaNumeroSeparador03.setWidth(5);
        hBoxRuaNumeroSeparador03.setHeight(20);
        hBoxRuaNumeroSeparador03.setBorderStyle("stNone");
        hBoxRuaNumeroSeparador03.setPaddingTop(0);
        hBoxRuaNumeroSeparador03.setPaddingLeft(0);
        hBoxRuaNumeroSeparador03.setPaddingRight(0);
        hBoxRuaNumeroSeparador03.setPaddingBottom(0);
        hBoxRuaNumeroSeparador03.setMarginTop(0);
        hBoxRuaNumeroSeparador03.setMarginLeft(0);
        hBoxRuaNumeroSeparador03.setMarginRight(0);
        hBoxRuaNumeroSeparador03.setMarginBottom(0);
        hBoxRuaNumeroSeparador03.setSpacing(1);
        hBoxRuaNumeroSeparador03.setFlexVflex("ftFalse");
        hBoxRuaNumeroSeparador03.setFlexHflex("ftFalse");
        hBoxRuaNumeroSeparador03.setScrollable(false);
        hBoxRuaNumeroSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxRuaNumeroSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxRuaNumeroSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxRuaNumeroSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxRuaNumeroSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxRuaNumeroSeparador03.setBoxShadowConfigOpacity(75);
        hBoxRuaNumeroSeparador03.setVAlign("tvTop");
        hBoxRuaNumero.addChildren(hBoxRuaNumeroSeparador03);
        hBoxRuaNumeroSeparador03.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador08 = new TFHBox();

    private void init_hBoxPrincipalSeparador08() {
        hBoxPrincipalSeparador08.setName("hBoxPrincipalSeparador08");
        hBoxPrincipalSeparador08.setLeft(0);
        hBoxPrincipalSeparador08.setTop(357);
        hBoxPrincipalSeparador08.setWidth(240);
        hBoxPrincipalSeparador08.setHeight(5);
        hBoxPrincipalSeparador08.setBorderStyle("stNone");
        hBoxPrincipalSeparador08.setPaddingTop(0);
        hBoxPrincipalSeparador08.setPaddingLeft(0);
        hBoxPrincipalSeparador08.setPaddingRight(0);
        hBoxPrincipalSeparador08.setPaddingBottom(0);
        hBoxPrincipalSeparador08.setMarginTop(0);
        hBoxPrincipalSeparador08.setMarginLeft(0);
        hBoxPrincipalSeparador08.setMarginRight(0);
        hBoxPrincipalSeparador08.setMarginBottom(0);
        hBoxPrincipalSeparador08.setSpacing(1);
        hBoxPrincipalSeparador08.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador08.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador08.setScrollable(false);
        hBoxPrincipalSeparador08.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador08.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador08.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador08.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador08.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador08.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador08.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador08);
        hBoxPrincipalSeparador08.applyProperties();
    }

    public TFHBox hBoxComplementoCaixaPostal = new TFHBox();

    private void init_hBoxComplementoCaixaPostal() {
        hBoxComplementoCaixaPostal.setName("hBoxComplementoCaixaPostal");
        hBoxComplementoCaixaPostal.setLeft(0);
        hBoxComplementoCaixaPostal.setTop(363);
        hBoxComplementoCaixaPostal.setWidth(260);
        hBoxComplementoCaixaPostal.setHeight(50);
        hBoxComplementoCaixaPostal.setBorderStyle("stNone");
        hBoxComplementoCaixaPostal.setPaddingTop(0);
        hBoxComplementoCaixaPostal.setPaddingLeft(0);
        hBoxComplementoCaixaPostal.setPaddingRight(0);
        hBoxComplementoCaixaPostal.setPaddingBottom(0);
        hBoxComplementoCaixaPostal.setMarginTop(0);
        hBoxComplementoCaixaPostal.setMarginLeft(0);
        hBoxComplementoCaixaPostal.setMarginRight(0);
        hBoxComplementoCaixaPostal.setMarginBottom(0);
        hBoxComplementoCaixaPostal.setSpacing(1);
        hBoxComplementoCaixaPostal.setFlexVflex("ftMin");
        hBoxComplementoCaixaPostal.setFlexHflex("ftTrue");
        hBoxComplementoCaixaPostal.setScrollable(false);
        hBoxComplementoCaixaPostal.setBoxShadowConfigHorizontalLength(10);
        hBoxComplementoCaixaPostal.setBoxShadowConfigVerticalLength(10);
        hBoxComplementoCaixaPostal.setBoxShadowConfigBlurRadius(5);
        hBoxComplementoCaixaPostal.setBoxShadowConfigSpreadRadius(0);
        hBoxComplementoCaixaPostal.setBoxShadowConfigShadowColor("clBlack");
        hBoxComplementoCaixaPostal.setBoxShadowConfigOpacity(75);
        hBoxComplementoCaixaPostal.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxComplementoCaixaPostal);
        hBoxComplementoCaixaPostal.applyProperties();
    }

    public TFHBox hBoxComplementoCaixaPostalSeparador01 = new TFHBox();

    private void init_hBoxComplementoCaixaPostalSeparador01() {
        hBoxComplementoCaixaPostalSeparador01.setName("hBoxComplementoCaixaPostalSeparador01");
        hBoxComplementoCaixaPostalSeparador01.setLeft(0);
        hBoxComplementoCaixaPostalSeparador01.setTop(0);
        hBoxComplementoCaixaPostalSeparador01.setWidth(5);
        hBoxComplementoCaixaPostalSeparador01.setHeight(20);
        hBoxComplementoCaixaPostalSeparador01.setBorderStyle("stNone");
        hBoxComplementoCaixaPostalSeparador01.setPaddingTop(0);
        hBoxComplementoCaixaPostalSeparador01.setPaddingLeft(0);
        hBoxComplementoCaixaPostalSeparador01.setPaddingRight(0);
        hBoxComplementoCaixaPostalSeparador01.setPaddingBottom(0);
        hBoxComplementoCaixaPostalSeparador01.setMarginTop(0);
        hBoxComplementoCaixaPostalSeparador01.setMarginLeft(0);
        hBoxComplementoCaixaPostalSeparador01.setMarginRight(0);
        hBoxComplementoCaixaPostalSeparador01.setMarginBottom(0);
        hBoxComplementoCaixaPostalSeparador01.setSpacing(1);
        hBoxComplementoCaixaPostalSeparador01.setFlexVflex("ftFalse");
        hBoxComplementoCaixaPostalSeparador01.setFlexHflex("ftFalse");
        hBoxComplementoCaixaPostalSeparador01.setScrollable(false);
        hBoxComplementoCaixaPostalSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxComplementoCaixaPostalSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxComplementoCaixaPostalSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxComplementoCaixaPostalSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxComplementoCaixaPostalSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxComplementoCaixaPostalSeparador01.setBoxShadowConfigOpacity(75);
        hBoxComplementoCaixaPostalSeparador01.setVAlign("tvTop");
        hBoxComplementoCaixaPostal.addChildren(hBoxComplementoCaixaPostalSeparador01);
        hBoxComplementoCaixaPostalSeparador01.applyProperties();
    }

    public TFVBox vBoxComplemento = new TFVBox();

    private void init_vBoxComplemento() {
        vBoxComplemento.setName("vBoxComplemento");
        vBoxComplemento.setLeft(5);
        vBoxComplemento.setTop(0);
        vBoxComplemento.setWidth(150);
        vBoxComplemento.setHeight(45);
        vBoxComplemento.setBorderStyle("stNone");
        vBoxComplemento.setPaddingTop(0);
        vBoxComplemento.setPaddingLeft(0);
        vBoxComplemento.setPaddingRight(0);
        vBoxComplemento.setPaddingBottom(0);
        vBoxComplemento.setMarginTop(0);
        vBoxComplemento.setMarginLeft(0);
        vBoxComplemento.setMarginRight(0);
        vBoxComplemento.setMarginBottom(0);
        vBoxComplemento.setSpacing(1);
        vBoxComplemento.setFlexVflex("ftMin");
        vBoxComplemento.setFlexHflex("ftTrue");
        vBoxComplemento.setScrollable(false);
        vBoxComplemento.setBoxShadowConfigHorizontalLength(10);
        vBoxComplemento.setBoxShadowConfigVerticalLength(10);
        vBoxComplemento.setBoxShadowConfigBlurRadius(5);
        vBoxComplemento.setBoxShadowConfigSpreadRadius(0);
        vBoxComplemento.setBoxShadowConfigShadowColor("clBlack");
        vBoxComplemento.setBoxShadowConfigOpacity(75);
        hBoxComplementoCaixaPostal.addChildren(vBoxComplemento);
        vBoxComplemento.applyProperties();
    }

    public TFLabel lblComplemento = new TFLabel();

    private void init_lblComplemento() {
        lblComplemento.setName("lblComplemento");
        lblComplemento.setLeft(0);
        lblComplemento.setTop(0);
        lblComplemento.setWidth(79);
        lblComplemento.setHeight(13);
        lblComplemento.setCaption("Complemento");
        lblComplemento.setFontColor("clWindowText");
        lblComplemento.setFontSize(-11);
        lblComplemento.setFontName("Tahoma");
        lblComplemento.setFontStyle("[fsBold]");
        lblComplemento.setVerticalAlignment("taVerticalCenter");
        lblComplemento.setWordBreak(false);
        vBoxComplemento.addChildren(lblComplemento);
        lblComplemento.applyProperties();
    }

    public TFString edtComplemento = new TFString();

    private void init_edtComplemento() {
        edtComplemento.setName("edtComplemento");
        edtComplemento.setLeft(0);
        edtComplemento.setTop(14);
        edtComplemento.setWidth(140);
        edtComplemento.setHeight(24);
        edtComplemento.setHint("Complemento");
        edtComplemento.setHelpCaption("Complemento");
        edtComplemento.setFlex(true);
        edtComplemento.setRequired(false);
        edtComplemento.setPrompt("Complemento");
        edtComplemento.setConstraintCheckWhen("cwImmediate");
        edtComplemento.setConstraintCheckType("ctExpression");
        edtComplemento.setConstraintFocusOnError(false);
        edtComplemento.setConstraintEnableUI(true);
        edtComplemento.setConstraintEnabled(false);
        edtComplemento.setConstraintFormCheck(true);
        edtComplemento.setCharCase("ccNormal");
        edtComplemento.setPwd(false);
        edtComplemento.setMaxlength(30);
        edtComplemento.setFontColor("clWindowText");
        edtComplemento.setFontSize(-13);
        edtComplemento.setFontName("Tahoma");
        edtComplemento.setFontStyle("[]");
        edtComplemento.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "edtComplemento", "OnEnter");
        });
        edtComplemento.setSaveLiteralCharacter(false);
        edtComplemento.applyProperties();
        vBoxComplemento.addChildren(edtComplemento);
        addValidatable(edtComplemento);
    }

    public TFHBox hBoxComplementoCaixaPostalSeparador02 = new TFHBox();

    private void init_hBoxComplementoCaixaPostalSeparador02() {
        hBoxComplementoCaixaPostalSeparador02.setName("hBoxComplementoCaixaPostalSeparador02");
        hBoxComplementoCaixaPostalSeparador02.setLeft(155);
        hBoxComplementoCaixaPostalSeparador02.setTop(0);
        hBoxComplementoCaixaPostalSeparador02.setWidth(5);
        hBoxComplementoCaixaPostalSeparador02.setHeight(20);
        hBoxComplementoCaixaPostalSeparador02.setBorderStyle("stNone");
        hBoxComplementoCaixaPostalSeparador02.setPaddingTop(0);
        hBoxComplementoCaixaPostalSeparador02.setPaddingLeft(0);
        hBoxComplementoCaixaPostalSeparador02.setPaddingRight(0);
        hBoxComplementoCaixaPostalSeparador02.setPaddingBottom(0);
        hBoxComplementoCaixaPostalSeparador02.setMarginTop(0);
        hBoxComplementoCaixaPostalSeparador02.setMarginLeft(0);
        hBoxComplementoCaixaPostalSeparador02.setMarginRight(0);
        hBoxComplementoCaixaPostalSeparador02.setMarginBottom(0);
        hBoxComplementoCaixaPostalSeparador02.setSpacing(1);
        hBoxComplementoCaixaPostalSeparador02.setFlexVflex("ftFalse");
        hBoxComplementoCaixaPostalSeparador02.setFlexHflex("ftFalse");
        hBoxComplementoCaixaPostalSeparador02.setScrollable(false);
        hBoxComplementoCaixaPostalSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxComplementoCaixaPostalSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxComplementoCaixaPostalSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxComplementoCaixaPostalSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxComplementoCaixaPostalSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxComplementoCaixaPostalSeparador02.setBoxShadowConfigOpacity(75);
        hBoxComplementoCaixaPostalSeparador02.setVAlign("tvTop");
        hBoxComplementoCaixaPostal.addChildren(hBoxComplementoCaixaPostalSeparador02);
        hBoxComplementoCaixaPostalSeparador02.applyProperties();
    }

    public TFVBox vBoxCaixaPostal = new TFVBox();

    private void init_vBoxCaixaPostal() {
        vBoxCaixaPostal.setName("vBoxCaixaPostal");
        vBoxCaixaPostal.setLeft(160);
        vBoxCaixaPostal.setTop(0);
        vBoxCaixaPostal.setWidth(90);
        vBoxCaixaPostal.setHeight(45);
        vBoxCaixaPostal.setBorderStyle("stNone");
        vBoxCaixaPostal.setPaddingTop(0);
        vBoxCaixaPostal.setPaddingLeft(0);
        vBoxCaixaPostal.setPaddingRight(0);
        vBoxCaixaPostal.setPaddingBottom(0);
        vBoxCaixaPostal.setMarginTop(0);
        vBoxCaixaPostal.setMarginLeft(0);
        vBoxCaixaPostal.setMarginRight(0);
        vBoxCaixaPostal.setMarginBottom(0);
        vBoxCaixaPostal.setSpacing(1);
        vBoxCaixaPostal.setFlexVflex("ftMin");
        vBoxCaixaPostal.setFlexHflex("ftMin");
        vBoxCaixaPostal.setScrollable(false);
        vBoxCaixaPostal.setBoxShadowConfigHorizontalLength(10);
        vBoxCaixaPostal.setBoxShadowConfigVerticalLength(10);
        vBoxCaixaPostal.setBoxShadowConfigBlurRadius(5);
        vBoxCaixaPostal.setBoxShadowConfigSpreadRadius(0);
        vBoxCaixaPostal.setBoxShadowConfigShadowColor("clBlack");
        vBoxCaixaPostal.setBoxShadowConfigOpacity(75);
        hBoxComplementoCaixaPostal.addChildren(vBoxCaixaPostal);
        vBoxCaixaPostal.applyProperties();
    }

    public TFLabel lblCaixaPostal = new TFLabel();

    private void init_lblCaixaPostal() {
        lblCaixaPostal.setName("lblCaixaPostal");
        lblCaixaPostal.setLeft(0);
        lblCaixaPostal.setTop(0);
        lblCaixaPostal.setWidth(69);
        lblCaixaPostal.setHeight(13);
        lblCaixaPostal.setCaption("Caixa postal");
        lblCaixaPostal.setFontColor("clWindowText");
        lblCaixaPostal.setFontSize(-11);
        lblCaixaPostal.setFontName("Tahoma");
        lblCaixaPostal.setFontStyle("[fsBold]");
        lblCaixaPostal.setVerticalAlignment("taVerticalCenter");
        lblCaixaPostal.setWordBreak(false);
        vBoxCaixaPostal.addChildren(lblCaixaPostal);
        lblCaixaPostal.applyProperties();
    }

    public TFString edtCxPostal = new TFString();

    private void init_edtCxPostal() {
        edtCxPostal.setName("edtCxPostal");
        edtCxPostal.setLeft(0);
        edtCxPostal.setTop(14);
        edtCxPostal.setWidth(95);
        edtCxPostal.setHeight(24);
        edtCxPostal.setHint("Caixa postal");
        edtCxPostal.setHelpCaption("Caixa postal");
        edtCxPostal.setFlex(false);
        edtCxPostal.setRequired(false);
        edtCxPostal.setPrompt("Caixa postal");
        edtCxPostal.setConstraintCheckWhen("cwImmediate");
        edtCxPostal.setConstraintCheckType("ctExpression");
        edtCxPostal.setConstraintFocusOnError(false);
        edtCxPostal.setConstraintEnableUI(true);
        edtCxPostal.setConstraintEnabled(false);
        edtCxPostal.setConstraintFormCheck(true);
        edtCxPostal.setCharCase("ccUpper");
        edtCxPostal.setPwd(false);
        edtCxPostal.setMaxlength(10);
        edtCxPostal.setFontColor("clWindowText");
        edtCxPostal.setFontSize(-13);
        edtCxPostal.setFontName("Tahoma");
        edtCxPostal.setFontStyle("[]");
        edtCxPostal.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "edtCxPostal", "OnEnter");
        });
        edtCxPostal.setSaveLiteralCharacter(false);
        edtCxPostal.applyProperties();
        vBoxCaixaPostal.addChildren(edtCxPostal);
        addValidatable(edtCxPostal);
    }

    public TFHBox hBoxComplementoCaixaPostalSeparador03 = new TFHBox();

    private void init_hBoxComplementoCaixaPostalSeparador03() {
        hBoxComplementoCaixaPostalSeparador03.setName("hBoxComplementoCaixaPostalSeparador03");
        hBoxComplementoCaixaPostalSeparador03.setLeft(250);
        hBoxComplementoCaixaPostalSeparador03.setTop(0);
        hBoxComplementoCaixaPostalSeparador03.setWidth(5);
        hBoxComplementoCaixaPostalSeparador03.setHeight(20);
        hBoxComplementoCaixaPostalSeparador03.setBorderStyle("stNone");
        hBoxComplementoCaixaPostalSeparador03.setPaddingTop(0);
        hBoxComplementoCaixaPostalSeparador03.setPaddingLeft(0);
        hBoxComplementoCaixaPostalSeparador03.setPaddingRight(0);
        hBoxComplementoCaixaPostalSeparador03.setPaddingBottom(0);
        hBoxComplementoCaixaPostalSeparador03.setMarginTop(0);
        hBoxComplementoCaixaPostalSeparador03.setMarginLeft(0);
        hBoxComplementoCaixaPostalSeparador03.setMarginRight(0);
        hBoxComplementoCaixaPostalSeparador03.setMarginBottom(0);
        hBoxComplementoCaixaPostalSeparador03.setSpacing(1);
        hBoxComplementoCaixaPostalSeparador03.setFlexVflex("ftFalse");
        hBoxComplementoCaixaPostalSeparador03.setFlexHflex("ftFalse");
        hBoxComplementoCaixaPostalSeparador03.setScrollable(false);
        hBoxComplementoCaixaPostalSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxComplementoCaixaPostalSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxComplementoCaixaPostalSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxComplementoCaixaPostalSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxComplementoCaixaPostalSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxComplementoCaixaPostalSeparador03.setBoxShadowConfigOpacity(75);
        hBoxComplementoCaixaPostalSeparador03.setVAlign("tvTop");
        hBoxComplementoCaixaPostal.addChildren(hBoxComplementoCaixaPostalSeparador03);
        hBoxComplementoCaixaPostalSeparador03.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador09 = new TFHBox();

    private void init_hBoxPrincipalSeparador09() {
        hBoxPrincipalSeparador09.setName("hBoxPrincipalSeparador09");
        hBoxPrincipalSeparador09.setLeft(0);
        hBoxPrincipalSeparador09.setTop(414);
        hBoxPrincipalSeparador09.setWidth(150);
        hBoxPrincipalSeparador09.setHeight(5);
        hBoxPrincipalSeparador09.setBorderStyle("stNone");
        hBoxPrincipalSeparador09.setPaddingTop(0);
        hBoxPrincipalSeparador09.setPaddingLeft(0);
        hBoxPrincipalSeparador09.setPaddingRight(0);
        hBoxPrincipalSeparador09.setPaddingBottom(0);
        hBoxPrincipalSeparador09.setMarginTop(0);
        hBoxPrincipalSeparador09.setMarginLeft(0);
        hBoxPrincipalSeparador09.setMarginRight(0);
        hBoxPrincipalSeparador09.setMarginBottom(0);
        hBoxPrincipalSeparador09.setSpacing(1);
        hBoxPrincipalSeparador09.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador09.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador09.setScrollable(false);
        hBoxPrincipalSeparador09.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador09.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador09.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador09.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador09.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador09.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador09.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador09);
        hBoxPrincipalSeparador09.applyProperties();
    }

    public TFHBox hBoxContato = new TFHBox();

    private void init_hBoxContato() {
        hBoxContato.setName("hBoxContato");
        hBoxContato.setLeft(0);
        hBoxContato.setTop(420);
        hBoxContato.setWidth(150);
        hBoxContato.setHeight(50);
        hBoxContato.setBorderStyle("stNone");
        hBoxContato.setPaddingTop(0);
        hBoxContato.setPaddingLeft(0);
        hBoxContato.setPaddingRight(0);
        hBoxContato.setPaddingBottom(0);
        hBoxContato.setMarginTop(0);
        hBoxContato.setMarginLeft(0);
        hBoxContato.setMarginRight(0);
        hBoxContato.setMarginBottom(0);
        hBoxContato.setSpacing(1);
        hBoxContato.setFlexVflex("ftMin");
        hBoxContato.setFlexHflex("ftTrue");
        hBoxContato.setScrollable(false);
        hBoxContato.setBoxShadowConfigHorizontalLength(10);
        hBoxContato.setBoxShadowConfigVerticalLength(10);
        hBoxContato.setBoxShadowConfigBlurRadius(5);
        hBoxContato.setBoxShadowConfigSpreadRadius(0);
        hBoxContato.setBoxShadowConfigShadowColor("clBlack");
        hBoxContato.setBoxShadowConfigOpacity(75);
        hBoxContato.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxContato);
        hBoxContato.applyProperties();
    }

    public TFHBox hBoxContatoSeparador01 = new TFHBox();

    private void init_hBoxContatoSeparador01() {
        hBoxContatoSeparador01.setName("hBoxContatoSeparador01");
        hBoxContatoSeparador01.setLeft(0);
        hBoxContatoSeparador01.setTop(0);
        hBoxContatoSeparador01.setWidth(5);
        hBoxContatoSeparador01.setHeight(20);
        hBoxContatoSeparador01.setBorderStyle("stNone");
        hBoxContatoSeparador01.setPaddingTop(0);
        hBoxContatoSeparador01.setPaddingLeft(0);
        hBoxContatoSeparador01.setPaddingRight(0);
        hBoxContatoSeparador01.setPaddingBottom(0);
        hBoxContatoSeparador01.setMarginTop(0);
        hBoxContatoSeparador01.setMarginLeft(0);
        hBoxContatoSeparador01.setMarginRight(0);
        hBoxContatoSeparador01.setMarginBottom(0);
        hBoxContatoSeparador01.setSpacing(1);
        hBoxContatoSeparador01.setFlexVflex("ftFalse");
        hBoxContatoSeparador01.setFlexHflex("ftFalse");
        hBoxContatoSeparador01.setScrollable(false);
        hBoxContatoSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxContatoSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxContatoSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxContatoSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxContatoSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxContatoSeparador01.setBoxShadowConfigOpacity(75);
        hBoxContatoSeparador01.setVAlign("tvTop");
        hBoxContato.addChildren(hBoxContatoSeparador01);
        hBoxContatoSeparador01.applyProperties();
    }

    public TFVBox vBoxContato = new TFVBox();

    private void init_vBoxContato() {
        vBoxContato.setName("vBoxContato");
        vBoxContato.setLeft(5);
        vBoxContato.setTop(0);
        vBoxContato.setWidth(130);
        vBoxContato.setHeight(45);
        vBoxContato.setBorderStyle("stNone");
        vBoxContato.setPaddingTop(0);
        vBoxContato.setPaddingLeft(0);
        vBoxContato.setPaddingRight(0);
        vBoxContato.setPaddingBottom(0);
        vBoxContato.setMarginTop(0);
        vBoxContato.setMarginLeft(0);
        vBoxContato.setMarginRight(0);
        vBoxContato.setMarginBottom(0);
        vBoxContato.setSpacing(1);
        vBoxContato.setFlexVflex("ftMin");
        vBoxContato.setFlexHflex("ftTrue");
        vBoxContato.setScrollable(false);
        vBoxContato.setBoxShadowConfigHorizontalLength(10);
        vBoxContato.setBoxShadowConfigVerticalLength(10);
        vBoxContato.setBoxShadowConfigBlurRadius(5);
        vBoxContato.setBoxShadowConfigSpreadRadius(0);
        vBoxContato.setBoxShadowConfigShadowColor("clBlack");
        vBoxContato.setBoxShadowConfigOpacity(75);
        hBoxContato.addChildren(vBoxContato);
        vBoxContato.applyProperties();
    }

    public TFLabel lblContato = new TFLabel();

    private void init_lblContato() {
        lblContato.setName("lblContato");
        lblContato.setLeft(0);
        lblContato.setTop(0);
        lblContato.setWidth(45);
        lblContato.setHeight(13);
        lblContato.setCaption("Contato");
        lblContato.setFontColor("clWindowText");
        lblContato.setFontSize(-11);
        lblContato.setFontName("Tahoma");
        lblContato.setFontStyle("[fsBold]");
        lblContato.setVerticalAlignment("taVerticalCenter");
        lblContato.setWordBreak(false);
        vBoxContato.addChildren(lblContato);
        lblContato.applyProperties();
    }

    public TFString edtContato = new TFString();

    private void init_edtContato() {
        edtContato.setName("edtContato");
        edtContato.setLeft(0);
        edtContato.setTop(14);
        edtContato.setWidth(120);
        edtContato.setHeight(24);
        edtContato.setHint("Contato");
        edtContato.setHelpCaption("Contato");
        edtContato.setFlex(true);
        edtContato.setRequired(false);
        edtContato.setPrompt("Contato");
        edtContato.setConstraintCheckWhen("cwImmediate");
        edtContato.setConstraintCheckType("ctExpression");
        edtContato.setConstraintFocusOnError(false);
        edtContato.setConstraintEnableUI(true);
        edtContato.setConstraintEnabled(false);
        edtContato.setConstraintFormCheck(true);
        edtContato.setCharCase("ccNormal");
        edtContato.setPwd(false);
        edtContato.setMaxlength(40);
        edtContato.setAlign("alLeft");
        edtContato.setFontColor("clWindowText");
        edtContato.setFontSize(-13);
        edtContato.setFontName("Tahoma");
        edtContato.setFontStyle("[]");
        edtContato.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "edtContato", "OnEnter");
        });
        edtContato.setSaveLiteralCharacter(false);
        edtContato.applyProperties();
        vBoxContato.addChildren(edtContato);
        addValidatable(edtContato);
    }

    public TFHBox hBoxContatoSeparador02 = new TFHBox();

    private void init_hBoxContatoSeparador02() {
        hBoxContatoSeparador02.setName("hBoxContatoSeparador02");
        hBoxContatoSeparador02.setLeft(135);
        hBoxContatoSeparador02.setTop(0);
        hBoxContatoSeparador02.setWidth(5);
        hBoxContatoSeparador02.setHeight(20);
        hBoxContatoSeparador02.setBorderStyle("stNone");
        hBoxContatoSeparador02.setPaddingTop(0);
        hBoxContatoSeparador02.setPaddingLeft(0);
        hBoxContatoSeparador02.setPaddingRight(0);
        hBoxContatoSeparador02.setPaddingBottom(0);
        hBoxContatoSeparador02.setMarginTop(0);
        hBoxContatoSeparador02.setMarginLeft(0);
        hBoxContatoSeparador02.setMarginRight(0);
        hBoxContatoSeparador02.setMarginBottom(0);
        hBoxContatoSeparador02.setSpacing(1);
        hBoxContatoSeparador02.setFlexVflex("ftFalse");
        hBoxContatoSeparador02.setFlexHflex("ftFalse");
        hBoxContatoSeparador02.setScrollable(false);
        hBoxContatoSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxContatoSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxContatoSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxContatoSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxContatoSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxContatoSeparador02.setBoxShadowConfigOpacity(75);
        hBoxContatoSeparador02.setVAlign("tvTop");
        hBoxContato.addChildren(hBoxContatoSeparador02);
        hBoxContatoSeparador02.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador10 = new TFHBox();

    private void init_hBoxPrincipalSeparador10() {
        hBoxPrincipalSeparador10.setName("hBoxPrincipalSeparador10");
        hBoxPrincipalSeparador10.setLeft(0);
        hBoxPrincipalSeparador10.setTop(471);
        hBoxPrincipalSeparador10.setWidth(150);
        hBoxPrincipalSeparador10.setHeight(5);
        hBoxPrincipalSeparador10.setBorderStyle("stNone");
        hBoxPrincipalSeparador10.setPaddingTop(0);
        hBoxPrincipalSeparador10.setPaddingLeft(0);
        hBoxPrincipalSeparador10.setPaddingRight(0);
        hBoxPrincipalSeparador10.setPaddingBottom(0);
        hBoxPrincipalSeparador10.setMarginTop(0);
        hBoxPrincipalSeparador10.setMarginLeft(0);
        hBoxPrincipalSeparador10.setMarginRight(0);
        hBoxPrincipalSeparador10.setMarginBottom(0);
        hBoxPrincipalSeparador10.setSpacing(1);
        hBoxPrincipalSeparador10.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador10.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador10.setScrollable(false);
        hBoxPrincipalSeparador10.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador10.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador10.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador10.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador10.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador10.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador10.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador10);
        hBoxPrincipalSeparador10.applyProperties();
    }

    public TFHBox hBoxEnderecoAtivo = new TFHBox();

    private void init_hBoxEnderecoAtivo() {
        hBoxEnderecoAtivo.setName("hBoxEnderecoAtivo");
        hBoxEnderecoAtivo.setLeft(0);
        hBoxEnderecoAtivo.setTop(477);
        hBoxEnderecoAtivo.setWidth(170);
        hBoxEnderecoAtivo.setHeight(45);
        hBoxEnderecoAtivo.setBorderStyle("stNone");
        hBoxEnderecoAtivo.setPaddingTop(0);
        hBoxEnderecoAtivo.setPaddingLeft(0);
        hBoxEnderecoAtivo.setPaddingRight(0);
        hBoxEnderecoAtivo.setPaddingBottom(0);
        hBoxEnderecoAtivo.setMarginTop(0);
        hBoxEnderecoAtivo.setMarginLeft(0);
        hBoxEnderecoAtivo.setMarginRight(0);
        hBoxEnderecoAtivo.setMarginBottom(0);
        hBoxEnderecoAtivo.setSpacing(1);
        hBoxEnderecoAtivo.setFlexVflex("ftMin");
        hBoxEnderecoAtivo.setFlexHflex("ftTrue");
        hBoxEnderecoAtivo.setScrollable(false);
        hBoxEnderecoAtivo.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecoAtivo.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecoAtivo.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecoAtivo.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecoAtivo.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecoAtivo.setBoxShadowConfigOpacity(75);
        hBoxEnderecoAtivo.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxEnderecoAtivo);
        hBoxEnderecoAtivo.applyProperties();
    }

    public TFHBox hBoxEnderecoAtivoSeparador01 = new TFHBox();

    private void init_hBoxEnderecoAtivoSeparador01() {
        hBoxEnderecoAtivoSeparador01.setName("hBoxEnderecoAtivoSeparador01");
        hBoxEnderecoAtivoSeparador01.setLeft(0);
        hBoxEnderecoAtivoSeparador01.setTop(0);
        hBoxEnderecoAtivoSeparador01.setWidth(5);
        hBoxEnderecoAtivoSeparador01.setHeight(20);
        hBoxEnderecoAtivoSeparador01.setBorderStyle("stNone");
        hBoxEnderecoAtivoSeparador01.setPaddingTop(0);
        hBoxEnderecoAtivoSeparador01.setPaddingLeft(0);
        hBoxEnderecoAtivoSeparador01.setPaddingRight(0);
        hBoxEnderecoAtivoSeparador01.setPaddingBottom(0);
        hBoxEnderecoAtivoSeparador01.setMarginTop(0);
        hBoxEnderecoAtivoSeparador01.setMarginLeft(0);
        hBoxEnderecoAtivoSeparador01.setMarginRight(0);
        hBoxEnderecoAtivoSeparador01.setMarginBottom(0);
        hBoxEnderecoAtivoSeparador01.setSpacing(1);
        hBoxEnderecoAtivoSeparador01.setFlexVflex("ftFalse");
        hBoxEnderecoAtivoSeparador01.setFlexHflex("ftFalse");
        hBoxEnderecoAtivoSeparador01.setScrollable(false);
        hBoxEnderecoAtivoSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecoAtivoSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecoAtivoSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecoAtivoSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecoAtivoSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecoAtivoSeparador01.setBoxShadowConfigOpacity(75);
        hBoxEnderecoAtivoSeparador01.setVAlign("tvTop");
        hBoxEnderecoAtivo.addChildren(hBoxEnderecoAtivoSeparador01);
        hBoxEnderecoAtivoSeparador01.applyProperties();
    }

    public TFVBox vBoxEnderecoAtivo = new TFVBox();

    private void init_vBoxEnderecoAtivo() {
        vBoxEnderecoAtivo.setName("vBoxEnderecoAtivo");
        vBoxEnderecoAtivo.setLeft(5);
        vBoxEnderecoAtivo.setTop(0);
        vBoxEnderecoAtivo.setWidth(150);
        vBoxEnderecoAtivo.setHeight(40);
        vBoxEnderecoAtivo.setBorderStyle("stNone");
        vBoxEnderecoAtivo.setPaddingTop(0);
        vBoxEnderecoAtivo.setPaddingLeft(0);
        vBoxEnderecoAtivo.setPaddingRight(0);
        vBoxEnderecoAtivo.setPaddingBottom(0);
        vBoxEnderecoAtivo.setMarginTop(0);
        vBoxEnderecoAtivo.setMarginLeft(0);
        vBoxEnderecoAtivo.setMarginRight(0);
        vBoxEnderecoAtivo.setMarginBottom(0);
        vBoxEnderecoAtivo.setSpacing(1);
        vBoxEnderecoAtivo.setFlexVflex("ftMin");
        vBoxEnderecoAtivo.setFlexHflex("ftTrue");
        vBoxEnderecoAtivo.setScrollable(false);
        vBoxEnderecoAtivo.setBoxShadowConfigHorizontalLength(10);
        vBoxEnderecoAtivo.setBoxShadowConfigVerticalLength(10);
        vBoxEnderecoAtivo.setBoxShadowConfigBlurRadius(5);
        vBoxEnderecoAtivo.setBoxShadowConfigSpreadRadius(0);
        vBoxEnderecoAtivo.setBoxShadowConfigShadowColor("clBlack");
        vBoxEnderecoAtivo.setBoxShadowConfigOpacity(75);
        hBoxEnderecoAtivo.addChildren(vBoxEnderecoAtivo);
        vBoxEnderecoAtivo.applyProperties();
    }

    public TFLabel lblEnderecoAtio = new TFLabel();

    private void init_lblEnderecoAtio() {
        lblEnderecoAtio.setName("lblEnderecoAtio");
        lblEnderecoAtio.setLeft(0);
        lblEnderecoAtio.setTop(0);
        lblEnderecoAtio.setWidth(84);
        lblEnderecoAtio.setHeight(13);
        lblEnderecoAtio.setCaption("Endere\u00E7o ativo");
        lblEnderecoAtio.setFontColor("clWindowText");
        lblEnderecoAtio.setFontSize(-11);
        lblEnderecoAtio.setFontName("Tahoma");
        lblEnderecoAtio.setFontStyle("[fsBold]");
        lblEnderecoAtio.setVerticalAlignment("taVerticalCenter");
        lblEnderecoAtio.setWordBreak(false);
        vBoxEnderecoAtivo.addChildren(lblEnderecoAtio);
        lblEnderecoAtio.applyProperties();
    }

    public TFCombo cboEnderecoAtivo = new TFCombo();

    private void init_cboEnderecoAtivo() {
        cboEnderecoAtivo.setName("cboEnderecoAtivo");
        cboEnderecoAtivo.setLeft(0);
        cboEnderecoAtivo.setTop(14);
        cboEnderecoAtivo.setWidth(145);
        cboEnderecoAtivo.setHeight(21);
        cboEnderecoAtivo.setHint("Endere\u00E7o ativo");
        cboEnderecoAtivo.setFlex(true);
        cboEnderecoAtivo.setListOptions("Sim=S;N\u00E3o=N");
        cboEnderecoAtivo.setHelpCaption("Endere\u00E7o ativo");
        cboEnderecoAtivo.setReadOnly(true);
        cboEnderecoAtivo.setRequired(false);
        cboEnderecoAtivo.setPrompt("Endere\u00E7o ativo");
        cboEnderecoAtivo.setConstraintCheckWhen("cwImmediate");
        cboEnderecoAtivo.setConstraintCheckType("ctExpression");
        cboEnderecoAtivo.setConstraintFocusOnError(false);
        cboEnderecoAtivo.setConstraintEnableUI(true);
        cboEnderecoAtivo.setConstraintEnabled(false);
        cboEnderecoAtivo.setConstraintFormCheck(true);
        cboEnderecoAtivo.setClearOnDelKey(false);
        cboEnderecoAtivo.setUseClearButton(false);
        cboEnderecoAtivo.setHideClearButtonOnNullValue(true);
        cboEnderecoAtivo.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteEndereco", "cboEnderecoAtivo", "OnEnter");
        });
        vBoxEnderecoAtivo.addChildren(cboEnderecoAtivo);
        cboEnderecoAtivo.applyProperties();
        addValidatable(cboEnderecoAtivo);
    }

    public TFHBox hBoxEnderecoAtivoSeparador02 = new TFHBox();

    private void init_hBoxEnderecoAtivoSeparador02() {
        hBoxEnderecoAtivoSeparador02.setName("hBoxEnderecoAtivoSeparador02");
        hBoxEnderecoAtivoSeparador02.setLeft(155);
        hBoxEnderecoAtivoSeparador02.setTop(0);
        hBoxEnderecoAtivoSeparador02.setWidth(5);
        hBoxEnderecoAtivoSeparador02.setHeight(20);
        hBoxEnderecoAtivoSeparador02.setBorderStyle("stNone");
        hBoxEnderecoAtivoSeparador02.setPaddingTop(0);
        hBoxEnderecoAtivoSeparador02.setPaddingLeft(0);
        hBoxEnderecoAtivoSeparador02.setPaddingRight(0);
        hBoxEnderecoAtivoSeparador02.setPaddingBottom(0);
        hBoxEnderecoAtivoSeparador02.setMarginTop(0);
        hBoxEnderecoAtivoSeparador02.setMarginLeft(0);
        hBoxEnderecoAtivoSeparador02.setMarginRight(0);
        hBoxEnderecoAtivoSeparador02.setMarginBottom(0);
        hBoxEnderecoAtivoSeparador02.setSpacing(1);
        hBoxEnderecoAtivoSeparador02.setFlexVflex("ftFalse");
        hBoxEnderecoAtivoSeparador02.setFlexHflex("ftFalse");
        hBoxEnderecoAtivoSeparador02.setScrollable(false);
        hBoxEnderecoAtivoSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecoAtivoSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecoAtivoSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecoAtivoSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecoAtivoSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecoAtivoSeparador02.setBoxShadowConfigOpacity(75);
        hBoxEnderecoAtivoSeparador02.setVAlign("tvTop");
        hBoxEnderecoAtivo.addChildren(hBoxEnderecoAtivoSeparador02);
        hBoxEnderecoAtivoSeparador02.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnConfirmarClick(final Event<Object> event) {
        if (btnConfirmar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConfirmar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void lblSituacaoCadSintegraClick(final Event<Object> event);

    public abstract void lblSituacaoSintegraClick(final Event<Object> event);

    public abstract void lblSintegraMultiIeClick(final Event<Object> event);

    public abstract void cboUFChange(final Event<Object> event);

    public abstract void cboUFExit(final Event<Object> event);

    public abstract void cboUFClearClick(final Event<Object> event);

    public abstract void cboCidadeChange(final Event<Object> event);

    public abstract void cbbCidadeExit(final Event<Object> event);

}