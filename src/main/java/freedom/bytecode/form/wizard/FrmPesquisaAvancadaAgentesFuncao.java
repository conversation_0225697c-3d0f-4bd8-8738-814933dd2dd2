package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPesquisaAvancadaAgentesFuncao extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PesquisaAvancadaAgentesFuncaoRNA rn = null;

    public FrmPesquisaAvancadaAgentesFuncao() {
        try {
            rn = (freedom.bytecode.rn.PesquisaAvancadaAgentesFuncaoRNA) getRN(freedom.bytecode.rn.wizard.PesquisaAvancadaAgentesFuncaoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbDepartamentos();
        init_tbEmpresas();
        init_tbDivisoes();
        init_vBoxPrincipal();
        init_hBoxSeparadorVertical01();
        init_hBoxLogin();
        init_hBoxLoginSeparador01();
        init_vBoxLoginLbl();
        init_hBoxLoginLblSeparador01();
        init_lblLogin();
        init_edtLogin();
        init_hBoxLoginSeparador02();
        init_hBoxSeparadorVertical02();
        init_hBoxNome();
        init_hBoxNomeSeparador01();
        init_vBoxNomeLbl();
        init_hBoxNomeLblSeparador01();
        init_lblNome();
        init_edtNome();
        init_hBoxNomeSeparador02();
        init_hBoxSeparadorVertical03();
        init_hBoxEmpresa();
        init_hBoxEmpresaSeparador01();
        init_vBoxEmpresaLbl();
        init_hBoxEmpresaLblSeparador01();
        init_lblEmpresa();
        init_cboEmpresa();
        init_hBoxEmpresaSeparador02();
        init_hBoxSeparadorVertical04();
        init_hBoxDepartamento();
        init_hBoxDepartamentoSeparador01();
        init_vBoxDepartamentoLbl();
        init_hBoxDepartamentoLblSeparador01();
        init_lblDepartamento();
        init_cboDepartamento();
        init_hBoxDepartamentoSeparador02();
        init_hBoxSeparadorVertical05();
        init_hBoxDivisao();
        init_hBoxDivisaoSeparador01();
        init_vBoxDivisaoLbl();
        init_hBoxDivisaoLblSeparador01();
        init_lblDivisao();
        init_cboDivisao();
        init_hBoxDivisaoSeparador02();
        init_hBoxSeparadorVertical06();
        init_hBoxCPF();
        init_hBoxCPFSeparador01();
        init_vBoxCPFLbl();
        init_hBoxCPFLblSeparador01();
        init_lblCPF();
        init_edtCPF();
        init_hBoxCPFSeparador02();
        init_hBoxSeparadorVertical07();
        init_hBoxAtivo();
        init_hBoxAtivoSeparador01();
        init_vBoxAtivoLbl();
        init_hBoxAtivoLblSeparador01();
        init_lblAtivo();
        init_cboAtivo();
        init_hBoxAtivoSeparador02();
        init_hBoxSeparadorVertical08();
        init_hBoxCadBD();
        init_hBoxCadBDSeparador01();
        init_vBoxCadBDLbl();
        init_hBoxCadBDLblSeparador01();
        init_lblCadBD();
        init_cboCadBD();
        init_hBoxCadBDSeparador02();
        init_hBoxSeparadorVertical09();
        init_hBoxBotoes();
        init_hBoxBotoesSeparador01();
        init_btnLimpar();
        init_hBoxBotoesSeparador02();
        init_btnPesquisar();
        init_hBoxBotoesSeparador03();
        init_FrmPesquisaAvancadaAgentesFuncao();
    }

    public LISTA_EMPRESAS_DEPARTAMENTOS tbDepartamentos;

    private void init_tbDepartamentos() {
        tbDepartamentos = rn.tbDepartamentos;
        tbDepartamentos.setName("tbDepartamentos");
        tbDepartamentos.setMaxRowCount(200);
        tbDepartamentos.setWKey("37501219;37502");
        tbDepartamentos.setRatioBatchSize(20);
        getTables().put(tbDepartamentos, "tbDepartamentos");
        tbDepartamentos.applyProperties();
    }

    public BUSCA_EMPRESAS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("37501219;37503");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public BUSCA_EMPRESAS_DIVISOES tbDivisoes;

    private void init_tbDivisoes() {
        tbDivisoes = rn.tbDivisoes;
        tbDivisoes.setName("tbDivisoes");
        tbDivisoes.setMaxRowCount(200);
        tbDivisoes.setWKey("37501219;37504");
        tbDivisoes.setRatioBatchSize(20);
        getTables().put(tbDivisoes, "tbDivisoes");
        tbDivisoes.applyProperties();
    }

    protected TFForm FrmPesquisaAvancadaAgentesFuncao = this;
    private void init_FrmPesquisaAvancadaAgentesFuncao() {
        FrmPesquisaAvancadaAgentesFuncao.setName("FrmPesquisaAvancadaAgentesFuncao");
        FrmPesquisaAvancadaAgentesFuncao.setCaption("Pesquisa avan\u00E7ada");
        FrmPesquisaAvancadaAgentesFuncao.setClientHeight(363);
        FrmPesquisaAvancadaAgentesFuncao.setClientWidth(592);
        FrmPesquisaAvancadaAgentesFuncao.setColor("clBtnFace");
        FrmPesquisaAvancadaAgentesFuncao.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "FrmPesquisaAvancadaAgentesFuncao", "OnCreate");
        });
        FrmPesquisaAvancadaAgentesFuncao.setWOrigem("EhMain");
        FrmPesquisaAvancadaAgentesFuncao.setWKey("37501219");
        FrmPesquisaAvancadaAgentesFuncao.setSpacing(0);
        FrmPesquisaAvancadaAgentesFuncao.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(592);
        vBoxPrincipal.setHeight(363);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmPesquisaAvancadaAgentesFuncao.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxSeparadorVertical01 = new TFHBox();

    private void init_hBoxSeparadorVertical01() {
        hBoxSeparadorVertical01.setName("hBoxSeparadorVertical01");
        hBoxSeparadorVertical01.setLeft(0);
        hBoxSeparadorVertical01.setTop(0);
        hBoxSeparadorVertical01.setWidth(370);
        hBoxSeparadorVertical01.setHeight(5);
        hBoxSeparadorVertical01.setBorderStyle("stNone");
        hBoxSeparadorVertical01.setPaddingTop(0);
        hBoxSeparadorVertical01.setPaddingLeft(0);
        hBoxSeparadorVertical01.setPaddingRight(0);
        hBoxSeparadorVertical01.setPaddingBottom(0);
        hBoxSeparadorVertical01.setMarginTop(0);
        hBoxSeparadorVertical01.setMarginLeft(0);
        hBoxSeparadorVertical01.setMarginRight(0);
        hBoxSeparadorVertical01.setMarginBottom(0);
        hBoxSeparadorVertical01.setSpacing(1);
        hBoxSeparadorVertical01.setFlexVflex("ftFalse");
        hBoxSeparadorVertical01.setFlexHflex("ftTrue");
        hBoxSeparadorVertical01.setScrollable(false);
        hBoxSeparadorVertical01.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorVertical01.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorVertical01.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorVertical01.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorVertical01.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorVertical01.setBoxShadowConfigOpacity(75);
        hBoxSeparadorVertical01.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxSeparadorVertical01);
        hBoxSeparadorVertical01.applyProperties();
    }

    public TFHBox hBoxLogin = new TFHBox();

    private void init_hBoxLogin() {
        hBoxLogin.setName("hBoxLogin");
        hBoxLogin.setLeft(0);
        hBoxLogin.setTop(6);
        hBoxLogin.setWidth(370);
        hBoxLogin.setHeight(35);
        hBoxLogin.setBorderStyle("stNone");
        hBoxLogin.setPaddingTop(0);
        hBoxLogin.setPaddingLeft(0);
        hBoxLogin.setPaddingRight(0);
        hBoxLogin.setPaddingBottom(0);
        hBoxLogin.setMarginTop(0);
        hBoxLogin.setMarginLeft(0);
        hBoxLogin.setMarginRight(0);
        hBoxLogin.setMarginBottom(0);
        hBoxLogin.setSpacing(1);
        hBoxLogin.setFlexVflex("ftMin");
        hBoxLogin.setFlexHflex("ftTrue");
        hBoxLogin.setScrollable(false);
        hBoxLogin.setBoxShadowConfigHorizontalLength(10);
        hBoxLogin.setBoxShadowConfigVerticalLength(10);
        hBoxLogin.setBoxShadowConfigBlurRadius(5);
        hBoxLogin.setBoxShadowConfigSpreadRadius(0);
        hBoxLogin.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogin.setBoxShadowConfigOpacity(75);
        hBoxLogin.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLogin);
        hBoxLogin.applyProperties();
    }

    public TFHBox hBoxLoginSeparador01 = new TFHBox();

    private void init_hBoxLoginSeparador01() {
        hBoxLoginSeparador01.setName("hBoxLoginSeparador01");
        hBoxLoginSeparador01.setLeft(0);
        hBoxLoginSeparador01.setTop(0);
        hBoxLoginSeparador01.setWidth(5);
        hBoxLoginSeparador01.setHeight(20);
        hBoxLoginSeparador01.setBorderStyle("stNone");
        hBoxLoginSeparador01.setPaddingTop(0);
        hBoxLoginSeparador01.setPaddingLeft(0);
        hBoxLoginSeparador01.setPaddingRight(0);
        hBoxLoginSeparador01.setPaddingBottom(0);
        hBoxLoginSeparador01.setMarginTop(0);
        hBoxLoginSeparador01.setMarginLeft(0);
        hBoxLoginSeparador01.setMarginRight(0);
        hBoxLoginSeparador01.setMarginBottom(0);
        hBoxLoginSeparador01.setSpacing(1);
        hBoxLoginSeparador01.setFlexVflex("ftFalse");
        hBoxLoginSeparador01.setFlexHflex("ftFalse");
        hBoxLoginSeparador01.setScrollable(false);
        hBoxLoginSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxLoginSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxLoginSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxLoginSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxLoginSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxLoginSeparador01.setBoxShadowConfigOpacity(75);
        hBoxLoginSeparador01.setVAlign("tvTop");
        hBoxLogin.addChildren(hBoxLoginSeparador01);
        hBoxLoginSeparador01.applyProperties();
    }

    public TFVBox vBoxLoginLbl = new TFVBox();

    private void init_vBoxLoginLbl() {
        vBoxLoginLbl.setName("vBoxLoginLbl");
        vBoxLoginLbl.setLeft(5);
        vBoxLoginLbl.setTop(0);
        vBoxLoginLbl.setWidth(80);
        vBoxLoginLbl.setHeight(30);
        vBoxLoginLbl.setBorderStyle("stNone");
        vBoxLoginLbl.setPaddingTop(0);
        vBoxLoginLbl.setPaddingLeft(0);
        vBoxLoginLbl.setPaddingRight(0);
        vBoxLoginLbl.setPaddingBottom(0);
        vBoxLoginLbl.setMarginTop(0);
        vBoxLoginLbl.setMarginLeft(0);
        vBoxLoginLbl.setMarginRight(0);
        vBoxLoginLbl.setMarginBottom(0);
        vBoxLoginLbl.setSpacing(1);
        vBoxLoginLbl.setFlexVflex("ftMin");
        vBoxLoginLbl.setFlexHflex("ftFalse");
        vBoxLoginLbl.setScrollable(false);
        vBoxLoginLbl.setBoxShadowConfigHorizontalLength(10);
        vBoxLoginLbl.setBoxShadowConfigVerticalLength(10);
        vBoxLoginLbl.setBoxShadowConfigBlurRadius(5);
        vBoxLoginLbl.setBoxShadowConfigSpreadRadius(0);
        vBoxLoginLbl.setBoxShadowConfigShadowColor("clBlack");
        vBoxLoginLbl.setBoxShadowConfigOpacity(75);
        hBoxLogin.addChildren(vBoxLoginLbl);
        vBoxLoginLbl.applyProperties();
    }

    public TFHBox hBoxLoginLblSeparador01 = new TFHBox();

    private void init_hBoxLoginLblSeparador01() {
        hBoxLoginLblSeparador01.setName("hBoxLoginLblSeparador01");
        hBoxLoginLblSeparador01.setLeft(0);
        hBoxLoginLblSeparador01.setTop(0);
        hBoxLoginLblSeparador01.setWidth(20);
        hBoxLoginLblSeparador01.setHeight(5);
        hBoxLoginLblSeparador01.setBorderStyle("stNone");
        hBoxLoginLblSeparador01.setPaddingTop(0);
        hBoxLoginLblSeparador01.setPaddingLeft(0);
        hBoxLoginLblSeparador01.setPaddingRight(0);
        hBoxLoginLblSeparador01.setPaddingBottom(0);
        hBoxLoginLblSeparador01.setMarginTop(0);
        hBoxLoginLblSeparador01.setMarginLeft(0);
        hBoxLoginLblSeparador01.setMarginRight(0);
        hBoxLoginLblSeparador01.setMarginBottom(0);
        hBoxLoginLblSeparador01.setSpacing(1);
        hBoxLoginLblSeparador01.setFlexVflex("ftFalse");
        hBoxLoginLblSeparador01.setFlexHflex("ftFalse");
        hBoxLoginLblSeparador01.setScrollable(false);
        hBoxLoginLblSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxLoginLblSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxLoginLblSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxLoginLblSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxLoginLblSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxLoginLblSeparador01.setBoxShadowConfigOpacity(75);
        hBoxLoginLblSeparador01.setVAlign("tvTop");
        vBoxLoginLbl.addChildren(hBoxLoginLblSeparador01);
        hBoxLoginLblSeparador01.applyProperties();
    }

    public TFLabel lblLogin = new TFLabel();

    private void init_lblLogin() {
        lblLogin.setName("lblLogin");
        lblLogin.setLeft(0);
        lblLogin.setTop(6);
        lblLogin.setWidth(25);
        lblLogin.setHeight(13);
        lblLogin.setCaption("Login");
        lblLogin.setFontColor("clWindowText");
        lblLogin.setFontSize(-11);
        lblLogin.setFontName("Tahoma");
        lblLogin.setFontStyle("[]");
        lblLogin.setVerticalAlignment("taVerticalCenter");
        lblLogin.setWordBreak(false);
        vBoxLoginLbl.addChildren(lblLogin);
        lblLogin.applyProperties();
    }

    public TFString edtLogin = new TFString();

    private void init_edtLogin() {
        edtLogin.setName("edtLogin");
        edtLogin.setLeft(85);
        edtLogin.setTop(0);
        edtLogin.setWidth(200);
        edtLogin.setHeight(24);
        edtLogin.setHint("Login");
        edtLogin.setHelpCaption("Login");
        edtLogin.setFlex(true);
        edtLogin.setRequired(false);
        edtLogin.setPrompt("Login");
        edtLogin.setConstraintCheckWhen("cwImmediate");
        edtLogin.setConstraintCheckType("ctExpression");
        edtLogin.setConstraintFocusOnError(false);
        edtLogin.setConstraintEnableUI(true);
        edtLogin.setConstraintEnabled(false);
        edtLogin.setConstraintFormCheck(true);
        edtLogin.setCharCase("ccNormal");
        edtLogin.setPwd(false);
        edtLogin.setMaxlength(14);
        edtLogin.setFontColor("clWindowText");
        edtLogin.setFontSize(-13);
        edtLogin.setFontName("Tahoma");
        edtLogin.setFontStyle("[]");
        edtLogin.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtLoginEnter(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "edtLogin", "OnEnter");
        });
        edtLogin.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtLoginExit(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "edtLogin", "OnExit");
        });
        edtLogin.setSaveLiteralCharacter(false);
        edtLogin.applyProperties();
        hBoxLogin.addChildren(edtLogin);
        addValidatable(edtLogin);
    }

    public TFHBox hBoxLoginSeparador02 = new TFHBox();

    private void init_hBoxLoginSeparador02() {
        hBoxLoginSeparador02.setName("hBoxLoginSeparador02");
        hBoxLoginSeparador02.setLeft(285);
        hBoxLoginSeparador02.setTop(0);
        hBoxLoginSeparador02.setWidth(5);
        hBoxLoginSeparador02.setHeight(20);
        hBoxLoginSeparador02.setBorderStyle("stNone");
        hBoxLoginSeparador02.setPaddingTop(0);
        hBoxLoginSeparador02.setPaddingLeft(0);
        hBoxLoginSeparador02.setPaddingRight(0);
        hBoxLoginSeparador02.setPaddingBottom(0);
        hBoxLoginSeparador02.setMarginTop(0);
        hBoxLoginSeparador02.setMarginLeft(0);
        hBoxLoginSeparador02.setMarginRight(0);
        hBoxLoginSeparador02.setMarginBottom(0);
        hBoxLoginSeparador02.setSpacing(1);
        hBoxLoginSeparador02.setFlexVflex("ftFalse");
        hBoxLoginSeparador02.setFlexHflex("ftFalse");
        hBoxLoginSeparador02.setScrollable(false);
        hBoxLoginSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxLoginSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxLoginSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxLoginSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxLoginSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxLoginSeparador02.setBoxShadowConfigOpacity(75);
        hBoxLoginSeparador02.setVAlign("tvTop");
        hBoxLogin.addChildren(hBoxLoginSeparador02);
        hBoxLoginSeparador02.applyProperties();
    }

    public TFHBox hBoxSeparadorVertical02 = new TFHBox();

    private void init_hBoxSeparadorVertical02() {
        hBoxSeparadorVertical02.setName("hBoxSeparadorVertical02");
        hBoxSeparadorVertical02.setLeft(0);
        hBoxSeparadorVertical02.setTop(42);
        hBoxSeparadorVertical02.setWidth(370);
        hBoxSeparadorVertical02.setHeight(5);
        hBoxSeparadorVertical02.setBorderStyle("stNone");
        hBoxSeparadorVertical02.setPaddingTop(0);
        hBoxSeparadorVertical02.setPaddingLeft(0);
        hBoxSeparadorVertical02.setPaddingRight(0);
        hBoxSeparadorVertical02.setPaddingBottom(0);
        hBoxSeparadorVertical02.setMarginTop(0);
        hBoxSeparadorVertical02.setMarginLeft(0);
        hBoxSeparadorVertical02.setMarginRight(0);
        hBoxSeparadorVertical02.setMarginBottom(0);
        hBoxSeparadorVertical02.setSpacing(1);
        hBoxSeparadorVertical02.setFlexVflex("ftFalse");
        hBoxSeparadorVertical02.setFlexHflex("ftTrue");
        hBoxSeparadorVertical02.setScrollable(false);
        hBoxSeparadorVertical02.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorVertical02.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorVertical02.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorVertical02.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorVertical02.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorVertical02.setBoxShadowConfigOpacity(75);
        hBoxSeparadorVertical02.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxSeparadorVertical02);
        hBoxSeparadorVertical02.applyProperties();
    }

    public TFHBox hBoxNome = new TFHBox();

    private void init_hBoxNome() {
        hBoxNome.setName("hBoxNome");
        hBoxNome.setLeft(0);
        hBoxNome.setTop(48);
        hBoxNome.setWidth(370);
        hBoxNome.setHeight(35);
        hBoxNome.setBorderStyle("stNone");
        hBoxNome.setPaddingTop(0);
        hBoxNome.setPaddingLeft(0);
        hBoxNome.setPaddingRight(0);
        hBoxNome.setPaddingBottom(0);
        hBoxNome.setMarginTop(0);
        hBoxNome.setMarginLeft(0);
        hBoxNome.setMarginRight(0);
        hBoxNome.setMarginBottom(0);
        hBoxNome.setSpacing(1);
        hBoxNome.setFlexVflex("ftMin");
        hBoxNome.setFlexHflex("ftTrue");
        hBoxNome.setScrollable(false);
        hBoxNome.setBoxShadowConfigHorizontalLength(10);
        hBoxNome.setBoxShadowConfigVerticalLength(10);
        hBoxNome.setBoxShadowConfigBlurRadius(5);
        hBoxNome.setBoxShadowConfigSpreadRadius(0);
        hBoxNome.setBoxShadowConfigShadowColor("clBlack");
        hBoxNome.setBoxShadowConfigOpacity(75);
        hBoxNome.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxNome);
        hBoxNome.applyProperties();
    }

    public TFHBox hBoxNomeSeparador01 = new TFHBox();

    private void init_hBoxNomeSeparador01() {
        hBoxNomeSeparador01.setName("hBoxNomeSeparador01");
        hBoxNomeSeparador01.setLeft(0);
        hBoxNomeSeparador01.setTop(0);
        hBoxNomeSeparador01.setWidth(5);
        hBoxNomeSeparador01.setHeight(20);
        hBoxNomeSeparador01.setBorderStyle("stNone");
        hBoxNomeSeparador01.setPaddingTop(0);
        hBoxNomeSeparador01.setPaddingLeft(0);
        hBoxNomeSeparador01.setPaddingRight(0);
        hBoxNomeSeparador01.setPaddingBottom(0);
        hBoxNomeSeparador01.setMarginTop(0);
        hBoxNomeSeparador01.setMarginLeft(0);
        hBoxNomeSeparador01.setMarginRight(0);
        hBoxNomeSeparador01.setMarginBottom(0);
        hBoxNomeSeparador01.setSpacing(1);
        hBoxNomeSeparador01.setFlexVflex("ftFalse");
        hBoxNomeSeparador01.setFlexHflex("ftFalse");
        hBoxNomeSeparador01.setScrollable(false);
        hBoxNomeSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxNomeSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxNomeSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxNomeSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxNomeSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxNomeSeparador01.setBoxShadowConfigOpacity(75);
        hBoxNomeSeparador01.setVAlign("tvTop");
        hBoxNome.addChildren(hBoxNomeSeparador01);
        hBoxNomeSeparador01.applyProperties();
    }

    public TFVBox vBoxNomeLbl = new TFVBox();

    private void init_vBoxNomeLbl() {
        vBoxNomeLbl.setName("vBoxNomeLbl");
        vBoxNomeLbl.setLeft(5);
        vBoxNomeLbl.setTop(0);
        vBoxNomeLbl.setWidth(80);
        vBoxNomeLbl.setHeight(30);
        vBoxNomeLbl.setBorderStyle("stNone");
        vBoxNomeLbl.setPaddingTop(0);
        vBoxNomeLbl.setPaddingLeft(0);
        vBoxNomeLbl.setPaddingRight(0);
        vBoxNomeLbl.setPaddingBottom(0);
        vBoxNomeLbl.setMarginTop(0);
        vBoxNomeLbl.setMarginLeft(0);
        vBoxNomeLbl.setMarginRight(0);
        vBoxNomeLbl.setMarginBottom(0);
        vBoxNomeLbl.setSpacing(1);
        vBoxNomeLbl.setFlexVflex("ftMin");
        vBoxNomeLbl.setFlexHflex("ftFalse");
        vBoxNomeLbl.setScrollable(false);
        vBoxNomeLbl.setBoxShadowConfigHorizontalLength(10);
        vBoxNomeLbl.setBoxShadowConfigVerticalLength(10);
        vBoxNomeLbl.setBoxShadowConfigBlurRadius(5);
        vBoxNomeLbl.setBoxShadowConfigSpreadRadius(0);
        vBoxNomeLbl.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomeLbl.setBoxShadowConfigOpacity(75);
        hBoxNome.addChildren(vBoxNomeLbl);
        vBoxNomeLbl.applyProperties();
    }

    public TFHBox hBoxNomeLblSeparador01 = new TFHBox();

    private void init_hBoxNomeLblSeparador01() {
        hBoxNomeLblSeparador01.setName("hBoxNomeLblSeparador01");
        hBoxNomeLblSeparador01.setLeft(0);
        hBoxNomeLblSeparador01.setTop(0);
        hBoxNomeLblSeparador01.setWidth(20);
        hBoxNomeLblSeparador01.setHeight(5);
        hBoxNomeLblSeparador01.setBorderStyle("stNone");
        hBoxNomeLblSeparador01.setPaddingTop(0);
        hBoxNomeLblSeparador01.setPaddingLeft(0);
        hBoxNomeLblSeparador01.setPaddingRight(0);
        hBoxNomeLblSeparador01.setPaddingBottom(0);
        hBoxNomeLblSeparador01.setMarginTop(0);
        hBoxNomeLblSeparador01.setMarginLeft(0);
        hBoxNomeLblSeparador01.setMarginRight(0);
        hBoxNomeLblSeparador01.setMarginBottom(0);
        hBoxNomeLblSeparador01.setSpacing(1);
        hBoxNomeLblSeparador01.setFlexVflex("ftFalse");
        hBoxNomeLblSeparador01.setFlexHflex("ftFalse");
        hBoxNomeLblSeparador01.setScrollable(false);
        hBoxNomeLblSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxNomeLblSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxNomeLblSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxNomeLblSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxNomeLblSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxNomeLblSeparador01.setBoxShadowConfigOpacity(75);
        hBoxNomeLblSeparador01.setVAlign("tvTop");
        vBoxNomeLbl.addChildren(hBoxNomeLblSeparador01);
        hBoxNomeLblSeparador01.applyProperties();
    }

    public TFLabel lblNome = new TFLabel();

    private void init_lblNome() {
        lblNome.setName("lblNome");
        lblNome.setLeft(0);
        lblNome.setTop(6);
        lblNome.setWidth(27);
        lblNome.setHeight(13);
        lblNome.setCaption("Nome");
        lblNome.setFontColor("clWindowText");
        lblNome.setFontSize(-11);
        lblNome.setFontName("Tahoma");
        lblNome.setFontStyle("[]");
        lblNome.setVerticalAlignment("taVerticalCenter");
        lblNome.setWordBreak(false);
        vBoxNomeLbl.addChildren(lblNome);
        lblNome.applyProperties();
    }

    public TFString edtNome = new TFString();

    private void init_edtNome() {
        edtNome.setName("edtNome");
        edtNome.setLeft(85);
        edtNome.setTop(0);
        edtNome.setWidth(200);
        edtNome.setHeight(24);
        edtNome.setHint("Nome");
        edtNome.setHelpCaption("Nome");
        edtNome.setFlex(true);
        edtNome.setRequired(false);
        edtNome.setPrompt("Nome");
        edtNome.setConstraintCheckWhen("cwImmediate");
        edtNome.setConstraintCheckType("ctExpression");
        edtNome.setConstraintFocusOnError(false);
        edtNome.setConstraintEnableUI(true);
        edtNome.setConstraintEnabled(false);
        edtNome.setConstraintFormCheck(true);
        edtNome.setCharCase("ccNormal");
        edtNome.setPwd(false);
        edtNome.setMaxlength(14);
        edtNome.setFontColor("clWindowText");
        edtNome.setFontSize(-13);
        edtNome.setFontName("Tahoma");
        edtNome.setFontStyle("[]");
        edtNome.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtNomeEnter(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "edtNome", "OnEnter");
        });
        edtNome.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtNomeExit(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "edtNome", "OnExit");
        });
        edtNome.setSaveLiteralCharacter(false);
        edtNome.applyProperties();
        hBoxNome.addChildren(edtNome);
        addValidatable(edtNome);
    }

    public TFHBox hBoxNomeSeparador02 = new TFHBox();

    private void init_hBoxNomeSeparador02() {
        hBoxNomeSeparador02.setName("hBoxNomeSeparador02");
        hBoxNomeSeparador02.setLeft(285);
        hBoxNomeSeparador02.setTop(0);
        hBoxNomeSeparador02.setWidth(5);
        hBoxNomeSeparador02.setHeight(20);
        hBoxNomeSeparador02.setBorderStyle("stNone");
        hBoxNomeSeparador02.setPaddingTop(0);
        hBoxNomeSeparador02.setPaddingLeft(0);
        hBoxNomeSeparador02.setPaddingRight(0);
        hBoxNomeSeparador02.setPaddingBottom(0);
        hBoxNomeSeparador02.setMarginTop(0);
        hBoxNomeSeparador02.setMarginLeft(0);
        hBoxNomeSeparador02.setMarginRight(0);
        hBoxNomeSeparador02.setMarginBottom(0);
        hBoxNomeSeparador02.setSpacing(1);
        hBoxNomeSeparador02.setFlexVflex("ftFalse");
        hBoxNomeSeparador02.setFlexHflex("ftFalse");
        hBoxNomeSeparador02.setScrollable(false);
        hBoxNomeSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxNomeSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxNomeSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxNomeSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxNomeSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxNomeSeparador02.setBoxShadowConfigOpacity(75);
        hBoxNomeSeparador02.setVAlign("tvTop");
        hBoxNome.addChildren(hBoxNomeSeparador02);
        hBoxNomeSeparador02.applyProperties();
    }

    public TFHBox hBoxSeparadorVertical03 = new TFHBox();

    private void init_hBoxSeparadorVertical03() {
        hBoxSeparadorVertical03.setName("hBoxSeparadorVertical03");
        hBoxSeparadorVertical03.setLeft(0);
        hBoxSeparadorVertical03.setTop(84);
        hBoxSeparadorVertical03.setWidth(370);
        hBoxSeparadorVertical03.setHeight(5);
        hBoxSeparadorVertical03.setBorderStyle("stNone");
        hBoxSeparadorVertical03.setPaddingTop(0);
        hBoxSeparadorVertical03.setPaddingLeft(0);
        hBoxSeparadorVertical03.setPaddingRight(0);
        hBoxSeparadorVertical03.setPaddingBottom(0);
        hBoxSeparadorVertical03.setMarginTop(0);
        hBoxSeparadorVertical03.setMarginLeft(0);
        hBoxSeparadorVertical03.setMarginRight(0);
        hBoxSeparadorVertical03.setMarginBottom(0);
        hBoxSeparadorVertical03.setSpacing(1);
        hBoxSeparadorVertical03.setFlexVflex("ftFalse");
        hBoxSeparadorVertical03.setFlexHflex("ftTrue");
        hBoxSeparadorVertical03.setScrollable(false);
        hBoxSeparadorVertical03.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorVertical03.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorVertical03.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorVertical03.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorVertical03.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorVertical03.setBoxShadowConfigOpacity(75);
        hBoxSeparadorVertical03.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxSeparadorVertical03);
        hBoxSeparadorVertical03.applyProperties();
    }

    public TFHBox hBoxEmpresa = new TFHBox();

    private void init_hBoxEmpresa() {
        hBoxEmpresa.setName("hBoxEmpresa");
        hBoxEmpresa.setLeft(0);
        hBoxEmpresa.setTop(90);
        hBoxEmpresa.setWidth(370);
        hBoxEmpresa.setHeight(35);
        hBoxEmpresa.setBorderStyle("stNone");
        hBoxEmpresa.setPaddingTop(0);
        hBoxEmpresa.setPaddingLeft(0);
        hBoxEmpresa.setPaddingRight(0);
        hBoxEmpresa.setPaddingBottom(0);
        hBoxEmpresa.setMarginTop(0);
        hBoxEmpresa.setMarginLeft(0);
        hBoxEmpresa.setMarginRight(0);
        hBoxEmpresa.setMarginBottom(0);
        hBoxEmpresa.setSpacing(1);
        hBoxEmpresa.setFlexVflex("ftMin");
        hBoxEmpresa.setFlexHflex("ftTrue");
        hBoxEmpresa.setScrollable(false);
        hBoxEmpresa.setBoxShadowConfigHorizontalLength(10);
        hBoxEmpresa.setBoxShadowConfigVerticalLength(10);
        hBoxEmpresa.setBoxShadowConfigBlurRadius(5);
        hBoxEmpresa.setBoxShadowConfigSpreadRadius(0);
        hBoxEmpresa.setBoxShadowConfigShadowColor("clBlack");
        hBoxEmpresa.setBoxShadowConfigOpacity(75);
        hBoxEmpresa.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxEmpresa);
        hBoxEmpresa.applyProperties();
    }

    public TFHBox hBoxEmpresaSeparador01 = new TFHBox();

    private void init_hBoxEmpresaSeparador01() {
        hBoxEmpresaSeparador01.setName("hBoxEmpresaSeparador01");
        hBoxEmpresaSeparador01.setLeft(0);
        hBoxEmpresaSeparador01.setTop(0);
        hBoxEmpresaSeparador01.setWidth(5);
        hBoxEmpresaSeparador01.setHeight(20);
        hBoxEmpresaSeparador01.setBorderStyle("stNone");
        hBoxEmpresaSeparador01.setPaddingTop(0);
        hBoxEmpresaSeparador01.setPaddingLeft(0);
        hBoxEmpresaSeparador01.setPaddingRight(0);
        hBoxEmpresaSeparador01.setPaddingBottom(0);
        hBoxEmpresaSeparador01.setMarginTop(0);
        hBoxEmpresaSeparador01.setMarginLeft(0);
        hBoxEmpresaSeparador01.setMarginRight(0);
        hBoxEmpresaSeparador01.setMarginBottom(0);
        hBoxEmpresaSeparador01.setSpacing(1);
        hBoxEmpresaSeparador01.setFlexVflex("ftFalse");
        hBoxEmpresaSeparador01.setFlexHflex("ftFalse");
        hBoxEmpresaSeparador01.setScrollable(false);
        hBoxEmpresaSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxEmpresaSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxEmpresaSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxEmpresaSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxEmpresaSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxEmpresaSeparador01.setBoxShadowConfigOpacity(75);
        hBoxEmpresaSeparador01.setVAlign("tvTop");
        hBoxEmpresa.addChildren(hBoxEmpresaSeparador01);
        hBoxEmpresaSeparador01.applyProperties();
    }

    public TFVBox vBoxEmpresaLbl = new TFVBox();

    private void init_vBoxEmpresaLbl() {
        vBoxEmpresaLbl.setName("vBoxEmpresaLbl");
        vBoxEmpresaLbl.setLeft(5);
        vBoxEmpresaLbl.setTop(0);
        vBoxEmpresaLbl.setWidth(80);
        vBoxEmpresaLbl.setHeight(30);
        vBoxEmpresaLbl.setBorderStyle("stNone");
        vBoxEmpresaLbl.setPaddingTop(0);
        vBoxEmpresaLbl.setPaddingLeft(0);
        vBoxEmpresaLbl.setPaddingRight(0);
        vBoxEmpresaLbl.setPaddingBottom(0);
        vBoxEmpresaLbl.setMarginTop(0);
        vBoxEmpresaLbl.setMarginLeft(0);
        vBoxEmpresaLbl.setMarginRight(0);
        vBoxEmpresaLbl.setMarginBottom(0);
        vBoxEmpresaLbl.setSpacing(1);
        vBoxEmpresaLbl.setFlexVflex("ftMin");
        vBoxEmpresaLbl.setFlexHflex("ftFalse");
        vBoxEmpresaLbl.setScrollable(false);
        vBoxEmpresaLbl.setBoxShadowConfigHorizontalLength(10);
        vBoxEmpresaLbl.setBoxShadowConfigVerticalLength(10);
        vBoxEmpresaLbl.setBoxShadowConfigBlurRadius(5);
        vBoxEmpresaLbl.setBoxShadowConfigSpreadRadius(0);
        vBoxEmpresaLbl.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmpresaLbl.setBoxShadowConfigOpacity(75);
        hBoxEmpresa.addChildren(vBoxEmpresaLbl);
        vBoxEmpresaLbl.applyProperties();
    }

    public TFHBox hBoxEmpresaLblSeparador01 = new TFHBox();

    private void init_hBoxEmpresaLblSeparador01() {
        hBoxEmpresaLblSeparador01.setName("hBoxEmpresaLblSeparador01");
        hBoxEmpresaLblSeparador01.setLeft(0);
        hBoxEmpresaLblSeparador01.setTop(0);
        hBoxEmpresaLblSeparador01.setWidth(20);
        hBoxEmpresaLblSeparador01.setHeight(5);
        hBoxEmpresaLblSeparador01.setBorderStyle("stNone");
        hBoxEmpresaLblSeparador01.setPaddingTop(0);
        hBoxEmpresaLblSeparador01.setPaddingLeft(0);
        hBoxEmpresaLblSeparador01.setPaddingRight(0);
        hBoxEmpresaLblSeparador01.setPaddingBottom(0);
        hBoxEmpresaLblSeparador01.setMarginTop(0);
        hBoxEmpresaLblSeparador01.setMarginLeft(0);
        hBoxEmpresaLblSeparador01.setMarginRight(0);
        hBoxEmpresaLblSeparador01.setMarginBottom(0);
        hBoxEmpresaLblSeparador01.setSpacing(1);
        hBoxEmpresaLblSeparador01.setFlexVflex("ftFalse");
        hBoxEmpresaLblSeparador01.setFlexHflex("ftFalse");
        hBoxEmpresaLblSeparador01.setScrollable(false);
        hBoxEmpresaLblSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxEmpresaLblSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxEmpresaLblSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxEmpresaLblSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxEmpresaLblSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxEmpresaLblSeparador01.setBoxShadowConfigOpacity(75);
        hBoxEmpresaLblSeparador01.setVAlign("tvTop");
        vBoxEmpresaLbl.addChildren(hBoxEmpresaLblSeparador01);
        hBoxEmpresaLblSeparador01.applyProperties();
    }

    public TFLabel lblEmpresa = new TFLabel();

    private void init_lblEmpresa() {
        lblEmpresa.setName("lblEmpresa");
        lblEmpresa.setLeft(0);
        lblEmpresa.setTop(6);
        lblEmpresa.setWidth(41);
        lblEmpresa.setHeight(13);
        lblEmpresa.setCaption("Empresa");
        lblEmpresa.setFontColor("clWindowText");
        lblEmpresa.setFontSize(-11);
        lblEmpresa.setFontName("Tahoma");
        lblEmpresa.setFontStyle("[]");
        lblEmpresa.setVerticalAlignment("taVerticalCenter");
        lblEmpresa.setWordBreak(false);
        vBoxEmpresaLbl.addChildren(lblEmpresa);
        lblEmpresa.applyProperties();
    }

    public TFCombo cboEmpresa = new TFCombo();

    private void init_cboEmpresa() {
        cboEmpresa.setName("cboEmpresa");
        cboEmpresa.setLeft(85);
        cboEmpresa.setTop(0);
        cboEmpresa.setWidth(200);
        cboEmpresa.setHeight(21);
        cboEmpresa.setHint("Empresa");
        cboEmpresa.setLookupTable(tbEmpresas);
        cboEmpresa.setLookupKey("COD_EMPRESA");
        cboEmpresa.setLookupDesc("EMPRESA_NOME_CODIGO");
        cboEmpresa.setFlex(true);
        cboEmpresa.setHelpCaption("Empresa");
        cboEmpresa.setReadOnly(true);
        cboEmpresa.setRequired(false);
        cboEmpresa.setPrompt("Empresa");
        cboEmpresa.setConstraintCheckWhen("cwImmediate");
        cboEmpresa.setConstraintCheckType("ctExpression");
        cboEmpresa.setConstraintFocusOnError(false);
        cboEmpresa.setConstraintEnableUI(true);
        cboEmpresa.setConstraintEnabled(false);
        cboEmpresa.setConstraintFormCheck(true);
        cboEmpresa.setClearOnDelKey(false);
        cboEmpresa.setUseClearButton(true);
        cboEmpresa.setHideClearButtonOnNullValue(false);
        cboEmpresa.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaChange(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboEmpresa", "OnChange");
        });
        cboEmpresa.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaEnter(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboEmpresa", "OnEnter");
        });
        cboEmpresa.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaClearClick(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboEmpresa", "OnClearClick");
        });
        hBoxEmpresa.addChildren(cboEmpresa);
        cboEmpresa.applyProperties();
        addValidatable(cboEmpresa);
    }

    public TFHBox hBoxEmpresaSeparador02 = new TFHBox();

    private void init_hBoxEmpresaSeparador02() {
        hBoxEmpresaSeparador02.setName("hBoxEmpresaSeparador02");
        hBoxEmpresaSeparador02.setLeft(285);
        hBoxEmpresaSeparador02.setTop(0);
        hBoxEmpresaSeparador02.setWidth(5);
        hBoxEmpresaSeparador02.setHeight(20);
        hBoxEmpresaSeparador02.setBorderStyle("stNone");
        hBoxEmpresaSeparador02.setPaddingTop(0);
        hBoxEmpresaSeparador02.setPaddingLeft(0);
        hBoxEmpresaSeparador02.setPaddingRight(0);
        hBoxEmpresaSeparador02.setPaddingBottom(0);
        hBoxEmpresaSeparador02.setMarginTop(0);
        hBoxEmpresaSeparador02.setMarginLeft(0);
        hBoxEmpresaSeparador02.setMarginRight(0);
        hBoxEmpresaSeparador02.setMarginBottom(0);
        hBoxEmpresaSeparador02.setSpacing(1);
        hBoxEmpresaSeparador02.setFlexVflex("ftFalse");
        hBoxEmpresaSeparador02.setFlexHflex("ftFalse");
        hBoxEmpresaSeparador02.setScrollable(false);
        hBoxEmpresaSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxEmpresaSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxEmpresaSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxEmpresaSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxEmpresaSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxEmpresaSeparador02.setBoxShadowConfigOpacity(75);
        hBoxEmpresaSeparador02.setVAlign("tvTop");
        hBoxEmpresa.addChildren(hBoxEmpresaSeparador02);
        hBoxEmpresaSeparador02.applyProperties();
    }

    public TFHBox hBoxSeparadorVertical04 = new TFHBox();

    private void init_hBoxSeparadorVertical04() {
        hBoxSeparadorVertical04.setName("hBoxSeparadorVertical04");
        hBoxSeparadorVertical04.setLeft(0);
        hBoxSeparadorVertical04.setTop(126);
        hBoxSeparadorVertical04.setWidth(370);
        hBoxSeparadorVertical04.setHeight(5);
        hBoxSeparadorVertical04.setBorderStyle("stNone");
        hBoxSeparadorVertical04.setPaddingTop(0);
        hBoxSeparadorVertical04.setPaddingLeft(0);
        hBoxSeparadorVertical04.setPaddingRight(0);
        hBoxSeparadorVertical04.setPaddingBottom(0);
        hBoxSeparadorVertical04.setMarginTop(0);
        hBoxSeparadorVertical04.setMarginLeft(0);
        hBoxSeparadorVertical04.setMarginRight(0);
        hBoxSeparadorVertical04.setMarginBottom(0);
        hBoxSeparadorVertical04.setSpacing(1);
        hBoxSeparadorVertical04.setFlexVflex("ftFalse");
        hBoxSeparadorVertical04.setFlexHflex("ftTrue");
        hBoxSeparadorVertical04.setScrollable(false);
        hBoxSeparadorVertical04.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorVertical04.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorVertical04.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorVertical04.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorVertical04.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorVertical04.setBoxShadowConfigOpacity(75);
        hBoxSeparadorVertical04.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxSeparadorVertical04);
        hBoxSeparadorVertical04.applyProperties();
    }

    public TFHBox hBoxDepartamento = new TFHBox();

    private void init_hBoxDepartamento() {
        hBoxDepartamento.setName("hBoxDepartamento");
        hBoxDepartamento.setLeft(0);
        hBoxDepartamento.setTop(132);
        hBoxDepartamento.setWidth(370);
        hBoxDepartamento.setHeight(35);
        hBoxDepartamento.setBorderStyle("stNone");
        hBoxDepartamento.setPaddingTop(0);
        hBoxDepartamento.setPaddingLeft(0);
        hBoxDepartamento.setPaddingRight(0);
        hBoxDepartamento.setPaddingBottom(0);
        hBoxDepartamento.setMarginTop(0);
        hBoxDepartamento.setMarginLeft(0);
        hBoxDepartamento.setMarginRight(0);
        hBoxDepartamento.setMarginBottom(0);
        hBoxDepartamento.setSpacing(1);
        hBoxDepartamento.setFlexVflex("ftMin");
        hBoxDepartamento.setFlexHflex("ftTrue");
        hBoxDepartamento.setScrollable(false);
        hBoxDepartamento.setBoxShadowConfigHorizontalLength(10);
        hBoxDepartamento.setBoxShadowConfigVerticalLength(10);
        hBoxDepartamento.setBoxShadowConfigBlurRadius(5);
        hBoxDepartamento.setBoxShadowConfigSpreadRadius(0);
        hBoxDepartamento.setBoxShadowConfigShadowColor("clBlack");
        hBoxDepartamento.setBoxShadowConfigOpacity(75);
        hBoxDepartamento.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxDepartamento);
        hBoxDepartamento.applyProperties();
    }

    public TFHBox hBoxDepartamentoSeparador01 = new TFHBox();

    private void init_hBoxDepartamentoSeparador01() {
        hBoxDepartamentoSeparador01.setName("hBoxDepartamentoSeparador01");
        hBoxDepartamentoSeparador01.setLeft(0);
        hBoxDepartamentoSeparador01.setTop(0);
        hBoxDepartamentoSeparador01.setWidth(5);
        hBoxDepartamentoSeparador01.setHeight(20);
        hBoxDepartamentoSeparador01.setBorderStyle("stNone");
        hBoxDepartamentoSeparador01.setPaddingTop(0);
        hBoxDepartamentoSeparador01.setPaddingLeft(0);
        hBoxDepartamentoSeparador01.setPaddingRight(0);
        hBoxDepartamentoSeparador01.setPaddingBottom(0);
        hBoxDepartamentoSeparador01.setMarginTop(0);
        hBoxDepartamentoSeparador01.setMarginLeft(0);
        hBoxDepartamentoSeparador01.setMarginRight(0);
        hBoxDepartamentoSeparador01.setMarginBottom(0);
        hBoxDepartamentoSeparador01.setSpacing(1);
        hBoxDepartamentoSeparador01.setFlexVflex("ftFalse");
        hBoxDepartamentoSeparador01.setFlexHflex("ftFalse");
        hBoxDepartamentoSeparador01.setScrollable(false);
        hBoxDepartamentoSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxDepartamentoSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxDepartamentoSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxDepartamentoSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxDepartamentoSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxDepartamentoSeparador01.setBoxShadowConfigOpacity(75);
        hBoxDepartamentoSeparador01.setVAlign("tvTop");
        hBoxDepartamento.addChildren(hBoxDepartamentoSeparador01);
        hBoxDepartamentoSeparador01.applyProperties();
    }

    public TFVBox vBoxDepartamentoLbl = new TFVBox();

    private void init_vBoxDepartamentoLbl() {
        vBoxDepartamentoLbl.setName("vBoxDepartamentoLbl");
        vBoxDepartamentoLbl.setLeft(5);
        vBoxDepartamentoLbl.setTop(0);
        vBoxDepartamentoLbl.setWidth(80);
        vBoxDepartamentoLbl.setHeight(30);
        vBoxDepartamentoLbl.setBorderStyle("stNone");
        vBoxDepartamentoLbl.setPaddingTop(0);
        vBoxDepartamentoLbl.setPaddingLeft(0);
        vBoxDepartamentoLbl.setPaddingRight(0);
        vBoxDepartamentoLbl.setPaddingBottom(0);
        vBoxDepartamentoLbl.setMarginTop(0);
        vBoxDepartamentoLbl.setMarginLeft(0);
        vBoxDepartamentoLbl.setMarginRight(0);
        vBoxDepartamentoLbl.setMarginBottom(0);
        vBoxDepartamentoLbl.setSpacing(1);
        vBoxDepartamentoLbl.setFlexVflex("ftMin");
        vBoxDepartamentoLbl.setFlexHflex("ftFalse");
        vBoxDepartamentoLbl.setScrollable(false);
        vBoxDepartamentoLbl.setBoxShadowConfigHorizontalLength(10);
        vBoxDepartamentoLbl.setBoxShadowConfigVerticalLength(10);
        vBoxDepartamentoLbl.setBoxShadowConfigBlurRadius(5);
        vBoxDepartamentoLbl.setBoxShadowConfigSpreadRadius(0);
        vBoxDepartamentoLbl.setBoxShadowConfigShadowColor("clBlack");
        vBoxDepartamentoLbl.setBoxShadowConfigOpacity(75);
        hBoxDepartamento.addChildren(vBoxDepartamentoLbl);
        vBoxDepartamentoLbl.applyProperties();
    }

    public TFHBox hBoxDepartamentoLblSeparador01 = new TFHBox();

    private void init_hBoxDepartamentoLblSeparador01() {
        hBoxDepartamentoLblSeparador01.setName("hBoxDepartamentoLblSeparador01");
        hBoxDepartamentoLblSeparador01.setLeft(0);
        hBoxDepartamentoLblSeparador01.setTop(0);
        hBoxDepartamentoLblSeparador01.setWidth(20);
        hBoxDepartamentoLblSeparador01.setHeight(5);
        hBoxDepartamentoLblSeparador01.setBorderStyle("stNone");
        hBoxDepartamentoLblSeparador01.setPaddingTop(0);
        hBoxDepartamentoLblSeparador01.setPaddingLeft(0);
        hBoxDepartamentoLblSeparador01.setPaddingRight(0);
        hBoxDepartamentoLblSeparador01.setPaddingBottom(0);
        hBoxDepartamentoLblSeparador01.setMarginTop(0);
        hBoxDepartamentoLblSeparador01.setMarginLeft(0);
        hBoxDepartamentoLblSeparador01.setMarginRight(0);
        hBoxDepartamentoLblSeparador01.setMarginBottom(0);
        hBoxDepartamentoLblSeparador01.setSpacing(1);
        hBoxDepartamentoLblSeparador01.setFlexVflex("ftFalse");
        hBoxDepartamentoLblSeparador01.setFlexHflex("ftFalse");
        hBoxDepartamentoLblSeparador01.setScrollable(false);
        hBoxDepartamentoLblSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxDepartamentoLblSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxDepartamentoLblSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxDepartamentoLblSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxDepartamentoLblSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxDepartamentoLblSeparador01.setBoxShadowConfigOpacity(75);
        hBoxDepartamentoLblSeparador01.setVAlign("tvTop");
        vBoxDepartamentoLbl.addChildren(hBoxDepartamentoLblSeparador01);
        hBoxDepartamentoLblSeparador01.applyProperties();
    }

    public TFLabel lblDepartamento = new TFLabel();

    private void init_lblDepartamento() {
        lblDepartamento.setName("lblDepartamento");
        lblDepartamento.setLeft(0);
        lblDepartamento.setTop(6);
        lblDepartamento.setWidth(69);
        lblDepartamento.setHeight(13);
        lblDepartamento.setCaption("Departamento");
        lblDepartamento.setFontColor("clWindowText");
        lblDepartamento.setFontSize(-11);
        lblDepartamento.setFontName("Tahoma");
        lblDepartamento.setFontStyle("[]");
        lblDepartamento.setVerticalAlignment("taVerticalCenter");
        lblDepartamento.setWordBreak(false);
        vBoxDepartamentoLbl.addChildren(lblDepartamento);
        lblDepartamento.applyProperties();
    }

    public TFCombo cboDepartamento = new TFCombo();

    private void init_cboDepartamento() {
        cboDepartamento.setName("cboDepartamento");
        cboDepartamento.setLeft(85);
        cboDepartamento.setTop(0);
        cboDepartamento.setWidth(200);
        cboDepartamento.setHeight(21);
        cboDepartamento.setHint("Departamento");
        cboDepartamento.setLookupTable(tbDepartamentos);
        cboDepartamento.setLookupKey("DPTO_CODIGO");
        cboDepartamento.setLookupDesc("DPTO_DESCRICAO_CODIGO");
        cboDepartamento.setFlex(true);
        cboDepartamento.setHelpCaption("Departamento");
        cboDepartamento.setReadOnly(true);
        cboDepartamento.setRequired(false);
        cboDepartamento.setPrompt("Departamento");
        cboDepartamento.setConstraintCheckWhen("cwImmediate");
        cboDepartamento.setConstraintCheckType("ctExpression");
        cboDepartamento.setConstraintFocusOnError(false);
        cboDepartamento.setConstraintEnableUI(true);
        cboDepartamento.setConstraintEnabled(false);
        cboDepartamento.setConstraintFormCheck(true);
        cboDepartamento.setClearOnDelKey(false);
        cboDepartamento.setUseClearButton(true);
        cboDepartamento.setHideClearButtonOnNullValue(false);
        cboDepartamento.setEnabled(false);
        cboDepartamento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboDepartamentoChange(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboDepartamento", "OnChange");
        });
        cboDepartamento.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboDepartamentoEnter(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboDepartamento", "OnEnter");
        });
        cboDepartamento.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboDepartamentoClearClick(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboDepartamento", "OnClearClick");
        });
        hBoxDepartamento.addChildren(cboDepartamento);
        cboDepartamento.applyProperties();
        addValidatable(cboDepartamento);
    }

    public TFHBox hBoxDepartamentoSeparador02 = new TFHBox();

    private void init_hBoxDepartamentoSeparador02() {
        hBoxDepartamentoSeparador02.setName("hBoxDepartamentoSeparador02");
        hBoxDepartamentoSeparador02.setLeft(285);
        hBoxDepartamentoSeparador02.setTop(0);
        hBoxDepartamentoSeparador02.setWidth(5);
        hBoxDepartamentoSeparador02.setHeight(20);
        hBoxDepartamentoSeparador02.setBorderStyle("stNone");
        hBoxDepartamentoSeparador02.setPaddingTop(0);
        hBoxDepartamentoSeparador02.setPaddingLeft(0);
        hBoxDepartamentoSeparador02.setPaddingRight(0);
        hBoxDepartamentoSeparador02.setPaddingBottom(0);
        hBoxDepartamentoSeparador02.setMarginTop(0);
        hBoxDepartamentoSeparador02.setMarginLeft(0);
        hBoxDepartamentoSeparador02.setMarginRight(0);
        hBoxDepartamentoSeparador02.setMarginBottom(0);
        hBoxDepartamentoSeparador02.setSpacing(1);
        hBoxDepartamentoSeparador02.setFlexVflex("ftFalse");
        hBoxDepartamentoSeparador02.setFlexHflex("ftFalse");
        hBoxDepartamentoSeparador02.setScrollable(false);
        hBoxDepartamentoSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxDepartamentoSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxDepartamentoSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxDepartamentoSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxDepartamentoSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxDepartamentoSeparador02.setBoxShadowConfigOpacity(75);
        hBoxDepartamentoSeparador02.setVAlign("tvTop");
        hBoxDepartamento.addChildren(hBoxDepartamentoSeparador02);
        hBoxDepartamentoSeparador02.applyProperties();
    }

    public TFHBox hBoxSeparadorVertical05 = new TFHBox();

    private void init_hBoxSeparadorVertical05() {
        hBoxSeparadorVertical05.setName("hBoxSeparadorVertical05");
        hBoxSeparadorVertical05.setLeft(0);
        hBoxSeparadorVertical05.setTop(168);
        hBoxSeparadorVertical05.setWidth(370);
        hBoxSeparadorVertical05.setHeight(5);
        hBoxSeparadorVertical05.setBorderStyle("stNone");
        hBoxSeparadorVertical05.setPaddingTop(0);
        hBoxSeparadorVertical05.setPaddingLeft(0);
        hBoxSeparadorVertical05.setPaddingRight(0);
        hBoxSeparadorVertical05.setPaddingBottom(0);
        hBoxSeparadorVertical05.setMarginTop(0);
        hBoxSeparadorVertical05.setMarginLeft(0);
        hBoxSeparadorVertical05.setMarginRight(0);
        hBoxSeparadorVertical05.setMarginBottom(0);
        hBoxSeparadorVertical05.setSpacing(1);
        hBoxSeparadorVertical05.setFlexVflex("ftFalse");
        hBoxSeparadorVertical05.setFlexHflex("ftTrue");
        hBoxSeparadorVertical05.setScrollable(false);
        hBoxSeparadorVertical05.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorVertical05.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorVertical05.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorVertical05.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorVertical05.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorVertical05.setBoxShadowConfigOpacity(75);
        hBoxSeparadorVertical05.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxSeparadorVertical05);
        hBoxSeparadorVertical05.applyProperties();
    }

    public TFHBox hBoxDivisao = new TFHBox();

    private void init_hBoxDivisao() {
        hBoxDivisao.setName("hBoxDivisao");
        hBoxDivisao.setLeft(0);
        hBoxDivisao.setTop(174);
        hBoxDivisao.setWidth(370);
        hBoxDivisao.setHeight(35);
        hBoxDivisao.setBorderStyle("stNone");
        hBoxDivisao.setPaddingTop(0);
        hBoxDivisao.setPaddingLeft(0);
        hBoxDivisao.setPaddingRight(0);
        hBoxDivisao.setPaddingBottom(0);
        hBoxDivisao.setMarginTop(0);
        hBoxDivisao.setMarginLeft(0);
        hBoxDivisao.setMarginRight(0);
        hBoxDivisao.setMarginBottom(0);
        hBoxDivisao.setSpacing(1);
        hBoxDivisao.setFlexVflex("ftMin");
        hBoxDivisao.setFlexHflex("ftTrue");
        hBoxDivisao.setScrollable(false);
        hBoxDivisao.setBoxShadowConfigHorizontalLength(10);
        hBoxDivisao.setBoxShadowConfigVerticalLength(10);
        hBoxDivisao.setBoxShadowConfigBlurRadius(5);
        hBoxDivisao.setBoxShadowConfigSpreadRadius(0);
        hBoxDivisao.setBoxShadowConfigShadowColor("clBlack");
        hBoxDivisao.setBoxShadowConfigOpacity(75);
        hBoxDivisao.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxDivisao);
        hBoxDivisao.applyProperties();
    }

    public TFHBox hBoxDivisaoSeparador01 = new TFHBox();

    private void init_hBoxDivisaoSeparador01() {
        hBoxDivisaoSeparador01.setName("hBoxDivisaoSeparador01");
        hBoxDivisaoSeparador01.setLeft(0);
        hBoxDivisaoSeparador01.setTop(0);
        hBoxDivisaoSeparador01.setWidth(5);
        hBoxDivisaoSeparador01.setHeight(20);
        hBoxDivisaoSeparador01.setBorderStyle("stNone");
        hBoxDivisaoSeparador01.setPaddingTop(0);
        hBoxDivisaoSeparador01.setPaddingLeft(0);
        hBoxDivisaoSeparador01.setPaddingRight(0);
        hBoxDivisaoSeparador01.setPaddingBottom(0);
        hBoxDivisaoSeparador01.setMarginTop(0);
        hBoxDivisaoSeparador01.setMarginLeft(0);
        hBoxDivisaoSeparador01.setMarginRight(0);
        hBoxDivisaoSeparador01.setMarginBottom(0);
        hBoxDivisaoSeparador01.setSpacing(1);
        hBoxDivisaoSeparador01.setFlexVflex("ftFalse");
        hBoxDivisaoSeparador01.setFlexHflex("ftFalse");
        hBoxDivisaoSeparador01.setScrollable(false);
        hBoxDivisaoSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxDivisaoSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxDivisaoSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxDivisaoSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxDivisaoSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxDivisaoSeparador01.setBoxShadowConfigOpacity(75);
        hBoxDivisaoSeparador01.setVAlign("tvTop");
        hBoxDivisao.addChildren(hBoxDivisaoSeparador01);
        hBoxDivisaoSeparador01.applyProperties();
    }

    public TFVBox vBoxDivisaoLbl = new TFVBox();

    private void init_vBoxDivisaoLbl() {
        vBoxDivisaoLbl.setName("vBoxDivisaoLbl");
        vBoxDivisaoLbl.setLeft(5);
        vBoxDivisaoLbl.setTop(0);
        vBoxDivisaoLbl.setWidth(80);
        vBoxDivisaoLbl.setHeight(30);
        vBoxDivisaoLbl.setBorderStyle("stNone");
        vBoxDivisaoLbl.setPaddingTop(0);
        vBoxDivisaoLbl.setPaddingLeft(0);
        vBoxDivisaoLbl.setPaddingRight(0);
        vBoxDivisaoLbl.setPaddingBottom(0);
        vBoxDivisaoLbl.setMarginTop(0);
        vBoxDivisaoLbl.setMarginLeft(0);
        vBoxDivisaoLbl.setMarginRight(0);
        vBoxDivisaoLbl.setMarginBottom(0);
        vBoxDivisaoLbl.setSpacing(1);
        vBoxDivisaoLbl.setFlexVflex("ftMin");
        vBoxDivisaoLbl.setFlexHflex("ftFalse");
        vBoxDivisaoLbl.setScrollable(false);
        vBoxDivisaoLbl.setBoxShadowConfigHorizontalLength(10);
        vBoxDivisaoLbl.setBoxShadowConfigVerticalLength(10);
        vBoxDivisaoLbl.setBoxShadowConfigBlurRadius(5);
        vBoxDivisaoLbl.setBoxShadowConfigSpreadRadius(0);
        vBoxDivisaoLbl.setBoxShadowConfigShadowColor("clBlack");
        vBoxDivisaoLbl.setBoxShadowConfigOpacity(75);
        hBoxDivisao.addChildren(vBoxDivisaoLbl);
        vBoxDivisaoLbl.applyProperties();
    }

    public TFHBox hBoxDivisaoLblSeparador01 = new TFHBox();

    private void init_hBoxDivisaoLblSeparador01() {
        hBoxDivisaoLblSeparador01.setName("hBoxDivisaoLblSeparador01");
        hBoxDivisaoLblSeparador01.setLeft(0);
        hBoxDivisaoLblSeparador01.setTop(0);
        hBoxDivisaoLblSeparador01.setWidth(20);
        hBoxDivisaoLblSeparador01.setHeight(5);
        hBoxDivisaoLblSeparador01.setBorderStyle("stNone");
        hBoxDivisaoLblSeparador01.setPaddingTop(0);
        hBoxDivisaoLblSeparador01.setPaddingLeft(0);
        hBoxDivisaoLblSeparador01.setPaddingRight(0);
        hBoxDivisaoLblSeparador01.setPaddingBottom(0);
        hBoxDivisaoLblSeparador01.setMarginTop(0);
        hBoxDivisaoLblSeparador01.setMarginLeft(0);
        hBoxDivisaoLblSeparador01.setMarginRight(0);
        hBoxDivisaoLblSeparador01.setMarginBottom(0);
        hBoxDivisaoLblSeparador01.setSpacing(1);
        hBoxDivisaoLblSeparador01.setFlexVflex("ftFalse");
        hBoxDivisaoLblSeparador01.setFlexHflex("ftFalse");
        hBoxDivisaoLblSeparador01.setScrollable(false);
        hBoxDivisaoLblSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxDivisaoLblSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxDivisaoLblSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxDivisaoLblSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxDivisaoLblSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxDivisaoLblSeparador01.setBoxShadowConfigOpacity(75);
        hBoxDivisaoLblSeparador01.setVAlign("tvTop");
        vBoxDivisaoLbl.addChildren(hBoxDivisaoLblSeparador01);
        hBoxDivisaoLblSeparador01.applyProperties();
    }

    public TFLabel lblDivisao = new TFLabel();

    private void init_lblDivisao() {
        lblDivisao.setName("lblDivisao");
        lblDivisao.setLeft(0);
        lblDivisao.setTop(6);
        lblDivisao.setWidth(34);
        lblDivisao.setHeight(13);
        lblDivisao.setCaption("Divis\u00E3o");
        lblDivisao.setFontColor("clWindowText");
        lblDivisao.setFontSize(-11);
        lblDivisao.setFontName("Tahoma");
        lblDivisao.setFontStyle("[]");
        lblDivisao.setVerticalAlignment("taVerticalCenter");
        lblDivisao.setWordBreak(false);
        vBoxDivisaoLbl.addChildren(lblDivisao);
        lblDivisao.applyProperties();
    }

    public TFCombo cboDivisao = new TFCombo();

    private void init_cboDivisao() {
        cboDivisao.setName("cboDivisao");
        cboDivisao.setLeft(85);
        cboDivisao.setTop(0);
        cboDivisao.setWidth(200);
        cboDivisao.setHeight(21);
        cboDivisao.setHint("Divis\u00E3o");
        cboDivisao.setLookupTable(tbDivisoes);
        cboDivisao.setLookupKey("COD_EMPRESA_DIVISAO");
        cboDivisao.setLookupDesc("DIVISAO_DESCRICAO_CODIGO");
        cboDivisao.setFlex(true);
        cboDivisao.setHelpCaption("Divis\u00E3o");
        cboDivisao.setReadOnly(true);
        cboDivisao.setRequired(false);
        cboDivisao.setPrompt("Divis\u00E3o");
        cboDivisao.setConstraintCheckWhen("cwImmediate");
        cboDivisao.setConstraintCheckType("ctExpression");
        cboDivisao.setConstraintFocusOnError(false);
        cboDivisao.setConstraintEnableUI(true);
        cboDivisao.setConstraintEnabled(false);
        cboDivisao.setConstraintFormCheck(true);
        cboDivisao.setClearOnDelKey(false);
        cboDivisao.setUseClearButton(true);
        cboDivisao.setHideClearButtonOnNullValue(false);
        cboDivisao.setEnabled(false);
        cboDivisao.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboDivisaoChange(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboDivisao", "OnChange");
        });
        cboDivisao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboDivisaoEnter(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboDivisao", "OnEnter");
        });
        hBoxDivisao.addChildren(cboDivisao);
        cboDivisao.applyProperties();
        addValidatable(cboDivisao);
    }

    public TFHBox hBoxDivisaoSeparador02 = new TFHBox();

    private void init_hBoxDivisaoSeparador02() {
        hBoxDivisaoSeparador02.setName("hBoxDivisaoSeparador02");
        hBoxDivisaoSeparador02.setLeft(285);
        hBoxDivisaoSeparador02.setTop(0);
        hBoxDivisaoSeparador02.setWidth(5);
        hBoxDivisaoSeparador02.setHeight(20);
        hBoxDivisaoSeparador02.setBorderStyle("stNone");
        hBoxDivisaoSeparador02.setPaddingTop(0);
        hBoxDivisaoSeparador02.setPaddingLeft(0);
        hBoxDivisaoSeparador02.setPaddingRight(0);
        hBoxDivisaoSeparador02.setPaddingBottom(0);
        hBoxDivisaoSeparador02.setMarginTop(0);
        hBoxDivisaoSeparador02.setMarginLeft(0);
        hBoxDivisaoSeparador02.setMarginRight(0);
        hBoxDivisaoSeparador02.setMarginBottom(0);
        hBoxDivisaoSeparador02.setSpacing(1);
        hBoxDivisaoSeparador02.setFlexVflex("ftFalse");
        hBoxDivisaoSeparador02.setFlexHflex("ftFalse");
        hBoxDivisaoSeparador02.setScrollable(false);
        hBoxDivisaoSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxDivisaoSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxDivisaoSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxDivisaoSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxDivisaoSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxDivisaoSeparador02.setBoxShadowConfigOpacity(75);
        hBoxDivisaoSeparador02.setVAlign("tvTop");
        hBoxDivisao.addChildren(hBoxDivisaoSeparador02);
        hBoxDivisaoSeparador02.applyProperties();
    }

    public TFHBox hBoxSeparadorVertical06 = new TFHBox();

    private void init_hBoxSeparadorVertical06() {
        hBoxSeparadorVertical06.setName("hBoxSeparadorVertical06");
        hBoxSeparadorVertical06.setLeft(0);
        hBoxSeparadorVertical06.setTop(210);
        hBoxSeparadorVertical06.setWidth(370);
        hBoxSeparadorVertical06.setHeight(5);
        hBoxSeparadorVertical06.setBorderStyle("stNone");
        hBoxSeparadorVertical06.setPaddingTop(0);
        hBoxSeparadorVertical06.setPaddingLeft(0);
        hBoxSeparadorVertical06.setPaddingRight(0);
        hBoxSeparadorVertical06.setPaddingBottom(0);
        hBoxSeparadorVertical06.setMarginTop(0);
        hBoxSeparadorVertical06.setMarginLeft(0);
        hBoxSeparadorVertical06.setMarginRight(0);
        hBoxSeparadorVertical06.setMarginBottom(0);
        hBoxSeparadorVertical06.setSpacing(1);
        hBoxSeparadorVertical06.setFlexVflex("ftFalse");
        hBoxSeparadorVertical06.setFlexHflex("ftTrue");
        hBoxSeparadorVertical06.setScrollable(false);
        hBoxSeparadorVertical06.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorVertical06.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorVertical06.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorVertical06.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorVertical06.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorVertical06.setBoxShadowConfigOpacity(75);
        hBoxSeparadorVertical06.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxSeparadorVertical06);
        hBoxSeparadorVertical06.applyProperties();
    }

    public TFHBox hBoxCPF = new TFHBox();

    private void init_hBoxCPF() {
        hBoxCPF.setName("hBoxCPF");
        hBoxCPF.setLeft(0);
        hBoxCPF.setTop(216);
        hBoxCPF.setWidth(370);
        hBoxCPF.setHeight(35);
        hBoxCPF.setBorderStyle("stNone");
        hBoxCPF.setPaddingTop(0);
        hBoxCPF.setPaddingLeft(0);
        hBoxCPF.setPaddingRight(0);
        hBoxCPF.setPaddingBottom(0);
        hBoxCPF.setMarginTop(0);
        hBoxCPF.setMarginLeft(0);
        hBoxCPF.setMarginRight(0);
        hBoxCPF.setMarginBottom(0);
        hBoxCPF.setSpacing(1);
        hBoxCPF.setFlexVflex("ftMin");
        hBoxCPF.setFlexHflex("ftTrue");
        hBoxCPF.setScrollable(false);
        hBoxCPF.setBoxShadowConfigHorizontalLength(10);
        hBoxCPF.setBoxShadowConfigVerticalLength(10);
        hBoxCPF.setBoxShadowConfigBlurRadius(5);
        hBoxCPF.setBoxShadowConfigSpreadRadius(0);
        hBoxCPF.setBoxShadowConfigShadowColor("clBlack");
        hBoxCPF.setBoxShadowConfigOpacity(75);
        hBoxCPF.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxCPF);
        hBoxCPF.applyProperties();
    }

    public TFHBox hBoxCPFSeparador01 = new TFHBox();

    private void init_hBoxCPFSeparador01() {
        hBoxCPFSeparador01.setName("hBoxCPFSeparador01");
        hBoxCPFSeparador01.setLeft(0);
        hBoxCPFSeparador01.setTop(0);
        hBoxCPFSeparador01.setWidth(5);
        hBoxCPFSeparador01.setHeight(20);
        hBoxCPFSeparador01.setBorderStyle("stNone");
        hBoxCPFSeparador01.setPaddingTop(0);
        hBoxCPFSeparador01.setPaddingLeft(0);
        hBoxCPFSeparador01.setPaddingRight(0);
        hBoxCPFSeparador01.setPaddingBottom(0);
        hBoxCPFSeparador01.setMarginTop(0);
        hBoxCPFSeparador01.setMarginLeft(0);
        hBoxCPFSeparador01.setMarginRight(0);
        hBoxCPFSeparador01.setMarginBottom(0);
        hBoxCPFSeparador01.setSpacing(1);
        hBoxCPFSeparador01.setFlexVflex("ftFalse");
        hBoxCPFSeparador01.setFlexHflex("ftFalse");
        hBoxCPFSeparador01.setScrollable(false);
        hBoxCPFSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxCPFSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxCPFSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxCPFSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxCPFSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxCPFSeparador01.setBoxShadowConfigOpacity(75);
        hBoxCPFSeparador01.setVAlign("tvTop");
        hBoxCPF.addChildren(hBoxCPFSeparador01);
        hBoxCPFSeparador01.applyProperties();
    }

    public TFVBox vBoxCPFLbl = new TFVBox();

    private void init_vBoxCPFLbl() {
        vBoxCPFLbl.setName("vBoxCPFLbl");
        vBoxCPFLbl.setLeft(5);
        vBoxCPFLbl.setTop(0);
        vBoxCPFLbl.setWidth(80);
        vBoxCPFLbl.setHeight(30);
        vBoxCPFLbl.setBorderStyle("stNone");
        vBoxCPFLbl.setPaddingTop(0);
        vBoxCPFLbl.setPaddingLeft(0);
        vBoxCPFLbl.setPaddingRight(0);
        vBoxCPFLbl.setPaddingBottom(0);
        vBoxCPFLbl.setMarginTop(0);
        vBoxCPFLbl.setMarginLeft(0);
        vBoxCPFLbl.setMarginRight(0);
        vBoxCPFLbl.setMarginBottom(0);
        vBoxCPFLbl.setSpacing(1);
        vBoxCPFLbl.setFlexVflex("ftMin");
        vBoxCPFLbl.setFlexHflex("ftFalse");
        vBoxCPFLbl.setScrollable(false);
        vBoxCPFLbl.setBoxShadowConfigHorizontalLength(10);
        vBoxCPFLbl.setBoxShadowConfigVerticalLength(10);
        vBoxCPFLbl.setBoxShadowConfigBlurRadius(5);
        vBoxCPFLbl.setBoxShadowConfigSpreadRadius(0);
        vBoxCPFLbl.setBoxShadowConfigShadowColor("clBlack");
        vBoxCPFLbl.setBoxShadowConfigOpacity(75);
        hBoxCPF.addChildren(vBoxCPFLbl);
        vBoxCPFLbl.applyProperties();
    }

    public TFHBox hBoxCPFLblSeparador01 = new TFHBox();

    private void init_hBoxCPFLblSeparador01() {
        hBoxCPFLblSeparador01.setName("hBoxCPFLblSeparador01");
        hBoxCPFLblSeparador01.setLeft(0);
        hBoxCPFLblSeparador01.setTop(0);
        hBoxCPFLblSeparador01.setWidth(20);
        hBoxCPFLblSeparador01.setHeight(5);
        hBoxCPFLblSeparador01.setBorderStyle("stNone");
        hBoxCPFLblSeparador01.setPaddingTop(0);
        hBoxCPFLblSeparador01.setPaddingLeft(0);
        hBoxCPFLblSeparador01.setPaddingRight(0);
        hBoxCPFLblSeparador01.setPaddingBottom(0);
        hBoxCPFLblSeparador01.setMarginTop(0);
        hBoxCPFLblSeparador01.setMarginLeft(0);
        hBoxCPFLblSeparador01.setMarginRight(0);
        hBoxCPFLblSeparador01.setMarginBottom(0);
        hBoxCPFLblSeparador01.setSpacing(1);
        hBoxCPFLblSeparador01.setFlexVflex("ftFalse");
        hBoxCPFLblSeparador01.setFlexHflex("ftFalse");
        hBoxCPFLblSeparador01.setScrollable(false);
        hBoxCPFLblSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxCPFLblSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxCPFLblSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxCPFLblSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxCPFLblSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxCPFLblSeparador01.setBoxShadowConfigOpacity(75);
        hBoxCPFLblSeparador01.setVAlign("tvTop");
        vBoxCPFLbl.addChildren(hBoxCPFLblSeparador01);
        hBoxCPFLblSeparador01.applyProperties();
    }

    public TFLabel lblCPF = new TFLabel();

    private void init_lblCPF() {
        lblCPF.setName("lblCPF");
        lblCPF.setLeft(0);
        lblCPF.setTop(6);
        lblCPF.setWidth(19);
        lblCPF.setHeight(13);
        lblCPF.setCaption("CPF");
        lblCPF.setFontColor("clWindowText");
        lblCPF.setFontSize(-11);
        lblCPF.setFontName("Tahoma");
        lblCPF.setFontStyle("[]");
        lblCPF.setVerticalAlignment("taVerticalCenter");
        lblCPF.setWordBreak(false);
        vBoxCPFLbl.addChildren(lblCPF);
        lblCPF.applyProperties();
    }

    public TFString edtCPF = new TFString();

    private void init_edtCPF() {
        edtCPF.setName("edtCPF");
        edtCPF.setLeft(85);
        edtCPF.setTop(0);
        edtCPF.setWidth(200);
        edtCPF.setHeight(24);
        edtCPF.setHint("CPF");
        edtCPF.setHelpCaption("CPF");
        edtCPF.setFlex(true);
        edtCPF.setRequired(false);
        edtCPF.setPrompt("CPF");
        edtCPF.setConstraintCheckWhen("cwImmediate");
        edtCPF.setConstraintCheckType("ctExpression");
        edtCPF.setConstraintFocusOnError(false);
        edtCPF.setConstraintEnableUI(true);
        edtCPF.setConstraintEnabled(false);
        edtCPF.setConstraintFormCheck(true);
        edtCPF.setCharCase("ccNormal");
        edtCPF.setPwd(false);
        edtCPF.setMaxlength(0);
        edtCPF.setFontColor("clWindowText");
        edtCPF.setFontSize(-13);
        edtCPF.setFontName("Tahoma");
        edtCPF.setFontStyle("[]");
        edtCPF.setAlignment("taRightJustify");
        edtCPF.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCPFChange(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "edtCPF", "OnChange");
        });
        edtCPF.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCPFEnter(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "edtCPF", "OnEnter");
        });
        edtCPF.setSaveLiteralCharacter(false);
        edtCPF.applyProperties();
        hBoxCPF.addChildren(edtCPF);
        addValidatable(edtCPF);
    }

    public TFHBox hBoxCPFSeparador02 = new TFHBox();

    private void init_hBoxCPFSeparador02() {
        hBoxCPFSeparador02.setName("hBoxCPFSeparador02");
        hBoxCPFSeparador02.setLeft(285);
        hBoxCPFSeparador02.setTop(0);
        hBoxCPFSeparador02.setWidth(5);
        hBoxCPFSeparador02.setHeight(20);
        hBoxCPFSeparador02.setBorderStyle("stNone");
        hBoxCPFSeparador02.setPaddingTop(0);
        hBoxCPFSeparador02.setPaddingLeft(0);
        hBoxCPFSeparador02.setPaddingRight(0);
        hBoxCPFSeparador02.setPaddingBottom(0);
        hBoxCPFSeparador02.setMarginTop(0);
        hBoxCPFSeparador02.setMarginLeft(0);
        hBoxCPFSeparador02.setMarginRight(0);
        hBoxCPFSeparador02.setMarginBottom(0);
        hBoxCPFSeparador02.setSpacing(1);
        hBoxCPFSeparador02.setFlexVflex("ftFalse");
        hBoxCPFSeparador02.setFlexHflex("ftFalse");
        hBoxCPFSeparador02.setScrollable(false);
        hBoxCPFSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxCPFSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxCPFSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxCPFSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxCPFSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxCPFSeparador02.setBoxShadowConfigOpacity(75);
        hBoxCPFSeparador02.setVAlign("tvTop");
        hBoxCPF.addChildren(hBoxCPFSeparador02);
        hBoxCPFSeparador02.applyProperties();
    }

    public TFHBox hBoxSeparadorVertical07 = new TFHBox();

    private void init_hBoxSeparadorVertical07() {
        hBoxSeparadorVertical07.setName("hBoxSeparadorVertical07");
        hBoxSeparadorVertical07.setLeft(0);
        hBoxSeparadorVertical07.setTop(252);
        hBoxSeparadorVertical07.setWidth(370);
        hBoxSeparadorVertical07.setHeight(5);
        hBoxSeparadorVertical07.setBorderStyle("stNone");
        hBoxSeparadorVertical07.setPaddingTop(0);
        hBoxSeparadorVertical07.setPaddingLeft(0);
        hBoxSeparadorVertical07.setPaddingRight(0);
        hBoxSeparadorVertical07.setPaddingBottom(0);
        hBoxSeparadorVertical07.setMarginTop(0);
        hBoxSeparadorVertical07.setMarginLeft(0);
        hBoxSeparadorVertical07.setMarginRight(0);
        hBoxSeparadorVertical07.setMarginBottom(0);
        hBoxSeparadorVertical07.setSpacing(1);
        hBoxSeparadorVertical07.setFlexVflex("ftFalse");
        hBoxSeparadorVertical07.setFlexHflex("ftTrue");
        hBoxSeparadorVertical07.setScrollable(false);
        hBoxSeparadorVertical07.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorVertical07.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorVertical07.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorVertical07.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorVertical07.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorVertical07.setBoxShadowConfigOpacity(75);
        hBoxSeparadorVertical07.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxSeparadorVertical07);
        hBoxSeparadorVertical07.applyProperties();
    }

    public TFHBox hBoxAtivo = new TFHBox();

    private void init_hBoxAtivo() {
        hBoxAtivo.setName("hBoxAtivo");
        hBoxAtivo.setLeft(0);
        hBoxAtivo.setTop(258);
        hBoxAtivo.setWidth(370);
        hBoxAtivo.setHeight(35);
        hBoxAtivo.setBorderStyle("stNone");
        hBoxAtivo.setPaddingTop(0);
        hBoxAtivo.setPaddingLeft(0);
        hBoxAtivo.setPaddingRight(0);
        hBoxAtivo.setPaddingBottom(0);
        hBoxAtivo.setMarginTop(0);
        hBoxAtivo.setMarginLeft(0);
        hBoxAtivo.setMarginRight(0);
        hBoxAtivo.setMarginBottom(0);
        hBoxAtivo.setSpacing(1);
        hBoxAtivo.setFlexVflex("ftMin");
        hBoxAtivo.setFlexHflex("ftTrue");
        hBoxAtivo.setScrollable(false);
        hBoxAtivo.setBoxShadowConfigHorizontalLength(10);
        hBoxAtivo.setBoxShadowConfigVerticalLength(10);
        hBoxAtivo.setBoxShadowConfigBlurRadius(5);
        hBoxAtivo.setBoxShadowConfigSpreadRadius(0);
        hBoxAtivo.setBoxShadowConfigShadowColor("clBlack");
        hBoxAtivo.setBoxShadowConfigOpacity(75);
        hBoxAtivo.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxAtivo);
        hBoxAtivo.applyProperties();
    }

    public TFHBox hBoxAtivoSeparador01 = new TFHBox();

    private void init_hBoxAtivoSeparador01() {
        hBoxAtivoSeparador01.setName("hBoxAtivoSeparador01");
        hBoxAtivoSeparador01.setLeft(0);
        hBoxAtivoSeparador01.setTop(0);
        hBoxAtivoSeparador01.setWidth(5);
        hBoxAtivoSeparador01.setHeight(20);
        hBoxAtivoSeparador01.setBorderStyle("stNone");
        hBoxAtivoSeparador01.setPaddingTop(0);
        hBoxAtivoSeparador01.setPaddingLeft(0);
        hBoxAtivoSeparador01.setPaddingRight(0);
        hBoxAtivoSeparador01.setPaddingBottom(0);
        hBoxAtivoSeparador01.setMarginTop(0);
        hBoxAtivoSeparador01.setMarginLeft(0);
        hBoxAtivoSeparador01.setMarginRight(0);
        hBoxAtivoSeparador01.setMarginBottom(0);
        hBoxAtivoSeparador01.setSpacing(1);
        hBoxAtivoSeparador01.setFlexVflex("ftFalse");
        hBoxAtivoSeparador01.setFlexHflex("ftFalse");
        hBoxAtivoSeparador01.setScrollable(false);
        hBoxAtivoSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxAtivoSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxAtivoSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxAtivoSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxAtivoSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxAtivoSeparador01.setBoxShadowConfigOpacity(75);
        hBoxAtivoSeparador01.setVAlign("tvTop");
        hBoxAtivo.addChildren(hBoxAtivoSeparador01);
        hBoxAtivoSeparador01.applyProperties();
    }

    public TFVBox vBoxAtivoLbl = new TFVBox();

    private void init_vBoxAtivoLbl() {
        vBoxAtivoLbl.setName("vBoxAtivoLbl");
        vBoxAtivoLbl.setLeft(5);
        vBoxAtivoLbl.setTop(0);
        vBoxAtivoLbl.setWidth(80);
        vBoxAtivoLbl.setHeight(30);
        vBoxAtivoLbl.setBorderStyle("stNone");
        vBoxAtivoLbl.setPaddingTop(0);
        vBoxAtivoLbl.setPaddingLeft(0);
        vBoxAtivoLbl.setPaddingRight(0);
        vBoxAtivoLbl.setPaddingBottom(0);
        vBoxAtivoLbl.setMarginTop(0);
        vBoxAtivoLbl.setMarginLeft(0);
        vBoxAtivoLbl.setMarginRight(0);
        vBoxAtivoLbl.setMarginBottom(0);
        vBoxAtivoLbl.setSpacing(1);
        vBoxAtivoLbl.setFlexVflex("ftMin");
        vBoxAtivoLbl.setFlexHflex("ftFalse");
        vBoxAtivoLbl.setScrollable(false);
        vBoxAtivoLbl.setBoxShadowConfigHorizontalLength(10);
        vBoxAtivoLbl.setBoxShadowConfigVerticalLength(10);
        vBoxAtivoLbl.setBoxShadowConfigBlurRadius(5);
        vBoxAtivoLbl.setBoxShadowConfigSpreadRadius(0);
        vBoxAtivoLbl.setBoxShadowConfigShadowColor("clBlack");
        vBoxAtivoLbl.setBoxShadowConfigOpacity(75);
        hBoxAtivo.addChildren(vBoxAtivoLbl);
        vBoxAtivoLbl.applyProperties();
    }

    public TFHBox hBoxAtivoLblSeparador01 = new TFHBox();

    private void init_hBoxAtivoLblSeparador01() {
        hBoxAtivoLblSeparador01.setName("hBoxAtivoLblSeparador01");
        hBoxAtivoLblSeparador01.setLeft(0);
        hBoxAtivoLblSeparador01.setTop(0);
        hBoxAtivoLblSeparador01.setWidth(20);
        hBoxAtivoLblSeparador01.setHeight(5);
        hBoxAtivoLblSeparador01.setBorderStyle("stNone");
        hBoxAtivoLblSeparador01.setPaddingTop(0);
        hBoxAtivoLblSeparador01.setPaddingLeft(0);
        hBoxAtivoLblSeparador01.setPaddingRight(0);
        hBoxAtivoLblSeparador01.setPaddingBottom(0);
        hBoxAtivoLblSeparador01.setMarginTop(0);
        hBoxAtivoLblSeparador01.setMarginLeft(0);
        hBoxAtivoLblSeparador01.setMarginRight(0);
        hBoxAtivoLblSeparador01.setMarginBottom(0);
        hBoxAtivoLblSeparador01.setSpacing(1);
        hBoxAtivoLblSeparador01.setFlexVflex("ftFalse");
        hBoxAtivoLblSeparador01.setFlexHflex("ftFalse");
        hBoxAtivoLblSeparador01.setScrollable(false);
        hBoxAtivoLblSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxAtivoLblSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxAtivoLblSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxAtivoLblSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxAtivoLblSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxAtivoLblSeparador01.setBoxShadowConfigOpacity(75);
        hBoxAtivoLblSeparador01.setVAlign("tvTop");
        vBoxAtivoLbl.addChildren(hBoxAtivoLblSeparador01);
        hBoxAtivoLblSeparador01.applyProperties();
    }

    public TFLabel lblAtivo = new TFLabel();

    private void init_lblAtivo() {
        lblAtivo.setName("lblAtivo");
        lblAtivo.setLeft(0);
        lblAtivo.setTop(6);
        lblAtivo.setWidth(25);
        lblAtivo.setHeight(13);
        lblAtivo.setCaption("Ativo");
        lblAtivo.setFontColor("clWindowText");
        lblAtivo.setFontSize(-11);
        lblAtivo.setFontName("Tahoma");
        lblAtivo.setFontStyle("[]");
        lblAtivo.setVerticalAlignment("taVerticalCenter");
        lblAtivo.setWordBreak(false);
        vBoxAtivoLbl.addChildren(lblAtivo);
        lblAtivo.applyProperties();
    }

    public TFCombo cboAtivo = new TFCombo();

    private void init_cboAtivo() {
        cboAtivo.setName("cboAtivo");
        cboAtivo.setLeft(85);
        cboAtivo.setTop(0);
        cboAtivo.setWidth(200);
        cboAtivo.setHeight(21);
        cboAtivo.setHint("Ativo");
        cboAtivo.setFlex(true);
        cboAtivo.setListOptions("Todos=T;Sim=S;N\u00E3o=N");
        cboAtivo.setHelpCaption("Ativo");
        cboAtivo.setReadOnly(true);
        cboAtivo.setRequired(true);
        cboAtivo.setPrompt("Ativo");
        cboAtivo.setConstraintCheckWhen("cwImmediate");
        cboAtivo.setConstraintCheckType("ctExpression");
        cboAtivo.setConstraintFocusOnError(false);
        cboAtivo.setConstraintEnableUI(true);
        cboAtivo.setConstraintEnabled(false);
        cboAtivo.setConstraintFormCheck(true);
        cboAtivo.setClearOnDelKey(false);
        cboAtivo.setUseClearButton(false);
        cboAtivo.setHideClearButtonOnNullValue(true);
        cboAtivo.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboAtivoEnter(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboAtivo", "OnEnter");
        });
        hBoxAtivo.addChildren(cboAtivo);
        cboAtivo.applyProperties();
        addValidatable(cboAtivo);
    }

    public TFHBox hBoxAtivoSeparador02 = new TFHBox();

    private void init_hBoxAtivoSeparador02() {
        hBoxAtivoSeparador02.setName("hBoxAtivoSeparador02");
        hBoxAtivoSeparador02.setLeft(285);
        hBoxAtivoSeparador02.setTop(0);
        hBoxAtivoSeparador02.setWidth(5);
        hBoxAtivoSeparador02.setHeight(20);
        hBoxAtivoSeparador02.setBorderStyle("stNone");
        hBoxAtivoSeparador02.setPaddingTop(0);
        hBoxAtivoSeparador02.setPaddingLeft(0);
        hBoxAtivoSeparador02.setPaddingRight(0);
        hBoxAtivoSeparador02.setPaddingBottom(0);
        hBoxAtivoSeparador02.setMarginTop(0);
        hBoxAtivoSeparador02.setMarginLeft(0);
        hBoxAtivoSeparador02.setMarginRight(0);
        hBoxAtivoSeparador02.setMarginBottom(0);
        hBoxAtivoSeparador02.setSpacing(1);
        hBoxAtivoSeparador02.setFlexVflex("ftFalse");
        hBoxAtivoSeparador02.setFlexHflex("ftFalse");
        hBoxAtivoSeparador02.setScrollable(false);
        hBoxAtivoSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxAtivoSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxAtivoSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxAtivoSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxAtivoSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxAtivoSeparador02.setBoxShadowConfigOpacity(75);
        hBoxAtivoSeparador02.setVAlign("tvTop");
        hBoxAtivo.addChildren(hBoxAtivoSeparador02);
        hBoxAtivoSeparador02.applyProperties();
    }

    public TFHBox hBoxSeparadorVertical08 = new TFHBox();

    private void init_hBoxSeparadorVertical08() {
        hBoxSeparadorVertical08.setName("hBoxSeparadorVertical08");
        hBoxSeparadorVertical08.setLeft(0);
        hBoxSeparadorVertical08.setTop(294);
        hBoxSeparadorVertical08.setWidth(370);
        hBoxSeparadorVertical08.setHeight(5);
        hBoxSeparadorVertical08.setBorderStyle("stNone");
        hBoxSeparadorVertical08.setPaddingTop(0);
        hBoxSeparadorVertical08.setPaddingLeft(0);
        hBoxSeparadorVertical08.setPaddingRight(0);
        hBoxSeparadorVertical08.setPaddingBottom(0);
        hBoxSeparadorVertical08.setMarginTop(0);
        hBoxSeparadorVertical08.setMarginLeft(0);
        hBoxSeparadorVertical08.setMarginRight(0);
        hBoxSeparadorVertical08.setMarginBottom(0);
        hBoxSeparadorVertical08.setSpacing(1);
        hBoxSeparadorVertical08.setFlexVflex("ftFalse");
        hBoxSeparadorVertical08.setFlexHflex("ftTrue");
        hBoxSeparadorVertical08.setScrollable(false);
        hBoxSeparadorVertical08.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorVertical08.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorVertical08.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorVertical08.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorVertical08.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorVertical08.setBoxShadowConfigOpacity(75);
        hBoxSeparadorVertical08.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxSeparadorVertical08);
        hBoxSeparadorVertical08.applyProperties();
    }

    public TFHBox hBoxCadBD = new TFHBox();

    private void init_hBoxCadBD() {
        hBoxCadBD.setName("hBoxCadBD");
        hBoxCadBD.setLeft(0);
        hBoxCadBD.setTop(300);
        hBoxCadBD.setWidth(370);
        hBoxCadBD.setHeight(35);
        hBoxCadBD.setBorderStyle("stNone");
        hBoxCadBD.setPaddingTop(0);
        hBoxCadBD.setPaddingLeft(0);
        hBoxCadBD.setPaddingRight(0);
        hBoxCadBD.setPaddingBottom(0);
        hBoxCadBD.setMarginTop(0);
        hBoxCadBD.setMarginLeft(0);
        hBoxCadBD.setMarginRight(0);
        hBoxCadBD.setMarginBottom(0);
        hBoxCadBD.setSpacing(1);
        hBoxCadBD.setFlexVflex("ftMin");
        hBoxCadBD.setFlexHflex("ftTrue");
        hBoxCadBD.setScrollable(false);
        hBoxCadBD.setBoxShadowConfigHorizontalLength(10);
        hBoxCadBD.setBoxShadowConfigVerticalLength(10);
        hBoxCadBD.setBoxShadowConfigBlurRadius(5);
        hBoxCadBD.setBoxShadowConfigSpreadRadius(0);
        hBoxCadBD.setBoxShadowConfigShadowColor("clBlack");
        hBoxCadBD.setBoxShadowConfigOpacity(75);
        hBoxCadBD.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxCadBD);
        hBoxCadBD.applyProperties();
    }

    public TFHBox hBoxCadBDSeparador01 = new TFHBox();

    private void init_hBoxCadBDSeparador01() {
        hBoxCadBDSeparador01.setName("hBoxCadBDSeparador01");
        hBoxCadBDSeparador01.setLeft(0);
        hBoxCadBDSeparador01.setTop(0);
        hBoxCadBDSeparador01.setWidth(5);
        hBoxCadBDSeparador01.setHeight(20);
        hBoxCadBDSeparador01.setBorderStyle("stNone");
        hBoxCadBDSeparador01.setPaddingTop(0);
        hBoxCadBDSeparador01.setPaddingLeft(0);
        hBoxCadBDSeparador01.setPaddingRight(0);
        hBoxCadBDSeparador01.setPaddingBottom(0);
        hBoxCadBDSeparador01.setMarginTop(0);
        hBoxCadBDSeparador01.setMarginLeft(0);
        hBoxCadBDSeparador01.setMarginRight(0);
        hBoxCadBDSeparador01.setMarginBottom(0);
        hBoxCadBDSeparador01.setSpacing(1);
        hBoxCadBDSeparador01.setFlexVflex("ftFalse");
        hBoxCadBDSeparador01.setFlexHflex("ftFalse");
        hBoxCadBDSeparador01.setScrollable(false);
        hBoxCadBDSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxCadBDSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxCadBDSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxCadBDSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxCadBDSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxCadBDSeparador01.setBoxShadowConfigOpacity(75);
        hBoxCadBDSeparador01.setVAlign("tvTop");
        hBoxCadBD.addChildren(hBoxCadBDSeparador01);
        hBoxCadBDSeparador01.applyProperties();
    }

    public TFVBox vBoxCadBDLbl = new TFVBox();

    private void init_vBoxCadBDLbl() {
        vBoxCadBDLbl.setName("vBoxCadBDLbl");
        vBoxCadBDLbl.setLeft(5);
        vBoxCadBDLbl.setTop(0);
        vBoxCadBDLbl.setWidth(80);
        vBoxCadBDLbl.setHeight(30);
        vBoxCadBDLbl.setBorderStyle("stNone");
        vBoxCadBDLbl.setPaddingTop(0);
        vBoxCadBDLbl.setPaddingLeft(0);
        vBoxCadBDLbl.setPaddingRight(0);
        vBoxCadBDLbl.setPaddingBottom(0);
        vBoxCadBDLbl.setMarginTop(0);
        vBoxCadBDLbl.setMarginLeft(0);
        vBoxCadBDLbl.setMarginRight(0);
        vBoxCadBDLbl.setMarginBottom(0);
        vBoxCadBDLbl.setSpacing(1);
        vBoxCadBDLbl.setFlexVflex("ftMin");
        vBoxCadBDLbl.setFlexHflex("ftFalse");
        vBoxCadBDLbl.setScrollable(false);
        vBoxCadBDLbl.setBoxShadowConfigHorizontalLength(10);
        vBoxCadBDLbl.setBoxShadowConfigVerticalLength(10);
        vBoxCadBDLbl.setBoxShadowConfigBlurRadius(5);
        vBoxCadBDLbl.setBoxShadowConfigSpreadRadius(0);
        vBoxCadBDLbl.setBoxShadowConfigShadowColor("clBlack");
        vBoxCadBDLbl.setBoxShadowConfigOpacity(75);
        hBoxCadBD.addChildren(vBoxCadBDLbl);
        vBoxCadBDLbl.applyProperties();
    }

    public TFHBox hBoxCadBDLblSeparador01 = new TFHBox();

    private void init_hBoxCadBDLblSeparador01() {
        hBoxCadBDLblSeparador01.setName("hBoxCadBDLblSeparador01");
        hBoxCadBDLblSeparador01.setLeft(0);
        hBoxCadBDLblSeparador01.setTop(0);
        hBoxCadBDLblSeparador01.setWidth(20);
        hBoxCadBDLblSeparador01.setHeight(5);
        hBoxCadBDLblSeparador01.setBorderStyle("stNone");
        hBoxCadBDLblSeparador01.setPaddingTop(0);
        hBoxCadBDLblSeparador01.setPaddingLeft(0);
        hBoxCadBDLblSeparador01.setPaddingRight(0);
        hBoxCadBDLblSeparador01.setPaddingBottom(0);
        hBoxCadBDLblSeparador01.setMarginTop(0);
        hBoxCadBDLblSeparador01.setMarginLeft(0);
        hBoxCadBDLblSeparador01.setMarginRight(0);
        hBoxCadBDLblSeparador01.setMarginBottom(0);
        hBoxCadBDLblSeparador01.setSpacing(1);
        hBoxCadBDLblSeparador01.setFlexVflex("ftFalse");
        hBoxCadBDLblSeparador01.setFlexHflex("ftFalse");
        hBoxCadBDLblSeparador01.setScrollable(false);
        hBoxCadBDLblSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxCadBDLblSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxCadBDLblSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxCadBDLblSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxCadBDLblSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxCadBDLblSeparador01.setBoxShadowConfigOpacity(75);
        hBoxCadBDLblSeparador01.setVAlign("tvTop");
        vBoxCadBDLbl.addChildren(hBoxCadBDLblSeparador01);
        hBoxCadBDLblSeparador01.applyProperties();
    }

    public TFLabel lblCadBD = new TFLabel();

    private void init_lblCadBD() {
        lblCadBD.setName("lblCadBD");
        lblCadBD.setLeft(0);
        lblCadBD.setTop(6);
        lblCadBD.setWidth(39);
        lblCadBD.setHeight(13);
        lblCadBD.setCaption("Cad. BD");
        lblCadBD.setFontColor("clWindowText");
        lblCadBD.setFontSize(-11);
        lblCadBD.setFontName("Tahoma");
        lblCadBD.setFontStyle("[]");
        lblCadBD.setVerticalAlignment("taVerticalCenter");
        lblCadBD.setWordBreak(false);
        vBoxCadBDLbl.addChildren(lblCadBD);
        lblCadBD.applyProperties();
    }

    public TFCombo cboCadBD = new TFCombo();

    private void init_cboCadBD() {
        cboCadBD.setName("cboCadBD");
        cboCadBD.setLeft(85);
        cboCadBD.setTop(0);
        cboCadBD.setWidth(200);
        cboCadBD.setHeight(21);
        cboCadBD.setHint("Ativo");
        cboCadBD.setFlex(true);
        cboCadBD.setListOptions("Todos=T;Sim=S;N\u00E3o=N");
        cboCadBD.setHelpCaption("Ativo");
        cboCadBD.setReadOnly(true);
        cboCadBD.setRequired(true);
        cboCadBD.setPrompt("Ativo");
        cboCadBD.setConstraintCheckWhen("cwImmediate");
        cboCadBD.setConstraintCheckType("ctExpression");
        cboCadBD.setConstraintFocusOnError(false);
        cboCadBD.setConstraintEnableUI(true);
        cboCadBD.setConstraintEnabled(false);
        cboCadBD.setConstraintFormCheck(true);
        cboCadBD.setClearOnDelKey(false);
        cboCadBD.setUseClearButton(false);
        cboCadBD.setHideClearButtonOnNullValue(true);
        cboCadBD.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboCadBDEnter(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "cboCadBD", "OnEnter");
        });
        hBoxCadBD.addChildren(cboCadBD);
        cboCadBD.applyProperties();
        addValidatable(cboCadBD);
    }

    public TFHBox hBoxCadBDSeparador02 = new TFHBox();

    private void init_hBoxCadBDSeparador02() {
        hBoxCadBDSeparador02.setName("hBoxCadBDSeparador02");
        hBoxCadBDSeparador02.setLeft(285);
        hBoxCadBDSeparador02.setTop(0);
        hBoxCadBDSeparador02.setWidth(5);
        hBoxCadBDSeparador02.setHeight(20);
        hBoxCadBDSeparador02.setBorderStyle("stNone");
        hBoxCadBDSeparador02.setPaddingTop(0);
        hBoxCadBDSeparador02.setPaddingLeft(0);
        hBoxCadBDSeparador02.setPaddingRight(0);
        hBoxCadBDSeparador02.setPaddingBottom(0);
        hBoxCadBDSeparador02.setMarginTop(0);
        hBoxCadBDSeparador02.setMarginLeft(0);
        hBoxCadBDSeparador02.setMarginRight(0);
        hBoxCadBDSeparador02.setMarginBottom(0);
        hBoxCadBDSeparador02.setSpacing(1);
        hBoxCadBDSeparador02.setFlexVflex("ftFalse");
        hBoxCadBDSeparador02.setFlexHflex("ftFalse");
        hBoxCadBDSeparador02.setScrollable(false);
        hBoxCadBDSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxCadBDSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxCadBDSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxCadBDSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxCadBDSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxCadBDSeparador02.setBoxShadowConfigOpacity(75);
        hBoxCadBDSeparador02.setVAlign("tvTop");
        hBoxCadBD.addChildren(hBoxCadBDSeparador02);
        hBoxCadBDSeparador02.applyProperties();
    }

    public TFHBox hBoxSeparadorVertical09 = new TFHBox();

    private void init_hBoxSeparadorVertical09() {
        hBoxSeparadorVertical09.setName("hBoxSeparadorVertical09");
        hBoxSeparadorVertical09.setLeft(0);
        hBoxSeparadorVertical09.setTop(336);
        hBoxSeparadorVertical09.setWidth(370);
        hBoxSeparadorVertical09.setHeight(5);
        hBoxSeparadorVertical09.setBorderStyle("stNone");
        hBoxSeparadorVertical09.setPaddingTop(0);
        hBoxSeparadorVertical09.setPaddingLeft(0);
        hBoxSeparadorVertical09.setPaddingRight(0);
        hBoxSeparadorVertical09.setPaddingBottom(0);
        hBoxSeparadorVertical09.setMarginTop(0);
        hBoxSeparadorVertical09.setMarginLeft(0);
        hBoxSeparadorVertical09.setMarginRight(0);
        hBoxSeparadorVertical09.setMarginBottom(0);
        hBoxSeparadorVertical09.setSpacing(1);
        hBoxSeparadorVertical09.setFlexVflex("ftFalse");
        hBoxSeparadorVertical09.setFlexHflex("ftTrue");
        hBoxSeparadorVertical09.setScrollable(false);
        hBoxSeparadorVertical09.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorVertical09.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorVertical09.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorVertical09.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorVertical09.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorVertical09.setBoxShadowConfigOpacity(75);
        hBoxSeparadorVertical09.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxSeparadorVertical09);
        hBoxSeparadorVertical09.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(342);
        hBoxBotoes.setWidth(370);
        hBoxBotoes.setHeight(60);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(1);
        hBoxBotoes.setFlexVflex("ftMin");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFHBox hBoxBotoesSeparador01 = new TFHBox();

    private void init_hBoxBotoesSeparador01() {
        hBoxBotoesSeparador01.setName("hBoxBotoesSeparador01");
        hBoxBotoesSeparador01.setLeft(0);
        hBoxBotoesSeparador01.setTop(0);
        hBoxBotoesSeparador01.setWidth(183);
        hBoxBotoesSeparador01.setHeight(5);
        hBoxBotoesSeparador01.setBorderStyle("stNone");
        hBoxBotoesSeparador01.setPaddingTop(0);
        hBoxBotoesSeparador01.setPaddingLeft(0);
        hBoxBotoesSeparador01.setPaddingRight(0);
        hBoxBotoesSeparador01.setPaddingBottom(0);
        hBoxBotoesSeparador01.setMarginTop(0);
        hBoxBotoesSeparador01.setMarginLeft(0);
        hBoxBotoesSeparador01.setMarginRight(0);
        hBoxBotoesSeparador01.setMarginBottom(0);
        hBoxBotoesSeparador01.setSpacing(1);
        hBoxBotoesSeparador01.setFlexVflex("ftFalse");
        hBoxBotoesSeparador01.setFlexHflex("ftTrue");
        hBoxBotoesSeparador01.setScrollable(false);
        hBoxBotoesSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesSeparador01.setBoxShadowConfigOpacity(75);
        hBoxBotoesSeparador01.setVAlign("tvTop");
        hBoxBotoes.addChildren(hBoxBotoesSeparador01);
        hBoxBotoesSeparador01.applyProperties();
    }

    public TFButton btnLimpar = new TFButton();

    private void init_btnLimpar() {
        btnLimpar.setName("btnLimpar");
        btnLimpar.setLeft(183);
        btnLimpar.setTop(0);
        btnLimpar.setWidth(55);
        btnLimpar.setHeight(55);
        btnLimpar.setHint("Limpar os filtros");
        btnLimpar.setCaption("Limpar");
        btnLimpar.setFontColor("clWindowText");
        btnLimpar.setFontSize(-11);
        btnLimpar.setFontName("Tahoma");
        btnLimpar.setFontStyle("[]");
        btnLimpar.setLayout("blGlyphTop");
        btnLimpar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLimparClick(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "btnLimpar", "OnClick");
        });
        btnLimpar.setImageId(4600235);
        btnLimpar.setColor("clBtnFace");
        btnLimpar.setAccess(false);
        btnLimpar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnLimpar);
        btnLimpar.applyProperties();
    }

    public TFHBox hBoxBotoesSeparador02 = new TFHBox();

    private void init_hBoxBotoesSeparador02() {
        hBoxBotoesSeparador02.setName("hBoxBotoesSeparador02");
        hBoxBotoesSeparador02.setLeft(238);
        hBoxBotoesSeparador02.setTop(0);
        hBoxBotoesSeparador02.setWidth(5);
        hBoxBotoesSeparador02.setHeight(20);
        hBoxBotoesSeparador02.setBorderStyle("stNone");
        hBoxBotoesSeparador02.setPaddingTop(0);
        hBoxBotoesSeparador02.setPaddingLeft(0);
        hBoxBotoesSeparador02.setPaddingRight(0);
        hBoxBotoesSeparador02.setPaddingBottom(0);
        hBoxBotoesSeparador02.setMarginTop(0);
        hBoxBotoesSeparador02.setMarginLeft(0);
        hBoxBotoesSeparador02.setMarginRight(0);
        hBoxBotoesSeparador02.setMarginBottom(0);
        hBoxBotoesSeparador02.setSpacing(1);
        hBoxBotoesSeparador02.setFlexVflex("ftFalse");
        hBoxBotoesSeparador02.setFlexHflex("ftFalse");
        hBoxBotoesSeparador02.setScrollable(false);
        hBoxBotoesSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesSeparador02.setBoxShadowConfigOpacity(75);
        hBoxBotoesSeparador02.setVAlign("tvTop");
        hBoxBotoes.addChildren(hBoxBotoesSeparador02);
        hBoxBotoesSeparador02.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(243);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(55);
        btnPesquisar.setHeight(55);
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmPesquisaAvancadaAgentesFuncao", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(0);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconClass("search");
        btnPesquisar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFHBox hBoxBotoesSeparador03 = new TFHBox();

    private void init_hBoxBotoesSeparador03() {
        hBoxBotoesSeparador03.setName("hBoxBotoesSeparador03");
        hBoxBotoesSeparador03.setLeft(298);
        hBoxBotoesSeparador03.setTop(0);
        hBoxBotoesSeparador03.setWidth(5);
        hBoxBotoesSeparador03.setHeight(20);
        hBoxBotoesSeparador03.setBorderStyle("stNone");
        hBoxBotoesSeparador03.setPaddingTop(0);
        hBoxBotoesSeparador03.setPaddingLeft(0);
        hBoxBotoesSeparador03.setPaddingRight(0);
        hBoxBotoesSeparador03.setPaddingBottom(0);
        hBoxBotoesSeparador03.setMarginTop(0);
        hBoxBotoesSeparador03.setMarginLeft(0);
        hBoxBotoesSeparador03.setMarginRight(0);
        hBoxBotoesSeparador03.setMarginBottom(0);
        hBoxBotoesSeparador03.setSpacing(1);
        hBoxBotoesSeparador03.setFlexVflex("ftFalse");
        hBoxBotoesSeparador03.setFlexHflex("ftFalse");
        hBoxBotoesSeparador03.setScrollable(false);
        hBoxBotoesSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesSeparador03.setBoxShadowConfigOpacity(75);
        hBoxBotoesSeparador03.setVAlign("tvTop");
        hBoxBotoes.addChildren(hBoxBotoesSeparador03);
        hBoxBotoesSeparador03.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void edtLoginEnter(final Event<Object> event);

    public abstract void edtLoginExit(final Event<Object> event);

    public abstract void edtNomeEnter(final Event<Object> event);

    public abstract void edtNomeExit(final Event<Object> event);

    public abstract void cboEmpresaChange(final Event<Object> event);

    public abstract void cboEmpresaEnter(final Event<Object> event);

    public abstract void cboEmpresaClearClick(final Event<Object> event);

    public abstract void cboDepartamentoChange(final Event<Object> event);

    public abstract void cboDepartamentoEnter(final Event<Object> event);

    public abstract void cboDepartamentoClearClick(final Event<Object> event);

    public abstract void cboDivisaoChange(final Event<Object> event);

    public abstract void cboDivisaoEnter(final Event<Object> event);

    public abstract void edtCPFChange(final Event<Object> event);

    public abstract void edtCPFEnter(final Event<Object> event);

    public abstract void cboAtivoEnter(final Event<Object> event);

    public abstract void cboCadBDEnter(final Event<Object> event);

    public void btnLimparClick(final Event<Object> event) {
        if (btnLimpar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLimpar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}