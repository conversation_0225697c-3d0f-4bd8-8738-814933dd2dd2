package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCadastroRapidoClienteProdRural extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CadastroRapidoClienteProdRuralRNA rn = null;

    public FrmCadastroRapidoClienteProdRural() {
        try {
            rn = (freedom.bytecode.rn.CadastroRapidoClienteProdRuralRNA) getRN(freedom.bytecode.rn.wizard.CadastroRapidoClienteProdRuralRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbUf();
        init_vBoxPrincipal();
        init_hBoxPrincipalSeparador01();
        init_hBoxBotoesTopo();
        init_btnVoltar();
        init_btnConfirmar();
        init_hBoxPrincipalSeparador02();
        init_hBoxDocumentoDataNascimentoEhProdutorRural();
        init_hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01();
        init_vBoxTipoDocumento();
        init_lblDocumento();
        init_edtCPFCNPJ();
        init_hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02();
        init_vBoxDataNascimento();
        init_lblDataNascimento();
        init_edtDataNasc();
        init_hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03();
        init_vBoxEhProdutorRural();
        init_lblProdutorRural();
        init_cboProdutorRural();
        init_hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04();
        init_hBoxPrincipalSeparador03();
        init_hBoxUFIE();
        init_hBoxUFIESeparador01();
        init_vBoxUF();
        init_lblUF();
        init_edtUF();
        init_hBoxUFIESeparador02();
        init_vBoxIE();
        init_lblInscricaoEstadual();
        init_edtInscricaoEstadual();
        init_hBoxUFIESeparador03();
        init_FrmCadastroRapidoClienteProdRural();
    }

    public UF tbUf;

    private void init_tbUf() {
        tbUf = rn.tbUf;
        tbUf.setName("tbUf");
        tbUf.setMaxRowCount(200);
        tbUf.setWKey("5300731;53001");
        tbUf.setRatioBatchSize(20);
        getTables().put(tbUf, "tbUf");
        tbUf.applyProperties();
    }

    protected TFForm FrmCadastroRapidoClienteProdRural = this;
    private void init_FrmCadastroRapidoClienteProdRural() {
        FrmCadastroRapidoClienteProdRural.setName("FrmCadastroRapidoClienteProdRural");
        FrmCadastroRapidoClienteProdRural.setCaption("Cadastro Rapido Cliente Produtor Rural");
        FrmCadastroRapidoClienteProdRural.setClientHeight(217);
        FrmCadastroRapidoClienteProdRural.setClientWidth(494);
        FrmCadastroRapidoClienteProdRural.setColor("clBtnFace");
        FrmCadastroRapidoClienteProdRural.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmCadastroRapidoClienteProdRural", "FrmCadastroRapidoClienteProdRural", "OnCreate");
        });
        FrmCadastroRapidoClienteProdRural.setWOrigem("EhMain");
        FrmCadastroRapidoClienteProdRural.setWKey("5300731");
        FrmCadastroRapidoClienteProdRural.setSpacing(0);
        FrmCadastroRapidoClienteProdRural.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(494);
        vBoxPrincipal.setHeight(217);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(5);
        vBoxPrincipal.setPaddingLeft(5);
        vBoxPrincipal.setPaddingRight(5);
        vBoxPrincipal.setPaddingBottom(5);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmCadastroRapidoClienteProdRural.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador01 = new TFHBox();

    private void init_hBoxPrincipalSeparador01() {
        hBoxPrincipalSeparador01.setName("hBoxPrincipalSeparador01");
        hBoxPrincipalSeparador01.setLeft(0);
        hBoxPrincipalSeparador01.setTop(0);
        hBoxPrincipalSeparador01.setWidth(140);
        hBoxPrincipalSeparador01.setHeight(5);
        hBoxPrincipalSeparador01.setBorderStyle("stNone");
        hBoxPrincipalSeparador01.setPaddingTop(0);
        hBoxPrincipalSeparador01.setPaddingLeft(0);
        hBoxPrincipalSeparador01.setPaddingRight(0);
        hBoxPrincipalSeparador01.setPaddingBottom(0);
        hBoxPrincipalSeparador01.setMarginTop(0);
        hBoxPrincipalSeparador01.setMarginLeft(0);
        hBoxPrincipalSeparador01.setMarginRight(0);
        hBoxPrincipalSeparador01.setMarginBottom(0);
        hBoxPrincipalSeparador01.setSpacing(1);
        hBoxPrincipalSeparador01.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador01.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador01.setScrollable(false);
        hBoxPrincipalSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador01.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador01.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador01);
        hBoxPrincipalSeparador01.applyProperties();
    }

    public TFHBox hBoxBotoesTopo = new TFHBox();

    private void init_hBoxBotoesTopo() {
        hBoxBotoesTopo.setName("hBoxBotoesTopo");
        hBoxBotoesTopo.setLeft(0);
        hBoxBotoesTopo.setTop(6);
        hBoxBotoesTopo.setWidth(140);
        hBoxBotoesTopo.setHeight(57);
        hBoxBotoesTopo.setBorderStyle("stNone");
        hBoxBotoesTopo.setPaddingTop(0);
        hBoxBotoesTopo.setPaddingLeft(5);
        hBoxBotoesTopo.setPaddingRight(0);
        hBoxBotoesTopo.setPaddingBottom(0);
        hBoxBotoesTopo.setMarginTop(0);
        hBoxBotoesTopo.setMarginLeft(0);
        hBoxBotoesTopo.setMarginRight(0);
        hBoxBotoesTopo.setMarginBottom(0);
        hBoxBotoesTopo.setSpacing(5);
        hBoxBotoesTopo.setFlexVflex("ftMin");
        hBoxBotoesTopo.setFlexHflex("ftTrue");
        hBoxBotoesTopo.setScrollable(false);
        hBoxBotoesTopo.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesTopo.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesTopo.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesTopo.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesTopo.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesTopo.setBoxShadowConfigOpacity(75);
        hBoxBotoesTopo.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoesTopo);
        hBoxBotoesTopo.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(50);
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmCadastroRapidoClienteProdRural", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoesTopo.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnConfirmar = new TFButton();

    private void init_btnConfirmar() {
        btnConfirmar.setName("btnConfirmar");
        btnConfirmar.setLeft(65);
        btnConfirmar.setTop(0);
        btnConfirmar.setWidth(65);
        btnConfirmar.setHeight(50);
        btnConfirmar.setCaption("Confirmar");
        btnConfirmar.setFontColor("clWindowText");
        btnConfirmar.setFontSize(-11);
        btnConfirmar.setFontName("Tahoma");
        btnConfirmar.setFontStyle("[]");
        btnConfirmar.setLayout("blGlyphTop");
        btnConfirmar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteProdRural", "btnConfirmar", "OnClick");
        });
        btnConfirmar.setImageId(700075);
        btnConfirmar.setColor("clBtnFace");
        btnConfirmar.setAccess(false);
        btnConfirmar.setIconReverseDirection(false);
        hBoxBotoesTopo.addChildren(btnConfirmar);
        btnConfirmar.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador02 = new TFHBox();

    private void init_hBoxPrincipalSeparador02() {
        hBoxPrincipalSeparador02.setName("hBoxPrincipalSeparador02");
        hBoxPrincipalSeparador02.setLeft(0);
        hBoxPrincipalSeparador02.setTop(64);
        hBoxPrincipalSeparador02.setWidth(140);
        hBoxPrincipalSeparador02.setHeight(5);
        hBoxPrincipalSeparador02.setBorderStyle("stNone");
        hBoxPrincipalSeparador02.setPaddingTop(0);
        hBoxPrincipalSeparador02.setPaddingLeft(0);
        hBoxPrincipalSeparador02.setPaddingRight(0);
        hBoxPrincipalSeparador02.setPaddingBottom(0);
        hBoxPrincipalSeparador02.setMarginTop(0);
        hBoxPrincipalSeparador02.setMarginLeft(0);
        hBoxPrincipalSeparador02.setMarginRight(0);
        hBoxPrincipalSeparador02.setMarginBottom(0);
        hBoxPrincipalSeparador02.setSpacing(1);
        hBoxPrincipalSeparador02.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador02.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador02.setScrollable(false);
        hBoxPrincipalSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador02.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador02.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador02);
        hBoxPrincipalSeparador02.applyProperties();
    }

    public TFHBox hBoxDocumentoDataNascimentoEhProdutorRural = new TFHBox();

    private void init_hBoxDocumentoDataNascimentoEhProdutorRural() {
        hBoxDocumentoDataNascimentoEhProdutorRural.setName("hBoxDocumentoDataNascimentoEhProdutorRural");
        hBoxDocumentoDataNascimentoEhProdutorRural.setLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setTop(70);
        hBoxDocumentoDataNascimentoEhProdutorRural.setWidth(370);
        hBoxDocumentoDataNascimentoEhProdutorRural.setHeight(50);
        hBoxDocumentoDataNascimentoEhProdutorRural.setBorderStyle("stNone");
        hBoxDocumentoDataNascimentoEhProdutorRural.setPaddingTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setPaddingLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setPaddingRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setPaddingBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setMarginTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setMarginLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setMarginRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setMarginBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setSpacing(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setFlexVflex("ftMin");
        hBoxDocumentoDataNascimentoEhProdutorRural.setFlexHflex("ftTrue");
        hBoxDocumentoDataNascimentoEhProdutorRural.setScrollable(false);
        hBoxDocumentoDataNascimentoEhProdutorRural.setBoxShadowConfigHorizontalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRural.setBoxShadowConfigVerticalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRural.setBoxShadowConfigBlurRadius(5);
        hBoxDocumentoDataNascimentoEhProdutorRural.setBoxShadowConfigSpreadRadius(0);
        hBoxDocumentoDataNascimentoEhProdutorRural.setBoxShadowConfigShadowColor("clBlack");
        hBoxDocumentoDataNascimentoEhProdutorRural.setBoxShadowConfigOpacity(75);
        hBoxDocumentoDataNascimentoEhProdutorRural.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxDocumentoDataNascimentoEhProdutorRural);
        hBoxDocumentoDataNascimentoEhProdutorRural.applyProperties();
    }

    public TFHBox hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01 = new TFHBox();

    private void init_hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01() {
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setName("hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setWidth(5);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setHeight(20);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setBorderStyle("stNone");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setPaddingTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setPaddingLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setPaddingRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setPaddingBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setMarginTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setMarginLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setMarginRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setMarginBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setSpacing(1);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setFlexVflex("ftFalse");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setFlexHflex("ftFalse");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setScrollable(false);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setBoxShadowConfigOpacity(75);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.setVAlign("tvTop");
        hBoxDocumentoDataNascimentoEhProdutorRural.addChildren(hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador01.applyProperties();
    }

    public TFVBox vBoxTipoDocumento = new TFVBox();

    private void init_vBoxTipoDocumento() {
        vBoxTipoDocumento.setName("vBoxTipoDocumento");
        vBoxTipoDocumento.setLeft(5);
        vBoxTipoDocumento.setTop(0);
        vBoxTipoDocumento.setWidth(105);
        vBoxTipoDocumento.setHeight(45);
        vBoxTipoDocumento.setAlign("alTop");
        vBoxTipoDocumento.setBorderStyle("stNone");
        vBoxTipoDocumento.setPaddingTop(0);
        vBoxTipoDocumento.setPaddingLeft(0);
        vBoxTipoDocumento.setPaddingRight(0);
        vBoxTipoDocumento.setPaddingBottom(0);
        vBoxTipoDocumento.setMarginTop(0);
        vBoxTipoDocumento.setMarginLeft(0);
        vBoxTipoDocumento.setMarginRight(0);
        vBoxTipoDocumento.setMarginBottom(0);
        vBoxTipoDocumento.setSpacing(1);
        vBoxTipoDocumento.setFlexVflex("ftMin");
        vBoxTipoDocumento.setFlexHflex("ftTrue");
        vBoxTipoDocumento.setScrollable(false);
        vBoxTipoDocumento.setBoxShadowConfigHorizontalLength(10);
        vBoxTipoDocumento.setBoxShadowConfigVerticalLength(10);
        vBoxTipoDocumento.setBoxShadowConfigBlurRadius(5);
        vBoxTipoDocumento.setBoxShadowConfigSpreadRadius(0);
        vBoxTipoDocumento.setBoxShadowConfigShadowColor("clBlack");
        vBoxTipoDocumento.setBoxShadowConfigOpacity(75);
        hBoxDocumentoDataNascimentoEhProdutorRural.addChildren(vBoxTipoDocumento);
        vBoxTipoDocumento.applyProperties();
    }

    public TFLabel lblDocumento = new TFLabel();

    private void init_lblDocumento() {
        lblDocumento.setName("lblDocumento");
        lblDocumento.setLeft(0);
        lblDocumento.setTop(0);
        lblDocumento.setWidth(54);
        lblDocumento.setHeight(13);
        lblDocumento.setCaption("Documento");
        lblDocumento.setFontColor("clWindowText");
        lblDocumento.setFontSize(-11);
        lblDocumento.setFontName("Tahoma");
        lblDocumento.setFontStyle("[]");
        lblDocumento.setVerticalAlignment("taVerticalCenter");
        lblDocumento.setWordBreak(false);
        vBoxTipoDocumento.addChildren(lblDocumento);
        lblDocumento.applyProperties();
    }

    public TFString edtCPFCNPJ = new TFString();

    private void init_edtCPFCNPJ() {
        edtCPFCNPJ.setName("edtCPFCNPJ");
        edtCPFCNPJ.setLeft(0);
        edtCPFCNPJ.setTop(14);
        edtCPFCNPJ.setWidth(100);
        edtCPFCNPJ.setHeight(26);
        edtCPFCNPJ.setFlex(true);
        edtCPFCNPJ.setRequired(false);
        edtCPFCNPJ.setConstraintCheckWhen("cwImmediate");
        edtCPFCNPJ.setConstraintCheckType("ctExpression");
        edtCPFCNPJ.setConstraintFocusOnError(false);
        edtCPFCNPJ.setConstraintEnableUI(true);
        edtCPFCNPJ.setConstraintEnabled(false);
        edtCPFCNPJ.setConstraintFormCheck(true);
        edtCPFCNPJ.setCharCase("ccNormal");
        edtCPFCNPJ.setPwd(false);
        edtCPFCNPJ.setMaxlength(0);
        edtCPFCNPJ.setEnabled(false);
        edtCPFCNPJ.setFontColor("clWindowText");
        edtCPFCNPJ.setFontSize(-15);
        edtCPFCNPJ.setFontName("Tahoma");
        edtCPFCNPJ.setFontStyle("[fsBold]");
        edtCPFCNPJ.setSaveLiteralCharacter(false);
        edtCPFCNPJ.applyProperties();
        vBoxTipoDocumento.addChildren(edtCPFCNPJ);
        addValidatable(edtCPFCNPJ);
    }

    public TFHBox hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02 = new TFHBox();

    private void init_hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02() {
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setName("hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setLeft(110);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setWidth(5);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setHeight(20);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setBorderStyle("stNone");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setPaddingTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setPaddingLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setPaddingRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setPaddingBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setMarginTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setMarginLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setMarginRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setMarginBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setSpacing(1);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setFlexVflex("ftFalse");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setFlexHflex("ftFalse");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setScrollable(false);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setBoxShadowConfigOpacity(75);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.setVAlign("tvTop");
        hBoxDocumentoDataNascimentoEhProdutorRural.addChildren(hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador02.applyProperties();
    }

    public TFVBox vBoxDataNascimento = new TFVBox();

    private void init_vBoxDataNascimento() {
        vBoxDataNascimento.setName("vBoxDataNascimento");
        vBoxDataNascimento.setLeft(115);
        vBoxDataNascimento.setTop(0);
        vBoxDataNascimento.setWidth(130);
        vBoxDataNascimento.setHeight(45);
        vBoxDataNascimento.setBorderStyle("stNone");
        vBoxDataNascimento.setPaddingTop(0);
        vBoxDataNascimento.setPaddingLeft(0);
        vBoxDataNascimento.setPaddingRight(0);
        vBoxDataNascimento.setPaddingBottom(0);
        vBoxDataNascimento.setMarginTop(0);
        vBoxDataNascimento.setMarginLeft(0);
        vBoxDataNascimento.setMarginRight(0);
        vBoxDataNascimento.setMarginBottom(0);
        vBoxDataNascimento.setSpacing(1);
        vBoxDataNascimento.setFlexVflex("ftMin");
        vBoxDataNascimento.setFlexHflex("ftMin");
        vBoxDataNascimento.setScrollable(false);
        vBoxDataNascimento.setBoxShadowConfigHorizontalLength(10);
        vBoxDataNascimento.setBoxShadowConfigVerticalLength(10);
        vBoxDataNascimento.setBoxShadowConfigBlurRadius(5);
        vBoxDataNascimento.setBoxShadowConfigSpreadRadius(0);
        vBoxDataNascimento.setBoxShadowConfigShadowColor("clBlack");
        vBoxDataNascimento.setBoxShadowConfigOpacity(75);
        hBoxDocumentoDataNascimentoEhProdutorRural.addChildren(vBoxDataNascimento);
        vBoxDataNascimento.applyProperties();
    }

    public TFLabel lblDataNascimento = new TFLabel();

    private void init_lblDataNascimento() {
        lblDataNascimento.setName("lblDataNascimento");
        lblDataNascimento.setLeft(0);
        lblDataNascimento.setTop(0);
        lblDataNascimento.setWidth(81);
        lblDataNascimento.setHeight(13);
        lblDataNascimento.setCaption("Data Nascimento");
        lblDataNascimento.setFontColor("clWindowText");
        lblDataNascimento.setFontSize(-11);
        lblDataNascimento.setFontName("Tahoma");
        lblDataNascimento.setFontStyle("[]");
        lblDataNascimento.setVerticalAlignment("taVerticalCenter");
        lblDataNascimento.setWordBreak(false);
        vBoxDataNascimento.addChildren(lblDataNascimento);
        lblDataNascimento.applyProperties();
    }

    public TFDate edtDataNasc = new TFDate();

    private void init_edtDataNasc() {
        edtDataNasc.setName("edtDataNasc");
        edtDataNasc.setLeft(0);
        edtDataNasc.setTop(14);
        edtDataNasc.setWidth(120);
        edtDataNasc.setHeight(24);
        edtDataNasc.setFieldName("ANIVERSARIO");
        edtDataNasc.setFlex(false);
        edtDataNasc.setRequired(false);
        edtDataNasc.setConstraintCheckWhen("cwImmediate");
        edtDataNasc.setConstraintCheckType("ctExpression");
        edtDataNasc.setConstraintFocusOnError(false);
        edtDataNasc.setConstraintEnableUI(true);
        edtDataNasc.setConstraintEnabled(false);
        edtDataNasc.setConstraintFormCheck(true);
        edtDataNasc.setFormat("dd/MM/yyyy");
        edtDataNasc.setShowCheckBox(false);
        vBoxDataNascimento.addChildren(edtDataNasc);
        edtDataNasc.applyProperties();
        addValidatable(edtDataNasc);
    }

    public TFHBox hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03 = new TFHBox();

    private void init_hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03() {
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setName("hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setLeft(245);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setWidth(5);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setHeight(20);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setBorderStyle("stNone");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setPaddingTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setPaddingLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setPaddingRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setPaddingBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setMarginTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setMarginLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setMarginRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setMarginBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setSpacing(1);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setFlexVflex("ftFalse");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setFlexHflex("ftFalse");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setScrollable(false);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setBoxShadowConfigOpacity(75);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.setVAlign("tvTop");
        hBoxDocumentoDataNascimentoEhProdutorRural.addChildren(hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador03.applyProperties();
    }

    public TFVBox vBoxEhProdutorRural = new TFVBox();

    private void init_vBoxEhProdutorRural() {
        vBoxEhProdutorRural.setName("vBoxEhProdutorRural");
        vBoxEhProdutorRural.setLeft(250);
        vBoxEhProdutorRural.setTop(0);
        vBoxEhProdutorRural.setWidth(110);
        vBoxEhProdutorRural.setHeight(41);
        vBoxEhProdutorRural.setBorderStyle("stNone");
        vBoxEhProdutorRural.setPaddingTop(0);
        vBoxEhProdutorRural.setPaddingLeft(0);
        vBoxEhProdutorRural.setPaddingRight(0);
        vBoxEhProdutorRural.setPaddingBottom(0);
        vBoxEhProdutorRural.setMarginTop(0);
        vBoxEhProdutorRural.setMarginLeft(0);
        vBoxEhProdutorRural.setMarginRight(0);
        vBoxEhProdutorRural.setMarginBottom(0);
        vBoxEhProdutorRural.setSpacing(1);
        vBoxEhProdutorRural.setFlexVflex("ftMin");
        vBoxEhProdutorRural.setFlexHflex("ftMin");
        vBoxEhProdutorRural.setScrollable(false);
        vBoxEhProdutorRural.setBoxShadowConfigHorizontalLength(10);
        vBoxEhProdutorRural.setBoxShadowConfigVerticalLength(10);
        vBoxEhProdutorRural.setBoxShadowConfigBlurRadius(5);
        vBoxEhProdutorRural.setBoxShadowConfigSpreadRadius(0);
        vBoxEhProdutorRural.setBoxShadowConfigShadowColor("clBlack");
        vBoxEhProdutorRural.setBoxShadowConfigOpacity(75);
        hBoxDocumentoDataNascimentoEhProdutorRural.addChildren(vBoxEhProdutorRural);
        vBoxEhProdutorRural.applyProperties();
    }

    public TFLabel lblProdutorRural = new TFLabel();

    private void init_lblProdutorRural() {
        lblProdutorRural.setName("lblProdutorRural");
        lblProdutorRural.setLeft(0);
        lblProdutorRural.setTop(0);
        lblProdutorRural.setWidth(84);
        lblProdutorRural.setHeight(13);
        lblProdutorRural.setCaption("\u00C9 Produtor Rural?");
        lblProdutorRural.setFontColor("clWindowText");
        lblProdutorRural.setFontSize(-11);
        lblProdutorRural.setFontName("Tahoma");
        lblProdutorRural.setFontStyle("[]");
        lblProdutorRural.setVerticalAlignment("taVerticalCenter");
        lblProdutorRural.setWordBreak(false);
        vBoxEhProdutorRural.addChildren(lblProdutorRural);
        lblProdutorRural.applyProperties();
    }

    public TFCombo cboProdutorRural = new TFCombo();

    private void init_cboProdutorRural() {
        cboProdutorRural.setName("cboProdutorRural");
        cboProdutorRural.setLeft(0);
        cboProdutorRural.setTop(14);
        cboProdutorRural.setWidth(100);
        cboProdutorRural.setHeight(21);
        cboProdutorRural.setHint("\u00C9 Produtor Rural");
        cboProdutorRural.setFlex(false);
        cboProdutorRural.setListOptions("Sim=S;N\u00E3o=N");
        cboProdutorRural.setHelpCaption("\u00C9 Produtor Rural");
        cboProdutorRural.setReadOnly(true);
        cboProdutorRural.setRequired(false);
        cboProdutorRural.setPrompt("\u00C9 Produtor Rural");
        cboProdutorRural.setConstraintCheckWhen("cwImmediate");
        cboProdutorRural.setConstraintCheckType("ctExpression");
        cboProdutorRural.setConstraintFocusOnError(false);
        cboProdutorRural.setConstraintEnableUI(true);
        cboProdutorRural.setConstraintEnabled(false);
        cboProdutorRural.setConstraintFormCheck(true);
        cboProdutorRural.setClearOnDelKey(true);
        cboProdutorRural.setUseClearButton(false);
        cboProdutorRural.setHideClearButtonOnNullValue(false);
        cboProdutorRural.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboProdutorRuralChange(event);
            processarFlow("FrmCadastroRapidoClienteProdRural", "cboProdutorRural", "OnChange");
        });
        vBoxEhProdutorRural.addChildren(cboProdutorRural);
        cboProdutorRural.applyProperties();
        addValidatable(cboProdutorRural);
    }

    public TFHBox hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04 = new TFHBox();

    private void init_hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04() {
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setName("hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setLeft(360);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setWidth(5);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setHeight(20);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setBorderStyle("stNone");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setPaddingTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setPaddingLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setPaddingRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setPaddingBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setMarginTop(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setMarginLeft(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setMarginRight(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setMarginBottom(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setSpacing(1);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setFlexVflex("ftFalse");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setFlexHflex("ftFalse");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setScrollable(false);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setBoxShadowConfigHorizontalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setBoxShadowConfigVerticalLength(10);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setBoxShadowConfigBlurRadius(5);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setBoxShadowConfigSpreadRadius(0);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setBoxShadowConfigShadowColor("clBlack");
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setBoxShadowConfigOpacity(75);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.setVAlign("tvTop");
        hBoxDocumentoDataNascimentoEhProdutorRural.addChildren(hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04);
        hBoxDocumentoDataNascimentoEhProdutorRuralSeparador04.applyProperties();
    }

    public TFHBox hBoxPrincipalSeparador03 = new TFHBox();

    private void init_hBoxPrincipalSeparador03() {
        hBoxPrincipalSeparador03.setName("hBoxPrincipalSeparador03");
        hBoxPrincipalSeparador03.setLeft(0);
        hBoxPrincipalSeparador03.setTop(121);
        hBoxPrincipalSeparador03.setWidth(210);
        hBoxPrincipalSeparador03.setHeight(5);
        hBoxPrincipalSeparador03.setBorderStyle("stNone");
        hBoxPrincipalSeparador03.setPaddingTop(0);
        hBoxPrincipalSeparador03.setPaddingLeft(0);
        hBoxPrincipalSeparador03.setPaddingRight(0);
        hBoxPrincipalSeparador03.setPaddingBottom(0);
        hBoxPrincipalSeparador03.setMarginTop(0);
        hBoxPrincipalSeparador03.setMarginLeft(0);
        hBoxPrincipalSeparador03.setMarginRight(0);
        hBoxPrincipalSeparador03.setMarginBottom(0);
        hBoxPrincipalSeparador03.setSpacing(1);
        hBoxPrincipalSeparador03.setFlexVflex("ftFalse");
        hBoxPrincipalSeparador03.setFlexHflex("ftFalse");
        hBoxPrincipalSeparador03.setScrollable(false);
        hBoxPrincipalSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxPrincipalSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxPrincipalSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxPrincipalSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxPrincipalSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxPrincipalSeparador03.setBoxShadowConfigOpacity(75);
        hBoxPrincipalSeparador03.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPrincipalSeparador03);
        hBoxPrincipalSeparador03.applyProperties();
    }

    public TFHBox hBoxUFIE = new TFHBox();

    private void init_hBoxUFIE() {
        hBoxUFIE.setName("hBoxUFIE");
        hBoxUFIE.setLeft(0);
        hBoxUFIE.setTop(127);
        hBoxUFIE.setWidth(210);
        hBoxUFIE.setHeight(50);
        hBoxUFIE.setBorderStyle("stNone");
        hBoxUFIE.setPaddingTop(0);
        hBoxUFIE.setPaddingLeft(0);
        hBoxUFIE.setPaddingRight(0);
        hBoxUFIE.setPaddingBottom(0);
        hBoxUFIE.setMarginTop(0);
        hBoxUFIE.setMarginLeft(0);
        hBoxUFIE.setMarginRight(0);
        hBoxUFIE.setMarginBottom(0);
        hBoxUFIE.setSpacing(1);
        hBoxUFIE.setFlexVflex("ftMin");
        hBoxUFIE.setFlexHflex("ftTrue");
        hBoxUFIE.setScrollable(false);
        hBoxUFIE.setBoxShadowConfigHorizontalLength(10);
        hBoxUFIE.setBoxShadowConfigVerticalLength(10);
        hBoxUFIE.setBoxShadowConfigBlurRadius(5);
        hBoxUFIE.setBoxShadowConfigSpreadRadius(0);
        hBoxUFIE.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFIE.setBoxShadowConfigOpacity(75);
        hBoxUFIE.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxUFIE);
        hBoxUFIE.applyProperties();
    }

    public TFHBox hBoxUFIESeparador01 = new TFHBox();

    private void init_hBoxUFIESeparador01() {
        hBoxUFIESeparador01.setName("hBoxUFIESeparador01");
        hBoxUFIESeparador01.setLeft(0);
        hBoxUFIESeparador01.setTop(0);
        hBoxUFIESeparador01.setWidth(5);
        hBoxUFIESeparador01.setHeight(20);
        hBoxUFIESeparador01.setBorderStyle("stNone");
        hBoxUFIESeparador01.setPaddingTop(0);
        hBoxUFIESeparador01.setPaddingLeft(0);
        hBoxUFIESeparador01.setPaddingRight(0);
        hBoxUFIESeparador01.setPaddingBottom(0);
        hBoxUFIESeparador01.setMarginTop(0);
        hBoxUFIESeparador01.setMarginLeft(0);
        hBoxUFIESeparador01.setMarginRight(0);
        hBoxUFIESeparador01.setMarginBottom(0);
        hBoxUFIESeparador01.setSpacing(1);
        hBoxUFIESeparador01.setFlexVflex("ftFalse");
        hBoxUFIESeparador01.setFlexHflex("ftFalse");
        hBoxUFIESeparador01.setScrollable(false);
        hBoxUFIESeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxUFIESeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxUFIESeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxUFIESeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxUFIESeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFIESeparador01.setBoxShadowConfigOpacity(75);
        hBoxUFIESeparador01.setVAlign("tvTop");
        hBoxUFIE.addChildren(hBoxUFIESeparador01);
        hBoxUFIESeparador01.applyProperties();
    }

    public TFVBox vBoxUF = new TFVBox();

    private void init_vBoxUF() {
        vBoxUF.setName("vBoxUF");
        vBoxUF.setLeft(5);
        vBoxUF.setTop(0);
        vBoxUF.setWidth(81);
        vBoxUF.setHeight(40);
        vBoxUF.setBorderStyle("stNone");
        vBoxUF.setPaddingTop(0);
        vBoxUF.setPaddingLeft(0);
        vBoxUF.setPaddingRight(0);
        vBoxUF.setPaddingBottom(0);
        vBoxUF.setMarginTop(0);
        vBoxUF.setMarginLeft(0);
        vBoxUF.setMarginRight(0);
        vBoxUF.setMarginBottom(0);
        vBoxUF.setSpacing(1);
        vBoxUF.setFlexVflex("ftMin");
        vBoxUF.setFlexHflex("ftFalse");
        vBoxUF.setScrollable(false);
        vBoxUF.setBoxShadowConfigHorizontalLength(10);
        vBoxUF.setBoxShadowConfigVerticalLength(10);
        vBoxUF.setBoxShadowConfigBlurRadius(5);
        vBoxUF.setBoxShadowConfigSpreadRadius(0);
        vBoxUF.setBoxShadowConfigShadowColor("clBlack");
        vBoxUF.setBoxShadowConfigOpacity(75);
        hBoxUFIE.addChildren(vBoxUF);
        vBoxUF.applyProperties();
    }

    public TFLabel lblUF = new TFLabel();

    private void init_lblUF() {
        lblUF.setName("lblUF");
        lblUF.setLeft(0);
        lblUF.setTop(0);
        lblUF.setWidth(13);
        lblUF.setHeight(13);
        lblUF.setCaption("UF");
        lblUF.setFontColor("clWindowText");
        lblUF.setFontSize(-11);
        lblUF.setFontName("Tahoma");
        lblUF.setFontStyle("[]");
        lblUF.setVerticalAlignment("taVerticalCenter");
        lblUF.setWordBreak(false);
        vBoxUF.addChildren(lblUF);
        lblUF.applyProperties();
    }

    public TFCombo edtUF = new TFCombo();

    private void init_edtUF() {
        edtUF.setName("edtUF");
        edtUF.setLeft(0);
        edtUF.setTop(14);
        edtUF.setWidth(70);
        edtUF.setHeight(21);
        edtUF.setLookupTable(tbUf);
        edtUF.setLookupKey("UF");
        edtUF.setLookupDesc("UF");
        edtUF.setFlex(true);
        edtUF.setReadOnly(false);
        edtUF.setRequired(false);
        edtUF.setPrompt("Selecione");
        edtUF.setConstraintCheckWhen("cwImmediate");
        edtUF.setConstraintCheckType("ctExpression");
        edtUF.setConstraintFocusOnError(false);
        edtUF.setConstraintEnableUI(true);
        edtUF.setConstraintEnabled(false);
        edtUF.setConstraintFormCheck(true);
        edtUF.setClearOnDelKey(true);
        edtUF.setUseClearButton(false);
        edtUF.setHideClearButtonOnNullValue(false);
        edtUF.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtUFChange(event);
            processarFlow("FrmCadastroRapidoClienteProdRural", "edtUF", "OnChange");
        });
        edtUF.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteProdRural", "edtUF", "OnEnter");
        });
        edtUF.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtUFExit(event);
            processarFlow("FrmCadastroRapidoClienteProdRural", "edtUF", "OnExit");
        });
        TFMaskExpression item0 = new TFMaskExpression();
        item0.setExpression("*");
        item0.setEvalType("etExpression");
        item0.setPadLength(0);
        item0.setPadDirection("pdNone");
        item0.setMaskType("mtText");
        edtUF.getMasks().add(item0);
        vBoxUF.addChildren(edtUF);
        edtUF.applyProperties();
        addValidatable(edtUF);
    }

    public TFHBox hBoxUFIESeparador02 = new TFHBox();

    private void init_hBoxUFIESeparador02() {
        hBoxUFIESeparador02.setName("hBoxUFIESeparador02");
        hBoxUFIESeparador02.setLeft(86);
        hBoxUFIESeparador02.setTop(0);
        hBoxUFIESeparador02.setWidth(5);
        hBoxUFIESeparador02.setHeight(20);
        hBoxUFIESeparador02.setBorderStyle("stNone");
        hBoxUFIESeparador02.setPaddingTop(0);
        hBoxUFIESeparador02.setPaddingLeft(0);
        hBoxUFIESeparador02.setPaddingRight(0);
        hBoxUFIESeparador02.setPaddingBottom(0);
        hBoxUFIESeparador02.setMarginTop(0);
        hBoxUFIESeparador02.setMarginLeft(0);
        hBoxUFIESeparador02.setMarginRight(0);
        hBoxUFIESeparador02.setMarginBottom(0);
        hBoxUFIESeparador02.setSpacing(1);
        hBoxUFIESeparador02.setFlexVflex("ftFalse");
        hBoxUFIESeparador02.setFlexHflex("ftFalse");
        hBoxUFIESeparador02.setScrollable(false);
        hBoxUFIESeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxUFIESeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxUFIESeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxUFIESeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxUFIESeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFIESeparador02.setBoxShadowConfigOpacity(75);
        hBoxUFIESeparador02.setVAlign("tvTop");
        hBoxUFIE.addChildren(hBoxUFIESeparador02);
        hBoxUFIESeparador02.applyProperties();
    }

    public TFVBox vBoxIE = new TFVBox();

    private void init_vBoxIE() {
        vBoxIE.setName("vBoxIE");
        vBoxIE.setLeft(91);
        vBoxIE.setTop(0);
        vBoxIE.setWidth(105);
        vBoxIE.setHeight(45);
        vBoxIE.setBorderStyle("stNone");
        vBoxIE.setPaddingTop(0);
        vBoxIE.setPaddingLeft(0);
        vBoxIE.setPaddingRight(0);
        vBoxIE.setPaddingBottom(0);
        vBoxIE.setMarginTop(0);
        vBoxIE.setMarginLeft(0);
        vBoxIE.setMarginRight(0);
        vBoxIE.setMarginBottom(0);
        vBoxIE.setSpacing(1);
        vBoxIE.setFlexVflex("ftMin");
        vBoxIE.setFlexHflex("ftTrue");
        vBoxIE.setScrollable(false);
        vBoxIE.setBoxShadowConfigHorizontalLength(10);
        vBoxIE.setBoxShadowConfigVerticalLength(10);
        vBoxIE.setBoxShadowConfigBlurRadius(5);
        vBoxIE.setBoxShadowConfigSpreadRadius(0);
        vBoxIE.setBoxShadowConfigShadowColor("clBlack");
        vBoxIE.setBoxShadowConfigOpacity(75);
        hBoxUFIE.addChildren(vBoxIE);
        vBoxIE.applyProperties();
    }

    public TFLabel lblInscricaoEstadual = new TFLabel();

    private void init_lblInscricaoEstadual() {
        lblInscricaoEstadual.setName("lblInscricaoEstadual");
        lblInscricaoEstadual.setLeft(0);
        lblInscricaoEstadual.setTop(0);
        lblInscricaoEstadual.setWidth(87);
        lblInscricaoEstadual.setHeight(13);
        lblInscricaoEstadual.setCaption("Inscri\u00E7\u00E3o Estadual");
        lblInscricaoEstadual.setFontColor("clWindowText");
        lblInscricaoEstadual.setFontSize(-11);
        lblInscricaoEstadual.setFontName("Tahoma");
        lblInscricaoEstadual.setFontStyle("[]");
        lblInscricaoEstadual.setVerticalAlignment("taVerticalCenter");
        lblInscricaoEstadual.setWordBreak(false);
        vBoxIE.addChildren(lblInscricaoEstadual);
        lblInscricaoEstadual.applyProperties();
    }

    public TFString edtInscricaoEstadual = new TFString();

    private void init_edtInscricaoEstadual() {
        edtInscricaoEstadual.setName("edtInscricaoEstadual");
        edtInscricaoEstadual.setLeft(0);
        edtInscricaoEstadual.setTop(14);
        edtInscricaoEstadual.setWidth(100);
        edtInscricaoEstadual.setHeight(26);
        edtInscricaoEstadual.setFlex(true);
        edtInscricaoEstadual.setRequired(false);
        edtInscricaoEstadual.setConstraintCheckWhen("cwImmediate");
        edtInscricaoEstadual.setConstraintCheckType("ctExpression");
        edtInscricaoEstadual.setConstraintFocusOnError(false);
        edtInscricaoEstadual.setConstraintEnableUI(true);
        edtInscricaoEstadual.setConstraintEnabled(false);
        edtInscricaoEstadual.setConstraintFormCheck(true);
        edtInscricaoEstadual.setCharCase("ccNormal");
        edtInscricaoEstadual.setPwd(false);
        edtInscricaoEstadual.setMaxlength(14);
        edtInscricaoEstadual.setEnabled(false);
        edtInscricaoEstadual.setFontColor("clWindowText");
        edtInscricaoEstadual.setFontSize(-15);
        edtInscricaoEstadual.setFontName("Tahoma");
        edtInscricaoEstadual.setFontStyle("[fsBold]");
        edtInscricaoEstadual.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmCadastroRapidoClienteProdRural", "edtInscricaoEstadual", "OnEnter");
        });
        edtInscricaoEstadual.setSaveLiteralCharacter(false);
        edtInscricaoEstadual.applyProperties();
        vBoxIE.addChildren(edtInscricaoEstadual);
        addValidatable(edtInscricaoEstadual);
    }

    public TFHBox hBoxUFIESeparador03 = new TFHBox();

    private void init_hBoxUFIESeparador03() {
        hBoxUFIESeparador03.setName("hBoxUFIESeparador03");
        hBoxUFIESeparador03.setLeft(196);
        hBoxUFIESeparador03.setTop(0);
        hBoxUFIESeparador03.setWidth(5);
        hBoxUFIESeparador03.setHeight(20);
        hBoxUFIESeparador03.setBorderStyle("stNone");
        hBoxUFIESeparador03.setPaddingTop(0);
        hBoxUFIESeparador03.setPaddingLeft(0);
        hBoxUFIESeparador03.setPaddingRight(0);
        hBoxUFIESeparador03.setPaddingBottom(0);
        hBoxUFIESeparador03.setMarginTop(0);
        hBoxUFIESeparador03.setMarginLeft(0);
        hBoxUFIESeparador03.setMarginRight(0);
        hBoxUFIESeparador03.setMarginBottom(0);
        hBoxUFIESeparador03.setSpacing(1);
        hBoxUFIESeparador03.setFlexVflex("ftFalse");
        hBoxUFIESeparador03.setFlexHflex("ftFalse");
        hBoxUFIESeparador03.setScrollable(false);
        hBoxUFIESeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxUFIESeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxUFIESeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxUFIESeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxUFIESeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFIESeparador03.setBoxShadowConfigOpacity(75);
        hBoxUFIESeparador03.setVAlign("tvTop");
        hBoxUFIE.addChildren(hBoxUFIESeparador03);
        hBoxUFIESeparador03.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnConfirmarClick(final Event<Object> event) {
        if (btnConfirmar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConfirmar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cboProdutorRuralChange(final Event<Object> event);

    public abstract void edtUFChange(final Event<Object> event);

    public abstract void edtUFExit(final Event<Object> event);

}