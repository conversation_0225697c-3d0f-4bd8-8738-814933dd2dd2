package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmClienteAlterarFrameExterno extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ClienteAlterarFrameExternoRNA rn = null;

    public FrmClienteAlterarFrameExterno() {
        try {
            rn = (freedom.bytecode.rn.ClienteAlterarFrameExternoRNA) getRN(freedom.bytecode.rn.wizard.ClienteAlterarFrameExternoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_FrameAlterarCadastroCliente();
        init_FrmClienteAlterarFrameExterno();
    }

    protected TFForm FrmClienteAlterarFrameExterno = this;
    private void init_FrmClienteAlterarFrameExterno() {
        FrmClienteAlterarFrameExterno.setName("FrmClienteAlterarFrameExterno");
        FrmClienteAlterarFrameExterno.setCaption("Alterar Cadastro Cliente");
        FrmClienteAlterarFrameExterno.setClientHeight(722);
        FrmClienteAlterarFrameExterno.setClientWidth(1228);
        FrmClienteAlterarFrameExterno.setColor("clBtnFace");
        FrmClienteAlterarFrameExterno.setWKey("7000133");
        FrmClienteAlterarFrameExterno.setSpacing(0);
        FrmClienteAlterarFrameExterno.applyProperties();
    }

    public TFFrame FrameAlterarCadastroCliente = new TFFrame();

    private void init_FrameAlterarCadastroCliente() {
        FrameAlterarCadastroCliente.setName("FrameAlterarCadastroCliente");
        FrameAlterarCadastroCliente.setLeft(0);
        FrameAlterarCadastroCliente.setTop(0);
        FrameAlterarCadastroCliente.setWidth(1228);
        FrameAlterarCadastroCliente.setHeight(722);
        FrameAlterarCadastroCliente.setAlign("alClient");
        FrameAlterarCadastroCliente.setFlexVflex("ftTrue");
        FrameAlterarCadastroCliente.setFlexHflex("ftTrue");
        FrmClienteAlterarFrameExterno.addChildren(FrameAlterarCadastroCliente);
        FrameAlterarCadastroCliente.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}