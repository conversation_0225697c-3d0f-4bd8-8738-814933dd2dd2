package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmGerencialPainelPreferencia extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.GerencialPainelPreferenciaRNA rn = null;

    public FrmGerencialPainelPreferencia() {
        try {
            rn = (freedom.bytecode.rn.GerencialPainelPreferenciaRNA) getRN(freedom.bytecode.rn.wizard.GerencialPainelPreferenciaRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbPainelGerencialUsuario();
        init_tbGridIndicadores();
        init_tbGridQuebraInd();
        init_tbEmpresasUsuarios();
        init_tbPreferencia();
        init_FVBox1();
        init_FHBox2();
        init_btnVoltar();
        init_btnAceitar();
        init_FHBox1();
        init_FVBox2();
        init_FLabel1();
        init_cbbPainel();
        init_FVBox3();
        init_FVBox6();
        init_imgPainel();
        init_FVBox7();
        init_FHBox3();
        init_FVBox4();
        init_FLabel2();
        init_gridIndicadores();
        init_FHBox4();
        init_FVBox10();
        init_FLabel3();
        init_gridQuebra();
        init_FrmGerencialPainelPreferencia();
    }

    public PAINEL_GERENCIAL_USUARIO tbPainelGerencialUsuario;

    private void init_tbPainelGerencialUsuario() {
        tbPainelGerencialUsuario = rn.tbPainelGerencialUsuario;
        tbPainelGerencialUsuario.setName("tbPainelGerencialUsuario");
        tbPainelGerencialUsuario.setMaxRowCount(200);
        tbPainelGerencialUsuario.setWKey("382036;38201");
        tbPainelGerencialUsuario.setRatioBatchSize(20);
        getTables().put(tbPainelGerencialUsuario, "tbPainelGerencialUsuario");
        tbPainelGerencialUsuario.applyProperties();
    }

    public BSC_GRID_INDICADORES tbGridIndicadores;

    private void init_tbGridIndicadores() {
        tbGridIndicadores = rn.tbGridIndicadores;
        tbGridIndicadores.setName("tbGridIndicadores");
        tbGridIndicadores.setMaxRowCount(200);
        tbGridIndicadores.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbGridIndicadoresAfterScroll(event);
            processarFlow("FrmGerencialPainelPreferencia", "tbGridIndicadores", "OnAfterScroll");
        });
        tbGridIndicadores.setWKey("382036;38202");
        tbGridIndicadores.setRatioBatchSize(20);
        getTables().put(tbGridIndicadores, "tbGridIndicadores");
        tbGridIndicadores.applyProperties();
    }

    public BSC_GRID_QUEBRA_IND tbGridQuebraInd;

    private void init_tbGridQuebraInd() {
        tbGridQuebraInd = rn.tbGridQuebraInd;
        tbGridQuebraInd.setName("tbGridQuebraInd");
        tbGridQuebraInd.setMaxRowCount(200);
        tbGridQuebraInd.setWKey("382036;38203");
        tbGridQuebraInd.setRatioBatchSize(20);
        getTables().put(tbGridQuebraInd, "tbGridQuebraInd");
        tbGridQuebraInd.applyProperties();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("382036;38204");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public BSC_PREFERENCIA tbPreferencia;

    private void init_tbPreferencia() {
        tbPreferencia = rn.tbPreferencia;
        tbPreferencia.setName("tbPreferencia");
        tbPreferencia.setMaxRowCount(200);
        tbPreferencia.setWKey("382036;38205");
        tbPreferencia.setRatioBatchSize(20);
        getTables().put(tbPreferencia, "tbPreferencia");
        tbPreferencia.applyProperties();
    }

    protected TFForm FrmGerencialPainelPreferencia = this;
    private void init_FrmGerencialPainelPreferencia() {
        FrmGerencialPainelPreferencia.setName("FrmGerencialPainelPreferencia");
        FrmGerencialPainelPreferencia.setCaption("Prefer\u00EAncias do Usu\u00E1rio");
        FrmGerencialPainelPreferencia.setClientHeight(678);
        FrmGerencialPainelPreferencia.setClientWidth(492);
        FrmGerencialPainelPreferencia.setColor("clBtnFace");
        FrmGerencialPainelPreferencia.setWKey("382036");
        FrmGerencialPainelPreferencia.setSpacing(0);
        FrmGerencialPainelPreferencia.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(492);
        FVBox1.setHeight(678);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(10);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmGerencialPainelPreferencia.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(476);
        FHBox2.setHeight(65);
        FHBox2.setAlign("alTop");
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(5);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(75);
        btnVoltar.setHeight(60);
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmGerencialPainelPreferencia", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox2.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(75);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(75);
        btnAceitar.setHeight(60);
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmGerencialPainelPreferencia", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox2.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(66);
        FHBox1.setWidth(485);
        FHBox1.setHeight(58);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(401);
        FVBox2.setHeight(50);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(36);
        FLabel1.setHeight(14);
        FLabel1.setCaption("Painel");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-12);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox2.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCombo cbbPainel = new TFCombo();

    private void init_cbbPainel() {
        cbbPainel.setName("cbbPainel");
        cbbPainel.setLeft(0);
        cbbPainel.setTop(15);
        cbbPainel.setWidth(269);
        cbbPainel.setHeight(21);
        cbbPainel.setLookupTable(tbPainelGerencialUsuario);
        cbbPainel.setLookupKey("ID");
        cbbPainel.setLookupDesc("DESCRICAO");
        cbbPainel.setFlex(true);
        cbbPainel.setReadOnly(true);
        cbbPainel.setRequired(false);
        cbbPainel.setPrompt("Selecione");
        cbbPainel.setConstraintCheckWhen("cwImmediate");
        cbbPainel.setConstraintCheckType("ctExpression");
        cbbPainel.setConstraintFocusOnError(false);
        cbbPainel.setConstraintEnableUI(true);
        cbbPainel.setConstraintEnabled(false);
        cbbPainel.setConstraintFormCheck(true);
        cbbPainel.setClearOnDelKey(true);
        cbbPainel.setUseClearButton(false);
        cbbPainel.setHideClearButtonOnNullValue(false);
        cbbPainel.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbPainelChange(event);
            processarFlow("FrmGerencialPainelPreferencia", "cbbPainel", "OnChange");
        });
        FVBox2.addChildren(cbbPainel);
        cbbPainel.applyProperties();
        addValidatable(cbbPainel);
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(401);
        FVBox3.setTop(0);
        FVBox3.setWidth(50);
        FVBox3.setHeight(55);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(10);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(0);
        FVBox6.setWidth(30);
        FVBox6.setHeight(10);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftTrue");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFImage imgPainel = new TFImage();

    private void init_imgPainel() {
        imgPainel.setName("imgPainel");
        imgPainel.setLeft(0);
        imgPainel.setTop(11);
        imgPainel.setWidth(30);
        imgPainel.setHeight(30);
        imgPainel.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            imgPainelClick(event);
            processarFlow("FrmGerencialPainelPreferencia", "imgPainel", "OnClick");
        });
        imgPainel.setImageSrc("/images/crmservice382023.png");
        imgPainel.setBoxSize(0);
        imgPainel.setGrayScaleOnDisable(false);
        imgPainel.setFlexVflex("ftFalse");
        imgPainel.setFlexHflex("ftFalse");
        FVBox3.addChildren(imgPainel);
        imgPainel.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(42);
        FVBox7.setWidth(30);
        FVBox7.setHeight(5);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftFalse");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(125);
        FHBox3.setWidth(485);
        FHBox3.setHeight(234);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(3);
        FHBox3.setFlexVflex("ftTrue");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FVBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(0);
        FVBox4.setWidth(401);
        FVBox4.setHeight(228);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(131);
        FLabel2.setHeight(14);
        FLabel2.setCaption("Indicadores do Painel");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-12);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox4.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFGrid gridIndicadores = new TFGrid();

    private void init_gridIndicadores() {
        gridIndicadores.setName("gridIndicadores");
        gridIndicadores.setLeft(0);
        gridIndicadores.setTop(15);
        gridIndicadores.setWidth(384);
        gridIndicadores.setHeight(144);
        gridIndicadores.setTable(tbGridIndicadores);
        gridIndicadores.setFlexVflex("ftTrue");
        gridIndicadores.setFlexHflex("ftTrue");
        gridIndicadores.setPagingEnabled(false);
        gridIndicadores.setFrozenColumns(0);
        gridIndicadores.setShowFooter(false);
        gridIndicadores.setShowHeader(true);
        gridIndicadores.setMultiSelection(false);
        gridIndicadores.setGroupingEnabled(false);
        gridIndicadores.setGroupingExpanded(false);
        gridIndicadores.setGroupingShowFooter(false);
        gridIndicadores.setCrosstabEnabled(false);
        gridIndicadores.setCrosstabGroupType("cgtConcat");
        gridIndicadores.setEditionEnabled(false);
        gridIndicadores.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID");
        item0.setTitleCaption("Id");
        item0.setWidth(55);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO");
        item1.setTitleCaption("Indicador");
        item1.setWidth(100);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("FAVORITO");
        item2.setTitleCaption("Favorito");
        item2.setWidth(72);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taCenter");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("FAVORITO = 'S'");
        item3.setHint("Meu Favorito");
        item3.setEvalType("etExpression");
        item3.setImageId(4300107);
        item2.getImages().add(item3);
        TFImageExpression item4 = new TFImageExpression();
        item4.setExpression("FAVORITO = 'N'");
        item4.setEvalType("etExpression");
        item4.setImageId(4300106);
        item4.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridIndicadoresfavGridIndicadoresClick(event);
            processarFlow("FrmGerencialPainelPreferencia", "item4", "OnClick");
        });
        item2.getImages().add(item4);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(false);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item2);
        FVBox4.addChildren(gridIndicadores);
        gridIndicadores.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(360);
        FHBox4.setWidth(485);
        FHBox4.setHeight(234);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(3);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FVBox1.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFVBox FVBox10 = new TFVBox();

    private void init_FVBox10() {
        FVBox10.setName("FVBox10");
        FVBox10.setLeft(0);
        FVBox10.setTop(0);
        FVBox10.setWidth(401);
        FVBox10.setHeight(228);
        FVBox10.setBorderStyle("stNone");
        FVBox10.setPaddingTop(0);
        FVBox10.setPaddingLeft(0);
        FVBox10.setPaddingRight(0);
        FVBox10.setPaddingBottom(0);
        FVBox10.setMarginTop(0);
        FVBox10.setMarginLeft(0);
        FVBox10.setMarginRight(0);
        FVBox10.setMarginBottom(0);
        FVBox10.setSpacing(1);
        FVBox10.setFlexVflex("ftTrue");
        FVBox10.setFlexHflex("ftTrue");
        FVBox10.setScrollable(false);
        FVBox10.setBoxShadowConfigHorizontalLength(10);
        FVBox10.setBoxShadowConfigVerticalLength(10);
        FVBox10.setBoxShadowConfigBlurRadius(5);
        FVBox10.setBoxShadowConfigSpreadRadius(0);
        FVBox10.setBoxShadowConfigShadowColor("clBlack");
        FVBox10.setBoxShadowConfigOpacity(75);
        FHBox4.addChildren(FVBox10);
        FVBox10.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(0);
        FLabel3.setWidth(226);
        FLabel3.setHeight(14);
        FLabel3.setCaption("Quebra para o Indicador Selecionado");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-12);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[fsBold]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FVBox10.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFGrid gridQuebra = new TFGrid();

    private void init_gridQuebra() {
        gridQuebra.setName("gridQuebra");
        gridQuebra.setLeft(0);
        gridQuebra.setTop(15);
        gridQuebra.setWidth(384);
        gridQuebra.setHeight(144);
        gridQuebra.setTable(tbGridQuebraInd);
        gridQuebra.setFlexVflex("ftTrue");
        gridQuebra.setFlexHflex("ftTrue");
        gridQuebra.setPagingEnabled(false);
        gridQuebra.setFrozenColumns(0);
        gridQuebra.setShowFooter(false);
        gridQuebra.setShowHeader(true);
        gridQuebra.setMultiSelection(false);
        gridQuebra.setGroupingEnabled(false);
        gridQuebra.setGroupingExpanded(false);
        gridQuebra.setGroupingShowFooter(false);
        gridQuebra.setCrosstabEnabled(false);
        gridQuebra.setCrosstabGroupType("cgtConcat");
        gridQuebra.setEditionEnabled(false);
        gridQuebra.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_QUEBRA");
        item0.setTitleCaption("Id");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridQuebra.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO_QUEBRA");
        item1.setTitleCaption("Quebra");
        item1.setWidth(120);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridQuebra.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("FAVORITO");
        item2.setTitleCaption("Favorito");
        item2.setWidth(72);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taCenter");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("FAVORITO = 'S'");
        item3.setHint("Meu Favorito");
        item3.setEvalType("etExpression");
        item3.setImageId(4300107);
        item2.getImages().add(item3);
        TFImageExpression item4 = new TFImageExpression();
        item4.setExpression("FAVORITO = 'N'");
        item4.setEvalType("etExpression");
        item4.setImageId(4300106);
        item4.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridQuebrafavGridQuebrasClick(event);
            processarFlow("FrmGerencialPainelPreferencia", "item4", "OnClick");
        });
        item2.getImages().add(item4);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(false);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridQuebra.getColumns().add(item2);
        FVBox10.addChildren(gridQuebra);
        gridQuebra.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbbPainelChange(final Event<Object> event);

    public abstract void imgPainelClick(final Event<Object> event);

    public abstract void gridIndicadoresfavGridIndicadoresClick(final Event<Object> event);

    public abstract void gridQuebrafavGridQuebrasClick(final Event<Object> event);

    public abstract void tbGridIndicadoresAfterScroll(final Event<Object> event);

}