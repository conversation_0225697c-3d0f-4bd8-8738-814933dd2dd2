package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAgrupaPagamentosW;
import freedom.client.controls.ITFTabsheet;
import freedom.client.controls.impl.TFGroupbox;
import freedom.client.controls.impl.TFHBox;
import freedom.client.controls.impl.TFPageControl;
import freedom.client.controls.impl.TFTable;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.util.*;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

public class FrmAgrupaPagamentosA extends FrmAgrupaPagamentosW {
    private static final long serialVersionUID = 20130827081850L;
    private String colorDestaque = "#C1C3C1";
    private String colorDefault = "#F3F6F3";
    private int codEmpresaUserLogged = EmpresaUtil.getCodEmpresaUserLogged();
    private double vlrAux = 0.0;
    private double vlrTotalAux = 0.0;
    private int seqCartao = 0;

    private final IWorkList wl = WorkListFactory.getInstance();
    private final Boolean isReponsive = !wl.get("MOBILE_RESPONSIVE").isNull();
    private boolean fechou = false;

    private Date dataOrcamento = null;


    @Override
    public void FFormCreate(Event<Object> event) {
        this.frmShow();

    }

    private void frmShow(){
        this.abrirTabelas();
        this.hBoxSelOrcPreNotas.setColor(this.colorDestaque);
        this.hBoxOrcPreNotasAgrupadas.setColor(this.colorDefault);
        this.hBoxFormasPagamento.setColor(this.colorDefault);
        this.grpBoxOrcPrenotaAgrupadas.setClosed(false);
        this.grpBoxOrcPrenotaAgrupadasClose(new Event("onClose", null, true));
        this.cbbComboEmpresa.setValue(this.codEmpresaUserLogged);

        Date dataInicial = DateUtils.subtractDays(7, new Date());
        if(this.dataOrcamento != null && this.dataOrcamento.compareTo(dataInicial) < 0){
            this.edtDataInicial.setValue(this.dataOrcamento);
        }else{
            this.edtDataInicial.setValue(dataInicial);
        }

        this.edtDataFinal.setValue(new Date());
        this.btnAddOrcNotas.setEnabled(false);
        this.btnSelecao.setIconClass("square-o");

        this.openLeadsPgto();

        if(!this.edtCpfCnpj.equals("")){
            this.pesquisaOrcamentos();
        }

    }

    public void agrupaPagamentosConsultaExterna(Date dataOrcamento){
        this.dataOrcamento = dataOrcamento;
        double codCliente  = this.edtCpfCnpj.getValue().asDecimal();
        if(codCliente <= 0){
            this.edtNomeCliente.setValue("");
        }else{
            this.filtraCliente();
        }

    }

    private void abrirTabelas(){
        try {
            this.rn.abrirTabelas();
        }catch(DataException e){
            EmpresaUtil.showError("Erro ao abrir tabelas",e);
        }
    }

    private void openLeadsPgto(){
        try{
            int codEmpresa = this.cbbComboEmpresa.getValue().asInteger();
            this.rn.openLeadsPgto(codEmpresa);
        }catch (DataException e){
            EmpresaUtil.showError("Falha ao abrir Pagamentos.",e);
        }
    }

    @Override
    public void btnAddOrcNotasClick(Event<Object> event) {
        boolean possuiAcesso = EmpresaUtil.validarAcesso("K1057");
        if (!possuiAcesso) {
            return;
        }
        this.addNotaParaAgrupamento();
        this.hBoxOrcPreNotasAgrupadasClick(event);
        this.setColorHBox(this.hBoxOrcPreNotasAgrupadas);
        this.gridOrcPreNotasParaAgrupar.invalidate();

    }

    @Override
    public void cbbFormasPgtoChange(Event<Object> event) {
        this.acaoFormasPgto();
    }

    private void acaoFormasPgto(){
        String value = this.cbbFormasPgto.getValue().asString();

        if(value.equals("2") || value.equals("4")){
            this.edtQtdParcelas.setValue(1);
            this.edtQtdParcelas.setEnabled(false);
            this.edtValor.setFocus();
        }

        if(value.equals("3")){
            this.edtQtdParcelas.setValue(1);
            this.edtQtdParcelas.setEnabled(true);
            this.edtQtdParcelas.setFocus();
        }
    }

    @Override
    public void btnDefinirFormasPagamentoClick(Event<Object> event) {
        if(!this.tbLeadsPgtoAgrupado.isEmpty()){
            double valor = this.carregaValorTotal();
            this.edtTotal.setValue(valor);
            this.edtValor.setValue(valor);
            this.hBoxFormasPagamentoClick(event);
            this.setColorHBox(this.hBoxFormasPagamento);

        }else{
            Dialog.create()
                    .title("Informação")
                    .message("Nenhum Item  foi informado")
                    .showInformation();
        }
    }

    private double carregaValorTotal(){
        double valor = 0.0;
        try{
            valor = this.rn.carregaValorTotal();
        }catch (DataException e){
            EmpresaUtil.showError("Falha ao somar os valores", e);
        }
        return valor;
    }

    private void aplicarPgtoParc(){
        double vlrTotal = this.edtTotal.getValue().asDecimal();
        String tipoParcPag = this.cbbFormasPgto.getValue().asString();
        int parcelas = this.edtQtdParcelas.getValue().asInteger();
        double valor = this.edtValor.getValue().asDecimal();

        if(parcelas < 1){
            EmpresaUtil.showInformationMessage("Valor das parcelas deve ser maior que 0. Verifique!");
            this.edtQtdParcelas.setFocus();
            return;
        }

        if(valor <= 0.0){
            EmpresaUtil.showInformationMessage("Valor não pode ser menor ou igual 0.00. Verifique!");
            this.edtValor.setFocus();
            return;
        }

        if(valor > vlrTotal){
            EmpresaUtil.showInformationMessage("Valor não pode ser maior que das OS Selecionadas. Verifique!");
            return;
        }

        try{

            boolean retFuncao = this.rn.existePixInformado();
            if(retFuncao && tipoParcPag.equals("4")){
                EmpresaUtil.showInformationMessage("Forma De Pagamento PIX já Lançada!!!");
                return;
            }

            if(this.tbLeadsPgtoAgrupadoParc.isEmpty()){
                rn.openLeadsPgtoAgrupParc();
            }

            this.seqCartao += 1;
            this.rn.aplicarPgtoParc(tipoParcPag, parcelas, valor, this.seqCartao);

            this.atualizaValor(vlrTotal);
            this.btnConfirmarFormasPgto.setEnabled(this.verificaTabela(this.tbLeadsPgtoAgrupadoParc));
            this.btnRemoverFormasPgto.setEnabled(this.verificaTabela(this.tbLeadsPgtoAgrupadoParc));

            this.cbbFormasPgto.setFocus();
            this.cbbFormasPgto.setOpen(true);

        }catch (DataException e){
            EmpresaUtil.showError("Falha ao inserir o pagamento", e);
        }
    }

    private boolean verificaTabela(TFTable table){
        if(!table.isEmpty()){
            return true;
        }
        return false;
    }

    private void confirmarPgtos(){
        boolean retFuncao = this.validaConfirmaPgto();
        if(!retFuncao){
            EmpresaUtil.showWarning("Aviso", "Valor total não fecha a OS/Orçamentos selecionados. Verifique!");
            return;
        }

        Dialog.create()
                .title("Informação")
                .message("Deseja realmente agrupar os pagamento?")
                .confirmOkCancelar((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        double codEmpresa = this.cbbComboEmpresa.getValue().asDecimal();
                        String value = this.rn.confirmarPgtos(codEmpresa);

                        if(StringUtils.isNotBlank(value) && value.equals("SUCESSO")){
                            Dialog.create()
                                    .title("Informação!")
                                    .message("Dados gravados com Sucesso!")
                                    .showInformation();

                            this.openLeadsOrcNotasPgtoAgrupados();

                            this.openLeadsPgto();
                            this.pesquisaOrcamentos();

                            this.btnRemoverFormasPgto.setEnabled(this.verificaTabela(this.tbLeadsPgtoAgrupadoParc));
                            this.btnConfirmarFormasPgto.setEnabled(this.verificaTabela(this.tbLeadsPgtoAgrupadoParc));
                            this.btnSelecao.setEnabled(verificaTabela(this.tbOrcPreNotasParaAgrupar));
                            this.btnSelecao.setIconClass("square-o");

                            int index = PageControlUtil.getIndex(this.pgcAgrupaPagamentos,this.tabPagamentosAgrupados);
                            this.pgcAgrupaPagamentos.selectTab(index);
                        }
                    }
                });
    }

    public void excluirPgtoAgrupado(){
        try {
            if (!this.tbLeadsOrcNotasPgtoAgrupados.isEmpty()) {
                if (this.tbLeadsOrcNotasPgtoAgrupados.locate("ID_PAGAMENTO", this.tbLeadsOrcNotasPgtoAgrupados.getID_PAGAMENTO().asDecimal().intValue())) {

                    double idPagamento = this.tbLeadsOrcNotasPgtoAgrupados.getID_PAGAMENTO().asDecimal();
                    double codEmpresa = this.tbLeadsOrcNotasPgtoAgrupados.getID_PAGAMENTO().asDecimal();

                    String value = this.rn.excluirPgtoAgrupado(idPagamento, codEmpresa);

                    if (StringUtils.isNotBlank(value) && value.equals("SUCESSO")) {
                        Dialog.create()
                                .title("Informação!")
                                .message("Agrupamento excluído com Sucesso!")
                                .showInformation();

                        this.openLeadsOrcNotasPgtoAgrupados();
                        this.openLeadsFormaPgtoAgrupado();

                        if(!verificaTabela(this.tbLeadsPgtoAgrupado)){
                            this.tbLeadsPgtoAgrupado.append();
                            this.tbLeadsPgtoAgrupado.setVALOR(0);
                            this.tbLeadsPgtoAgrupado.post();
                            this.rn.openLeadsPgtoAgrupado();
                        }

                        if(!verificaTabela(this.tbLeadsPgtoAgrupadoParc)){
                            this.tbLeadsPgtoAgrupadoParc.append();
                            this.tbLeadsPgtoAgrupadoParc.setVALOR(0);
                            this.tbLeadsPgtoAgrupadoParc.post();
                            this.rn.openLeadsPgtoAgrupParc();
                        }

                        if(!verificaTabela(this.tbOrcPreNotasParaAgrupar)){
                            this.tbOrcPreNotasParaAgrupar.append();
                            this.tbOrcPreNotasParaAgrupar.setSALDO_PARA_AGRUPAR(0);
                            this.tbOrcPreNotasParaAgrupar.post();
                        }

                        this.pesquisaOrcamentos();

                        this.btnSelecao.setEnabled(verificaTabela(this.tbOrcPreNotasParaAgrupar));
                        this.btnSelecao.setIconClass("square-o");

                        int index = PageControlUtil.getIndex(this.pgcAgrupaPagamentos, this.tabPagamentosAgrupados);
                        this.pgcAgrupaPagamentos.selectTab(index);

                    } else {
                        EmpresaUtil.showWarningMessage("Selecione um agrupamento para Exclusão!");
                    }
                }
            }
            }catch(DataException e){
                EmpresaUtil.showError("Falha ao adicionar Notas para agrupamento", e);
            }
    }

    private void fechamentoParcial(){
        try{

            if(this.tbLeadsOrcNotasPgtoAgrupados.locate("ID_PAGAMENTO", this.tbLeadsOrcNotasPgtoAgrupados.getID_PAGAMENTO().asInteger())){

                double codEmpresa        = this.tbLeadsOrcNotasPgtoAgrupados.getCOD_EMPRESA().asDecimal();
                double idPagamento       = this.tbLeadsOrcNotasPgtoAgrupados.getID_PAGAMENTO().asDecimal();
                String origemAgrupamento = this.tbLeadsOrcNotasPgtoAgrupados.getTIPO().asString();

                String value = this.rn.fechamentoParcial(idPagamento, origemAgrupamento, codEmpresa);

                if (StringUtils.isNotBlank(value) && value.equals("SUCESSO")) {
                    Dialog.create()
                            .title("Informação!")
                            .message("Fechamento Parcial realizado com Sucesso!")
                            .showInformation();

                    this.openLeadsOrcNotasPgtoAgrupados();
                    this.pesquisaOrcamentos();

                    int index = PageControlUtil.getIndex(this.pgcAgrupaPagamentos, this.tabPagamentosAgrupados);
                    this.pgcAgrupaPagamentos.selectTab(index);

                }else{
                    EmpresaUtil.showErrorMessage("Falha ao realizar o Fechamento Parcial!\n"+value);
                }
            }
        }catch(DataException e){
            EmpresaUtil.showError("Falha ao realizar o Fechamento Parcial!", e);
        }
    }

    @Override
    public void btnFechamentoParcialClick(Event<Object> event) {
        this.fechamentoParcial();
    }
    @Override
    public void btnExcluirPgtoClick(Event<Object> event) {

        boolean possuiAcesso = EmpresaUtil.validarAcesso("K1058");
        if (!possuiAcesso) {
            return;
        }

        Dialog.create()
                .title("Informação")
                .message("Deseja realmente excluir o pagamento agrupado?")
                .confirmOkCancelar((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        this.excluirPgtoAgrupado();
                    }
                });
    }


    private void openLeadsOrcNotasPgtoAgrupados(){
        double codEmpresa = this.cbbComboEmpresa.getValue().asDecimal();
        double codCliente = this.edtCpfCnpj.getValue().asDecimal();
        Date dataInicial  = this.edtDataInicial.getValue().asDate();
        Date dataFinal    = this.edtDataFinal.getValue().asDate();

        try{
            this.rn.openLeadsOrcNotasPgtoAgrupados(codEmpresa, codCliente, dataInicial, dataFinal);
            if(!this.tbLeadsOrcNotasPgtoAgrupados.isEmpty()){
                this.btnExcluirPgto.setEnabled(true);
                this.btnFechamentoParcial.setEnabled(true);

            }else{
                this.btnExcluirPgto.setEnabled(false);
                this.btnFechamentoParcial.setEnabled(false);

            }
        }catch(DataException e){
            EmpresaUtil.showError("Falha ao carregar Orçamentos e Pré-Notas agrupadas!", e);
        }
    }

    private boolean validaConfirmaPgto(){
        boolean retFuncao = false;
        try{
            double valorTotalParcelas = this.rn.carregaValorParcTotal();
            double valorTotalAgrupado = this.edtTotal.getValue().asDecimal();

            if(valorTotalParcelas != valorTotalAgrupado){
                return retFuncao;
            }else{
                retFuncao = true;
            }
        }catch (DataException e){
            EmpresaUtil.showError("Falha ao carregar as Parcelas!", e);
        }

        return retFuncao;
    }

    private void atualizaValor(double valorTotal){
        double valorTotalParcelas = this.carregaValorParcTotal();
        this.vlrTotalAux = valorTotal - valorTotalParcelas;
        this.edtValor.setValue(this.vlrTotalAux);
    }

    private double carregaValorParcTotal(){
        double value = 0.0;
        try{
            value =  this.rn.carregaValorParcTotal();
        }catch (DataException e){
            EmpresaUtil.showError("Falha ao carregar o total das parcelas!", e);
        }
        return value;
    }

    @Override
    public void btnAplicarFormasPagamentosClick(Event<Object> event) {
        this.aplicarPgtoParc();
    }


    private void openLeadsPgtoAgrupParc() {
        try{
            this.rn.openLeadsPgtoAgrupParc();
        }catch (DataException e){
            EmpresaUtil.showError("Falha ao carregar formas de pagamentos parcelas!", e);
        }
    }

    @Override
    public void btnConfirmarFormasPgtoClick(Event<Object> event) {
        confirmarPgtos();
    }

    @Override
    public void grpBoxOrcPrenotaAgrupadasClose(Event<Object> event) {
        this.atualizarGroupBox(event, this.grpBoxOrcPrenotaAgrupadas);
    }

    private void atualizarGroupBox(Event<Object> event, TFGroupbox groupBox) {
        boolean isOpen = (Boolean) event.getValue();
        if (isOpen) {
            groupBox.setHeaderImageId(31002);
        } else {
            groupBox.setHeaderImageId(31001);
        }
    }

    @Override
    public void hBoxSelOrcPreNotasClick(Event<Object> event) {
        int index = PageControlUtil.getIndex(this.pgcAgrupamentoDePagamentos,this.tabOrcPreNotas);
        this.pgcAgrupamentoDePagamentos.selectTab(index);
        this.setColorHBox(this.hBoxSelOrcPreNotas);
    }

    @Override
    public void hBoxOrcPreNotasAgrupadasClick(Event<Object> event) {
        int index = PageControlUtil.getIndex(this.pgcAgrupamentoDePagamentos,this.tabOrcPreNotasSelecionadas);
        this.pgcAgrupamentoDePagamentos.selectTab(index);
        this.setColorHBox(this.hBoxOrcPreNotasAgrupadas);
    }

    @Override
    public void hBoxFormasPagamentoClick(Event<Object> event) {
        int index = PageControlUtil.getIndex(this.pgcAgrupamentoDePagamentos,this.tabFormasDePagamento);
        this.pgcAgrupamentoDePagamentos.selectTab(index);
        this.setColorHBox(this.hBoxFormasPagamento);
        this.cbbFormasPgto.setValue("2");
        this.edtQtdParcelas.setEnabled(false);
        this.edtQtdParcelas.setValue(1);
    }

    private void setColorHBox(TFHBox hBox){
        if (hBox == this.hBoxSelOrcPreNotas) {
            this.hBoxSelOrcPreNotas.setColor(this.colorDestaque);
            this.hBoxOrcPreNotasAgrupadas.setColor(this.colorDefault);
            this.hBoxFormasPagamento.setColor(this.colorDefault);

        } else if (hBox == this.hBoxOrcPreNotasAgrupadas) {
            this.hBoxSelOrcPreNotas.setColor(this.colorDefault);
            this.hBoxOrcPreNotasAgrupadas.setColor(this.colorDestaque);
            this.hBoxFormasPagamento.setColor(this.colorDefault);

        } else if (hBox == this.hBoxFormasPagamento) {
            this.hBoxSelOrcPreNotas.setColor(this.colorDefault);
            this.hBoxOrcPreNotasAgrupadas.setColor(this.colorDefault);
            this.hBoxFormasPagamento.setColor(this.colorDestaque);
        }

        this.hBoxSelOrcPreNotas.invalidate();
        this.hBoxOrcPreNotasAgrupadas.invalidate();
        this.hBoxFormasPagamento.invalidate();

    }

    @Override
    public void btnPesquisarClick(Event<Object> event) {
        String cpfCnpj = this.edtCpfCnpj.getValue().asString().trim();

        if (StringUtils.isBlank(cpfCnpj)) {
            EmpresaUtil.showInformationMessage("CPF/CNPJ, é obrigatório o preenchimento!");
            return;
        }

        if(this.pgcAgrupaPagamentos.getSelectedTab().equals(this.tabOrcamentosPreNota)){
            hBoxSelOrcPreNotasClick(event);
            this.pesquisaOrcamentos();
            this.openLeadsOrcNotasPgtoAgrupados();
            this.openLeadsFormaPgtoAgrupado();

            int index = PageControlUtil.getIndex(this.pgcAgrupaPagamentos,this.tabOrcamentosPreNota);
            this.pgcAgrupaPagamentos.selectTab(index);
        }

        if(this.pgcAgrupaPagamentos.getSelectedTab().equals(this.tabPagamentosAgrupados)){
            this.pesquisaOrcamentos();
            this.openLeadsOrcNotasPgtoAgrupados();
            this.openLeadsFormaPgtoAgrupado();

            int index = PageControlUtil.getIndex(this.pgcAgrupaPagamentos,this.tabPagamentosAgrupados);
            this.pgcAgrupaPagamentos.selectTab(index);
        }

        this.btnSelecao.setEnabled(verificaTabela(this.tbOrcPreNotasParaAgrupar));
        this.btnSelecao.setIconClass("square-o");
        this.btnAddOrcNotas.setEnabled(false);

    }

    @Override
    public void edtCpfCnpjEnter(Event<Object> event) {
        String cpfCnpj = this.edtCpfCnpj.getValue().asString().trim();
        if (StringUtils.isBlank(cpfCnpj)) {
            EmpresaUtil.showInformationMessage("CPF/CNPJ, é obrigatório o preenchimento!");
            this.edtNomeCliente.setValue("");
            return;
        }

        double codCliente  = this.edtCpfCnpj.getValue().asDecimal();
        if(codCliente <= 0){
            this.edtNomeCliente.setValue("");
        }else{
            this.filtraCliente();
        }
    }

    @Override
    public void edtCpfCnpjExit(Event<Object> event) {
        double codCliente  = this.edtCpfCnpj.getValue().asDecimal();
        if(codCliente <= 0){
            this.edtNomeCliente.setValue("");
        }else{
            this.filtraCliente();
        }
    }

    private void filtraCliente(){
        String codCliente = this.edtCpfCnpj.getValue().asString();
        FrmPesquisaRapidaClienteA frm = new FrmPesquisaRapidaClienteA();
        frm.edtPesqRapidaCliente.setValue(codCliente);
        frm.filtrarCliente();

        String nomeCliente = frm.tbLeadsConsultaClientes.getCLIENTE().asString();
        if (nomeCliente.length() > 100) {
            nomeCliente = nomeCliente.substring(0, 100);
        }

        this.edtNomeCliente.setValue(nomeCliente);
    }

    private void pesquisaOrcamentos(){
        this.vlrAux = 0.0;
        this.vlrTotalAux = 0.0;
        this.seqCartao = 0;
        this.edtTotal.setValue(0);
        this.edtValor.setValue(0);

        int codEmpresa    = this.cbbComboEmpresa.getValue().asInteger();
        int nrAgrupamento = this.edtNumeroAgrupamento.getValue().asInteger();
        Date dataInicial  = this.edtDataInicial.getValue().asDate();
        Date dataFinal    = this.edtDataFinal.getValue().asDate();
        double codCliente = this.edtCpfCnpj.getValue().asDecimal();

        try{
            this.rn.pesquisaOrcamentos(codEmpresa, nrAgrupamento, dataInicial, dataFinal, codCliente);
            this.btnSelecao.setEnabled(verificaTabela(this.tbOrcPreNotasParaAgrupar));
            this.btnSelecao.setIconClass("square-o");

            if(!verificaTabela(this.tbLeadsPgtoAgrupado)){
                this.tbLeadsPgtoAgrupado.append();
                this.tbLeadsPgtoAgrupado.setVALOR(0);
                this.tbLeadsPgtoAgrupado.post();
                this.rn.openLeadsPgtoAgrupado();
            }

            if(!verificaTabela(this.tbLeadsPgtoAgrupadoParc)){
                this.tbLeadsPgtoAgrupadoParc.append();
                this.tbLeadsPgtoAgrupadoParc.setVALOR(0);
                this.tbLeadsPgtoAgrupadoParc.post();
                this.rn.openLeadsPgtoAgrupParc();
            }

        }catch (DataException e){
            EmpresaUtil.showError("Falha ao pesquisar Orçamentos e Pré-notas.",e);
        }
    }

    @Override
    public void btnPesquisarClienteClick(Event<Object> event) {
        FrmPesquisaRapidaClienteA frm = new FrmPesquisaRapidaClienteA();
        FormUtil.doShow(frm,
                t -> {
                    String nomeCliente = frm.tbLeadsConsultaClientes.getCLIENTE().asString();
                    if (nomeCliente.length() > 100) {
                        nomeCliente = nomeCliente.substring(0, 100);
                    }

                    String codCliente = frm.tbLeadsConsultaClientes.getCOD_CLIENTE().asString();
                    this.edtCpfCnpj.setValue(codCliente);
                    this.edtNomeCliente.setValue(nomeCliente);

                });
    }

    @Override
    public void selecionaOrcPreNotasParaAgrupar(Event<Object> event) {
        this.selecionaOrcamentos();
    }

    private void selecionaOrcamentos(){
        try{
            this.tbOrcPreNotasParaAgrupar.edit();
            String sel = this.tbOrcPreNotasParaAgrupar.getSEL().asString();

            if(sel.equals("S")){
                this.tbOrcPreNotasParaAgrupar.setSEL("N");
            }else {
                this.tbOrcPreNotasParaAgrupar.setSEL("S");
            }

            this.tbOrcPreNotasParaAgrupar.post();
            this.tbOrcPreNotasParaAgrupar.commitUpdates();
            this.tbOrcPreNotasParaAgrupar.applyUpdates();

            this.habilitaBtnAddOrcNotas();

        }catch (Exception e){
            EmpresaUtil.showError("Falha ao selecionar Orçamentos e Pré-notas.", e);
        }
    }

    @Override
    public void btnSelecaoClick(Event<Object> event) {
        String selecaoIcon = this.btnSelecao.getIconClass().trim();

        if(selecaoIcon.equals("square-o")){
            this.mnSelecionarTodosClick(event);
            btnSelecao.setIconClass("check-square-o");
        }else{
            this.mnSelecionarNenhumClick(event);
            btnSelecao.setIconClass("square-o");
        }
    }

    @Override
    public void mnSelecionarTodosClick(Event<Object> event) {
        this.selecaoOrcamentos(true);
    }

    @Override
    public void mnSelecionarNenhumClick(Event<Object> event) {
        this.selecaoOrcamentos(false);
    }

    @Override
    public void tbLeadsOrcNotasPgtoAgrupadosAfterScroll(Event<Object> event) {
        this.openLeadsFormaPgtoAgrupado();
    }

    private void openLeadsFormaPgtoAgrupado(){
        try{
            this.rn.openLeadsFormaPgtoAgrupado();
        }catch (DataException e){
            EmpresaUtil.showError("Falha ao Carregar as formas de pagamentos.", e);
        }
    }

    private void selecaoOrcamentos(boolean selecionar){
        try{
            this.tbOrcPreNotasParaAgrupar.first();
            while (Boolean.FALSE.equals(this.tbOrcPreNotasParaAgrupar.eof())){
                this.tbOrcPreNotasParaAgrupar.edit();

                String marcador = selecionar ? "S" : "N";
                this.tbOrcPreNotasParaAgrupar.setSEL(marcador);

                if(marcador.equals("N")){
                    this.btnSelecao.setIconClass("square-o");
                }

                if(marcador.equals("S")){
                    this.btnSelecao.setIconClass("check-square-o");
                }

                this.tbOrcPreNotasParaAgrupar.post();
                this.tbOrcPreNotasParaAgrupar.next();
            }

            this.tbOrcPreNotasParaAgrupar.commitUpdates();
            this.tbOrcPreNotasParaAgrupar.applyUpdates();

            this.habilitaBtnAddOrcNotas();

        }catch (Exception e){
            EmpresaUtil.showError("Falha na seleção de Orçamentos e Pré-notas!", e);
        }
    }

    private void habilitaBtnAddOrcNotas(){
        try {
            this.tbOrcPreNotasParaAgrupar.first();
            boolean enabled = false;

            while (!this.tbOrcPreNotasParaAgrupar.eof()) {
                String value = this.tbOrcPreNotasParaAgrupar.getSEL().asString();
                if ("S".equals(value)) {
                    enabled = true;
                    break;
                }
                this.tbOrcPreNotasParaAgrupar.next();
            }
            this.btnAddOrcNotas.setEnabled(enabled);
        } catch (Exception e){
            EmpresaUtil.showError("Falha na seleção de Orçamentos e Pré-notas!", e);
        }
    }

    private void addNotaParaAgrupamento (){
        try{

            int nrOs          = 0;
            String  tipo      = "";
            double valor      = 0;
            int codEmpresa    = 0;
            int codOrcMapa    = 0;
            double codCliente    = 0;

            this.tbOrcPreNotasParaAgrupar.first();
            while(!this.tbOrcPreNotasParaAgrupar.eof()){
                String value = this.tbOrcPreNotasParaAgrupar.getSEL().asString();
                if(value.equals("S")){
                    nrOs       = this.tbOrcPreNotasParaAgrupar.getCOD_ORDEM_COMPRA().asInteger();
                    tipo       = this.tbOrcPreNotasParaAgrupar.getTIPO().asString();
                    valor      = this.tbOrcPreNotasParaAgrupar.getSALDO_PARA_AGRUPAR().asDecimal();
                    codEmpresa = this.tbOrcPreNotasParaAgrupar.getCOD_EMPRESA().asInteger();
                    codOrcMapa = this.tbOrcPreNotasParaAgrupar.getCOD_ORC_MAPA().asInteger();
                    codCliente = this.tbOrcPreNotasParaAgrupar.getCOD_CLIENTE().asDecimal();

                    if(!this.rn.temNota(codOrcMapa, this.tbLeadsPgtoAgrupado)){
                        this.rn.addNotaParaAgrupamento(codEmpresa, nrOs, codOrcMapa, codCliente, tipo, valor);

                    }else{
                        Dialog.create()
                                .title("Informação")
                                .message("O Nr. Orçamento ["+codOrcMapa+"] já foi informado!")
                                .confirmOkCancelar((String dialogResult) -> {
                                    if (CastUtil.asInteger(dialogResult) == IDialog.OK) {
                                        try {
                                            this.tbOrcPreNotasParaAgrupar.next();
                                        } catch (DataException e) {
                                            EmpresaUtil.showError("Falha ao adicionar Notas para agrupamento",e);
                                        }
                                    }
                                });
                    }
                }

                this.tbOrcPreNotasParaAgrupar.next();
            }
        }catch (DataException e){
            EmpresaUtil.showError("Falha ao adicionar Notas para agrupamento",e);
        }
    }

    @Override
    public void btnRemoverTodosItensClick(Event<Object> event) {
        this.removeTodosItensAgrupado();
    }

    @Override
    public void edtValorEnter(Event<Object> event) {
        this.btnAplicarFormasPagamentosClick(event);
    }

    @Override
    public void gridOrcPreNotasSelecionadasremoveItemAgrupadoGrid(Event<Object> event) {
        this.removeItemAgrupado();
    }

    private void removeItemAgrupado(){
        try{
            if(this.tbLeadsPgtoAgrupado.locate("COD_ORC_MAPA", tbLeadsPgtoAgrupado.getCOD_ORC_MAPA().asInteger())) {
                this.rn.removeItemAgrupado();

            }

            if(!verificaTabela(this.tbLeadsPgtoAgrupado)){
                this.tbLeadsPgtoAgrupado.append();
                this.tbLeadsPgtoAgrupado.setVALOR(0);
                this.tbLeadsPgtoAgrupado.post();
                this.rn.openLeadsPgtoAgrupado();
            }
        }catch (DataException e){
            EmpresaUtil.showError("Falha ao remover Item do agrupamento.", e);
        }
    }

    private void removeTodosItensAgrupado(){
        try{
            this.rn.openLeadsPgtoAgrupado();

            if(!verificaTabela(this.tbLeadsPgtoAgrupado)){
                this.tbLeadsPgtoAgrupado.append();
                this.tbLeadsPgtoAgrupado.setVALOR(0);
                this.tbLeadsPgtoAgrupado.post();
                this.rn.openLeadsPgtoAgrupado();
            }
        }catch (DataException e){
            EmpresaUtil.showError("Falha ao remover Item do agrupamento.", e);
        }
    }

    @Override
    public void btnRemoverFormasPagamentosClick(Event<Object> event) {
        this.removeTodosItensFormasPgto();

    }
    private void removeTodosItensFormasPgto(){
        this.openLeadsPgtoAgrupParc();

        try{
            if(!this.verificaTabela(this.tbLeadsPgtoAgrupadoParc)){
                this.tbLeadsPgtoAgrupadoParc.append();
                this.tbLeadsPgtoAgrupadoParc.setVALOR(0);
                this.tbLeadsPgtoAgrupadoParc.post();
                this.openLeadsPgtoAgrupParc();
            }

            double vlrTotal = this.edtTotal.getValue().asDecimal();
            atualizaValor(vlrTotal);

            this.btnConfirmarFormasPgto.setEnabled(this.verificaTabela(this.tbLeadsPgtoAgrupadoParc));
            this.btnRemoverFormasPgto.setEnabled(this.verificaTabela(this.tbLeadsPgtoAgrupadoParc));

        }catch (DataException e){
            EmpresaUtil.showError("Falha ao remover Item Formas Pagamento.", e);
        }
    }

    @Override
    public void gridFormasPgtoOrcNotasbtnRemoverFormasPagamentosClick(Event<Object> event) {
        this.removeItemFormasPgto();
    }

    private void removeItemFormasPgto(){
        double vlrTotal = this.edtTotal.getValue().asDecimal();
        try{

            if(verificaTabela(this.tbLeadsPgtoAgrupadoParc)){
                if(this.tbLeadsPgtoAgrupadoParc.locate("SEQUENCIA_CARTAO", tbLeadsPgtoAgrupadoParc.getSEQUENCIA_CARTAO().asString())) {
                    rn.removeItemFormaPgto();
                }
            }

            if(!this.verificaTabela(this.tbLeadsPgtoAgrupadoParc)){
                this.tbLeadsPgtoAgrupadoParc.append();
                this.tbLeadsPgtoAgrupadoParc.setVALOR(0);
                this.tbLeadsPgtoAgrupadoParc.post();
                this.openLeadsPgtoAgrupParc();
                this.seqCartao = 0;
            }

            atualizaValor(vlrTotal);
            this.btnConfirmarFormasPgto.setEnabled(this.verificaTabela(this.tbLeadsPgtoAgrupadoParc));
            this.btnRemoverFormasPgto.setEnabled(this.verificaTabela(this.tbLeadsPgtoAgrupadoParc));

        }catch (DataException e){
            EmpresaUtil.showError("Falha ao remover Item Formas Pagamento.", e);
        }
    }

    @Override
    public void btnVoltarClick(final Event event) {
        fechou = true;
        if (!abriuComoTabSeet()){
            close();
        }
    }

    private boolean abriuComoTabSeet() {
        boolean ehTabSheet = false;
        TFPageControl pg = FormUtil.getPgCtrl();
        for (int i = 0; i < pg.getPageCount(); i++) {
            ITFTabsheet tab = pg.getTabSheet(i);
            if (tab.getCaption().equals("Agrupa Pagamentos")) {
                if (!this.isReponsive) {
                    tab.close();
                    ehTabSheet = true;
                }
                break;
            }
        }
        return ehTabSheet;
    }
}