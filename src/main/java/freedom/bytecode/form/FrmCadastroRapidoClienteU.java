package freedom.bytecode.form;

import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * <AUTHOR>
 */
public class FrmCadastroRapidoClienteU extends FrmCadastroRapidoClienteA {

    private Double codCliente = -1.0;
    private final boolean ehCRMParts = EmpresaUtil.isCrmParts();

    private final boolean ehCRMService = EmpresaUtil.isCrmService();

    @Override
    public boolean buscarCliente(Double codCliente) {
        this.codCliente = codCliente;
        if (!EmpresaUtil.validarAcesso("B2200")) {
            return false;
        } else {
            return super.buscarCliente(codCliente);
        }
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        if (ehCRMService) {
            if (codCliente < 0.0) {
                if (EmpresaUtil.validarAcesso("B2208")) {
                    super.btnSalvarClick(event);
                }
            } else {
                if (EmpresaUtil.validarAcesso("B2201")) {
                    super.btnSalvarClick(event);
                }
            }
        } else {
            super.btnSalvarClick(event);
        }
    }

    //Campos ckCrmEmail e ckCrmSms foram alterados para campos do tipo TFCombo
    @Override
    public void cboCrmEmailChange(Event<Object> event) {
        if (!EmpresaUtil.validarAcesso("B2202")) {
            try {
                tbClienteDiverso.edit();
                tbClienteDiverso.setCRM_EMAIL(tbClienteDiverso.getCRM_EMAIL().oldValue().asString());
            } catch (DataException ex) {
                EmpresaUtil.showError("ERRO", ex);
            }
        }
        super.cboCrmEmailChange(event);
    }

    @Override
    public void cboCrmSMSChange(Event<Object> event) {
        if (!EmpresaUtil.validarAcesso("B2202")) {
            try {
                tbClienteDiverso.edit();
                tbClienteDiverso.setCRM_SMS(tbClienteDiverso.getCRM_SMS().oldValue().asString());
            } catch (DataException ex) {
                EmpresaUtil.showError("ERRO", ex);
            }
        }
        super.cboCrmSMSChange(event);
    }

    @Override
    public void edtDddCelChange(Event<Object> event) {
        validaAcessoCelular();
        super.edtDddCelChange(event);
    }

    @Override
    public void edtTelefoneCelChange(Event<Object> event) {
        validaAcessoCelular();
        super.edtTelefoneCelChange(event);
    }

    private void validaAcessoCelular() {
        if (codCliente > 0.0) {
            String codAcesso = "";
            if (ehCRMParts) {
                codAcesso = "K0251";
            } else {
                //CrmService
                codAcesso = "B2206";
            }
            if (!EmpresaUtil.validarAcesso(codAcesso)) {
                try {
                    tbClientes.edit();
                    tbClientes.setPREFIXO_CEL(tbClientes.getPREFIXO_CEL().oldValue().asInteger());
                    tbClientes.setTELEFONE_CEL(tbClientes.getTELEFONE_CEL().oldValue().asInteger());
                } catch (DataException ex) {
                    EmpresaUtil.showError("ERRO", ex);
                }
            }
        }
    }

    @Override
    public void edtEmailChange(Event<Object> event) {
        if (codCliente > 0.0) {
            String codAcesso = "";
            if (ehCRMParts) {
                codAcesso = "K0254";
            } else {
                //CrmService
                codAcesso = "B2207";
            }
            if (!EmpresaUtil.validarAcesso(codAcesso)) {
                try {
                    tbClientes.edit();
                    tbClientes.setENDERECO_ELETRONICO(tbClientes.getENDERECO_ELETRONICO().oldValue().asString());
                } catch (DataException ex) {
                    EmpresaUtil.showError("ERRO", ex);
                }
            }
        }
        super.edtEmailChange(event);
    }

//    @Override
//    public void ckCRM_SMSCheck(Event<Object> event) {
//        if (!EmpresaUtil.validarAcesso("B2202")) {
//            try {
//                tbClienteDiverso.edit();
//                tbClienteDiverso.setCRM_SMS(tbClienteDiverso.getCRM_SMS().oldValue().asString());
//            } catch (DataException ex) {
//                EmpresaUtil.showError("ERRO", ex);
//            }
//        }
//        super.ckCRM_SMSCheck(event);
//    }
//
//    @Override
//    public void ckCRM_EMAILCheck(Event<Object> event) {
//        if (!EmpresaUtil.validarAcesso("B2202")) {
//            try {
//                tbClienteDiverso.edit();
//                tbClienteDiverso.setCRM_EMAIL(tbClienteDiverso.getCRM_EMAIL().oldValue().asString());
//            } catch (DataException ex) {
//                EmpresaUtil.showError("ERRO", ex);
//            }
//        }
//        super.ckCRM_EMAILCheck(event);
//    }


}
