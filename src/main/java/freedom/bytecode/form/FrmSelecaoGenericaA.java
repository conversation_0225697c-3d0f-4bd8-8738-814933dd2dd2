package freedom.bytecode.form;
import freedom.bytecode.form.wizard.*;
import freedom.data.impl.View;
import freedom.util.EmpresaUtil;

import freedom.client.event.*;
import freedom.data.DataException;

import java.util.ArrayList;
import java.util.List;

/**
 * ************* /EXEMPLO DE UTILIZAÇÃO/ ***************************************************************
 * // Cria uma nova instância da classe FrmSelecaoGenericaA
 * FrmSelecaoGenericaA frmSelecaoGenerica = new FrmSelecaoGenericaA();
 *
 * // Define o título da janela do formulário
 * frmSelecaoGenerica.setTitulo("Titulo da Tela");
 *
 * // Permite a seleção de múltiplos itens no formulário
 * frmSelecaoGenerica.setPermitirSelecaoMultipla(true);
 *
 * // Carrega a tabela "EMPRESAS" no formulário com os campos "COD_EMPRESA" e "NOME"
 * // Filtra os registros onde "STATUS='S'" e limita a 100 registros
 * if (frmSelecaoGenerica.carregarTabela("EMPRESAS", "COD_EMPRESA", "NOME", "STATUS='S'",100)) {
 *
 *  // Exibe o formulário e define um EventListener para processar a seleção do usuário
 * 	FormUtil.doShow(frmSelecaoGenerica, (EventListener) t -> {
 *
 * 		// Exemplo 1: Caso sem seleção múltipla
 * 		// Processa o item selecionado
 * 		// Se o usuário confirmou a seleção (clicou em OK)
 * 		if (frmSelecaoGenerica.isOk()) {
 * 			// Obtém a chave (LookupKey) e a descrição (LookupDescricao) do item selecionado
 * 			String valorLookupKey = frmSelecaoGenerica.getLookupKey();
 * 			String valorLookupDescricao = frmSelecaoGenerica.getLookupDescricao();
 * 			// Aqui você pode adicionar o código para processar o valorLookupKey e valorLookupDescricao
 *                }
 *
 * 		// Exemplo 2: Caso com seleção múltipla
 * 		// Processa todos os itens selecionados
 * 		// Se o usuário confirmou a seleção (clicou em OK)
 * 		if (frmSelecaoGenerica.isOk()) {
 * 			// Itera sobre todos os itens selecionados
 * 			for(FrmSelecaoGenericaA.Item item : frmSelecaoGenerica.getItensSelecionados()){
 * 				// Obtém a chave (LookupKey) e a descrição (LookupDescricao) do item
 * 				String valorKey = item.getLookupKey();
 * 				String valorField = item.getLookupDescricao();
 * 				// Aqui você pode adicionar o código para processar o valorKey e valorField
 *            }
 *        }* 	});
 * }
 * *****************************************************************************************************
 */
public class FrmSelecaoGenericaA extends FrmSelecaoGenericaW {
    private static final long serialVersionUID = 20130827081850L;
    private String titulo;
    private boolean permiteSelecaoMultipla;


    /**
     * Classe interna Item.
     * <p>
     * Esta classe é usada para retornar um valor iterável em um método da classe externa.
     * Cada instância de Item representa um item individual que pode ser selecionado no formulário.
     * <p>
     * Cada item tem uma chave (lookupKey) e uma descrição (lookupDescricaoField).
     */
    class Item{
        private String lookupKey;
        private String lookupDescricaoField;

        /**
         * Construtor da classe Item.
         *
         * @param lookupKey A chave do item.
         * @param lookupDescricaoField A descrição do item.
         */
        public Item(String lookupKey, String lookupDescricaoField) {
            this.lookupKey = lookupKey;
            this.lookupDescricaoField = lookupDescricaoField;
        }

        /**
         * Retorna a chave do item.
         *
         * @return A chave do item.
         */
        public String getLookupKey() {
            return lookupKey;
        }

        /**
         * Retorna a descrição do item.
         *
         * @return A descrição do item.
         */
        public String getLookupDescricao() {
            return lookupDescricaoField;
        }
    }

    private boolean ok = false;

    public boolean isOk() {
        return ok;
    }

    public void setOk(boolean ok) {
        this.ok = ok;
    }

    /**
     * Construtor da classe FrmSelecaoGenericaA.
     * <p>
     * Este construtor inicializa uma nova instância da classe FrmSelecaoGenericaA com configurações padrão.
     * A seleção múltipla é desativada e o título do formulário é definido como "Selecionar".
     */
    public FrmSelecaoGenericaA(){
        // Desativa a seleção múltipla
        setPermitirSelecaoMultipla(false);

        // Define o título do formulário como "Selecionar"
        setTitulo("Selecionar");
    }

    public void setPermitirSelecaoMultipla(boolean isTrue){
        this.permiteSelecaoMultipla = isTrue;
        this.gridSelecaoGenerica.getColumns().get(0).setVisible(isTrue);
    }

    public void setTitulo(String titulo){
        this.titulo = titulo;
        this.setCaption(this.titulo);
    }

    /**
     * Carrega uma tabela no formulário.
     *
     * @param table O nome da tabela a ser carregada.
     * @param keyField O nome do campo chave da tabela.
     * @param descField O nome do campo de descrição da tabela.
     * @param filtro O filtro a ser aplicado na tabela.
     * @param numeroMaximoRegistro O número máximo de registros a serem retornados.
     * @return Verdadeiro se a tabela foi carregada com sucesso, falso se ocorreu um erro.
     */
    public boolean carregarTabela(String table, String keyField, String descField, String filtro, int numeroMaximoRegistro){
        try {
            // Define os valores iniciais para a pesquisa na tabela
            rn.setValoresIniciais(table, keyField, descField, filtro, numeroMaximoRegistro);

            // Realiza a pesquisa na tabela
            pesquisar(edFiltroKey.getValue().asString(),edFiltroField.getValue().asString());

            // Se não houve erros, retorna verdadeiro
            return true;
        } catch (DataException e) {
            // Se ocorreu um erro, mostra uma mensagem de erro e retorna falso
            EmpresaUtil.showError("Erro ao carregar tabela", e);
            return false;
        }
    }

    public void pesquisar(String filterKey, String filterField) throws DataException {
       rn.filtrarTbTSelecaoGenerica(filterKey, filterField);
    }

    @Override
    public void btnPesquisarClick(Event<Object> event) {
        try {
            pesquisar(edFiltroKey.getValue().asString().trim(), edFiltroField.getValue().asString().trim());
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao carregar tabela", e);
        }
    }

    @Override
    public void edFiltroKeyEnter(Event<Object> event) {
        btnPesquisarClick(null);
    }

    @Override
    public void edFiltroFieldEnter(Event<Object> event) {
        btnPesquisarClick(null);
    }

    @Override
    public void btnFecharClick(Event<Object> event) {
        this.setOk(false);
        close();
    }

    @Override
    public void btnAceitarClick(Event<Object> event) {
        this.setOk(true);
        close();
    }

    @Override
    public void gridSelecaoGenericadesmarcarRegistroNaGridSelecaoGenerica(Event<Object> event) {
        try {
            rn.desmarcarRegistroDaGrade();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao desmarcar item", e);
        }
    }

    @Override
    public void gridSelecaoGenericamarcarRegistroNaGridSelecaoGenerica(Event<Object> event) {
        try {
            rn.marcarRegistroDaGrade();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao marcar item", e);
        }
    }


    /**
     * Retorna a chave (lookupKey) do item selecionado.
     *
     * @return A chave do item selecionado.
     * @throws DataException Se ocorrer um erro ao obter a chave.
     */
    public String getLookupKey() throws DataException {
        // Retorna a chave do item selecionado
        return rn.getLookupKey();
    }

    /**
     * Retorna a descrição (lookupDescricao) do item selecionado.
     *
     * @return A descrição do item selecionado.
     * @throws DataException Se ocorrer um erro ao obter a descrição.
     */
    public String getLookupDescricao() throws DataException {
        // Retorna a descrição do item selecionado
        return rn.getLookupDescricao();
    }

    /**
     * Retorna uma lista iteravel com os itens selecionados.
     *
     * @return Uma lista de itens selecionados.
     * @throws DataException Se ocorrer um erro ao obter os itens selecionados.
     */
    public Iterable<Item> getItensSelecionados() throws DataException {
        // Seleciona os registros onde "EHSELECIONADO='S'" na tabela tbSelecaoGenerica
        View v = tbSelecaoGenerica.select("EHSELECIONADO='S'");
        List<Item> itens = new ArrayList<>();


        v.first();
        while (!v.eof()){
            // Adiciona um novo item à lista com a chave (LOOKUP_KEY) e a descrição (LOOKUP_DESCRICAO) do registro atual
            itens.add(new Item(v.getField("LOOKUP_KEY").asString(),v.getField("LOOKUP_DESCRICAO").asString()));
            v.next();
        }

        return itens;
    }
}
