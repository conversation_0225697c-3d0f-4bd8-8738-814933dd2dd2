package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmWhatsAppFrameExternoW;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;

public class FrmWhatsAppFrameExternoA extends FrmWhatsAppFrameExternoW {

    private static final long serialVersionUID = 20130827081850L;

    private final String urlApi = "https://api.leadzap.com.br/v1/"; //WorkList.getParameter("URL_API").asString();
    private final String urlBase = "https://api.leadzap.com.br/index.html?token=";

    protected String chatToken = ""; // WorkList.getParameter("CHAT_TOKEN").asString();
    protected String phoneCliente = ""; // WorkList.getParameter("FONE_CLIENTE").asString();
    protected String usuarioNome = ""; // WorkList.getParameter("USUARIO_LOGADO").asString();
    protected String phoneNumberToken = ""; //WorkList.getParameter("PHONE_NUMBER_TOKEN").asString();

    public FrmWhatsAppFrameExternoA(String chatToken, String phoneCliente, String usuarioNome, String phoneNumberToken) {
        try {
            this.phoneCliente = phoneCliente.replaceAll("[^0-9]", "").trim();
            if (this.phoneCliente.length() < 11) {
                Dialog.create()
                        .title("Erro ao abrir Chat")
                        .message("Telefone celular do cliente não esta completo com 9 digitos + DDD com 2 digitos")
                        .showError((EventListener) arg0 -> {
                            close();

                        });
                return;
            }
            this.usuarioNome = usuarioNome;
            this.chatToken = chatToken;
            this.phoneNumberToken = phoneNumberToken;
//            leadzap.actions.Conversas conversa = new leadzap.actions.Conversas();

            setCaption("WhatsApp: " + phoneCliente);
            if (chatToken.isEmpty()) {
//                chatToken = conversa.abrirMensagem(urlApi,
//                        phoneNumberToken, usuarioNome, this.phoneCliente);
            }

            FFrameWhatsApp.setUrl(urlBase + chatToken);
        } catch (Exception ex) {
            Dialog.create()
                    .title("Erro ao abrir Chat")
                    .message(ex.getMessage())
                    .showError((EventListener) arg0 -> {
                        close();
                    });
        }
    }

    public FrmWhatsAppFrameExternoA(String chatToken, String phoneCliente) {
        try {

            this.chatToken = chatToken;

            setCaption("WhatsApp: " + phoneCliente);
            if (chatToken.isEmpty()) {
                Dialog.create()
                        .title("Erro ao abrir Chat")
                        .message("Link não informado")
                        .showError();
                return;
            }

            FFrameWhatsApp.setUrl(urlBase + chatToken);
        } catch (Exception ex) {
            Dialog.create()
                    .title("Erro ao abrir Chat")
                    .message("NBS-ERROR-C0101: " + ex.getMessage())
                    .showError();
        }
    }
}
