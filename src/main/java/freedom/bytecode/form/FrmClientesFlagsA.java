package freedom.bytecode.form;

import freedom.bytecode.cursor.LISTA_FORMAS_PGTO;
import freedom.bytecode.form.wizard.FrmClientesFlagsW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.util.CastUtil;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;
import lombok.Setter;
import org.zkoss.zk.ui.HtmlBasedComponent;
import org.zkoss.zul.Tabbox;

public class FrmClientesFlagsA extends FrmClientesFlagsW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private final String codigosDasEmpresasDoUsuario = this.getCodigosDasEmpresasDoUsuario(
            this.usuarioLogado
    );

    private String representanteCliente = "";

    private LISTA_FORMAS_PGTO tabelaDeFormasDePagamentoSelecionadasNaGrade;

  	@Setter
    private long codCliente = 0;

    public FrmClientesFlagsA() {
        ((Tabbox) this.pgFlags.getImpl()).getTabs().getChildren().forEach(t -> ((HtmlBasedComponent) t).setStyle("font-size: 15px"));
        ((Tabbox) this.pgFinanceiro.getImpl()).getTabs().getChildren().forEach(t -> ((HtmlBasedComponent) t).setStyle("font-size: 15px"));
        this.tabelaDeFormasDePagamentoSelecionadasNaGrade = new LISTA_FORMAS_PGTO("tabelaDeFormasDePagamentoSelecionadasNaGrade");
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        boolean retornoValidacaoAcessoK0235 = EmpresaUtil.validarAcesso(
                "K0235"
        );
        if (!retornoValidacaoAcessoK0235) {
            this.close();
        }
        this.carregarCboFormaCob(
                this.usuarioLogado
        );
        this.definirPropriedadeEnabledDosCampos();
        this.carregarCboVendedor();
        this.carregarCboRepresentante();
        this.carregarLblNomeCliente(
                this.codCliente
        );
        this.carregarGrdClienteFatLivre(
                this.codCliente
        );
        this.carregarGrdCondPgto(
                this.codCliente
        );
        this.carregarGrdFormasDePagamento(
                this.codCliente
        );
        this.carregarGrdDescontoLetra(
                this.codCliente
        );
        this.carregarGrdMargemMinimaMarkup(
                this.codCliente
        );
        this.carregarCboSegmentoLookupTable();
        this.carregarCboSegmento(
                this.codCliente
        );
        this.carregarGrdDescontoLetraEmpresa(
                this.codCliente
        );
        this.carregarGradeMargemMinimaMarkupEmpresa(
                this.codCliente
        );
        this.carregarEmpresasParaAColunaDaGradeDescontoPorLetraEmpresa();
        boolean tbDadosJuridicosFlagNotEmpty = !this.tbDadosJuridicosFlag.isEmpty();
        this.vBoxSegmento.setVisible(
                tbDadosJuridicosFlagNotEmpty
        );
        this.carregarGrdVendedoresAssociados(
                this.codCliente
                ,this.codigosDasEmpresasDoUsuario
        );
    }

    private void carregarEmpresasParaAColunaDaGradeDescontoPorLetraEmpresa() {
        try {
            this.rn.carregarEmpresasParaAColunaDaGradeDescontoPorLetraEmpresa();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de descontos por letra/empresa"
                    ,dataException
            );
        }
    }

    private void carregarGrdDescontoLetraEmpresa(
            long codCliente
    ) {
        try {
            this.rn.carregarGrdDescontoLetraEmpresa(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de descontos por letra/empresa"
                    ,dataException
            );
        }
    }

    private void carregarCboSegmento(
            long codCliente
    ) {
        try {
            this.rn.carregarCboSegmento(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar os segmentos"
                    ,dataException
            );
        }
    }

    private void carregarCboSegmentoLookupTable() {
        try {
            this.rn.carregarCboSegmentoLookupTable();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar os segmentos"
                    ,dataException
            );
        }
    }

    private void carregarGrdMargemMinimaMarkup(
            long codCliente
    ) {
        try {
            this.rn.carregarGrdMargemMinimaMarkup(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de margem mínima e markup"
                    ,dataException
            );
        }
    }

    private void carregarGrdDescontoLetra(
            long codCliente
    ) {
        try {
            this.rn.carregarGrdDescontoLetra(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de descontos por letra"
                    ,dataException
            );
        }
    }

    private void carregarGrdFormasDePagamento(
            long codCliente
    ) {
        try {
            this.rn.carregarGrdFormasDePagamento(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar as formas de pagamento"
                    ,dataException
            );
        }
    }

    @Override
    public void cboVendedorChange(Event<Object> event) {
        this.salvarClienteFlag();
    }

    @Override
    public void cboRepresentanteChange(final Event<Object> event) {
        try {
            this.representanteCliente = this.tbBuscaRepresentanteCliente.getNOME().asString();
            this.rn.setRepresentante(this.representanteCliente);
            this.salvarClienteFlag();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao alterar representante",
                    dataException);
        }
    }

    @Override
    public void grdClienteFatLivresetFaturamentoLivre(Event<Object> event) {
        try {
            this.rn.setFaturamentoLivre(
                    this.codCliente
            );
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Falha ao definir faturamento livre"
                    ,exception
            );
        }
    }

    @Override
    public void cboFormaCobChange(Event<Object> event) {
        try {
            boolean tbClienteDiversoFlagNotEmpty = !this.tbClienteDiversoFlag.isEmpty();
            boolean cboFormaCobNotEmpty = !this.cboFormaCob.getText().isEmpty();
            if (tbClienteDiversoFlagNotEmpty
                    && cboFormaCobNotEmpty) {
                this.tbClienteDiversoFlag.edit();
                long codEmpresa = this.tbFormaCobrancaFlag.getCOD_EMPRESA().asLong();
                this.tbClienteDiversoFlag.setCOD_EMPRESA_FC(codEmpresa);
                int codFormaCobranca = this.tbFormaCobrancaFlag.getCOD_FORMA_COBRANCA().asInteger();
                this.tbClienteDiversoFlag.setCOD_FORMA_COBRANCA(codFormaCobranca);
                this.salvarClienteFlag();
            }
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    Constantes.FALHA_AO_DEFINIR_FORMA_DE_COBRANCA
                    ,exception
            );
        }
    }

    private void salvarClienteFlag() {
        try {
            this.rn.salvarClienteFlag();
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Falha ao salvar cliente flag"
                    ,exception
            );
        }
    }

    @Override
    public void chkFatLivreCheck(Event<Object> event) {
        this.salvarClienteFlag();
    }

    @Override
    public void chkUsaPrecoFabricaCheck(Event<Object> event) {
        this.salvarClienteFlag();
    }

    @Override
    public void chkUsaPrecoGarantiaCheck(Event<Object> event) {
        this.salvarClienteFlag();
    }

    @Override
    public void chkAprovaAutoCheck(Event<Object> event) {
        this.salvarClienteFlag();
    }

    @Override
    public void chkReservaAutoCheck(Event<Object> event) {
        this.salvarClienteFlag();
    }

    @Override
    public void chkAtacadistaCheck(Event<Object> event) {
        this.salvarClienteFlag();
    }

    @Override
    public void intTempoReservaExit(Event<Object> event) {
        this.salvarClienteFlag();
    }

    private void excluirCondicaoPagamentoGrdCondPgto() {
        Dialog.create()
                .title(Constantes.CONFIRMACAO)
                .message(Constantes.DESEJA_REALMENTE_EXCLUIR)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        long codEmpresa = this.tbClienteFormaPgtoFlag.getCOD_EMPRESA().asLong();
                        long codEmpresaDepartamento = this.tbClienteFormaPgtoFlag.getCOD_EMPRESA_DEPARTAMENTO().asLong();
                        long codFormaPgto = this.tbClienteFormaPgtoFlag.getCOD_FORMA_PGTO().asLong();
                        long codCondicaoPagamento = this.tbClienteFormaPgtoFlag.getCOD_CONDICAO_PAGAMENTO().asLong();
                        this.excluirClienteCondicaoDePagamento(
                                codEmpresa
                                ,codEmpresaDepartamento
                                ,codFormaPgto
                                ,codCondicaoPagamento
                                ,this.codCliente
                        );
                        this.carregarGrdCondPgto(
                                this.codCliente
                        );
                    }
                });
    }

    @Override
    public void grdCondPgtoexcluirCondicaoDePagamento(Event<Object> event) {
        this.excluirCondicaoPagamentoGrdCondPgto();
    }

    private void excluirClienteCondicaoDePagamento(
            long codEmpresa
            ,long codEmpresaDepartamento
            ,long codFormaPgto
            ,long codCondicaoPagamento
            ,long codCliente
    ) {
        try {
            this.rn.excluirClienteCondicaoDePagamento(
                    codEmpresa
                    ,codEmpresaDepartamento
                    ,codFormaPgto
                    ,codCondicaoPagamento
                    ,codCliente
            );
        } catch (Exception exception) {
            EmpresaUtil.showError("Erro ao excluir condição de pagamento",
                    exception);
        }
    }

    @Override
    public void cboFormaCobExit(final Event<Object> event) {
        try {
            boolean tbClienteDiversoFlagNotEmpty = !this.tbClienteDiversoFlag.isEmpty();
            boolean comboFormaCobEmpty = this.cboFormaCob.getText().isEmpty();
            if (tbClienteDiversoFlagNotEmpty
                && comboFormaCobEmpty) {
                this.tbClienteDiversoFlag.edit();
                this.tbClienteDiversoFlag.setCOD_EMPRESA_FC(null);
                this.tbClienteDiversoFlag.setCOD_FORMA_COBRANCA(null);
                this.salvarClienteFlag();
            }
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    Constantes.FALHA_AO_DEFINIR_FORMA_DE_COBRANCA
                    ,exception
            );
        }
    }

    @Override
    public void cboVendedorExit(final Event<Object> event) {
        try {
            if (this.cboVendedor.getText().isEmpty()) {
                this.tbClienteDiversoFlag.edit();
                this.tbClienteDiversoFlag.setVENDEDOR_RESPONSAVEL(null);
                this.salvarClienteFlag();
            }
        } catch (Exception exception) {
            EmpresaUtil.showError("Falha ao definir vendedor",
                    exception);
        }
    }

    @Override
    public void cboVendedorClearClick(Event<Object> event) {
        this.salvarClienteFlag();
    }

    @Override
    public void cboRepresentanteClearClick(final Event<Object> event) {
        try {
            this.representanteCliente = "";
            this.rn.setRepresentante(this.representanteCliente);
            this.salvarClienteFlag();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar representante"
                    ,dataException
            );
        }
    }

    private void excluirFormaDePagamentoGrdFormasDePagamento() {
        Dialog.create()
                .title(Constantes.CONFIRMACAO)
                .message(Constantes.DESEJA_REALMENTE_EXCLUIR)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            long formaPgtoCodigo = this.tbListaClienteFormasPagamento.getFORMA_PGTO_CODIGO().asLong();
                            long codEmpresa = this.tbListaClienteFormasPagamento.getEMPRESA_CODIGO().asLong();
                            long codDepartamento = this.tbListaClienteFormasPagamento.getDEPARTAMENTO_CODIGO().asLong();
                            String registroExcluido = this.rn.excluirClienteFormasPagamento(
                                    formaPgtoCodigo
                                    ,codEmpresa
                                    ,codDepartamento
                                    ,this.codCliente
                            );
                            if (!registroExcluido.equals("S")) {
                                String mensagemDeNaoExclusaoDoRegistro = "O registro não foi excluído."
                                        + System.lineSeparator()
                                        + System.lineSeparator()
                                        + registroExcluido;
                                EmpresaUtil.showInformationMessage(mensagemDeNaoExclusaoDoRegistro);
                            } else {
                                this.rn.carregarGrdFormasDePagamento(
                                        this.codCliente
                                );
                            }
                        } catch (
                                DataException dataException
                        ) {
                            EmpresaUtil.showError(
                                    "Erro ao excluir forma de pagamento"
                                    ,dataException
                            );
                        }
                    }
                });
    }

    @Override
    public void grdFormasDePagamentoexcluirFormaDePagamento(final Event<Object> event) {
        this.excluirFormaDePagamentoGrdFormasDePagamento();
    }

    @Override
    public void btnIncluirDescontoPorLetraClick(final Event<Object> event) {
        try {
            FrmAdicionarDescLetraClienteA frmAdicionarDescLetraClienteA = new FrmAdicionarDescLetraClienteA();
            FormUtil.doShow(
                    frmAdicionarDescLetraClienteA
                    ,t -> {
                        String codigoDaLetraDeDesconto = frmAdicionarDescLetraClienteA.getCodigoDaLetraDeDesconto();
                        double descontoParaOCliente = frmAdicionarDescLetraClienteA.getDescontoParaOCliente();
                        if ((codigoDaLetraDeDesconto != null)
                                && (descontoParaOCliente != 0.0)) {
                            this.rn.incluirOuAlterarLetraDeDescontoParaOCliente(
                                    codigoDaLetraDeDesconto
                                    ,descontoParaOCliente
                                    ,this.codCliente
                            );
                            this.rn.carregarGrdDescontoLetra(
                                    this.codCliente
                            );
                        }
                    });
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Falha ao incluir letra"
                    ,exception
            );
        }
    }

    private void alterarDescontoPorLetraGrdDescontoLetra() {
        try {
            String letra = this.tbClientesDescontosFlag.getLETRA().asString();
            double perDesc = this.tbClientesDescontosFlag.getPER_DESC().asDecimal();
            FrmAdicionarDescLetraClienteA frmAdicionarDescLetraClienteA = new FrmAdicionarDescLetraClienteA();
            frmAdicionarDescLetraClienteA.setCodigoDaLetraDeDesconto(letra);
            frmAdicionarDescLetraClienteA.setDescontoParaOCliente(perDesc);
            FormUtil.doShow(
                    frmAdicionarDescLetraClienteA
                    ,t -> {
                        boolean frmAdicionarDescLetraClienteAFechadoAoSalvar = frmAdicionarDescLetraClienteA.isFechadoAoSalvar();
                        if (frmAdicionarDescLetraClienteAFechadoAoSalvar) {
                            String codigoDaLetraDeDesconto = frmAdicionarDescLetraClienteA.getCodigoDaLetraDeDesconto();
                            double descontoParaOCliente = frmAdicionarDescLetraClienteA.getDescontoParaOCliente();
                            if ((codigoDaLetraDeDesconto != null)
                                    && (descontoParaOCliente != 0.0)) {
                                this.rn.incluirOuAlterarLetraDeDescontoParaOCliente(
                                        codigoDaLetraDeDesconto
                                        ,descontoParaOCliente
                                        ,this.codCliente
                                );
                                this.rn.carregarGrdDescontoLetra(
                                        this.codCliente
                                );
                            }
                        }
                    });
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Falha ao editar letra"
                    ,exception
            );
        }
    }

    @Override
    public void grdDescontoLetraalterarDescontoPorLetra(Event<Object> event) {
        this.alterarDescontoPorLetraGrdDescontoLetra();
    }

    @Override
    public void grdDescontoLetraexcluirDescontoPorLetra(Event<Object> event) {
        Dialog.create()
                .title(Constantes.CONFIRMACAO)
                .message(Constantes.DESEJA_REALMENTE_EXCLUIR)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            this.rn.excluirDescontoPorLetra();
                        } catch (
                                DataException dataException
                        ) {
                            EmpresaUtil.showError(
                                    "Erro ao excluir desconto por letra"
                                    ,dataException
                            );
                        }
                    }
                });
    }

    @Override
    public void btnIncluirDescontoPorLetraEmpresaClick(final Event<Object> event) {
        try {
            FrmAdicionarDescLetraCliEmpA frmAdicionarDescLetraCliEmpA = new FrmAdicionarDescLetraCliEmpA();
            FormUtil.doShow(frmAdicionarDescLetraCliEmpA,
                    t -> {
                        String codigoDaLetraDeDesconto = frmAdicionarDescLetraCliEmpA.getCodigoDaLetraDeDesconto();
                        double descontoParaOCliente = frmAdicionarDescLetraCliEmpA.getDescontoParaOCliente();
                        long codigoEmpresaLetraDesconto = frmAdicionarDescLetraCliEmpA.getCodigoDaEmpresaLetraDeDesconto();
                        if ((codigoDaLetraDeDesconto != null)
                                && (descontoParaOCliente != 0.0)
                                && (codigoEmpresaLetraDesconto != 0.0)) {
                            this.rn.incluirOuAlterarLetraDeDescontoParaOClientePorEmpresa(
                                    codigoDaLetraDeDesconto
                                    ,descontoParaOCliente
                                    ,codigoEmpresaLetraDesconto
                                    ,this.codCliente
                            );
                            this.rn.carregarGradeEmpresaDescontoPorLetra(
                                    this.codCliente
                            );
                        }
                    });
        } catch (Exception exception) {
            EmpresaUtil.showError("Falha ao incluir letra",
                    exception);
        }
    }

    private void alterarDescontoPorLetraEmpresaGrdDescontoLetraEmpresa() {
        try {
            String letra = this.tbDescEmp.getLETRA().asString();
            double perDesc = this.tbDescEmp.getPER_DESC().asDecimal();
            long codEmpresa = this.tbDescEmp.getCOD_EMPRESA().asLong();
            FrmAdicionarDescLetraCliEmpA frmAdicionarDescLetraCliEmpA = new FrmAdicionarDescLetraCliEmpA();
            frmAdicionarDescLetraCliEmpA.setCodigoDaLetraDeDesconto(
                    letra
            );
            frmAdicionarDescLetraCliEmpA.setDescontoParaOCliente(
                    perDesc
            );
            frmAdicionarDescLetraCliEmpA.setCodigoDaEmpresaLetraDeDesconto(
                    codEmpresa
            );
            FormUtil.doShow(
                    frmAdicionarDescLetraCliEmpA
                    ,t -> {
                        String codigoDaLetraDeDesconto = frmAdicionarDescLetraCliEmpA.getCodigoDaLetraDeDesconto();
                        double descontoParaOCliente = frmAdicionarDescLetraCliEmpA.getDescontoParaOCliente();
                        long codigoDaEmpresaLetraDesconto = frmAdicionarDescLetraCliEmpA.getCodigoDaEmpresaLetraDeDesconto();
                        if ((codigoDaLetraDeDesconto != null)
                                && (descontoParaOCliente != 0.0)
                                && (codigoDaEmpresaLetraDesconto != 0.0)) {
                            this.rn.incluirOuAlterarLetraDeDescontoParaOClientePorEmpresa(
                                    codigoDaLetraDeDesconto
                                    ,descontoParaOCliente
                                    ,codigoDaEmpresaLetraDesconto
                                    ,this.codCliente
                            );
                            this.rn.carregarGradeEmpresaDescontoPorLetra(
                                    this.codCliente
                            );
                        }
                    });
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Falha ao editar letra"
                    ,exception
            );
        }
    }

    @Override
    public void grdDescontoLetraEmpresaalterarDescontoPorLetraEmpresa(Event<Object> event) {
        this.alterarDescontoPorLetraEmpresaGrdDescontoLetraEmpresa();
    }

    @Override
    public void grdDescontoLetraEmpresaexcluirDescontoPorLetraEmpresa(Event<Object> event) {
        Dialog.create()
                .title(Constantes.CONFIRMACAO)
                .message(Constantes.DESEJA_REALMENTE_EXCLUIR)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            this.rn.excluirDescontoPorLetraEmpresa();
                        } catch (
                                DataException dataException
                        ) {
                            EmpresaUtil.showError(
                                    "Erro ao excluir desconto por letra por empresa"
                                    ,dataException
                            );
                        }
                    }
                });
    }

    @Override
    public void btnAddCondicaoPgtoClick(final Event<Object> event) {
        FrmClientesFlagsPgtoA frmClientesFlagsPgtoA = new FrmClientesFlagsPgtoA(
                this.codCliente
        );
        FormUtil.doShow(
                frmClientesFlagsPgtoA
                ,t -> {
                    boolean frmClientesFlagsPgtoAFechadoAoSalvar = frmClientesFlagsPgtoA.isFechadoAoSalvar();
                    if (frmClientesFlagsPgtoAFechadoAoSalvar) {
                        frmClientesFlagsPgtoA.tbClienteFormaPgtoDisp.first();
                        while (Boolean.FALSE.equals(frmClientesFlagsPgtoA.tbClienteFormaPgtoDisp.eof())) {
                            String sel = frmClientesFlagsPgtoA.tbClienteFormaPgtoDisp.getSEL().asString();
                            if (sel.equals("S")) {
                                long codEmpresa = frmClientesFlagsPgtoA.tbClienteFormaPgtoDisp.getCOD_EMPRESA().asLong();
                                long codEmpresaDepartamento = frmClientesFlagsPgtoA.tbClienteFormaPgtoDisp.getCOD_EMPRESA_DEPARTAMENTO().asLong();
                                long codFormaPgto = frmClientesFlagsPgtoA.tbClienteFormaPgtoDisp.getCOD_FORMA_PGTO().asLong();
                                long codCondicaoPgto = frmClientesFlagsPgtoA.tbClienteFormaPgtoDisp.getCOD_CONDICAO_PAGAMENTO().asLong();
                                this.rn.incluirClienteCondicaoDePagamento(
                                        codEmpresa
                                        ,codEmpresaDepartamento
                                        ,codFormaPgto
                                        ,codCondicaoPgto
                                        ,this.codCliente
                                );
                            }
                            frmClientesFlagsPgtoA.tbClienteFormaPgtoDisp.next();
                        }
                        this.carregarGrdCondPgto(
                                this.codCliente
                        );
                    }
                });
    }

    @Override
    public void btnAddFormaPgtoClick(final Event<Object> event) {
        FrmPesquisaFormaPgtoA frmPesquisaFormaPgtoA = new FrmPesquisaFormaPgtoA(
                this.codCliente
        );
        FormUtil.doShow(
                frmPesquisaFormaPgtoA
                ,t -> {
                    this.rn.carregarGrdFormasDePagamento(
                            this.codCliente
                    );
                    this.tabelaDeFormasDePagamentoSelecionadasNaGrade = frmPesquisaFormaPgtoA.getTabelaDeRegistrosSelecionadosNaGrade();
                    boolean tabelaDeFormasDePagamentoSelecionadasNaGradeEmpty = !this.tabelaDeFormasDePagamentoSelecionadasNaGrade.isEmpty();
                    if (tabelaDeFormasDePagamentoSelecionadasNaGradeEmpty) {
                        this.tabelaDeFormasDePagamentoSelecionadasNaGrade.first();
                        while (Boolean.FALSE.equals(this.tabelaDeFormasDePagamentoSelecionadasNaGrade.eof())) {
                            long codFormaPgto = this.tabelaDeFormasDePagamentoSelecionadasNaGrade.getFORMA_CODIGO().asLong();
                            long codEmpresa = this.tabelaDeFormasDePagamentoSelecionadasNaGrade.getEMP_CODIGO().asLong();
                            long codDepartamento = this.tabelaDeFormasDePagamentoSelecionadasNaGrade.getDPTO_CODIGO().asLong();
                            this.rn.incluirClienteFormasPagamento(
                                    codFormaPgto
                                    ,codEmpresa
                                    ,codDepartamento
                                    ,this.codCliente
                            );
                            this.tabelaDeFormasDePagamentoSelecionadasNaGrade.next();
                        }
                        this.rn.carregarGrdFormasDePagamento(
                                this.codCliente
                        );
                    }
                });
    }

    @Override
    public void btnIncluirMargemMinimaMarkupClick(final Event<Object> event) {
        double margemMinima = 0.0;
        boolean tbClientesEspeciaisMargemNotEmpty = !this.tbClientesEspeciaisMargem.isEmpty();
        if (tbClientesEspeciaisMargemNotEmpty) {
            margemMinima = this.tbClientesEspeciaisMargem.getMARGEM_MINIMA().asDecimal();
        }
        FrmAdicMargemMinimaMarkupClienteA frmAdicMargemMinimaMarkupClienteA = new FrmAdicMargemMinimaMarkupClienteA(
                margemMinima
        );
        FormUtil.doShow(
                frmAdicMargemMinimaMarkupClienteA
                ,t -> {
                    double frmAdicMargemMinimaMarkupClienteAMargemMinima = frmAdicMargemMinimaMarkupClienteA.edtMargemMinima.getValue().asDecimal();
                    if (frmAdicMargemMinimaMarkupClienteAMargemMinima > 0.0) {
                        this.rn.incluirOuAlterarMargemMinimaMarkupParaOCliente(
                                frmAdicMargemMinimaMarkupClienteAMargemMinima
                                ,this.codCliente
                        );
                        this.rn.carregarGrdMargemMinimaMarkup(
                                this.codCliente
                        );
                    }
                });
    }

    private void alterarMargemMinimaMarkupGrdMargemMinimaMarkup() {
        try {
            double margemMinima = 0.0;
            boolean tbClientesEspeciaisMargemNotEmpty = !this.tbClientesEspeciaisMargem.isEmpty();
            if (tbClientesEspeciaisMargemNotEmpty) {
                margemMinima = this.tbClientesEspeciaisMargem.getMARGEM_MINIMA().asDecimal();
            }
            FrmAdicMargemMinimaMarkupClienteA frmAdicMargemMinimaMarkupClienteA = new FrmAdicMargemMinimaMarkupClienteA(
                    margemMinima
            );
            FormUtil.doShow(
                    frmAdicMargemMinimaMarkupClienteA
                    ,t -> {
                        double frmAdicMargemMinimaMarkupClienteAMargemMinima = frmAdicMargemMinimaMarkupClienteA.edtMargemMinima.getValue().asDecimal();
                        if (frmAdicMargemMinimaMarkupClienteAMargemMinima > 0.0) {
                            this.rn.incluirOuAlterarMargemMinimaMarkupParaOCliente(
                                    frmAdicMargemMinimaMarkupClienteAMargemMinima
                                    ,this.codCliente
                            );
                            this.rn.carregarGrdMargemMinimaMarkup(
                                    this.codCliente
                            );
                        }
                    });
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Falha ao alterar margem mínima markup"
                    ,exception
            );
        }
    }

    @Override
    public void grdMargemMinimaMarkupalterarMargemMinimaMarkup(Event<Object> event) {
        this.alterarMargemMinimaMarkupGrdMargemMinimaMarkup();
    }

    @Override
    public void grdMargemMinimaMarkupexcluirMargemMinimaMarkup(Event<Object> event) {
        Dialog.create()
                .title(Constantes.CONFIRMACAO)
                .message(Constantes.DESEJA_REALMENTE_EXCLUIR)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        try {
                            this.rn.excluirMargemMinimaMarkup();
                            boolean tbClientesEspeciaisMargemEmpty = this.tbClientesEspeciaisMargem.isEmpty();
                            this.btnIncluirMargemMinimaMarkup.setEnabled(tbClientesEspeciaisMargemEmpty);
                        } catch (DataException dataException) {
                            EmpresaUtil.showError("Erro ao excluir margem mínima markup",
                                    dataException);
                        }
            }
        });
    }

    private void carregarGradeMargemMinimaMarkupEmpresa(
            long codCliente
    ) {
        try {
            this.rn.carregarGradeMargemMinimaMarkupEmpresa(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar grade Margem Mínima Markup/Empresa"
                    ,dataException
            );
        }
    }

    @Override
    public void btnIncluirMargemMinimaMarkupEmpresaClick(Event<Object> event) {
        FrmAdicMargMinMarkupCliEmpA frmAdicMargMinMarkupCliEmpA = new FrmAdicMargMinMarkupCliEmpA(
                this.codCliente
                ,0.0
                ,0.0
        );
        FormUtil.doShow(
                frmAdicMargMinMarkupCliEmpA
                ,t -> {
                    boolean fechadoAoClicarEmSalvar = frmAdicMargMinMarkupCliEmpA.isFechadoAoClicarEmSalvar();
                    if (fechadoAoClicarEmSalvar) {
                        long codEmpresa = frmAdicMargMinMarkupCliEmpA.cboEmpresa.getValue().asLong();
                        this.carregarGradeMargemMinimaMarkupEmpresa(
                                this.codCliente
                        );
                        this.selecionarRegistroGradeMargemMinimaMarkupEmpresa(
                                this.codCliente
                                ,codEmpresa
                        );
                    }
                });
    }

    private boolean excluirMargemMinimaMarkupEmpresa(
            long codCliente
            ,long codEmpresa
    ) {
        boolean retFuncao = false;
        try {
            retFuncao = this.rn.excluirMargemMinimaMarkupEmpresa(
                    codCliente
                    ,codEmpresa
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao excluir margem mínima markup/empresa"
                    ,dataException
            );
        }
        return retFuncao;
    }

    private void selecionarRegistroGradeMargemMinimaMarkupEmpresa(long codCliente,
                                                                  long codEmpresa) {
        try {
            this.rn.selecionarRegistroGradeMargemMinimaMarkupEmpresa(codCliente,
                    codEmpresa);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao selecionar registro na grade Margem Mínima Markup/Empresa",
                    dataException);
        }
    }

    private void alterarMargemMinimaMarkupEmpresaGrdMargemMinimaMarkupEmpresa() {
        long codEmpresa = this.tbCliEspMargEmp.getCOD_EMPRESA().asLong();
        double margemMinima = this.tbCliEspMargEmp.getMARGEM_MINIMA().asDecimal();
        FrmAdicMargMinMarkupCliEmpA frmAdicMargMinMarkupCliEmpA = new FrmAdicMargMinMarkupCliEmpA(
                this.codCliente
                ,codEmpresa
                ,margemMinima
        );
        FormUtil.doShow(
                frmAdicMargMinMarkupCliEmpA
                ,t -> {
                    boolean fechadoAoClicarEmSalvar = frmAdicMargMinMarkupCliEmpA.isFechadoAoClicarEmSalvar();
                    if (fechadoAoClicarEmSalvar) {
                        this.carregarGradeMargemMinimaMarkupEmpresa(
                                this.codCliente
                        );
                        this.selecionarRegistroGradeMargemMinimaMarkupEmpresa(
                                this.codCliente
                                ,codEmpresa
                        );
                    }
                });
    }


    @Override
    public void grdMargemMinimaMarkupEmpresaalterarMargemMinimaMarkupEmpresa(Event<Object> event) {
        this.alterarMargemMinimaMarkupEmpresaGrdMargemMinimaMarkupEmpresa();
    }

    @Override
    public void grdMargemMinimaMarkupEmpresaexcluirMargemMinimaMarkupEmpresa(Event<Object> event) {
        Dialog.create()
                .title(Constantes.CONFIRMACAO)
                .message(Constantes.DESEJA_REALMENTE_EXCLUIR)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        long codCliente2 = this.tbCliEspMargEmp.getCOD_CLIENTE().asLong();
                        long codEmpresa = this.tbCliEspMargEmp.getCOD_EMPRESA().asLong();
                        boolean margemMinimaMarkupEmpresaExcluidaComSucesso = this.excluirMargemMinimaMarkupEmpresa(
                                codCliente2
                                ,codEmpresa
                        );
                        if (margemMinimaMarkupEmpresaExcluidaComSucesso) {
                            this.carregarGradeMargemMinimaMarkupEmpresa(
                                    this.codCliente
                            );
                        }
                    }
                });
    }

    private void incluirTransportadoraCliente(long codTransportadora,
                                              long codCliente) {
        try {
            this.rn.incluirTransportadoraCliente(codTransportadora,
                    codCliente);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao incluir transportadora para o cliente",
                    dataException);
        }
    }

    @Override
    public void btnIncluirTransportadoraClick(Event<Object> event) {
        FrmPesquisaTransportadoraA frmPesquisaTransportadoraA = new FrmPesquisaTransportadoraA();
        FormUtil.doShow(
                frmPesquisaTransportadoraA
                ,t -> {
                    boolean frmPesquisaTransportadoraAFechadaAoAceitar = frmPesquisaTransportadoraA.isFechadaAoAceitar();
                    if (frmPesquisaTransportadoraAFechadaAoAceitar) {
                        long codTransportadora = frmPesquisaTransportadoraA.tbTransportadoras.getCOD_TRANSPORTADORA().asLong();
                        this.incluirTransportadoraCliente(
                                codTransportadora
                                ,this.codCliente
                        );
                        this.carregarGradeTransportadora(
                                this.codCliente
                        );
                    }
                });
    }

    private void carregarGradeTransportadora(
            long codCliente
    ) {
        try {
            this.rn.carregarGradeTransportadora(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar grade transportadora"
                    ,dataException
            );
        }
    }

    @Override
    public void pgFlagsChange(Event<Object> event) {
        if (this.pgFlags.getSelectedTab().equals(this.tabVenda)) {
            this.carregarGradeTransportadora(
                    this.codCliente
            );
        }
    }

    private void excluirTransportadoraCliente(
            long codTransportadora
            ,long codCliente
    ) {
        try {
            this.rn.excluirTransportadoraCliente(
                    codTransportadora
                    ,codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao excluir transportadora do cliente"
                    ,dataException
            );
        }
    }

    @Override
    public void grdClienteTranspexcluirTransportadora(Event<Object> event) {
        Dialog.create()
                .title(Constantes.CONFIRMACAO)
                .message(Constantes.DESEJA_REALMENTE_EXCLUIR)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        long codTransportadora = this.tbClienteTransportadoraFlag.getCOD_TRANSPORTADORA().asLong();
                        this.excluirTransportadoraCliente(
                                codTransportadora
                                ,this.codCliente
                        );
                        this.carregarGradeTransportadora(
                                this.codCliente
                        );
                    }
                });
    }

    @Override
    public void btnVincularVendedorClick(Event<Object> event) {
        boolean retornoValidacaoAcessoK1030 = EmpresaUtil.validarAcesso(
                "K1030"
        );
        if (!retornoValidacaoAcessoK1030) {
            return;
        }
        String curva = this.tbClienteDiversoFlag.getCURVA().asString();
        FrmVendedorResponsavelA frmVendedorResponsavelA = new FrmVendedorResponsavelA(
                this.codCliente
                ,curva
        );
        FormUtil.doShow(
                frmVendedorResponsavelA
                ,t-> this.carregarGrdVendedoresAssociados(
                        this.codCliente
                        ,this.codigosDasEmpresasDoUsuario
                ));
    }

    private String getCodigosDasEmpresasDoUsuario(
            String loginUsuario
    ) {
        String retFuncao = "";
        try {
            long codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();
            retFuncao = this.rn.getCodigosDasEmpresasDoUsuario(
                    loginUsuario
                    ,codEmpresaUsuarioLogado
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao obter os códigos das empresas do usuário"
                    ,dataException
            );
        }
        return retFuncao;
    }

    private void carregarGrdVendedoresAssociados(
            long codCliente
            ,String codigosDasEmpresasDoUsuario
    ) {
        try {
            this.rn.carregarGrdVendedoresAssociados(
                    codCliente
                    ,codigosDasEmpresasDoUsuario
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar os vendedores"
                    ,dataException
            );
        }
    }

    @Override
    public void cboFormaCobClearClick(Event<Object> event) {
        try {
            boolean tbClienteDiversoFlagNotEmpty = !this.tbClienteDiversoFlag.isEmpty();
            if (tbClienteDiversoFlagNotEmpty) {
                this.tbClienteDiversoFlag.edit();
                long codEmpresa = this.tbFormaCobrancaFlag.getCOD_EMPRESA().asLong();
                this.tbClienteDiversoFlag.setCOD_EMPRESA_FC(
                        codEmpresa
                );
                this.tbClienteDiversoFlag.setCOD_FORMA_COBRANCA(
                        null
                );
                this.salvarClienteFlag();
            }
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    Constantes.FALHA_AO_DEFINIR_FORMA_DE_COBRANCA
                    ,exception
            );
        }
    }

    private void definirPropriedadeEnabledDosCampos() {
        boolean ehCRMParts = EmpresaUtil.isCrmParts();
        if (ehCRMParts) {
            //region Guia Desconto
            boolean possuiAcessoK0244;
            try {
                possuiAcessoK0244 = this.pkgCrmPartsRNA.validarAcesso(
                        this.usuarioLogado
                        ,"K0244"
                ).equals("S");
            } catch (
                    DataException dataException
            ) {
                possuiAcessoK0244 = false;
            }
            this.cboSegmento.setEnabled(
                    possuiAcessoK0244
            );
            this.cboSegmento.setHint(
                    this.cboSegmento.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            //region Desconto por letra
            this.btnIncluirDescontoPorLetra.setEnabled(
                    possuiAcessoK0244
            );
            this.btnIncluirDescontoPorLetra.setHint(
                    this.btnIncluirDescontoPorLetra.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            this.grdDescontoLetra.setEnabled(
                    possuiAcessoK0244
            );
            this.grdDescontoLetra.getColumns().get(0).getImages().get(0).setHint(
                    this.grdDescontoLetra.getColumns().get(0).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            this.grdDescontoLetra.getColumns().get(1).getImages().get(0).setHint(
                    this.grdDescontoLetra.getColumns().get(1).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            //endregion
            //region Margem mínima markup
            boolean tbClientesEspeciaisMargemEmpty = this.tbClientesEspeciaisMargem.isEmpty();
            this.btnIncluirMargemMinimaMarkup.setEnabled(
                    tbClientesEspeciaisMargemEmpty
                            && possuiAcessoK0244
            );
            this.btnIncluirMargemMinimaMarkup.setHint(
                    this.btnIncluirMargemMinimaMarkup.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            this.grdMargemMinimaMarkup.setEnabled(
                    possuiAcessoK0244
            );
            this.grdMargemMinimaMarkup.getColumns().get(0).getImages().get(0).setHint(
                    this.grdMargemMinimaMarkup.getColumns().get(0).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            this.grdMargemMinimaMarkup.getColumns().get(1).getImages().get(0).setHint(
                    this.grdMargemMinimaMarkup.getColumns().get(1).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            //endregion
            //region Desconto por letra/empresa
            this.btnIncluirDescontoPorLetraEmpresa.setEnabled(
                    possuiAcessoK0244
            );
            this.btnIncluirDescontoPorLetraEmpresa.setHint(
                    this.btnIncluirDescontoPorLetraEmpresa.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            this.grdDescontoLetraEmpresa.setEnabled(
                    possuiAcessoK0244
            );
            this.grdDescontoLetraEmpresa.getColumns().get(0).getImages().get(0).setHint(
                    this.grdDescontoLetraEmpresa.getColumns().get(0).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            this.grdDescontoLetraEmpresa.getColumns().get(1).getImages().get(0).setHint(
                    this.grdDescontoLetraEmpresa.getColumns().get(1).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            //endregion
            //region Margem mínima markup/empresa
            this.btnIncluirMargemMinimaMarkupEmpresa.setEnabled(
                    possuiAcessoK0244
            );
            this.btnIncluirMargemMinimaMarkupEmpresa.setHint(
                    this.btnIncluirMargemMinimaMarkupEmpresa.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            this.grdMargemMinimaMarkupEmpresa.setEnabled(
                    possuiAcessoK0244
            );
            this.grdMargemMinimaMarkupEmpresa.getColumns().get(0).getImages().get(0).setHint(
                    this.grdMargemMinimaMarkupEmpresa.getColumns().get(0).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            this.grdMargemMinimaMarkupEmpresa.getColumns().get(1).getImages().get(0).setHint(
                    this.grdMargemMinimaMarkupEmpresa.getColumns().get(1).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0244"
                            + "\""
            );
            //endregion
            //endregion
            //region Guia Venda
            boolean possuiAcessoK0236 = EmpresaUtil.validarAcesso(
                    "K0236"
                    ,false
            );
            //region Guia Negociação
            this.chkUsaPrecoFabrica.setEnabled(
                    possuiAcessoK0236
            );
            this.chkUsaPrecoFabrica.setHint(
                    this.chkUsaPrecoFabrica.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            this.chkUsaPrecoGarantia.setEnabled(
                    possuiAcessoK0236
            );
            this.chkUsaPrecoGarantia.setHint(
                    this.chkUsaPrecoGarantia.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            this.chkAprovaAuto.setEnabled(
                    possuiAcessoK0236
            );
            this.chkAprovaAuto.setHint(
                    this.chkAprovaAuto.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            this.chkReservaAuto.setEnabled(
                    possuiAcessoK0236
            );
            this.chkReservaAuto.setHint(
                    this.chkReservaAuto.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            this.chkAtacadista.setEnabled(
                    possuiAcessoK0236
            );
            this.chkAtacadista.setHint(
                    this.chkAtacadista.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            this.intTempoReserva.setEnabled(
                    possuiAcessoK0236
            );
            this.intTempoReserva.setHint(
                    this.intTempoReserva.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            //endregion
            //region Guia Vendedor
            this.cboRepresentante.setEnabled(
                    possuiAcessoK0236
            );
            this.cboRepresentante.setHint(
                    this.cboRepresentante.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            this.cboVendedor.setEnabled(
                    possuiAcessoK0236
            );
            this.cboVendedor.setHint(
                    this.cboVendedor.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            this.btnVincularVendedor.setEnabled(
                    possuiAcessoK0236
            );
            this.btnVincularVendedor.setHint(
                this.btnVincularVendedor.getHint()
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "Acesso \""
                        + "K0236"
                        + "\""
            );
            //endregion
            //region Guia Transportadora
            this.btnIncluirTransportadora.setEnabled(
                    possuiAcessoK0236
            );
            this.btnIncluirTransportadora.setHint(
                    this.btnIncluirTransportadora.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            this.grdClienteTransp.setEnabled(
                    possuiAcessoK0236
            );
            this.grdClienteTransp.getColumns().get(0).getImages().get(0).setHint(
                    this.grdClienteTransp.getColumns().get(0).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0236"
                            + "\""
            );
            //endregion
            //endregion
            //region Guia Financeiro
            boolean possuiAcessoK0245;
            try {
                possuiAcessoK0245 = this.pkgCrmPartsRNA.validarAcesso(
                        this.usuarioLogado
                        ,"K0245"
                ).equals("S");
            } catch (
                    DataException dataException
            ) {
                possuiAcessoK0245 = false;
            }
            //region Guia Forma/Condição de pagamento
            this.cboFormaCob.setEnabled(
                    possuiAcessoK0245
            );
            this.cboFormaCob.setHint(
                    this.cboFormaCob.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0245"
                            + "\""
            );
            //region Formas de pagamento
            this.btnAddFormaPgto.setEnabled(
                    possuiAcessoK0245
            );
            this.btnAddFormaPgto.setHint(
                    this.btnAddFormaPgto.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0245"
                            + "\""
            );
            this.grdFormasDePagamento.setEnabled(
                    possuiAcessoK0245
            );
            this.grdFormasDePagamento.getColumns().get(0).getImages().get(1).setHint(
                    this.grdFormasDePagamento.getColumns().get(0).getImages().get(1).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0245"
                            + "\""
            );
            //endregion
            //region Condições de pagamento
            this.btnAddCondicaoPgto.setEnabled(
                    possuiAcessoK0245
            );
            this.btnAddCondicaoPgto.setHint(
                    this.btnAddCondicaoPgto.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0245"
                            + "\""
            );
            this.grdCondPgto.setEnabled(
                    possuiAcessoK0245
            );
            this.grdCondPgto.getColumns().get(0).getImages().get(1).setHint(
                    this.grdCondPgto.getColumns().get(0).getImages().get(1).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0245"
                            + "\""
            );
            //endregion
            //endregion
            //region Guia Faturamento livre
            this.chkFatLivre.setEnabled(
                    possuiAcessoK0245
            );
            this.chkFatLivre.setHint(
                    this.chkFatLivre.getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0245"
                            + "\""
            );
            this.grdClienteFatLivre.setEnabled(
                    possuiAcessoK0245
            );
            this.grdClienteFatLivre.getColumns().get(3).getImages().get(0).setHint(
                    this.grdClienteFatLivre.getColumns().get(3).getImages().get(0).getHint()
                            + System.lineSeparator()
                            + System.lineSeparator()
                            + "Acesso \""
                            + "K0245"
                            + "\""
            );
            //endregion
            //endregion
        }
    }

    private void carregarCboFormaCob(
            String usuarioLogado
    ) {
        try {
            this.rn.carregarCboFormaCob(
                    usuarioLogado
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar as formas de cobrança"
                    ,dataException
            );
        }
    }

    private void carregarCboVendedor() {
        try {
            this.rn.carregarCboVendedor();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar os vendedores associados ao cliente"
                    ,dataException
            );
        }
    }

    private void carregarCboRepresentante() {
        try {
            this.rn.carregarCboRepresentante();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar as formas de cobrança"
                    ,dataException
            );
        }
    }

    private void carregarLblNomeCliente(
            long codCliente
    ) {
        try {
            this.rn.carregarLblNomeCliente(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar o nome do cliente"
                    ,dataException
            );
        }
    }

    private void carregarGrdClienteFatLivre(
            long codCliente
    ) {
        try {
            this.rn.carregarGrdClienteFatLivre(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de faturamento livre"
                    ,dataException
            );
        }
    }

    private void carregarGrdCondPgto(
            long codCliente
    ) {
        try {
            this.rn.carregarGrdCondPgto(
                    codCliente
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de condições de pagamento"
                    ,dataException
            );
        }
    }

    @Override
    public void grdDescontoLetraDoubleClick(Event<Object> event) {
       this.alterarDescontoPorLetraGrdDescontoLetra();
    }

    @Override
    public void grdMargemMinimaMarkupDoubleClick(Event<Object> event) {
       boolean grdMargemMinimaMarkupEnabled = this.grdMargemMinimaMarkup.isEnabled();
       if (grdMargemMinimaMarkupEnabled) {
           boolean tbClientesEspeciaisMargemNotEmpty = !this.tbClientesEspeciaisMargem.isEmpty();
           if (tbClientesEspeciaisMargemNotEmpty) {
               this.alterarMargemMinimaMarkupGrdMargemMinimaMarkup();
           }
       }
    }

    @Override
    public void grdDescontoLetraEmpresaDoubleClick(Event<Object> event) {
        boolean grdDescontoLetraEmpresaEnabled = this.grdDescontoLetraEmpresa.isEnabled();
        if (grdDescontoLetraEmpresaEnabled) {
            boolean tbDescEmpNotEmpty = !this.tbDescEmp.isEmpty();
            if (tbDescEmpNotEmpty) {
                this.alterarDescontoPorLetraEmpresaGrdDescontoLetraEmpresa();
            }
        }
    }

    @Override
    public void grdMargemMinimaMarkupEmpresaDoubleClick(Event<Object> event) {
        boolean grdMargemMinimaMarkupEmpresaEnabled = this.grdMargemMinimaMarkupEmpresa.isEnabled();
        if (grdMargemMinimaMarkupEmpresaEnabled) {
            boolean tbCliEspMargEmpNotEmpty = !this.tbCliEspMargEmp.isEmpty();
            if (tbCliEspMargEmpNotEmpty) {
                this.alterarMargemMinimaMarkupEmpresaGrdMargemMinimaMarkupEmpresa();
            }
        }
    }

    @Override
    public void grdFormasDePagamentoDoubleClick(Event<Object> event) {
        boolean grdFormasDePagamentoEnabled = this.grdFormasDePagamento.isEnabled();
        if (grdFormasDePagamentoEnabled) {
            boolean tbListaClienteFormasPagamentoNotEmpty = !this.tbListaClienteFormasPagamento.isEmpty();
            if (tbListaClienteFormasPagamentoNotEmpty) {
                this.excluirFormaDePagamentoGrdFormasDePagamento();
            }
        }
    }

    @Override
    public void grdCondPgtoDoubleClick(Event<Object> event) {
        boolean grdCondPgtoEnabled = this.grdCondPgto.isEnabled();
        if (grdCondPgtoEnabled) {
            boolean tbClienteFormaPgtoFlagNotEmpty = !this.tbClienteFormaPgtoFlag.isEmpty();
            if (tbClienteFormaPgtoFlagNotEmpty) {
                this.excluirCondicaoPagamentoGrdCondPgto();
            }
        }
    }

    private void alterarSegmento(
            long codSegmento
    ) {
        try {
            this.rn.alterarSegmento(
                    codSegmento
            );
        } catch (
                Exception exception
        ) {
            EmpresaUtil.showError(
                    "Erro ao alterar o segmento"
                    ,exception
            );
        }
    }

    private void alterarSegmento() {
        long codSegmento = this.cboSegmento.getValue().asLong();
        this.alterarSegmento(
                codSegmento
        );
    }

    @Override
    public void cboSegmentoClearClick(Event<Object> event) {
        this.alterarSegmento();
    }

    @Override
    public void cboSegmentoChange(Event<Object> event) {
        this.alterarSegmento();
    }

}