package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmTemplatesW;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.CastUtil;
import freedom.util.EmpresaUtil;
import org.zkoss.zul.Textbox;

public class FrmTemplatesA extends FrmTemplatesW {

    private static final long serialVersionUID = 20130827081850L;

    public FrmTemplatesA() {
        super();
        edCatTag.setValue("G");
        filtrarModeloTag();
        edTpArquivoAnexoMensagem44002.setListOptions("Proposta de Veículo=PV;Orçamento de Peças=OP;Orçamento de Veículo=OV;Ordem de Serviço=OS");
//        gridPrincipal.getColumns().get(7).setListOptions("Proposta de Veículo=PV;Orçamento de Peças=OP;Orçamento de Veículo=OV;Ordem de Serviço=OS");
//        gridPrincipal.getColumns().get(7).setHint("");
        try {
            tbEmailModeloTag.setOrderBy("TAG");
            tbEmailModeloTag.open();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void FrmTemplateskeyActionAceitar(final Event<Object> event) {
        super.FrmTemplateskeyActionAceitar(event);
    }

    @Override
    public void FrmTemplateskeyActionAlterar(final Event<Object> event) {
        super.FrmTemplateskeyActionAlterar(event);
    }

    @Override
    public void FrmTemplateskeyActionAnterior(final Event<Object> event) {
        super.FrmTemplateskeyActionAnterior(event);
    }

    @Override
    public void FrmTemplateskeyActionCancelar(final Event<Object> event) {
        super.FrmTemplateskeyActionCancelar(event);
    }

    @Override
    public void FrmTemplateskeyActionExcluir(final Event<Object> event) {
        super.FrmTemplateskeyActionExcluir(event);
    }

    @Override
    public void FrmTemplateskeyActionIncluir(final Event<Object> event) {
        super.FrmTemplateskeyActionIncluir(event);
    }

    @Override
    public void FrmTemplateskeyActionPesquisar(final Event<Object> event) {
        super.FrmTemplateskeyActionPesquisar(event);
    }

    @Override
    public void FrmTemplateskeyActionProximo(final Event<Object> event) {
        super.FrmTemplateskeyActionProximo(event);
    }

    @Override
    public void FrmTemplateskeyActionSalvar(final Event<Object> event) {
        super.FrmTemplateskeyActionSalvar(event);
    }

    @Override
    public void FrmTemplateskeyActionSalvarContinuar(final Event<Object> event) {
        super.FrmTemplateskeyActionSalvarContinuar(event);
    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        super.btnAceitarClick(event);
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {

        if (tbEmailModelo.getSTATUS().asString().toUpperCase().equals("APROVADO")) {
            EmpresaUtil.showWarning("Atenção", "Atenção cuidado ao alterar esse template, "
                    + "pois o mesmo está com status de APROVADO na Zenvia. "
                    + "Caso queira alterar o texto, clique no botão \"Template Zenvia\" "
                    + "para excluir o template. Após excluir o template altere seu "
                    + "texto e clique novamente no botão o \"Template Zenvia\", "
                    + "para solicitar a aprovação");
        }

        super.btnAlterarClick(event);
    }

    @Override
    public void btnAnteriorClick(final Event<Object> event) {
        super.btnAnteriorClick(event);
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        super.btnCancelarClick(event);
    }

    @Override
    public void btnConsultarClick(final Event<Object> event) {
        super.btnConsultarClick(event);
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        super.btnExcluirClick(event);
    }

    @Override
    public void btnFiltroAvancadoClick(final Event<Object> event) {
        super.btnFiltroAvancadoClick(event);
    }

    @Override
    public void btnMaisClick(final Event<Object> event) {
        super.btnMaisClick(event);
    }

    @Override
    public void btnNovoClick(final Event<Object> event) {
        super.btnNovoClick(event);
    }

    @Override
    public void btnProximoClick(final Event<Object> event) {
        super.btnProximoClick(event);
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {

        if ((tbEmailModelo.getMENSAGEM().asString().contains("{")) || (tbEmailModelo.getMENSAGEM().asString().contains("}"))) {
            EmpresaUtil.showWarning("Atenção", "Caracter não permitido: \"{chaves}\". " +
                    "Caso queira utilizar parâmetros utilize entre [colchetes]");
            return;
        }

        super.btnSalvarClick(event);
        pgPrincipal.setSelectedIndex(0);
    }

    @Override
    public void btnSalvarContinuarClick(final Event<Object> event) {
        super.btnSalvarContinuarClick(event);
    }

    @Override
    public void gridPrincipalClickImageAlterar(final Event<Object> event) {
        super.gridPrincipalClickImageAlterar(event);
    }

    @Override
    public void gridPrincipalClickImageDelete(final Event<Object> event) {
        super.gridPrincipalClickImageDelete(event);
    }

    @Override
    public void menuHabilitaNavegacaoClick(final Event<Object> event) {
        super.menuHabilitaNavegacaoClick(event);
    }

    @Override
    public void menuItemAbreTabelaAuxClick(final Event<Object> event) {
        super.menuItemAbreTabelaAuxClick(event);
    }

    @Override
    public void menuItemConfgGridClick(final Event<Object> event) {
        super.menuItemConfgGridClick(event);
    }

    @Override
    public void menuItemExportExcelClick(final Event<Object> event) {
        super.menuItemExportExcelClick(event);
    }

    @Override
    public void menuItemExportPdfClick(final Event<Object> event) {
        super.menuItemExportPdfClick(event);
    }

    @Override
    public void menuItemHelpClick(final Event<Object> event) {
        super.menuItemHelpClick(event);
    }

    @Override
    public void menuSelecaoMultiplaClick(final Event<Object> event) {
        super.menuSelecaoMultiplaClick(event);
    }

    @Override
    public void tbEmailModeloAfterScroll(final Event<Object> event) {
        super.tbEmailModeloAfterScroll(event);
    }

    @Override
    public void edCatTagChange(Event<Object> event) {
        filtrarModeloTag();
    }

    private void filtrarModeloTag() {
        try {
            tbEmailModeloTag.close();
            tbEmailModeloTag.clearFilters();
            if (!edCatTag.getValue().asString().equals("T")) {
                tbEmailModeloTag.addFilter("CATEGORIA");
                tbEmailModeloTag.addParam("CATEGORIA", edCatTag.getValue().asString());
            }
            tbEmailModeloTag.setOrderBy("TAG");
            tbEmailModeloTag.open();
        } catch (DataException ex) {
            EmpresaUtil.showError("Erro", ex);
        }
    }

    @Override
    public void FGrid1DoubleClick(final Event event) {
        if (!FGrid1.isEnabled()) {
            return;
        }


        Textbox texto = (Textbox) edMensagem.getImpl();
        texto.setInsertedText(tbEmailModeloTag.getTAG().asString().replace("<", "").replace(">", "").replace(" ", "_").replace(
                "-", "_"));

        //caso não precise do </p> no final
        edMensagem.setValue(texto.getValue().toString());

//        //caso precise do </p> no final
//        FreedomUtilities.invokeLater(() -> {
//            String novoTexto = texto.getValue().replace("</p>", "") + "</p>";
//            edMensagem.setValue(novoTexto);
//        });

        edMensagem.setFocus();
    }

    @Override
    public void tbEmailModeloTagAfterOpen(Event<Object> event) {

    }

    @Override
    protected void habilitaComp(Boolean enabled) {
        super.habilitaComp(enabled);
        edMensagem.setReadOnly(!enabled);
        FGrid1.setEnabled(enabled);
        edCatTag.setEnabled(enabled);
        btnEnviarAlterarTemplate.setEnabled(!enabled && !tbEmailModelo.isEmpty());

        edIdTemplate44002.setEnabled(enabled && tbEmailModelo.getID_NBSAPI_MESSAGE_TEMPLATE().asInteger() == 0);
    }

    @Override
    public void btnEnviarAlterarTemplateClick(final Event event) {

        String tipoEnvio;
        String msg;

        if (tbEmailModelo.getID_NBSAPI_MESSAGE_TEMPLATE().asInteger() == 0) {
            tipoEnvio = "S"; //Send
            msg = "Confirma enviar template para Zenvia?";
        } else {
            msg = "Confirma excluir o template enviado para Zenvia?";
            tipoEnvio = "E"; //Excluir
        }

        if (tbEmailModelo.getSTATUS().asString().toUpperCase().equals("PENDENTE")) {
            EmpresaUtil.showWarning("Atenção", "Ação não pode ser executada com status pendente. Aguarde resposta da Zenvia.");
            return;
        }

        Dialog.create()
                .title("Confirma")
                .message(msg)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {

                        try {
                            Value outMensagem = new Value(null);
                            String nomeTemplate = tbEmailModelo.getMODELO().asString();
                            String template = tbEmailModelo.getMENSAGEM().asString();

                            FrmTemplatesCelularAprovarA celularAprovar = new FrmTemplatesCelularAprovarA();
                            if (tipoEnvio.equals("E")) {
                                celularAprovar.setCaption("Informe o celular que aprovou o template");
                                String cel = tbEmailModelo.getCELULAR_APROVOU_TEMPLATE().asString().trim();
                                if (!cel.equals("")) {
                                    if (cel.contains("+")) {
                                        cel = cel.replace("+55", "");
                                    } else {
                                        if (cel.substring(0, 2).equals("55")) {
                                            cel = cel.substring(2, cel.length());
                                        }
                                    }
                                    celularAprovar.tbCelularAtivosDisparos.locate("CELULAR", cel);
                                }
                            } else if (tipoEnvio.equals("S")) {
                                celularAprovar.setCaption("Celular aprovação template");
                            }
                            FormUtil.doShow(celularAprovar, (EventListener) t -> {
                                if (celularAprovar.isOk()) {
                                    int idCelular = celularAprovar.getIdCelular();
                                    if (tipoEnvio.equals("S")) {
                                        String id = rn.criarTemplateZenvia(idCelular, nomeTemplate, template, outMensagem);
                                        if (id.trim().equals("")) {
                                            EmpresaUtil.showMessage("Atenção", outMensagem.asString());
                                        } else {
                                            tbEmailModelo.edit();
                                            tbEmailModelo.setID_NBSAPI_MESSAGE_TEMPLATE(id);
                                            tbEmailModelo.post();
                                            tbEmailModelo.applyUpdates();
                                            tbEmailModelo.commitUpdates();
                                            tbEmailModelo.refreshRecord();
                                        }
                                    } else if (tipoEnvio.equals("E")) {
                                        //Falta a parte da API.
                                        String ret = rn.excluirTemplateZenvia(idCelular, tbEmailModelo.getID_NBSAPI_MESSAGE_TEMPLATE().asInteger());
                                        if (ret.equals("OK")) {
                                            tbEmailModelo.edit();
                                            tbEmailModelo.setID_NBSAPI_MESSAGE_TEMPLATE(null);
                                            tbEmailModelo.setID_TEMPLATE(null);
                                            tbEmailModelo.setCELULAR_APROVOU_TEMPLATE(null);
                                            tbEmailModelo.post();
                                            tbEmailModelo.applyUpdates();
                                            tbEmailModelo.commitUpdates();
                                            tbEmailModelo.refreshRecord();
                                        } else {
                                            EmpresaUtil.showWarning("Atenção", ret);
                                        }
                                    }
                                }
                            });

                        } catch (Exception ex) {
                            EmpresaUtil.showError("OPS! Ocorreu um erro inesperado!", ex);
                        }

                    }
                });
    }

}
