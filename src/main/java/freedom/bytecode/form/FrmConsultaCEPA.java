package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmConsultaCEPW;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;

public class FrmConsultaCEPA extends FrmConsultaCEPW {

    private static final long serialVersionUID = 20130827081850L;
    private final PkgCrmPartsRNA pkCrmPartsRna = new PkgCrmPartsRNA();
    private int codEmpresa = EmpresaUtil.getCodEmpresaUserLogged();
    private boolean ok = false;

    public boolean isOk() {
        return ok;
    }

    public void setCidadeEnd(String uf, int codCidades) {
        try {
            tbUf.close();
            tbUf.setOrderBy("UF");
            tbUf.open();
            if (uf.length() > 0) {
                tbUf.locate("UF", uf);
            } else {
                Double codEmp = new Double(codEmpresa);
                String ufParam = pkCrmPartsRna.getParametro(codEmp, "PARM_SYS", "UF");
                tbUf.locate("UF", ufParam);
            }
            comboUf.setText(tbUf.getUF().asString());
            pesquisarCidades();
            if (codCidades > 0) {
                tbCidades.locate("COD_CIDADES", codCidades);
                comboCidade.setText(tbCidades.getDESCRICAO().asString());
            }

        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao iniciar consulta cep", e);
        }
    }

    private void pesquisarCidades() {
        try {
            tbCidades.close();
            tbCidades.clearFilters();
            tbCidades.clearParams();
            tbCidades.addFilter("UF");
            tbCidades.addParam("UF", tbUf.getUF());
            tbCidades.setOrderBy("DESCRICAO");
            tbCidades.open();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao pesquisar cidades", e);
        }
    }

    private int getCodCidades() {
        if (comboCidade.getText().trim().length() > 0) {
            return tbCidades.getCOD_CIDADES().asInteger();
        } else {
            return 0;
        }
    }

    private String getNomeTabela() {
        switch (tbUf.getUF().asString()) {
            case "PR":
            case "RS":
            case "SC":
                return "LOG_SUL";
            case "RN":
            case "AL":
            case "MA":
            case "PB":
            case "PE":
            case "BA":
            case "CE":
            case "PI":
                return "LOG_NORDESTE";
            case "GO":
            case "MT":
            case "DF":
            case "MS":
                return "LOG_CENTRO";
            case "RJ":
                return "LOG_RJ";
            case "SP":
                return "LOG_SP_CAPITAL";
            default:
                return "LOG_SUDESTE";
        }
    }

    private void pesquisar() {
        if (edtPesquisa.getValue().asString().trim().length() < 3) {
            EmpresaUtil.showWarning("Atenção", "Informe ao menos 3 caracteres para a pesquisa.");
            return;
        }
        try {
            tbConsultaCep.close();
            tbConsultaCep.clearFilters();
            tbConsultaCep.clearParams();
            tbConsultaCep.addParam("UF", tbUf.getUF());
            tbConsultaCep.addParam("COD_CIDADES", getCodCidades());
            tbConsultaCep.addParam("NOME_TABELA", getNomeTabela());
            tbConsultaCep.addParam("CEP", edtPesquisa.getValue().asString().trim().toUpperCase());
            if (ckLogrQualquerPosicao.isChecked()) {
                tbConsultaCep.addParam("CEP_LIKE", "%" + edtPesquisa.getValue().asString().trim().toUpperCase() + "%");
                tbConsultaCep.addParam("RUA", "%" + edtPesquisa.getValue().asString().trim().toUpperCase() + "%");
            } else {
                tbConsultaCep.addParam("CEP_LIKE", edtPesquisa.getValue().asString().trim().toUpperCase() + "%");
                tbConsultaCep.addParam("RUA", edtPesquisa.getValue().asString().trim().toUpperCase() + "%");
            }
            tbConsultaCep.open();
        } catch (DataException e) {
            EmpresaUtil.showError("Falha ao pesquisar", e);
        }
    }

    @Override
    public void btnPesquisarClick(final Event event) {
        pesquisar();
    }

    @Override
    public void btnAceitarClick(final Event event) {
        if (tbConsultaCep.isEmpty()) {
            EmpresaUtil.showWarning("Atenção", "Nenhum Logradouro Selecionado!");
            return;
        }
        ok = true;
        close();
    }

    @Override
    public void btnVoltarClick(final Event event) {
        close();
    }

    @Override
    public void tbUfAfterScroll(Event<Object> event) {
        pesquisarCidades();
    }

    @Override
    public void edtPesquisaEnter(Event<Object> event) {
        pesquisar();
    }
}
