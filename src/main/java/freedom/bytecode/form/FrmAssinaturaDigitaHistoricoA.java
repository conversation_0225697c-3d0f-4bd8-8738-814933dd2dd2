package freedom.bytecode.form;
import freedom.bytecode.form.wizard.*;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalUtils;
import freedom.util.EmpresaUtil;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;
import lombok.Getter;
import lombok.Setter;
import org.json.JSONObject;

@Setter
@Getter
public class FrmAssinaturaDigitaHistoricoA extends FrmAssinaturaDigitaHistoricoW {
    private static final long serialVersionUID = 20130827081850L;
    TipoAssinaturaStrategy tipoAssinatura;
    JSONObject jsonParametros;


    public boolean carregarAssinaturaDigitalHistorico(TipoAssinaturaStrategy tipoAssinatura, JSONObject jsonParametros){
        try {
            setTipoAssinatura(tipoAssinatura);
            setJsonParametros(jsonParametros);
            initForm();
            return true;
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao carregar Histórico assinatura digital", e);
            return false;
        }
    }

    public void initForm() throws DataException {
        rn.filtrarTbAssinaturaDigital(getTipoAssinatura());
        validarCrmAssinaturaDigital();
        rn.filtrarTbNbsapiEnvelopesLog(getTipoAssinatura(), getJsonParametros());
    }


    public void validarCrmAssinaturaDigital() throws DataException {
        if (rn.tbAssinaturaDigitalIsEmpty()) {
            throw new DataException("Assinatura digital não cadastrada em CRM_ASSINATURA_DIGITAL: " + getTipoAssinatura().getTipo());
        }
        String chavesFaltantes = CrmAssinaturaDigitalUtils.validarJsonChavesFaltantesNecessariasAssinaturaDigital(tbAssinaturaDigital.getTIPO_CODIGO().asString(),getJsonParametros());
        if (!chavesFaltantes.isEmpty()) {
            throw new DataException("Parametros pendentes para a assinatura digital: " + getTipoAssinatura().getTipo() + " os seguintes campos devem ser informados nos Parametros Json {" + chavesFaltantes + "}");
        }
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }
}
