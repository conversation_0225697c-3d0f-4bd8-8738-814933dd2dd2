package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.Event;
import freedom.util.DateUtils;
import java.util.Date;

public class FrmRetornarDataHoraA extends FrmRetornarDataHoraW {

    private static final long serialVersionUID = 20130827081850L;
    private Date dataHora;
    private boolean isOk = false;

    public boolean isOk() {
        return isOk;
    }

    public FrmRetornarDataHoraA() {
        timeAgenda.setValue(new Date());
    }

    @Override
    public void btnOkClick(final Event event) {
        Date dateSel = monthCalendarData.getValue().asDate();
        Date hora = timeAgenda.getValue().asDate();
        dataHora = DateUtils.setDateTime(dateSel, hora);
        isOk = true;
        close();
    }

    public Date getDataHora() {
        return dataHora;
    }

}
