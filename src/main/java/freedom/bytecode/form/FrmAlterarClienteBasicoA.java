package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmAlterarClienteBasicoW;
import freedom.client.controls.impl.TFString;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class FrmAlterarClienteBasicoA extends FrmAlterarClienteBasicoW {

    private static final long serialVersionUID = 20130827081850L;
    private final EmpresaUtil util;

    public FrmAlterarClienteBasicoA() {
        util = new EmpresaUtil();
    }

    public void filtrarCliente(long codCliente) {
        try {
            rn.filtrarClientes(codCliente);
            edtFoneCelular.setValue(edtFoneCelular.getValue());
        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao filtrar cliente")
                    .message("[35] " + ex.getMessage())
                    .showError();
        }
    }

    @Override
    public void btnCancelarClick(final Event event) {
        try {
            tbClientes.cancel();
            tbClientes.cancelUpdates();
            close();
        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao cancelar")
                    .message("[38] " + ex.getMessage())
                    .showError();
        }
    }

    @Override
    public void btnSalvarClick(final Event event) {
        try {
            tbClientes.post();
            util.salvar(sc);
            tbClientes.commitUpdates();
            close();
        } catch (DataException ex) {
            Dialog.create()
                    .title("Erro ao Salvar")
                    .message("[39] " + ex.getMessage())
                    .showError();
        }
    }

    private void formartMask(TFString edit) {
        if (edit.getValue().asString().length() >= 9) {
            edit.setMask("99999-999?9");
        } else if (edit.getValue().asString().length() <= 8) {
            edit.setMask("9999-9999?9");
        }
        edit.setValue(edit.getValue());
    }

    @Override
    public void edtFoneCelularChange(Event<Object> event) {
        formartMask(edtFoneCelular);
    }

    @Override
    public void edtFoneCelularExit(Event<Object> event) {
        formartMask(edtFoneCelular);
    }

    @Override
    public void edtFoneComercialChange(Event<Object> event) {
        formartMask(edtFoneComercial);
    }

    @Override
    public void edtFoneComercialExit(Event<Object> event) {
        formartMask(edtFoneComercial);
    }

}
