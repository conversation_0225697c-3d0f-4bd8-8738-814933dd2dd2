package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmCEPClienteOnLineW;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.FormUtil;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import org.json.JSONObject;

public class FrmCEPClienteOnLineA extends FrmCEPClienteOnLineW {

    private static final long serialVersionUID = 20130827081850L;

    // A-Aceitar, M=Manter
    private String statusRes = "M";

    // A-Aceitar, M=Manter
    private String statusCom = "M";

    // A-Aceitar, M=Manter
    private String statusCob = "M";

    private boolean ok = false;

    boolean xDivEndIE;

    boolean xDivOutrosEnd;

    // 68;
    private Integer formHeightAux = 138;

    private Integer formHeight = 788;

    private Value gRetUFViaCEP = new Value(null);

    private Value gRetCodCidadeViaCEP = new Value(null);

    private Value gRetRuaViaCEP = new Value(null);

    private Value gRetBairroViaCEP = new Value(null);

    public boolean isOk() {
        return ok;
    }

    public boolean isDivergencia() {
        if (xDivOutrosEnd) {
            return xDivOutrosEnd;
        }
        return xDivEndIE;
    }

    public String isStatusRes() {
        return statusRes;
    }

    public String isStatusCom() {
        return statusCom;
    }

    public String isStatusCob() {
        return statusCob;
    }

    public FrmCEPClienteOnLineA() {
        hbCEPOnLineAceitarRes.setAttribute("SCLASS_BASE", "hbbuttonleftradius");
        hbCEPOnLineManterRes.setAttribute("SCLASS_BASE", "hbbuttonrightradius");
        hbCEPOnLineAceitarCom.setAttribute("SCLASS_BASE", "hbbuttonleftradius");
        hbCEPOnLineManterCom.setAttribute("SCLASS_BASE", "hbbuttonrightradius");
        hbCEPOnLineAceitarCob.setAttribute("SCLASS_BASE", "hbbuttonleftradius");
        hbCEPOnLineManterCob.setAttribute("SCLASS_BASE", "hbbuttonrightradius");
        hbCEPOnLineAceitarIE.setAttribute("SCLASS_BASE", "hbbuttonleftradius");
        hbCEPOnLineManterIE.setAttribute("SCLASS_BASE", "hbbuttonrightradius");
        hBoxlblEndResidencial.setColor("#e4e7eb");
        hBoxlblEndComercial.setColor("#e4e7eb");
        hBoxlblEndCob.setColor("#e4e7eb");
        hBoxlblEndIE.setColor("#e4e7eb");
        FrmCEPClienteOnLine.setClientHeight(formHeight);
        FrmCEPClienteOnLine.setClientWidth(480);
        btnSalvar.setEnabled(false);
    }

    public boolean getEnderecos(Double codCliente, String strCEP, Integer tpEndereco) {
        boolean ret;
        try {
            rn.filtrarClienteEndereco(codCliente);
            ret = validaCEPOnLine(codCliente, strCEP, tpEndereco);
        } catch (Exception ex) {
            EmpresaUtil.showError("Falha ao Buscar Endereços", ex);
            ret = false;
        }
        return ret;
    }

    public boolean servicoOk(String strCEP, Boolean allEnd, Value oRetMensagem) throws Exception {
        return rn.servicoOk(strCEP, allEnd, oRetMensagem);
    }

    public JSONObject retJsonCepDados() {
        return rn.retJsonCepDados();
    }

    public boolean getEnderecoViaCEPOnLine(String strCEP) {
        boolean ret;
        try {
            ret = rn.getEnderecoViaCEPOnLine(strCEP, gRetUFViaCEP, gRetCodCidadeViaCEP, gRetRuaViaCEP, gRetBairroViaCEP);
        } catch (Exception ex) {
            EmpresaUtil.showError("Falha ao Buscar Endereço", ex);
            ret = false;
        }
        return ret;
    }

    private boolean validaCEPOnLine(Double codCliente, String strCEP, Integer tpEndereco) {
        boolean ret;
        try {
            hBoxTitEndRes.setVisible(false);
            boxEndRes.setVisible(false);
            hBoxTitEndCom.setVisible(false);
            boxEndCom.setVisible(false);
            hBoxTitEndCob.setVisible(false);
            boxEndCob.setVisible(false);
            hBoxTitEndIE.setVisible(false);
            boxEndIE.setVisible(false);
            if (!strCEP.isEmpty()) {
                if (strCEP.trim().length() > 0 && strCEP.trim().length() == 8) {
                    switch (tpEndereco) {
                        case 1:
                            ret = (rn.getDadosCEPOnLine(codCliente, strCEP.trim(), "", tbClientesEndereco.getCOD_CID_RES().asInteger(), 1) && tbClientesEndereco.getCOD_CID_RES().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndRes.setVisible(ret);
                                boxEndRes.setVisible(ret);
                                if (boxEndRes.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(tpEndereco);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndRes.setVisible(xDivOutrosEnd);
                                    boxEndRes.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            break;
                        case 2:
                            ret = (rn.getDadosCEPOnLine(codCliente, strCEP.trim(), "", tbClientesEndereco.getCOD_CID_COM().asInteger(), 2) && tbClientesEndereco.getCOD_CID_COM().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndCom.setVisible(ret);
                                boxEndCom.setVisible(ret);
                                if (boxEndCom.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(tpEndereco);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndCom.setVisible(xDivOutrosEnd);
                                    boxEndCom.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            break;
                        case 3:
                            ret = (rn.getDadosCEPOnLine(codCliente, strCEP.trim(), "", tbClientesEndereco.getCOD_CID_COBRANCA().asInteger(), 3) && tbClientesEndereco.getCOD_CID_COBRANCA().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndCob.setVisible(ret);
                                boxEndCob.setVisible(ret);
                                if (boxEndCob.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(tpEndereco);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndCob.setVisible(xDivOutrosEnd);
                                    boxEndCob.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            break;
                        case 4:
                            if (!tbClientesEnderecoIe.isEmpty()) {
                                tbClientesEnderecoIe.first();
                                while (!tbClientesEnderecoIe.eof()) {
                                    ret = rn.getDadosCEPOnLine(codCliente, tbClientesEnderecoIe.getCEP().asString(), tbClientesEnderecoIe.getINSCRICAO_ESTADUAL().asString(), tbClientesEnderecoIe.getCOD_CIDADES().asInteger(), 4);
                                    tbClientesEnderecoIe.next();
                                }
                                tbClientesEnderecoIe.first();
                            }
                            hBoxTitEndIE.setVisible(!tbClientesEnderecoIe.isEmpty());
                            boxEndIE.setVisible(!tbClientesEnderecoIe.isEmpty());
                            if (boxEndIE.isVisible()) {
                                formHeightAux = formHeightAux + 370;
                            }
                            break;
                        case 0:
                            ret = (rn.getDadosCEPOnLine(codCliente, tbClientesEndereco.getCEP_RES().asString(), "", tbClientesEndereco.getCOD_CID_RES().asInteger(), 1) && tbClientesEndereco.getCOD_CID_RES().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndRes.setVisible(ret);
                                boxEndRes.setVisible(ret);
                                if (boxEndRes.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(1);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndRes.setVisible(xDivOutrosEnd);
                                    boxEndRes.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            ret = (rn.getDadosCEPOnLine(codCliente, tbClientesEndereco.getCEP_COM().asString(), "", tbClientesEndereco.getCOD_CID_COM().asInteger(), 2) && tbClientesEndereco.getCOD_CID_COM().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndCom.setVisible(ret);
                                boxEndCom.setVisible(ret);
                                if (boxEndCom.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(2);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndCom.setVisible(xDivOutrosEnd);
                                    boxEndCom.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            ret = (rn.getDadosCEPOnLine(codCliente, tbClientesEndereco.getCEP_COBRANCA().asString(), "", tbClientesEndereco.getCOD_CID_COBRANCA().asInteger(), 3) && tbClientesEndereco.getCOD_CID_COBRANCA().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndCob.setVisible(ret);
                                boxEndCob.setVisible(ret);
                                if (boxEndCob.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(3);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndCob.setVisible(xDivOutrosEnd);
                                    boxEndCob.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            if (!tbClientesEnderecoIe.isEmpty()) {
                                tbClientesEnderecoIe.first();
                                while (!tbClientesEnderecoIe.eof()) {
                                    ret = rn.getDadosCEPOnLine(codCliente, tbClientesEnderecoIe.getCEP().asString(), tbClientesEnderecoIe.getINSCRICAO_ESTADUAL().asString(), tbClientesEnderecoIe.getCOD_CIDADES().asInteger(), 4);
                                    tbClientesEnderecoIe.next();
                                }
                                tbClientesEnderecoIe.first();
                            }
                            hBoxTitEndIE.setVisible(!tbClientesEnderecoIe.isEmpty());
                            boxEndIE.setVisible(!tbClientesEnderecoIe.isEmpty());
                            if (boxEndIE.isVisible()) {
                                formHeightAux = formHeightAux + 370;
                            }
                            break;
                    }
                }
            } else {
                hBoxTitEndRes.setVisible(false);
                boxEndRes.setVisible(false);
                hBoxTitEndCom.setVisible(false);
                boxEndCom.setVisible(false);
                hBoxTitEndCob.setVisible(false);
                boxEndCob.setVisible(false);
                hBoxTitEndIE.setVisible(false);
                boxEndIE.setVisible(false);
                if (!tbClientesEndereco.isEmpty()) {
                    switch (tpEndereco) {
                        case 1:
                            ret = (rn.getDadosCEPOnLine(codCliente, tbClientesEndereco.getCEP_RES().asString(), "", tbClientesEndereco.getCOD_CID_RES().asInteger(), 1) && tbClientesEndereco.getCOD_CID_RES().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndRes.setVisible(ret);
                                boxEndRes.setVisible(ret);
                                if (boxEndRes.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(tpEndereco);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndRes.setVisible(xDivOutrosEnd);
                                    boxEndRes.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            break;
                        case 2:
                            ret = (rn.getDadosCEPOnLine(codCliente, tbClientesEndereco.getCEP_COM().asString(), "", tbClientesEndereco.getCOD_CID_COM().asInteger(), 2) && tbClientesEndereco.getCOD_CID_COM().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndCom.setVisible(ret);
                                boxEndCom.setVisible(ret);
                                if (boxEndCom.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(tpEndereco);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndCom.setVisible(xDivOutrosEnd);
                                    boxEndCom.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            break;
                        case 3:
                            ret = (rn.getDadosCEPOnLine(codCliente, tbClientesEndereco.getCEP_COBRANCA().asString(), "", tbClientesEndereco.getCOD_CID_COBRANCA().asInteger(), 3) && tbClientesEndereco.getCOD_CID_COBRANCA().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndCob.setVisible(ret);
                                boxEndCob.setVisible(ret);
                                if (boxEndCob.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(tpEndereco);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndCob.setVisible(xDivOutrosEnd);
                                    boxEndCob.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            break;
                        case 4:
                            if (!tbClientesEnderecoIe.isEmpty()) {
                                tbClientesEnderecoIe.first();
                                while (!tbClientesEnderecoIe.eof()) {
                                    ret = rn.getDadosCEPOnLine(codCliente, tbClientesEnderecoIe.getCEP().asString(), tbClientesEnderecoIe.getINSCRICAO_ESTADUAL().asString(), tbClientesEnderecoIe.getCOD_CIDADES().asInteger(), 4);
                                    tbClientesEnderecoIe.next();
                                }
                                tbClientesEnderecoIe.first();
                            }
                            hBoxTitEndIE.setVisible(!tbClientesEnderecoIe.isEmpty());
                            boxEndIE.setVisible(!tbClientesEnderecoIe.isEmpty());
                            if (boxEndIE.isVisible()) {
                                formHeightAux = formHeightAux + 370;
                            }
                            break;
                        case 0:
                            ret = (rn.getDadosCEPOnLine(codCliente, tbClientesEndereco.getCEP_RES().asString(), "", tbClientesEndereco.getCOD_CID_RES().asInteger(), 1) && tbClientesEndereco.getCOD_CID_RES().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndRes.setVisible(ret);
                                boxEndRes.setVisible(ret);
                                if (boxEndRes.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(1);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndRes.setVisible(xDivOutrosEnd);
                                    boxEndRes.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            ret = (rn.getDadosCEPOnLine(codCliente, tbClientesEndereco.getCEP_COM().asString(), "", tbClientesEndereco.getCOD_CID_COM().asInteger(), 2) && tbClientesEndereco.getCOD_CID_COM().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndCom.setVisible(ret);
                                boxEndCom.setVisible(ret);
                                if (boxEndCom.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(2);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndCom.setVisible(xDivOutrosEnd);
                                    boxEndCom.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            ret = (rn.getDadosCEPOnLine(codCliente, tbClientesEndereco.getCEP_COBRANCA().asString(), "", tbClientesEndereco.getCOD_CID_COBRANCA().asInteger(), 3) && tbClientesEndereco.getCOD_CID_COBRANCA().asInteger() > 0);
                            if (ret) {
                                hBoxTitEndCob.setVisible(ret);
                                boxEndCob.setVisible(ret);
                                if (boxEndCob.isVisible()) {
                                    formHeightAux = formHeightAux + 207;
                                }
                                xDivOutrosEnd = verificaDivergencias(3);
                                if (!xDivOutrosEnd) {
                                    hBoxTitEndCob.setVisible(xDivOutrosEnd);
                                    boxEndCob.setVisible(xDivOutrosEnd);
                                    formHeightAux = formHeightAux - 207;
                                }
                            }
                            if (!tbClientesEnderecoIe.isEmpty()) {
                                tbClientesEnderecoIe.first();
                                while (!tbClientesEnderecoIe.eof()) {
                                    ret = rn.getDadosCEPOnLine(codCliente, tbClientesEnderecoIe.getCEP().asString(), tbClientesEnderecoIe.getINSCRICAO_ESTADUAL().asString(), tbClientesEnderecoIe.getCOD_CIDADES().asInteger(), 4);
                                    tbClientesEnderecoIe.next();
                                }
                                tbClientesEnderecoIe.first();
                            }
                            hBoxTitEndIE.setVisible(!tbClientesEnderecoIe.isEmpty());
                            boxEndIE.setVisible(!tbClientesEnderecoIe.isEmpty());
                            if (boxEndIE.isVisible()) {
                                formHeightAux = formHeightAux + 370;
                            }
                            break;
                    }
                } else {
                    ret = false;
                }
            }
            if (tpEndereco.equals(4)) {
                if (!tbClientesEnderecoIe.isEmpty()) {
                    xDivEndIE = existeDivergenciaEndIE();
                    if (!xDivEndIE) {
                        if (boxEndIE.isVisible()) {
                            hBoxTitEndIE.setVisible(false);
                            boxEndIE.setVisible(false);
                            formHeightAux = formHeightAux - 370;
                        }
                    }
                }
            } else if (tpEndereco.equals(0)) {
                if (!tbClientesEnderecoIe.isEmpty()) {
                    xDivEndIE = existeDivergenciaEndIE();
                    if (!xDivEndIE) {
                        if (boxEndIE.isVisible()) {
                            hBoxTitEndIE.setVisible(false);
                            boxEndIE.setVisible(false);
                            formHeightAux = formHeightAux - 370;
                        }
                    }
                }
            }
            xDivOutrosEnd = verificaDivergencias(tpEndereco);
            ret = xDivOutrosEnd;
            if (boxEndIE.isVisible()) {
                ret = xDivEndIE;
            }
            if (ret) {
                switch (tpEndereco) {
                    case 1:
                        lblCEPResidencial.setCaption("CEP: " + tbClientesEndereco.getCEP_RES().asString());
                        atribuirCorBtnRes("M");
                        break;
                    case 2:
                        lblCEPCom.setCaption("CEP: " + tbClientesEndereco.getCEP_COM().asString());
                        atribuirCorBtnCom("M");
                        break;
                    case 3:
                        lblCEPCob.setCaption("CEP: " + tbClientesEndereco.getCEP_COBRANCA().asString());
                        atribuirCorBtnCob("M");
                        break;
                    case 4:
                        atribuirCorBtnIE("M");
                        break;
                    case 0:
                        lblCEPResidencial.setCaption("CEP: " + tbClientesEndereco.getCEP_RES().asString());
                        lblCEPCom.setCaption("CEP: " + tbClientesEndereco.getCEP_COM().asString());
                        lblCEPCob.setCaption("CEP: " + tbClientesEndereco.getCEP_COBRANCA().asString());
                        atribuirCorBtnRes("M");
                        atribuirCorBtnCom("M");
                        atribuirCorBtnCob("M");
                        atribuirCorBtnIE("M");
                        break;
                    default:
                        break;
                }
                if (formHeightAux < formHeight) {
                    formHeight = formHeightAux;
                    vBoxTela.setHeight(formHeight);
                    vBoxTela.invalidate();
                    vBoxEnderecos.setHeight(formHeight);
                    vBoxTela.invalidate();
                    FrmCEPClienteOnLine.setClientHeight(formHeight);
                }
            }
        } catch (Exception ex) {
            EmpresaUtil.showError("Falha ao Buscar CEP OnLine", ex);
            ret = false;
        }
        return ret;
    }

    private boolean verificaDivergencias(Integer tpEndereco) throws DataException {
        // tpEndereco = 0-Todos, 1-Residencial, 2-Comercial, 3-Cobranca, 4-Inscricao Estadual
        Boolean xAchouDivergencia;
        Integer xCountDivergencia = 0;
        Integer xDivergenciaTotal = 0;
        try {
            if (tpEndereco.equals(0)) {
                if (hBoxTitEndRes.isVisible()) {
                    if (!tbClientesEndereco.getCEP_RES().isNull()) {
                        if (!tbClientesEndereco.getCEP_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CEP_RES").asString().trim().toUpperCase())) {
                            lblCepResAtual.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblCepResAtual.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getUF_RES().isNull()) {
                        if (!tbClientesEndereco.getUF_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("UF_RES").asString().trim().toUpperCase())) {
                            lblUFResCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblUFResCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getCIDADE_RES().isNull()) {
                        if (!tbClientesEndereco.getCIDADE_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CIDADE_RES").asString().trim().toUpperCase())) {
                            lblCidadeResCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblCidadeResCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getRUA_RES().isNull()) {
                        if (!tbClientesEndereco.getRUA_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("RUA_RES").asString().trim().toUpperCase())) {
                            lblRuaResCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblRuaResCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getBAIRRO_RES().isNull()) {
                        if (!tbClientesEndereco.getBAIRRO_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("BAIRRO_RES").asString().trim().toUpperCase())) {
                            lblBairroResCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblBairroResCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getCOMPLEMENTO_RES().isNull()) {
                        if (!tbClientesEndereco.getCOMPLEMENTO_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("COMPLEMENTO_RES").asString().trim().toUpperCase())) {
                            if (!tbClientesEnderecoTemp.getField("COMPLEMENTO_RES").isEmpty()) {
                                edComplementoResCad.setFontColor("clRed");
                                xCountDivergencia++;
                            }
                        } else {
                            edComplementoResCad.setFontColor("clGreen");
                        }
                    }
                }
                if (xCountDivergencia < 0) {
                    if (boxEndRes.isVisible()) {
                        boxEndRes.setVisible(false);
                        formHeightAux = formHeightAux - 207;
                    }
                }
                xDivergenciaTotal = xDivergenciaTotal + xCountDivergencia;
                if (hBoxTitEndCom.isVisible()) {
                    if (!tbClientesEndereco.getCEP_COM().isNull()) {
                        if (!tbClientesEndereco.getCEP_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CEP_COM").asString().trim().toUpperCase())) {
                            lblCepComAtual.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblCepComAtual.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getUF_COM().isNull()) {
                        if (!tbClientesEndereco.getUF_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("UF_COM").asString().trim().toUpperCase())) {
                            lblUFComCad.setFontColor("clRed");
                            xAchouDivergencia = true;
                            return xAchouDivergencia;
                        } else {
                            lblUFComCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getCIDADE_COM().isNull()) {
                        if (!tbClientesEndereco.getCIDADE_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CIDADE_COM").asString().trim().toUpperCase())) {
                            lblCidadeComCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblCidadeComCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getRUA_COM().isNull()) {
                        if (!tbClientesEndereco.getRUA_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("RUA_COM").asString().trim().toUpperCase())) {
                            lblRuaComCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblRuaComCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getBAIRRO_COM().isNull()) {
                        if (!tbClientesEndereco.getBAIRRO_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("BAIRRO_COM").asString().trim().toUpperCase())) {
                            lblBairroComCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblBairroComCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getCOMPLEMENTO_COM().isNull()) {
                        if (!tbClientesEndereco.getCOMPLEMENTO_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("COMPLEMENTO_COM").asString().trim().toUpperCase())) {
                            if (!tbClientesEnderecoTemp.getField("COMPLEMENTO_COM").isEmpty()) {
                                edComplementoComCad.setFontColor("clRed");
                                xCountDivergencia++;
                            }
                        } else {
                            edComplementoComCad.setFontColor("clGreen");
                        }
                    }
                }
                if (xCountDivergencia < 0) {
                    if (boxEndCom.isVisible()) {
                        boxEndCom.setVisible(false);
                        formHeightAux = formHeightAux - 207;
                    }
                }
                xDivergenciaTotal = xDivergenciaTotal + xCountDivergencia;
                if (hBoxTitEndCob.isVisible()) {
                    if (!tbClientesEndereco.getCEP_COBRANCA().isNull()) {
                        if (!tbClientesEndereco.getCEP_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CEP_COBRANCA").asString().trim().toUpperCase())) {
                            lblCepCobAtual.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblCepCobAtual.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getUF_COBRANCA().isNull()) {
                        if (!tbClientesEndereco.getUF_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("UF_COBRANCA").asString().trim().toUpperCase())) {
                            lblUFCobCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblUFCobCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getCIDADE_COB().isNull()) {
                        if (!tbClientesEndereco.getCIDADE_COB().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CIDADE_COB").asString().trim().toUpperCase())) {
                            lblCidadeCobCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblCidadeCobCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getRUA_COBRANCA().isNull()) {
                        if (!tbClientesEndereco.getRUA_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("RUA_COBRANCA").asString().trim().toUpperCase())) {
                            lblRuaCobCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblRuaCobCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getBAIRRO_COBRANCA().isNull()) {
                        if (!tbClientesEndereco.getBAIRRO_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("BAIRRO_COBRANCA").asString().trim().toUpperCase())) {
                            lblBairroCobCad.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblBairroCobCad.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEndereco.getCOMPLEMENTO_COBRANCA().isNull()) {
                        if (!tbClientesEndereco.getCOMPLEMENTO_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("COMPLEMENTO_COBRANCA").asString().trim().toUpperCase())) {
                            if (!tbClientesEnderecoTemp.getField("COMPLEMENTO_COBRANCA").isEmpty()) {
                                edComplementoCobCad.setFontColor("clRed");
                                xCountDivergencia++;
                            }
                        } else {
                            edComplementoCobCad.setFontColor("clGreen");
                        }
                    }
                }
                if (xCountDivergencia < 0) {
                    if (boxEndCob.isVisible()) {
                        boxEndCob.setVisible(false);
                        formHeightAux = formHeightAux - 207;
                    }
                }
                xDivergenciaTotal = xDivergenciaTotal + xCountDivergencia;
                if (boxEndIE.isVisible()) {
                    if (!tbClientesEnderecoIe.getUF().isNull()) {
                        if (!tbClientesEnderecoIe.getUF().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("UF").asString().trim().toUpperCase())) {
                            lblUFCadIE.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblUFCadIE.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEnderecoIe.getCIDADE().isNull()) {
                        if (!tbClientesEnderecoIe.getCIDADE().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("CIDADE").asString().trim().toUpperCase())) {
                            lblCidadeCadIE.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblCidadeCadIE.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEnderecoIe.getRUA().isNull()) {
                        if (!tbClientesEnderecoIe.getRUA().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("RUA").asString().trim().toUpperCase())) {
                            lblRuaCadIE.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblRuaCadIE.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEnderecoIe.getBAIRRO().isNull()) {
                        if (!tbClientesEnderecoIe.getBAIRRO().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("BAIRRO").asString().trim().toUpperCase())) {
                            lblBairroCadIE.setFontColor("clRed");
                            xCountDivergencia++;
                        } else {
                            lblBairroCadIE.setFontColor("clGreen");
                        }
                    }
                    if (!tbClientesEnderecoIe.getCOMPLEMENTO().isNull()) {
                        if (!tbClientesEnderecoIe.getCOMPLEMENTO().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("COMPLEMENTO").asString().trim().toUpperCase())) {
                            if (!tbClientesEnderecoIeTemp.getField("COMPLEMENTO").isEmpty()) {
                                edComplementoCadIE.setFontColor("clRed");
                                xCountDivergencia++;
                            }
                        } else {
                            edComplementoCadIE.setFontColor("clGreen");
                        }
                    }
                }
            } else {
                switch (tpEndereco) {
                    case 1:
                        if (hBoxTitEndRes.isVisible()) {
                            if (!tbClientesEndereco.getCEP_RES().isNull()) {
                                if (!tbClientesEndereco.getCEP_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CEP_RES").asString().trim().toUpperCase())) {
                                    lblCepResAtual.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblCepResAtual.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getUF_RES().isNull()) {
                                if (!tbClientesEndereco.getUF_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("UF_RES").asString().trim().toUpperCase())) {
                                    lblUFResCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblUFResCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getCIDADE_RES().isNull()) {
                                if (!tbClientesEndereco.getCIDADE_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CIDADE_RES").asString().trim().toUpperCase())) {
                                    lblCidadeResCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblCidadeResCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getRUA_RES().isNull()) {
                                if (!tbClientesEndereco.getRUA_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("RUA_RES").asString().trim().toUpperCase())) {
                                    lblRuaResCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblRuaResCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getBAIRRO_RES().isNull()) {
                                if (!tbClientesEndereco.getBAIRRO_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("BAIRRO_RES").asString().trim().toUpperCase())) {
                                    lblBairroResCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblBairroResCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getCOMPLEMENTO_RES().isNull()) {
                                if (!tbClientesEndereco.getCOMPLEMENTO_RES().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("COMPLEMENTO_RES").asString().trim().toUpperCase())) {
                                    if (!tbClientesEnderecoTemp.getField("COMPLEMENTO_RES").isEmpty()) {
                                        edComplementoResCad.setFontColor("clRed");
                                        xCountDivergencia++;
                                    }
                                } else {
                                    edComplementoResCad.setFontColor("clGreen");
                                }
                            }
                        }
                        if (xCountDivergencia < 0) {
                            if (boxEndRes.isVisible()) {
                                boxEndRes.setVisible(false);
                                formHeightAux = formHeightAux - 207;
                            }
                        }
                        xDivergenciaTotal = xDivergenciaTotal + xCountDivergencia;
                        break;
                    case 2:
                        if (hBoxTitEndCom.isVisible()) {
                            if (!tbClientesEndereco.getCEP_COM().isNull()) {
                                if (!tbClientesEndereco.getCEP_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CEP_COM").asString().trim().toUpperCase())) {
                                    lblCepComAtual.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblCepComAtual.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getUF_COM().isNull()) {
                                if (!tbClientesEndereco.getUF_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("UF_COM").asString().trim().toUpperCase())) {
                                    lblUFComCad.setFontColor("clRed");
                                    xAchouDivergencia = true;
                                    return xAchouDivergencia;
                                } else {
                                    lblUFComCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getCIDADE_COM().isNull()) {
                                if (!tbClientesEndereco.getCIDADE_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CIDADE_COM").asString().trim().toUpperCase())) {
                                    lblCidadeComCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblCidadeComCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getRUA_COM().isNull()) {
                                if (!tbClientesEndereco.getRUA_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("RUA_COM").asString().trim().toUpperCase())) {
                                    lblRuaComCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblRuaComCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getBAIRRO_COM().isNull()) {
                                if (!tbClientesEndereco.getBAIRRO_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("BAIRRO_COM").asString().trim().toUpperCase())) {
                                    lblBairroComCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblBairroComCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getCOMPLEMENTO_COM().isNull()) {
                                if (!tbClientesEndereco.getCOMPLEMENTO_COM().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("COMPLEMENTO_COM").asString().trim().toUpperCase())) {
                                    if (!tbClientesEnderecoTemp.getField("COMPLEMENTO_COM").isEmpty()) {
                                        edComplementoComCad.setFontColor("clRed");
                                        xCountDivergencia++;
                                    }
                                } else {
                                    edComplementoComCad.setFontColor("clGreen");
                                }
                            }
                        }
                        if (xCountDivergencia < 0) {
                            if (boxEndCom.isVisible()) {
                                boxEndCom.setVisible(false);
                                formHeightAux = formHeightAux - 207;
                            }
                        }
                        xDivergenciaTotal = xDivergenciaTotal + xCountDivergencia;
                        break;
                    case 3:
                        if (hBoxTitEndCob.isVisible()) {
                            if (!tbClientesEndereco.getCEP_COBRANCA().isNull()) {
                                if (!tbClientesEndereco.getCEP_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CEP_COBRANCA").asString().trim().toUpperCase())) {
                                    lblCepCobAtual.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblCepCobAtual.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getUF_COBRANCA().isNull()) {
                                if (!tbClientesEndereco.getUF_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("UF_COBRANCA").asString().trim().toUpperCase())) {
                                    lblUFCobCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblUFCobCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getCIDADE_COB().isNull()) {
                                if (!tbClientesEndereco.getCIDADE_COB().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("CIDADE_COB").asString().trim().toUpperCase())) {
                                    lblCidadeCobCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblCidadeCobCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getRUA_COBRANCA().isNull()) {
                                if (!tbClientesEndereco.getRUA_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("RUA_COBRANCA").asString().trim().toUpperCase())) {
                                    lblRuaCobCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblRuaCobCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getBAIRRO_COBRANCA().isNull()) {
                                if (!tbClientesEndereco.getBAIRRO_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("BAIRRO_COBRANCA").asString().trim().toUpperCase())) {
                                    lblBairroCobCad.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblBairroCobCad.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEndereco.getCOMPLEMENTO_COBRANCA().isNull()) {
                                if (!tbClientesEndereco.getCOMPLEMENTO_COBRANCA().asString().trim().toUpperCase().equals(tbClientesEnderecoTemp.getField("COMPLEMENTO_COBRANCA").asString().trim().toUpperCase())) {
                                    if (!tbClientesEnderecoTemp.getField("COMPLEMENTO_COBRANCA").isEmpty()) {
                                        edComplementoCobCad.setFontColor("clRed");
                                        xCountDivergencia++;
                                    }
                                } else {
                                    edComplementoCobCad.setFontColor("clGreen");
                                }
                            }
                        }
                        if (xCountDivergencia < 0) {
                            if (boxEndCob.isVisible()) {
                                boxEndCob.setVisible(false);
                                formHeightAux = formHeightAux - 207;
                            }
                        }
                        xDivergenciaTotal = xDivergenciaTotal + xCountDivergencia;
                        break;
                    case 4:
                        if (boxEndIE.isVisible()) {
                            if (!tbClientesEnderecoIe.getUF().isNull()) {
                                if (!tbClientesEnderecoIe.getUF().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("UF").asString().trim().toUpperCase())) {
                                    lblUFCadIE.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblUFCadIE.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEnderecoIe.getCIDADE().isNull()) {
                                if (!tbClientesEnderecoIe.getCIDADE().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("CIDADE").asString().trim().toUpperCase())) {
                                    lblCidadeCadIE.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblCidadeCadIE.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEnderecoIe.getRUA().isNull()) {
                                if (!tbClientesEnderecoIe.getRUA().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("RUA").asString().trim().toUpperCase())) {
                                    lblRuaCadIE.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblRuaCadIE.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEnderecoIe.getBAIRRO().isNull()) {
                                if (!tbClientesEnderecoIe.getBAIRRO().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("BAIRRO").asString().trim().toUpperCase())) {
                                    lblBairroCadIE.setFontColor("clRed");
                                    xCountDivergencia++;
                                } else {
                                    lblBairroCadIE.setFontColor("clGreen");
                                }
                            }
                            if (!tbClientesEnderecoIe.getCOMPLEMENTO().isNull()) {
                                if (!tbClientesEnderecoIe.getCOMPLEMENTO().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("COMPLEMENTO").asString().trim().toUpperCase())) {
                                    if (!tbClientesEnderecoIeTemp.getField("COMPLEMENTO").isEmpty()) {
                                        edComplementoCadIE.setFontColor("clRed");
                                        xCountDivergencia++;
                                    }
                                } else {
                                    edComplementoCadIE.setFontColor("clGreen");
                                }
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
            xDivergenciaTotal = xDivergenciaTotal + xCountDivergencia;
            xAchouDivergencia = (xDivergenciaTotal > 0);
        } catch (Exception ex) {
            ex.printStackTrace();
            EmpresaUtil.showError("Falha ao verificaDivergencias. Motivo: ", ex);
            xAchouDivergencia = false;
        }
        return xAchouDivergencia;
    }

    private boolean existeDivergenciaEndIE() {
        Boolean xAchouDivergencia = false;
        Integer xCountDivergencia = 0;
        try {
            tbClientesEnderecoIe.first();
            while (!tbClientesEnderecoIe.eof()) {
                if (tbClientesEnderecoIe.getMANTER().asString().equals("X")) {
                    if (!tbClientesEnderecoIe.getUF().isNull()) {
                        if (!tbClientesEnderecoIe.getUF().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("UF").asString().trim().toUpperCase())) {
                            xCountDivergencia++;
                        }
                    }
                    if (!tbClientesEnderecoIe.getCIDADE().isNull()) {
                        if (!tbClientesEnderecoIe.getCIDADE().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("CIDADE").asString().trim().toUpperCase())) {
                            xCountDivergencia++;
                        }
                    }
                    if (!tbClientesEnderecoIe.getRUA().isNull()) {
                        if (!tbClientesEnderecoIe.getRUA().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("RUA").asString().trim().toUpperCase())) {
                            xCountDivergencia++;
                        }
                    }
                    if (!tbClientesEnderecoIe.getBAIRRO().isNull()) {
                        if (!tbClientesEnderecoIe.getBAIRRO().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("BAIRRO").asString().trim().toUpperCase())) {
                            xCountDivergencia++;
                        }
                    }
                    if (!tbClientesEnderecoIe.getCOMPLEMENTO().isNull()) {
                        if (!tbClientesEnderecoIe.getCOMPLEMENTO().asString().trim().toUpperCase().equals(tbClientesEnderecoIeTemp.getField("COMPLEMENTO").asString().trim().toUpperCase())) {
                            if (!tbClientesEnderecoIeTemp.getField("COMPLEMENTO").isEmpty()) {
                                xCountDivergencia++;
                            }
                        }
                    }
                }
                if (xCountDivergencia.equals(0)) {
                    tbClientesEnderecoIe.edit();
                    tbClientesEnderecoIe.setMANTER("X");
                    tbClientesEnderecoIe.post();
                }
                tbClientesEnderecoIe.next();
            }
            tbClientesEnderecoIe.first();
            xAchouDivergencia = (xCountDivergencia > 0);
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Executar existeDivergenciaEndIE. Motivo: ", ex);
        }
        return xAchouDivergencia;
    }

    private void atribuirCorBtnRes(String status) {
        statusRes = status;
        String colorDestaque = "clSilver";
        String colorDefault = "#FFFFFF";
        hbCEPOnLineAceitarRes.setColor(colorDefault);
        hbCEPOnLineManterRes.setColor(colorDefault);
        if (null != status) {
            switch (status) {
                case "A":
                    hbCEPOnLineAceitarRes.setColor(colorDestaque);
                    break;
                case "M":
                    hbCEPOnLineManterRes.setColor(colorDestaque);
                    break;
                default:
                    statusRes = "A";
                    hbCEPOnLineAceitarRes.setColor(colorDestaque);
                    break;
            }
        }
        hbCEPOnLineAceitarRes.invalidate();
        hbCEPOnLineManterRes.invalidate();
    }

    private void atribuirCorBtnCom(String status) {
        statusCom = status;
        String colorDestaque = "clSilver";
        String colorDefault = "#FFFFFF";
        hbCEPOnLineAceitarCom.setColor(colorDefault);
        hbCEPOnLineManterCom.setColor(colorDefault);
        if (null != status) {
            switch (status) {
                case "A":
                    hbCEPOnLineAceitarCom.setColor(colorDestaque);
                    break;
                case "M":
                    hbCEPOnLineManterCom.setColor(colorDestaque);
                    break;
                default:
                    statusRes = "A";
                    hbCEPOnLineAceitarCom.setColor(colorDestaque);
                    break;
            }
        }
        hbCEPOnLineAceitarCom.invalidate();
        hbCEPOnLineManterCom.invalidate();
    }

    private void atribuirCorBtnCob(String status) {
        statusCob = status;
        String colorDestaque = "clSilver";
        String colorDefault = "#FFFFFF";
        hbCEPOnLineAceitarCob.setColor(colorDefault);
        hbCEPOnLineManterCob.setColor(colorDefault);
        if (null != status) {
            switch (status) {
                case "A":
                    hbCEPOnLineAceitarCob.setColor(colorDestaque);
                    break;
                case "M":
                    hbCEPOnLineManterCob.setColor(colorDestaque);
                    break;
                default:
                    statusRes = "A";
                    hbCEPOnLineAceitarCob.setColor(colorDestaque);
                    break;
            }
        }
        hbCEPOnLineAceitarCob.invalidate();
        hbCEPOnLineManterCob.invalidate();
    }

    private void atribuirCorBtnIE(String status) {
        String colorDestaque = "clSilver";
        String colorDefault = "#FFFFFF";
        hbCEPOnLineAceitarIE.setColor(colorDefault);
        hbCEPOnLineManterIE.setColor(colorDefault);
        if (null != status) {
            switch (status) {
                case "A":
                    hbCEPOnLineAceitarIE.setColor(colorDestaque);
                    break;
                case "M":
                    hbCEPOnLineManterIE.setColor(colorDestaque);
                    break;
                default:
                    statusRes = "A";
                    hbCEPOnLineAceitarIE.setColor(colorDestaque);
                    break;
            }
        }
        hbCEPOnLineAceitarIE.invalidate();
        hbCEPOnLineManterIE.invalidate();
    }

    @Override
    public void btnSalvarClick(final Event event) {
        try {
            Integer codCidadeIBGE;
            if (statusRes.equals("A")) {
                tbClientesEnderecoTemp.edit();
                tbClientesEnderecoTemp.setField("UF_RES", lblUFResCad.getCaption().trim());
                tbClientesEnderecoTemp.setField("COD_CID_RES", tbClientesEnderecoTemp.getField("COD_CID_RES").asInteger());
                tbClientesEnderecoTemp.setField("RUA_RES", lblRuaResCad.getCaption().trim());
                tbClientesEnderecoTemp.setField("BAIRRO_RES", lblBairroResCad.getCaption().trim());
                tbClientesEnderecoTemp.setField("FACHADA_RES", edNumeroResCad.getValue().asString().trim());
                tbClientesEnderecoTemp.setField("COMPLEMENTO_RES", edComplementoResCad.getValue().asString().trim());
                tbClientesEnderecoTemp.post();
                tbClientesEndereco.edit();
                if (tbCidades.locate("COD_CIDADES", tbClientesEnderecoTemp.getField("COD_CID_RES").asInteger())) {
                    tbClientesEndereco.setCOD_CID_RES(tbClientesEnderecoTemp.getField("COD_CID_RES").asInteger());
                }
                tbClientesEndereco.post();
                tbClientesEndereco.applyUpdates();
                tbClientesEndereco.commitUpdates();
                if (tbClientesEndereco.getField("CODCIDADEIBGE_RES").asInteger() > 0) {
                    if (tbCidades.locate("COD_CIDADES", tbClientesEnderecoTemp.getField("COD_CID_RES").asInteger())) {
                        codCidadeIBGE = tbClientesEndereco.getField("CODCIDADEIBGE_RES").asInteger();
                        if (codCidadeIBGE > 0) {
                            tbCidades.edit();
                            tbCidades.setCODCIDADEIBGE(tbClientesEndereco.getField("CODCIDADEIBGE_RES").asInteger());
                            tbCidades.post();
                            tbCidades.applyUpdates();
                            tbCidades.commitUpdates();
                        }
                    }
                }
            }
            if (statusCom.equals("A")) {
                tbClientesEnderecoTemp.edit();
                tbClientesEnderecoTemp.setField("UF_COM", lblUFComCad.getCaption().trim());
                tbClientesEnderecoTemp.setField("COD_CID_COM", tbClientesEnderecoTemp.getField("COD_CID_COM").asInteger());
                tbClientesEnderecoTemp.setField("RUA_COM", lblRuaComCad.getCaption().trim());
                tbClientesEnderecoTemp.setField("BAIRRO_COM", lblBairroComCad.getCaption().trim());
                tbClientesEnderecoTemp.setField("FACHADA_COM", edNumeroComCad.getValue().asString().trim());
                tbClientesEnderecoTemp.setField("COMPLEMENTO_COM", edComplementoComCad.getValue().asString().trim());
                tbClientesEnderecoTemp.post();
                tbClientesEndereco.edit();
                if (tbCidades.locate("COD_CIDADES", tbClientesEnderecoTemp.getField("COD_CID_COM").asInteger())) {
                    tbClientesEndereco.setCOD_CID_COM(tbClientesEnderecoTemp.getField("COD_CID_COM").asInteger());
                }
                tbClientesEndereco.post();
                tbClientesEndereco.applyUpdates();
                tbClientesEndereco.commitUpdates();
                if (tbClientesEndereco.getField("CODCIDADEIBGE_COM").asInteger() > 0) {
                    if (tbCidades.locate("COD_CIDADES", tbClientesEnderecoTemp.getField("COD_CID_COM").asInteger())) {
                        codCidadeIBGE = tbClientesEndereco.getField("CODCIDADEIBGE_COM").asInteger();
                        if (codCidadeIBGE > 0) {
                            tbCidades.edit();
                            tbCidades.setCODCIDADEIBGE(tbClientesEndereco.getField("CODCIDADEIBGE_COM").asInteger());
                            tbCidades.post();
                            tbCidades.applyUpdates();
                            tbCidades.commitUpdates();
                        }
                    }
                }
            }
            if (statusCob.equals("A")) {
                tbClientesEnderecoTemp.edit();
                tbClientesEnderecoTemp.setField("UF_COBRANCA", lblUFCobCad.getCaption().trim());
                tbClientesEnderecoTemp.setField("RUA_COBRANCA", lblRuaCobCad.getCaption().trim());
                tbClientesEnderecoTemp.setField("BAIRRO_COBRANCA", lblBairroCobCad.getCaption().trim());
                tbClientesEnderecoTemp.setField("FACHADA_COBRANCA", edNumeroCobCad.getValue().asString().trim());
                tbClientesEnderecoTemp.setField("COMPLEMENTO_COBRANCA", edComplementoCobCad.getValue().asString().trim());
                tbClientesEnderecoTemp.post();
                tbClientesEndereco.edit();
                if (tbCidades.locate("COD_CIDADES", tbClientesEnderecoTemp.getField("COD_CID_COBRANCA").asInteger())) {
                    tbClientesEndereco.setCOD_CID_COBRANCA(tbClientesEnderecoTemp.getField("COD_CID_COBRANCA").asInteger());
                }
                tbClientesEndereco.post();
                tbClientesEndereco.applyUpdates();
                tbClientesEndereco.commitUpdates();
                if (tbClientesEndereco.getField("CODCIDADEIBGE_COB").asInteger() > 0) {
                    if (tbCidades.locate("COD_CIDADES", tbClientesEnderecoTemp.getField("COD_CID_COBRANCA").asInteger())) {
                        codCidadeIBGE = tbClientesEndereco.getField("CODCIDADEIBGE_COB").asInteger();
                        if (codCidadeIBGE > 0) {
                            tbCidades.edit();
                            tbCidades.setCODCIDADEIBGE(tbClientesEndereco.getField("CODCIDADEIBGE_COB").asInteger());
                            tbCidades.post();
                            tbCidades.applyUpdates();
                            tbCidades.commitUpdates();
                        }
                    }
                }
            }
            if (!tbClientesEnderecoIe.isEmpty()) {
                tbClientesEnderecoIe.first();
                while (!tbClientesEnderecoIe.eof()) {
                    if (tbClientesEnderecoIe.getMANTER().asString().equals("X")) {
                        tbClientesEnderecoIe.edit();
                        tbClientesEnderecoIe.post();
                        tbClientesEnderecoIe.applyUpdates();
                        tbClientesEnderecoIe.commitUpdates();
                    }
                    tbClientesEnderecoIe.next();
                }
                tbClientesEnderecoIe.first();
            }
            ok = true;
            close();
        } catch (DataException ex) {
            // A-Aceitar, M=Manter
            statusRes = "M";
            // A-Aceitar, M=Manter
            statusCom = "M";
            // A-Aceitar, M=Manter
            statusCob = "M";
            atribuirCorBtnRes("M");
            atribuirCorBtnCom("M");
            atribuirCorBtnCob("M");
            atribuirCorBtnIE("M");
            EmpresaUtil.showError("Falha ao Salvar Alterações. Motivo: ", ex);
        }
    }

    @Override
    public void btnVoltarClick(final Event event) {
        // A-Aceitar, M=Manter
        statusRes = "M";
        // A-Aceitar, M=Manter
        statusCom = "M";
        // A-Aceitar, M=Manter
        statusCob = "M";
        close();
    }

    @Override
    public void hbCEPOnLineAceitarResClick(final Event event) {
        try {
            Value qtdeReg = new Value(0);
            Integer codCidRes = rn.getCodCidade(lblUFResCorreios.getCaption(), lblCidadeResCorreios.getCaption(), tbClientesEndereco.getCEP_RES().asString(), tbClientesEndereco.getField("CODCIDADEIBGE_RES").asInteger(), qtdeReg);
            if (qtdeReg.asInteger() > 0 && codCidRes > 0) {
                tbClientesEndereco.edit();
                tbClientesEndereco.setUF_RES(lblUFResCorreios.getCaption());
                tbClientesEndereco.setCIDADE_RES(lblCidadeResCorreios.getCaption());
                tbClientesEndereco.setRUA_RES(lblRuaResCorreios.getCaption());
                tbClientesEndereco.setBAIRRO_RES(lblBairroResCorreios.getCaption());
                ///if (edComplementoResCad.getValue().asString().trim().length() <= 0) {
                String complEnd = edComplementoResCad.getValue().asString().trim() + ' ' + lblComplementoResCorreios.getCaption().trim();
                if (complEnd.length() > 30) {
                    complEnd = complEnd.substring(0, 30);
                }
                tbClientesEndereco.setCOMPLEMENTO_RES(complEnd);
                //}
                tbClientesEndereco.getCOD_CID_RES().setValue(codCidRes);
                tbClientesEndereco.post();
                tbClientesEnderecoTemp.edit();
                tbClientesEnderecoTemp.setField("COD_CID_RES", codCidRes);
                tbClientesEnderecoTemp.post();
                atribuirCorBtnRes("A");
            } else {
                FrmConsultaCidadeA form = new FrmConsultaCidadeA();
                if (!tbClientesEnderecoTemp.getField("UF_RES").isEmpty()) {
                    form.setCidadeEnd(tbClientesEndereco.getUF_RES().asString(), lblCidadeResCorreios.getCaption());
                }
                FormUtil.doShow(form, (EventListener) t -> {
                    if (form.isOk()) {
                        tbClientesEnderecoTemp.edit();
                        tbClientesEnderecoTemp.setField("COD_CID_RES", form.tbCidades.getCOD_CIDADES());
                        tbClientesEnderecoTemp.setField("CODCIDADEIBGE_RES", form.tbCidades.getCODCIDADEIBGE());
                        tbClientesEnderecoTemp.post();
                        lblUFResCad.setCaption(lblUFResCorreios.getCaption());
                        tbClientesEndereco.edit();
                        tbClientesEndereco.setUF_RES(lblUFResCorreios.getCaption());
                        lblUFResCad.setCaption(lblUFResCorreios.getCaption());
                        tbClientesEndereco.setCIDADE_RES(form.tbCidades.getDESCRICAO().asString());
                        lblCidadeResCad.setCaption(form.tbCidades.getDESCRICAO().asString());
                        tbClientesEndereco.setRUA_RES(lblRuaResCorreios.getCaption());
                        lblRuaResCad.setCaption(lblRuaResCorreios.getCaption());
                        tbClientesEndereco.setBAIRRO_RES(lblBairroResCorreios.getCaption());
                        lblBairroResCad.setCaption(lblBairroResCorreios.getCaption());
                        //if (edComplementoResCad.getValue().asString().trim().length() <= 0) {
                        tbClientesEndereco.setCOMPLEMENTO_RES(lblComplementoResCorreios.getCaption());
                        edComplementoResCad.setValue(lblComplementoResCorreios.getCaption());
                        //}
                        tbClientesEndereco.getCOD_CID_RES().setValue(tbClientesEnderecoTemp.getField("COD_CID_RES"));
                        tbClientesEndereco.getCODCIDADEIBGE_RES().setValue(tbClientesEnderecoTemp.getField("CODCIDADEIBGE_RES"));
                        tbClientesEndereco.post();
                        atribuirCorBtnRes("A");
                    }
                });
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Encontrar Cadastro da Cidade. Motivo: ", ex);
        }
    }

    @Override
    public void hbCEPOnLineManterResClick(final Event event) {
        try {
            tbClientesEndereco.edit();
            tbClientesEndereco.setUF_RES(tbClientesEndereco.getUF_RES_OLD().asString().trim());
            tbClientesEndereco.setCIDADE_RES(tbClientesEndereco.getCIDADE_RES_OLD().asString().trim());
            tbClientesEndereco.setRUA_RES(tbClientesEndereco.getRUA_RES_OLD().asString().trim());
            tbClientesEndereco.setBAIRRO_RES(tbClientesEndereco.getBAIRRO_RES_OLD().asString().trim());
            tbClientesEndereco.setCOMPLEMENTO_RES(tbClientesEndereco.getCOMPLEMENTO_RES_OLD().asString().trim());
            tbClientesEndereco.setCOD_CID_RES(tbClientesEndereco.getCOD_CID_RES_OLD().asInteger());
            tbClientesEndereco.setCODCIDADEIBGE_COB(tbClientesEndereco.getCODCIDADEIBGE_RES_OLD().asInteger());
            tbClientesEndereco.post();
            atribuirCorBtnRes("M");
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Manter Dados do Endereço Residencial. Motivo: ", ex);
        }
    }

    @Override
    public void hbCEPOnLineAceitarComClick(final Event event) {
        try {
            Value qtdeReg = new Value(0);
            Integer codCidCom = rn.getCodCidade(lblUFComCorreios.getCaption(), lblCidadeComCorreios.getCaption(), tbClientesEndereco.getCEP_COM().asString(), tbClientesEndereco.getField("CODCIDADEIBGE_COM").asInteger(), qtdeReg);
            if (qtdeReg.asInteger() > 0 && codCidCom > 0) {
                tbClientesEndereco.edit();
                tbClientesEndereco.setUF_COM(lblUFComCorreios.getCaption());
                tbClientesEndereco.setCIDADE_COM(lblCidadeComCorreios.getCaption());
                tbClientesEndereco.setRUA_COM(lblRuaComCorreios.getCaption());
                tbClientesEndereco.setBAIRRO_COM(lblBairroComCorreios.getCaption());
                //if (edComplementoComCad.getValue().asString().trim().length() <= 0) {
                String complEnd = edComplementoComCad.getValue().asString().trim() + ' ' + lblComplementoComCorreios.getCaption().trim();
                if (complEnd.length() > 30) {
                    complEnd = complEnd.substring(0, 30);
                }
                tbClientesEndereco.setCOMPLEMENTO_COM(complEnd);
                //}
                tbClientesEndereco.getCOD_CID_COM().setValue(codCidCom);
                tbClientesEndereco.post();
                tbClientesEnderecoTemp.edit();
                tbClientesEnderecoTemp.setField("COD_CID_COM", codCidCom);
                tbClientesEnderecoTemp.post();
                atribuirCorBtnCom("A");
            } else {
                FrmConsultaCidadeA form = new FrmConsultaCidadeA();
                if (!tbClientesEnderecoTemp.getField("UF_COM").isEmpty()) {
                    form.setCidadeEnd(tbClientesEndereco.getUF_COM().asString(), lblCidadeComCorreios.getCaption());
                }
                FormUtil.doShow(form, (EventListener) t -> {
                    if (form.isOk()) {
                        tbClientesEnderecoTemp.edit();
                        tbClientesEnderecoTemp.setField("COD_CID_COM", form.tbCidades.getCOD_CIDADES());
                        tbClientesEnderecoTemp.setField("CODCIDADEIBGE_COM", form.tbCidades.getCODCIDADEIBGE());
                        tbClientesEnderecoTemp.post();
                        lblUFCobCad.setCaption(lblUFComCorreios.getCaption());
                        tbClientesEndereco.edit();
                        tbClientesEndereco.setUF_COM(lblUFComCorreios.getCaption());
                        lblUFCobCad.setCaption(lblUFComCorreios.getCaption());
                        tbClientesEndereco.setCIDADE_COM(form.tbCidades.getDESCRICAO().asString());
                        lblCidadeCobCad.setCaption(form.tbCidades.getDESCRICAO().asString());
                        tbClientesEndereco.setRUA_COM(lblRuaComCorreios.getCaption());
                        lblRuaCobCad.setCaption(lblRuaComCorreios.getCaption());
                        tbClientesEndereco.setBAIRRO_COM(lblBairroComCorreios.getCaption());
                        lblBairroCobCad.setCaption(lblBairroComCorreios.getCaption());
                        //if (edComplementoComCad.getValue().asString().trim().length() <= 0) {
                        tbClientesEndereco.setCOMPLEMENTO_COM(lblComplementoComCorreios.getCaption());
                        edComplementoCobCad.setValue(lblComplementoComCorreios.getCaption());
                        //}
                        tbClientesEndereco.getCOD_CID_COM().setValue(tbClientesEnderecoTemp.getField("COD_CID_COM"));
                        tbClientesEndereco.getCODCIDADEIBGE_COM().setValue(tbClientesEnderecoTemp.getField("CODCIDADEIBGE_COM"));
                        tbClientesEndereco.post();
                        atribuirCorBtnCom("A");
                    }
                });
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Encontrar Cadastro da Cidade. Motivo: ", ex);
        }
    }

    @Override
    public void hbCEPOnLineManterComClick(final Event event) {
        try {
            tbClientesEndereco.edit();
            tbClientesEndereco.setUF_COM(tbClientesEndereco.getUF_COM_OLD().asString().trim());
            tbClientesEndereco.setCIDADE_COM(tbClientesEndereco.getCIDADE_COM_OLD().asString().trim());
            tbClientesEndereco.setRUA_COM(tbClientesEndereco.getRUA_COM_OLD().asString().trim());
            tbClientesEndereco.setBAIRRO_COM(tbClientesEndereco.getBAIRRO_COM_OLD().asString().trim());
            tbClientesEndereco.setCOMPLEMENTO_COM(tbClientesEndereco.getCOMPLEMENTO_COM_OLD().asString().trim());
            tbClientesEndereco.setCOD_CID_COM(tbClientesEndereco.getCOD_CID_COM_OLD().asInteger());
            tbClientesEndereco.setCODCIDADEIBGE_COM(tbClientesEndereco.getCODCIDADEIBGE_COM_OLD().asInteger());
            tbClientesEndereco.post();
            atribuirCorBtnCom("M");
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Manter Dados do Endereço Comercial. Motivo: ", ex);
        }
    }

    @Override
    public void hbCEPOnLineAceitarCobClick(final Event event) {
        try {
            Value qtdeReg = new Value(0);
            Integer codCidCob = rn.getCodCidade(lblUFCobCorreios.getCaption(), lblCidadeCobCorreios.getCaption(), tbClientesEndereco.getCEP_COBRANCA().asString(), tbClientesEndereco.getField("CODCIDADEIBGE_COB").asInteger(), qtdeReg);
            if (qtdeReg.asInteger() > 0 && codCidCob > 0) {
                tbClientesEndereco.edit();
                tbClientesEndereco.setUF_COBRANCA(lblUFCobCorreios.getCaption());
                tbClientesEndereco.setCIDADE_COB(lblCidadeCobCorreios.getCaption());
                tbClientesEndereco.setRUA_COBRANCA(lblRuaCobCorreios.getCaption());
                tbClientesEndereco.setBAIRRO_COBRANCA(lblBairroCobCorreios.getCaption());
                //if (edComplementoCobCad.getValue().asString().trim().length() <= 0) {
                String complEnd = edComplementoCobCad.getValue().asString().trim() + ' ' + lblComplementoCobCorreios.getCaption().trim();
                if (complEnd.length() > 30) {
                    complEnd = complEnd.substring(0, 30);
                }
                tbClientesEndereco.setCOMPLEMENTO_COBRANCA(complEnd);
                //}
                tbClientesEndereco.getCOD_CID_COBRANCA().setValue(codCidCob);
                tbClientesEndereco.post();
                tbClientesEnderecoTemp.edit();
                tbClientesEnderecoTemp.setField("COD_CID_COBRANCA", codCidCob);
                tbClientesEnderecoTemp.post();
                atribuirCorBtnCob("A");
            } else {
                FrmConsultaCidadeA form = new FrmConsultaCidadeA();
                if (!tbClientesEnderecoTemp.getField("UF_COBRANCA").isEmpty()) {
                    form.setCidadeEnd(tbClientesEndereco.getUF_COBRANCA().asString(), lblCidadeCobCorreios.getCaption());
                }
                FormUtil.doShow(form, (EventListener) t -> {
                    if (form.isOk()) {
                        tbClientesEnderecoTemp.edit();
                        tbClientesEnderecoTemp.setField("COD_CID_COBRANCA", form.tbCidades.getCOD_CIDADES());
                        tbClientesEnderecoTemp.setField("CODCIDADEIBGE_COB", form.tbCidades.getCODCIDADEIBGE());
                        tbClientesEnderecoTemp.post();
                        lblUFCobCad.setCaption(lblUFCobCorreios.getCaption());
                        tbClientesEndereco.edit();
                        tbClientesEndereco.setUF_COBRANCA(lblUFCobCorreios.getCaption());
                        lblUFCobCad.setCaption(lblUFCobCorreios.getCaption());
                        tbClientesEndereco.setCIDADE_COB(form.tbCidades.getDESCRICAO().asString());
                        lblCidadeCobCad.setCaption(form.tbCidades.getDESCRICAO().asString());
                        tbClientesEndereco.setRUA_COBRANCA(lblRuaCobCorreios.getCaption());
                        lblRuaCobCad.setCaption(lblRuaCobCorreios.getCaption());
                        tbClientesEndereco.setBAIRRO_COBRANCA(lblBairroCobCorreios.getCaption());
                        lblBairroCobCad.setCaption(lblBairroCobCorreios.getCaption());
                        //if (edComplementoCobCad.getValue().asString().trim().length() <= 0) {
                        tbClientesEndereco.setCOMPLEMENTO_COBRANCA(lblComplementoCobCorreios.getCaption());
                        edComplementoCobCad.setValue(lblComplementoCobCorreios.getCaption());
                        // }
                        tbClientesEndereco.getCOD_CID_COBRANCA().setValue(tbClientesEnderecoTemp.getField("COD_CID_COBRANCA"));
                        tbClientesEndereco.getCODCIDADEIBGE_COB().setValue(tbClientesEnderecoTemp.getField("CODCIDADEIBGE_COB"));
                        tbClientesEndereco.post();
                        atribuirCorBtnCob("A");
                    }
                });
            }
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Encontrar Cadastro da Cidade. Motivo: ", ex);
        }
    }

    @Override
    public void hbCEPOnLineManterCobClick(final Event event) {
        try {
            tbClientesEndereco.edit();
            tbClientesEndereco.setUF_COBRANCA(tbClientesEndereco.getUF_COBRANCA_OLD().asString().trim());
            tbClientesEndereco.setCIDADE_COB(tbClientesEndereco.getCIDADE_COB_OLD().asString().trim());
            tbClientesEndereco.setRUA_COBRANCA(tbClientesEndereco.getRUA_COBRANCA_OLD().asString().trim());
            tbClientesEndereco.setBAIRRO_COBRANCA(tbClientesEndereco.getBAIRRO_COBRANCA_OLD().asString().trim());
            tbClientesEndereco.setCOMPLEMENTO_COBRANCA(tbClientesEndereco.getCOMPLEMENTO_COBRANCA_OLD().asString().trim());
            tbClientesEndereco.setCOD_CID_COBRANCA(tbClientesEndereco.getCOD_CID_COBRANCA_OLD().asInteger());
            tbClientesEndereco.setCODCIDADEIBGE_COB(tbClientesEndereco.getCODCIDADEIBGE_COB_OLD().asInteger());
            tbClientesEndereco.post();
            atribuirCorBtnCob("M");
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Manter Dados do Endereço de Cobrança. Motivo: ", ex);
        }
    }

    @Override
    public void hbCEPOnLineAceitarIEClick(final Event event) {
        try {
            if (!tbClientesEnderecoIe.isEmpty()) {
                tbClientesEnderecoIe.first();
                while (!tbClientesEnderecoIe.eof()) {
                    tbClientesEnderecoIe.edit();
                    tbClientesEnderecoIe.setMANTER("S");
                    tbClientesEnderecoIe.post();
                    manterAceitarEndIE();
                    tbClientesEnderecoIe.next();
                }
                tbClientesEnderecoIe.first();
            }
            atribuirCorBtnIE("A");
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Aceitar Dados do Endereço de Inscrição Estuadual. Motivo: ", ex);
        }
    }

    @Override
    public void hbCEPOnLineManterIEClick(final Event event) {
        try {
            if (!tbClientesEnderecoIe.isEmpty()) {
                tbClientesEnderecoIe.first();
                while (!tbClientesEnderecoIe.eof()) {
                    tbClientesEnderecoIe.edit();
                    tbClientesEnderecoIe.setMANTER("X");
                    tbClientesEnderecoIe.post();
                    manterAceitarEndIE();
                    tbClientesEnderecoIe.next();
                }
                tbClientesEnderecoIe.first();
            }
            atribuirCorBtnIE("M");
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Manter Dados do Endereço de Inscrição Estuadual. Motivo: ", ex);
        }
    }

    @Override
    public void tbClientesEnderecoIeAfterScroll(Event<Object> event) {
        boolean ret;
        try {
            if (!tbClientesEnderecoIe.isEmpty()) {
                if (!tbClientesEnderecoIeTemp.locate("COD_CLIENTE,INSCRICAO_ESTADUAL,COD_CIDADES", tbClientesEnderecoIe.getCOD_CLIENTE().asDecimal(), tbClientesEnderecoIe.getINSCRICAO_ESTADUAL().asString().equals("") ? null : tbClientesEnderecoIe.getINSCRICAO_ESTADUAL().asString(), tbClientesEnderecoIe.getCOD_CIDADES().asInteger())) {
                    if (tbClientesEnderecoIeTemp.getField("UF").asString().equals("99")) {
                        ret = rn.getDadosCEPOnLine(tbClientesEnderecoIe.getCOD_CLIENTE().asDecimal(), tbClientesEnderecoIe.getCEP().asString().trim().toUpperCase(), tbClientesEnderecoIe.getINSCRICAO_ESTADUAL().asString(), tbClientesEnderecoIe.getCOD_CIDADES().asInteger(), 4);
                        if (ret) {
                            validaCEPOnLine(tbClientesEnderecoIe.getCOD_CLIENTE().asDecimal(), tbClientesEnderecoIe.getCEP().asString().trim().toUpperCase(), 4);
                            verificaDivergencias(4);
                        }
                    }
                } else {
                    verificaDivergencias(4);
                }
            }
        } catch (Exception ex) {
            EmpresaUtil.showError("Falha ao Buscar Endereços IE", ex);
        }
    }

    private void manterAceitarEndIE() {
        if (!existeDivergenciaEndIE()) {
            return;
        }
        try {
            Value qtdeReg = new Value(0);
            if (!tbClientesEnderecoIe.isEmpty()) {
                if (tbClientesEnderecoIe.getMANTER().asString().equals("S")) {
                    String xUFCorreios = tbClientesEnderecoIeTemp.getField("UF").asString().trim();
                    String xCidadeCorreios = tbClientesEnderecoIeTemp.getField("CIDADE").asString().trim();
                    String xRuaCorreios = tbClientesEnderecoIeTemp.getField("RUA").asString().trim();
                    String xBairroCorreios = tbClientesEnderecoIeTemp.getField("BAIRRO").asString().trim();
                    String xComplementoCorreios = tbClientesEnderecoIeTemp.getField("COMPLEMENTO").asString().trim();
                    tbClientesEnderecoIe.edit();
                    tbClientesEnderecoIe.setCOD_CIDADES(rn.getCodCidade(xUFCorreios, xCidadeCorreios, tbClientesEnderecoIe.getCEP().asString(), tbClientesEndereco.getField("CODCIDADEIBGE_COB").asInteger(), qtdeReg));
                    tbClientesEnderecoIe.post();
                    if (qtdeReg.asInteger() > 0) {
                        tbClientesEnderecoIe.edit();
                        tbClientesEnderecoIe.setMANTER("X");
                        tbClientesEnderecoIe.setUF(xUFCorreios);
                        lblUFCadIE.setCaption(xUFCorreios);
                        tbClientesEnderecoIe.setCIDADE(xCidadeCorreios);
                        lblCidadeCadIE.setCaption(xCidadeCorreios);
                        tbClientesEnderecoIe.setRUA(xRuaCorreios);
                        lblRuaCadIE.setCaption(xRuaCorreios);
                        tbClientesEnderecoIe.setBAIRRO(xBairroCorreios);
                        lblBairroCadIE.setCaption(xBairroCorreios);
                        if (edComplementoCadIE.getValue().asString().trim().length() <= 0) {
                            tbClientesEnderecoIe.setCOMPLEMENTO(xComplementoCorreios);
                            edComplementoCadIE.setValue(xComplementoCorreios);
                        }
                        tbClientesEnderecoIe.post();
                    } else {
                        FrmConsultaCidadeA form = new FrmConsultaCidadeA();
                        if (!tbClientesEnderecoIe.getUF().isEmpty()) {
                            form.setCidadeEnd(tbClientesEnderecoIe.getUF().asString(), xCidadeCorreios);
                        }
                        FormUtil.doShow(form, (EventListener) t -> {
                            if (form.isOk()) {
                                tbClientesEnderecoIe.edit();
                                tbClientesEnderecoIe.setMANTER("X");
                                tbClientesEnderecoIe.setUF(xUFCorreios);
                                lblUFCadIE.setCaption(xUFCorreios);
                                tbClientesEnderecoIe.setCIDADE(xCidadeCorreios);
                                lblCidadeCadIE.setCaption(xCidadeCorreios);
                                tbClientesEnderecoIe.setRUA(xRuaCorreios);
                                lblRuaCadIE.setCaption(xRuaCorreios);
                                tbClientesEnderecoIe.setBAIRRO(xBairroCorreios);
                                lblBairroCadIE.setCaption(xBairroCorreios);
                                if (edComplementoCadIE.getValue().asString().trim().length() <= 0) {
                                    tbClientesEnderecoIe.setCOMPLEMENTO(xComplementoCorreios);
                                    edComplementoCadIE.setValue(xComplementoCorreios);
                                }
                                tbClientesEnderecoIe.post();
                            }
                        });
                    }
                } else {
                    String xUFCad = tbClientesEnderecoIe.getUF_OLD().asString().trim();
                    String xCidadeCad = tbClientesEnderecoIe.getCIDADE_OLD().asString().trim();
                    String xRuaCad = tbClientesEnderecoIe.getRUA_OLD().asString().trim();
                    String xBairroCad = tbClientesEnderecoIe.getBAIRRO_OLD().asString().trim();
                    String xComplementoCad = tbClientesEnderecoIe.getCOMPLEMENTO_OLD().asString().trim();
                    tbClientesEnderecoIe.edit();
                    tbClientesEnderecoIe.setCOD_CIDADES(tbClientesEnderecoIeTemp.getField("COD_CIDADES").asInteger());
                    tbClientesEnderecoIe.setMANTER("S");
                    tbClientesEnderecoIe.setUF(xUFCad);
                    lblUFCadIE.setCaption(xUFCad);
                    tbClientesEnderecoIe.setCIDADE(xCidadeCad);
                    lblCidadeCadIE.setCaption(xCidadeCad);
                    tbClientesEnderecoIe.setRUA(xRuaCad);
                    lblRuaCadIE.setCaption(xRuaCad);
                    tbClientesEnderecoIe.setBAIRRO(xBairroCad);
                    lblBairroCadIE.setCaption(xBairroCad);
                    tbClientesEnderecoIe.setCOMPLEMENTO(xComplementoCad);
                    edComplementoCadIE.setValue(xComplementoCad);
                    tbClientesEnderecoIe.post();
                }
                verificaDivergencias(4);
            }
        } catch (Exception ex) {
            EmpresaUtil.showError("Falha ao Manter Endereço IE", ex);
        }
    }

    @Override
    public void grdEndIEmanterAceitarEndIE(Event<Object> event) {
        if (!existeDivergenciaEndIE()) {
            EmpresaUtil.showMessage("Atenção", "Nenhuma Divergencia encontrada entre o Endereço Cadastrado e os Dados do Correio!");
            return;
        }
        try {
            Integer xCountAceitou = 0;
            manterAceitarEndIE();
            verificaDivergencias(4);
            if (!tbClientesEnderecoIe.isEmpty()) {
                tbClientesEnderecoIe.first();
                while (!tbClientesEnderecoIe.eof()) {
                    if (tbClientesEnderecoIe.getMANTER().asString().equals("X")) {
                        xCountAceitou++;
                    }
                    tbClientesEnderecoIe.next();
                }
                tbClientesEnderecoIe.first();
            }
            if (xCountAceitou.equals(tbClientesEnderecoIe.count())) {
                atribuirCorBtnIE("A");
            } else {
                atribuirCorBtnIE("M");
            }
        } catch (Exception ex) {
            EmpresaUtil.showError("Falha ao Manter Endereço IE", ex);
        }
    }

    @Override
    public void chkAceitarMudancaResCheck(final Event<Object> event) {
        if (chkAceitarMudancaRes.isChecked()) {
            btnSalvar.setEnabled(true);
            hbCEPOnLineAceitarResClick(null);
        } else {
            btnSalvar.setEnabled(false);
            hbCEPOnLineManterResClick(null);
        }
    }

    @Override
    public void chkAceitarMudancaComCheck(final Event<Object> event) {
        if (chkAceitarMudancaCom.isChecked()) {
            btnSalvar.setEnabled(true);
            hbCEPOnLineAceitarComClick(null);
        } else {
            btnSalvar.setEnabled(false);
            hbCEPOnLineManterComClick(null);
        }
    }

    @Override
    public void chkAceitarMudancaCobrCheck(final Event<Object> event) {
        if (chkAceitarMudancaCobr.isChecked()) {
            btnSalvar.setEnabled(true);
            hbCEPOnLineAceitarCobClick(null);
        } else {
            btnSalvar.setEnabled(false);
            hbCEPOnLineManterCobClick(null);
        }
    }
}
