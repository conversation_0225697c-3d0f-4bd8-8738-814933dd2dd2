package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.data.DataException;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;
import lombok.Getter;
import java.util.List;

public class FrmBloquearDesbloquearItemCompraA extends FrmBloquearDesbloquearItemCompraW {

    private static final long serialVersionUID = 20130827081850L;

    private final String codItem;

    private final long codFornecedor;

    private final boolean itemBloqueadoParaCompra;

    @Getter
    private boolean fechadoAoSalvar = false;

    FrmBloquearDesbloquearItemCompraA (
            boolean itemBloqueadoParaCompra
            ,String codItem
            ,long codFornecedor
    ) {
        this.itemBloqueadoParaCompra = itemBloqueadoParaCompra;
        if (this.itemBloqueadoParaCompra) {
            this.setCaption("Desbloquear item para compra");
        } else {
            this.setCaption("Bloquear item para compra");
        }
        this.codItem = codItem;
        this.codFornecedor = codFornecedor;
        this.adicionarEventoOnClickAColunaSELDaGrnEmpresas();
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.memoObservacao.setMaxlength(
                255
        );
        String descricaoItem = this.getDescricaoItem(
                this.codItem
        );
        this.lblItem.setCaption(
                "Item: "
                        + descricaoItem
                        + " ["
                        + this.codItem
                        + "]"
        );
        String nomeFornecedor = this.getNomeFornecedor(
                this.codFornecedor
        );
        this.lblFornecedor.setCaption(
                "Fornecedor: "
                        + nomeFornecedor
                        + " ["
                        + this.codFornecedor
                        + "]"
        );
        this.memoObservacao.setFocus();
        String loginUsuarioLogado = EmpresaUtil.getUserLogged();
        this.carregarGrdEmpresas(
                loginUsuarioLogado
                ,this.codItem
                ,this.codFornecedor
        );
    }

    private String getNomeFornecedor(
            long codFornecedor
    ) {
        String retFuncao = null;
        try {
            retFuncao = this.rn.getNomeFornecedor(
                    codFornecedor
            );
        } catch (
                DataException dataException
        ) {
            String mensagem = (
                    "Erro ao obter o nome do fornecedor \""
                            + codFornecedor
                            + "\""
            );
            EmpresaUtil.showError(
                    mensagem
                    ,dataException
            );
        }
        return retFuncao;
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        this.close();
    }

    @Override
    public void btnSalvarClick(Event<Object> event) {
        //region Obriga o preenchimento do campo Observação
        String observacao = this.memoObservacao.getValue().asString().trim();
        if (observacao.isEmpty()) {
            String mensagem = (
                    "O campo \""
                            + this.lblObservacao.getCaption()
                            + "\" deve ser preenchido."
            );
            Dialog.create()
                    .showNotificationInfo(
                            mensagem
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.memoObservacao      // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            this.memoObservacao.setValue(
                    observacao
            );
            this.memoObservacao.setFocus();
            return;
        }
        //endregion
        //region Obriga selecionar alguma empresa na grade
        List<Long> listaDeEmpresasSelecionadas = this.getEmpresasSelecionadasGrdEmpresas();
        if ((listaDeEmpresasSelecionadas == null)
                || (listaDeEmpresasSelecionadas.isEmpty())) {
            Dialog.create()
                    .showNotificationInfo(
                            "Selecione alguma empresa."
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.grdEmpresas         // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            return;
        }
        //endregion
        String bloquearBDesbloquearD = (this.itemBloqueadoParaCompra ? "D" : "B");
        String loginUsuarioLogado = EmpresaUtil.getUserLogged();
        String bloqueouDesbloqueou = this.bloquearDesbloquearItemCompraParaTodasEmpresasSelecionadas(
                this.codItem
                ,this.codFornecedor
                ,bloquearBDesbloquearD
                ,observacao
                ,loginUsuarioLogado
        );
        if (!bloqueouDesbloqueou.equals("S")) {
            EmpresaUtil.showInformationMessage(
                    bloqueouDesbloqueou
            );
            return;
        }
        this.fechadoAoSalvar = true;
        this.close();
    }

    private void carregarGrdEmpresas(
            String loginUsuario
            ,String codItem
            ,long codFornecedor
    ) {
        try {
            this.rn.carregarGrdEmpresas(
                    loginUsuario
                    ,codItem
                    ,codFornecedor
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao carregar a grade de empresas"
                    ,dataException
            );
        }
    }

    private void marcarDesmarcarGrdEmpresas() {
        try {
            this.rn.marcarDesmarcarGrdEmpresas();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao marcar/desmarcar a grade de empresas"
                    ,dataException
            );
        }
    }

    private void grdEmpresasSELClick() {
        this.marcarDesmarcarGrdEmpresas();
    }

    private void adicionarEventoOnClickAColunaSELDaGrnEmpresas() {
        this.grdEmpresas.getColumns().forEach(coluna -> {
            if (coluna.getFieldName().equals("SEL")) {
                coluna.addEventListener("onClick",
                        (Event<Object> event) -> {
                            this.grdEmpresasSELClick();
                            processarFlow(
                                    "FrmBloquearDesbloquearItemCompra"
                                    ,"SEL"
                                    ,"OnClick"
                            );
                        });
            }
        });
    }

    private void definirValorColunaSelTodosRegistrosGrdEmpresas(
            char valorSN
    ) {
        try {
            long codEmpresaComFoco = this.tbItensCustosCompraBloq.getCOD_EMPRESA().asLong();
            this.tbItensCustosCompraBloq.disableControls();
            try {
                this.tbItensCustosCompraBloq.first();
                while (Boolean.FALSE.equals(this.tbItensCustosCompraBloq.eof())) {
                    this.tbItensCustosCompraBloq.edit();
                    if (valorSN == 'S') {
                        this.tbItensCustosCompraBloq.setSEL(
                                "S"
                        );
                    } else {
                        this.tbItensCustosCompraBloq.setSEL(
                                "N"
                        );
                    }
                    this.tbItensCustosCompraBloq.post();
                    this.tbItensCustosCompraBloq.next();
                }
                this.tbItensCustosCompraBloq.first();
                while (Boolean.FALSE.equals(this.tbItensCustosCompraBloq.eof())) {
                    long codEmpresaLoop = this.tbItensCustosCompraBloq.getCOD_EMPRESA().asLong();
                    if (codEmpresaComFoco == codEmpresaLoop) {
                        break;
                    }
                    this.tbItensCustosCompraBloq.next();
                }
            } finally {
                this.tbItensCustosCompraBloq.enableControls();
            }
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao definir o valor da coluna SEL para todos os registros da grade de empresas"
                    ,dataException
            );
        }
    }

    @Override
    public void mmSelecionarTodosOsRegistrosGrdEmpresasClick(Event<Object> event) {
        this.definirValorColunaSelTodosRegistrosGrdEmpresas(
                'S'
        );
    }

    @Override
    public void mmSelecionarNenhumRegistroGrdEmpresasClick(Event<Object> event) {
        this.definirValorColunaSelTodosRegistrosGrdEmpresas(
                'N'
        );
    }

    private List<Long> getEmpresasSelecionadasGrdEmpresas() {
        List<Long> retFuncao = null;
        try {
            retFuncao = this.rn.getEmpresasSelecionadasGrdEmpresas();
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao obter a lista de empresas selecionadas na grade"
                    ,dataException
            );
        }
        return retFuncao;
    }

    private String bloquearDesbloquearItemCompraParaTodasEmpresasSelecionadas (
            String codItem
            ,long codFornecedor
            ,String bloquearBDesbloquearD
            ,String observacao
            ,String loginUsuario
    ) {
        String retFuncao = null;
        try {
            retFuncao = this.rn.bloquearDesbloquearItemCompraParaTodasEmpresasSelecionadas(
                    codItem
                    ,codFornecedor
                    ,bloquearBDesbloquearD
                    ,observacao
                    ,loginUsuario
            );
        } catch (
                DataException dataException
        ) {
            EmpresaUtil.showError(
                    "Erro ao bloquear/desbloquear o item de compra para todas as empresas selecionadas"
                    ,dataException
            );
        }
        return retFuncao;
    }

    private String getDescricaoItem(
            String codItem
    ) {
        String retFuncao = null;
        try {
            retFuncao = this.rn.getDescricaoItem(
                    codItem
            );
        } catch (
                DataException dataException
        ) {
            String mensagem = (
                    "Erro ao obter a descrição do item \""
                            + codItem
                            + "\""
            );
            EmpresaUtil.showError(
                    mensagem
                    ,dataException
            );
        }
        return retFuncao;
    }

}
