package freedom.bytecode.form;
import freedom.bytecode.form.wizard.*;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalUtils;
import freedom.util.EmpresaUtil;
import lombok.Getter;
import lombok.Setter;
import freedom.client.event.*;
import freedom.data.DataException;

@Getter
@Setter
public class FrmAssinaturaDigitalSignatarioA extends FrmAssinaturaDigitalSignatarioW {
    private static final long serialVersionUID = 20130827081850L;

    boolean ok=false;
    CrmAssinaturaDigitalUtils.EnTipoEnvio tipoEnvio;

    String getNome(){
        return edNome.getValue().asString();
    }

    String getTelefone(){
        return edTelefone.getValue().asString();
    }

    String getEmail(){
        return edEmail.getValue().asString();
    }


    boolean carregarAssinaturaDigitalSignatario(String nome, String email, String telefone, CrmAssinaturaDigitalUtils.EnTipoEnvio tipoEnvio){
       edNome.setValue(nome);
       edTelefone.setValue(telefone);
       edEmail.setValue(email);
       setTipoEnvio(tipoEnvio);
       return true;
    }

    @Override
    public void btnVoltarClick(Event<Object> event) {
        close();
    }

    @Override
    public void btnAceitarClick(Event<Object> event) {
        try {
            if (CrmAssinaturaDigitalUtils.isSignatarioInvalido(getNome(), getEmail(), getTelefone(), getTipoEnvio())) {
                return;
            }
            setOk(true);
            close();
        } catch (DataException e) {
            EmpresaUtil.showError("Erro ao validar o dados do signatário.", e);
        }
    }

}
