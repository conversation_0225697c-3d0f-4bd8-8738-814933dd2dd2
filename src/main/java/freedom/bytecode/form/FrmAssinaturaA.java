package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.ImageUtil;
import freedom.util.ImgUtil;
import freedom.util.pkg.PkgCrmPartsRNA;

import java.awt.*;
import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.Date;

public class FrmAssinaturaA extends FrmAssinaturaW {

    private static final long serialVersionUID = 20130827081850L;
    private final PkgCrmPartsRNA pkCrmPartsRna = new PkgCrmPartsRNA();

    private byte[] imageBytes;
    private boolean ok = false;
    private int combustivelSaida = 0;
    private int combustivelEntrada = 0;
    private int kmSaida = 0;
    private int kmEntrada = 0;
    private  boolean validarKmCombustivel = false;
    private boolean clienteAguardaConcessionaria = false;
    private boolean validarDataContatoEntrega = false;

    public FrmAssinaturaA() {
        dataAssinatura.setValue(new Date());
        hboxMarca0.setAttribute("SCLASS_BASE", "hboxmarca0 hbbuttonleftradius");
        hboxMarca1.setAttribute("SCLASS_BASE", "hboxmarca1 hbbuttoncenterradius");
        hboxMarca2.setAttribute("SCLASS_BASE", "hboxmarca2 hbbuttoncenterradius");
        hboxMarca3.setAttribute("SCLASS_BASE", "hboxmarca3 hbbuttoncenterradius");
        hboxMarca4.setAttribute("SCLASS_BASE", "hboxmarca4 hbbuttonrightradius");
    }

    public void alterarNomeAssinatura(String nome){
        flabelNomeAssinatura.setCaption(nome);
    }

    public void esconderControleData(){
        vboxDadosData.setVisible(false);
    }

    public boolean isKmCombustivelValidos(){
        if (this.kmSaida < this.kmEntrada){
            //lblValidarKm.setVisible(true);
            Dialog.create()
                    .title("Atenção")
                    .message("O Km de saída deve ser maior que ou igual ao Km de Entrada.")
                            .showInformation((EventListener) -> {
                                edtKm.setFocus();
                                edtKm.setSelectionRange(0, this.edtKm.getValue().asString().length()+1);
                            });
            return false;
        }
        return true;
    }


    @Override
    public void btnAceitarClick(final Event event) {

        if (this.validarKmCombustivel && !isKmCombustivelValidos()){
            return;
        }

        signature.save();
    }

    @Override
    public void btnVoltarClick(final Event event) {
        ok = false;
        close();
    }

    @Override
    public void signatureSave(UploadEvent event) {
        try {
            imageBytes = ImageUtil.streamToByteArray(event.getStreamData());

            if (!ImgUtil.isImageValid(imageBytes)) {
                EmpresaUtil.showMessage("Atenção", "Assinatura obrigatória.");
                return;
            }

            if (dataAssinatura.getValue().isNull()) {
                EmpresaUtil.showMessage("Atenção", "Data assinatura obrigatória.");
                return;
            }

            ok = true;
            close();

        } catch (Exception ex) {
            EmpresaUtil.showError("Ocorreu um erro inesperado.", ex);
        }
    }

    public byte[] getImageBytes() {
        return imageBytes;
    }


    public boolean isOk() {
        return ok;
    }

    /**
     * Função torna visivel e permite alterar KM e combustivel e tornar visivel os campos KM e Combustivel
     * dessa forma pois nem sempre que o form for chamado será permitido alterar o combustivel
     * @param combustivelEntrada
     * @param kmEntrada
     * @param combustivelSaida
     * @param kmSaida
     * @param codEmpresa
     */
    public void PermitirAlterarKMCombustivel(int combustivelEntrada, int kmEntrada, int combustivelSaida, int kmSaida, Double codEmpresa){

        String obrigarKmCombustivelSaida = null;

        try {
            obrigarKmCombustivelSaida = pkCrmPartsRna.getParametro(codEmpresa, "CRM_PARM_FLUXO", "OBRIGAR_KM_COMBUST_SAIDA");
        } catch (DataException e) {
            obrigarKmCombustivelSaida = "N";
        }
        this.validarKmCombustivel = obrigarKmCombustivelSaida.equals("S");

        this.combustivelEntrada = combustivelEntrada;
        this.combustivelSaida = (combustivelSaida == 0) ? combustivelEntrada : combustivelSaida;
        this.kmEntrada = kmEntrada;
        this.kmSaida = (kmSaida == 0) ? kmEntrada : kmSaida;
        edtKm.setValue(this.kmSaida);
        vboxDadosKMCombustivel.setVisible(true);
        String KmFormatada = new DecimalFormat(",##0").format(this.kmEntrada);
        lblKmEntrada.setCaption("(" + KmFormatada + ")");
        carregaMarcadorCombustivel();
    };


    public Date getDataAssinatura() {
        return dataAssinatura.getValue().asDate();
    }


    public int getCombustivelSaida(){return combustivelSaida;}

    public int getKmSaida(){return kmSaida;}


    @Override
    public void btnDesfazerClick(final Event event) {
        signature.undo();
    }

    @Override
    public void btnLimparClick(final Event event) {
        signature.clear();
        imageBytes = null;
    }

    @Override
    public void hboxMarca0Click(final Event<Object> event) {
        marcadorCombustivel(0);
    }

    @Override
    public void hboxMarca1Click(final Event<Object> event) {
        marcadorCombustivel(1);
    }

    @Override
    public void hboxMarca2Click(final Event<Object> event) {
        marcadorCombustivel(2);
    }

    @Override
    public void hboxMarca3Click(final Event<Object> event) {
        marcadorCombustivel(3);
    }

    @Override
    public void hboxMarca4Click(final Event<Object> event) {
        marcadorCombustivel(4);
    }

    private void marcadorCombustivel(int marcaCombustivel) {
        //lblValidarCombustivel.setVisible(false);
        String colorDestaque = "#3a93f8";
        String colorDefault = "#e5e5e5";
        hboxMarca0.setColor(colorDefault);
        hboxMarca1.setColor(colorDefault);
        hboxMarca2.setColor(colorDefault);
        hboxMarca3.setColor(colorDefault);
        hboxMarca4.setColor(colorDefault);
        hboxMarcadorCombustivel.invalidate();
        switch (marcaCombustivel) {
            case 0:
                hboxMarca0.setColor(colorDestaque);
                combustivelSaida = 1;
                break;
            case 1:
                hboxMarca1.setColor(colorDestaque);
                combustivelSaida = 25;
                break;
            case 2:
                hboxMarca2.setColor(colorDestaque);
                combustivelSaida = 50;
                break;
            case 3:
                hboxMarca3.setColor(colorDestaque);
                combustivelSaida = 75;
                break;
            case 4:
                hboxMarca4.setColor(colorDestaque);
                combustivelSaida = 100;
                break;
        }
    }

    private void carregaMarcadorCombustivel() {
        if (this.combustivelSaida <= 19) {
            marcadorCombustivel(0);
        } else {
            if (this.combustivelSaida > 19 && combustivelSaida <= 39) {
                marcadorCombustivel(1);
            } else {
                if (this.combustivelSaida > 39 && combustivelSaida <= 59) {
                    marcadorCombustivel(2);
                } else {
                    if (this.combustivelSaida > 59 && combustivelSaida <= 79) {
                        marcadorCombustivel(3);
                    } else {
                        marcadorCombustivel(4);
                    }
                }
            }
        }
    }

    @Override
    public void edtKmChange(final Event<Object> event){
        //lblValidarKm.setVisible(false);
        this.kmSaida = edtKm.getValue().asInteger();
    }


}
