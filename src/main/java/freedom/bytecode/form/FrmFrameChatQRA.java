package freedom.bytecode.form;
import encrypt.criptografia.CryptDecryptCesar;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.form.wizard.*;
import freedom.bytecode.rn.wizard.*;
import freedom.util.EmpresaUtil;
import freedom.util.FRLogger;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import javax.servlet.http.HttpServletRequest;

public class FrmFrameChatQRA extends FrmFrameChatQRW {
    private static final long serialVersionUID = 20130827081850L;

    public boolean prepareChat(int idCelular) {
        try {
            if (this.rn.filtrarCadastroWhatsApp(idCelular)) {
                if (!isvalidCadastroWhatsap()){
                    return false;
                }

                String urlBase = this.tbCadastroWhatsapp.getURL_PAINEL_WEB().asString();


                if (urlBase.isEmpty()) {
                    EmpresaUtil.showWarningMessage("Não encontrou URL do Painel Web cadastrada no cadastro do whatsapp");
                    return false;
                }
                URL url = new URL(((HttpServletRequest) Executions.getCurrent().getNativeRequest()).getRequestURL().toString());
                if ((url.getProtocol().equals("https")
                        && (urlBase.contains("http:")))) {
                    EmpresaUtil.showWarningMessage("Para abrir o chat a URL "
                            + urlBase
                            + " precisa ser do tipo https. Devido à sua aplicação ser https (Seguro).");
                }
                String foneLoja = this.ajustaFone(this.tbCadastroWhatsapp.getCELULAR().asString().replaceAll("\\D",
                                ""),
                        "L");
                if (foneLoja.contains("Fone")) {
                    EmpresaUtil.showWarningMessage(foneLoja);
                    return false;
                }

                String urlAux = urlBase
                        + "/chatqr?"
                        + "fromNumber="
                        + foneLoja
                        ;
                this.frameChat.setUrl(urlAux);
                FRLogger.log("url="
                                + urlAux,
                        this.getClass());
            } else {
                EmpresaUtil.showWarningMessage("Não encontrou whatsapp cadastrado.");
                return false;
            }
        } catch (DataException | MalformedURLException ex) {
            EmpresaUtil.showError("Ocorreu um erro inesperado.",
                    ex);
            return false;
        }
        return true;
    }

    private boolean isvalidCadastroWhatsap() {
        if (!tbCadastroWhatsapp.getAPI_TIPO().asString().equals("Z-API")){
            EmpresaUtil.showInformationMessage("tipo API não é do tipo Z-API");
            return false;
        }
        if (tbCadastroWhatsapp.getZAPI_INSTANCE().asString().isEmpty()){
            EmpresaUtil.showInformationMessage("Instância Z-API é obrigatória");
            return false;
        }
        if (tbCadastroWhatsapp.getZAPI_INSTANCE_TOKEN().asString().isEmpty()){
            EmpresaUtil.showInformationMessage("Token Instância Z-API é obrigatória");
            return false;
        }
        if (tbCadastroWhatsapp.getZAPI_CLIENT_TOKEN().asString().isEmpty()){
            EmpresaUtil.showInformationMessage("Cliente Token Z-API é obrigatória");
            return false;
        }
        return true;
    }


    public String ajustaFone(String fone, String TP) {

        fone = fone.replaceAll("-", "");

        if (!fone.isEmpty()) {
            if (fone.length() <= 11 && fone.length() >= 10) {
                return "55" + fone;
            }
            if (fone.length() <= 13 && fone.length() >= 12) {
                return fone;
            }
        }
        return "Fone " + (TP.equals("C") ? "do cliente" : "da loja")
                + " {" + fone + "} não esta no formato esperado (DD)99999-9999 ou (+DI)(DD)99999-9999";
    }
}
