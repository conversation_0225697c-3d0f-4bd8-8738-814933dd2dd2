package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmConsultaSimplesEventoAbertoW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import lombok.Getter;

import static freedom.util.CastUtil.asString;

@Getter
public class FrmConsultaSimplesEventoAbertoA extends FrmConsultaSimplesEventoAbertoW {

    private static final long serialVersionUID = 20130827081850L;

    private boolean fechadoAoSelecionar = false;

    private long codEventoSelecionado = 0L;

    private long codEmpresaSelecionada = 0L;

    private boolean fechadoAoContinuar = false;

    public void filtrarEvento(long codEmpresa,
                              long codCliente,
                              String fone,
                              String email,
                              String respEvento) {
        int tipoFiltro = 0;
        String filtrarPor = "";
        if (codCliente > 1) {
            tipoFiltro = 3;
            filtrarPor = asString(codCliente);
        } else if (!fone.trim().isEmpty()) {
            tipoFiltro = 1;
            filtrarPor = fone;
        } else if (!email.trim().isEmpty()) {
            tipoFiltro = 2;
            filtrarPor = email;
        }
        this.filtrarEvento(tipoFiltro,
                filtrarPor,
                codEmpresa,
                respEvento);
    }

    @Override
    public void btnSelecionarClick(final Event<Object> event) {
        this.fechadoAoSelecionar = true;
        this.codEventoSelecionado = this.tbCrmpartsConsultaEvento.getCOD_EVENTO().asLong();
        this.codEmpresaSelecionada = this.tbCrmpartsConsultaEvento.getCOD_EMPRESA().asLong();
        this.close();
    }

    @Override
    public void btnContinuarClick(final Event<Object> event) {
        this.fechadoAoContinuar = true;
        this.close();
    }

    public boolean encontrouEventos() {
        return !this.tbCrmpartsConsultaEvento.isEmpty();
    }

    private void filtrarEvento(int tipoFiltro,
                               String filtrarPor,
                               long codEmpresa,
                               String usuarioRespEvent) {
        try {
            this.rn.filtrarEvento(tipoFiltro,
                    filtrarPor,
                    codEmpresa,
                    usuarioRespEvent);
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao filtrar evento",
                    dataException);
        }
    }

}
