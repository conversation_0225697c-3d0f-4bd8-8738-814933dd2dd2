package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.*;
import freedom.client.util.Dialog;
import freedom.client.util.IDialog;
import freedom.util.*;
import lombok.Getter;
import org.json.JSONObject;

@Getter
public class FrmPesquisaCEPOnlineA extends FrmPesquisaCEPOnlineW {

    private static final long serialVersionUID = 20130827081850L;

    private boolean fechadoAoAceitar = false;

    @Override
    public void btnVoltarFrmPesquisaCEPOnlineClick(Event<Object> event) {
        this.close();
    }

    @Override
    public void btnAceitarFrmPesquisaCEPOnlineClick(Event<Object> event) {
        String mensagem = (
                "Deseja aceitar os dados do CEP \""
                        + StringUtil.removerCaracteresNaoNumericos(
                                this.lblValorCEP.getCaption()
                )
                        + "\"?"
        );
        Dialog.create()
                .title("Confirmação")
                .message(mensagem)
                .confirmSimNao((String dialogResult) -> {
                    if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                        this.fechadoAoAceitar = true;
                        this.close();
                    }
                });
    }

    @Override
    public void FFormCreate(Event<Object> event) {
        this.edtCEP.setFocus();
        this.edtCEP.setSelectionRange(
                0
                ,(this.edtCEP.getValue().asString().length() + 1)
        );
        this.hBoxDadosDoCEPCabecalho.setColor(
                Constantes.EEE9E9
        );
        this.hBoxEnderecosLogradouro.setColor(
                Constantes.EEE9E9
        );
        this.hBoxEnderecosUnidade.setColor(
                Constantes.EEE9E9
        );
        this.hBoxEnderecosLocalidade.setColor(
                Constantes.EEE9E9
        );
        this.hBoxEnderecosEstado.setColor(
                Constantes.EEE9E9
        );
        this.hBoxEnderecosIBGE.setColor(
                Constantes.EEE9E9
        );
        this.hBoxEnderecosDDD.setColor(
                Constantes.EEE9E9
        );
        TestUtil.setIdTestComponents(
                this
        );
    }

    public void pesquisarCEP() {
        String cepDigitado = this.edtCEP.getValue().asString().trim();
        String cepSomenteNumeros = StringUtil.removerCaracteresNaoNumericos(
                cepDigitado
        );
        this.edtCEP.setValue(
                cepSomenteNumeros
        );
        JSONObject jsonObjectDoCEPPesquisado = ViaCEP.getCEPOnLine(
                cepSomenteNumeros
        );
        if (jsonObjectDoCEPPesquisado.has("erro")) {
            String erro = jsonObjectDoCEPPesquisado.getString(
                    "erro"
            );
            Dialog.create()
                    .showNotificationInfo(
                            erro
                            ,Constantes.BEFORE_CENTER // A mensagem aparece acima da âncora, alinhada ao centro.
                            ,10000                    // Tempo em milissegundos para exibir a mensagem
                            ,this.edtCEP              // Nome do componente do formulário em frente ao qual a mensagem será exibida
                            ,true                     // Habilita o botão X que fecha a mensagem antes do término do tempo de exibição da mensagem
                    );
            return;
        }
        //region CEP
        String cepDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("cep").trim()
        );
        this.lblValorCEP.setCaption(
                cepDoCEPPesquisado
        );
        this.lblValorCEP.setVisible(
                true
        );
        //endregion
        //region Logradouro
        String logradouroDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("logradouro").trim()
        );
        this.lblValorLogradouro.setCaption(
                logradouroDoCEPPesquisado
        );
        this.lblValorLogradouro.setVisible(
                true
        );
        //endregion
        //region Complemento
        String complementoDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("complemento").trim()
        );
        this.lblValorComplemento.setCaption(
                complementoDoCEPPesquisado
        );
        this.lblValorComplemento.setVisible(
                true
        );
        //endregion
        //region Unidade
        String unidadeDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("unidade").trim()
        );
        this.lblValorUnidade.setCaption(
                unidadeDoCEPPesquisado
        );
        this.lblValorUnidade.setVisible(
                true
        );
        //endregion
        //region Bairro
        String bairroDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("bairro").trim()
        );
        this.lblValorBairro.setCaption(
                bairroDoCEPPesquisado
        );
        this.lblValorBairro.setVisible(
                true
        );
        //endregion
        //region Localidade
        String localidadeDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("localidade").trim()
        );
        this.lblValorLocalidade.setCaption(
                localidadeDoCEPPesquisado
        );
        this.lblValorLocalidade.setVisible(
                true
        );
        //endregion
        //region UF
        String ufDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("uf").trim()
        );
        this.lblValorUF.setCaption(
                ufDoCEPPesquisado
        );
        this.lblValorUF.setVisible(
                true
        );
        //endregion
        //region Estado
        String estadoDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("estado").trim()
        );
        this.lblValorEstado.setCaption(
                estadoDoCEPPesquisado
        );
        this.lblValorEstado.setVisible(
                true
        );
        //endregion
        //region Região
        String regiaoDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("regiao").trim()
        );
        this.lblValorRegiao.setCaption(
                regiaoDoCEPPesquisado
        );
        this.lblValorRegiao.setVisible(
                true
        );
        //endregion
        //region IBGE
        String ibgeDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("ibge").trim()
        );
        this.lblValorIBGE.setCaption(
                ibgeDoCEPPesquisado
        );
        this.lblValorIBGE.setVisible(
                true
        );
        //endregion
        //region GIA
        String giaDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("gia").trim()
        );
        this.lblValorGIA.setCaption(
                giaDoCEPPesquisado
        );
        this.lblValorGIA.setVisible(
                true
        );
        //endregion
        //region DDD
        String dddDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("ddd").trim()
        );
        this.lblValorDDD.setCaption(
                dddDoCEPPesquisado
        );
        this.lblValorDDD.setVisible(
                true
        );
        //endregion
        //region SIAFI
        String siafiDoCEPPesquisado = XmlUtil.removeAcentos(
                jsonObjectDoCEPPesquisado.getString("siafi").trim()
        );
        this.lblValorSIAFI.setCaption(
                siafiDoCEPPesquisado
        );
        this.lblValorSIAFI.setVisible(
                true
        );
        //endregion
        String hintEdtCEP = (
                "CEP"
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "https://viacep.com.br/ws/"
                        + cepDigitado
                        + "/json/"
        );
        this.edtCEP.setHint(
                hintEdtCEP
        );
        this.btnAceitar.setVisible(
                true
        );
        this.edtCEP.setFocus();
        this.edtCEP.setSelectionRange(
                0
                ,(this.edtCEP.getValue().asString().length() + 1)
        );
    }

    @Override
    public void btnPesquisarCEPFrmPesquisaCEPOnlineClick(Event<Object> event) {
        this.pesquisarCEP();
    }

    @Override
    public void edtCEPFrmPesquisaCEPOnlineEnter(Event<Object> event) {
        this.pesquisarCEP();
    }

}
