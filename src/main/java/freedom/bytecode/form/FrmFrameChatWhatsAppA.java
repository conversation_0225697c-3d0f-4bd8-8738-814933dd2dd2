package freedom.bytecode.form;

import encrypt.criptografia.CryptDecryptCesar;
import freedom.bytecode.form.wizard.FrmFrameChatWhatsAppW;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.FRLogger;
import freedom.util.MensagemWhatsAppZenvia;
import freedom.util.WorkListFactory;
import org.zkoss.zk.ui.Executions;

import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;

public class FrmFrameChatWhatsAppA extends FrmFrameChatWhatsAppW {

    private static final long serialVersionUID = 20130827081850L;

    private final IWorkList wl = WorkListFactory.getInstance();

    private final String monitorAtivo = this.wl.sysget("SQL_MONITOR_ATIVO").asString();

    private final MensagemWhatsAppZenvia zap = new MensagemWhatsAppZenvia(this.monitorAtivo);

    public boolean prepareChat(int codEmpresa,
                               double codEvento,
                               String foneCliente,
                               String usuario) {
        try {
            if (this.rn.filtrarCadastroWhatsApp(codEmpresa)) {
                String urlBase = this.tbConsultaNumberWhats.getURL_PAINEL_WEB().asString();
                if (urlBase.isEmpty()) {
                    EmpresaUtil.showWarningMessage("Não encontrou URL do Painel Web cadastrada no cadastro do whatsapp");
                    return false;
                }
                if (foneCliente.isEmpty()) {
                    EmpresaUtil.showWarningMessage("Não à um fone informado para contato por WhatsApp.");
                    return false;
                }
                URL url = new URL(((HttpServletRequest) Executions.getCurrent().getNativeRequest()).getRequestURL().toString());
                if ((url.getProtocol().equals("https")
                        && (urlBase.contains("http:")))) {
                    EmpresaUtil.showWarningMessage("Para abrir o chat a URL "
                            + urlBase
                            + " precisa ser do tipo https. Devido à sua aplicação ser https (Seguro).");
                }
                String foneLoja = this.zap.ajustaFone(this.tbConsultaNumberWhats.getCELULAR().asString().replaceAll("\\D",
                                ""),
                        "L");
                if (foneLoja.contains("Fone")) {
                    EmpresaUtil.showWarningMessage(foneLoja);
                    return false;
                }
                foneCliente = this.zap.ajustaFone(foneCliente.replaceAll("\\D",
                                ""),
                        "C");
                if (foneCliente.contains("Fone")) {
                    EmpresaUtil.showWarningMessage(foneCliente);
                    return false;
                }
                this.rn.filtrarWhatsAtendimento(codEmpresa,
                        codEvento);
                boolean ehCrmServiceOuCrmParts = false;

                if (!tbEventos.getSTATUS().asString().equals("P")) {
                    ehCrmServiceOuCrmParts = ((EmpresaUtil.isCrmService())
                            || (EmpresaUtil.isCrmParts()));
                }

                String chatToken = this.tbWhatsappAtendimento.getLINK().asString();
                if (chatToken.isEmpty()) {
                    chatToken = this.zap.gerarHashChat(foneLoja,
                            foneCliente);
                }
                String key = this.rn.getKeyHashCode();
                FRLogger.log("freedom.bytecode.form.FrmFrameChatWhatsAppA.prepareChat()",
                        this.getClass());
                CryptDecryptCesar cryptDecryptCesar = new CryptDecryptCesar(key);
                String usuarioLogado = EmpresaUtil.getUserLogged();
                String senha = EmpresaUtil.getPwdLogged();
                String hashCode = usuarioLogado
                        + "||"
                        + senha;
                hashCode = cryptDecryptCesar.criptografa(hashCode);
                String hashCode64 = java.net.URLEncoder.encode(hashCode);
                FRLogger.log("hashCode="
                                + hashCode,
                        this.getClass());
                FRLogger.log("hashCode64="
                                + hashCode64,
                        this.getClass());
                this.rn.salvarAtendimentoWhats(codEmpresa,
                        codEvento,
                        foneCliente,
                        chatToken,
                        usuario);
                String urlAux = urlBase
                        + "/api?frmConversaLeadzapPluginZenvia&tokenchat="
                        + chatToken
                        + "&codevento="
                        + (int) codEvento
                        + "&codempresa="
                        + codEmpresa
                        + "&hashcode="
                        + hashCode64 + "&toNumber=" + foneCliente
                        + "&disablecomands=" + ehCrmServiceOuCrmParts;
                this.frameChat.setUrl(urlAux);
                FRLogger.log("url="
                                + urlAux,
                        this.getClass());
            } else {
                EmpresaUtil.showWarningMessage("Não encontrou whatsapp cadastrado.");
                return false;
            }
        } catch (DataException | MalformedURLException ex) {
            EmpresaUtil.showError("Ocorreu um erro inesperado.",
                    ex);
            return false;
        }
        return true;
    }

}