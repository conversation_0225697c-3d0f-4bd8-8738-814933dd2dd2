package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmDadosSintegraW;
import freedom.client.event.Event;
import freedom.client.util.Dialog;
import freedom.client.util.FreedomUtilities;
import freedom.client.util.IDialog;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.*;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;
import org.zkoss.zk.ui.HtmlBasedComponent;
import org.zkoss.zul.Tabbox;

import java.text.SimpleDateFormat;

public class FrmDadosSintegraA extends FrmDadosSintegraW {

    private static final long serialVersionUID = 20130827081850L;

    public static final String CONSULTA_DE_DADOS_RETORNOU = "Consulta de dados retornou ";

    public static final String INFORMACAO = "Informação";

    private final IWorkList wl = WorkListFactory.getInstance();

    private String cpfCnpj;

    private String ie;

    private String uf;

    private boolean consultandoApi = false;

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private double codEmpresa = EmpresaUtil.getCodEmpresaUserLogged();

    private boolean usaApiConsulta = false;

    private double idConsultaNbs;

    @Getter
    private boolean atualizouCadastro = false;

    private boolean cadastroJaAtualizado = false;

    private String schemaAtual;

    private static final String DATA_INVALIDA_BEFORE = "01/01/1900";

    public FrmDadosSintegraA() {
        ((Tabbox) this.pgcDados.getImpl()).getTabs().getChildren().forEach(t -> ((HtmlBasedComponent) t).setStyle("font-size: 15px"));
        lblDadosReceitaDescricao.setCaption("");
        lblDadosSintegraDescricao.setCaption("");
    }

    @Override
    public void FFormCreate(final Event<Object> event) {
        btnConsultaAPIIntegracao.setVisible(this.usaApiConsulta);
        /*
         Validar se o cliente precisa de verificação de dados API - "Data Validade de Cadastro" - "Dias antecipar"
         e os dados consultados na API são recentes e o Endereço do evento está válido  "ATIVO"
         Se nao precisar consultar novamente, nem mostra o botão para Sincronizar.
         Se vem de um Evento de Pendência - Processar a pendência e seguir o fluxo
        */
        if (this.cadastroJaAtualizado) {
            btnConsultaAPIIntegracao.setHint("Refazer/Forçar consulta de dados no pluguin-nbs-Consulta - Acesso: K0619");
        } else {
            btnConsultaAPIIntegracao.setHint("Fazer consulta de dados no pluguin-nbs-Consulta - Acesso: K0618");
        }
        btnConsultaAPIIntegracao.setEnabled(true);
        abreDadosIntegracao();
        if (this.cpfCnpj.length() == 14) {
            lblCpfCnpj.setCaption(StringUtil.mascararCNPJ(this.cpfCnpj));
        } else {
            lblCpfCnpj.setCaption(StringUtil.mascararCPF(this.cpfCnpj));
        }
        lblInscricao.setCaption(this.ie);
        lblInscricao.setVisible(StringUtils.isNotBlank(this.ie));
        tbsSintegra.setVisible(StringUtils.isNotBlank(this.ie));
    }

    private boolean usaPluginConsultaNBS() {
        return rn.usaPluginConsultaNBS(this.codEmpresa);
    }

    @Override
    public void btnVoltarClick(final Event<Object> event) {
        close();
    }

    public void abreDadosConsultados(
            String cpfCnpj
            ,String inscricaoEstadual
            ,String uf
    ) {
        if (cpfCnpj.length() == 14) {
            this.cpfCnpj = cpfCnpj;
        } else if (cpfCnpj.length() == 11) {
            this.cpfCnpj = cpfCnpj;
        } else if (cpfCnpj.length() > 11) {
            this.cpfCnpj = StringUtils.leftPad(cpfCnpj, 14, '0');
        } else {
            this.cpfCnpj = StringUtils.leftPad(cpfCnpj, 11, '0');
        }
        this.ie = inscricaoEstadual;
        this.uf = uf;
        this.usaApiConsulta = this.usaPluginConsultaNBS();
        this.cadastroJaAtualizado = this.rn.cadastroIntegracaoJaAtualizado(
                this.codEmpresa
                ,Double.parseDouble(this.cpfCnpj)
                ,this.ie
        );
    }

    private void abreDadosIntegracao() {
        try {
            this.rn.openDadosConsultaApi(
                    this.cpfCnpj
                    ,this.ie
            );
            this.exibeStatusCadastro();
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao abrir dados de integração",
                    dataException);
        }
    }

    private void exibeStatusCadastro() {
        vBoxStatusCadastro.setVisible(false);
        if (!this.usaApiConsulta) {
            return;
        }

        vBoxStatusCadastro.setVisible(true);
        hBoxSituacaoCadReceitaFederal.setVisible(true);
        hBoxSituacaoCadSintegra.setVisible(true);

        lblSintegraMultiIe.setVisible(false);
        lblSituacaoCadReceitaFederal.setVisible(true);
        lblCadastroIrregular.setVisible(false);

        hBoxSituacaoCadSintegra.setVisible(false);
        lblSituacaoSintegra.setCaption(" -- ");
        lblSituacaoSintegra.setHint("");
        lblSituacaoReceitaFederal.setCaption(" -- ");
        lblSituacaoReceitaFederal.setHint("");
        lblSintegraMultiIe.setHint("");
        lblSituacaoCadSintegra.setHint("");
        Value aRfbSituacao = new Value(null);
        Value aRfbSituacaoMensagem = new Value(null);
        Value aSintegraSituacao = new Value(null);
        Value aSintegraMensagem = new Value(null);
        Value aSintegraCadastroIsento = new Value(null);
        Value aSintegraMultiplasIe = new Value(null);
        try {
            String tipoPessoa = this.cpfCnpj.length() > 11 ? "J" : "F";
            String respfunc = rn.infoSituacaoCadastralIntegracao(this.codEmpresa, Double.parseDouble(this.cpfCnpj), tipoPessoa, this.ie, aRfbSituacao, aRfbSituacaoMensagem, aSintegraSituacao, aSintegraMensagem, aSintegraCadastroIsento, aSintegraMultiplasIe);
            if (!respfunc.equals("S")) {
                lblCadastroIrregular.setVisible(true);
            }

            if (StringUtils.isNotBlank(this.ie)) {
                hBoxSituacaoCadSintegra.setVisible(true);
            }
            lblSituacaoReceitaFederal.setHint(aRfbSituacaoMensagem.asString());
            lblSituacaoSintegra.setHint(aSintegraMensagem.asString());

            if (StringUtils.isNotBlank(aSintegraMultiplasIe.asString())) {
                boolean hBoxSituacaoCadSintegraNotVisible = !this.hBoxSituacaoCadSintegra.isVisible();
                if (hBoxSituacaoCadSintegraNotVisible) {
                    hBoxSituacaoCadSintegra.setVisible(true);
                }
                lblSintegraMultiIe.setVisible(true);
                lblSintegraMultiIe.setHint(aSintegraMultiplasIe.asString());
                lblSituacaoCadSintegra.setHint("Multiplas Ie\n" + aSintegraMultiplasIe.asString());
            }

            lblSituacaoSintegra.setCaption(aSintegraSituacao.asString());
            lblSituacaoReceitaFederal.setCaption(aRfbSituacao.asString());
            vBoxStatusCadastro.invalidate();

        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao exibir status cadastro",
                    dataException);
        }
    }

    private String consultaDadosApiPessoaFisica(String urlConsultaNbs, String cpf, String nascimento, String uf, String inscricaoEstadual) throws Exception {
        String retFuncao;
        atualizaSchemma();

        StringBuilder url = new StringBuilder();
        url.append(urlConsultaNbs);
        url.append("/plugin-nbs-consulta/api/find");
        url.append("?empresa=").append((int) this.codEmpresa);
        url.append("&usuario=").append(this.usuarioLogado.toLowerCase());
        url.append("&sistema=").append(this.wl.sysget("APPLICATION_NAME").asString());
        url.append("&doc=").append(cpf);
        if ((nascimento != null) && (!nascimento.isEmpty())) {
            url.append("&nascimento=").append(nascimento);
        }
        if (StringUtils.isNotBlank(inscricaoEstadual)) {
            if (StringUtils.isNotBlank(uf)) {
                url.append("&uf=").append(uf);
            }
            url.append("&ie=").append(inscricaoEstadual);
        }
        this.idConsultaNbs = 0.0;
        IntegracaoPluginNbsConsulta plugin = new IntegracaoPluginNbsConsulta();
        this.idConsultaNbs = plugin.consultaCadastro(urlConsultaNbs, url.toString(), this.usuarioLogado, this.schemaAtual);

        retFuncao = rn.validarRetornoApiConsulta(this.idConsultaNbs);
        edtLog.setValue(edtLog.getValue().asString() + "[" + this.idConsultaNbs + "]");
        return retFuncao;
    }

    private void atualizaSchemma() {
        if (StringUtils.isBlank(this.schemaAtual)) {
            try {
                this.schemaAtual = (rn.getSchemaAtual()).toUpperCase();
            } catch (DataException e) {
                EmpresaUtil.showInformationMessage("Não carregou o schemma do usuário." + System.lineSeparator() + "Motivo: " + e.getMessage());
            }
        }
    }

    public String consultaDadosApiPessoaJuridica(String urlConsultaNbs, String cnpj, String uf, String inscricaoEstadual) throws Exception {
        String retFuncao;
        atualizaSchemma();

        this.idConsultaNbs = 0.0;
        IntegracaoPluginNbsConsulta plugin = new IntegracaoPluginNbsConsulta();

        this.idConsultaNbs = plugin.consultaCadastro(urlConsultaNbs, getUrlConsultaDadosApiPessoaJuridica(urlConsultaNbs, cnpj, uf, inscricaoEstadual), this.usuarioLogado, this.schemaAtual);
        retFuncao = rn.validarRetornoApiConsulta(this.idConsultaNbs);
        if (retFuncao.contains("[refazer consulta]")){
            this.idConsultaNbs = 0.0;
            this.idConsultaNbs = plugin.consultaCadastro(urlConsultaNbs, getUrlConsultaDadosApiPessoaJuridica(urlConsultaNbs, cnpj, uf, ""), this.usuarioLogado, this.schemaAtual);
            retFuncao = rn.validarRetornoApiConsulta(this.idConsultaNbs);
        }
        edtLog.setValue(edtLog.getValue().asString() + "[" + this.idConsultaNbs + "]");
        return retFuncao;
    }

    private String getUrlConsultaDadosApiPessoaJuridica(String urlConsultaNbs, String cnpj, String uf, String inscricaoEstadual) {
        StringBuilder url = new StringBuilder();
        url.append(urlConsultaNbs);
        url.append("/plugin-nbs-consulta/api/find");
        url.append("?empresa=").append((int) this.codEmpresa);
        url.append("&usuario=").append(this.usuarioLogado.toLowerCase());
        url.append("&sistema=").append(this.wl.sysget("APPLICATION_NAME").asString());
        url.append("&doc=").append(cnpj);
        if (StringUtils.isNotBlank(inscricaoEstadual)) {
            if (StringUtils.isNotBlank(uf)) {
                url.append("&uf=").append(uf);
            }
            url.append("&ie=").append(inscricaoEstadual);
        }
        return url.toString();
    }

    public boolean existeCliente(Double codCliente) {
        boolean result = false;
        try {
            result = rn.buscaCliente(codCliente);
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao buscarCliente. Motivo: ", ex);
        }
        return result;
    }

    public String atualizarCadastroPendenciaOrcamento(double codEmpresa, double codEvento) {
        try {
            this.rn.openPendenciasOrcamento(codEmpresa, codEvento);
            this.codEmpresa = tbLeadsPendenciaCadIntegracao.getCOD_EMPRESA().asDecimal();
            this.cpfCnpj = tbLeadsPendenciaCadIntegracao.getCPF_CNPJ().asString();
            this.uf = tbLeadsPendenciaCadIntegracao.getUF().asString();
            this.ie = tbLeadsPendenciaCadIntegracao.getINSCRICAO().asString();
            this.usaApiConsulta = usaPluginConsultaNBS();
            this.cadastroJaAtualizado = rn.cadastroIntegracaoJaAtualizado(this.codEmpresa, Double.parseDouble(this.cpfCnpj), this.ie);
            if ((tbLeadsPendenciaCadIntegracao.getTEM_PENDENCIAS_ORC().asString().equals("S")) && (this.cadastroJaAtualizado)) {
                rn.openPendenciasOrcamento(codEmpresa, codEvento);
            }
        } catch (DataException dataException) {
            EmpresaUtil.showError("Erro ao atualizar cadastro de pendência do orçamento",
                    dataException);
        }
        return tbLeadsPendenciaCadIntegracao.getTEM_PENDENCIAS_ORC().asString();
    }

    @Override
    public void btnConsultaAPIIntegracaoClick(final Event<Object> event) {
        if (!this.consultandoApi) {
            this.consultandoApi = true;
            consultaAPIIntegracao();
        }
    }

    @Override
    public void lblSituacaoCadSintegraClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    @Override
    public void lblSituacaoSintegraClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    @Override
    public void lblSintegraMultiIeClick(Event<Object> event) {
        exibirSintegraMultiplasIe();
    }

    private void exibirSintegraMultiplasIe() {
        if (StringUtils.isNotBlank(lblSintegraMultiIe.getHint())) {
            EmpresaUtil.showInformationMessage("Inscrições Estaduais \n" + lblSintegraMultiIe.getHint().replace(",", ",\n"));
        }
    }

    private void consultaAPIIntegracao() {
        if (this.cadastroJaAtualizado) {
            if (!rn.podeForcarConsultaPluguinApi()) {
                abreDadosIntegracao();
                Dialog.create()
                        .title("Sem Permissão")
                        .message("Sem permissão de acesso para Refazer/Forçar consulta de dados no pluguin-nbs-Consulta - Acesso: K0619")
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
                return;
            }

            Dialog.create()
                    .title("Confirmação")
                    .message("Deseja refazer a consulta de dados no plugin-nbs-consulta?")
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            executarConsultaAPIIntegracao();
                        }
                        abreDadosIntegracao();
                        consultandoApi = false;
                    });
            consultandoApi = false;
            return;
        } else {
            if (!(rn.podeFazerConsultaPluguinApi() || rn.podeForcarConsultaPluguinApi())) {
                Dialog.create()
                        .title("Sem Permissão")
                        .message("Sem permissão de acesso para Fazer consulta de dados no pluguin-nbs-Consulta - Acesso: K0618")
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
                return;
            }
        }
        executarConsultaAPIIntegracao();
    }

    private void executarConsultaAPIIntegracao() {
        SimpleDateFormat str = new SimpleDateFormat("dd/MM/yyyy");
        StringBuilder msgConsultaApi = new StringBuilder();

        if (this.usaApiConsulta) {
            String urlConsultaNbs = rn.getUrlConsultaNbs(this.codEmpresa);
            if (StringUtils.isBlank(urlConsultaNbs)) {
                Dialog.create()
                        .title(FrmDadosSintegraA.INFORMACAO)
                        .message("É Obrigatório configurar a URL de consulta plugin-consulta-nbs em Parametros>Clientes.")
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
                return;
            }

            try {
                String inscricao;
                if (StringUtils.isNotBlank(this.ie)) {
                    try {
                        inscricao = rn.padronizarInscricaoEstadual(this.uf, this.ie);
                    } catch (DataException e) {
                        inscricao = this.ie.replaceAll("\\D", "");
                    }
                } else {
                    inscricao = this.ie;
                }


                if  (!podeContinuarConsultaCadastro(this.cpfCnpj, inscricao)){
                    abreDadosIntegracao();
                    return;
                }
                if (this.cpfCnpj.length() > 11) {
                    String retFuncao = consultaDadosApiPessoaJuridica(urlConsultaNbs, this.cpfCnpj, this.uf, inscricao);
                    this.atualizouCadastro = true;
                    if (!retFuncao.equals("S")) {
                        msgConsultaApi.append(FrmDadosSintegraA.CONSULTA_DE_DADOS_RETORNOU).append(retFuncao);
                    }
                } else {
                    if (this.tbLeadsPendenciaCadIntegracao.isActive()) {
                        if (this.tbLeadsPendenciaCadIntegracao.getDATA_NASCIMENTO().asDate() == null) {
                            Dialog.create()
                                    .title(FrmDadosSintegraA.INFORMACAO)
                                    .message("Informe a Data de Nascimento para consulta na Receita Federal. Verifique!")
                                    .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
                            return;
                        }

                        String nascimento = str.format(tbLeadsPendenciaCadIntegracao.getDATA_NASCIMENTO().asDate());
                        if (ValidarUtil.validarDataNascimento(nascimento, DATA_INVALIDA_BEFORE)) {
                            Dialog.create()
                                    .title(FrmDadosSintegraA.INFORMACAO)
                                    .message("Data de nascimento informada [" + nascimento + "] é Inválida para consulta na Receita Federal. Verifique!")
                                    .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
                            return;
                        }

                        String retFuncao = consultaDadosApiPessoaFisica(urlConsultaNbs, this.cpfCnpj, nascimento, this.uf, inscricao);
                        if (!retFuncao.equals("S")) {
                            Dialog.create()
                                    .title(FrmDadosSintegraA.INFORMACAO)
                                    .message(FrmDadosSintegraA.CONSULTA_DE_DADOS_RETORNOU + retFuncao)
                                    .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
                            return;
                        }
                    } else {
                        Double codCliente = Double.parseDouble(this.cpfCnpj);
                        if (existeCliente(codCliente)) {
                            if (this.tbDadosFisicos.getANIVERSARIO().asDate() == null) {
                                Dialog.create()
                                        .title(FrmDadosSintegraA.INFORMACAO)
                                        .message("Informe a Data de Nascimento para consulta na Receita Federal. Verifique!")
                                        .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
                                return;
                            }

                            String nascimento = str.format(tbDadosFisicos.getANIVERSARIO().asDate());
                            if (ValidarUtil.validarDataNascimento(nascimento, DATA_INVALIDA_BEFORE)) {
                                Dialog.create()
                                        .title(FrmDadosSintegraA.INFORMACAO)
                                        .message("Data de nascimento informada [" + nascimento + "] é Inválida para consulta na Receita Federal. Verifique!")
                                        .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
                                return;
                            }

                            String retFuncao = consultaDadosApiPessoaFisica(urlConsultaNbs, this.cpfCnpj, nascimento, this.uf, inscricao);
                            this.atualizouCadastro = true;
                            if (!retFuncao.equals("S")) {
                                msgConsultaApi.append(FrmDadosSintegraA.CONSULTA_DE_DADOS_RETORNOU).append(retFuncao);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                msgConsultaApi.append(e.getMessage());
            }


            this.ie = rn.validaalteracaoIEIsenta(Double.parseDouble(this.cpfCnpj), this.ie);
            lblInscricao.setCaption(this.ie);
            lblInscricao.setVisible(StringUtils.isNotBlank(this.ie));
            tbsSintegra.setVisible(StringUtils.isNotBlank(this.ie));
            abreDadosIntegracao();

            if (StringUtils.isBlank(msgConsultaApi.toString()) || msgConsultaApi.toString().equals("S")) {
                Dialog.create()
                        .title(FrmDadosSintegraA.INFORMACAO)
                        .message("Cadastro consultado!")
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
                this.atualizouCadastro = true;
            } else {
                Dialog.create()
                        .title(FrmDadosSintegraA.INFORMACAO)
                        .message("Integração com API - " + msgConsultaApi)
                        .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
            }
        }
        abreDadosIntegracao();
    }

    private boolean podeContinuarConsultaCadastro(String cpfCnpj, String inscricao) {
        String respFunc = rn.validarCadastroConsulta(cpfCnpj, inscricao);
        if (!respFunc.equals("S")) {
            Dialog.create()
                    .title(FrmDadosSintegraA.INFORMACAO)
                    .message(respFunc)
                    .showInformation(t -> FreedomUtilities.invokeLater(() -> consultandoApi = false));
        }
        return respFunc.equals("S");
    }
}
