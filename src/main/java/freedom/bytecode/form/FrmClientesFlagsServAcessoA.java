package freedom.bytecode.form;

import freedom.bytecode.form.wizard.*;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.util.EmpresaUtil;
//import freedom.util.CrmServiceUtil;

public class FrmClientesFlagsServAcessoA extends FrmClientesFlagsServAcessoW {

    private static final long serialVersionUID = 20130827081850L;

    public FrmClientesFlagsServAcessoA() {
        carregaDados();
        carregaGrid();
    }

    private void carregaDados() {
        try {
            rn.carregaDados();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados", ex);
        }
    }

    private void carregaComboUsuario() {
        try {
            rn.carregaComboUsuario(cbbFuncoes.getValue().asInteger());
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados Usuário!", ex);
        }
    }

    private void carregaGrid() {
        try {
            rn.carregaGrid(cbbGrupo.getValue().asInteger(), cbbFuncoes.getValue().asInteger(), cbbNome.getValue().asString());
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Carregar Dados!", ex);
        }
    }

    @Override
    public void btnAdicionarClick(final Event event) {
        try {
            if (cbbGrupo.getValue().isNull()) {
                EmpresaUtil.showMessage("Validação","É necessário informar um grupo!");
                return;
            }
            if (cbbFuncoes.getValue().isNull()) {
                EmpresaUtil.showMessage("Validação","É necessário informar uma função!");
                return;
            }
            carregaGrid();
            if (tbClienteFlagAcesso.count() > 0) {
                EmpresaUtil.showMessage("Validação", "Já Existe Registro Cadastrado!");
            } else {
                tbClienteFlagAcesso.append();
                tbClienteFlagAcesso.setField("ID_SEQ", SequenceUtil.nextVal("SEQ_CLIENTE_FLAG_ACESSO"));
                tbClienteFlagAcesso.setField("ID_GRUPO", cbbGrupo.getValue().asInteger());
                tbClienteFlagAcesso.setField("COD_FUNCAO", cbbFuncoes.getValue().asInteger());
                tbClienteFlagAcesso.setField("NOME", cbbNome.getValue().asString());
                tbClienteFlagAcesso.post();
                tbClienteFlagAcesso.applyUpdates();
                carregaGrid();
            }

        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao Inserir registros", ex);
        }

    }

    @Override
    public void tbEmpresasUsuariosMaxRow(Event<Object> event) {
    }

    @Override
    public void cbbFuncoesChange(Event<Object> event) {
        carregaComboUsuario();
        carregaGrid();
    }

    @Override
    public void cbbGrupoChange(Event<Object> event) {
        carregaGrid();
    }

    @Override
    public void cbbGrupoClearClick(Event<Object> event) {
        carregaGrid();
    }

    @Override
    public void cbbFuncoesClearClick(Event<Object> event) {
        carregaGrid();
    }

    @Override
    public void cbbNomeChange(Event<Object> event) {
        carregaGrid();
    }

    @Override
    public void cbbNomeClearClick(Event<Object> event) {
        carregaGrid();
    }

    @Override
    public void gridCliFlagAcessoExcluirClick(Event<Object> event) {
        try {
            tbClienteFlagAcesso.edit();
            tbClienteFlagAcesso.delete();;
            tbClienteFlagAcesso.post();
            tbClienteFlagAcesso.applyUpdates();
            carregaGrid();
        } catch (DataException ex) {
            EmpresaUtil.showError("Falha ao excluir registros", ex);
        }
    }
}
