package freedom.bytecode.rn.enun;

import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;



@Getter
public enum EnCheckListEstrategy {

    CK_HONDA_INSPECAO_27_ITENS(20000,"Honda - Inspeção 27 Itens" ,EnTipoRevenda.HONDA_AUTO,"CheckListHondaVinteSeteItens"){

        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && ("R".equals(aplicacao) || "O".equals(aplicacao));
        }

        @Override
        public JSONObject getJasperRelatorioParametros() {
            return null;
        }
    },

    CK_GWM_PDS_HAVAL_H6_HEV(30000, "GWM - PDS [Haval H6 - HEV]",EnTipoRevenda.GWM, "CheckListGwmPds"){

        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && "R".equals(aplicacao);
        }

        @Override
        public JSONObject getJasperRelatorioParametros() {
            JSONObject json = new JSONObject();
            json.put("ID_CHECKLIST", 30000.0);
            json.put("LISTA_GRUPOS", "30000,30050,30100,30150,30200");
            json.put("SUBTITULO_RELATORIO", "[HAVAL H6 - HEV]");
            return json;
        }
    },

    CK_GWM_PDS_HAVAL_H6_GT_E_PHEV(30250, "GWM - PDS [Haval H6 - GT E PHEV]", EnTipoRevenda.GWM, "CheckListGwmPds"){
        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && "R".equals(aplicacao);
        }

        @Override
        public JSONObject getJasperRelatorioParametros() {
            JSONObject json = new JSONObject();
            json.put("ID_CHECKLIST", 30250.0);
            json.put("LISTA_GRUPOS", "30250,30300,30350,30400,30450");
            json.put("SUBTITULO_RELATORIO", "[HAVAL H6 GT & PHEV]");
            return json;
        }
    },

    CK_HONDA_MOTO_INSPEC_21_ITENS(40000, "Honda - Inspeção 21 Itens", EnTipoRevenda.HONDA_MOTO, "CheckListHondaMotoVinteUmItens"){
        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && ("R".equals(aplicacao) || "O".equals(aplicacao));
        }
        @Override
        public JSONObject getJasperRelatorioParametros() {
            return null;
        }
    },

    CK_GWM_PADRAO(30500, "GWM - Padrão", EnTipoRevenda.GWM, "CheckListGwmPds"){
        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && ("R".equals(aplicacao) || "E".equals(aplicacao));
        }
        @Override
        public JSONObject getJasperRelatorioParametros() {
            JSONObject json = new JSONObject();
            json.put("LISTA_GRUPOS", "30500,30525,30550,30575,30600,30625,30650,30675"); //futuramente remover esse parametro do relatorio ChecklistGwm
            json.put("ID_CHECKLIST", 30500.0);
            json.put("SUBTITULO_RELATORIO", "[Padrão GWM]");
            return json;
        }
    },

    CK_GWM_PDS_ORA03(30750, "GWM - PDS [ORA 03]",EnTipoRevenda.GWM, "CheckListGwmPds"){

        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && aplicacao.equals("R");
        }

        @Override
        public JSONObject getJasperRelatorioParametros() {
            JSONObject json = new JSONObject();
            json.put("ID_CHECKLIST", 30750.0);
            json.put("LISTA_GRUPOS", "30750,30800,30850,30900,30950");
            json.put("SUBTITULO_RELATORIO", "[ORA 03]");
            return json;
        }
    },

    CK_CHERY_ENTRADA_SAIDA(50000, "Chery Entrada/Saida",EnTipoRevenda.CHERY, "CheckListCheryEntradaSaida"){

        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && ("R".equals(aplicacao) || "E".equals(aplicacao));
        }

        @Override
        public JSONObject getJasperRelatorioParametros() {
            JSONObject json = new JSONObject();
            json.put("ID_CHECKLIST", 5000);
            return json;
        }
    },

    CK_GWM_DIAGNOSTICO(31000, "Gwm Diagnóstico",EnTipoRevenda.GWM, "OsGwmAcompanhamento"){

        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return false;
        }

        @Override
        public JSONObject getJasperRelatorioParametros() {
            return new JSONObject();
        }
    },

    CK_VOLKS_RECEPCAO(60000, "Volks Recepção",EnTipoRevenda.VOLKSWAGEN, "ChecklistVolksRecepcao"){
        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && ("R".equals(aplicacao));
        }

        @Override
        public JSONObject getJasperRelatorioParametros() {
            return new JSONObject();
        }
    },

    CK_GWM_PDS_TANK_PHEV(31250, "GWM - PDS [TANK PHEV]",EnTipoRevenda.GWM, "CheckListGwmPds"){

        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && "R".equals(aplicacao);
        }

        @Override
        public JSONObject getJasperRelatorioParametros() {
            JSONObject json = new JSONObject();
            json.put("ID_CHECKLIST", 31250.0);
            json.put("LISTA_GRUPOS", "31250,31290,31310,31330,31375, 31395");
            json.put("SUBTITULO_RELATORIO", "[TANK PHEV]");
            return json;
        }
    },

    CK_GWM_PDS_HAVAL_H9(31550, "GWM - PDS [HAVAL H9]", EnTipoRevenda.GWM, "CheckListGwmPds"){
        @Override
        public boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora) {
            return this.montadora.equals(tipoMontadora) && "R".equals(aplicacao);
        }

        @Override
        public JSONObject getJasperRelatorioParametros() {
            JSONObject json = new JSONObject();
            json.put("ID_CHECKLIST", 31550.0);
            //json.put("LISTA_GRUPOS", "31550, 31600, 31630, 31670, 31720, 31740");
            json.put("SUBTITULO_RELATORIO", "[HAVAL H9]");
            return json;
        }
    },

    ;

    final int idChecklist;
    final String nomeChecklist;
    final EnTipoRevenda montadora;
    final String nomeRelatorioJasper;

    public abstract boolean isDisponivel(String aplicacao, EnTipoRevenda tipoMontadora);
    public abstract JSONObject getJasperRelatorioParametros();

    EnCheckListEstrategy(int idChecklist, @NotNull String nomeChecklist, @NotNull EnTipoRevenda montadora, String nomeRelatorioJasper) {
        this.idChecklist = idChecklist;
        this.nomeChecklist = nomeChecklist;
        this.montadora = montadora;
        this.nomeRelatorioJasper = nomeRelatorioJasper;
    }

}
