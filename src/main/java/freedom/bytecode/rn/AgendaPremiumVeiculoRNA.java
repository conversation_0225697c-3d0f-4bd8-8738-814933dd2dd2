package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.AgendaPremiumVeiculoRNW;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmServiceOrcarA;

import java.text.DecimalFormat;
import java.util.Date;

public class AgendaPremiumVeiculoRNA extends AgendaPremiumVeiculoRNW {

    private static final long serialVersionUID = 20130827081850L;
    private Double codCliente;
    private Double codEmpresa;
    private Double codOsAgenda;
    private Double codProdutivo;
    private Date horaAgenda;
    private final PkgOsAgendaA pkOsAgendaA = new PkgOsAgendaA();
    private final PkgOsAgendaRNA pkOsAgendaRna = new PkgOsAgendaRNA();
    private final PkgCrmServiceOrcarA pkCrmServiceOrcarA = new PkgCrmServiceOrcarA();
    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    public Double getCodCliente() {
        return codCliente;
    }

    public Double getCodProdutivo() {
        return codProdutivo;
    }

    public Date getHoraAgenda() {
        return horaAgenda;
    }

    public Double getCodEmpresa() {
        return codEmpresa;
    }

    public Double getCodOsAgenda() {
        return codOsAgenda;
    }

    public void continuarOs(Double codEmpresa, Double codOsAgenda, Double codCliente, Double codProdutivo, Date horaAgenda) throws DataException {
        this.codEmpresa = codEmpresa;
        this.codOsAgenda = codOsAgenda;
        this.codCliente = codCliente;
        this.codProdutivo = codProdutivo;
        this.horaAgenda = horaAgenda;
        tbAgenda.close();
        tbAgenda.addFilter("COD_EMPRESA;COD_OS_AGENDA");
        tbAgenda.addParam("COD_EMPRESA", codEmpresa);
        tbAgenda.addParam("COD_OS_AGENDA", codOsAgenda);
        tbAgenda.open();
        tbAgenda.edit();
    }

    public String validarVeiculoAgenda(Value campoFoco) throws DataException {
        ISession session;
        String retFuncao;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            tbAgenda.setSession(session);
            tbAgenda.post();
            tbAgenda.applyUpdates();
            retFuncao = pkOsAgendaA.validarVeiculoAgenda(session, codEmpresa, codOsAgenda, campoFoco);
            if (retFuncao.equals("S")) {
                session.commit();
            } else {
                session.rollback();
            }
        } catch (DataException e) {
            session.rollback();
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
        return retFuncao;
    }

    public String getUltimaKm() {
        try {
            String ultimaKm = new DecimalFormat(",##0").format(pkOsAgendaRna.getUltimaKmChassi(codEmpresa, tbAgenda.getCHASSI().asString()).intValue());
            return "Ult.KM (" + ultimaKm + ")";
        } catch (DataException e) {
            return "";
        }
    }

    public void getDocumentoChassi(String chassi) throws DataException {
        if (tbDocPorChassi.count() > 0) {
            if (!tbDocPorChassi.getCHASSI_COMPLETO().equals(chassi)) {
                tbDocPorChassi.first();
                while (!tbDocPorChassi.eof()) {
                    tbDocPorChassi.edit();
                    tbDocPorChassi.setCHASSI_COMPLETO(chassi);
                    tbDocPorChassi.post();
                    tbDocPorChassi.applyUpdates();

                    tbDocPorChassi.next();
                }
            }
        }

        tbDocPorChassi.close();
        tbDocPorChassi.clearFilters();
        tbDocPorChassi.clearParams();
        tbDocPorChassi.addFilter("CHASSI_COMPLETO");
        tbDocPorChassi.addParam("CHASSI_COMPLETO", chassi);
        tbDocPorChassi.open();
    }

    public void limpaDocumento(String chassi) throws DataException {
        tbValidaDocChassi.close();
        tbValidaDocChassi.clearFilters();
        tbValidaDocChassi.clearParams();
        tbValidaDocChassi.addParam("CHASSI", chassi);
        tbValidaDocChassi.open();

        if (tbDocPorChassi.count() > 0 && !tbValidaDocChassi.getEXISTE().equals("S")) {
            tbDocPorChassi.first();
            while (!tbDocPorChassi.eof()) {
                tbDocPorChassi.delete();
                tbDocPorChassi.applyUpdates();

                tbDocPorChassi.next();
            }
        }
    }

    public boolean pesquisarOrcCliente() throws DataException {
        tbConsultaOrcCliente.close();
        tbConsultaOrcCliente.clearFilters();
        tbConsultaOrcCliente.clearParams();
        tbConsultaOrcCliente.addFilter("COD_EMPRESA;STATUS_OS;COD_CLIENTE;COD_PRODUTO;COD_MODELO");
        tbConsultaOrcCliente.addParam("COD_EMPRESA", codEmpresa);
        tbConsultaOrcCliente.addParam("STATUS_OS", 0);
        tbConsultaOrcCliente.addParam("COD_CLIENTE", codCliente);
        tbConsultaOrcCliente.addParam("COD_PRODUTO", tbAgenda.getCOD_PRODUTO().asDecimal());
        tbConsultaOrcCliente.addParam("COD_MODELO", tbAgenda.getCOD_MODELO().asDecimal());
        tbConsultaOrcCliente.open();

        if (tbConsultaOrcCliente.count() > 0) {
            return true;
        } else {
            return false;
        }
    }

    public String copiaDadosOrcAgenda(Double numOrc) throws DataException {
        ISession session;
        String retFuncao;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            retFuncao = pkCrmServiceOrcarA.anexarOrcamentoAgenda(session, codEmpresa, codOsAgenda, numOrc, usuarioLogado);
            if (retFuncao.equals("S")) {
                session.commit();
            } else {
                session.rollback();
            }
        } catch (DataException e) {
            session.rollback();
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
        return retFuncao;
    }

    public String limpaVinculoOrcAgenda() throws DataException {
        ISession session;
        String retFuncao;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            retFuncao = pkCrmServiceOrcarA.limpaVinculoOrcAgenda(session, codEmpresa, codOsAgenda, usuarioLogado);
            if (retFuncao.equals("S")) {
                session.commit();
            } else {
                session.rollback();
            }
        } catch (DataException e) {
            session.rollback();
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
        return retFuncao;
    }
}
