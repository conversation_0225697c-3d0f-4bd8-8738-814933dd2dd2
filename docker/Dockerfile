FROM pullernbs/nbsfreedomzk:********
MAINTAINER <EMAIL>

USER root

ENV NOME_WAR=crmservice

COPY ${NOME_WAR}.war $DEPLOY_DIR/
RUN mkdir /opt/payara/reports/${NOME_WAR}/
COPY reports/ /opt/payara/reports/${NOME_WAR}/
COPY dictionary.zip /opt/payara/dictionary/dictionary.zip
COPY nbs-validade-linux.jar $SCRIPT_DIR/
COPY entrypoint-nbs.sh $SCRIPT_DIR/

USER root
RUN chmod +x ${SCRIPT_DIR}/*

ENTRYPOINT ["/opt/payara/scripts/entrypoint-nbs.sh"]