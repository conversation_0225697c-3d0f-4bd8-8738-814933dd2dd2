object FrmOrcarAlterarFormaPgtoDesc: TFForm
  Left = 321
  Top = 163
  ActiveControl = vBoxAlterarFormaPgto
  Caption = 'Alterar Formas de Pgto'
  ClientHeight = 661
  ClientWidth = 544
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '40803'
  ShortcutKeys = <>
  InterfaceRN = 'OrcarAlterarFormaPgtoDescRN'
  Access = False
  ChangedProp = 
    'FrmOrcarAlterarFormaPgto.Width;'#13#10'FrmOrcarAlterarFormaPgto.Height' +
    ';'#13#10#13#10'FrmOrcarAlterarFormaPgto.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxAlterarFormaPgto: TFVBox
    Left = 0
    Top = 0
    Width = 544
    Height = 661
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox11: TFHBox
      Left = 0
      Top = 0
      Width = 479
      Height = 61
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Voltar Tela'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Aceitar'
        Align = alLeft
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
          0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
          1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
          95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
          101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
          1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
          A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
          C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
          482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
          1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
          C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
          8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
        ImageId = 700088
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object pgAlterarFormaPgto: TFPageControl
      Left = 0
      Top = 62
      Width = 537
      Height = 397
      ActivePage = tabSheetPecas
      TabOrder = 1
      TabPosition = tpTop
      OnChange = pgAlterarFormaPgtoChange
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabSheetServicos: TFTabsheet
        Caption = 'Servi'#231'os'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        object vBoxServicos: TFVBox
          Left = 0
          Top = 0
          Width = 529
          Height = 369
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hBoxFormaCondCbb: TFHBox
            Left = 0
            Top = 0
            Width = 523
            Height = 57
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox1: TFVBox
              Left = 0
              Top = 0
              Width = 185
              Height = 51
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel1: TFLabel
                Left = 0
                Top = 0
                Width = 78
                Height = 13
                Caption = 'Escolha a Forma'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cbbFormaPgtoServ: TFCombo
                Left = 0
                Top = 14
                Width = 181
                Height = 21
                LookupTable = tbServiceFormaPgtoServCombo
                LookupKey = 'COD_FORMA_PGTO'
                LookupDesc = 'DESCRICAO'
                Flex = True
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = False
                HideClearButtonOnNullValue = False
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object vBoxCondicaoServ: TFVBox
              Left = 185
              Top = 0
              Width = 185
              Height = 51
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Visible = False
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel2: TFLabel
                Left = 0
                Top = 0
                Width = 92
                Height = 13
                Caption = 'Escolha a Condi'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cbbCondicaoPagtoServ: TFCombo
                Left = 0
                Top = 14
                Width = 180
                Height = 21
                LookupTable = tbServiceCondPgtoServCombo
                LookupKey = 'COD_CONDICAO_PAGAMENTO'
                LookupDesc = 'DESCRICAO'
                Flex = True
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = False
                HideClearButtonOnNullValue = False
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object FVBox3: TFVBox
              Left = 370
              Top = 0
              Width = 80
              Height = 51
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 20
              Padding.Left = 5
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object btnAplicarServ: TFButton
                Left = 0
                Top = 0
                Width = 75
                Height = 30
                Caption = 'Aplicar'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 0
                OnClick = btnAplicarServClick
                ImageId = 0
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
          end
          object GridServicosFormaPgto: TFGrid
            Left = 0
            Top = 58
            Width = 524
            Height = 120
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbOrcarServAltFormaPgto
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = False
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            ContextMenu = popMenuGridServicos
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            ActionColumn.Title = 'A'#231#245'es'
            ActionColumn.Width = 100
            ActionColumn.TextAlign = taCenter
            ActionColumn.Visible = True
            Columns = <
              item
                Expanded = False
                FieldName = 'SEL'
                Font = <>
                Title.Caption = '#'
                Width = 32
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = False
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{0396E419-9B0B-46D1-887C-6EB8260B4FAE}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'COD_SERVICO'
                Font = <>
                Title.Caption = 'C'#243'd. Servi'#231'o'
                Width = 85
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{22162C65-C906-4410-B3B8-E223B762396B}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'PRECO_VENDA'
                Font = <>
                Title.Caption = 'P. Unit.'
                Width = 87
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftDecimal
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{DBF62051-FFB4-4307-A2D6-7F8497470275}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = 'R$ ,##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{892029F7-2FC4-4515-987D-B346D031CEA4}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'DESCONTO_POR_SERV'
                Font = <>
                Title.Caption = '% Desc.'
                Width = 74
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftDecimal
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{9E3D8838-D733-4FA3-AE90-FBAC54607FBE}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'TOTAL_LIQUIDO'
                Font = <>
                Title.Caption = 'P. Final'
                Width = 79
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftDecimal
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{91FA0F55-20AF-471E-B430-8705590D1706}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = 'R$ ,##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{14255DF5-CC0A-4162-95F4-C031B079AD7A}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'DESC_FORMA_PGTO'
                Font = <>
                Title.Caption = 'Forma Pgto'
                Width = 98
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{4B191272-F9E3-48C6-986F-40D3B2B6B291}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end>
          end
          object FHBox2: TFHBox
            Left = 0
            Top = 179
            Width = 185
            Height = 21
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel3: TFLabel
              Left = 0
              Top = 0
              Width = 37
              Height = 13
              Caption = 'FLabel3'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = 15174738
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'DESCRICAO_SERVICO'
              Table = tbOrcarServAltFormaPgto
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
          end
        end
      end
      object tabSheetPecas: TFTabsheet
        Caption = 'Pe'#231'as'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxPecas: TFVBox
          Left = 0
          Top = 0
          Width = 529
          Height = 369
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hBoxFormaCondCbbPeca: TFHBox
            Left = 0
            Top = 0
            Width = 523
            Height = 57
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox5: TFVBox
              Left = 0
              Top = 0
              Width = 241
              Height = 51
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel4: TFLabel
                Left = 0
                Top = 0
                Width = 78
                Height = 13
                Caption = 'Escolha a Forma'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cbbFormaPgtoItem: TFCombo
                Left = 0
                Top = 14
                Width = 181
                Height = 21
                LookupTable = tbServiceFormaPgtoItemCombo
                LookupKey = 'COD_FORMA_PGTO'
                LookupDesc = 'DESCRICAO'
                Flex = True
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = False
                HideClearButtonOnNullValue = False
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object vBoxCondicaoPeca: TFVBox
              Left = 241
              Top = 0
              Width = 201
              Height = 51
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Visible = False
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel5: TFLabel
                Left = 0
                Top = 0
                Width = 92
                Height = 13
                Caption = 'Escolha a Condi'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cbbCondicaoPagtoItem: TFCombo
                Left = 0
                Top = 14
                Width = 180
                Height = 21
                LookupTable = tbServiceCondicaoPgtoItemCombo
                LookupKey = 'COD_CONDICAO_PAGAMENTO'
                LookupDesc = 'DESCRICAO'
                Flex = True
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = False
                HideClearButtonOnNullValue = False
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object FVBox7: TFVBox
              Left = 442
              Top = 0
              Width = 80
              Height = 51
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 20
              Padding.Left = 5
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object btnAplicarItem: TFButton
                Left = 0
                Top = 0
                Width = 75
                Height = 30
                Caption = 'Aplicar'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 0
                OnClick = btnAplicarItemClick
                ImageId = 0
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
          end
          object GridItensFormaPgto: TFGrid
            Left = 0
            Top = 58
            Width = 524
            Height = 120
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbOrcarItemAltFormaPgto
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = False
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            ContextMenu = popMenuGridItens
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            ActionColumn.Title = 'A'#231#245'es'
            ActionColumn.Width = 100
            ActionColumn.TextAlign = taCenter
            ActionColumn.Visible = True
            Columns = <
              item
                Expanded = False
                FieldName = 'SEL'
                Font = <>
                Title.Caption = '#'
                Width = 28
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = False
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{0396E419-9B0B-46D1-887C-6EB8260B4FAE}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'COD_ITEM'
                Font = <>
                Title.Caption = 'C'#243'digo'
                Width = 84
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{7F8CA81D-4E22-4897-B2F9-5F12685F108D}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'PRECO_VENDA'
                Font = <>
                Title.Caption = 'P. Unit.'
                Width = 87
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftDecimal
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{0BD3DB34-**************-D90889A479C0}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = 'R$ ,##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{505907AD-BAD1-4A64-9D88-0C8A7E48C51B}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'DESCONTO'
                Font = <>
                Title.Caption = '% Desc.'
                Width = 74
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftDecimal
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{E2076BAE-4A48-4B3E-8EA7-37CB87CB3CFA}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = ',##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{85F55446-FA02-4C17-8719-CD598F48BAD5}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'PRECO_LIQUIDO'
                Font = <>
                Title.Caption = 'P. Final'
                Width = 87
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftDecimal
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{FD8EF721-0CC3-4EC1-B8EC-34428E810E41}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = 'R$ ,##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{E44CE2E3-4B38-48DE-B7DD-277967416EC4}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'DESC_FORMA_PGTO'
                Font = <>
                Title.Caption = 'Forma Pgto'
                Width = 105
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{4DED2602-7673-4576-83AC-260D4812CC92}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end>
          end
          object FHBox4: TFHBox
            Left = 0
            Top = 179
            Width = 185
            Height = 21
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel6: TFLabel
              Left = 0
              Top = 0
              Width = 37
              Height = 13
              Caption = 'FLabel3'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = 15174738
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'DESCRICAO_ITEM'
              Table = tbOrcarItemAltFormaPgto
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
          end
        end
      end
      object tabSheetResumo: TFTabsheet
        Caption = 'Resumo da Forma'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        object FVBox2: TFVBox
          Left = 0
          Top = 0
          Width = 529
          Height = 369
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hBoxPagto: TFHBox
            Left = 0
            Top = 0
            Width = 532
            Height = 363
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 5
            Padding.Left = 5
            Padding.Right = 5
            Padding.Bottom = 5
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox123: TFVBox
              Left = 0
              Top = 0
              Width = 260
              Height = 357
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object vBoxTitleFormaPgto: TFHBox
                Left = 0
                Top = 0
                Width = 255
                Height = 33
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel21: TFLabel
                  Left = 0
                  Top = 0
                  Width = 82
                  Height = 13
                  Caption = 'Forma de Pgto'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
              end
              object gridFormaPgto: TFGrid
                Left = 0
                Top = 34
                Width = 254
                Height = 316
                TabOrder = 1
                TitleFont.Charset = DEFAULT_CHARSET
                TitleFont.Color = clWindowText
                TitleFont.Height = -11
                TitleFont.Name = 'Tahoma'
                TitleFont.Style = []
                Table = tbOrcarFormaPgto
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Paging.Enabled = False
                Paging.PageSize = 0
                Paging.DbPaging = False
                FrozenColumns = 0
                ShowFooter = False
                ShowHeader = False
                MultiSelection = False
                Grouping.Enabled = False
                Grouping.Expanded = False
                Grouping.ShowFooter = False
                Crosstab.Enabled = False
                Crosstab.GroupType = cgtConcat
                EnablePopup = False
                WOwner = FrInterno
                WOrigem = EhNone
                EditionEnabled = False
                AuxColumnHeaders = <>
                NoBorder = False
                ActionButtons.BtnAccept = False
                ActionButtons.BtnView = False
                ActionButtons.BtnEdit = False
                ActionButtons.BtnDelete = False
                ActionButtons.BtnInLineEdit = False
                CustomActionButtons = <>
                ActionColumn.Title = 'A'#231#245'es'
                ActionColumn.Width = 100
                ActionColumn.TextAlign = taCenter
                ActionColumn.Visible = True
                Columns = <
                  item
                    Expanded = False
                    FieldName = 'DESCRICAO'
                    Font = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{02F4BBD8-76C9-4C59-9884-B87C15D92210}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clWindowText
                        Font.Height = -11
                        Font.Name = 'Tahoma'
                        Font.Style = []
                      end>
                    Title.Caption = 'Descri'#231#227'o'
                    Width = 88
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = True
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{BA6396A6-4DB1-4933-AF82-59A34AEC6AA3}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'VALOR'
                    Font = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{C698D0DD-D851-48F4-9F09-9CB255E1D543}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clWindowText
                        Font.Height = -11
                        Font.Name = 'Tahoma'
                        Font.Style = []
                      end>
                    Title.Caption = 'Valor'
                    Width = 102
                    Visible = True
                    Precision = 0
                    TextAlign = taRight
                    FieldType = ftDecimal
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{166517AE-4A83-45BE-9227-5DA584D8A2D6}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = 'R$ ,##0.00'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDecimal
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{3EA7DC32-16CD-4E35-847F-D7BE8E4B1372}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end>
              end
            end
            object vBoxCondicaoPgto: TFVBox
              Left = 260
              Top = 0
              Width = 260
              Height = 357
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 10
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Visible = False
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox74: TFHBox
                Left = 0
                Top = 0
                Width = 255
                Height = 33
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel22: TFLabel
                  Left = 0
                  Top = 0
                  Width = 48
                  Height = 13
                  Caption = 'Parcelas'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
              end
              object gridCondPgto: TFGrid
                Left = 0
                Top = 34
                Width = 254
                Height = 316
                TabOrder = 1
                TitleFont.Charset = DEFAULT_CHARSET
                TitleFont.Color = clWindowText
                TitleFont.Height = -11
                TitleFont.Name = 'Tahoma'
                TitleFont.Style = []
                Table = tbOrcarCondicaoPgto
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Paging.Enabled = False
                Paging.PageSize = 0
                Paging.DbPaging = False
                FrozenColumns = 0
                ShowFooter = False
                ShowHeader = False
                MultiSelection = False
                Grouping.Enabled = False
                Grouping.Expanded = False
                Grouping.ShowFooter = False
                Crosstab.Enabled = False
                Crosstab.GroupType = cgtConcat
                EnablePopup = False
                WOwner = FrInterno
                WOrigem = EhNone
                EditionEnabled = False
                AuxColumnHeaders = <>
                NoBorder = False
                ActionButtons.BtnAccept = False
                ActionButtons.BtnView = False
                ActionButtons.BtnEdit = False
                ActionButtons.BtnDelete = False
                ActionButtons.BtnInLineEdit = False
                CustomActionButtons = <>
                ActionColumn.Title = 'A'#231#245'es'
                ActionColumn.Width = 100
                ActionColumn.TextAlign = taCenter
                ActionColumn.Visible = True
                Columns = <
                  item
                    Expanded = False
                    FieldName = 'PARCELA'
                    Font = <>
                    Title.Caption = 'Parcela'
                    Width = 58
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{BA6396A6-4DB1-4933-AF82-59A34AEC6AA3}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'DATA_PARC'
                    Font = <>
                    Title.Caption = 'Vencto'
                    Width = 73
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = True
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{F362F55E-9EC8-45BD-87EF-96EFA3F0322C}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'VALOR_PARCELA'
                    Font = <>
                    Title.Caption = 'Valor'
                    Width = 95
                    Visible = True
                    Precision = 0
                    TextAlign = taRight
                    FieldType = ftDecimal
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{89EB2101-ACE1-4F4A-8BD2-2D7DE34BB00D}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = 'R$ ,##0.00'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDecimal
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{56CCA4B2-1E4B-4617-89DB-00C28F502D24}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end>
              end
            end
          end
        end
      end
    end
  end
  object coachmarkMessage: TFCoachmark
    ShowNextButton = False
    ShowPriorButton = False
    Items = <
      item
        TargetName = 'GridServicosFormaPgto'
        Position = poBefore_start
        Name = 'coachmarkMessageItem0'
      end
      item
        TargetName = 'GridItensFormaPgto'
        Position = poBefore_start
        Name = 'coachmarkMessageItem1'
      end>
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 406
    Top = 12
  end
  object popMenuGridServicos: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 45
    Top = 301
    object mmItemSelTodos: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Todos'
      OnClick = mmItemSelTodosClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{8558527B-0CE0-4DC7-A0BE-C16B942F0144}'
    end
    object mmItemSelNenhum: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Nenhum'
      OnClick = mmItemSelNenhumClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{033D604B-BCEB-417C-AC27-9BD21593EC5D}'
    end
  end
  object popMenuGridItens: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 148
    Top = 302
    object mmSelTodos: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Todos'
      OnClick = mmSelTodosClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{4A833A1B-C313-4063-A546-370E1CDA57AA}'
    end
    object mmSelNenhum: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Nenhum'
      OnClick = mmSelNenhumClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{5BBDEF33-E31C-42D4-9108-607E68BD2D18}'
    end
  end
  object tbServiceFormaPgtoServCombo: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_FORMA_PGTO_SERV_COMBO'
    MaxRowCount = 200
    OnAfterScroll = tbServiceFormaPgtoServComboAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '40803;40801'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServiceCondPgtoServCombo: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_COND_PGTO_SERV_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '40803;40802'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOrcarServAltFormaPgto: TFTable
    FieldDefs = <
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_POR_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Por Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Total L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_KIT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Kit'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEM_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tem Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORCAR_SERV_ALT_FORMA_PGTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '40803;40804'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServiceFormaPgtoItemCombo: TFTable
    FieldDefs = <
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_FORMA_PGTO_COMBO'
    MaxRowCount = 200
    OnAfterScroll = tbServiceFormaPgtoItemComboAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '40803;40805'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServiceCondicaoPgtoItemCombo: TFTable
    FieldDefs = <
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_CONDICAO_PGTO_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '40803;40806'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOrcarItemAltFormaPgto: TFTable
    FieldDefs = <
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORCAR_ITEM_ALT_FORMA_PGTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '40803;40807'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOrcarFormaPgto: TFTable
    FieldDefs = <
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORCAR_FORMA_PGTO'
    MaxRowCount = 200
    OnAfterScroll = tbOrcarFormaPgtoAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '40803;40808'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOrcarCondicaoPgto: TFTable
    FieldDefs = <
      item
        Name = 'PARCELA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Parcela'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PARC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Parc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_PARCELA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Parcela'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORCAR_CONDICAO_PGTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '40803;40809'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPagamentoParcTmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_FC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Fc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Cobranca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PARCELA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Parcela'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_VENCIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Vencimento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_PARCELA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Parcela'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_PAGAMENTO_PARC_TMP'
    Cursor = 'OS_PAGAMENTO_PARC_TMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '40803;408010'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
