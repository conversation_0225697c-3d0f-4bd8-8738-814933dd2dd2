object FrmLeadzapReceptivo: TFForm
  Left = 44
  Top = 160
  ActiveControl = vboxPrincipal
  Caption = 'Chatbot Receptivo'
  ClientHeight = 667
  ClientWidth = 893
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600506'
  ShortcutKeys = <>
  InterfaceRN = 'LeadzapReceptivoRN'
  Access = False
  ChangedProp = 
    'FrmLeadzapReceptivo.Height;'#13#10'FrmLeadzapReceptivo.Caption;'#13#10#13#10'Frm' +
    'LeadzapReceptivo.ActiveControltbEventosTipo.MaxRowCount;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 893
    Height = 667
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hboxBotoes: TFHBox
      Left = 0
      Top = 0
      Width = 890
      Height = 68
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Color = 16514043
      Padding.Top = 5
      Padding.Left = 2
      Padding.Right = 0
      Padding.Bottom = 5
      ParentBackground = False
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox1: TFHBox
        Left = 0
        Top = 0
        Width = 801
        Height = 60
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 2
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        ParentBackground = False
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object btnConsultar: TFButton
          Left = 0
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Executa Pesquisa (CRTL+ 1)'
          Caption = 'Pesquisar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 0
          OnClick = btnConsultarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000004BD4944415478DA9D955948636714C7CF4D62E292C47D19199BAB1444
            A6A363C1818A2B8856EA8E4A952A06D427A14F3EF4D127518A880BB850B562A5
            3EF8A80F76AC11DC1DC4655C06B154A341E3BEC6254BCFF932B9F5AA38A51F5C
            6EEE77BFFBFDBEF33FFF73C2D96C36E8E9E991A5A6A6FEE1E4E49400FF614824
            12B8BABA5A191E1E7E5D5050607D6E2D4780C1C1C1A0F8F8789DABAB6B083D7F
            6ED09ACBCB4B30180CBD333333A5C5C5C5A66701FDFDFD7C4A4A8A4EA9546AAC
            56EB6701777777603299E0FAFA1A767777FB2A2A2A7E181B1B333F0B484E4ED6
            A954AA470092E3E1B8BDBD85A3A323F0F4F4848B8B0B585F5FEF89898929319B
            CD9627017D7D7D3CE640A756AB4500DA9C9E777676D86965322928952AF0F2F2
            02A3D108EEEEEE80B2C2F1F1316C6C6CFC3E35355582D1DC3C02F4F6F6F26969
            690280E338766D6EFE0D6B6B1F715325787878B03993E91AE47227C068D90114
            0A05A039E0F0F010B6B6B67A878686CA6A6A6AAE440074119F9E9EAEC3133100
            7DB8BABA0A7ABD1EA2A2A284CD68D0FBD3D353D8DBDB03373737364F92C96432
            72161E682D373B3BBB5F04E8EEEEE633323274784A0D4DD2C70B0B8B909090C0
            64A1A48AC2E6242CC9676767A056AB00B567CEB2582CB0B2B2529A9898F88B08
            D0D5D5C5676666EA30690C303E3E0E1A0D0F010101F8A15D32FAF85F07DBD8EF
            F3F30BA0C028428A8CF2B4B0B0508207FB5504E8ECEC64004C9E864EF5E1C312
            BC79F3F5275938B0E7050400DDE9D5CDCD0DD6C305F8FBFB83542A65CF5817DA
            B8B8B82E11A0A3A38301BCBDBD35240F260B5EBDFA0A37E2D8C60F87D56A63F3
            24CDC181115EBC08C0643B33C0ECECAC168B560C686F6FE773727204805EBF0D
            21215F3269E8641209774F1A1B6E6CD79B40C7C7461601D995724511600EC480
            B6B63601400E595D5D83D0D030269154CA319908466BE9A28D2D167BBD6C6DFD
            05C1C1C1E0E2E2C200580BDAA4A42431A0A5A58501C84564B7F7EF67D1F75E98
            E860FCE89639C901A08DCDE63B94C405DB840125DA87F0F0701611D975626242
            8B452B06343737F3B9B9B9AC0EE8D494B8999959CCC36B78F93250482C398A0A
            9D6C6A306C836EE41D7C9BFA1DF8FAFAB21A2000F6242D16AD18D0D0D0C0E7E7
            E70B952C972BB0320F606969097C7CBC2128E80BB4A29A45717E7E0EDBDB7A98
            9E9E86136C116161A110F5F61BCC831F6B19A3A3A35A54E331202F2F4F879529
            F422B95CCE4E849509FBFBFB9FA2B0319DA9C991EEF3F3F3D0DADA0A111111A0
            D56A51520D0C0C0C68510D31A0A9A989E5009D2000689E7A8CA305D04573944C
            2A2C85428E4DD000D5D5D530323202E81C282A2A026767E7EF232323FB4480FA
            FAFA4711382034C8AA8E06E8680974276B52CFAAADAD85C9C949888E8EA64EFB
            23E6B44104A8ABAB7B1260EF3B1C3CF72F47D12C2F2F4355551559140203038D
            58800598BF3F05406363239F9595A5C3F034CF6DC63D51D63447AD7C7171112A
            2B2B616E6E8EF2777572729285397CC700480F292F2F9F469BFA383AE34399EE
            3F3F7CEF806015435959193306CAB889EFDE32406C6CACAAB0B0F0673F3FBF30
            94E89664B257ACF55EF55A858B06E5C171C7F7368448D11437F8E7F51BE6A314
            FBD23CAEFD89739C0617C8F0E64CBD0CFEDF20FD2CB8DF354AED850630E1FFB6
            E91F70B7FB1897F803840000000049454E44AE426082}
          ImageId = 13
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = True
          IconReverseDirection = False
        end
        object btnNovo: TFButton
          Left = 65
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Inclui um Novo Registro  (CRTL+ 2)'
          Caption = 'Novo'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 1
          OnClick = btnNovoClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F80000046A4944415478DA8D967F681B6518C7BFEF5DDAA5AE6E36D58EC2DA4E
            BA5A1D42BBE93A990A56F6873237D81036D0BF1404C76C374BA508ADCC8D29FE
            530A8AEC0FC58242152B4341879B0C6BE956D0DDADBF67D35FA95996D4A5499A
            E4EE92BBBCBEEF2517EFB254FB1CC7BD6FDE7B9FCFF3E37D9E0B414E4856446C
            440420B33FE3C64B78C3B5C5F54AADA7761757E10BFB26F5B8FE1516F1293E86
            62EAB56D7BB4BBBBFB2CA5D4A98C10C74B5C7C311FFACBFBF71E78EE40DDF1C6
            E3B85F2CE79BC89A11C7C0AD017A65ECCA14D238882E2CD9F736AFAE46244555
            210884E63CE29ED9C750D20A7DFEA75652595B490F3DFC027119693CE56E3501
            D7D5ABD0C512FAC3C22532228F4C62062D0E402412BDA16A1C20C05268BFB9F4
            C97DF820781E2F3F7E18D56E116E4DC3B18A77CDB5AF57CF43DDB40901D5C0B7
            13DF43FB45EB7200A2D198A4A534D36AA18807462683677F7C86CE5578C9139B
            EBE80EB748B6A674BC5EF3396571249FF95E43B4D445175583FC9158827A4D1B
            7300626B6B528A594404A16888946412DB2F6DA7C6E634E9F3F4D23D35BB89C8
            343C56DD6486683A70130605BDB12C91F6F069646248D9014D6B1C904EE74322
            1484281A8DA26EA80E99521D7D15BDD85DD30C0ED855DD6C2A980AC81C006959
            46FB2A03A8D01D80783C2E738060599DCD45DE0366009EFE6D3F9D77CF916DCC
            D29DF7816CCB005D0D574D0F3E9C6D455000F52641824C339DC6AC139048C869
            0B209859F817C6C67CAD73B8935E485F20656CFF8E7206601A3E7A240B78E74F
            0660E0C5388859045FE0130720914848BAAE6743C2AC178A9C22694AC2D1DB47
            11D00228D3010F2BBAF71A7BCDB533B74E23CC3C525C6C12C66D9CC461072099
            4CCA268025397F8A6C632EAAAAD2C1E141D22576D1A01A2424068869981E1825
            CCAD2D6C1C43109DECBA834127405124230BC85B6FD544AE9D98CF582C86A1D1
            21F427FA315A368A10BBB854B16B9FB20F175FBD78121ABEE4AF3A008AA2C886
            6138AC66009A6B5516C49CA75229EAF7FBC9FCFC3CC2E1B0F99BC7E3417D7D3D
            6D686868612DE7F7C25ED4C4DA849CE1005E68B9245BA7C8E6813537956658F1
            B1DB1C8BA2681AE472B9F6B0DFE47B002CBE32AF56C106B014167A603D7973A4
            59C91921D0D2D29275009A26710F0A7B91657D612E7838F9A160E132C75C4457
            092A1ED85A1CA0699ACC5D261BF08029A6CC63C2AB3B1289507640080B0D8647
            AE5D7FBBBDED0803DE591720D87A911D60CDD966C20E040D8556CC24B339ADAA
            7A884C4C4ECCB4BDD5768841BDF9CF490140CAB098162B30FB98BD87BB77C3F0
            7ABDA87CB0123BEBEB71F9F2CFCB6F9E38F1A2FF2FFF944DE7BD1EF0A4157C64
            1CA78867931524F12D2FD3E9E919D2B2F7498C8F8FF9DB4F9D3AE89DF5DE4481
            3801A9944C6D39B0626E07F023C99A22999B9BA7C15088A84A62AEA3A3E3C8C2
            C2E2388A482140CA01D60D11CF11036061C987A1A15FE573EF9F39B6B2F2F72C
            D6113BA0B1A7A7E75C7E8190A21B780853BA81403014FDEE9B81B3AC852FE13F
            84148C37F6B725C762B7F17F2FFD034C50719467FC49DB0000000049454E44AE
            426082}
          ImageId = 6
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = True
          IconReverseDirection = False
        end
        object btnAlterar: TFButton
          Left = 130
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Altera o Registro Selecionado  (CRTL+ 3)'
          Caption = 'Alterar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 2
          OnClick = btnAlterarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000005094944415478DAAD956B6C536518C79FE7F4D0DDC82ED95A262BB0AC
            026EC16D201035121308514CCC844020881335442E11818D6BD84C4087CB3E90
            4C037E21A21F34442111A209E3E218868B894047071B3BDD4AB76EACDDADD773
            DAD39EC7F7F4E65A36C2079FE4DCCFFBFF3DB7F77D11FE476B5DB76259B63FFD
            3CA728D7291CDEB9B0A5C58AF18F1A8D465F5757F70511258F42047C0EF190B7
            1B372F685B1FFAAD38CF67B2A35796074196DF9838D63836362E8892041C8784
            5163FA49F72A8C20F214BDAA03C3012729E6B53830E8849AC634DA6A1BC5E93E
            1F8478FE4212607CDCD52D0554000709C189473CA2A86EE459098B106CDF00A2
            DB0A1F7CA5815E7B0066F276D89F0964EAC4F3490097CB2D04828188D7DC2411
            40FC59D56657A0308AE6ADA011EFD2278D1C9A1E89A048763ABD5F0E8B226F5A
            7B48AE4A02B83D1E211860008E9B3C459100309122FFA32398215E81DA131C5D
            BA3982A2D701273EF75361BEC6B6FA90BC667084BA26024A3C2A40961329E126
            4953BCE852DF0F90E6FA198EFF9A09A7CF09E0F7B9E068F528AC7CAD0056EE74
            7CD865A38BECB7B12480D7EBB5A8002EEE75B4164F1539E8BC82BCE35B3A7B43
            87479A6F422020D1F6779EE0A6B73361A4A0892A977DBA9CE9FDC30E6F32C0E7
            B3C8710017A9C27FB01820E4BE4FD0DF88B78599B4A3BE05032CA5554B07A876
            830687B26A000B56D3A2458B5E657A77D4EE4D02F87C3E21140A453D65DEA7A6
            88151094C70D2038F2E1A37D17C1EDF6C2EBF39CD0B04586E1B48DE09E5E0D85
            8585307FFEFC054CEF41BCD31200BFDF6F89005891135D14BB57641784ACC760
            D89341D57BAFAA3D4F65063736EFF0823F633959B9EDA88ACF993387F47AFD3C
            455184A701A22884A38084F7EA9C002508416B13484184CDFB5BA14BB083215F
            84933B4781CF29877BFE5DA0D3BF002525259108D2D3D3E73280E52980288A96
            70389C1C010205ACC7D93904DBEBAFC1AD3B56CA9B2EE3C9CF86495F68C06BCE
            5D9057308B8C4623161717435A5A1AF13C3F0540922C8A0A50275AACC801DB77
            C4830B0F345E873F5A7B2143AB50F3362796CDCDA1DFADDB3033D7A87A4EA5A5
            A5989595A5EA4C0D901820AC2891CE510192FD0C68C55BD474CA8C3F9E1F60EF
            098E7D3C4ACB17F378AEB39A28A31459CEA1A2A282727373631173A4D54E9B02
            1008086A04F1B568C0FC13FCD9F23D349C1A501D83DAB52E787F6518CE9AAB60
            9C2AA1A8A8082A2B2B41A7D301138CAECAFC34C8CBCD991CC07ADAA2FEA8A6C8
            EF7A8243D656103AFFA6A3CD5771D52B63B06F830417CC6F92E05EAA760C9597
            97A34EAF07AFC743AC4190A506FEBA71B375EF9EDDEF058341D7940016010D0A
            6DE8B0B68067E401897E172E9CD5079DC3F3C034B28A66CF9E8D068381B2B3B3
            B1BFDF0EAC31586BEAB0A3C3DC5E5353BBCAE9740E24B6931480A0B00D476DD1
            53DF1C80B2223658F683A29D0B9A9C25A037BC0CFA193340ABD5B249E6819E9E
            1EC82FC887178D46B87CF99275F79E3D2B846EA13769BF4A8D40DDD11E5BADB4
            7ACD1ADCBC7E196CDCB4857267BC84ACFDD45D0FD8776213126D7D7DF4F06127
            2E5DB218CCE6FBB683070FBE6532B577A6EE74C98060D0422C45A67B7749C3F3
            5851519958ECA27B0DAAC524B628A2C5D243430E074A92BFABEEF0E1773B3A1E
            74C324960A10285AE4E4253A261E03000340EF631BB4B55DBBDDD4F8F53A5687
            7E98C226028AEAEBEBBF8CBF8B0BA79A9AC260280C83438EF173BF9C69F0B8DD
            0E7886A5AA70F0FC46B1E399F62F6A8BA82D8608FAC10000000049454E44AE42
            6082}
          ImageId = 7
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = True
          IconReverseDirection = False
        end
        object btnSalvar: TFButton
          Left = 195
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Salvar  (CRTL+ 5)'
          Caption = 'Salvar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 3
          OnClick = btnSalvarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934
            31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2
            D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE
            6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5
            6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013
            090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF
            349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E
            8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871
            175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39
            FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622
            F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673
            BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E
            01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146
            0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219
            506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6
            0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991
            CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313
            9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0
            39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D
            AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7
            C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743
            FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE
            E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660
            474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF
            D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61
            33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E
            0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE
            F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF
            921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5
            A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E
            9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163
            8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09
            C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C
            D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E
            23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA
            FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E
            9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1
            5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A
            25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2
            FF02B065C443D9FE4B070000000049454E44AE426082}
          ImageId = 4
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnCancelar: TFButton
          Left = 260
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Cancela as Altera'#231#245'es Correntes  (CRTL+ 6)'
          Caption = 'Cancelar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 4
          OnClick = btnCancelarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000004EF4944415478DAAD566B4C1C55143E7766F6BD145020744BA98F1608
            A820DA508BB42A84B62A264D6A24D158E323C646230D4D7D24901AA8466BD336
            1A9AF84B1B7F580BD49ADA6A0B518A3450915D0BE9F2461E5BDEEC93DD9DDD9D
            39DE997D53B49A7893D9993B73EFF9CEF79DC75D02A141828385FF6120A2402F
            94EDC6BCCFA9A9A9A90BBD8F0E42E216DD6E5027A1BEBEBE4614C5FE95000556
            ABCDE8F17A8161088618491B629FA50D28D30DDD4346306C4BA150A05EAF2FA4
            00A65B006C367B8F9797001888188CBDC28C827623F358431CC7814EA75B1DC0
            6E7718791F2F7BCDACC200C273695FF81E048930E05816B55AEDEA000EA7D3E8
            E32900C3AC2E5150E38844244C254622960268349A28C08F8D1FAA76EE799FA7
            4BF31D0E0AE0F7CB12D8E60660B4F7023817478165782A9B0F44810351544342
            F206D8545801A9190510C5080E495EB55A1D05B8D6F2F96C47EB570D750DC673
            13169B716176047A5A8E6362A28BE43D940177A45136E00AC551DAA143EB0221
            E6EE29B42D2A49D1930721393D3BC24662A852A9A200674F96FAD3D6A6B2478E
            5F3A7DF8D32F2BCD9D0DB07D470E26ACF1DC427FC51CDDCB6AD27E6100EECADF
            8BB95BF64400944A6514A0E9B392C0CE8A3CE6BAC9014EA7171E29310043FCE1
            8AF91799CFC1CF1747213DFB79C82FA994258B03683C511C28DF95CBCA9910AD
            29D94BBFC30A9EA9110CCCCF11F00BC0A8B4A04C31A026732361944C8419028B
            DF37F79392678F41C6BD05486B210A70E6D816A1AC3C8F50B783CB25B55104E7
            C4EFE0991905AF4B8489291A0587086A4E80BB937D90A055803E771BA80DEBE9
            6251F6C8ED0538FBC322BCFEC177F10CBE3DBA5978A22C9701963A42931FA863
            CE9B9DE85F9E2137CC2C589C45985F5C49925232C0BE340B3D6D67F04EFB4FE4
            E1756ED46FDA4A546B374418B75D9D82ACC76AF181A21D51806F3E29141F2FCD
            22C0D17CE780F83C13E075F6A1CB4DC8B5EE62D8FB6E13320C1B0EA06CACFD7C
            0359F8E53D2C48779384FBCA81516BE5A2732C0BD03164C0970E7E1105387528
            4B2C2DA39A6A54489454204E04419CC280384EFABA11F4490770EBD307223545
            42557BA5E9301AACCD44AB53D1B8645255790262004EB75A71FF89AE2840C3FE
            F4E5CD391E0D9BB006B83549C0E913A847B460540140CE02FD9D023CB8FD2418
            EEA9801810989BE8034BFB1148532D8222391330E00614BC70F1AA055EFDA8A3
            90266010E0D02BEB9EE179B1C43CEE4D7BF9B5B75E945293EEA7B210228544AD
            51D2185490C494BC38068EA5691C683D4A52D91910153A14033C61580E4EB5CC
            77D6379CDF2D8A3813D78B6885E77B3CBC89520B363BB9DB31B2E6B1ED5A9A0B
            8240E66F8EE0E46F5F9380CB029C3A1193330A48D790A7FFCDAA772A68D31C8E
            9C0F311593CFF3BC51A469C7D0A4908A82A51EC5CA127EA6EB60F07A1B4DE50E
            48DBB80DD6E73C0A972F5F9A7C63DFBE5D9629CB8DB80368058049AA01EA238A
            4280704A0D65C6C6F619E938C4C5B949326EFE1597A607C9FD252F80B177D0F2
            7655D553C343C37FACACF378009FCF442D53003F9548411562E32492E2E3E3DD
            689D1F23B39366B4D9DDC4EA4B1AA9AEAEDE3D36F667EF6A8D64258011E51848
            DE32D1534C960C2566B465FBC1659F86C9F161B8D26536D5D57DFCDCFCFCC2D0
            DF75AA5880ECDADADAFAC887D8B3048310F22F95D0E7F3C0CCDC9CBDB9F15C9D
            D3E91AFFA75648563CFF97BF2D12A270BB457F0173456D3788187BC400000000
            49454E44AE426082}
          ImageId = 9
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object FHBox3: TFHBox
          Left = 325
          Top = 0
          Width = 26
          Height = 28
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = 'FHBox2'
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 5
          Visible = False
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
    end
    object pgChatbot: TFPageControl
      Left = 0
      Top = 69
      Width = 881
      Height = 585
      ActivePage = tabMenu
      TabOrder = 1
      TabPosition = tpTop
      OnChange = pgChatbotChange
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabListagem: TFTabsheet
        Caption = 'Listagem'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        object FVBox1: TFVBox
          Left = 0
          Top = 0
          Width = 873
          Height = 557
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object gridPrincipal: TFGrid
            Left = 0
            Top = 0
            Width = 544
            Height = 260
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbLeadzapMenu
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = True
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            Columns = <
              item
                Expanded = False
                FieldName = 'ID_MENU'
                Font = <>
                Title.Caption = 'Id'
                Width = 40
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = True
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{422C5AB3-0FF2-4BFB-B023-66594D8999C3}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'MENU_DESCRICAO'
                Font = <>
                Title.Caption = 'Descri'#231#227'o'
                Width = 120
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = True
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{FBEC77FC-0ABF-4A3C-AB8C-BF6573678D8A}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'ATIVO'
                Font = <>
                Title.Caption = 'Ativo'
                Width = 60
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = 'ATIVO = '#39'S'#39
                    EvalType = etExpression
                    GUID = '{6BD01C7D-5622-4FB5-B21F-545FA8ED947B}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 310012
                    Color = clBlack
                  end
                  item
                    Expression = 'ATIVO = '#39'N'#39
                    EvalType = etExpression
                    GUID = '{D5186AA6-A983-446A-B320-FF75622FE84C}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 310013
                    Color = clBlack
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = False
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{05B73FC4-070B-4893-AB68-A8660A19F120}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end>
          end
        end
      end
      object tabCadastro: TFTabsheet
        Caption = 'Cadastro'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        object FVBox2: TFVBox
          Left = 0
          Top = 0
          Width = 873
          Height = 557
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox7: TFHBox
            Left = 0
            Top = 0
            Width = 185
            Height = 13
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FGroupbox1: TFGroupbox
            Left = 0
            Top = 14
            Width = 867
            Height = 64
            Caption = 'LeadZap Menu'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = False
            Closable = False
            Closed = False
            Orient = coHorizontal
            Style = grpLine
            HeaderImageId = 0
            object FHBox2: TFHBox
              Left = 8
              Top = 20
              Width = 537
              Height = 37
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox4: TFHBox
                Left = 0
                Top = 0
                Width = 80
                Height = 25
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 5
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox10: TFHBox
                  Left = 0
                  Top = 0
                  Width = 9
                  Height = 17
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object lblDescricao: TFLabel
                  Left = 9
                  Top = 0
                  Width = 46
                  Height = 13
                  Caption = 'Descri'#231#227'o'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
              end
              object edtDescricao: TFString
                Left = 80
                Top = 0
                Width = 121
                Height = 24
                Table = tbLeadzapMenu
                FieldName = 'MENU_DESCRICAO'
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
              object FHBox20: TFHBox
                Left = 201
                Top = 0
                Width = 126
                Height = 29
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 5
                Padding.Left = 2
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object chkAtivo: TFCheckBox
                  Left = 0
                  Top = 0
                  Width = 97
                  Height = 17
                  Caption = 'Ativo'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  Table = tbLeadzapMenu
                  FieldName = 'ATIVO'
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
              end
            end
          end
          object FHBox8: TFHBox
            Left = 0
            Top = 79
            Width = 185
            Height = 13
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FGroupbox3: TFGroupbox
            Left = 0
            Top = 93
            Width = 865
            Height = 165
            Caption = 'Se N'#195'O Achar o Fone no Cadastro'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 3
            OnClick = FGroupbox3Click
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = False
            Closable = False
            Closed = False
            Orient = coHorizontal
            Style = grpLine
            HeaderImageId = 0
            object FVBox9: TFVBox
              Left = 4
              Top = 15
              Width = 857
              Height = 146
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 3
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox5: TFHBox
                Left = 0
                Top = 0
                Width = 537
                Height = 37
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox14: TFHBox
                  Left = 0
                  Top = 0
                  Width = 80
                  Height = 25
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox18: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 17
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel3: TFLabel
                    Left = 9
                    Top = 0
                    Width = 44
                    Height = 13
                    Caption = 'Template'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object cbbTemplateQualificado: TFCombo
                  Left = 80
                  Top = 0
                  Width = 152
                  Height = 21
                  Table = tbLeadzapMenu
                  LookupTable = tbTemplateQualificado
                  FieldName = 'ID_TEMPLATE_LEAD'
                  LookupKey = 'ID_EMAIL_MODELO'
                  LookupDesc = 'MODELO'
                  Flex = True
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
              object FVBox3: TFVBox
                Left = 0
                Top = 38
                Width = 536
                Height = 58
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 70
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object chkMostraNome: TFCheckBox
                  Left = 0
                  Top = 0
                  Width = 97
                  Height = 17
                  Caption = 'Mostra o Nome'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  Visible = False
                  Table = tbLeadzapMenu
                  FieldName = 'LEAD_PERGUNTA_NOME'
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
                object chkMostraEmail: TFCheckBox
                  Left = 0
                  Top = 18
                  Width = 97
                  Height = 17
                  Caption = 'Pergunta  Email'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  Table = tbLeadzapMenu
                  FieldName = 'LEAD_PERGUNTA_EMAIL'
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
                object chkCadDuplo: TFCheckBox
                  Left = 0
                  Top = 36
                  Width = 525
                  Height = 17
                  Caption = 
                    'Se achar mais de um n'#250'mero de telefone no cadastro de cliente, p' +
                    'ergunta qual o cliente?'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 2
                  Visible = False
                  Table = tbLeadzapMenu
                  FieldName = 'CAD_DUPLO_MOSTRA_AO_CLIENTE'
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
              end
            end
          end
          object FHBox9: TFHBox
            Left = 0
            Top = 259
            Width = 185
            Height = 13
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FGroupbox2: TFGroupbox
            Left = 0
            Top = 273
            Width = 866
            Height = 70
            Caption = 'Se Achar o Fone no Cadastro'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 5
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = False
            Closable = False
            Closed = False
            Orient = coHorizontal
            Style = grpLine
            HeaderImageId = 0
            object FHBox15: TFHBox
              Left = 8
              Top = 20
              Width = 537
              Height = 40
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox16: TFHBox
                Left = 0
                Top = 0
                Width = 80
                Height = 25
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 5
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox17: TFHBox
                  Left = 0
                  Top = 0
                  Width = 9
                  Height = 17
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FLabel2: TFLabel
                  Left = 9
                  Top = 0
                  Width = 44
                  Height = 13
                  Caption = 'Template'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
              end
              object cbbTemplateLead: TFCombo
                Left = 80
                Top = 0
                Width = 145
                Height = 21
                Table = tbLeadzapMenu
                LookupTable = tbTemplateLead
                FieldName = 'ID_TEMPLATE_QUALIFICADO'
                LookupKey = 'ID_EMAIL_MODELO'
                LookupDesc = 'MODELO'
                Flex = True
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = False
                HideClearButtonOnNullValue = False
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
          end
          object FHBox36: TFHBox
            Left = 0
            Top = 344
            Width = 185
            Height = 13
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 6
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FGroupbox5: TFGroupbox
            Left = 0
            Top = 358
            Width = 866
            Height = 194
            Caption = 'Vendedor Template'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 7
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = False
            Closable = False
            Closed = False
            Orient = coHorizontal
            Style = grpLine
            HeaderImageId = 0
            object FVBox10: TFVBox
              Left = 8
              Top = 19
              Width = 757
              Height = 158
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox33: TFHBox
                Left = 0
                Top = 0
                Width = 537
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox34: TFHBox
                  Left = 0
                  Top = 0
                  Width = 120
                  Height = 25
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox35: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 17
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel7: TFLabel
                    Left = 9
                    Top = 0
                    Width = 71
                    Height = 13
                    Caption = ' Novo Leadzap'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object cbbTemplateVendedor: TFCombo
                  Left = 120
                  Top = 0
                  Width = 245
                  Height = 21
                  Table = tbLeadzapMenu
                  LookupTable = tbTemplateLead
                  FieldName = 'ID_TEMPLATE_VENDEDOR'
                  LookupKey = 'ID_EMAIL_MODELO'
                  LookupDesc = 'MODELO'
                  Flex = True
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
              object FHBox24: TFHBox
                Left = 0
                Top = 41
                Width = 537
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox37: TFHBox
                  Left = 0
                  Top = 0
                  Width = 120
                  Height = 25
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox38: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 17
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel8: TFLabel
                    Left = 9
                    Top = 0
                    Width = 84
                    Height = 13
                    Caption = ' Apov. Orc. Parts'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object cbbTemplateVendedorAprov: TFCombo
                  Left = 120
                  Top = 0
                  Width = 245
                  Height = 21
                  Table = tbLeadzapMenu
                  LookupTable = tbTemplateLeadAprov
                  FieldName = 'ID_TEMPLATE_VENDEDOR_PARTS'
                  LookupKey = 'ID_EMAIL_MODELO'
                  LookupDesc = 'MODELO'
                  Flex = True
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
              object FHBox39: TFHBox
                Left = 0
                Top = 82
                Width = 537
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox40: TFHBox
                  Left = 0
                  Top = 0
                  Width = 120
                  Height = 25
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox41: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 17
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel9: TFLabel
                    Left = 9
                    Top = 0
                    Width = 91
                    Height = 13
                    Caption = 'Reprov. Orc. Parts'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object cbbTemplateVendedorReprov: TFCombo
                  Left = 120
                  Top = 0
                  Width = 245
                  Height = 21
                  Table = tbLeadzapMenu
                  LookupTable = tbTemplateLeadReprov
                  FieldName = 'ID_TEMPLATE_VENDEDOR_PARTS_REP'
                  LookupKey = 'ID_EMAIL_MODELO'
                  LookupDesc = 'MODELO'
                  Flex = True
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
            end
          end
        end
      end
      object tabMenu: TFTabsheet
        Caption = 'Itens do Menu'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox4: TFVBox
          Left = 0
          Top = 0
          Width = 873
          Height = 557
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object gridItensMenu: TFGrid
            Left = 0
            Top = 0
            Width = 867
            Height = 176
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbLeadzapItem
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = True
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            Columns = <
              item
                Expanded = False
                FieldName = 'ID_ITEM'
                Font = <>
                Title.Caption = 'Id. Item'
                Width = 80
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{1C5758CD-B84C-48B3-B41B-C571A2E11D94}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'ID_MENU'
                Font = <>
                Title.Caption = 'Id. Menu'
                Width = 79
                Visible = False
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{E60FCD80-9411-4745-B880-049630F048D1}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'SEQUENCIA'
                Font = <>
                Title.Caption = 'Seq'#252#234'ncia'
                Width = 92
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{7E484E39-4A55-49E8-9223-5E306271832D}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'DESCRICAO'
                Font = <>
                Title.Caption = 'Descri'#231#227'o'
                Width = 90
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{29D1739F-FD67-4B96-8B4C-13114723C2F4}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end>
          end
          object FHBox6: TFHBox
            Left = 0
            Top = 177
            Width = 866
            Height = 273
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 2
            Padding.Left = 3
            Padding.Right = 3
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox5: TFVBox
              Left = 0
              Top = 0
              Width = 444
              Height = 268
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox11: TFHBox
                Left = 0
                Top = 0
                Width = 438
                Height = 41
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object btnNovoItem: TFButton
                  Left = 0
                  Top = 0
                  Width = 48
                  Height = 35
                  Hint = 'Novo Item'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  OnClick = btnNovoItemClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000003EC4944415478DAB5965B681C5518C7FF33BB9BDD6433934DD6D840B5
                    5523B489ED830F8A7879B708E2B320EA8B977A01056DB5F6C10749B14F229446
                    2822A5488B0FA542DFBCE083340A5E9A6EBA694D8D4D1392B6BB3BDDD9CD6E32
                    33E7F83FB333B31B6603C5E20CDF7E67CE39F3FDCE77396756C3FF7C69B73BB1
                    6EEF36A97280BC9B3A4F9DA54E511C9AA953DFA0FE2B6B4C57BB020E7DFAC92B
                    5493DD8CF7F6369149AFC134EB30FAEBFE732AE550D2709C35DCB76DF1483E6F
                    9FA7B9054E9FCE1A1716BA01E4337B9EC5E8E8280DF446C6A5AC51AE438839B8
                    CE15B617D979038DF9A3FEB8187E0ECB2B750CE6964F99A6F74722A1CDD2EC8F
                    84946380B7DF7C079EE775ACDDA5417A2EE7E02CEC41CFBDEFB2EF2AD617BEC1
                    E078039A9641B9A0411B7914954A1AD9AC75DA30C414CDFE4EF99EE1723600F6
                    BDF7216AB55A6BE5EA960CA7BCC65514097829C29A3B7E4132F948E0A185CACC
                    603496D93E3E1900BEA5174B5D01522AF3EAED126516EEB5A7D1FFE07748A69E
                    84AEF76C5A08CE7A11F6E531053908E86709F82D06B06DBB05A0402E736486A1
                    281072906171D84E6E0A50E1CA6C1FFB89AB9F6AE5A1703606A8DEBA158447C9
                    0A746D3A007C8CBE07F6219D99E073BCBA5DB780EAEC2E053847E34A7E20E04C
                    0C6059960F8050BF6502CEFB005D2F300F2791DB79137A22DF75F5A97BC69148
                    C8D083D304FC1C0754CA103E007E9235FD2221339422DCC5630CD33A81A9AE00
                    37FF30FAFB9B6768FC57E6E06BE6602E06A8544AAC79D9AA23C998E30A0D1691
                    D02FC15B3A8CA187547F8395D3E7BF97DB799D1E0DFB002BFD1446464A932D80
                    768280660C502ADD8C92EC271A3586E70212DA1CBDB90AB1F4993F3FBD6D37C1
                    0E9AFF1483F27C031767E731B663FE031A3F49E37FC736DADED7DE42B3B11A25
                    19116485B1BD4410F784B6CC7095A8D5F1E3706612526491EAD98A7353F378FC
                    B13F9F50B1EF7A54284063B5DE5145A20D4215498649D35768DCE28B04681E87
                    74027AD197BD1FC74F14B0F7F5890D251603ACD6ED68A3B500AA2102A0CB870A
                    3DA16865CEF1203C039E303194DF852347BFC0FEF70F7405E804780A50AB5769
                    AF1D9ED003E1971522CF5A7DD25F809ABF65642B019F6F0AE82160CD07D85647
                    92C33349B49F6508EAEC9330CC1CBE3AFEA50224E017F946804140F5C5175EE6
                    69EAB457AF6E213B8022285F74E4A8D53607864280FA10354348081822A0A400
                    AEB30EDFF95612028888562FD1F6A43D476060E0AE1030CC5E8BA2CE7D190206
                    08B014E04EAE00B0456D6C753C757A9039F0D1FE6386693C7F2700AB629D3A34
                    71F85536EDC08308A0B43AE80D8AFAB8AB73204D51094B053A19CCD3FD44B445
                    C55B7DA5AA1DE20563B7FFAFE2BF5EFF02A8A671371FD8FBD30000000049454E
                    44AE426082}
                  ImageId = 6
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
                object btnAlterarItem: TFButton
                  Left = 48
                  Top = 0
                  Width = 48
                  Height = 35
                  Hint = 'Alterar Item'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnAlterarItemClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000003094944415478DAB5955B4853711CC7BF53A7E1D26990E14374F172BA
                    5850910F163DFA641245049104654A48D04BB59C5A14A5250425157651307B31
                    ED02068BE841891E4C66D843584FBA34DD76B6E32EB1D13CFFFEFF73F3B88B4E
                    C13FFBEFFF3FE7FCF7FDFCAE6706ACF230E82F5AEEDEAAA14BFB4A8422E1C89E
                    86C66BDF9602909AEAF3C8CDCD5D96B8DD6EC7C74F367C1EFC52D2DFFFFE07BD
                    2526045CB9548F4020B0408010A24D7A052212F68D81810194951D405E5E9E74
                    EE4EEB6DD88747B89E9EDE9F4901241955987D88C8BE24F38828C266B3A1A2E2
                    30CC663382C1204C269304B15CB61A96043C7CFC60D1B0549F3987376FDF81F7
                    38639E2D09F0FBFD3ACB45885268945051CB89EC0EC6271CE05D2E0C0D7F9584
                    AC0D4DC979E09B9D8D0A8F2429C79E88DA338FC78BC8BF3084591FDD7B505979
                    0423559A64D5DE6E74C705088280F6A78F965549A5FBF623A7B31C9C05C8F875
                    08A37D83A000437C80D70366274428162BABEC060D1914AF4418D38CF81B0C80
                    6F2CD4C4C3438318732031C0EBE5218A4A49AA252A12A582E4FBFAE74ECBA618
                    713A9A28E0665C00CFBBF1ACE34952A1291FB526144F9864B7DB199BE0051524
                    87CC7975738CF8F78DC771BAF99541D12671012EE78C5629D0BA58D481085CF5
                    B1E2DC8D61B4F57E50CB3431C039338DCEAE8E6585656D6D1F8A0E1E55FB2045
                    4A50A210CD4C4FC99646558FEC09E0B66EC1EE63F3E239D7C7248FB716708B36
                    5A0A05CC31C09FE949AD6AF4B9A0050ADE5AC0CA0F75274A71D638442DEF454A
                    7E8974BEB0689B0A4885F246D503D229202C01A61CE87AF9226E78E88F613FC5
                    42F21AA9F93B34AFD85A54BC7D51401605F81860727262DE7AF5F5AC4BB65CFF
                    984FBEB22FE676AA00133D1462103D601D05F00CF0DB312E7529D13A56295102
                    DDBB489F1FB9AB396E970A584FEF0A7446F40033050817EA2E22333333A9268B
                    1EA15008F7DBEE31C0067AE98906ACB136589E6765679D5C91BA3204AFD0D3D2
                    DC5A4BB77E3AE7F400B64F67B9A0339B4EE646069D2C6146654D53CEA975AE4E
                    166FF637E8D3CD394437DA6A8CFFFE8D6837CCAC0E4F0000000049454E44AE42
                    6082}
                  ImageId = 7
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
                object btnExcluirItem: TFButton
                  Left = 96
                  Top = 0
                  Width = 48
                  Height = 35
                  Hint = 'Excluir Item'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 2
                  OnClick = btnExcluirItemClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000004F24944415478DAB5965B6C145518C7FF736666AFA5DBEEB6DB6D4BE9
                    426B5B682D8AA912B50662C40B1A5F34A6B531A009D1102331519EF0457D3131
                    DE1E080F4653914B626262BC61141510039142A076DDDADBA6F4B2BB855E662F
                    DDDD99397E33B36C172C8D26BAD97FCE9CD933FFDFF9CEF9E63B2BE07FFE082B
                    FD7810D84C031E1519B6E81CED9CC349B775EA87A97F82749CFA9FF700FC5F01
                    C8D82D0A784F94C4AEFAA65A7B79A05CF4F8BD707A3DC825D358885DC56C7496
                    8F0F4F26530BA9014DC7530419FB470032BF8F09385ABDDAE769BD77A353D616
                    C1D20B404A81905600878BF0A5E0AE55E0AB7C183D17524317463314CD2B04D9
                    BF2280CC9BC8BC6FD35D2DEECA60007C7C082C350B511220524802B39EE03AAD
                    93C6A1494E08C11624942CCEFC703E914E657713A47759009933F238DFB4614D
                    6BDDBA2A918F8520316E99CB4201623C619AABA49CD5EA81B54868369CFEBE4F
                    A1485A0832F937C0A7C0DED252E7BE8E2DB7BBF5A17E48820A49B6CC0BADC4CC
                    27AE99AB399D645DE3968D18EE8FE44687267FE9E2D87A1D80666FA78B85CD9D
                    ED36477A16C27C1C928DC1F3C111B0721FB2FB9E81A8C49700AB2A21BD7E085A
                    7C0A8997BBAC4868B950DF8293C7CE26338BB947288A13C5800EBB4D3A7EF7D6
                    3B4AB4F085C28CBD477E86746B07F4B141645EDA0E91C0F0FA21BDF325587D13
                    D40BA7A1ECDCB61405DDFB636022333131F31A01DE2A06BCE0AFF0BCBD7EFD1A
                    A71E195C5A92AA00CA3EFA06E2DA6613B2489138DEE835CDB5B13012BB1E861A
                    8D5A119078452DA28A8AF040E46B5AA6ED05C021FA068381AE9A7207109F5CDA
                    D43CC4F3A105E1D90C049BBD60AEC562D67EE4F784BB3D48BABCE83B1B8A7573
                    5415008769BD1A1B6A3B7D76EACCC52DF3BC9804C8CD6D283B72D2343720F3DD
                    9DD0067F876EA4EB3580914D3617548AE2CCAFFDD9A7695F8B97E8CD1ABF776F
                    7DC023F2E8E52273013245E0FDE45B48EB96225047C298DBF190B93C66CA920C
                    102FF5629E7C43A1C8255AA2F662C0E3252E476FDBFA60A91E19B2CC29E7A540
                    005547BF83DCD00C75984CF7F4A0ECDD8390A89FA3FE4CF78304992E40987F35
                    C6E30A1F9F9CD94F11EC2E06D40A8230D2715BB34D1F1F03836A026ABFF8118E
                    3BEF416E288C78D736E857A2601555A83C6C4133E7CF62EAB14E13A06BF4A6D6
                    3722148E24E694D4F33DE6AB55F4A2D1465FACF3FB5AFDDE12A64F5F360135BD
                    9F5114D5883DFB04F4996861B468400E1C86766506D33B9EA4BDE0103C3EA425
                    072E118012B69100D11B4B453375FADA1AEA5CB251D4920A18B3EA8FD11A2385
                    6BA3C9819BE2E64653BA81D5D4E1E2C0486231A7BE48E61FDFACD8BDEAB2C9FB
                    9A83B52548500555E64D53032208D7175EC3DC8018D555ACF0E3F2442C1B9D53
                    4E7503F7AF544D194DE827A7246D6AA8AE743B6C22ADFB0C90CB5A11E4877323
                    042682797DD0653B4627628BB3A9B4F215F0C051A09F8668CB0164524004AAF7
                    003B37926A3CA5B2DF5BC644A306118467B310641982DD6E96ECB9850446E257
                    72C39CFFF63E70E02A10218F69D20449B9114027096A48D5865A800DBB80E72A
                    29C3A84E6B6E9B8D796499A5340D73193A5F545548D23144A972EC14708E9E99
                    CA9B1BED9FA4E472279A2D0F3040E40D1FD5C84A8AA6A91108D6D17D4A8DF961
                    32B944B3A51953F5C32C892ECDEB11586741F6A647E60DB012929B641CF654A8
                    CCD7DFD8DA1C6931AF85FC6CB3CB99ACF8AFE2BFF8FC05DEE5323773766BE800
                    00000049454E44AE426082}
                  ImageId = 8
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
                object btnSalvarItem: TFButton
                  Left = 144
                  Top = 0
                  Width = 48
                  Height = 35
                  Hint = 'Salvar Item'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 3
                  OnClick = btnSalvarItemClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3
                    0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220
                    AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950
                    4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5
                    5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37
                    35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067
                    603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7
                    29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B
                    DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA
                    B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B
                    A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194
                    B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002
                    EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715
                    87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B
                    AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9
                    32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66
                    5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E
                    4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05
                    05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF
                    0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76
                    093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0
                    C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65
                    C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70
                    9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D
                    80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12
                    B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8
                    6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087
                    94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF
                    0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34
                    2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2
                    134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000
                    0049454E44AE426082}
                  ImageId = 4
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
                object btnCancelarItem: TFButton
                  Left = 192
                  Top = 0
                  Width = 48
                  Height = 35
                  Hint = 'Cancelar Item'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 4
                  OnClick = btnCancelarItemClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F80000036F4944415478DAB5937B48537114C7CFDDBD9B5B375FA98569605A14
                    8A2414988A927F88996952948948149A6020998614BD0B7A5A448564A5882912
                    1469A9B3971AE5BB742DCC245F99736ACE747373BBAF7E8B4BDD86530BF783C3
                    FDFDF89EDFF7737EE77031B0F1C2E69B189B27D954BEDF546B4BC01486611965
                    29C63C9B00B6DD9150EECEDEF4F044DF0D8661B3D16BB805079CDBFD9028797B
                    D9D0A96AA962682601414C0B0AB898F894A03803C8DB8AA61BBA2A9434CD4422
                    C8F87F0150CF49D4F354424C6C060E5611205D7932BE18DA0DA5E0278D81775F
                    6AA827ADF92A86A1CDC3EFFB2740DC5D699608C34F6C5C1B49047885C99C644B
                    81B47384214601FDA6264030F0936D8501752F5B54776992A64D1108D23A2700
                    558D897051AECF32FFA49D41E98B285C0B8394020CAC06284C0FE84580C0FC45
                    1C7CA55B403761805B55D94686A5A310A4665600EAF3D9D51E0199311B92654D
                    E345306CFCFC57B2D9DC1CB8080767F10A085EB217DE763E31D677567D66192E
                    1C01345601A87A4F0921ED4A8B3A2F6B9C28041DFD1D569281E0245E0E8E2848
                    DC05BA74B5F071B212DC243EBFCC9F298AA795FD0DCD1C0BE6EAF5B3B6281655
                    BFC93FEE8887C772BC77AA09825CF6C017753B3730D2AD1FD27CE574FA49F240
                    F445AC515308EB9DE2A1AC39CFD0ADFE508DCC7721736ACE21C7E5DBBD4A0C3D
                    1CAEC6DAC0DF211AE41F0B8C9F7A143DC8E014923FA0501EDD51404C535A7850
                    7F5D3FA8E92E465AEA5C3FDC1FC03DBBEEB4C8CBDE2AFA3DC8287728A9CBE945
                    0601C860926F2195BEF52A51F2FA8A614CABCA294B311D87792C21A02C213433
                    D6D1C11106C6BAA0A2E97EEDE37DC670C18C287B9933A39D1ECF2A4F31DD9C8F
                    B9E5900F86F86EB910B236D66E4CA782FC17674738965B835EF083D7B5E89382
                    CEA5F335B704D8E338DE931C71DAD595F484965E39FDBCBD5439D8C286B5DEA6
                    8D9BAF89C3E419D41BC15D4E102C1FD6013C24DACDC1F3414268C6227BA90BBC
                    EA28353574C8ABCB534D49481659E4B3BC39C3EF193E68AB00F339FA96248924
                    C9DCED41A9322F177FEC666526A5908F062A4B1835D271FE0E3B83A9F04B5903
                    98AB24C2CF88D739B8638F5C17AF583AAAFD36F8F298316C6AE4978EF339ACC0
                    90B6D8D3B3017E43CC117C88F0EE79C98CAA151C25A85ED822D60246F17B6E36
                    8050C305558BAC001881293793894D97CD013F01E2D89E284DE0BF3E00000000
                    49454E44AE426082}
                  ImageId = 9
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
              end
              object pgItem: TFPageControl
                Left = 0
                Top = 42
                Width = 442
                Height = 221
                ActivePage = pgItemtabCadastro
                TabOrder = 1
                TabPosition = tpTop
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                WOwner = FrInterno
                WOrigem = EhNone
                RenderStyle = rsTabbed
                object pgItemtabCadastro: TFTabsheet
                  Caption = 'Menu'
                  Visible = True
                  Closable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  object FVBoxPgItemPrincipalCadastro: TFVBox
                    Left = 0
                    Top = 0
                    Width = 434
                    Height = 193
                    Align = alClient
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 3
                    Padding.Left = 0
                    Padding.Right = 5
                    Padding.Bottom = 3
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FHBox27: TFHBox
                      Left = 0
                      Top = 0
                      Width = 422
                      Height = 41
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 5
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object FHBox28: TFHBox
                        Left = 0
                        Top = 0
                        Width = 77
                        Height = 37
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        Padding.Top = 5
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        VAlign = tvTop
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                        object FHBox29: TFHBox
                          Left = 0
                          Top = 0
                          Width = 9
                          Height = 31
                          AutoWrap = False
                          BevelKind = bkTile
                          BevelOuter = bvNone
                          BorderStyle = stNone
                          Caption = ' '
                          Padding.Top = 0
                          Padding.Left = 0
                          Padding.Right = 0
                          Padding.Bottom = 0
                          TabOrder = 0
                          Margin.Top = 0
                          Margin.Left = 0
                          Margin.Right = 0
                          Margin.Bottom = 0
                          Spacing = 1
                          Flex.Vflex = ftTrue
                          Flex.Hflex = ftTrue
                          Scrollable = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          BoxShadowConfig.HorizontalLength = 10
                          BoxShadowConfig.VerticalLength = 10
                          BoxShadowConfig.BlurRadius = 5
                          BoxShadowConfig.SpreadRadius = 0
                          BoxShadowConfig.ShadowColor = clBlack
                          BoxShadowConfig.Opacity = 75
                          VAlign = tvTop
                          BorderRadius.TopLeft = 0
                          BorderRadius.TopRight = 0
                          BorderRadius.BottomRight = 0
                          BorderRadius.BottomLeft = 0
                        end
                        object FLabel1: TFLabel
                          Left = 9
                          Top = 0
                          Width = 49
                          Height = 13
                          Caption = 'Sequ'#234'ncia'
                          Font.Charset = DEFAULT_CHARSET
                          Font.Color = clWindowText
                          Font.Height = -11
                          Font.Name = 'Tahoma'
                          Font.Style = []
                          ParentFont = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          VerticalAlignment = taVerticalCenter
                          WordBreak = False
                          MaskType = mtText
                        end
                      end
                      object edtSequencia: TFInteger
                        Left = 77
                        Top = 0
                        Width = 121
                        Height = 24
                        Table = tbLeadzapItem
                        FieldName = 'SEQUENCIA'
                        TabOrder = 0
                        AccessLevel = 0
                        Flex = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Required = False
                        Constraint.CheckWhen = cwImmediate
                        Constraint.CheckType = ctExpression
                        Constraint.FocusOnError = False
                        Constraint.EnableUI = True
                        Constraint.Enabled = False
                        Constraint.FormCheck = True
                        IconDirection = idLeft
                        Maxlength = 0
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clWindowText
                        Font.Height = -13
                        Font.Name = 'Tahoma'
                        Font.Style = []
                        Alignment = taRightJustify
                      end
                    end
                    object FHBox12: TFHBox
                      Left = 0
                      Top = 42
                      Width = 422
                      Height = 41
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 5
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object FHBox13: TFHBox
                        Left = 0
                        Top = 0
                        Width = 77
                        Height = 37
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        Padding.Top = 5
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        VAlign = tvTop
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                        object FHBox19: TFHBox
                          Left = 0
                          Top = 0
                          Width = 9
                          Height = 31
                          AutoWrap = False
                          BevelKind = bkTile
                          BevelOuter = bvNone
                          BorderStyle = stNone
                          Caption = ' '
                          Padding.Top = 0
                          Padding.Left = 0
                          Padding.Right = 0
                          Padding.Bottom = 0
                          TabOrder = 0
                          Margin.Top = 0
                          Margin.Left = 0
                          Margin.Right = 0
                          Margin.Bottom = 0
                          Spacing = 1
                          Flex.Vflex = ftTrue
                          Flex.Hflex = ftTrue
                          Scrollable = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          BoxShadowConfig.HorizontalLength = 10
                          BoxShadowConfig.VerticalLength = 10
                          BoxShadowConfig.BlurRadius = 5
                          BoxShadowConfig.SpreadRadius = 0
                          BoxShadowConfig.ShadowColor = clBlack
                          BoxShadowConfig.Opacity = 75
                          VAlign = tvTop
                          BorderRadius.TopLeft = 0
                          BorderRadius.TopRight = 0
                          BorderRadius.BottomRight = 0
                          BorderRadius.BottomLeft = 0
                        end
                        object lblDescricaoItem: TFLabel
                          Left = 9
                          Top = 0
                          Width = 46
                          Height = 13
                          Caption = 'Descri'#231#227'o'
                          Font.Charset = DEFAULT_CHARSET
                          Font.Color = clWindowText
                          Font.Height = -11
                          Font.Name = 'Tahoma'
                          Font.Style = []
                          ParentFont = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          VerticalAlignment = taVerticalCenter
                          WordBreak = False
                          MaskType = mtText
                        end
                      end
                      object edtDescricaoItem: TFString
                        Left = 77
                        Top = 0
                        Width = 121
                        Height = 24
                        Table = tbLeadzapItem
                        FieldName = 'DESCRICAO'
                        TabOrder = 0
                        AccessLevel = 0
                        Flex = True
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Required = False
                        Constraint.CheckWhen = cwImmediate
                        Constraint.CheckType = ctExpression
                        Constraint.FocusOnError = False
                        Constraint.EnableUI = True
                        Constraint.Enabled = False
                        Constraint.FormCheck = True
                        IconDirection = idLeft
                        CharCase = ccNormal
                        Pwd = False
                        Maxlength = 0
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clWindowText
                        Font.Height = -13
                        Font.Name = 'Tahoma'
                        Font.Style = []
                        SaveLiteralCharacter = False
                        TextAlign = taLeft
                      end
                    end
                    object FHBox21: TFHBox
                      Left = 0
                      Top = 84
                      Width = 422
                      Height = 41
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 2
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 5
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object FHBox22: TFHBox
                        Left = 0
                        Top = 0
                        Width = 77
                        Height = 37
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        Padding.Top = 5
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        VAlign = tvTop
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                        object FHBox23: TFHBox
                          Left = 0
                          Top = 0
                          Width = 9
                          Height = 31
                          AutoWrap = False
                          BevelKind = bkTile
                          BevelOuter = bvNone
                          BorderStyle = stNone
                          Caption = ' '
                          Padding.Top = 0
                          Padding.Left = 0
                          Padding.Right = 0
                          Padding.Bottom = 0
                          TabOrder = 0
                          Margin.Top = 0
                          Margin.Left = 0
                          Margin.Right = 0
                          Margin.Bottom = 0
                          Spacing = 1
                          Flex.Vflex = ftTrue
                          Flex.Hflex = ftTrue
                          Scrollable = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          BoxShadowConfig.HorizontalLength = 10
                          BoxShadowConfig.VerticalLength = 10
                          BoxShadowConfig.BlurRadius = 5
                          BoxShadowConfig.SpreadRadius = 0
                          BoxShadowConfig.ShadowColor = clBlack
                          BoxShadowConfig.Opacity = 75
                          VAlign = tvTop
                          BorderRadius.TopLeft = 0
                          BorderRadius.TopRight = 0
                          BorderRadius.BottomRight = 0
                          BorderRadius.BottomLeft = 0
                        end
                        object lblArea: TFLabel
                          Left = 9
                          Top = 0
                          Width = 23
                          Height = 13
                          Caption = 'Area'
                          Font.Charset = DEFAULT_CHARSET
                          Font.Color = clWindowText
                          Font.Height = -11
                          Font.Name = 'Tahoma'
                          Font.Style = []
                          ParentFont = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          VerticalAlignment = taVerticalCenter
                          WordBreak = False
                          MaskType = mtText
                        end
                      end
                      object cbbArea: TFCombo
                        Left = 77
                        Top = 0
                        Width = 145
                        Height = 21
                        Table = tbLeadzapItem
                        LookupTable = tbLeadzapArea
                        FieldName = 'ID_AREA'
                        LookupKey = 'ID_AREA'
                        LookupDesc = 'DESCRICAO'
                        Flex = True
                        ReadOnly = True
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Required = False
                        Prompt = 'Selecione'
                        Constraint.CheckWhen = cwImmediate
                        Constraint.CheckType = ctExpression
                        Constraint.FocusOnError = False
                        Constraint.EnableUI = True
                        Constraint.Enabled = False
                        Constraint.FormCheck = True
                        ClearOnDelKey = True
                        UseClearButton = False
                        HideClearButtonOnNullValue = False
                        OnChange = cbbAreaChange
                        Colors = <>
                        Images = <>
                        Masks = <>
                        Fonts = <>
                        MultiSelection = False
                        IconReverseDirection = False
                      end
                    end
                    object hboxTipoEvento: TFHBox
                      Left = 0
                      Top = 126
                      Width = 422
                      Height = 41
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 3
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 5
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object FHBox25: TFHBox
                        Left = 0
                        Top = 0
                        Width = 77
                        Height = 37
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        Padding.Top = 5
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        VAlign = tvTop
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                        object FHBox26: TFHBox
                          Left = 0
                          Top = 0
                          Width = 9
                          Height = 31
                          AutoWrap = False
                          BevelKind = bkTile
                          BevelOuter = bvNone
                          BorderStyle = stNone
                          Caption = ' '
                          Padding.Top = 0
                          Padding.Left = 0
                          Padding.Right = 0
                          Padding.Bottom = 0
                          TabOrder = 0
                          Margin.Top = 0
                          Margin.Left = 0
                          Margin.Right = 0
                          Margin.Bottom = 0
                          Spacing = 1
                          Flex.Vflex = ftTrue
                          Flex.Hflex = ftTrue
                          Scrollable = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          BoxShadowConfig.HorizontalLength = 10
                          BoxShadowConfig.VerticalLength = 10
                          BoxShadowConfig.BlurRadius = 5
                          BoxShadowConfig.SpreadRadius = 0
                          BoxShadowConfig.ShadowColor = clBlack
                          BoxShadowConfig.Opacity = 75
                          VAlign = tvTop
                          BorderRadius.TopLeft = 0
                          BorderRadius.TopRight = 0
                          BorderRadius.BottomRight = 0
                          BorderRadius.BottomLeft = 0
                        end
                        object lblTipoEvento: TFLabel
                          Left = 9
                          Top = 0
                          Width = 57
                          Height = 13
                          Caption = 'Tipo Evento'
                          Font.Charset = DEFAULT_CHARSET
                          Font.Color = clWindowText
                          Font.Height = -11
                          Font.Name = 'Tahoma'
                          Font.Style = []
                          ParentFont = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          VerticalAlignment = taVerticalCenter
                          WordBreak = False
                          MaskType = mtText
                        end
                      end
                      object cbbTipoEvento: TFCombo
                        Left = 77
                        Top = 0
                        Width = 145
                        Height = 21
                        Table = tbLeadzapItem
                        LookupTable = tbEventosTipo
                        FieldName = 'COD_TIPO_EVENTO'
                        LookupKey = 'COD_TIPO_EVENTO'
                        LookupDesc = 'DESC_TIPO_EVENTO'
                        Flex = True
                        ReadOnly = True
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Required = False
                        Prompt = 'Selecione'
                        Constraint.CheckWhen = cwImmediate
                        Constraint.CheckType = ctExpression
                        Constraint.FocusOnError = False
                        Constraint.EnableUI = True
                        Constraint.Enabled = False
                        Constraint.FormCheck = True
                        ClearOnDelKey = True
                        UseClearButton = False
                        HideClearButtonOnNullValue = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        Fonts = <>
                        MultiSelection = False
                        IconReverseDirection = False
                      end
                    end
                  end
                end
                object pgItemtabTemplates: TFTabsheet
                  Caption = 'Templates'
                  Visible = True
                  Closable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  ExplicitLeft = 0
                  ExplicitTop = 0
                  ExplicitWidth = 0
                  ExplicitHeight = 0
                  object FVBoxPrincipalTemplatesPgItem: TFVBox
                    Left = 0
                    Top = 0
                    Width = 434
                    Height = 193
                    Align = alClient
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 3
                    Padding.Left = 5
                    Padding.Right = 5
                    Padding.Bottom = 3
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FVBoxPrincipalTemplatesPgItemItem1: TFHBox
                      Left = 0
                      Top = 0
                      Width = 425
                      Height = 41
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 5
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object FVBoxPrincipalTemplatesPgItemLabel1: TFHBox
                        Left = 0
                        Top = 0
                        Width = 177
                        Height = 37
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        Padding.Top = 5
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        VAlign = tvTop
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                        object FVBoxPrincipalTemplatesPgItemRecuo1: TFHBox
                          Left = 0
                          Top = 0
                          Width = 9
                          Height = 31
                          AutoWrap = False
                          BevelKind = bkTile
                          BevelOuter = bvNone
                          BorderStyle = stNone
                          Caption = ' '
                          Padding.Top = 0
                          Padding.Left = 0
                          Padding.Right = 0
                          Padding.Bottom = 0
                          TabOrder = 0
                          Margin.Top = 0
                          Margin.Left = 0
                          Margin.Right = 0
                          Margin.Bottom = 0
                          Spacing = 1
                          Flex.Vflex = ftTrue
                          Flex.Hflex = ftTrue
                          Scrollable = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          BoxShadowConfig.HorizontalLength = 10
                          BoxShadowConfig.VerticalLength = 10
                          BoxShadowConfig.BlurRadius = 5
                          BoxShadowConfig.SpreadRadius = 0
                          BoxShadowConfig.ShadowColor = clBlack
                          BoxShadowConfig.Opacity = 75
                          VAlign = tvTop
                          BorderRadius.TopLeft = 0
                          BorderRadius.TopRight = 0
                          BorderRadius.BottomRight = 0
                          BorderRadius.BottomLeft = 0
                        end
                        object FLabelPrincipalTemplatesPgItem1: TFLabel
                          Left = 9
                          Top = 0
                          Width = 106
                          Height = 13
                          Caption = 'Boas Vindas Vendedor'
                          Font.Charset = DEFAULT_CHARSET
                          Font.Color = clWindowText
                          Font.Height = -11
                          Font.Name = 'Tahoma'
                          Font.Style = []
                          ParentFont = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          VerticalAlignment = taVerticalCenter
                          WordBreak = False
                          MaskType = mtText
                        end
                      end
                      object cbbIdTemplateBoasVIndasVendedor: TFCombo
                        Left = 177
                        Top = 0
                        Width = 145
                        Height = 21
                        Table = tbLeadzapItem
                        LookupTable = tbTemplateLead
                        FieldName = 'ID_TEMPATE_VEND_CONTATO'
                        LookupKey = 'ID_EMAIL_MODELO'
                        LookupDesc = 'MODELO'
                        Flex = True
                        ReadOnly = True
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Required = False
                        Prompt = 'Selecione o Template'
                        Constraint.CheckWhen = cwImmediate
                        Constraint.CheckType = ctExpression
                        Constraint.FocusOnError = False
                        Constraint.EnableUI = True
                        Constraint.Enabled = False
                        Constraint.FormCheck = True
                        ClearOnDelKey = True
                        UseClearButton = False
                        HideClearButtonOnNullValue = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        Fonts = <>
                        MultiSelection = False
                        IconReverseDirection = False
                      end
                    end
                    object FVBoxPrincipalTemplatesPgItemItem2: TFHBox
                      Left = 0
                      Top = 42
                      Width = 425
                      Height = 41
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 5
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object FVBoxPrincipalTemplatesPgItemLabel2: TFHBox
                        Left = 0
                        Top = 0
                        Width = 177
                        Height = 37
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        Padding.Top = 5
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        VAlign = tvTop
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                        object FVBoxPrincipalTemplatesPgItemRecuo2: TFHBox
                          Left = 0
                          Top = 0
                          Width = 9
                          Height = 31
                          AutoWrap = False
                          BevelKind = bkTile
                          BevelOuter = bvNone
                          BorderStyle = stNone
                          Caption = ' '
                          Padding.Top = 0
                          Padding.Left = 0
                          Padding.Right = 0
                          Padding.Bottom = 0
                          TabOrder = 0
                          Margin.Top = 0
                          Margin.Left = 0
                          Margin.Right = 0
                          Margin.Bottom = 0
                          Spacing = 1
                          Flex.Vflex = ftTrue
                          Flex.Hflex = ftTrue
                          Scrollable = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          BoxShadowConfig.HorizontalLength = 10
                          BoxShadowConfig.VerticalLength = 10
                          BoxShadowConfig.BlurRadius = 5
                          BoxShadowConfig.SpreadRadius = 0
                          BoxShadowConfig.ShadowColor = clBlack
                          BoxShadowConfig.Opacity = 75
                          VAlign = tvTop
                          BorderRadius.TopLeft = 0
                          BorderRadius.TopRight = 0
                          BorderRadius.BottomRight = 0
                          BorderRadius.BottomLeft = 0
                        end
                        object FLabelPrincipalTemplatesPgItem2: TFLabel
                          Left = 9
                          Top = 0
                          Width = 106
                          Height = 13
                          Caption = 'Boas Vindas Consultor'
                          Font.Charset = DEFAULT_CHARSET
                          Font.Color = clWindowText
                          Font.Height = -11
                          Font.Name = 'Tahoma'
                          Font.Style = []
                          ParentFont = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          VerticalAlignment = taVerticalCenter
                          WordBreak = False
                          MaskType = mtText
                        end
                      end
                      object cbbIdTemplateBoasVIndasConsultor: TFCombo
                        Left = 177
                        Top = 0
                        Width = 145
                        Height = 21
                        Table = tbLeadzapItem
                        LookupTable = tbTemplateLead
                        FieldName = 'ID_TEMPATE_ATEND_CONTATO'
                        LookupKey = 'ID_EMAIL_MODELO'
                        LookupDesc = 'MODELO'
                        Flex = True
                        ReadOnly = True
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Required = False
                        Prompt = 'Selecione o Template'
                        Constraint.CheckWhen = cwImmediate
                        Constraint.CheckType = ctExpression
                        Constraint.FocusOnError = False
                        Constraint.EnableUI = True
                        Constraint.Enabled = False
                        Constraint.FormCheck = True
                        ClearOnDelKey = True
                        UseClearButton = False
                        HideClearButtonOnNullValue = False
                        OnChange = cbbIdTemplateJaExisteAtendimentoVendedorChange
                        Colors = <>
                        Images = <>
                        Masks = <>
                        Fonts = <>
                        MultiSelection = False
                        IconReverseDirection = False
                      end
                    end
                    object FVBoxPrincipalTemplatesPgItemItem3: TFHBox
                      Left = 0
                      Top = 84
                      Width = 425
                      Height = 41
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 2
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 5
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object FVBoxPrincipalTemplatesPgItemLabel3: TFHBox
                        Left = 0
                        Top = 0
                        Width = 177
                        Height = 37
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        Padding.Top = 5
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        VAlign = tvTop
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                        object FVBoxPrincipalTemplatesPgItemRecuo3: TFHBox
                          Left = 0
                          Top = 0
                          Width = 9
                          Height = 31
                          AutoWrap = False
                          BevelKind = bkTile
                          BevelOuter = bvNone
                          BorderStyle = stNone
                          Caption = ' '
                          Padding.Top = 0
                          Padding.Left = 0
                          Padding.Right = 0
                          Padding.Bottom = 0
                          TabOrder = 0
                          Margin.Top = 0
                          Margin.Left = 0
                          Margin.Right = 0
                          Margin.Bottom = 0
                          Spacing = 1
                          Flex.Vflex = ftTrue
                          Flex.Hflex = ftTrue
                          Scrollable = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          BoxShadowConfig.HorizontalLength = 10
                          BoxShadowConfig.VerticalLength = 10
                          BoxShadowConfig.BlurRadius = 5
                          BoxShadowConfig.SpreadRadius = 0
                          BoxShadowConfig.ShadowColor = clBlack
                          BoxShadowConfig.Opacity = 75
                          VAlign = tvTop
                          BorderRadius.TopLeft = 0
                          BorderRadius.TopRight = 0
                          BorderRadius.BottomRight = 0
                          BorderRadius.BottomLeft = 0
                        end
                        object FLabelPrincipalTemplatesPgItem3: TFLabel
                          Left = 9
                          Top = 0
                          Width = 155
                          Height = 13
                          Caption = 'J'#225' existe atendimento Vendedor'
                          Font.Charset = DEFAULT_CHARSET
                          Font.Color = clWindowText
                          Font.Height = -11
                          Font.Name = 'Tahoma'
                          Font.Style = []
                          ParentFont = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          VerticalAlignment = taVerticalCenter
                          WordBreak = False
                          MaskType = mtText
                        end
                      end
                      object cbbIdTemplateJaExisteAtendimentoVendedor: TFCombo
                        Left = 177
                        Top = 0
                        Width = 145
                        Height = 21
                        Table = tbLeadzapItem
                        LookupTable = tbTemplateLead
                        FieldName = 'ID_TEMPATE_VEND_EXIST_ATD'
                        LookupKey = 'ID_EMAIL_MODELO'
                        LookupDesc = 'MODELO'
                        Flex = True
                        ReadOnly = True
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Required = False
                        Prompt = 'Selecione o Template'
                        Constraint.CheckWhen = cwImmediate
                        Constraint.CheckType = ctExpression
                        Constraint.FocusOnError = False
                        Constraint.EnableUI = True
                        Constraint.Enabled = False
                        Constraint.FormCheck = True
                        ClearOnDelKey = True
                        UseClearButton = False
                        HideClearButtonOnNullValue = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        Fonts = <>
                        MultiSelection = False
                        IconReverseDirection = False
                      end
                    end
                    object FVBoxPrincipalTemplatesPgItemItem4: TFHBox
                      Left = 0
                      Top = 126
                      Width = 425
                      Height = 41
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 3
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 5
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object FVBoxPrincipalTemplatesPgItemLabel4: TFHBox
                        Left = 0
                        Top = 0
                        Width = 177
                        Height = 37
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        Padding.Top = 5
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        VAlign = tvTop
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                        object FVBoxPrincipalTemplatesPgItemRecuo4: TFHBox
                          Left = 0
                          Top = 0
                          Width = 9
                          Height = 31
                          AutoWrap = False
                          BevelKind = bkTile
                          BevelOuter = bvNone
                          BorderStyle = stNone
                          Caption = ' '
                          Padding.Top = 0
                          Padding.Left = 0
                          Padding.Right = 0
                          Padding.Bottom = 0
                          TabOrder = 0
                          Margin.Top = 0
                          Margin.Left = 0
                          Margin.Right = 0
                          Margin.Bottom = 0
                          Spacing = 1
                          Flex.Vflex = ftTrue
                          Flex.Hflex = ftTrue
                          Scrollable = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          BoxShadowConfig.HorizontalLength = 10
                          BoxShadowConfig.VerticalLength = 10
                          BoxShadowConfig.BlurRadius = 5
                          BoxShadowConfig.SpreadRadius = 0
                          BoxShadowConfig.ShadowColor = clBlack
                          BoxShadowConfig.Opacity = 75
                          VAlign = tvTop
                          BorderRadius.TopLeft = 0
                          BorderRadius.TopRight = 0
                          BorderRadius.BottomRight = 0
                          BorderRadius.BottomLeft = 0
                        end
                        object FLabelPrincipalTemplatesPgItem4: TFLabel
                          Left = 9
                          Top = 0
                          Width = 155
                          Height = 13
                          Caption = 'J'#225' existe atendimento Consultor'
                          Font.Charset = DEFAULT_CHARSET
                          Font.Color = clWindowText
                          Font.Height = -11
                          Font.Name = 'Tahoma'
                          Font.Style = []
                          ParentFont = False
                          WOwner = FrInterno
                          WOrigem = EhNone
                          VerticalAlignment = taVerticalCenter
                          WordBreak = False
                          MaskType = mtText
                        end
                      end
                      object cbbIdTemplateJaExisteAtendimentoConsultor: TFCombo
                        Left = 177
                        Top = 0
                        Width = 145
                        Height = 21
                        Table = tbLeadzapItem
                        LookupTable = tbTemplateLead
                        FieldName = 'ID_TEMPATE_ATEND_EXIST_ATD'
                        LookupKey = 'ID_EMAIL_MODELO'
                        LookupDesc = 'MODELO'
                        Flex = True
                        ReadOnly = True
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Required = False
                        Prompt = 'Selecione o Template'
                        Constraint.CheckWhen = cwImmediate
                        Constraint.CheckType = ctExpression
                        Constraint.FocusOnError = False
                        Constraint.EnableUI = True
                        Constraint.Enabled = False
                        Constraint.FormCheck = True
                        ClearOnDelKey = True
                        UseClearButton = False
                        HideClearButtonOnNullValue = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        Fonts = <>
                        MultiSelection = False
                        IconReverseDirection = False
                      end
                    end
                  end
                end
              end
            end
            object FVBox6: TFVBox
              Left = 444
              Top = 0
              Width = 420
              Height = 268
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 7
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object groupBoxAtendimento: TFGroupbox
                Left = 0
                Top = 0
                Width = 414
                Height = 197
                Caption = 'Atendimento'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                ParentFont = False
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                WOwner = FrInterno
                WOrigem = EhNone
                Scrollable = False
                Closable = False
                Closed = False
                Orient = coHorizontal
                Style = grpLine
                HeaderImageId = 0
                object FVBox8: TFVBox
                  Left = 7
                  Top = 23
                  Width = 407
                  Height = 164
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 5
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox30: TFHBox
                    Left = 0
                    Top = 0
                    Width = 402
                    Height = 54
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 5
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FHBox31: TFHBox
                      Left = 0
                      Top = 0
                      Width = 118
                      Height = 37
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 5
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object FHBox32: TFHBox
                        Left = 0
                        Top = 0
                        Width = 9
                        Height = 31
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        Padding.Top = 0
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftTrue
                        Flex.Hflex = ftTrue
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        VAlign = tvTop
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                      end
                      object FLabel4: TFLabel
                        Left = 9
                        Top = 0
                        Width = 102
                        Height = 13
                        Caption = 'Time que vai Atender'
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clWindowText
                        Font.Height = -11
                        Font.Name = 'Tahoma'
                        Font.Style = []
                        ParentFont = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        VerticalAlignment = taVerticalCenter
                        WordBreak = False
                        MaskType = mtText
                      end
                    end
                    object FVBox7: TFVBox
                      Left = 118
                      Top = 0
                      Width = 273
                      Height = 48
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftTrue
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object cbbTime: TFCombo
                        Left = 0
                        Top = 0
                        Width = 145
                        Height = 21
                        Table = tbLeadzapItem
                        LookupTable = tbTime
                        FieldName = 'ID_TIME'
                        LookupKey = 'ID_TIME'
                        LookupDesc = 'DESCRICAO'
                        Flex = True
                        ReadOnly = True
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Required = False
                        Prompt = 'Selecione'
                        Constraint.CheckWhen = cwImmediate
                        Constraint.CheckType = ctExpression
                        Constraint.FocusOnError = False
                        Constraint.EnableUI = True
                        Constraint.Enabled = False
                        Constraint.FormCheck = True
                        ClearOnDelKey = True
                        UseClearButton = False
                        HideClearButtonOnNullValue = False
                        OnChange = cbbTimeChange
                        Colors = <>
                        Images = <>
                        Masks = <>
                        Fonts = <>
                        MultiSelection = False
                        IconReverseDirection = False
                      end
                      object FLabel6: TFLabel
                        Left = 0
                        Top = 22
                        Width = 299
                        Height = 13
                        Caption = '(A empresa do vendedor escolhido ser'#225' a empresa do evento)'
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clRed
                        Font.Height = -11
                        Font.Name = 'Tahoma'
                        Font.Style = []
                        ParentFont = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        VerticalAlignment = taVerticalCenter
                        WordBreak = False
                        MaskType = mtText
                      end
                    end
                  end
                  object FLabel5: TFLabel
                    Left = 0
                    Top = 55
                    Width = 161
                    Height = 13
                    Caption = 'Exce'#231#245'es(Somente para Ve'#237'culos)'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clRed
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object chkUltimoAgenteEvento: TFCheckBox
                    Left = 0
                    Top = 69
                    Width = 398
                    Height = 17
                    Caption = 
                      'Pega o agente do ultimo evento encerrado, se achar, ignorando a ' +
                      'fila'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 1
                    Table = tbLeadzapItem
                    FieldName = 'PREF_ULT_AGENTE'
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taAlignTop
                  end
                  object chkEventoPerdido: TFCheckBox
                    Left = 0
                    Top = 87
                    Width = 396
                    Height = 17
                    Caption = 
                      'Se achar evento perdido, reabre e utiliza. o Agente do evento fi' +
                      'ca respons'#225'vel, ignorando a fila'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 2
                    Table = tbLeadzapItem
                    FieldName = 'EVENTO_PERDIDO_REABRE'
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taAlignTop
                  end
                  object chkCentralUnica: TFCheckBox
                    Left = 0
                    Top = 105
                    Width = 396
                    Height = 17
                    Hint = 
                      'Direciona atendimento para qualqer Vendedor do time, caso contr'#225 +
                      'rio vai direcionar para Vendedor do time mas que seja da mesma E' +
                      'mpresa selecionada.'
                    Caption = 'Central Unica'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 3
                    Table = tbLeadzapItem
                    FieldName = 'CENTRAL_UNICA'
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taAlignTop
                  end
                end
              end
            end
          end
        end
      end
    end
  end
  object tbLeadzapItem: TFTable
    FieldDefs = <
      item
        Name = 'ID_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_AREA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Area'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREF_ULT_AGENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pref Ult Agente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVENTO_PERDIDO_REABRE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Evento Perdido Reabre'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CENTRAL_UNICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Central Unica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPATE_VEND_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Tempate Vend Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPATE_ATEND_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Tempate Atend Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPATE_VEND_EXIST_ATD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Tempate Vend Exist Atd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPATE_ATEND_EXIST_ATD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Tempate Atend Exist Atd'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_LEADZAP_ITEM'
    Cursor = 'CRM_LEADZAP_ITEM'
    MaxRowCount = 200
    OnAfterScroll = tbLeadzapItemAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadzapArea: TFTable
    FieldDefs = <
      item
        Name = 'ID_AREA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Area'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_LEADZAP_AREA'
    Cursor = 'CRM_LEADZAP_AREA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadzapMenu: TFTable
    FieldDefs = <
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENU_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Menu Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPLATE_QUALIFICADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template Qualificado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPLATE_LEAD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template Lead'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LEAD_PERGUNTA_NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Lead Pergunta Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LEAD_PERGUNTA_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Lead Pergunta Email'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAD_DUPLO_MOSTRA_AO_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cad Duplo Mostra '#195'o Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPLATE_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template Vendedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPLATE_VENDEDOR_PARTS_REP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template Vendedor Parts Rep'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPLATE_VENDEDOR_PARTS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template Vendedor Parts'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_LEADZAP_MENU'
    Cursor = 'CRM_LEADZAP_MENU'
    MaxRowCount = 200
    OnAfterScroll = tbLeadzapMenuAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTemplateQualificado: TFTable
    FieldDefs = <
      item
        Name = 'ID_EMAIL_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Email Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_MODELO'
    Cursor = 'CRM_EMAIL_MODELO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTemplateLead: TFTable
    FieldDefs = <
      item
        Name = 'ID_EMAIL_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Email Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_MODELO'
    Cursor = 'CRM_EMAIL_MODELO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTime: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIME'
    Cursor = 'CRM_TIME'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;46006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEventosTipo: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Tipo Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EVENTOS_TIPO'
    Cursor = 'CRM_EVENTOS_TIPO'
    MaxRowCount = 600
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;46007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadzapTemMembrosTime: TFTable
    FieldDefs = <
      item
        Name = 'TOT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tot'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADZAP_TEM_MEMBROS_TIME'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;46008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadzapSetorMembroTime: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADZAP_SETOR_MEMBRO_TIME'
    MaxRowCount = 200
    OnMaxRow = tbLeadzapSetorMembroTimeMaxRow
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;46009'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTemplateLeadAprov: TFTable
    FieldDefs = <
      item
        Name = 'ID_EMAIL_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Email Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_MODELO'
    Cursor = 'CRM_EMAIL_MODELO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;460010'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTemplateLeadReprov: TFTable
    FieldDefs = <
      item
        Name = 'ID_EMAIL_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Email Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_MODELO'
    Cursor = 'CRM_EMAIL_MODELO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;460011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCadastroWhatsapp: TFTable
    FieldDefs = <
      item
        Name = 'ID_CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Celular'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_CADASTRO_WHATSAPP'
    Cursor = 'CRM_CADASTRO_WHATSAPP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;460012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbWhatsappEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_WHATSAPP_EMPRESA'
    Cursor = 'CRM_WHATSAPP_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;460013'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbReceptTimesMembrosEmpUser: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'RECEPT_TIMES_MEMBROS_EMP_USER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600506;460014'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
