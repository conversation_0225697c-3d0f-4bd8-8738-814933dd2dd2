object FrmGerencialPainelFiltro: TFForm
  Left = 321
  Top = 160
  ActiveControl = vboxPrincipal
  Caption = 'Filtro'
  ClientHeight = 302
  ClientWidth = 492
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '382035'
  ShortcutKeys = <>
  InterfaceRN = 'GerencialPainelFiltroRN'
  Access = False
  ChangedProp = 
    'FrmGerencialPainelFiltro.Width;'#13#10#13#10'FrmGerencialPainelFiltro.Acti' +
    'veControlFrmGerencialPainelFiltro.Height;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 492
    Height = 302
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 6
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitHeight = 295
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 476
      Height = 70
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 75
        Height = 60
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 75
        Top = 0
        Width = 75
        Height = 60
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
          0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
          1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
          95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
          101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
          1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
          A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
          C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
          482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
          1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
          C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
          8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
        ImageId = 700088
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnPreferencia: TFButton
        Left = 150
        Top = 0
        Width = 75
        Height = 60
        Caption = 'Prefer'#234'ncia'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnPreferenciaClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000002404944415478DAB5554D68134114FE5E1277965A110B152BA2072982
          7AB337936CEB450F2A2D88277FF054A85541AC57AD27A1FE808A883D08BD0A6A
          A1221E24DD98DE2A9E1484EA45A89152146C6437D966FCDA068DC9269B357160
          E6BD7D33F37DEFBD79332B08D1F4B43A8228B424DCA946F748C3E069D382D6A9
          B55DD22749C76E2D81AD26298E963E27C572FB5B46A05F993B10D31FA9464BA6
          2222D22D09E7536B086C354671A9C27C83518C344DA067D1869CF9995A47C5D4
          7738EE3639885C7304B61AA4785063FBA058CEC3D004FA3536A1686C01229DAC
          9CFB5CB5C79F1DEF5851433C920544F25949E05B15819E3606B8E80C5502EA2E
          DA3AD9555064359ACBBE40AC2FC4CAD2B947A26D73D127BF2D6A7A51745ADD64
          A817FF0BBEE0D6EA19F0202F50DC46888B17E43AA1AEB100AEFE0664AA4E711C
          A76A3409EEF1B51A925E671C951E93E400C7275437FE23F8127D3F2EBDEE8B3F
          59AA8C2D65ECE533F09CEAF690E0F32CD7C36215DE961BFDEF415A0DD393BBA1
          E005E724E9DEAB36FB121813CCE3C97001E809B1F2A71B2450738C6067C814CD
          F1F1EB0E24D033ED9BE115BE86045F6BC55897F4E5B2F509D2463FD3F3B406C4
          5249B6FBCE8A1E9064FE597D02FFB77FE5EA4C211639CB2D1E8ADE753A71A26A
          BFC6184BF472104186627F99E93DC1867971527FAF5BBD332B95B6BBCC3CC373
          88071098B31CF751FDC13E8AF5EE1DE941C12F23FC19AD434E9DA77A857D03E1
          DEF079E8A94F9051BBB08C4388461F4BFCE73C1A683AD3B615DEF231C4F052E2
          EE87F2B95FF014C23665E619CA0000000049454E44AE426082}
        ImageId = 382020
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object FVBox1: TFVBox
      Left = 0
      Top = 71
      Width = 475
      Height = 61
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 3
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FLabel1: TFLabel
        Left = 0
        Top = 0
        Width = 179
        Height = 14
        Caption = 'Pa'#237'neis que a fun'#231#227'o enxerga'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object cbbPaineis: TFCombo
        Left = 0
        Top = 15
        Width = 269
        Height = 21
        LookupTable = tbPainelGerencialUsuario
        LookupKey = 'ID'
        LookupDesc = 'DESCRICAO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Selecione'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
    end
    object FVBox2: TFVBox
      Left = 0
      Top = 133
      Width = 475
      Height = 61
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 3
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FLabel2: TFLabel
        Left = 0
        Top = 0
        Width = 205
        Height = 14
        Caption = 'Empresas Cruzadas para a Fun'#231#227'o'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object cbbEmpresas: TFCombo
        Left = 0
        Top = 15
        Width = 269
        Height = 21
        LookupTable = tbEmpresaCruzadasFuncao
        LookupKey = 'CE'
        LookupDesc = 'EMPRESA'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Selecione'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
    end
    object FHBox2: TFHBox
      Left = 0
      Top = 195
      Width = 476
      Height = 73
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 3
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox3: TFVBox
        Left = 0
        Top = 0
        Width = 170
        Height = 65
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel3: TFLabel
          Left = 0
          Top = 0
          Width = 24
          Height = 14
          Caption = 'M'#234's'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object cbbMes: TFCombo
          Left = 0
          Top = 15
          Width = 161
          Height = 21
          Flex = True
          ListOptions = 
            'Janeiro=1;Fevereiro=2;Mar'#231'o=3;Abril=4;Maio=5;Junho=6;Julho=7;Ago' +
            'sto=8;Setembro=9;Outubro=10;Novembro=11;Dezembro=12'
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
      end
      object FVBox4: TFVBox
        Left = 170
        Top = 0
        Width = 130
        Height = 65
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel4: TFLabel
          Left = 0
          Top = 0
          Width = 25
          Height = 14
          Caption = 'Ano'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object cbbAno: TFCombo
          Left = 0
          Top = 15
          Width = 100
          Height = 21
          LookupTable = tbAno
          LookupKey = 'ANO'
          LookupDesc = 'ANO'
          Flex = False
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
      end
    end
  end
  object tbAno: TFTable
    FieldDefs = <
      item
        Name = 'ANO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ano'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ANO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382035;38201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPainelGerencialUsuario: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PAINEL_GERENCIAL_USUARIO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382035;38202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresaCruzadasFuncao: TFTable
    FieldDefs = <
      item
        Name = 'CE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'EMPRESA_CRUZADAS_FUNCAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382035;38203'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
