object HomeClienteRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '340057'
  Height = 299
  Width = 442
  object tbUserInformation: TFTable
    FieldDefs = <
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Usu'#225'rio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Divis'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'USER_INFORMATION'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34001'
    DeltaMode = dmChanged
  end
  object tbMenu: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Id. Menu'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU_SUPER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Id. Menu Super'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_FORM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Id. Form'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORDEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Ordem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_IMAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Id. Imagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'C'#243'd. Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'DBMENU'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34002'
    DeltaMode = dmChanged
  end
  object tbSistemaAcesso: TFTable
    FieldDefs = <
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'C'#243'd. Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_PAI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'C'#243'd. Acesso Pai'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LEVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Level'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERIGOSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Perigoso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'C'#243'd. Acesso Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Ord Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENU_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Menu Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_FUNCAO_PAI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'C'#243'd. Acesso Fun'#231#227'o Pai'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SISTEMA_ACESSO'
    Cursor = 'SISTEMA_ACESSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34003'
    DeltaMode = dmChanged
  end
  object tbClienteDiverso: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO_ELETRONICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Endere'#231'o Eletronico'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_DIVERSO'
    Cursor = 'CLIENTE_DIVERSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34005'
    DeltaMode = dmChanged
  end
  object tbCrmPartsFilaDaVez: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FILA_CRMPARTS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        Caption = 'Fila Crmparts'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'VW_CRM_PARTS_FILA_DA_VEZ'
    Cursor = 'VW_CRM_PARTS_FILA_DA_VEZ'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34006'
    DeltaMode = dmChanged
  end
  object tbClienteSelecionarEmpresas: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CLIENTE_SELECIONAR_EMPRESAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34007'
    DeltaMode = dmChanged
  end
  object tbMapa: TFTable
    FieldDefs = <
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Orc Mapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ORC_MAPA'
    Cursor = 'ORC_MAPA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34008'
    DeltaMode = dmChanged
  end
end
