object FrmConsultaOrcTreeviewDetalhe: TFForm
  Left = 44
  Top = 162
  ActiveControl = container
  Caption = 'Consulta Detalhes da Treeview Or'#231'amento'
  ClientHeight = 295
  ClientWidth = 556
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600434'
  ShortcutKeys = <>
  InterfaceRN = 'ConsultaOrcTreeviewDetalheRN'
  Access = False
  ChangedProp = 'FrmConsultaOrcTreeviewDetalhe.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object container: TFVBox
    Left = 0
    Top = 0
    Width = 556
    Height = 295
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FGrid1: TFGrid
      Left = 0
      Top = 0
      Width = 553
      Height = 120
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Table = tbConsultaServReclamacao
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Paging.Enabled = True
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = True
      MultiSelection = False
      Grouping.Enabled = False
      Grouping.Expanded = False
      Grouping.ShowFooter = False
      Crosstab.Enabled = False
      Crosstab.GroupType = cgtConcat
      EnablePopup = False
      WOwner = FrInterno
      WOrigem = EhNone
      EditionEnabled = False
      AuxColumnHeaders = <>
      NoBorder = False
      Columns = <
        item
          Expanded = False
          FieldName = 'CODIGO'
          Font = <>
          Title.Caption = 'C'#243'digo'
          Width = 80
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{DFA83C33-B5BD-465B-B56A-BE6EE5973C6A}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
        end
        item
          Expanded = False
          FieldName = 'SERVICO'
          Font = <>
          Title.Caption = 'Servi'#231'o'
          Width = 120
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{A75B9661-4C1E-4CD3-9107-8839BD66314C}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
        end
        item
          Expanded = False
          FieldName = 'TMO'
          Font = <>
          Width = 40
          Visible = True
          Precision = 0
          TextAlign = taCenter
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{B737689C-AAD3-442A-AAC6-4F15590A0F25}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
        end>
    end
    object FHBox1: TFHBox
      Left = 0
      Top = 121
      Width = 550
      Height = 129
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox1: TFVBox
        Left = 0
        Top = 0
        Width = 517
        Height = 109
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblAgendado: TFLabel
          Left = 0
          Top = 0
          Width = 59
          Height = 13
          Caption = 'lblAgendado'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clLime
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          Visible = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
        end
        object FGrid2: TFGrid
          Left = 0
          Top = 14
          Width = 492
          Height = 88
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'Tahoma'
          TitleFont.Style = []
          Table = tbConsultaPecas
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Paging.Enabled = True
          FrozenColumns = 0
          ShowFooter = False
          ShowHeader = True
          MultiSelection = False
          Grouping.Enabled = False
          Grouping.Expanded = False
          Grouping.ShowFooter = False
          Crosstab.Enabled = False
          Crosstab.GroupType = cgtConcat
          EnablePopup = False
          WOwner = FrInterno
          WOrigem = EhNone
          EditionEnabled = False
          AuxColumnHeaders = <>
          NoBorder = False
          Columns = <
            item
              Expanded = False
              FieldName = 'DESCRICAO'
              Font = <>
              Title.Caption = 'Descri'#231#227'o'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{84A8188C-EAD0-4079-9DAA-A3FCEE385FA3}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end
            item
              Expanded = False
              FieldName = 'COD_ITEM'
              Font = <>
              Title.Caption = 'C'#243'd. Item'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{0ED7ECD2-9158-4D9E-8A02-2BDC117C6A91}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end
            item
              Expanded = False
              FieldName = 'FORNEC'
              Font = <>
              Title.Caption = 'Fornec'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{A238AA38-245C-4A29-97CD-32AB1426D2F2}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end
            item
              Expanded = False
              FieldName = 'QTDE'
              Font = <>
              Title.Caption = 'Quantidade'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{4D429337-06AF-4036-876C-038CEC8DB59E}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end
            item
              Expanded = False
              FieldName = 'RES'
              Font = <>
              Title.Caption = 'Res'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{A6BCBC2A-D690-409E-A838-C428BADAD0AA}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end
            item
              Expanded = False
              FieldName = 'VP'
              Font = <>
              Title.Caption = 'Vp'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{59144A47-77EF-49F1-BDB7-72BC3E3FF29E}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end
            item
              Expanded = False
              FieldName = 'PRECO'
              Font = <>
              Title.Caption = 'Pre'#231'o'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{C1F24C2E-F76F-466F-8F76-7006BEE29D48}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end
            item
              Expanded = False
              FieldName = 'PDESC'
              Font = <>
              Title.Caption = 'Pdesc'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{4D3EB50C-C9AB-42D3-957F-0C42157F9A75}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end
            item
              Expanded = False
              FieldName = 'PRECO_LIQUIDO'
              Font = <>
              Title.Caption = 'Pre'#231'o L'#237'quido'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{8C816505-CC8C-4253-AAE5-059304E0B381}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end
            item
              Expanded = False
              FieldName = 'COD_SERVICO'
              Font = <>
              Title.Caption = 'C'#243'd. Servi'#231'o'
              Width = 40
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{432AFD6A-D38F-42C9-B4F1-35D800D415CF}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
            end>
        end
      end
    end
  end
  object tbConsultaServTodos: TFTable
    FieldDefs = <
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tmo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORC_CONSULTA_SERV_TODOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600434;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaServReclamacao: TFTable
    FieldDefs = <
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tmo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORC_CONSULTA_SERV_RECLAMACAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600434;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaPecas: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORNEC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fornec'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vp'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PDESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pdesc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORC_CONSULTA_PECAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600434;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaAgendaProdutivo: TFTable
    FieldDefs = <>
    Cursor = 'ORC_CONSULTA_AGENDA_PRODUTIVO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600434;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
