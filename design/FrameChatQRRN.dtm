object FrameChatQRRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '513017'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbCadastroWhatsapp: TFTable
    FieldDefs = <
      item
        Name = 'ID_CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Celular'
        GUID = '{AE0A7237-E8B4-4010-BA28-7ADFFFB31F92}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{716120A7-8853-46F0-AD9C-548C2F3F42A0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{19928669-0C1F-400F-889C-0E2F2F882E0F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        GUID = '{891B13E7-F61C-460D-8EFD-45A1063805D6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        GUID = '{743DEAC7-C868-46F8-9FEE-581510A48D2F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        GUID = '{0FC4554E-C550-4923-99AE-13A519B84848}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular'
        GUID = '{B6B09BB9-FD7A-4394-A8E0-7C1918215AB1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERESSE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Interesse'
        GUID = '{EFF9A829-ED41-4D7A-8D8D-C36E9864BEB6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Whatsapp Id.'
        GUID = '{0F3EF7CF-650E-4883-9B9E-ACBE77F91755}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        GUID = '{9C9599B9-5388-4925-BFE5-06DDDAF6B103}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Imagem'
        GUID = '{ED2B079C-442E-4F9B-8D74-3F9AB82A4A03}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PHONE_NUMBER_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Phone Number Token'
        GUID = '{C2FBA323-FE6E-46FE-9B0D-96468DEDF183}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE_WHATS_DEFAULT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Template Whats Default'
        GUID = '{A7FF2E33-D14B-468E-9214-155EC7CF55A0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ULTIMA_MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data '#218'ltima Mensagem'
        GUID = '{7882ED06-80F2-4867-81AB-426A049605D4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SICRONIZADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sicronizado'
        GUID = '{08358461-4C91-44E1-902B-13CCAEC5C2C0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR_AVISO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular Aviso'
        GUID = '{B72E7BCC-684E-4E4F-9508-CDF034F5F2DA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_LEADZAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Leadzap'
        GUID = '{EDCBF0F3-E2D1-4A33-82A1-F08FDD409CA4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{2C7B7FC1-9AAC-43EA-BF5F-A80526F50E21}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{9647E46B-9757-403C-977E-1AC468797549}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Dia'
        GUID = '{15DD3E92-889F-43D6-AE60-DA502F1981DA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'X_API_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'X Api Token'
        GUID = '{E251A221-0C24-4154-874F-42698588C859}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_API'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Api'
        GUID = '{B0AA3896-23B6-4A03-A37B-580E972F26F4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOKEN_API'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tokapi'
        GUID = '{99DCF42C-C1E0-49E7-A095-0CC463FF63D6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_PAINEL_WEB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Painel Web'
        GUID = '{84B796F1-85EA-4AF8-AC0E-AFD06ED92C4F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Usu'#225'rio'
        GUID = '{9E7DBC64-CF5E-4AC1-B752-F75B494A3470}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_SENHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Senha'
        GUID = '{30A26D00-A3F1-4A05-8FE8-8FC5A3DFCB2C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_EMAIL_NOTIFICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Email Notifica'#231#227'o'
        GUID = '{748A909E-87CB-4ADE-9A52-B86AD5757072}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_INSTANCE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Instance'
        GUID = '{F31A7FFE-870A-4497-903F-2E289CEE941E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_INSTANCE_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Instance Token'
        GUID = '{29C526BA-AC19-482D-991B-27D3299E2D1B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_CLIENT_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Client Token'
        GUID = '{46F52A81-9BEA-48CA-9051-9697960FDED9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_API_URL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Api Url'
        GUID = '{136515D8-603D-4A0D-8B67-5E1EE4751BC8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Tipo'
        GUID = '{43219C2F-FD0E-44CE-A87C-9F2874A4A51C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_CONECTADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Conectado'
        GUID = '{10D70A59-06B5-46BE-8ABC-C8B431D4B401}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_CADASTRO_WHATSAPP'
    Cursor = 'CRM_CADASTRO_WHATSAPP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '513017;51301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
