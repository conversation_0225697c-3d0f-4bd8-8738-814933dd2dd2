object FrmPerfil: TFForm
  Left = 44
  Top = 162
  ActiveControl = cmbSetorVenda
  Caption = 'Perfil'
  ClientHeight = 662
  ClientWidth = 829
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000145'
  ShortcutKeys = <>
  InterfaceRN = 'PerfilRN'
  Access = False
  ChangedProp = 
    'FrmPerfil.Height;'#13#10'FrmPerfil.Width;'#13#10#13#10'FrmPerfil.ActiveControl'#13#10 +
    'FrmPerfil_1.Touch.InteractiveGestures;'#13#10'FrmPerfil_1.Touch.Intera' +
    'ctiveGestureOptions;'#13#10'FrmPerfil_1.Touch.ParentTabletOptions;'#13#10'Fr' +
    'mPerfil_1.Touch.TabletOptions;'#13#10'FrmPerfil_1.Touch.InteractiveGes' +
    'tures;'#13#10'FrmPerfil_1.Touch.InteractiveGestureOptions;'#13#10'FrmPerfil_' +
    '1.Touch.ParentTabletOptions;'#13#10'FrmPerfil_1.Touch.TabletOptions;'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxPerfil: TFVBox
    Left = 0
    Top = 0
    Width = 829
    Height = 662
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 10
    Padding.Left = 20
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = True
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FVBox1: TFVBox
      Left = 0
      Top = 0
      Width = 808
      Height = 225
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblNomeCompleto: TFLabel
        Left = 0
        Top = 0
        Width = 145
        Height = 19
        Caption = 'lblNomeCompleto'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clNavy
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        OnClick = lblNomeCompletoClick
        FieldName = 'NOME_COMPLETO'
        Table = tbPerfil
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taAlignBottom
        WordBreak = False
        MaskType = mtText
      end
      object FHBox1: TFHBox
        Left = 0
        Top = 20
        Width = 591
        Height = 201
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox2: TFVBox
          Left = 0
          Top = 0
          Width = 373
          Height = 197
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 20
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox5: TFHBox
            Left = 0
            Top = 0
            Width = 158
            Height = 18
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox6: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 2
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass3: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617036040000424D360400000000000036000000280000001000
                  0000100000000100200000000000000400000000000000000000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'user'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 16
                Color = clBlack
              end
            end
            object lblLogin: TFLabel
              Left = 16
              Top = 0
              Width = 34
              Height = 16
              Align = alLeft
              Caption = 'Login'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox19: TFHBox
            Left = 0
            Top = 19
            Width = 158
            Height = 20
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox20: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 13
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblCapLogin: TFLabel
              Left = 16
              Top = 0
              Width = 65
              Height = 16
              Caption = 'lblCapLogin'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'NOME'
              Table = tbPerfil
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignTop
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox3: TFHBox
            Left = 0
            Top = 40
            Width = 158
            Height = 20
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox4: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass2: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'credit-card'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
            object lblFuncao: TFLabel
              Left = 16
              Top = 0
              Width = 45
              Height = 16
              Align = alLeft
              Caption = 'Fun'#231#227'o'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              FieldName = 'CPF'
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox21: TFHBox
            Left = 0
            Top = 61
            Width = 158
            Height = 25
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox22: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 13
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblCapFuncao: TFLabel
              Left = 16
              Top = 0
              Width = 76
              Height = 16
              Align = alLeft
              Caption = 'lblCapFuncao'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'FUNCAO'
              Table = tbPerfil
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignTop
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox2: TFHBox
            Left = 0
            Top = 87
            Width = 159
            Height = 24
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox23: TFHBox
              Left = 0
              Top = 0
              Width = 17
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 2
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                13
                17)
              object FIconClass1: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617036040000424D360400000000000036000000280000001000
                  0000100000000100200000000000000400000000000000000000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'envelope'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 16
                Color = clBlack
              end
            end
            object lblemail: TFLabel
              Left = 17
              Top = 0
              Width = 32
              Height = 16
              Align = alLeft
              Caption = 'Email'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox24: TFHBox
            Left = 0
            Top = 112
            Width = 158
            Height = 25
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 5
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox40: TFHBox
              Left = 0
              Top = 0
              Width = 114
              Height = 23
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 3
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftMin
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox25: TFHBox
                Left = 0
                Top = 0
                Width = 16
                Height = 13
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblCapEmail: TFLabel
                Left = 16
                Top = 0
                Width = 66
                Height = 16
                Align = alLeft
                Caption = 'lblCapEmail'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                FieldName = 'EMAIL'
                Table = tbPerfil
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taAlignTop
                WordBreak = False
                MaskType = mtText
              end
            end
            object hBoxEditarEmail: TFHBox
              Left = 114
              Top = 0
              Width = 44
              Height = 40
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 5
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object icoEditarEmail: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Picture.Data = {
                  07544269746D6170460E0000424D460E00000000000036000000280000001E00
                  00001E0000000100200000000000100E00000000000000000000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F0000000000000000000000000000000000000000000000000000000
                  0000000000000000000000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
                  0000000000000000000000000000000000000000000000000000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  0000000000000000000000000000000000000000000000000000000000000000
                  00000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000}
                OnClick = icoEditarEmailClick
                IconClass = 'edit'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 30
                Color = clBlack
              end
            end
          end
          object FHBox44: TFHBox
            Left = 0
            Top = 138
            Width = 160
            Height = 30
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 3
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 6
            Margin.Top = 4
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox51: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object iconRamalUsuario: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617046030000424D460300000000000036000000280000000E00
                  00000E0000000100200000000000100300000000000000000000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F0000000000000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                  0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000}
                IconClass = 'phone'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 14
                Color = clBlack
              end
            end
            object FLabel8: TFLabel
              Left = 16
              Top = 0
              Width = 39
              Height = 16
              Align = alLeft
              Caption = 'Ramal'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox52: TFHBox
            Left = 0
            Top = 169
            Width = 158
            Height = 25
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 7
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox53: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 13
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblRamalUsuario: TFLabel
              Left = 16
              Top = 0
              Width = 92
              Height = 16
              Align = alLeft
              Caption = 'lblRamalUsuario'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'RAMAL'
              Table = tbPerfil
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignTop
              WordBreak = False
              MaskType = mtText
            end
          end
        end
        object lblImagePerfil: TFImage
          Left = 373
          Top = 0
          Width = 156
          Height = 148
          Stretch = True
          OnClick = lblImagePerfilClick
          Table = tbPerfil
          FieldName = 'FOTO_ICONE'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          Preview = False
        end
      end
    end
    object FVBox3: TFVBox
      Left = 0
      Top = 226
      Width = 808
      Height = 485
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = True
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox26: TFHBox
        Left = 0
        Top = 0
        Width = 591
        Height = 241
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 5
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox4: TFVBox
          Left = 0
          Top = 0
          Width = 350
          Height = 226
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox7: TFHBox
            Left = 0
            Top = 0
            Width = 160
            Height = 15
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox8: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass4: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'building-o'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
            object lblEmpresa: TFLabel
              Left = 16
              Top = 0
              Width = 55
              Height = 16
              Align = alLeft
              Caption = 'Empresa'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox27: TFHBox
            Left = 0
            Top = 16
            Width = 158
            Height = 25
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox28: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 13
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblCapEmpresa: TFLabel
              Left = 16
              Top = 0
              Width = 85
              Height = 16
              Align = alLeft
              Caption = 'lblCapEmpresa'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'EMPRESA'
              Table = tbPerfil
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignTop
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox9: TFHBox
            Left = 0
            Top = 42
            Width = 159
            Height = 15
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BackgroundImage = ' '
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox10: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass5: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'archive'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
            object lblDepartamento: TFLabel
              Left = 16
              Top = 0
              Width = 94
              Height = 16
              Align = alLeft
              Caption = 'Departamento'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox29: TFHBox
            Left = 0
            Top = 58
            Width = 158
            Height = 25
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox30: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 13
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblCapDepartamento: TFLabel
              Left = 16
              Top = 0
              Width = 116
              Height = 16
              Align = alLeft
              Caption = 'lblCapDepartamento'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'DEPARTAMENTO'
              Table = tbPerfil
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignTop
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox11: TFHBox
            Left = 0
            Top = 84
            Width = 158
            Height = 15
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox12: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              ParentFont = False
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass6: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'trello'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
            object lblDivisao: TFLabel
              Left = 16
              Top = 0
              Width = 46
              Height = 16
              Align = alLeft
              Caption = 'Divis'#227'o'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox31: TFHBox
            Left = 0
            Top = 100
            Width = 158
            Height = 25
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 5
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox32: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 13
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblCapDivisao: TFLabel
              Left = 16
              Top = 0
              Width = 75
              Height = 16
              Align = alLeft
              Caption = 'lblCapDivisao'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'DIVISAO'
              Table = tbPerfil
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignTop
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox36: TFHBox
            Left = 0
            Top = 126
            Width = 158
            Height = 15
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 6
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox37: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              ParentFont = False
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass9: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'bars'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
            object FLabel4: TFLabel
              Left = 16
              Top = 0
              Width = 40
              Height = 16
              Align = alLeft
              Caption = 'Marca'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox38: TFHBox
            Left = 0
            Top = 142
            Width = 158
            Height = 25
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 7
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox39: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 13
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblmarca: TFLabel
              Left = 16
              Top = 0
              Width = 75
              Height = 16
              Align = alLeft
              Caption = 'lblCapDivisao'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'MARCA'
              Table = tbPerfil
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignTop
              WordBreak = False
              MaskType = mtText
            end
          end
        end
        object FVBox5: TFVBox
          Left = 350
          Top = 0
          Width = 225
          Height = 229
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox15: TFHBox
            Left = 0
            Top = 0
            Width = 217
            Height = 15
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BackgroundImage = ' '
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox16: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass8: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'money'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
            object FLabel2: TFLabel
              Left = 16
              Top = 0
              Width = 153
              Height = 16
              Align = alLeft
              Caption = 'Tabela de pre'#231'o padr'#227'o'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
            object FHBox17: TFHBox
              Left = 169
              Top = 0
              Width = 6
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FHBox18: TFHBox
              Left = 175
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object iconAltTabPreco: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                OnClick = iconAltTabPrecoClick
                IconClass = 'edit'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
          end
          object FHBox33: TFHBox
            Left = 0
            Top = 16
            Width = 214
            Height = 25
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox34: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 13
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FLabel3: TFLabel
              Left = 16
              Top = 0
              Width = 124
              Height = 16
              Align = alLeft
              Caption = 'lblTabelaPrecoPadrao'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'TAB_PRECO'
              Table = tbPerfil
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignTop
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox13: TFHBox
            Left = 0
            Top = 42
            Width = 185
            Height = 19
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox14: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              ParentFont = False
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass7: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'archive'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
            object FLabel1: TFLabel
              Left = 16
              Top = 0
              Width = 80
              Height = 16
              Align = alLeft
              Caption = 'Setor venda'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox35: TFHBox
            Left = 0
            Top = 62
            Width = 214
            Height = 41
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object cmbSetorVenda: TFCombo
              Left = 0
              Top = 0
              Width = 161
              Height = 21
              Table = tbPerfil
              LookupTable = tbSetorVenda
              FieldName = 'COD_SETOR'
              LookupKey = 'COD_SETOR'
              LookupDesc = 'DESCRICAO_SETOR'
              Flex = False
              ReadOnly = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Selecione'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              ClearOnDelKey = True
              UseClearButton = False
              HideClearButtonOnNullValue = False
              OnChange = cmbSetorVendaChange
              Colors = <>
              Images = <>
              Masks = <>
              Fonts = <>
              MultiSelection = False
              IconReverseDirection = False
            end
          end
          object FHBox41: TFHBox
            Left = 0
            Top = 104
            Width = 158
            Height = 22
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox42: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              ParentFont = False
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass11: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'car'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
            object FLabel5: TFLabel
              Left = 16
              Top = 0
              Width = 65
              Height = 16
              Align = alLeft
              Caption = 'Segmento'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox43: TFHBox
            Left = 0
            Top = 127
            Width = 214
            Height = 38
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 5
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object cmbSegmento: TFCombo
              Left = 0
              Top = 0
              Width = 161
              Height = 21
              Table = tbPerfil
              LookupTable = tbProdutoSegmento
              FieldName = 'COD_SEGMENTO'
              LookupKey = 'COD_SEGMENTO'
              LookupDesc = 'DESCRICAO_SEGMENTO'
              Flex = False
              ReadOnly = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Selecione'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              ClearOnDelKey = True
              UseClearButton = False
              HideClearButtonOnNullValue = False
              OnChange = cmbSegmentoChange
              Colors = <>
              Images = <>
              Masks = <>
              Fonts = <>
              MultiSelection = False
              IconReverseDirection = False
            end
          end
          object FHBox48: TFHBox
            Left = 0
            Top = 166
            Width = 185
            Height = 21
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 6
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox49: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 21
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              ParentFont = False
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                12
                17)
              object FIconClass13: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Anchors = []
                Picture.Data = {
                  07544269746D617076020000424D760200000000000036000000280000000C00
                  00000C0000000100200000000000400200000000000000000000000000000000
                  0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                  F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                  00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                  000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000}
                IconClass = 'archive'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 12
                Color = clBlack
              end
            end
            object FLabel6: TFLabel
              Left = 16
              Top = 0
              Width = 109
              Height = 16
              Align = alLeft
              Caption = 'Local do Estoque'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taAlignBottom
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox50: TFHBox
            Left = 0
            Top = 188
            Width = 214
            Height = 34
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 7
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object cmbLocalEstoque: TFCombo
              Left = 0
              Top = 0
              Width = 161
              Height = 21
              Table = tbEmpresasUsuarios
              LookupTable = tbLocalEstoque
              FieldName = 'COD_LOCAL_ESTOQUE'
              LookupKey = 'COD_LOCAL_ESTOQUE'
              LookupDesc = 'NOME_DO_LOCAL'
              Flex = False
              ReadOnly = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Selecione'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              ClearOnDelKey = True
              UseClearButton = False
              HideClearButtonOnNullValue = False
              OnChange = cmbLocalEstoqueChange
              Colors = <>
              Images = <>
              Masks = <>
              Fonts = <>
              MultiSelection = False
              IconReverseDirection = False
            end
          end
        end
      end
      object vBoxAssinaturaUsadaRecepcao: TFVBox
        Left = 0
        Top = 242
        Width = 721
        Height = 197
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox46: TFHBox
          Left = 0
          Top = 0
          Width = 569
          Height = 15
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox47: TFHBox
            Left = 0
            Top = 0
            Width = 16
            Height = 21
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            DesignSize = (
              12
              17)
            object FIconClass12: TFIconClass
              Left = 0
              Top = 0
              Width = 16
              Height = 16
              Anchors = []
              Picture.Data = {
                07544269746D617076020000424D760200000000000036000000280000000C00
                00000C0000000100200000000000400200000000000000000000000000000000
                0000F0F0F000F0F0F00000000000000000000000000000000000000000000000
                000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                0000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000}
              IconClass = 'pencil'
              WOwner = FrInterno
              WOrigem = EhNone
              Size = 12
              Color = clBlack
            end
          end
          object FLabel7: TFLabel
            Left = 16
            Top = 0
            Width = 239
            Height = 16
            Align = alLeft
            Caption = 'Assinatura a Ser Usada na Recep'#231#227'o'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taAlignBottom
            WordBreak = False
            MaskType = mtText
          end
        end
        object signature: TFSignature
          Left = 0
          Top = 16
          Width = 714
          Height = 153
          AutoWrap = False
          Caption = 'signature'
          FlowStyle = fsTopBottomLeftRight
          TabOrder = 1
          BackgroundColor = clWhite
          PenColor = clBlack
          PenSize = 4
          SaveType = 'image/png'
          ToolbarVisible = False
          OnSave = signatureSave
          OnClear = signatureClear
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          WOwner = FrInterno
          WOrigem = EhNone
        end
        object vBoxImgSignatrueChk: TFVBox
          Left = 0
          Top = 170
          Width = 757
          Height = 100
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Visible = False
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object imgSignatureChk: TFImage
            Left = 0
            Top = 0
            Width = 753
            Height = 200
            Stretch = False
            Table = tbEmpresasUsuarios
            FieldName = 'ASSINATURA'
            WOwner = FrInterno
            WOrigem = EhNone
            BoxSize = 0
            GrayScaleOnDisable = False
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            Preview = False
          end
        end
      end
      object FHBox45: TFHBox
        Left = 0
        Top = 440
        Width = 718
        Height = 44
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 3
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox7: TFVBox
          Left = 0
          Top = 0
          Width = 6
          Height = 28
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object btnDesfazer: TFButton
          Left = 6
          Top = 0
          Width = 88
          Height = 38
          Caption = 'Desfazer'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          OnClick = btnDesfazerClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
            782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
            B9B0DBF5D30000000049454E44AE426082}
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconClass = 'undo'
          IconReverseDirection = False
        end
        object FVBox8: TFVBox
          Left = 94
          Top = 0
          Width = 6
          Height = 28
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object btnLimpar: TFButton
          Left = 100
          Top = 0
          Width = 84
          Height = 38
          Caption = 'Limpar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 3
          OnClick = btnLimparClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
            782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
            B9B0DBF5D30000000049454E44AE426082}
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconClass = 'close'
          IconReverseDirection = False
        end
        object FVBox9: TFVBox
          Left = 184
          Top = 0
          Width = 6
          Height = 28
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object btnSalvar: TFButton
          Left = 190
          Top = 0
          Width = 84
          Height = 38
          Caption = 'Salvar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 5
          OnClick = btnSalvarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
            782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
            B9B0DBF5D30000000049454E44AE426082}
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconClass = 'save'
          IconReverseDirection = False
        end
      end
    end
  end
  object tbPerfil: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Divis'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO_ICONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto Icone'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAB_PRECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tab Pre'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Segmento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Segmento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RAMAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ramal'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PERFIL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;70001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbUsuarioFoto
        GUID = '{8DF74FE0-B3CC-4835-B260-34E64E5DC6CB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbUsuarioFoto: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO_ICONE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto Icone'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'USUARIO_FOTO'
    TableName = 'USUARIO_FOTO'
    Cursor = 'USUARIO_FOTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;70002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSetorVenda: TFTable
    FieldDefs = <
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SETOR_VENDA'
    Cursor = 'SETOR_VENDA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_LOCAL_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Local Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_USUARIOS'
    Cursor = 'EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLocalEstoque: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_LOCAL_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Local Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DO_LOCAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Do Local'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Principal'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFERENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Preferencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'APLICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aplica'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'LOCAL_ESTOQUE'
    Cursor = 'LOCAL_ESTOQUE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;53003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProdutoSegmento: TFTable
    FieldDefs = <
      item
        Name = 'COD_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Segmento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Segmento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'PRODUTO_SEGMENTO'
    Cursor = 'PRODUTO_SEGMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
