object FrmAlterarClienteBasico: TFForm
  Left = 52
  Top = 52
  Caption = 'Alterar Dados do Cliente'
  ClientHeight = 266
  ClientWidth = 444
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000150'
  ShortcutKeys = <>
  InterfaceRN = 'AlterarClienteBasicoRN'
  Access = False
  ChangedProp = 
    'FrmAlterarClienteBasico.Height;'#13#10'FrmAlterarClienteBasico.Width;'#13 +
    #10'FrmAlterarClienteBasico.Caption;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object hboxEditarLead: TFHBox
    Left = 0
    Top = 0
    Width = 444
    Height = 61
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 3
    Padding.Left = 5
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    object btnSalvar: TFButton
      Left = 0
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Salvar Altera'#231#245'es nos Dados do Cliente'
      Align = alLeft
      Caption = 'Salvar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 0
      OnClick = btnSalvarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
        C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
        D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
        1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
        D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
        9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
        A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
        BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
      ImageId = 700080
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
    object FVBox4: TFVBox
      Left = 60
      Top = 0
      Width = 5
      Height = 55
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
    end
    object btnCancelar: TFButton
      Left = 65
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Cancelar Altera'#231#227'o de Dados do Cliente'
      Align = alLeft
      Caption = 'Cancelar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 2
      OnClick = btnCancelarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F80000022B4944415478DAB595DF4B544114C7CF2692A12F22195B28855AF8A0
        50F4121111E9F64304F34585D4C00705317AAABFA18720AA97602B044B097221
        88486559C444A8968405454537D0087F15063EC4BAF53DCC1918C6BDEBDC950E
        7CE09C997BCFF7CECC997303F49F2DE0F85C31380D8E48FC037C053FF723C073
        4DE036B898E1D934888147E02DF8EB47E004E807171C57380E3AC03717813360
        0494585F1B078B1257C8961D309E590321D93A4F8172F0191C9638051E8207B2
        EFA605C15D70C7185B950F5CC924C0FE28B82CF1266800531EDB9207C2E09635
        FE015C23391353A05EB6866D87D4C17EF499DCCC35660B7025348AFF04F46549
        FE0C741A6361C9D5257104349B0205E0173828315751D247F26E500566656C9B
        D4DDF9A305F860BE883F074E79247F4EAA1CEDE4698997C071F16B40420B5C07
        EFC4E773B8924372B628B8649E8316B80ADE8BCF9514CA21395B8C5471B07135
        46B5402D98163F2967A093BF00ED0EC939D732382A71359F8916C827D5B80A8D
        FD9BF1919C8D6F765CFC2D529D206596E91068117F90D42D764DAEDF6915FF25
        B8A997A5ED3C98F07879AFE4DC7523467C8EA403D8BD6818DCC821F92B7048E2
        D7C64EEC1228059F48353D6D03E03E481863FC1E17C63DD0668C27C159B0E125
        C07692541F29B3C6B94216E49D4A70CC9AE77F419D3C43D904F44A9ECAF25DEC
        0DE801EBF6C45EFF64FEA3F5926ADB45D6DC6F52B7FF3198F44AE0FAD3E77BC2
        CD2C28F177304FAA94B39AAB40CEF60F541979196CA1E08A0000000049454E44
        AE426082}
      ImageId = 700098
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
  end
  object hboxDadosClientes: TFVBox
    Left = 0
    Top = 61
    Width = 444
    Height = 205
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 0
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    object gridAlterarDadosBasicos: TFGridPanel
      Left = 0
      Top = 0
      Width = 435
      Height = 163
      Align = alClient
      Caption = 'gridAlterarDadosBasicos'
      ColumnCollection = <
        item
          SizeStyle = ssAbsolute
          Value = 70.000000000000000000
        end
        item
          Value = 100.000000000000000000
        end>
      ControlCollection = <
        item
          Column = 0
          Control = FLabel1
          Row = 0
        end
        item
          Column = 1
          Control = edtNome
          Row = 0
        end
        item
          Column = 0
          Control = FLabel2
          Row = 1
        end
        item
          Column = 0
          Control = email
          Row = 4
        end
        item
          Column = 1
          Control = FHBox1
          Row = 1
        end
        item
          Column = 1
          Control = edtEmail
          Row = 4
        end
        item
          Column = 0
          Control = lblRes
          Row = 2
        end
        item
          Column = 1
          Control = FHBox3
          Row = 3
        end
        item
          Column = 1
          Control = FHBox2
          Row = 2
        end
        item
          Column = 0
          Control = FLabel3
          Row = 3
        end
        item
          Column = 0
          Control = FLabel4
          Row = 5
        end
        item
          Column = 1
          Control = edtEmailNfe
          Row = 5
        end>
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      RowCollection = <
        item
          SizeStyle = ssAuto
        end
        item
          SizeStyle = ssAuto
          Value = 28.000000000000000000
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
        end
        item
          SizeStyle = ssAuto
        end
        item
          Value = 100.000000000000000000
        end>
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      AllRowFlex = False
      WOwner = FrInterno
      WOrigem = EhNone
      ColumnTabOrder = False
      object FLabel1: TFLabel
        Left = 39
        Top = 1
        Width = 32
        Height = 13
        Align = alRight
        Caption = 'Nome'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
      end
      object edtNome: TFString
        Left = 71
        Top = 1
        Width = 305
        Height = 24
        Table = tbClientes
        FieldName = 'NOME'
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Align = alLeft
        Enabled = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        ExplicitLeft = 61
      end
      object FLabel2: TFLabel
        Left = 32
        Top = 25
        Width = 39
        Height = 13
        Align = alRight
        Caption = 'Celular'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
      end
      object email: TFLabel
        Left = 37
        Top = 107
        Width = 34
        Height = 13
        Align = alRight
        Caption = 'E-Mail'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
      end
      object FHBox1: TFHBox
        Left = 71
        Top = 25
        Width = 201
        Height = 28
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftMin
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        ExplicitLeft = 61
        object edtPrefixoCel: TFString
          Left = 0
          Top = 0
          Width = 41
          Height = 24
          Table = tbClientes
          FieldName = 'PREFIXO_CEL'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object FVBox2: TFVBox
          Left = 41
          Top = 0
          Width = 5
          Height = 24
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
        end
        object edtFoneCelular: TFString
          Left = 46
          Top = 0
          Width = 145
          Height = 24
          Table = tbClientes
          FieldName = 'TELEFONE_CEL'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Mask = '99999-999?9'
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          OnChange = edtFoneCelularChange
          OnExit = edtFoneCelularExit
          SaveLiteralCharacter = False
        end
      end
      object edtEmail: TFString
        Left = 71
        Top = 107
        Width = 304
        Height = 24
        Table = tbClientes
        FieldName = 'ENDERECO_ELETRONICO'
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
      end
      object lblRes: TFLabel
        Left = 7
        Top = 53
        Width = 64
        Height = 13
        Align = alRight
        Caption = 'Resid'#234'ncial'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
      end
      object FHBox3: TFHBox
        Left = 71
        Top = 78
        Width = 201
        Height = 29
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftMin
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        object edtPrefixComercial: TFString
          Left = 0
          Top = 0
          Width = 41
          Height = 24
          Table = tbClientes
          FieldName = 'PREFIXO_COM'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object FVBox5: TFVBox
          Left = 41
          Top = 0
          Width = 5
          Height = 24
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
        end
        object edtFoneComercial: TFString
          Left = 46
          Top = 0
          Width = 145
          Height = 24
          Table = tbClientes
          FieldName = 'TELEFONE_COM'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Mask = '99999-999?9'
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          OnChange = edtFoneComercialChange
          OnExit = edtFoneComercialExit
          SaveLiteralCharacter = False
        end
      end
      object FHBox2: TFHBox
        Left = 71
        Top = 53
        Width = 201
        Height = 25
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftMin
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        ExplicitLeft = 61
        object edtPrefixRes: TFString
          Left = 0
          Top = 0
          Width = 41
          Height = 24
          Table = tbClientes
          FieldName = 'PREFIXO_RES'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object FVBox3: TFVBox
          Left = 41
          Top = 0
          Width = 5
          Height = 24
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
        end
        object edtFoneRes: TFString
          Left = 46
          Top = 0
          Width = 145
          Height = 24
          Table = tbClientes
          FieldName = 'TELEFONE_RES'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Mask = '9999-9999'
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
      end
      object FLabel3: TFLabel
        Left = 15
        Top = 78
        Width = 56
        Height = 13
        Align = alRight
        Caption = 'Comercial'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
      end
      object FLabel4: TFLabel
        Left = 9
        Top = 131
        Width = 62
        Height = 13
        Align = alRight
        Caption = 'E-Mail NF-e'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
      end
      object edtEmailNfe: TFString
        Left = 71
        Top = 131
        Width = 304
        Height = 24
        Table = tbClientes
        FieldName = 'EMAIL_NFE'
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Align = alLeft
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        ExplicitLeft = 61
      end
    end
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbClientes
        GUID = '{DF5E776E-4BD7-43E0-B0F6-26796D3E0448}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbClientes: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO_ELETRONICO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Endere'#231'o Eletronico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Prefixo Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Telefone Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Prefixo com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Telefone com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_CEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Prefixo Cel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_CEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Telefone Cel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_NFE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Email Nfe'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTES'
    TableName = 'CLIENTES'
    Cursor = 'CLIENTES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000150;70001'
    DeltaMode = dmAll
  end
end
