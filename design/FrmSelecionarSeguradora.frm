object FrmSelecionarSeguradora: TFForm
  Left = 44
  Top = 162
  ActiveControl = FVBox1
  Caption = 'Seguradoras'
  ClientHeight = 415
  ClientWidth = 554
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '310069'
  ShortcutKeys = <>
  InterfaceRN = 'SelecionarSeguradoraRN'
  Access = False
  ChangedProp = 
    'FrmSelecionarSeguradora.Height;'#13#10#13#10'FrmSelecionarSeguradora.Activ' +
    'eControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 61
    Width = 554
    Height = 354
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 2
    Padding.Right = 2
    Padding.Bottom = 3
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    DesignSize = (
      550
      350)
    object hBoxPesqLupa: TFHBox
      Left = 0
      Top = 0
      Width = 437
      Height = 40
      Anchors = []
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 2
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object edtPesquisa: TFString
        Left = 0
        Top = 0
        Width = 358
        Height = 27
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Pesquisar por C'#243'digo ou Nome'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        OnEnter = edtPesquisaEnter
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
    end
    object gridConcess: TFGrid
      Left = 0
      Top = 41
      Width = 524
      Height = 280
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Table = tbSeguradora
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Paging.Enabled = True
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = False
      MultiSelection = False
      Grouping.Enabled = False
      Grouping.Expanded = False
      Grouping.ShowFooter = False
      Crosstab.Enabled = False
      Crosstab.GroupType = cgtConcat
      EnablePopup = False
      WOwner = FrInterno
      WOrigem = EhNone
      EditionEnabled = False
      AuxColumnHeaders = <>
      NoBorder = False
      Columns = <
        item
          Expanded = False
          FieldName = 'COD_SEGURADORA'
          Font = <>
          Title.Caption = 'C'#243'd.'
          Visible = False
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{4812297D-C359-46A5-B7A7-16DA5830D403}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end
        item
          Expanded = False
          FieldName = 'SEGURADORA'
          Font = <>
          Title.Caption = 'Seguradora'
          Width = 130
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{4A127290-F340-42E1-BA5A-3A72D1937BF6}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end
        item
          Expanded = False
          FieldName = 'VALOR_HORA'
          Font = <>
          Title.Caption = 'Valor hora'
          Width = 85
          Visible = False
          Precision = 0
          TextAlign = taRight
          FieldType = ftDecimal
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <
            item
              Expression = '*'
              EvalType = etExpression
              GUID = '{0723A95E-DBC5-4786-8C92-5399119F91EF}'
              WOwner = FrInterno
              WOrigem = EhNone
              Mask = 'R$ ,##0.00'
              PadLength = 0
              PadDirection = pdNone
              MaskType = mtDecimal
            end>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{3DEF8A88-8426-40ED-BC36-9080CFC41167}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end
        item
          Expanded = False
          FieldName = 'COD_CLIENTE'
          Font = <>
          Title.Caption = 'CNPJ'
          Width = 137
          Visible = True
          Precision = 0
          TextAlign = taRight
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{C620B922-7191-4452-B758-D6CCBC5E20FD}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end
        item
          Expanded = False
          Font = <>
          Width = 50
          Visible = False
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <
            item
              Expression = '*'
              Hint = 'Selecionar'
              EvalType = etExpression
              GUID = '{475CD548-6927-4D7E-BCE4-D0E2E2DE4027}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 310047
              OnClick = 'selecionarSeguradora'
              Color = clBlack
            end>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{F599647E-B442-443F-A067-BE33932B4DAF}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end>
    end
  end
  object FHBox11: TFHBox
    Left = 0
    Top = 0
    Width = 554
    Height = 61
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 3
    Padding.Left = 3
    Padding.Right = 3
    Padding.Bottom = 0
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object btnVoltar: TFButton
      Left = 0
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Voltar Tela'
      Align = alLeft
      Caption = 'Voltar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 0
      OnClick = btnVoltarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
        FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
        290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
        086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
        152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
        F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
        86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
        B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
        AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
        EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
        AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
        736BB6EF9B710000000049454E44AE426082}
      ImageId = 700081
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
    object FHBox4: TFHBox
      Left = 60
      Top = 0
      Width = 7
      Height = 50
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object btnPesquisar: TFButton
      Left = 67
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Pesquisar'
      Align = alLeft
      Caption = 'Pesquisar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 2
      OnClick = btnPesquisarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000002224944415478DAAD954D481561148667840C347F1229DD0822680B4B
        282368D12A90144522027321B8B44D8128B6085CA508091A486D021191D4853F
        F8B30B42022D085D184482D4E6128982284A747B4E9DF0F03933772EDC0F5E5E
        BE33EF39EF9CEF67C6F72286EFFBE7A13250020E40026C2593C95F5ECCE10714
        95D86D30022A42F2C640274689B40CA85D08BD079762BE600FE8C72899D280E2
        B20C5F414EDCF6758C53BF35D280E267A16D70D13C5B07ED608302C7BA74D2E1
        033004B28CB60BCD4094813CEC34F17E693FAC75F4C5FA02A5267C01F98F00AD
        5F00EFC66DD93149984E5E93D71E64700F9E34B13C84FB71169FDC87D00B133A
        E31E61319886EFEA7C0E41539CE29A2CF764C7842AC9FFE21A6CC1E53AEF4030
        928681ECE16F13BA45FE3B57B307E7EBBC0DC1685C032D600F4203F90BAEC10A
        7C53E7CF103C49A3783674644237C85F750D9EC2BD3A97F52C8EBA994E722DB4
        664292FBD33590CFC2A689D5235A8CB9FE9FC0150D7D23AF2C4827C2CFA05263
        72CCCA117F4F61D007759B50333933A7742ABEECFDBB999E3191E3BAE42E17DA
        73D02BD062C272518B8296D67EEC1E43CF9DE7B2272FB5C35C7007348634D542
        FD8950033579040DA65AFF8871CA24E88723CB35E59DEC49D81806F36039CAC4
        0FCAD48DAF02F7411DA80687E00378036629B2ABDAABD0C730934083740726D7
        D4DC8EBFA72A23066AE25E3A195919335093EBD0FF4FC53A1DD464D4404DE4B7
        5B03DECAAFF60FF9CFC91D70A0B6C30000000049454E44AE426082}
      ImageId = 27001
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
    object FHBox3: TFHBox
      Left = 127
      Top = 0
      Width = 7
      Height = 48
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object btnAceitar: TFButton
      Left = 134
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Aceitar'
      Align = alLeft
      Caption = 'Aceitar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 4
      OnClick = btnAceitarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
        0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
        1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
        95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
        101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
        1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
        A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
        C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
        482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
        1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
        C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
        8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
      ImageId = 700088
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
  end
  object tbSeguradora: TFTable
    FieldDefs = <
      item
        Name = 'COD_SEGURADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Seguradora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGURADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Seguradora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_HORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Hora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGURADORA_PAGA_ISS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Seguradora Paga Iss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOTA_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nota Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_REQUISICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Requisi'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_ISS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Iss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEPARA_NOTA_PECAS_SERVICOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Separa Nota Pecas Servi'#231'os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEG_ARQ_INT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Seguro Arq Int'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEG_ARQ_INT_DATE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Seguro Arq Int Date'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNPJ_SEG'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnpj Seguro'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SEGURADORA'
    Cursor = 'SEGURADORA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310069;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
