object FrmCadClienteContato: TFForm
  Left = 321
  Top = 163
  ActiveControl = vBoxPrincipal
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Contatos da empresa'
  ClientHeight = 796
  ClientWidth = 534
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600220'
  ShortcutKeys = <>
  InterfaceRN = 'CadClienteContatoRN'
  Access = False
  ChangedProp = 
    'FrmCadClienteContato.Height;'#13#10'FrmCadClienteContato.ActiveControl' +
    'FrmCadClienteContato.Width;'#13#10'FrmCadClienteContato_1.Touch.Intera' +
    'ctiveGestures;'#13#10'FrmCadClienteContato_1.Touch.InteractiveGestureO' +
    'ptions;'#13#10'FrmCadClienteContato_1.Touch.ParentTabletOptions;'#13#10'FrmC' +
    'adClienteContato_1.Touch.TabletOptions;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 534
    Height = 796
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 3
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitWidth = 524
    object hBoxBotoes: TFHBox
      Left = 0
      Top = 0
      Width = 514
      Height = 60
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 5
      Margin.Left = 5
      Margin.Right = 5
      Margin.Bottom = 5
      Spacing = 4
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 55
        Hint = 'Voltar Tela'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 55
        Hint = 'Aceita Registro Selecionado na Grid'
        Align = alLeft
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        Visible = False
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000002C64944415478DAB594DF4B145114C7CF9D3BEEAEAB39B8FE58DD69D5
          25480A322822535490122D948C24A2C71E7AE8A1E8C522C220CA7FA05EA2B7C0
          87840C0CCD0C1FC244A25F043D485B8EAE291968D9EEECCEECCE9DCEEC8E35FE
          58D7183D70E6CE9D33F3FDDC7BCE994B609B8D647AA1E278F72421C4912EAEEB
          BA2A0D5C0BD801E8ADCD87012129414D3354FFC6FB87DF0002882DC099F60698
          082D26E7D1A802B1989ABCAFDD2742CF9351FB80D36DF5F0696A21398F446220
          23C4B0C6037E78D43F661F70F2441D4CCCA4762023206202EAAA44E81B1CB70F
          686DA981E0EC520A2023404E01AAF796C2D3E1D7F6012D4D4740FAFE3B952214
          3720861DACF4C2D0C81614B9E96835847E844D00D6C0DC41D5AE221879F96EF3
          0014EBC2E112F6245D7E4609E435D41F82B945D94C91928418B6A7DC0363E31F
          41D3616985AAAE87F0DA82E0D06A00C57E7F2E08B93595BB032E4A2918EDAF32
          807034BEA606854236ECC8E68D9F2D39FFF92B02C12FD3C64A6A51FCC3BA2942
          881B21E3DE9282CA40C0EF584261C6FEC5AD3B308CC315140A2E6089384C7C96
          649DE9C7507C6CC31A20A40097FE5E148B4B4B4A4B7859D5D202841C0770C020
          1894A228DE86E22F365564845420E4ED4EBF2FBFB0C84394442A0D5104844D40
          8E8B071E1526BF4A51C6F47328DEF75F5D8490FD0819F59789B942BE80C524C9
          A3228C3F9BD341C1C1014C4D4EA338BB80E20F3376511A482342FAC532D19D27
          0820C714506271C8A23A7C9B9E9199C63A51FC6EBAEF33024CC859C2710FBC3E
          9FDBE97281AA28303F372B6B0976471AB87A7BA36F498618673A296FBE758566
          396F788ABDEE85F9799925D4FBD2E0F54E8C318B67041873BACA397324654D37
          BB39DE795ED7D49EA9A1AECB16616D1D5F035816E22D4E2D23058EA7BEDA8B1D
          B3AFEEF562E3272C6209CB68F59500CCB5065B64CBE7D3EAA3423FD5D16C5BFC
          71EFB3F4806DDDC176D81F0F015C28B7AC831E0000000049454E44AE426082}
        ImageId = 10
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnPesquisar: TFButton
        Left = 120
        Top = 0
        Width = 60
        Height = 55
        Caption = 'Pesquisar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnPesquisarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004BD4944415478DA9D955948636714C7CF4D62E292C47D19199BAB1444
          A6A363C1818A2B8856EA8E4A952A06D427A14F3EF4D127518A880BB850B562A5
          3EF8A80F76AC11DC1DC4655C06B154A341E3BEC6254BCFF932B9F5AA38A51F5C
          6EEE77BFFBFDBEF33FFF73C2D96C36E8E9E991A5A6A6FEE1E4E49400FF614824
          12B8BABA5A191E1E7E5D5050607D6E2D4780C1C1C1A0F8F8789DABAB6B083D7F
          6ED09ACBCB4B30180CBD333333A5C5C5C5A66701FDFDFD7C4A4A8A4EA9546AAC
          56EB6701777777603299E0FAFA1A767777FB2A2A2A7E181B1B333F0B484E4ED6
          A954AA470092E3E1B8BDBD85A3A323F0F4F4848B8B0B585F5FEF89898929319B
          CD9627017D7D7D3CE640A756AB4500DA9C9E777676D86965322928952AF0F2F2
          02A3D108EEEEEE80B2C2F1F1316C6C6CFC3E35355582D1DC3C02F4F6F6F26969
          690280E338766D6EFE0D6B6B1F715325787878B03993E91AE47227C068D90114
          0A05A039E0F0F010B6B6B67A878686CA6A6A6AAE440074119F9E9EAEC3133100
          7DB8BABA0A7ABD1EA2A2A284CD68D0FBD3D353D8DBDB03373737364F92C96432
          72161E682D373B3BBB5F04E8EEEEE633323274784A0D4DD2C70B0B8B909090C0
          64A1A48AC2E6242CC9676767A056AB00B567CEB2582CB0B2B2529A9898F88B08
          D0D5D5C5676666EA30690C303E3E0E1A0D0F010101F8A15D32FAF85F07DBD8EF
          F3F30BA0C028428A8CF2B4B0B0508207FB5504E8ECEC64004C9E864EF5E1C312
          BC79F3F5275938B0E7050400DDE9D5CDCD0DD6C305F8FBFB83542A65CF5817DA
          B8B8B82E11A0A3A38301BCBDBD35240F260B5EBDFA0A37E2D8C60F87D56A63F3
          24CDC181115EBC08C0643B33C0ECECAC168B560C686F6FE773727204805EBF0D
          21215F3269E8641209774F1A1B6E6CD79B40C7C7461601D995724511600EC480
          B6B63601400E595D5D83D0D030269154CA319908466BE9A28D2D167BBD6C6DFD
          05C1C1C1E0E2E2C200580BDAA4A42431A0A5A58501C84564B7F7EF67D1F75E98
          E860FCE89639C901A08DCDE63B94C405DB840125DA87F0F0701611D975626242
          8B452B06343737F3B9B9B9AC0EE8D494B8999959CCC36B78F93250482C398A0A
          9D6C6A306C836EE41D7C9BFA1DF8FAFAB21A2000F6242D16AD18D0D0D0C0E7E7
          E70B952C972BB0320F606969097C7CBC2128E80BB4A29A45717E7E0EDBDB7A98
          9E9E86136C116161A110F5F61BCC831F6B19A3A3A35A54E331202F2F4F879529
          F422B95CCE4E849509FBFBFB9FA2B0319DA9C991EEF3F3F3D0DADA0A111111A0
          D56A51520D0C0C0C68510D31A0A9A989E5009D2000689E7A8CA305D04573944C
          2A2C85428E4DD000D5D5D530323202E81C282A2A026767E7EF232323FB4480FA
          FAFA4711382034C8AA8E06E8680974276B52CFAAADAD85C9C949888E8EA64EFB
          23E6B44104A8ABAB7B1260EF3B1C3CF72F47D12C2F2F4355551559140203038D
          58800598BF3F05406363239F9595A5C3F034CF6DC63D51D63447AD7C7171112A
          2B2B616E6E8EF2777572729285397CC700480F292F2F9F469BFA383AE34399EE
          3F3F7CEF806015435959193306CAB889EFDE32406C6CACAAB0B0F0673F3FBF30
          94E89664B257ACF55EF55A858B06E5C171C7F7368448D11437F8E7F51BE6A314
          FBD23CAEFD89739C0617C8F0E64CBD0CFEDF20FD2CB8DF354AED850630E1FFB6
          E91F70B7FB1897F803840000000049454E44AE426082}
        ImageId = 13
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnNovo: TFButton
        Left = 180
        Top = 0
        Width = 60
        Height = 55
        Caption = 'Novo'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 3
        OnClick = btnNovoClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003EC4944415478DAB5965B681C5518C7FF33BB9BDD6433934DD6D840B5
          5523B489ED830F8A7879B708E2B320EA8B977A01056DB5F6C10749B14F229446
          2822A5488B0FA542DFBCE083340A5E9A6EBA694D8D4D1392B6BB3BDDD9CD6E32
          33E7F83FB333B31B6603C5E20CDF7E67CE39F3FDCE77396756C3FF7C69B73BB1
          6EEF36A97280BC9B3A4F9DA54E511C9AA953DFA0FE2B6B4C57BB020E7DFAC92B
          5493DD8CF7F6369149AFC134EB30FAEBFE732AE550D2709C35DCB76DF1483E6F
          9FA7B9054E9FCE1A1716BA01E4337B9EC5E8E8280DF446C6A5AC51AE438839B8
          CE15B617D979038DF9A3FEB8187E0ECB2B750CE6964F99A6F74722A1CDD2EC8F
          84946380B7DF7C079EE775ACDDA5417A2EE7E02CEC41CFBDEFB2EF2AD617BEC1
          E078039A9641B9A0411B7914954A1AD9AC75DA30C414CDFE4EF99EE1723600F6
          BDF7216AB55A6BE5EA960CA7BCC65514097829C29A3B7E4132F948E0A185CACC
          603496D93E3E1900BEA5174B5D01522AF3EAED126516EEB5A7D1FFE07748A69E
          84AEF76C5A08CE7A11F6E531053908E86709F82D06B06DBB05A0402E736486A1
          281072906171D84E6E0A50E1CA6C1FFB89AB9F6AE5A1703606A8DEBA158447C9
          0A746D3A007C8CBE07F6219D99E073BCBA5DB780EAEC2E053847E34A7E20E04C
          0C6059960F8050BF6502CEFB005D2F300F2791DB79137A22DF75F5A97BC69148
          C8D083D304FC1C0754CA103E007E9235FD2221339422DCC5630CD33A81A9AE00
          37FF30FAFB9B6768FC57E6E06BE6602E06A8544AAC79D9AA23C998E30A0D1691
          D02FC15B3A8CA187547F8395D3E7BF97DB799D1E0DFB002BFD1446464A932D80
          768280660C502ADD8C92EC271A3586E70212DA1CBDB90AB1F4993F3FBD6D37C1
          0E9AFF1483F27C031767E731B663FE031A3F49E37FC736DADED7DE42B3B11A25
          19116485B1BD4410F784B6CC7095A8D5F1E3706612526491EAD98A7353F378FC
          B13F9F50B1EF7A54284063B5DE5145A20D4215498649D35768DCE28B04681E87
          74027AD197BD1FC74F14B0F7F5890D251603ACD6ED68A3B500AA2102A0CB870A
          3DA16865CEF1203C039E303194DF852347BFC0FEF70F7405E804780A50AB5769
          AF1D9ED003E1971522CF5A7DD25F809ABF65642B019F6F0AE82160CD07D85647
          92C33349B49F6508EAEC9330CC1CBE3AFEA50224E017F946804140F5C5175EE6
          69EAB457AF6E213B8022285F74E4A8D53607864280FA10354348081822A0A400
          AEB30EDFF95612028888562FD1F6A43D476060E0AE1030CC5E8BA2CE7D190206
          08B014E04EAE00B0456D6C753C757A9039F0D1FE6386693C7F2700AB629D3A34
          71F85536EDC08308A0B43AE80D8AFAB8AB73204D51094B053A19CCD3FD44B445
          C55B7DA5AA1DE20563B7FFAFE2BF5EFF02A8A671371FD8FBD30000000049454E
          44AE426082}
        ImageId = 6
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAlterar: TFButton
        Left = 240
        Top = 0
        Width = 60
        Height = 55
        Caption = 'Alterar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 4
        OnClick = btnAlterarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003094944415478DAB5955B4853711CC7BF53A7E1D26990E14374F172BA
          5850910F163DFA641245049104654A48D04BB59C5A14A5250425157651307B31
          ED02068BE841891E4C66D843584FBA34DD76B6E32EB1D13CFFFEFF73F3B88B4E
          C13FFBEFFF3FE7FCF7FDFCAE6706ACF230E82F5AEEDEAAA14BFB4A8422E1C89E
          86C66BDF9602909AEAF3C8CDCD5D96B8DD6EC7C74F367C1EFC52D2DFFFFE07BD
          2526045CB9548F4020B0408010A24D7A052212F68D81810194951D405E5E9E74
          EE4EEB6DD88747B89E9EDE9F4901241955987D88C8BE24F38828C266B3A1A2E2
          30CC663382C1204C269304B15CB61A96043C7CFC60D1B0549F3987376FDF81F7
          38639E2D09F0FBFD3ACB45885268945051CB89EC0EC6271CE05D2E0C0D7F9584
          AC0D4DC979E09B9D8D0A8F2429C79E88DA338FC78BC8BF3084591FDD7B505979
          0423559A64D5DE6E74C705088280F6A78F965549A5FBF623A7B31C9C05C8F875
          08A37D83A000437C80D70366274428162BABEC060D1914AF4418D38CF81B0C80
          6F2CD4C4C3438318732031C0EBE5218A4A49AA252A12A582E4FBFAE74ECBA618
          713A9A28E0665C00CFBBF1ACE34952A1291FB526144F9864B7DB199BE0051524
          87CC7975738CF8F78DC771BAF99541D12671012EE78C5629D0BA58D481085CF5
          B1E2DC8D61B4F57E50CB3431C039338DCEAE8E6585656D6D1F8A0E1E55FB2045
          4A50A210CD4C4FC99646558FEC09E0B66EC1EE63F3E239D7C7248FB716708B36
          5A0A05CC31C09FE949AD6AF4B9A0050ADE5AC0CA0F75274A71D638442DEF454A
          7E8974BEB0689B0A4885F246D503D229202C01A61CE87AF9226E78E88F613FC5
          42F21AA9F93B34AFD85A54BC7D51401605F81860727262DE7AF5F5AC4BB65CFF
          984FBEB22FE676AA00133D1462103D601D05F00CF0DB312E7529D13A56295102
          DDBB489F1FB9AB396E970A584FEF0A7446F40033050817EA2E22333333A9268B
          1EA15008F7DBEE31C0067AE98906ACB136589E6765679D5C91BA3204AFD0D3D2
          DC5A4BB77E3AE7F400B64F67B9A0339B4EE646069D2C6146654D53CEA975AE4E
          166FF637E8D3CD394437DA6A8CFFFE8D6837CCAC0E4F0000000049454E44AE42
          6082}
        ImageId = 7
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnExcluir: TFButton
        Left = 300
        Top = 0
        Width = 60
        Height = 55
        Caption = 'Excluir'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 5
        OnClick = btnExcluirClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004F24944415478DAB5965B6C145518C7FF736666AFA5DBEEB6DB6D4BE9
          426B5B682D8AA912B50662C40B1A5F34A6B531A009D1102331519EF0457D3131
          DE1E080F4653914B626262BC61141510039142A076DDDADBA6F4B2BB855E662F
          DDDD99397E33B36C172C8D26BAD97FCE9CD933FFDFF9CEF9E63B2BE07FFE082B
          FD7810D84C031E1519B6E81CED9CC349B775EA87A97F82749CFA9FF700FC5F01
          C8D82D0A784F94C4AEFAA65A7B79A05CF4F8BD707A3DC825D358885DC56C7496
          8F0F4F26530BA9014DC7530419FB470032BF8F09385ABDDAE769BD77A353D616
          C1D20B404A81905600878BF0A5E0AE55E0AB7C183D17524317463314CD2B04D9
          BF2280CC9BC8BC6FD35D2DEECA60007C7C082C350B511220524802B39EE03AAD
          93C6A1494E08C11624942CCEFC703E914E657713A47759009933F238DFB4614D
          6BDDBA2A918F8520316E99CB4201623C619AABA49CD5EA81B54868369CFEBE4F
          A1485A0832F937C0A7C0DED252E7BE8E2DB7BBF5A17E48820A49B6CC0BADC4CC
          27AE99AB399D645DE3968D18EE8FE44687267FE9E2D87A1D80666FA78B85CD9D
          ED36477A16C27C1C928DC1F3C111B0721FB2FB9E81A8C49700AB2A21BD7E085A
          7C0A8997BBAC4868B950DF8293C7CE26338BB947288A13C5800EBB4D3A7EF7D6
          3B4AB4F085C28CBD477E86746B07F4B141645EDA0E91C0F0FA21BDF325587D13
          D40BA7A1ECDCB61405DDFB636022333131F31A01DE2A06BCE0AFF0BCBD7EFD1A
          A71E195C5A92AA00CA3EFA06E2DA6613B2489138DEE835CDB5B13012BB1E861A
          8D5A119078452DA28A8AF040E46B5AA6ED05C021FA068381AE9A7207109F5CDA
          D43CC4F3A105E1D90C049BBD60AEC562D67EE4F784BB3D48BABCE83B1B8A7573
          5415008769BD1A1B6A3B7D76EACCC52DF3BC9804C8CD6D283B72D2343720F3DD
          9DD0067F876EA4EB3580914D3617548AE2CCAFFDD9A7695F8B97E8CD1ABF776F
          7DC023F2E8E52273013245E0FDE45B48EB96225047C298DBF190B93C66CA920C
          102FF5629E7C43A1C8255AA2F662C0E3252E476FDBFA60A91E19B2CC29E7A540
          005547BF83DCD00C75984CF7F4A0ECDD8390A89FA3FE4CF78304992E40987F35
          C6E30A1F9F9CD94F11EC2E06D40A8230D2715BB34D1F1F03836A026ABFF8118E
          3BEF416E288C78D736E857A2601555A83C6C4133E7CF62EAB14E13A06BF4A6D6
          3722148E24E694D4F33DE6AB55F4A2D1465FACF3FB5AFDDE12A64F5F360135BD
          9F5114D5883DFB04F4996861B468400E1C86766506D33B9EA4BDE0103C3EA425
          072E118012B69100D11B4B453375FADA1AEA5CB251D4920A18B3EA8FD11A2385
          6BA3C9819BE2E64653BA81D5D4E1E2C0486231A7BE48E61FDFACD8BDEAB2C9FB
          9A83B52548500555E64D53032208D7175EC3DC8018D555ACF0E3F2442C1B9D53
          4E7503F7AF544D194DE827A7246D6AA8AE743B6C22ADFB0C90CB5A11E4877323
          042682797DD0653B4627628BB3A9B4F215F0C051A09F8668CB0164524004AAF7
          003B37926A3CA5B2DF5BC644A306118467B310641982DD6E96ECB9850446E257
          72C39CFFF63E70E02A10218F69D20449B9114027096A48D5865A800DBB80E72A
          29C3A84E6B6E9B8D796499A5340D73193A5F545548D23144A972EC14708E9E99
          CA9B1BED9FA4E472279A2D0F3040E40D1FD5C84A8AA6A91108D6D17D4A8DF961
          32B944B3A51953F5C32C892ECDEB11586741F6A647E60DB012929B641CF654A8
          CCD7DFD8DA1C6931AF85FC6CB3CB99ACF8AFE2BFF8FC05DEE5323773766BE800
          00000049454E44AE426082}
        ImageId = 8
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnSalvar: TFButton
        Left = 360
        Top = 0
        Width = 60
        Height = 55
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 6
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3
          0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220
          AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950
          4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5
          5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37
          35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067
          603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7
          29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B
          DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA
          B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B
          A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194
          B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002
          EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715
          87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B
          AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9
          32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66
          5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E
          4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05
          05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF
          0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76
          093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0
          C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65
          C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70
          9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D
          80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12
          B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8
          6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087
          94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF
          0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34
          2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2
          134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000
          0049454E44AE426082}
        ImageId = 4
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnCancelar: TFButton
        Left = 420
        Top = 0
        Width = 55
        Height = 55
        Caption = 'Cancelar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 7
        OnClick = btnCancelarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000036F4944415478DAB5937B48537114C7CFDDBD9B5B375FA98569605A14
          8A2414988A927F88996952948948149A6020998614BD0B7A5A448564A5882912
          1469A9B3971AE5BB742DCC245F99736ACE747373BBAF7E8B4BDD86530BF783C3
          FDFDF89EDFF7737EE77031B0F1C2E69B189B27D954BEDF546B4BC01486611965
          29C63C9B00B6DD9150EECEDEF4F044DF0D8661B3D16BB805079CDBFD9028797B
          D9D0A96AA962682601414C0B0AB898F894A03803C8DB8AA61BBA2A9434CD4422
          C8F87F0150CF49D4F354424C6C060E5611205D7932BE18DA0DA5E0278D81775F
          6AA827ADF92A86A1CDC3EFFB2740DC5D699608C34F6C5C1B49047885C99C644B
          81B47384214601FDA6264030F0936D8501752F5B54776992A64D1108D23A2700
          558D897051AECF32FFA49D41E98B285C0B8394020CAC06284C0FE84580C0FC45
          1C7CA55B403761805B55D94686A5A310A4665600EAF3D9D51E0199311B92654D
          E345306CFCFC57B2D9DC1CB8080767F10A085EB217DE763E31D677567D66192E
          1C01345601A87A4F0921ED4A8B3A2F6B9C28041DFD1D569281E0245E0E8E2848
          DC05BA74B5F071B212DC243EBFCC9F298AA795FD0DCD1C0BE6EAF5B3B6281655
          BFC93FEE8887C772BC77AA09825CF6C017753B3730D2AD1FD27CE574FA49F240
          F445AC515308EB9DE2A1AC39CFD0ADFE508DCC7721736ACE21C7E5DBBD4A0C3D
          1CAEC6DAC0DF211AE41F0B8C9F7A143DC8E014923FA0501EDD51404C535A7850
          7F5D3FA8E92E465AEA5C3FDC1FC03DBBEEB4C8CBDE2AFA3DC8287728A9CBE945
          0601C860926F2195BEF52A51F2FA8A614CABCA294B311D87792C21A02C213433
          D6D1C11106C6BAA0A2E97EEDE37DC670C18C287B9933A39D1ECF2A4F31DD9C8F
          B9E5900F86F86EB910B236D66E4CA782FC17674738965B835EF083D7B5E89382
          CEA5F335B704D8E338DE931C71DAD595F484965E39FDBCBD5439D8C286B5DEA6
          8D9BAF89C3E419D41BC15D4E102C1FD6013C24DACDC1F3414268C6227BA90BBC
          EA28353574C8ABCB534D49481659E4B3BC39C3EF193E68AB00F339FA96248924
          C9DCED41A9322F177FEC666526A5908F062A4B1835D271FE0E3B83A9F04B5903
          98AB24C2CF88D739B8638F5C17AF583AAAFD36F8F298316C6AE4978EF339ACC0
          90B6D8D3B3017E43CC117C88F0EE79C98CAA151C25A85ED822D60246F17B6E36
          8050C305558BAC001881293793894D97CD013F01E2D89E284DE0BF3E00000000
          49454E44AE426082}
        ImageId = 9
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object pgcPrincipal: TFPageControl
      Left = 0
      Top = 61
      Width = 520
      Height = 742
      ActivePage = tbsListagem
      TabOrder = 1
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tbsListagem: TFTabsheet
        Caption = 'Listagem'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxTbsListagem: TFVBox
          Left = 0
          Top = 0
          Width = 512
          Height = 714
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 5
          Margin.Left = 5
          Margin.Right = 5
          Margin.Bottom = 5
          Spacing = 0
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hBoxFiltrosLinha01: TFHBox
            Left = 0
            Top = 0
            Width = 466
            Height = 60
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            DesignSize = (
              462
              56)
            object vBoxFiltroContato: TFVBox
              Left = 0
              Top = 0
              Width = 110
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblFiltroContato: TFLabel
                Left = 0
                Top = 0
                Width = 39
                Height = 13
                Caption = 'Contato'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                Visible = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtFiltroContato: TFString
                Left = 0
                Top = 14
                Width = 100
                Height = 24
                Hint = 'Contato'
                HelpCaption = 'Contato'
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Contato / Email / Celular'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = btnPesquisarClick
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object vBoxFiltroRespPesqFabr: TFVBox
              Left = 110
              Top = 0
              Width = 130
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Visible = False
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftMin
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblFiltroResponsavelPesqFabr: TFLabel
                Left = 0
                Top = 0
                Width = 118
                Height = 13
                Caption = 'Respons'#225'vel pesq. f'#225'br.'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cboFiltroResponsavelPesqFabr: TFCombo
                Left = 0
                Top = 14
                Width = 120
                Height = 21
                Hint = 'Respons'#225'vel pela pesquisa da f'#225'brica'
                Flex = False
                ListOptions = 'Sim=S;N'#227'o=N'
                HelpCaption = 'Respons'#225'vel pela pesquisa da f'#225'brica'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = True
                Prompt = 'Respons'#225'vel pela pesquisa da f'#225'brica'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = False
                UseClearButton = True
                HideClearButtonOnNullValue = True
                OnEnter = btnPesquisarClick
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object vBoxFiltroAreaDeContato: TFVBox
              Left = 240
              Top = 0
              Width = 130
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Visible = False
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftMin
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblFiltroAreaDeContato: TFLabel
                Left = 0
                Top = 0
                Width = 78
                Height = 13
                Caption = #193'rea de contato'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cboFiltroAreaDeContato: TFCombo
                Left = 0
                Top = 14
                Width = 120
                Height = 21
                Hint = #193'rea de contato'
                LookupTable = tbFiltroClienteContatoTipo
                LookupKey = 'AREA_CONTATO'
                LookupDesc = 'DESCRICAO_CODIGO'
                Flex = False
                HelpCaption = #193'rea de contato'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = True
                Prompt = #193'rea de contato'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = False
                UseClearButton = True
                HideClearButtonOnNullValue = True
                OnEnter = btnPesquisarClick
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object iconLimparPesquisa: TFIconClass
              Left = 370
              Top = 0
              Width = 16
              Height = 16
              Hint = 'Limpar Time'
              Anchors = []
              Picture.Data = {
                07544269746D6170FA090000424DFA0900000000000036000000280000001900
                0000190000000100200000000000C40900000000000000000000000000000000
                0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000000000000000000000000000000000000000000000000000000000000000
                0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F0000000000000000000000000000000000000000000000000000000
                000000000000000000000000000000000000000000000000000000000000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                0000000000000000000000000000000000000000000000000000000000000000
                00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000000000000000000000000000000000000000000000000000000000000000
                0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                F000F0F0F000}
              OnClick = iconLimparPesquisaClick
              IconClass = 'trash'
              WOwner = FrInterno
              WOrigem = EhNone
              Size = 25
              Color = clBlack
            end
          end
          object hBoxTbsListagemSeparador01: TFHBox
            Left = 0
            Top = 61
            Width = 100
            Height = 5
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object grdContatos: TFGrid
            Left = 0
            Top = 67
            Width = 488
            Height = 202
            Align = alClient
            TabOrder = 2
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbClienteContato
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = True
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            ActionColumn.Title = 'A'#231#245'es'
            ActionColumn.Width = 100
            ActionColumn.TextAlign = taCenter
            ActionColumn.Visible = True
            Columns = <
              item
                Expanded = False
                FieldName = 'CONTATO'
                Font = <>
                Title.Caption = 'Contato'
                Width = 151
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{A243ACEB-CAFC-48A4-B6C3-CCC788E96CA9}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'CPF'
                Font = <>
                Width = 90
                Visible = False
                Precision = 0
                TextAlign = taRight
                FieldType = ftInteger
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{099B39BF-90AA-424A-9F01-523AFA7821CF}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'PREFIXO_CEL'
                Font = <>
                Title.Caption = 'DDD'
                Width = 45
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{F03AF2C8-96E7-4D51-9F2B-306DEF20F01A}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'CELULAR'
                Font = <>
                Title.Caption = 'Celular'
                Width = 92
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{6EE5FBB3-358F-4AE3-A146-48E1D8AE2EA1}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'EMAIL'
                Font = <>
                Title.Caption = 'E-mail'
                Width = 150
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{CE0B831B-F6F6-4D18-9D38-75E508C39932}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end>
          end
        end
      end
      object tbsCadastro: TFTabsheet
        Caption = 'Cadastro'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxTbsCadastro: TFVBox
          Left = 0
          Top = 0
          Width = 512
          Height = 714
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = True
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hBoxTbsCadastroLinha01: TFHBox
            Left = 0
            Top = 0
            Width = 500
            Height = 70
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 5
            Margin.Right = 5
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxContato: TFVBox
              Left = 0
              Top = 0
              Width = 160
              Height = 60
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              ParentBackground = False
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblContato: TFLabel
                Left = 0
                Top = 0
                Width = 44
                Height = 16
                Caption = 'Contato'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object hBoxContato: TFHBox
                Left = 0
                Top = 17
                Width = 150
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object edtContato: TFString
                  Left = 0
                  Top = 0
                  Width = 110
                  Height = 24
                  Hint = 'Contato'
                  Table = tbClienteContato
                  FieldName = 'CONTATO'
                  HelpCaption = 'Contato'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Contato'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccUpper
                  Pwd = False
                  Maxlength = 50
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  OnEnter = btnSalvarClick
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
                object btnPesquisarContato: TFButton
                  Left = 110
                  Top = 0
                  Width = 30
                  Height = 30
                  Hint = 'Pesquisar contato'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnPesquisarContatoClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                    E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                    B9B9B6418D210000000049454E44AE426082}
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconClass = 'search'
                  IconReverseDirection = False
                end
              end
            end
          end
          object FHBox1: TFHBox
            Left = 0
            Top = 71
            Width = 500
            Height = 61
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxContatoSexo: TFVBox
              Left = 0
              Top = 0
              Width = 130
              Height = 50
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblSexo: TFLabel
                Left = 0
                Top = 0
                Width = 28
                Height = 16
                Caption = 'Sexo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cboContatoSexo: TFCombo
                Left = 0
                Top = 17
                Width = 120
                Height = 21
                Hint = 'Sexo'
                Table = tbClienteContato
                FieldName = 'COD_SEXO'
                Flex = True
                ListOptions = 'Masculino=M;Feminino=F'
                HelpCaption = 'Sexo'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Sexo'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = False
                HideClearButtonOnNullValue = False
                OnEnter = btnSalvarClick
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object vBoxContatoDataNasc: TFVBox
              Left = 130
              Top = 0
              Width = 160
              Height = 50
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblNascimento: TFLabel
                Left = 0
                Top = 0
                Width = 66
                Height = 16
                Caption = 'Nascimento'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object dtNascimento: TFDate
                Left = 0
                Top = 17
                Width = 150
                Height = 24
                Hint = 'Nascimento'
                Table = tbClienteContato
                FieldName = 'ANIVERSARIO'
                HelpCaption = 'Nascimento'
                TabOrder = 0
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Nascimento'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Format = 'dd/MM/yyyy'
                ShowCheckBox = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = btnSalvarClick
                ShowTime = False
                ShowOnFocus = False
              end
            end
          end
          object FHBox2: TFHBox
            Left = 0
            Top = 133
            Width = 500
            Height = 52
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxAreaDeContato: TFVBox
              Left = 0
              Top = 0
              Width = 190
              Height = 60
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblAreaDeContato: TFLabel
                Left = 0
                Top = 0
                Width = 91
                Height = 16
                Caption = #193'rea de contato'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object hBoxAreaDeContato: TFHBox
                Left = 0
                Top = 17
                Width = 180
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object cboAreaDeContato: TFCombo
                  Left = 0
                  Top = 0
                  Width = 150
                  Height = 21
                  Hint = #193'rea de contato'
                  Table = tbClienteContato
                  LookupTable = tbClienteContatoTipo
                  FieldName = 'AREA_CONTATO'
                  LookupKey = 'AREA_CONTATO'
                  LookupDesc = 'DESCRICAO_CODIGO'
                  Flex = True
                  HelpCaption = #193'rea de contato'
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = #193'rea de contato'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  OnEnter = btnSalvarClick
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
                object icoAreaDeContato: TFIconClass
                  Left = 150
                  Top = 0
                  Width = 30
                  Height = 30
                  Hint = #193'rea de contato'
                  Picture.Data = {
                    07544269746D6170460E0000424D460E00000000000036000000280000001E00
                    00001E0000000100200000000000100E00000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    000000000000000000000000000000000000000000000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000}
                  OnClick = icoAreaDeContatoClick
                  IconClass = 'plus-square-o'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 30
                  Color = clBlack
                end
              end
            end
            object vBoxContatoFuncao: TFVBox
              Left = 190
              Top = 0
              Width = 164
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblFuncao: TFLabel
                Left = 0
                Top = 0
                Width = 41
                Height = 16
                Caption = 'Fun'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FHBox5: TFHBox
                Left = 0
                Top = 17
                Width = 158
                Height = 31
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 3
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  154
                  27)
                object edtFuncao: TFString
                  Left = 0
                  Top = 0
                  Width = 110
                  Height = 24
                  Hint = 'Fun'#231#227'o'
                  Table = tbClienteContato
                  FieldName = 'FUNCAO'
                  HelpCaption = 'Fun'#231#227'o'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Fun'#231#227'o'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccUpper
                  Pwd = False
                  Maxlength = 50
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  OnEnter = btnSalvarClick
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
                object iconLimparFuncao: TFIconClass
                  Left = 110
                  Top = 0
                  Width = 16
                  Height = 16
                  Hint = 'Limpar Fun'#231#227'o'
                  Anchors = []
                  Picture.Data = {
                    07544269746D6170FA090000424DFA0900000000000036000000280000001900
                    0000190000000100200000000000C40900000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000000000000000000000000000000000000000
                    000000000000000000000000000000000000000000000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000}
                  OnClick = iconLimparFuncaoClick
                  IconClass = 'trash'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 25
                  Color = clBlack
                end
              end
            end
          end
          object FVBox19: TFVBox
            Left = 0
            Top = 186
            Width = 500
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Color = 15724527
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 3
            Padding.Left = 7
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 3
            Margin.Top = 10
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel8: TFLabel
              Left = 0
              Top = 0
              Width = 88
              Height = 19
              Caption = 'Documentos'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = 13392431
              Font.Height = -16
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox3: TFHBox
            Left = 0
            Top = 212
            Width = 500
            Height = 57
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxContatoCpf: TFVBox
              Left = 0
              Top = 0
              Width = 120
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftMin
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblCPF: TFLabel
                Left = 0
                Top = 0
                Width = 22
                Height = 16
                Caption = 'CPF'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtCPF: TFString
                Left = 0
                Top = 17
                Width = 110
                Height = 24
                Hint = 'CPF'
                Table = tbClienteContato
                FieldName = 'CPF'
                HelpCaption = 'CPF'
                TabOrder = 0
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'CPF'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccUpper
                Pwd = False
                Mask = '999.999.999-99'
                Maxlength = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnChange = edtCPFChange
                OnEnter = btnSalvarClick
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object vBoxContatoRg: TFVBox
              Left = 120
              Top = 0
              Width = 120
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblRG: TFLabel
                Left = 0
                Top = 0
                Width = 20
                Height = 16
                Caption = 'RG '
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtRG: TFString
                Left = 0
                Top = 17
                Width = 110
                Height = 24
                Hint = 'RG'
                Table = tbClienteContato
                FieldName = 'RG'
                HelpCaption = 'RG'
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'RG'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccUpper
                Pwd = False
                Maxlength = 20
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = btnSalvarClick
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object vBoxContatoOrgaoEmissor: TFVBox
              Left = 240
              Top = 0
              Width = 90
              Height = 50
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftMin
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblOrgaoEmissor: TFLabel
                Left = 0
                Top = 0
                Width = 88
                Height = 16
                Caption = 'Org'#227'o emissor '
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtOrgaoEmissor: TFString
                Left = 0
                Top = 17
                Width = 80
                Height = 24
                Hint = #211'rg'#227'o Emissor'
                Table = tbClienteContato
                FieldName = 'ORGAO_EMISSOR'
                HelpCaption = #211'rg'#227'o Emissor'
                TabOrder = 0
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = #211'rg'#227'o Emissor'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccUpper
                Pwd = False
                Maxlength = 6
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = btnSalvarClick
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object vBoxContatoDataDeEmissao: TFVBox
              Left = 330
              Top = 0
              Width = 160
              Height = 50
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 3
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftMin
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblDataDeEmissao: TFLabel
                Left = 0
                Top = 0
                Width = 99
                Height = 16
                Caption = 'Data de emiss'#227'o '
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object dtEmissao: TFDate
                Left = 0
                Top = 17
                Width = 150
                Height = 24
                Hint = 'Data de emiss'#227'o'
                Table = tbClienteContato
                FieldName = 'DATA_EMISSAO'
                HelpCaption = 'Data de emiss'#227'o'
                TabOrder = 0
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Data de emiss'#227'o'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Format = 'dd/MM/yyyy'
                ShowCheckBox = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = btnSalvarClick
                ShowTime = False
                ShowOnFocus = False
              end
            end
          end
          object hBoxTbsCadastroLinha02: TFHBox
            Left = 0
            Top = 270
            Width = 500
            Height = 60
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 5
            Margin.Top = 0
            Margin.Left = 5
            Margin.Right = 5
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxContatoCnh: TFVBox
              Left = 0
              Top = 0
              Width = 120
              Height = 50
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblCNH: TFLabel
                Left = 0
                Top = 0
                Width = 24
                Height = 16
                Caption = 'CNH'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtCNH: TFString
                Left = 0
                Top = 17
                Width = 110
                Height = 24
                Hint = 'CNH'
                Table = tbClienteContato
                FieldName = 'CNH'
                HelpCaption = 'CNH'
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'CNH'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccUpper
                Pwd = False
                Maxlength = 11
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = btnSalvarClick
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object vBoxContatoPassaporteCarteira: TFVBox
              Left = 120
              Top = 0
              Width = 130
              Height = 50
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblPassaporteCarteira: TFLabel
                Left = 0
                Top = 0
                Width = 126
                Height = 16
                Caption = 'Passaporte / Carteira '
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtPassaporteCarteira: TFString
                Left = 0
                Top = 17
                Width = 120
                Height = 24
                Hint = 'Passaporte / Carteira'
                Table = tbClienteContato
                FieldName = 'PASSAPORTE_CARTEIRACIVIL'
                HelpCaption = 'Passaporte / Carteira'
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Passaporte / Carteira'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccUpper
                Pwd = False
                Maxlength = 20
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = btnSalvarClick
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
          end
          object FVBox1: TFVBox
            Left = 0
            Top = 331
            Width = 500
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Color = 15724527
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 3
            Padding.Left = 7
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 6
            Margin.Top = 10
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel1: TFLabel
              Left = 0
              Top = 0
              Width = 55
              Height = 19
              Caption = 'Contato'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = 13392431
              Font.Height = -16
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
          end
          object hBoxTbsCadastroLinha03: TFHBox
            Left = 0
            Top = 357
            Width = 500
            Height = 60
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 7
            Margin.Top = 0
            Margin.Left = 5
            Margin.Right = 5
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxContatoEmail: TFVBox
              Left = 0
              Top = 0
              Width = 120
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblEmail: TFLabel
                Left = 0
                Top = 0
                Width = 40
                Height = 16
                Caption = 'E-mail '
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtEmail: TFString
                Left = 0
                Top = 17
                Width = 110
                Height = 24
                Hint = 'E-mail'
                Table = tbClienteContato
                FieldName = 'EMAIL'
                HelpCaption = 'E-mail'
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'E-mail'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccLower
                Pwd = False
                Maxlength = 60
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = btnSalvarClick
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object vBoxContatoWhatsapp: TFVBox
              Left = 120
              Top = 0
              Width = 200
              Height = 70
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hBoxContatoWhatsappLbl: TFHBox
                Left = 0
                Top = 0
                Width = 190
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftMin
                Flex.Hflex = ftMin
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object lblWhatsapp: TFLabel
                  Left = 0
                  Top = 0
                  Width = 57
                  Height = 16
                  Caption = 'Whatsapp'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object icoWhatsapp: TFIconClass
                  Left = 57
                  Top = 0
                  Width = 16
                  Height = 16
                  Picture.Data = {
                    07544269746D617036040000424D360400000000000036000000280000001000
                    0000100000000100200000000000000400000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000}
                  OnClick = icoWhatsappClick
                  IconClass = 'whatsapp'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 16
                  Color = clBlack
                end
              end
              object hBoxContatoWhatsapp: TFHBox
                Left = 0
                Top = 21
                Width = 190
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  186
                  36)
                object edtDDDWhatsapp: TFInteger
                  Left = 0
                  Top = 0
                  Width = 50
                  Height = 29
                  Hint = 'DDD Whatsapp'
                  Table = tbClienteContato
                  FieldName = 'PREFIXO_WHATSAPP'
                  HelpCaption = 'DDD Whatsapp'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'DDD Whatsapp'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Align = alClient
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -17
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  OnEnter = btnSalvarClick
                end
                object edtTelefoneWhatsapp: TFInteger
                  Left = 50
                  Top = 0
                  Width = 100
                  Height = 29
                  Hint = 'Whatsapp'
                  Table = tbClienteContato
                  FieldName = 'WHATSAPP'
                  HelpCaption = 'Whatsapp'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Whatsapp'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Align = alClient
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -17
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  OnEnter = btnSalvarClick
                end
                object icoLimparTelefoneWhatsapp: TFIconClass
                  Left = 150
                  Top = 0
                  Width = 16
                  Height = 16
                  Hint = 'Limpar Telefone Whatsapp'
                  Anchors = []
                  Picture.Data = {
                    07544269746D6170FA090000424DFA0900000000000036000000280000001900
                    0000190000000100200000000000C40900000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000000000000000000000000000000000000000
                    000000000000000000000000000000000000000000000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000}
                  OnClick = icoLimparTelefoneWhatsappClick
                  IconClass = 'trash'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 25
                  Color = clBlack
                end
              end
            end
          end
          object hBoxTbsCadastroLinha04: TFHBox
            Left = 0
            Top = 418
            Width = 500
            Height = 80
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 8
            Margin.Top = 0
            Margin.Left = 5
            Margin.Right = 5
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxContatoFoneRes: TFVBox
              Left = 0
              Top = 0
              Width = 200
              Height = 70
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hBoxContatoFoneResLbl: TFHBox
                Left = 0
                Top = 0
                Width = 190
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftMin
                Flex.Hflex = ftMin
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object lblResidencial: TFLabel
                  Left = 0
                  Top = 0
                  Width = 64
                  Height = 16
                  Caption = 'Residencial'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object icoTelefone: TFIconClass
                  Left = 64
                  Top = 0
                  Width = 16
                  Height = 16
                  Picture.Data = {
                    07544269746D617036040000424D360400000000000036000000280000001000
                    0000100000000100200000000000000400000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000}
                  IconClass = 'phone'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 16
                  Color = clBlack
                end
              end
              object hBoxContatoFoneRes: TFHBox
                Left = 0
                Top = 21
                Width = 190
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  186
                  36)
                object edtDDDResidencial: TFInteger
                  Left = 0
                  Top = 0
                  Width = 50
                  Height = 29
                  Hint = 'DDD Residencial'
                  Table = tbClienteContato
                  FieldName = 'PREFIXO_RES'
                  HelpCaption = 'DDD Residencial'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'DDD Residencial'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Align = alClient
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -17
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  OnEnter = btnSalvarClick
                end
                object edtTelefoneResidencial: TFInteger
                  Left = 50
                  Top = 0
                  Width = 100
                  Height = 29
                  Hint = 'Residencial'
                  Table = tbClienteContato
                  FieldName = 'FONE'
                  HelpCaption = 'Residencial'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Residencial'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Align = alClient
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -17
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  OnEnter = btnSalvarClick
                end
                object icoLimparTelefoneResidencial: TFIconClass
                  Left = 150
                  Top = 0
                  Width = 16
                  Height = 16
                  Hint = 'Limpar Telefone Residencial'
                  Anchors = []
                  Picture.Data = {
                    07544269746D6170FA090000424DFA0900000000000036000000280000001900
                    0000190000000100200000000000C40900000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000000000000000000000000000000000000000
                    000000000000000000000000000000000000000000000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000}
                  OnClick = icoLimparTelefoneResidencialClick
                  IconClass = 'trash'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 25
                  Color = clBlack
                end
              end
            end
            object vBoxContatoCelular: TFVBox
              Left = 200
              Top = 0
              Width = 200
              Height = 70
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hBoxContatoCelularLbl: TFHBox
                Left = 0
                Top = 0
                Width = 190
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftMin
                Flex.Hflex = ftMin
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object lblCelular: TFLabel
                  Left = 0
                  Top = 0
                  Width = 44
                  Height = 16
                  Caption = 'Celular '
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object icoCelular: TFIconClass
                  Left = 44
                  Top = 0
                  Width = 16
                  Height = 16
                  Picture.Data = {
                    07544269746D617036040000424D360400000000000036000000280000001000
                    0000100000000100200000000000000400000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000}
                  OnClick = icoCelularClick
                  IconClass = 'mobile'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 16
                  Color = clBlack
                end
              end
              object hBoxContatoCelular: TFHBox
                Left = 0
                Top = 21
                Width = 190
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  186
                  36)
                object edtDDDCelular: TFInteger
                  Left = 0
                  Top = 0
                  Width = 50
                  Height = 29
                  Hint = 'DDD Celular'
                  Table = tbClienteContato
                  FieldName = 'PREFIXO_CEL'
                  HelpCaption = 'DDD Celular'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'DDD Celular'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Align = alClient
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -17
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  OnEnter = btnSalvarClick
                end
                object edtTelefoneCelular: TFInteger
                  Left = 50
                  Top = 0
                  Width = 100
                  Height = 29
                  Hint = 'Celular'
                  Table = tbClienteContato
                  FieldName = 'CELULAR'
                  HelpCaption = 'Celular'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Celular'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Align = alClient
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -17
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  OnEnter = btnSalvarClick
                end
                object icoLimparTelefoneCelular: TFIconClass
                  Left = 150
                  Top = 0
                  Width = 16
                  Height = 16
                  Hint = 'Limpar Telefone Celular'
                  Anchors = []
                  Picture.Data = {
                    07544269746D6170FA090000424DFA0900000000000036000000280000001900
                    0000190000000100200000000000C40900000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000000000000000000000000000000000000000
                    000000000000000000000000000000000000000000000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000}
                  OnClick = icoLimparTelefoneCelularClick
                  IconClass = 'trash'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 25
                  Color = clBlack
                end
              end
            end
          end
          object FVBox2: TFVBox
            Left = 0
            Top = 499
            Width = 500
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Color = 15724527
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 3
            Padding.Left = 7
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 9
            Margin.Top = 10
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel2: TFLabel
              Left = 0
              Top = 0
              Width = 52
              Height = 19
              Caption = 'Op'#231#245'es'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = 13392431
              Font.Height = -16
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
          end
          object hBoxTbsCadastroLinha05: TFHBox
            Left = 0
            Top = 525
            Width = 500
            Height = 60
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 10
            Margin.Top = 0
            Margin.Left = 5
            Margin.Right = 5
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxContatoPoliticamenteExposto: TFVBox
              Left = 0
              Top = 0
              Width = 210
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblTituloPoliticamenteExposto: TFLabel
                Left = 0
                Top = 0
                Width = 135
                Height = 16
                Caption = #201' politicamente exposto'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cboPoliticamenteExposto: TFCombo
                Left = 0
                Top = 17
                Width = 200
                Height = 21
                Hint = #201' Politicamente Exposto'
                Table = tbClienteContato
                FieldName = 'POLITICAMENTE_EXPOSTO'
                Flex = True
                ListOptions = '0-N'#227'o '#233' PEP N'#227'o possui informa'#231#227'o=0;1-'#201' PEP=1'
                HelpCaption = #201' Politicamente Exposto'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = #201' Politicamente Exposto'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = True
                HideClearButtonOnNullValue = True
                Align = alLeft
                OnEnter = btnSalvarClick
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object vBoxEhComprador: TFVBox
              Left = 210
              Top = 0
              Width = 110
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftMin
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblEhComprador: TFLabel
                Left = 0
                Top = 0
                Width = 73
                Height = 16
                Caption = #201' comprador'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cboEhComprador: TFCombo
                Left = 0
                Top = 17
                Width = 100
                Height = 21
                Hint = #201' comprador'
                Table = tbClienteContato
                FieldName = 'EH_COMPRADOR'
                Flex = False
                ListOptions = 'Sim=S;N'#227'o=N'
                HelpCaption = #201' comprador'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = True
                Prompt = #201' comprador'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = False
                UseClearButton = False
                HideClearButtonOnNullValue = True
                OnEnter = btnSalvarClick
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object vBoxEhPreposto: TFVBox
              Left = 320
              Top = 0
              Width = 110
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftMin
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblEhPreposto: TFLabel
                Left = 0
                Top = 0
                Width = 61
                Height = 16
                Caption = #201' preposto'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cboEhPreposto: TFCombo
                Left = 0
                Top = 17
                Width = 100
                Height = 21
                Hint = #201' preposto'
                Table = tbClienteContato
                FieldName = 'EH_PREPOSTO'
                Flex = False
                ListOptions = 'Sim=S;N'#227'o=N'
                HelpCaption = #201' preposto'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = True
                Prompt = #201' preposto'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = False
                UseClearButton = False
                HideClearButtonOnNullValue = True
                OnEnter = btnSalvarClick
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
          end
          object FHBox4: TFHBox
            Left = 0
            Top = 586
            Width = 500
            Height = 53
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 11
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxEhResponsavelPelaGarantiaDaMontadora: TFVBox
              Left = 0
              Top = 0
              Width = 240
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblResponsavelPelaGarantiaDaMontadora: TFLabel
                Left = 0
                Top = 0
                Width = 234
                Height = 16
                Caption = 'Respons'#225'vel pela garantia da montadora'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cboResponsavelPelaGarantiaDaMontadora: TFCombo
                Left = 0
                Top = 17
                Width = 230
                Height = 21
                Hint = 'Respons'#225'vel pela garantia da montadora'
                Table = tbClienteContato
                FieldName = 'RESP_GARANTIA_MONTADORA'
                Flex = True
                ListOptions = 'Sim=S;N'#227'o=N'
                HelpCaption = 'Respons'#225'vel pela garantia da montadora'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = True
                Prompt = 'Respons'#225'vel pela garantia da montadora'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = False
                UseClearButton = False
                HideClearButtonOnNullValue = True
                OnEnter = btnSalvarClick
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object vBosResponsavelPelaPesquisaDaFabrica: TFVBox
              Left = 240
              Top = 0
              Width = 230
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblResponsavelPelaPesquisaDaFabrica: TFLabel
                Left = 0
                Top = 0
                Width = 214
                Height = 16
                Caption = 'Respons'#225'vel pela pesquisa da f'#225'brica'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cboResponsavelPelaPesquisaDaFabrica: TFCombo
                Left = 0
                Top = 17
                Width = 210
                Height = 21
                Hint = 'Respons'#225'vel pela pesquisa da f'#225'brica'
                Table = tbClienteContato
                FieldName = 'EH_PESQUISA'
                Flex = True
                ListOptions = 'Sim=S;N'#227'o=N'
                HelpCaption = 'Respons'#225'vel pela pesquisa da f'#225'brica'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = True
                Prompt = 'Respons'#225'vel pela pesquisa da f'#225'brica'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = False
                UseClearButton = False
                HideClearButtonOnNullValue = True
                OnEnter = btnSalvarClick
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
          end
          object hBoxTbsCadastroLinha06: TFHBox
            Left = 0
            Top = 640
            Width = 500
            Height = 60
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 12
            Margin.Top = 0
            Margin.Left = 5
            Margin.Right = 5
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object vBoxContatoTime: TFVBox
              Left = 0
              Top = 0
              Width = 181
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblTime: TFLabel
                Left = 0
                Top = 0
                Width = 33
                Height = 16
                Caption = 'Time '
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FHBox6: TFHBox
                Left = 0
                Top = 17
                Width = 172
                Height = 31
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 3
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  168
                  27)
                object edtTime: TFString
                  Left = 0
                  Top = 0
                  Width = 110
                  Height = 24
                  Hint = 'Time'
                  Table = tbClienteContato
                  FieldName = 'TIME_ESPORTE'
                  HelpCaption = 'Time'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Time'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccUpper
                  Pwd = False
                  Maxlength = 30
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  OnEnter = btnSalvarClick
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
                object iconLimparTime: TFIconClass
                  Left = 110
                  Top = 0
                  Width = 16
                  Height = 16
                  Hint = 'Limpar Time'
                  Anchors = []
                  Picture.Data = {
                    07544269746D6170FA090000424DFA0900000000000036000000280000001900
                    0000190000000100200000000000C40900000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000000000000000000000000000000000000000
                    000000000000000000000000000000000000000000000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000}
                  OnClick = iconLimparTimeClick
                  IconClass = 'trash'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 25
                  Color = clBlack
                end
              end
            end
            object vBoxContatoHobby: TFVBox
              Left = 181
              Top = 0
              Width = 180
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblHobby: TFLabel
                Left = 0
                Top = 0
                Width = 39
                Height = 16
                Caption = 'Hobby '
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FHBox7: TFHBox
                Left = 0
                Top = 17
                Width = 167
                Height = 26
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 3
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  163
                  22)
                object edtHobby: TFString
                  Left = 0
                  Top = 0
                  Width = 110
                  Height = 24
                  Hint = 'Hobby'
                  Table = tbClienteContato
                  FieldName = 'HOBBY'
                  HelpCaption = 'Hobby'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Hobby'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccUpper
                  Pwd = False
                  Maxlength = 30
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  OnEnter = btnSalvarClick
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
                object iconLimparHobby: TFIconClass
                  Left = 110
                  Top = 0
                  Width = 16
                  Height = 16
                  Hint = 'Limpar Hobby'
                  Anchors = []
                  Picture.Data = {
                    07544269746D6170FA090000424DFA0900000000000036000000280000001900
                    0000190000000100200000000000C40900000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000000000000000000000000000000000000000
                    000000000000000000000000000000000000000000000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
                    0000000000000000000000000000000000000000000000000000000000000000
                    00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000000000000000000000000000000000000000000000000000000000000000
                    0000000000000000000000000000000000000000000000000000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000}
                  OnClick = iconLimparHobbyClick
                  IconClass = 'trash'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 25
                  Color = clBlack
                end
              end
            end
          end
        end
      end
    end
  end
  object tbClienteContato: TFTable
    FieldDefs = <
      item
        Name = 'USUARIO_CAD_NOME_COMPL_LOGIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio Cad Nome Complemento Login'
        GUID = '{DF590590-E64C-4D7F-98B0-598BDDA6D19D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato'
        GUID = '{30C15F8B-7FA2-4095-88CE-0085C496CBD0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        GUID = '{393B94BC-5AC1-495D-A796-BE49C420AC5D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo Cel'
        GUID = '{285C03B2-9E5F-4309-9BC3-49EBBD6C73E4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular'
        GUID = '{1B93B53C-4A6F-45B5-8112-75272A122D89}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        GUID = '{0FC3D55F-5E03-4C1D-AAD1-BE7A036B7785}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_CADASTRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Cadastro'
        GUID = '{B68C5DB5-0775-4C49-83F5-CA17D7FA5CB5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESP_GARANTIA_MONTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Resp Garantia Montadora'
        GUID = '{E4CD08FC-1B10-49CA-8995-CBF98C015436}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_COMPRADOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Comprador'
        GUID = '{2AF6CCE9-DE46-4CAC-8A99-6189059A98D2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ULTIMA_ATUALIZACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data '#218'ltima Atualiza'#231#227'o'
        GUID = '{81B9B356-1671-49B4-859C-EDEB43E39A9C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUAR_ULT_ATU_NOME_COMPL_LOGIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usuar Ult Atu Nome Complemento Login'
        GUID = '{524B3DB0-CADB-4F48-8E26-8002D02FC937}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_PESQUISA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Pesquisa'
        GUID = '{815181B9-3BB3-4B61-BCBD-8DE46D72AD18}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AREA_CONTATO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Area Contato Descri'#231#227'o Codigo'
        GUID = '{DD5760EA-C3E4-4098-959C-F83CE53A614F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_CONTATO'
    Cursor = 'BUSCA_CLIENTE_CONTATO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600220;44802'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteContatoTipo: TFTable
    FieldDefs = <
      item
        Name = 'AREA_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Area Contato'
        GUID = '{E240A50D-A6CD-4167-9EC6-284E1ADA9F8B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo'
        GUID = '{CE68C952-6D82-449E-AA08-F64036E22E31}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        GUID = '{AF6B0D25-5FC7-44D4-88AB-DDA797184BA5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_CLIENTE_CONTATO_TIPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600220;44803'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFiltroClienteContatoTipo: TFTable
    FieldDefs = <
      item
        Name = 'AREA_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Area Contato'
        GUID = '{8F4ECC1E-117D-4AAF-AF52-C1444D4EA3EF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo'
        GUID = '{D49841A4-824A-41F7-B3C8-76235925A505}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        GUID = '{51643EDB-1FCD-4C97-A790-14A711A0F14F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_CLIENTE_CONTATO_TIPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600220;44804'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
