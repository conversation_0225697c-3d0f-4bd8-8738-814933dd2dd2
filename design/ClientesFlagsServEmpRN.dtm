object ClientesFlagsServEmpRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600452'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object sc: TFSchema
    Tables = <
      item
        Table = tbClienteCpagarIcmsSubEmp
        GUID = '{C222A705-DE12-4467-A326-5D545F918C8C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbAcessorioEmp
        GUID = '{BFC51E55-80E1-403C-B913-************}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbIndustriaEmp
        GUID = '{435A44CA-AE0A-4581-8049-23A95511EECB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbClienteDiverso
        GUID = '{4750218E-0F6E-4230-A19F-B651BF0F28E7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbFornecDescontaIcmsstEmp
        GUID = '{CA4FAB3D-13E8-40E6-8261-76DA1D85614A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbClienteIssEmpresa
        GUID = '{4BD0FC6D-FFC7-48D7-857C-BFECDFC3A0BB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbClienteIsentoIssEmpresa
        GUID = '{5FF0A046-61A7-4819-A6CA-576BD02700FC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbClienteIgnoraPjReterIss
        GUID = '{3D4EF712-365B-4883-9C02-2C1F20657FB0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbFornecSubIcmsEmp
        GUID = '{8AC8C05A-3A08-4719-A077-92B2459F4BB4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbFornecIvaEmp
        GUID = '{94E3DF9C-9F13-462B-9977-5F6DA8E1C0EC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbClienteCpagarIcmsSubEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_CPAGAR_ICMS_SUB_EMP'
    TableName = 'CLIENTE_CPAGAR_ICMS_SUB_EMP'
    Cursor = 'CLIENTE_CPAGAR_ICMS_SUB_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;46001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbAcessorioEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'ACESSORIO_EMP'
    TableName = 'ACESSORIO_EMP'
    Cursor = 'ACESSORIO_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;46002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbIndustriaEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'INDUSTRIA_EMP'
    TableName = 'INDUSTRIA_EMP'
    Cursor = 'INDUSTRIA_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;46003'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteDiverso: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_DIVERSO'
    TableName = 'CLIENTE_DIVERSO'
    Cursor = 'CLIENTE_DIVERSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;46004'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFornecDescontaIcmsstEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'FORNEC_DESCONTA_ICMSST_EMP'
    TableName = 'FORNEC_DESCONTA_ICMSST_EMP'
    Cursor = 'FORNEC_DESCONTA_ICMSST_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;46005'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteIssEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_ISS_EMPRESA'
    TableName = 'CLIENTE_ISS_EMPRESA'
    Cursor = 'CLIENTE_ISS_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;46006'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteIsentoIssEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_ISENTO_ISS_EMPRESA'
    TableName = 'CLIENTE_ISENTO_ISS_EMPRESA'
    Cursor = 'CLIENTE_ISENTO_ISS_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;46007'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteIgnoraPjReterIss: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_IGNORA_PJ_RETER_ISS'
    TableName = 'CLIENTE_IGNORA_PJ_RETER_ISS'
    Cursor = 'CLIENTE_IGNORA_PJ_RETER_ISS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;46008'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFornecSubIcmsEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORNEC_SUB_ICMS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fornec Sub Icms'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'FORNEC_SUB_ICMS_EMP'
    TableName = 'FORNEC_SUB_ICMS_EMP'
    Cursor = 'FORNEC_SUB_ICMS_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;46009'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFornecIvaEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Iva'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'FORNEC_IVA_EMP'
    TableName = 'FORNEC_IVA_EMP'
    Cursor = 'FORNEC_IVA_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;460010'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteflagempresa: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_GRID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Grid'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CLIENTEFLAGEMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;460011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresas: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS'
    Cursor = 'EMPRESAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600452;460013'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
