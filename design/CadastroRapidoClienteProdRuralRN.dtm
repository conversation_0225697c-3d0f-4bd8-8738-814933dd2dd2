object CadastroRapidoClienteProdRuralRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '5300731'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbUf: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'UF'
    Cursor = 'UF'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300731;53001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
