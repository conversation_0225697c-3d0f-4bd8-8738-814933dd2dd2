object FrmClientesFlagsPgto: TFForm
  Left = 44
  Top = 162
  ActiveControl = vBoxPrincipal
  Caption = 'Selecionar condi'#231#245'es de pagamento'
  ClientHeight = 382
  ClientWidth = 1014
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '310037'
  ShortcutKeys = <>
  InterfaceRN = 'ClientesFlagsPgtoRN'
  Access = False
  ChangedProp = 
    'FrmClientesFlagsPgto.Height;'#13#10#13#10'FrmClientesFlagsPgto.ActiveContr' +
    'olFrmClientesFlagsPgto.Caption;'#13#10'FrmClientesFlagsPgto.Width;'#13#10#13#10 +
    'FrmClientesFlagsPgto.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 1014
    Height = 382
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 8
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitTop = -12
    ExplicitWidth = 964
    object FHBox4: TFHBox
      Left = 0
      Top = 0
      Width = 1000
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object hBoxLinha01: TFHBox
      Left = 0
      Top = 6
      Width = 1000
      Height = 61
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 2
      Margin.Left = 3
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox1: TFHBox
        Left = 0
        Top = 0
        Width = 5
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnVoltar: TFButton
        Left = 5
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox2: TFHBox
        Left = 70
        Top = 0
        Width = 5
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnSalvar: TFButton
        Left = 75
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Salvar'
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 3
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934
          31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2
          D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE
          6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5
          6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013
          090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF
          349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E
          8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871
          175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39
          FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622
          F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673
          BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E
          01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146
          0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219
          506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6
          0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991
          CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313
          9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0
          39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D
          AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7
          C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743
          FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE
          E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660
          474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF
          D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61
          33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E
          0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE
          F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF
          921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5
          A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E
          9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163
          8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09
          C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C
          D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E
          23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA
          FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E
          9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1
          5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A
          25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2
          FF02B065C443D9FE4B070000000049454E44AE426082}
        ImageId = 4
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox3: TFHBox
        Left = 140
        Top = 0
        Width = 5
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object FHBox5: TFHBox
      Left = 0
      Top = 68
      Width = 1000
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object hBoxLinha02: TFHBox
      Left = 0
      Top = 74
      Width = 1000
      Height = 30
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox12: TFHBox
        Left = 0
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object cboEmpresa: TFCombo
        Left = 5
        Top = 0
        Width = 200
        Height = 21
        Hint = 'Empresa'
        LookupTable = tbEmpresasFiliaisSel
        LookupKey = 'CODIGODAEMPRESA'
        LookupDesc = 'NOMEECODIGODAEMPRESA'
        Flex = False
        HelpCaption = 'Empresa'
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Empresa'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = False
        OnChange = cboEmpresaChange
        OnClearClick = cboEmpresaClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FHBox13: TFHBox
        Left = 205
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object cboDepartamento: TFCombo
        Left = 210
        Top = 0
        Width = 200
        Height = 21
        Hint = 'Departamento'
        LookupTable = tbListaEmpresasDepartamentos
        LookupKey = 'DPTO_CODIGO'
        LookupDesc = 'DPTO_DESCRICAO_CODIGO'
        Flex = False
        HelpCaption = 'Departamento'
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Departamento'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = False
        OnChange = cboDepartamentoChange
        OnClearClick = cboDepartamentoClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FHBox14: TFHBox
        Left = 410
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object cboTipoDaFormaDePagamento: TFCombo
        Left = 415
        Top = 0
        Width = 200
        Height = 21
        Hint = 'Tipo da forma de pagamento'
        LookupTable = tbListaTipoPgtoNbs
        LookupKey = 'COD_TIPO_PGTO_NBS'
        LookupDesc = 'DESC_COD_TIPO_PGTO_NBS'
        Flex = False
        HelpCaption = 'Tipo da forma de pagamento'
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Tipo da forma de pagamento'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = False
        OnChange = cboTipoDaFormaDePagamentoChange
        OnClearClick = cboTipoDaFormaDePagamentoClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FHBox15: TFHBox
        Left = 615
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object cboFormaDePagamentoExclusiva: TFCombo
        Left = 620
        Top = 0
        Width = 200
        Height = 21
        Hint = 'Forma exclusiva'
        Flex = False
        ListOptions = 'Sim=S;N'#227'o=N'
        HelpCaption = 'Forma exclusiva'
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Forma exclusiva'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = False
        OnChange = cboFormaDePagamentoExclusivaChange
        OnClearClick = cboFormaDePagamentoExclusivaClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FHBox16: TFHBox
        Left = 820
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 8
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object FHBox6: TFHBox
      Left = 0
      Top = 105
      Width = 1000
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 4
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object hBoxLinha03: TFHBox
      Left = 0
      Top = 111
      Width = 1000
      Height = 30
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 5
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox17: TFHBox
        Left = 0
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object cboFormaDePagamento: TFCombo
        Left = 5
        Top = 0
        Width = 200
        Height = 21
        Hint = 'Forma de pagamento'
        LookupTable = tbListaFormasPgto
        LookupKey = 'FORMA_CODIGO'
        LookupDesc = 'FORMA_DESCRICAO_COD'
        Flex = False
        HelpCaption = 'Forma de pagamento'
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Forma de pagamento'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = False
        OnChange = cboFormaDePagamentoChange
        OnClearClick = cboFormaDePagamentoClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FHBox18: TFHBox
        Left = 205
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object cboCondicaoDePagamentoExclusiva: TFCombo
        Left = 210
        Top = 0
        Width = 200
        Height = 21
        Hint = 'Condi'#231#227'o exclusiva'
        Flex = False
        ListOptions = 'Sim=S;N'#227'o=N'
        HelpCaption = 'Condi'#231#227'o exclusiva'
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Condi'#231#227'o exclusiva'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = False
        OnChange = cboCondicaoDePagamentoExclusivaChange
        OnClearClick = cboCondicaoDePagamentoExclusivaClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FHBox19: TFHBox
        Left = 410
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object cboCondicaoDePagamento: TFCombo
        Left = 415
        Top = 0
        Width = 200
        Height = 21
        Hint = 'Condi'#231#227'o de pagamento'
        LookupTable = tbClienteFormaPgtoDisp1
        LookupKey = 'COD_CONDICAO_PAGAMENTO'
        LookupDesc = 'NOME_COD_CONDICAO_PAGAMENTO'
        Flex = False
        HelpCaption = 'Condi'#231#227'o de pagamento'
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Condi'#231#227'o de pagamento'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = False
        OnChange = cboCondicaoDePagamentoChange
        OnClearClick = cboCondicaoDePagamentoClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FHBox20: TFHBox
        Left = 615
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnPesquisar: TFButton
        Left = 620
        Top = 0
        Width = 24
        Height = 25
        Hint = 'Pesquisar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 7
        OnClick = btnPesquisarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
          782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
          B9B0DBF5D30000000049454E44AE426082}
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'refresh'
        IconReverseDirection = False
      end
      object FHBox21: TFHBox
        Left = 644
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 8
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object FHBox7: TFHBox
      Left = 0
      Top = 142
      Width = 1000
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 6
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object FHBox8: TFHBox
      Left = 0
      Top = 148
      Width = 1000
      Height = 200
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 7
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox10: TFHBox
        Left = 0
        Top = 0
        Width = 5
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object gridPgtos: TFGrid
        Left = 5
        Top = 0
        Width = 957
        Height = 100
        Align = alClient
        TabOrder = 1
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Table = tbClienteFormaPgtoDisp
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Paging.Enabled = False
        Paging.PageSize = 0
        Paging.DbPaging = False
        FrozenColumns = 0
        ShowFooter = False
        ShowHeader = True
        MultiSelection = False
        Grouping.Enabled = False
        Grouping.Expanded = False
        Grouping.ShowFooter = False
        Crosstab.Enabled = False
        Crosstab.GroupType = cgtConcat
        EnablePopup = False
        WOwner = FrInterno
        WOrigem = EhNone
        EditionEnabled = False
        AuxColumnHeaders = <>
        ContextMenu = popGridPgtos
        NoBorder = False
        ActionButtons.BtnAccept = False
        ActionButtons.BtnView = False
        ActionButtons.BtnEdit = False
        ActionButtons.BtnDelete = False
        ActionButtons.BtnInLineEdit = False
        Columns = <
          item
            Expanded = False
            Font = <>
            Width = 33
            Visible = True
            Precision = 0
            TextAlign = taCenter
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <
              item
                Expression = 'SEL = '#39'S'#39
                Hint = 'Registro selecionado'
                EvalType = etExpression
                GUID = '{DCA388BE-4796-49AC-BB9B-3F132DF3785B}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 310010
                OnClick = 'desmarcarRegistroNaGradeGridPgtos'
                Color = clBlack
              end
              item
                Expression = 'SEL = '#39'N'#39
                Hint = 'Registro n'#227'o selecionado'
                EvalType = etExpression
                GUID = '{30119637-CF13-4CC9-9B31-FCE4426C1085}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 310011
                OnClick = 'marcarRegistroNaGradeGridPgtos'
                Color = clBlack
              end>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{B076976B-28BF-48D2-8B81-B8FB94124F34}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end
          item
            Expanded = False
            FieldName = 'NOME_COD_EMPRESA'
            Font = <>
            Title.Caption = 'Empresa'
            Width = 200
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = True
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{A14068F4-7127-4594-9F4B-05F4DDBD0B9D}'
            WOwner = FrInterno
            WOrigem = EhNone
            Hint = 'Empresa'
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end
          item
            Expanded = False
            FieldName = 'NOME_COD_DEPARTAMENTO'
            Font = <>
            Title.Caption = 'Departamento'
            Width = 160
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = True
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{EA1B014F-FE71-4417-AF2A-52ADD18ACA18}'
            WOwner = FrInterno
            WOrigem = EhNone
            Hint = 'Departamento'
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end
          item
            Expanded = False
            FieldName = 'NOME_COD_FORMA_PGTO'
            Font = <>
            Title.Caption = 'Forma de pagamento'
            Width = 200
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = True
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{21BC5F69-7A9A-46F5-A088-F22E40E2C0AE}'
            WOwner = FrInterno
            WOrigem = EhNone
            Hint = 'Forma de pagamento'
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end
          item
            Expanded = False
            FieldName = 'NOME_COD_CONDICAO_PAGAMENTO'
            Font = <>
            Title.Caption = 'Condi'#231#227'o de pagamento'
            Width = 200
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = True
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{98AE9821-7099-47EF-A1C1-4AA380D09914}'
            WOwner = FrInterno
            WOrigem = EhNone
            Hint = 'Condi'#231#227'o de pagamento'
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end
          item
            Expanded = False
            FieldName = 'CONDICAO_PAGAMENTO_EXCLUSIVA'
            Font = <>
            Title.Caption = 'Condi'#231#227'o exclusiva'
            Width = 120
            Visible = True
            Precision = 0
            TextAlign = taCenter
            FieldType = ftString
            FlexRatio = 0
            Sort = True
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <
              item
                Expression = 'CONDICAO_PAGAMENTO_EXCLUSIVA = '#39'S'#39
                Hint = 'Condi'#231#227'o de pagamento exclusiva'
                EvalType = etExpression
                GUID = '{DE80C38F-A21C-4F3A-8F38-898D0780F6BB}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 4300107
                Color = clBlack
              end
              item
                Expression = 'CONDICAO_PAGAMENTO_EXCLUSIVA = '#39'N'#39
                Hint = 'Condi'#231#227'o de pagamento n'#227'o exclusiva'
                EvalType = etExpression
                GUID = '{4B40317C-1C1A-4B97-B60A-7371858DD856}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 4300106
                Color = clBlack
              end>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = False
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{A8E28882-F6D5-4D38-AC42-381542F02E72}'
            WOwner = FrInterno
            WOrigem = EhNone
            Hint = 'Condi'#231#227'o exclusiva'
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end
          item
            Expanded = False
            FieldName = 'NOME_COD_TIPO_FORMA_PGTO'
            Font = <>
            Title.Caption = 'Tipo da forma de pagamento'
            Width = 120
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = True
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{E61DF251-0AF1-4561-B365-7BAD1EF21699}'
            WOwner = FrInterno
            WOrigem = EhNone
            Hint = 'Tipo da forma de pagamento'
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end
          item
            Expanded = False
            FieldName = 'FORMA_PGTO_EXCLUSIVA'
            Font = <>
            Title.Caption = 'Forma exclusiva'
            Width = 110
            Visible = True
            Precision = 0
            TextAlign = taCenter
            FieldType = ftString
            FlexRatio = 0
            Sort = True
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <
              item
                Expression = 'FORMA_PGTO_EXCLUSIVA = '#39'S'#39
                Hint = 'Forma de pagamento exclusiva'
                EvalType = etExpression
                GUID = '{31697A81-0F97-4B57-B88E-1A0A126D01C3}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 4300107
                Color = clBlack
              end
              item
                Expression = 'FORMA_PGTO_EXCLUSIVA = '#39'N'#39
                Hint = 'Forma de pagamento n'#227'o exclusiva'
                EvalType = etExpression
                GUID = '{4D627BDA-B387-4455-9EED-B8DAA4209BD7}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 4300106
                Color = clBlack
              end>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = False
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{62144B7E-7D7D-43CA-BE0D-C37BD2B53470}'
            WOwner = FrInterno
            WOrigem = EhNone
            Hint = 'Forma exclusiva'
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end>
      end
      object FHBox11: TFHBox
        Left = 962
        Top = 0
        Width = 5
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object FHBox9: TFHBox
      Left = 0
      Top = 349
      Width = 1000
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 8
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
  end
  object popGridPgtos: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    object mmSelecionarTodosOsRegistrosDaGradeGridPgtos: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar todos os registros'
      Hint = 'Selecionar todos os registros'
      OnClick = mmSelecionarTodosOsRegistrosDaGradeGridPgtosClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{31CEE72A-DBB3-4174-9E77-C3FD6BDC4DD1}'
    end
    object mmSelecionarNenhumRegistroDaGradeGridPgtos: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar nenhum registro'
      Hint = 'Selecionar nenhum registro'
      OnClick = mmSelecionarNenhumRegistroDaGradeGridPgtosClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{BB07103A-0BDB-4610-BD83-595F05180117}'
    end
  end
  object tbClienteFormaPgtoDisp: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONDICAO_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_TIPO_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Tipo Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_PGTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Pagamento Exclusiva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONDICAO_PAGAMENTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Condi'#231#227'o Pagamento Exclusiva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_FORMAS_PGTO'
    Cursor = 'CLIENTE_FORMA_PGTO_DISP'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresas: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS'
    Cursor = 'EMPRESAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;31002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasDepartamentos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_DEPARTAMENTOS'
    Cursor = 'EMPRESAS_DEPARTAMENTOS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;31003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFormaPgto: TFTable
    FieldDefs = <
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'FORMA_PGTO'
    Cursor = 'FORMA_PGTO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;31004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasFiliaisSel: TFTable
    FieldDefs = <
      item
        Name = 'CODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOMEECODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nomeecodigodaempresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESA'
    Cursor = 'EMPRESAS_FILIAIS_SEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaEmpresasDepartamentos: TFTable
    FieldDefs = <
      item
        Name = 'DPTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dpto Descri'#231#227'o Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DPTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_EMPRESAS_DEPARTAMENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaTipoPgtoNbs: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_PGTO_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Pagamento Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_COD_TIPO_PGTO_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto C'#243'd. Tipo Pagamento Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TIPO_PGTO_NBS'
    Cursor = 'LISTA_TIPO_PGTO_NBS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaFormasPgto: TFTable
    FieldDefs = <
      item
        Name = 'FORMA_DESCRICAO_COD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Descri'#231#227'o C'#243'd.'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_FORMAS_PGTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteFormaPgtoDisp1: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_FORMAS_PGTO'
    Cursor = 'CLIENTE_FORMA_PGTO_DISP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
