object MotivoPerdasRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '340036'
  Left = 321
  Top = 158
  Height = 299
  Width = 442
  object scCrmDescartes: TFSchema
    Tables = <
      item
        Table = tbDescartes
        GUID = '{CC50985B-078A-4C78-A478-0D26DE5CD479}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbDescartes: TFTable
    FieldDefs = <
      item
        Name = 'COD_DESCARTE'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Descarte'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_DESCARTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ELIMINAR_CICLO_FUTURO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Excluir eventos de ciclo '#224' frente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANCELOU_VENDA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cancelou Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NIVEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nivel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRM_PLUS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Crm Plus'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRM_PLUS_TIPO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Crm Plus Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INATIVACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inativa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AUTOMATICO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Automatico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USAR_NO_GOLD'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usar No Gold'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USAR_NO_CRMPARTS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usar No CRMParts'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXCLUSIVO_DESCARTE_AUTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descarte autom'#225'tico (Job)'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_DESCARTES'
    TableName = 'CRM_DESCARTES'
    Cursor = 'CRM_DESCARTES'
    MaxRowCount = 400
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340036;34001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDescarteCheck: TFTable
    FieldDefs = <
      item
        Name = 'TOTAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Total'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRM_DESCARTE_CHECK'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340036;34002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
