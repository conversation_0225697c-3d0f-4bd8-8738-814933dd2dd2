object PerfilRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000145'
  Left = 44
  Top = 162
  Height = 299
  Width = 442
  object tbPerfil: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Divis'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO_ICONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto Icone'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAB_PRECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tab Pre'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Segmento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Segmento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RAMAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ramal'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PERFIL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;70001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbUsuarioFoto
        GUID = '{6AF804D5-B496-4D96-A5E5-CB05305339C9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbUsuarioFoto: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO_ICONE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto Icone'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'USUARIO_FOTO'
    TableName = 'USUARIO_FOTO'
    Cursor = 'USUARIO_FOTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;70002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSetorVenda: TFTable
    FieldDefs = <
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SETOR_VENDA'
    Cursor = 'SETOR_VENDA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_LOCAL_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Local Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_USUARIOS'
    Cursor = 'EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLocalEstoque: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_LOCAL_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Local Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DO_LOCAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Do Local'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Principal'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFERENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Preferencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'APLICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aplica'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'LOCAL_ESTOQUE'
    Cursor = 'LOCAL_ESTOQUE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;53003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProdutoSegmento: TFTable
    FieldDefs = <
      item
        Name = 'COD_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Segmento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Segmento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'PRODUTO_SEGMENTO'
    Cursor = 'PRODUTO_SEGMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000145;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
