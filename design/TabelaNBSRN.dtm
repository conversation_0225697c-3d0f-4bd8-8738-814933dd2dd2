object TabelaNBSRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600204'
  Height = 299
  Width = 442
  object scTbNbs: TFSchema
    Tables = <
      item
        Table = tbNbs
        GUID = '{26CF792A-1EF0-4703-A97E-67F0E2C0BCDB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbNbs: TFTable
    FieldDefs = <
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'TB_NBS'
    MaxRowCount = 1000
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600204;46001'
    DeltaMode = dmAll
    RatioBatchSize = 20
  end
end
