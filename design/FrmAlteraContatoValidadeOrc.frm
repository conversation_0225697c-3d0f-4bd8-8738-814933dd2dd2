object FrmAlteraContatoValidadeOrc: TFForm
  Left = 44
  Top = 162
  ActiveControl = vBoxForm
  Caption = 'Remarcar Contato'
  ClientHeight = 295
  ClientWidth = 364
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600381'
  ShortcutKeys = <>
  InterfaceRN = 'AlteraContatoValidadeOrcRN'
  Access = False
  ChangedProp = 
    'FrmAlteraContatoValidadeOrc.ActiveControlFrmAlteraContatoValidad' +
    'eOrc.Width;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxForm: TFVBox
    Left = 0
    Top = 0
    Width = 364
    Height = 295
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hbTopButtons: TFHBox
      Left = 0
      Top = 0
      Width = 425
      Height = 67
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 3
      Padding.Left = 3
      Padding.Right = 3
      Padding.Bottom = 3
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnSair: TFButton
        Left = 0
        Top = 0
        Width = 65
        Height = 61
        Hint = 'Sair'
        Align = alLeft
        Caption = 'Sair'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnSairClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D494844520000001E0000002408060000009ED18A
          69000001C74944415478DAED964D2B44511880E736313433A4F191AF8D7C3449
          63A344D1105176CA4E4AD9B350ACFC01C91F9805C90AA10819AC64A1EC247676
          16B2411A99EB39395337C69D73C7BD83BAA79EDE9973DEF33EE77E9EABE9BAEE
          F98DA6B96257EC8A45634E48D3B4FB9C89C9CD27ACC230AC201F755C4C9E8FB0
          0643B22B81D8E7A8989C02C2060C1ABA9388BD8E89192F246C42FF97C93447C4
          8CF909DBD0F34D8A1777D25631FD01C20E7499CCDF820B38803316A17CA7A615
          D35744D8854E0B0771030B106301AF96C5FC2F26EC417B36A790760963C8CF95
          C5FC2E21EC435B96D2544BC004F26555B1B8567D3F94A69AB8E9C6912FA9885B
          098710B2492E8E3C8AFCD4542CE511292FB5497E0D11E42FA662296F21C4A1CC
          26F914E2C58C62296F96F20A1BC4B75087FC2DA358412E4E9D78E4C4E3570E0D
          D001DD104C93DF8BF848492CE56129AFFC34F44C217F9A7CF16E1F815968320C
          CD933FAD2C96C54401B1DA2A43F71385022673F20833300762173B213F6A492C
          0B354A79B5EC7AA4505061DE80E7631F7F20BFD6B25816A9271C430D5C5128AC
          384F6CA9EBC6855AFEE6225F5C6B71CAE214BAB3306FD2F848E5EC2B138F66DC
          36FFD7E7AD2B76C57F5AFC0E2AEAFAB92FC4B7AF0000000049454E44AE426082}
        ImageId = 430032
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FVBox2: TFVBox
        Left = 65
        Top = 0
        Width = 5
        Height = 48
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnSalvar: TFButton
        Left = 70
        Top = 0
        Width = 62
        Height = 61
        Hint = 'Salvar Altera'#231#245'es'
        Align = alLeft
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001374944415478DA6364A031608431FEFFFF2F09A4F449D4FF1D88CF33
          32327EC26B01D4F00740CC468623419654002D9984CF020F20B51D88D700F141
          206E00626134B5CB80F838946D09C451503D1A40AC08C48D404B1A0859900B54
          3405C8BF03642BA3A9F504CAED40570FC4AB81783F106B62B384620BA0EAC571
          5942150BA062C896E4C3E2846A16205972028841B43850EE332916608B64140B
          A0665500A976207600CA1DC465C152A80184800550FD49340B7280D464988F71
          59C002645B0331271EC36F03D5DE451724CA02225C8E130C0E0B880C2264002A
          2E8E02F5FE21D602622319192C03EA8D26D60250321560809449C40090BA0F40
          BD2AA458C000D24064B8C3D58F5A40B105C8150E030369910CA3ED813804DD02
          4AAA4C6CE017102B002D784E69A58F0B5C04190E0E362A1988130000D0053128
          1A34B73E0000000049454E44AE426082}
        ImageId = 310032
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object gridPanelRemarcarEvento: TFGridPanel
      Left = 0
      Top = 68
      Width = 355
      Height = 219
      Align = alClient
      Caption = 'gridPanelRemarcarEvento'
      ColumnCollection = <
        item
          SizeStyle = ssAbsolute
          Value = 150.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAbsolute
          Value = 140.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      ControlCollection = <
        item
          Column = 0
          Control = lblTitDataContatoValid
          Row = 0
        end
        item
          Column = 1
          Control = FLabel2
          Row = 0
        end
        item
          Column = 0
          Control = edtDataNovoContato
          Row = 1
        end
        item
          Column = 0
          Control = lblObservacao
          Row = 2
        end
        item
          Column = 0
          ColumnSpan = 3
          Control = memObservacao
          Row = 3
        end
        item
          Column = 1
          Control = edtHorario
          Row = 1
        end>
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      RowCollection = <
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      AllRowFlex = False
      WOwner = FrInterno
      WOrigem = EhNone
      ColumnTabOrder = False
      object lblTitDataContatoValid: TFLabel
        Left = 1
        Top = 1
        Width = 93
        Height = 24
        Align = alLeft
        Caption = 'Data Novo Contato'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 13
      end
      object FLabel2: TFLabel
        Left = 151
        Top = 1
        Width = 35
        Height = 24
        Align = alLeft
        Caption = 'Hor'#225'rio'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 13
      end
      object edtDataNovoContato: TFDate
        Left = 1
        Top = 25
        Width = 121
        Height = 24
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        Format = 'dd/MM/yyyy'
        ShowCheckBox = False
        Align = alLeft
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
      end
      object lblObservacao: TFLabel
        Left = 1
        Top = 49
        Width = 58
        Height = 20
        Align = alLeft
        Caption = 'Observa'#231#227'o'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 13
      end
      object memObservacao: TFMemo
        Left = 1
        Top = 69
        Width = 353
        Height = 149
        Align = alClient
        CharCase = ccNormal
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Lines.Strings = (
          'memObservacao')
        Maxlength = 0
        ParentFont = False
        TabOrder = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        WOwner = FrInterno
        WOrigem = EhNone
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
      end
      object edtHorario: TFTime
        Left = 151
        Top = 25
        Width = 121
        Height = 24
        TabOrder = 0
        AccessLevel = 0
        Flex = False
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = False
        Constraint.Enabled = False
        Constraint.FormCheck = True
        Format = 'HH:mm'
        Align = alLeft
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        ExplicitLeft = 178
        ExplicitTop = 34
      end
    end
  end
  object tbOs: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PROX_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Prox Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Validade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS'
    Cursor = 'OS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600381;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
