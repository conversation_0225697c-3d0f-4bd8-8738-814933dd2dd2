object GerenciaDisparoLeadZapDetailRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600426'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbQueryDisparoLzDetailGrid: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{6253BC0B-6529-4777-8D52-727C55DAF297}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Tipo Evento'
        GUID = '{47D67A5D-E6DB-4723-8A77-74C13CDB7B64}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato'
        GUID = '{F7A7479E-0490-4E5C-946B-E9E260D3CCDA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{84103433-256E-4223-A1C2-45E5232D1761}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        GUID = '{363EF272-1AA1-494C-A49E-09E288EFCBF7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_LEADZAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Leadzap'
        GUID = '{A4DB4692-2AE6-4B2C-B5CD-6767A29A2C51}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'QUERY_DISPARO_LZ_DETAIL_GRID'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600426;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEventos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{B44DF8FC-05FA-43BC-9D33-653CE98F7E03}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        GUID = '{EF0D93B4-05D2-469A-A94D-D20A039BF903}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EVENTOS'
    Cursor = 'CRM_EVENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600426;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
