object CrmQuestionarioPerguntaRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '45205'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbPerguntas: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        GUID = '{856B55BC-E3A2-4E3A-9379-7FC4A4B8A52C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pergunta'
        GUID = '{02050999-97DF-4C8A-B3A3-FBE3A132CA5D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MULTIPLA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Multipla'
        GUID = '{A4FDB7CD-EAB0-4DB4-AD4E-DE22A0D276CD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta'
        GUID = '{69A6B675-23C9-4A5A-A7CD-C64477DE9E7E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativa'
        GUID = '{161E1BBF-F1C3-4542-88B2-843E05E4161E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Obrigatorio'
        GUID = '{38ED4EB2-4F15-456F-8088-BA917607FDC1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'
        GUID = '{53E8CD35-AC70-4E81-834F-C92C3B5E2796}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESULTADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Resultado'
        GUID = '{78A89B38-B53C-49F3-A597-9C8083227098}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_DEPENDENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Dependente'
        GUID = '{0A5FD468-FC25-4234-B011-5D1B7FF3F721}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_RESP_ESPEC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Resp Espec'
        GUID = '{49BF4F7B-FD59-4388-BBC0-F997BC811387}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_QTDE_RESP_ESPEC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Quantidade Resp Espec'
        GUID = '{43320AD0-24EA-4489-BC2F-A8211868B67D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_DEPENDENTE_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Dependente Pergunta'
        GUID = '{98178F1F-4B45-4209-89D5-0BD54B931351}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_DEPENDENTE_RESPOSTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Dependente Resposta'
        GUID = '{BE890C5F-21AE-410B-BECA-DFA113535B3F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VIGENCIA_INICIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Vigencia Inicial'
        GUID = '{0225795D-A137-4171-88C2-2EFB29B6BE62}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VIGENCIA_FINAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Vigencia Final'
        GUID = '{C2616F0A-5DB7-48AF-B5D4-0CDC13431D95}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        GUID = '{583DB1AA-11BD-40B2-9E33-39094756D91F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AVISO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aviso'
        GUID = '{9F8CBD97-C443-4A3A-A226-4F96921DC006}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESUMO_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Resumo Pergunta'
        GUID = '{E2AC9A8A-35D9-47A9-8653-784617D671ED}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MYHONDA_SEQ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Myhonda Seq'
        GUID = '{3889F57F-C345-45AF-A029-27161C3130C8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXIBE_RECLAMACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Exibe Reclama'#231#227'o'
        GUID = '{E11C52C9-7ACD-44A9-9846-EEA12422422C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_RESUMIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Resumida'
        GUID = '{E2134A65-FF76-4FEE-BB2B-A491D178A014}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_PERGUNTAS'
    Cursor = 'CRM_PERGUNTAS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTipoPerguntas: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Pergunta'
        GUID = '{5A2A5EC7-6D40-4AA2-AE7F-3FF7E011F3A9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Tipo'
        GUID = '{A2726748-B2A0-4A1A-A5CB-D76C138563D0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO_NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo Nome'
        GUID = '{11B8CA2D-5718-4A37-BC11-7B8BCB6E21E5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIPO_PERGUNTAS'
    Cursor = 'CRM_TIPO_PERGUNTAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbQuestionario: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        GUID = '{CA976E18-7E6A-4E9D-8D2D-E131F1984ADD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Questionario'
        GUID = '{FE4C695D-6C49-4FC5-B418-DB7F838A2B2F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_DESCRICAO_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Descri'#231#227'o Questionario'
        GUID = '{A2DA5B7F-6E86-4A7D-B1D1-F007D9883F7D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_QUESTIONARIO'
    Cursor = 'CRM_QUESTIONARIO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45203'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOpcoes: TFTable
    FieldDefs = <
      item
        Name = 'COD_OPCOES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Opc'#245'es'
        GUID = '{CB8A701C-C792-4572-A470-A9D98CA610F4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OPCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Op'#231#227'o'
        GUID = '{A31299AA-0ADB-464B-94EC-7612DE75A36C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INITCAP_OPCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Initcap Op'#231#227'o'
        GUID = '{BCE39D7A-F728-4C54-BAD2-BC6184BF70E8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_OPCOES'
    Cursor = 'CRM_OPCOES'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45205'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRespostasOpcoes: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        GUID = '{4DE9EE11-B4A1-4CA9-BB97-F336FDF669DE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OPCOES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Opc'#245'es'
        GUID = '{19ABFBE4-7FBD-4368-AD98-5C1BB20E7454}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pergunta'
        GUID = '{0AE2B2E5-0B81-4A0E-985C-5AA9E5C1A218}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MULTIPLA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Multipla'
        GUID = '{880EEA0C-AE06-4F09-99F0-49D8B83A3ADF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'
        GUID = '{586F78D0-E926-40CF-AB56-CA4B217BDACE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INITCAP_OPCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Initcap Op'#231#227'o'
        GUID = '{F68B9E09-48C2-43E9-9650-5DA90D6E7CE4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        GUID = '{173B1A9F-0B6E-4D04-842A-B8CC642A8915}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    MasterFields = 'COD_QUESTIONARIO; COD_PERGUNTA; MULTIPLA'
    DetailFilters = 'COD_QUESTIONARIO; COD_PERGUNTA; MULTIPLA'
    TableName = 'CRM_RESPOSTAS_OPCOES'
    Cursor = 'CRM_RESPOSTAS_OPCOES'
    MaxRowCount = 0
    MasterTable = tbPerguntas
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45206'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbQuestionarioCadastro: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        GUID = '{10800FC2-CE4A-4613-BDB6-10F84759BE7B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Questionario'
        GUID = '{BC28778C-EFD0-4AE3-BFEC-04E39F9AD056}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_DESCRICAO_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Descri'#231#227'o Questionario'
        GUID = '{D1C60A1D-5A2B-434F-9E7B-A2F07C19B257}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_QUESTIONARIO'
    Cursor = 'CRM_QUESTIONARIO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45207'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTipoEventoRac: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{EB25A1E2-4BEA-4A81-B31F-5074A2AB8EF1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        GUID = '{B121462A-6A3E-4104-9FC5-EA55921D160D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRM_TIPO_EVENTO_RAC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;49401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
