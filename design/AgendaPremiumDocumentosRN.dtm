object AgendaPremiumDocumentosRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '489015'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbAgendaDoc: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{8CD3DC92-5E33-4554-BD8D-FC23EA348E0B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OS_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Os Agenda'
        GUID = '{*************-4120-ABB0-C11621202020}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia'
        GUID = '{FEDED7D3-5BD8-40CA-826B-4FA980A41951}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Arquivo'
        GUID = '{1341F7FB-3E3F-4814-9FBB-A80998D66742}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXTENSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Extens'#227'o'
        GUID = '{1317FDE5-E1CD-4115-9F5E-B95F4526B6D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{B2C0D25C-AB31-4390-B64F-1CD4A604AD7A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Arquivo'
        GUID = '{78E4D600-03CD-46D7-893F-50F4ED16452D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIRETORIO_ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Diretorio Arquivo'
        GUID = '{D1C1971F-2C33-4ABF-B6CF-54ECE907C314}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_AGENDA_DOC'
    Cursor = 'OS_AGENDA_DOC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '489015;48901'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
