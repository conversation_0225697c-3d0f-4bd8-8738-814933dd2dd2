object RequisitarMaisFiltrosPecasRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '103015'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbItensGrupoInterno: TFTable
    FieldDefs = <
      item
        Name = 'COD_GRUPO_INTERNO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Interno'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_GRUPO_INTERNO'
    Cursor = 'ITENS_GRUPO_INTERNO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '103015;10301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensSubGrupo: TFTable
    FieldDefs = <
      item
        Name = 'COD_GRUPO_INTERNO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Interno'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SUB_GRUPO_INTERNO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Grupo Interno'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_SUB_GRUPO'
    Cursor = 'ITENS_SUB_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '103015;10302'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDadosRequisicao: TFTable
    FieldDefs = <>
    Cursor = 'OS_DADOS_REQUISICAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '103015;10303'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
