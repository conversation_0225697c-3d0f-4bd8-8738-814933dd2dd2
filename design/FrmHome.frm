object FrmHome: TFForm
  Left = 44
  Top = 163
  ActiveControl = borderPanel
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Form Home'
  ClientHeight = 494
  ClientWidth = 1248
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Menu = mnuPrincipal
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '29002'
  ShortcutKeys = <
    item
      Modifier = smCtrl
      Key = sk0
      OnKeyAction = 'pesquisarItensEmBranco'
      WOwner = FrNone
      WOrigem = EhNone
    end>
  InterfaceRN = 'HomeRN'
  Access = False
  ChangedProp = 
    'FrmHome.Width;'#13#10'FrmHome.Spacing;'#13#10'FrmHome_1.Touch.InteractiveGes' +
    'tures;'#13#10'FrmHome_1.Touch.InteractiveGestureOptions;'#13#10'FrmHome_1.To' +
    'uch.ParentTabletOptions;'#13#10'FrmHome_1.Touch.TabletOptions;'#13#10'FrmHom' +
    'e.Height;'#13#10'FrmHome.ActiveControlFrmHome.Width;'#13#10'FrmHome.ActiveCo' +
    'ntrol;'#13#10
  Spacing = 2
  DesignSize = (
    1248
    494)
  PixelsPerInch = 96
  TextHeight = 13
  object borderPanel: TFBorderPanel
    Left = 0
    Top = 0
    Width = 1248
    Height = 494
    North = pnlTop
    Center = pnlCenter
    West = pnlWest
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    NorthCollapsible = False
    NorthSplittable = False
    NorthOpen = True
    SouthOpen = True
    EastOpen = True
    WestOpen = True
    SouthCollapsible = False
    SouthSplittable = False
    EastCollapsible = True
    EastSplittable = True
    WestCollapsible = True
    WestSplittable = True
    WOwner = FrInterno
    WOrigem = EhNone
    NorthSizePercent = 0
    SouthSizePercent = 0
    EastSizePercent = 0
    WestSizePercent = 0
    Align = alClient
    object pnlTop: TFVBox
      Left = 0
      Top = 0
      Width = 1244
      Height = 73
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stBoxShadow
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 1
      Margin.Right = 1
      Margin.Bottom = 1
      Spacing = 0
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 5
      BoxShadowConfig.VerticalLength = 0
      BoxShadowConfig.BlurRadius = 30
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 8
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox2: TFHBox
        Left = 0
        Top = 0
        Width = 1241
        Height = 67
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        ParentBackground = False
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox2: TFVBox
          Left = 0
          Top = 0
          Width = 258
          Height = 62
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stBoxShadow
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 5
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 5
          BoxShadowConfig.VerticalLength = 5
          BoxShadowConfig.BlurRadius = 64
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 8
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox10: TFHBox
            Left = 0
            Top = 0
            Width = 253
            Height = 57
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 10
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object imgLogo: TFImage
              Left = 0
              Top = 0
              Width = 250
              Height = 50
              Align = alRight
              Stretch = True
              OnClick = imgLogoClick
              ImageSrc = '/images/crmparts7000256.jpg'
              WOwner = FrInterno
              WOrigem = EhNone
              BoxSize = 0
              GrayScaleOnDisable = False
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              Preview = False
              ImageId = 0
            end
          end
        end
        object FHBox4: TFHBox
          Left = 258
          Top = 0
          Width = 987
          Height = 62
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          ParentBackground = False
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 10
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox3: TFVBox
            Left = 0
            Top = 0
            Width = 864
            Height = 58
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hBoxPesquisas: TFHBox
              Left = 0
              Top = 0
              Width = 857
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 5
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblUsuarioLogadoTopoEsquerdo: TFLabel
                Left = 0
                Top = 0
                Width = 150
                Height = 13
                Caption = 'lblUsuarioLogadoTopoEsquerdo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clSilver
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object hBoxFiltros: TFHBox
              Left = 0
              Top = 21
              Width = 857
              Height = 35
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hBoxFiltrosEsquerdos: TFHBox
                Left = 0
                Top = 0
                Width = 428
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  424
                  26)
                object FHBox6: TFHBox
                  Left = 0
                  Top = 0
                  Width = 10
                  Height = 20
                  Anchors = []
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object imgBuscar: TFImage
                  Left = 10
                  Top = 0
                  Width = 24
                  Height = 24
                  Anchors = []
                  Stretch = False
                  ImageSrc = '/images/700089.png'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxSize = 0
                  GrayScaleOnDisable = False
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  Preview = False
                  ImageId = 0
                end
                object FHBox9: TFHBox
                  Left = 34
                  Top = 0
                  Width = 10
                  Height = 20
                  Anchors = []
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object edtBuscar: TFString
                  Left = 44
                  Top = 0
                  Width = 250
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Buscar Por...'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccNormal
                  Pwd = False
                  Maxlength = 0
                  Align = alLeft
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  OnEnter = edtBuscarEnter
                  OnChanging = edtBuscarChanging
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
                object FHBox5: TFHBox
                  Left = 294
                  Top = 0
                  Width = 10
                  Height = 20
                  Anchors = []
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 3
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object btnSearch: TFButton
                  Left = 304
                  Top = 0
                  Width = 50
                  Height = 26
                  Align = alLeft
                  Caption = 'Buscar'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 4
                  Visible = False
                  OnClick = btnSearchClick
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
                object btnPesquisaAvancadaCliente: TFButton
                  Left = 354
                  Top = 0
                  Width = 20
                  Height = 20
                  Hint = 'Pesquisa avan'#231'ada'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 5
                  OnClick = btnPesquisaAvancadaClienteClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                    61000002214944415478DAA5925F48536118C69F51172B697AD56CDA70B1BC50
                    1063179A88D04DD24DE29D6012836A6511678254504689EE30447735DC09647A
                    E1AC067911BA19B855068D38E08D15E3B04D9B7367FE3B13644AE0E93B67389D
                    3B46D003E7E6BCDFF37B9FF7FD3E954884FF902AE80B8AAAA23D149DD2603BB3
                    85CDED2D60275BE4791E2BAB2B482C25108E86119809140222918848D334DA3B
                    DAD0D47845FE994EA7C1711CE25C1C53B353B279C0360093C95408904648914E
                    CF7A7A607F6947B1B6188B7C1A6B710E0CC3C8E611D7080C0683F208FB3B8846
                    A3181B1F03F580422C2920F4D10F8FD783570E1AEA9361ECED08B241CC6C4048
                    02E16406A515F50700492CCBCA9F14D566B7A1F35627AA74AB387DBE1C1A8D3A
                    7B68D1855F5C0256460DEF6B6F3E4092C5629101D3EFA6E174D9F15BF0415F73
                    9D54D6C856DF0331379E8C36C0FA7C1067B55A6580246976CFD05D94561BB385
                    DD38304FE1C5642DEE51C3B2396F078701A9A5144A4EC4D03BD885F2CAA66CF7
                    85A760BEE849BA3B79B7A10890BA53EDB568B979E3A0202510E6C91B5986F9FE
                    37F43BDFE09CFE6221605F6F5D8FB1B12962995FC7199D0EDDDD2D720AF3A364
                    CEAC98E0A8589621919BC9F6FBE0747CC0D5DB133056D51D3FC2612D8426505D
                    77996CDF0D27EDC6B587B3050FEA5800F73D0463D94F32B340CC0EB45A6772B1
                    FF09F0D93F891A9811FC5A824B1D7EE82F54FEFD291FD5A7B9007ECCF9D06AEE
                    CADDB992FE00D4950D0CC656D00E0000000049454E44AE426082}
                  ImageId = 5300469
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clWindow
                  Access = False
                  IconReverseDirection = False
                end
                object FHBox14: TFHBox
                  Left = 374
                  Top = 0
                  Width = 10
                  Height = 20
                  Anchors = []
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 6
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
              object hBoxFiltrosDireitos: TFHBox
                Left = 428
                Top = 0
                Width = 428
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  424
                  26)
                object FHBox8: TFHBox
                  Left = 0
                  Top = 0
                  Width = 10
                  Height = 20
                  Anchors = []
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object imgBuscarItem: TFImage
                  Left = 10
                  Top = 0
                  Width = 24
                  Height = 24
                  Anchors = []
                  Stretch = False
                  Visible = False
                  ImageSrc = '/images/700089.png'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxSize = 0
                  GrayScaleOnDisable = False
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  Preview = False
                  ImageId = 0
                end
                object FHBox11: TFHBox
                  Left = 34
                  Top = 0
                  Width = 10
                  Height = 20
                  Anchors = []
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object edtBuscarCodigo: TFString
                  Left = 44
                  Top = 0
                  Width = 250
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Buscar por C'#243'digo/Descri'#231#227'o Item'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccNormal
                  Pwd = False
                  Maxlength = 0
                  Align = alLeft
                  Visible = False
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  OnEnter = edtBuscarCodigoEnter
                  OnChanging = edtBuscarCodigoChanging
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
                object FHBox12: TFHBox
                  Left = 294
                  Top = 0
                  Width = 10
                  Height = 20
                  Anchors = []
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 3
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object btnSearchCodigo: TFButton
                  Left = 304
                  Top = 0
                  Width = 50
                  Height = 26
                  Align = alLeft
                  Caption = 'Buscar'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 4
                  Visible = False
                  OnClick = btnSearchCodigoClick
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
                object FHBox13: TFHBox
                  Left = 354
                  Top = 0
                  Width = 10
                  Height = 20
                  Anchors = []
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 5
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
          end
          object FVBox1: TFVBox
            Left = 864
            Top = 0
            Width = 117
            Height = 61
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hBoxVersaoSistemaTopoDireito: TFHBox
              Left = 0
              Top = 0
              Width = 108
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 5
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox7: TFHBox
                Left = 0
                Top = 0
                Width = 5
                Height = 10
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblVersaoSistemaTopoDireito: TFLabel
                Left = 5
                Top = 0
                Width = 135
                Height = 13
                Caption = 'lblVersaoSistemaTopoDireito'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clSilver
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                OnClick = lblVersaoSistemaTopoDireitoClick
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FHBox1: TFHBox
              Left = 0
              Top = 21
              Width = 110
              Height = 34
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              DesignSize = (
                106
                30)
              object vBoxPesqOrcItem: TFVBox
                Left = 0
                Top = 0
                Width = 37
                Height = 29
                Align = alLeft
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 6
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Visible = False
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  33
                  25)
                object iconItensOrc: TFIconClass
                  Left = 0
                  Top = 0
                  Width = 16
                  Height = 16
                  Hint = 'Pesquisar todos Or'#231'amentos que cont'#233'm um determinado item.'
                  Anchors = []
                  Picture.Data = {
                    07544269746D6170C60A0000424DC60A00000000000036000000280000001A00
                    00001A0000000100200000000000900A00000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F00067676700676767006767670067676700676767006767
                    6700676767006767670067676700676767006767670067676700676767006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700676767006767
                    6700676767006767670067676700676767006767670067676700676767006767
                    6700676767006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                    6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                    6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                    6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                    6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F00067676700676767006767670067676700676767006767
                    6700676767006767670067676700676767006767670067676700676767006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700676767006767
                    6700676767006767670067676700676767006767670067676700676767006767
                    6700676767006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000}
                  OnClick = iconItensOrcClick
                  IconClass = 'shopping-cart'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 26
                  Color = 6776679
                end
              end
              object vBoxNovoImage: TFVBox
                Left = 37
                Top = 0
                Width = 37
                Height = 29
                Align = alLeft
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 6
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Visible = False
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                DesignSize = (
                  33
                  25)
                object iconClassNovo: TFIconClass
                  Left = 0
                  Top = 0
                  Width = 16
                  Height = 16
                  Hint = 'Novo'
                  Anchors = []
                  Picture.Data = {
                    07544269746D6170C60A0000424DC60A00000000000036000000280000001A00
                    00001A0000000100200000000000900A00000000000000000000000000000000
                    0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F00067676700676767006767670067676700676767006767
                    6700676767006767670067676700676767006767670067676700676767006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700676767006767
                    6700676767006767670067676700676767006767670067676700676767006767
                    6700676767006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                    6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                    6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                    6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                    6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F0006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F00067676700676767006767670067676700676767006767
                    6700676767006767670067676700676767006767670067676700676767006767
                    670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700676767006767
                    6700676767006767670067676700676767006767670067676700676767006767
                    6700676767006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                    F000F0F0F000F0F0F000F0F0F000F0F0F000}
                  OnClick = iconClassNovoClick
                  IconClass = 'user-plus'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Size = 26
                  Color = 6776679
                end
              end
              object imageUsuario: TFImage
                Left = 74
                Top = 0
                Width = 30
                Height = 30
                Hint = 'Usu'#225'rio'
                Anchors = []
                Stretch = False
                Visible = False
                OnClick = imageUsuarioClick
                ImageSrc = '/images/700090.png'
                WOwner = FrInterno
                WOrigem = EhNone
                BoxSize = 0
                GrayScaleOnDisable = False
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                Preview = False
                ImageId = 0
              end
            end
          end
        end
      end
    end
    object pnlCenter: TFVBox
      Left = 262
      Top = 76
      Width = 572
      Height = 389
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 1
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object pgctrlPrincipal: TFPageControl
        Left = 0
        Top = 0
        Width = 554
        Height = 351
        ActivePage = tabHome
        TabOrder = 0
        TabPosition = tpTop
        OnChange = pgctrlPrincipalChange
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        WOwner = FrInterno
        WOrigem = EhNone
        RenderStyle = rsTabbed
        object tabHome: TFTabsheet
          Caption = 'Painel'
          Visible = True
          Closable = False
          WOwner = FrInterno
          WOrigem = EhNone
        end
      end
    end
    object pnlWest: TFVBox
      Left = 2
      Top = 76
      Width = 260
      Height = 390
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      ParentBackground = False
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object accordion: TFAccordionMenu
        Left = 0
        Top = 0
        Width = 244
        Height = 366
        WOwner = FrInterno
        WOrigem = EhNone
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Menu = mnuPrincipal
        ExpandAll = False
        ShowSearchBar = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        OpenOnSelect = True
      end
      object lblVersaoSistema: TFLabel
        Left = 0
        Top = 367
        Width = 76
        Height = 13
        Caption = 'Vers'#227'o: *******'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindow
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsUnderline]
        ParentFont = False
        OnClick = lblVersaoSistemaClick
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object FHBox15: TFHBox
        Left = 0
        Top = 381
        Width = 185
        Height = 5
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
  end
  object FHBox3: TFHBox
    Left = 10
    Top = 0
    Width = 10
    Height = 20
    Anchors = []
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
  end
  object mnuPrincipal: TFMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 114
    Top = 207
  end
  object tmrRelogio: TFTimer
    Enabled = False
    Interval = 60000
    OnTimer = tmrRelogioTimer
    Repeats = False
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 442
    Top = 112
  end
  object popMenuLogin: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 345
    Top = 160
    object mmPerfil: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Perfil'
      ImageIndex = 310061
      OnClick = mmPerfilClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{5F7AB040-819B-4B16-86C2-AAA875DAA46B}'
    end
    object mmAlterarSenha: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Alterar Senha'
      ImageIndex = 310067
      OnClick = mmAlterarSenhaClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{088B3F17-36C0-4813-8F26-56191DC488EA}'
    end
    object mmHelp: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Help'
      ImageIndex = 7000109
      OnClick = mmHelpClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{403F7BB9-CC8F-4A33-BEF1-B30C4B37AD88}'
    end
    object FMenuItem1: TFMenuItem
      AutoHotkeys = maManual
      Caption = '-'
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{0FFC53E9-624B-4BC4-A56D-BBCD934385F2}'
    end
    object mmSair: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Sair'
      ImageIndex = 310060
      OnClick = mmSairClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{2B518677-4731-4FF5-87F3-1AE12EA0C456}'
    end
  end
  object imageList: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 615
    Top = 217
    object FMenuItem2: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'ImagemUsuario'
      ImageIndex = 700090
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{60F3998E-8E77-4D05-99FD-807E5ADAAD10}'
    end
    object FMenuItem3: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'ImagemSair'
      ImageIndex = 310065
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{F04714CE-74EF-46BC-B5F8-1EE1E22728B6}'
    end
    object FMenuItem4: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Calendar'
      ImageIndex = 310062
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{1171A811-9853-48B2-ABBC-2E83689EC050}'
    end
    object FMenuItem5: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Globo'
      ImageIndex = 310063
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{72663FB0-18ED-4BA4-AE2E-98FEBA45926F}'
    end
    object FMenuItem6: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'User Cicle'
      ImageIndex = 310064
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{FF491F45-D306-48F0-B2A1-764A30EB6AD5}'
    end
    object FMenuItem7: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Account Logout Black'
      ImageIndex = 310065
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{29CEF3D5-21FB-4061-899C-566102111D5E}'
    end
    object FMenuItem8: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Version'
      ImageIndex = 310066
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{FFD43B50-3D86-42A6-96BD-5867C1AE9374}'
    end
    object FMenuItem9: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Alterar Senha'
      ImageIndex = 310067
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{99140CB8-1C41-4222-8E55-C20A34650164}'
    end
    object Pesquisar: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Pesquiisar'
      ImageIndex = 700089
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{AF1D1D3D-D665-49C0-9E9D-A256A508C7A6}'
    end
    object FMenuItem10: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'LogoNbs'
      ImageIndex = 310038
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{9859AD41-ABBD-4E48-B9F5-EBD28B3E8EF9}'
    end
    object FMenuItemCarAdd: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'FMenuItemCarAdd'
      ImageIndex = 34008
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{4667AA9B-DB0B-417B-A3D9-652D3E2FD342}'
    end
    object LogoParts: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'LogoParts'
      ImageIndex = 7000256
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{62B8AC60-8A30-40BD-8707-D3482D171BF1}'
    end
  end
  object tmrMensagem: TFTimer
    Enabled = False
    Interval = 30000
    OnTimer = tmrMensagemTimer
    Repeats = False
    WOwner = FrNone
    WOrigem = EhNone
    Left = 848
    Top = 102
  end
  object tbUserInformation: TFTable
    FieldDefs = <
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Divis'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Segmento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'USER_INFORMATION'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;70002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMenu: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU_SUPER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu Super'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_FORM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Form'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORDEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Ordem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_IMAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Imagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'DBMENU'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;70003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSistemaAcesso: TFTable
    FieldDefs = <
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_PAI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso Pai'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LEVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Level'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERIGOSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perigoso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Ord Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENU_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Menu Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SISTEMA_ACESSO'
    Cursor = 'SISTEMA_ACESSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;70004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 6
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbProjeto
        GUID = '{4134C13C-9C74-4F1B-A7F1-BCB87EBCC8CC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbModulo
        GUID = '{39678245-98C1-4BB4-83D4-363DA8C932DE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbForm
        GUID = '{1D1CDFF5-5482-43DB-903C-CC975766D449}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbProjeto: TFTable
    FieldDefs = <
      item
        Name = 'ID_PROJETO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Projeto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATUALIZAR_MENU'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Atualizar Menu'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'FR_PROJETO'
    TableName = 'FR_PROJETO'
    Cursor = 'FR_PROJETO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;46001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProjetoConnDic: TFTable
    FieldDefs = <
      item
        Name = 'ID_PROJETO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Projeto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'FR_PROJETO'
    Cursor = 'FR_PROJETO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbModulo: TFTable
    FieldDefs = <
      item
        Name = 'ID_PROJETO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Projeto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MODULO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Modulo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'FR_MODULO'
    TableName = 'FR_MODULO'
    Cursor = 'FR_MODULO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;46003'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbModuloConnDic: TFTable
    FieldDefs = <
      item
        Name = 'ID_PROJETO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Projeto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MODULO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Modulo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'FR_MODULO'
    Cursor = 'FR_MODULO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbForm: TFTable
    FieldDefs = <
      item
        Name = 'ID_FORM'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Form'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PROJETO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Projeto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TITLE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Title'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MODULO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Modulo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'FR_FORM'
    TableName = 'FR_FORM'
    Cursor = 'FR_FORM'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;46005'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFormConnDic: TFTable
    FieldDefs = <
      item
        Name = 'ID_FORM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Form'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MODULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Modulo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TITLE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Title'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PROJETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Projeto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'FR_FORM'
    Cursor = 'FR_FORM'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;46006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFrMenu: TFTable
    FieldDefs = <
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_FORM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Form'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU_SUPER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu Super'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_IMAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Imagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORDEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Ordem'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'FR_MENU'
    Cursor = 'FR_MENU'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;46007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFrMenuConnDic: TFTable
    FieldDefs = <
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_FORM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Form'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU_SUPER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu Super'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_IMAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Imagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORDEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Ordem'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'FR_MENU'
    Cursor = 'FR_MENU'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;46008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMenuAcesso: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU_SUPER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu Super'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_FORM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Form'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORDEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ordem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_IMAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Imagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'DBMENU_ACESSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29002;37901'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
