object FrmOrcarServicosKitsMaisFiltroKit: TFForm
  Left = 321
  Top = 163
  ActiveControl = vBoxMaisFiltroKit
  Caption = 'Filtro Kit'
  ClientHeight = 310
  ClientWidth = 367
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600477'
  ShortcutKeys = <>
  InterfaceRN = 'OrcarServicosKitsMaisFiltroKitRN'
  Access = False
  ChangedProp = 
    'FrmOrcarServicosKitsMaisFiltroKit.Height;'#13#10#13#10'FrmOrcarServicosKit' +
    'sMaisFiltroKit.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxMaisFiltroKit: TFVBox
    Left = 0
    Top = 0
    Width = 367
    Height = 310
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox11: TFHBox
      Left = 0
      Top = 0
      Width = 360
      Height = 61
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 2
      Padding.Left = 2
      Padding.Right = 2
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Voltar Tela'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Aceitar'
        Align = alLeft
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
          0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
          1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
          95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
          101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
          1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
          A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
          C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
          482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
          1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
          C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
          8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
        ImageId = 700088
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnLimpar: TFButton
        Left = 120
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Limpar'
        Align = alLeft
        Caption = 'Limpar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnLimparClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000017A4944415478DAB594394A444110867B1841345030D0710915B753B8
          45623046828118EB6DF400A2188A6220266E277003F508E2CC68EA98E85F4CD7
          A37C54F58258F0056F9AF77F35D5AFBBE2FEB92AA5E75EB00E8E4133336B08AC
          8003D0D604147E0A16C00B9803AF19E19760069C833AF8940219CE952A91E15C
          8584055B605779F9D14B1B46F8880F9F54D636C11E0BA88B6B30952109853F80
          79D0927B30E85F98555E288F4B1B8B0CA7869A720F7224DFA9E19A806AD80768
          E37A0755D0AFACDD834557FABC35018FE0C2F8275AA9E121418EC40C8F09785C
          CFC648A8DE7C03E6A98F0946C113E833D61B5E609D93A0A0E63A9B3D1D692278
          E22D416A7854A209682C57604259A399778101654D3DF165C1980F1F5702EE5C
          E76BA9BAF413FF4B9012DEF2CFC9D70A0BE8E2BA31C26FC19208E7AAF986AC0B
          9224C565B70D7632C253241B609F05DDE0082C07C6629536AE33B00ADA720FA4
          24355C9314E1720FB87AC01A38011F89E15242CD1D822FFE317655FCB97E0090
          F96719D2A81AD50000000049454E44AE426082}
        ImageId = 4600385
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object cbbGrupoVenda: TFCombo
      Left = 0
      Top = 62
      Width = 358
      Height = 21
      LookupTable = tbReclamacoesGrupoVenda
      LookupKey = 'ID_GRUPO_VENDA'
      LookupDesc = 'DESCRICAO_INIT'
      Flex = False
      ReadOnly = True
      WOwner = FrInterno
      WOrigem = EhNone
      Required = False
      Prompt = 'Grupo de Venda'
      Constraint.CheckWhen = cwImmediate
      Constraint.CheckType = ctExpression
      Constraint.FocusOnError = False
      Constraint.EnableUI = True
      Constraint.Enabled = False
      Constraint.FormCheck = True
      ClearOnDelKey = True
      UseClearButton = False
      HideClearButtonOnNullValue = False
      Colors = <>
      Images = <>
      Masks = <>
      Fonts = <>
      MultiSelection = False
      IconReverseDirection = False
    end
    object chkEmPromo: TFCheckBox
      Left = 0
      Top = 84
      Width = 97
      Height = 17
      Caption = 'Em promo'#231#227'o'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 2
      ReadOnly = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taAlignTop
    end
    object chkAtivo: TFCheckBox
      Left = 0
      Top = 102
      Width = 97
      Height = 17
      Caption = 'Ativo?'
      Checked = True
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      State = cbChecked
      TabOrder = 3
      Visible = False
      ReadOnly = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taAlignTop
    end
    object chkPesqQualquerPos: TFCheckBox
      Left = 0
      Top = 120
      Width = 181
      Height = 17
      Align = alLeft
      Caption = 'Pesquisar em qualquer posi'#231#227'o'
      Checked = True
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      State = cbChecked
      TabOrder = 4
      ReadOnly = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taAlignTop
    end
    object rbSmtModeloVeic: TFRadioButton
      Left = 0
      Top = 138
      Width = 297
      Height = 17
      Caption = 'Somente kits para o Modelo do ve'#237'culo da O.S'
      Checked = True
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 5
      TabStop = True
      RadioGroup = FRadioGroup1
      WOwner = FrInterno
      WOrigem = EhNone
    end
    object rbSmtModeloVeicAno: TFRadioButton
      Left = 0
      Top = 156
      Width = 303
      Height = 17
      Caption = 'Somente kits para o Modelo/Ano do ve'#237'culo da O.S'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 6
      RadioGroup = FRadioGroup1
      WOwner = FrInterno
      WOrigem = EhNone
    end
    object rbIgnorarModeloAno: TFRadioButton
      Left = 0
      Top = 174
      Width = 231
      Height = 17
      Caption = 'Ignorar Modelo/Ano ve'#237'culo da O.S'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 7
      RadioGroup = FRadioGroup1
      WOwner = FrInterno
      WOrigem = EhNone
    end
  end
  object FRadioGroup1: TFRadioGroup
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 262
    Top = 122
  end
  object tbReclamacoesGrupoVenda: TFTable
    FieldDefs = <
      item
        Name = 'ID_GRUPO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_INIT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Init'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'RECLAMACOES_GRUPO_VENDA'
    Cursor = 'RECLAMACOES_GRUPO_VENDA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600477;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
