object FrmGerenciarOsMaisFiltros: TFForm
  Left = 44
  Top = 162
  ActiveControl = vBoxMaisFiltro
  Caption = 'Mais Filtros O.S.'
  ClientHeight = 332
  ClientWidth = 329
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600472'
  ShortcutKeys = <>
  InterfaceRN = 'GerenciarOsMaisFiltrosRN'
  Access = False
  ChangedProp = 
    'FrmGerenciarOsMaisFiltros.Caption;'#13#10'FrmGerenciarOsMaisFiltros.He' +
    'ight;'#13#10#13#10'FrmGerenciarOsMaisFiltros.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxMaisFiltro: TFVBox
    Left = 0
    Top = 0
    Width = 329
    Height = 332
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxButton: TFHBox
      Left = 0
      Top = 0
      Width = 322
      Height = 65
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Aceitar'
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
          0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
          1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
          95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
          101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
          1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
          A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
          C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
          482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
          1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
          C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
          8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
        ImageId = 700088
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object FLabel1: TFLabel
      Left = 0
      Top = 66
      Width = 27
      Height = 13
      Caption = 'Nome'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
      MaskType = mtText
    end
    object FHBox1: TFHBox
      Left = 0
      Top = 80
      Width = 314
      Height = 40
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object edtNome: TFString
        Left = 0
        Top = 0
        Width = 268
        Height = 24
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
      object vBoxLimpNome: TFVBox
        Left = 268
        Top = 0
        Width = 36
        Height = 35
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 3
        Padding.Left = 10
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        OnClick = vBoxLimpNomeClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        DesignSize = (
          32
          31)
        object FIconClass1: TFIconClass
          Left = 0
          Top = 0
          Width = 16
          Height = 16
          Hint = 'Apagar telefone residencial'
          Anchors = []
          Picture.Data = {
            07544269746D6170C6070000424DC60700000000000036000000280000001600
            0000160000000100200000000000900700000000000000000000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000000000000000000000000000000000000000
            00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000000000000000000000000000000000000000000000000000000000000000
            0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
            0000000000000000000000000000000000000000000000000000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000}
          IconClass = 'trash'
          WOwner = FrInterno
          WOrigem = EhNone
          Size = 22
          Color = clBlack
        end
      end
    end
    object FLabel2: TFLabel
      Left = 0
      Top = 121
      Width = 48
      Height = 13
      Caption = 'CPF/CNPJ'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
      MaskType = mtText
    end
    object FHBox2: TFHBox
      Left = 0
      Top = 135
      Width = 315
      Height = 40
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object edtCpfCnpj: TFInteger
        Left = 0
        Top = 0
        Width = 268
        Height = 24
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Alignment = taRightJustify
      end
      object vBoxLimpCpfCnpj: TFVBox
        Left = 268
        Top = 0
        Width = 36
        Height = 35
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 3
        Padding.Left = 10
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        OnClick = vBoxLimpCpfCnpjClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        DesignSize = (
          32
          31)
        object FIconClass2: TFIconClass
          Left = 0
          Top = 0
          Width = 16
          Height = 16
          Hint = 'Apagar telefone residencial'
          Anchors = []
          Picture.Data = {
            07544269746D6170C6070000424DC60700000000000036000000280000001600
            0000160000000100200000000000900700000000000000000000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000000000000000000000000000000000000000
            00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000000000000000000000000000000000000000000000000000000000000000
            0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
            0000000000000000000000000000000000000000000000000000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000}
          IconClass = 'trash'
          WOwner = FrInterno
          WOrigem = EhNone
          Size = 22
          Color = clBlack
        end
      end
    end
    object FLabel4: TFLabel
      Left = 0
      Top = 176
      Width = 42
      Height = 13
      Caption = 'Telefone'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
      MaskType = mtText
    end
    object FHBox108: TFHBox
      Left = 0
      Top = 190
      Width = 201
      Height = 40
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftMin
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object edtPrefixo: TFInteger
        Left = 0
        Top = 0
        Width = 40
        Height = 29
        TabOrder = 0
        AccessLevel = 0
        Flex = False
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        Maxlength = 2
        Align = alClient
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -17
        Font.Name = 'Tahoma'
        Font.Style = []
        Alignment = taRightJustify
      end
      object edtTelefone: TFInteger
        Left = 40
        Top = 0
        Width = 103
        Height = 29
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        Maxlength = 9
        Align = alClient
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -17
        Font.Name = 'Tahoma'
        Font.Style = []
        Alignment = taRightJustify
      end
      object vBoxLimpTelefone: TFVBox
        Left = 143
        Top = 0
        Width = 36
        Height = 35
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 3
        Padding.Left = 10
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        OnClick = vBoxLimpTelefoneClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        DesignSize = (
          32
          31)
        object iconApagarResidencial: TFIconClass
          Left = 0
          Top = 0
          Width = 16
          Height = 16
          Hint = 'Apagar telefone residencial'
          Anchors = []
          Picture.Data = {
            07544269746D6170C6070000424DC60700000000000036000000280000001600
            0000160000000100200000000000900700000000000000000000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000000000000000000000000000000000000000
            00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000000000000000000000000000000000000000000000000000000000000000
            0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
            0000000000000000000000000000000000000000000000000000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000}
          IconClass = 'trash'
          WOwner = FrInterno
          WOrigem = EhNone
          Size = 22
          Color = clBlack
        end
      end
    end
    object FLabel3: TFLabel
      Left = 0
      Top = 231
      Width = 76
      Height = 13
      Caption = 'Placa do Veiculo'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
      MaskType = mtText
    end
    object FHBox3: TFHBox
      Left = 0
      Top = 245
      Width = 163
      Height = 40
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 4
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftMin
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object edtPlacaVeic: TFString
        Left = 0
        Top = 0
        Width = 121
        Height = 24
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
      object vBoxLimpPlaca: TFVBox
        Left = 121
        Top = 0
        Width = 36
        Height = 35
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 3
        Padding.Left = 10
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        OnClick = vBoxLimpPlacaClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        DesignSize = (
          32
          31)
        object FIconClass3: TFIconClass
          Left = 0
          Top = 0
          Width = 16
          Height = 16
          Hint = 'Apagar telefone residencial'
          Anchors = []
          Picture.Data = {
            07544269746D6170C6070000424DC60700000000000036000000280000001600
            0000160000000100200000000000900700000000000000000000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000000000000000000000000000000000000000
            00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F00000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            0000000000000000000000000000000000000000000000000000000000000000
            0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
            0000000000000000000000000000000000000000000000000000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000}
          IconClass = 'trash'
          WOwner = FrInterno
          WOrigem = EhNone
          Size = 22
          Color = clBlack
        end
      end
    end
  end
end
