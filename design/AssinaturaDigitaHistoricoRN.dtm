object AssinaturaDigitaHistoricoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '469012'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbNbsapiEnvelopesLog: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{98DDC552-E639-461B-84EF-C31B00EEC7F3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Codigo'
        GUID = '{2E3F09D3-4EAC-4489-8118-0ED92BD68184}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Codigo'
        GUID = '{517B1A6A-2FE5-4A05-B3FF-BD7B8D9C86EA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{F9846FF5-4B04-43E7-A6C0-0AA834E22752}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PACOTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pacote'
        GUID = '{949AF434-B218-454C-B050-02A56EC79B1E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{ABE822AE-EBD4-4B27-97EA-E1F8F355C609}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel'
        GUID = '{999B1D71-ECD5-4038-BAE1-8BA0D79AF912}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_LOG'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Log'
        GUID = '{CB92060F-1C39-4AB5-8A48-7DA19B5DBE47}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Envelope'
        GUID = '{C3754E73-11D1-445A-8A98-2E4D27B34F1C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{1CC3D5D3-0037-45A0-B80B-1029582B091B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_SISTEMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Sistema'
        GUID = '{B1CD9675-BB4E-4BDA-B19C-4DD5E186C6AF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBSAPI_ENVELOPES_LOG'
    Cursor = 'NBSAPI_ENVELOPES_LOG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '469012;46901'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbAssinaturaDigital: TFTable
    FieldDefs = <
      item
        Name = 'ID_ASSINATURA_DIGITAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Assinatura Digital'
        GUID = '{7B9DAAAF-1F6B-4B13-8305-B40C6DB3593E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{D089A0D1-7412-4736-BB94-6288F6D698CC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{14BB4B5C-2779-4064-A2FA-C36736DDCF59}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{BF797D84-CB22-46E0-9BC3-15534C23723D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Codigo'
        GUID = '{9BF59B70-8498-4A4B-9FD1-9D0F3794D443}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_SISTEMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Sistema'
        GUID = '{633E89D5-E776-45BA-BA82-4920641A8311}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_ASSINATURA_DIGITAL'
    Cursor = 'CRM_ASSINATURA_DIGITAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '469012;46902'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
