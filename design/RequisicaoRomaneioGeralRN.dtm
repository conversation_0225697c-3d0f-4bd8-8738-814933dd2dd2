object RequisicaoRomaneioGeralRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '17609'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbReqAssinaturaGrid: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{13D53D1B-12A7-49EC-A693-C9CCF7DAF233}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{ECCF83A7-30EC-4FC4-A89F-024FED3E224A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REQUISICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Requisi'#231#227'o'
        GUID = '{B6A53D2B-D959-423E-A8ED-37720BC7E532}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTREGUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Entregue'
        GUID = '{21FA1C70-083D-40A3-925F-37701D10A00A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Item'
        GUID = '{C299DCBD-DFF4-4B3B-90B5-F3C86D1EDBD4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{427FB1C5-A0FE-4AE7-A1ED-A3B1C374DC13}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_REQ_ASSINATURA_GRID'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '17609;17601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbReqAssinaturaTecnico: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{E6C955C4-2C08-4CCF-95E7-2D9402EEE8E4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{70C6C6D9-D663-4983-9B67-FCDAF0B73E58}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{7DEEA0D4-6BFD-49D7-AB6D-48F3DE2FBA37}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REQUISICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Requisi'#231#227'o'
        GUID = '{8AE831FE-2DF2-4B65-B582-A97E58539ABF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura Produtivo'
        GUID = '{C8B6D33D-67D8-49DB-BCAD-338F42E951D7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        GUID = '{86C4F8D7-11BC-4E14-B6C9-F4EFDFC30FC5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{F1F66675-8ABC-467F-A91E-E8D819CA83AC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio'
        GUID = '{F2A9F322-2981-475E-9560-2BF3C4A0D91D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_REQ_ASSINATURA_TECNICO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '17609;17603'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
