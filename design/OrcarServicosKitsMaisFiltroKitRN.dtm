object OrcarServicosKitsMaisFiltroKitRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600477'
  Height = 299
  Width = 442
  object tbReclamacoesGrupoVenda: TFTable
    FieldDefs = <
      item
        Name = 'ID_GRUPO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_INIT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Init'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'RECLAMACOES_GRUPO_VENDA'
    Cursor = 'RECLAMACOES_GRUPO_VENDA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600477;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
