object AgendaProgramacaoDetalheFiltroRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '466011'
  Height = 299
  Width = 442
  object tbProgramacaoServicoDetalhe: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o'
        GUID = '{9BF6762D-3F77-4DD5-AA12-B7B73272545B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{0ABBD678-007E-47E3-8236-7CB1E4A38FF6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Entrada'
        GUID = '{D72224AC-A184-41E1-AFC3-C15547C9904E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{94D28FBF-E884-4948-8B44-186595E9C6BE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ROW_NUMBER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Row Number'
        GUID = '{624EDC65-1970-4FE7-B712-2746E41D5BF2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{5F4FFA32-7880-402C-9C48-FC63E2FA36A3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{C10DEECE-0B64-404D-810B-44344CC3D798}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_COMECA_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Comeca Servi'#231'o'
        GUID = '{DB45B281-678D-4A56-BD92-D2A8E2522332}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PROMETIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Prometida'
        GUID = '{09C6EDCB-2576-4D99-B868-ED1F3C3161E2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_TIMELINE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Timeline'
        GUID = '{E45F7D63-0849-433E-9D4B-92197A6D5EBD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEM_APONTADOMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tem Apontadomento'
        GUID = '{0673AA1B-9DE8-49BE-BC9C-321B01942F4A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROMESSA_ATRASADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Promessa Atrasada'
        GUID = '{2AABCC7E-C9CC-444A-AE79-DD3B5510BE11}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_PROMESSA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Promessa'
        GUID = '{0D9EF403-C017-4F52-995B-B1477CBBFDB2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COLOR_PROMESSA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Color Promessa'
        GUID = '{69237245-56A2-4030-8763-5AAB09E899CA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PROGRAMACAO_SERV_DETAIL_FILTER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466011;46601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProgramacaoServDetailProd: TFTable
    FieldDefs = <
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        GUID = '{97E1ABBA-4339-4FB9-B101-C744D8DDCC52}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Produtivo'
        GUID = '{EFDD4AD6-8C90-45B7-AA8B-A8544F76FE2E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Entrada'
        GUID = '{50C38CFC-812A-4A59-9286-8E8EBD1D738A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Entrada'
        GUID = '{863A494C-26AC-4091-9B7C-8D730ABCAF4C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Sa'#237'da'
        GUID = '{1445D566-2D67-4750-A649-91A6E61453B7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Sa'#237'da'
        GUID = '{3B728B0F-AE4B-44F1-B9A6-8F4435450B46}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PROGRAMACAO_SERV_DETAIL_PROD'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466011;46602'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProgramacaoServDetailVeic: TFTable
    FieldDefs = <
      item
        Name = 'PLACA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Placa'
        GUID = '{EB992E06-C47F-478A-BC3E-AC1704531221}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VEICULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ve'#237'culo'
        GUID = '{476CDFB5-38F9-4578-9176-18EE98F0363A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PROGRAMACAO_SERV_DETAIL_VEIC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466011;46603'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProgramacaoServDetailServ: TFTable
    FieldDefs = <
      item
        Name = 'ROW_NUMBER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Row Number'
        GUID = '{90D88D5D-2473-4B66-B853-0A4A52F0D215}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o'
        GUID = '{E41B7EA2-264F-413F-BB85-1E60589A4419}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Servi'#231'o'
        GUID = '{16974F6E-852E-4F81-82DC-FC42396277D6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_SERVICO_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Servi'#231'o Desconto'
        GUID = '{0DBB32D9-5011-4BAD-836F-3FC0DB2A6C11}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TRABALHADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Trabalhado'
        GUID = '{06AFF7F4-8CE0-4BC8-BE4A-494443AF09F7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PROGRAMACAO_SERV_DETAIL_SERV'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466011;46604'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbParadaMotivos: TFTable
    FieldDefs = <
      item
        Name = 'COD_MOTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Motivo'
        GUID = '{71A10AE3-542F-4E3B-A0D9-196CBD890BFB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{1AD4FFAB-543F-4D28-8A0B-47A056EDC945}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_PARADA_MOTIVOS'
    Cursor = 'OS_PARADA_MOTIVOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466011;46605'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProgramacaoPecasFaltando: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{F5D5C530-50F8-485C-8FB6-************}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        GUID = '{C5445D05-AFDF-42FF-8863-3D497F2B2796}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Original'
        GUID = '{713C36FD-2F57-4701-846C-FEDD779A2808}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Estoque'
        GUID = '{01BACEBC-9D77-443D-A96E-C4DC566DD391}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ESTOQUE_FILIAIS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Estoque Filiais'
        GUID = '{BE550AB4-0D36-4A68-A017-0E40400192D6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PEDIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pedido'
        GUID = '{C06CC0B9-6AC2-451E-930D-1DF808BDAACE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTROLE_VENDA_PENDENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Controle Venda Pendente'
        GUID = '{B03740E9-1067-464D-895D-300812C153A6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PROGRAMACAO_PECAS_FALTANDO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466011;46606'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbReprogramarMotivBolsaoCombo: TFTable
    FieldDefs = <
      item
        Name = 'ID_BOLSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Bols'#227'o'
        GUID = '{5A8D2E26-28B0-473C-B578-C83325D9AD35}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO_BOLSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Motivo Bols'#227'o'
        GUID = '{A6371352-C4FA-49E4-93AA-19237A0433AD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REPROGRAMAR_MOTIV_BOLSAO_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466011;466011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbReprogramarProdutivoCombo: TFTable
    FieldDefs = <
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        GUID = '{4BA95F32-C405-458D-9587-100F96FCBEAA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Produtivo'
        GUID = '{8EA2EF09-82C0-4681-8A73-CEB7D10EECDD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REPROGRAMAR_PRODUTIVO_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466011;466012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbReprogramarOsCombo: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{E3423BD6-3C39-43B9-8753-009F45B64071}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os Desconto'
        GUID = '{96F57121-81DE-406E-9D15-C2601C229149}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REPROGRAMAR_OS_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466011;466013'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
