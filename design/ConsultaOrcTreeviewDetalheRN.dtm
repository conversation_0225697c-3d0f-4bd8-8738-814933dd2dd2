object ConsultaOrcTreeviewDetalheRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600434'
  Height = 299
  Width = 442
  object tbConsultaServTodos: TFTable
    FieldDefs = <
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tmo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORC_CONSULTA_SERV_TODOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600434;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaServReclamacao: TFTable
    FieldDefs = <
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tmo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORC_CONSULTA_SERV_RECLAMACAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600434;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaPecas: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORNEC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fornec'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vp'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PDESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pdesc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORC_CONSULTA_PECAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600434;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaAgendaProdutivo: TFTable
    FieldDefs = <>
    Cursor = 'ORC_CONSULTA_AGENDA_PRODUTIVO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600434;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
