object FrmCadastroBancoPedidosService: TFForm
  Left = 321
  Top = 162
  Caption = 'Cadastro de banco de Pedidos'
  ClientHeight = 449
  ClientWidth = 404
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600478'
  ShortcutKeys = <>
  InterfaceRN = 'CadastroBancoPedidosServiceRN'
  Access = False
  ChangedProp = 
    'FrmCadastroBancoPedidosService.Height;'#13#10'FrmCadastroBancoPedidosS' +
    'ervice.Width;'#13#10#13#10'FrmCadastroBancoPedidosService.ActiveControl'#13#10'F' +
    'rmCadastroBancoPedidosService.ActiveControl'#13#10'FrmCadastroBancoPed' +
    'idosService_1.Touch.InteractiveGestures;'#13#10'FrmCadastroBancoPedido' +
    'sService_1.Touch.InteractiveGestureOptions;'#13#10'FrmCadastroBancoPed' +
    'idosService_1.Touch.ParentTabletOptions;'#13#10'FrmCadastroBancoPedido' +
    'sService_1.Touch.TabletOptions;'#13#10'FrmCadastroBancoPedidosService_' +
    '1.Touch.InteractiveGestures;'#13#10'FrmCadastroBancoPedidosService_1.T' +
    'ouch.InteractiveGestureOptions;'#13#10'FrmCadastroBancoPedidosService_' +
    '1.Touch.ParentTabletOptions;'#13#10'FrmCadastroBancoPedidosService_1.T' +
    'ouch.TabletOptions;FrmCadastroBancoPedidosService.Caption;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 404
    Height = 449
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 2
    Padding.Left = 2
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 12
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitLeft = 256
    ExplicitTop = 420
    ExplicitWidth = 185
    ExplicitHeight = 41
    object hboxEditarItem: TFHBox
      Left = 0
      Top = 0
      Width = 404
      Height = 61
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 5
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnSalvar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Salvar'
        Align = alLeft
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
          C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
          D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
          1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
          D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
          9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
          A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
          BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
        ImageId = 700080
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FVBox4: TFVBox
        Left = 60
        Top = 0
        Width = 5
        Height = 55
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnCancelar: TFButton
        Left = 65
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Cancelar'
        Align = alLeft
        Caption = 'Cancelar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnCancelarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000022B4944415478DAB595DF4B544114C7CF2692A12F22195B28855AF8A0
          50F4121111E9F64304F34585D4C00705317AAABFA18720AA97602B044B097221
          88486559C444A8968405454537D0087F15063EC4BAF53DCC1918C6BDEBDC950E
          7CE09C997BCFF7CECC997303F49F2DE0F85C31380D8E48FC037C053FF723C073
          4DE036B898E1D934888147E02DF8EB47E004E807171C57380E3AC03717813360
          0494585F1B078B1257C8961D309E590321D93A4F8172F0191C9638051E8207B2
          EFA605C15D70C7185B950F5CC924C0FE28B82CF1266800531EDB9207C2E09635
          FE015C23391353A05EB6866D87D4C17EF499DCCC35660B7025348AFF04F46549
          FE0C741A6361C9D5257104349B0205E0173828315751D247F26E500566656C9B
          D4DDF9A305F860BE883F074E79247F4EAA1CEDE4698997C071F16B40420B5C07
          EFC4E773B8924372B628B8649E8316B80ADE8BCF9514CA21395B8C5471B07135
          46B5402D98163F2967A093BF00ED0EC939D732382A71359F8916C827D5B80A8D
          FD9BF1919C8D6F765CFC2D529D206596E91068117F90D42D764DAEDF6915FF25
          B8A997A5ED3C98F07879AFE4DC7523467C8EA403D8BD6818DCC821F92B7048E2
          D7C64EEC1228059F48353D6D03E03E481863FC1E17C63DD0668C27C159B0E125
          C07692541F29B3C6B94216E49D4A70CC9AE77F419D3C43D904F44A9ECAF25DEC
          0DE801EBF6C45EFF64FEA3F5926ADB45D6DC6F52B7FF3198F44AE0FAD3E77BC2
          CD2C28F177304FAA94B39AAB40CEF60F541979196CA1E08A0000000049454E44
          AE426082}
        ImageId = 700098
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FVBox1: TFVBox
        Left = 125
        Top = 0
        Width = 5
        Height = 55
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnExcluir: TFButton
        Left = 130
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Excluir do banco de Pedido'
        Align = alLeft
        Caption = 'Excluir'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 4
        OnClick = btnExcluirClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
          782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
          B9B0DBF5D30000000049454E44AE426082}
        ImageId = -1
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'trash'
        IconReverseDirection = False
      end
    end
    object FPanel1: TFPanel
      Left = 0
      Top = 62
      Width = 400
      Height = 230
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      OnClick = FPanel1Click
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      WOwner = FrInterno
      WOrigem = EhNone
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      object FGridPanel1: TFGridPanel
        Left = 20
        Top = -2
        Width = 360
        Height = 205
        Caption = 'FGridPanel1'
        ColumnCollection = <
          item
            SizeStyle = ssAbsolute
            Value = 100.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            Value = 100.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        ControlCollection = <
          item
            Column = 0
            Control = lblEmpresaPedido
            Row = 0
          end
          item
            Column = 1
            Control = FComboEmpresaPedido
            Row = 0
          end
          item
            Column = 0
            Control = lblCodItem
            Row = 1
          end
          item
            Column = 1
            Control = edtCodItem
            Row = 1
          end
          item
            Column = 0
            Control = lblDescricaoItem
            Row = 2
          end
          item
            Column = 1
            Control = edtDescricaoItem
            Row = 2
          end
          item
            Column = 0
            Control = lblFornecedor
            Row = 3
          end
          item
            Column = 1
            Control = edtFornecedor
            Row = 3
          end
          item
            Column = 0
            Control = lblQuantidadePedir
            Row = 4
          end
          item
            Column = 1
            Control = edtQuantidadePedir
            Row = 4
          end
          item
            Column = 0
            Control = lblObservacao
            Row = 5
          end
          item
            Column = 1
            Control = edtObservacao
            Row = 5
          end>
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        RowCollection = <
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        AllRowFlex = False
        WOwner = FrInterno
        WOrigem = EhNone
        ColumnTabOrder = False
        object lblEmpresaPedido: TFLabel
          Left = 21
          Top = 1
          Width = 80
          Height = 21
          Align = alRight
          Alignment = taRightJustify
          Caption = 'Empresa Pedido:'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object FComboEmpresaPedido: TFCombo
          Left = 101
          Top = 1
          Width = 255
          Height = 21
          LookupTable = tbEmpresasBcoPedidos
          LookupKey = 'COD_EMPRESA'
          LookupDesc = 'NOME'
          Flex = False
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = True
          HideClearButtonOnNullValue = True
          Align = alLeft
          OnChange = FComboEmpresaPedidoChange
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object lblCodItem: TFLabel
          Left = 24
          Top = 22
          Width = 77
          Height = 24
          Align = alRight
          Alignment = taRightJustify
          Caption = 'C'#243'digo do Item:'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object edtCodItem: TFString
          Left = 101
          Top = 22
          Width = 255
          Height = 24
          Table = tbItensListaBcoPedido
          FieldName = 'COD_ITEM'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Align = alLeft
          Enabled = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object lblDescricaoItem: TFLabel
          Left = 11
          Top = 46
          Width = 90
          Height = 24
          Align = alRight
          Alignment = taRightJustify
          Caption = 'Descri'#231#227'o do Item:'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object edtDescricaoItem: TFString
          Left = 101
          Top = 46
          Width = 255
          Height = 24
          Table = tbItensListaBcoPedido
          FieldName = 'DESCRICAO'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Align = alLeft
          Enabled = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object lblFornecedor: TFLabel
          Left = 42
          Top = 70
          Width = 59
          Height = 24
          Align = alRight
          Alignment = taRightJustify
          Caption = 'Fornecedor:'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object edtFornecedor: TFString
          Left = 101
          Top = 70
          Width = 255
          Height = 24
          Table = tbItensListaBcoPedido
          FieldName = 'NOME_FORNECEDOR'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Align = alLeft
          Enabled = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          OnChange = edtFornecedorChange
          SaveLiteralCharacter = False
        end
        object lblQuantidadePedir: TFLabel
          Left = 9
          Top = 94
          Width = 92
          Height = 24
          Align = alRight
          Alignment = taRightJustify
          Caption = 'Quantidade a Pedir'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object edtQuantidadePedir: TFString
          Left = 101
          Top = 94
          Width = 255
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Align = alLeft
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object lblObservacao: TFLabel
          Left = 39
          Top = 118
          Width = 62
          Height = 24
          Align = alRight
          Alignment = taRightJustify
          Caption = 'Observa'#231#227'o:'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object edtObservacao: TFString
          Left = 101
          Top = 118
          Width = 255
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Align = alLeft
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
      end
    end
    object FPanel2: TFPanel
      Left = 0
      Top = 293
      Width = 400
      Height = 121
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Visible = False
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      WOwner = FrInterno
      WOrigem = EhNone
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      object FGridPanel2: TFGridPanel
        Left = 20
        Top = 20
        Width = 370
        Height = 63
        Caption = 'FGridPanel2'
        ColumnCollection = <
          item
            SizeStyle = ssAbsolute
            Value = 100.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            Value = 100.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        ControlCollection = <
          item
            Column = 0
            Control = FLabel1
            Row = 0
          end
          item
            Column = 1
            Control = edtQtdPedido
            Row = 0
          end
          item
            Column = 0
            Control = FLabel2
            Row = 1
          end
          item
            Column = 1
            Control = edtDataLancamento
            Row = 1
          end>
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        RowCollection = <
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        AllRowFlex = False
        WOwner = FrInterno
        WOrigem = EhNone
        ColumnTabOrder = False
        object FLabel1: TFLabel
          Left = 9
          Top = 1
          Width = 92
          Height = 24
          Align = alRight
          Alignment = taRightJustify
          Caption = 'Quantidade a Pedir'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object edtQtdPedido: TFString
          Left = 101
          Top = 1
          Width = 255
          Height = 24
          Table = tbBancoDePedido
          FieldName = 'QTDE_A_PEDIR'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Align = alLeft
          Enabled = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object FLabel2: TFLabel
          Left = 2
          Top = 25
          Width = 99
          Height = 24
          Align = alRight
          Alignment = taRightJustify
          Caption = 'Data do Lancamento'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object edtDataLancamento: TFDate
          Left = 101
          Top = 25
          Width = 255
          Height = 24
          Table = tbBancoDePedido
          FieldName = 'DATA_LANCAMENTO'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          Format = 'dd/MM/yyyy'
          ShowCheckBox = False
          Align = alLeft
          Enabled = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
        end
      end
      object FHBox3: TFHBox
        Left = 20
        Top = -2
        Width = 370
        Height = 21
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel3: TFLabel
          Left = 0
          Top = 0
          Width = 117
          Height = 13
          Caption = '                                       '
          Color = clBtnFace
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentColor = False
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
        end
        object FLabel5: TFLabel
          Left = 117
          Top = 0
          Width = 283
          Height = 13
          Alignment = taCenter
          Caption = '   Banco de Pedido j'#225' tem este Item com este Fornecedor   '
          Color = clBtnFace
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clRed
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentColor = False
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
        end
      end
      object FHBox4: TFHBox
        Left = 0
        Top = 91
        Width = 396
        Height = 26
        Align = alBottom
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel4: TFLabel
          Left = 0
          Top = 0
          Width = 129
          Height = 13
          Alignment = taCenter
          Caption = '                                           '
          Color = clBtnFace
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentColor = False
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
        end
      end
    end
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbBancoDePedido
        GUID = '{E5220145-7EDA-408A-8709-36B29A24C43F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbBancoDePedido: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_LANCAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Lan'#231'amento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_A_PEDIR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade a Pedir'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SUGESTAO_FABRICA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sugest'#227'o Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'BANCO_DE_PEDIDO'
    TableName = 'BANCO_DE_PEDIDO'
    Cursor = 'BANCO_DE_PEDIDO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600478;46001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasBcoPedidos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'EMPRESAS_BCO_PEDIDOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600478;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensListaBcoPedido: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ITENS_LISTA_BCO_PEDIDO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600478;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaBancoPedido: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_BANCO_PEDIDO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600478;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsPesqItem: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_PESQ_ITEM'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600478;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
