object FrmGerencialVendasPendentes: TFForm
  Left = 44
  Top = 160
  ActiveControl = cbbEmpresas
  Caption = 'Vendas Pendentes'
  ClientHeight = 645
  ClientWidth = 817
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '123032'
  ShortcutKeys = <>
  InterfaceRN = 'GerencialVendasPendentesRN'
  Access = False
  ChangedProp = 
    'FrmGerencialVendasPendentes.Width;'#13#10'FrmGerencialVendasPendentes.' +
    'Height;'#13#10#13#10'FrmGerencialVendasPendentes.ActiveControl'#13#10'FrmGerenci' +
    'alVendasPendentes_1.Touch.InteractiveGestures;'#13#10'FrmGerencialVend' +
    'asPendentes_1.Touch.InteractiveGestureOptions;'#13#10'FrmGerencialVend' +
    'asPendentes_1.Touch.ParentTabletOptions;'#13#10'FrmGerencialVendasPend' +
    'entes_1.Touch.TabletOptions;'#13#10'FrmGerencialVendasPendentes_1.Touc' +
    'h.InteractiveGestures;'#13#10'FrmGerencialVendasPendentes_1.Touch.Inte' +
    'ractiveGestureOptions;'#13#10'FrmGerencialVendasPendentes_1.Touch.Pare' +
    'ntTabletOptions;'#13#10'FrmGerencialVendasPendentes_1.Touch.TabletOpti' +
    'ons;'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 817
    Height = 645
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 798
      Height = 77
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox1: TFVBox
        Left = 0
        Top = 0
        Width = 305
        Height = 69
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbbEmpresas: TFCombo
          Left = 0
          Top = 0
          Width = 297
          Height = 21
          LookupTable = tbConsultaComboEmpresa
          LookupKey = 'COD_EMPRESA'
          LookupDesc = 'EMPRESA'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object FHBox2: TFHBox
          Left = 0
          Top = 22
          Width = 296
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 3
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object edtNumero: TFInteger
            Left = 0
            Top = 0
            Width = 221
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Num OS / ORC'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Maxlength = 0
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            Alignment = taRightJustify
            OnExit = edtNumeroExit
          end
          object vBoxIconApagar: TFVBox
            Left = 221
            Top = 0
            Width = 43
            Height = 34
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 5
            Padding.Left = 11
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            OnClick = vBoxIconApagarClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object iconTrash: TFImage
              Left = 0
              Top = 0
              Width = 16
              Height = 18
              Align = alClient
              Stretch = False
              ImageSrc = '/images/crmservice4600235.png'
              WOwner = FrInterno
              WOrigem = EhNone
              BoxSize = 0
              GrayScaleOnDisable = False
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              Preview = False
            end
          end
        end
      end
      object FVBox2: TFVBox
        Left = 305
        Top = 0
        Width = 305
        Height = 69
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbbTipoPesquisa: TFCombo
          Left = 0
          Top = 0
          Width = 297
          Height = 21
          Flex = True
          ListOptions = 'Todos OS/ORC=1;OS=2;ORC=3'
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = True
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object FHBox3: TFHBox
          Left = 0
          Top = 22
          Width = 296
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 3
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object edtCodItem: TFString
            Left = 0
            Top = 0
            Width = 185
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'C'#243'd / Desc do Item'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccNormal
            Pwd = False
            Maxlength = 0
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            OnExit = edtCodItemExit
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
          object vBoxIconApagar2: TFVBox
            Left = 185
            Top = 0
            Width = 43
            Height = 34
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 5
            Padding.Left = 11
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            OnClick = vBoxIconApagar2Click
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FImage1: TFImage
              Left = 0
              Top = 0
              Width = 16
              Height = 18
              Align = alClient
              Stretch = False
              ImageSrc = '/images/crmservice4600235.png'
              WOwner = FrInterno
              WOrigem = EhNone
              BoxSize = 0
              GrayScaleOnDisable = False
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              Preview = False
            end
          end
        end
      end
      object FHBox4: TFHBox
        Left = 610
        Top = 0
        Width = 158
        Height = 70
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object btnFiltros: TFButton
          Left = 0
          Top = 0
          Width = 75
          Height = 70
          Caption = '++ Filtros'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          OnClick = btnFiltrosClick
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnPesquisar: TFButton
          Left = 75
          Top = 0
          Width = 75
          Height = 70
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          OnClick = btnPesquisarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D49484452000000300000003008060000005702F9
            870000049F4944415478DAD5997B88555514C6F74D2B4B2A8334CA174CA2948A
            266449983286CA8835A9A8898F081FA3282821697FF410411D3432115F043A51
            A9F91C6DA66470CAFE50844C7C20063E41F281685ABED26EBF8FBD2F5C2E7367
            F63E73EFDC33073E16F7CCACBDBEEF9CB3D75E7BED8489702593C94730C5A004
            1481F60E4F814BE082C371B0031C4E2412C928B11ABA1281C4DFC0948161E0C9
            00D7B3603B588E90734D2E00E22F611683E169B7EF835FC111F0A7C35FA01D78
            0E3C0F5E073DD27CEE822FC122845CCBBB0088B774C4E78087DCEDDDE01BF023
            24AE7B882F72C2A780EEEEB6C897E1BF396F0208DC06B3090C76B7F6817904DD
            1F2510E3B5C08C070B402777FB33FD66CCFF722AC03DB52AD00DDC035341452E
            262263B7C27C01A6B95BDF83098C7D372702DC933FE0C82BA39432F881C612AF
            23CE0C63E783DECC7AF07E940794C81854DFFC0FC67E3622FF6AAEB34646BCB7
            8C4DB3E23197584B1B2B40037C606CB618988F275F87880F8D4D147AFA4389B9
            279200067A1173CCD86CF31E036DC8377917571CBE0563C11FA007B1FF8D22A0
            D2D874A76C33305F2B671611CF624E81D66006B1570509702BEC2FEE5EBFA6F8
            74EA10A194FA31B80CBAC0E1668800BDC277C16E1C87FB38E641804A13250C65
            C189F0F8DA4B802BCCAE185BDB8CC3F1BB420870222A3013C076788CF0153014
            5B6D6C6DD32E57354A4401A38C5DD86E8367E072CB4780169359A01687E24291
            77029EC05C050F8312F854FB085071A6F258A5EEEC420A70224E62BA8299F059
            E923E0776C6F630BB5253110F0336680B125F7473E0294B6DA82493854C44080
            92881635158F937C04A86C502652D1B6330602561B5BA956C2E76D1F01E7B11D
            C1541CD6C540C036CC3B602D7CA635F4FF12A00DCA6BE0131C16C44040101F09
            D8821D09D6E0501603015A8DB5639B0C9FAF7C04A46A906338F42C30797DCAE7
            DD4FAF9A4C025EC61E72BF5FC0E97401056897A6DCAF0E47079FBD72C2D5E367
            4067301BA7E50514A09557A5CD6A784CF7F14955A39F1BDB3A5127AD17CE0F0A
            40BE0BE604D0B67688EFCE2C25404F5F4BF8A3A609776319023662C680DF405F
            DF564BFA8EAC1C33D7D849D48D01EE3421F93E8EB8AE6262D7FAFAA60B78DAD8
            6D9DACF7379803F2AA4095FBD5B5AB22EEB010FFCCAEC46863BB71BA82F6A611
            C9AB81B015941A5B46BF42CC339105B84153EB8226F20806ACCC23F9BDC6569E
            DA4C0D22D6BED071125906D65BD0EE489D89F9A03C975D0AF7D9A8F22D75B7B4
            F3EA4F8C43A16365EB8D2A1BAD01A9725619426BC4A51C90EFE3C8EB9BD79357
            EFF571633BD66F868AA8AF3BADBFA94B57EEFEEF1FB04C20C88D08C495E7171A
            9B2A75E99BD7C6FD6F50636CF20816D1E0010781D5275D61EC364F97CE047601
            CD8D9FEAEBDFB8DA466D1AF5400719BB48E952E77B666AC2BAB7124984EF098D
            36D993C1A7C69EC0A42EB5004522DB094DA78CA1444A4DDCBD75C4882422F48C
            4C934F134F1B0ED52C8F79B85D34B603AD33B29AFA56D8282282046404D3C453
            0A4C3FA56CE308A79F521E0C398109151159403EAF1011B114102222B6027C45
            C45A808F88D80B684844B310509F88662320AB8842936AA4888BCD4E409A089D
            671FFD1F719BF90A58A66F7A0000000049454E44AE426082}
          ImageId = 7000252
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
      end
    end
    object hBoxBtnTotais: TFHBox
      Left = 0
      Top = 78
      Width = 797
      Height = 85
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 10
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox153: TFVBox
        Left = 0
        Top = 0
        Width = 7
        Height = 62
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object FPanelButtonTodas: TFPanelButton
        Left = 7
        Top = 0
        Width = 80
        Height = 67
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        OnClick = FPanelButtonTodasClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonTodasItems: TFFastDesignCmpItems
          object FPanelButtonItem5: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object lblTotTodas: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{8FB2EAA8-D8C6-4596-B601-2CB12E8FF213}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlack
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem11: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem4'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{676D4144-C057-439A-9D44-7EC07ED35C18}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice123031.png'
              Flex = False
              Width = 28
              Height = 28
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object lblTodosDesc: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Todas'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FPanelButtonSemPedido: TFPanelButton
        Left = 87
        Top = 0
        Width = 80
        Height = 67
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        OnClick = FPanelButtonSemPedidoClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonSemPedidoItems: TFFastDesignCmpItems
          object FPanelButtonItem1: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object lblTotSemPedidos: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{8FB2EAA8-D8C6-4596-B601-2CB12E8FF213}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clMaroon
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem4: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem4'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{E8E6BC1C-61FE-4254-A06C-70DA4C79ED6E}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice123033.png'
              Flex = False
              Width = 28
              Height = 28
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem2: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Sem Pedido'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FPanelButtonComPedido: TFPanelButton
        Left = 167
        Top = 0
        Width = 80
        Height = 67
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        OnClick = FPanelButtonComPedidoClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonComPedidoItems: TFFastDesignCmpItems
          object FPanelButtonItem3: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object lblTotComPedido: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{8FB2EAA8-D8C6-4596-B601-2CB12E8FF213}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clGray
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem6: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem4'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{676D4144-C057-439A-9D44-7EC07ED35C18}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice4600437.png'
              Flex = False
              Width = 28
              Height = 28
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem7: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Com Pedido'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FPanelButtonAtendidos: TFPanelButton
        Left = 247
        Top = 0
        Width = 80
        Height = 67
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        OnClick = FPanelButtonAtendidosClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonAtendidosItems: TFFastDesignCmpItems
          object FPanelButtonItem8: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object lblTotAtendidos: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{8FB2EAA8-D8C6-4596-B601-2CB12E8FF213}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clGreen
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem10: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem4'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{676D4144-C057-439A-9D44-7EC07ED35C18}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice123029.png'
              Flex = False
              Width = 28
              Height = 28
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem12: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Atendidos'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FPanelButtonLiberado: TFPanelButton
        Left = 327
        Top = 0
        Width = 80
        Height = 67
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 5
        OnClick = FPanelButtonLiberadoClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonLiberadoItems: TFFastDesignCmpItems
          object FPanelButtonItem13: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object lblTotLiberado: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{8FB2EAA8-D8C6-4596-B601-2CB12E8FF213}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlue
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem15: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem4'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{676D4144-C057-439A-9D44-7EC07ED35C18}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice123028.png'
              Flex = False
              Width = 28
              Height = 28
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem16: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Liberado'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FPanelButtonCancelado: TFPanelButton
        Left = 407
        Top = 0
        Width = 80
        Height = 67
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        OnClick = FPanelButtonCanceladoClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonCanceladoItems: TFFastDesignCmpItems
          object FPanelButtonItem17: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object lblTotCancelado: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{8FB2EAA8-D8C6-4596-B601-2CB12E8FF213}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clRed
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem19: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem4'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{676D4144-C057-439A-9D44-7EC07ED35C18}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice123030.png'
              Flex = False
              Width = 28
              Height = 28
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonItem20: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Cancelado'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FVBox5: TFVBox
        Left = 487
        Top = 0
        Width = 7
        Height = 62
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 7
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object borderPainel: TFBorderPanel
      Left = 0
      Top = 164
      Width = 798
      Height = 481
      Center = vboxCenter
      East = vBoxRigth
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      NorthCollapsible = False
      NorthSplittable = False
      NorthOpen = True
      SouthOpen = True
      EastOpen = False
      WestOpen = True
      SouthCollapsible = False
      SouthSplittable = False
      EastCaption = 'A'#231#245'es'
      EastCollapsible = True
      EastSplittable = True
      WestCollapsible = False
      WestSplittable = False
      WOwner = FrInterno
      WOrigem = EhNone
      NorthSizePercent = 0
      SouthSizePercent = 0
      EastSizePercent = 0
      WestSizePercent = 0
      Visible = False
      object vboxCenter: TFVBox
        Left = 2
        Top = -16
        Width = 681
        Height = 481
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object gridVendaPend: TFGrid
          Left = 0
          Top = 0
          Width = 678
          Height = 120
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'Tahoma'
          TitleFont.Style = []
          Table = tbConsVendasPendentes
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Paging.Enabled = True
          Paging.PageSize = 0
          Paging.DbPaging = False
          FrozenColumns = 0
          ShowFooter = False
          ShowHeader = True
          MultiSelection = False
          Grouping.Enabled = False
          Grouping.Expanded = False
          Grouping.ShowFooter = False
          Crosstab.Enabled = False
          Crosstab.GroupType = cgtConcat
          EnablePopup = False
          WOwner = FrInterno
          WOrigem = EhNone
          EditionEnabled = False
          AuxColumnHeaders = <>
          NoBorder = False
          ActionButtons.BtnAccept = False
          ActionButtons.BtnView = False
          ActionButtons.BtnEdit = False
          ActionButtons.BtnDelete = False
          ActionButtons.BtnInLineEdit = False
          CustomActionButtons = <>
          Columns = <
            item
              Expanded = False
              FieldName = 'COD_ITEM'
              Font = <
                item
                  Expression = '*'
                  EvalType = etExpression
                  GUID = '{C79D8A4B-9909-401A-BD03-38FD39A9FA81}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clBlue
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                end>
              Title.Caption = 'C'#243'd Item'
              Width = 145
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{1FBC8127-C2E4-41C4-A43C-A1A1614E2C1F}'
              WOwner = FrInterno
              WOrigem = EhNone
              OnClick = gridVendaPendColumns0Click
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
            end
            item
              Expanded = False
              FieldName = 'DESCRICAO'
              Font = <>
              Title.Caption = 'Descri'#231#227'o'
              Width = 150
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = True
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{B1E1A4F7-A3A9-42BB-B4B4-A420306B1477}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
            end
            item
              Expanded = False
              FieldName = 'QTDE'
              Font = <>
              Title.Caption = 'Qtde'
              Width = 55
              Visible = True
              Precision = 0
              TextAlign = taRight
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{976FAFF3-3A4F-48CD-A499-A7A2FA46E109}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
            end
            item
              Expanded = False
              FieldName = 'EST'
              Font = <>
              Title.Caption = 'Est.'
              Width = 55
              Visible = True
              Precision = 0
              TextAlign = taRight
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{6E840923-E07A-4F88-9CB0-E9CC494F8B8A}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
            end
            item
              Expanded = False
              FieldName = 'PRECO_VENDA'
              Font = <>
              Title.Caption = 'Vlr Venda'
              Width = 105
              Visible = True
              Precision = 0
              TextAlign = taRight
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <
                item
                  Expression = '*'
                  EvalType = etExpression
                  GUID = '{22E1FA5E-F00B-4B96-8910-D4E30FC69EB7}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Mask = 'R$ ,##0.00'
                  PadLength = 0
                  PadDirection = pdNone
                  MaskType = mtDecimal
                end>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{930E767A-9B58-485E-A51B-8163C47EDBF9}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
            end>
        end
        object FHBox9: TFHBox
          Left = 0
          Top = 121
          Width = 676
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hboxDetalhePrincipal: TFHBox
            Left = 0
            Top = 0
            Width = 553
            Height = 41
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object vBoxbtnViewPageControl: TFVBox
            Left = 553
            Top = 0
            Width = 85
            Height = 37
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object btnViewPageControl: TFButton
              Left = 0
              Top = 0
              Width = 84
              Height = 30
              Caption = '++ Detalhes'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 0
              OnClick = btnViewPageControlClick
              ImageId = 0
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
          end
        end
        object vboxDetalhe: TFVBox
          Left = 0
          Top = 163
          Width = 677
          Height = 245
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 6
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox5: TFHBox
            Left = 0
            Top = 0
            Width = 671
            Height = 46
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox4: TFVBox
              Left = 0
              Top = 0
              Width = 229
              Height = 41
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel1: TFLabel
                Left = 0
                Top = 0
                Width = 42
                Height = 14
                Caption = 'Cliente'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object lblCliente: TFLabel
                Left = 0
                Top = 15
                Width = 42
                Height = 14
                Caption = 'Cliente'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clSilver
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold, fsUnderline]
                ParentFont = False
                OnClick = lblClienteClick
                FieldName = 'CLIENTE'
                Table = tbConsVendasPendentes
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FVBox7: TFVBox
              Left = 229
              Top = 0
              Width = 229
              Height = 40
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel2: TFLabel
                Left = 0
                Top = 0
                Width = 26
                Height = 14
                Caption = 'Tipo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object lblOsOrc: TFLabel
                Left = 0
                Top = 15
                Width = 26
                Height = 14
                Caption = 'Tipo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clSilver
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold, fsUnderline]
                ParentFont = False
                OnClick = lblOsOrcClick
                FieldName = 'OS'
                Table = tbConsVendasPendentes
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FVBox8: TFVBox
              Left = 458
              Top = 0
              Width = 205
              Height = 42
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel3: TFLabel
                Left = 0
                Top = 0
                Width = 41
                Height = 14
                Caption = 'Status'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FLabel6: TFLabel
                Left = 0
                Top = 15
                Width = 41
                Height = 14
                Caption = 'Stauts'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clSilver
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                FieldName = 'STATUS_OS'
                Table = tbConsVendasPendentes
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
          end
          object FHBox6: TFHBox
            Left = 0
            Top = 47
            Width = 671
            Height = 62
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox9: TFVBox
              Left = 0
              Top = 0
              Width = 229
              Height = 57
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 3
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox7: TFHBox
                Left = 0
                Top = 0
                Width = 217
                Height = 48
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 6
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox13: TFHBox
                  Left = 0
                  Top = 0
                  Width = 41
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 3
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object imgWhatzap: TFImage
                    Left = 0
                    Top = 0
                    Width = 38
                    Height = 38
                    Stretch = False
                    OnClick = imgWhatzapClick
                    ImageSrc = '/images/crmservice4600177.png'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxSize = 0
                    GrayScaleOnDisable = False
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    Preview = False
                  end
                end
                object FHBox8: TFHBox
                  Left = 41
                  Top = 0
                  Width = 169
                  Height = 39
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 15
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FLabel7: TFLabel
                    Left = 0
                    Top = 0
                    Width = 41
                    Height = 14
                    Caption = 'Celular'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clSilver
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    FieldName = 'CELULAR'
                    Table = tbConsVendasPendentes
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
              end
            end
            object FVBox10: TFVBox
              Left = 229
              Top = 0
              Width = 229
              Height = 57
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 15
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FCheckBox1: TFCheckBox
                Left = 0
                Top = 0
                Width = 97
                Height = 17
                Caption = 'Liberado'
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 0
                Table = tbConsVendasPendentes
                FieldName = 'LIBERADO'
                CheckedValue = 'S'
                UncheckedValue = 'N'
                ReadOnly = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taAlignTop
              end
            end
            object FVBox11: TFVBox
              Left = 458
              Top = 0
              Width = 205
              Height = 57
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 15
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 3
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FCheckBox3: TFCheckBox
                Left = 0
                Top = 0
                Width = 145
                Height = 17
                Caption = 'Ve'#237'culo Imobilizado'
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 0
                Table = tbConsVendasPendentes
                FieldName = 'IMOBILIZADO'
                CheckedValue = 'S'
                UncheckedValue = 'N'
                ReadOnly = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taAlignTop
              end
            end
          end
          object FHBox10: TFHBox
            Left = 0
            Top = 110
            Width = 671
            Height = 130
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 3
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox13: TFVBox
              Left = 0
              Top = 0
              Width = 273
              Height = 120
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel10: TFLabel
                Left = 0
                Top = 0
                Width = 107
                Height = 14
                Caption = 'Pedido Associado'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clBlue
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FHBox11: TFHBox
                Left = 0
                Top = 15
                Width = 267
                Height = 49
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FVBox15: TFVBox
                  Left = 0
                  Top = 0
                  Width = 117
                  Height = 36
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftMin
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FLabel11: TFLabel
                    Left = 0
                    Top = 0
                    Width = 99
                    Height = 14
                    Caption = 'Num Fornecedor'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object FLabel12: TFLabel
                    Left = 0
                    Top = 15
                    Width = 99
                    Height = 14
                    Caption = 'num Fornecedor'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clSilver
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    FieldName = 'NUMERO_FORNECEDOR'
                    Table = tbConsVendasPendentes
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object FVBox16: TFVBox
                  Left = 117
                  Top = 0
                  Width = 117
                  Height = 38
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftMin
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FLabel13: TFLabel
                    Left = 0
                    Top = 0
                    Width = 78
                    Height = 14
                    Caption = 'Num Interno'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object FLabel14: TFLabel
                    Left = 0
                    Top = 15
                    Width = 78
                    Height = 14
                    Caption = 'Num Interno'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clSilver
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    FieldName = 'PEDIDO_NUMERO'
                    Table = tbConsVendasPendentes
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
              end
              object FHBox12: TFHBox
                Left = 0
                Top = 65
                Width = 267
                Height = 49
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FVBox17: TFVBox
                  Left = 0
                  Top = 0
                  Width = 117
                  Height = 37
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftMin
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FLabel15: TFLabel
                    Left = 0
                    Top = 0
                    Width = 75
                    Height = 14
                    Caption = 'Data Pedido'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object FLabel16: TFLabel
                    Left = 0
                    Top = 15
                    Width = 29
                    Height = 14
                    Caption = 'Data'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clSilver
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    FieldName = 'PEDIDO_DATA'
                    Table = tbConsVendasPendentes
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object FVBox18: TFVBox
                  Left = 117
                  Top = 0
                  Width = 117
                  Height = 39
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftMin
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FLabel17: TFLabel
                    Left = 0
                    Top = 0
                    Width = 51
                    Height = 14
                    Caption = 'Previs'#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object FLabel18: TFLabel
                    Left = 0
                    Top = 15
                    Width = 51
                    Height = 14
                    Caption = 'Previs'#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clSilver
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    FieldName = 'PEDIDO_PREVISAO'
                    Table = tbConsVendasPendentes
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
              end
            end
            object FVBox14: TFVBox
              Left = 273
              Top = 0
              Width = 273
              Height = 119
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 3
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel8: TFLabel
                Left = 0
                Top = 0
                Width = 70
                Height = 14
                Caption = 'Observa'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FMemo1: TFMemo
                Left = 0
                Top = 15
                Width = 185
                Height = 89
                CharCase = ccNormal
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Lines.Strings = (
                  'FMemo1')
                Maxlength = 0
                ParentFont = False
                ReadOnly = True
                TabOrder = 0
                FieldName = 'OBSERVACAO'
                Table = tbConsVendasPendentes
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                WOwner = FrInterno
                WOrigem = EhNone
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                Required = False
              end
            end
          end
        end
      end
      object vBoxRigth: TFVBox
        Left = 693
        Top = 1
        Width = 104
        Height = 479
        Align = alRight
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 5
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox6: TFVBox
          Left = 0
          Top = 0
          Width = 95
          Height = 4
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object btnLiberar: TFButton
          Left = 0
          Top = 5
          Width = 92
          Height = 52
          Hint = 'Pre'#231'os'
          Caption = 'Liberar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 1
          OnClick = btnLiberarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000004CD4944415478DAAD960D4C95551880CFF97E2F5CB8DE3F7E244A2889
            643AB01096B6D94A5B203F19618D903257832B95E16696CDB0368BE922931089
            D96C6DACD2282EE4CAC0722AE32F9912B8C1C4BAB708BADCFBDD7BF5FE7FDFE9
            9C0B1F5EDA05DDF4DDCE76DEF77CE73CEF79DFF79CF341B08064BFFA43220888
            8500820D08807BB129B177F83725433322CF71569EE5BB382EE2BDF16F5EEE9F
            6F0D18CEB8A6F2FB04BF44EF01006DC52AC21FFD2A21344841F0D7A8F9EA2312
            92E27D017FAACBE352E361A052AA4694BCA6C02FB9CA2140F6C996CA77E70564
            56B4E6501036E3AE1F4FDEE7E3C4CF073EDE28847324B1A821C513F0D50A4E47
            2ED1299A12218492F7A7B7F8B080AC0A6339B6D4E16EAB18506CE96F5C6F07B7
            20FAA7EABEB208964DB2BE581F5F347EBCFCDB398019CF8D38D675BDB1FD55A0
            BA5A92C7FAFA106B8FB77E0125A01F1DB73E48D1B4330E28D2F233135C32C0E6
            108A4529105C4FA3D29CB1B5BEBE7616301D736A08773B7B62FB9F095D9CC869
            F3542242C044FA97AEFE1BB4C54545663DB7F29E5EF91BEDF35FAA1897B0C3EB
            F795B00C77CAF25DA561169065686BC0F12EC261592A87A5D364795AF28BE700
            C3DE4F41F42136AD0E052838C6A95470BB24E03B030153B6393D6967D82A22A5
            8844710C0376F6D417D412638779AA1822F035EE3A718BC48D9627C800398334
            84489410D4442B6ACB3292AA8839BEB871AD60B79ED429B51930CB60ACC4B68F
            3804E3CE1ECEB39D3799223C307208575F52388FE6004284C515A4D6A1F892D4
            54CBDDCF362D374D982FC5A8F54709A01D8FF33DF5F9EB82F1364D6DC5896E0A
            5F2FC83BF88785C7F9082BEA28FED80B2B935F24FD88276B1C0CA49D043082B7
            DADAF369FE0E32F0F3958938C8D24D10C0BC39B144A8C1EDD56DB74E59791FEB
            E872BABC69A1E34A9E9DE078B4AE2C7DE9601096577BC1EDF3A41180139FBEBD
            DDF50507E48F3BFE9C7A0242F063C87C8FC7A355E7A6402F515A2E8CA94DD7BD
            B6D09DE854FCDBA5E9C91FCC9E8DC24FDA6C4E7B2E0138F00EDEC73BD87F2701
            DAC243ED7687904300971142277B0F17BCB1508870D28F40D1B15D1080421051
            97C3ED7BE026211AC0215A46002DE4DCE024AFBE934956E6ECB741487961F6B6
            B697F00E3E83AC94D07DB070E2F4D89802B1AAE1DB2BD323F79927FF19D5456B
            9AE143AF18F534037E0114CAEFA92B180BE6E0360F1A49F094C3BA215E7F5766
            D8F7200831593622BF789EA6D91444AE0A08D68402F055E15045726FFA25DF59
            88D8D2CD194B76113BF1FE6FCBE44874A4724830562D9F05ACAA682BC50FCAE3
            084A27AC14736AF450AE571EBB95CB2E288F56335191D166B7C71DBB58A3CD30
            9F305CBC01D8D6B6171FA63D33AA8000DADD5B5F504F14725D3BE2ACC7705E62
            C8754DD3B483D2C1655B92933DA18BABA317F5D99DF6F4587DECEE89E3867D33
            519C96872BDB57889274512E170CA89401371312169BF3DAB9EB2E679C4EA56D
            B6B4BE561292A61B926D300E230015F85E504D63408D180047FB1BF32DF32DEC
            F6780FDAAED973C9CBADD76ADF913D0F0B586530E6E3574DE9A3A80E46126B70
            259562337E7C50375EA01B83C7AF98C71E0B4852A25FF42F7179DC1AE2C5A228
            D5EF519CA284C4FCFF4ECC5B4544C84B1740F4267C4ED66375056E317D970778
            9661FC2CCD080A2EA293E5F8030BFDB6FC07B5B963861F01C540000000004945
            4E44AE426082}
          ImageId = 123034
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnExcluir: TFButton
          Left = 0
          Top = 58
          Width = 92
          Height = 52
          Hint = 'Excluir'
          Caption = 'Excluir'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 2
          OnClick = btnExcluirClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F80000010B4944415478DA6364A031601CB5805A16E800712210F340F95F8078
            1E105FA586055C407C1F886F00F14BA89838106B00B122107F23C7026E206683
            B2A380B81D8835910CE304E29B405C0EC4CBA162BF80F82BB116BC0762012283
            0F598F10B1166801F114207E0DC45D040C2E03625120CE01E26BC45A0002EB80
            F82910E712B06032104B0371103649622CE801E29340AC0DC4D540FC9F011227
            5780D81C884B28B5602A105F076219206E83CA5501F1130648C4678F5A30F42D
            00E556506E5E04C42650B933401C07C4CB80B8935C0BD600F11B20CE60C00F66
            3040727018A916543240D2F83902161801710B10F7936A013B0324FB8B12B000
            54C28272F31F522DA00A18B5802000008C474A19256DF6A20000000049454E44
            AE426082}
          ImageId = 700095
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnImprimir: TFButton
          Left = 0
          Top = 111
          Width = 92
          Height = 52
          Hint = 'Imprimir Autoriza'#231#227'o'
          Caption = 'Autoriza'#231#227'o'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 3
          OnClick = btnImprimirClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000003404944415478DAAD95CB4F53511087E79EB6B78F5BA0BAD38498A009
            24929010FF0711058DC488516334A98647C1F252C22B3C0A9B225D080BBB51E3
            06E3838A41E34A36AEDC98A0092CDCB122318A7DB7F71C67EEED6D6A689B8B38
            37CDC99CDBCE777E3373A61294B0FEFE7E01FBB0F9F979A9D8BE54EA07040806
            83A6820F0C0CFC3B4055D5B2C12D16CBC100A9548AA3CBF053B81AC6EC76FBC1
            00C964B22CC0E1701C0CF05F6AD0DBDB2B5BADD624F912EEACC78E6C4A99D809
            C5A5309573514E019A4826E2F09BCB5B673C3BB5B9D613A8AC32100844A562A7
            3D76631ACE9E3B0DC944C208561240BEDD6197D63F7E92BE2DF5ED512551F0B1
            B131A8AAAA02CE553C92051ABA83E0EDB80A897822779E9C5663352CE7531D9E
            2FAFC2FBFBD74071BBB4CD783C0EE3E3E3A001666767492B4763B22CF3C6BB0F
            A0A7EF168BEE46B99E07095F0A6EAC06CCF09D8A139E3D596111FF25EE4680CA
            05C394C3E0E0A00E989A9A029BCD86F1556C3B076FF42F40B7FF268BE7004447
            793CBF4AF8087C72BE0678BAC25EFBDAB8BBC20558352684809191111D405208
            2050814B71F0065F08BC77DA19A6C85C0D9C0E78B1BCC65EF92FF20A45D100F4
            9252AF014647478124D1E12A2B5D70F4CA345C6E6F854C2A6DAA4D6DB20C2B91
            0FF065A1032C18874A430A26272775C0F0F0305D79AD0687DC2E3EF9F82D3C7C
            F719D3259B5290CE64E1C2A9E32C7CEF3AFFF1334ABDCEA8DF67666674C0D0D0
            503EBD64872B15B0CA56BD4BCC18165D6454D8F915D55D0C4EF1E6E6E67480DF
            EFD7BA08071BF3783C3C93CD924696C9644C29A0F44A18C08A59D8DDDDCD2BC0
            7BA0037C3E9F0650DC6E361B0850703A86C9E3FF6D584F82D00D875028A401D4
            969616565757074EA713A865BF6E6C4073F379F0F574980ABAB41486D5372FE1
            647D3D4C4C4C68976C6B6B0B2291083746056F6A6A92AAABAB211C0E6B8096D6
            36E8ECF4F2582C5636458AA270043003E0F57A617B7B1BD6D6D604A688E5F340
            4A203708102019001CD7650138260A01465B50700B4091714D2004B01C00D2E9
            F27701474B618AB811B8A0C1F60268BFA6A6564280C866B365C73576904080F4
            FDFBA6283C794980618B8B8F0429305BE4AEAEDBFBFB47430055D7658A009040
            40D1EFFE01F10DC796BECD94CB0000000049454E44AE426082}
          ImageId = 11003
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
      end
    end
  end
  object imageList: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 600
    Top = 82
    object pecasLiberadas: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'pecasLiberadas'
      ImageIndex = 123028
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{CC326C6A-1B69-4BC9-943A-04E27D8E40A6}'
    end
    object FMenuItem2: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'PecasAtendidas'
      ImageIndex = 123029
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{4CCAEBA8-D089-42EA-9967-C301C4302087}'
    end
    object FMenuItem1: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'PecasCanceladas'
      ImageIndex = 123030
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{3B94FF35-122C-44A2-B14B-7B7677257E6B}'
    end
    object FMenuItem3: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'TodasPecas'
      ImageIndex = 123031
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{F983383D-8356-46C0-8CBD-04B2FEB9BB69}'
    end
    object FMenuItem4: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'PecasBranca'
      ImageIndex = 123032
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{472E8054-D8EF-463A-8E69-405294D918DF}'
    end
    object FMenuItem5: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'PecasSemPedido'
      ImageIndex = 123033
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{99115DF6-0171-4602-AE66-1F76AD5E84D1}'
    end
    object FMenuItem6: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'PecasComPedido'
      ImageIndex = 4600437
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{2E4709E5-B539-4ECF-AE37-E921BC564558}'
    end
    object PecasLiberar: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'PecasLiberar'
      ImageIndex = 123034
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{97AB716F-BD97-4725-AB24-6808D5D440E9}'
    end
    object FMenuItem7: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'whatzap'
      ImageIndex = 4600177
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{3405F6D8-603D-4478-AF7C-67660FC07303}'
    end
  end
  object FCoachmark: TFCoachmark
    ShowNextButton = False
    ShowPriorButton = False
    Items = <
      item
        TargetName = 'btnExcluir'
        Position = poBefore_start
        Name = 'btnExcluir'
      end
      item
        TargetName = 'btnLiberar'
        Position = poBefore_start
        Name = 'btnLiberar'
      end>
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 714
    Top = 26
  end
  object tbConsultaComboEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_COMBO_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123032;12301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsVendasPendentes: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Original'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PEDIDO_NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Interno'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PEDIDO_DATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Pedido Data'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PEDIDO_PREVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Previs'#227'o Chegada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMOBILIZADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Imobilizado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_LIBERACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Libera'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_LIBEROU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Liberou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO_LIBERACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Motivo Libera'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIAS_LIBERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dias Liberado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_CANCELAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Cancelamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_CANCELOU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Cancelou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO_CANCELAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Motivo Cancelamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTROLE_VENDA_PENDENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Controle Venda Pendente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_RESERVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Reserva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSULTOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consultor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOC_RESERVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Reserva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_RESERVADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Reservada'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONS_VENDAS_PENDENTES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123032;12302'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbContaVendasPendentes: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONS_VENDAS_PENDENTES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123032;12303'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEventos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EVENTOS'
    Cursor = 'CRM_EVENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123032;12304'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
