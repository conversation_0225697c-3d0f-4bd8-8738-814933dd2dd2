object FrmAssinaturaDigitalCentral: TFForm
  Left = 321
  Top = 163
  ActiveControl = ftEmpresa
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Assinatura Digital'
  ClientHeight = 466
  ClientWidth = 845
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '455010'
  ShortcutKeys = <>
  InterfaceRN = 'AssinaturaDigitalCentralRN'
  Access = False
  ChangedProp = 
    'FrmAssinaturaDigitalCentral.Width;'#13#10'FrmAssinaturaDigitalCentral.' +
    'Height;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 845
    Height = 466
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 8
    Padding.Left = 8
    Padding.Right = 8
    Padding.Bottom = 8
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 8
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hboxTopo: TFHBox
      Left = 0
      Top = 0
      Width = 779
      Height = 137
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox28: TFVBox
        Left = 0
        Top = 0
        Width = 633
        Height = 130
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 8
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object ftLinha1: TFHBox
          Left = 0
          Top = 0
          Width = 538
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 4
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object ftEmpresa: TFCombo
            Left = 0
            Top = 0
            Width = 246
            Height = 21
            LookupTable = tbLeadsEmpresasUsuarios
            LookupKey = 'COD_EMPRESA'
            LookupDesc = 'CODIGO_NOME_APELIDO'
            Flex = True
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Empresa'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = False
            UseClearButton = False
            HideClearButtonOnNullValue = False
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
          object ftPeriodo: TFCombo
            Left = 246
            Top = 0
            Width = 241
            Height = 21
            Flex = True
            ListOptions = 
              'Enviadas Hoje=HOJE;Enviadas Ontem=ONTEM;Esta Semana=SEMANA;Este ' +
              'M'#234's=MES;M'#234's Anterior=MES_ANTERIOR;Selecionar Per'#237'odo=PERIODO'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Periodo'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = False
            UseClearButton = False
            HideClearButtonOnNullValue = False
            OnChange = ftPeriodoChange
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object ftLinha3: TFHBox
          Left = 0
          Top = 42
          Width = 538
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 4
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object ftTipoAssinatura: TFCombo
            Left = 0
            Top = 0
            Width = 487
            Height = 21
            LookupTable = tbAssinaturaDigital
            LookupKey = 'TIPO_ASSINATURA'
            LookupDesc = 'DESCRICAO'
            Flex = True
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Tipo Assinatura'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = True
            HideClearButtonOnNullValue = False
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object ftLinha2: TFHBox
          Left = 0
          Top = 84
          Width = 538
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 4
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object ftNomeOuDocumento: TFString
            Left = 0
            Top = 0
            Width = 489
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Nome, Documento'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccNormal
            Pwd = False
            Maxlength = 0
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
        end
      end
      object FVBox1: TFVBox
        Left = 633
        Top = 0
        Width = 57
        Height = 129
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 8
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object btnPesquisar: TFButton
          Left = 0
          Top = 0
          Width = 50
          Height = 54
          Hint = 'Pesquisar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          OnClick = btnPesquisarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000000244944415478DA63FC0F040C34048CA3168C5A306AC1A805A3168C5A
            306AC1A80543C302008F805FB9E6FF08BA0000000049454E44AE426082}
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconClass = 'fas fa-search'
          IconReverseDirection = False
        end
        object btnExportarExcel: TFButton
          Left = 0
          Top = 55
          Width = 50
          Height = 54
          Hint = 'Exportar Excel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          OnClick = btnExportarExcelClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000000244944415478DA63FC0F040C34048CA3168C5A306AC1A805A3168C5A
            306AC1A80543C302008F805FB9E6FF08BA0000000049454E44AE426082}
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconClass = 'fal fa-table'
          IconReverseDirection = False
        end
      end
    end
    object FHBox2: TFHBox
      Left = 0
      Top = 138
      Width = 779
      Height = 99
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 2
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 7
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox3: TFHBox
        Left = 0
        Top = 0
        Width = 9
        Height = 68
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object FPanelButtonTodas: TFPanelButton
        Left = 9
        Top = 0
        Width = 70
        Height = 80
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        OnClick = FPanelButtonTodasClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonTodasItems: TFFastDesignCmpItems
          object FPanelButtonTodasVbox: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object FPanelButtonTodasLblTotal: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlack
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonTodasImg: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem2'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{082B6F01-28F2-4D2D-BD84-62F6AB6C5D69}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice474010.png'
              Flex = False
              Width = 25
              Height = 25
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonTodasLblDescricao: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Todos'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{33C33A83-AD85-4C78-9934-0202A35F4E0C}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaBottom
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FPanelButtonAguardando: TFPanelButton
        Left = 79
        Top = 0
        Width = 70
        Height = 80
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        OnClick = FPanelButtonAguardandoClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonAguardandoItems: TFFastDesignCmpItems
          object FPanelButtonAguardandoVbox: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object FPanelButtonAguardandoLblTotal: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlack
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonAguardandoImg: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem2'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{082B6F01-28F2-4D2D-BD84-62F6AB6C5D69}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice47409.png'
              Flex = False
              Width = 25
              Height = 25
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonAguardandoLblDescricao: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Aguardando'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{33C33A83-AD85-4C78-9934-0202A35F4E0C}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaBottom
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FPanelButtonAssinadas: TFPanelButton
        Left = 149
        Top = 0
        Width = 70
        Height = 80
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        OnClick = FPanelButtonAssinadasClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonAssinadasItems: TFFastDesignCmpItems
          object FPanelButtonAssinadasVbox: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object FPanelButtonAssinadasLblTotal: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clGreen
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonAssinadasImg: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem2'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{082B6F01-28F2-4D2D-BD84-62F6AB6C5D69}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice474011.png'
              Flex = False
              Width = 25
              Height = 25
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonAssinadasLblDescricao: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Assinados'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{33C33A83-AD85-4C78-9934-0202A35F4E0C}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaBottom
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FPanelButtonCanceladas: TFPanelButton
        Left = 219
        Top = 0
        Width = 70
        Height = 80
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        OnClick = FPanelButtonCanceladasClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonCanceladasItems: TFFastDesignCmpItems
          object FPanelButtonCanceladasVbox: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object FPanelButtonCanceladasLblTotal: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clRed
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonCanceladasImg: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem2'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{082B6F01-28F2-4D2D-BD84-62F6AB6C5D69}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice474013.png'
              Flex = False
              Width = 25
              Height = 25
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonCanceladasLblDescricao: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Cancelados'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{33C33A83-AD85-4C78-9934-0202A35F4E0C}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaBottom
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = True
            end
          end
        end
      end
      object FPanelButtonExpiradas: TFPanelButton
        Left = 289
        Top = 0
        Width = 70
        Height = 80
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 5
        OnClick = FPanelButtonExpiradasClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonExpiradasItems: TFFastDesignCmpItems
          object FPanelButtonItem1: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object FPanelButtonExpiradasLblTotal: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clRed
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonExpiradasImage: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem2'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{082B6F01-28F2-4D2D-BD84-62F6AB6C5D69}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice553026.png'
              Flex = False
              Width = 25
              Height = 25
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonExpiradasLblDescricao: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Expirados'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{33C33A83-AD85-4C78-9934-0202A35F4E0C}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaBottom
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FPanelButtonFalhas: TFPanelButton
        Left = 359
        Top = 0
        Width = 70
        Height = 80
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stBoxShadow
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        OnClick = FPanelButtonFalhasClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Toggle = True
        ToggleColor = clSilver
        WOwner = FrInterno
        WOrigem = EhNone
        object FPanelButtonFalhasItems: TFFastDesignCmpItems
          object FPanelButtonFalhasVbox: TFPanelButtonItem
            AutoHotkeys = maManual
            Caption = 'FPanelButtonItem1'
            WOwner = FrInterno
            WOrigem = EhNone
            GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
            ItemType = itVBox
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ItemAlign = iaLeft
            Flex = False
            ItemVAlign = ivaTop
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BorderStyle = stNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            BevelInner = bvNone
            BevelKind = bkTile
            BevelOuter = bvNone
            Color = clBtnFace
            WordBreak = False
            object FPanelButtonFalhasLblTotal: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = '0'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clRed
              Font.Height = -15
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonFalhasImg: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem2'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{082B6F01-28F2-4D2D-BD84-62F6AB6C5D69}'
              ItemType = itImage
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              ImageSrc = '/images/crmservice474012.png'
              Flex = False
              Width = 25
              Height = 25
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
            object FPanelButtonFalhasLblDescricao: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'Falhas'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{33C33A83-AD85-4C78-9934-0202A35F4E0C}'
              ItemType = itLabel
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = False
              ItemVAlign = ivaBottom
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
            end
          end
        end
      end
      object FHBox4: TFHBox
        Left = 429
        Top = 0
        Width = 9
        Height = 68
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 7
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object gridAssinaturas: TFGrid
      Left = 0
      Top = 238
      Width = 568
      Height = 184
      TabOrder = 2
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Table = tbCentralAssinaturaGrid
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Paging.Enabled = True
      Paging.PageSize = 9
      Paging.DbPaging = True
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = True
      MultiSelection = False
      Grouping.Enabled = False
      Grouping.Expanded = False
      Grouping.ShowFooter = False
      Crosstab.Enabled = False
      Crosstab.GroupType = cgtConcat
      EnablePopup = False
      WOwner = FrInterno
      WOrigem = EhNone
      EditionEnabled = False
      AuxColumnHeaders = <>
      NoBorder = False
      ActionButtons.BtnAccept = False
      ActionButtons.BtnView = False
      ActionButtons.BtnEdit = False
      ActionButtons.BtnDelete = False
      ActionButtons.BtnInLineEdit = False
      CustomActionButtons = <>
      ActionColumn.Title = 'A'#231#245'es'
      ActionColumn.Width = 100
      ActionColumn.TextAlign = taCenter
      ActionColumn.Visible = True
      Columns = <
        item
          Expanded = False
          FieldName = 'DESCRICAO'
          Font = <>
          Title.Caption = 'Tipo'
          Width = 106
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 30
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{97AEA2BC-1F9F-45D9-8C38-80DFBE24E560}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'DOCUMENTO'
          Font = <>
          Title.Caption = 'Documento'
          Width = 88
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 40
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{387993B1-E8C0-4EBD-80C9-6372C8C11B15}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'DATA_ENVIO'
          Font = <>
          Title.Caption = 'Data'
          Width = 102
          Visible = True
          Precision = 0
          TextAlign = taCenter
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <
            item
              Expression = '*'
              EvalType = etExpression
              GUID = '{A1D9BBA9-C75C-4520-A030-9459B176E905}'
              WOwner = FrInterno
              WOrigem = EhNone
              Mask = 'dd/MM/yyyy'
              PadLength = 0
              PadDirection = pdNone
              MaskType = mtDateTime
            end>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{FA4F74D5-687A-4567-B8E9-F411B1C9907E}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'DESC_STATUS_LABEL'
          Font = <>
          Title.Caption = 'Status'
          Width = 84
          Visible = True
          Precision = 0
          TextAlign = taCenter
          FieldType = ftString
          FlexRatio = 20
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{2A6BF9A3-B655-44A0-8F85-F23570C105AC}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'ABRIR_ASSINATURA'
          Font = <>
          Title.Caption = ' '
          Width = 46
          Visible = True
          Precision = 0
          TextAlign = taCenter
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <
            item
              Expression = '*'
              EvalType = etExpression
              GUID = '{EA853BC7-28E5-4AC8-8097-8690857B274C}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 0
              OnClick = 'AbrirAssinatura'
              IconClass = 'i fas fa-file-signature fa-lg'
              Color = clBlack
            end>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = False
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{238D3DD4-990E-4ECC-BEF6-31449B31D75B}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end>
    end
  end
  object listaimagens: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 724
    Top = 6
    object imgAssinaturaAguardando: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'imgAssinaturaAguardando'
      ImageIndex = 47409
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{29EADE5A-8EA7-449B-9332-1AEC1CC53DB4}'
    end
    object imgAssinaturaAssinado: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'imgAssinaturaAssinado'
      ImageIndex = 474011
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{5C41E19A-941E-4667-AEFB-D3B7AA0226BF}'
    end
    object imgAssinaturaCancelado: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'imgAssinaturaCancelado'
      ImageIndex = 474013
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{70924F0C-76A5-4C16-B977-D0B483E5AF6C}'
    end
    object imgAssinaturaFalha: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'imgAssinaturaFalha'
      ImageIndex = 474012
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{5299B8E4-75B0-43B9-A54C-17DEFD9F05D3}'
    end
    object imgAssinaturaTodos: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'imgAssinaturaTodos'
      ImageIndex = 474010
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{951EDD37-418B-4C33-B078-E119903C6209}'
    end
    object imgAssinaturaExpiradas: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'imgAssinaturaExpiradas'
      ImageIndex = 553026
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{12F2D947-241E-4841-AF38-A69B35EDEBDB}'
    end
  end
  object tbCentralAssinaturaGrid: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{D742A643-8D0C-4480-83B4-D4C5F808652B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{0A4C6672-7A0E-4F55-8B36-BD805F988F8A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Codigo'
        GUID = '{0384C577-A3A9-44E4-BE75-C636F3F224A5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Codigo'
        GUID = '{77DD3E8D-2946-45F6-8C6F-67E2511EAB73}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Status'
        GUID = '{182C025A-46F8-4DFD-BF66-93C469E0ABEC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENVIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Envio'
        GUID = '{967BF24C-53EC-4D24-94FB-015FB5DC30FD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ABRIR_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Abrir Assinatura'
        GUID = '{EBBD70C6-423D-443D-BE95-EA28C28A4B78}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento'
        GUID = '{56A94221-8D8E-4527-BC4B-9C4AFE35E796}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{C8663D7A-6757-4D56-86B5-A3B553E826BA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_STATUS_LABEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Status Label'
        GUID = '{6847E651-41EE-4351-9842-59ACE32A6525}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CENTRAL_ASSINATURA_GRID'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '455010;47401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{C2A10109-8365-46CC-9C5D-B434BBC21CF6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        GUID = '{2EF20D39-E615-4AEA-88B7-CD2635EE5FE4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_NOME_APELIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Nome Apelido'
        GUID = '{C68ADE7A-3890-434A-B36A-61475FCB7E19}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '455010;47402'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbAssinaturaDigital: TFTable
    FieldDefs = <
      item
        Name = 'ID_ASSINATURA_DIGITAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Assinatura Digital'
        GUID = '{11D6EC9E-AE5B-4FEA-86FB-4A995791C9C3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{20907F0B-31F1-473F-8978-D7196CED5D1F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{E8544464-ADAF-4D17-A5EF-4AD8077A1264}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_ASSINATURA_DIGITAL'
    Cursor = 'CRM_ASSINATURA_DIGITAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '455010;47403'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
