object AgendaProgramacaoRepAutomaticaRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '466012'
  Height = 299
  Width = 442
  object tbReprogramacaoAutoServico: TFTable
    FieldDefs = <
      item
        Name = 'DATA_PROMETIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Prometida'
        GUID = '{11ECE20C-1225-4E58-90E3-865CABEB93F1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{E9CB12CD-8B18-4450-923C-40257B059DAF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{414288F6-F999-4765-97D5-445012241BDF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{F15190C9-393D-4D6A-86B5-D9763034879C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{2B6FA04C-82D8-4BDF-9E0C-F975ECE0A360}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        GUID = '{144A6963-E0CE-45D6-A796-4F23E6036E00}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROMESSA_ATRASADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Promessa Atrasada'
        GUID = '{F7ED9F5A-FA95-426B-A18F-BA26CB05F977}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_TECNICO_NOVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Tecnico Novo'
        GUID = '{1E6968FF-13BA-48C0-9736-BD3F6EABEDB2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOVO_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Novo Produtivo'
        GUID = '{7BD37CC1-E980-410E-8DDA-419EE4872EC2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOVA_DATA_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nova Data Agenda'
        GUID = '{4C64277D-4257-4B78-B9B4-3652F1DF092B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHIP_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chip Servi'#231'o'
        GUID = '{339B89A7-CA29-4538-B861-36D2DFC7C094}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_COMECA_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Comeca Servi'#231'o'
        GUID = '{33BD4BF5-9A61-4736-BB51-BC1C3F3A30D2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PRODUTIVO_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Produtivo Agenda'
        GUID = '{C67E8966-A3E4-44DD-94E6-D983BA04331B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PRODUTIVO_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produtivo Agenda'
        GUID = '{B77BBFA8-B6C9-4F09-AC83-A2631E03F7D2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item Agenda'
        GUID = '{F0C1180F-7324-462A-A2C8-922E188811A3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHIP_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chip Os'
        GUID = '{C1E4B8A4-7086-43DC-8C18-CD86E90A6619}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREVISAO_ENTREGA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Previs'#227'o Entrega'
        GUID = '{CA669971-B076-460E-A6B2-0B5EBDB7615C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OS_AGENDA_TEMP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Os Agenda Temp'
        GUID = '{B9E27F16-76B4-4967-8DFC-04355369DB4E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PADRAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Padr'#227'o'
        GUID = '{92F85C71-2586-43E7-A6CC-2D990528EF7C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOVO_TEMPO_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Novo Tempo Agenda'
        GUID = '{F4313B67-5C8E-4AEF-9C3B-57586DF020CC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REPROGRAMACAO_AUTO_SERVICO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466012;46601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaComboProdutivo: TFTable
    FieldDefs = <
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        GUID = '{1556D9B2-D8E9-4A86-80EF-DC3199D0B664}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{FB146D80-957F-44F5-81EA-8D5FDE61F62B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_COMBO_PRODUTIVO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466012;46603'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNumerosOsComboTemp: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_OS'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'NUMERO_OS'
        GUID = '{993F835F-88CE-4776-A3EB-D56FC19B806F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DUAL'
    Cursor = 'DUAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466012;46604'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDataFormatPadrao: TFTable
    FieldDefs = <
      item
        Name = 'DATA_FORMAT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Format'
        GUID = '{B607396D-34FF-441B-B46C-60F043491DEB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GET_DATA_FORMAT_PADRAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466012;46605'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbUltimaHoraServicoAgenda: TFTable
    FieldDefs = <
      item
        Name = 'ULTIMA_HORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #218'ltima Hora'
        GUID = '{4A12F623-5BD5-40A8-9C55-CFEFE701EE14}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GET_ULTIMA_HORA_SERVICO_AGENDA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466012;46606'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
