object CadastroEventoClienteAvulsoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '45407'
  Left = 44
  Top = 163
  Height = 299
  Width = 442
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{6F96EFE0-634A-4F0A-B9D9-6C5A17743CE3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        GUID = '{8C1D19F8-AAF3-421F-811F-7C385FF1C8B0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45407;45401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEventos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{C8305FBB-29E2-46EC-AEEB-475DD9D31B34}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        GUID = '{CB70A6CE-59C6-4F72-8278-6C624025B2FE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EVENTOS'
    Cursor = 'CRM_EVENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45407;45402'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEventosTipo: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        GUID = '{30E68653-EAF6-4A73-9618-7CC63A9E3D8F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EVENTOS_TIPO'
    Cursor = 'CRM_EVENTOS_TIPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45407;45403'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTimeMembro: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        GUID = '{4F7DF992-F6FC-4758-8663-90429D750CE7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{E2DB0D29-9E85-4631-B6F2-0D6539C5989A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{586B0070-5171-41AF-9018-DC29987B21D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIME_MEMBRO'
    Cursor = 'CRM_TIME_MEMBRO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45407;45404'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
