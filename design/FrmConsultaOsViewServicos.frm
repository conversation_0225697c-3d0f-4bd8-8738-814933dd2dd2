object FrmConsultaOsViewServicos: TFForm
  Left = 320
  Top = 162
  ActiveControl = vBoxTela
  Caption = 'Servi'#231'os'
  ClientHeight = 749
  ClientWidth = 524
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600263'
  ShortcutKeys = <>
  InterfaceRN = 'ConsultaOsViewServicosRN'
  Access = False
  ChangedProp = 
    'FrmConsultaOsViewServicos.Width;'#13#10'FrmConsultaOsViewServicos.Heig' +
    'ht;'#13#10#13#10'FrmConsultaOsViewServicos.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxTela: TFVBox
    Left = 0
    Top = 0
    Width = 524
    Height = 749
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox33: TFHBox
      Left = 0
      Top = 0
      Width = 634
      Height = 68
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Color = 15724527
      Padding.Top = 5
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      ParentBackground = False
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FVBox10: TFVBox
        Left = 60
        Top = 0
        Width = 2
        Height = 46
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object vBoxExpadColapseTreeView: TFVBox
        Left = 62
        Top = 0
        Width = 55
        Height = 60
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 7
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox1: TFHBox
          Left = 0
          Top = 0
          Width = 51
          Height = 55
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hBoxExpadColapseTreeView: TFHBox
            Left = 0
            Top = 0
            Width = 46
            Height = 50
            Hint = 'Expandir/Recolher Lista'
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 12
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            OnClick = hBoxExpadColapseTreeViewClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox128: TFHBox
              Left = 0
              Top = 0
              Width = 5
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object imgExpandTreeView: TFImage
              Left = 5
              Top = 0
              Width = 32
              Height = 32
              Stretch = False
              ImageSrc = '/images/crmservice4600246.png'
              WOwner = FrInterno
              WOrigem = EhNone
              BoxSize = 0
              GrayScaleOnDisable = False
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FHBox129: TFHBox
              Left = 37
              Top = 0
              Width = 5
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
        end
      end
      object FVBox2: TFVBox
        Left = 117
        Top = 0
        Width = 7
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object FVBox48: TFVBox
        Left = 124
        Top = 0
        Width = 143
        Height = 60
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 7
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftMin
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox21: TFHBox
          Left = 0
          Top = 0
          Width = 62
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox49: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblTitNumeroOS: TFLabel
            Left = 8
            Top = 0
            Width = 47
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = 'N'#250'mero'
            Font.Charset = ANSI_CHARSET
            Font.Color = clBlack
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
        object FHBox22: TFHBox
          Left = 0
          Top = 21
          Width = 62
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox52: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblNumeroOS: TFLabel
            Left = 8
            Top = 0
            Width = 32
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = '1111'
            Color = clBtnFace
            Font.Charset = ANSI_CHARSET
            Font.Color = 13392431
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentColor = False
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
        object FHBox18: TFHBox
          Left = 0
          Top = 42
          Width = 62
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Visible = False
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox42: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblTitTipoOS: TFLabel
            Left = 8
            Top = 0
            Width = 26
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = 'Tipo'
            Font.Charset = ANSI_CHARSET
            Font.Color = clBlack
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
        object FHBox19: TFHBox
          Left = 0
          Top = 63
          Width = 152
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Visible = False
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox43: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblTipoOS: TFLabel
            Left = 8
            Top = 0
            Width = 136
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = 'V1 - Ordem de Servi'#231'o'
            Color = clBtnFace
            Font.Charset = ANSI_CHARSET
            Font.Color = 13392431
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentColor = False
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
      end
      object FVBox3: TFVBox
        Left = 267
        Top = 0
        Width = 7
        Height = 53
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 5
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object FVBox64: TFVBox
        Left = 274
        Top = 0
        Width = 80
        Height = 60
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 7
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftMin
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox20: TFHBox
          Left = 0
          Top = 0
          Width = 75
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox65: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblTitStatusOS: TFLabel
            Left = 8
            Top = 0
            Width = 41
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = 'Status'
            Font.Charset = ANSI_CHARSET
            Font.Color = clBlack
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
        object FHBox25: TFHBox
          Left = 0
          Top = 21
          Width = 75
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox66: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblStatusOS: TFLabel
            Left = 8
            Top = 0
            Width = 32
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = '1111'
            Color = clBtnFace
            Font.Charset = ANSI_CHARSET
            Font.Color = 13392431
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentColor = False
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
        object FHBox27: TFHBox
          Left = 0
          Top = 42
          Width = 144
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Visible = False
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox69: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblTitPromessaOS: TFLabel
            Left = 8
            Top = 0
            Width = 129
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = 'Prometido ao Cliente'
            Font.Charset = ANSI_CHARSET
            Font.Color = clBlack
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
        object FHBox28: TFHBox
          Left = 0
          Top = 63
          Width = 144
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Visible = False
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox70: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblPromessaOS: TFLabel
            Left = 8
            Top = 0
            Width = 32
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = '1111'
            Color = clBtnFace
            Font.Charset = ANSI_CHARSET
            Font.Color = clRed
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentColor = False
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
      end
      object FVBox21: TFVBox
        Left = 354
        Top = 0
        Width = 7
        Height = 46
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 7
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object FVBox146: TFVBox
        Left = 361
        Top = 0
        Width = 144
        Height = 60
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 7
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 8
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 0
        BoxShadowConfig.VerticalLength = 7
        BoxShadowConfig.BlurRadius = 10
        BoxShadowConfig.SpreadRadius = -5
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox124: TFHBox
          Left = 0
          Top = 0
          Width = 144
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox149: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FLabel51: TFLabel
            Left = 8
            Top = 0
            Width = 72
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = 'Andamento'
            Font.Charset = ANSI_CHARSET
            Font.Color = clBlack
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
        object FHBox126: TFHBox
          Left = 0
          Top = 21
          Width = 144
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox153: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 14
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblAndamento: TFLabel
            Left = 8
            Top = 0
            Width = 32
            Height = 14
            Align = alTop
            Alignment = taCenter
            Caption = '1111'
            Color = clBtnFace
            Font.Charset = ANSI_CHARSET
            Font.Color = 13392431
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentColor = False
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
      end
      object FVBox154: TFVBox
        Left = 505
        Top = 0
        Width = 10
        Height = 46
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 9
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object tGridItemOsCO: TFTreeGrid
      Left = 0
      Top = 69
      Width = 635
      Height = 392
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Table = tbItemGrid
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Paging.Enabled = False
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = False
      MultiSelection = False
      KeyField = 'ID_ITEM'
      ParentField = 'ID_ITEM_PAI'
      Expanded = False
      WOwner = FrInterno
      WOrigem = EhNone
      EnablePopup = False
      EditionEnabled = False
      SortColumns = <>
      NoBorder = False
      OpenOnSelect = True
      Columns = <
        item
          Expanded = False
          FieldName = 'DESCRICAO_ITEM'
          Font = <>
          Title.Caption = 'Descri'#231#227'o Item'
          Width = 283
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = True
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          HiperLink = False
          GUID = '{EF287B08-154B-4EAE-BF4F-99E830EF56BC}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
        end>
    end
    object hBoxGridPanelLegenda: TFHBox
      Left = 0
      Top = 462
      Width = 520
      Height = 53
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 7
      Padding.Right = 0
      Padding.Bottom = 7
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FGridPanelLegenda: TFGridPanel
        Left = 0
        Top = 0
        Width = 515
        Height = 41
        Caption = 'FGridPanelLegenda'
        ColumnCollection = <
          item
            SizeStyle = ssAuto
            Value = 12.499486635481970000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            Value = 26.780585529214050000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            Value = 14.285043772726690000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            Value = 30.152475128775090000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            Value = 16.665337387277570000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            Value = 22.036993235662230000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            Value = 20.002085485386340000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            Value = 21.029946106348620000
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        ControlCollection = <
          item
            Column = 0
            Control = imgEmServico
            Row = 0
          end
          item
            Column = 1
            Control = lblEmServico
            Row = 0
          end
          item
            Column = 2
            Control = imgParado
            Row = 0
          end
          item
            Column = 3
            Control = lblParado
            Row = 0
          end
          item
            Column = 2
            Control = imgParadoMotivo
            Row = 1
          end
          item
            Column = 3
            Control = lblParadoComMotivo
            Row = 1
          end
          item
            Column = 0
            Control = imgEncerrado
            Row = 1
          end
          item
            Column = 1
            Control = lblEncerrado
            Row = 1
          end
          item
            Column = 4
            Control = imgTestando
            Row = 0
          end
          item
            Column = 5
            Control = lblParadoA
            Row = 0
          end
          item
            Column = 4
            Control = imgParadoAtrasadoMotivo
            Row = 1
          end
          item
            Column = 5
            Control = lblRetorno
            Row = 1
          end
          item
            Column = 6
            Control = imgEncerradoAtrasado
            Row = 0
          end
          item
            Column = 7
            Control = lblEncerradoA
            Row = 0
          end
          item
            Column = 6
            Control = imgNaoTestou
            Row = 1
          end
          item
            Column = 7
            Control = lblNaoTestou
            Row = 1
          end>
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        RowCollection = <
          item
            Value = 50.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            Value = 50.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        AllRowFlex = False
        WOwner = FrInterno
        WOrigem = EhNone
        ColumnTabOrder = False
        DesignSize = (
          515
          41)
        object imgEmServico: TFImage
          Left = 1
          Top = 1
          Width = 18
          Height = 18
          Anchors = []
          Stretch = False
          ImageSrc = '/images/crmservice310050.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitLeft = 0
        end
        object lblEmServico: TFLabel
          Left = 19
          Top = 1
          Width = 52
          Height = 19
          Align = alLeft
          Caption = 'Em Servico'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object imgParado: TFImage
          Left = 131
          Top = 1
          Width = 18
          Height = 18
          Anchors = []
          Stretch = False
          ImageSrc = '/images/crmservice310052.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitLeft = 136
        end
        object lblParado: TFLabel
          Left = 149
          Top = 1
          Width = 34
          Height = 19
          Align = alLeft
          Caption = 'Parado'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitLeft = 135
          ExplicitHeight = 13
        end
        object imgParadoMotivo: TFImage
          Left = 131
          Top = 21
          Width = 18
          Height = 18
          Anchors = []
          Stretch = False
          ImageSrc = '/images/crmservice310054.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitLeft = 232
          ExplicitTop = 1
        end
        object lblParadoComMotivo: TFLabel
          Left = 149
          Top = 20
          Width = 91
          Height = 20
          Align = alLeft
          Caption = 'Parado com motivo'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitLeft = 135
          ExplicitHeight = 13
        end
        object imgEncerrado: TFImage
          Left = 1
          Top = 21
          Width = 18
          Height = 18
          Anchors = []
          Stretch = False
          ImageSrc = '/images/crmservice310056.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitLeft = 377
          ExplicitTop = 1
        end
        object lblEncerrado: TFLabel
          Left = 19
          Top = 20
          Width = 49
          Height = 20
          Align = alLeft
          Caption = 'Encerrado'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitHeight = 13
        end
        object imgTestando: TFImage
          Left = 275
          Top = 3
          Width = 28
          Height = 15
          Anchors = []
          Stretch = False
          ImageSrc = '/images/crmservice4600255.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitLeft = 281
          ExplicitTop = 4
        end
        object lblParadoA: TFLabel
          Left = 303
          Top = 1
          Width = 45
          Height = 19
          Align = alLeft
          Caption = 'Testando'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitLeft = 273
          ExplicitHeight = 13
        end
        object imgParadoAtrasadoMotivo: TFImage
          Left = 275
          Top = 20
          Width = 28
          Height = 20
          Anchors = []
          Stretch = False
          ImageSrc = '/images/crmservice4600256.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitLeft = 245
          ExplicitTop = 21
        end
        object lblRetorno: TFLabel
          Left = 303
          Top = 20
          Width = 39
          Height = 20
          Align = alLeft
          Caption = 'Retorno'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitLeft = 273
          ExplicitHeight = 13
        end
        object imgEncerradoAtrasado: TFImage
          Left = 395
          Top = 3
          Width = 28
          Height = 15
          Anchors = []
          Stretch = False
          ImageSrc = '/images/crmservice4600257.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitLeft = 380
          ExplicitTop = 1
        end
        object lblEncerradoA: TFLabel
          Left = 423
          Top = 1
          Width = 44
          Height = 19
          Align = alLeft
          Caption = 'Teste OK'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitLeft = 435
          ExplicitHeight = 13
        end
        object imgNaoTestou: TFImage
          Left = 395
          Top = 22
          Width = 28
          Height = 15
          Anchors = []
          Stretch = False
          ImageSrc = '/images/crmservice4600258.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitLeft = 385
        end
        object lblNaoTestou: TFLabel
          Left = 423
          Top = 20
          Width = 55
          Height = 20
          Align = alLeft
          Caption = 'N'#227'o Testou'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          ExplicitLeft = 435
          ExplicitHeight = 13
        end
      end
    end
  end
  object tbItemGrid: TFTable
    FieldDefs = <
      item
        Name = 'ID_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_ITEM_PAI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Item Pai'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_ADICIONAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Adicional'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERV_COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_TRAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Trab'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PAGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Pago'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PADRAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Padr'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_DISP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Disp'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_IMAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Imagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REQUISICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Requisi'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_RECLAMACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Reclama'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_ITEM_GRID'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600263;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object imageList: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 180
    Top = 207
    object FMenuItemConsultar: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Consultar'
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{914750E1-76E3-477D-823C-7609FFC72327}'
    end
    object FMenuItemManut: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Manut. Aprova'
      ImageIndex = 430096
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{B1F697C5-95AB-4590-9EA9-AFEDE1E3C7B6}'
    end
    object FMenuItemOficina: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Oficina'
      ImageIndex = 4600237
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{149CB34B-1590-48F5-A766-BC15F55D550F}'
    end
    object FMenuItemQualidade: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Qualidade'
      ImageIndex = 4300101
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{5EEDE651-3C31-4996-9205-49616C3CECEA}'
    end
    object FMenuItemEntrega: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Entrega'
      ImageIndex = 4300104
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{ADCD33F4-D9D5-4EDA-87C7-AA15071C5F44}'
    end
    object FMenuItemImprimir: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Imprimir'
      ImageIndex = 4300100
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{8369BEBE-EC59-46E1-BF7E-552C509910D1}'
    end
    object FMenuItemRelacionados: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Relacionados'
      ImageIndex = 430098
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{679462A8-35D2-4834-BC06-D4FE32964D10}'
    end
    object FMenuItemMensagem: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Mensagem'
      ImageIndex = 430099
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{FEB3554F-1DCA-4348-A4F7-1BA9A60032EB}'
    end
    object FMenuItemEstrelaGray: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Estrela Cinza'
      ImageIndex = 4300106
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{B0642FE3-649A-470D-8BA3-0DB8DF58582A}'
    end
    object FMenuItemEstrelaGold: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Estrela Gold'
      ImageIndex = 4300107
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{C43A918B-52FB-4B9D-AFC0-98C845EE3D23}'
    end
    object FMenuItemChecklist: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Checklist'
      ImageIndex = 4300110
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{C74C143B-FBF1-4C54-9DFD-754874427DDD}'
    end
    object FMenuItemLivre: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Livre'
      ImageIndex = 430011
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{E0F687F8-8E86-4839-B331-78F585EC52C5}'
    end
    object FMenuItemKit: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Kit'
      ImageIndex = 430015
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{B96D3797-B4E1-4E38-BF28-2F6200360C32}'
    end
    object FMenuItemAgendada: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Agendada'
      ImageIndex = 430012
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{C59C4DF9-20B5-46A3-AD07-EAE4E977E813}'
    end
    object FMenuItemAutomatica: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Automatica'
      ImageIndex = 430013
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{93210BD5-FAA0-41FD-8CAA-B1C3CF9426AB}'
    end
    object FMenuItemCampanha: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Campanha'
      ImageIndex = 430014
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{721C48BB-15DF-4165-89FB-E0BD39E310BF}'
    end
    object FMenuItemExcluido: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Excluido'
      ImageIndex = 310073
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{11C9CBA9-5E77-4D06-A020-A407E9E03B67}'
    end
    object FMenuItemLibDesc: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Liberar Desconto'
      ImageIndex = 430079
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{076489E2-1E72-44EE-A168-B47BA07E7865}'
    end
    object FMenuItemPreFechamento: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Pr'#233' Fechamento'
      ImageIndex = 430035
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{D74A5024-F79B-4E9E-A935-1CC99902A6C3}'
    end
    object FMenuItemFranquia: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Franquia'
      ImageIndex = 34005
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{0C09E5CA-10A3-4E2F-9B8A-5FAE7DA107FD}'
    end
    object FMenuItemCortesia: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Cortesia'
      ImageIndex = 4300114
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{AD8E1292-C184-4E04-9DFF-282584B4BB52}'
    end
    object FMenuItemLibCredito: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Liberar Cr'#233'dito'
      ImageIndex = 430080
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{7606D0C9-F01B-4831-974B-E00164DB8E40}'
    end
    object FMenuItemFaturar: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Faturar'
      ImageIndex = 4300113
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{C0D7AD45-3A37-44D9-8373-716AE4FD990B}'
    end
    object FMenuItemTabsheetOpen: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Tabsheet Open'
      ImageIndex = 4300115
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{E5464BF5-5A1D-4AD6-A51C-B4C04458FAA3}'
    end
    object FMenuItemTabsheetClose: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Tabsheet Close'
      ImageIndex = 4300116
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{8AA85B9F-C952-4D44-BF05-27A522ED7964}'
    end
    object ConsultaOSWhatsapp: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'ConsultaOSWhatsapp'
      ImageIndex = 440022
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{D78225E0-E66F-4EEF-B978-75CB8D089E28}'
    end
    object EnviarOS: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'EnviarOS'
      ImageIndex = 4600175
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{36F5315C-EB99-461B-A057-5DC2A4050730}'
    end
    object AtualizarOS: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'AtualizarOS'
      ImageIndex = 4600174
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{67D9508A-A8FF-40CA-9BC6-A165AFF056E9}'
    end
    object CriticasOS: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'CriticasOS'
      ImageIndex = 4600176
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{43FC8F7F-B9CB-400B-B389-0F8275FBC5CD}'
    end
    object miFormTreeview: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Treeview Servi'#231'os'
      ImageIndex = 4600250
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{03B07CB1-570F-458E-BD83-E67D42C60753}'
    end
    object miRetorno: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Retorno'
      ImageIndex = 4600251
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{523267C6-A03C-4590-91A4-AF9847992BAD}'
    end
    object miChecklist2: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Checklist2'
      ImageIndex = 4600254
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{CA7B0851-3E85-44A6-B017-5DA6E13ED634}'
    end
    object miQualidadeTestando: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Testando'
      ImageIndex = 4600255
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{7B92E60A-8E22-411F-9D3E-F78973AA7E77}'
    end
    object miQualidadeRetorno: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Retorno'
      ImageIndex = 4600256
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{A5356E7A-F47F-4759-99BC-8A28E1D8D257}'
    end
    object miTesteOK: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Teste OK'
      ImageIndex = 4600257
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{D154B945-4C33-4E10-B0E7-5542C7E16260}'
    end
    object miQualidadeNaoTestou: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'N'#227'o Testou'
      ImageIndex = 4600258
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{82A56A2C-B4E9-48ED-AF2E-5F95411800D1}'
    end
    object miExpandTreeView: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Expand TreeView'
      ImageIndex = 4600246
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{6968B248-E35E-4749-BDF1-30496F13D291}'
    end
    object miEncerrado: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Encerrado'
      ImageIndex = 310056
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{2B8E936C-8E0C-4DD3-9150-A02E088EA6C8}'
    end
    object miEmServico: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Em Servi'#231'o'
      ImageIndex = 310050
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{691C5295-CFD8-4EDA-A289-06095F1AA94A}'
    end
    object miParado: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Parado'
      ImageIndex = 310052
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{761D0351-EE96-4C06-A0E7-82C69D8AB019}'
    end
    object miParadoComMotivo: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Parado com Motivo'
      ImageIndex = 310054
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{E6F6F4C6-E536-4C40-BBA9-62C21793039C}'
    end
  end
end
