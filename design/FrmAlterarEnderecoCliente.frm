object FrmAlterarEnderecoCliente: TFForm
  Left = 44
  Top = 163
  ActiveControl = hboxEditarLead
  Caption = 'Alterar Endere'#231'o do Cliente'
  ClientHeight = 295
  ClientWidth = 656
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '430063'
  ShortcutKeys = <>
  InterfaceRN = 'AlterarEnderecoClienteRN'
  Access = False
  ChangedProp = 
    'FrmAlterarEnderecoCliente.Width;'#13#10'FrmAlterarEnderecoCliente.Capt' +
    'ion;'#13#10'tbCidades.MaxRowCount;'#13#10'tbUf.MaxRowCount;'#13#10#13#10'FrmAlterarEnd' +
    'erecoCliente.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object hboxEditarLead: TFHBox
    Left = 0
    Top = 0
    Width = 656
    Height = 61
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 3
    Padding.Left = 5
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object btnSalvar: TFButton
      Left = 0
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Salvar Altera'#231#245'es no Endere'#231'o do Cliente'
      Align = alLeft
      Caption = 'Salvar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 0
      OnClick = btnSalvarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
        C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
        D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
        1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
        D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
        9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
        A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
        BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
      ImageId = 700080
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
    object FVBox4: TFVBox
      Left = 60
      Top = 0
      Width = 5
      Height = 55
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object btnCancelar: TFButton
      Left = 65
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Cancelar Altera'#231#227'o de Endere'#231'o do Cliente'
      Align = alLeft
      Caption = 'Cancelar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 2
      OnClick = btnCancelarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F80000022B4944415478DAB595DF4B544114C7CF2692A12F22195B28855AF8A0
        50F4121111E9F64304F34585D4C00705317AAABFA18720AA97602B044B097221
        88486559C444A8968405454537D0087F15063EC4BAF53DCC1918C6BDEBDC950E
        7CE09C997BCFF7CECC997303F49F2DE0F85C31380D8E48FC037C053FF723C073
        4DE036B898E1D934888147E02DF8EB47E004E807171C57380E3AC03717813360
        0494585F1B078B1257C8961D309E590321D93A4F8172F0191C9638051E8207B2
        EFA605C15D70C7185B950F5CC924C0FE28B82CF1266800531EDB9207C2E09635
        FE015C23391353A05EB6866D87D4C17EF499DCCC35660B7025348AFF04F46549
        FE0C741A6361C9D5257104349B0205E0173828315751D247F26E500566656C9B
        D4DDF9A305F860BE883F074E79247F4EAA1CEDE4698997C071F16B40420B5C07
        EFC4E773B8924372B628B8649E8316B80ADE8BCF9514CA21395B8C5471B07135
        46B5402D98163F2967A093BF00ED0EC939D732382A71359F8916C827D5B80A8D
        FD9BF1919C8D6F765CFC2D529D206596E91068117F90D42D764DAEDF6915FF25
        B8A997A5ED3C98F07879AFE4DC7523467C8EA403D8BD6818DCC821F92B7048E2
        D7C64EEC1228059F48353D6D03E03E481863FC1E17C63DD0668C27C159B0E125
        C07692541F29B3C6B94216E49D4A70CC9AE77F419D3C43D904F44A9ECAF25DEC
        0DE801EBF6C45EFF64FEA3F5926ADB45D6DC6F52B7FF3198F44AE0FAD3E77BC2
        CD2C28F177304FAA94B39AAB40CEF60F541979196CA1E08A0000000049454E44
        AE426082}
      ImageId = 700098
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
  end
  object hboxDadosClientes: TFVBox
    Left = 0
    Top = 61
    Width = 656
    Height = 234
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 0
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object gridAlterarDadosBasicos: TFGridPanel
      Left = 0
      Top = 0
      Width = 646
      Height = 227
      Align = alClient
      Caption = 'gridAlterarDadosBasicos'
      ColumnCollection = <
        item
          SizeStyle = ssAbsolute
          Value = 145.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      ControlCollection = <
        item
          Column = 0
          Control = lblTpEndereco
          Row = 0
        end
        item
          Column = 1
          Control = edtTPEndereco
          Row = 0
        end
        item
          Column = 0
          Control = lblUFCidadeCEP
          Row = 1
        end
        item
          Column = 0
          Control = email
          Row = 4
        end
        item
          Column = 1
          Control = edtBairro
          Row = 2
        end
        item
          Column = 0
          Control = lblBairro
          Row = 2
        end
        item
          Column = 1
          Control = hBoxUFCidadeCEP
          Row = 1
        end
        item
          Column = 0
          Control = lblRuaNumero
          Row = 3
        end
        item
          Column = 0
          Control = lblContato
          Row = 5
        end
        item
          Column = 1
          Control = hBoxRuaNumero
          Row = 3
        end
        item
          Column = 1
          Control = FHBox1
          Row = 4
        end
        item
          Column = 1
          Control = edtContato
          Row = 5
        end>
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      RowCollection = <
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 28.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      AllRowFlex = False
      WOwner = FrInterno
      WOrigem = EhNone
      ColumnTabOrder = False
      object lblTpEndereco: TFLabel
        Left = 67
        Top = 1
        Width = 79
        Height = 24
        Align = alRight
        Caption = 'Tipo Endere'#231'o'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 13
      end
      object edtTPEndereco: TFString
        Left = 146
        Top = 1
        Width = 305
        Height = 24
        Table = tbAlteraEnderecoCliente
        FieldName = 'TIPO_ENDERECO'
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Align = alLeft
        Enabled = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
      object lblUFCidadeCEP: TFLabel
        Left = 62
        Top = 25
        Width = 84
        Height = 41
        Align = alRight
        Caption = 'UF/Cidade/CEP'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 13
      end
      object email: TFLabel
        Left = 9
        Top = 131
        Width = 137
        Height = 41
        Align = alRight
        Caption = 'Complemento/Cx.Postal'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 13
      end
      object edtBairro: TFString
        Left = 146
        Top = 66
        Width = 424
        Height = 24
        Table = tbAlteraEnderecoCliente
        FieldName = 'BAIRRO'
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccNormal
        Pwd = False
        Maxlength = 30
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
      object lblBairro: TFLabel
        Left = 112
        Top = 66
        Width = 34
        Height = 24
        Align = alRight
        Caption = 'Bairro'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 13
      end
      object hBoxUFCidadeCEP: TFHBox
        Left = 146
        Top = 25
        Width = 485
        Height = 41
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbUF: TFCombo
          Left = 0
          Top = 0
          Width = 126
          Height = 21
          Table = tbAlteraEnderecoCliente
          LookupTable = tbUf
          FieldName = 'UF'
          LookupKey = 'UF'
          LookupDesc = 'UF'
          Flex = False
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = False
          UseClearButton = True
          HideClearButtonOnNullValue = True
          OnChange = cbUFChange
          OnClearClick = cbUFClearClick
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object FVBox5: TFVBox
          Left = 126
          Top = 0
          Width = 5
          Height = 24
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object cbCidade: TFCombo
          Left = 131
          Top = 0
          Width = 245
          Height = 21
          Table = tbAlteraEnderecoCliente
          LookupTable = tbCidades
          FieldName = 'COD_CIDADES'
          LookupKey = 'COD_CIDADES'
          LookupDesc = 'DESCRICAO'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = False
          UseClearButton = True
          HideClearButtonOnNullValue = True
          OnChange = cbCidadeChange
          OnClearClick = cbCidadeClearClick
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object FVBox1: TFVBox
          Left = 376
          Top = 0
          Width = 5
          Height = 24
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object edtCEP: TFString
          Left = 381
          Top = 0
          Width = 80
          Height = 24
          Table = tbAlteraEnderecoCliente
          FieldName = 'CEP'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Enabled = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
      end
      object lblRuaNumero: TFLabel
        Left = 74
        Top = 90
        Width = 72
        Height = 41
        Align = alRight
        Caption = 'Rua/N'#250'mero'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 13
      end
      object lblContato: TFLabel
        Left = 101
        Top = 172
        Width = 45
        Height = 24
        Align = alRight
        Caption = 'Contato'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 13
      end
      object hBoxRuaNumero: TFHBox
        Left = 146
        Top = 90
        Width = 433
        Height = 41
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edtRua: TFString
          Left = 0
          Top = 0
          Width = 331
          Height = 24
          Table = tbAlteraEnderecoCliente
          FieldName = 'RUA'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 50
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
        object FVBox2: TFVBox
          Left = 331
          Top = 0
          Width = 5
          Height = 24
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object edtNumero: TFString
          Left = 336
          Top = 0
          Width = 80
          Height = 24
          Table = tbAlteraEnderecoCliente
          FieldName = 'NUMERO'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 5
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
      end
      object FHBox1: TFHBox
        Left = 146
        Top = 131
        Width = 433
        Height = 41
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edtComplemento: TFString
          Left = 0
          Top = 0
          Width = 331
          Height = 24
          Table = tbAlteraEnderecoCliente
          FieldName = 'COMPLEMENTO'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 40
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
        object FVBox3: TFVBox
          Left = 331
          Top = 0
          Width = 5
          Height = 24
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object edtCxPostal: TFString
          Left = 336
          Top = 0
          Width = 80
          Height = 24
          Table = tbAlteraEnderecoCliente
          FieldName = 'CX_POSTAL'
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 10
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
      end
      object edtContato: TFString
        Left = 146
        Top = 172
        Width = 315
        Height = 24
        Table = tbAlteraEnderecoCliente
        FieldName = 'CONTATO'
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccNormal
        Pwd = False
        Maxlength = 40
        Align = alLeft
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
    end
  end
  object tbAlteraEnderecoCliente: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_ENDERECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Endere'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ENDERECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Endere'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CX_POSTAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cx Postal'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ALTERA_ENDERECO_CLIENTE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430063;43001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbUf: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'UF'
    Cursor = 'UF'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430063;43002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCidades: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CIDADES'
    Cursor = 'CIDADES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430063;43003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
