object DadosFinanceiroRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600700'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbDadosFinanceiroCliente: TFTable
    FieldDefs = <
      item
        Name = 'DADOS_CREDITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dados Cr'#233'dito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DUPLICATA_PENDENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Duplicata Pendente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHEQUE_PENDENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cheque Pendente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOTA_SEM_FATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nota Sem Fatura'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMITE_CREDITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Limite Cr'#233'dito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DISPONIVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Disponivel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENC_CADASTRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vencimento Cadastro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO_ULTIMO_BLOQUEIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Motivo '#218'ltimo Bloqueio'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'DADOS_FINANCEIRO_CLIENTE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600700;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteDiverso: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMITE_CREDITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Limite Cr'#233'dito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_DIVERSO'
    Cursor = 'CLIENTE_DIVERSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600700;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
