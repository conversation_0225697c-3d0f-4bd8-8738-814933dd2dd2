object LoginClienteRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '340052'
  Height = 299
  Width = 442
  object tbSchema: TFTable
    FieldDefs = <
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRIPTION'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SYS.NBS_SCHEMA'
    Cursor = 'NBS_SCHEMA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340052;34001'
    DeltaMode = dmChanged
  end
  object tbSchemaUser: TFTable
    FieldDefs = <
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SCHEMA_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBS_SCHEMA_USER'
    Cursor = 'NBS_SCHEMA_USER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340052;34003'
    DeltaMode = dmChanged
  end
  object tbUsuarioLogado: TFTable
    FieldDefs = <
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'USUARIO_LOGADO'
    Cursor = 'USUARIO_LOGADO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340052;34004'
    DeltaMode = dmChanged
  end
  object tbBtobCadastro: TFTable
    FieldDefs = <
      item
        Name = 'ID_BTOB'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'BTOB_CADASTRO'
    Cursor = 'BTOB_CADASTRO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340052;34007'
    DeltaMode = dmChanged
  end
end
