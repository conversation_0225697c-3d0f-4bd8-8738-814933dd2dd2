object FrmPgtoVendaInf: TFForm
  Left = 321
  Top = 163
  ActiveControl = vBoxTopo
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Informe o valor do Pagamento'
  ClientHeight = 443
  ClientWidth = 854
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = frmShow
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '183028'
  ShortcutKeys = <>
  InterfaceRN = 'PgtoVendaInfRN'
  Access = False
  ChangedProp = 
    'FrmPgtoVendaInf.Width;'#13#10'FrmPgtoVendaInf.Height;'#13#10'FrmPgtoVendaInf' +
    '.ActiveControl;'#13#10'FrmPgtoVendaInf.ActiveControlFrmPgtoVendaInf.Wi' +
    'dth;'#13#10'FrmPgtoVendaInf.ActiveControlFrmPgtoVendaInf.ActiveControl' +
    ';'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FHBox28: TFHBox
    Left = 0
    Top = 0
    Width = 68
    Height = 4
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
  end
  object vBoxTopo: TFVBox
    Left = 0
    Top = 4
    Width = 768
    Height = 61
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxBotoesTopo: TFHBox
      Left = 0
      Top = 0
      Width = 739
      Height = 57
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 1
      Padding.Left = 1
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox35: TFHBox
        Left = 0
        Top = 0
        Width = 4
        Height = 12
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnCancelar: TFButton
        Left = 4
        Top = 0
        Width = 65
        Height = 50
        Hint = 'Voltar...'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnCancelarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
          E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
          B9B9B6418D210000000049454E44AE426082}
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'undo'
        IconReverseDirection = False
      end
      object btnConfirmar: TFButton
        Left = 69
        Top = 0
        Width = 65
        Height = 50
        Caption = 'Confirmar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnConfirmarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
          E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
          B9B9B6418D210000000049454E44AE426082}
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'check'
        IconReverseDirection = False
      end
    end
  end
  object FHBox2: TFHBox
    Left = 0
    Top = 70
    Width = 853
    Height = 373
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 0
    TabOrder = 2
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object pgcInfoPgto: TFPageControl
      Left = 0
      Top = 0
      Width = 847
      Height = 337
      ActivePage = tbsCartoes
      TabOrder = 0
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tbsCartoes: TFTabsheet
        Caption = 'Informe cart'#245'es'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxInformeCartoes: TFVBox
          Left = 0
          Top = 0
          Width = 837
          Height = 308
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox27: TFHBox
            Left = 0
            Top = 0
            Width = 33
            Height = 1
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stSingleLine
            Caption = ' '
            Color = clBackground
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object hBoxValorCartoes: TFHBox
            Left = 0
            Top = 2
            Width = 831
            Height = 72
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox4: TFHBox
              Left = 0
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object vBoxCartaoValorTotal: TFVBox
              Left = 4
              Top = 0
              Width = 115
              Height = 55
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblCartoesValorTotal: TFLabel
                Left = 0
                Top = 0
                Width = 49
                Height = 13
                Caption = 'Valor total'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtCartaoValorTotal: TFDecimal
                Left = 0
                Top = 14
                Width = 108
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Precision = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                Mode = dmDecimal
              end
            end
            object FHBox31: TFHBox
              Left = 119
              Top = 0
              Width = 6
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox10: TFVBox
              Left = 125
              Top = 0
              Width = 115
              Height = 55
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 3
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel4: TFLabel
                Left = 0
                Top = 0
                Width = 56
                Height = 13
                Caption = 'Falta lan'#231'ar'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtCartaoValorFaltaLanc: TFDecimal
                Left = 0
                Top = 14
                Width = 108
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Precision = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                Mode = dmDecimal
              end
            end
            object FHBox68: TFHBox
              Left = 240
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 4
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox8: TFVBox
              Left = 244
              Top = 0
              Width = 340
              Height = 55
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 5
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblCartoesObservacao: TFLabel
                Left = 0
                Top = 0
                Width = 58
                Height = 13
                Caption = 'Observa'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtCartoesObservacao: TFString
                Left = 0
                Top = 14
                Width = 341
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 60
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object FHBox69: TFHBox
              Left = 584
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 6
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object FHBox29: TFHBox
            Left = 0
            Top = 75
            Width = 33
            Height = 1
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stSingleLine
            Caption = ' '
            Color = clBackground
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FHBox24: TFHBox
            Left = 0
            Top = 77
            Width = 724
            Height = 223
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox1: TFVBox
              Left = 0
              Top = 0
              Width = 236
              Height = 215
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftTrue
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox20: TFHBox
                Left = 0
                Top = 0
                Width = 68
                Height = 4
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object FHBox5: TFHBox
                Left = 0
                Top = 5
                Width = 232
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox110: TFHBox
                  Left = 0
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox56: TFVBox
                  Left = 4
                  Top = 0
                  Width = 100
                  Height = 25
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox108: TFHBox
                    Left = 0
                    Top = 0
                    Width = 68
                    Height = 4
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object lblSetor: TFLabel
                    Left = 0
                    Top = 5
                    Width = 58
                    Height = 13
                    Caption = 'Valor cart'#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object FHBox23: TFHBox
                  Left = 104
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object edtCartaoValor: TFDecimal
                  Left = 108
                  Top = 0
                  Width = 121
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Precision = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  Mode = dmDecimal
                end
              end
              object FHBox6: TFHBox
                Left = 0
                Top = 46
                Width = 232
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox8: TFHBox
                  Left = 0
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox2: TFVBox
                  Left = 4
                  Top = 0
                  Width = 100
                  Height = 25
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox9: TFHBox
                    Left = 0
                    Top = 0
                    Width = 68
                    Height = 4
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object lblCartaoQtdeParc: TFLabel
                    Left = 0
                    Top = 5
                    Width = 67
                    Height = 13
                    Caption = 'Qtde parcelas'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object FHBox21: TFHBox
                  Left = 104
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object edtCartaoQtdParc: TFInteger
                  Left = 108
                  Top = 0
                  Width = 47
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  OnExit = edtCartaoQtdParcExit
                end
              end
              object FHBox7: TFHBox
                Left = 0
                Top = 87
                Width = 227
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 3
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox10: TFHBox
                  Left = 0
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox3: TFVBox
                  Left = 4
                  Top = 0
                  Width = 71
                  Height = 25
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox11: TFHBox
                    Left = 0
                    Top = 0
                    Width = 68
                    Height = 4
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object lblCartaoNrAut: TFLabel
                    Left = 0
                    Top = 5
                    Width = 39
                    Height = 13
                    Caption = 'Nr. Aut.'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object FHBox22: TFHBox
                  Left = 75
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object edtCartaoNrAut: TFString
                  Left = 79
                  Top = 0
                  Width = 129
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccNormal
                  Pwd = False
                  Maxlength = 20
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
              end
              object FHBox12: TFHBox
                Left = 0
                Top = 128
                Width = 230
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 4
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox13: TFHBox
                  Left = 0
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox4: TFVBox
                  Left = 4
                  Top = 0
                  Width = 44
                  Height = 25
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox14: TFHBox
                    Left = 0
                    Top = 0
                    Width = 30
                    Height = 4
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel1: TFLabel
                    Left = 0
                    Top = 5
                    Width = 33
                    Height = 13
                    Caption = 'Cart'#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object cbbCartao: TFCombo
                  Left = 48
                  Top = 0
                  Width = 145
                  Height = 21
                  LookupTable = tbLeadsCartoes
                  LookupKey = 'COD_CARTAO_CREDITO'
                  LookupDesc = 'DESCRICAO_CARTAO'
                  Flex = True
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
              object FHBox15: TFHBox
                Left = 0
                Top = 169
                Width = 185
                Height = 8
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 5
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object FHBox16: TFHBox
                Left = 0
                Top = 178
                Width = 243
                Height = 29
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 6
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox17: TFHBox
                  Left = 0
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object btnIncluirCartoes: TFButton
                  Left = 4
                  Top = 0
                  Width = 111
                  Height = 25
                  Caption = 'Incluir Cart'#245'es'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnIncluirCartoesClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                    E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                    B9B9B6418D210000000049454E44AE426082}
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconClass = 'sign-in'
                  IconReverseDirection = False
                end
                object FHBox18: TFHBox
                  Left = 115
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object btnExcluirCartoes: TFButton
                  Left = 119
                  Top = 0
                  Width = 111
                  Height = 25
                  Caption = 'Excluir Cart'#245'es'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 3
                  OnClick = btnExcluirCartoesClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                    E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                    B9B9B6418D210000000049454E44AE426082}
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconClass = 'trash-o'
                  IconReverseDirection = False
                end
              end
              object FHBox3: TFHBox
                Left = 0
                Top = 208
                Width = 33
                Height = 4
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 7
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
            object FHBox25: TFHBox
              Left = 236
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object vBoxGridCartoes: TFVBox
              Left = 240
              Top = 0
              Width = 464
              Height = 219
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object grdCartoesInformados: TFGrid
                Left = 0
                Top = 0
                Width = 456
                Height = 120
                TabOrder = 0
                TitleFont.Charset = DEFAULT_CHARSET
                TitleFont.Color = clWindowText
                TitleFont.Height = -11
                TitleFont.Name = 'Tahoma'
                TitleFont.Style = []
                Table = tbLeadsPgtoVendaInfCartao
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Paging.Enabled = False
                Paging.PageSize = 0
                Paging.DbPaging = False
                FrozenColumns = 0
                ShowFooter = False
                ShowHeader = True
                MultiSelection = False
                Grouping.Enabled = False
                Grouping.Expanded = False
                Grouping.ShowFooter = False
                Crosstab.Enabled = False
                Crosstab.GroupType = cgtConcat
                EnablePopup = False
                WOwner = FrInterno
                WOrigem = EhNone
                EditionEnabled = False
                AuxColumnHeaders = <>
                NoBorder = False
                ActionButtons.BtnAccept = False
                ActionButtons.BtnView = False
                ActionButtons.BtnEdit = False
                ActionButtons.BtnDelete = False
                ActionButtons.BtnInLineEdit = False
                CustomActionButtons = <>
                ActionColumn.Title = 'A'#231#245'es'
                ActionColumn.Width = 100
                ActionColumn.TextAlign = taCenter
                ActionColumn.Visible = True
                Columns = <
                  item
                    Expanded = False
                    FieldName = 'NOME'
                    Font = <>
                    Title.Caption = 'Cart'#227'o'
                    Width = 148
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = True
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{EF2F614E-0D79-4FA3-940C-12ACF5A5805C}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'NR_AUTORIZACAO_VISA'
                    Font = <>
                    Title.Caption = 'Nr. Autoriza'#231#227'o'
                    Width = 160
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{2F767722-E111-445A-8114-28C00A451FBE}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'PARCELA'
                    Font = <>
                    Title.Caption = 'Parc'
                    Width = 40
                    Visible = True
                    Precision = 0
                    TextAlign = taCenter
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{4F35DE8C-C441-4ABB-BCCA-825A6F0E0C69}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'DATA_VENCIMENTO'
                    Font = <>
                    Title.Caption = 'Dt.Vencimento'
                    Width = 90
                    Visible = True
                    Precision = 0
                    TextAlign = taCenter
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{5E27C418-1DBB-452E-8594-AB29EFCD610F}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = 'dd/MM/yyyy'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDateTime
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{7359AA4E-A138-49A3-8A9E-FFB94D20EB43}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'VALOR'
                    Font = <>
                    Title.Caption = 'Valor'
                    Width = 105
                    Visible = True
                    Precision = 0
                    TextAlign = taRight
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{CC193B45-03F1-46B5-9759-1E8F37B855D1}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = ',##0.00'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDecimal
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{85979D08-B397-4D03-84E9-CCCB5BB443DC}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end>
              end
              object hBoxEditarCartoes: TFHBox
                Left = 0
                Top = 121
                Width = 781
                Height = 50
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FVBox36: TFVBox
                  Left = 0
                  Top = 0
                  Width = 8
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox37: TFVBox
                  Left = 8
                  Top = 0
                  Width = 140
                  Height = 45
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FLabel3: TFLabel
                    Left = 0
                    Top = 0
                    Width = 33
                    Height = 13
                    Align = alLeft
                    Caption = 'Cart'#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhAttribute
                    WKey = '7000139;70004;70002'
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object edtCartaoNomeCartao: TFString
                    Left = 0
                    Top = 14
                    Width = 133
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = True
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    CharCase = ccNormal
                    Pwd = False
                    Maxlength = 0
                    Enabled = False
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    SaveLiteralCharacter = False
                    TextAlign = taLeft
                  end
                end
                object FVBox7: TFVBox
                  Left = 148
                  Top = 0
                  Width = 37
                  Height = 45
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblCartaoParc: TFLabel
                    Left = 0
                    Top = 0
                    Width = 35
                    Height = 13
                    Align = alLeft
                    Caption = 'Parcela'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhAttribute
                    WKey = '7000139;70004;70002'
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object edtCartaoParcCartao: TFString
                    Left = 0
                    Top = 14
                    Width = 31
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = True
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    CharCase = ccNormal
                    Pwd = False
                    Maxlength = 20
                    Enabled = False
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    Alignment = taCenter
                    SaveLiteralCharacter = False
                    TextAlign = taLeft
                  end
                end
                object FVBox38: TFVBox
                  Left = 185
                  Top = 0
                  Width = 4
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 3
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox41: TFVBox
                  Left = 189
                  Top = 0
                  Width = 135
                  Height = 45
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 4
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblCartaoNrAutAlterar: TFLabel
                    Left = 0
                    Top = 0
                    Width = 75
                    Height = 13
                    Align = alLeft
                    Caption = 'Nr. Autoriza'#231#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhAttribute
                    WKey = '7000139;70004;70002'
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object edtCartaoAlterarNrAut: TFString
                    Left = 0
                    Top = 14
                    Width = 129
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = True
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    CharCase = ccNormal
                    Pwd = False
                    Maxlength = 20
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    SaveLiteralCharacter = False
                    TextAlign = taLeft
                  end
                end
                object vBoxCartaoAlterar: TFVBox
                  Left = 324
                  Top = 0
                  Width = 154
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 5
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object hBoxCartoesTopAlterar: TFHBox
                    Left = 0
                    Top = 0
                    Width = 33
                    Height = 13
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FHBox72: TFHBox
                    Left = 0
                    Top = 14
                    Width = 152
                    Height = 34
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    DesignSize = (
                      148
                      30)
                    object btnAlterarCartao: TFButton
                      Left = 0
                      Top = 0
                      Width = 70
                      Height = 25
                      Hint = 'Alterar Cart'#227'o'
                      Anchors = []
                      Caption = 'Alterar'
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 0
                      OnClick = btnAlterarCartaoClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                        F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                        E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                        B9B9B6418D210000000049454E44AE426082}
                      ImageId = 0
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconClass = 'edit'
                      IconReverseDirection = False
                    end
                    object FHBox76: TFHBox
                      Left = 70
                      Top = 0
                      Width = 4
                      Height = 12
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                  end
                end
                object FVBox42: TFVBox
                  Left = 478
                  Top = 0
                  Width = 5
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 6
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object vBoxCartaoSalvarAlteracao: TFVBox
                  Left = 483
                  Top = 0
                  Width = 170
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 7
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object hBoxCartoesTopSalvarAlterar: TFHBox
                    Left = 0
                    Top = 0
                    Width = 33
                    Height = 13
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FHBox78: TFHBox
                    Left = 0
                    Top = 14
                    Width = 185
                    Height = 34
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    DesignSize = (
                      181
                      30)
                    object btnCartaoSalvarAlteracao: TFButton
                      Left = 0
                      Top = 0
                      Width = 82
                      Height = 28
                      Anchors = []
                      Caption = 'Confirmar'
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 0
                      OnClick = btnCartaoSalvarAlteracaoClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                        F8000000F44944415478DA6364A031601CB560D40262800E10FB00F15420FE4C
                        6D0BCC80781710F303F149207645B784120B7481781F108B2089F5027109352C
                        D0811A2E8A24F61B88FD81783B311680BC3A1388E70371339A9C1610EF076231
                        34C32380781DBA41D82CB087BA8213CA6F00E246285B136AB8389AE1E140BC1E
                        9B4BB159D005C4A56862B540BC066AB804B186E3B20094227602B1399A382875
                        F09262382E0B40800B88B702B1030EF91F401C0075085E802F157103F1162C96
                        106D38210B4040800192914CA1FC5FD060D9408CE1C458806C0928638192E246
                        620D27D6021060678024DB0FA4184E8A056483510B06DE0200931E291901D395
                        FF0000000049454E44AE426082}
                      ImageId = 220011
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconReverseDirection = False
                    end
                    object FHBox79: TFHBox
                      Left = 82
                      Top = 0
                      Width = 4
                      Height = 12
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object btnCartaoCancelarAlteracao: TFButton
                      Left = 86
                      Top = 0
                      Width = 82
                      Height = 28
                      Anchors = []
                      Caption = 'Cancelar'
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 2
                      OnClick = btnCartaoCancelarAlteracaoClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                        F8000001204944415478DA6364A031601C11168803F10720FE490B0B8C817837
                        103702F1446A5B6001C43B81980F88AB80B89D9A16C05C2E08E53701F10420FE
                        0DC45F28B5C00888F720198E0EFE3040E204842F02F166205E0DC4DF88B1C008
                        EA72211243E225101703F1524216800C7721D1706450080D4A9C166800F13E20
                        9624D3825F5033EEE3B20004E481F800102B9069493510B7E1B3000434A13E91
                        80F2D193292F10EB00B13D102700B13A92DC2620F6276401BA25F8F2013B10CF
                        06E25828FF2403240F11B40066C97E206E01E22978D4812CB9090DDED3406C46
                        AC05B0E0F80AC4FF08A8EB02E252205E0CC471A458402C0019BA1088D3A04146
                        750BBC1820395A0A883FD2C28200200E420E1E6A5BA0C700A9336ED2CA02AC60
                        D4028200007DC932197643AC4E0000000049454E44AE426082}
                      ImageId = 220020
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconReverseDirection = False
                    end
                  end
                end
              end
              object FHBox80: TFHBox
                Left = 0
                Top = 172
                Width = 33
                Height = 4
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
            object FHBox39: TFHBox
              Left = 704
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 3
              Visible = False
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object FHBox34: TFHBox
            Left = 0
            Top = 301
            Width = 33
            Height = 4
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
      end
      object tbsCheques: TFTabsheet
        Caption = 'Informe os cheques'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxInformeCheques: TFVBox
          Left = 0
          Top = 0
          Width = 839
          Height = 306
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox48: TFHBox
            Left = 0
            Top = 0
            Width = 746
            Height = 37
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox50: TFHBox
              Left = 0
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox15: TFVBox
              Left = 4
              Top = 0
              Width = 58
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox51: TFHBox
                Left = 0
                Top = 0
                Width = 33
                Height = 4
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblChequesValorTotal: TFLabel
                Left = 0
                Top = 5
                Width = 53
                Height = 13
                Caption = 'Valor total:'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FVBox16: TFVBox
              Left = 62
              Top = 0
              Width = 117
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox52: TFHBox
                Left = 0
                Top = 0
                Width = 33
                Height = 1
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object edtChequeValorTotal: TFDecimal
                Left = 0
                Top = 2
                Width = 108
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Precision = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                Mode = dmDecimal
              end
            end
            object FHBox54: TFHBox
              Left = 179
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 3
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox17: TFVBox
              Left = 183
              Top = 0
              Width = 67
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 4
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox55: TFHBox
                Left = 0
                Top = 0
                Width = 33
                Height = 4
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblChequesObservacao: TFLabel
                Left = 0
                Top = 5
                Width = 62
                Height = 13
                Caption = 'Observa'#231#227'o:'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FVBox18: TFVBox
              Left = 250
              Top = 0
              Width = 361
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 5
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox56: TFHBox
                Left = 0
                Top = 0
                Width = 33
                Height = 1
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object edtChequesObservacao: TFString
                Left = 0
                Top = 2
                Width = 341
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 60
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object FHBox70: TFHBox
              Left = 611
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 6
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object hBoxInformeCheques: TFHBox
            Left = 0
            Top = 38
            Width = 834
            Height = 253
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox57: TFHBox
              Left = 0
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox27: TFVBox
              Left = 4
              Top = 0
              Width = 289
              Height = 212
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stSingleLine
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object grdChequesCondicao: TFGrid
                Left = 0
                Top = 0
                Width = 287
                Height = 120
                TabOrder = 0
                TitleFont.Charset = DEFAULT_CHARSET
                TitleFont.Color = clWindowText
                TitleFont.Height = -11
                TitleFont.Name = 'Tahoma'
                TitleFont.Style = []
                Table = tbLeadsFormaPgtoCheque
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Paging.Enabled = False
                Paging.PageSize = 0
                Paging.DbPaging = False
                FrozenColumns = 0
                ShowFooter = False
                ShowHeader = True
                MultiSelection = False
                Grouping.Enabled = False
                Grouping.Expanded = False
                Grouping.ShowFooter = False
                Crosstab.Enabled = False
                Crosstab.GroupType = cgtConcat
                EnablePopup = False
                WOwner = FrInterno
                WOrigem = EhNone
                EditionEnabled = False
                AuxColumnHeaders = <>
                NoBorder = False
                ActionButtons.BtnAccept = False
                ActionButtons.BtnView = False
                ActionButtons.BtnEdit = False
                ActionButtons.BtnDelete = False
                ActionButtons.BtnInLineEdit = False
                CustomActionButtons = <>
                ActionColumn.Title = 'A'#231#245'es'
                ActionColumn.Width = 100
                ActionColumn.TextAlign = taCenter
                ActionColumn.Visible = True
                Columns = <
                  item
                    Expanded = False
                    FieldName = 'DESCRICAO'
                    Font = <>
                    Title.Caption = 'Condi'#231#227'o'
                    Width = 153
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = True
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{74D28B53-C990-4321-842C-C3549AE6EE4E}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'ENTRADA_DIAS'
                    Font = <>
                    Title.Caption = 'Entr'
                    Width = 35
                    Visible = True
                    Precision = 0
                    TextAlign = taCenter
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{F2B2542D-2DB7-4BA4-8ECB-507421A2DF4F}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'INTERVALO'
                    Font = <>
                    Title.Caption = 'Interv'
                    Width = 40
                    Visible = True
                    Precision = 0
                    TextAlign = taCenter
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{A2CB2D7A-6E9F-4892-963C-59B373371D9F}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'TOTAL_PARCELAS'
                    Font = <>
                    Title.Caption = 'Parc'
                    Width = 35
                    Visible = True
                    Precision = 0
                    TextAlign = taCenter
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{DDF77214-29FB-4283-87CD-99B752C8DC83}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end>
              end
              object FHBox58: TFHBox
                Left = 0
                Top = 121
                Width = 284
                Height = 29
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox19: TFHBox
                  Left = 0
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object btnIncluirCheques: TFButton
                  Left = 4
                  Top = 0
                  Width = 111
                  Height = 25
                  Caption = 'Incluir Cheques'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnIncluirChequesClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                    E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                    B9B9B6418D210000000049454E44AE426082}
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconClass = 'sign-in'
                  IconReverseDirection = False
                end
                object FHBox59: TFHBox
                  Left = 115
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object btnExcluirCheques: TFButton
                  Left = 119
                  Top = 0
                  Width = 111
                  Height = 25
                  Caption = 'Excluir Cheques'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 3
                  OnClick = btnExcluirChequesClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                    E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                    B9B9B6418D210000000049454E44AE426082}
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconClass = 'trash-o'
                  IconReverseDirection = False
                end
              end
              object FHBox64: TFHBox
                Left = 0
                Top = 151
                Width = 282
                Height = 34
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stSingleLine
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                ParentBackground = False
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox65: TFHBox
                  Left = 0
                  Top = 0
                  Width = 4
                  Height = 12
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox29: TFVBox
                  Left = 4
                  Top = 0
                  Width = 64
                  Height = 30
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox66: TFHBox
                    Left = 0
                    Top = 0
                    Width = 33
                    Height = 4
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel2: TFLabel
                    Left = 0
                    Top = 5
                    Width = 60
                    Height = 13
                    Caption = 'Falta lan'#231'ar:'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object FVBox30: TFVBox
                  Left = 68
                  Top = 0
                  Width = 117
                  Height = 30
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox67: TFHBox
                    Left = 0
                    Top = 0
                    Width = 33
                    Height = 1
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object edtChequeValorFaltaLancar: TFDecimal
                    Left = 0
                    Top = 2
                    Width = 108
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = True
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    Maxlength = 0
                    Precision = 0
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    Alignment = taRightJustify
                    Mode = dmDecimal
                  end
                end
              end
            end
            object FHBox60: TFHBox
              Left = 293
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object vBoxGridCheques: TFVBox
              Left = 297
              Top = 0
              Width = 531
              Height = 226
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stSingleLine
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 3
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object grdChequesInformados: TFGrid
                Left = 0
                Top = 0
                Width = 456
                Height = 120
                TabOrder = 0
                TitleFont.Charset = DEFAULT_CHARSET
                TitleFont.Color = clWindowText
                TitleFont.Height = -11
                TitleFont.Name = 'Tahoma'
                TitleFont.Style = []
                Table = tbLeadsPgtoVendaInfCheque
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Paging.Enabled = False
                Paging.PageSize = 0
                Paging.DbPaging = False
                FrozenColumns = 0
                ShowFooter = False
                ShowHeader = True
                MultiSelection = False
                Grouping.Enabled = False
                Grouping.Expanded = False
                Grouping.ShowFooter = False
                Crosstab.Enabled = False
                Crosstab.GroupType = cgtConcat
                EnablePopup = False
                WOwner = FrInterno
                WOrigem = EhNone
                EditionEnabled = False
                AuxColumnHeaders = <>
                NoBorder = False
                ActionButtons.BtnAccept = False
                ActionButtons.BtnView = False
                ActionButtons.BtnEdit = False
                ActionButtons.BtnDelete = False
                ActionButtons.BtnInLineEdit = False
                CustomActionButtons = <>
                ActionColumn.Title = 'A'#231#245'es'
                ActionColumn.Width = 100
                ActionColumn.TextAlign = taCenter
                ActionColumn.Visible = True
                Columns = <
                  item
                    Expanded = False
                    FieldName = 'NR_CHEQUE'
                    Font = <>
                    Title.Caption = 'Nr. Cheque'
                    Width = 80
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{2F767722-E111-445A-8114-28C00A451FBE}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'DATA_VENCIMENTO'
                    Font = <>
                    Title.Caption = 'Dt.Vencimento'
                    Width = 90
                    Visible = True
                    Precision = 0
                    TextAlign = taCenter
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{5E27C418-1DBB-452E-8594-AB29EFCD610F}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = 'dd/MM/yyyy'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDateTime
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{7359AA4E-A138-49A3-8A9E-FFB94D20EB43}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'VALOR'
                    Font = <>
                    Title.Caption = 'Valor'
                    Width = 105
                    Visible = True
                    Precision = 0
                    TextAlign = taRight
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{CC193B45-03F1-46B5-9759-1E8F37B855D1}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = ',##0.00'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDecimal
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{85979D08-B397-4D03-84E9-CCCB5BB443DC}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    Font = <>
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = True
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{D1446EF7-9BB6-4668-AF1D-1C4E09896F97}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end>
              end
              object hBoxEditarCheques: TFHBox
                Left = 0
                Top = 121
                Width = 781
                Height = 48
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FVBox5: TFVBox
                  Left = 0
                  Top = 0
                  Width = 8
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox28: TFVBox
                  Left = 8
                  Top = 0
                  Width = 86
                  Height = 45
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblChequeNr: TFLabel
                    Left = 0
                    Top = 0
                    Width = 55
                    Height = 13
                    Align = alLeft
                    Caption = 'Nr. Cheque'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhAttribute
                    WKey = '7000139;70004;70002'
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object edtChequeNumero: TFString
                    Left = 0
                    Top = 14
                    Width = 81
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    CharCase = ccNormal
                    Pwd = False
                    Maxlength = 8
                    Enabled = False
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    OnExit = edtChequeNumeroExit
                    SaveLiteralCharacter = False
                    TextAlign = taLeft
                  end
                end
                object FVBox33: TFVBox
                  Left = 94
                  Top = 0
                  Width = 4
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox34: TFVBox
                  Left = 98
                  Top = 0
                  Width = 127
                  Height = 45
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 3
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblChequeVencimento: TFLabel
                    Left = 0
                    Top = 0
                    Width = 70
                    Height = 13
                    Align = alLeft
                    Caption = 'Dt.Vencimento'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhAttribute
                    WKey = '7000139;70004;70002'
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object edtChequeDtVencimento: TFDate
                    Left = 0
                    Top = 14
                    Width = 121
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    Format = 'dd/MM/yyyy'
                    ShowCheckBox = False
                    Enabled = False
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ShowTime = False
                    ShowOnFocus = False
                  end
                end
                object FVBox35: TFVBox
                  Left = 225
                  Top = 0
                  Width = 4
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 4
                  Visible = False
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox32: TFVBox
                  Left = 229
                  Top = 0
                  Width = 115
                  Height = 45
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 5
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FLabel6: TFLabel
                    Left = 0
                    Top = 0
                    Width = 24
                    Height = 13
                    Align = alLeft
                    Caption = 'Valor'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhAttribute
                    WKey = '7000139;70004;70002'
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object edtChequeValor: TFDecimal
                    Left = 0
                    Top = 14
                    Width = 108
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = True
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    Maxlength = 0
                    Precision = 0
                    Enabled = False
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    Alignment = taRightJustify
                    Mode = dmDecimal
                  end
                end
                object FVBox31: TFVBox
                  Left = 344
                  Top = 0
                  Width = 5
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 6
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object vBoxChequeAlterar: TFVBox
                  Left = 349
                  Top = 0
                  Width = 154
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 7
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object hBoxChequeBtnAlterarTop: TFHBox
                    Left = 0
                    Top = 0
                    Width = 33
                    Height = 13
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FHBox73: TFHBox
                    Left = 0
                    Top = 14
                    Width = 152
                    Height = 34
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    DesignSize = (
                      148
                      30)
                    object btnChequeAlterar: TFButton
                      Left = 0
                      Top = 0
                      Width = 70
                      Height = 25
                      Hint = 'Alterar cheque'
                      Anchors = []
                      Caption = 'Alterar'
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 0
                      OnClick = btnChequeAlterarClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                        F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                        E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                        B9B9B6418D210000000049454E44AE426082}
                      ImageId = 0
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconClass = 'edit'
                      IconReverseDirection = False
                    end
                    object FHBox74: TFHBox
                      Left = 70
                      Top = 0
                      Width = 4
                      Height = 12
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object btnChequeExcluir: TFButton
                      Left = 74
                      Top = 0
                      Width = 70
                      Height = 25
                      Hint = 'Excluir cheque'
                      Anchors = []
                      Caption = 'Excluir'
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 2
                      OnClick = btnChequeExcluirClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                        F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                        E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                        B9B9B6418D210000000049454E44AE426082}
                      ImageId = 0
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconClass = 'trash'
                      IconReverseDirection = False
                    end
                  end
                end
                object vBoxChequesSalvarAlteracoes: TFVBox
                  Left = 503
                  Top = 0
                  Width = 170
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 8
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object hBoxChequeBtnSalvarTop: TFHBox
                    Left = 0
                    Top = 0
                    Width = 33
                    Height = 13
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FHBox71: TFHBox
                    Left = 0
                    Top = 14
                    Width = 185
                    Height = 34
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    DesignSize = (
                      181
                      30)
                    object btnChequeConfirmar: TFButton
                      Left = 0
                      Top = 0
                      Width = 82
                      Height = 25
                      Anchors = []
                      Caption = 'Confirmar'
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 0
                      OnClick = btnChequeConfirmarClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                        F8000000F44944415478DA6364A031601CB560D40262800E10FB00F15420FE4C
                        6D0BCC80781710F303F149207645B784120B7481781F108B2089F5027109352C
                        D0811A2E8A24F61B88FD81783B311680BC3A1388E70371339A9C1610EF076231
                        34C32380781DBA41D82CB087BA8213CA6F00E246285B136AB8389AE1E140BC1E
                        9B4BB159D005C4A56862B540BC066AB804B186E3B20094227602B1399A382875
                        F09262382E0B40800B88B702B1030EF91F401C0075085E802F157103F1162C96
                        106D38210B4040800192914CA1FC5FD060D9408CE1C458806C0928638192E246
                        620D27D6021060678024DB0FA4184E8A056483510B06DE0200931E291901D395
                        FF0000000049454E44AE426082}
                      ImageId = 220011
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconReverseDirection = False
                    end
                    object FHBox75: TFHBox
                      Left = 82
                      Top = 0
                      Width = 4
                      Height = 12
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object btnChequeCancelar: TFButton
                      Left = 86
                      Top = 0
                      Width = 82
                      Height = 25
                      Anchors = []
                      Caption = 'Cancelar'
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 2
                      OnClick = btnChequeCancelarClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                        F8000001204944415478DA6364A031601C11168803F10720FE490B0B8C817837
                        103702F1446A5B6001C43B81980F88AB80B89D9A16C05C2E08E53701F10420FE
                        0DC45F28B5C00888F720198E0EFE3040E204842F02F166205E0DC4DF88B1C008
                        EA72211243E225101703F1524216800C7721D1706450080D4A9C166800F13E20
                        9624D3825F5033EEE3B20004E481F800102B9069493510B7E1B3000434A13E91
                        80F2D193292F10EB00B13D102700B13A92DC2620F6276401BA25F8F2013B10CF
                        06E25828FF2403240F11B40066C97E206E01E22978D4812CB9090DDED3406C46
                        AC05B0E0F80AC4FF08A8EB02E252205E0CC471A458402C0019BA1088D3A04146
                        750BBC1820395A0A883FD2C28200200E420E1E6A5BA0C700A9336ED2CA02AC60
                        D4028200007DC932197643AC4E0000000049454E44AE426082}
                      ImageId = 220020
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconReverseDirection = False
                    end
                  end
                end
              end
            end
          end
        end
      end
      object tbsBoleto: TFTabsheet
        Caption = 'Boleto Banc'#225'rio'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxBoletoBancario: TFVBox
          Left = 0
          Top = 0
          Width = 837
          Height = 314
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox40: TFHBox
            Left = 0
            Top = 0
            Width = 746
            Height = 37
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Visible = False
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox41: TFHBox
              Left = 0
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox12: TFVBox
              Left = 4
              Top = 0
              Width = 58
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox42: TFHBox
                Left = 0
                Top = 0
                Width = 33
                Height = 4
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object FLabel5: TFLabel
                Left = 0
                Top = 5
                Width = 53
                Height = 13
                Caption = 'Valor total:'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FVBox13: TFVBox
              Left = 62
              Top = 0
              Width = 117
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox43: TFHBox
                Left = 0
                Top = 0
                Width = 33
                Height = 1
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object edtBoletoValorTotal: TFDecimal
                Left = 0
                Top = 2
                Width = 108
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Precision = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                Mode = dmDecimal
              end
            end
            object FHBox44: TFHBox
              Left = 179
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 3
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object FHBox45: TFHBox
            Left = 0
            Top = 38
            Width = 733
            Height = 155
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox46: TFHBox
              Left = 0
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Visible = False
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox14: TFVBox
              Left = 4
              Top = 0
              Width = 601
              Height = 150
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object grdBoletos: TFGrid
                Left = 0
                Top = 0
                Width = 595
                Height = 118
                TabOrder = 0
                TitleFont.Charset = DEFAULT_CHARSET
                TitleFont.Color = clWindowText
                TitleFont.Height = -11
                TitleFont.Name = 'Tahoma'
                TitleFont.Style = []
                Table = tbLeadsPgtoVendaInfBoleto
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Paging.Enabled = False
                Paging.PageSize = 0
                Paging.DbPaging = False
                FrozenColumns = 0
                ShowFooter = False
                ShowHeader = True
                MultiSelection = False
                Grouping.Enabled = False
                Grouping.Expanded = False
                Grouping.ShowFooter = False
                Crosstab.Enabled = False
                Crosstab.GroupType = cgtConcat
                EnablePopup = False
                WOwner = FrInterno
                WOrigem = EhNone
                EditionEnabled = False
                AuxColumnHeaders = <>
                NoBorder = False
                ActionButtons.BtnAccept = False
                ActionButtons.BtnView = False
                ActionButtons.BtnEdit = False
                ActionButtons.BtnDelete = False
                ActionButtons.BtnInLineEdit = False
                CustomActionButtons = <>
                ActionColumn.Title = 'A'#231#245'es'
                ActionColumn.Width = 100
                ActionColumn.TextAlign = taCenter
                ActionColumn.Visible = True
                Columns = <
                  item
                    Expanded = False
                    FieldName = 'PARCELA'
                    Font = <>
                    Title.Caption = 'Parcela'
                    Width = 50
                    Visible = True
                    Precision = 0
                    TextAlign = taCenter
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{D4765079-EABD-41EA-B2F5-E3D6015952C3}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'VALOR'
                    Font = <>
                    Title.Caption = 'Valor'
                    Width = 100
                    Visible = True
                    Precision = 0
                    TextAlign = taRight
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{C95692F8-A59F-422E-936C-F6BB9B2280E8}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = ',##0.00'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDecimal
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{7B999DE5-CE96-43F0-928F-92B8264640CF}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'VENCIMENTO'
                    Font = <>
                    Title.Caption = 'Vencimento'
                    Width = 100
                    Visible = True
                    Precision = 0
                    TextAlign = taCenter
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{32B9E9B2-0F92-4284-97A0-CBFFFA5631C3}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = 'dd/MM/yyyy'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDateTime
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{DA407441-**************-61E9618414A3}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'NOSSO_NUMERO'
                    Font = <>
                    Title.Caption = 'Nosso N'#250'mero'
                    Width = 235
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = True
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{C6B45F45-7309-44D6-B1AA-DC7BBF1E89D1}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    Font = <>
                    Width = 5
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    Editor.ShowClearButton = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{992E1155-50A6-4EF9-9110-1DDC2B69D0DE}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end>
              end
            end
            object FHBox47: TFHBox
              Left = 605
              Top = 0
              Width = 4
              Height = 12
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Visible = False
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object FHBox49: TFHBox
            Left = 0
            Top = 194
            Width = 33
            Height = 1
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Color = clSilver
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FHBox170004: TFHBox
            Left = 0
            Top = 196
            Width = 774
            Height = 34
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stSingleLine
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            DesignSize = (
              770
              30)
            object FPanel170004: TFPanel
              Left = 0
              Top = 0
              Width = 24
              Height = 12
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              WOwner = FrInterno
              WOrigem = EhNone
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
            end
            object btnEditDetalheBoleto: TFButton
              Left = 24
              Top = 0
              Width = 92
              Height = 28
              Anchors = []
              Caption = 'Alterar'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 1
              OnClick = btnEditDetalheBoletoClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                B9B9B6418D210000000049454E44AE426082}
              ImageId = 0
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconClass = 'edit'
              IconReverseDirection = False
            end
            object FPanel1: TFPanel
              Left = 116
              Top = 0
              Width = 12
              Height = 15
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 4
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              WOwner = FrInterno
              WOrigem = EhNone
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
            end
            object btnConfirmarBoleto: TFButton
              Left = 128
              Top = 0
              Width = 92
              Height = 28
              Anchors = []
              Caption = 'Confirmar'
              Enabled = False
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 3
              OnClick = btnConfirmarBoletoClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000000F44944415478DA6364A031601CB560D40262800E10FB00F15420FE4C
                6D0BCC80781710F303F149207645B784120B7481781F108B2089F5027109352C
                D0811A2E8A24F61B88FD81783B311680BC3A1388E70371339A9C1610EF076231
                34C32380781DBA41D82CB087BA8213CA6F00E246285B136AB8389AE1E140BC1E
                9B4BB159D005C4A56862B540BC066AB804B186E3B20094227602B1399A382875
                F09262382E0B40800B88B702B1030EF91F401C0075085E802F157103F1162C96
                106D38210B4040800192914CA1FC5FD060D9408CE1C458806C0928638192E246
                620D27D6021060678024DB0FA4184E8A056483510B06DE0200931E291901D395
                FF0000000049454E44AE426082}
              ImageId = 220011
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
            object FPanel2: TFPanel
              Left = 220
              Top = 0
              Width = 12
              Height = 15
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              WOwner = FrInterno
              WOrigem = EhNone
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
            end
            object btnCancelarBoleto: TFButton
              Left = 232
              Top = 0
              Width = 92
              Height = 28
              Anchors = []
              Caption = 'Cancelar'
              Enabled = False
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 5
              OnClick = btnCancelarBoletoClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000001204944415478DA6364A031601C11168803F10720FE490B0B8C817837
                103702F1446A5B6001C43B81980F88AB80B89D9A16C05C2E08E53701F10420FE
                0DC45F28B5C00888F720198E0EFE3040E204842F02F166205E0DC4DF88B1C008
                EA72211243E225101703F1524216800C7721D1706450080D4A9C166800F13E20
                9624D3825F5033EEE3B20004E481F800102B9069493510B7E1B3000434A13E91
                80F2D193292F10EB00B13D102700B13A92DC2620F6276401BA25F8F2013B10CF
                06E25828FF2403240F11B40066C97E206E01E22978D4812CB9090DDED3406C46
                AC05B0E0F80AC4FF08A8EB02E252205E0CC471A458402C0019BA1088D3A04146
                750BBC1820395A0A883FD2C28200200E420E1E6A5BA0C700A9336ED2CA02AC60
                D4028200007DC932197643AC4E0000000049454E44AE426082}
              ImageId = 220020
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
          end
          object hBoxDetalheBoleto: TFHBox
            Left = 0
            Top = 231
            Width = 781
            Height = 50
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox19: TFVBox
              Left = 0
              Top = 0
              Width = 24
              Height = 41
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox20: TFVBox
              Left = 24
              Top = 0
              Width = 43
              Height = 45
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblBoletoParcela: TFLabel
                Left = 0
                Top = 0
                Width = 35
                Height = 13
                Align = alLeft
                Caption = 'Parcela'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhAttribute
                WKey = '7000139;70004;70002'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtBoletoParc: TFInteger
                Left = 0
                Top = 14
                Width = 22
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
              end
            end
            object FVBox21: TFVBox
              Left = 67
              Top = 0
              Width = 8
              Height = 41
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox22: TFVBox
              Left = 75
              Top = 0
              Width = 115
              Height = 45
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 3
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblBoletoValor: TFLabel
                Left = 0
                Top = 0
                Width = 24
                Height = 13
                Align = alLeft
                Caption = 'Valor'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhAttribute
                WKey = '7000139;70004;70002'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtBoletoValor: TFDecimal
                Left = 0
                Top = 14
                Width = 108
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Precision = 0
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                Mode = dmDecimal
              end
            end
            object FVBox23: TFVBox
              Left = 190
              Top = 0
              Width = 8
              Height = 41
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 4
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox24: TFVBox
              Left = 198
              Top = 0
              Width = 133
              Height = 45
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 5
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblBoletoVencimento: TFLabel
                Left = 0
                Top = 0
                Width = 55
                Height = 13
                Align = alLeft
                Caption = 'Vencimento'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhAttribute
                WKey = '7000139;70004;70002'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtBoletoVencimento: TFDate
                Left = 0
                Top = 14
                Width = 121
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Format = 'dd/MM/yyyy'
                ShowCheckBox = False
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ShowTime = False
                ShowOnFocus = False
              end
            end
            object FVBox25: TFVBox
              Left = 331
              Top = 0
              Width = 8
              Height = 41
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 6
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FVBox26: TFVBox
              Left = 339
              Top = 0
              Width = 270
              Height = 45
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 7
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblBoletoNossoNumero: TFLabel
                Left = 0
                Top = 0
                Width = 69
                Height = 13
                Align = alLeft
                Caption = 'Nosso N'#250'mero'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhAttribute
                WKey = '7000139;70004;70002'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtBoletoNossoNumero: TFString
                Left = 0
                Top = 14
                Width = 260
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 30
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object FVBox6: TFVBox
              Left = 609
              Top = 0
              Width = 8
              Height = 41
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 8
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
        end
      end
    end
  end
  object FHBox53: TFHBox
    Left = 0
    Top = 412
    Width = 50
    Height = 3
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 3
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
  end
  object tbLeadsPgtoVendaInfCartao: TFTable
    FieldDefs = <
      item
        Name = 'COD_PAGAMENTO_INF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pagamento Inf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Orc Mapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_VENCIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Vencimento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PARCELA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Parcela'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CARTAO_CREDITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cart'#227'o Cr'#233'dito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NR_AUTORIZACAO_VISA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Autoriza'#231#227'o Visa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NR_NSU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Nsu'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Cart'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_ACRES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Acres'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_ORIGINAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Original'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'PAGAMENTO_VENDA_INF'
    Cursor = 'LEADS_PGTO_VENDA_INF_CARTAO'
    MaxRowCount = 200
    OnAfterScroll = tbLeadsPgtoVendaInfCartaoAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '183028;18301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 552
    Top = 20
  end
  object tbLeadsCartoes: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CARTAO_CREDITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cart'#227'o Cr'#233'dito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Cart'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NR_PARCELAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Parcelas'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TRATAR_DIF_PRIM_ULTIMA_PARC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tratar Dif Prim '#218'ltima Parc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIAS_VENCIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Dias Vencimento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_ACRES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Acres'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_CARTOES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '183028;18302'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 492
    Top = 16
  end
  object tbLeadsPgtoVendaInfBoleto: TFTable
    FieldDefs = <
      item
        Name = 'PARCELA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Parcela'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENCIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Vencimento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Orc Mapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOSSO_NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nosso N'#250'mero'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_PGTO_VENDA_INF_BOLETO'
    MaxRowCount = 200
    OnAfterScroll = tbLeadsPgtoVendaInfBoletoAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '183028;18303'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 634
    Top = 19
  end
  object tbLeadsFormaPgtoCheque: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTRADA_DIAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Entrada Dias'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERVALO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Intervalo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_PARCELAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Parcelas'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_PGTO_VENDA_FORMA_CHEQUE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '183028;18304'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsPgtoVendaInfCheque: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Orc Mapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NR_CHEQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Cheque'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NR_AGENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Ag'#234'ncia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NR_BANCO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Banco'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRACA_COMP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Praca Comp'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NR_CONTA_CORRENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Conta Corrente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_VENCIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Vencimento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_ORIGINAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Original'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_PGTO_VENDA_INF_CHEQUE'
    MaxRowCount = 200
    OnAfterScroll = tbLeadsPgtoVendaInfChequeAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '183028;18305'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 690
    Top = 19
  end
end
