object FrmOficinaManutencaoAlterarServ: TFForm
  Left = 44
  Top = 163
  ActiveControl = hBoxBtnTopo
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Alterar Servi'#231'o'
  ClientHeight = 451
  ClientWidth = 504
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600264'
  ShortcutKeys = <>
  InterfaceRN = 'OficinaManutencaoAlterarServRN'
  Access = False
  ChangedProp = 
    'FrmOficinaManutencaoAlterarServ.ActiveControlFrmOficinaManutenca' +
    'oAlterarServ.Height;'#13#10'FrmOficinaManutencaoAlterarServ.Width;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object hBoxBtnTopo: TFHBox
    Left = 0
    Top = 0
    Width = 504
    Height = 65
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 3
    Padding.Left = 5
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object btnVoltar: TFButton
      Left = 0
      Top = 0
      Width = 60
      Height = 59
      Hint = 'Voltar'
      Caption = 'Voltar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 0
      OnClick = btnVoltarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
        FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
        290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
        086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
        152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
        F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
        86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
        B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
        AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
        EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
        AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
        736BB6EF9B710000000049454E44AE426082}
      ImageId = 700081
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
    object btnAceitar: TFButton
      Left = 60
      Top = 0
      Width = 60
      Height = 59
      Hint = 'Aceitar'
      Caption = 'Aceitar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 1
      OnClick = btnAceitarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
        0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
        1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
        95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
        101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
        1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
        A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
        C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
        482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
        1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
        C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
        8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
      ImageId = 700088
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
  end
  object vBoxForm: TFVBox
    Left = 0
    Top = 65
    Width = 504
    Height = 386
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftMin
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxDadosServico: TFHBox
      Left = 0
      Top = 0
      Width = 550
      Height = 50
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 5
      Padding.Right = 5
      Padding.Bottom = 10
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object vBoxCodServico: TFVBox
        Left = 0
        Top = 0
        Width = 68
        Height = 47
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 5
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblCaptionCodigo: TFLabel
          Left = 0
          Top = 0
          Width = 38
          Height = 13
          Caption = 'C'#243'digo'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object vBoxlblCodServico: TFVBox
          Left = 0
          Top = 14
          Width = 65
          Height = 25
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblCodServico: TFLabel
            Left = 0
            Top = 0
            Width = 64
            Height = 13
            Caption = 'lblCodServico'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = 13392431
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = [fsUnderline]
            ParentFont = False
            OnClick = lblCodServicoClick
            FieldName = 'COD_SERVICO'
            Table = tbOficinaManutServicos
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
        end
      end
      object vBoxServico: TFVBox
        Left = 68
        Top = 0
        Width = 105
        Height = 47
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 5
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblServico: TFLabel
          Left = 0
          Top = 0
          Width = 42
          Height = 13
          Caption = 'Servi'#231'o'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object vBoxlblDescricaoServico: TFVBox
          Left = 0
          Top = 14
          Width = 100
          Height = 29
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblDescricaoServico: TFLabel
            Left = 0
            Top = 0
            Width = 35
            Height = 13
            Caption = 'Servi'#231'o'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            FieldName = 'DESCRICAO'
            Table = tbOficinaManutServicos
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = True
            MaskType = mtText
          end
        end
      end
      object vBoxTMOOriginal: TFVBox
        Left = 173
        Top = 0
        Width = 85
        Height = 47
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 5
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblDescricaoTMO: TFLabel
          Left = 0
          Top = 0
          Width = 71
          Height = 13
          Caption = 'TMO Original'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object FHBox1: TFHBox
          Left = 0
          Top = 14
          Width = 74
          Height = 26
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 5
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox1: TFVBox
            Left = 0
            Top = 0
            Width = 17
            Height = 13
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblTMO: TFLabel
            Left = 17
            Top = 0
            Width = 22
            Height = 13
            Caption = '0,00'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            Mask = '##0.00'
            WordBreak = True
            MaskType = mtText
          end
        end
      end
    end
    object FPageControlPrincipal: TFPageControl
      Left = 0
      Top = 51
      Width = 500
      Height = 332
      ActivePage = tabNaoCobra
      TabOrder = 1
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabTempo: TFTabsheet
        Caption = 'Tempo'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        object FVBox5: TFVBox
          Left = 0
          Top = 0
          Width = 492
          Height = 304
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox4: TFHBox
            Left = 0
            Top = 0
            Width = 490
            Height = 81
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox6: TFVBox
              Left = 0
              Top = 0
              Width = 111
              Height = 76
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 3
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel1: TFLabel
                Left = 0
                Top = 0
                Width = 25
                Height = 13
                Caption = 'TMO'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FHBox3: TFHBox
                Left = 0
                Top = 14
                Width = 110
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object edtTmo: TFDecimal
                  Left = 0
                  Top = 0
                  Width = 70
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Precision = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  Mode = dmDecimal
                end
                object vBoxIconApagarTmo: TFVBox
                  Left = 70
                  Top = 0
                  Width = 43
                  Height = 34
                  Align = alLeft
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 5
                  Padding.Left = 11
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  OnClick = vBoxIconApagarTmoClick
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 0
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FImage1: TFImage
                    Left = 0
                    Top = 0
                    Width = 16
                    Height = 18
                    Align = alClient
                    Stretch = False
                    ImageSrc = '/images/crmservice4600235.png'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxSize = 0
                    GrayScaleOnDisable = False
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    Preview = False
                    ImageId = 0
                  end
                end
              end
            end
            object FVBox7: TFVBox
              Left = 111
              Top = 0
              Width = 30
              Height = 75
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 30
              Padding.Left = 10
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel2: TFLabel
                Left = 0
                Top = 0
                Width = 9
                Height = 16
                Caption = 'X'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FVBox8: TFVBox
              Left = 141
              Top = 0
              Width = 120
              Height = 76
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 3
              Flex.Vflex = ftMin
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel3: TFLabel
                Left = 0
                Top = 0
                Width = 76
                Height = 13
                Caption = 'Valor da Hora'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtTempoValorHora: TFDecimal
                Left = 0
                Top = 14
                Width = 110
                Height = 24
                Table = tbOficinaManutServicos
                FieldName = 'VALOR_HORA'
                TabOrder = 0
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Precision = 0
                Format = 'R$ ##0.00'
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                Mode = dmDecimal
              end
              object FLabel4: TFLabel
                Left = 0
                Top = 39
                Width = 41
                Height = 13
                Caption = 'Origem'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object lblOrigemValorHora: TFLabel
                Left = 0
                Top = 53
                Width = 100
                Height = 13
                Caption = 'Setor da Seguradora'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                FieldName = 'ORIGEM_VALOR_HORA'
                Table = tbOficinaManutServicos
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FVBox9: TFVBox
              Left = 261
              Top = 0
              Width = 30
              Height = 75
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 30
              Padding.Left = 10
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 3
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel6: TFLabel
                Left = 0
                Top = 0
                Width = 11
                Height = 16
                Caption = '='
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FVBox10: TFVBox
              Left = 291
              Top = 0
              Width = 118
              Height = 76
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 4
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 3
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel7: TFLabel
                Left = 0
                Top = 0
                Width = 91
                Height = 13
                Caption = 'Valor do Servi'#231'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtTempoValorServico: TFDecimal
                Left = 0
                Top = 14
                Width = 110
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Precision = 0
                Format = 'R$ ##0.00'
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                Mode = dmDecimal
              end
            end
            object btnSimular: TFButton
              Left = 409
              Top = 0
              Width = 65
              Height = 65
              Caption = 'Simular'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -16
              Font.Name = 'Tahoma'
              Font.Style = []
              Layout = blGlyphTop
              ParentFont = False
              TabOrder = 5
              OnClick = btnSimularClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000001CA4944415478DAED95CB2B455114C6D7F54A8A8181BA915248B9E551
                32A05C33269432243163A48C4C30F11F780C2831F41811C9E00E1871F3187884
                AE9232A14492707D5F679FECAB73F6D9D7D8AA5F9DBDCEDEEB3B67ADBDD70E89
                D9B24103E8002DA01C7C8213B00B56C039F8F20B10F2F167816130A99E4DF602
                06C0AA12A7658005F0EC255006F64058D2B30468040F600D747AFD412D384C33
                B06E4CD531A8F34A5109B8F558C43C8F834BF0AED6B0369560027499145D01E6
                F906146BEF5E4114EC077C7544A5B4C024D00FE635FF9B127BB4480B0BBA01DA
                FC0432C5D909B99ABF5EEC6AC1E0CBA63451A0061C69BE2DD06E119CB6087A4C
                1328C03D3CA7F9AAC085A5C0141834BC9F0E5906FAB3FD0B580904160A0C59C6
                E30639D3C6510A2403162D815E4B814D493D70E1200176C56E31F47BCD7838E3
                DA987DADD424B02DCE81B3095E08EE24B51BB482984980CDAE59825B066FBC18
                C8D37C71E54FDAD480AD63149C8AD3AE393F075480319542DD38A7083C71F05B
                805FCBDE946191162F6370EEA484EBD005DC8252FD4052EF061BBB5629BDD79D
                EE39C8077DF253505E40EC92B32A1D26E3DD310266C46343049D640A45D49F35
                816AE5BF023B605D9C827EF805F806234A5B93CE66A4AE0000000049454E44AE
                426082}
              ImageId = 700071
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
          end
          object chkTempoCruzado: TFCheckBox
            Left = 0
            Top = 82
            Width = 497
            Height = 17
            Caption = 'N'#227'o tem Tempo Cruzado para o Modelo. J'#225' Cruza com o TMO Acima ?'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clRed
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            TabOrder = 1
            ReadOnly = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taAlignTop
          end
          object FVBox11: TFVBox
            Left = 0
            Top = 100
            Width = 454
            Height = 64
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 3
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel9: TFLabel
              Left = 0
              Top = 0
              Width = 39
              Height = 13
              Caption = 'Motivo'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object edtTempoMotivo: TFString
              Left = 0
              Top = 14
              Width = 433
              Height = 24
              TabOrder = 0
              AccessLevel = 0
              Flex = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              IconDirection = idLeft
              CharCase = ccNormal
              Pwd = False
              Maxlength = 0
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              SaveLiteralCharacter = False
              TextAlign = taLeft
            end
          end
        end
      end
      object tabValor: TFTabsheet
        Caption = 'Valor'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        object FVBox12: TFVBox
          Left = 0
          Top = 0
          Width = 492
          Height = 304
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox13: TFVBox
            Left = 0
            Top = 0
            Width = 492
            Height = 64
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 3
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel10: TFLabel
              Left = 0
              Top = 0
              Width = 118
              Height = 13
              Caption = 'Novo Pre'#231'o de Venda'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object FHBox2: TFHBox
              Left = 0
              Top = 14
              Width = 313
              Height = 41
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object edtValor: TFDecimal
                Left = 0
                Top = 0
                Width = 115
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Precision = 0
                Format = 'R$ ##0.00'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                Mode = dmDecimal
              end
              object vBoxIconApagarPrecoVenda: TFVBox
                Left = 115
                Top = 0
                Width = 43
                Height = 34
                Align = alLeft
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 5
                Padding.Left = 11
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                OnClick = vBoxIconApagarPrecoVendaClick
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 0
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object iconTrash: TFImage
                  Left = 0
                  Top = 0
                  Width = 16
                  Height = 18
                  Align = alClient
                  Stretch = False
                  ImageSrc = '/images/crmservice4600235.png'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxSize = 0
                  GrayScaleOnDisable = False
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  Preview = False
                  ImageId = 0
                end
              end
            end
          end
          object FVBox14: TFVBox
            Left = 0
            Top = 65
            Width = 494
            Height = 64
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 3
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel11: TFLabel
              Left = 0
              Top = 0
              Width = 39
              Height = 13
              Caption = 'Motivo'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object edtValorMotivo: TFString
              Left = 0
              Top = 14
              Width = 473
              Height = 24
              TabOrder = 0
              AccessLevel = 0
              Flex = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              IconDirection = idLeft
              CharCase = ccNormal
              Pwd = False
              Maxlength = 0
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              SaveLiteralCharacter = False
              TextAlign = taLeft
            end
          end
        end
      end
      object tabNaoCobra: TFTabsheet
        Caption = 'N'#227'o Cobrar'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox15: TFVBox
          Left = 0
          Top = 0
          Width = 492
          Height = 304
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox16: TFVBox
            Left = 0
            Top = 0
            Width = 534
            Height = 45
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox5: TFHBox
              Left = 0
              Top = 0
              Width = 485
              Height = 45
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox6: TFHBox
                Left = 0
                Top = 0
                Width = 290
                Height = 45
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox9: TFHBox
                  Left = 0
                  Top = 0
                  Width = 13
                  Height = 31
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FHBox8: TFHBox
                  Left = 13
                  Top = 0
                  Width = 219
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object hboxAlterarServTempo: TFHBox
                    Left = 0
                    Top = 0
                    Width = 106
                    Height = 36
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 5
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    OnClick = hboxAlterarServTempoClick
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FHBox11: TFHBox
                      Left = 0
                      Top = 0
                      Width = 13
                      Height = 31
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object FLabel13: TFLabel
                      Left = 13
                      Top = 0
                      Width = 61
                      Height = 13
                      Caption = 'Por Tempo'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = [fsBold]
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object FHBox12: TFHBox
                      Left = 74
                      Top = 0
                      Width = 13
                      Height = 31
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                  end
                  object hboxAlterarServValor: TFHBox
                    Left = 106
                    Top = 0
                    Width = 106
                    Height = 36
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 5
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    OnClick = hboxAlterarServValorClick
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FHBox13: TFHBox
                      Left = 0
                      Top = 0
                      Width = 13
                      Height = 31
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object FLabel14: TFLabel
                      Left = 13
                      Top = 0
                      Width = 51
                      Height = 13
                      Caption = 'Por Valor'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = [fsBold]
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object FHBox14: TFHBox
                      Left = 64
                      Top = 0
                      Width = 13
                      Height = 31
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftTrue
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                  end
                end
                object FHBox10: TFHBox
                  Left = 232
                  Top = 0
                  Width = 13
                  Height = 31
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
          end
          object vboxPainelTempo: TFVBox
            Left = 0
            Top = 46
            Width = 485
            Height = 126
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox15: TFHBox
              Left = 0
              Top = 0
              Width = 480
              Height = 64
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox18: TFVBox
                Left = 0
                Top = 0
                Width = 125
                Height = 62
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 3
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel15: TFLabel
                  Left = 0
                  Top = 0
                  Width = 25
                  Height = 13
                  Caption = 'TMO'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object FHBox7: TFHBox
                  Left = 0
                  Top = 14
                  Width = 116
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object edtTmoPorTempo: TFDecimal
                    Left = 0
                    Top = 0
                    Width = 90
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    Maxlength = 0
                    Precision = 0
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    Alignment = taRightJustify
                    OnEnter = edtTmoPorTempoEnter
                    OnExit = edtTmoPorTempoExit
                    Mode = dmDecimal
                  end
                  object vBoxApagarTmoNc: TFVBox
                    Left = 90
                    Top = 0
                    Width = 43
                    Height = 34
                    Align = alLeft
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 5
                    Padding.Left = 11
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    OnClick = vBoxApagarTmoNcClick
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 0
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FImage2: TFImage
                      Left = 0
                      Top = 0
                      Width = 16
                      Height = 18
                      Align = alClient
                      Stretch = False
                      ImageSrc = '/images/crmservice4600235.png'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxSize = 0
                      GrayScaleOnDisable = False
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      Preview = False
                      ImageId = 0
                    end
                  end
                end
              end
              object FVBox19: TFVBox
                Left = 125
                Top = 0
                Width = 40
                Height = 62
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 30
                Padding.Left = 10
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel16: TFLabel
                  Left = 0
                  Top = 0
                  Width = 6
                  Height = 13
                  Caption = 'X'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
              end
              object FVBox20: TFVBox
                Left = 165
                Top = 0
                Width = 125
                Height = 62
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 3
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel17: TFLabel
                  Left = 0
                  Top = 0
                  Width = 76
                  Height = 13
                  Caption = 'Valor da Hora'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object FDecimal4: TFDecimal
                  Left = 0
                  Top = 14
                  Width = 115
                  Height = 24
                  Table = tbOficinaManutServicos
                  FieldName = 'VALOR_HORA'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Precision = 0
                  Format = 'R$ ,##0.00'
                  Enabled = False
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  Mode = dmDecimal
                end
              end
              object FVBox21: TFVBox
                Left = 290
                Top = 0
                Width = 40
                Height = 62
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 30
                Padding.Left = 10
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 3
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel20: TFLabel
                  Left = 0
                  Top = 0
                  Width = 8
                  Height = 13
                  Caption = '='
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
              end
              object FVBox22: TFVBox
                Left = 330
                Top = 0
                Width = 126
                Height = 62
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 4
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 3
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel21: TFLabel
                  Left = 0
                  Top = 0
                  Width = 91
                  Height = 13
                  Caption = 'Valor do Servi'#231'o'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object edtTempoValorServicoNc: TFDecimal
                  Left = 0
                  Top = 14
                  Width = 115
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Precision = 0
                  Format = 'R$ ,##0.00'
                  Enabled = False
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  Mode = dmDecimal
                end
              end
            end
            object FVBox23: TFVBox
              Left = 0
              Top = 65
              Width = 494
              Height = 64
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 3
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel18: TFLabel
                Left = 0
                Top = 0
                Width = 39
                Height = 13
                Caption = 'Motivo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtMotivoTempoNc: TFString
                Left = 0
                Top = 14
                Width = 433
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
          end
          object vboxPainelValor: TFVBox
            Left = 0
            Top = 173
            Width = 485
            Height = 260
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox16: TFHBox
              Left = 0
              Top = 0
              Width = 480
              Height = 70
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox17: TFVBox
                Left = 0
                Top = 0
                Width = 136
                Height = 62
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 3
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel19: TFLabel
                  Left = 0
                  Top = 0
                  Width = 35
                  Height = 13
                  Caption = 'Venda'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object FHBox17: TFHBox
                  Left = 0
                  Top = 14
                  Width = 185
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftMin
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object edValorNc: TFDecimal
                    Left = 0
                    Top = 0
                    Width = 115
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    Maxlength = 0
                    Precision = 0
                    Format = 'R$ ,##0.00'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    Alignment = taRightJustify
                    Mode = dmDecimal
                  end
                  object vBoxApagarValorNc: TFVBox
                    Left = 115
                    Top = 0
                    Width = 43
                    Height = 34
                    Align = alLeft
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 5
                    Padding.Left = 11
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    OnClick = vBoxApagarValorNcClick
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 0
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FImage3: TFImage
                      Left = 0
                      Top = 0
                      Width = 16
                      Height = 18
                      Align = alClient
                      Stretch = False
                      ImageSrc = '/images/crmservice4600235.png'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxSize = 0
                      GrayScaleOnDisable = False
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      Preview = False
                      ImageId = 0
                    end
                  end
                end
              end
              object FVBox24: TFVBox
                Left = 136
                Top = 0
                Width = 136
                Height = 64
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 3
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel23: TFLabel
                  Left = 0
                  Top = 0
                  Width = 32
                  Height = 13
                  Caption = 'Custo'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object edtPrecoCustoNc: TFDecimal
                  Left = 0
                  Top = 14
                  Width = 115
                  Height = 24
                  Table = tbOficinaManutServicos
                  FieldName = 'PRECO_CUSTO'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Precision = 0
                  Format = 'R$ ##0.00'
                  Enabled = False
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  Mode = dmDecimal
                end
              end
            end
            object FVBox25: TFVBox
              Left = 0
              Top = 71
              Width = 480
              Height = 64
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 3
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel24: TFLabel
                Left = 0
                Top = 0
                Width = 39
                Height = 13
                Caption = 'Motivo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtMotivoValorNc: TFString
                Left = 0
                Top = 14
                Width = 464
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
          end
        end
      end
    end
  end
  object FCoachmarkTempo: TFCoachmark
    ShowNextButton = False
    ShowPriorButton = False
    Items = <>
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 150
    Top = 10
  end
  object FCoachmarkValor: TFCoachmark
    ShowNextButton = False
    ShowPriorButton = False
    Items = <>
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 254
    Top = 13
  end
  object FCoachmarkNaoCobrar: TFCoachmark
    ShowNextButton = False
    ShowPriorButton = False
    Items = <>
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 362
    Top = 14
  end
  object tbOficinaManutServicos: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{EFFA8857-6DB2-45D6-9653-E4D282911C4B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{8CE11EE4-388B-405D-AD6D-2AEBCD6EE7BC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo'
        GUID = '{D64D4AD5-F081-495B-A793-38BD6C6F903F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SUB_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Grupo'
        GUID = '{23219416-BC47-488A-9A09-F146E195968D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        GUID = '{AB27F39F-E5E4-4A3D-AF1B-3F1859D59540}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        GUID = '{C81452CC-2EED-4AC3-AFBE-711E576EB416}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TERCEIROS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Terceiros'
        GUID = '{625E682C-D2D2-4489-8ED7-F4198E60C1BF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGINAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Original'
        GUID = '{FBFC553C-8297-4E69-A011-D5AE6D25EDEA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LR_REPAIR_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Lr Repair Id.'
        GUID = '{C05D4ED7-4B30-4740-8E2B-1D99371BF951}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tmo'
        GUID = '{177705BB-71A5-4B37-AC37-A904EE6124ED}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        GUID = '{4574ED78-9F48-41B6-9485-37650929BFB7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AVISO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aviso'
        GUID = '{E15F9FDE-4D66-4706-9C5E-84CF88F82CC6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{08EAADD4-82BA-4E90-8D73-8540F2B2972B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM_TMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Origem Tmo'
        GUID = '{EA06E81E-BC5A-4332-8658-6F295034772D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TMO_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tmo Agenda'
        GUID = '{DC687D92-D939-47C8-A832-3CAAB2E19746}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM_VALOR_HORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Origem Valor Hora'
        GUID = '{D6976B44-8ED7-4168-A182-29C68DA4F257}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_HORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Hora'
        GUID = '{029FF290-D922-4A63-9BDF-4DAA9583E184}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMO_COBRAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Como Cobrar'
        GUID = '{03B71B6A-3FA3-4AA2-9F87-9D0E400E2FEE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_CUSTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Custo'
        GUID = '{8AA9A49B-5D48-40B2-BB9B-D521761609D2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERV_LC11603'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o Lc11603'
        GUID = '{DF59D696-68D4-41B1-B57D-02B463983E60}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OFICINA_MANUT_SERVICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600264;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServicos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{33AB535E-9492-4590-8A98-2C74BECC03DF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{F1F03B5B-A2C6-4803-8E78-6B61DE47516C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{DFD5DFEB-F37B-484D-8DFD-BB18420F5FCB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{7B724DF8-D6EF-4DF8-8111-CB42CA807155}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_SERVICOS'
    Cursor = 'OS_SERVICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600264;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTemposPadroes: TFTable
    FieldDefs = <
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{0A109FE5-0B31-4551-8B32-528FD98C0E93}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produto'
        GUID = '{CD070F97-42C5-4B56-AC95-4965A572D2BE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo'
        GUID = '{A9114B56-CC86-4BA7-A663-707A4FCFF923}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TEMPOS_PADROES'
    Cursor = 'TEMPOS_PADROES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600264;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServOrc: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{AF8170DD-D7A8-47FA-A99F-A2E0064E7B76}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{0BB9E1A8-0220-44E1-8158-08A15A18F1A1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{3847EBA2-47CE-4FED-8F9D-F46DE35F56CE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{F7C1953E-56B5-4D45-9D12-10C9DBF3A871}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_SERV_ORC'
    Cursor = 'OS_SERV_ORC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600264;51601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
