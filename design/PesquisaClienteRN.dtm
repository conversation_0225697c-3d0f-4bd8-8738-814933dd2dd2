object PesquisaClienteRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000122'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbLeadsConsultaClientes: TFTable
    FieldDefs = <
      item
        Name = 'CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente'
        GUID = '{D15F8E1C-21FC-4439-B10A-E7826460A622}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{85A1FAC0-DC15-452D-AAEE-C782A7001396}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel Res'
        GUID = '{E8FAB779-89C0-4E35-B011-0230E22615B7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel com'
        GUID = '{AE197187-802F-4E3A-A2E3-A7CA37DB2379}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel Cel'
        GUID = '{D5DBBC3B-5C78-4041-8868-E17322ED4EE6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        GUID = '{7A4DA170-D5C5-4136-8CC5-1824A6B8243F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email Nfe'
        GUID = '{F45E57FC-5AA5-4549-9F9E-CD58AAC5C727}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Exibir'
        GUID = '{B0E37499-BD5F-402A-AEE5-2B384EC5BA81}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente Exibir'
        GUID = '{E17E5798-A594-4571-99C8-B6462788A7BF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_RES_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel Res Exibir'
        GUID = '{8187C27B-5603-47C7-B2AF-************}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_COM_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel com Exibir'
        GUID = '{B37A8ECC-5ED9-468D-94BB-1CE04D1F8825}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_CEL_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel Cel Exibir'
        GUID = '{CBE701F7-CAF6-4B98-B89F-4FA568485266}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email Exibir'
        GUID = '{E005E5E6-82B2-4215-A216-28567ABE247C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_NFE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email Nfe Exibir'
        GUID = '{D534DBF4-3FC0-48A5-8912-CD29B1AC327E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe'
        GUID = '{120DA9EC-1192-4382-AA4E-50F38D0CC0E1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sexo'
        GUID = '{5C18CDD3-4AEA-4022-9F93-E680AC090F1C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_ATACADISTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Atacadista'
        GUID = '{CD5D3B0D-7266-4BE8-B5C1-7DF4680A7F14}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MIDIA_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Midia Descri'#231#227'o Codigo'
        GUID = '{9FC2E3F6-E214-42D6-9A60-FF016140C9EF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_CONSULTA_CLIENTES'
    MaxRowCount = 1000
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;70001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsEnderecoCliente: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_ENDERECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Endere'#231'o'
        GUID = '{5FE81C4C-7749-43FD-877B-18409E76FEFF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ENDERECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Endere'#231'o'
        GUID = '{C331493A-C6B5-4647-90BF-5B1FD5BEE758}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{0F230CCC-2B7B-4D19-8EC3-373BAF41551A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        GUID = '{E582060A-ADD6-4465-A36B-5FC634B85B63}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        GUID = '{CA4005AA-24F8-4FD9-B655-DCF2C7EBE396}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        GUID = '{F12AA33C-6137-41D1-8BC4-54037C9375F7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHECKED'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CHECKED'
        GUID = '{668AA527-8F16-402C-BAB0-C09235846155}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        GUID = '{83F91A74-A0D7-4D8B-AB82-7EEB4FB2E07A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua'
        GUID = '{80B3BEF3-D22F-4C76-A5F3-C6D55D16622B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        GUID = '{189BBD71-51C5-4690-9E20-CD57BC45A401}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade Exibir'
        GUID = '{7ABC746D-9A2B-43FC-BFCB-7C529CC5DCD4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Exibir'
        GUID = '{DC202AD7-9B26-43AB-8267-DFB5F100AC01}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual Exibir'
        GUID = '{0EC17C22-6095-4E9F-8CE3-F0A80B125475}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro Exibir'
        GUID = '{208A7D0E-4DD6-41BA-A1C8-3E98886C8122}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua Exibir'
        GUID = '{4F9761D6-C149-4F25-9D02-D19A6815EB12}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Exibir'
        GUID = '{6092F139-8DF9-4B19-B7E1-E5C36C2F5C4E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_ENDERECO_CLIENTE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;70002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCrmpartsExisteEvento: TFTable
    FieldDefs = <
      item
        Name = 'EXIST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Exist'
        GUID = '{878A3F23-ACE0-4CD0-BACB-F89177C8B2EA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRMPARTS_EXISTE_EVENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;70004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListFoneCliente: TFTable
    FieldDefs = <
      item
        Name = 'TIPO_FONE'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'TIPO_FONE'
        GUID = '{E5E2CC31-0283-4A62-B028-80737E1DA4DE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'FONE'
        GUID = '{BC62CBD5-916D-4C2D-9B77-DC018F127B4D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DUAL'
    Cursor = 'DUAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;70006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesDescontos: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{04FE3A44-6DF0-4A55-9F28-496EF6396C90}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LETRA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Letra'
        GUID = '{FF0A847D-71A0-415C-BBC1-77ACB0260DCE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_DESCONTOS'
    Cursor = 'CLIENTES_DESCONTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCrmpartsGridClienteEspecial: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{D18DEAEF-0DF7-4886-9AEE-33E8EF9D2C65}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRMPARTS_GRID_CLIENTE_ESPECIAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;53002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
