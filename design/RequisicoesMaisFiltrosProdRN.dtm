object RequisicoesMaisFiltrosProdRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '103014'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbServicosTecnicosTrab: TFTable
    FieldDefs = <
      item
        Name = 'TEC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tec'
        GUID = '{DBD31408-9528-4D13-BD7B-3F6B5DB253F4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{BEC9D399-44BA-4A38-B744-66B83D3997A9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{BF47472A-7496-4F2F-B6DF-784B7C8FEB6A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o'
        GUID = '{D85DA041-34E5-4E69-A5E6-8A99D55F6E4A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        GUID = '{F12CDCD1-660B-4B77-BF3C-92F53B64FF02}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{F907844F-ADF3-48FC-8D1D-4B3B7F86D517}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_DESC_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Desconto Servi'#231'o'
        GUID = '{B50E43BC-F066-4B01-8B1D-CF21938567F2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os Fabrica'
        GUID = '{5681AB40-0EE6-4A38-AED1-08DA1510CDD9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_SERVICOS_TECNICOS_TRAB'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '103014;10301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServicosTecnicosCombo: TFTable
    FieldDefs = <
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        GUID = '{123592EF-5410-4BF1-B122-187B4112891A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{9D4B8B3D-7543-46D0-B3D9-5B1A3A079336}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LOGIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Login'
        GUID = '{9321ED8E-C3C2-419A-BB04-B9D222F22B1A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICOS_TECNICOS_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '103014;10302'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
