object AgendaPremiumPesqOsPlacaRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600330'
  Height = 299
  Width = 442
  object tbAgendaPremiumPesqOsPlaca: TFTable
    FieldDefs = <
      item
        Name = 'DATA_COMECA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Comeca'
        GUID = '{04842B35-E23F-45A2-8F47-5CF2B760C017}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_FIM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Fim'
        GUID = '{A58F634F-2468-4144-B777-B772A9CCA5BC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Nome'
        GUID = '{D8587D44-5214-4A0F-9E2C-685C99E93F1C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PLACA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Placa'
        GUID = '{E6B80C12-A055-4091-8DE7-57B1FED6DBB5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{E31F4A22-6964-4F05-B2CF-E79DDCD7E3B9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o'
        GUID = '{FF8C6931-FD15-4F20-A24C-3329E7702A7F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRISMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prisma'
        GUID = '{9E888E9B-0B7E-494E-8AD7-AD69B14DEE0B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os Fabrica'
        GUID = '{57844010-3C32-49AA-929E-45484995FABA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'AGENDA_PREMIUM_PESQ_OS_PLACA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600330;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
