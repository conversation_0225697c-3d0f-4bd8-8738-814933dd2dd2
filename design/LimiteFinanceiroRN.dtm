object LimiteFinanceiroRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '5300783'
  Left = 44
  Top = 162
  Height = 299
  Width = 442
  object tbLimiteCliente: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMITE_CREDITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Limite Cr'#233'dito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_COMPL_LIMITE_CREDITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Complemento Limite Cr'#233'dito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMITE_UTILIZADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Limite Utilizado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMITE_DISPONIVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Limite Disponivel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MOTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Motivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_MOTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Motivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Motivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_VENC_CADASTRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Vencimento Cadastro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_VENC_LIMITE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Vencimento Limite'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CADASTRO_VENCIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cadastro Vencido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMITE_VENCIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Limite Vencido'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_CONSULTA_FINAN_CLIENTE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300783;53001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
