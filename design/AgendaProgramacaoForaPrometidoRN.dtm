object AgendaProgramacaoForaPrometidoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '466015'
  Height = 299
  Width = 442
  object tbProgramacaoForaPrometVeic: TFTable
    FieldDefs = <
      item
        Name = 'PLACA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Placa'
        GUID = '{7F97D930-D15B-4013-A8CD-A0D2CAB46528}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VEICULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ve'#237'culo'
        GUID = '{215B8277-0CAF-4630-B518-60F7F75677C5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PROMETIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Prometida'
        GUID = '{8EF4AB0F-2480-41C8-A3E3-A3BBA24A52D4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PROMETIDA_DISPLAY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Prometida Display'
        GUID = '{7F237F85-FD0F-48AA-BDCA-CEF520FF8425}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{23D01F1E-F558-4A5E-848D-123705F39EEB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{77A0EF2E-CE4F-4744-B8E7-6FF0A2444A40}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PROGRAMACAO_FORA_PROMET_VEIC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466015;46601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProgramacaoForaPrometServ: TFTable
    FieldDefs = <
      item
        Name = 'ROW_NUMBER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Row Number'
        GUID = '{7CB55241-8BD5-477E-8497-1EA82010752B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o'
        GUID = '{1ABEE7B7-74A9-473C-86DE-B6F454E203C1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Servi'#231'o'
        GUID = '{A0DF1452-17C3-4F05-8AF1-6EF904448421}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_SERVICO_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Servi'#231'o Desconto'
        GUID = '{1ABF000C-FD55-45BC-B408-1A897A6F59A1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TRABALHADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Trabalhado'
        GUID = '{FDBC0EB4-041F-435E-814C-E17BDEFCAE99}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PADRAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Padr'#227'o'
        GUID = '{BD14FF16-B7FB-42DC-BA70-71CE015FE960}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{83CAF93E-5F7F-4F0F-83C6-CE8255DD023A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{F4DFF88C-CD8E-4AA4-A6C0-4860EA2C4A88}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{1359D5EB-4CD6-4229-B9EE-DB7AC75CFD78}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{AAC7D0E1-C97D-4ED0-A32A-98E862DB7F97}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item Agenda'
        GUID = '{26E59725-64F0-4E60-84D9-5754D1E2CE66}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PROGRAMACAO_FORA_PROMET_SERV'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466015;46602'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProgramacaoForaPrometProd: TFTable
    FieldDefs = <
      item
        Name = 'ROW_NUMBER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Row Number'
        GUID = '{00F7703B-DA2C-45D8-AAEE-FED1B1CE53C4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        GUID = '{AA3D7DE5-243B-47E6-94DA-6F202307D50B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Produtivo'
        GUID = '{01520F0E-355E-4464-A17B-9DF8EB89F01B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        GUID = '{50058E56-E669-44DC-870A-03A8DFACA310}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PADRAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Padr'#227'o'
        GUID = '{908291F7-BE82-423F-91E7-1595986AA8FD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Entrada'
        GUID = '{ECC371E0-96A9-4D8E-B558-C9B6FCCDF533}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Entrada'
        GUID = '{9BD0F163-BB54-4629-9961-D7C2D2B2991E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Sa'#237'da'
        GUID = '{82A2FED8-1E88-41A3-885A-5E029676690B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Sa'#237'da'
        GUID = '{6C0DBCA6-054C-45D6-A11C-0CCB25E3125C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Servi'#231'o'
        GUID = '{47828B08-50F7-4F8C-B8A2-3B329E649A6B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{0B7C9C09-2960-47EA-88FF-40A8E17F8345}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{F4758CC4-5970-4DA7-A6BF-EBA58673C2CE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{367770F6-B84B-4CE7-8634-4FB2A7F76D08}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{1DA8475A-D981-4CDF-BE36-824A2C22C65A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PROGRAMACAO_FORA_PROMET_PROD'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466015;46603'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
