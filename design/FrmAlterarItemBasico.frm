object FrmAlterarItemBasico: TFForm
  Left = 44
  Top = 163
  ActiveControl = vBoxPrincipal
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Alterar Item'
  ClientHeight = 496
  ClientWidth = 904
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000173'
  ShortcutKeys = <>
  InterfaceRN = 'AlterarItemBasicoRN'
  Access = False
  ChangedProp = 
    'FrmAlterarItemBasico.Height;'#13#10'FrmAlterarItemBasico.Width;'#13#10'FrmAl' +
    'terarItemBasico.ActiveControlFrmAlterarItemBasico.Height;'#13#10'FrmAl' +
    'terarItemBasico.ActiveControl;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 904
    Height = 496
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hboxEditarItem: TFHBox
      Left = 0
      Top = 0
      Width = 900
      Height = 60
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnSalvar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 55
        Hint = 'Salvar'
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
          C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
          D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
          1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
          D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
          9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
          A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
          BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
        ImageId = 700080
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnCancelar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 55
        Hint = 'Cancelar'
        Caption = 'Cancelar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnCancelarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000022B4944415478DAB595DF4B544114C7CF2692A12F22195B28855AF8A0
          50F4121111E9F64304F34585D4C00705317AAABFA18720AA97602B044B097221
          88486559C444A8968405454537D0087F15063EC4BAF53DCC1918C6BDEBDC950E
          7CE09C997BCFF7CECC997303F49F2DE0F85C31380D8E48FC037C053FF723C073
          4DE036B898E1D934888147E02DF8EB47E004E807171C57380E3AC03717813360
          0494585F1B078B1257C8961D309E590321D93A4F8172F0191C9638051E8207B2
          EFA605C15D70C7185B950F5CC924C0FE28B82CF1266800531EDB9207C2E09635
          FE015C23391353A05EB6866D87D4C17EF499DCCC35660B7025348AFF04F46549
          FE0C741A6361C9D5257104349B0205E0173828315751D247F26E500566656C9B
          D4DDF9A305F860BE883F074E79247F4EAA1CEDE4698997C071F16B40420B5C07
          EFC4E773B8924372B628B8649E8316B80ADE8BCF9514CA21395B8C5471B07135
          46B5402D98163F2967A093BF00ED0EC939D732382A71359F8916C827D5B80A8D
          FD9BF1919C8D6F765CFC2D529D206596E91068117F90D42D764DAEDF6915FF25
          B8A997A5ED3C98F07879AFE4DC7523467C8EA403D8BD6818DCC821F92B7048E2
          D7C64EEC1228059F48353D6D03E03E481863FC1E17C63DD0668C27C159B0E125
          C07692541F29B3C6B94216E49D4A70CC9AE77F419D3C43D904F44A9ECAF25DEC
          0DE801EBF6C45EFF64FEA3F5926ADB45D6DC6F52B7FF3198F44AE0FAD3E77BC2
          CD2C28F177304FAA94B39AAB40CEF60F541979196CA1E08A0000000049454E44
          AE426082}
        ImageId = 700098
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnBloquearDesbloquearItemParaCompra: TFButton
        Left = 120
        Top = 0
        Width = 70
        Height = 55
        Hint = 'Bloquear'
        Caption = 'Bloquear'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnBloquearDesbloquearItemParaCompraClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000025F4944415478DA636420006E18BA4A31B330E43132307803B12248EC
          3F03C37D20DEFAF70FC3248DF3BB9FE1D3CF884FF2AE895BD27FC6FF53804C4E
          1C4ABE31FE67CC553EB36B1EC916DC3671CD6664649842C887101F3166AB9EDE
          358D680B6E9BBA6B3332FCBB0864322309BFFFCFF0FF284413A33590124492FB
          FB9FE99F9EEAC9BDD788B2E08EA9CB0AA05438920BD7B2FCFE95A278E1C00710
          FFA18DB7E0EF9FBFE600994148FE58A9727A4F04410BCE181BB30A300BBD039A
          CA03557297EB1FBB9ED4D9CDDF90D53D33F6E5FAC6F4F312D06065A8495F3EFC
          7D276472F6EC6FBC16DC30735364F9FFFF1E9250AFCAE9DD25D87C7AD7C4B5F7
          3F2343118CCFCCCCACA87862C703BC16DC357335F9FF9FE134311178DBD42D8B
          91E1FF54B8618C0CA6CAA7769F21C902A0A64CA0A619587D60E69A01543B9D28
          0BEE193AC9FF656571013A5905E8AA0A2415CB818A0E61B30098D9EC80442492
          6F3B1818FFDFFDCBC0B857E3D4AEFB700BEE98B85A0359BB804C2E06EA80EF4C
          8C4CEE4AA7761E668484A5EB2E20C3954A86C37CB75BF5F46E37880F4C5D41E1
          664C4D0B80E02C30F5999060C1FFC3FF99185B98FF32FEFFC7F8AF1618BAB6D4
          B4E0CDFF3F2C5AAAE7B7BF0607A9A1A72823CB1F50B120422D0B16001527220B
          00F5CC075209D4B2E0F2FFF72CA6AA77B6FF04FB40C5939D51F00F28AFE85233
          0E66010BB374887AB759407E2A35E300AE8154F583CA8237C0DCBE16125A0CC1
          0CF853108605A0083361A02E3803B4C0145A54B84D03167099D4341D68DE34E5
          D37BB2C116DC377010F8CBC2321F58DEFA01B94C149AFD8FE1FFFF4DCC7FFE24
          82AA580004A90B2862DEDD860000000049454E44AE426082}
        ImageId = 7000189
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object hBoxCampos: TFHBox
      Left = 0
      Top = 61
      Width = 900
      Height = 350
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object vBoxColuna01: TFVBox
        Left = 0
        Top = 0
        Width = 300
        Height = 340
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxGrupo: TFVBox
          Left = 0
          Top = 0
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblGrupo: TFLabel
            Left = 0
            Top = 0
            Width = 29
            Height = 13
            Caption = 'Grupo'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object cboGrupo: TFCombo
            Left = 0
            Top = 14
            Width = 200
            Height = 21
            Hint = 'Grupo'
            Table = tbItens
            LookupTable = tbItensGrupoInterno
            FieldName = 'COD_GRUPO_INTERNO'
            LookupKey = 'COD_GRUPO_INTERNO'
            LookupDesc = 'DESCRICAO'
            Flex = True
            HelpCaption = 'Grupo'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Grupo'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = True
            HideClearButtonOnNullValue = True
            OnChange = cboGrupoChange
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object vBoxSubgrupo: TFVBox
          Left = 0
          Top = 46
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblSubGrupo: TFLabel
            Left = 0
            Top = 0
            Width = 46
            Height = 13
            Caption = 'Subgrupo'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object cboSubgrupo: TFCombo
            Left = 0
            Top = 14
            Width = 200
            Height = 21
            Hint = 'Subgrupo'
            Table = tbItens
            LookupTable = tbItensSubGrupo
            FieldName = 'COD_SUB_GRUPO_INTERNO'
            LookupKey = 'COD_SUB_GRUPO_INTERNO'
            LookupDesc = 'DESCRICAO'
            Flex = True
            HelpCaption = 'Subgrupo'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Subgrupo'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = True
            HideClearButtonOnNullValue = True
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object vBoxFabricante: TFVBox
          Left = 0
          Top = 92
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblFabricante: TFLabel
            Left = 0
            Top = 0
            Width = 51
            Height = 13
            Caption = 'Fabricante'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object cboFabricante: TFCombo
            Left = 0
            Top = 14
            Width = 200
            Height = 21
            Hint = 'Fabricante'
            Table = tbItensFornecedor
            LookupTable = tbFabricante
            FieldName = 'COD_FABRICANTE'
            LookupKey = 'COD_FABRICANTE'
            LookupDesc = 'NOME'
            Flex = True
            HelpCaption = 'Fabricante'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Fabricante'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = True
            HideClearButtonOnNullValue = True
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object vBoxMarca: TFVBox
          Left = 0
          Top = 138
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblMarca: TFLabel
            Left = 0
            Top = 0
            Width = 29
            Height = 13
            Caption = 'Marca'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object cboMarca: TFCombo
            Left = 0
            Top = 14
            Width = 200
            Height = 21
            Hint = 'Marca'
            Table = tbItensFornecedor
            LookupTable = tbMarca
            FieldName = 'COD_MARCA'
            LookupKey = 'COD_MARCA'
            LookupDesc = 'DESCRICAO'
            Flex = True
            HelpCaption = 'Marca'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Marca'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = True
            HideClearButtonOnNullValue = True
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object vBoxSituacaoEspecial: TFVBox
          Left = 0
          Top = 184
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblSituacaoEspecial: TFLabel
            Left = 0
            Top = 0
            Width = 82
            Height = 13
            Caption = 'Situa'#231#227'o Especial'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object cboSituacaoEspecial: TFCombo
            Left = 0
            Top = 14
            Width = 200
            Height = 21
            Hint = 'Situa'#231#227'o Especial'
            Table = tbItensFornecedor
            LookupTable = tbItemSituacaoEspecial
            FieldName = 'ID_SITUACAO_ESPECIAL'
            LookupKey = 'ID_SITUACAO_ESPECIAL'
            LookupDesc = 'DESCRICAO'
            Flex = True
            HelpCaption = 'Situa'#231#227'o Especial'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Situa'#231#227'o Especial'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = True
            HideClearButtonOnNullValue = True
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object vBoxLetraDesconto: TFVBox
          Left = 0
          Top = 230
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 5
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblLetraDesconto: TFLabel
            Left = 0
            Top = 0
            Width = 88
            Height = 13
            Caption = 'Letra de Desconto'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object cboLetraDesconto: TFCombo
            Left = 0
            Top = 14
            Width = 200
            Height = 21
            Hint = 'Letra de Desconto'
            Table = tbItens
            LookupTable = tbListaLetraDescontoGeral
            FieldName = 'COD_MAX_DESC'
            LookupKey = 'LETRA_CODIGO'
            LookupDesc = 'LETRA_CODIGO'
            Flex = True
            HelpCaption = 'Letra de Desconto'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Letra de Desconto'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = False
            UseClearButton = False
            HideClearButtonOnNullValue = True
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object vBoxBloquearItemCompra: TFVBox
          Left = 0
          Top = 276
          Width = 290
          Height = 50
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 6
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblStatus: TFLabel
            Left = 0
            Top = 0
            Width = 31
            Height = 13
            Caption = 'Status'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtStatus: TFString
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'Status'
            HelpCaption = 'Status'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Status'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccNormal
            Pwd = False
            Maxlength = 0
            Enabled = False
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
        end
      end
      object vBoxColuna02: TFVBox
        Left = 300
        Top = 0
        Width = 300
        Height = 340
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxCodigoGTIN: TFVBox
          Left = 0
          Top = 0
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblCodigoGTIN: TFLabel
            Left = 0
            Top = 0
            Width = 60
            Height = 13
            Hint = 'C'#243'digo GTIN (Global Trade Item Number)'
            Caption = 'C'#243'digo GTIN'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtStringCodigoGTIN: TFString
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'C'#243'digo GTIN'
            Table = tbItensFornecedor
            FieldName = 'COD_GTIN'
            HelpCaption = 'C'#243'digo GTIN'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'C'#243'digo GTIN'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccUpper
            Pwd = False
            Maxlength = 14
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
        end
        object vBoxCodigoBarras: TFVBox
          Left = 0
          Top = 46
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblCodigoBarras: TFLabel
            Left = 0
            Top = 0
            Width = 82
            Height = 13
            Caption = 'C'#243'digo de Barras'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtStringCodigoBarras: TFString
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'C'#243'digo de Barras'
            Table = tbItensFornecedor
            FieldName = 'COD_BARRAS'
            HelpCaption = 'C'#243'digo de Barras'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'C'#243'digo de Barras'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccUpper
            Pwd = False
            Maxlength = 0
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
        end
        object vBoxTipoPeca: TFVBox
          Left = 0
          Top = 92
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblTipoPeca: TFLabel
            Left = 0
            Top = 0
            Width = 49
            Height = 13
            Caption = 'Tipo Pe'#231'a '
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object cboTipoPeca: TFCombo
            Left = 0
            Top = 14
            Width = 200
            Height = 21
            Hint = 'Tipo Pe'#231'a'
            Table = tbItensFornecedor
            FieldName = 'TIPO_PECA'
            Flex = True
            ListOptions = 'C'#226'mbio=C;Motor=M'
            HelpCaption = 'Tipo Pe'#231'a'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Tipo Pe'#231'a'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = True
            HideClearButtonOnNullValue = False
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object vBoxEstocagemReservaDias: TFVBox
          Left = 0
          Top = 138
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblEstocagemReservaDias: TFLabel
            Left = 0
            Top = 0
            Width = 122
            Height = 13
            Caption = 'Estocagem-Reserva Dias '
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtIntegerReservaDias: TFInteger
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'Estocagem-Reserva Dias '
            Table = tbItensFornecedor
            FieldName = 'RESERVA_DIAS'
            HelpCaption = 'Estocagem-Reserva Dias '
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Estocagem-Reserva Dias '
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Maxlength = 0
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            Alignment = taRightJustify
          end
        end
        object vBoxPartNumber: TFVBox
          Left = 0
          Top = 184
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblPartNumber: TFLabel
            Left = 0
            Top = 0
            Width = 61
            Height = 13
            Caption = 'Part-Number'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtStringPartNumber: TFString
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'Part-Number'
            Table = tbItensFornecedor
            FieldName = 'PART_NUMBER'
            HelpCaption = 'Part-Number'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Part-Number'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccNormal
            Pwd = False
            Maxlength = 20
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
        end
        object vBoxOrigem: TFVBox
          Left = 0
          Top = 230
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 5
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblOrigem: TFLabel
            Left = 0
            Top = 0
            Width = 34
            Height = 13
            Caption = 'Origem'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object cboOrigem: TFCombo
            Left = 0
            Top = 14
            Width = 200
            Height = 21
            Hint = 'Origem'
            Table = tbItensFornecedor
            LookupTable = tbItensOrigem
            FieldName = 'COD_ORIGEM'
            LookupKey = 'COD_ORIGEM'
            LookupDesc = 'DESCRICAO'
            Flex = True
            HelpCaption = 'Origem'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Origem'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = False
            UseClearButton = False
            HideClearButtonOnNullValue = True
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
      end
      object vBoxColuna03: TFVBox
        Left = 600
        Top = 0
        Width = 300
        Height = 340
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxEmbalagemUnidade: TFVBox
          Left = 0
          Top = 0
          Width = 290
          Height = 50
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblEmbalagemUnidade: TFLabel
            Left = 0
            Top = 0
            Width = 97
            Height = 13
            Caption = 'Embalagem/Unidade'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object hBoxEmbalagemUnidade: TFHBox
            Left = 0
            Top = 14
            Width = 280
            Height = 30
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object edtSpinnerEmbalagem: TFSpinner
              Left = 0
              Top = 0
              Width = 120
              Height = 24
              Hint = 'Embalagem'
              Table = tbItens
              FieldName = 'EMBALAGEM'
              HelpCaption = 'Embalagem'
              TabOrder = 0
              AccessLevel = 0
              Flex = False
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Embalagem'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              IconDirection = idLeft
              Maxlength = 0
              Step = 1
              ButtonVisible = True
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
            end
            object cboUnidade: TFCombo
              Left = 120
              Top = 0
              Width = 150
              Height = 21
              Hint = 'Unidade'
              Table = tbItens
              LookupTable = tbItensUnidadeMedida
              FieldName = 'UNIDADE'
              LookupKey = 'COD_UNIDADE'
              LookupDesc = 'DESCRICAO'
              Flex = True
              HelpCaption = 'Unidade'
              ReadOnly = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Unidade'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              ClearOnDelKey = True
              UseClearButton = True
              HideClearButtonOnNullValue = True
              Colors = <>
              Images = <>
              Masks = <>
              Fonts = <>
              MultiSelection = False
              IconReverseDirection = False
            end
          end
        end
        object vBoxPesoLiquido: TFVBox
          Left = 0
          Top = 51
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblPesoLiquido: TFLabel
            Left = 0
            Top = 0
            Width = 166
            Height = 13
            Caption = 'Peso L'#237'quido (Kg - Usado p/Rateio)'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtDecimalPesoLiquido: TFDecimal
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'Peso L'#237'quido (Kg - Usado p/Rateio)'
            Table = tbItens
            FieldName = 'PESO_LIQUIDO'
            HelpCaption = 'Peso L'#237'quido (Kg - Usado p/Rateio)'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Peso L'#237'quido (Kg - Usado p/Rateio)'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Maxlength = 0
            Precision = 5
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            Alignment = taRightJustify
            Mode = dmDecimal
          end
        end
        object vBoxPesoBruto: TFVBox
          Left = 0
          Top = 97
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblPesoBruto: TFLabel
            Left = 0
            Top = 0
            Width = 75
            Height = 13
            Caption = 'Peso Bruto (Kg)'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtDecimalPesoBruto: TFDecimal
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'Peso Bruto (Kg)'
            Table = tbItens
            FieldName = 'PESO_BRUTO'
            HelpCaption = 'Peso Bruto (Kg)'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Peso Bruto (Kg)'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Maxlength = 0
            Precision = 3
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            Alignment = taRightJustify
            Mode = dmDecimal
          end
        end
        object vBoxLargura: TFVBox
          Left = 0
          Top = 143
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblLargura: TFLabel
            Left = 0
            Top = 0
            Width = 56
            Height = 13
            Caption = 'Largura (m)'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtDecimalLargura: TFDecimal
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'Largura (m)'
            Table = tbItens
            FieldName = 'LARGURA_VOLUME'
            HelpCaption = 'Largura (m)'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Largura (m)'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Maxlength = 0
            Precision = 3
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            Alignment = taRightJustify
            Mode = dmDecimal
          end
        end
        object vBoxComprimento: TFVBox
          Left = 0
          Top = 189
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblComprimento: TFLabel
            Left = 0
            Top = 0
            Width = 82
            Height = 13
            Caption = 'Comprimento (m)'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtDecimalComprimento: TFDecimal
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'Comprimento'
            Table = tbItens
            FieldName = 'COMPRIMENTO_VOLUME'
            HelpCaption = 'Comprimento'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Comprimento'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Maxlength = 0
            Precision = 3
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            Alignment = taRightJustify
            Mode = dmDecimal
          end
        end
        object vBoxAltura: TFVBox
          Left = 0
          Top = 235
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 5
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblAltura: TFLabel
            Left = 0
            Top = 0
            Width = 48
            Height = 13
            Caption = 'Altura (m)'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtDecimalAltura: TFDecimal
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'Altura'
            Table = tbItens
            FieldName = 'ALTURA_VOLUME'
            HelpCaption = 'Altura'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Altura'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Maxlength = 0
            Precision = 3
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            Alignment = taRightJustify
            Mode = dmDecimal
          end
        end
        object vBoxVolume: TFVBox
          Left = 0
          Top = 281
          Width = 290
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 6
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblVolume: TFLabel
            Left = 0
            Top = 0
            Width = 58
            Height = 13
            Caption = 'Volume (m'#179')'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtDecimalVolume: TFDecimal
            Left = 0
            Top = 14
            Width = 200
            Height = 24
            Hint = 'Volume'
            Table = tbItens
            FieldName = 'VOLUME'
            HelpCaption = 'Volume'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Volume'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Maxlength = 0
            Precision = 3
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            Alignment = taRightJustify
            Mode = dmDecimal
          end
        end
      end
    end
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbItens
        GUID = '{CC44ED0C-A0C1-4276-B077-8664F60633CE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbItensFornecedor
        GUID = '{77D8E840-48FF-4DF9-BA89-27931EFFF7FC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbItensHistBloqueio
        GUID = '{5E7DF193-EDDA-4F6F-8BA0-04C0ACA246FE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbItens: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        GUID = '{0AEF661A-6998-485B-9BC7-25CDD7996788}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GRUPO_INTERNO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Interno'
        GUID = '{66EDE68E-2DCB-4F88-A6F5-E233A0884862}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SUB_GRUPO_INTERNO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Grupo Interno'
        GUID = '{FCA37335-42AB-4B25-846E-5B713E00BB58}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMBALAGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Embalagem'
        GUID = '{BF369DA4-D556-4DAD-B6F2-E7C18271C49D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UNIDADE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Unidade'
        GUID = '{30C083D7-1821-46BE-8444-EB521B08315C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'APLICACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aplica'#231#227'o'
        GUID = '{1498DFE8-FB7D-45FE-AF96-5461ECD44829}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_LIQUIDO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso L'#237'quido'
        GUID = '{0896F38F-DFBD-41F0-B0EA-49751C5A6249}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_BRUTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso Bruto'
        GUID = '{A2D04A07-B1F8-4DED-A315-BABC2A00A112}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VOLUME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Volume'
        GUID = '{FD179974-86F4-40EA-B125-CD0028DE184B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALTURA_VOLUME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Altura Volume'
        GUID = '{19ECC93F-25C0-4564-A092-5BC3807337BD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LARGURA_VOLUME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Largura Volume'
        GUID = '{5DDFC79F-38E8-478A-A160-F1319CF4FD69}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPRIMENTO_VOLUME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comprimento Volume'
        GUID = '{0589A030-F5D9-4120-9AE8-7B4154FF6932}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MAX_DESC'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Max Desconto'
        GUID = '{60BD1C34-ADAE-4625-AB21-F4F13B65041D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'ITENS'
    TableName = 'ITENS'
    Cursor = 'ITENS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensFornecedor: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        GUID = '{76201A75-6D56-4BC2-B5AE-469F90829E79}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        GUID = '{0E786508-A603-4D64-95B4-A77E84678146}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FABRICANTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabricante'
        GUID = '{CA3F494B-5BA0-4508-89B2-31073A674CD0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_SITUACAO_ESPECIAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Situa'#231#227'o Especial'
        GUID = '{9EDD474B-930F-428D-8018-82C9E1D34DD2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_BARRAS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Barras'
        GUID = '{18392879-A297-485A-BD80-7A17D03A9B16}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        GUID = '{EFDC6D24-1CCB-41C9-BD29-BA1A9DFED1F6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GTIN'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Gtin'
        GUID = '{BC4C751B-8BF1-44A5-8CFD-3470F5751828}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_PECA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Peca'
        GUID = '{DDE96260-A524-4B17-927A-593201E4C46D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_DIAS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Reserva Dias'
        GUID = '{B18C0971-E7D4-4D92-854F-FDEB683B86B9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PART_NUMBER'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Part Number'
        GUID = '{2E729BE4-1B24-46C1-8735-7B439A438AB8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORIGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Origem'
        GUID = '{5EAB3058-3163-43FF-8FB2-FFD6475DDA4C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPRAS_BLOQUEADAS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Compras Bloqueadas'
        GUID = '{44041107-F448-4EEE-8747-57E1F8A31E8B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'ITENS_FORNECEDOR'
    TableName = 'ITENS_FORNECEDOR'
    Cursor = 'ITENS_FORNECEDOR'
    MaxRowCount = 1000
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensGrupoInterno: TFTable
    FieldDefs = <
      item
        Name = 'COD_GRUPO_INTERNO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Interno'
        GUID = '{4C02A845-9330-4716-A9F0-35CBA9D2CF01}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{D255B104-02D9-4488-A22D-8F91A9C22A5C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_GRUPO_INTERNO'
    Cursor = 'ITENS_GRUPO_INTERNO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensSubGrupo: TFTable
    FieldDefs = <
      item
        Name = 'COD_SUB_GRUPO_INTERNO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Grupo Interno'
        GUID = '{8CE17FB1-6F84-4D8C-B06E-8410547FF93F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{71502D9E-FC61-4C94-A4EB-600EA433CFBA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_SUB_GRUPO'
    Cursor = 'ITENS_SUB_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFabricante: TFTable
    FieldDefs = <
      item
        Name = 'COD_FABRICANTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabricante'
        GUID = '{AE5F1694-4D52-4588-94F4-8C28AE0A2ECE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{CE51E064-3BBB-444F-B91C-602AD4356A7D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'FABRICANTE'
    Cursor = 'FABRICANTE'
    MaxRowCount = 2000
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensUnidadeMedida: TFTable
    FieldDefs = <
      item
        Name = 'COD_UNIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Unidade'
        GUID = '{5D333A1F-74A7-4D81-829A-36C6665DB61F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{21A4D822-0AED-49DB-91FD-17CBD9DD8707}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_UNIDADE_MEDIDA'
    Cursor = 'ITENS_UNIDADE_MEDIDA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItemSituacaoEspecial: TFTable
    FieldDefs = <
      item
        Name = 'ID_SITUACAO_ESPECIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Situa'#231#227'o Especial'
        GUID = '{014291E6-C835-4585-B971-FA2CCCBD939B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{140F8BAA-D084-40A6-93BB-A935A29C61B9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITEM_SITUACAO_ESPECIAL'
    Cursor = 'ITEM_SITUACAO_ESPECIAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMarca: TFTable
    FieldDefs = <
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        GUID = '{C55017AB-6EEC-48EA-92EF-035448B7B847}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{C70E4995-AFBE-4E35-A008-99472722B7E0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'MARCA'
    Cursor = 'MARCA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;24301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaLetraDescontoGeral: TFTable
    FieldDefs = <
      item
        Name = 'LETRA_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Max Desconto'
        GUID = '{3E12E029-689D-41F6-B396-B0CCD0AA5242}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LETRA_PERCENTUAL_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        GUID = '{A1A5DF48-00FC-44E0-800D-63FE5A784600}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_LETRA_DESCONTO_GERAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;27402'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensOrigem: TFTable
    FieldDefs = <
      item
        Name = 'COD_ORIGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Origem'
        GUID = '{0E5C1CC7-2B87-4524-B89C-1474DF341FE0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{FBBA8150-0820-478D-B8FE-12460DF3AE60}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_ORIGEM'
    Cursor = 'BUSCA_ITENS_ORIGEM'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;31101'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensHistBloqueio: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{A3317FDE-993E-4D35-9B95-0EDD5C3E6CC0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{2EA7EB58-54DA-4CDC-981B-972AF9596201}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        GUID = '{1BD56465-3367-4157-B2B4-ED3A6B824F4C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        GUID = '{AB2AE37B-4A8F-4787-A4CA-A1AE43E0021D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{F2D097AC-E582-47E0-A6EA-FD12D3D644A6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_HORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Hora'
        GUID = '{593960A9-17E4-4C16-AD23-2D523CDB5C88}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        GUID = '{12D40916-74DC-435A-94AB-D029BAE7A3A7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{84B3867F-21F2-4096-8466-BA27DAC79C7E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'ITENS_HIST_BLOQUEIO'
    TableName = 'ITENS_HIST_BLOQUEIO'
    Cursor = 'ITENS_HIST_BLOQUEIO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;47703'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbBuscaItensFornecedor: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        GUID = '{9ED7CBB5-9FBD-4854-9640-416D26D3BFD1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        GUID = '{00E8FBE2-5190-465F-8235-E22658D759D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_FORNECEDOR'
    Cursor = 'BUSCA_ITENS_FORNECEDOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;47704'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
