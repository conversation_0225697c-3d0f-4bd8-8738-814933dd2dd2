object FrmGerenciamentoDescontosServicos: TFForm
  Left = 321
  Top = 162
  ActiveControl = vboxPrincipal
  Caption = ' Descontos Servi'#231'os'
  ClientHeight = 742
  ClientWidth = 784
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600439'
  ShortcutKeys = <>
  InterfaceRN = 'GerenciamentoDescontosServicosRN'
  Access = False
  ChangedProp = 
    'FrmGerenciamentoDescontosServicos.Height;'#13#10'FrmGerenciamentoDesco' +
    'ntosServicos.Caption;'#13#10#13#10'FrmGerenciamentoDescontosServicos.Activ' +
    'eControl'#13#10'FrmGerenciamentoDescontosServicos.ActiveControl'#13#10'FrmGe' +
    'renciamentoDescontosServicos.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 784
    Height = 742
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object pagDescontos: TFPageControl
      Left = 0
      Top = 0
      Width = 753
      Height = 760
      ActivePage = tabEmpresa
      Align = alClient
      TabOrder = 0
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabClienteEmpSetor: TFTabsheet
        Caption = 'Cliente / Empresa / Setor'
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxCliente: TFVBox
          Left = 0
          Top = 0
          Width = 745
          Height = 732
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = True
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hboxPesquisa: TFHBox
            Left = 0
            Top = 0
            Width = 700
            Height = 34
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 3
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object edtPesqCliente: TFString
              Left = 0
              Top = 0
              Width = 293
              Height = 24
              TabOrder = 0
              AccessLevel = 0
              Flex = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Informe o nome do cliente a ser pesquisado'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              CharCase = ccNormal
              Pwd = False
              Maxlength = 0
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              OnEnter = edtPesqClienteEnter
              SaveLiteralCharacter = False
            end
            object btnLimparPesqCliente: TFButton
              Left = 293
              Top = 0
              Width = 35
              Height = 33
              Hint = 'Limpar Pesquisa Cliente'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 1
              OnClick = btnLimparPesqClienteClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000001584944415478DAEDD4BF2B85511CC7F1EFCD8F92148B2C0677206551
                CA28913291E1C6C0C4E62259943FC0A2886C4A16145D8B0599A53061B9061621
                191585F7E97C4B1DA7FB9C872B916FBDEADEE79CE77EEEF33DE73C09F9E64AFC
                07E42BA01E9DA8C433AEB083CBAF06D462567FDCAD576C62027758C53EE64203
                5AB08DD2883F718F2C9AB180744840124728D7EF2BFA24A7DAA2468C62C0B92F
                38600B5DFA39AD37BA55824334C40DA8C1858EADA3D733C78CEDA2ED334F607A
                3F2576114D1BCE3D738AB18632E7FA2232212DCA5BFD48400F269DB15B74E3C9
                33DF1CC265146008275101F31876AEEDA143ECBAB86536424AC3ABF01037E00C
                4D78F4CC1D91F793BB84C19016F99E6003D3384691D8BD3F867E1DBF167BF86E
                E2041CA00E1592BB4C4BDA35FC43F902CCDE6F459FF67446EC02BB735FC49EF8
                71C9F1560DDDA6E6749B45AE46A1B6C4BC08B35137FECD83F6BB02DE00C87B43
                19FC2EB5520000000049454E44AE426082}
              ImageId = 700092
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
          end
          object gridCliente: TFGrid
            Left = 0
            Top = 35
            Width = 520
            Height = 240
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbDescServicoCliente
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Paging.Enabled = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            Columns = <
              item
                Expanded = False
                FieldName = 'COD_CLIENTE'
                Font = <>
                Title.Caption = 'C'#243'd. Cliente'
                Width = 40
                Visible = False
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{0E8C8073-2D46-4023-B085-CADB02C8D289}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                FieldName = 'CLIENTE'
                Font = <>
                Title.Caption = 'Cliente'
                Width = 40
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <
                  item
                    Expression = 'cod_cliente = 0'
                    EvalType = etExpression
                    GUID = '{BAD8DC12-100D-4E91-B99F-66ECBD4D3080}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 7000213
                    OnClick = 'AddClienteClick'
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{32E1D225-45DD-4C19-BF6D-43A8B642BDAE}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                FieldName = 'DESC_SERVICO'
                Font = <>
                Title.Caption = '% Desc Servi'#231'o'
                Width = 125
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{9C6562FE-FC02-472C-A840-08703A8CD1A3}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = '##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{46B4B89B-43F7-4681-BDEA-E5D59482EDBB}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                Font = <>
                Title.Caption = 'Editar'
                Width = 65
                Visible = True
                Precision = 0
                TextAlign = taCenter
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = 'cod_cliente > 0'
                    EvalType = etExpression
                    GUID = '{49B93014-1A4A-41DC-BD10-3CBF459F1C6E}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 7
                    OnClick = 'EditClienteClick'
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{842C0D94-75A4-43A5-B06C-EC33CDD89C71}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                Font = <>
                Title.Caption = 'Excluir'
                Width = 65
                Visible = True
                Precision = 0
                TextAlign = taCenter
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = 'cod_cliente > 0'
                    EvalType = etExpression
                    GUID = '{D401895A-DD79-48FB-BD70-0F228D214F60}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 8
                    OnClick = 'ExcluirClienteClick'
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{93D4C55F-1EA5-4D8C-92AB-DC6A35D96818}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end>
          end
          object hboxClienteEmpresa: TFHBox
            Left = 0
            Top = 276
            Width = 500
            Height = 240
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 5
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object gridClienteEmpresa: TFGrid
              Left = 0
              Top = 0
              Width = 520
              Height = 240
              TabOrder = 0
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbDescServicoClienteEmpresa
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              Columns = <
                item
                  Expanded = False
                  FieldName = 'EMPRESA'
                  Font = <>
                  Title.Caption = 'Empresa do Cliente'
                  Width = 40
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <
                    item
                      Expression = 'cod_empresa = 0'
                      EvalType = etExpression
                      GUID = '{C46ADDEB-4463-4908-AE70-DCAD0A49DF0C}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 7000213
                      OnClick = 'addClienteEmpresaClick'
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{F4418A65-9A17-441C-A9C5-6FEC50542E1F}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  FieldName = 'DESC_SERVICO'
                  Font = <>
                  Title.Caption = '% Desc Servi'#231'o'
                  Width = 125
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{F0CA5A81-A068-4738-9E67-D14CA7791FE8}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = '##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{0852EEBC-EE40-43B5-824B-7BFC2FAD153F}'
                  WOwner = FrWizard
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  FieldName = 'COD_EMPRESA'
                  Font = <>
                  Title.Caption = 'C'#243'd. Empresa'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{CA3928A2-A68F-4524-B5C2-F04F4D6510CA}'
                  WOwner = FrWizard
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  FieldName = 'COD_CLIENTE'
                  Font = <>
                  Title.Caption = 'C'#243'd. Cliente'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{05B4811B-DD9B-4260-9168-84F6BE59A415}'
                  WOwner = FrWizard
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  Font = <>
                  Title.Caption = 'Editar'
                  Width = 65
                  Visible = True
                  Precision = 0
                  TextAlign = taCenter
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = 'cod_empresa > 0'
                      EvalType = etExpression
                      GUID = '{93B9A0D8-40ED-46AC-9DEC-373738C5D26D}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 7
                      OnClick = 'EditClienteEmpresaClick'
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{7FBD60A7-F670-482F-9F0B-6834E41F93AE}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  Font = <>
                  Title.Caption = 'Excluir'
                  Width = 65
                  Visible = True
                  Precision = 0
                  TextAlign = taCenter
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = 'cod_empresa > 0'
                      EvalType = etExpression
                      GUID = '{9331E078-5C28-4E9B-BDFF-8EB641F00E5D}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 8
                      OnClick = 'ExcluirClienteEmpresaClick'
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{33B7D46B-5D23-4BBC-A2F3-8B132B72E7FA}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end>
            end
          end
          object hboxClienteEmpSetor: TFHBox
            Left = 0
            Top = 517
            Width = 500
            Height = 240
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 5
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object gridClienteEmpSetor: TFGrid
              Left = 0
              Top = 0
              Width = 520
              Height = 240
              TabOrder = 0
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbDescServicoClienteEmpSetor
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              Columns = <
                item
                  Expanded = False
                  FieldName = 'SETOR'
                  Font = <>
                  Title.Caption = 'Setor do Cliente'
                  Width = 40
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <
                    item
                      Expression = 'COD_SETOR = 0'
                      EvalType = etExpression
                      GUID = '{4229DBC4-0312-4495-B321-2CD69B6747D3}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 7000213
                      OnClick = 'addClienteEmpSetorClick'
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{0000ADA8-E131-452F-9519-87885D26E4CF}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  FieldName = 'DESCONTO'
                  Font = <>
                  Title.Caption = '% Desc Servi'#231'o '
                  Width = 125
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{960179DC-01FA-4D3C-927E-03F1EA4262B8}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = '##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{2FB7F59B-DD70-4254-AAA5-A53D9AD19AAD}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  FieldName = 'COD_CLIENTE'
                  Font = <>
                  Title.Caption = 'C'#243'd. Cliente'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{1831363D-D3F0-4700-81D1-59952B254153}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  FieldName = 'COD_EMPRESA'
                  Font = <>
                  Title.Caption = 'C'#243'd. Empresa'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{4885999A-2C5A-4C86-898A-C3E98925FF34}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  FieldName = 'COD_SETOR'
                  Font = <>
                  Title.Caption = 'C'#243'd. Setor'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{AFB6B8B9-732E-4495-B2A0-C2925967564B}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  Font = <>
                  Title.Caption = 'Editar'
                  Width = 65
                  Visible = True
                  Precision = 0
                  TextAlign = taCenter
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = 'COD_SETOR > 0'
                      EvalType = etExpression
                      GUID = '{4C0D9121-E838-4F07-821F-FE9C23693C8A}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 7
                      OnClick = 'EditarClienteEmpresaSetorClick'
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{6D990952-EA22-4A6F-A1A8-860B5B582484}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end
                item
                  Expanded = False
                  Font = <>
                  Title.Caption = 'Excluir'
                  Width = 65
                  Visible = True
                  Precision = 0
                  TextAlign = taCenter
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = 'COD_SETOR > 0'
                      EvalType = etExpression
                      GUID = '{6C178F61-B648-4121-98A8-23CCF0540D02}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 8
                      OnClick = 'ExcluirClienteEmpresaSetorClick'
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{D9460B5B-1857-4267-ACC9-53490330280B}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                end>
            end
          end
        end
      end
      object tabTime: TFTabsheet
        Caption = 'Time'
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxTime: TFVBox
          Left = 0
          Top = 0
          Width = 745
          Height = 732
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox1: TFHBox
            Left = 0
            Top = 0
            Width = 685
            Height = 35
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object cbbTime: TFCombo
              Left = 0
              Top = 0
              Width = 350
              Height = 21
              LookupTable = tbDescServicoTime
              LookupKey = 'ID_TIME'
              LookupDesc = 'TIME'
              Flex = True
              ReadOnly = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Selecione o Time'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              ClearOnDelKey = True
              UseClearButton = True
              HideClearButtonOnNullValue = False
              OnChange = cbbTimeChange
              OnClearClick = cbbTimeClearClick
              Colors = <>
              Images = <>
              Masks = <>
              Fonts = <>
            end
          end
          object gridTime: TFGrid
            Left = 0
            Top = 36
            Width = 520
            Height = 220
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbDescServicoTimeGrid
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            Columns = <
              item
                Expanded = False
                FieldName = 'ID_TIME'
                Font = <>
                Title.Caption = 'Id. Time'
                Width = 40
                Visible = False
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{EB554E03-F44E-4687-8227-F75EAD94B99F}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                FieldName = 'TIME'
                Font = <>
                Title.Caption = 'Time'
                Width = 40
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <
                  item
                    Expression = 'id_time = 0'
                    EvalType = etExpression
                    GUID = '{D1BD7AD6-DEF3-4549-AEDA-D44711E070B2}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 7000213
                    OnClick = 'AddTimeClick'
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{19E195B9-0104-447C-A4D3-1557EAD110D5}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                FieldName = 'MAX_DESC_SERV'
                Font = <>
                Title.Caption = '% Desc Servi'#231'o'
                Width = 125
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{D27E8F33-CD96-4870-80FD-1117CF0AEA84}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = '##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{F83FF0DA-3FE9-48F4-9875-CC44E0502A83}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                Font = <>
                Title.Caption = 'Editar'
                Width = 65
                Visible = True
                Precision = 0
                TextAlign = taCenter
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = 'id_time > 0'
                    EvalType = etExpression
                    GUID = '{06EC0427-D851-4142-B8AD-1D953AAE0CBA}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 7
                    OnClick = 'EditTimeClick'
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{F1983961-CB61-4190-8852-B6798F255531}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                Font = <>
                Title.Caption = 'Excluir'
                Width = 65
                Visible = True
                Precision = 0
                TextAlign = taCenter
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = 'id_time > 0'
                    EvalType = etExpression
                    GUID = '{4BC31395-8C0B-4E9B-AAC0-C14FF6F33B0A}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 8
                    OnClick = 'ExcluirTimeClick'
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{25D5B0CF-3A0D-4C19-ABC0-96174F6FBF0B}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end>
          end
        end
      end
      object tabEmpresa: TFTabsheet
        Caption = 'Empresa'
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxEmpresa: TFVBox
          Left = 0
          Top = 0
          Width = 745
          Height = 732
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox5: TFHBox
            Left = 0
            Top = 0
            Width = 685
            Height = 35
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object cbbEmpresa: TFCombo
              Left = 0
              Top = 0
              Width = 350
              Height = 21
              LookupTable = tbDescServicoEmpresa
              LookupKey = 'COD_EMPRESA'
              LookupDesc = 'EMPRESA'
              Flex = True
              ReadOnly = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Selecione a Empresa'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              ClearOnDelKey = True
              UseClearButton = True
              HideClearButtonOnNullValue = False
              OnChange = cbbEmpresaChange
              OnClearClick = cbbEmpresaClearClick
              Colors = <>
              Images = <>
              Masks = <>
              Fonts = <>
            end
          end
          object gridEmpresa: TFGrid
            Left = 0
            Top = 36
            Width = 520
            Height = 220
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbDescServicoEmpresaGrid
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = True
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            Columns = <
              item
                Expanded = False
                FieldName = 'COD_EMPRESA'
                Font = <>
                Title.Caption = 'C'#243'd. Empresa'
                Width = 40
                Visible = False
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{7021A22C-C995-4DCF-B3B4-FC2598AE1756}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                FieldName = 'EMPRESA'
                Font = <>
                Title.Caption = 'Empresa'
                Width = 40
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <
                  item
                    Expression = 'COD_EMPRESA = 0'
                    EvalType = etExpression
                    GUID = '{ACDE5419-233D-4A2B-BE98-99B0520BE6C9}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 7000213
                    OnClick = 'AddEmpresaClick'
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{D41E7FA7-49C4-4CB7-85B8-46FA90906FA6}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                FieldName = 'DESCONTO'
                Font = <>
                Title.Caption = '% Desc Servi'#231'o'
                Width = 125
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{94E0AD77-1302-46DD-8519-A188AEFD1984}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = '##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{29C5299F-FEFB-4CAB-A539-198BC2A1168C}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                Font = <>
                Title.Caption = 'Editar'
                Width = 65
                Visible = True
                Precision = 0
                TextAlign = taCenter
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = 'COD_EMPRESA > 0'
                    EvalType = etExpression
                    GUID = '{34738965-C279-4099-BD3B-22417EC01190}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 7
                    OnClick = 'EditDescEmpresaClick'
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{A07ED40F-383C-47AD-8EE4-20BDFF18CC29}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end
              item
                Expanded = False
                Font = <>
                Title.Caption = 'Excluir'
                Width = 65
                Visible = True
                Precision = 0
                TextAlign = taCenter
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = 'COD_EMPRESA > 0'
                    EvalType = etExpression
                    GUID = '{D69D6483-7A1E-416F-893B-A6DB4A67461F}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 8
                    OnClick = 'ExcluirEmpresaClick'
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{353853CE-CC38-4CFF-B061-D46B1B1C0D25}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
              end>
          end
        end
      end
    end
  end
  object tbDescServicoTime: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESC_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIME'
    Cursor = 'DESC_SERVICO_TIME'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbDescServicoEmpresa
        GUID = '{7C753406-79FF-40C9-8416-24D772653B15}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbDescServicoTimeGrid
        GUID = '{189B5AE2-A1C1-4EFB-A299-891BCE491BB0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbDescServicoEmpresaGrid
        GUID = '{64AEEDD0-1E28-434B-AD36-E1C05F8AA136}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbClienteSetorDesc
        GUID = '{E2B2C6AC-BAA7-4D7C-B4A0-1095C83E98D2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbClienteDescontosEmpresa
        GUID = '{BCB88B39-DC02-4923-9EBD-44E95136316F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbClienteDiverso
        GUID = '{18A7A4FC-2F7B-4C30-8BCA-DA97EAF6D3AF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbDescServicoEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'PARM_SYS'
    TableName = 'PARM_SYS'
    Cursor = 'DESC_SERVICO_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;46002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDescServicoTimeGrid: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESC_SERV'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_TIME'
    TableName = 'CRM_TIME'
    Cursor = 'DESC_SERVICO_TIME'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;46003'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDescServicoEmpresaGrid: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'PARM_SYS'
    TableName = 'PARM_SYS'
    Cursor = 'DESC_SERVICO_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;46004'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbParmSys: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESC_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'PARM_SYS'
    Cursor = 'PARM_SYS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTime: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESC_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIME'
    Cursor = 'CRM_TIME'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;46006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDescServicoCliente: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'DESC_SERVICO_CLIENTE'
    MaxRowCount = 200
    OnAfterScroll = tbDescServicoClienteAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;46008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDescServicoClienteEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'DESC_SERVICO_CLIENTE_EMPRESA'
    MaxRowCount = 200
    OnAfterScroll = tbDescServicoClienteEmpresaAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;460010'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDescServicoClienteEmpSetor: TFTable
    FieldDefs = <
      item
        Name = 'SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'DESC_SERVICO_CLIENTE_EMP_SETOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;460011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteSetorDesc: TFTable
    FieldDefs = <
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_SETOR_DESC'
    TableName = 'CLIENTE_SETOR_DESC'
    Cursor = 'CLIENTE_SETOR_DESC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;460012'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteDescontosEmpresa: TFTable
    FieldDefs = <>
    UpdateTable = 'CLIENTE_DESCONTOS_EMPRESA'
    TableName = 'CLIENTE_DESCONTOS_EMPRESA'
    Cursor = 'CLIENTE_DESCONTOS_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;460013'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteDiverso: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_DIVERSO'
    TableName = 'CLIENTE_DIVERSO'
    Cursor = 'CLIENTE_DIVERSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600439;460016'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
