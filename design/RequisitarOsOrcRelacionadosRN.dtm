object RequisitarOsOrcRelacionadosRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '44206'
  Height = 299
  Width = 442
  object tbRequisitarOsOrcRelac: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_ORC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Orcamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_ORC_DISPLAY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Orc Display'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_OS_ORC_RELAC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRequisitarOsOrcItensAprov: TFTable
    FieldDefs = <
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_REQUISTADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Requistada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SALDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Saldo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REQUISITAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Requisitar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORC_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Orc Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_OS_ORC_ITENS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGetProdutivoSugestao: TFTable
    FieldDefs = <
      item
        Name = 'COD_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produtivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_OS_ORC_PRODUTIVO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44204'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRequisitarOsOrcServCombo: TFTable
    FieldDefs = <
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_OS_ORC_SERV_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44206'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRequisitarTempOrcComboEx: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_ORC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Orc'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_TEMP_ORC_COMBO_EX'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44207'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
