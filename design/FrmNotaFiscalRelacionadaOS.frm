object FrmNotaFiscalRelacionadaOS: TFForm
  Left = 321
  Top = 163
  ActiveControl = FVBox1
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Notas Fiscais Relacionadas a OS'
  ClientHeight = 393
  ClientWidth = 576
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '426013'
  ShortcutKeys = <>
  InterfaceRN = 'NotaFiscalRelacionadaOSRN'
  Access = False
  ChangedProp = 
    'FrmNotaFiscalRelacionadaOS.ActiveControlFrmNotaFiscalRelacionada' +
    'OS.Width;'#13#10'FrmNotaFiscalRelacionadaOS.Height;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 576
    Height = 393
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object HBoxSeparTop: TFHBox
      Left = 0
      Top = 0
      Width = 90
      Height = 10
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object HBoxBotao: TFHBox
      Left = 0
      Top = 11
      Width = 500
      Height = 65
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 9
      Padding.Right = 9
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 9
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 82
        Height = 59
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -15
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnImprimir: TFButton
        Left = 82
        Top = 0
        Width = 82
        Height = 59
        Hint = 'Imprimir'
        Caption = 'Imprimir'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -15
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnImprimirClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001754944415478DAC5953F2845511CC7CF9362212506AB2262F36F3049
          5262451691C88B52264929992C888997C58641892C0C8AB051A22C0683B23C93
          123EBFEE7975DEF3EE3BEFDC775FBEF5E9767FF79EF3E99E7F37A2FEA61B8E95
          3D63B0657B2992A636AC1BB6F9B46984187CC120EC0511CC42834F1B119FC30C
          AC6AC97E3E04C530016B9924B90A24E3B00E03701086A015AE60DBA88DEA6B07
          9CE52A288245284BA9F7C382FE9A9C047EB98715D84915C8B855C0A7AE954325
          3C380AEAE00DDEF57D09148AE047792BE0D6B1435BDAA12721680E49300735F0
          04A77013B6600A6AE1112EF32130D3F46F8202A80AD8E92B7CDB04D3CA3BC482
          444ED9C4AE4E126C18825EA8864EC7CE65D53CC39121888AE00E4AE1433F904D
          F7A2BF4A86AA2B8B8E65684EA01EE2BA2E1B2D9EEEA89857DE26913F5B1F2C5B
          04B2F60FB5E00296CC8736814B02098694373F991285DDA002999B168BE05A79
          E3EE249854DEF8BB44E661331BC1884AFE5BB944F640CC2CFC024BD862B16781
          991D0000000049454E44AE426082}
        ImageId = 4600353
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnRecuperar: TFButton
        Left = 164
        Top = 0
        Width = 82
        Height = 59
        Hint = 'Recuperar NFE'
        Caption = 'NFe'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -15
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnRecuperarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D49484452000000110000001408060000006BA0D6
          49000000D44944415478DA6364A0026004E2A940AC4DA4FA17401C07C4BFD00D
          F90F65EF01E2A33834832C0981B25581F80E2E436600F14A1C863800713D3186
          100BA862483C102F22C59072203E0565F303F10620FE0BC46140BC8E58433C81
          7807942D02C4AFA161970CC44140BC855C4340611201C4B540EC0BC4BBC83504
          14B02D405C04C4EEA418C203C46F81980D4DCD15520C010135209642E26B01F1
          54520D410716407C7C7019F21188F92835441188E5B1280025A410620DC1053E
          400DBA83478D0C1027E333A40D88AD180883F38C442822080069213DADB00430
          B60000000049454E44AE426082}
        ImageId = 7000122
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object FHBox2: TFHBox
      Left = 0
      Top = 77
      Width = 90
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object FHBox3: TFHBox
      Left = 0
      Top = 83
      Width = 568
      Height = 157
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 9
      Padding.Right = 9
      Padding.Bottom = 9
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object pcRelacionado: TFPageControl
        Left = 0
        Top = 0
        Width = 536
        Height = 146
        ActivePage = tsVenda
        TabOrder = 0
        TabPosition = tpTop
        OnChange = pcRelacionadoChange
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        WOwner = FrInterno
        WOrigem = EhNone
        RenderStyle = rsTabbed
        object tsVenda: TFTabsheet
          Caption = 'Venda'
          Visible = True
          Closable = False
          WOwner = FrInterno
          WOrigem = EhNone
          object FVBox5: TFVBox
            Left = 0
            Top = 0
            Width = 528
            Height = 118
            Align = alClient
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 5
            Padding.Left = 5
            Padding.Right = 5
            Padding.Bottom = 5
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FGrid1: TFGrid
              Left = 0
              Top = 0
              Width = 486
              Height = 101
              TabOrder = 0
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbVendas
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = True
              Paging.PageSize = 0
              Paging.DbPaging = True
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = True
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              CustomActionButtons = <>
              ActionColumn.Title = 'A'#231#245'es'
              ActionColumn.Width = 100
              ActionColumn.TextAlign = taCenter
              ActionColumn.Visible = True
              Columns = <
                item
                  Expanded = False
                  FieldName = 'NUMERO'
                  Font = <>
                  Title.Caption = 'N'#250'mero'
                  Width = 90
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{FBE4B29C-4C22-4ED9-BC82-5E6AEA87CBB0}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'OPERACAO_VENDA'
                  Font = <>
                  Title.Caption = 'Opera'#231#227'o Venda'
                  Width = 180
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{9884A83A-48A5-4A93-B4C2-6240A871B2CF}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'TOTAL_NOTA'
                  Font = <>
                  Title.Caption = 'Total'
                  Width = 95
                  Visible = True
                  Precision = 2
                  TextAlign = taRight
                  FieldType = ftDecimal
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{5F7EB6EB-DC22-42C6-A821-16D22F68A813}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'DESCRICAO_STATUS'
                  Font = <>
                  Title.Caption = 'Status'
                  Width = 60
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{D43A1881-9C33-4A0D-BAEC-C33CC6C25D9F}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'GEROU_NFE'
                  Font = <>
                  Title.Caption = 'Nfe'
                  Width = 40
                  Visible = True
                  Precision = 0
                  TextAlign = taCenter
                  FieldType = ftCheckBox
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{18247D28-C2AD-4D5A-B6F3-41067C1EEB3E}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'EMISSAO'
                  Font = <>
                  Title.Caption = 'Emiss'#227'o'
                  Width = 95
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftDate
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{D5A04D0C-B64E-455F-9866-D3383AC75126}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end>
            end
          end
        end
        object tsCompra: TFTabsheet
          Caption = 'Compra'
          Visible = True
          Closable = False
          WOwner = FrInterno
          WOrigem = EhNone
          ExplicitLeft = 0
          ExplicitTop = 0
          ExplicitWidth = 0
          ExplicitHeight = 0
          object FVBox4: TFVBox
            Left = 0
            Top = 0
            Width = 528
            Height = 118
            Align = alClient
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 5
            Padding.Left = 5
            Padding.Right = 5
            Padding.Bottom = 5
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FGrid2: TFGrid
              Left = 0
              Top = 0
              Width = 486
              Height = 101
              TabOrder = 0
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbCompras
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = True
              Paging.PageSize = 0
              Paging.DbPaging = True
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = True
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              CustomActionButtons = <>
              ActionColumn.Title = 'A'#231#245'es'
              ActionColumn.Width = 100
              ActionColumn.TextAlign = taCenter
              ActionColumn.Visible = True
              Columns = <
                item
                  Expanded = False
                  FieldName = 'COD_CONTROLE'
                  Font = <>
                  Title.Caption = 'Controle'
                  Width = 85
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{A0F782F1-FAB0-4069-AC07-FA16BF99F2BC}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'SERIE_NOTA'
                  Font = <>
                  Title.Caption = 'S'#233'rie'
                  Width = 60
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{B44E4A0A-B18A-49A4-A8F5-4CCC375B694B}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'NUMERO_NOTA'
                  Font = <>
                  Title.Caption = 'N'#250'mero'
                  Width = 40
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{FFFC70D9-6A8F-4AD6-A25A-B0F050EE14E9}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'TOTAL_NOTA'
                  Font = <>
                  Title.Caption = 'Total'
                  Width = 95
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftDecimal
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{1900D02D-08B7-42CE-9861-6C1FEED03055}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'DESC_STATUS'
                  Font = <>
                  Title.Caption = 'Status'
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{1A30826D-E8F3-4560-AA1D-A2BC4184E523}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'DATA_EMISSAO'
                  Font = <>
                  Title.Caption = 'Emiss'#227'o'
                  Width = 95
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftDate
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{884CA049-E5F7-4FED-97B1-4D67EDC79A0D}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'GEROU_NFE'
                  Font = <>
                  Title.Caption = 'Nfe'
                  Width = 50
                  Visible = True
                  Precision = 0
                  TextAlign = taCenter
                  FieldType = ftCheckBox
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{8AF83E22-F8AC-488C-B463-4F4903C7D6EC}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end>
            end
          end
        end
      end
    end
  end
  object tbVendas: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        GUID = '{4B11417A-00F5-442C-9A0E-620BCF847043}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OPERACAO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Opera'#231#227'o Venda'
        GUID = '{2F259835-7A31-43F7-89F1-18D9B5BBF734}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Nota'
        GUID = '{C8070814-F852-4DD9-BC7C-E42A1528B2A1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        GUID = '{2781B0E6-9C60-41D1-A58E-DFE3BE91DCD7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GEROU_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nfe'
        GUID = '{07616820-EDC2-4401-964C-F2FACBF801EE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Emiss'#227'o'
        GUID = '{399B56B1-FBF8-474B-A2EF-59A4B5F09498}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{7E63EEF7-8342-4C32-8CDE-AE83AB41A0BC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_VENDAS'
    MaxRowCount = 200
    OnAfterScroll = tbVendasAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '426013;42601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 482
    Top = 22
  end
  object tbCompras: TFTable
    FieldDefs = <
      item
        Name = 'COD_CONTROLE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Controle'
        GUID = '{CDDD278F-4B49-47D0-B20D-54BB5446F203}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Nota'
        GUID = '{4AE6EBC8-0145-45A1-83BE-0E468FD88152}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERIE_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'S'#233'rie Nota'
        GUID = '{AA39B185-F517-4106-8DA8-526074DFA920}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Nota'
        GUID = '{0908B606-16F2-403E-A3D4-DD907F3394C1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{CF5BBC03-D587-447F-A050-ACCCB09EADF4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Emiss'#227'o'
        GUID = '{6A01ADE7-CB93-4CED-87ED-4769B8CCA342}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GEROU_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nfe'
        GUID = '{034E98BA-2CC3-48D7-80D8-E285A8440496}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Status'
        GUID = '{5E028608-3CE9-4552-B96C-3510AB829892}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_COMPRAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '426013;42602'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 538
    Top = 26
  end
  object tbCrmpartsNfeMovimento: TFTable
    FieldDefs = <
      item
        Name = 'PDF_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Pdf Nota'
        GUID = '{1FC7A631-F027-4CC9-99F8-BC644206ADB7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHAVE_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chave Nfe'
        GUID = '{BF2C7B2C-BD5D-4A1C-9F05-5B8BA8ED8C6A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERIE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'S'#233'rie'
        GUID = '{002607D7-C520-4191-BFEE-24A97643CC9F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEM_PDF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tem Pdf'
        GUID = '{D06BE454-1351-4B4E-8102-20AB6FD5CF6A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Nfe'
        GUID = '{47438F62-0C11-4246-9538-E317874CEAAB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRMPARTS_NFE_MOVIMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '426013;47401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object popMenuImpressao: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 448
    Top = 59
    object imprimirNfe: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Imprimir NFe'
      ImageIndex = 4600353
      OnClick = imprimirNfeClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{28652B50-E9DD-4095-B20E-938EAD562ACF}'
    end
    object abrirAssinaturaDigital: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Assinatura Digital'
      OnClick = abrirAssinaturaDigitalClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{1B3A5513-9AC6-4A1C-AC17-77FEEC7D16D1}'
    end
  end
end
