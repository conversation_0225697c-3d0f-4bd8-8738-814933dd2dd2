object FrmInclusaoEventoReclamacao: TFForm
  Left = 320
  Top = 162
  ActiveControl = vBoxInclusaoEventoReclamacao
  Caption = 'Inclus'#227'o de Evento de Reclama'#231#227'o'
  ClientHeight = 275
  ClientWidth = 512
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '430019'
  ShortcutKeys = <>
  InterfaceRN = 'InclusaoEventoReclamacaoRN'
  Access = False
  ChangedProp = 
    'FrmInclusaoEventoReclamacao.Height;'#13#10#13#10'FrmInclusaoEventoReclamac' +
    'ao.ActiveControlFrmInclusaoEventoReclamacao.Width;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxInclusaoEventoReclamacao: TFVBox
    Left = 0
    Top = 0
    Width = 512
    Height = 275
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = True
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hbTopButtons: TFHBox
      Left = 0
      Top = 0
      Width = 556
      Height = 63
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 3
      Padding.Left = 3
      Padding.Right = 3
      Padding.Bottom = 3
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnSair: TFButton
        Left = 0
        Top = 0
        Width = 62
        Height = 58
        Hint = 'Sair'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnSairClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D494844520000001E0000002408060000009ED18A
          69000001C74944415478DAED964D2B44511880E736313433A4F191AF8D7C3449
          63A344D1105176CA4E4AD9B350ACFC01C91F9805C90AA10819AC64A1EC247676
          16B2411A99EB39395337C69D73C7BD83BAA79EDE9973DEF33EE77E9EABE9BAEE
          F98DA6B96257EC8A45634E48D3B4FB9C89C9CD27ACC230AC201F755C4C9E8FB0
          0643B22B81D8E7A8989C02C2060C1ABA9388BD8E89192F246C42FF97C93447C4
          8CF909DBD0F34D8A1777D25631FD01C20E7499CCDF820B38803316A17CA7A615
          D35744D8854E0B0771030B106301AF96C5FC2F26EC417B36A790760963C8CF95
          C5FC2E21EC435B96D2544BC004F26555B1B8567D3F94A69AB8E9C6912FA9885B
          098710B2492E8E3C8AFCD4542CE511292FB5497E0D11E42FA662296F21C4A1CC
          26F914E2C58C62296F96F20A1BC4B75087FC2DA358412E4E9D78E4C4E3570E0D
          D001DD104C93DF8BF848492CE56129AFFC34F44C217F9A7CF16E1F815968320C
          CD933FAD2C96C54401B1DA2A43F71385022673F20833300762173B213F6A492C
          0B354A79B5EC7AA4505061DE80E7631F7F20BFD6B25816A9271C430D5C5128AC
          384F6CA9EBC6855AFEE6225F5C6B71CAE214BAB3306FD2F848E5EC2B138F66DC
          36FFD7E7AD2B76C57F5AFC0E2AEAFAB92FC4B7AF0000000049454E44AE426082}
        ImageId = 430032
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FVBox2: TFVBox
        Left = 62
        Top = 0
        Width = 5
        Height = 48
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnSalvar: TFButton
        Left = 67
        Top = 0
        Width = 62
        Height = 58
        Hint = 'Salvar Altera'#231#245'es'
        Align = alLeft
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001374944415478DA6364A031608431FEFFFF2F09A4F449D4FF1D88CF33
          32327EC26B01D4F00740CC468623419654002D9984CF020F20B51D88D700F141
          206E00626134B5CB80F838946D09C451503D1A40AC08C48D404B1A0859900B54
          3405C8BF03642BA3A9F504CAED40570FC4AB81783F106B62B384620BA0EAC571
          5942150BA062C896E4C3E2846A16205972028841B43850EE332916608B64140B
          A0665500A976207600CA1DC465C152A80184800550FD49340B7280D464988F71
          59C002645B0331271EC36F03D5DE451724CA02225C8E130C0E0B880C2264002A
          2E8E02F5FE21D602622319192C03EA8D26D60250321560809449C40090BA0F40
          BD2AA458C000D24064B8C3D58F5A40B105C8150E030369910CA3ED813804DD02
          4AAA4C6CE017102B002D784E69A58F0B5C04190E0E362A1988130000D0053128
          1A34B73E0000000049454E44AE426082}
        ImageId = 310032
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object FVBox1: TFVBox
      Left = 0
      Top = 64
      Width = 509
      Height = 44
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox1: TFHBox
        Left = 0
        Top = 0
        Width = 505
        Height = 39
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 5
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox2: TFHBox
          Left = 0
          Top = 0
          Width = 104
          Height = 29
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 6
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FLabel1: TFLabel
            Left = 0
            Top = 0
            Width = 95
            Height = 13
            Caption = 'Tipo de Reclama'#231#227'o'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
          end
        end
        object cbbTipoReclamacao: TFCombo
          Left = 104
          Top = 0
          Width = 394
          Height = 21
          LookupTable = tbServiceTipoReclamacao
          LookupKey = 'COD_TIPO_EVENTO'
          LookupDesc = 'DESC_TIPO_EVENTO'
          Flex = False
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = True
          Prompt = 'Selecione o tipo de reclama'#231#227'o'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
        end
      end
    end
    object FLabel2: TFLabel
      Left = 0
      Top = 109
      Width = 108
      Height = 13
      Caption = 'Reclama'#231#227'o do Cliente'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
    end
    object memReclamacao: TFMemo
      Left = 0
      Top = 123
      Width = 508
      Height = 149
      CharCase = ccNormal
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Lines.Strings = (
        'FMemo1')
      Maxlength = 0
      ParentFont = False
      TabOrder = 2
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      Constraint.CheckWhen = cwImmediate
      Constraint.CheckType = ctExpression
      Constraint.FocusOnError = False
      Constraint.EnableUI = True
      Constraint.Enabled = False
      Constraint.FormCheck = True
    end
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbEventos
        GUID = '{AD5B328C-C075-466C-9A38-257CE4F0493F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbObs
        GUID = '{3347D8AE-FEF7-4B7C-8A62-3B8E07B48078}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbEventos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_CRIACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Cria'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EVENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRIOU_O_EVENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Criou o Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL_PELO_EVENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel Pelo Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_EVENTOS'
    TableName = 'CRM_EVENTOS'
    Cursor = 'CRM_EVENTOS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430019;43001'
    DeltaMode = dmAll
    RatioBatchSize = 20
  end
  object tbObs: TFTable
    FieldDefs = <
      item
        Name = 'SEQ_OBS'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Origem'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_OBS'
    TableName = 'CRM_OBS'
    Cursor = 'CRM_OBS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430019;43002'
    DeltaMode = dmAll
    RatioBatchSize = 20
  end
  object tbServiceTipoReclamacao: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Tipo Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_TIPO_RECLAMACAO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430019;43003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbServiceFilaReclamacao: TFTable
    FieldDefs = <
      item
        Name = 'FILA_RECLAMACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fila Reclama'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_FILA_RECLAMACAO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430019;43004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
end
