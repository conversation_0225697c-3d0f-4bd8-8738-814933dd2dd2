object FrmGerPendenciasFaturamentoFiltro: TFForm
  Left = 44
  Top = 162
  ActiveControl = FVBox1
  Caption = 'Filtro'
  ClientHeight = 449
  ClientWidth = 554
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600504'
  ShortcutKeys = <>
  InterfaceRN = 'GerPendenciasFaturamentoFiltroRN'
  Access = False
  ChangedProp = 
    'FrmGerPendenciasFaturamentoFiltro.Height;'#13#10'FrmGerPendenciasFatur' +
    'amentoFiltro.Width;'#13#10#13#10'FrmGerPendenciasFaturamentoFiltro.ActiveC' +
    'ontrol'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 554
    Height = 449
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 550
      Height = 60
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 1
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      ParentBackground = False
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnAceitar: TFButton
        Left = 0
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Aceitar'
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001374944415478DA6364A031608431FEFFFF2F09A4F449D4FF1D88CF33
          32327EC26B01D4F00740CC468623419654002D9984CF020F20B51D88D700F141
          206E00626134B5CB80F838946D09C451503D1A40AC08C48D404B1A0859900B54
          3405C8BF03642BA3A9F504CAED40570FC4AB81783F106B62B384620BA0EAC571
          5942150BA062C896E4C3E2846A16205972028841B43850EE332916608B64140B
          A0665500A976207600CA1DC465C152A80184800550FD49340B7280D464988F71
          59C002645B0331271EC36F03D5DE451724CA02225C8E130C0E0B880C2264002A
          2E8E02F5FE21D602622319192C03EA8D26D60250321560809449C40090BA0F40
          BD2AA458C000D24064B8C3D58F5A40B105C8150E030369910CA3ED813804DD02
          4AAA4C6CE017102B002D784E69A58F0B5C04190E0E362A1988130000D0053128
          1A34B73E0000000049454E44AE426082}
        ImageId = 310032
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
      object btnVoltar: TFButton
        Left = 65
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D494844520000001E00000019080600000026359E
          1A000001DC4944415478DAEDD6CF2B04611CC771CF4EAC6D776D5A3FF2EB226C
          92D64589A2251BE5A6DCA4943B07C5C93F20F9071C484E0845C82E2739283789
          9B9B835CD0B6DA19EFA9E7306DDBEC33BBB3EB62EAE99966BEDFCF6B9F999D9D
          15157FB4897F5865D3753DECF178DECB061B865105BA278498667F57D3B4D992
          C3805EB07DD029F921D2C0DE92C220D5C087A09396633AB0563218D00772041A
          CF3EC77D2EF8CB69DB08EA073D011DCDD92C84C6D05D850103C0A7040FDBD41C
          73FE81DD4BE63B8651140C5843E8194143AA41D4BF50BFC1D862FC3886090801
          9FD33C50C825A4FF91DE39EEFFBD324C532DE8058DFD85A0969C34190BE03B4A
          70269331EFD57831A805D7C99A07DF5659711F2BBEA221EC126EAE3C067E6B0B
          9B1B70940613AF73097F068E9297B28525DE4B4382E27A3770B625F0CDBCB0C4
          7B24DEE8C2AA5F81DBC9CAE485F3E11C4FC9472EC4686074726C907984E3C11C
          7163E0492558E2DD126FCA82BF7947F873D4FB986638BF4A4FC452BF4EFDB232
          2CC3223426096AB6047D1114B0B9BC958C15C69AF99BCE7C437DCC112CF12E89
          B7C8E04F82820A7D13E67B9CDD0FEADB1CC332A483906BF056E62782BA15FBE2
          D41F583FA8E3F72921E6BD362F59822FCB9B83BE45EB2355B67F99AC58585F9B
          BFD1F70D29E2565C250000000049454E44AE426082}
        ImageId = 4600399
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
      object btnLimpar: TFButton
        Left = 130
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Limpar'
        Caption = 'Limpar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnLimparClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000017000000190806000000DA20B5
          D00000021B4944415478DAB5944F28445114C6E78D8C10C30E858D254A16EC0C
          2516560A0B692C2863C342C9C22C90582836480AC9C29FEC1425C68E8514962C
          5058C99F109A19BF3BEED3C3CC7DEF89A9D377EE79DFF7DDF3CEDC7735C73FFE
          343342381C7613F3A4D59AA6C5CBDA1BB0CEBA89B8FBB57928141A06DA893EE2
          419653083F31EE743ABB4DCD3149A48BB8289D2F03713CABFD565F0582D4EBA2
          68826CFAAC190A01A0EC8FC6BDC3A61E63E70D4029C54E708ECD8EEDB8A1CB07
          BCE846C15D3A5FFC327336C886740EA19587D376CCD1B6A29D429B83F6E2CBCC
          E568928047A207E2904D73A119244D069F7E984B92309F60F72EB9F609643D11
          2B97BC11A08D75F2E7A8A2989FB1F336D12CDF262067EA89954BDD1CE0C13C57
          65BE0F5C41AAB169BE0664A02B56996F006E48A536CDF7805B74552AF305A004
          529E4DF353C7C7116C54998F21F0126936CDC51D338B7987CABC1781B8475CE0
          9B15737217E90BE8C7BC5F65EE43304E9A095E5B314723B897D4DAF5A319D51C
          82B88896C00288C716CD0BC04396F5E0B2AAF372085BA4020316CD231A6A1534
          B4AD328F7421DE00E28AC599EB6F5B88E64865AECFCF0771D262E791FF895A16
          9A2BD5CCF57FBE17E280BC8AC51DB2A8C8F5139600BEC634379CD919849D0E0B
          3FF16D00CDF0DDC67A2CF313E000729D457371428AF4AFDACC7C4A7442CC12F7
          26DEA9849798C7BCC5D49C79A71393A49584CBC4FC95396FC21777F98DF1C13B
          4636AB29E27144870000000049454E44AE426082}
        ImageId = 4600400
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
    end
    object FVBox2: TFVBox
      Left = 0
      Top = 61
      Width = 550
      Height = 230
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 2
      Padding.Right = 2
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object cbbEmpresa: TFCombo
        Left = 0
        Top = 0
        Width = 145
        Height = 21
        LookupTable = tbLeadsEmpresasUsuarios
        LookupKey = 'COD_EMPRESA'
        LookupDesc = 'EMPRESA'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Empresa'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        OnChange = cbbEmpresaChange
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object cbbGrupoPendencia: TFCombo
        Left = 0
        Top = 22
        Width = 145
        Height = 21
        LookupTable = tbOsPendenciaGrupo
        LookupKey = 'ID_GRUPO'
        LookupDesc = 'GRUPO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Grupo de Pend'#234'ncia'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        OnChange = cbbGrupoPendenciaChange
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object cbbPendencia: TFCombo
        Left = 0
        Top = 44
        Width = 145
        Height = 21
        LookupTable = tbOsPendenciaGrid
        LookupKey = 'ID'
        LookupDesc = 'PENDENCIA'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Pend'#234'ncia'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object cbbFuncao: TFCombo
        Left = 0
        Top = 66
        Width = 145
        Height = 21
        LookupTable = tbFuncaoLiberaPendenciaFat
        LookupKey = 'COD_FUNCAO'
        LookupDesc = 'FUNCAO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Fun'#231#227'o do Respons'#225'vel'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        OnChange = cbbFuncaoChange
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object cbbUsuario: TFCombo
        Left = 0
        Top = 88
        Width = 145
        Height = 21
        LookupTable = tbRespLiberaPendenciaFat
        LookupKey = 'ID'
        LookupDesc = 'USUARIO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Usu'#225'rio Respons'#225'vel'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object cbbConsultor: TFCombo
        Left = 0
        Top = 110
        Width = 145
        Height = 21
        LookupTable = tbConsultaComboConsultor
        LookupKey = 'NOME'
        LookupDesc = 'NOME_COMPLETO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Consultor'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FVBox3: TFVBox
        Left = 0
        Top = 132
        Width = 417
        Height = 60
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 2
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 2
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel1: TFLabel
          Left = 0
          Top = 0
          Width = 22
          Height = 13
          Caption = 'O.S.'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object edtNumeroOs: TFInteger
          Left = 0
          Top = 14
          Width = 121
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          Alignment = taRightJustify
        end
      end
      object chkboxSouResp: TFCheckBox
        Left = 0
        Top = 193
        Width = 419
        Height = 17
        Caption = 'Somente as Pend'#234'ncias que Sou Respons'#225'vel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 7
        CheckedValue = 'S'
        UncheckedValue = 'N'
        ReadOnly = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taAlignTop
      end
    end
  end
  object tbOsPendenciaGrupo: TFTable
    FieldDefs = <
      item
        Name = 'ID_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_OS_PENDENCIA_GRUPO'
    Cursor = 'CRM_OS_PENDENCIA_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600504;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOsPendenciaGrid: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRM_OS_PENDENCIA_GRID'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600504;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600504;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFuncaoLiberaPendenciaFat: TFTable
    FieldDefs = <
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'FUNCAO_LIBERA_PENDENCIA_FAT'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600504;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRespLiberaPendenciaFat: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'RESP_LIBERA_PENDENCIA_FAT'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600504;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaComboConsultor: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_COMBO_CONSULTOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600504;46006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
