object GerencialVendasPendentesRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '123032'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbConsultaComboEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_COMBO_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123032;12301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsVendasPendentes: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Original'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PEDIDO_NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Interno'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PEDIDO_DATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Pedido Data'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PEDIDO_PREVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Previs'#227'o Chegada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMOBILIZADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Imobilizado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_LIBERACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Libera'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_LIBEROU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Liberou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO_LIBERACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Motivo Libera'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIAS_LIBERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dias Liberado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_CANCELAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Cancelamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_CANCELOU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Cancelou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO_CANCELAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Motivo Cancelamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTROLE_VENDA_PENDENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Controle Venda Pendente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_RESERVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Reserva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSULTOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consultor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOC_RESERVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Reserva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_RESERVADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Reservada'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONS_VENDAS_PENDENTES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123032;12302'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbContaVendasPendentes: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONS_VENDAS_PENDENTES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123032;12303'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEventos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EVENTOS'
    Cursor = 'CRM_EVENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123032;12304'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
