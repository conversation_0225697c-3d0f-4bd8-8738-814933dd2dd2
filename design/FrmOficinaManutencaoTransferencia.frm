object FrmOficinaManutencaoTransferencia: TFForm
  Left = 321
  Top = 162
  ActiveControl = vboxPrincipal
  Caption = 'Transfer'#234'ncias'
  ClientHeight = 702
  ClientWidth = 484
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600704'
  ShortcutKeys = <>
  InterfaceRN = 'OficinaManutencaoTransferenciaRN'
  Access = False
  ChangedProp = 
    'FrmOficinaManutencaoTransferencia.Width;'#13#10'FrmOficinaManutencaoTr' +
    'ansferencia.Height;'#13#10#13#10'FrmOficinaManutencaoTransferencia.ActiveC' +
    'ontrol'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 484
    Height = 702
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 2
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitHeight = 687
    object hBoxBtnTopo: TFHBox
      Left = 0
      Top = 0
      Width = 480
      Height = 65
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 3
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Aceitar'
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D494844520000001E0000001E08060000003B30AE
          A20000050A4944415478DAB5976B4C5C4514C7FF73F7FD2ECF428552406C8B25
          D894B6A056B4155F4841FDA4C4A8A9B104134DAD49DDA67C6915F143435A039A
          DA0F1634FD52A36D6309222628102C2DC48A292D48A1506C80E5B1BBECB28F7B
          AFE72E65C302CB2E154F3259EECC30BF73E6CC39738689A2885072B899A9E8E7
          796A4FABE4BA6CADDC98A6511875D298D363B53BBCD65E9777BA8D3E7FA65657
          FEB8E80AB5265B0E4CC058FA3910A18E7F2FD1F8B03656BB011EC125C1E0F4DA
          7D7334723D3523649C1C63CE410C5ABB1C1333774FD250252930B26230414BF4
          8AC8CACDD1BBD4A2C863D0F617461D03104461E9851843B42611098674524281
          EEB19619BB67FC00C1BF0C0B4C400D449C5D6FDA5218AB4DC68DF116D8DCE321
          DD315F740A131E8ACC81C53984DB535DE7C1F02A29E00C0A26A816027EDD189D
          B3CDC53BD03FF5C78A800B25C1B0195A52E2E658DB55707882E08E4560823282
          3624ADC9D863F74CF8B45D0D8950C7C1A08CC6EDC9AE4682E7115C0C0437B123
          51FA84633226C788A37F55A07312A35DEF3B1B16FB505979AEF8B11F4CD62671
          90F5ADD5A570FF4CF7AC2A744EE274A91899EE1704F02964F5C02CF8177621CA
          F840C1A47B04BCE0F95FC072991C26C55A58AC772E96EF16F732F36F8860222C
          7A5514B3B92DAB0E5431231ED117A3D35E03B542039B6B4C1419A298B9018754
          3A4D854770931FF85585A6AB8A90F7E0517CD3FD0A2C7C0F3826838253C235ED
          FC88997F428752A7DEEA1666560D6812135090F4393625BE88AFAFBC805E5783
          7F4CC9A9E19E9EE964E646583995CC2073ABC0CBC96AE6BD6F201338641B4AF1
          CC964FA0521871E9CF0FD03C55193087631C04976063E626F0F4370791C37391
          9FC1C15B7079E2149CB29565AB783E132FA59F4242CC0EDF7747DF199C1B7E73
          B172D428B204E970F953974C50E2ADB43A24C664A3B3AF06ADC3273122BB3E3B
          3B88283C5AEC8E29C3AEF483E028474B3238DA86AF6E3C092F825F5292C522D9
          EB17356FC23B194D888BCC947443CF503D5A064EA0C7530F513E2FAFD35D91C6
          9E45516635228C29FE6E9B6318559DDB611587836B2B48E046F050CE4703467E
          1D4AB6B5628D3EC9DF373AD98DD69B27D039550B25D3213FB91299A9AF05ACE7
          E56770AA3D1743DECBCBFB453A4AE63A58A18761E1588CB009FB773643AB8A0A
          E877BA667DAF51452E5AEFDCD537D0E1AC097D20ECA0C3F53D3A108DAD4B8D27
          E131ECCB69A0ACA309B95673F7715C1AFB30345492315038D5E21036A022D89C
          7459218AB3BFA38B5E16749D9E3BF538732B9F5C176602EA0725902A44208574
          D007FA79BEEC50EF4751D6928504E5DE1E545FDB092726C283DA49BF3E44CF5E
          12DFB20BB4AF05CBCDCF331DC5531965017D2EF714BE68CFC188783D3CA82403
          B8585E4C97840F5CCD929088BFC9F6E0FB4921F072FC6964A5EDF37D8AD451FB
          FB5E747B7E0C1F3A41BE18446A79E9BD6B5192C3A7D911DAF263145A4185E3E5
          783DE5076C4CCC47FD35339AAC156112218510688BCBCADF9E5708F8C0D554FA
          68D08064EC813CF8FF2B791D1E35BD8F26FBA76475E89ADC2752FABF85463A08
          79646D60E9730FAE257813F93B6B39CB572492A503B842D05C822E2EF6E6C135
          50E02CD6A110A6FF089DA2368CF3F050795BBA4C79BB408112CA67C711076917
          562612E22E1CB0E12001C32BE817C07D4F18E8F02E596FA058A75B24C864A98E
          B0FBACB4611A55909E30A5F7F18459A080FFD14669663BF93F8D026F36BFF394
          EBDDE8A5E86AC7DCA3AD34F4A3ED5F176212D08960780E0000000049454E44AE
          426082}
        ImageId = 4600188
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object hboxLabelTopo: TFHBox
      Left = 0
      Top = 66
      Width = 440
      Height = 30
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 3
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FLabel7: TFLabel
        Left = 0
        Top = 0
        Width = 143
        Height = 14
        Caption = 'Quer Transferir da O.S.:'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object lblOsR: TFLabel
        Left = 143
        Top = 0
        Width = 29
        Height = 14
        Caption = 'lblOs'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
    end
    object vboxOrigem: TFVBox
      Left = 0
      Top = 97
      Width = 480
      Height = 305
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 2
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 2
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object pcOrigem: TFPageControl
        Left = 0
        Top = 0
        Width = 469
        Height = 281
        ActivePage = tabRequisicao
        TabOrder = 0
        TabPosition = tpTop
        OnChange = pcOrigemChange
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        WOwner = FrInterno
        WOrigem = EhNone
        RenderStyle = rsTabbed
        object tabServico: TFTabsheet
          Caption = 'Servi'#231'o'
          Closable = False
          WOwner = FrInterno
          WOrigem = EhNone
          object FVBox2: TFVBox
            Left = 0
            Top = 0
            Width = 461
            Height = 250
            Align = alCustom
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 2
            Padding.Left = 2
            Padding.Right = 2
            Padding.Bottom = 2
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox2: TFHBox
              Left = 0
              Top = 0
              Width = 441
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 2
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel4: TFLabel
                Left = 0
                Top = 0
                Width = 211
                Height = 14
                Caption = 'Escolha o Servi'#231'o a ser Transferido'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FGrid1: TFGrid
              Left = 0
              Top = 31
              Width = 456
              Height = 132
              TabOrder = 1
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbServiceConsOsServicos
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              Columns = <
                item
                  Expanded = False
                  FieldName = 'NUMERO_OS'
                  Font = <>
                  Title.Caption = 'N'#250'mero Os'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{1997BD30-0FE6-4102-A60A-1DA41ED1A1A6}'
                  WOwner = FrWizard
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'COD_SERVICO'
                  Font = <>
                  Title.Caption = 'C'#243'd. Servi'#231'o'
                  Width = 110
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{F1A41DE2-F033-425E-9643-8603060C0EBF}'
                  WOwner = FrWizard
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'ITEM'
                  Font = <>
                  Title.Caption = 'Item'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{32005B40-9AAA-46EE-9C97-E3A81EB068D3}'
                  WOwner = FrWizard
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'STATUS'
                  Font = <>
                  Title.Caption = 'Status'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{12FE56E5-9721-45B4-BD4B-1228FBA45B05}'
                  WOwner = FrWizard
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'SERVICO'
                  Font = <>
                  Title.Caption = 'Servi'#231'o'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{964AF8BB-8749-4A31-A7F8-32DABB107517}'
                  WOwner = FrWizard
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'DESC_SERVICO'
                  Font = <>
                  Title.Caption = 'Servi'#231'o'
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{355DCA3A-AB36-49F3-A5BE-48199D183F17}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end>
            end
          end
        end
        object tabRequisicao: TFTabsheet
          Caption = 'Requisi'#231#227'o'
          Closable = False
          WOwner = FrInterno
          WOrigem = EhNone
          object FVBox4: TFVBox
            Left = 0
            Top = 0
            Width = 461
            Height = 250
            Align = alCustom
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 2
            Padding.Left = 2
            Padding.Right = 2
            Padding.Bottom = 2
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 2
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox6: TFHBox
              Left = 0
              Top = 0
              Width = 441
              Height = 60
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 2
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox5: TFVBox
                Left = 0
                Top = 0
                Width = 185
                Height = 41
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel9: TFLabel
                  Left = 0
                  Top = 0
                  Width = 105
                  Height = 14
                  Caption = 'Escolha o Servi'#231'o'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -12
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object comboServRequisicao: TFCombo
                  Left = 0
                  Top = 15
                  Width = 145
                  Height = 21
                  LookupTable = tbComboServReq
                  LookupKey = 'COD_SERVICO'
                  LookupDesc = 'SERVICO'
                  Flex = True
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  OnChange = comboServRequisicaoChange
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
              object FVBox6: TFVBox
                Left = 185
                Top = 0
                Width = 185
                Height = 41
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel11: TFLabel
                  Left = 0
                  Top = 0
                  Width = 125
                  Height = 14
                  Caption = 'Escolha a Requisi'#231#227'o'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -12
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object comboRequisicao: TFCombo
                  Left = 0
                  Top = 15
                  Width = 145
                  Height = 21
                  LookupTable = tbRequisicaoOs
                  LookupKey = 'REQUISICAO'
                  LookupDesc = 'REQUISICAO_DESC'
                  Flex = True
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  OnChange = comboRequisicaoChange
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
            end
            object gridRequisicaoPecas: TFGrid
              Left = 0
              Top = 61
              Width = 364
              Height = 144
              TabOrder = 1
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbGridRequisicaoPecas
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              Columns = <
                item
                  Expanded = False
                  FieldName = 'COD_ITEM'
                  Font = <>
                  Title.Caption = 'Item'
                  Width = 120
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{679636BC-09D4-4885-9FB6-68CB9EDA3BEE}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'DESCRICAO'
                  Font = <>
                  Title.Caption = 'Descri'#231#227'o'
                  Width = 80
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{9F456FDB-B6D0-43F2-8B9C-C84308742DFD}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'QUANTIDADE'
                  Font = <>
                  Title.Caption = 'Qtde'
                  Width = 55
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{F2A6DC8C-2C38-471B-8154-809F8AFA3CD4}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end>
            end
          end
        end
        object tabPecas: TFTabsheet
          Caption = 'Pe'#231'as'
          Closable = False
          WOwner = FrInterno
          WOrigem = EhNone
          object FVBox7: TFVBox
            Left = 0
            Top = 0
            Width = 461
            Height = 250
            Align = alCustom
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 2
            Padding.Left = 2
            Padding.Right = 2
            Padding.Bottom = 2
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 2
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox12: TFHBox
              Left = 0
              Top = 0
              Width = 441
              Height = 60
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 2
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox8: TFVBox
                Left = 0
                Top = 0
                Width = 417
                Height = 53
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel12: TFLabel
                  Left = 0
                  Top = 0
                  Width = 105
                  Height = 14
                  Caption = 'Escolha o Servi'#231'o'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -12
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object comboServPecas: TFCombo
                  Left = 0
                  Top = 15
                  Width = 145
                  Height = 21
                  LookupTable = tbComboServReq
                  LookupKey = 'COD_SERVICO'
                  LookupDesc = 'SERVICO'
                  Flex = True
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  OnChange = comboServPecasChange
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
                object FLabel10: TFLabel
                  Left = 0
                  Top = 37
                  Width = 193
                  Height = 14
                  Caption = 'Escolha a Pe'#231'a a ser Transferida'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -12
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
              end
            end
            object gridPecas: TFGrid
              Left = 0
              Top = 61
              Width = 364
              Height = 144
              TabOrder = 1
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbGridPecas
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              Columns = <
                item
                  Expanded = False
                  FieldName = 'COD_ITEM'
                  Font = <>
                  Title.Caption = 'Item'
                  Width = 120
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{679636BC-09D4-4885-9FB6-68CB9EDA3BEE}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'DESCRICAO'
                  Font = <>
                  Title.Caption = 'Descri'#231#227'o'
                  Width = 80
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{9F456FDB-B6D0-43F2-8B9C-C84308742DFD}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'QUANTIDADE'
                  Font = <>
                  Title.Caption = 'Qtde'
                  Width = 55
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{F2A6DC8C-2C38-471B-8154-809F8AFA3CD4}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end>
            end
          end
        end
      end
    end
    object vboxAnotacao: TFVBox
      Left = 0
      Top = 403
      Width = 479
      Height = 270
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 5
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 4
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox3: TFHBox
        Left = 0
        Top = 0
        Width = 398
        Height = 30
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 2
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel3: TFLabel
          Left = 0
          Top = 0
          Width = 67
          Height = 14
          Caption = 'Para a O.S.'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
      object comboOs: TFCombo
        Left = 0
        Top = 31
        Width = 397
        Height = 21
        LookupTable = tbTransferenciaComboOsRel
        LookupKey = 'NUMERO_OS'
        LookupDesc = 'OS_TIPO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Prompt = 'Selecione'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        OnChange = comboOsChange
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FHBox4: TFHBox
        Left = 0
        Top = 53
        Width = 395
        Height = 34
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 2
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object hboxEscolhaAnotacao: TFHBox
          Left = 0
          Top = 0
          Width = 185
          Height = 30
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 5
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          OnClick = hboxEscolhaAnotacaoClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox7: TFHBox
            Left = 0
            Top = 0
            Width = 13
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FLabel5: TFLabel
            Left = 13
            Top = 0
            Width = 119
            Height = 14
            Caption = 'Escolha a Anota'#231#227'o'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object FHBox9: TFHBox
            Left = 132
            Top = 0
            Width = 13
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
        object hboxNovaAnotacao: TFHBox
          Left = 185
          Top = 0
          Width = 185
          Height = 30
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 5
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = hboxNovaAnotacaoClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox8: TFHBox
            Left = 0
            Top = 0
            Width = 13
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FLabel6: TFLabel
            Left = 13
            Top = 0
            Width = 93
            Height = 14
            Caption = 'Nova Anota'#231#227'o'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object FHBox10: TFHBox
            Left = 106
            Top = 0
            Width = 13
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
      end
      object gridAnotacao: TFGrid
        Left = 0
        Top = 88
        Width = 399
        Height = 168
        TabOrder = 3
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Table = tbServiceConsOsOriginal
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Paging.Enabled = False
        Paging.PageSize = 0
        Paging.DbPaging = False
        FrozenColumns = 0
        ShowFooter = False
        ShowHeader = True
        MultiSelection = False
        Grouping.Enabled = False
        Grouping.Expanded = False
        Grouping.ShowFooter = False
        Crosstab.Enabled = False
        Crosstab.GroupType = cgtConcat
        EnablePopup = False
        WOwner = FrInterno
        WOrigem = EhNone
        EditionEnabled = False
        AuxColumnHeaders = <>
        NoBorder = False
        ActionButtons.BtnAccept = False
        ActionButtons.BtnView = False
        ActionButtons.BtnEdit = False
        ActionButtons.BtnDelete = False
        ActionButtons.BtnInLineEdit = False
        Columns = <
          item
            Expanded = False
            FieldName = 'ITEM'
            Font = <>
            Title.Caption = 'Item'
            Width = 46
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{C2901DE0-7F46-49FA-BC34-2B5747941F0E}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end
          item
            Expanded = False
            FieldName = 'DESCRICAO'
            Font = <>
            Title.Caption = 'Descri'#231#227'o'
            Width = 80
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = True
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{D8B0F7AB-8D9C-4CE9-8905-EF9FCB4AE281}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end>
      end
      object edtNovaAnotacao: TFString
        Left = 0
        Top = 257
        Width = 121
        Height = 24
        HelpCaption = 'Informe a Nova Anota'#231#227'o'
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
    end
    object vboxServDestino: TFVBox
      Left = 0
      Top = 674
      Width = 479
      Height = 266
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 5
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 4
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 4
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox13: TFHBox
        Left = 0
        Top = 0
        Width = 398
        Height = 30
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 2
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel13: TFLabel
          Left = 0
          Top = 0
          Width = 67
          Height = 14
          Caption = 'Para a O.S.'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
      object comboOsDestino: TFCombo
        Left = 0
        Top = 31
        Width = 397
        Height = 21
        LookupTable = tbTransferenciaComboOsRel
        LookupKey = 'NUMERO_OS'
        LookupDesc = 'OS_TIPO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Prompt = 'Selecione'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        OnChange = comboOsDestinoChange
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object gridServicosDestino: TFGrid
        Left = 0
        Top = 53
        Width = 399
        Height = 168
        TabOrder = 2
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Table = tbGridServDestino
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Paging.Enabled = False
        Paging.PageSize = 0
        Paging.DbPaging = False
        FrozenColumns = 0
        ShowFooter = False
        ShowHeader = True
        MultiSelection = False
        Grouping.Enabled = False
        Grouping.Expanded = False
        Grouping.ShowFooter = False
        Crosstab.Enabled = False
        Crosstab.GroupType = cgtConcat
        EnablePopup = False
        WOwner = FrInterno
        WOrigem = EhNone
        EditionEnabled = False
        AuxColumnHeaders = <>
        NoBorder = False
        ActionButtons.BtnAccept = False
        ActionButtons.BtnView = False
        ActionButtons.BtnEdit = False
        ActionButtons.BtnDelete = False
        ActionButtons.BtnInLineEdit = False
        Columns = <
          item
            Expanded = False
            FieldName = 'COD_SERVICO'
            Font = <>
            Title.Caption = 'C'#243'd. Servi'#231'o'
            Width = 110
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{899228EE-FB79-4166-A8A6-EBBD63866257}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end
          item
            Expanded = False
            FieldName = 'DESC_SERVICO'
            Font = <>
            Title.Caption = 'Servi'#231'o'
            Width = 100
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = True
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{B62A2F8C-3C4C-450A-A25F-A65B57B8E2AD}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
          end>
      end
    end
  end
  object tbServiceConsOsServicos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_CONS_OS_SERVICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600704;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServiceConsOsOriginal: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_CONS_OS_ORIGINAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600704;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTransferenciaComboOsRel: TFTable
    FieldDefs = <
      item
        Name = 'OS_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Os Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'TRANSFERENCIA_COMBO_OS_REL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600704;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbComboServReq: TFTable
    FieldDefs = <
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_CONS_OS_SERVICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600704;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRequisicaoOs: TFTable
    FieldDefs = <
      item
        Name = 'REQUISICAO_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Requisi'#231#227'o Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REQUISICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Requisi'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISICAO_OS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600704;46006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGridRequisicaoPecas: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUANTIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GRID_REQUISICAO_PECAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600704;46007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGridPecas: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUANTIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GRID_REQUISICAO_PECAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600704;46008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGridServDestino: TFTable
    FieldDefs = <
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SERVICE_CONS_OS_SERVICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600704;46009'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object FCoachmark: TFCoachmark
    ShowNextButton = False
    ShowPriorButton = False
    Items = <
      item
        TargetName = 'btnAceitar'
        Position = poBefore_start
        Name = 'btnAceitar'
      end>
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 332
    Top = 16
  end
end
