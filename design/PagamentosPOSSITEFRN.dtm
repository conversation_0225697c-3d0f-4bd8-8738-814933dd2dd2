object PagamentosPOSSITEFRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '53601464'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbEmpresas: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{A01BB552-7D90-4942-A486-D5E3CC4038F4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        GUID = '{96D59334-731F-482D-83B2-44561A3CE043}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '53601464;53601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPagamentosPosSitef: TFTable
    FieldDefs = <
      item
        Name = 'EMPRESA_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Nome Codigo'
        GUID = '{36698A96-1586-41C2-A962-9C8ACE9436EE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Orc Mapa'
        GUID = '{4B06CA16-3121-4050-8C92-676052A63917}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMISSAO_DATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Emiss'#227'o Data'
        GUID = '{ACB18572-C93B-4DEC-A7B8-8FB4D7635977}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Nome Codigo'
        GUID = '{A1110151-FD95-46D2-8C31-489C864A7788}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Descri'#231#227'o Codigo'
        GUID = '{1DFC9D12-DF7E-4288-9B5A-7C6983BAD53C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_PAGAMENTOS_POS_SITEF'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '53601464;53602'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbParcelasPgtoPosSitef: TFTable
    FieldDefs = <
      item
        Name = 'ID_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pagamento'
        GUID = '{1493607C-5735-40AE-B3AF-3F6C98DBEAC2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia'
        GUID = '{FAF0F59E-E4BB-4081-B890-666DFBEFCAFB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Pagamento'
        GUID = '{CA17D500-F617-4DBA-8F5A-45C451F241F8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Descri'#231#227'o Codigo'
        GUID = '{E0452A21-0389-452D-9FF7-BEBB7D9E7F72}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Cart'#227'o'
        GUID = '{1038E568-A250-42FC-8C1D-E668C5958B38}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_PARCELAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Parcelas'
        GUID = '{04043BCC-0409-4EC6-9A0F-2BC498F01773}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_PARCELAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Parcelas'
        GUID = '{3361FD0A-ABDF-4A63-B5D5-290BACD80B52}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_PARCELAS_PGTO_POS_SITEF'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '53601464;53603'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
