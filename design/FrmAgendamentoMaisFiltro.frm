object FrmAgendamentoMaisFiltro: TFForm
  Left = 321
  Top = -2
  ActiveControl = FVBox1
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Mais Filtro Agendamento'
  ClientHeight = 622
  ClientWidth = 354
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600425'
  ShortcutKeys = <>
  InterfaceRN = 'AgendamentoMaisFiltroRN'
  Access = False
  ChangedProp = 
    'FrmAgendamentoMaisFiltro.Width;'#13#10'FrmAgendamentoMaisFiltro.Height' +
    ';'#13#10'FrmAgendamentoMaisFiltro.ActiveControlFrmAgendamentoMaisFiltr' +
    'o.Width;'#13#10'FrmAgendamentoMaisFiltro.ActiveControl;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 354
    Height = 622
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = True
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitHeight = 742
    object vBoxMaisFiltroAgendamento: TFVBox
      Left = 0
      Top = 0
      Width = 354
      Height = 890
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 5
      Padding.Left = 5
      Padding.Right = 5
      Padding.Bottom = 5
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object hBoxButton: TFHBox
        Left = 0
        Top = 0
        Width = 322
        Height = 65
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object btnVoltar: TFButton
          Left = 0
          Top = 0
          Width = 60
          Height = 59
          Hint = 'Voltar'
          Caption = 'Voltar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -16
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 0
          OnClick = btnVoltarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
            FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
            290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
            086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
            152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
            F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
            86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
            B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
            AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
            EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
            AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
            736BB6EF9B710000000049454E44AE426082}
          ImageId = 700081
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnAceitar: TFButton
          Left = 60
          Top = 0
          Width = 60
          Height = 59
          Hint = 'Aceitar'
          Caption = 'Aceitar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -16
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 1
          OnClick = btnAceitarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
            0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
            1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
            95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
            101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
            1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
            A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
            C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
            482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
            1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
            C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
            8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
          ImageId = 700088
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
      end
      object FHBox8: TFHBox
        Left = 0
        Top = 66
        Width = 345
        Height = 33
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 3
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox9: TFHBox
          Left = 0
          Top = 0
          Width = 30
          Height = 26
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object lblFiltrosDireto: TFLabel
          Left = 30
          Top = 0
          Width = 174
          Height = 14
          Caption = 'Filtros Direto - Ignora Todos'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object FHBox10: TFHBox
          Left = 204
          Top = 0
          Width = 30
          Height = 26
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
      object lblNome: TFLabel
        Left = 0
        Top = 100
        Width = 27
        Height = 13
        Caption = 'Nome'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxNome: TFHBox
        Left = 0
        Top = 114
        Width = 314
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edtNome: TFString
          Left = 0
          Top = 0
          Width = 268
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
        object vBoxLimpNome: TFVBox
          Left = 268
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vBoxLimpNomeClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object NomeLimpNome: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar Nome'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblCpfCnpj: TFLabel
        Left = 0
        Top = 155
        Width = 48
        Height = 13
        Caption = 'CPF/CNPJ'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxCpfCnpj: TFHBox
        Left = 0
        Top = 169
        Width = 315
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edtCpfCnpj2: TFInteger
          Left = 0
          Top = 0
          Width = 30
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          Maxlength = 0
          Visible = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          Alignment = taRightJustify
        end
        object edtCpfCnpj: TFString
          Left = 30
          Top = 0
          Width = 236
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
        object vBoxLimpCpfCnpj: TFVBox
          Left = 266
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          OnClick = vBoxLimpCpfCnpjClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpCpfCnpj: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar CPF/CNPJ'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblTelefone: TFLabel
        Left = 0
        Top = 210
        Width = 42
        Height = 13
        Caption = 'Telefone'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxTelefone: TFHBox
        Left = 0
        Top = 224
        Width = 201
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edtPrefixo: TFInteger
          Left = 0
          Top = 0
          Width = 40
          Height = 29
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          Maxlength = 2
          Align = alClient
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -17
          Font.Name = 'Tahoma'
          Font.Style = []
          Alignment = taRightJustify
        end
        object edtTelefone: TFInteger
          Left = 40
          Top = 0
          Width = 103
          Height = 29
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          Maxlength = 9
          Align = alClient
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -17
          Font.Name = 'Tahoma'
          Font.Style = []
          Alignment = taRightJustify
        end
        object vBoxLimpTelefone: TFVBox
          Left = 143
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          OnClick = vBoxLimpTelefoneClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpTelefone: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar telefone'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblPlacaVeiculo: TFLabel
        Left = 0
        Top = 265
        Width = 76
        Height = 13
        Caption = 'Placa do Veiculo'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxPlaca: TFHBox
        Left = 0
        Top = 279
        Width = 163
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 5
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edtPlacaVeic: TFString
          Left = 0
          Top = 0
          Width = 121
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
        object vBoxLimpPlaca: TFVBox
          Left = 121
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vBoxLimpPlacaClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpPlaca: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar Placa do Ve'#237'culo'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblChassi: TFLabel
        Left = 0
        Top = 320
        Width = 82
        Height = 13
        Caption = 'Chassi do Ve'#237'culo'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxChassi: TFHBox
        Left = 0
        Top = 334
        Width = 315
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edtChassi: TFString
          Left = 0
          Top = 0
          Width = 268
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
        object vboxLimpaChassi: TFVBox
          Left = 268
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vboxLimpaChassiClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpaChassi: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar chassi do Ve'#237'culo'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblNumeroFrota: TFLabel
        Left = 0
        Top = 375
        Width = 41
        Height = 13
        Caption = 'N'#176' Frota'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxNumeroFrota: TFHBox
        Left = 0
        Top = 389
        Width = 163
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 7
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edNumeroFrota: TFInteger
          Left = 0
          Top = 0
          Width = 121
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          Alignment = taRightJustify
        end
        object vboxLimpaNumeroFrota: TFVBox
          Left = 121
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vboxLimpaNumeroFrotaClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpaNumeroFrota: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar Evento'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblPrisma: TFLabel
        Left = 0
        Top = 430
        Width = 31
        Height = 13
        Caption = 'Prisma'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxPrisma: TFHBox
        Left = 0
        Top = 444
        Width = 206
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 8
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbbPrisma: TFCombo
          Left = 0
          Top = 0
          Width = 145
          Height = 21
          LookupTable = tbPrisma
          LookupKey = 'PRISMA'
          LookupDesc = 'PRISMA_DESCRICAO'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object vboxLimpaPrisma: TFVBox
          Left = 145
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vboxLimpaPrismaClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpaPrisma: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar Evento'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblEvento: TFLabel
        Left = 0
        Top = 485
        Width = 34
        Height = 13
        Caption = 'Evento'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxEvento: TFHBox
        Left = 0
        Top = 499
        Width = 163
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 9
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edtEvento: TFInteger
          Left = 0
          Top = 0
          Width = 121
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          Alignment = taRightJustify
        end
        object vBoxLimpEvento: TFVBox
          Left = 121
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vBoxLimpEventoClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpEvento: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar Evento'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblPreOrdem: TFLabel
        Left = 0
        Top = 540
        Width = 83
        Height = 13
        Caption = 'Pr'#233'-Ordem / AGD'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxPreOrdem: TFHBox
        Left = 0
        Top = 554
        Width = 163
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 10
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object edtPreOrdemAgd: TFInteger
          Left = 0
          Top = 0
          Width = 121
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          Alignment = taRightJustify
        end
        object vboxLimpaPreOrdem: TFVBox
          Left = 121
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vboxLimpaPreOrdemClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpaPreOrdem: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar Evento'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object hboxEventosPendentes: TFVBox
        Left = 0
        Top = 595
        Width = 304
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 7
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 11
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object chkEventosPendentes: TFCheckBox
          Left = 0
          Top = 0
          Width = 225
          Height = 17
          Caption = 'Somente Eventos Pendentes'
          Checked = True
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          State = cbChecked
          TabOrder = 0
          ReadOnly = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taAlignTop
        end
      end
      object hboxLblFiltrosGerais: TFHBox
        Left = 0
        Top = 637
        Width = 345
        Height = 35
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 8
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 12
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox12: TFHBox
          Left = 0
          Top = 0
          Width = 30
          Height = 26
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object lblFiltrosGerais: TFLabel
          Left = 30
          Top = 0
          Width = 77
          Height = 14
          Caption = 'Filtros Gerais'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object FHBox13: TFHBox
          Left = 107
          Top = 0
          Width = 30
          Height = 26
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
      object lblEstado: TFLabel
        Left = 0
        Top = 673
        Width = 33
        Height = 13
        Caption = 'Estado'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxEstado: TFHBox
        Left = 0
        Top = 687
        Width = 163
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 13
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbbEstado: TFCombo
          Left = 0
          Top = 0
          Width = 121
          Height = 21
          LookupTable = tbUf
          LookupKey = 'UF'
          LookupDesc = 'DESCRICAO'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          OnChange = cbbEstadoChange
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object vBoxLimpaUf: TFVBox
          Left = 121
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vBoxLimpaUfClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpaUf: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar UF'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblCidade: TFLabel
        Left = 0
        Top = 728
        Width = 33
        Height = 13
        Caption = 'Cidade'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxCidade: TFHBox
        Left = 0
        Top = 742
        Width = 186
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 14
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbbCidade: TFCombo
          Left = 0
          Top = 0
          Width = 145
          Height = 21
          LookupTable = tbCidades
          LookupKey = 'COD_CIDADES'
          LookupDesc = 'DESCRICAO'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object vBoxLimpaCidade: TFVBox
          Left = 145
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vBoxLimpaCidadeClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpaCidade: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar Cidade'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object lblTipoAtendimento: TFLabel
        Left = 0
        Top = 783
        Width = 84
        Height = 13
        Caption = 'Tipo Atendimento'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object hboxTipoAtendimento: TFHBox
        Left = 0
        Top = 797
        Width = 186
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 15
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbbTipoAtendimento: TFCombo
          Left = 0
          Top = 0
          Width = 145
          Height = 21
          Flex = True
          ListOptions = 'Ativo=A;Receptivo=R;Passante=P'
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object vBoxLimpaTipoAtendimento: TFVBox
          Left = 145
          Top = 0
          Width = 36
          Height = 35
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 3
          Padding.Left = 10
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = vBoxLimpaTipoAtendimentoClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            32
            31)
          object iconLimpaTipoAtendimento: TFIconClass
            Left = 0
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Apagar Cidade'
            Anchors = []
            Picture.Data = {
              07544269746D6170C6070000424DC60700000000000036000000280000001600
              0000160000000100200000000000900700000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000000000000000000000000000000000000000
              00000000000000000000000000000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              0000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00000000000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            IconClass = 'trash'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 22
            Color = clBlack
          end
        end
      end
      object vBoxMaisFiltroAgendamentoEspaco1: TFHBox
        Left = 0
        Top = 838
        Width = 185
        Height = 17
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 16
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
  end
  object tbUf: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{08B40202-8C5C-445E-8859-F302A0E0737E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{F3830D0B-6CDC-42B9-8C4B-6C6454B5489F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'UF'
    Cursor = 'UF'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600425;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCidades: TFTable
    FieldDefs = <
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{A3DDC05C-7A26-4B71-86F2-2F4D28F422C4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{E3072002-617C-4866-A6D8-28F0D7D46780}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CIDADES'
    Cursor = 'CIDADES'
    MaxRowCount = 0
    OnMaxRow = tbCidadesMaxRow
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600425;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPrisma: TFTable
    FieldDefs = <
      item
        Name = 'PRISMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prisma'
        GUID = '{1C4D6271-AEF7-4323-B593-35A87047BF27}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{17BF552D-F2A9-472B-B289-5714D5EF1BE4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRISMA_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prisma Descri'#231#227'o'
        GUID = '{4392B5DE-52CD-41E0-9394-AF9E89D30D7F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_PRISMA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600425;48901'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
