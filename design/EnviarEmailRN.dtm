object EnviarEmailRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '340042'
  Height = 299
  Width = 442
  object scCrmEmailFluxo: TFSchema
    Tables = <
      item
        Table = tbEmailFluxo
        GUID = '{F6B547BB-8CEE-4C2C-B917-871FE4AEBB0A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbEmailFluxo: TFTable
    FieldDefs = <
      item
        Name = 'NOME_EMPRESAS_USUARIOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ_ID_MAIL_FLUXO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSUNTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_ENVIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESTINO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIDO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CORPO_EMAIL_HTML'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftText
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EMAIL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENSAGEM_ERRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EXIBICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_EMAIL_FLUXO'
    TableName = 'CRM_EMAIL_FLUXO'
    Cursor = 'CRM_EMAIL_FLUXO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;34001'
    DeltaMode = dmAll
  end
  object tbEmailModelo: TFTable
    FieldDefs = <
      item
        Name = 'ID_EMAIL_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'APLICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_CRIOU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRIVADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSUNTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_MODELO'
    Cursor = 'CRM_EMAIL_MODELO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;34002'
    DeltaMode = dmChanged
  end
  object tbEmailModeloTag: TFTable
    FieldDefs = <
      item
        Name = 'ID_TAG'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_MODELO_TAG'
    Cursor = 'CRM_EMAIL_MODELO_TAG'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;34004'
    DeltaMode = dmChanged
  end
  object tbCrmpartsDadosEmail: TFTable
    FieldDefs = <
      item
        Name = 'SAUDACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SR_SRA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DO_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'E_MAIL_DO_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'N_ORCAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_BRUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'VW_CRMPARTS_DADOS_EMAIL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;31002'
    DeltaMode = dmChanged
  end
  object tbParmFluxo: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERVALO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FLUXO_TEMPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTREGA_TEMPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TDRIVE_NA_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TDRIVE_TEMPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTREGA_NA_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTREGA_RESERVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAXIMO_ATRASADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_INICIO_NORMAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_FIM_NORMAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_INICIO_SABADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_FIM_SABADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_INICIO_DOMINGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_FIM_DOMINGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSIDERADA_FERIADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ANDAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RECEPCAO_ENXERGA_TUDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBERA_FIM_DE_SEMANA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_CLIENTE_FONE2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_CLIENTE_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_CLIENTE_SEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_VEICULO_FAMILIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_VEICULO_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_VEICULO_COR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_OBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEFAULT_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEFAULT_INTERESSE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USA_FLUXO_DIVERSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AUTO_OCUPAR_VENDEDORES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEST_DRIVE_DEFAULT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO_GERENTE_FI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO_MESA_FI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO_FINANC_EXTERNA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_ESPERA_MIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_FLUXO_LOJA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_FONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORAS_RESERVA_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_FICHA_LEILAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBRIGAR_TEST_DRIVE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBRIGAR_SEGURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPT_VEIC_NOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPT_VEIC_USADOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TROCO_ACESSORIO_VEIC_NOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TROCO_ACESSORIO_VEIC_USADOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TROCO_AVALIACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_PLACA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_GER_NOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_VEND_NOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_SEC_VENDAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_GER_SNOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_VEND_SNOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_SEC_SVENDAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_FINANCIADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_SEGURADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FI_MAX_RANK_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEXTO_MENU_SELLING'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USA_CRM_GOLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REL_PERCENT_01'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REL_PERCENT_02'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REL_PERCENT_03'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REL_PERCENT_04'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_SEGURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_CONSORCIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_ACESSORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_VISITA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FLUXO_CONSIDERA_DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_VENDEDORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEXTO_TERMO_TDRIVE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVENTO_OBRIGAR_DDD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE_LIBERA_VEZ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_RESERVA_TEMP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_RESERVA_TEMP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FAVORECIDO_TROCO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USA_NUM_FILA_MANUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PLANO_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRAZO_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STEP_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PLANO_OURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRAZO_OURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STEP_OURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PLANO_PRATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRAZO_PRATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STEP_PRATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_TAG_CLIENTE_OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMPAR_FILA_DA_VEZ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FI_IMPOSTOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MINIMO_R_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_VISITA_EXTERNA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_MIDIA_SOCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TERMOMETRO_DEFAULT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TDRIVE_OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TDRIVE_MUDA_TERMOMETRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXIGE_TERMOMETRO_ACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONVERTER_MONTADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BALCAO_TIPO_EVENTO_LOJA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_COMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BALCAO_TIPO_EVENTO_FONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_OBRIGAR_FONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_OBRIGAR_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRMPARTS_USA_RESGATE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_RESGATE_TEMPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CP_GERENTE_LOJA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USA_ENTREGA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_TERMOMETRO_DEFAULT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_RESGATE_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_APR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_A'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_B'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_C'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_D'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_INTEGRADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_LINK'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_KEY_SECRET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ULTIMA_CHECAGEM_WHATSAPP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_TIME_ZONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAUCAO_OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAUCAO_PECA_A_LIBERA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAUCAO_VALOR_MINIMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAUCAO_PERCENT_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO_CRMPARTS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CREDLINE_AUTO_ATUALIZAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_USERNAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_SENHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_API_KEY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_TIPO_HOST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_HOST_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_HOST_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_PORTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_PORTA_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_USA_SSL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO_PERDA_CAMPANHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE_EMAIL_ORCAMENTO_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE_EMAIL_ORCAMENTO_ZAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_LINK_ORCAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_PARM_FLUXO'
    Cursor = 'CRM_PARM_FLUXO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;31003'
    DeltaMode = dmChanged
  end
  object tbEmailFluxoAnexo: TFTable
    FieldDefs = <
      item
        Name = 'SEQ_ID_MAIL_FLUXO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MAIL_ANEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ANEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXTENSAO_ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_FLUXO_ANEXO'
    Cursor = 'CRM_EMAIL_FLUXO_ANEXO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;31004'
    DeltaMode = dmChanged
  end
end
