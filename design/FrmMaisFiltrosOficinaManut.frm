object FrmMaisFiltrosOficinaManut: TFForm
  Left = 44
  Top = 162
  ActiveControl = FVBox1
  Caption = 'Mais Filtros Oficina'
  ClientHeight = 533
  ClientWidth = 481
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '242012'
  ShortcutKeys = <>
  InterfaceRN = 'MaisFiltrosOficinaManutRN'
  Access = False
  ChangedProp = 
    'FrmMaisFiltrosOficinaManut.Width;'#13#10'FrmMaisFiltrosOficinaManut.He' +
    'ight;'#13#10#13#10'FrmMaisFiltrosOficinaManut.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 481
    Height = 533
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxBtnTopo: TFHBox
      Left = 0
      Top = 0
      Width = 480
      Height = 60
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 5
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 10
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Voltar Tela'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnPesquisar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Pesquisar'
        Align = alLeft
        Caption = 'Pesquisar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnPesquisarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000002224944415478DAAD954D481561148667840C347F1229DD0822680B4B
          282368D12A90144522027321B8B44D8128B6085CA508091A486D021191D4853F
          F8B30B42022D085D184482D4E6128982284A747B4E9DF0F03933772EDC0F5E5E
          BE33EF39EF9CEF67C6F72286EFFBE7A13250020E40026C2593C95F5ECCE10714
          95D86D30022A42F2C640274689B40CA85D08BD079762BE600FE8C72899D280E2
          B20C5F414EDCF6758C53BF35D280E267A16D70D13C5B07ED608302C7BA74D2E1
          033004B28CB60BCD4094813CEC34F17E693FAC75F4C5FA02A5267C01F98F00AD
          5F00EFC66DD93149984E5E93D71E64700F9E34B13C84FB71169FDC87D00B133A
          E31E61319886EFEA7C0E41539CE29A2CF764C7842AC9FFE21A6CC1E53AEF4030
          928681ECE16F13BA45FE3B57B307E7EBBC0DC1685C032D600F4203F90BAEC10A
          7C53E7CF103C49A3783674644237C85F750D9EC2BD3A97F52C8EBA994E722DB4
          664292FBD33590CFC2A689D5235A8CB9FE9FC0150D7D23AF2C4827C2CFA05263
          72CCCA117F4F61D007759B50333933A7742ABEECFDBB999E3191E3BAE42E17DA
          73D02BD062C272518B8296D67EEC1E43CF9DE7B2272FB5C35C7007348634D542
          FD8950033579040DA65AFF8871CA24E88723CB35E59DEC49D81806F36039CAC4
          0FCAD48DAF02F7411DA80687E00378036629B2ABDAABD0C730934083740726D7
          D4DC8EBFA72A23066AE25E3A195919335093EBD0FF4FC53A1DD464D4404DE4B7
          5B03DECAAFF60FF9CFC91D70A0B6C30000000049454E44AE426082}
        ImageId = 27001
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnLimpar: TFButton
        Left = 120
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Limpar'
        Align = alLeft
        Caption = 'Limpar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnLimparClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000016D4944415478DAEDD6CD2B055118C7F173950D29B150364A62435E36
          0A795F58B3C142FC0752B2F2BA932C6D2C9562C15E79B90965E3A56CA49494A2
          48E9DAA8E1FB34CFD4699A193323BBFBD4A7B973CF39F7377316CFB919F3CF95
          8939AF09D5A8D2FB07DCE3EAAF01FD58464BC8F80566B09F26A00307B8C50A4E
          F1AC631568C7346AD1A7E38902F650A3DBF31132A744B7E90E0351013D984581
          35D686577D83A8AA4339CEACEF1CCCE1C40B90ED5842191A718D7793AC4AADB5
          6FFE00AFBA71A46F944D1810B8F6B700B98E6142C727F18535BD5FC7863C69DA
          8005CC5BF3B2D63CA96F2CEABC7C403E201F1033403AE72586B06BDC7E228BBD
          2678A8D75EBD3A3A2E7D6C103B6836D641E40F901F7A316EC392F65B892EE3B6
          0329698A85FAA452E338C6A371DB7B8371CF0A272C406A049BC6ED2FB23DE7F8
          34C1558456DD26091FC5963D21ECC019C6AABE419C7AC214B6FD0351275A313A
          51AF9F832A871BDDA65CD084B8FF2A52D70F1A0B8E192C1E0DF5000000004945
          4E44AE426082}
        ImageId = 4600235
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnModelo: TFButton
        Left = 180
        Top = 0
        Width = 60
        Height = 56
        Hint = 'F'#225'milia/Modelo Or'#231'amento'
        Align = alLeft
        Caption = 'F'#225'milia'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 3
        Visible = False
        OnClick = btnModeloClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001D44944415478DADDD54D2865611CC7F107110BC9C242F25252165263
          4159B0C066B2C0C64A5E53363363A2CC08A1DC59CC94B7B29197AC6C485E4A21
          0BD9284A4D8D98226161316916445EBEFF9EE7A9DB758E7389C8539FEE3DD73D
          FFDF3DFFFF738E10F5C22BE4DD0784211509E6BDFFBAC611F670F394807AB422
          C9E347ECA31B238F09F885AF58C1388E5DBE275756833CF8F03D98808F98433F
          1A71EB71055263100D28C68253400ACACD7BF9E27F64E14A05B7C2B189680C99
          CF26A57D36A0C2B4428E2F5080F5208BDB958B65449AABAEC484538BE291AEEE
          EF1AAF25BBEA0F4E9C5A64DB3486FC47160E5CABA8C2817F40043694DE15B2E5
          7E3FB17806DA70886C5CDA0099FEACC309328F4CECBA144CC3B6D27D0F5C5273
          DE067C430F7E9A5E46E113165162829C96149E41110670AEF4EC9A94BE277C36
          60D4F46DC91CCBE32151E99D21AD1B46724071E9711D72B066DAF2D7FCAD50E9
          7956BB059C9AA22BE6F8A1005B50EEE838B7805E7C468B5F810F4A3F67FE790C
          3656E91DB8E5F7D90FF4E18B0D9842A97ADE358D321BD08E4E842A7D17CAAB0C
          BB035D1E851E3CD706949AAB9059EC287D27CB4C6407CD780494985FEB78AE0D
          9057D9A2B588C199D2836D56C13D4D5DCF7DF57F996F3FE00EEA666719AE3942
          D90000000049454E44AE426082}
        ImageId = 4600389
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object FHBox1: TFHBox
      Left = 0
      Top = 61
      Width = 480
      Height = 57
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox2: TFHBox
        Left = 0
        Top = 0
        Width = 21
        Height = 45
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object hBoxFiltroServicos: TFHBox
        Left = 21
        Top = 0
        Width = 128
        Height = 42
        Align = alLeft
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 7
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object hbServico: TFHBox
          Left = 0
          Top = 0
          Width = 62
          Height = 30
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Color = clSilver
          Padding.Top = 4
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          ParentBackground = False
          TabOrder = 0
          OnClick = hbServicoClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 0
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox18: TFVBox
            Left = 0
            Top = 0
            Width = 8
            Height = 19
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblServicos: TFLabel
            Left = 8
            Top = 0
            Width = 35
            Height = 13
            Caption = 'Servico'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object FVBox29: TFVBox
            Left = 43
            Top = 0
            Width = 9
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
        object hbKit: TFHBox
          Left = 62
          Top = 0
          Width = 62
          Height = 30
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 4
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          ParentBackground = False
          TabOrder = 1
          OnClick = hbKitClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 0
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox31: TFVBox
            Left = 0
            Top = 0
            Width = 14
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblCliente: TFLabel
            Left = 14
            Top = 0
            Width = 12
            Height = 13
            Caption = 'Kit'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object FVBox32: TFVBox
            Left = 26
            Top = 0
            Width = 14
            Height = 21
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
      end
      object FHBox3: TFHBox
        Left = 149
        Top = 0
        Width = 21
        Height = 45
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object vBoxParametrosServ: TFVBox
      Left = 0
      Top = 119
      Width = 477
      Height = 152
      Align = alClient
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 7
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object cbGrupoFiltro: TFCombo
        Left = 0
        Top = 0
        Width = 433
        Height = 21
        LookupTable = tbServicosGrupo
        LookupKey = 'COD_GRUPO_SERV'
        LookupDesc = 'GRUPO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Grupo'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = True
        OnChange = cbGrupoFiltroChange
        OnClearClick = cbGrupoFiltroClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object cbSbGrupoFiltro: TFCombo
        Left = 0
        Top = 22
        Width = 145
        Height = 21
        LookupTable = tbServicosSubGrupo
        LookupKey = 'COD_SUB_GRUPO_SERV'
        LookupDesc = 'SUBGRUPO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'SubGrupo'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = True
        OnClearClick = cbSbGrupoFiltroClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object cbSetorFiltro: TFCombo
        Left = 0
        Top = 44
        Width = 145
        Height = 21
        LookupTable = tbServicosSetores
        LookupKey = 'COD_SETOR'
        LookupDesc = 'SETOR'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Setor'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = True
        OnClearClick = cbSetorFiltroClearClick
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object hBoxMarca: TFHBox
        Left = 0
        Top = 66
        Width = 453
        Height = 34
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object hBoxchkFiltrarMarca: TFHBox
          Left = 0
          Top = 0
          Width = 162
          Height = 34
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 7
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object chkFiltrarMarca: TFCheckBox
            Left = 0
            Top = 0
            Width = 150
            Height = 17
            Caption = 'Filtrar Servi'#231'os da Marca:'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            TabOrder = 0
            OnCheck = chkFiltrarMarcaCheck
            ReadOnly = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taAlignTop
          end
        end
        object FHBox7: TFHBox
          Left = 162
          Top = 0
          Width = 280
          Height = 34
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 7
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblMarca: TFLabel
            Left = 0
            Top = 0
            Width = 35
            Height = 13
            Caption = 'Marca'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = 13392431
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            FieldName = 'CLIENTE_HINT'
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
        end
      end
      object hBoxModelo: TFHBox
        Left = 0
        Top = 101
        Width = 453
        Height = 34
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object hBoxchkFiltrarModelo: TFHBox
          Left = 0
          Top = 0
          Width = 162
          Height = 34
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 7
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object chkFiltrarModelo: TFCheckBox
            Left = 0
            Top = 0
            Width = 151
            Height = 17
            Caption = 'Filtrar Servi'#231'os do Modelo:'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            TabOrder = 0
            OnCheck = chkFiltrarModeloCheck
            ReadOnly = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taAlignTop
          end
        end
        object FHBox8: TFHBox
          Left = 162
          Top = 0
          Width = 280
          Height = 34
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 7
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblModelo: TFLabel
            Left = 0
            Top = 0
            Width = 41
            Height = 13
            Caption = 'Modelo'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = 13392431
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            FieldName = 'CLIENTE_HINT'
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
        end
      end
      object hBoxchkTerceiro: TFHBox
        Left = 0
        Top = 136
        Width = 157
        Height = 34
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 7
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 5
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object chkTerceiro: TFCheckBox
          Left = 0
          Top = 0
          Width = 151
          Height = 19
          Caption = 'Terceiros'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          OnCheck = chkTerceiroCheck
          ReadOnly = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taAlignTop
        end
      end
      object hBoxchkOriginal: TFHBox
        Left = 0
        Top = 171
        Width = 157
        Height = 34
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 7
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object chkOriginal: TFCheckBox
          Left = 0
          Top = 0
          Width = 151
          Height = 17
          Caption = 'Original'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          OnCheck = chkOriginalCheck
          ReadOnly = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taAlignTop
        end
      end
      object hboxQualquerPos: TFHBox
        Left = 0
        Top = 206
        Width = 157
        Height = 34
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 7
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 7
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object chkFiltraQualquerPos: TFCheckBox
          Left = 0
          Top = 0
          Width = 151
          Height = 17
          Caption = 'Filtrar Qualquer Posi'#231#227'o'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          OnCheck = chkFiltrarQualquerPosCheck
          ReadOnly = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taAlignTop
        end
      end
      object vBoxLRRepairID: TFVBox
        Left = 0
        Top = 241
        Width = 156
        Height = 47
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 7
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 8
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblRepairID: TFLabel
          Left = 0
          Top = 0
          Width = 126
          Height = 13
          Caption = 'LR Repair ID (Land Rover)'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object strRepairID: TFString
          Left = 0
          Top = 14
          Width = 140
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 20
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
      end
    end
    object vBoxParametrosKits: TFVBox
      Left = 0
      Top = 272
      Width = 477
      Height = 150
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox2: TFVBox
        Left = 0
        Top = 0
        Width = 381
        Height = 81
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbbFamilia: TFCombo
          Left = 0
          Top = 0
          Width = 359
          Height = 21
          LookupTable = tbProdutosModelosSimples
          LookupKey = 'COD_PRODUTO'
          LookupDesc = 'DESCRICAO_PRODUTO'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Fam'#237'lia'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Enabled = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object cbbModelo: TFCombo
          Left = 0
          Top = 22
          Width = 359
          Height = 21
          LookupTable = tbProdutosModelosSimples
          LookupKey = 'COD_MODELO'
          LookupDesc = 'MODELO'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Modelo'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = False
          HideClearButtonOnNullValue = False
          Enabled = False
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
        object cbbGrupoVendas: TFCombo
          Left = 0
          Top = 44
          Width = 358
          Height = 21
          Hint = 'Grupo de Vendas'
          LookupTable = tbKitsGrupoVendaCombo
          LookupKey = 'ID_GRUPO_VENDA'
          LookupDesc = 'DESCRICAO'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Grupo de Vendas'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = True
          HideClearButtonOnNullValue = False
          OnClearClick = cbbGrupoVendasClearClick
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
      end
      object FVBox3: TFVBox
        Left = 0
        Top = 82
        Width = 384
        Height = 58
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 4
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 6
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object chkAnoModelo: TFCheckBox
          Left = 0
          Top = 0
          Width = 377
          Height = 17
          Caption = 'Pesquisar por Ano / Modelo'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          ReadOnly = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taAlignTop
        end
        object chkQualquerPosicao: TFCheckBox
          Left = 0
          Top = 18
          Width = 377
          Height = 17
          Caption = 'Pesquisar em Qualquer Posi'#231#227'o'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          ReadOnly = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taAlignTop
        end
      end
    end
  end
  object tbServicosGrupo: TFTable
    FieldDefs = <
      item
        Name = 'COD_GRUPO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SERVICOS_GRUPO'
    Cursor = 'SERVICOS_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServicosSubGrupo: TFTable
    FieldDefs = <
      item
        Name = 'COD_GRUPO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SUB_GRUPO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Grupo Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SUB_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Sub Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SUBGRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Subgrupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SERVICOS_SUB_GRUPO'
    Cursor = 'SERVICOS_SUB_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24203'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServicosSetores: TFTable
    FieldDefs = <
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GRUPO_PC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Plano Contas'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_INTEGRACAO_SETOR_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Integra'#231#227'o Setor Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALTERAR_NA_CODIFICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Alterar Na Codifica'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COR_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cor Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SERVICOS_SETORES'
    Cursor = 'SERVICOS_SETORES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24204'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMarcas: TFTable
    FieldDefs = <
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRM_COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Crm C'#243'd. Questionario'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_ABRAC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca Abrac'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORD_FACIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ford Facil'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_EXTRANET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca Extranet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_HONDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca Honda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PARCELAR_FINANCEIRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Parcelar Financeiro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTRADA_MULTIPLA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Entrada Multipla'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAO_GERAR_AVISO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#227'o Gerar Aviso Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CALC_CAPLAN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Calc Caplan'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GERAR_TIPO_PGTO_VW'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Gerar Tipo Pagamento Vw'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CALC_FLOORPLAN_DATA_PAGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Calc Floorplan Data Pagto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BONUS_COMO_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'B'#244'nus Como Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENCARGOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Encargos'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIG_TOT_NOTA_FAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Orig Tot Nota Fab'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FLOOR_PLAN_POR_DATA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Floor Plan Por Data Entrada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_VEICULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Ve'#237'culo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CONCESSIONARIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Concessionaria'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MARCA_BMW'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca Bmw'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MARCA2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Marca2'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'MARCAS'
    Cursor = 'MARCAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24205'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProdutosModelos: TFTable
    FieldDefs = <
      item
        Name = 'COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_NOTA_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Nota Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_CONTABIL_VEIC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Contabil Veic'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NCM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ncm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ESPECIE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Esp'#233'cie'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIAS_RESERVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Dias Reserva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ANO_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ano Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_TABELA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Tabela'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TRANSITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tr'#226'nsito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CARENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Carencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOD_VER_SERIE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'M'#243'dulo Ver S'#233'rie'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMISSAO_FIXA_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comiss'#227'o Fixa Vendedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMISSAO_FIXA_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comiss'#227'o Fixa Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Minima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_MAXIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Maxima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Minima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_MAXIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Maxima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_MINIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Minima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_MINIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Minima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_MAXIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Maxima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_MAXIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Maxima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_FORPLAN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Forplan'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MULTA_PERCENT_FORPLAN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Multa Percent Forplan'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRETE_CUSTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Frete Custo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRETE_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Frete Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROMOCAO_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Promo'#231#227'o Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'APLICA_MARGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aplica Margem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PATIO_VALOR_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Patio Valor Dia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PATIO_CARENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Patio Carencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_LUCRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Lucro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_LUCRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Lucro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POT_MAX_LIQ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pot Max L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_NOTA_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Nota Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAXA_ALFANDEGARIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Taxa Alfandegaria'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGURO_FRETE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Seguro Frete'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CG_MIN2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cg Min2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CG_MIN_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cg Min Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CV_MIN_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cv Min Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MIN2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Min2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MIN_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Min Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CG_MAX2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cg Max2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CV_MAX2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cv Max2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CV_MAX_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cv Max Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CG_MAX_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cg Max Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CV_MIN2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cv Min2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA_GERENTE_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima Gerente Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RENAVAM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Renavam'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OFICINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Oficina'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_VEICULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Ve'#237'culo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_USADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Usado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_CONSIGNADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Consignado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FAIXA_MINIMA_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Faixa Minima Vendedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FAIXA_MINIMA_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Faixa Minima Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_INTERNO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Interno2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_VEICULO_ANO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Ve'#237'culo Ano'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_PORTAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Portas'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_LUGARES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Lugares'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_TAXI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Taxi'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_DEFICIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Deficiente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRASE_TAXI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Frase Taxi'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRASE_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Frase Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRASE_DEFICIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Frase Deficiente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHASSI_BASICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chassi Basico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENDA_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Venda Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMISSAO_BRUTA_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comiss'#227'o Bruta Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HOLD_BACK_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Hold Back Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_COMISSAO_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Comiss'#227'o Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Vendedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIXO_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Fixo Vendedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIXO_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Fixo Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ADM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Adm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ADM_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Adm Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HB_VD_NO_CUSTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hb Vd No Custo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO_DIVERSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o Diverso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_HORA_GARANTIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Hora Garantia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ford'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERFACE_ATUALIZACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Interface Atualiza'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMISSAO_FIXA_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comiss'#227'o Fixa Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRODUTO_FORCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Produto Forca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Ford'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO_RENAVAM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo Renavam'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TRANSMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Transmiss'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POT_MAX_LIQ_ALCOOL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pot Max L'#237'quido Alcool'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POT_MAX_LIQ_GAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pot Max L'#237'quido Gas'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POT_MAX_LIQ_DIESEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pot Max L'#237'quido Diesel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POPULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Popular'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_VEICULO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Veiculo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMBUSTIVEL_FIPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Combustivel Fipe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_TAXI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Taxi'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_DEFICIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Deficiente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_SIGA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Siga'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_COMI_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Comi Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_VERSAOANO_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Versaoano Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CAMBIO_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cambio Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_NPORTAS_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Nportas Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IPI_IMPORTADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Ipi Importado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORCAR_OPCIONAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forcar Opcional'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_MOLICAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Molicar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO_EXTRANET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo Extranet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACOES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observac'#245'es'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_ICMS_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Icms Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_H3S_HONDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca H3s Honda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO_H3S_HONDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo H3s Honda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VERSAO_H3S_HONDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vers'#227'o H3s Honda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CILINDRADAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Cilindradas'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CMT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cmt'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_USD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Usd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Imc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRETE_INTERNACIONAL_BRL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Frete Internacional Brl'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRETE_INTERNACIONAL_USD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Frete Internacional Usd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_KIT_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Kit Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GTIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Gtin'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_IPI_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Ipi Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_COMBUSTIVEL_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Combustivel Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_COR_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cor Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_ATACADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Atacado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INCIDE_PIS_COFINS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Incide Pis Cofins'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_GARANTIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Garantida'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_GARANTIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Garantida'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_GARANTIDA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Garantida Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_GARANTIDA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Garantida Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAPLAN_GM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Caplan Gm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MOTOR_BMW'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Motor Bmw'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_BRUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso Bruto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_MEDIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso M'#233'dio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DISTANCIA_EIXO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Distancia Eixo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_MODELO_FORMAT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Modelo Format'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORIGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Origem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_IMAGEM_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Imagem Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MESES_GARANTIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Meses Garantia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_INTERNO3'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Interno3'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cest'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_COMBUSTIVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Combustivel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INCLUSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Inclus'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SAF_ALT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Saf Alt'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SAF_ENVIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Saf Envio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO_ALTERACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio Altera'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_FIPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Fipe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO_FABRICA_INTERFACE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo Fabrica Interface'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INCIDE_PIS_FRETE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Incide Pis Frete'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO_COMBO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo Combo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO_INITCAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo Initcap'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO_INITCAP2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo Initcap2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO_ANO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo Ano'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LABEL_LINHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Label Linha'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LABEL_MVS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Label Mvs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LABEL_COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Label C'#243'd. Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LABEL_COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Label C'#243'd. Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'PRODUTOS_MODELOS'
    Cursor = 'PRODUTOS_MODELOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24206'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbKitsGrupoVendaCombo: TFTable
    FieldDefs = <
      item
        Name = 'ID_GRUPO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'KITS_GRUPO_VENDA_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24207'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProdutosModelosSimples: TFTable
    FieldDefs = <
      item
        Name = 'VEIC_FAMILIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Veic Familia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PRODUTOS_MODELOS_SIMPLES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24208'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
