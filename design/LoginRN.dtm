object LoginRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '29001'
  Height = 299
  Width = 442
  object tbSchema: TFTable
    FieldDefs = <
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'Schema Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRIPTION'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Description'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SYS.NBS_SCHEMA'
    Cursor = 'NBS_SCHEMA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70001'
    DeltaMode = dmChanged
  end
  object tbSchemaUser: TFTable
    FieldDefs = <
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'Schema Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SCHEMA_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'Schema User'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Principal'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBS_SCHEMA_USER'
    Cursor = 'NBS_SCHEMA_USER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70002'
    DeltaMode = dmChanged
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbUsuarioLogado
        GUID = '{60DB9949-72CE-43DD-B5B8-81BE5615E29E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbUsuarioLogado: TFTable
    FieldDefs = <
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'Usu'#225'rio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Url'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'USUARIO_LOGADO'
    TableName = 'USUARIO_LOGADO'
    Cursor = 'USUARIO_LOGADO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70003'
    DeltaMode = dmAll
  end
  object tbTables: TFTable
    FieldDefs = <
      item
        Name = 'TABLE_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Table Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OWNER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Owner'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ALL_TABLES'
    Cursor = 'ALL_TABLES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70004'
    DeltaMode = dmChanged
  end
  object tbServidor: TFTable
    FieldDefs = <
      item
        Name = 'SERVIDOR_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'Servidor Id.'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Seguro'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBS_SERVIDOR'
    Cursor = 'NBS_SERVIDOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70005'
    DeltaMode = dmChanged
  end
  object tbSysServidor: TFTable
    FieldDefs = <
      item
        Name = 'SERVIDOR_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'Servidor Id.'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Seguro'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SYS.NBS_SERVIDOR'
    Cursor = 'SYS_NBS_SERVIDOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70006'
    DeltaMode = dmChanged
  end
  object tbServidorUser: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PW_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Pw User'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AD_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Ad User'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBS_SERVIDOR_USER'
    Cursor = 'NBS_SERVIDOR_USER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70007'
    DeltaMode = dmChanged
  end
  object tbSysServidorUser: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PW_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Pw User'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AD_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Ad User'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SYS.NBS_SERVIDOR_USER'
    Cursor = 'SYS_NBS_SERVIDOR_USER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70008'
    DeltaMode = dmChanged
  end
end
