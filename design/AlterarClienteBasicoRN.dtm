object AlterarClienteBasicoRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000150'
  Height = 299
  Width = 442
  object sc: TFSchema
    Tables = <
      item
        Table = tbClientes
        GUID = '{808E6C3A-C334-49E7-8BD4-249834E42D11}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbClientes: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO_ELETRONICO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Endere'#231'o Eletronico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Prefixo Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Telefone Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Prefixo com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Telefone com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_CEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Prefixo Cel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_CEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Telefone Cel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_NFE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Email Nfe'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTES'
    TableName = 'CLIENTES'
    Cursor = 'CLIENTES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000150;70001'
    DeltaMode = dmAll
  end
end
