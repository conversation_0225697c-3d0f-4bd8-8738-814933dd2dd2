object PesquisarKitMaisFiltroRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600727'
  Height = 299
  Width = 442
  object tbProdutosModelosSimples: TFTable
    FieldDefs = <
      item
        Name = 'VEIC_FAMILIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Veic Familia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PRODUTOS_MODELOS_SIMPLES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600727;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
