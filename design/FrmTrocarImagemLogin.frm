object FrmTrocarImagemLogin: TFForm
  Left = 43
  Top = 162
  ActiveControl = FHBox1
  Caption = 'Trocar Imagem Tela Inicial'
  ClientHeight = 491
  ClientWidth = 811
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000182'
  ShortcutKeys = <>
  InterfaceRN = 'TrocarImagemLoginRN'
  Access = False
  ChangedProp = 
    'FrmTrocarImagemLogin.Height;'#13#10#13#10'FrmTrocarImagemLogin.ActiveContr' +
    'ol'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FHBox1: TFHBox
    Left = 0
    Top = 0
    Width = 811
    Height = 65
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 3
    Padding.Left = 5
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object btnUploadImagem: TFButton
      Left = 0
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Carregar Imagem'
      Align = alLeft
      Caption = 'Upload'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 0
      OnClick = btnUploadImagemClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000001634944415478DABDD5CD2B055118C7F173B31059785978E946DD64A3
        8849B664753756140B0B65E3A52456C442491245AE85973F40142B2F59CB6581
        94F296241B1B6565A3F89EE6DC1AB71977AEFBCC7DEAD33453737EE79C39734E
        48055CA16C05E8EB10EA84DABDC22ABE1301C5B8C18850C0126AF0E10CB84495
        50C02B6AA5021A711144402176F1806674E25E32E00903384404A7CACCBB4440
        3B5AD5EF4531885C2C4A0454E2C03492A86D6C9A11894CD1042C4CA30F25E84E
        F71BE46318B31E211D88E20C6B8EE7BE024A718D183E319FC6E85206E8D5B08F
        6A73BF80BBA45EFE3BC0320D59492F6DE0185B8E6739A8C733DEFD04F4620A2D
        1E3DDBC13A8ECCFD98E9C8177A5205DC22AEEC35FE57E9514CA209E518C70CDE
        B0EC1550864714F89CE33D65FFB9732EC171B780304E54E6BBA9DE268AF0A204
        7753673528FB2C88B805E8036734C3005D6DE8428533405FFB95BDE4242A0F2B
        38CFDAA11F58FD006094671970A9BBE70000000049454E44AE426082}
      ImageId = 7000194
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
    object btnLimparImagem: TFButton
      Left = 60
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Limpar Imagem'
      Align = alLeft
      Caption = 'Limpar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 1
      Visible = False
      OnClick = btnLimparImagemClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F80000016D4944415478DAEDD6CD2B055118C7F173950D29B150364A62435E36
        0A795F58B3C142FC0752B2F2BA932C6D2C9562C15E79B90965E3A56CA49494A2
        48E9DAA8E1FB34CFD4699A193323BBFBD4A7B973CF39F7377316CFB919F3CF95
        8939AF09D5A8D2FB07DCE3EAAF01FD58464BC8F80566B09F26A00307B8C50A4E
        F1AC631568C7346AD1A7E38902F650A3DBF31132A744B7E90E0351013D984581
        35D686577D83A8AA4339CEACEF1CCCE1C40B90ED5842191A718D7793AC4AADB5
        6FFE00AFBA71A46F944D1810B8F6B700B98E6142C727F18535BD5FC7863C69DA
        8005CC5BF3B2D63CA96F2CEABC7C403E201F1033403AE72586B06BDC7E228BBD
        2678A8D75EBD3A3A2E7D6C103B6836D641E40F901F7A316EC392F65B892EE3B6
        0329698A85FAA452E338C6A371DB7B8371CF0A272C406A049BC6ED2FB23DE7F8
        34C1558456DD26091FC5963D21ECC019C6AABE419C7AC214B6FD0351275A313A
        51AF9F832A871BDDA65CD084B8FF2A52D70F1A0B8E192C1E0DF5000000004945
        4E44AE426082}
      ImageId = 4600235
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
  end
  object vBoxImage: TFVBox
    Left = 0
    Top = 65
    Width = 811
    Height = 426
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
  end
end
