object FrmTimeTemplateHorarios: TFForm
  Left = 44
  Top = 163
  ActiveControl = vboxPrincipal
  Caption = 'Time templates horarios'
  ClientHeight = 536
  ClientWidth = 764
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '24509'
  ShortcutKeys = <>
  InterfaceRN = 'TimeTemplateHorariosRN'
  Access = False
  ChangedProp = 
    'FrmTimeTemplateHorarios.Width;'#13#10'FrmTimeTemplateHorarios.Height;'#13 +
    #10#13#10'FrmTimeTemplateHorarios.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 764
    Height = 536
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxBotoes: TFHBox
      Left = 0
      Top = 0
      Width = 750
      Height = 65
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 5
      Padding.Left = 5
      Padding.Right = 5
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Voltar Tela'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnNovo: TFButton
        Left = 60
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Novo'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnNovoClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003EC4944415478DAB5965B681C5518C7FF33BB9BDD6433934DD6D840B5
          5523B489ED830F8A7879B708E2B320EA8B977A01056DB5F6C10749B14F229446
          2822A5488B0FA542DFBCE083340A5E9A6EBA694D8D4D1392B6BB3BDDD9CD6E32
          33E7F83FB333B31B6603C5E20CDF7E67CE39F3FDCE77396756C3FF7C69B73BB1
          6EEF36A97280BC9B3A4F9DA54E511C9AA953DFA0FE2B6B4C57BB020E7DFAC92B
          5493DD8CF7F6369149AFC134EB30FAEBFE732AE550D2709C35DCB76DF1483E6F
          9FA7B9054E9FCE1A1716BA01E4337B9EC5E8E8280DF446C6A5AC51AE438839B8
          CE15B617D979038DF9A3FEB8187E0ECB2B750CE6964F99A6F74722A1CDD2EC8F
          84946380B7DF7C079EE775ACDDA5417A2EE7E02CEC41CFBDEFB2EF2AD617BEC1
          E078039A9641B9A0411B7914954A1AD9AC75DA30C414CDFE4EF99EE1723600F6
          BDF7216AB55A6BE5EA960CA7BCC65514097829C29A3B7E4132F948E0A185CACC
          603496D93E3E1900BEA5174B5D01522AF3EAED126516EEB5A7D1FFE07748A69E
          84AEF76C5A08CE7A11F6E531053908E86709F82D06B06DBB05A0402E736486A1
          281072906171D84E6E0A50E1CA6C1FFB89AB9F6AE5A1703606A8DEBA158447C9
          0A746D3A007C8CBE07F6219D99E073BCBA5DB780EAEC2E053847E34A7E20E04C
          0C6059960F8050BF6502CEFB005D2F300F2791DB79137A22DF75F5A97BC69148
          C8D083D304FC1C0754CA103E007E9235FD2221339422DCC5630CD33A81A9AE00
          37FF30FAFB9B6768FC57E6E06BE6602E06A8544AAC79D9AA23C998E30A0D1691
          D02FC15B3A8CA187547F8395D3E7BF97DB799D1E0DFB002BFD1446464A932D80
          768280660C502ADD8C92EC271A3586E70212DA1CBDB90AB1F4993F3FBD6D37C1
          0E9AFF1483F27C031767E731B663FE031A3F49E37FC736DADED7DE42B3B11A25
          19116485B1BD4410F784B6CC7095A8D5F1E3706612526491EAD98A7353F378FC
          B13F9F50B1EF7A54284063B5DE5145A20D4215498649D35768DCE28B04681E87
          74027AD197BD1FC74F14B0F7F5890D251603ACD6ED68A3B500AA2102A0CB870A
          3DA16865CEF1203C039E303194DF852347BFC0FEF70F7405E804780A50AB5769
          AF1D9ED003E1971522CF5A7DD25F809ABF65642B019F6F0AE82160CD07D85647
          92C33349B49F6508EAEC9330CC1CBE3AFEA50224E017F946804140F5C5175EE6
          69EAB457AF6E213B8022285F74E4A8D53607864280FA10354348081822A0A400
          AEB30EDFF95612028888562FD1F6A43D476060E0AE1030CC5E8BA2CE7D190206
          08B014E04EAE00B0456D6C753C757A9039F0D1FE6386693C7F2700AB629D3A34
          71F85536EDC08308A0B43AE80D8AFAB8AB73204D51094B053A19CCD3FD44B445
          C55B7DA5AA1DE20563B7FFAFE2BF5EFF02A8A671371FD8FBD30000000049454E
          44AE426082}
        ImageId = 6
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnExcluir: TFButton
        Left = 125
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Excluir'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        Visible = False
        OnClick = btnExcluirClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004F24944415478DAB5965B6C145518C7FF736666AFA5DBEEB6DB6D4BE9
          426B5B682D8AA912B50662C40B1A5F34A6B531A009D1102331519EF0457D3131
          DE1E080F4653914B626262BC61141510039142A076DDDADBA6F4B2BB855E662F
          DDDD99397E33B36C172C8D26BAD97FCE9CD933FFDFF9CEF9E63B2BE07FFE082B
          FD7810D84C031E1519B6E81CED9CC349B775EA87A97F82749CFA9FF700FC5F01
          C8D82D0A784F94C4AEFAA65A7B79A05CF4F8BD707A3DC825D358885DC56C7496
          8F0F4F26530BA9014DC7530419FB470032BF8F09385ABDDAE769BD77A353D616
          C1D20B404A81905600878BF0A5E0AE55E0AB7C183D17524317463314CD2B04D9
          BF2280CC9BC8BC6FD35D2DEECA60007C7C082C350B511220524802B39EE03AAD
          93C6A1494E08C11624942CCEFC703E914E657713A47759009933F238DFB4614D
          6BDDBA2A918F8520316E99CB4201623C619AABA49CD5EA81B54868369CFEBE4F
          A1485A0832F937C0A7C0DED252E7BE8E2DB7BBF5A17E48820A49B6CC0BADC4CC
          27AE99AB399D645DE3968D18EE8FE44687267FE9E2D87A1D80666FA78B85CD9D
          ED36477A16C27C1C928DC1F3C111B0721FB2FB9E81A8C49700AB2A21BD7E085A
          7C0A8997BBAC4868B950DF8293C7CE26338BB947288A13C5800EBB4D3A7EF7D6
          3B4AB4F085C28CBD477E86746B07F4B141645EDA0E91C0F0FA21BDF325587D13
          D40BA7A1ECDCB61405DDFB636022333131F31A01DE2A06BCE0AFF0BCBD7EFD1A
          A71E195C5A92AA00CA3EFA06E2DA6613B2489138DEE835CDB5B13012BB1E861A
          8D5A119078452DA28A8AF040E46B5AA6ED05C021FA068381AE9A7207109F5CDA
          D43CC4F3A105E1D90C049BBD60AEC562D67EE4F784BB3D48BABCE83B1B8A7573
          5415008769BD1A1B6A3B7D76EACCC52DF3BC9804C8CD6D283B72D2343720F3DD
          9DD0067F876EA4EB3580914D3617548AE2CCAFFDD9A7695F8B97E8CD1ABF776F
          7DC023F2E8E52273013245E0FDE45B48EB96225047C298DBF190B93C66CA920C
          102FF5629E7C43A1C8255AA2F662C0E3252E476FDBFA60A91E19B2CC29E7A540
          005547BF83DCD00C75984CF7F4A0ECDD8390A89FA3FE4CF78304992E40987F35
          C6E30A1F9F9CD94F11EC2E06D40A8230D2715BB34D1F1F03836A026ABFF8118E
          3BEF416E288C78D736E857A2601555A83C6C4133E7CF62EAB14E13A06BF4A6D6
          3722148E24E694D4F33DE6AB55F4A2D1465FACF3FB5AFDDE12A64F5F360135BD
          9F5114D5883DFB04F4996861B468400E1C86766506D33B9EA4BDE0103C3EA425
          072E118012B69100D11B4B453375FADA1AEA5CB251D4920A18B3EA8FD11A2385
          6BA3C9819BE2E64653BA81D5D4E1E2C0486231A7BE48E61FDFACD8BDEAB2C9FB
          9A83B52548500555E64D53032208D7175EC3DC8018D555ACF0E3F2442C1B9D53
          4E7503F7AF544D194DE827A7246D6AA8AE743B6C22ADFB0C90CB5A11E4877323
          042682797DD0653B4627628BB3A9B4F215F0C051A09F8668CB0164524004AAF7
          003B37926A3CA5B2DF5BC644A306118467B310641982DD6E96ECB9850446E257
          72C39CFFF63E70E02A10218F69D20449B9114027096A48D5865A800DBB80E72A
          29C3A84E6B6E9B8D796499A5340D73193A5F545548D23144A972EC14708E9E99
          CA9B1BED9FA4E472279A2D0F3040E40D1FD5C84A8AA6A91108D6D17D4A8DF961
          32B944B3A51953F5C32C892ECDEB11586741F6A647E60DB012929B641CF654A8
          CCD7DFD8DA1C6931AF85FC6CB3CB99ACF8AFE2BFF8FC05DEE5323773766BE800
          00000049454E44AE426082}
        ImageId = 8
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAlterar: TFButton
        Left = 190
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Alterar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 3
        OnClick = btnAlterarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003094944415478DAB5955B4853711CC7BF53A7E1D26990E14374F172BA
          5850910F163DFA641245049104654A48D04BB59C5A14A5250425157651307B31
          ED02068BE841891E4C66D843584FBA34DD76B6E32EB1D13CFFFEFF73F3B88B4E
          C13FFBEFFF3FE7FCF7FDFCAE6706ACF230E82F5AEEDEAAA14BFB4A8422E1C89E
          86C66BDF9602909AEAF3C8CDCD5D96B8DD6EC7C74F367C1EFC52D2DFFFFE07BD
          2526045CB9548F4020B0408010A24D7A052212F68D81810194951D405E5E9E74
          EE4EEB6DD88747B89E9EDE9F4901241955987D88C8BE24F38828C266B3A1A2E2
          30CC663382C1204C269304B15CB61A96043C7CFC60D1B0549F3987376FDF81F7
          38639E2D09F0FBFD3ACB45885268945051CB89EC0EC6271CE05D2E0C0D7F9584
          AC0D4DC979E09B9D8D0A8F2429C79E88DA338FC78BC8BF3084591FDD7B505979
          0423559A64D5DE6E74C705088280F6A78F965549A5FBF623A7B31C9C05C8F875
          08A37D83A000437C80D70366274428162BABEC060D1914AF4418D38CF81B0C80
          6F2CD4C4C3438318732031C0EBE5218A4A49AA252A12A582E4FBFAE74ECBA618
          713A9A28E0665C00CFBBF1ACE34952A1291FB526144F9864B7DB199BE0051524
          87CC7975738CF8F78DC771BAF99541D12671012EE78C5629D0BA58D481085CF5
          B1E2DC8D61B4F57E50CB3431C039338DCEAE8E6585656D6D1F8A0E1E55FB2045
          4A50A210CD4C4FC99646558FEC09E0B66EC1EE63F3E239D7C7248FB716708B36
          5A0A05CC31C09FE949AD6AF4B9A0050ADE5AC0CA0F75274A71D638442DEF454A
          7E8974BEB0689B0A4885F246D503D229202C01A61CE87AF9226E78E88F613FC5
          42F21AA9F93B34AFD85A54BC7D51401605F81860727262DE7AF5F5AC4BB65CFF
          984FBEB22FE676AA00133D1462103D601D05F00CF0DB312E7529D13A56295102
          DDBB489F1FB9AB396E970A584FEF0A7446F40033050817EA2E22333333A9268B
          1EA15008F7DBEE31C0067AE98906ACB136589E6765679D5C91BA3204AFD0D3D2
          DC5A4BB77E3AE7F400B64F67B9A0339B4EE646069D2C6146654D53CEA975AE4E
          166FF637E8D3CD394437DA6A8CFFFE8D6837CCAC0E4F0000000049454E44AE42
          6082}
        ImageId = 7
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnSalvar: TFButton
        Left = 255
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 4
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3
          0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220
          AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950
          4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5
          5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37
          35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067
          603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7
          29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B
          DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA
          B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B
          A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194
          B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002
          EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715
          87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B
          AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9
          32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66
          5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E
          4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05
          05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF
          0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76
          093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0
          C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65
          C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70
          9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D
          80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12
          B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8
          6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087
          94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF
          0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34
          2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2
          134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000
          0049454E44AE426082}
        ImageId = 4
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnCancelar: TFButton
        Left = 320
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Cancelar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 5
        OnClick = btnCancelarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000036F4944415478DAB5937B48537114C7CFDDBD9B5B375FA98569605A14
          8A2414988A927F88996952948948149A6020998614BD0B7A5A448564A5882912
          1469A9B3971AE5BB742DCC245F99736ACE747373BBAF7E8B4BDD86530BF783C3
          FDFDF89EDFF7737EE77031B0F1C2E69B189B27D954BEDF546B4BC01486611965
          29C63C9B00B6DD9150EECEDEF4F044DF0D8661B3D16BB805079CDBFD9028797B
          D9D0A96AA962682601414C0B0AB898F894A03803C8DB8AA61BBA2A9434CD4422
          C8F87F0150CF49D4F354424C6C060E5611205D7932BE18DA0DA5E0278D81775F
          6AA827ADF92A86A1CDC3EFFB2740DC5D699608C34F6C5C1B49047885C99C644B
          81B47384214601FDA6264030F0936D8501752F5B54776992A64D1108D23A2700
          558D897051AECF32FFA49D41E98B285C0B8394020CAC06284C0FE84580C0FC45
          1C7CA55B403761805B55D94686A5A310A4665600EAF3D9D51E0199311B92654D
          E345306CFCFC57B2D9DC1CB8080767F10A085EB217DE763E31D677567D66192E
          1C01345601A87A4F0921ED4A8B3A2F6B9C28041DFD1D569281E0245E0E8E2848
          DC05BA74B5F071B212DC243EBFCC9F298AA795FD0DCD1C0BE6EAF5B3B6281655
          BFC93FEE8887C772BC77AA09825CF6C017753B3730D2AD1FD27CE574FA49F240
          F445AC515308EB9DE2A1AC39CFD0ADFE508DCC7721736ACE21C7E5DBBD4A0C3D
          1CAEC6DAC0DF211AE41F0B8C9F7A143DC8E014923FA0501EDD51404C535A7850
          7F5D3FA8E92E465AEA5C3FDC1FC03DBBEEB4C8CBDE2AFA3DC8287728A9CBE945
          0601C860926F2195BEF52A51F2FA8A614CABCA294B311D87792C21A02C213433
          D6D1C11106C6BAA0A2E97EEDE37DC670C18C287B9933A39D1ECF2A4F31DD9C8F
          B9E5900F86F86EB910B236D66E4CA782FC17674738965B835EF083D7B5E89382
          CEA5F335B704D8E338DE931C71DAD595F484965E39FDBCBD5439D8C286B5DEA6
          8D9BAF89C3E419D41BC15D4E102C1FD6013C24DACDC1F3414268C6227BA90BBC
          EA28353574C8ABCB534D49481659E4B3BC39C3EF193E68AB00F339FA96248924
          C9DCED41A9322F177FEC666526A5908F062A4B1835D271FE0E3B83A9F04B5903
          98AB24C2CF88D739B8638F5C17AF583AAAFD36F8F298316C6AE4978EF339ACC0
          90B6D8D3B3017E43CC117C88F0EE79C98CAA151C25A85ED822D60246F17B6E36
          8050C305558BAC001881293793894D97CD013F01E2D89E284DE0BF3E00000000
          49454E44AE426082}
        ImageId = 9
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object pgControlTemplates: TFPageControl
      Left = 0
      Top = 66
      Width = 748
      Height = 461
      ActivePage = tabCadastro
      TabOrder = 1
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabListagem: TFTabsheet
        Caption = 'Listagem'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        object vboxPrincipal2: TFVBox
          Left = 0
          Top = 0
          Width = 740
          Height = 433
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object gridTemplates: TFGrid
            Left = 0
            Top = 0
            Width = 452
            Height = 135
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbTimeTemplate
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = False
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            ActionColumn.Title = 'A'#231#245'es'
            ActionColumn.Width = 100
            ActionColumn.TextAlign = taCenter
            ActionColumn.Visible = True
            Columns = <
              item
                Expanded = False
                FieldName = 'ID_TEMPLATE'
                Font = <>
                Title.Caption = 'Id'
                Width = 40
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 10
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{D65919BE-B868-46B6-8184-C77B8B3C12C6}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'DESCRICAO_INITCAP'
                Font = <>
                Title.Caption = 'Descri'#231#227'o'
                Width = 270
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 90
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{C6588474-09B8-47E1-A109-C31E0CAFFEE0}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end>
          end
          object gridTemplatesHorariosAmostra: TFGrid
            Left = 0
            Top = 136
            Width = 454
            Height = 151
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbTimeTemplateHorario
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = False
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            ActionColumn.Title = 'A'#231#245'es'
            ActionColumn.Width = 100
            ActionColumn.TextAlign = taCenter
            ActionColumn.Visible = True
            Columns = <
              item
                Expanded = False
                FieldName = 'NOME_DIA_SEMANA'
                Font = <>
                Title.Caption = 'Dia Semana'
                Width = 165
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 50
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{145E3AC0-9748-447E-A2C9-6D5DB9D479C0}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'INICIO'
                Font = <>
                Title.Caption = 'In'#237'cio'
                Width = 66
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftDateTime
                FlexRatio = 25
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{DDCDC408-D501-49D2-91A6-C148DE2FAF53}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = 'HH:mm'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDateTime
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{6650BE38-AA4B-459C-9E80-BBD0643EDE65}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'FIM'
                Font = <>
                Title.Caption = 'Fim'
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftDateTime
                FlexRatio = 25
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{B454ED32-23BC-4D8B-AE46-6D4DA76C176F}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = 'HH:mm'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDateTime
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{2B622419-EE21-455F-8483-99779F390152}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end>
          end
        end
      end
      object tabCadastro: TFTabsheet
        Caption = 'Cadastro'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object boxCadastroPrincipal: TFVBox
          Left = 0
          Top = 0
          Width = 740
          Height = 433
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object groupboxDeltalheTemplate: TFGroupbox
            Left = 0
            Top = 0
            Width = 726
            Height = 427
            Caption = 'Detalhes Template'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = False
            Closable = False
            Closed = False
            Orient = coHorizontal
            Style = grp3D
            HeaderImageId = 0
            object vboxDetalheTemplate: TFVBox
              Left = 2
              Top = 15
              Width = 722
              Height = 410
              Align = alClient
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 5
              Padding.Left = 5
              Padding.Right = 5
              Padding.Bottom = 5
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 8
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object vboxDescricao: TFVBox
                Left = 0
                Top = 0
                Width = 654
                Height = 61
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object lblDescricao: TFLabel
                  Left = 0
                  Top = 0
                  Width = 46
                  Height = 13
                  Caption = 'Descri'#231#227'o'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object edDescricao: TFString
                  Left = 0
                  Top = 14
                  Width = 613
                  Height = 24
                  Table = tbTimeTemplate
                  FieldName = 'DESCRICAO'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = True
                  Prompt = 'Descri'#231#227'o'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = True
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccNormal
                  Pwd = False
                  Maxlength = 100
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
              end
              object gridTemplatesHorarios: TFGrid
                Left = 0
                Top = 62
                Width = 617
                Height = 208
                TabOrder = 1
                TitleFont.Charset = DEFAULT_CHARSET
                TitleFont.Color = clWindowText
                TitleFont.Height = -11
                TitleFont.Name = 'Tahoma'
                TitleFont.Style = []
                Table = tbTimeTemplateHorario
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Paging.Enabled = False
                Paging.PageSize = 0
                Paging.DbPaging = False
                FrozenColumns = 0
                ShowFooter = False
                ShowHeader = True
                MultiSelection = False
                Grouping.Enabled = False
                Grouping.Expanded = False
                Grouping.ShowFooter = False
                Crosstab.Enabled = False
                Crosstab.GroupType = cgtConcat
                EnablePopup = False
                WOwner = FrInterno
                WOrigem = EhNone
                EditionEnabled = False
                AuxColumnHeaders = <>
                NoBorder = False
                ActionButtons.BtnAccept = False
                ActionButtons.BtnView = False
                ActionButtons.BtnEdit = False
                ActionButtons.BtnDelete = False
                ActionButtons.BtnInLineEdit = False
                CustomActionButtons = <>
                ActionColumn.Title = 'A'#231#245'es'
                ActionColumn.Width = 100
                ActionColumn.TextAlign = taCenter
                ActionColumn.Visible = True
                Columns = <
                  item
                    Expanded = False
                    FieldName = 'NOME_DIA_SEMANA'
                    Font = <>
                    Title.Caption = 'Dia Semana'
                    Width = 165
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 50
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = True
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{145E3AC0-9748-447E-A2C9-6D5DB9D479C0}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'INICIO'
                    Font = <>
                    Title.Caption = 'In'#237'cio'
                    Width = 66
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftDateTime
                    FlexRatio = 25
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = True
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{DDCDC408-D501-49D2-91A6-C148DE2FAF53}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = 'HH:mm'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDateTime
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{6650BE38-AA4B-459C-9E80-BBD0643EDE65}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end
                  item
                    Expanded = False
                    FieldName = 'FIM'
                    Font = <>
                    Title.Caption = 'Fim'
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 25
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = True
                    Colors = <>
                    Images = <>
                    Masks = <
                      item
                        Expression = '*'
                        EvalType = etExpression
                        GUID = '{69750167-270B-470A-B2C7-DF916ECCF3C3}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Mask = 'HH:mm'
                        PadLength = 0
                        PadDirection = pdNone
                        MaskType = mtDateTime
                      end>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    Editor.Filter = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{2B622419-EE21-455F-8483-99779F390152}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                    Priority = 0
                  end>
              end
              object vboxEditarHoarario: TFVBox
                Left = 0
                Top = 271
                Width = 686
                Height = 129
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Visible = False
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object blPeriodoAtendimento: TFLabel
                  Left = 0
                  Top = 0
                  Width = 178
                  Height = 21
                  Caption = 'Per'#237'odo de Atendimento'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clHighlight
                  Font.Height = -17
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object FHBox11: TFHBox
                  Left = 0
                  Top = 22
                  Width = 438
                  Height = 41
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 5
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object btnAlterarHorario: TFButton
                    Left = 0
                    Top = 0
                    Width = 48
                    Height = 35
                    Hint = 'Alterar periodo atendimento'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 0
                    OnClick = btnAlterarHorarioClick
                    PngImage.Data = {
                      89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                      F8000003094944415478DAB5955B4853711CC7BF53A7E1D26990E14374F172BA
                      5850910F163DFA641245049104654A48D04BB59C5A14A5250425157651307B31
                      ED02068BE841891E4C66D843584FBA34DD76B6E32EB1D13CFFFEFF73F3B88B4E
                      C13FFBEFFF3FE7FCF7FDFCAE6706ACF230E82F5AEEDEAAA14BFB4A8422E1C89E
                      86C66BDF9602909AEAF3C8CDCD5D96B8DD6EC7C74F367C1EFC52D2DFFFFE07BD
                      2526045CB9548F4020B0408010A24D7A052212F68D81810194951D405E5E9E74
                      EE4EEB6DD88747B89E9EDE9F4901241955987D88C8BE24F38828C266B3A1A2E2
                      30CC663382C1204C269304B15CB61A96043C7CFC60D1B0549F3987376FDF81F7
                      38639E2D09F0FBFD3ACB45885268945051CB89EC0EC6271CE05D2E0C0D7F9584
                      AC0D4DC979E09B9D8D0A8F2429C79E88DA338FC78BC8BF3084591FDD7B505979
                      0423559A64D5DE6E74C705088280F6A78F965549A5FBF623A7B31C9C05C8F875
                      08A37D83A000437C80D70366274428162BABEC060D1914AF4418D38CF81B0C80
                      6F2CD4C4C3438318732031C0EBE5218A4A49AA252A12A582E4FBFAE74ECBA618
                      713A9A28E0665C00CFBBF1ACE34952A1291FB526144F9864B7DB199BE0051524
                      87CC7975738CF8F78DC771BAF99541D12671012EE78C5629D0BA58D481085CF5
                      B1E2DC8D61B4F57E50CB3431C039338DCEAE8E6585656D6D1F8A0E1E55FB2045
                      4A50A210CD4C4FC99646558FEC09E0B66EC1EE63F3E239D7C7248FB716708B36
                      5A0A05CC31C09FE949AD6AF4B9A0050ADE5AC0CA0F75274A71D638442DEF454A
                      7E8974BEB0689B0A4885F246D503D229202C01A61CE87AF9226E78E88F613FC5
                      42F21AA9F93B34AFD85A54BC7D51401605F81860727262DE7AF5F5AC4BB65CFF
                      984FBEB22FE676AA00133D1462103D601D05F00CF0DB312E7529D13A56295102
                      DDBB489F1FB9AB396E970A584FEF0A7446F40033050817EA2E22333333A9268B
                      1EA15008F7DBEE31C0067AE98906ACB136589E6765679D5C91BA3204AFD0D3D2
                      DC5A4BB77E3AE7F400B64F67B9A0339B4EE646069D2C6146654D53CEA975AE4E
                      166FF637E8D3CD394437DA6A8CFFFE8D6837CCAC0E4F0000000049454E44AE42
                      6082}
                    ImageId = 7
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Color = clBtnFace
                    Access = False
                    IconReverseDirection = False
                  end
                  object btnSalvarHorario: TFButton
                    Left = 48
                    Top = 0
                    Width = 48
                    Height = 35
                    Hint = 'Salvar periodo atendimento'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 1
                    OnClick = btnSalvarHorarioClick
                    PngImage.Data = {
                      89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                      F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3
                      0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220
                      AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950
                      4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5
                      5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37
                      35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067
                      603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7
                      29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B
                      DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA
                      B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B
                      A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194
                      B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002
                      EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715
                      87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B
                      AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9
                      32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66
                      5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E
                      4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05
                      05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF
                      0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76
                      093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0
                      C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65
                      C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70
                      9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D
                      80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12
                      B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8
                      6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087
                      94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF
                      0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34
                      2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2
                      134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000
                      0049454E44AE426082}
                    ImageId = 4
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Color = clBtnFace
                    Access = False
                    IconReverseDirection = False
                  end
                  object btnCancelarHorario: TFButton
                    Left = 96
                    Top = 0
                    Width = 48
                    Height = 35
                    Hint = 'Cancelar pedido antendimento'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 2
                    OnClick = btnCancelarHorarioClick
                    PngImage.Data = {
                      89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                      F80000036F4944415478DAB5937B48537114C7CFDDBD9B5B375FA98569605A14
                      8A2414988A927F88996952948948149A6020998614BD0B7A5A448564A5882912
                      1469A9B3971AE5BB742DCC245F99736ACE747373BBAF7E8B4BDD86530BF783C3
                      FDFDF89EDFF7737EE77031B0F1C2E69B189B27D954BEDF546B4BC01486611965
                      29C63C9B00B6DD9150EECEDEF4F044DF0D8661B3D16BB805079CDBFD9028797B
                      D9D0A96AA962682601414C0B0AB898F894A03803C8DB8AA61BBA2A9434CD4422
                      C8F87F0150CF49D4F354424C6C060E5611205D7932BE18DA0DA5E0278D81775F
                      6AA827ADF92A86A1CDC3EFFB2740DC5D699608C34F6C5C1B49047885C99C644B
                      81B47384214601FDA6264030F0936D8501752F5B54776992A64D1108D23A2700
                      558D897051AECF32FFA49D41E98B285C0B8394020CAC06284C0FE84580C0FC45
                      1C7CA55B403761805B55D94686A5A310A4665600EAF3D9D51E0199311B92654D
                      E345306CFCFC57B2D9DC1CB8080767F10A085EB217DE763E31D677567D66192E
                      1C01345601A87A4F0921ED4A8B3A2F6B9C28041DFD1D569281E0245E0E8E2848
                      DC05BA74B5F071B212DC243EBFCC9F298AA795FD0DCD1C0BE6EAF5B3B6281655
                      BFC93FEE8887C772BC77AA09825CF6C017753B3730D2AD1FD27CE574FA49F240
                      F445AC515308EB9DE2A1AC39CFD0ADFE508DCC7721736ACE21C7E5DBBD4A0C3D
                      1CAEC6DAC0DF211AE41F0B8C9F7A143DC8E014923FA0501EDD51404C535A7850
                      7F5D3FA8E92E465AEA5C3FDC1FC03DBBEEB4C8CBDE2AFA3DC8287728A9CBE945
                      0601C860926F2195BEF52A51F2FA8A614CABCA294B311D87792C21A02C213433
                      D6D1C11106C6BAA0A2E97EEDE37DC670C18C287B9933A39D1ECF2A4F31DD9C8F
                      B9E5900F86F86EB910B236D66E4CA782FC17674738965B835EF083D7B5E89382
                      CEA5F335B704D8E338DE931C71DAD595F484965E39FDBCBD5439D8C286B5DEA6
                      8D9BAF89C3E419D41BC15D4E102C1FD6013C24DACDC1F3414268C6227BA90BBC
                      EA28353574C8ABCB534D49481659E4B3BC39C3EF193E68AB00F339FA96248924
                      C9DCED41A9322F177FEC666526A5908F062A4B1835D271FE0E3B83A9F04B5903
                      98AB24C2CF88D739B8638F5C17AF583AAAFD36F8F298316C6AE4978EF339ACC0
                      90B6D8D3B3017E43CC117C88F0EE79C98CAA151C25A85ED822D60246F17B6E36
                      8050C305558BAC001881293793894D97CD013F01E2D89E284DE0BF3E00000000
                      49454E44AE426082}
                    ImageId = 9
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Color = clBtnFace
                    Access = False
                    IconReverseDirection = False
                  end
                  object btnLimparHorario: TFButton
                    Left = 144
                    Top = 0
                    Width = 48
                    Height = 35
                    Hint = 'Limpar horarios'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 3
                    OnClick = btnLimparHorarioClick
                    PngImage.Data = {
                      89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                      F80000016D4944415478DAEDD6CD2B055118C7F173950D29B150364A62435E36
                      0A795F58B3C142FC0752B2F2BA932C6D2C9562C15E79B90965E3A56CA49494A2
                      48E9DAA8E1FB34CFD4699A193323BBFBD4A7B973CF39F7377316CFB919F3CF95
                      8939AF09D5A8D2FB07DCE3EAAF01FD58464BC8F80566B09F26A00307B8C50A4E
                      F1AC631568C7346AD1A7E38902F650A3DBF31132A744B7E90E0351013D984581
                      35D686577D83A8AA4339CEACEF1CCCE1C40B90ED5842191A718D7793AC4AADB5
                      6FFE00AFBA71A46F944D1810B8F6B700B98E6142C727F18535BD5FC7863C69DA
                      8005CC5BF3B2D63CA96F2CEABC7C403E201F1033403AE72586B06BDC7E228BBD
                      2678A8D75EBD3A3A2E7D6C103B6836D641E40F901F7A316EC392F65B892EE3B6
                      0329698A85FAA452E338C6A371DB7B8371CF0A272C406A049BC6ED2FB23DE7F8
                      34C1558456DD26091FC5963D21ECC019C6AABE419C7AC214B6FD0351275A313A
                      51AF9F832A871BDDA65CD084B8FF2A52D70F1A0B8E192C1E0DF5000000004945
                      4E44AE426082}
                    ImageId = 4600235
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Color = clBtnFace
                    Access = False
                    IconReverseDirection = False
                  end
                end
                object hboxEditTimeTemplateHorario: TFHBox
                  Left = 0
                  Top = 64
                  Width = 654
                  Height = 61
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stSingleLine
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 5
                  Padding.Right = 5
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object vboxInicio: TFVBox
                    Left = 0
                    Top = 0
                    Width = 118
                    Height = 56
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object lblInicio: TFLabel
                      Left = 0
                      Top = 0
                      Width = 27
                      Height = 13
                      Caption = 'Inicial'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object edInicial: TFTime
                      Left = 0
                      Top = 14
                      Width = 108
                      Height = 24
                      Table = tbTimeTemplateHorario
                      FieldName = 'INICIO'
                      TabOrder = 0
                      AccessLevel = 0
                      Flex = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Prompt = 'Inicial'
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      IconDirection = idLeft
                      Format = 'HH:mm'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -13
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      HourFormat = hf24
                      TimeSeparator = ':'
                      ShowSeconds = False
                      StepHour = 1
                      StepMinute = 1
                      StepSecond = 1
                    end
                  end
                  object vboxFim: TFVBox
                    Left = 118
                    Top = 0
                    Width = 118
                    Height = 56
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object lblFim: TFLabel
                      Left = 0
                      Top = 0
                      Width = 22
                      Height = 13
                      Caption = 'Final'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object edFinal: TFTime
                      Left = 0
                      Top = 14
                      Width = 108
                      Height = 24
                      Table = tbTimeTemplateHorario
                      FieldName = 'FIM'
                      TabOrder = 0
                      AccessLevel = 0
                      Flex = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Prompt = 'Final'
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      IconDirection = idLeft
                      Format = 'HH:mm'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -13
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      HourFormat = hf24
                      TimeSeparator = ':'
                      ShowSeconds = False
                      StepHour = 1
                      StepMinute = 1
                      StepSecond = 1
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
  end
  object tbTimeTemplateHorario: TFTable
    FieldDefs = <
      item
        Name = 'ID_TEMPLATE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIA_SEMANA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Dia Semana'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INICIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'In'#237'cio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Fim'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DIA_SEMANA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Dia Semana'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TIME_TEMPLATE_HORARIO'
    Cursor = 'TIME_TEMPLATE_HORARIO_SQL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '24509;24503'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTimeTemplate: TFTable
    FieldDefs = <
      item
        Name = 'ID_TEMPLATE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_INITCAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Initcap'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TIME_TEMPLATE'
    Cursor = 'TIME_TEMPLATE_SQL'
    MaxRowCount = 200
    OnAfterScroll = tbTimeTemplateAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '24509;24505'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
