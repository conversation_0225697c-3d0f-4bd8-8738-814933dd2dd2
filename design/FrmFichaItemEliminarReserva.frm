object FrmFichaItemEliminarReserva: TFForm
  Left = 321
  Top = 163
  ActiveControl = FHBox1
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Eliminando Reserva'
  ClientHeight = 334
  ClientWidth = 615
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '5300420'
  ShortcutKeys = <>
  InterfaceRN = 'FichaItemEliminarReservaRN'
  Access = False
  ChangedProp = 
    'FrmFichaItemEliminarReserva.ActiveControlFrmFichaItemEliminarRes' +
    'erva.Width;'#13#10'FrmFichaItemEliminarReserva.Height;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FHBox1: TFHBox
    Left = 0
    Top = 0
    Width = 615
    Height = 61
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 3
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 2
    Margin.Left = 3
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object btnExcluir: TFButton
      Left = 0
      Top = 0
      Width = 65
      Height = 53
      Hint = 'Confirmar a elimina'#231#227'o da  reserva.'
      Caption = 'Confirmar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 0
      OnClick = btnExcluirClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
        E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
        B9B9B6418D210000000049454E44AE426082}
      ImageId = 0
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconClass = 'trash'
      IconReverseDirection = False
    end
  end
  object FHBox2: TFHBox
    Left = 0
    Top = 60
    Width = 601
    Height = 59
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 5
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FVBox2: TFVBox
      Left = 0
      Top = 0
      Width = 118
      Height = 54
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stSingleLine
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FLabel1: TFLabel
        Left = 0
        Top = 0
        Width = 40
        Height = 13
        Hint = 'Quantidade a ser liberada'
        Caption = 'Reserva'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object edtControleReserva: TFInteger
        Left = 0
        Top = 14
        Width = 98
        Height = 24
        Hint = 'Nr. reserva'
        TabOrder = 0
        AccessLevel = 0
        Flex = False
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        Maxlength = 0
        Enabled = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Alignment = taRightJustify
      end
    end
    object FVBox4: TFVBox
      Left = 118
      Top = 0
      Width = 220
      Height = 54
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stSingleLine
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FLabel2: TFLabel
        Left = 0
        Top = 0
        Width = 33
        Height = 13
        Hint = 'Quantidade a ser liberada'
        Caption = 'C'#243'digo'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object edtCodigoItem: TFString
        Left = 0
        Top = 14
        Width = 205
        Height = 24
        TabOrder = 0
        AccessLevel = 0
        Flex = False
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'CodigoItem'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Enabled = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
    end
    object FVBox5: TFVBox
      Left = 338
      Top = 0
      Width = 120
      Height = 54
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stSingleLine
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblQtdeReservada: TFLabel
        Left = 0
        Top = 0
        Width = 79
        Height = 13
        Hint = 'Quantidade reservada'
        Caption = 'Qtde Reservada'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object edtQtdeReservada: TFInteger
        Left = 0
        Top = 14
        Width = 78
        Height = 24
        Hint = 'Qtde a ser liberada'
        FieldName = 'QTDE'
        TabOrder = 0
        AccessLevel = 0
        Flex = False
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        Maxlength = 0
        Enabled = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Alignment = taRightJustify
      end
    end
    object FVBox6: TFVBox
      Left = 458
      Top = 0
      Width = 120
      Height = 54
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stSingleLine
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblQtdeASerLiberada: TFLabel
        Left = 0
        Top = 0
        Width = 95
        Height = 13
        Hint = 'Quantidade a ser liberada'
        Caption = 'Qtde a ser Liberada'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object edtQtdeEliminarReserva: TFInteger
        Left = 0
        Top = 14
        Width = 94
        Height = 24
        Hint = 'Qtde a ser liberada'
        TabOrder = 0
        AccessLevel = 0
        Flex = False
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        Maxlength = 0
        Enabled = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Alignment = taRightJustify
      end
    end
  end
  object FVBox3: TFVBox
    Left = 2
    Top = 120
    Width = 581
    Height = 115
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stSingleLine
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 2
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FVBox1: TFVBox
      Left = 0
      Top = 0
      Width = 576
      Height = 64
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 10
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 0
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object rdButtonEliminarTotal: TFRadioButton
        Left = 0
        Top = 0
        Width = 257
        Height = 17
        Caption = 'Eliminar a reserva por completo'
        Checked = True
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        TabStop = True
        OnCheck = rdButtonEliminarTotalCheck
        RadioGroup = FRadiogroupParcial
        WOwner = FrInterno
        WOrigem = EhNone
      end
      object rdButtonEliminarParcial: TFRadioButton
        Left = 0
        Top = 18
        Width = 369
        Height = 17
        Caption = 'Eliminar parcialmente (Especificar a Quantidade a ser liberada)'
        Enabled = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        OnCheck = rdButtonEliminarParcialCheck
        RadioGroup = FRadiogroupParcial
        WOwner = FrInterno
        WOrigem = EhNone
      end
    end
    object FVBox7: TFVBox
      Left = 0
      Top = 65
      Width = 576
      Height = 41
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 2
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object chkVendaPendente: TFCheckBox
        Left = 0
        Top = 0
        Width = 363
        Height = 27
        Hint = 'Eliminar Venda Pendente, se houver'
        Caption = 'Eliminar Venda Pendente, se houver'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        Visible = False
        OnClick = chkEliminarVendaPendenteClick
        ReadOnly = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
      end
    end
  end
  object FVBox8: TFVBox
    Left = 2
    Top = 234
    Width = 580
    Height = 61
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stSingleLine
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 5
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 3
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object lblObservacao: TFLabel
      Left = 0
      Top = 0
      Width = 58
      Height = 13
      Hint = 'Quantidade a ser liberada'
      Caption = 'Observa'#231#227'o'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
      MaskType = mtText
    end
    object edtObservacao: TFString
      Left = 0
      Top = 14
      Width = 569
      Height = 24
      FieldName = 'OBSERVACAO'
      TabOrder = 0
      AccessLevel = 0
      Flex = False
      WOwner = FrInterno
      WOrigem = EhNone
      Required = False
      Constraint.CheckWhen = cwImmediate
      Constraint.CheckType = ctExpression
      Constraint.FocusOnError = False
      Constraint.EnableUI = True
      Constraint.Enabled = False
      Constraint.FormCheck = True
      IconDirection = idLeft
      CharCase = ccNormal
      Pwd = False
      Maxlength = 0
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'Tahoma'
      Font.Style = []
      OnEnter = edtObservacaoEnter
      SaveLiteralCharacter = False
      TextAlign = taLeft
    end
  end
  object FRadiogroupParcial: TFRadioGroup
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 88
    Top = 4
  end
end
