object MarkupPrincipalRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600650'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbMarkupModelo: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CUSTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Custo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CP_MARKUP_MODELO'
    Cursor = 'CP_MARKUP_MODELO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbMarkup
        GUID = '{D369FA99-FC12-48AB-810F-3852E41244C5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbMarkup: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MARKUP_TIPO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_FIXO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Fixo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_PERCENTUAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Percentual'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCR_MARKUP_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_COMISSAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Comiss'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CP_MARKUP'
    TableName = 'CP_MARKUP'
    Cursor = 'CP_MARKUP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMarkupTipo: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CP_MARKUP_TIPO'
    Cursor = 'CP_MARKUP_TIPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMarkup1: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MARKUP_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CP_MARKUP'
    Cursor = 'CP_MARKUP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbParmFluxo: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MARKUP_OFICINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup Oficina'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_PARM_FLUXO'
    Cursor = 'CRM_PARM_FLUXO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
