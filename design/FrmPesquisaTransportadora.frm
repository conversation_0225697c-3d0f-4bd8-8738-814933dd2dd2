object FrmPesquisaTransportadora: TFForm
  Left = 321
  Top = 163
  ActiveControl = FVBox1
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Pesquisar transportadora'
  ClientHeight = 295
  ClientWidth = 584
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '1610882'
  ShortcutKeys = <>
  InterfaceRN = 'PesquisaTransportadoraRN'
  Access = False
  ChangedProp = 
    'FrmPesquisaTransportadora.Width;'#13#10'FrmPesquisaTransportadora.Acti' +
    'veControlFrmPesquisaTransportadora.Width;'#13#10'FrmPesquisaTransporta' +
    'dora.ActiveControl;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 584
    Height = 295
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 570
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object hBoxLinha01: TFHBox
      Left = 0
      Top = 6
      Width = 570
      Height = 63
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox7: TFHBox
        Left = 0
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnVoltar: TFButton
        Left = 5
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox9: TFHBox
        Left = 70
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnPesquisar: TFButton
        Left = 75
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Pesquisar'
        Caption = 'Pesquisar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 3
        OnClick = btnPesquisarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004BD4944415478DA9D955948636714C7CF4D62E292C47D19199BAB1444
          A6A363C1818A2B8856EA8E4A952A06D427A14F3EF4D127518A880BB850B562A5
          3EF8A80F76AC11DC1DC4655C06B154A341E3BEC6254BCFF932B9F5AA38A51F5C
          6EEE77BFFBFDBEF33FFF73C2D96C36E8E9E991A5A6A6FEE1E4E49400FF614824
          12B8BABA5A191E1E7E5D5050607D6E2D4780C1C1C1A0F8F8789DABAB6B083D7F
          6ED09ACBCB4B30180CBD333333A5C5C5C5A66701FDFDFD7C4A4A8A4EA9546AAC
          56EB6701777777603299E0FAFA1A767777FB2A2A2A7E181B1B333F0B484E4ED6
          A954AA470092E3E1B8BDBD85A3A323F0F4F4848B8B0B585F5FEF89898929319B
          CD9627017D7D7D3CE640A756AB4500DA9C9E777676D86965322928952AF0F2F2
          02A3D108EEEEEE80B2C2F1F1316C6C6CFC3E35355582D1DC3C02F4F6F6F26969
          690280E338766D6EFE0D6B6B1F715325787878B03993E91AE47227C068D90114
          0A05A039E0F0F010B6B6B67A878686CA6A6A6AAE440074119F9E9EAEC3133100
          7DB8BABA0A7ABD1EA2A2A284CD68D0FBD3D353D8DBDB03373737364F92C96432
          72161E682D373B3BBB5F04E8EEEEE633323274784A0D4DD2C70B0B8B909090C0
          64A1A48AC2E6242CC9676767A056AB00B567CEB2582CB0B2B2529A9898F88B08
          D0D5D5C5676666EA30690C303E3E0E1A0D0F010101F8A15D32FAF85F07DBD8EF
          F3F30BA0C028428A8CF2B4B0B0508207FB5504E8ECEC64004C9E864EF5E1C312
          BC79F3F5275938B0E7050400DDE9D5CDCD0DD6C305F8FBFB83542A65CF5817DA
          B8B8B82E11A0A3A38301BCBDBD35240F260B5EBDFA0A37E2D8C60F87D56A63F3
          24CDC181115EBC08C0643B33C0ECECAC168B560C686F6FE773727204805EBF0D
          21215F3269E8641209774F1A1B6E6CD79B40C7C7461601D995724511600EC480
          B6B63601400E595D5D83D0D030269154CA319908466BE9A28D2D167BBD6C6DFD
          05C1C1C1E0E2E2C200580BDAA4A42431A0A5A58501C84564B7F7EF67D1F75E98
          E860FCE89639C901A08DCDE63B94C405DB840125DA87F0F0701611D975626242
          8B452B06343737F3B9B9B9AC0EE8D494B8999959CCC36B78F93250482C398A0A
          9D6C6A306C836EE41D7C9BFA1DF8FAFAB21A2000F6242D16AD18D0D0D0C0E7E7
          E70B952C972BB0320F606969097C7CBC2128E80BB4A29A45717E7E0EDBDB7A98
          9E9E86136C116161A110F5F61BCC831F6B19A3A3A35A54E331202F2F4F879529
          F422B95CCE4E849509FBFBFB9FA2B0319DA9C991EEF3F3F3D0DADA0A111111A0
          D56A51520D0C0C0C68510D31A0A9A989E5009D2000689E7A8CA305D04573944C
          2A2C85428E4DD000D5D5D530323202E81C282A2A026767E7EF232323FB4480FA
          FAFA4711382034C8AA8E06E8680974276B52CFAAADAD85C9C949888E8EA64EFB
          23E6B44104A8ABAB7B1260EF3B1C3CF72F47D12C2F2F4355551559140203038D
          58800598BF3F05406363239F9595A5C3F034CF6DC63D51D63447AD7C7171112A
          2B2B616E6E8EF2777572729285397CC700480F292F2F9F469BFA383AE34399EE
          3F3F7CEF806015435959193306CAB889EFDE32406C6CACAAB0B0F0673F3FBF30
          94E89664B257ACF55EF55A858B06E5C171C7F7368448D11437F8E7F51BE6A314
          FBD23CAEFD89739C0617C8F0E64CBD0CFEDF20FD2CB8DF354AED850630E1FFB6
          E91F70B7FB1897F803840000000049454E44AE426082}
        ImageId = 13
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox14: TFHBox
        Left = 140
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnAceitar: TFButton
        Left = 145
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Aceitar'
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 5
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000BC4944415478DA6364A031601CAA16B802B11910B7D2C20290E11B81
          9813883BA96D01B2E120D0404D0B300C07E2466A5980D57010831A16E0349C1A
          16E0359C520B081A8E6E810C103F21D3F046A805180066413E10B701B13F10EF
          A186CB912D3007E2E350F67720F6C36309D12E47F74139107740D93F81381888
          B752E272740B08594296E1E816E0B2E417B98663B30066403D94FD1D4A131DE6
          C45880EE13648B897639210BD02D21CB704216C00C46A6A96E01C560D4028200
          006ADD312011B225B20000000049454E44AE426082}
        ImageId = 4600376
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox8: TFHBox
        Left = 210
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object FHBox10: TFHBox
      Left = 0
      Top = 70
      Width = 570
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object FHBox2: TFHBox
      Left = 0
      Top = 76
      Width = 570
      Height = 60
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox3: TFHBox
        Left = 0
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object vBoxDescricao: TFVBox
        Left = 5
        Top = 0
        Width = 200
        Height = 50
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblDescricao: TFLabel
          Left = 0
          Top = 0
          Width = 46
          Height = 13
          Caption = 'Descri'#231#227'o'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object edtDescricao: TFString
          Left = 0
          Top = 14
          Width = 190
          Height = 24
          Hint = 'Descri'#231#227'o'
          HelpCaption = 'Descri'#231#227'o'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Descri'#231#227'o'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          OnEnter = edtDescricaoEnter
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
      end
      object FHBox4: TFHBox
        Left = 205
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object vBoxCNPJCPF: TFVBox
        Left = 210
        Top = 0
        Width = 130
        Height = 50
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftMin
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblCNPJCPF: TFLabel
          Left = 0
          Top = 0
          Width = 48
          Height = 13
          Caption = 'CNPJ/CPF'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object edtCNPJCPF: TFString
          Left = 0
          Top = 14
          Width = 120
          Height = 24
          Hint = 'CNPJ/CPF'
          HelpCaption = 'CNPJ/CPF'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'CNPJ/CPF'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          OnChange = edtCNPJCPFChange
          OnEnter = edtCNPJCPFEnter
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
      end
      object FHBox5: TFHBox
        Left = 340
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object vBoxCodigo: TFVBox
        Left = 345
        Top = 0
        Width = 110
        Height = 50
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 5
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftMin
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblCodigo: TFLabel
          Left = 0
          Top = 0
          Width = 33
          Height = 13
          Caption = 'C'#243'digo'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object edtCodigo: TFString
          Left = 0
          Top = 14
          Width = 100
          Height = 24
          Hint = 'C'#243'digo'
          HelpCaption = 'C'#243'digo'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'C'#243'digo'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          OnChange = edtCodigoChange
          OnEnter = edtCodigoEnter
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
      end
      object FHBox6: TFHBox
        Left = 455
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object FHBox11: TFHBox
      Left = 0
      Top = 137
      Width = 570
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 4
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object FHBox12: TFHBox
      Left = 0
      Top = 143
      Width = 570
      Height = 130
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 5
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox15: TFHBox
        Left = 0
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object gridTransportadora: TFGrid
        Left = 5
        Top = 0
        Width = 500
        Height = 120
        TabOrder = 1
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Table = tbTransportadoras
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Paging.Enabled = False
        Paging.PageSize = 0
        Paging.DbPaging = False
        FrozenColumns = 0
        ShowFooter = False
        ShowHeader = True
        MultiSelection = False
        Grouping.Enabled = False
        Grouping.Expanded = False
        Grouping.ShowFooter = False
        Crosstab.Enabled = False
        Crosstab.GroupType = cgtConcat
        EnablePopup = False
        WOwner = FrInterno
        WOrigem = EhNone
        EditionEnabled = False
        AuxColumnHeaders = <>
        NoBorder = False
        ActionButtons.BtnAccept = False
        ActionButtons.BtnView = False
        ActionButtons.BtnEdit = False
        ActionButtons.BtnDelete = False
        ActionButtons.BtnInLineEdit = False
        CustomActionButtons = <>
        ActionColumn.Title = 'A'#231#245'es'
        ActionColumn.Width = 100
        ActionColumn.TextAlign = taCenter
        ActionColumn.Visible = True
        Columns = <
          item
            Expanded = False
            FieldName = 'DESCRICAO_INITCAP'
            Font = <>
            Title.Caption = 'Descri'#231#227'o'
            Width = 200
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = True
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{97C34035-ECD3-4ABA-85CE-EED0E52FC7A2}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'CNPJ_CPF'
            Font = <>
            Title.Caption = 'CNPJ/CPF'
            Width = 130
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{9DAE2EB9-29EC-438F-A140-9BE296B84F84}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'COD_TRANSPORTADORA'
            Font = <>
            Title.Caption = 'C'#243'digo'
            Width = 60
            Visible = True
            Precision = 0
            TextAlign = taRight
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{F3A15924-F763-44C1-BA5B-0BCF1B914E2B}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end>
      end
      object FHBox16: TFHBox
        Left = 505
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object FHBox13: TFHBox
      Left = 0
      Top = 274
      Width = 570
      Height = 5
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 6
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
  end
  object scTransportadoras: TFSchema
    Tables = <
      item
        Table = tbTransportadoras
        GUID = '{448079BF-64F6-4EEE-93E4-5044FB602D79}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbTransportadoras: TFTable
    FieldDefs = <
      item
        Name = 'COD_TRANSPORTADORA'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Transportadora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FAX'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fax'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CGC'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cgc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSC_EST'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Insc Est'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERCENT_SOBRE_NOTA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Percent Sobre Nota'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_POR_PESO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Por Peso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_POR_VOLUME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Por Volume'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMR_DOCUMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Numr Documento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO_ELETRONICO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Endere'#231'o Eletronico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FABRICA_INTERFACE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabrica Interface'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_TRANSPORTADORA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Transportadora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TRANSP_ECOMMERCE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Transportadora Ecommerce'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNPJ_CPF'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnpj Cpf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CNPJCPF_TRANSP'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Cnpjcpf Transportadora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_INITCAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Initcap'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'TRANSPORTADORAS'
    TableName = 'TRANSPORTADORAS'
    Cursor = 'TRANSPORTADORAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '1610882;16101'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
