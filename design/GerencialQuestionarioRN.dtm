object GerencialQuestionarioRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600416'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbQuestionario: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Questionario'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_QUESTIONARIO'
    Cursor = 'CRM_QUESTIONARIO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPerguntas: TFTable
    FieldDefs = <
      item
        Name = 'COD_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pergunta'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_PERGUNTAS'
    Cursor = 'CRM_PERGUNTAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGerencialQuestionarioGrafico: TFTable
    FieldDefs = <
      item
        Name = 'TOT_RESP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tot Resp'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OPCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Op'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pergunta'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OPCOES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Opc'#245'es'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GERENCIAL_QUESTIONARIO_GRAFICO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGerencialQuestionarioGrid: TFTable
    FieldDefs = <
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VEIC_CHASSI_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Veic Chassi Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_RECLAMACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Reclama'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL_PELO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel Pelo Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEXTO_LIVRE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GERENCIAL_QUESTIONARIO_GRID'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
