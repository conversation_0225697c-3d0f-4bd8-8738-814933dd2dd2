object GerencialProdutivoMaisDetRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '299019'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbGridServicosProdutivo: TFTable
    FieldDefs = <
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_LABEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Label'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERCENT_TRABALHADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Percent Trabalhado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_TEMPO_TRABALHADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Tempo Trabalhado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COLOR_PERC_TRAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Color Perc Trab'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_TRABALHADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Trabalhado'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GRID_SERVICOS_PRODUTIVO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '299019;29901'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGridServicosProdOutrasOs: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERCENT_TRAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Percent Trab'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GRID_SERVICOS_PROD_OUTRAS_OS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '299019;29902'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
