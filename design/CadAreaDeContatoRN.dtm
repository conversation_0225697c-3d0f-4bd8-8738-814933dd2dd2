object CadAreaDeContatoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '44801360'
  Left = 44
  Top = 163
  Height = 299
  Width = 442
  object scClienteContatoTipo: TFSchema
    Tables = <
      item
        Table = tbClienteContatoTipo
        GUID = '{A51B36B3-0610-45B7-9D34-AA9155A1CC84}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbClienteContatoTipo: TFTable
    FieldDefs = <
      item
        Name = 'AREA_CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'digo'
        GUID = '{F32A6B28-C2EB-4C4E-93D3-58077528A7DF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{D3C74D2E-2DDF-41FE-8509-1D9E7BCCE269}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        GUID = '{2F4CE15B-F353-43C4-998C-9E1C4ACBFCF3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_CONTATO_TIPO'
    TableName = 'CLIENTE_CONTATO_TIPO'
    Cursor = 'CLIENTE_CONTATO_TIPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44801360;44801'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSimNao: TFTable
    FieldDefs = <
      item
        Name = 'FIELD_KEY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Field Key'
        GUID = '{F713C188-F471-4D7A-A4ED-20DC486BFA59}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIELD_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Field Desconto'
        GUID = '{BBAE92C4-F347-46FA-8175-258E366DBA6B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SIM_NAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44801360;44802'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSimNaoFt: TFTable
    FieldDefs = <
      item
        Name = 'FIELD_KEY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Field Key'
        GUID = '{3F5B8B08-FB5A-4D17-833F-20130E2E35AD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIELD_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Field Desconto'
        GUID = '{5C822C4C-CCA3-49FD-9D08-BECECCAA21C9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SIM_NAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44801360;44801'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
