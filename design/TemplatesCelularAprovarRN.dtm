object TemplatesCelularAprovarRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600665'
  Height = 299
  Width = 442
  object tbCelularAtivosDisparos: TFTable
    FieldDefs = <
      item
        Name = 'ID_CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Celular'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CELULAR_ATIVOS_DISPAROS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600665;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
