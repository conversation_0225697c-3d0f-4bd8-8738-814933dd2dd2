object TimeMembrosRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000194'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object sc: TFSchema
    Tables = <
      item
        Table = tbTime
        GUID = '{71875B7A-D47E-4A7B-A8E1-A0A976403883}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbTimeMembro
        GUID = '{20765F8B-26F7-45E7-A5BC-1B6374FD7D62}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbTime: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        GUID = '{8F95587A-2523-4199-8F33-D937ADE7D080}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{4CCC0B9E-362A-4840-822F-73E7B481AA85}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GRUPO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Grupo'
        GUID = '{DF4A0214-99EA-4E70-A21B-7C8ABD4C3FC4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        GUID = '{BED8D2CD-9FE5-4DBE-8F6C-94BBFC8300D9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_FILA_DA_VEZ'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Fila Da Vez'
        GUID = '{07BEBF9D-0937-4EEE-803E-800D95FA1F28}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE_FILA_SEPARADA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fone Fila Separada'
        GUID = '{42F092F3-AE77-4046-A216-4E9D049B8F4B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEIA_AGENDA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueia Agenda'
        GUID = '{3CB47AAA-5E46-4600-9BD2-A1CA7F8404F9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESBLOQ_FIM_SEMANA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desbloq Fim Semana'
        GUID = '{37BF74FB-959D-442B-BF74-9FC0115C7AEB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GRUPO_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Grupo Desconto'
        GUID = '{9680BE80-09D6-4C12-BA80-0723B0773A21}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESC_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = ' Desconto Servi'#231'o'
        GUID = '{352AC16D-8D01-46D7-BA10-BE4C85CE5298}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_TIME'
    TableName = 'CRM_TIME'
    Cursor = 'CRM_TIME'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000194;70001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTimeMembro: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        GUID = '{*************-4D58-AA9B-1238D68E83E4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{C80FD8A1-28F4-49A8-AAB2-388F23DB76F2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ_ATENDIMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq Atendimento'
        GUID = '{57C05555-0592-4DA1-BEED-3AF365B7DB56}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ULTIMO_ATENDIMENTO_BALCAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = #218'ltimo Atendimento Balc'#227'o'
        GUID = '{063CCBDD-E5F9-4947-BC51-59D398121475}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{B306C333-5900-4ECC-BDBA-1591098E6FEC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        GUID = '{DA2B773E-92D6-4F0F-883F-A4D8FF0D1472}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPLATE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template'
        GUID = '{B4E4C1A6-563A-4691-B14D-98FD9F5E1EEF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Template Descri'#231#227'o'
        GUID = '{3E8F0A7E-CF72-42C0-B830-6B5B185BB37B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    MasterFields = 'ID_TIME'
    DetailFilters = 'ID_TIME'
    UpdateTable = 'CRM_TIME_MEMBRO'
    TableName = 'CRM_TIME_MEMBRO'
    Cursor = 'CRM_TIME_MEMBRO'
    MaxRowCount = 0
    MasterTable = tbTime
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000194;43001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresas: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{A881E73E-B405-44B5-90CD-AB64A37C625E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{D14002AD-46DB-46C7-AC53-2C677B1C5916}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS'
    Cursor = 'EMPRESAS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000194;43002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasFuncoes: TFTable
    FieldDefs = <
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        GUID = '{B44C429F-4785-43E5-9C5D-97E4419CA673}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{D5B3A77E-B040-4A2B-8FBB-545F180E7767}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_FUNCOES'
    Cursor = 'EMPRESAS_FUNCOES'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000194;43003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTimeAgentesDisponiveis: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{634EFE13-D0FC-4329-8AB4-AC540D900804}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        GUID = '{780374FB-975C-4695-A711-F85733A0DD10}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{395059CA-536A-4441-A180-9138376123DF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        GUID = '{99B74E16-1CB1-41E1-AFBB-FD6B8DFB7E75}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'TIME_AGENTES_DISPONIVEIS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000194;43004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTimeTemplate: TFTable
    FieldDefs = <
      item
        Name = 'ID_TEMPLATE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template'
        GUID = '{DA3DDF2F-DE02-4B72-BC12-4F525C90977B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{828B494B-126C-4109-8A17-A5D612A007E5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_INITCAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Initcap'
        GUID = '{52408FB8-946D-4CA6-B8C1-1E5CADE75D55}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TIME_TEMPLATE'
    Cursor = 'TIME_TEMPLATE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000194;26501'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTimeMembroTemplateDisponivel: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        GUID = '{A5BA7B42-F18F-4185-895F-1ACAD13D1B7E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{AA9CD3D4-5316-4DD7-B9B6-32182AE91484}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ_ATENDIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq Atendimento'
        GUID = '{0A394E14-1CF8-4C82-8944-FCCC515980B6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ULTIMO_ATENDIMENTO_BALCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = #218'ltimo Atendimento Balc'#227'o'
        GUID = '{70FE88FB-5F08-4B71-8233-AC166DE1D11B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{36B4FEC3-D26B-498E-90D1-F2EAFE951286}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        GUID = '{6776D7EA-D504-4697-890B-ABC59D3A3D65}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPLATE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template'
        GUID = '{7A70D240-575C-4612-B786-3EC90F4EAFC5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Template Descri'#231#227'o'
        GUID = '{376C9D51-4A46-4BED-A8C0-76538D2B166F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIME_MEMBRO'
    Cursor = 'CRM_TIME_MEMBRO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000194;26502'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTimeMembroTemplateCruzado: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        GUID = '{F6A0ABD0-B026-4AC3-BDAB-DD1023A5317B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{621C1ACE-A553-438E-8870-31079CDBC1C1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ_ATENDIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq Atendimento'
        GUID = '{E636E689-FD65-40CB-A89B-72457DB16976}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ULTIMO_ATENDIMENTO_BALCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = #218'ltimo Atendimento Balc'#227'o'
        GUID = '{BB79FCFC-F1FC-48D8-94A2-2FCD115E5626}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{D9B45310-518C-4523-88F9-0E59F10B3C3C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        GUID = '{7CAA3F22-9A04-4BDC-B2DA-4C96FECF7321}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPLATE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template'
        GUID = '{01F81794-625F-4A33-AD84-2F9D03571AC5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Template Descri'#231#227'o'
        GUID = '{DD7003B5-1A25-465D-BC68-6E3C0E210C20}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIME_MEMBRO'
    Cursor = 'CRM_TIME_MEMBRO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000194;26503'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
