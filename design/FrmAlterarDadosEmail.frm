object FrmAlterarDadosEmail: TFForm
  Left = 320
  Top = 161
  Caption = 'Altera'#231#227'o de e-mail e senha do usu'#225'rio'
  ClientHeight = 236
  ClientWidth = 542
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '430090'
  ShortcutKeys = <>
  InterfaceRN = 'AlterarDadosEmailRN'
  Access = False
  ChangedProp = 'FrmAlterarDadosEmail.Height;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object grbUsuario: TFGroupbox
    Left = 0
    Top = 0
    Width = 542
    Height = 236
    Align = alClient
    Caption = 'Usu'#225'rio:'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = []
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    ParentFont = False
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    WOwner = FrInterno
    WOrigem = EhNone
    Scrollable = False
    Closable = False
    Closed = False
    Orient = coHorizontal
    Style = grp3D
    HeaderImageId = 0
    object FVBox1: TFVBox
      Left = 2
      Top = 15
      Width = 538
      Height = 219
      Align = alClient
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 5
      Padding.Right = 5
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      object FGridPanel1: TFGridPanel
        Left = 0
        Top = 0
        Width = 538
        Height = 156
        Align = alTop
        Caption = 'FGridPanel1'
        ColumnCollection = <
          item
            SizeStyle = ssAbsolute
            Value = 90.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            Value = 100.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        ControlCollection = <
          item
            Column = 0
            Control = FLabel1
            Row = 0
          end
          item
            Column = 1
            Control = edtEmail
            Row = 0
          end
          item
            Column = 0
            Control = FLabel2
            Row = 1
          end
          item
            Column = 1
            Control = edtSenhaAntiga
            Row = 1
          end
          item
            Column = 0
            Control = FLabel3
            Row = 2
          end
          item
            Column = 1
            Control = edtSenha
            Row = 2
          end
          item
            Column = 0
            Control = FLabel4
            Row = 3
          end
          item
            Column = 1
            Control = edtConfimarSenha
            Row = 3
          end>
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        RowCollection = <
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        AllRowFlex = True
        WOwner = FrInterno
        WOrigem = EhNone
        ColumnTabOrder = False
        object FLabel1: TFLabel
          Left = 63
          Top = 1
          Width = 28
          Height = 24
          Align = alRight
          Caption = 'e-mail'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          ExplicitHeight = 13
        end
        object edtEmail: TFString
          Left = 91
          Top = 1
          Width = 188
          Height = 24
          Table = tbEmpresasUsuarios
          FieldName = 'EMAIL'
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = True
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object FLabel2: TFLabel
          Left = 28
          Top = 25
          Width = 63
          Height = 24
          Align = alRight
          Caption = 'Senha antiga'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          ExplicitHeight = 13
        end
        object edtSenhaAntiga: TFString
          Left = 91
          Top = 25
          Width = 188
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = True
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = True
          Maxlength = 0
          Enabled = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object FLabel3: TFLabel
          Left = 31
          Top = 49
          Width = 60
          Height = 24
          Align = alRight
          Caption = 'e-mail senha'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          ExplicitHeight = 13
        end
        object edtSenha: TFString
          Left = 91
          Top = 49
          Width = 188
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = True
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = True
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
        object FLabel4: TFLabel
          Left = 11
          Top = 73
          Width = 80
          Height = 24
          Align = alRight
          Caption = 'Confirmar Senha'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          ExplicitHeight = 13
        end
        object edtConfimarSenha: TFString
          Left = 91
          Top = 73
          Width = 188
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = True
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          CharCase = ccNormal
          Pwd = True
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
        end
      end
      object FHBox1: TFHBox
        Left = 0
        Top = 157
        Width = 533
        Height = 52
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        object FHBox2: TFHBox
          Left = 0
          Top = 0
          Width = 168
          Height = 24
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
        end
        object btnSalvar: TFButton
          Left = 168
          Top = 0
          Width = 88
          Height = 40
          Caption = 'Salvar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          OnClick = btnSalvarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000001374944415478DA6364A031608431FEFFFF2F09A4F449D4FF1D88CF33
            32327EC26B01D4F00740CC468623419654002D9984CF020F20B51D88D700F141
            206E00626134B5CB80F838946D09C451503D1A40AC08C48D404B1A0859900B54
            3405C8BF03642BA3A9F504CAED40570FC4AB81783F106B62B384620BA0EAC571
            5942150BA062C896E4C3E2846A16205972028841B43850EE332916608B64140B
            A0665500A976207600CA1DC465C152A80184800550FD49340B7280D464988F71
            59C002645B0331271EC36F03D5DE451724CA02225C8E130C0E0B880C2264002A
            2E8E02F5FE21D602622319192C03EA8D26D60250321560809449C40090BA0F40
            BD2AA458C000D24064B8C3D58F5A40B105C8150E030369910CA3ED813804DD02
            4AAA4C6CE017102B002D784E69A58F0B5C04190E0E362A1988130000D0053128
            1A34B73E0000000049454E44AE426082}
          ImageId = 310032
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object FHBox3: TFHBox
          Left = 256
          Top = 0
          Width = 8
          Height = 28
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
        end
        object btnCancelar: TFButton
          Left = 264
          Top = 0
          Width = 88
          Height = 40
          Caption = 'Cancelar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 3
          OnClick = btnCancelarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F80000017E4944415478DA6364A031601CB5802C0BFE8786323F7DCD53C1C2F6
            6782C4AEC55FF11970D7258D9FEDCF9F4C1907B92EC686867F042D8018CEBB04
            C88A00720F022DF1C665C95BCF68BEEFDFD9760199E64093E64BDBCBA7A05B82
            61C153C7C4BAFFFF191A9184B05A826238CCB0FF8C65D207E775E3B500E465F6
            DFBF763130329AE1B2046AF84E20D30249CD1136064E4FB103D3BE108C03B025
            7F7EEF06324DD12D6165FEC78C6938E35136060E0F74C3715A80CF1220660362
            4B620CC76B0108DC774810606360DCFD9F81C104AB82FF0CC7D8FF307A881E9D
            F719971904F3014E4B88309C280B5E5B27F1FE64FDBF13355820C1852F091365
            011EC389B604A705580D07060B2323031B5A70E1B5849168C381A985FD3783E7
            17D67FCC58E204A7258CA4180E8B501C118FD512228A0AECE91C9B25C0E02B97
            DE3FBF0BAF05E0C2EE15CF32A04C18038EEC8FD592FF0C8BA41DE51309167660
            4B1C1A589E313EAC62FDCFD987CB70644B58191873A41DE4DB882AAEA90D462D
            200800F164D519910AF68B0000000049454E44AE426082}
          ImageId = 310030
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object FHBox4: TFHBox
          Left = 352
          Top = 0
          Width = 177
          Height = 24
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
        end
      end
    end
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbEmpresasUsuarios
        GUID = '{FAEE411F-97B2-4DAA-83B8-26D5CF4F1E80}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_SENHA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Email Senha'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Email'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'EMPRESAS_USUARIOS'
    TableName = 'EMPRESAS_USUARIOS'
    Cursor = 'EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430090;43001'
    DeltaMode = dmAll
    RatioBatchSize = 20
  end
end
