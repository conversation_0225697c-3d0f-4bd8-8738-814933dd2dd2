object TimeTemplateHorariosRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '24509'
  Left = 44
  Top = 163
  Height = 299
  Width = 442
  object tbTimeTemplateHorario: TFTable
    FieldDefs = <
      item
        Name = 'ID_TEMPLATE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIA_SEMANA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Dia Semana'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INICIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'In'#237'cio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Fim'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DIA_SEMANA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Dia Semana'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TIME_TEMPLATE_HORARIO'
    Cursor = 'TIME_TEMPLATE_HORARIO_SQL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '24509;24503'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTimeTemplate: TFTable
    FieldDefs = <
      item
        Name = 'ID_TEMPLATE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_INITCAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Initcap'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TIME_TEMPLATE'
    Cursor = 'TIME_TEMPLATE_SQL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '24509;24505'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
