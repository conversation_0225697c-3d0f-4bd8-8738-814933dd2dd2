object FrmCentralPendenciaAprovItem: TFForm
  Left = 321
  Top = 163
  ActiveControl = vBoxAprovItem
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Aprova'#231#227'o de desconto'
  ClientHeight = 561
  ClientWidth = 522
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '123045'
  ShortcutKeys = <>
  InterfaceRN = 'CentralPendenciaAprovItemRN'
  Access = False
  ChangedProp = 
    'FrmCentralPendenciaAprovItem.Width;'#13#10'FrmCentralPendenciaAprovIte' +
    'm.Height;'#13#10'FrmCentralPendenciaAprovItem.ActiveControlFrmCentralP' +
    'endenciaAprovItem.Width;'#13#10'FrmCentralPendenciaAprovItem.ActiveCon' +
    'trol;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxAprovItem: TFVBox
    Left = 0
    Top = 0
    Width = 522
    Height = 561
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 577
      Height = 65
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAprovarItem: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Aprovar Or'#231'amento'
        Align = alLeft
        Caption = 'Aprovar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAprovarItemClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000023C4944415478DAB5964D4815511CC5EF582409A914586010F9158491
          868B8A3E8CB068252E2B92A022A402DD58B85274230541941B29175AB412C372
          214196D8A236BDB082020D2BFB008D12B18C9C7EA77B5F0C0FF34D396FE0C799
          B96FEE39EFCEDCFBBFE3999087EFFB8790F33025F53CEF6A987E5E48F31CE40D
          3C866FB0174E137225AA800AA41FB6C123E8863D9047C8441401C79176C8C570
          9CEB02CE5FC129AEDBA208B889EC7201BE6B7B8FF4705DB3A8008CF29161B884
          D9D940FB732446DBC1FF0EC02413B90D1BA104B3B781DFDE21DDB49D5930801B
          155205C59006DF8D7DA14BA11334820318F507CC57219FE03EDC8159C78C6B57
          F8B01EA7022E72519BF8E7DDE89EC1496E1C4A18D961A42BC9D37D01950AF8CA
          C92DA8562247BA33D642BA40DB8FBF3C3E8D76C93C64C366B80C23BF1D3969C2
          A831D07980EBF2641320C9FB6B41EA5312E0567E0FAC8934807E5A749A719A18
          6BA12155235886A88474A52A6039F211AE4516409F75C87AD0BFD734AE86DD51
          06EC43B61B3B4D4FC0433C2A52F588B430E7F0D81979007D5722AFE1BA2AED9F
          006357EE7EB8017DFF1280C506445535C37914C1563C62C1006D229310832350
          C50D9F43066421A5CE431BD339FAB6EA3705CCA1CD700F8E4183B1854CB3E22E
          3C80413A8C86082A445E1A5BD73AE3014F51D57D35FC8415A05D6A0C561B3B2B
          747C3176E31F73A82CFB017FCD7D957DBD8362023EC403B6A01DB0C9D812AD9A
          DE0B478DFD82D03EB103CA8C2D03AA33DA0FB21206306DEC57471DE64FE28DBF
          00A62826ADD4CA5ECF0000000049454E44AE426082}
        ImageId = 430080
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox3: TFHBox
        Left = 120
        Top = 0
        Width = 97
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object FVBox3: TFVBox
        Left = 217
        Top = 0
        Width = 115
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 5
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftMin
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel9: TFLabel
          Left = 0
          Top = 0
          Width = 41
          Height = 13
          Caption = 'Origem'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object FLabel10: TFLabel
          Left = 0
          Top = 14
          Width = 37
          Height = 13
          Caption = 'FLabel6'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          FieldName = 'ORIGEM_DESCONTO'
          Table = tbPendenciaAprovItemGrid
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
      object vBoxMaxFuncao: TFVBox
        Left = 332
        Top = 0
        Width = 81
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 5
        Padding.Left = 15
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        OnClick = vBoxMaxFuncaoClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel11: TFLabel
          Left = 0
          Top = 0
          Width = 71
          Height = 13
          Caption = 'Max. Origem'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object FLabel12: TFLabel
          Left = 0
          Top = 14
          Width = 37
          Height = 13
          Caption = 'FLabel6'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = 15174738
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          FieldName = 'PERC_DESC_MAX_GERENTE'
          Table = tbPendenciaAprovItemGrid
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          Mask = '##0,00%'
          WordBreak = False
          MaskType = mtText
        end
      end
      object FHBox4: TFHBox
        Left = 413
        Top = 0
        Width = 145
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 5
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object hBoxDescTitle: TFHBox
      Left = 0
      Top = 66
      Width = 578
      Height = 40
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FGridPanel1: TFGridPanel
        Left = 0
        Top = 0
        Width = 572
        Height = 41
        Caption = 'FGridPanel1'
        ColumnCollection = <
          item
            SizeStyle = ssAuto
            Value = 17.143310216361850000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            Value = 17.241843057003540000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            Value = 25.000937535157560000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            Value = 33.331666583329170000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            Value = 50.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            SizeStyle = ssAuto
            Value = 100.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end
          item
            Value = 100.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        ControlCollection = <
          item
            Column = 0
            Control = FLabel1
            Row = 0
          end
          item
            Column = 1
            Control = edtPercDesc
            Row = 0
          end
          item
            Column = 3
            Control = btnAplicar
            Row = 0
          end
          item
            Column = 4
            Control = btnAplicarDesejado
            Row = 0
          end
          item
            Column = 5
            Control = btnAplicarMaximo
            Row = 0
          end
          item
            Column = 2
            Control = vBoxIconApagar
            Row = 0
          end>
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        RowCollection = <
          item
            SizeStyle = ssAbsolute
            Value = 34.000000000000000000
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftMin
        AllRowFlex = False
        WOwner = FrInterno
        WOrigem = EhNone
        ColumnTabOrder = False
        object FLabel1: TFLabel
          Left = 1
          Top = 1
          Width = 41
          Height = 13
          Caption = '% Desc.'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object edtPercDesc: TFDecimal
          Left = 42
          Top = 1
          Width = 73
          Height = 34
          TabOrder = 0
          AccessLevel = 0
          Flex = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          Maxlength = 0
          Precision = 0
          Align = alLeft
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          Alignment = taRightJustify
          Mode = dmDecimal
          ExplicitHeight = 24
        end
        object btnAplicar: TFButton
          Left = 155
          Top = 1
          Width = 95
          Height = 34
          Hint = 'Aplicar o percentual informado.'
          Align = alLeft
          Caption = 'Aplicar Desconto'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          OnClick = btnAplicarClick
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnAplicarDesejado: TFButton
          Left = 250
          Top = 1
          Width = 95
          Height = 34
          Hint = 'Aplicar o percentual informado pelo usu'#225'rio que requisitou.'
          Align = alLeft
          Caption = 'Aplicar desejado'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 2
          OnClick = btnAplicarDesejadoClick
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnAplicarMaximo: TFButton
          Left = 345
          Top = 1
          Width = 95
          Height = 34
          Hint = 'Volta o desconto praticado na politica do item.'
          Align = alLeft
          Caption = 'Aplicar m'#225'ximo'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 3
          OnClick = btnAplicarMaximoClick
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object vBoxIconApagar: TFVBox
          Left = 115
          Top = 1
          Width = 40
          Height = 34
          Align = alLeft
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 11
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          OnClick = vBoxIconApagarClick
          Margin.Top = 0
          Margin.Left = 5
          Margin.Right = 5
          Margin.Bottom = 0
          Spacing = 0
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object iconTrash: TFImage
            Left = 0
            Top = 0
            Width = 16
            Height = 18
            Align = alClient
            Stretch = False
            ImageSrc = '/images/crmservice4600235.png'
            WOwner = FrInterno
            WOrigem = EhNone
            BoxSize = 0
            GrayScaleOnDisable = False
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            Preview = False
            ImageId = 0
          end
        end
      end
    end
    object gridItensAprov: TFGrid
      Left = 0
      Top = 107
      Width = 575
      Height = 400
      TabOrder = 2
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Table = tbPendenciaAprovItemGrid
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Paging.Enabled = False
      Paging.PageSize = 0
      Paging.DbPaging = False
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = True
      MultiSelection = False
      Grouping.Enabled = False
      Grouping.Expanded = False
      Grouping.ShowFooter = False
      Crosstab.Enabled = False
      Crosstab.GroupType = cgtConcat
      EnablePopup = False
      WOwner = FrInterno
      WOrigem = EhNone
      EditionEnabled = False
      AuxColumnHeaders = <>
      ContextMenu = popMenuSelGrid
      NoBorder = False
      ActionButtons.BtnAccept = False
      ActionButtons.BtnView = False
      ActionButtons.BtnEdit = False
      ActionButtons.BtnDelete = False
      ActionButtons.BtnInLineEdit = False
      CustomActionButtons = <>
      ActionColumn.Title = 'A'#231#245'es'
      ActionColumn.Width = 100
      ActionColumn.TextAlign = taCenter
      ActionColumn.Visible = True
      Columns = <
        item
          Expanded = False
          FieldName = 'SEL'
          Font = <>
          Title.Caption = '#'
          Width = 44
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <
            item
              Expression = 'SEL = '#39'S'#39
              EvalType = etExpression
              GUID = '{83A83D68-DCEF-48A6-A866-A19E0C94C941}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 7000105
              OnClick = 'CheckSel'
              Color = clBlack
            end
            item
              Expression = 'SEL = '#39'N'#39
              EvalType = etExpression
              GUID = '{11AB9A81-8D34-4024-9336-64BDB79246C9}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 7000106
              OnClick = 'UnCheckSel'
              Color = clBlack
            end>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = False
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{8DA633DF-5FA7-4A52-9501-91D09FBB09E8}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'COD_ITEM'
          Font = <>
          Title.Caption = 'C'#243'd. Item'
          Width = 123
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{B321ADA8-8874-4AEE-A4F4-08EFF7FD0619}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'DESCONTO_POR_ITEM_DES'
          Font = <
            item
              Expression = 
                '((PERC_DESCONTO_LIBERADO = 0) OR (PERC_DESCONTO_LIBERADO IS NULL' +
                '))'
              EvalType = etExpression
              GUID = '{CC7C5B2F-C391-4ACF-9869-43D97D2EF1AA}'
              WOwner = FrInterno
              WOrigem = EhNone
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clRed
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
            end>
          Title.Caption = '% Des.'
          Width = 70
          Visible = True
          Precision = 0
          TextAlign = taRight
          FieldType = ftDecimal
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{B210B87B-67B2-483C-9254-692B3A12E33B}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'PERC_DESC_MAX'
          Font = <>
          Title.Caption = '% M'#225'x.'
          Width = 70
          Visible = True
          Precision = 0
          TextAlign = taRight
          FieldType = ftDecimal
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{D5B4DF49-B4D6-4180-AEF3-0457F5EA5C76}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'PERC_DESCONTO_LIBERADO'
          Font = <
            item
              Expression = '(PERC_DESCONTO_LIBERADO > 0 AND LIBEROU = '#39'R'#39')'
              EvalType = etExpression
              GUID = '{68F45232-D1EF-4DE0-BBF4-D9BCE93C5B4C}'
              WOwner = FrInterno
              WOrigem = EhNone
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clRed
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
            end
            item
              Expression = 
                '(PERC_DESCONTO_LIBERADO > 0 AND LIBEROU = '#39'S'#39' AND PERC_DESCONTO_' +
                'LIBERADO = DESCONTO_POR_ITEM_DES)'
              EvalType = etExpression
              GUID = '{E9B5242D-C9A9-4F91-B735-5B4C66FE5122}'
              WOwner = FrInterno
              WOrigem = EhNone
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clGreen
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
            end
            item
              Expression = 
                '(PERC_DESCONTO_LIBERADO > 0 AND LIBEROU = '#39'S'#39' AND PERC_DESCONTO_' +
                'LIBERADO != DESCONTO_POR_ITEM_DES)'
              EvalType = etExpression
              GUID = '{9F807707-3AF4-4734-BE71-21F5172C3D9E}'
              WOwner = FrInterno
              WOrigem = EhNone
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clFuchsia
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
            end>
          Title.Caption = '% Lib.'
          Width = 70
          Visible = True
          Precision = 0
          TextAlign = taRight
          FieldType = ftDecimal
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{EDC1F6F5-5B37-420C-970E-9146C80A5915}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'PRECO_LIQUIDO'
          Font = <>
          Title.Caption = 'Pre'#231'o L'#237'quido'
          Width = 119
          Visible = True
          Precision = 0
          TextAlign = taRight
          FieldType = ftDecimal
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <
            item
              Expression = '*'
              EvalType = etExpression
              GUID = '{54939EBB-5D0A-4DB0-BFB7-56619A839E26}'
              WOwner = FrInterno
              WOrigem = EhNone
              Mask = 'R$ ,##0.00'
              PadLength = 0
              PadDirection = pdNone
              MaskType = mtDecimal
            end>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{150A3014-40AC-4F63-B05D-FE44AD8EF22F}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end>
    end
    object FHBox2: TFHBox
      Left = 0
      Top = 508
      Width = 574
      Height = 45
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 5
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 10
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox1: TFVBox
        Left = 0
        Top = 0
        Width = 185
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel4: TFLabel
          Left = 0
          Top = 0
          Width = 55
          Height = 13
          Caption = 'Descri'#231#227'o'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object lblDescItem: TFLabel
          Left = 0
          Top = 14
          Width = 53
          Height = 13
          Caption = 'lblDescitem'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          FieldName = 'DESC_ITEM'
          Table = tbPendenciaAprovItemGrid
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
      object FVBox2: TFVBox
        Left = 185
        Top = 0
        Width = 69
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel5: TFLabel
          Left = 0
          Top = 0
          Width = 38
          Height = 13
          Caption = 'P. unit.'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object FLabel6: TFLabel
          Left = 0
          Top = 14
          Width = 37
          Height = 13
          Caption = 'FLabel6'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          FieldName = 'PRECO_VENDA'
          Table = tbPendenciaAprovItemGrid
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          Mask = 'R$ ,##0.00'
          WordBreak = False
          MaskType = mtText
        end
      end
      object FVBox5: TFVBox
        Left = 254
        Top = 0
        Width = 69
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel7: TFLabel
          Left = 0
          Top = 0
          Width = 27
          Height = 13
          Caption = 'Qtde'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object FLabel8: TFLabel
          Left = 0
          Top = 14
          Width = 37
          Height = 13
          Caption = 'FLabel6'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          FieldName = 'QUANTIDADE'
          Table = tbPendenciaAprovItemGrid
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
      object vBoxMargem: TFVBox
        Left = 323
        Top = 0
        Width = 69
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        OnClick = vBoxMargemClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel2: TFLabel
          Left = 0
          Top = 0
          Width = 47
          Height = 13
          Caption = 'Margem'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object lblMargem: TFLabel
          Left = 0
          Top = 14
          Width = 48
          Height = 13
          Caption = 'lblMargem'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsUnderline]
          ParentFont = False
          FieldName = 'MARGEM_ITEM'
          Table = tbPendenciaDadosMarkup
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          Mask = ',##0.00'
          WordBreak = False
          MaskType = mtText
        end
      end
      object vBoxMarkup: TFVBox
        Left = 392
        Top = 0
        Width = 69
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        OnClick = vBoxMarkupClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel3: TFLabel
          Left = 0
          Top = 0
          Width = 43
          Height = 13
          Caption = 'Markup'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object lblMarkup: TFLabel
          Left = 0
          Top = 14
          Width = 45
          Height = 13
          Caption = 'lblMarkup'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsUnderline]
          ParentFont = False
          FieldName = 'MARKUP_ITEM'
          Table = tbPendenciaDadosMarkup
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          Mask = ',##0.00'
          WordBreak = False
          MaskType = mtText
        end
      end
    end
  end
  object coachmarkMessage: TFCoachmark
    ShowNextButton = False
    ShowPriorButton = False
    Items = <
      item
        TargetName = 'gridItensAprov'
        Position = poBefore_start
        Name = 'coachmarkMessageItem0'
      end
      item
        TargetName = 'edtPercDesc'
        Position = poBefore_start
        Name = 'coachmarkMessageItem1'
      end>
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 472
    Top = 19
  end
  object popMenuSelGrid: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 220
    Top = 205
    object mmSelTodos: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Todas'
      OnClick = mmSelTodosClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{5B5CFB28-A4BF-4AF8-BACF-A12635CA0228}'
    end
    object mmSelNenhum: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Nenhum'
      OnClick = mmSelNenhumClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{78E86E06-0544-47D4-A519-BBD6F31BE0B4}'
    end
  end
  object tbPendenciaAprovItemGrid: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_POR_ITEM_DES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Por Item Des'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_DESC_MAX'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Desconto Max'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARKUP_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Markup Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_DESCONTO_LIBERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Desconto Liberado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBEROU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUANTIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_DESC_MAX_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Desconto Max Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Origem Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PENDENCIA_APROV_ITEM_GRID'
    MaxRowCount = 200
    OnAfterScroll = tbPendenciaAprovItemGridAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123045;12301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOrcamentosItens: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_POR_ITEM_LIB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Por Item Lib'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBEROU_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberou Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_ORCAMENTOS_ITENS'
    Cursor = 'OS_ORCAMENTOS_ITENS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123045;12302'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPendenciaDadosMarkup: TFTable
    FieldDefs = <
      item
        Name = 'MARGEM_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARKUP_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Markup Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PENDENCIA_DADOS_MARKUP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123045;12303'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
