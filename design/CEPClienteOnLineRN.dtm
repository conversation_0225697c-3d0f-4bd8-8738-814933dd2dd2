object CEPClienteOnLineRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600193'
  Height = 299
  Width = 442
  object sc: TFSchema
    Tables = <
      item
        Table = tbClientesEndereco
        GUID = '{8FFA1248-D9AA-49AA-9C43-DA45D3D96247}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbClientesEnderecoIe
        GUID = '{A93CC392-6385-46AB-B00C-A98EEF20D45F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbCidades
        GUID = '{006B2C6E-D440-48B8-B9BF-AF0D55E16C8A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbClientesEndereco: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cid Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_RES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cid com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_COM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_COBRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Cobranca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_COBRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Cobranca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_COBRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cid Cobranca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_COBRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua Cobranca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_COBRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada Cobranca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_COBRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro Cobranca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_COBRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento Cobranca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Codcidadeibge Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Codcidadeibge com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE_COB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Codcidadeibge Cob'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade Res'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade com'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_COB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade Cob'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_RES_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Res Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_RES_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Res Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_RES_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cid Res Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE_RES_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codcidadeibge Res Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_RES_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade Res Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_RES_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua Res Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_RES_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada Res Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_RES_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro Res Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_RES_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento Res Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_COM_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep com Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_COM_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf com Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_COM_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cid com Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE_COM_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codcidadeibge com Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_COM_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade com Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_COM_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua com Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_COM_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada com Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_COM_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro com Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_COM_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento com Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_COBRANCA_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Cobranca Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_COBRANCA_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Cobranca Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_COBRANCA_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cid Cobranca Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE_COB_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codcidadeibge Cob Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_COB_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade Cob Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_COBRANCA_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua Cobranca Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_COBRANCA_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada Cobranca Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_COBRANCA_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro Cobranca Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_COBRANCA_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento Cobranca Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTES'
    TableName = 'CLIENTES'
    Cursor = 'CLIENTES_ENDERECO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600193;46001'
    DeltaMode = dmAll
    RatioBatchSize = 20
  end
  object tbClientesEnderecoIe: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MANTER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Manter'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_OLD'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_OLD'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_OLD'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_OLD'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_OLD'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_OLD'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'ENDERECO_POR_INSCRICAO'
    TableName = 'ENDERECO_POR_INSCRICAO'
    Cursor = 'CLIENTES_ENDERECO_IE'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600193;46002'
    DeltaMode = dmAll
    RatioBatchSize = 20
  end
  object tbClientesEnderecoTemp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'COD_CLIENTE'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_RES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CEP_RES'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_RES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'UF_RES'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_RES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'COD_CID_RES'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_RES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'RUA_RES'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_RES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'FACHADA_RES'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_RES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'BAIRRO_RES'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_RES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'COMPLEMENTO_RES'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_COM'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CEP_COM'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_COM'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'UF_COM'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_COM'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'COD_CID_COM'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_COM'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'RUA_COM'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_COM'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'FACHADA_COM'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_COM'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'BAIRRO_COM'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_COM'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'COMPLEMENTO_COM'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_COBRANCA'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CEP_COBRANCA'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_COBRANCA'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'UF_COBRANCA'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_COBRANCA'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'COD_CID_COBRANCA'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_COBRANCA'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'RUA_COBRANCA'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_COBRANCA'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'FACHADA_COBRANCA'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_COBRANCA'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'BAIRRO_COBRANCA'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_COBRANCA'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'COMPLEMENTO_COBRANCA'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_RES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CIDADE_RES'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_COB'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CIDADE_COB'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_COM'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CIDADE_COM'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE_RES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'CODCIDADEIBGE_RES'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE_COM'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'CODCIDADEIBGE_COM'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE_COB'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'CODCIDADEIBGE_COB'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DUAL'
    Cursor = 'DUAL'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600193;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbClientesEnderecoIeTemp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'COD_CLIENTE'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'INSCRICAO_ESTADUAL'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CEP'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'UF'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'RUA'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CIDADE'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'BAIRRO'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'COMPLEMENTO'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'FACHADA'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'COD_CIDADES'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DUAL'
    Cursor = 'DUAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600193;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbCidades: TFTable
    FieldDefs = <
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODCIDADEIBGE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Codcidadeibge'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CIDADES'
    TableName = 'CIDADES'
    Cursor = 'CIDADES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600193;46006'
    DeltaMode = dmAll
    RatioBatchSize = 20
  end
end
