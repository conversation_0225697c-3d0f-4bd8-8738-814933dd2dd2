object FrmConsultaOrcAprovCliente: TFForm
  Left = 321
  Top = 162
  ActiveControl = vBoxAprovacaoCliente
  Caption = 'Aprova'#231#227'o do Cliente'
  ClientHeight = 559
  ClientWidth = 523
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '296049'
  ShortcutKeys = <>
  InterfaceRN = 'ConsultaOrcAprovClienteRN'
  Access = False
  ChangedProp = 
    'FrmConsultaOrcAprovCliente.Width;'#13#10'FrmConsultaOrcAprovCliente.He' +
    'ight;'#13#10#13#10'FrmConsultaOrcAprovCliente.ActiveControl'#13#10'FrmConsultaOr' +
    'cAprovCliente_1.Touch.InteractiveGestures;'#13#10'FrmConsultaOrcAprovC' +
    'liente_1.Touch.InteractiveGestureOptions;'#13#10'FrmConsultaOrcAprovCl' +
    'iente_1.Touch.ParentTabletOptions;'#13#10'FrmConsultaOrcAprovCliente_1' +
    '.Touch.TabletOptions;'#13#10'FrmConsultaOrcAprovCliente_1.Touch.Intera' +
    'ctiveGestures;'#13#10'FrmConsultaOrcAprovCliente_1.Touch.InteractiveGe' +
    'stureOptions;'#13#10'FrmConsultaOrcAprovCliente_1.Touch.ParentTabletOp' +
    'tions;'#13#10'FrmConsultaOrcAprovCliente_1.Touch.TabletOptions;'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxAprovacaoCliente: TFVBox
    Left = 0
    Top = 0
    Width = 523
    Height = 559
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 10
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox11: TFHBox
      Left = 0
      Top = 0
      Width = 517
      Height = 61
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Voltar Tela'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAcaoCliente: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Aprovar Manual'
        Align = alLeft
        Caption = 'Enviar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAcaoClienteClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000023C4944415478DAB5964D4815511CC5EF582409A914586010F9158491
          868B8A3E8CB068252E2B92A022A402DD58B85274230541941B29175AB412C372
          214196D8A236BDB082020D2BFB008D12B18C9C7EA77B5F0C0FF34D396FE0C799
          B96FEE39EFCEDCFBBFE3999087EFFB8790F33025F53CEF6A987E5E48F31CE40D
          3C866FB0174E137225AA800AA41FB6C123E8863D9047C8441401C79176C8C570
          9CEB02CE5FC129AEDBA208B889EC7201BE6B7B8FF4705DB3A8008CF29161B884
          D9D940FB732446DBC1FF0EC02413B90D1BA104B3B781DFDE21DDB49D5930801B
          155205C59006DF8D7DA14BA11334820318F507CC57219FE03EDC8159C78C6B57
          F8B01EA7022E72519BF8E7DDE89EC1496E1C4A18D961A42BC9D37D01950AF8CA
          C92DA8562247BA33D642BA40DB8FBF3C3E8D76C93C64C366B80C23BF1D3969C2
          A831D07980EBF2641320C9FB6B41EA5312E0567E0FAC8934807E5A749A719A18
          6BA12155235886A88474A52A6039F211AE4516409F75C87AD0BFD734AE86DD51
          06EC43B61B3B4D4FC0433C2A52F588B430E7F0D81979007D5722AFE1BA2AED9F
          006357EE7EB8017DFF1280C506445535C37914C1563C62C1006D229310832350
          C50D9F43066421A5CE431BD339FAB6EA3705CCA1CD700F8E4183B1854CB3E22E
          3C80413A8C86082A445E1A5BD73AE3014F51D57D35FC8415A05D6A0C561B3B2B
          747C3176E31F73A82CFB017FCD7D957DBD8362023EC403B6A01DB0C9D812AD9A
          DE0B478DFD82D03EB103CA8C2D03AA33DA0FB21206306DEC57471DE64FE28DBF
          00A62826ADD4CA5ECF0000000049454E44AE426082}
        ImageId = 430080
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox1: TFHBox
        Left = 120
        Top = 0
        Width = 48
        Height = 56
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object FVBox1: TFVBox
        Left = 168
        Top = 0
        Width = 296
        Height = 56
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        DesignSize = (
          292
          52)
        object FVBox2: TFVBox
          Left = 0
          Top = 0
          Width = 185
          Height = 13
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object hBoxTipoCusto: TFHBox
          Left = 0
          Top = 14
          Width = 290
          Height = 27
          Anchors = []
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 0
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hbEnviarLinkCliente: TFHBox
            Left = 0
            Top = 0
            Width = 144
            Height = 23
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Color = clSilver
            Padding.Top = 2
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 0
            OnClick = hbEnviarLinkClienteClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox15: TFVBox
              Left = 0
              Top = 0
              Width = 16
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblCustoReposicao: TFLabel
              Left = 16
              Top = 0
              Width = 110
              Height = 14
              Caption = 'Enviar link ao cliente'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object FVBox109: TFVBox
              Left = 126
              Top = 0
              Width = 12
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object hbAprovarManual: TFHBox
            Left = 144
            Top = 0
            Width = 126
            Height = 23
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 2
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 1
            OnClick = hbAprovarManualClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox110: TFVBox
              Left = 0
              Top = 0
              Width = 16
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblCustoContabil: TFLabel
              Left = 16
              Top = 0
              Width = 84
              Height = 14
              Caption = 'Aprovar manual'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object FVBox111: TFVBox
              Left = 100
              Top = 0
              Width = 12
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
        end
        object FVBox3: TFVBox
          Left = 0
          Top = 42
          Width = 185
          Height = 13
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
      object FHBox2: TFHBox
        Left = 464
        Top = 0
        Width = 48
        Height = 56
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 4
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object pageControlAprovCliente: TFPageControl
      Left = 0
      Top = 62
      Width = 520
      Height = 493
      ActivePage = tabSheetEnviarLink
      Align = alClient
      TabOrder = 1
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsCard
      object tabSheetEnviarLink: TFTabsheet
        Caption = 'tabSheetEnviarLink'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox5: TFVBox
          Left = 0
          Top = 0
          Width = 512
          Height = 465
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox3: TFHBox
            Left = 0
            Top = 0
            Width = 296
            Height = 48
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox6: TFVBox
              Left = 0
              Top = 0
              Width = 185
              Height = 45
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel2: TFLabel
                Left = 0
                Top = 0
                Width = 94
                Height = 13
                Caption = 'Status Template'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FLabel3: TFLabel
                Left = 0
                Top = 14
                Width = 37
                Height = 13
                Caption = 'FLabel3'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                FieldName = 'STATUS_ZENVIA'
                Table = tbOrcarEnviarWhatsCliModel
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
          end
          object FLabel4: TFLabel
            Left = 0
            Top = 49
            Width = 116
            Height = 13
            Caption = 'Modelo Envio WhatsApp'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object FMemo1: TFMemo
            Left = 0
            Top = 63
            Width = 504
            Height = 333
            Align = alClient
            CharCase = ccNormal
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Lines.Strings = (
              'FMemo1')
            Maxlength = 0
            ParentFont = False
            ReadOnly = True
            TabOrder = 1
            FieldName = 'MODELO_MENSAGEM'
            Table = tbOrcarEnviarWhatsCliModel
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            Required = False
          end
        end
      end
      object tabSheetAprovarManual: TFTabsheet
        Caption = 'tabSheetAprovarManual'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 281
        ExplicitHeight = 165
        object FVBox4: TFVBox
          Left = 0
          Top = 0
          Width = 512
          Height = 465
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitLeft = 194
          ExplicitTop = 72
          ExplicitWidth = 185
          ExplicitHeight = 41
          object vBoxSignatureResponsavel: TFVBox
            Left = 0
            Top = 0
            Width = 518
            Height = 168
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stSingleLine
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 5
            Padding.Right = 5
            Padding.Bottom = 5
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel1: TFLabel
              Left = 0
              Top = 0
              Width = 240
              Height = 13
              Caption = 'Assinatura do respons'#225'vel(Obrigat'#243'rio se manual)'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object signatureResponsavel: TFSignature
              Left = 0
              Top = 14
              Width = 513
              Height = 60
              AutoWrap = False
              Caption = 'SignatureGroup'
              FlowStyle = fsTopBottomLeftRight
              TabOrder = 0
              BackgroundColor = clWhite
              PenColor = clBlack
              PenSize = 4
              SaveType = 'image/png'
              ToolbarVisible = False
              OnSave = signatureResponsavelSave
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              WOwner = FrInterno
              WOrigem = EhNone
            end
            object VB1imgSignatureResponsavelChk: TFVBox
              Left = 0
              Top = 75
              Width = 764
              Height = 53
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 9
              Padding.Left = 9
              Padding.Right = 9
              Padding.Bottom = 9
              ParentBackground = False
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object imgSignatureResponsavelChk: TFImage
                Left = 0
                Top = 0
                Width = 509
                Height = 30
                Stretch = False
                OnClick = imgSignatureResponsavelChkClick
                Table = tbAprovacaoCliente
                FieldName = 'ASSINATURA_RESPONSAVEL'
                HelpCaption = 'Clique para Assinar'
                WOwner = FrInterno
                WOrigem = EhNone
                BoxSize = 0
                GrayScaleOnDisable = False
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                Preview = False
              end
            end
            object imgSignatureResponsavelSemAssinatura: TFImage
              Left = 0
              Top = 129
              Width = 332
              Height = 30
              Stretch = False
              OnClick = imgSignatureResponsavelSemAssinaturaClick
              HelpCaption = 'Clique para Assinar'
              WOwner = FrInterno
              WOrigem = EhNone
              BoxSize = 0
              GrayScaleOnDisable = True
              Flex.Vflex = ftTrue
              Flex.Hflex = ftFalse
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              Preview = False
            end
          end
          object hBoxButtonAssinaturaResponsavel: TFHBox
            Left = 0
            Top = 169
            Width = 518
            Height = 44
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object btnDesfazerResponsavel: TFButton
              Left = 0
              Top = 0
              Width = 88
              Height = 38
              Caption = 'Desfazer'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 0
              OnClick = btnDesfazerResponsavelClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
                782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
                B9B0DBF5D30000000049454E44AE426082}
              ImageId = 0
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconClass = 'undo'
              IconReverseDirection = False
            end
            object btnLimparAssinaturaResponsavel: TFButton
              Left = 88
              Top = 0
              Width = 84
              Height = 38
              Caption = 'Limpar'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 1
              OnClick = btnLimparAssinaturaResponsavelClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
                782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
                B9B0DBF5D30000000049454E44AE426082}
              ImageId = 0
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconClass = 'close'
              IconReverseDirection = False
            end
            object btnCancelarResponsavel: TFButton
              Left = 172
              Top = 0
              Width = 84
              Height = 38
              Caption = 'Cancelar'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 2
              OnClick = btnCancelarResponsavelClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
                782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
                B9B0DBF5D30000000049454E44AE426082}
              ImageId = 0
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconClass = 'trash'
              IconReverseDirection = False
            end
          end
          object vBoxSignatureCliente: TFVBox
            Left = 0
            Top = 214
            Width = 518
            Height = 168
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stSingleLine
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 5
            Padding.Right = 5
            Padding.Bottom = 5
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object lblAssinaturaCliente: TFLabel
              Left = 0
              Top = 0
              Width = 136
              Height = 13
              Caption = 'Assinatura Cliente(Opcional)'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object signatureCliente: TFSignature
              Left = 0
              Top = 14
              Width = 513
              Height = 60
              AutoWrap = False
              Caption = 'SignatureGroup'
              FlowStyle = fsTopBottomLeftRight
              TabOrder = 0
              BackgroundColor = clWhite
              PenColor = clBlack
              PenSize = 4
              SaveType = 'image/png'
              ToolbarVisible = False
              OnSave = signatureClienteSave
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              WOwner = FrInterno
              WOrigem = EhNone
            end
            object VB1imgSignatureClienteChk: TFVBox
              Left = 0
              Top = 75
              Width = 512
              Height = 53
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 9
              Padding.Left = 9
              Padding.Right = 9
              Padding.Bottom = 9
              ParentBackground = False
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object imgSignatureClienteChk: TFImage
                Left = 0
                Top = 0
                Width = 509
                Height = 30
                Stretch = False
                OnClick = imgSignatureClienteChkClick
                Table = tbAprovacaoCliente
                FieldName = 'ASSINATURA_CLIENTE'
                HelpCaption = 'Clique para Assinar'
                WOwner = FrInterno
                WOrigem = EhNone
                BoxSize = 0
                GrayScaleOnDisable = False
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                Preview = False
              end
            end
            object imgSignatureClienteSemAssinatura: TFImage
              Left = 0
              Top = 129
              Width = 332
              Height = 30
              Stretch = False
              OnClick = imgSignatureClienteSemAssinaturaClick
              HelpCaption = 'Clique para Assinar'
              WOwner = FrInterno
              WOrigem = EhNone
              BoxSize = 0
              GrayScaleOnDisable = True
              Flex.Vflex = ftTrue
              Flex.Hflex = ftFalse
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              Preview = False
            end
          end
          object hBoxButtonAssinaturaCliente: TFHBox
            Left = 0
            Top = 383
            Width = 517
            Height = 44
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object btnDesfazerCliente: TFButton
              Left = 0
              Top = 0
              Width = 88
              Height = 38
              Caption = 'Desfazer'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 0
              OnClick = btnDesfazeClienterClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
                782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
                B9B0DBF5D30000000049454E44AE426082}
              ImageId = 0
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconClass = 'undo'
              IconReverseDirection = False
            end
            object btnLimparAssinaturaCliente: TFButton
              Left = 88
              Top = 0
              Width = 84
              Height = 38
              Caption = 'Limpar'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 1
              OnClick = btnLimparAssinaturaClienteClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
                782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
                B9B0DBF5D30000000049454E44AE426082}
              ImageId = 0
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconClass = 'close'
              IconReverseDirection = False
            end
            object btnCancelarCliente: TFButton
              Left = 172
              Top = 0
              Width = 84
              Height = 38
              Caption = 'Cancelar'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 2
              OnClick = btnCancelarClienteClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
                782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
                B9B0DBF5D30000000049454E44AE426082}
              ImageId = 0
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconClass = 'trash'
              IconReverseDirection = False
            end
          end
        end
      end
    end
  end
  object tbAprovacaoCliente: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA_RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura Respons'#225'vel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_APROVACAO_CLIENTE'
    Cursor = 'OS_APROVACAO_CLIENTE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '296049;29601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOrcarEnviarWhatsCliModel: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Template'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO_MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Mensagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_ZENVIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Zenvia'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORCAR_ENVIAR_WHATS_CLI_MODEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '296049;29602'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
