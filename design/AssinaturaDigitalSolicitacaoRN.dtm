object AssinaturaDigitalSolicitacaoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '45506'
  Left = 44
  Top = 163
  Height = 299
  Width = 442
  object tbSolicitacoesAssinaturas: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{2A2587DC-B78A-440E-938D-4F2FA40CFCA2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SIGNATARIO_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Signatario Tipo'
        GUID = '{650BD8A8-CE77-4AC9-B0E2-51C7BCA3F9A9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SIGNATARIO_COD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Signatario C'#243'd.'
        GUID = '{5F9810FE-5FC4-4F3A-92EC-1947DCCE40E4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        GUID = '{521CDF0E-67B8-49A5-830C-CF2E3AA30DF1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone'
        GUID = '{4B30F91B-B549-4691-B684-3F35989BF260}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Envelope'
        GUID = '{9738732B-F6CC-4BC4-A6B7-22785B3AFD0B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAG_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tag Documento'
        GUID = '{16B17723-D764-4096-884B-EF738A4FB28C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Codigo'
        GUID = '{EA08D011-BC8F-4D0C-AA90-558E153B1911}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        GUID = '{69B67891-84B8-457E-9808-4AC319B89F8A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QR_CODE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Qr Code'
        GUID = '{C90BFA5D-70FB-4042-80E7-A293E416AB41}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Assinatura'
        GUID = '{9475E6AE-0620-42A1-BF48-922A7943E9D9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_ANDAMENTO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Andamento Assinatura'
        GUID = '{2F260868-01A1-48C6-9649-1D16D22D112A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{6D4DE168-14F6-4C92-9BCE-D316EC16BE96}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{332186E0-1D99-469D-B9F5-B967C5457C05}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_DESTINATARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Destinatario'
        GUID = '{AA74A0C2-3864-4C8C-AF51-CDD6D69AA41A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EDITAR'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'EDITAR'
        GUID = '{8EDEAF2D-DCD1-41EE-AEE4-C6F2BD64FE38}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VER'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'VER'
        GUID = '{EAF74170-EE14-4AA2-9F71-A71B78EE4E03}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINAR'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'ASSINAR'
        GUID = '{11EF3E48-BC8F-447D-A428-B904016FC87E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SOLICITACOES_ASSINATURAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45501'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNbsapiEnvelopeFilaResumo: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{6E66EFE6-A8D9-4412-8E3C-5B14B478892A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Codigo'
        GUID = '{99B10231-F811-4BA7-9361-52FF37311B54}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{EED49209-ACBD-4620-98EF-790AC7512050}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSUNTO_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Assunto Envelope'
        GUID = '{A366717F-9228-48A1-B95B-CAA57AD1A5DC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Nbs'
        GUID = '{9235FA4C-A934-4C85-9E97-D131F10AD4FC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Status'
        GUID = '{087A6973-8931-4C12-B728-718661ECF8EA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATUALIZAR_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Atualizar Status'
        GUID = '{2CDCDF03-40A3-45B6-89AC-88F6A69B9B58}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BUSCAR_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Buscar Documento'
        GUID = '{402303AB-F876-49FD-8CD1-752C38F82F87}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PACOTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pacote'
        GUID = '{B9DD0EE1-FBCC-4F6B-BF32-6692544F16F7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRESENCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Presencial'
        GUID = '{C05C3F49-04C0-448E-8A7D-64B20E746D75}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_EXTERNAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status External'
        GUID = '{91D60CE0-493C-4143-9B00-52C2A38E30D0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Documento'
        GUID = '{C3E0F650-3C46-4354-B585-915B6F31383C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOC_BASE64'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Base64'
        GUID = '{A4D1E366-3214-4F12-A8C2-86182B494A99}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINK_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Link Assinatura'
        GUID = '{699CA5AC-1A7E-4467-B8D4-52DC1F6270E4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QR_CODE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Qr Code'
        GUID = '{3CCC98A4-15DC-492F-BFB7-FFA26CBA262A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_OUVINTE_ASS_DIG'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observadores'
        GUID = '{261ECCD0-9184-4406-BF5A-838DA578302C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'NBSAPI_ENVELOPE_FILA_RESUMO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45503'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbAssinaturaDigital: TFTable
    FieldDefs = <
      item
        Name = 'ID_ASSINATURA_DIGITAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Assinatura Digital'
        GUID = '{CE1FED73-89C7-4D30-AADE-03A394DF02D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{A24ED07F-885E-4DF5-ACB1-56A9EDE7D1B1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{0D4CCC02-12C1-4645-AD3A-44C887ACD3AC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{BBE0CEA7-FCEE-4210-BD64-60336AE9E1C2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Codigo'
        GUID = '{FBD94A1C-A082-410E-A29B-CEC30484BEDC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_ASSINATURA_DIGITAL'
    Cursor = 'CRM_ASSINATURA_DIGITAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45504'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNbsapiDocumentos: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{618DF827-5487-49F3-91A3-3E033C51FD21}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Envelope'
        GUID = '{F2520F37-BA41-42A9-B28D-8E3106F4045E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOC_BASE64'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Base64'
        GUID = '{B98DE288-1113-47F4-989B-43A6672A3CD1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Documento'
        GUID = '{2FFF6504-6E2A-4E97-A44F-96021FB1A204}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXTENSAO_ARQ_DOC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Extens'#227'o Arq Documento'
        GUID = '{11960D6F-2768-4B1D-AE53-E8187106CF18}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia'
        GUID = '{033D70CF-77CD-4FD9-9ED9-736D78D185B2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_DOC_EXTERNAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Documento External'
        GUID = '{E01E4C29-A477-4CE4-9A12-DE184569544E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROCESSADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Processado'
        GUID = '{9594BB04-5D32-4894-AB38-81567DF89A0E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBSAPI_DOCUMENTOS'
    Cursor = 'NBSAPI_DOCUMENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45505'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbAssinaturaDigitalDoc: TFTable
    FieldDefs = <
      item
        Name = 'ID_ASSINATURA_DIGITAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Assinatura Digital'
        GUID = '{6847A650-0634-4841-AA06-1ACED8450CFD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENTO_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Item'
        GUID = '{C48FA677-C025-48D0-941C-4D69C852A7C3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{A05322D9-0ADC-4915-8BFE-1E6E2CBE0952}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RELATORIO_NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Relatorio Nome'
        GUID = '{D2006B6F-4196-4FD8-AD3C-00AB52A3F51E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{BC68D07D-9AB6-433D-80A7-A22A5F67AF3C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_ASSINATURA_DIGITAL_DOC'
    Cursor = 'CRM_ASSINATURA_DIGITAL_DOC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45506'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNbsapiDocumentosAssinados: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{DA4A7929-5E6B-4EC3-9566-5CB2E20D7A04}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENVELOPE_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Envelope Id.'
        GUID = '{F21E3F96-3C46-4B1B-95E2-82704E721350}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENT_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Document Id.'
        GUID = '{A05E2189-C264-477A-A72B-110417E15EFC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Documento'
        GUID = '{AB572DB3-F95B-48BF-989D-D174C4B427CD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Assinatura'
        GUID = '{C98CCA99-5B9A-4CD3-AAA2-84B75B22BACC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENTO_ASSINADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Assinado'
        GUID = '{2661BC01-43AF-4F2D-A54B-DC9C060F86D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBSAPI_DOCUMENTOS_ASSINADOS'
    Cursor = 'NBSAPI_DOCUMENTOS_ASSINADOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45507'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNbsapiEnvelopeFilaRank: TFTable
    FieldDefs = <
      item
        Name = 'ID_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Envelope'
        GUID = '{16B1BF26-72AC-433C-8058-6F098703B09C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA_MAIS_RECENTE_RANK'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura Mais Recente Rank'
        GUID = '{436D7AE7-4146-41F0-B2F8-BD2DF84EBD62}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'NBSAPI_ENVELOPE_FILA_RANK'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;47408'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
