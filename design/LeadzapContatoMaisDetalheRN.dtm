object LeadzapContatoMaisDetalheRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '494010'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbQueryDisparoLzDetailGrid: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{0E9B885E-F7B2-4F05-A3BD-3E770F4CD352}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{319E0582-E59C-4DEC-9036-3C32C18B0DDC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMP_NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Emp Nome'
        GUID = '{360E2B83-870B-4D39-A248-97CEDCA0B03F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular Disparo'
        GUID = '{2B7B8F58-3855-4610-BFED-85432085CD8B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_LEADZAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Leadzap'
        GUID = '{E3EA0C73-A7A7-4758-A164-5B2BB5F40571}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        GUID = '{B141AFC2-DDED-40F8-95FA-0C798922CB58}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{900BAC81-75F1-4677-AA71-6E0E6943A869}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{E7D35BFE-9C90-4920-B039-7C5EE82A20AF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Tipo Evento'
        GUID = '{066E5C73-1B11-44EA-B07B-4BC5A1624439}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Disparo'
        GUID = '{2D1DC984-B9D1-43E9-AAEC-6932C8D4F0CE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato'
        GUID = '{1629793F-5EFF-4BA9-BF47-04F570617575}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DDD_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo Cel'
        GUID = '{DB7602DD-8BC3-4ED7-A920-DB7DF4356F73}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Cel'
        GUID = '{3BDFB294-0EF7-4EA1-93FD-2F1D79FA47BE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'QUERY_DISPARO_LZ_DETAIL_GRID'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '494010;49401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
