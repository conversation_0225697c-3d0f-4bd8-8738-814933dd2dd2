object FrmPerfilTabPreco: TFForm
  Left = 44
  Top = 158
  ActiveControl = FVBox1
  Caption = 'Alterar Divis'#227'o para o Usu'#225'rio Selecionado'
  ClientHeight = 295
  ClientWidth = 515
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '310039'
  ShortcutKeys = <>
  InterfaceRN = 'PerfilTabPrecoRN'
  Access = False
  ChangedProp = 
    'FrmPerfilTabPreco.Width;'#13#10'FrmPerfilTabPreco.Caption;'#13#10#13#10'FrmPerfi' +
    'lTabPreco.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 515
    Height = 295
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 3
    Padding.Right = 3
    Padding.Bottom = 3
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitWidth = 450
    object hboxEditarLead: TFHBox
      Left = 0
      Top = 0
      Width = 508
      Height = 61
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 3
      Padding.Left = 2
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnSalvar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Salvar Lead'
        Align = alLeft
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
          C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
          D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
          1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
          D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
          9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
          A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
          BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
        ImageId = 700080
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FVBox4: TFVBox
        Left = 60
        Top = 0
        Width = 5
        Height = 55
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object gridTabPreco: TFGrid
      Left = 0
      Top = 62
      Width = 507
      Height = 224
      Align = alClient
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Table = tbPerfilTabPreco
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Paging.Enabled = False
      Paging.PageSize = 0
      Paging.DbPaging = False
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = True
      MultiSelection = False
      Grouping.Enabled = False
      Grouping.Expanded = False
      Grouping.ShowFooter = False
      Crosstab.Enabled = False
      Crosstab.GroupType = cgtConcat
      EnablePopup = False
      WOwner = FrInterno
      WOrigem = EhNone
      EditionEnabled = False
      AuxColumnHeaders = <>
      NoBorder = False
      ActionButtons.BtnAccept = False
      ActionButtons.BtnView = False
      ActionButtons.BtnEdit = False
      ActionButtons.BtnDelete = False
      ActionButtons.BtnInLineEdit = False
      CustomActionButtons = <>
      Columns = <
        item
          Expanded = False
          FieldName = 'DESCRICAO'
          Font = <>
          Title.Caption = 'Divis'#227'o empresa'
          Width = 150
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{4353B580-958A-4DA5-9B5D-C2D68F1BD774}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end
        item
          Expanded = False
          FieldName = 'TAB_PRECO'
          Font = <>
          Title.Caption = 'Tabela Pre'#231'o'
          Width = 200
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{067D1AA0-13A4-425A-A2A1-529A0EE8A253}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end>
    end
  end
  object tbPerfilTabPreco: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAB_PRECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PERFIL_TAB_PRECO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310039;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
