object FrmTipoDocumento: TFForm
  Left = 44
  Top = 163
  ActiveControl = vboxTipoDocumento
  Caption = 'Tipos Documento'
  ClientHeight = 566
  ClientWidth = 915
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '43702'
  ShortcutKeys = <>
  InterfaceRN = 'TipoDocumentoRN'
  Access = False
  ChangedProp = 
    'FrmTipoDocumento.Width;'#13#10'FrmTipoDocumento.Height;'#13#10#13#10'FrmTipoDocu' +
    'mento.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxTipoDocumento: TFVBox
    Left = 0
    Top = 0
    Width = 915
    Height = 566
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox6: TFHBox
      Left = 0
      Top = 0
      Width = 981
      Height = 68
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Color = 16514043
      Padding.Top = 5
      Padding.Left = 2
      Padding.Right = 0
      Padding.Bottom = 5
      ParentBackground = False
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox1: TFHBox
        Left = 0
        Top = 0
        Width = 908
        Height = 60
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 2
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        ParentBackground = False
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object btnConsultar: TFButton
          Left = 0
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Executa Pesquisa (CRTL+ 1)'
          Caption = 'Pesquisar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 0
          OnClick = btnConsultarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000004BD4944415478DA9D955948636714C7CF4D62E292C47D19199BAB1444
            A6A363C1818A2B8856EA8E4A952A06D427A14F3EF4D127518A880BB850B562A5
            3EF8A80F76AC11DC1DC4655C06B154A341E3BEC6254BCFF932B9F5AA38A51F5C
            6EEE77BFFBFDBEF33FFF73C2D96C36E8E9E991A5A6A6FEE1E4E49400FF614824
            12B8BABA5A191E1E7E5D5050607D6E2D4780C1C1C1A0F8F8789DABAB6B083D7F
            6ED09ACBCB4B30180CBD333333A5C5C5C5A66701FDFDFD7C4A4A8A4EA9546AAC
            56EB6701777777603299E0FAFA1A767777FB2A2A2A7E181B1B333F0B484E4ED6
            A954AA470092E3E1B8BDBD85A3A323F0F4F4848B8B0B585F5FEF89898929319B
            CD9627017D7D7D3CE640A756AB4500DA9C9E777676D86965322928952AF0F2F2
            02A3D108EEEEEE80B2C2F1F1316C6C6CFC3E35355582D1DC3C02F4F6F6F26969
            690280E338766D6EFE0D6B6B1F715325787878B03993E91AE47227C068D90114
            0A05A039E0F0F010B6B6B67A878686CA6A6A6AAE440074119F9E9EAEC3133100
            7DB8BABA0A7ABD1EA2A2A284CD68D0FBD3D353D8DBDB03373737364F92C96432
            72161E682D373B3BBB5F04E8EEEEE633323274784A0D4DD2C70B0B8B909090C0
            64A1A48AC2E6242CC9676767A056AB00B567CEB2582CB0B2B2529A9898F88B08
            D0D5D5C5676666EA30690C303E3E0E1A0D0F010101F8A15D32FAF85F07DBD8EF
            F3F30BA0C028428A8CF2B4B0B0508207FB5504E8ECEC64004C9E864EF5E1C312
            BC79F3F5275938B0E7050400DDE9D5CDCD0DD6C305F8FBFB83542A65CF5817DA
            B8B8B82E11A0A3A38301BCBDBD35240F260B5EBDFA0A37E2D8C60F87D56A63F3
            24CDC181115EBC08C0643B33C0ECECAC168B560C686F6FE773727204805EBF0D
            21215F3269E8641209774F1A1B6E6CD79B40C7C7461601D995724511600EC480
            B6B63601400E595D5D83D0D030269154CA319908466BE9A28D2D167BBD6C6DFD
            05C1C1C1E0E2E2C200580BDAA4A42431A0A5A58501C84564B7F7EF67D1F75E98
            E860FCE89639C901A08DCDE63B94C405DB840125DA87F0F0701611D975626242
            8B452B06343737F3B9B9B9AC0EE8D494B8999959CCC36B78F93250482C398A0A
            9D6C6A306C836EE41D7C9BFA1DF8FAFAB21A2000F6242D16AD18D0D0D0C0E7E7
            E70B952C972BB0320F606969097C7CBC2128E80BB4A29A45717E7E0EDBDB7A98
            9E9E86136C116161A110F5F61BCC831F6B19A3A3A35A54E331202F2F4F879529
            F422B95CCE4E849509FBFBFB9FA2B0319DA9C991EEF3F3F3D0DADA0A111111A0
            D56A51520D0C0C0C68510D31A0A9A989E5009D2000689E7A8CA305D04573944C
            2A2C85428E4DD000D5D5D530323202E81C282A2A026767E7EF232323FB4480FA
            FAFA4711382034C8AA8E06E8680974276B52CFAAADAD85C9C949888E8EA64EFB
            23E6B44104A8ABAB7B1260EF3B1C3CF72F47D12C2F2F4355551559140203038D
            58800598BF3F05406363239F9595A5C3F034CF6DC63D51D63447AD7C7171112A
            2B2B616E6E8EF2777572729285397CC700480F292F2F9F469BFA383AE34399EE
            3F3F7CEF806015435959193306CAB889EFDE32406C6CACAAB0B0F0673F3FBF30
            94E89664B257ACF55EF55A858B06E5C171C7F7368448D11437F8E7F51BE6A314
            FBD23CAEFD89739C0617C8F0E64CBD0CFEDF20FD2CB8DF354AED850630E1FFB6
            E91F70B7FB1897F803840000000049454E44AE426082}
          ImageId = 13
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = True
          IconReverseDirection = False
        end
        object btnNovo: TFButton
          Left = 65
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Inclui um Novo Registro  (CRTL+ 2)'
          Caption = 'Novo'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 1
          OnClick = btnNovoClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F80000046A4944415478DA8D967F681B6518C7BFEF5DDAA5AE6E36D58EC2DA4E
            BA5A1D42BBE93A990A56F6873237D81036D0BF1404C76C374BA508ADCC8D29FE
            530A8AEC0FC58242152B4341879B0C6BE956D0DDADBF67D35FA95996D4A5499A
            E4EE92BBBCBEEF2517EFB254FB1CC7BD6FDE7B9FCFF3E37D9E0B414E4856446C
            440420B33FE3C64B78C3B5C5F54AADA7761757E10BFB26F5B8FE1516F1293E86
            62EAB56D7BB4BBBBFB2CA5D4A98C10C74B5C7C311FFACBFBF71E78EE40DDF1C6
            E3B85F2CE79BC89A11C7C0AD017A65ECCA14D238882E2CD9F736AFAE46244555
            210884E63CE29ED9C750D20A7DFEA75652595B490F3DFC027119693CE56E3501
            D7D5ABD0C512FAC3C22532228F4C62062D0E402412BDA16A1C20C05268BFB9F4
            C97DF820781E2F3F7E18D56E116E4DC3B18A77CDB5AF57CF43DDB40901D5C0B7
            13DF43FB45EB7200A2D198A4A534D36AA18807462683677F7C86CE5578C9139B
            EBE80EB748B6A674BC5EF3396571249FF95E43B4D445175583FC9158827A4D1B
            7300626B6B528A594404A16888946412DB2F6DA7C6E634E9F3F4D23D35BB89C8
            343C56DD6486683A70130605BDB12C91F6F069646248D9014D6B1C904EE74322
            1484281A8DA26EA80E99521D7D15BDD85DD30C0ED855DD6C2A980AC81C006959
            46FB2A03A8D01D80783C2E738060599DCD45DE0366009EFE6D3F9D77CF916DCC
            D29DF7816CCB005D0D574D0F3E9C6D455000F52641824C339DC6AC139048C869
            0B209859F817C6C67CAD73B8935E485F20656CFF8E7206601A3E7A240B78E74F
            0660E0C5388859045FE0130720914848BAAE6743C2AC178A9C22694AC2D1DB47
            11D00228D3010F2BBAF71A7BCDB533B74E23CC3C525C6C12C66D9CC461072099
            4CCA268025397F8A6C632EAAAAD2C1E141D22576D1A01A2424068869981E1825
            CCAD2D6C1C43109DECBA834127405124230BC85B6FD544AE9D98CF582C86A1D1
            21F427FA315A368A10BBB854B16B9FB20F175FBD78121ABEE4AF3A008AA2C886
            6138AC66009A6B5516C49CA75229EAF7FBC9FCFC3CC2E1B0F99BC7E3417D7D3D
            6D686868612DE7F7C25ED4C4DA849CE1005E68B9245BA7C8E6813537956658F1
            B1DB1C8BA2681AE472B9F6B0DFE47B002CBE32AF56C106B014167A603D7973A4
            59C91921D0D2D29275009A26710F0A7B91657D612E7838F9A160E132C75C4457
            092A1ED85A1CA0699ACC5D261BF08029A6CC63C2AB3B1289507640080B0D8647
            AE5D7FBBBDED0803DE591720D87A911D60CDD966C20E040D8556CC24B339ADAA
            7A884C4C4ECCB4BDD5768841BDF9CF490140CAB098162B30FB98BD87BB77C3F0
            7ABDA87CB0123BEBEB71F9F2CFCB6F9E38F1A2FF2FFF944DE7BD1EF0A4157C64
            1CA78867931524F12D2FD3E9E919D2B2F7498C8F8FF9DB4F9D3AE89DF5DE4481
            3801A9944C6D39B0626E07F023C99A22999B9BA7C15088A84A62AEA3A3E3C8C2
            C2E2388A482140CA01D60D11CF11036061C987A1A15FE573EF9F39B6B2F2F72C
            D6113BA0B1A7A7E75C7E8190A21B780853BA81403014FDEE9B81B3AC852FE13F
            84148C37F6B725C762B7F17F2FFD034C50719467FC49DB0000000049454E44AE
            426082}
          ImageId = 6
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = True
          IconReverseDirection = False
        end
        object btnAlterar: TFButton
          Left = 130
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Altera o Registro Selecionado  (CRTL+ 3)'
          Caption = 'Alterar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 2
          OnClick = btnAlterarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000005094944415478DAAD956B6C536518C79FE7F4D0DDC82ED95A262BB0AC
            026EC16D201035121308514CCC844020881335442E11818D6BD84C4087CB3E90
            4C037E21A21F34442111A209E3E218868B894047071B3BDD4AB76EACDDADD773
            DAD39EC7F7F4E65A36C2079FE4DCCFFBFF3DB7F77D11FE476B5DB76259B63FFD
            3CA728D7291CDEB9B0A5C58AF18F1A8D465F5757F70511258F42047C0EF190B7
            1B372F685B1FFAAD38CF67B2A35796074196DF9838D63836362E8892041C8784
            5163FA49F72A8C20F214BDAA03C3012729E6B53830E8849AC634DA6A1BC5E93E
            1F8478FE4212607CDCD52D0554000709C189473CA2A86EE459098B106CDF00A2
            DB0A1F7CA5815E7B0066F276D89F0964EAC4F3490097CB2D04828188D7DC2411
            40FC59D56657A0308AE6ADA011EFD2278D1C9A1E89A048763ABD5F0E8B226F5A
            7B48AE4A02B83D1E211860008E9B3C459100309122FFA32398215E81DA131C5D
            BA3982A2D701273EF75361BEC6B6FA90BC667084BA26024A3C2A40961329E126
            4953BCE852DF0F90E6FA198EFF9A09A7CF09E0F7B9E068F528AC7CAD0056EE74
            7CD865A38BECB7B12480D7EBB5A8002EEE75B4164F1539E8BC82BCE35B3A7B43
            87479A6F422020D1F6779EE0A6B73361A4A0892A977DBA9CE9FDC30E6F32C0E7
            B3C8710017A9C27FB01820E4BE4FD0DF88B78599B4A3BE05032CA5554B07A876
            830687B26A000B56D3A2458B5E657A77D4EE4D02F87C3E21140A453D65DEA7A6
            88151094C70D2038F2E1A37D17C1EDF6C2EBF39CD0B04586E1B48DE09E5E0D85
            8585307FFEFC054CEF41BCD31200BFDF6F89005891135D14BB57641784ACC760
            D89341D57BAFAA3D4F65063736EFF0823F633959B9EDA88ACF993387F47AFD3C
            455184A701A22884A38084F7EA9C002508416B13484184CDFB5BA14BB083215F
            84933B4781CF29877BFE5DA0D3BF002525259108D2D3D3E73280E52980288A96
            70389C1C010205ACC7D93904DBEBAFC1AD3B56CA9B2EE3C9CF86495F68C06BCE
            5D9057308B8C4623161717435A5A1AF13C3F0540922C8A0A50275AACC801DB77
            C4830B0F345E873F5A7B2143AB50F3362796CDCDA1DFADDB3033D7A87A4EA5A5
            A5989595A5EA4C0D901820AC2891CE510192FD0C68C55BD474CA8C3F9E1F60EF
            098E7D3C4ACB17F378AEB39A28A31459CEA1A2A282727373631173A4D54E9B02
            1008086A04F1B568C0FC13FCD9F23D349C1A501D83DAB52E787F6518CE9AAB60
            9C2AA1A8A8082A2B2B41A7D301138CAECAFC34C8CBCD991CC07ADAA2FEA8A6C8
            EF7A8243D656103AFFA6A3CD5771D52B63B06F830417CC6F92E05EAA760C9597
            97A34EAF07AFC743AC4190A506FEBA71B375EF9EDDEF058341D7940016010D0A
            6DE8B0B68067E401897E172E9CD5079DC3F3C034B28A66CF9E8D068381B2B3B3
            B1BFDF0EAC31586BEAB0A3C3DC5E5353BBCAE9740E24B6931480A0B00D476DD1
            53DF1C80B2223658F683A29D0B9A9C25A037BC0CFA193340ABD5B249E6819E9E
            1EC82FC887178D46B87CF99275F79E3D2B846EA13769BF4A8D40DDD11E5BADB4
            7ACD1ADCBC7E196CDCB4857267BC84ACFDD45D0FD8776213126D7D7DF4F06127
            2E5DB218CCE6FBB683070FBE6532B577A6EE74C98060D0422C45A67B7749C3F3
            5851519958ECA27B0DAAC524B628A2C5D243430E074A92BFABEEF0E1773B3A1E
            74C324960A10285AE4E4253A261E03000340EF631BB4B55DBBDDD4F8F53A5687
            7E98C226028AEAEBEBBF8CBF8B0BA79A9AC260280C83438EF173BF9C69F0B8DD
            0E7886A5AA70F0FC46B1E399F62F6A8BA82D8608FAC10000000049454E44AE42
            6082}
          ImageId = 7
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = True
          IconReverseDirection = False
        end
        object btnExcluir: TFButton
          Left = 195
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Exclui o Registro Selecionado  (CRTL+ 4)'
          Caption = 'Excluir'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 3
          Visible = False
          OnClick = btnExcluirClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F80000040C4944415478DADD955D4C5B6518C79FF79C7EAC2D1F1DED6A0714DB
            B51DA5C810506250468A0A9A4CB3E885268652A98BD178B3A98BD311B3B8442F
            8CEECEEC032C5DA66E5113B338B31AD814CDB61841663F28E3F0D1A1B44821DB
            282DB5F0FA9C0354C533D4446FF626BDE8F33ECFFFF77CF52D81FFF9905B13F0
            31CB166B1A1A7C098EFB76361279B685D22531BF0E0046ABD31DCEAFA9B9377E
            FE7CD3E3C9E4C4DF023E0230B0003DF6E75A2CB14BDFC3745FD09300703F0DF0
            27C86114DF880C4D85D555F8A083FADF39C2A143E31300576F0A380150CC009C
            2B7DD4612EBBDB417ECD53C08523EFD1E9C048D72C425E58811C42F14DBC7899
            C955E772838C4869E8876FC8E007A787D1C1F114C08428C00B10B438AA6D96EA
            06A0999584F50570E9B8078682A3DE30423268AA44717399D159FB244ACDCC09
            6E4C6E0E8C0CF541F8D417834E00BB28E07D80A31B8DBA672A1F6AA68B2976F5
            8EB2C53AE2FBE414044363C7794385EDF696C69D8FD1A5E86CD6476E2A24FD27
            3F847870F418B6739728E02D4C4483D9151837B5DAB7D793F475A10A4AD18F31
            6D86D3673E0706BFEE687E982C45A6E86ABC5CAFA6C1733D241E8E786E60957B
            FE30AFBF0CF96016A26DB5DD55030BF1B4605FC48FC45228E016B94904F15406
            94E64208747F09712EDAC58BEF5DB30CA26B7A002105FC108D1A57A9DD4653D1
            A4E02744124255C5C504144ACAE46748A8FB6B981E8E797012EE7D6BC46F0AE0
            4FFB32A4536BD4386D362BCC47AE0391CB8162D689F171906A59984CA6213E3E
            EB45F1B67611F17501FB119087001D02AC1B242431185BBD127AAFB2E9E99554
            864C8D4D772511F0FABF01BCB22CDE719B51EBAA5248E9426892D09539085BC5
            378ADF1C7B11E9C72A62A3BF7810E27EE39FB4E8657EA5515C6FD2B9EA1532C8
            0427848CF9C84479091F4095810861562A91A0AD3795A151EE67BE12F79BEB0D
            798F42C6E426D31D9BCD45ADCD0A29807F2CDB931BDBACB03B9110420EA95490
            7B79E8F7C06D5BE12C56121D1EEF526ED9E2DECB71E26BFA5A91F69841AD76EF
            94B0543210CEFE88AEDD594E5E44F16B57C6BC7CA4DA6A72BE9BA3A279FDFEAC
            4F067DCECA3600A3D674B6F87C6E51C0FE125DB0B6B2A2ACF1423F85E919E16E
            AEAA82BE944890A9A1112FF6BE2DB3BCAE9D855BCD2D6FE7A888AAEFF272915A
            0DB9D850073F7E3710DA1D89883F15ED4AB9819D5FE8B9E7FEEDE6FA8100CC97
            14C1C1C43C70E1613EF3B6332BFD6DC2394910622BB538F7A994A08CFC04BD95
            77C0C5EEAF38562673B4A7D3E28F1D7F70DD0C68ECA97EA4C9E21F19077F20DC
            859D6FFB6CCDF07620241F2155F6D2D6BA8A72EA3BF92987B36A3CB0DE73BD7A
            5E35E98D542EF5A526677AE373C95DDEC525D11D6F6559A6D660386A379BEF0B
            F9FD0F3C1F8B5D5DEB738BFE27FF97E7376E298F288718CA1E0000000049454E
            44AE426082}
          ImageId = 8
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = True
          IconReverseDirection = False
        end
        object btnSalvar: TFButton
          Left = 260
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Salvar  (CRTL+ 5)'
          Caption = 'Salvar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 4
          OnClick = btnSalvarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934
            31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2
            D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE
            6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5
            6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013
            090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF
            349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E
            8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871
            175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39
            FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622
            F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673
            BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E
            01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146
            0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219
            506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6
            0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991
            CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313
            9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0
            39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D
            AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7
            C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743
            FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE
            E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660
            474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF
            D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61
            33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E
            0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE
            F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF
            921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5
            A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E
            9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163
            8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09
            C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C
            D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E
            23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA
            FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E
            9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1
            5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A
            25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2
            FF02B065C443D9FE4B070000000049454E44AE426082}
          ImageId = 4
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnCancelar: TFButton
          Left = 325
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Cancela as Altera'#231#245'es Correntes  (CRTL+ 6)'
          Caption = 'Cancelar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 5
          OnClick = btnCancelarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000004EF4944415478DAAD566B4C1C55143E7766F6BD145020744BA98F1608
            A820DA508BB42A84B62A264D6A24D158E323C646230D4D7D24901AA8466BD336
            1A9AF84B1B7F580BD49ADA6A0B518A3450915D0BE9F2461E5BDEEC93DD9DDD9D
            39DE997D53B49A7893D9993B73EFF9CEF79DC75D02A141828385FF6120A2402F
            94EDC6BCCFA9A9A9A90BBD8F0E42E216DD6E5027A1BEBEBE4614C5FE95000556
            ABCDE8F17A8161088618491B629FA50D28D30DDD4346306C4BA150A05EAF2FA4
            00A65B006C367B8F9797001888188CBDC28C827623F358431CC7814EA75B1DC0
            6E7718791F2F7BCDACC200C273695FF81E048930E05816B55AEDEA000EA7D3E8
            E32900C3AC2E5150E38844244C254622960268349A28C08F8D1FAA76EE799FA7
            4BF31D0E0AE0F7CB12D8E60660B4F7023817478165782A9B0F44810351544342
            F206D8545801A9190510C5080E495EB55A1D05B8D6F2F96C47EB570D750DC673
            13169B716176047A5A8E6362A28BE43D940177A45136E00AC551DAA143EB0221
            E6EE29B42D2A49D1930721393D3BC24662A852A9A200674F96FAD3D6A6B2478E
            5F3A7DF8D32F2BCD9D0DB07D470E26ACF1DC427FC51CDDCB6AD27E6100EECADF
            8BB95BF64400944A6514A0E9B392C0CE8A3CE6BAC9014EA7171E29310043FCE1
            8AF91799CFC1CF1747213DFB79C82FA994258B03683C511C28DF95CBCA9910AD
            29D94BBFC30A9EA9110CCCCF11F00BC0A8B4A04C31A026732361944C8419028B
            DF37F79392678F41C6BD05486B210A70E6D816A1AC3C8F50B783CB25B55104E7
            C4EFE0991905AF4B8489291A0587086A4E80BB937D90A055803E771BA80DEBE9
            6251F6C8ED0538FBC322BCFEC177F10CBE3DBA5978A22C9701963A42931FA863
            CE9B9DE85F9E2137CC2C589C45985F5C49925232C0BE340B3D6D67F04EFB4FE4
            E1756ED46FDA4A546B374418B75D9D82ACC76AF181A21D51806F3E29141F2FCD
            22C0D17CE780F83C13E075F6A1CB4DC8B5EE62D8FB6E13320C1B0EA06CACFD7C
            0359F8E53D2C48779384FBCA81516BE5A2732C0BD03164C0970E7E1105387528
            4B2C2DA39A6A54489454204E04419CC280384EFABA11F4490770EBD307223545
            42557BA5E9301AACCD44AB53D1B8645255790262004EB75A71FF89AE2840C3FE
            F4E5CD391E0D9BB006B83549C0E913A847B460540140CE02FD9D023CB8FD2418
            EEA9801810989BE8034BFB1148532D8222391330E00614BC70F1AA055EFDA8A3
            90266010E0D02BEB9EE179B1C43CEE4D7BF9B5B75E945293EEA7B210228544AD
            51D2185490C494BC38068EA5691C683D4A52D91910153A14033C61580E4EB5CC
            77D6379CDF2D8A3813D78B6885E77B3CBC89520B363BB9DB31B2E6B1ED5A9A0B
            8240E66F8EE0E46F5F9380CB029C3A1193330A48D790A7FFCDAA772A68D31C8E
            9C0F311593CFF3BC51A469C7D0A4908A82A51EC5CA127EA6EB60F07A1B4DE50E
            48DBB80DD6E73C0A972F5F9A7C63DFBE5D9629CB8DB80368058049AA01EA238A
            4280704A0D65C6C6F619E938C4C5B949326EFE1597A607C9FD252F80B177D0F2
            7655D553C343C37FACACF378009FCF442D53003F9548411562E32492E2E3E3DD
            689D1F23B39366B4D9DDC4EA4B1AA9AEAEDE3D36F667EF6A8D64258011E51848
            DE32D1534C960C2566B465FBC1659F86C9F161B8D26536D5D57DFCDCFCFCC2D0
            DF75AA5880ECDADADAFAC887D8B3048310F22F95D0E7F3C0CCDC9CBDB9F15C9D
            D3E91AFFA75648563CFF97BF2D12A270BB457F0173456D3788187BC400000000
            49454E44AE426082}
          ImageId = 9
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
      end
    end
    object pgPrincipal: TFPageControl
      Left = 0
      Top = 69
      Width = 974
      Height = 499
      ActivePage = tabListagem
      TabOrder = 1
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabListagem: TFTabsheet
        Caption = 'Listagem'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox1: TFVBox
          Left = 0
          Top = 0
          Width = 966
          Height = 471
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = 'FVBox1'
          Color = clWhite
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          ParentBackground = False
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object grpBoxFiltro: TFGroupbox
            Left = 0
            Top = 0
            Width = 960
            Height = 74
            Caption = 'Filtro R'#225'pido'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = False
            Closable = True
            Closed = False
            Orient = coHorizontal
            Style = grp3D
            HeaderImageId = 0
            object gpFiltroPrincipal: TFGridPanel
              Left = 2
              Top = 15
              Width = 956
              Height = 47
              Align = alTop
              ColumnCollection = <
                item
                  SizeStyle = ssAbsolute
                  Value = 100.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  Value = 100.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end>
              ControlCollection = <
                item
                  Column = 0
                  Control = FLabel2
                  Row = 0
                end
                item
                  Column = 1
                  Control = cbbFilAtivo
                  Row = 0
                end>
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              RowCollection = <
                item
                  SizeStyle = ssAbsolute
                  Value = 21.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  SizeStyle = ssAbsolute
                  Value = 21.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end>
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              AllRowFlex = True
              WOwner = FrInterno
              WOrigem = EhNone
              ColumnTabOrder = False
              ExplicitTop = 14
              DesignSize = (
                956
                47)
              object FLabel2: TFLabel
                Left = 76
                Top = 1
                Width = 25
                Height = 21
                Align = alRight
                Anchors = []
                Caption = 'Ativo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;70003'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
                ExplicitLeft = 316
                ExplicitHeight = 13
              end
              object cbbFilAtivo: TFCombo
                Left = 455
                Top = 1
                Width = 145
                Height = 21
                Flex = False
                ListOptions = 'Sim=S;N'#227'o=N;Todos=T'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = True
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = False
                HideClearButtonOnNullValue = False
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
                ExplicitLeft = 275
                ExplicitTop = 11
              end
            end
          end
          object gridPrincipal: TFGrid
            Left = 0
            Top = 75
            Width = 956
            Height = 355
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbTipoDocumento
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = True
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            ActionColumn.Title = 'A'#231#245'es'
            ActionColumn.Width = 100
            ActionColumn.TextAlign = taCenter
            ActionColumn.Visible = True
            Columns = <
              item
                Expanded = False
                FieldName = 'ID_TIPO_DOCUMENTO'
                Font = <>
                Title.Caption = 'Id.'
                Width = 60
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{9EC39A62-D571-44B2-BF3F-7CE17D1B6E9D}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'DESCRICAO'
                Font = <>
                Title.Caption = 'Descri'#231#227'o'
                Width = 319
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = True
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{81B255A1-B6C6-44A8-9F62-C1A9692653A2}'
                WOwner = FrNone
                WOrigem = EhAttribute
                WKey = '7000194;70001;70002'
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'ATIVO'
                Font = <>
                Title.Caption = 'Ativo'
                Width = 91
                Visible = True
                Precision = 0
                TextAlign = taCenter
                FieldType = ftCheckBox
                FlexRatio = 0
                Sort = True
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{6C6493B3-6112-4FD6-A5B7-DF916FFF1499}'
                WOwner = FrNone
                WOrigem = EhAttribute
                WKey = '7000194;70001;70003'
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end>
          end
        end
      end
      object tabCadastro: TFTabsheet
        Caption = 'Cadastro'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox2: TFVBox
          Left = 0
          Top = 0
          Width = 966
          Height = 471
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          ParentBackground = False
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object grpBoxPrincipal: TFGroupbox
            Left = 0
            Top = 0
            Width = 729
            Height = 210
            Caption = 'Tipo Documento'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = True
            Closable = False
            Closed = False
            Orient = coVertical
            Style = grp3D
            HeaderImageId = 0
            object FGridPanel2: TFGridPanel
              Left = 2
              Top = 15
              Width = 725
              Height = 169
              Align = alTop
              Alignment = taLeftJustify
              ColumnCollection = <
                item
                  SizeStyle = ssAbsolute
                  Value = 100.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  Value = 100.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end>
              ControlCollection = <
                item
                  Column = 0
                  Control = lblIdTipoDocumento
                  Row = 0
                end
                item
                  Column = 1
                  Control = edIdTipoDoc
                  Row = 0
                end
                item
                  Column = 0
                  Control = lblDescricao
                  Row = 1
                end
                item
                  Column = 1
                  Control = edtDescricao
                  Row = 1
                end
                item
                  Column = 0
                  Control = lblAtivo
                  Row = 2
                end
                item
                  Column = 1
                  Control = chkAtivo
                  Row = 2
                end
                item
                  Column = 0
                  Control = lblPermitirObrigarAss
                  Row = 3
                end
                item
                  Column = 0
                  Control = lblClienteAssina
                  Row = 4
                end
                item
                  Column = 1
                  Control = chkClienteAssina
                  Row = 4
                end
                item
                  Column = 0
                  Control = lblProdutivoAssina
                  Row = 5
                end
                item
                  Column = 0
                  Control = lblConsultor
                  Row = 6
                end
                item
                  Column = 0
                  Control = lblGerente
                  Row = 7
                end
                item
                  Column = 1
                  Control = chkProdutivoAssina
                  Row = 5
                end
                item
                  Column = 1
                  Control = chkConsultorAssina
                  Row = 6
                end
                item
                  Column = 1
                  Control = chkGerenteAssina
                  Row = 7
                end
                item
                  Column = 1
                  Control = cbbObrigarAssinatura
                  Row = 3
                end>
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              RowCollection = <
                item
                  SizeStyle = ssAbsolute
                  Value = 19.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  SizeStyle = ssAbsolute
                  Value = 19.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  SizeStyle = ssAbsolute
                  Value = 21.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  SizeStyle = ssAbsolute
                  Value = 19.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  SizeStyle = ssAbsolute
                  Value = 21.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  SizeStyle = ssAbsolute
                  Value = 19.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  SizeStyle = ssAbsolute
                  Value = 19.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end
                item
                  SizeStyle = ssAbsolute
                  Value = 19.000000000000000000
                  WOwner = FrInterno
                  WOrigem = EhNone
                end>
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              AllRowFlex = True
              WOwner = FrInterno
              WOrigem = EhNone
              ColumnTabOrder = False
              DesignSize = (
                725
                169)
              object lblIdTipoDocumento: TFLabel
                Left = 30
                Top = 1
                Width = 71
                Height = 19
                Align = alRight
                Anchors = []
                Caption = 'Id. Documento'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;70001'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object edIdTipoDoc: TFInteger
                Left = 101
                Top = 1
                Width = 80
                Height = 19
                Table = tbTipoDocumento
                FieldName = 'ID_TIPO_DOCUMENTO'
                HelpCaption = 'Id. Tipo Documento'
                Help = 'SEQUENCIA DO ID TIME'
                TabOrder = 1
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;70001'
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Align = alLeft
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                ExplicitHeight = 24
              end
              object lblDescricao: TFLabel
                Left = 55
                Top = 20
                Width = 46
                Height = 19
                Align = alRight
                Anchors = []
                Caption = 'Descri'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;70002'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object edtDescricao: TFString
                Left = 101
                Top = 20
                Width = 580
                Height = 19
                Table = tbTipoDocumento
                FieldName = 'DESCRICAO'
                HelpCaption = 'Descri'#231#227'o'
                Help = 'NOME DO TIME'
                TabOrder = 2
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;70002'
                Required = True
                Constraint.Expression = 'value is null or trim(value) = '#39#39
                Constraint.Message = 'Campo Descri'#231#227'o, preenchimento '#233' obrigat'#243'rio'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.GroupName = 'grpTbtime'
                Constraint.EnableUI = True
                Constraint.Enabled = True
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 100
                Align = alLeft
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                SaveLiteralCharacter = False
                TextAlign = taLeft
                ExplicitHeight = 24
              end
              object lblAtivo: TFLabel
                Left = 76
                Top = 39
                Width = 25
                Height = 21
                Align = alRight
                Anchors = []
                Caption = 'Ativo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;70003'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object chkAtivo: TFCheckBox
                Left = 101
                Top = 39
                Width = 16
                Height = 21
                Align = alLeft
                Anchors = []
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 2
                Table = tbTipoDocumento
                FieldName = 'ATIVO'
                UncheckedValue = 'N'
                HelpCaption = 'Ativo'
                Help = 'TIME ATIVOS E NAO ATIVOS'
                ReadOnly = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;70003'
                VerticalAlignment = taAlignTop
              end
              object lblPermitirObrigarAss: TFLabel
                Left = 11
                Top = 60
                Width = 90
                Height = 19
                Align = alRight
                Anchors = []
                Caption = 'Obrigar Assinatura'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;46001'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object lblClienteAssina: TFLabel
                Left = 34
                Top = 79
                Width = 67
                Height = 21
                Align = alRight
                Anchors = []
                Caption = 'Cliente Assina'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;46001'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object chkClienteAssina: TFCheckBox
                Left = 364
                Top = 81
                Width = 97
                Height = 17
                Anchors = []
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 3
                Table = tbTipoDocumento
                FieldName = 'CLIENTE_ASSINA'
                CheckedValue = 'S'
                UncheckedValue = 'N'
                ReadOnly = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taAlignTop
              end
              object lblProdutivoAssina: TFLabel
                Left = 21
                Top = 100
                Width = 80
                Height = 19
                Align = alRight
                Anchors = []
                Caption = 'Produtivo Assina'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;46001'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object lblConsultor: TFLabel
                Left = 21
                Top = 119
                Width = 80
                Height = 19
                Align = alRight
                Anchors = []
                Caption = 'Consultor Assina'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;46001'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object lblGerente: TFLabel
                Left = 28
                Top = 138
                Width = 73
                Height = 19
                Align = alRight
                Anchors = []
                Caption = 'Gerente Assina'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000194;70001;46001'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object chkProdutivoAssina: TFCheckBox
                Left = 364
                Top = 101
                Width = 97
                Height = 17
                Anchors = []
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 4
                Table = tbTipoDocumento
                FieldName = 'PRODUTIVO_ASSINA'
                CheckedValue = 'S'
                UncheckedValue = 'N'
                ReadOnly = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taAlignTop
              end
              object chkConsultorAssina: TFCheckBox
                Left = 364
                Top = 120
                Width = 97
                Height = 17
                Anchors = []
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 5
                Table = tbTipoDocumento
                FieldName = 'CONSULTOR_ASSINA'
                CheckedValue = 'S'
                UncheckedValue = 'N'
                ReadOnly = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taAlignTop
              end
              object chkGerenteAssina: TFCheckBox
                Left = 364
                Top = 139
                Width = 97
                Height = 17
                Anchors = []
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 6
                Table = tbTipoDocumento
                FieldName = 'GERENTE_ASSINA'
                CheckedValue = 'S'
                UncheckedValue = 'N'
                ReadOnly = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taAlignTop
              end
              object cbbObrigarAssinatura: TFCombo
                Left = 101
                Top = 60
                Width = 145
                Height = 21
                Table = tbTipoDocumento
                FieldName = 'PERMITIR_OBRIGAR_ASSINATURA'
                Flex = False
                ListOptions = 'N'#227'o permite assinar=N; Permite assinar=P; Obriga Assinar=O'
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = False
                HideClearButtonOnNullValue = False
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
          end
        end
      end
    end
  end
  object tbTipoDocumento: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIPO_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Tipo Documento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBRIGATORIO_AVALIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Obrigatorio Avalia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modulo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERMITIR_OBRIGAR_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Permitir Obrigar Assinatura'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_ASSINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Assina'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSULTOR_ASSINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consultor Assina'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRODUTIVO_ASSINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Produtivo Assina'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GERENTE_ASSINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Gerente Assina'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIPO_DOCUMENTO'
    Cursor = 'CRM_TIPO_DOCUMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '43702;43701'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTipoDocumentoID: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIPO_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Tipo Documento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIPO_DOCUMENTO'
    Cursor = 'CRM_TIPO_DOCUMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '43702;43702'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
