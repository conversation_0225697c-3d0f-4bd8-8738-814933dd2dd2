object ControleAcessoGeralRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000188'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbControleAcesso: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{1AAC68BE-901C-4FC5-B1E3-85A1203EC9D2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_PAI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso Pai'
        GUID = '{99CE3908-5941-4E6B-BE96-39F99183B00B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso'
        GUID = '{7D4BC716-A27D-422C-AE6A-2C9BA2AAB5E9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERIGOSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perigoso'
        GUID = '{3909D1E3-2BD5-40A9-89EA-300F0B1966A2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso Fun'#231#227'o'
        GUID = '{AC75B0EF-**************-1AC38A31516D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Ord Acesso'
        GUID = '{1736F129-3378-4C44-9434-E26470C5D1D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENU_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Menu Acesso'
        GUID = '{D6130A38-EF9B-4660-BFAB-5911AB671654}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SISTEMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sistema'
        GUID = '{E333C94D-9DFB-417F-B001-86F4388A6637}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONTROLE_ACESSO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000188;70002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasFuncoes: TFTable
    FieldDefs = <
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        GUID = '{DECD4C6F-3FA0-46B4-8CE4-755A2787B476}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{9B885FB8-C13F-46D3-AD4F-813F7714942E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_FUNCOES'
    Cursor = 'EMPRESAS_FUNCOES'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000188;70003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbSistemaAcessoFuncao
        GUID = '{1D42D19B-268E-4860-AAE6-6B8BD7226666}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbEmpresasUsuarios
        GUID = '{0D5DA729-7CF0-413F-97A5-19F10034D372}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbUsuarioFoto
        GUID = '{1DF1F02D-87FE-446A-AA72-8CA2CB7232D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbSistemaAcessoFuncao: TFTable
    FieldDefs = <
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Acesso'
        GUID = '{41D813F9-CBF4-4AAA-8293-F5D4ED0E1842}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        GUID = '{98D87402-A6A5-4E8E-AC71-22AE6626E168}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'SISTEMA_ACESSO_FUNCAO'
    TableName = 'SISTEMA_ACESSO_FUNCAO'
    Cursor = 'SISTEMA_ACESSO_FUNCAO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000188;70004'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPerfilUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{F74DC64B-E918-435C-8EF9-8A9CBD1410CB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{4676459D-F50F-4B35-AECA-49C3B8BB0C37}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        GUID = '{F0F705FF-9B80-49DB-81F6-9947F9D67382}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fone'
        GUID = '{1EE834B4-A75C-4829-BA9C-1E05729230F4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Vendedor'
        GUID = '{B311EC40-AAC1-49EE-9DCE-6FC4BA3D1C3D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto'
        GUID = '{AE299A23-8927-4432-9BC1-D2353E89AFF4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RAMAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ramal'
        GUID = '{1129A9CD-B431-40DD-80C3-196CCB754D16}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        GUID = '{3905FC7F-05B9-4E27-BA57-81F0EA99F99A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Nome Codigo'
        GUID = '{848447D0-3415-4052-B7FF-9ADC86EFAE38}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Departamento Descri'#231#227'o Codigo'
        GUID = '{6B65744F-61D7-4169-9555-E77F74C97C25}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIVISAO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Divis'#227'o Descri'#231#227'o Codigo'
        GUID = '{19738DFB-6A3D-4AB8-AC5A-0C849F13B186}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        GUID = '{17DA6923-980E-4265-8CFA-6194ABBE9E60}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAD_BD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cad Bd'
        GUID = '{665FF738-010E-4E90-AC8C-0F274556FD46}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TROCAR_SENHA_LOGAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Trocar Senha Logar'
        GUID = '{D51217C2-E075-4B67-A59D-6B9B2ACF3E35}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PERFIL'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000188;70005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{1A9872EE-0490-4A56-85E7-639EDD571486}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fone'
        GUID = '{04BB0195-FCEF-4E8B-BBA9-6A7C02FAD680}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        GUID = '{EFDBE48F-D2D9-4029-95FA-94B1633D6E79}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_VENDEDOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Vendedor'
        GUID = '{42F8C2F4-DF05-447B-9C71-A4F264E8A5B6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RAMAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ramal'
        GUID = '{7636DB24-9C3B-4C00-BB56-0BC9229B4AA0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'EMPRESAS_USUARIOS'
    TableName = 'EMPRESAS_USUARIOS'
    Cursor = 'EMPRESAS_USUARIOS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000188;70006'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbUsuarioFoto: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{2F562743-0841-47BA-8CD8-7E222B0FB1D3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto'
        GUID = '{9BEFFA76-551F-4313-9CEF-6E49B2B3C68E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FOTO_ICONE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Foto Icone'
        GUID = '{8961DA5E-8D08-4B9C-B687-611D6DBEED34}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'USUARIO_FOTO'
    TableName = 'USUARIO_FOTO'
    Cursor = 'USUARIO_FOTO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000188;70007'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
