object FrmGerencialQuestionario: TFForm
  Left = 321
  Top = 70
  ActiveControl = cbbEmpresa
  Caption = 'Question'#225'rios - Gerencial'
  ClientHeight = 862
  ClientWidth = 957
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600416'
  ShortcutKeys = <>
  InterfaceRN = 'GerencialQuestionarioRN'
  Access = False
  ChangedProp = 
    'FrmGerencialQuestionario.Height;'#13#10#13#10'FrmGerencialQuestionario.Act' +
    'iveControl'#13#10'FrmGerencialQuestionario_1.Touch.InteractiveGestures' +
    ';'#13#10'FrmGerencialQuestionario_1.Touch.InteractiveGestureOptions;'#13#10 +
    'FrmGerencialQuestionario_1.Touch.ParentTabletOptions;'#13#10'FrmGerenc' +
    'ialQuestionario_1.Touch.TabletOptions;'#13#10'FrmGerencialQuestionario' +
    '_1.Touch.InteractiveGestures;'#13#10'FrmGerencialQuestionario_1.Touch.' +
    'InteractiveGestureOptions;'#13#10'FrmGerencialQuestionario_1.Touch.Par' +
    'entTabletOptions;'#13#10'FrmGerencialQuestionario_1.Touch.TabletOption' +
    's;'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxGerencialQuest: TFVBox
    Left = 0
    Top = 0
    Width = 957
    Height = 862
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = True
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 856
      Height = 86
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox1: TFVBox
        Left = 0
        Top = 0
        Width = 752
        Height = 84
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FGridPanel1: TFGridPanel
          Left = 0
          Top = 0
          Width = 673
          Height = 73
          Caption = 'FGridPanel1'
          ColumnCollection = <
            item
              Value = 50.000000000000000000
              WOwner = FrInterno
              WOrigem = EhNone
            end
            item
              Value = 50.000000000000000000
              WOwner = FrInterno
              WOrigem = EhNone
            end>
          ControlCollection = <
            item
              Column = 0
              Control = cbbEmpresa
              Row = 1
            end
            item
              Column = 1
              Control = cbbPeriodo
              Row = 1
            end
            item
              Column = 0
              Control = cbbQuestionario
              Row = 0
            end
            item
              Column = 1
              Control = cbbPerguntas
              Row = 0
            end>
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          RowCollection = <
            item
              SizeStyle = ssAuto
              WOwner = FrInterno
              WOrigem = EhNone
            end
            item
              SizeStyle = ssAuto
              WOwner = FrInterno
              WOrigem = EhNone
            end>
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          AllRowFlex = False
          WOwner = FrInterno
          WOrigem = EhNone
          ColumnTabOrder = False
          object cbbEmpresa: TFCombo
            Left = 1
            Top = 22
            Width = 335
            Height = 21
            LookupTable = tbLeadsEmpresasUsuarios
            LookupKey = 'COD_EMPRESA'
            LookupDesc = 'EMPRESA'
            Flex = True
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Empresa'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = True
            HideClearButtonOnNullValue = True
            Align = alClient
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
          object cbbPeriodo: TFCombo
            Left = 336
            Top = 22
            Width = 336
            Height = 21
            Flex = True
            ListOptions = 'M'#234's atual=M;M'#234's anterior=A;Per'#237'odo=P'
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Periodo'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = False
            UseClearButton = False
            HideClearButtonOnNullValue = False
            Align = alClient
            OnChange = cbbPeriodoChange
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
          object cbbQuestionario: TFCombo
            Left = 1
            Top = 1
            Width = 335
            Height = 21
            LookupTable = tbQuestionario
            LookupKey = 'COD_QUESTIONARIO'
            LookupDesc = 'DESC_QUESTIONARIO'
            Flex = True
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = True
            Prompt = 'Question'#225'rio'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = False
            HideClearButtonOnNullValue = False
            Align = alClient
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
          object cbbPerguntas: TFCombo
            Left = 336
            Top = 1
            Width = 336
            Height = 21
            LookupTable = tbPerguntas
            LookupKey = 'COD_PERGUNTA'
            LookupDesc = 'PERGUNTA'
            Flex = True
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Pergunta'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = True
            HideClearButtonOnNullValue = False
            Align = alClient
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
      end
      object FVBox30: TFVBox
        Left = 752
        Top = 0
        Width = 91
        Height = 75
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 3
        Padding.Left = 5
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object btnPesquisar: TFButton
          Left = 0
          Top = 0
          Width = 83
          Height = 68
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 0
          OnClick = btnPesquisarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000026000000260806000000A83DE9
            AE000003084944415478DAED9869888D5118C79F8B19EB9025B21691254B2126
            85A24842890C6AC60794E40365CD32B664298A1435222245B2C407A22C890F96
            2425348A2C25641BEBF5FFF79CA9D77BCF7BCF7BDE7BEBA279EAD774CF3DEF9D
            DFD99FF3A6D2E9B4FC8D91AA13FB87C41A819A7C8B3505A3C070D00F7402C5E0
            0B78011E802BE03CF86879BE07980716E54B8C024BC12CD02C467D4A1D01DBC0
            C340F939D3A0CEC02A1057AC81115A019A24E8E11F60375809468313A6BC14DC
            482AD6121C131DBA5CE391E89077319F37836549C45A838BA07F1EA46CC1E1ED
            E92BC6965D02C31C3F5E0D1E8BCEA736A03768E521D717DCF711DB2411DD6C24
            76822A23F5C76F828160AEE822297688AD021BE28AF502F744277D386E8269A6
            A75CD11D1C90ECBD7E0B0C8A2B76184CB7945F056344F7ABB8D11C3C032511DF
            53A05BB8A136B1B6E6878A42E5AFC000F3D727D6804A479D8560874B8C73638F
            E5E1056097A75457D189DDD851EF3218E912B30DE307D041ECC74BB638052604
            3E7F131D8D20CFC15370D225C616F609959D05E33DA5B807CE0C49BC04BFE23C
            6C137B2F3A6183C1B36EB1A7584E6113FB09EA85CA78C66D2CB4D867C99CAC5B
            440FF1828A3D115D4DC1603630B9D062672473A2BF13DDDFBE17528C39976D3E
            4D01C7F3F03F1B8A6E1B59D31A9B1877F73B96BA77C160D1A42F69705131B7FB
            046683AF3E62B512B61C8CC7CBBA1CC4D68BAE70C635D179FBDA47AC02ECB794
            73739C018E26902A133D555281B26A305134938925561FDC16BD308483FBDC12
            B05D1CF3241053C141D1F9150EE6FCA571C5184344D39CA288EF99727358AE67
            116A273AF473423D551B6FC050C94C369D39FF7C716714EC595EC778C6F22CE4
            E6CCFC8A97977111BDC4E0221A6B1A9811716E496BC16A5725CF489B5EAC8AAA
            10F75EC91BF356C93C4393047B8A23B1375B259F9B3853EA7DA0630E527C7D50
            0E2EB82AFABE22603AB4DCB4B8C4E3B91AD34395E06D9C0792BE54A120B78049
            60046861A9C30B0BB782D3E090446CA4F9160B06E71DAFFCED45DF6BF0A0E7EA
            649692F8F8AA7B71F7DF88FD06F10403C478DB7DF50000000049454E44AE4260
            82}
          ImageId = 4600223
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
      end
    end
    object lblPeriodoDesc: TFLabel
      Left = 0
      Top = 87
      Width = 69
      Height = 13
      Caption = 'lblPeriodoDesc'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlue
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      Visible = False
      OnClick = lblPeriodoDescClick
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
      MaskType = mtText
    end
    object hBoxPergunta: TFHBox
      Left = 0
      Top = 101
      Width = 855
      Height = 49
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 3
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Visible = False
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox3: TFHBox
        Left = 0
        Top = 0
        Width = 356
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 5
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblPergunta: TFLabel
          Left = 0
          Top = 0
          Width = 119
          Height = 19
          Caption = '1/10 Pergunta'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -16
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
      object hBoxPerguntaAnterior: TFHBox
        Left = 356
        Top = 0
        Width = 40
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        OnClick = hBoxPerguntaAnteriorClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FImage1: TFImage
          Left = 0
          Top = 0
          Width = 36
          Height = 36
          Stretch = False
          ImageSrc = '/images/crmservice4600367.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          Preview = False
        end
      end
      object hBoxProximaPergunta: TFHBox
        Left = 396
        Top = 0
        Width = 40
        Height = 40
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        OnClick = hBoxProximaPerguntaClick
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object imgProximo: TFImage
          Left = 0
          Top = 0
          Width = 36
          Height = 36
          Stretch = False
          ImageSrc = '/images/crmservice4600366.png'
          WOwner = FrInterno
          WOrigem = EhNone
          BoxSize = 0
          GrayScaleOnDisable = False
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          Preview = False
        end
      end
    end
    object hBoxGrafico: TFHBox
      Left = 0
      Top = 151
      Width = 908
      Height = 320
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Visible = False
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox2: TFVBox
        Left = 0
        Top = 0
        Width = 536
        Height = 314
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        DesignSize = (
          532
          310)
        object hBoxFiltroGrafico: TFHBox
          Left = 0
          Top = 0
          Width = 323
          Height = 27
          Anchors = []
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 0
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox7: TFHBox
            Left = 0
            Top = 0
            Width = 36
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object hbBarra: TFHBox
            Left = 36
            Top = 0
            Width = 110
            Height = 23
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Color = clSilver
            Padding.Top = 2
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 1
            OnClick = hbBarraClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox75: TFVBox
              Left = 0
              Top = 0
              Width = 16
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblTodos: TFLabel
              Left = 16
              Top = 0
              Width = 27
              Height = 14
              Caption = 'Barra'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object FVBox77: TFVBox
              Left = 43
              Top = 0
              Width = 12
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object hbPizza: TFHBox
            Left = 146
            Top = 0
            Width = 110
            Height = 23
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 2
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 2
            OnClick = hbPizzaClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox16: TFVBox
              Left = 0
              Top = 0
              Width = 16
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblAtrasado: TFLabel
              Left = 16
              Top = 0
              Width = 25
              Height = 14
              Caption = 'Pizza'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object FVBox18: TFVBox
              Left = 41
              Top = 0
              Width = 12
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object FHBox8: TFHBox
            Left = 256
            Top = 0
            Width = 36
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
        object FChartBarQuest: TFChartBar
          Left = 0
          Top = 28
          Width = 228
          Height = 126
          WOwner = FrInterno
          WOrigem = EhNone
          BuildIn3d = False
          CategoryField = 'OPCAO'
          ShowLegend = True
          ShowTooltip = True
          Table = tbGerencialQuestionarioGrafico
          TooltipFormat = '{0}: ({1}, {2})'
          Series = <
            item
              CaptionField = 'OPCAO'
              XField = 'OPCAO'
              YField = 'TOT_RESP'
              GUID = '{22670A3A-CD75-4BED-AA11-DE6D9F0B976C}'
              WOwner = FrInterno
              WOrigem = EhNone
            end>
          Orient = coVertical
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          YAxisTickInterval = 0
          XAxisTickInterval = 0
          Stacking = False
          ShowDataLabel = True
          XLabelRotation = 0
          YLabelRotation = 0
          LegendAlign = center
          LegendVerticalAlign = bottom
          ColorPaletteIndex = 13
        end
        object chartPieQuest: TFChartPie
          Left = 0
          Top = 155
          Width = 230
          Height = 129
          WOwner = FrInterno
          WOrigem = EhNone
          Table = tbGerencialQuestionarioGrafico
          CaptionField = 'OPCAO'
          CategoryField = 'OPCAO'
          ValueField = 'TOT_RESP'
          ShowLegend = True
          BuildIn3d = False
          ShowLabel = True
          ShowTooltip = False
          LabelFormat = '{0} = {2}'
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Visible = False
          ColorPaletteIndex = 13
        end
      end
      object FVBox4: TFVBox
        Left = 536
        Top = 0
        Width = 341
        Height = 310
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        DesignSize = (
          337
          306)
        object FHBox2: TFHBox
          Left = 0
          Top = 0
          Width = 323
          Height = 36
          Anchors = []
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 0
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object FGrid2: TFGrid
          Left = 0
          Top = 37
          Width = 320
          Height = 120
          TabOrder = 1
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'Tahoma'
          TitleFont.Style = []
          Table = tbGerencialQuestionarioGrafico
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Paging.Enabled = False
          Paging.PageSize = 0
          Paging.DbPaging = False
          FrozenColumns = 0
          ShowFooter = False
          ShowHeader = False
          MultiSelection = False
          Grouping.Enabled = False
          Grouping.Expanded = False
          Grouping.ShowFooter = False
          Crosstab.Enabled = False
          Crosstab.GroupType = cgtConcat
          EnablePopup = False
          WOwner = FrInterno
          WOrigem = EhNone
          EditionEnabled = False
          AuxColumnHeaders = <>
          NoBorder = True
          ActionButtons.BtnAccept = False
          ActionButtons.BtnView = False
          ActionButtons.BtnEdit = False
          ActionButtons.BtnDelete = False
          ActionButtons.BtnInLineEdit = False
          CustomActionButtons = <>
          ActionColumn.Title = 'A'#231#245'es'
          ActionColumn.Width = 100
          ActionColumn.TextAlign = taCenter
          ActionColumn.Visible = True
          Columns = <
            item
              Expanded = False
              FieldName = 'OPCAO'
              Font = <>
              Title.Caption = 'Op'#231#227'o'
              Width = 125
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = True
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{A88FEB53-E629-4E4D-A179-4F449818136F}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'TOT_RESP'
              Font = <>
              Title.Caption = 'Tot Resp'
              Width = 73
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{46AF6465-CC69-41B8-96D4-1AE78DDC53C7}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end>
        end
      end
    end
    object vBoxDetail: TFVBox
      Left = 0
      Top = 472
      Width = 927
      Height = 346
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Visible = False
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblEventosResposta: TFLabel
        Left = 0
        Top = 0
        Width = 111
        Height = 13
        Caption = 'lblEventosResposta'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object gridDetalheGrafico: TFGrid
        Left = 0
        Top = 14
        Width = 910
        Height = 244
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Table = tbGerencialQuestionarioGrid
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Paging.Enabled = True
        Paging.PageSize = 0
        Paging.DbPaging = False
        FrozenColumns = 0
        ShowFooter = False
        ShowHeader = True
        MultiSelection = False
        Grouping.Enabled = False
        Grouping.Expanded = False
        Grouping.ShowFooter = False
        Crosstab.Enabled = False
        Crosstab.GroupType = cgtConcat
        EnablePopup = False
        WOwner = FrInterno
        WOrigem = EhNone
        EditionEnabled = False
        AuxColumnHeaders = <>
        NoBorder = False
        ActionButtons.BtnAccept = False
        ActionButtons.BtnView = False
        ActionButtons.BtnEdit = False
        ActionButtons.BtnDelete = False
        ActionButtons.BtnInLineEdit = False
        CustomActionButtons = <>
        ActionColumn.Title = 'A'#231#245'es'
        ActionColumn.Width = 100
        ActionColumn.TextAlign = taCenter
        ActionColumn.Visible = True
        Columns = <
          item
            Expanded = False
            FieldName = 'COD_EVENTO'
            Font = <>
            Title.Caption = 'Evento'
            Width = 117
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{12799418-FD13-4420-9AC5-73B7C2441C3D}'
            WOwner = FrInterno
            WOrigem = EhNone
            OnClick = CodEventoClick
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'NOME_CLIENTE'
            Font = <>
            Title.Caption = 'Cliente'
            Width = 601
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = True
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{E5408EF3-7F35-4777-9C36-FCA384DC79C2}'
            WOwner = FrInterno
            WOrigem = EhNone
            OnClick = NomeClienteClick
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'NUMERO_OS'
            Font = <>
            Title.Caption = 'O.S'
            Width = 116
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{8BE2A894-23D9-4861-8299-DF6A656C6651}'
            WOwner = FrInterno
            WOrigem = EhNone
            OnClick = NumeroOsClick
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end>
      end
      object FHBox9: TFHBox
        Left = 0
        Top = 259
        Width = 908
        Height = 49
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 5
        Padding.Left = 5
        Padding.Right = 5
        Padding.Bottom = 5
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox3: TFVBox
          Left = 0
          Top = 0
          Width = 241
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FLabel1: TFLabel
            Left = 0
            Top = 0
            Width = 41
            Height = 16
            Caption = 'Chassi'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object FLabel2: TFLabel
            Left = 0
            Top = 17
            Width = 37
            Height = 13
            Caption = 'FLabel2'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            FieldName = 'VEIC_CHASSI_COMPLETO'
            Table = tbGerencialQuestionarioGrid
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
        end
        object FCheckBox1: TFCheckBox
          Left = 241
          Top = 0
          Width = 133
          Height = 17
          Caption = 'Abriu reclama'#231#227'o'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clRed
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          Table = tbGerencialQuestionarioGrid
          FieldName = 'EH_RECLAMACAO'
          CheckedValue = 'S'
          UncheckedValue = 'N'
          ReadOnly = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taAlignTop
        end
      end
      object hboxTextoLivre: TFHBox
        Left = 0
        Top = 309
        Width = 908
        Height = 49
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 5
        Padding.Left = 5
        Padding.Right = 5
        Padding.Bottom = 5
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox5: TFVBox
          Left = 0
          Top = 0
          Width = 241
          Height = 45
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FLabel3: TFLabel
            Left = 0
            Top = 0
            Width = 72
            Height = 16
            Caption = 'Texto Livre'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object FLabel4: TFLabel
            Left = 0
            Top = 17
            Width = 37
            Height = 13
            Caption = 'FLabel2'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            FieldName = 'TEXTO_LIVRE'
            Table = tbGerencialQuestionarioGrid
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
        end
      end
    end
  end
  object imageList: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 808
    Top = 103
    object FMenuItem1: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Pr'#243'ximo'
      ImageIndex = 4600366
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{884655DE-4B0A-48E2-8E41-47BF058E5C40}'
    end
    object FMenuItem2: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Anterior'
      ImageIndex = 4600367
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{*************-46E8-9B70-CF0EAFF5D63A}'
    end
  end
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbQuestionario: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Questionario'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_QUESTIONARIO'
    Cursor = 'CRM_QUESTIONARIO'
    MaxRowCount = 200
    OnAfterScroll = tbQuestionarioAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPerguntas: TFTable
    FieldDefs = <
      item
        Name = 'COD_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pergunta'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_PERGUNTAS'
    Cursor = 'CRM_PERGUNTAS'
    MaxRowCount = 200
    OnAfterScroll = tbPerguntasAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGerencialQuestionarioGrafico: TFTable
    FieldDefs = <
      item
        Name = 'TOT_RESP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tot Resp'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OPCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Op'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pergunta'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OPCOES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Opc'#245'es'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GERENCIAL_QUESTIONARIO_GRAFICO'
    MaxRowCount = 200
    OnAfterScroll = tbGerencialQuestionarioGraficoAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGerencialQuestionarioGrid: TFTable
    FieldDefs = <
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VEIC_CHASSI_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Veic Chassi Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_RECLAMACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Reclama'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL_PELO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel Pelo Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEXTO_LIVRE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GERENCIAL_QUESTIONARIO_GRID'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600416;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
