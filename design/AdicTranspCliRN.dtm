object AdicTranspCliRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '1610880'
  Left = 44
  Top = 162
  Height = 299
  Width = 442
  object tbTransportadoras: TFTable
    FieldDefs = <
      item
        Name = 'COD_TRANSPORTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Transportadora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CNPJCPF_TRANSP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Cnpjcpf Transportadora'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TRANSPORTADORAS'
    Cursor = 'TRANSPORTADORAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '1610880;16101'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
