object NotaFiscalRelacionadaOSRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '426013'
  Left = 44
  Top = 163
  Height = 299
  Width = 442
  object tbVendas: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        GUID = '{BCD1A307-AB64-4582-96CB-712B4268156A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OPERACAO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Opera'#231#227'o Venda'
        GUID = '{0A661BA8-A337-4BAD-9D65-09F2D0F7EFF3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Nota'
        GUID = '{F446B374-C943-44FE-967D-B6D6807F7D30}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        GUID = '{5BD0F34B-8524-4660-8C85-610F5F953345}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GEROU_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nfe'
        GUID = '{6C8F0DFB-2D03-42EC-B54A-9A70731606A7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Emiss'#227'o'
        GUID = '{17AF15B2-5D6B-4779-A814-6BE5A24A8C55}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{D4999B02-B5E1-494E-8110-B6141CF3427D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_VENDAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '426013;42601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCompras: TFTable
    FieldDefs = <
      item
        Name = 'COD_CONTROLE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Controle'
        GUID = '{FBEB263C-3336-4A26-96B6-7AA24270BA93}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Nota'
        GUID = '{76E3144A-1D25-4251-A48C-64C566218727}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERIE_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'S'#233'rie Nota'
        GUID = '{27B325F6-BF84-4420-AFAB-8EE689A791D0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Nota'
        GUID = '{4D564FC0-1D9F-4EC4-9B86-CDF73BDAEDA8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{311465F0-1090-45A1-9467-FE70EB22298C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Emiss'#227'o'
        GUID = '{1E569B59-C2AF-4B56-8D4E-7213FF49F35C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GEROU_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nfe'
        GUID = '{41284E02-5FDA-43F9-A4BB-4008C8C35077}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Status'
        GUID = '{C960C975-C6C2-4E33-BC07-EE2088FAB3F4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_COMPRAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '426013;42602'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCrmpartsNfeMovimento: TFTable
    FieldDefs = <
      item
        Name = 'PDF_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Pdf Nota'
        GUID = '{2642B96A-FCBD-4568-9B48-67BB4164D47F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHAVE_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chave Nfe'
        GUID = '{27D12A20-**************-B49454B42AE0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERIE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'S'#233'rie'
        GUID = '{40E60539-3A69-41E1-8B58-E18431CAAA41}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEM_PDF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tem Pdf'
        GUID = '{EA1E18BF-1AB1-4672-9623-E771643B854B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Nfe'
        GUID = '{4365E3D3-82A2-4129-9658-32AA1959DA7A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRMPARTS_NFE_MOVIMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '426013;47401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
