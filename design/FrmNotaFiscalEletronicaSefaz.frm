object FrmNotaFiscalEletronicaSefaz: TFForm
  Left = 321
  Top = 163
  ActiveControl = vBoxNFe
  Caption = 'Nota Fiscal Eletr'#244'nica'
  ClientHeight = 277
  ClientWidth = 604
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '5300656'
  ShortcutKeys = <>
  InterfaceRN = 'NotaFiscalEletronicaSefazRN'
  Access = False
  ChangedProp = 
    'FrmNotaFiscalEletronicaSefaz.Width;'#13#10'FrmNotaFiscalEletronicaSefa' +
    'z.Height;'#13#10#13#10'FrmNotaFiscalEletronicaSefaz.ActiveControl'#13#10'FrmNota' +
    'FiscalEletronicaSefaz.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxNFe: TFVBox
    Left = 0
    Top = 0
    Width = 604
    Height = 277
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object pgcNFe: TFPageControl
      Left = 0
      Top = 0
      Width = 581
      Height = 236
      ActivePage = tbsAutorizarNfe
      Align = alClient
      TabOrder = 0
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tbsAutorizarNfe: TFTabsheet
        Caption = 'Emitir NF-e'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object hBoxNotaFiscal: TFHBox
          Left = 0
          Top = 0
          Width = 573
          Height = 208
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 8
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object imgNFe: TFImage
            Left = 0
            Top = 0
            Width = 81
            Height = 77
            Stretch = False
            ImageSrc = '/images/crmparts310025.png'
            WOwner = FrInterno
            WOrigem = EhNone
            BoxSize = 0
            GrayScaleOnDisable = False
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            Preview = False
          end
          object vBoxAguardar: TFVBox
            Left = 81
            Top = 0
            Width = 461
            Height = 201
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox6: TFHBox
              Left = 0
              Top = 0
              Width = 185
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FHBox8: TFHBox
              Left = 0
              Top = 20
              Width = 449
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox14: TFHBox
                Left = 0
                Top = 0
                Width = 10
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblNotaFiscalNr: TFLabel
                Left = 10
                Top = 0
                Width = 169
                Height = 23
                Caption = 'Nota fiscal nr. 150/1'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -19
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object hBoxStatus: TFHBox
              Left = 0
              Top = 51
              Width = 446
              Height = 22
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox10: TFHBox
                Left = 0
                Top = 0
                Width = 10
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblStatusNfe: TFLabel
                Left = 10
                Top = 0
                Width = 131
                Height = 16
                Caption = '[00:00] Processando...'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object hBoxStatusPdf: TFHBox
              Left = 0
              Top = 74
              Width = 446
              Height = 22
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 3
              Visible = False
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox2: TFHBox
                Left = 0
                Top = 0
                Width = 10
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblStatusPdf: TFLabel
                Left = 10
                Top = 0
                Width = 131
                Height = 16
                Caption = '[00:00] Processando...'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object FHBox9: TFHBox
              Left = 0
              Top = 97
              Width = 454
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 4
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object hBoxBotoes: TFHBox
              Left = 0
              Top = 148
              Width = 456
              Height = 41
              Align = alTop
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 5
              Padding.Left = 10
              Padding.Right = 25
              Padding.Bottom = 10
              TabOrder = 5
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox12: TFHBox
                Left = 0
                Top = 0
                Width = 50
                Height = 25
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object btnReenviar: TFButton
                Left = 50
                Top = 0
                Width = 99
                Height = 30
                Hint = 'Reenviar NF-e para a Sefaz'
                Caption = 'Reenviar'
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 1
                OnClick = btnReenviarClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                  E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                  B9B9B6418D210000000049454E44AE426082}
                ImageId = 0
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconClass = 'refresh'
                IconReverseDirection = False
              end
              object btnGerarFilaPdf: TFButton
                Left = 149
                Top = 0
                Width = 99
                Height = 30
                Hint = 'Gerar Pdf '
                Caption = 'Gerar PDF'
                Enabled = False
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 2
                OnClick = btnGerarFilaPdfClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                  E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                  B9B9B6418D210000000049454E44AE426082}
                ImageId = 0
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconClass = 'file-pdf-o'
                IconReverseDirection = False
              end
              object btnDownloadXML: TFButton
                Left = 248
                Top = 0
                Width = 99
                Height = 30
                Hint = 'Download XML'
                Caption = 'Download XML'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 3
                OnClick = btnDownloadXMLClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                  61000001F84944415478DA9DD24F4814511C07F0EF7B33F376565A4CC66CC9FE
                  EC44C2E6F60FA14E51D09F8B1E3A181D22A843D4253C14888722C49B50970E05
                  D5215822BC75A84B99141191A74432532BA40D25465D5DD79DF7E6CD6B76B225
                  F1CF543F7897DFEFFD3EBC3F3FA294C2C4C4E8064A8D310009AC129E278A92F3
                  2B5B528D77FECC9332E0385FD3529221AC114270140BB32AC6CC8B5BEDCCDDFF
                  02E6E7664008553A332FD876E6DE5F01C49D868AD5C0F72504777FE5085DD08D
                  EA6DC964F2C78A00E1794096A0E21B610CDC804C9D809FD81E146805D634B5D3
                  B2521F970184CFC018BC05B1AB2D78B922B45C2F7C6B2FB4EF7D10994B156455
                  80F55F85D8DD0665D6411FC942D61F81AADA14402F40E73E43A4CF479CA0300E
                  63F421F89ECB600337C1F77580CCE7607C7A10E6A0996B03BF111A2CB23009CF
                  6E8536F906B2B6A9D21C0984A1E4A2A6ADF8ADD140442C01F2AF3B8F915CEFB3
                  7F0154FDD1E3D507AF3F0F81E957EDCDFA48CF9372C1D8D10AE54E414E0DC3B0
                  9BC187B260E9D350250762EC7105F01A4EB5D41CEA7EBA0CD0EA9A60349C44E9
                  6D27AA5A7A20861F812636438902F8FBDBD1006B3C0B625A7007EF8365CE053B
                  4B00A5C15BC6E17DEB83743E0453EA2E059C975DFBD997ECBB7281C4ADB049F9
                  3E88CE82EBE44162EB83A3C540D83AF8B3E3E13472FBCC01EBF0B5FE9F654E39
                  F06B5363870000000049454E44AE426082}
                ImageId = 3930484
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
              object btnTentarDepois: TFButton
                Left = 347
                Top = 0
                Width = 99
                Height = 30
                Hint = 'Adiar resolu'#231#227'o da pend'#234'ncia junto a Sefaz.'
                Caption = 'Tentar depois'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clRed
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 4
                OnClick = btnTentarDepoisClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F8000000384944415478DA63FC0F040C34048CA316906F012323E9A661316A10
                  58404C08E2513B6AC1A805A3168C5A30342C20050C2E0BA80486BE050083ED9B
                  B99381D9600000000049454E44AE426082}
                ImageId = 0
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconClass = 'reply'
                IconReverseDirection = False
              end
            end
          end
        end
      end
      object tbsMensagens: TFTabsheet
        Caption = 'Mensagens'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
      end
    end
  end
  object timerNfe: TFTimer
    Enabled = False
    Interval = 0
    OnTimer = timerNfeTimer
    Repeats = False
    WOwner = FrNone
    WOrigem = EhNone
    Left = 425
  end
  object timerPdf: TFTimer
    Enabled = False
    Interval = 0
    OnTimer = timerPdfTimer
    Repeats = False
    WOwner = FrNone
    WOrigem = EhNone
    Left = 341
  end
  object tbCrmpartsNfeMovimento: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Orc Mapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTROLE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Controle'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PDF_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Pdf Nota'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHAVE_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chave Nfe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'XML_NOTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Xml Nota'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERIE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'S'#233'rie'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Emiss'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INTEGRACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Integra'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nfe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_NF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Nf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NFCE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nfce'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEM_PDF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tem Pdf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cfe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Nfe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_STATUS_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Status Nfe'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRMPARTS_NFE_MOVIMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300656;53001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMovimento: TFTable
    FieldDefs = <
      item
        Name = 'ID_MOVIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Movimento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Nfe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_IMPRESSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Impress'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_DANFE_PDF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Danfe Pdf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_NF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Nf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERIE_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'S'#233'rie Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMR_CONTROLE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Numr Controle'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHAVE_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chave Nfe'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NFE_MOVIMENTO'
    Cursor = 'NFE_MOVIMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300656;53002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsNfeMensagem: TFTable
    FieldDefs = <
      item
        Name = 'DATA_MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Mensagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Mensagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Mensagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_NFE_MENSAGEM'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300656;53003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object ListaImagem: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 530
    Top = 14
    object LogoNFE: TFMenuItem
      Caption = 'LogoNFE'
      ImageIndex = 310025
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
    end
  end
end
