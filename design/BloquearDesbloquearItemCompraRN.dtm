object BloquearDesbloquearItemCompraRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '51201450'
  Left = 44
  Top = 163
  Height = 299
  Width = 442
  object tbItensCustosCompraBloq: TFTable
    FieldDefs = <
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        GUID = '{CC690B02-7B98-4F59-B149-FDBFB7DE8081}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Nome Codigo'
        GUID = '{17F52468-D97F-404A-9E51-CB43D1AA4922}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPRAS_BLOQUEADAS_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Compras Bloqueadas Descri'#231#227'o'
        GUID = '{22187991-C6A4-4867-8035-BF56FE040EFA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_ITENS_CUSTOS_COMPRA_BLOQ'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '51201450;51202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
