object ClientesFlagsPgtoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '310037'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbClienteFormaPgtoDisp: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONDICAO_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_TIPO_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Tipo Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_PGTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Pagamento Exclusiva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONDICAO_PAGAMENTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Condi'#231#227'o Pagamento Exclusiva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_FORMAS_PGTO'
    Cursor = 'CLIENTE_FORMA_PGTO_DISP'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresas: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS'
    Cursor = 'EMPRESAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;31002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasDepartamentos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_DEPARTAMENTOS'
    Cursor = 'EMPRESAS_DEPARTAMENTOS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;31003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFormaPgto: TFTable
    FieldDefs = <
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'FORMA_PGTO'
    Cursor = 'FORMA_PGTO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;31004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasFiliaisSel: TFTable
    FieldDefs = <
      item
        Name = 'CODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOMEECODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nomeecodigodaempresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESA'
    Cursor = 'EMPRESAS_FILIAIS_SEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaEmpresasDepartamentos: TFTable
    FieldDefs = <
      item
        Name = 'DPTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dpto Descri'#231#227'o Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DPTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_EMPRESAS_DEPARTAMENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaTipoPgtoNbs: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_PGTO_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Pagamento Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_COD_TIPO_PGTO_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto C'#243'd. Tipo Pagamento Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TIPO_PGTO_NBS'
    Cursor = 'LISTA_TIPO_PGTO_NBS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaFormasPgto: TFTable
    FieldDefs = <
      item
        Name = 'FORMA_DESCRICAO_COD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Descri'#231#227'o C'#243'd.'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_FORMAS_PGTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteFormaPgtoDisp1: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome C'#243'd. Condi'#231#227'o Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_FORMAS_PGTO'
    Cursor = 'CLIENTE_FORMA_PGTO_DISP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310037;53005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
