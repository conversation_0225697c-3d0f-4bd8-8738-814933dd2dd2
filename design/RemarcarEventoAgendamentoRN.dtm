object RemarcarEventoAgendamentoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '430014'
  Height = 299
  Width = 442
  object sc: TFSchema
    Tables = <
      item
        Table = tbEventos
        GUID = '{26E2282B-92E5-4563-8612-BEED233C018F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbObs
        GUID = '{00F2F9FE-1EC6-41EE-A396-09605351D555}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbEventos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_NOVO_CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        Caption = 'Data Novo Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ULTIMO_CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        Caption = #218'ltimo Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_EVENTOS'
    TableName = 'CRM_EVENTOS'
    Cursor = 'CRM_EVENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430014;43001'
    DeltaMode = dmAll
  end
  object tbObs: TFTable
    FieldDefs = <
      item
        Name = 'SEQ_OBS'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'Seq Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Respons'#225'vel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        Caption = 'Data'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftText
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Origem'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_OBS'
    TableName = 'CRM_OBS'
    Cursor = 'CRM_OBS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430014;43002'
    DeltaMode = dmAll
  end
end
