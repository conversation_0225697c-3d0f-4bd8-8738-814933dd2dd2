object FrmRequisitarOsOrcRelacionados: TFForm
  Left = 44
  Top = 163
  ActiveControl = vBoxOrcRelacionados
  Caption = 'Or'#231'amentos relacionados a O.S'
  ClientHeight = 711
  ClientWidth = 571
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '44206'
  ShortcutKeys = <>
  InterfaceRN = 'RequisitarOsOrcRelacionadosRN'
  Access = False
  ChangedProp = 
    'FrmRequisitarOsOrcRelacionados.Width;'#13#10'FrmRequisitarOsOrcRelacio' +
    'nados.Height;'#13#10#13#10'FrmRequisitarOsOrcRelacionados.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxOrcRelacionados: TFVBox
    Left = 0
    Top = 0
    Width = 571
    Height = 711
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxBtnTopo: TFHBox
      Left = 0
      Top = 0
      Width = 565
      Height = 65
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 63
        Height = 59
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -15
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 63
        Top = 0
        Width = 63
        Height = 59
        Hint = 'Aceitar'
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -15
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
          0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
          1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
          95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
          101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
          1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
          A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
          C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
          482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
          1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
          C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
          8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
        ImageId = 700088
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnItensComprar: TFButton
        Left = 126
        Top = 0
        Width = 63
        Height = 59
        Hint = 'Itens '#224' comprar'
        Caption = 'Comprar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -15
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnItensComprarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000002654944415478DADD945F48537114C7CFF9DD6D2E0C8CA27C89C2D7E6
          5D8416E8DA248888724D8D9120FD79884A0DB2A0B7068B8822080CC9AC971E2C
          08A29A6B115A1077CB15D91EE2CE14DFA2202B218592FDBBBFD36F2B4164BBBB
          8BF950E7E1C2EFC739E7F3FB9E3F1761990DFF0F405DDD3173A2F2AB0F08AAB3
          67C6F87D55093E2F1BC0E6DAB703817523F201CE1943A42B6B70B65E5194CCB2
          2800A046F1F18F47861E9405B0D46CDB5B9A11E1825072EF6F9212B14FF1C8A3
          BB05015EAF579A984E4F12A18F49A0950AE044E7AC3F330DB15868BEE014D53A
          5B2E1250589469B894E47253EB7ACEF9551177A0A082ACD99DEE1A8ED2A57838
          D05E0AC0E6F4F8C5C08C89123DD105FC56E1799A62DAC1292534632479AEB45F
          525131818E85092C0268ED20E06B85DC5E43AF7779DC22E1B67878C8B770A70B
          D8D874D8BA92CF8D8C47022E83800097A453132F1E7E3004F813D427A669F07D
          24F046CF6F93A379033253AF784CDBE2FBA200D9E59139401F10BED6F3133BB3
          1589CEAB9160B82440D6ECEEA335F47D66B59E8F46996F2B0EF93FC78ED7A70D
          031AAFABBBC446FBC53E24C5F15DB4CBDE93CFCF31A0B611C1592448115238DA
          6937D66447BF1A655272BF96B65603E31D023632DA697FB6D8A7EEE65BB355AB
          5034CC7825CDB20A24AD8773BCF5AA5B1ED305E402B9659834760625DA498073
          8C28F1B24B1E5CE25755C12D7718E06571DCCC092A91C1E4E809F971710537D4
          6B449402645308FC8829A1ED554E6F99CDA3F436217C6400D304D09E60C9DDA2
          17F3869A9CED83F83FAE33A7B450BEE4391373DCD0AFEE614855262E059593B5
          3F0CF5A01CF6EF037E014C7C01283A8F23350000000049454E44AE426082}
        ImageId = 44206
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object FVBox3: TFVBox
      Left = 0
      Top = 66
      Width = 556
      Height = 562
      Align = alClient
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 5
      Padding.Left = 5
      Padding.Right = 5
      Padding.Bottom = 5
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox5: TFHBox
        Left = 0
        Top = 0
        Width = 543
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbbOrcamentos: TFComboEx
          Left = 0
          Top = 0
          Width = 280
          Height = 21
          Table = tbRequisitarTempOrcComboEx
          LookupTable = tbRequisitarOsOrcRelac
          FieldName = 'NUMERO_ORC'
          LookupKey = 'NUMERO_ORC'
          LookupDesc = 'NUMERO_ORC_DISPLAY'
          Flex = True
          ReadOnly = False
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione o or'#231'amento'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          MultiSelection = True
          OnItemClick = cbbOrcamentosItemClick
          OnClose = cbbOrcamentosClose
        end
        object cbbServico: TFCombo
          Left = 280
          Top = 0
          Width = 246
          Height = 21
          LookupTable = tbRequisitarOsOrcServCombo
          LookupKey = 'COD_SERVICO'
          LookupDesc = 'DESCRICAO_SERVICO'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Selecione um servi'#231'o'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = True
          HideClearButtonOnNullValue = False
          OnChange = cbbServicoChange
          OnClearClick = cbbServicoClearClick
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
          MultiSelection = False
          IconReverseDirection = False
        end
      end
      object FHBox4: TFHBox
        Left = 0
        Top = 42
        Width = 544
        Height = 44
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 3
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 6
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox1: TFHBox
          Left = 0
          Top = 0
          Width = 293
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object edCodPeca: TFString
            Left = 0
            Top = 0
            Width = 203
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'C'#243'digo ou Descri'#231#227'o'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccNormal
            Pwd = False
            Maxlength = 0
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            OnEnter = edCodPecaEnter
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
          object vBoxIconLimparDesc: TFVBox
            Left = 203
            Top = 0
            Width = 27
            Height = 25
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 5
            Padding.Left = 11
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            OnClick = vBoxIconLimparDescClick
            Margin.Top = 0
            Margin.Left = 5
            Margin.Right = 5
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FImage7: TFImage
              Left = 0
              Top = 0
              Width = 16
              Height = 18
              Align = alClient
              Stretch = False
              ImageSrc = '/images/crmservice4600235.png'
              WOwner = FrInterno
              WOrigem = EhNone
              BoxSize = 0
              GrayScaleOnDisable = False
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              Preview = False
            end
          end
        end
        object FHBox2: TFHBox
          Left = 293
          Top = 0
          Width = 237
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 5
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftFalse
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object chkExcluirReq: TFCheckBox
            Left = 0
            Top = 0
            Width = 124
            Height = 17
            Caption = 'Excluir j'#225' requisitados'
            Checked = True
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            State = cbChecked
            TabOrder = 0
            OnCheck = chkExcluirReqCheck
            ReadOnly = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taAlignTop
          end
          object chkSmtEstoqDisp: TFCheckBox
            Left = 124
            Top = 0
            Width = 116
            Height = 17
            Caption = 'Estoque dispon'#237'vel'
            Checked = True
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            State = cbChecked
            TabOrder = 1
            OnCheck = chkSmtEstoqDispCheck
            ReadOnly = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taAlignTop
          end
        end
      end
      object gridItensAprovados: TFGrid
        Left = 0
        Top = 87
        Width = 563
        Height = 415
        TabOrder = 2
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Table = tbRequisitarOsOrcItensAprov
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Paging.Enabled = False
        Paging.PageSize = 0
        Paging.DbPaging = False
        FrozenColumns = 0
        ShowFooter = False
        ShowHeader = True
        MultiSelection = False
        Grouping.Enabled = False
        Grouping.Expanded = False
        Grouping.ShowFooter = False
        Crosstab.Enabled = False
        Crosstab.GroupType = cgtConcat
        EnablePopup = False
        WOwner = FrInterno
        WOrigem = EhNone
        EditionEnabled = False
        AuxColumnHeaders = <>
        ContextMenu = popMenuGrid
        NoBorder = False
        ActionButtons.BtnAccept = False
        ActionButtons.BtnView = False
        ActionButtons.BtnEdit = False
        ActionButtons.BtnDelete = False
        ActionButtons.BtnInLineEdit = False
        CustomActionButtons = <>
        ActionColumn.Title = 'A'#231#245'es'
        ActionColumn.Width = 100
        ActionColumn.TextAlign = taCenter
        ActionColumn.Visible = True
        Columns = <
          item
            Expanded = False
            FieldName = 'SEL'
            Font = <>
            Title.Caption = '#'
            Width = 32
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = True
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = False
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{F4324790-8E43-436D-9276-2310426EFEF5}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'COD_ITEM'
            Font = <>
            Title.Caption = 'C'#243'd. Item'
            Width = 96
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = True
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{581C9A17-7E45-483B-A6FA-EE973D2EB345}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'QTDE'
            Font = <>
            Title.Caption = 'Qtde'
            Width = 65
            Visible = True
            Precision = 0
            TextAlign = taRight
            FieldType = ftInteger
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{7179B2BF-744D-42FE-8338-E20EBB8CD02F}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'QTDE_REQUISTADA'
            Font = <>
            Title.Caption = 'Req. Total'
            Width = 88
            Visible = True
            Precision = 0
            TextAlign = taRight
            FieldType = ftInteger
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{2CAC860F-66AA-4165-A6F4-32BA94EE6188}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'SALDO'
            Font = <>
            Title.Caption = 'Saldo'
            Width = 80
            Visible = True
            Precision = 0
            TextAlign = taRight
            FieldType = ftInteger
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{25D8E7EC-B5FC-498E-9809-28904A5D63DB}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'REQUISITAR'
            Font = <
              item
                Expression = '*'
                EvalType = etExpression
                GUID = '{225594D9-4982-4EA1-B234-2F32301BB136}'
                WOwner = FrInterno
                WOrigem = EhNone
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clBlue
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
              end>
            Title.Caption = 'Requisitar'
            Width = 88
            Visible = True
            Precision = 0
            TextAlign = taRight
            FieldType = ftInteger
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{225E15E5-0412-46BC-99F7-4C8E2B7E42A3}'
            WOwner = FrInterno
            WOrigem = EhNone
            OnClick = gridItensAprovadosColumns5Click
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'ESTOQUE'
            Font = <>
            Title.Caption = 'Est. Disp.'
            Width = 80
            Visible = True
            Precision = 0
            TextAlign = taRight
            FieldType = ftInteger
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <
              item
                Expression = 'SALDO > ESTOQUE'
                EvalType = etExpression
                GUID = '{74B99C1C-0F32-469F-8BB8-E3CAF9B65A37}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 44203
                OnClick = 'ClickComprarSemEstoque'
                Color = clBlack
              end>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{7A55E9A4-347D-431A-A51A-BCF2F45F7DDA}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end>
      end
      object FHBox3: TFHBox
        Left = 0
        Top = 503
        Width = 537
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel4: TFLabel
          Left = 0
          Top = 0
          Width = 33
          Height = 13
          Caption = 'lblDesc'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          FieldName = 'DESCRICAO'
          Table = tbRequisitarOsOrcItensAprov
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object FLabel1: TFLabel
          Left = 33
          Top = 0
          Width = 37
          Height = 13
          Caption = 'FLabel1'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          FieldName = 'ORC_DESC'
          Table = tbRequisitarOsOrcItensAprov
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
    end
  end
  object ImageList: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 286
    Top = 20
    object FMenuItem2: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Pesquisar'
      ImageIndex = 44202
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{C5331FDB-46D5-41C5-88EA-FF5DAC10C79F}'
    end
    object FMenuItem1: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Limpar'
      ImageIndex = 4600235
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{0623AF57-**************-B44A39215B16}'
    end
    object miBuy: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Comprar'
      ImageIndex = 44203
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{B0615ADE-BCBB-46DA-8399-7E3AD31C86CD}'
    end
  end
  object coachmarkMessage: TFCoachmark
    ShowNextButton = False
    ShowPriorButton = False
    Items = <
      item
        TargetName = 'gridItensAprovados'
        Position = poBefore_start
        Name = 'coachmarkMessageItem0'
      end>
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 392
    Top = 32
  end
  object popMenuGrid: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 180
    Top = 190
    object miSelAll: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Todos'
      OnClick = miSelAllClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{D50F0043-CB34-4797-8AE5-A9D8CD6D4EC2}'
    end
    object mmSelAllNone: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Nenhum'
      OnClick = mmSelAllNoneClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{CE056E86-064A-42C6-A277-646095BAA985}'
    end
  end
  object tbRequisitarOsOrcRelac: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_ORC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Orcamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_ORC_DISPLAY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Orc Display'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_OS_ORC_RELAC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRequisitarOsOrcItensAprov: TFTable
    FieldDefs = <
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_REQUISTADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Requistada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SALDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Saldo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REQUISITAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Requisitar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORC_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Orc Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_OS_ORC_ITENS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbGetProdutivoSugestao: TFTable
    FieldDefs = <
      item
        Name = 'COD_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produtivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_OS_ORC_PRODUTIVO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44204'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRequisitarOsOrcServCombo: TFTable
    FieldDefs = <
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_OS_ORC_SERV_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44206'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRequisitarTempOrcComboEx: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_ORC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Orc'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_TEMP_ORC_COMBO_EX'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44206;44207'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
