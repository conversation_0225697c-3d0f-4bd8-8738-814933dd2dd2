object ClientesProfissaoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600175'
  Height = 299
  Width = 442
  object tbClientesProfissao: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO_INIT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Init'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_PROFISSAO'
    Cursor = 'CLIENTES_PROFISSAO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600175;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
end
