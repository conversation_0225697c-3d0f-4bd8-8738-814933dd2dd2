object FrmCadastroPainel: TFForm
  Left = 22
  Top = 23
  Caption = 'Novo Painel'
  ClientHeight = 295
  ClientWidth = 556
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '382029'
  ShortcutKeys = <>
  InterfaceRN = 'CadastroPainelRN'
  Access = False
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object tbPainel: TFTable
    FieldDefs = <
      item
        Name = 'ID_PAINEL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'BSC_PAINEL'
    Cursor = 'BSC_PAINEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382029;38201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbComboGrupo: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BSC_COMBO_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382029;38202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMaxIdPainel: TFTable
    FieldDefs = <
      item
        Name = 'ID_PAINEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'GET_MAX_ID_PAINEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382029;38203'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
