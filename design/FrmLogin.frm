object FrmLogin: TFForm
  Left = 44
  Top = 163
  ActiveControl = cboTema
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Form Login'
  ClientHeight = 430
  ClientWidth = 974
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '29001'
  ShortcutKeys = <>
  InterfaceRN = 'LoginRN'
  Access = False
  ChangedProp = 
    'FrmLogin.Left;'#13#10#10#13'FrmLogin.Top;'#13#10#10#13'FrmLogin.Width;'#13#10#10#13'FrmLogin.H' +
    'eight;'#13#10#10#13'FrmLogin.ClientHeight;'#13#10#10#13'FrmLogin.ClientWidth;'#13#10#10#13'Frm' +
    'Login.BackgroundImage;'#13#10'FrmLogin.ActiveControlFrmLogin.Left;'#13#10'Fr' +
    'mLogin.ActiveControl;'#13#10
  Spacing = 0
  BackgroundImage = 'images/CrmParts.jpg'
  PixelsPerInch = 96
  TextHeight = 13
  object FHBox2: TFHBox
    Left = 0
    Top = 32
    Width = 966
    Height = 367
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 10
    Padding.Right = 15
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object logoNBS: TFImage
      Left = 0
      Top = 0
      Width = 120
      Height = 60
      Stretch = True
      WOwner = FrInterno
      WOrigem = EhNone
      BoxSize = 0
      GrayScaleOnDisable = False
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      Preview = False
      ImageId = 0
    end
    object FGridPanelLabel: TFGridPanel
      Left = 120
      Top = 0
      Width = 693
      Height = 197
      Align = alClient
      Caption = 'FGridPanelLabel'
      ColumnCollection = <
        item
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAbsolute
          Value = 205.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAbsolute
          Value = 30.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      ControlCollection = <
        item
          Column = 1
          Control = cboTema
          Row = 0
        end
        item
          Column = 1
          Control = cboSchema
          Row = 1
        end
        item
          Column = 1
          Control = edtStringUsuario
          Row = 2
        end
        item
          Column = 1
          Control = edtStringSenha
          Row = 3
        end
        item
          Column = 1
          Control = btnLogin
          Row = 5
        end
        item
          Column = 0
          Control = lblTema
          Row = 0
        end
        item
          Column = 0
          Control = lblSchema
          Row = 1
        end
        item
          Column = 0
          Control = lblUsuario
          Row = 2
        end
        item
          Column = 0
          Control = lblSenha
          Row = 3
        end
        item
          Column = 1
          Control = chkLembrarSenha
          Row = 4
        end>
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      RowCollection = <
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 50.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      AllRowFlex = True
      WOwner = FrInterno
      WOrigem = EhNone
      ColumnTabOrder = False
      DesignSize = (
        693
        197)
      object cboTema: TFCombo
        Left = 457
        Top = 1
        Width = 145
        Height = 21
        Hint = 'Tema'
        Flex = True
        HelpCaption = 'Tema'
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Tema'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        OnChange = cboTemaChange
        OnEnter = cboTemaEnter
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object cboSchema: TFCombo
        Left = 457
        Top = 22
        Width = 145
        Height = 21
        Hint = 'Schema'
        Flex = True
        HelpCaption = 'Schema'
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Prompt = 'Schema'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        OnChange = cboSchemaChange
        OnEnter = cboSchemaEnter
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object edtStringUsuario: TFString
        Left = 457
        Top = 43
        Width = 145
        Height = 21
        Hint = 'Usu'#225'rio'
        HelpCaption = 'Usu'#225'rio'
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Prompt = 'Usu'#225'rio'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccUpper
        Pwd = False
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        OnEnter = edtStringUsuarioEnter
        OnExit = edtStringUsuarioExit
        SaveLiteralCharacter = True
        TextAlign = taLeft
      end
      object edtStringSenha: TFString
        Left = 457
        Top = 64
        Width = 145
        Height = 21
        Hint = 'Senha'
        HelpCaption = 'Senha'
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Prompt = 'Senha'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccNormal
        Pwd = True
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        OnEnter = edtStringSenhaEnter
        SaveLiteralCharacter = True
        TextAlign = taLeft
      end
      object btnLogin: TFButton
        Left = 555
        Top = 102
        Width = 107
        Height = 53
        Align = alRight
        Caption = 'Login'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 4
        OnClick = btnLoginClick
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object lblTema: TFLabel
        Left = 426
        Top = 1
        Width = 31
        Height = 21
        Align = alRight
        Caption = 'Tema'
        Color = clBtnFace
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentColor = False
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 14
      end
      object lblSchema: TFLabel
        Left = 414
        Top = 22
        Width = 43
        Height = 21
        Align = alRight
        Caption = 'Schema'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 14
      end
      object lblUsuario: TFLabel
        Left = 418
        Top = 43
        Width = 39
        Height = 21
        Align = alRight
        Caption = 'Usu'#225'rio'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 14
      end
      object lblSenha: TFLabel
        Left = 423
        Top = 64
        Width = 34
        Height = 21
        Align = alRight
        Caption = 'Senha'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitHeight = 14
      end
      object chkLembrarSenha: TFCheckBox
        Left = 511
        Top = 85
        Width = 97
        Height = 17
        Hint = 'Lembrar Senha'
        Anchors = []
        Caption = 'Lembrar Senha?'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 5
        CheckedValue = 'S'
        UncheckedValue = 'N'
        OnCheck = chkLembrarSenhaCheck
        ReadOnly = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taAlignTop
      end
    end
    object FHBox6: TFHBox
      Left = 813
      Top = 0
      Width = 54
      Height = 41
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
  end
  object FHBox3: TFHBox
    Left = 0
    Top = 402
    Width = 842
    Height = 28
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Color = 6776679
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    ParentBackground = False
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 0
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox4: TFHBox
      Left = 0
      Top = 0
      Width = 409
      Height = 23
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 5
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblVersaoSistema: TFLabel
        Left = 0
        Top = 0
        Width = 37
        Height = 13
        Caption = 'Versao:'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
    end
    object FVBox1: TFVBox
      Left = 409
      Top = 0
      Width = 392
      Height = 23
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 0
      Flex.Vflex = ftTrue
      Flex.Hflex = ftMin
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox5: TFHBox
        Left = 0
        Top = 0
        Width = 185
        Height = 9
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 0
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object FLabel5: TFLabel
        Left = 0
        Top = 10
        Width = 274
        Height = 13
        Align = alRight
        Caption = 'Copyright NBS Tecnologia - Todos os direitos reservados '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
    end
    object FVBox2: TFVBox
      Left = 801
      Top = 0
      Width = 10
      Height = 7
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
  end
  object FHBox1: TFHBox
    Left = 0
    Top = 0
    Width = 974
    Height = 33
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 2
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
  end
  object imageList: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 294
    Top = 198
    object logo: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'logo'
      ImageIndex = 7000195
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{44FC0F47-A281-439C-A47E-E6AFF5A63A70}'
    end
  end
  object tbSchema: TFTable
    FieldDefs = <
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Schema Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRIPTION'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Description'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SYS.NBS_SCHEMA'
    Cursor = 'NBS_SCHEMA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSchemaUser: TFTable
    FieldDefs = <
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Schema Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SCHEMA_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Schema User'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Principal'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBS_SCHEMA_USER'
    Cursor = 'NBS_SCHEMA_USER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbUsuarioLogado
        GUID = '{608909CB-2CAB-4362-99E7-627B32644955}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbUsuarioLogado: TFTable
    FieldDefs = <
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'USUARIO_LOGADO'
    TableName = 'USUARIO_LOGADO'
    Cursor = 'USUARIO_LOGADO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70003'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTables: TFTable
    FieldDefs = <
      item
        Name = 'TABLE_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Table Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OWNER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Owner'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ALL_TABLES'
    Cursor = 'ALL_TABLES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServidor: TFTable
    FieldDefs = <
      item
        Name = 'SERVIDOR_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Servidor Id.'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Seguro'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBS_SERVIDOR'
    Cursor = 'NBS_SERVIDOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSysServidor: TFTable
    FieldDefs = <
      item
        Name = 'SERVIDOR_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Servidor Id.'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Seguro'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SYS.NBS_SERVIDOR'
    Cursor = 'SYS_NBS_SERVIDOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServidorUser: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PW_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pw User'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AD_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ad User'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBS_SERVIDOR_USER'
    Cursor = 'NBS_SERVIDOR_USER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSysServidorUser: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PW_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pw User'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AD_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ad User'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SYS.NBS_SERVIDOR_USER'
    Cursor = 'SYS_NBS_SERVIDOR_USER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '29001;70008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
