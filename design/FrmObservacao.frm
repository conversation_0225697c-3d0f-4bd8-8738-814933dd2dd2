object FrmObservacao: TFForm
  Left = 321
  Top = 163
  ActiveControl = vBoxObsercacao
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Observa'#231#227'o'
  ClientHeight = 275
  ClientWidth = 524
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000153'
  ShortcutKeys = <>
  InterfaceRN = 'ObservacaoRN'
  Access = False
  ChangedProp = 'FrmObservacao.Height;'#13#10'FrmObservacao.Width;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxObsercacao: TFVBox
    Left = 0
    Top = 0
    Width = 524
    Height = 275
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hbBoxObservacaoOkCancel: TFHBox
      Left = 0
      Top = 0
      Width = 515
      Height = 65
      Align = alBottom
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 5
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnCancel: TFButton
        Left = 0
        Top = 0
        Width = 66
        Height = 55
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnCancelClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D494844520000001E0000002408060000009ED18A
          69000001C74944415478DAED964D2B44511880E736313433A4F191AF8D7C3449
          63A344D1105176CA4E4AD9B350ACFC01C91F9805C90AA10819AC64A1EC247676
          16B2411A99EB39395337C69D73C7BD83BAA79EDE9973DEF33EE77E9EABE9BAEE
          F98DA6B96257EC8A45634E48D3B4FB9C89C9CD27ACC230AC201F755C4C9E8FB0
          0643B22B81D8E7A8989C02C2060C1ABA9388BD8E89192F246C42FF97C93447C4
          8CF909DBD0F34D8A1777D25631FD01C20E7499CCDF820B38803316A17CA7A615
          D35744D8854E0B0771030B106301AF96C5FC2F26EC417B36A790760963C8CF95
          C5FC2E21EC435B96D2544BC004F26555B1B8567D3F94A69AB8E9C6912FA9885B
          098710B2492E8E3C8AFCD4542CE511292FB5497E0D11E42FA662296F21C4A1CC
          26F914E2C58C62296F96F20A1BC4B75087FC2DA358412E4E9D78E4C4E3570E0D
          D001DD104C93DF8BF848492CE56129AFFC34F44C217F9A7CF16E1F815968320C
          CD933FAD2C96C54401B1DA2A43F71385022673F20833300762173B213F6A492C
          0B354A79B5EC7AA4505061DE80E7631F7F20BFD6B25816A9271C430D5C5128AC
          384F6CA9EBC6855AFEE6225F5C6B71CAE214BAB3306FD2F848E5EC2B138F66DC
          36FFD7E7AD2B76C57F5AFC0E2AEAFAB92FC4B7AF0000000049454E44AE426082}
        ImageId = 430032
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object vBoxObservacaoOkCancelSeparador01: TFVBox
        Left = 66
        Top = 0
        Width = 5
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnOk: TFButton
        Left = 71
        Top = 0
        Width = 66
        Height = 55
        Hint = 'Salvar'
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnOkClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001374944415478DA6364A031608431FEFFFF2F09A4F449D4FF1D88CF33
          32327EC26B01D4F00740CC468623419654002D9984CF020F20B51D88D700F141
          206E00626134B5CB80F838946D09C451503D1A40AC08C48D404B1A0859900B54
          3405C8BF03642BA3A9F504CAED40570FC4AB81783F106B62B384620BA0EAC571
          5942150BA062C896E4C3E2846A16205972028841B43850EE332916608B64140B
          A0665500A976207600CA1DC465C152A80184800550FD49340B7280D464988F71
          59C002645B0331271EC36F03D5DE451724CA02225C8E130C0E0B880C2264002A
          2E8E02F5FE21D602622319192C03EA8D26D60250321560809449C40090BA0F40
          BD2AA458C000D24064B8C3D58F5A40B105C8150E030369910CA3ED813804DD02
          4AAA4C6CE017102B002D784E69A58F0B5C04190E0E362A1988130000D0053128
          1A34B73E0000000049454E44AE426082}
        ImageId = 310032
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object memObservacao: TFMemo
      Left = 0
      Top = 66
      Width = 515
      Height = 216
      Hint = 'Observa'#231#227'o'
      Align = alClient
      CharCase = ccNormal
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'Tahoma'
      Font.Style = []
      Lines.Strings = (
        'memObservacao')
      Maxlength = 0
      ParentFont = False
      TabOrder = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      HelpCaption = 'Observa'#231#227'o'
      WOwner = FrInterno
      WOrigem = EhNone
      Constraint.CheckWhen = cwImmediate
      Constraint.CheckType = ctExpression
      Constraint.FocusOnError = False
      Constraint.EnableUI = True
      Constraint.Enabled = False
      Constraint.FormCheck = True
      Required = False
    end
  end
end
