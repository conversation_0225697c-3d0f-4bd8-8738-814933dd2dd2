object FrmMensagemGenerica: TFForm
  Left = 44
  Top = 163
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Mensagem gen'#233'rica'
  ClientHeight = 114
  ClientWidth = 357
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '548023'
  ShortcutKeys = <>
  InterfaceRN = 'MensagemGenericaRN'
  Access = False
  ChangedProp = 'FrmMensagemGenerica.Width;'#13#10'FrmMensagemGenerica.Height;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 357
    Height = 114
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 10
    Padding.Left = 10
    Padding.Right = 10
    Padding.Bottom = 10
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 0
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
  end
end
