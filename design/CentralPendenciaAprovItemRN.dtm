object CentralPendenciaAprovItemRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '123045'
  Height = 299
  Width = 442
  object tbPendenciaAprovItemGrid: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_POR_ITEM_DES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Por Item Des'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_DESC_MAX'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Desconto Max'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARKUP_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Markup Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_DESCONTO_LIBERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Desconto Liberado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBEROU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUANTIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_DESC_MAX_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Desconto Max Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Origem Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PENDENCIA_APROV_ITEM_GRID'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123045;12301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOrcamentosItens: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_POR_ITEM_LIB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Por Item Lib'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBEROU_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberou Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_ORCAMENTOS_ITENS'
    Cursor = 'OS_ORCAMENTOS_ITENS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123045;12302'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPendenciaDadosMarkup: TFTable
    FieldDefs = <
      item
        Name = 'MARGEM_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARKUP_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Markup Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PENDENCIA_DADOS_MARKUP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '123045;12303'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
