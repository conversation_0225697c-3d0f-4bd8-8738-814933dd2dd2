object FrmMarcasVsConcessionaria: TFForm
  Left = 104
  Top = 104
  ActiveControl = cmbMarca
  Caption = 'Marcas x Dealer'
  ClientHeight = 428
  ClientWidth = 741
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600176'
  ShortcutKeys = <>
  InterfaceRN = 'MarcasVsConcessionariaRN'
  Access = False
  ChangedProp = 
    'FrmMarcasVsConcessionaria.Height;'#13#10#13#10'FrmMarcasVsConcessionaria.A' +
    'ctiveControl'#13#10'FrmMarcasVsConcessionaria.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 741
    Height = 428
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 505
      Height = 35
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 15
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 10
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      object FHBox4: TFHBox
        Left = 0
        Top = 0
        Width = 57
        Height = 22
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 7
        Padding.Left = 10
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        object FLabel1: TFLabel
          Left = 0
          Top = 0
          Width = 35
          Height = 16
          Caption = 'Marca'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
        end
      end
      object cmbMarca: TFCombo
        Left = 57
        Top = 0
        Width = 217
        Height = 21
        LookupTable = tbMarcasLkp
        LookupKey = 'COD_MARCA'
        LookupDesc = 'DESCRICAO_MARCA2'
        Flex = False
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Selecione'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
      end
    end
    object FGroupbox1: TFGroupbox
      Left = 0
      Top = 36
      Width = 635
      Height = 389
      Caption = 'Tipos de Concession'#225'rias'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      ParentFont = False
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 5
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      Scrollable = False
      Closable = False
      Closed = False
      Orient = coHorizontal
      Style = grp3D
      HeaderImageId = 0
      object FHBox3: TFHBox
        Left = 2
        Top = 15
        Width = 631
        Height = 372
        Align = alClient
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 5
        Margin.Left = 3
        Margin.Right = 3
        Margin.Bottom = 5
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        object FDualList1: TFDualList
          Left = 0
          Top = 0
          Width = 533
          Height = 357
          Caption = 'Selecionados'
          LookupCaption = 'Dispon'#237'veis'
          CaptionHiperLink = False
          LookupCaptionHiperLink = False
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          LookupColumns = <
            item
              Expanded = False
              FieldName = 'DESCRICAO2'
              Font = <>
              Title.Caption = 'Tipo Concession'#225'ria'
              Width = 324
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = True
              ImageHeader = 0
              Wrap = False
              Flex = True
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{69B00AB0-5A80-4716-8FF2-B1FEC52581EE}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
            end>
          Columns = <
            item
              Expanded = False
              FieldName = 'DESCRICAO_MARCA'
              Font = <>
              Title.Caption = 'Marca'
              Width = 145
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = True
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{395BA1BB-32E0-4D98-83E1-E939DA0AD367}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
            end
            item
              Expanded = False
              FieldName = 'DESCRICAO'
              Font = <>
              Title.Caption = 'Tipo Concession'#225'ria'
              Width = 204
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = True
              ImageHeader = 0
              Wrap = False
              Flex = True
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{E2E3E425-8146-45F6-B883-721F390909A5}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
            end>
          Table = tbMarcaConcessionaria
          LookupTable = tbConcessionariaTipoDL
          MultiSelection = False
          Paging = False
          OnAfterDeleteRight = FDualList1AfterDeleteRight
          OnAfterInsertRight = FDualList1AfterInsertRight
          WOwner = FrInterno
          WOrigem = EhNone
          RelationshipCols = <
            item
              LookupFieldName = 'DESCRICAO2'
              FieldName = 'DESCRICAO'
            end>
          Grouping.Enabled = False
          Grouping.Expanded = False
          Grouping.ShowFooter = False
          GroupingLookup.Enabled = False
          GroupingLookup.Expanded = False
          GroupingLookup.ShowFooter = False
        end
      end
    end
  end
  object scMarcas: TFSchema
    Tables = <
      item
        Table = tbMarcaConcessionaria
        GUID = '{1F8DC8A2-56B1-433A-A7D8-7F35B64B7868}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbMarcaConcessionaria: TFTable
    FieldDefs = <
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_CONCESSIONARIA'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Concessionaria'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'MARCA_CONCESSIONARIA'
    TableName = 'MARCA_CONCESSIONARIA'
    Cursor = 'MARCA_CONCESSIONARIA'
    MaxRowCount = 500000
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600176;46001'
    DeltaMode = dmAll
    RatioBatchSize = 20
  end
  object tbMarcasLkp: TFTable
    FieldDefs = <
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MARCA2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Marca2'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'MARCAS'
    Cursor = 'MARCAS'
    MaxRowCount = 500000
    OnAfterScroll = tbMarcasLkpAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600176;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbConcessionariaTipoDL: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_CONCESSIONARIA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Concessionaria'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRA_SERVICO_GARANTIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Mostra Servi'#231'o Garantia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descricao2'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    MasterFields = 'COD_MARCA'
    DetailFilters = 'COD_MARCA'
    TableName = 'CONCESSIONARIA_TIPO'
    Cursor = 'CONCESSIONARIA_TIPO'
    MaxRowCount = 500000
    MasterTable = tbMarcasLkp
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600176;46006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
end
