object FrmCadAreaDeContato: TFForm
  Left = 321
  Top = 163
  ActiveControl = pgPrincipal
  Align = alTop
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = #193'rea de contato'
  ClientHeight = 511
  ClientWidth = 514
  Color = clWhite
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Menu = popMenuPrincipal
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '44801360'
  ShortcutKeys = <
    item
      Modifier = smCtrl
      Key = sk1
      OnKeyAction = 'keyActionPesquisar'
      WOwner = FrNone
      WOrigem = EhNone
    end
    item
      Modifier = smCtrl
      Key = sk2
      OnKeyAction = 'keyActionIncluir'
      WOwner = FrNone
      WOrigem = EhNone
    end
    item
      Modifier = smCtrl
      Key = sk3
      OnKeyAction = 'keyActionAlterar'
      WOwner = FrNone
      WOrigem = EhNone
    end
    item
      Modifier = smCtrl
      Key = sk4
      OnKeyAction = 'keyActionExcluir'
      WOwner = FrNone
      WOrigem = EhNone
    end
    item
      Modifier = smCtrl
      Key = sk5
      OnKeyAction = 'keyActionSalvar'
      WOwner = FrNone
      WOrigem = EhNone
    end
    item
      Modifier = smCtrl
      Key = sk6
      OnKeyAction = 'keyActionCancelar'
      WOwner = FrNone
      WOrigem = EhNone
    end
    item
      Modifier = smCtrl
      Key = sk7
      OnKeyAction = 'keyActionAnterior'
      WOwner = FrNone
      WOrigem = EhNone
    end
    item
      Modifier = smCtrl
      Key = sk8
      OnKeyAction = 'keyActionProximo'
      WOwner = FrNone
      WOrigem = EhNone
    end
    item
      Modifier = smCtrl
      Key = sk9
      OnKeyAction = 'keyActionAceitar'
      WOwner = FrNone
      WOrigem = EhNone
    end
    item
      Modifier = smCtrl
      Key = sk0
      OnKeyAction = 'keyActionSalvarContinuar'
      WOwner = FrNone
      WOrigem = EhNone
    end>
  InterfaceRN = 'CadAreaDeContatoRN'
  Access = True
  ChangedProp = 
    'edAtivo44801.Height;'#13#10'FrmCadAreaDeContato.Width;'#13#10'FHBox6.Width;'#13 +
    #10'hbListagem.Padding.Top;'#13#10'hbListagem.Padding.Left;'#13#10'hbListagem.P' +
    'adding.Right;'#13#10'hbListagem.Padding.Bottom;'#13#10'efDescricao.Width;'#13#10'e' +
    'fDescricao.Flex;'#13#10'FVBox2.Padding.Top;'#13#10'FVBox2.Padding.Left;'#13#10'FVB' +
    'ox2.Padding.Right;'#13#10'FVBox2.Padding.Bottom;'#13#10'edDescricao44801.Fle' +
    'x;'#13#10'FHBox4.Padding.Left;'#13#10'FHBox1.Padding.Left;'#13#10'FHBox1.Padding.R' +
    'ight;'#13#10'FHBox1.Padding.Bottom;'#13#10'FHBox4.Padding.Right;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object pgPrincipal: TFPageControl
    Left = 2
    Top = 75
    Width = 747
    Height = 426
    ActivePage = tabListagem
    TabOrder = 0
    TabPosition = tpTop
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    WOwner = FrWizard
    WOrigem = EhNone
    RenderStyle = rsTabbed
    object tabListagem: TFTabsheet
      Caption = 'Listagem'
      Visible = True
      Closable = False
      WOwner = FrWizard
      WOrigem = EhNone
      object hbListagem: TFHBox
        Left = 0
        Top = 0
        Width = 739
        Height = 398
        Align = alClient
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 5
        Padding.Left = 5
        Padding.Right = 5
        Padding.Bottom = 5
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrWizard
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox1: TFVBox
          Left = 0
          Top = 0
          Width = 739
          Height = 398
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = 'FVBox1'
          Color = clWhite
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          ParentBackground = False
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrWizard
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object grpBoxFiltro: TFGroupbox
            Left = 0
            Top = 0
            Width = 719
            Height = 74
            Caption = 'Filtro R'#225'pido'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            WOwner = FrWizard
            WOrigem = EhNone
            Scrollable = False
            Closable = True
            Closed = False
            Orient = coHorizontal
            Style = grp3D
            HeaderImageId = 0
            object gpFiltroPrincipal: TFGridPanel
              Left = 2
              Top = 15
              Width = 715
              Height = 47
              Align = alTop
              ColumnCollection = <
                item
                  SizeStyle = ssAbsolute
                  Value = 58.000000000000000000
                  WOwner = FrWizard
                  WOrigem = EhNone
                end
                item
                  Value = 80.924855491329480000
                  WOwner = FrWizard
                  WOrigem = EhNone
                end
                item
                  Value = 19.075144508670520000
                  WOwner = FrWizard
                  WOrigem = EhNone
                end>
              ControlCollection = <
                item
                  Column = 0
                  Control = lfAreaContato
                  Row = 0
                end
                item
                  Column = 0
                  Control = efAreaContato
                  Row = 1
                end
                item
                  Column = 1
                  Control = lfDescricao
                  Row = 0
                end
                item
                  Column = 1
                  Control = efDescricao
                  Row = 1
                end
                item
                  Column = 2
                  Control = lfAtivoEquals
                  Row = 0
                end
                item
                  Column = 2
                  Control = efAtivoEquals
                  Row = 1
                end>
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              RowCollection = <
                item
                  SizeStyle = ssAbsolute
                  Value = 21.000000000000000000
                  WOwner = FrWizard
                  WOrigem = EhNone
                end
                item
                  SizeStyle = ssAbsolute
                  Value = 21.000000000000000000
                  WOwner = FrWizard
                  WOrigem = EhNone
                end>
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              AllRowFlex = True
              WOwner = FrWizard
              WOrigem = EhNone
              ColumnTabOrder = False
              object lfAreaContato: TFLabel
                Left = 1
                Top = 1
                Width = 33
                Height = 21
                Align = alLeft
                Anchors = []
                Caption = 'C'#243'digo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrWizard
                WOrigem = EhFilter
                WKey = '44801360;44801;44801'
                VerticalAlignment = taAlignBottom
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object efAreaContato: TFString
                Left = 1
                Top = 22
                Width = 38
                Height = 21
                Hint = 'Filtro: Area contato'
                TabOrder = -1
                AccessLevel = 0
                Flex = False
                WOwner = FrWizard
                WOrigem = EhFilter
                WKey = '44801360;44801;44801'
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 0
                Align = alLeft
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = efAreaContatoEnter
                SaveLiteralCharacter = False
                TextAlign = taLeft
                ExplicitHeight = 24
              end
              object lfDescricao: TFLabel
                Left = 59
                Top = 1
                Width = 46
                Height = 21
                Align = alLeft
                Anchors = []
                Caption = 'Descri'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrWizard
                WOrigem = EhFilter
                WKey = '44801360;44801;44802'
                VerticalAlignment = taAlignBottom
                WordBreak = False
                MaskType = mtText
                ExplicitHeight = 13
              end
              object efDescricao: TFString
                Left = 59
                Top = 22
                Width = 400
                Height = 21
                Hint = 'Filtro: Descri'#231#227'o'
                TabOrder = -1
                AccessLevel = 0
                Flex = True
                WOwner = FrWizard
                WOrigem = EhFilter
                WKey = '44801360;44801;44802'
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 0
                Align = alLeft
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = efDescricaoEnter
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
              object lfAtivoEquals: TFLabel
                Left = 589
                Top = 1
                Width = 25
                Height = 21
                Align = alLeft
                Anchors = []
                Caption = 'Ativo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrWizard
                WOrigem = EhFilter
                WKey = '44801360;44801;44803'
                VerticalAlignment = taAlignBottom
                WordBreak = False
                MaskType = mtText
                ExplicitLeft = 619
                ExplicitHeight = 13
              end
              object efAtivoEquals: TFCombo
                Left = 589
                Top = 22
                Width = 80
                Height = 21
                Hint = 'Filtro: Ativo '
                LookupTable = tbSimNao
                LookupKey = 'FIELD_KEY'
                LookupDesc = 'FIELD_DESC'
                Flex = False
                ReadOnly = True
                WOwner = FrWizard
                WOrigem = EhFilter
                WKey = '44801360;44801;44803'
                Required = False
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = False
                HideClearButtonOnNullValue = False
                Align = alLeft
                OnEnter = efAtivoEqualsEnter
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
                ExplicitLeft = 619
              end
            end
          end
          object gridPrincipal: TFGrid
            Left = 0
            Top = 75
            Width = 729
            Height = 371
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbClienteContatoTipo
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = True
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrWizard
            WOrigem = EhAttribute
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            ActionColumn.Title = 'A'#231#245'es'
            ActionColumn.Width = 100
            ActionColumn.TextAlign = taCenter
            ActionColumn.Visible = True
            Columns = <
              item
                Expanded = False
                Font = <>
                Title.Caption = 'Alt.'
                Width = 30
                Visible = True
                Precision = 0
                TextAlign = taCenter
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{8B7BF587-1EE6-4E0E-ABF9-FC88C09492B5}'
                    WOwner = FrWizard
                    WOrigem = EhNone
                    ImageId = 7
                    OnClick = 'ClickImageAlterar'
                    Color = clBlack
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{492F3B6D-C61D-446E-BB52-A45F22803790}'
                WOwner = FrWizard
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                Font = <>
                Title.Caption = 'Exc.'
                Width = 30
                Visible = True
                Precision = 0
                TextAlign = taCenter
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{6A491ACD-99A0-4B98-9D9A-41EA44FEC093}'
                    WOwner = FrWizard
                    WOrigem = EhNone
                    ImageId = 8
                    OnClick = 'ClickImageDelete'
                    Color = clBlack
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{D20EDC87-8497-42E1-972D-92CA373B5CE2}'
                WOwner = FrWizard
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'AREA_CONTATO'
                Font = <>
                Title.Caption = 'C'#243'digo'
                Width = 144
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = True
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{9DF48609-096C-45C3-B649-B93F662188ED}'
                WOwner = FrWizard
                WOrigem = EhAttribute
                WKey = '44801360;44801;44801'
                Hint = 'Area contato'
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'DESCRICAO'
                Font = <>
                Title.Caption = 'Descri'#231#227'o'
                Width = 550
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = True
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{7CCE982C-6D6C-4579-8E2B-C01A584B38F8}'
                WOwner = FrWizard
                WOrigem = EhAttribute
                WKey = '44801360;44801;44802'
                Hint = 'Descri'#231#227'o'
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 1
              end
              item
                Expanded = False
                FieldName = 'ATIVO'
                Font = <>
                Title.Caption = 'Ativo'
                Width = 60
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftCombo
                FlexRatio = 0
                Sort = True
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{41C4862F-C8D3-48B3-89EF-14C8AA12269E}'
                WOwner = FrWizard
                WOrigem = EhAttribute
                WKey = '44801360;44801;44803'
                Hint = 'Ativo '
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 2
              end>
          end
        end
      end
    end
    object tabCadastro: TFTabsheet
      Caption = 'Cadastro'
      Visible = True
      Closable = False
      WOwner = FrWizard
      WOrigem = EhNone
      object FVBox2: TFVBox
        Left = 0
        Top = 0
        Width = 739
        Height = 398
        Align = alClient
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Color = clWhite
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 5
        Padding.Left = 5
        Padding.Right = 5
        Padding.Bottom = 5
        ParentBackground = False
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrWizard
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object grpBoxPrincipal: TFGroupbox
          Left = 0
          Top = 0
          Width = 729
          Height = 67
          Caption = 'Cliente Contato Tipo'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          ParentFont = False
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          WOwner = FrWizard
          WOrigem = EhNone
          Scrollable = True
          Closable = True
          Closed = False
          Orient = coVertical
          Style = grp3D
          HeaderImageId = 0
          object FGridPanel2: TFGridPanel
            Left = 2
            Top = 15
            Width = 725
            Height = 62
            Align = alTop
            Alignment = taLeftJustify
            ColumnCollection = <
              item
                SizeStyle = ssAbsolute
                Value = 145.000000000000000000
                WOwner = FrWizard
                WOrigem = EhNone
              end
              item
                Value = 100.000000000000000000
                WOwner = FrWizard
                WOrigem = EhNone
              end>
            ControlCollection = <
              item
                Column = 0
                Control = lbAreaContato44801
                Row = 0
              end
              item
                Column = 1
                Control = edAreaContato44801
                Row = 0
              end
              item
                Column = 0
                Control = lbDescricao44801
                Row = 1
              end
              item
                Column = 1
                Control = edDescricao44801
                Row = 1
              end
              item
                Column = 0
                Control = lbAtivo44801
                Row = 2
              end
              item
                Column = 1
                Control = edAtivo44801
                Row = 2
              end>
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            RowCollection = <
              item
                SizeStyle = ssAbsolute
                Value = 19.000000000000000000
                WOwner = FrWizard
                WOrigem = EhNone
              end
              item
                SizeStyle = ssAbsolute
                Value = 19.000000000000000000
                WOwner = FrWizard
                WOrigem = EhNone
              end
              item
                SizeStyle = ssAbsolute
                Value = 19.000000000000000000
                WOwner = FrWizard
                WOrigem = EhNone
              end>
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            AllRowFlex = True
            WOwner = FrWizard
            WOrigem = EhNone
            ColumnTabOrder = False
            object lbAreaContato44801: TFLabel
              Left = 113
              Top = 1
              Width = 33
              Height = 19
              Align = alRight
              Anchors = []
              Caption = 'C'#243'digo'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrWizard
              WOrigem = EhAttribute
              WKey = '44801360;44801;44801'
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
              ExplicitLeft = 112
              ExplicitTop = 3
              ExplicitHeight = 13
            end
            object edAreaContato44801: TFString
              Left = 146
              Top = 1
              Width = 38
              Height = 19
              Hint = 'Area contato'
              Table = tbClienteContatoTipo
              FieldName = 'AREA_CONTATO'
              HelpCaption = 'C'#243'digo'
              Help = 'Area contato'
              TabOrder = 1
              AccessLevel = 0
              Flex = False
              WOwner = FrWizard
              WOrigem = EhAttribute
              WKey = '44801360;44801;44801'
              Required = True
              Constraint.Expression = 'value is null or trim(value) = '#39#39
              Constraint.Message = 'Campo C'#243'digo, preenchimento '#233' obrigat'#243'rio'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.GroupName = 'grpTbclientecontatotipo'
              Constraint.EnableUI = True
              Constraint.Enabled = True
              Constraint.FormCheck = True
              IconDirection = idLeft
              CharCase = ccNormal
              Pwd = False
              Maxlength = 3
              Align = alLeft
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              OnExit = edAreaContato44801Exit
              SaveLiteralCharacter = False
              TextAlign = taLeft
              ExplicitHeight = 24
            end
            object lbDescricao44801: TFLabel
              Left = 100
              Top = 20
              Width = 46
              Height = 19
              Align = alRight
              Anchors = []
              Caption = 'Descri'#231#227'o'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrWizard
              WOrigem = EhAttribute
              WKey = '44801360;44801;44802'
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
              ExplicitLeft = 99
              ExplicitTop = 3
              ExplicitHeight = 13
            end
            object edDescricao44801: TFString
              Left = 146
              Top = 20
              Width = 550
              Height = 19
              Hint = 'Descri'#231#227'o'
              Table = tbClienteContatoTipo
              FieldName = 'DESCRICAO'
              HelpCaption = 'Descri'#231#227'o'
              Help = 'Descri'#231#227'o'
              TabOrder = 2
              AccessLevel = 0
              Flex = True
              WOwner = FrWizard
              WOrigem = EhAttribute
              WKey = '44801360;44801;44802'
              Required = True
              Constraint.Expression = 'value is null or trim(value) = '#39#39
              Constraint.Message = 'Campo Descri'#231#227'o, preenchimento '#233' obrigat'#243'rio'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.GroupName = 'grpTbclientecontatotipo'
              Constraint.EnableUI = True
              Constraint.Enabled = True
              Constraint.FormCheck = True
              IconDirection = idLeft
              CharCase = ccNormal
              Pwd = False
              Maxlength = 100
              Align = alLeft
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              SaveLiteralCharacter = False
              TextAlign = taLeft
              ExplicitHeight = 24
            end
            object lbAtivo44801: TFLabel
              Left = 121
              Top = 39
              Width = 25
              Height = 19
              Align = alRight
              Anchors = []
              Caption = 'Ativo'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrWizard
              WOrigem = EhAttribute
              WKey = '44801360;44801;44803'
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
              ExplicitLeft = 120
              ExplicitTop = 3
              ExplicitHeight = 13
            end
            object edAtivo44801: TFCombo
              Left = 146
              Top = 39
              Width = 80
              Height = 21
              Hint = 'Ativo '
              Table = tbClienteContatoTipo
              LookupTable = tbSimNao
              FieldName = 'ATIVO'
              LookupKey = 'FIELD_KEY'
              LookupDesc = 'FIELD_DESC'
              Flex = False
              HelpCaption = 'Ativo'
              Help = 'Ativo '
              ReadOnly = False
              WOwner = FrWizard
              WOrigem = EhAttribute
              WKey = '44801360;44801;44803'
              Required = True
              Prompt = 'Selecione'
              Constraint.Expression = 'value is null'
              Constraint.Message = 'Campo Ativo, preenchimento '#233' obrigat'#243'rio'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.GroupName = 'grpTbclientecontatotipo'
              Constraint.EnableUI = True
              Constraint.Enabled = True
              Constraint.FormCheck = True
              ClearOnDelKey = True
              UseClearButton = False
              HideClearButtonOnNullValue = False
              Align = alLeft
              Colors = <>
              Images = <>
              Masks = <>
              Fonts = <>
              MultiSelection = False
              IconReverseDirection = False
            end
          end
        end
      end
    end
  end
  object FHBox4: TFHBox
    Left = 2
    Top = 48
    Width = 750
    Height = 18
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Color = 15592941
    Padding.Top = 0
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 0
    ParentBackground = False
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrWizard
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object lblMensagem: TFLabel
      Left = 0
      Top = 0
      Width = 67
      Height = 13
      Caption = 'Mensagem....'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clNavy
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      WOwner = FrWizard
      WOrigem = EhNone
      VerticalAlignment = taAlignTop
      WordBreak = False
      MaskType = mtText
    end
  end
  object FHBox6: TFHBox
    Left = 0
    Top = 0
    Width = 514
    Height = 43
    Align = alTop
    AutoWrap = False
    BevelInner = bvRaised
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stRaised
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 2
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrWizard
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitWidth = 754
    object FHBox1: TFHBox
      Left = 1
      Top = 1
      Width = 654
      Height = 38
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Color = 15592941
      Padding.Top = 6
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      ParentBackground = False
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrWizard
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox2: TFHBox
        Left = 0
        Top = 0
        Width = 13
        Height = 33
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrWizard
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnConsultar: TFButton
        Left = 13
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Executa Pesquisa (CRTL+ 1)'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clHotLight
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnConsultarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004BD4944415478DA9D955948636714C7CF4D62E292C47D19199BAB1444
          A6A363C1818A2B8856EA8E4A952A06D427A14F3EF4D127518A880BB850B562A5
          3EF8A80F76AC11DC1DC4655C06B154A341E3BEC6254BCFF932B9F5AA38A51F5C
          6EEE77BFFBFDBEF33FFF73C2D96C36E8E9E991A5A6A6FEE1E4E49400FF614824
          12B8BABA5A191E1E7E5D5050607D6E2D4780C1C1C1A0F8F8789DABAB6B083D7F
          6ED09ACBCB4B30180CBD333333A5C5C5C5A66701FDFDFD7C4A4A8A4EA9546AAC
          56EB6701777777603299E0FAFA1A767777FB2A2A2A7E181B1B333F0B484E4ED6
          A954AA470092E3E1B8BDBD85A3A323F0F4F4848B8B0B585F5FEF89898929319B
          CD9627017D7D7D3CE640A756AB4500DA9C9E777676D86965322928952AF0F2F2
          02A3D108EEEEEE80B2C2F1F1316C6C6CFC3E35355582D1DC3C02F4F6F6F26969
          690280E338766D6EFE0D6B6B1F715325787878B03993E91AE47227C068D90114
          0A05A039E0F0F010B6B6B67A878686CA6A6A6AAE440074119F9E9EAEC3133100
          7DB8BABA0A7ABD1EA2A2A284CD68D0FBD3D353D8DBDB03373737364F92C96432
          72161E682D373B3BBB5F04E8EEEEE633323274784A0D4DD2C70B0B8B909090C0
          64A1A48AC2E6242CC9676767A056AB00B567CEB2582CB0B2B2529A9898F88B08
          D0D5D5C5676666EA30690C303E3E0E1A0D0F010101F8A15D32FAF85F07DBD8EF
          F3F30BA0C028428A8CF2B4B0B0508207FB5504E8ECEC64004C9E864EF5E1C312
          BC79F3F5275938B0E7050400DDE9D5CDCD0DD6C305F8FBFB83542A65CF5817DA
          B8B8B82E11A0A3A38301BCBDBD35240F260B5EBDFA0A37E2D8C60F87D56A63F3
          24CDC181115EBC08C0643B33C0ECECAC168B560C686F6FE773727204805EBF0D
          21215F3269E8641209774F1A1B6E6CD79B40C7C7461601D995724511600EC480
          B6B63601400E595D5D83D0D030269154CA319908466BE9A28D2D167BBD6C6DFD
          05C1C1C1E0E2E2C200580BDAA4A42431A0A5A58501C84564B7F7EF67D1F75E98
          E860FCE89639C901A08DCDE63B94C405DB840125DA87F0F0701611D975626242
          8B452B06343737F3B9B9B9AC0EE8D494B8999959CCC36B78F93250482C398A0A
          9D6C6A306C836EE41D7C9BFA1DF8FAFAB21A2000F6242D16AD18D0D0D0C0E7E7
          E70B952C972BB0320F606969097C7CBC2128E80BB4A29A45717E7E0EDBDB7A98
          9E9E86136C116161A110F5F61BCC831F6B19A3A3A35A54E331202F2F4F879529
          F422B95CCE4E849509FBFBFB9FA2B0319DA9C991EEF3F3F3D0DADA0A111111A0
          D56A51520D0C0C0C68510D31A0A9A989E5009D2000689E7A8CA305D04573944C
          2A2C85428E4DD000D5D5D530323202E81C282A2A026767E7EF232323FB4480FA
          FAFA4711382034C8AA8E06E8680974276B52CFAAADAD85C9C949888E8EA64EFB
          23E6B44104A8ABAB7B1260EF3B1C3CF72F47D12C2F2F4355551559140203038D
          58800598BF3F05406363239F9595A5C3F034CF6DC63D51D63447AD7C7171112A
          2B2B616E6E8EF2777572729285397CC700480F292F2F9F469BFA383AE34399EE
          3F3F7CEF806015435959193306CAB889EFDE32406C6CACAAB0B0F0673F3FBF30
          94E89664B257ACF55EF55A858B06E5C171C7F7368448D11437F8E7F51BE6A314
          FBD23CAEFD89739C0617C8F0E64CBD0CFEDF20FD2CB8DF354AED850630E1FFB6
          E91F70B7FB1897F803840000000049454E44AE426082}
        ImageId = 13
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
      object btnFiltroAvancado: TFButton
        Left = 46
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Filtro Avan'#231'ado'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnFiltroAvancadoClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000031A4944415478DAB5965F4853511CC7BFE7EEBF9A492624B94C61FDC1
          97D47C49374B8D42130CA187E825C269F4474C29CDA6A82DFF408886948BE8A1
          F02D08FC43915A6E1A98FF7AD0974C48F620A8858973DBDD766FE7DE6928BA31
          977DE1F23BF79EF3BB9FF33BE77E774678AAB1B131E4E6E6626E6E0EBBA1E8E8
          687476762239391964747494D7DFBC8399B07848F747819348C0C964E0A534CA
          657049156064843E9782A77D8288C703C6E306E7E221733BC1B02E10B7075287
          030C8DAEC579C4DB67616A6B06A134DE76320F69A56588A049EBE21826E01933
          1C27C6553A0956886E82AFCDF5508D7781D07B7E4F9111CA634970ADDA825E16
          875C898B1366E42F7C47C7D1447413159CAD656B804B65901E3A0EB8D9A0014B
          8A10548F74A25AA340C9AC042D5171E05FD57801AACC02280FC46F18FE9B5E7B
          77DC0E77DB10B73C8F99C8182CCE5961EF7BEE05E8F20B1079F80808EB08BA02
          412E460227DD875042F0F3C73798DFAC01EA1F37233125054EC7BF01D6A5502A
          313132828AD2122FE0E5EB0EA46AD361B7AFEE0A40A50AC190650057AF5CF602
          DE52539CC9C8142BF0B858486472B43F7B8A9EAE6EB0ACD3EFCBE47205B22FE4
          A0B0E8FADF5CA1828FFD7DC8A3E615011F7AFB90959981E5553BE41206AC8743
          A3F12106078770BBB8D82FA0B5A5056969A9305455897982C24354E8EDEBC7D9
          AC4C2F60C06C864EABDD926C32993035358516FA92ED544CE1090909D0EBF55B
          FACC160BD2753AFF004195959562341A8D013DDF1660A600AD0F80A0C2C24268
          341A9C484CA26B2EC397E1614C4F4FA3BDBDDD678E85027481020465676743AD
          568B6DABD58A9E9E1EBFE3770C10AAA8A9AD15DBD57443FDCD3E68C0BDF272B1
          DDD8D0B03380BF4D0E1610F057B411207C8E076362025AA24D8075A3399CBE5D
          3B353929FA4290923AB5B1A9C9E758A542B1D968DDEFDE23E3743A56A893FD89
          1EDF7850510E756CACF8D34008D9765C187572FFA701E49C3FE73D326FDDBD8F
          1BFA6B58F8B58440C4713C1886F8EC8FDA178136D30B3C697AE43DF40D060392
          4EE9101A1606C2B90382F8AC9291C2B6B282F1CF66D4D5D581FCEFBF2D7F0022
          9575683CD811980000000049454E44AE426082}
        ImageId = 50002
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
      object btnNovo: TFButton
        Left = 79
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Inclui um Novo Registro  (CRTL+ 2)'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 3
        OnClick = btnNovoClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000046A4944415478DA8D967F681B6518C7BFEF5DDAA5AE6E36D58EC2DA4E
          BA5A1D42BBE93A990A56F6873237D81036D0BF1404C76C374BA508ADCC8D29FE
          530A8AEC0FC58242152B4341879B0C6BE956D0DDADBF67D35FA95996D4A5499A
          E4EE92BBBCBEEF2517EFB254FB1CC7BD6FDE7B9FCFF3E37D9E0B414E4856446C
          440420B33FE3C64B78C3B5C5F54AADA7761757E10BFB26F5B8FE1516F1293E86
          62EAB56D7BB4BBBBFB2CA5D4A98C10C74B5C7C311FFACBFBF71E78EE40DDF1C6
          E3B85F2CE79BC89A11C7C0AD017A65ECCA14D238882E2CD9F736AFAE46244555
          210884E63CE29ED9C750D20A7DFEA75652595B490F3DFC027119693CE56E3501
          D7D5ABD0C512FAC3C22532228F4C62062D0E402412BDA16A1C20C05268BFB9F4
          C97DF820781E2F3F7E18D56E116E4DC3B18A77CDB5AF57CF43DDB40901D5C0B7
          13DF43FB45EB7200A2D198A4A534D36AA18807462683677F7C86CE5578C9139B
          EBE80EB748B6A674BC5EF3396571249FF95E43B4D445175583FC9158827A4D1B
          7300626B6B528A594404A16888946412DB2F6DA7C6E634E9F3F4D23D35BB89C8
          343C56DD6486683A70130605BDB12C91F6F069646248D9014D6B1C904EE74322
          1484281A8DA26EA80E99521D7D15BDD85DD30C0ED855DD6C2A980AC81C006959
          46FB2A03A8D01D80783C2E738060599DCD45DE0366009EFE6D3F9D77CF916DCC
          D29DF7816CCB005D0D574D0F3E9C6D455000F52641824C339DC6AC139048C869
          0B209859F817C6C67CAD73B8935E485F20656CFF8E7206601A3E7A240B78E74F
          0660E0C5388859045FE0130720914848BAAE6743C2AC178A9C22694AC2D1DB47
          11D00228D3010F2BBAF71A7BCDB533B74E23CC3C525C6C12C66D9CC461072099
          4CCA268025397F8A6C632EAAAAD2C1E141D22576D1A01A2424068869981E1825
          CCAD2D6C1C43109DECBA834127405124230BC85B6FD544AE9D98CF582C86A1D1
          21F427FA315A368A10BBB854B16B9FB20F175FBD78121ABEE4AF3A008AA2C886
          6138AC66009A6B5516C49CA75229EAF7FBC9FCFC3CC2E1B0F99BC7E3417D7D3D
          6D686868612DE7F7C25ED4C4DA849CE1005E68B9245BA7C8E6813537956658F1
          B1DB1C8BA2681AE472B9F6B0DFE47B002CBE32AF56C106B014167A603D7973A4
          59C91921D0D2D29275009A26710F0A7B91657D612E7838F9A160E132C75C4457
          092A1ED85A1CA0699ACC5D261BF08029A6CC63C2AB3B1289507640080B0D8647
          AE5D7FBBBDED0803DE591720D87A911D60CDD966C20E040D8556CC24B339ADAA
          7A884C4C4ECCB4BDD5768841BDF9CF490140CAB098162B30FB98BD87BB77C3F0
          7ABDA87CB0123BEBEB71F9F2CFCB6F9E38F1A2FF2FFF944DE7BD1EF0A4157C64
          1CA78867931524F12D2FD3E9E919D2B2F7498C8F8FF9DB4F9D3AE89DF5DE4481
          3801A9944C6D39B0626E07F023C99A22999B9BA7C15088A84A62AEA3A3E3C8C2
          C2E2388A482140CA01D60D11CF11036061C987A1A15FE573EF9F39B6B2F2F72C
          D6113BA0B1A7A7E75C7E8190A21B780853BA81403014FDEE9B81B3AC852FE13F
          84148C37F6B725C762B7F17F2FFD034C50719467FC49DB0000000049454E44AE
          426082}
        ImageId = 6
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
      object btnAlterar: TFButton
        Left = 112
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Altera o Registro Selecionado  (CRTL+ 3)'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 4
        OnClick = btnAlterarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000005094944415478DAAD956B6C536518C79FE7F4D0DDC82ED95A262BB0AC
          026EC16D201035121308514CCC844020881335442E11818D6BD84C4087CB3E90
          4C037E21A21F34442111A209E3E218868B894047071B3BDD4AB76EACDDADD773
          DAD39EC7F7F4E65A36C2079FE4DCCFFBFF3DB7F77D11FE476B5DB76259B63FFD
          3CA728D7291CDEB9B0A5C58AF18F1A8D465F5757F70511258F42047C0EF190B7
          1B372F685B1FFAAD38CF67B2A35796074196DF9838D63836362E8892041C8784
          5163FA49F72A8C20F214BDAA03C3012729E6B53830E8849AC634DA6A1BC5E93E
          1F8478FE4212607CDCD52D0554000709C189473CA2A86EE459098B106CDF00A2
          DB0A1F7CA5815E7B0066F276D89F0964EAC4F3490097CB2D04828188D7DC2411
          40FC59D56657A0308AE6ADA011EFD2278D1C9A1E89A048763ABD5F0E8B226F5A
          7B48AE4A02B83D1E211860008E9B3C459100309122FFA32398215E81DA131C5D
          BA3982A2D701273EF75361BEC6B6FA90BC667084BA26024A3C2A40961329E126
          4953BCE852DF0F90E6FA198EFF9A09A7CF09E0F7B9E068F528AC7CAD0056EE74
          7CD865A38BECB7B12480D7EBB5A8002EEE75B4164F1539E8BC82BCE35B3A7B43
          87479A6F422020D1F6779EE0A6B73361A4A0892A977DBA9CE9FDC30E6F32C0E7
          B3C8710017A9C27FB01820E4BE4FD0DF88B78599B4A3BE05032CA5554B07A876
          830687B26A000B56D3A2458B5E657A77D4EE4D02F87C3E21140A453D65DEA7A6
          88151094C70D2038F2E1A37D17C1EDF6C2EBF39CD0B04586E1B48DE09E5E0D85
          8585307FFEFC054CEF41BCD31200BFDF6F89005891135D14BB57641784ACC760
          D89341D57BAFAA3D4F65063736EFF0823F633959B9EDA88ACF993387F47AFD3C
          455184A701A22884A38084F7EA9C002508416B13484184CDFB5BA14BB083215F
          84933B4781CF29877BFE5DA0D3BF002525259108D2D3D3E73280E52980288A96
          70389C1C010205ACC7D93904DBEBAFC1AD3B56CA9B2EE3C9CF86495F68C06BCE
          5D9057308B8C4623161717435A5A1AF13C3F0540922C8A0A50275AACC801DB77
          C4830B0F345E873F5A7B2143AB50F3362796CDCDA1DFADDB3033D7A87A4EA5A5
          A5989595A5EA4C0D901820AC2891CE510192FD0C68C55BD474CA8C3F9E1F60EF
          098E7D3C4ACB17F378AEB39A28A31459CEA1A2A282727373631173A4D54E9B02
          1008086A04F1B568C0FC13FCD9F23D349C1A501D83DAB52E787F6518CE9AAB60
          9C2AA1A8A8082A2B2B41A7D301138CAECAFC34C8CBCD991CC07ADAA2FEA8A6C8
          EF7A8243D656103AFFA6A3CD5771D52B63B06F830417CC6F92E05EAA760C9597
          97A34EAF07AFC743AC4190A506FEBA71B375EF9EDDEF058341D7940016010D0A
          6DE8B0B68067E401897E172E9CD5079DC3F3C034B28A66CF9E8D068381B2B3B3
          B1BFDF0EAC31586BEAB0A3C3DC5E5353BBCAE9740E24B6931480A0B00D476DD1
          53DF1C80B2223658F683A29D0B9A9C25A037BC0CFA193340ABD5B249E6819E9E
          1EC82FC887178D46B87CF99275F79E3D2B846EA13769BF4A8D40DDD11E5BADB4
          7ACD1ADCBC7E196CDCB4857267BC84ACFDD45D0FD8776213126D7D7DF4F06127
          2E5DB218CCE6FBB683070FBE6532B577A6EE74C98060D0422C45A67B7749C3F3
          5851519958ECA27B0DAAC524B628A2C5D243430E074A92BFABEEF0E1773B3A1E
          74C324960A10285AE4E4253A261E03000340EF631BB4B55DBBDDD4F8F53A5687
          7E98C226028AEAEBEBBF8CBF8B0BA79A9AC260280C83438EF173BF9C69F0B8DD
          0E7886A5AA70F0FC46B1E399F62F6A8BA82D8608FAC10000000049454E44AE42
          6082}
        ImageId = 7
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
      object btnExcluir: TFButton
        Left = 145
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Exclui o Registro Selecionado  (CRTL+ 4)'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 5
        OnClick = btnExcluirClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000040C4944415478DADD955D4C5B6518C79FF79C7EAC2D1F1DED6A0714DB
          B51DA5C810506250468A0A9A4CB3E885268652A98BD178B3A98BD311B3B8442F
          8CEECEEC032C5DA66E5113B338B31AD814CDB61841663F28E3F0D1A1B44821DB
          282DB5F0FA9C0354C533D4446FF626BDE8F33ECFFFF77CF52D81FFF9905B13F0
          31CB166B1A1A7C098EFB76361279B685D22531BF0E0046ABD31DCEAFA9B9377E
          FE7CD3E3C9E4C4DF023E0230B0003DF6E75A2CB14BDFC3745FD09300703F0DF0
          27C86114DF880C4D85D555F8A083FADF39C2A143E31300576F0A380150CC009C
          2B7DD4612EBBDB417ECD53C08523EFD1E9C048D72C425E58811C42F14DBC7899
          C955E772838C4869E8876FC8E007A787D1C1F114C08428C00B10B438AA6D96EA
          06A0999584F50570E9B8078682A3DE30423268AA44717399D159FB244ACDCC09
          6E4C6E0E8C0CF541F8D417834E00BB28E07D80A31B8DBA672A1F6AA68B2976F5
          8EB2C53AE2FBE414044363C7794385EDF696C69D8FD1A5E86CD6476E2A24FD27
          3F847870F418B6739728E02D4C4483D9151837B5DAB7D793F475A10A4AD18F31
          6D86D3673E0706BFEE687E982C45A6E86ABC5CAFA6C1733D241E8E786E60957B
          FE30AFBF0CF96016A26DB5DD55030BF1B4605FC48FC45228E016B94904F15406
          94E64208747F09712EDAC58BEF5DB30CA26B7A002105FC108D1A57A9DD4653D1
          A4E02744124255C5C504144ACAE46748A8FB6B981E8E797012EE7D6BC46F0AE0
          4FFB32A4536BD4386D362BCC47AE0391CB8162D689F171906A59984CA6213E3E
          EB45F1B67611F17501FB119087001D02AC1B242431185BBD127AAFB2E9E99554
          864C8D4D772511F0FABF01BCB22CDE719B51EBAA5248E9426892D09539085BC5
          378ADF1C7B11E9C72A62A3BF7810E27EE39FB4E8657EA5515C6FD2B9EA1532C8
          0427848CF9C84479091F4095810861562A91A0AD3795A151EE67BE12F79BEB0D
          798F42C6E426D31D9BCD45ADCD0A29807F2CDB931BDBACB03B9110420EA95490
          7B79E8F7C06D5BE12C56121D1EEF526ED9E2DECB71E26BFA5A91F69841AD76EF
          94B0543210CEFE88AEDD594E5E44F16B57C6BC7CA4DA6A72BE9BA3A279FDFEAC
          4F067DCECA3600A3D674B6F87C6E51C0FE125DB0B6B2A2ACF1423F85E919E16E
          AEAA82BE944890A9A1112FF6BE2DB3BCAE9D855BCD2D6FE7A888AAEFF272915A
          0DB9D850073F7E3710DA1D89883F15ED4AB9819D5FE8B9E7FEEDE6FA8100CC97
          14C1C1C43C70E1613EF3B6332BFD6DC2394910622BB538F7A994A08CFC04BD95
          77C0C5EEAF38562673B4A7D3E28F1D7F70DD0C68ECA97EA4C9E21F19077F20DC
          859D6FFB6CCDF07620241F2155F6D2D6BA8A72EA3BF92987B36A3CB0DE73BD7A
          5E35E98D542EF5A526677AE373C95DDEC525D11D6F6559A6D660386A379BEF0B
          F9FD0F3C1F8B5D5DEB738BFE27FF97E7376E298F288718CA1E0000000049454E
          44AE426082}
        ImageId = 8
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
      object btnSalvar: TFButton
        Left = 178
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Salvar  (CRTL+ 5)'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 6
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934
          31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2
          D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE
          6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5
          6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013
          090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF
          349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E
          8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871
          175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39
          FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622
          F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673
          BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E
          01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146
          0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219
          506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6
          0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991
          CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313
          9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0
          39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D
          AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7
          C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743
          FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE
          E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660
          474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF
          D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61
          33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E
          0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE
          F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF
          921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5
          A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E
          9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163
          8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09
          C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C
          D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E
          23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA
          FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E
          9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1
          5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A
          25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2
          FF02B065C443D9FE4B070000000049454E44AE426082}
        ImageId = 4
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnSalvarContinuar: TFButton
        Left = 211
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Salvar e Continuar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 7
        OnClick = btnSalvarContinuarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000005FE4944415478DAB5557954546514BFF7BD99371B30808A9080DA2925
          72D70C4134528F8A0CB8800B02117A723905B479424AFBC7E568E49AB96B2EA5
          A0861B5A4A258887724549CD1DB344901950646698E1DDBEEFC14C8EF96F6FCE
          B7BC79F7BBBF7B7F77F910FEE7079F7E999B9333CB6AB5060882083DFB0E80F0
          1EBD4052A900058109B21FB221B4AD4C9E48E693B2125B5B5A9C50597909CE9F
          39CDF62D6034FAD47A00A4A7BD25D7D4D4604848088D8C1F8FD1D16F802020B9
          0CE113D34D7C76DEBE4996AD1BD130280A8C23479120494C90A0B8F8273A5CB8
          5791D76834E4013035395976389C60F4F181D8B18930E0F54878627378BACC4E
          381B6F83B13C131AF37560BF750FECE13DC12F6F1518FD7CA0FCD44938B8AF40
          919524C993A28CF4B7A96D4BA6099370E48861200A82DB033EB7D8EAA8E9EC18
          7C50FD372D592861C65D0BC8B24C21595918FCD97C38FAE3713AB8375F91574B
          123D4B911B20216932C68E1A01AAA700E4161B349C1E4FB6FAAB98BA40A48AAB
          161CA0AB870C3B52506C0AF6FD7A1D1C67141DD8B31BDB3CF004484B49955DFB
          84A429101139D84D110FA4743B1B8CF21998B15484D2736600472D6CCF4508F4
          0D87C6F01DD0B173089CFDB50C0EEED9AD9C51ABD59E14A5244FA55665440913
          93312626C61D64C7AD25E0653D0239EB880A8BEF6363430DADFFC889FD7B0543
          43E836320684A1A4D1C2C9921374686FAB070CC0D383299326BB298A4F9A8283
          87B02C4224AA29403F6B3EAC2954C19A9D97C95C578B8BA75B69F4605FBCE7BF
          1AB4ED0790AFAF2FCB5E11CACB4A18C0AEE7034C4A9AE8A6C89438057ABD1609
          B2A50C5E1276C3E1DFBC2177E92F60369BE1BD8406C830E9E012E582CD6B08F8
          F9B7039DDE001AB50895E7CAA168EFAEE75394342191DA08A7B8A4641CD82708
          829DDBE9C26D3F9CF94921D431E5F1031FD2A7E96AFC439E4116AD09FDDB75E0
          00849C4B566C15A7CBE8C8BE568A542A95A70713C68E737B3028AA174C8F75C2
          FD7A1F48CBCA87EA0735D0AF6B0DACCC44385B3D102A1A4CE0ED6354AC174515
          389D4EA57AEF5CBF02D72A2F283A54CF7A30363E41F140AF219A37BB2B7AB7EF
          CE94EFA29BB7EE62A8FF43D8F4B1032AAADAD3B2FDA128491A32E8F5C869E03E
          F344680BA03BADFFE3417C9C89542CEDE74C0BA5B09E9138EDC31D70BEE21AF9
          6A2DB8654E1358AC46C8D9124282A841A69CB45A2D2AA5CDB9E10DAA8D5ED75E
          258A9E00A6D8587A67821F0C1B9D48D9B9DBB0B8E4224842236DFCA0017D7C0C
          90B5B63338643D797979A1B7B7373F8C326F76AC92193DC82A5AA96AF63CDF83
          0D0B47C96919B360FEE29DB0E7D03976DA062B669BA1EF2BDEB0F54C22A02604
          020303212C2C0CDAA8510C6E6E6E86ECCC4CE05D98EF3B060428DF4451FC3706
          37BEEF96D6A9CFBBDFACDE5202EBBEBDC08C72E2E7290F212E4AA2B9DBBBE343
          EB0BA0D7EB212535950C060323A1B5005D567F363717B9429BCDA67C6F0368F5
          60F7CA9962FF7E817F959695765CB0A14A39303BCE8C33E209369546D1B22D95
          4A3019A7B071F3664531A38458E6F09567102D5AB05001B03300D6A63D014A0B
          E60DEDD0E5E59F4B4E14C3971B4E8269603DCC4F6F868DC7BAC0657304FC70E4
          2878791994D8AD58B54A4949575ABAF6CBF3F2DC14F136CD87126F0E70AA70DE
          72B564CF7C54F73B563FA885913D6ED0AE62C4EFCABA41F49021B4AF600FEA74
          3AB035DBE18BBC3C97E5E47038902B672B7DB57295E201F74652AB5BD397D188
          C9E36284E1D15DEFBC18F438D8D9DC88758FBDE058E99F547EB101A30647C318
          531CBD9F958DDC325125C2C2458B1400AE88678E0B60FDDAB528320F3830A788
          DD23AD00C3864644D45A1A4FC54619AF5EB96EDE7FE7BE90C5BE680526101119
          096F0E1FA624818B92A6A6260F8A5C63D3FA0DFC7252686100CA19AE03BB76EE
          946AA97FA466C129B2DA1D1DF53A5DB6BF9FDF50CE67EFDEBDD5E13D5E55B18E
          2A22FF839D610A049959C9222DF30B8E5FCF2C739A0FEE3F60B3DB6DBCD590A8
          882ACF031E372D9336F0BAE00D900D5F36749CCFF0F0706D5050908AF12FF0AA
          E59738BFA518BF322B2299BDB7B07787C562B11615153DA9AAAA929F2A2B9E6D
          B67F007731E966AC4002910000000049454E44AE426082}
        ImageId = 22001
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnCancelar: TFButton
        Left = 244
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Cancela as Altera'#231#245'es Correntes  (CRTL+ 6)'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 8
        OnClick = btnCancelarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004EF4944415478DAAD566B4C1C55143E7766F6BD145020744BA98F1608
          A820DA508BB42A84B62A264D6A24D158E323C646230D4D7D24901AA8466BD336
          1A9AF84B1B7F580BD49ADA6A0B518A3450915D0BE9F2461E5BDEEC93DD9DDD9D
          39DE997D53B49A7893D9993B73EFF9CEF79DC75D02A141828385FF6120A2402F
          94EDC6BCCFA9A9A9A90BBD8F0E42E216DD6E5027A1BEBEBE4614C5FE95000556
          ABCDE8F17A8161088618491B629FA50D28D30DDD4346306C4BA150A05EAF2FA4
          00A65B006C367B8F9797001888188CBDC28C827623F358431CC7814EA75B1DC0
          6E7718791F2F7BCDACC200C273695FF81E048930E05816B55AEDEA000EA7D3E8
          E32900C3AC2E5150E38844244C254622960268349A28C08F8D1FAA76EE799FA7
          4BF31D0E0AE0F7CB12D8E60660B4F7023817478165782A9B0F44810351544342
          F206D8545801A9190510C5080E495EB55A1D05B8D6F2F96C47EB570D750DC673
          13169B716176047A5A8E6362A28BE43D940177A45136E00AC551DAA143EB0221
          E6EE29B42D2A49D1930721393D3BC24662A852A9A200674F96FAD3D6A6B2478E
          5F3A7DF8D32F2BCD9D0DB07D470E26ACF1DC427FC51CDDCB6AD27E6100EECADF
          8BB95BF64400944A6514A0E9B392C0CE8A3CE6BAC9014EA7171E29310043FCE1
          8AF91799CFC1CF1747213DFB79C82FA994258B03683C511C28DF95CBCA9910AD
          29D94BBFC30A9EA9110CCCCF11F00BC0A8B4A04C31A026732361944C8419028B
          DF37F79392678F41C6BD05486B210A70E6D816A1AC3C8F50B783CB25B55104E7
          C4EFE0991905AF4B8489291A0587086A4E80BB937D90A055803E771BA80DEBE9
          6251F6C8ED0538FBC322BCFEC177F10CBE3DBA5978A22C9701963A42931FA863
          CE9B9DE85F9E2137CC2C589C45985F5C49925232C0BE340B3D6D67F04EFB4FE4
          E1756ED46FDA4A546B374418B75D9D82ACC76AF181A21D51806F3E29141F2FCD
          22C0D17CE780F83C13E075F6A1CB4DC8B5EE62D8FB6E13320C1B0EA06CACFD7C
          0359F8E53D2C48779384FBCA81516BE5A2732C0BD03164C0970E7E1105387528
          4B2C2DA39A6A54489454204E04419CC280384EFABA11F4490770EBD307223545
          42557BA5E9301AACCD44AB53D1B8645255790262004EB75A71FF89AE2840C3FE
          F4E5CD391E0D9BB006B83549C0E913A847B460540140CE02FD9D023CB8FD2418
          EEA9801810989BE8034BFB1148532D8222391330E00614BC70F1AA055EFDA8A3
          90266010E0D02BEB9EE179B1C43CEE4D7BF9B5B75E945293EEA7B210228544AD
          51D2185490C494BC38068EA5691C683D4A52D91910153A14033C61580E4EB5CC
          77D6379CDF2D8A3813D78B6885E77B3CBC89520B363BB9DB31B2E6B1ED5A9A0B
          8240E66F8EE0E46F5F9380CB029C3A1193330A48D790A7FFCDAA772A68D31C8E
          9C0F311593CFF3BC51A469C7D0A4908A82A51EC5CA127EA6EB60F07A1B4DE50E
          48DBB80DD6E73C0A972F5F9A7C63DFBE5D9629CB8DB80368058049AA01EA238A
          4280704A0D65C6C6F619E938C4C5B949326EFE1597A607C9FD252F80B177D0F2
          7655D553C343C37FACACF378009FCF442D53003F9548411562E32492E2E3E3DD
          689D1F23B39366B4D9DDC4EA4B1AA9AEAEDE3D36F667EF6A8D64258011E51848
          DE32D1534C960C2566B465FBC1659F86C9F161B8D26536D5D57DFCDCFCFCC2D0
          DF75AA5880ECDADADAFAC887D8B3048310F22F95D0E7F3C0CCDC9CBDB9F15C9D
          D3E91AFFA75648563CFF97BF2D12A270BB457F0173456D3788187BC400000000
          49454E44AE426082}
        ImageId = 9
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox3: TFHBox
        Left = 277
        Top = 0
        Width = 26
        Height = 28
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = 'FHBox2'
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 9
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrWizard
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object btnAnterior: TFButton
        Left = 303
        Top = 0
        Width = 33
        Height = 30
        Hint = 
          'Registro Anterior, Estando em Modo de Inclus'#227'o/Altera'#231#227'o Salva A' +
          'ntes de Mover o Registro (CRTL+ 7)'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 10
        OnClick = btnAnteriorClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000002D14944415478DACD95DF4B544114C7BF737FBAEBDE9D9556D6D0BCF5
          52909804B5ED635B2F56C646620FF9EB21080A0DA27F22ACA0170BDF7AD035B4
          4042C2CC82B61F141A6248A5695A8A3F42CD5FB96DDEB9D3EC2E428FEBBA4267
          F8DE732F73673E73CE99B997609B8DFCDF8052108FC77339581C3CDED3DB53B1
          D2B1C232073805A718DD180A866A63D118BA5F7553BBCB5EC90CA00CA6AAA86D
          55A12A7FF1816234DD6BE243C343144F900140198EE6B8735A2BCB2B7DDE5C2F
          613643F841988F8E8C52746D0520F20D0575668179337432A42ABA02C619E280
          CEEE4E8C0F8F533C4E17703A99EF92FD25B581400036B189989A5BDC4A441079
          1BE1139F27281EA5033883425993DB0307037E73B70926897593448BAF3F71ED
          FFD0CFA706A7283A360B388BA0D37086FDC57E9F410D3085C1922D3099C541D8
          68635FC6303D304DF13045C0DE4BFBC8E8CF917A4A69439159A449591261AA98
          2A292E4044880B483C5598FFB6C067DFCF50B4A70828BD7EE2D6CCD2CC55D556
          60EB3698CE92CA62E03A0774315027E032872DD9581C5BC2EC3B01684B115075
          B7FA7664F0E595C9AFDF095CE012958810C43DB88B73DB298AEC605C80E23B0B
          DAA4CEFFF4C628EEA70838722D20F5CDF5D5C9D94A035F65DABABE4E4045875B
          C800174A80E1105E13018D3878EC4D94A275B345AE2641D5ABB5CA44F2FD56A2
          4840A898D82DC6B9857709EF001C83D93CFAE21745389D6D5A0D53CE55DB1D1E
          C7A15575391905FD47E2D9D96F60AD6785A225DD83765E1C34AFDCE8DE65D4AE
          395761191649444291489D6B80F2D5A7CB02C0B7F0A92817EFFAA47A638FFB86
          E58D69516734598B2C11C4780E5F7EBE48D1BC15C0865D94820E333BAC15C8BE
          256D11F122D3F91D588A2C640810B70BC454F3B3DA3D85EEC373FC07722C2F5F
          783D974140DC6A8893E4297776EEF1D578741D9F9E8D51DE92A91FCE869D2344
          CD57EBF2728D63D31F172BAC6666651690826D3BE02F3B28322890C2D21D0000
          000049454E44AE426082}
        ImageId = 14
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnProximo: TFButton
        Left = 336
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Pr'#243'ximo Registro   (CRTL+ 8)'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 11
        OnClick = btnProximoClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000002D74944415478DAC595EB4B145118C69F7766D4352F5B9BD18D0417CC
          BE18B54A164B451218D2F910949054104597DD3E86FF40FD0559081588117E90
          58DB218B02B13E15155D28A4A012B58DB5746D2FCDCE65674EC7DD8ABE58BBBA
          D1393CBCCC303CBF77DEF75C08FF78D07F03B83BDC756D4D6D57D547EA397D50
          BF5F7400316281FD81F083E70FACD7EF5E9F85838BB80D5EBC12ED056B6F6D57
          77EFD8CD87EE0E61F8E1709F781BC42D68C50130B0567FABEADFEAE78AACD0E8
          E8284277428FAD8C75404026160FD807E66FF1877D1B7D902539ABD8971842B7
          4253F164BC5340461607E8006B6E6E5637D46FE0326527E66646CFF07BC3F7CC
          4834D2051BDD18FA735FE607748235FA1AD5DADA5AAE9092B3E7428ECC255BA2
          672F9FF137EFDEF489E6077173FEBECC0F3802D6E06B08D7ACACC16FE690ED9C
          145B412412C18BB72F9ED886DD8110C60B031C05F3367955D7721717E6A4380A
          E48C3C272E5BA260562E6A490DAFC65F451D83771AFDFA48FE80E360755BBCE1
          D29A12488E04B208DCE4209D400641D22528A6928D30C4F72E326BDDEB0E0D9E
          1DBC911FE00458E9E632D55C667058A03953392D83BE11A714114F72EE241CE2
          710ED990F9FAC6066B47FDF6C33DC77A06F2039C2256B6AD4C3556E85C6448D9
          36A6849262D524C4732217CB1C17EC254EB424AD74A67BB5024A749A58F9AE72
          35BD5AE3C29C8431B28AFF008858419594368C27CEACD87CD70B6D7280D8923D
          15616DAD483B9135CEE92B20276594DB15487D4EF561D609A27F21CB3448AC72
          4F959A5A95F8997116E04A8955952835BF4DA6BA1073BA31B0D08D764662D53B
          ABD584E7EBAF125569D5B03ED953FA47ED202EF3451E1502E06E591A8EBB6220
          93E0D197233E967A9A891A0770958FE763FE971249CCB3C9A32629CEDD190F66
          DECF5CE3D37600BDBC48C77550664DDB1AD4B1E96933F661A60BD3CE05F4F3E2
          5D38252715EF1AEFB22B9313B3E79D4B99BCEA5DD81F1469FC73C077B6D24728
          09C6F8B50000000049454E44AE426082}
        ImageId = 15
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox8: TFHBox
        Left = 369
        Top = 0
        Width = 32
        Height = 32
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = 'FHBox3'
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 12
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrWizard
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object FHBox5: TFHBox
      Left = 655
      Top = 1
      Width = 84
      Height = 38
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Color = 15592941
      Padding.Top = 6
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      ParentBackground = False
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrWizard
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnAceitar: TFButton
        Left = 0
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Aceita o Registro Selecionado para Outro Formulario  (CRTL+ 9)'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000002C64944415478DAB594DF4B145114C7CF9D3BEEAEAB39B8FE58DD69D5
          25480A322822535490122D948C24A2C71E7AE8A1E8C522C220CA7FA05EA2B7C0
          87840C0CCD0C1FC244A25F043D485B8EAE291968D9EEECCEECCE9DCEEC8E35FE
          58D7183D70E6CE9D33F3FDDC7BCE994B609B8D647AA1E278F72421C4912EAEEB
          BA2A0D5C0BD801E8ADCD87012129414D3354FFC6FB87DF0002882DC099F60698
          082D26E7D1A802B1989ABCAFDD2742CF9351FB80D36DF5F0696A21398F446220
          23C4B0C6037E78D43F661F70F2441D4CCCA4762023206202EAAA44E81B1CB70F
          686DA981E0EC520A2023404E01AAF796C2D3E1D7F6012D4D4740FAFE3B952214
          3720861DACF4C2D0C81614B9E96835847E844D00D6C0DC41D5AE221879F96EF3
          0014EBC2E112F6245D7E4609E435D41F82B945D94C91928418B6A7DC0363E31F
          41D3616985AAAE87F0DA82E0D06A00C57E7F2E08B93595BB032E4A2918EDAF32
          807034BEA606854236ECC8E68D9F2D39FFF92B02C12FD3C64A6A51FCC3BA2942
          881B21E3DE9282CA40C0EF584261C6FEC5AD3B308CC315140A2E6089384C7C96
          649DE9C7507C6CC31A20A40097FE5E148B4B4B4A4B7859D5D202841C0770C020
          1894A228DE86E22F365564845420E4ED4EBF2FBFB0C84394442A0D5104844D40
          8E8B071E1526BF4A51C6F47328DEF75F5D8490FD0819F59789B942BE80C524C9
          A3228C3F9BD341C1C1014C4D4EA338BB80E20F3376511A482342FAC532D19D27
          0820C714506271C8A23A7C9B9E9199C63A51FC6EBAEF33024CC859C2710FBC3E
          9FDBE97281AA28303F372B6B0976471AB87A7BA36F498618673A296FBE758566
          396F788ABDEE85F9799925D4FBD2E0F54E8C318B67041873BACA397324654D37
          BB39DE795ED7D49EA9A1AECB16616D1D5F035816E22D4E2D23058EA7BEDA8B1D
          B3AFEEF562E3272C6209CB68F59500CCB5065B64CBE7D3EAA3423FD5D16C5BFC
          71EFB3F4806DDDC176D81F0F015C28B7AC831E0000000049454E44AE426082}
        ImageId = 10
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnMais: TFButton
        Left = 33
        Top = 0
        Width = 33
        Height = 30
        Hint = 'Mais Op'#231#245'es'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -21
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnMaisClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000444944415478DA6364A031601C161684D3DA82FF508CCCC7C5461623
          463D983199D63EA02918B5607058F07EC85B4053306AC1E0B080E6851DCD8B6B
          9A5738340543DF020000C715C7D877D4660000000049454E44AE426082}
        ImageId = 22002
        WOwner = FrWizard
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox7: TFHBox
        Left = 66
        Top = 0
        Width = 28
        Height = 32
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = 'FHBox3'
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrWizard
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
  end
  object filtroAvancado: TFFilterWindow
    Width = 450
    Height = 400
    Caption = 'Filtro'
    Columns = 2
    Table = tbClienteContatoTipo
    FilterStyle = fsAddCondition
    WOwner = FrWizard
    WOrigem = EhNone
    Left = 221
    Top = 124
  end
  object popMenuPrincipal: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrWizard
    Left = 526
    Top = 141
    object menuItemAbreTabelaAux: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Abre Tabela Auxiliares'
      Hint = 
        'For'#231'a a abertura das tabela auxiliares e lookup, visto que usuar' +
        'io pode abrir o cadastro em um aba independente e alterar o regi' +
        'stro'
      ImageIndex = 200022
      OnClick = menuItemAbreTabelaAuxClick
      WOwner = FrWizard
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{1E6A4062-4501-4A38-ABB8-A85C5621222A}'
    end
    object menuHabilitaNavegacao: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Habilitar Navega'#231#227'o Durante Edi'#231#227'o'
      Hint = 
        'Quando habilita a navega'#231#227'o durante a edi'#231#227'o (Inclus'#227'o/Altera'#231#227'o' +
        ') ao mover para frente ou para tr'#225's o registro atual '#233' automatic' +
        'amente salvo'
      OnClick = menuHabilitaNavegacaoClick
      WOwner = FrWizard
      WOrigem = EhNone
      Access = True
      Checkmark = True
      GUID = '{F7401A1E-A31E-4212-AD3C-684823C1BEDD}'
    end
    object menuSelecaoMultipla: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Multiplos Registros'
      Hint = 'Permite altera'#231#227'o/exclus'#227'o de todos os registros selecionados'
      OnClick = menuSelecaoMultiplaClick
      WOwner = FrWizard
      WOrigem = EhNone
      Access = True
      Checkmark = True
      GUID = '{866D9E1E-6FE8-40AC-9C70-242A78629C89}'
    end
    object FMenuItem1: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Grid'
      ImageIndex = 22006
      WOwner = FrWizard
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{89137869-1C21-451D-99DC-1A9E7EE52842}'
      object menuItemConfgGrid: TFMenuItem
        AutoHotkeys = maManual
        Caption = 'Configurar Colunas da Grid'
        ImageIndex = 200021
        OnClick = menuItemConfgGridClick
        WOwner = FrWizard
        WOrigem = EhNone
        Access = True
        Checkmark = False
        GUID = '{F696A4E0-86FA-4C3A-AD90-8B257295BBED}'
      end
      object menuItemExportPdf: TFMenuItem
        AutoHotkeys = maManual
        Caption = 'Exportar PDF'
        ImageIndex = 22005
        OnClick = menuItemExportPdfClick
        WOwner = FrWizard
        WOrigem = EhNone
        Access = True
        Checkmark = False
        GUID = '{6E23CB21-0BCA-4AEF-A5AF-4C085E99C336}'
      end
      object menuItemExportExcel: TFMenuItem
        AutoHotkeys = maManual
        Caption = 'Exportar para Excel'
        ImageIndex = 22004
        OnClick = menuItemExportExcelClick
        WOwner = FrWizard
        WOrigem = EhNone
        Access = True
        Checkmark = False
        GUID = '{8151B2A2-880A-43C4-8BB9-A316AC7C0592}'
      end
    end
    object menuItemHelp: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Help'
      Hint = 'Help da Tela'
      ImageIndex = 11
      OnClick = menuItemHelpClick
      WOwner = FrWizard
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{5750FC7D-551D-40CB-91C2-25A2CFE370AD}'
    end
  end
  object gridConfig: TFGridConfigWindow
    Width = 500
    Height = 500
    Caption = 'Configura'#231#227'o de Grid'
    Grid = gridPrincipal
    WOwner = FrWizard
    WOrigem = EhNone
    Left = 468
    Top = 115
  end
  object scClienteContatoTipo: TFSchema
    Tables = <
      item
        Table = tbClienteContatoTipo
        GUID = '{FBAA3413-60C6-49E4-838D-C7F33DBDFC6B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbClienteContatoTipo: TFTable
    FieldDefs = <
      item
        Name = 'AREA_CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'digo'
        GUID = '{C48785E0-46AB-4FEB-9217-677DC872D76E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{86906F4D-A2C0-4E0D-ABBD-2921585ACD2D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        GUID = '{71F9FCCE-071A-439D-B1D3-94DBC0F5D787}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_CONTATO_TIPO'
    TableName = 'CLIENTE_CONTATO_TIPO'
    Cursor = 'CLIENTE_CONTATO_TIPO'
    MaxRowCount = 200
    OnAfterScroll = tbClienteContatoTipoAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44801360;44801'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSimNao: TFTable
    FieldDefs = <
      item
        Name = 'FIELD_KEY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Field Key'
        GUID = '{B1E62230-580A-441A-8447-12C7F84DC9F7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIELD_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Field Desconto'
        GUID = '{F5CBE054-E37A-43A1-893A-31C55C4137C6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SIM_NAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44801360;44802'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSimNaoFt: TFTable
    FieldDefs = <
      item
        Name = 'FIELD_KEY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Field Key'
        GUID = '{39411DEE-BAE9-455B-BE83-B42F53CDA99F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIELD_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Field Desconto'
        GUID = '{68E27020-A4E6-48E1-88EE-CE575D4833AA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SIM_NAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44801360;44801'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
