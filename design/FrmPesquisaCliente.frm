object FrmPesquisaCliente: TFForm
  Left = 44
  Top = 163
  ActiveControl = hboxMenuBotoes
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Pesquisa Cliente'
  ClientHeight = 604
  ClientWidth = 698
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000122'
  ShortcutKeys = <>
  InterfaceRN = 'PesquisaClienteRN'
  Access = False
  ChangedProp = 
    'FrmPesquisaCliente.Height;'#13#10'FrmPesquisaCliente.Width;'#13#10'FrmPesqui' +
    'saCliente_1.Touch.InteractiveGestures;'#13#10'FrmPesquisaCliente_1.Tou' +
    'ch.InteractiveGestureOptions;'#13#10'FrmPesquisaCliente_1.Touch.Parent' +
    'TabletOptions;'#13#10'FrmPesquisaCliente_1.Touch.TabletOptions;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object hboxMenuBotoes: TFHBox
    Left = 0
    Top = 0
    Width = 698
    Height = 61
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 3
    Padding.Left = 3
    Padding.Right = 3
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object btnVoltar: TFButton
      Left = 0
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Voltar Tela'
      Align = alLeft
      Caption = 'Voltar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 0
      OnClick = btnVoltarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
        FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
        290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
        086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
        152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
        F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
        86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
        B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
        AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
        EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
        AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
        736BB6EF9B710000000049454E44AE426082}
      ImageId = 700081
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
      UploadMime = 'image/*'
    end
    object btnPesquisar: TFButton
      Left = 60
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Pesquisar'
      Align = alLeft
      Caption = 'Pesquisar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 1
      OnClick = btnPesquisarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000002224944415478DAAD954D481561148667840C347F1229DD0822680B4B
        282368D12A90144522027321B8B44D8128B6085CA508091A486D021191D4853F
        F8B30B42022D085D184482D4E6128982284A747B4E9DF0F03933772EDC0F5E5E
        BE33EF39EF9CEF67C6F72286EFFBE7A13250020E40026C2593C95F5ECCE10714
        95D86D30022A42F2C640274689B40CA85D08BD079762BE600FE8C72899D280E2
        B20C5F414EDCF6758C53BF35D280E267A16D70D13C5B07ED608302C7BA74D2E1
        033004B28CB60BCD4094813CEC34F17E693FAC75F4C5FA02A5267C01F98F00AD
        5F00EFC66DD93149984E5E93D71E64700F9E34B13C84FB71169FDC87D00B133A
        E31E61319886EFEA7C0E41539CE29A2CF764C7842AC9FFE21A6CC1E53AEF4030
        928681ECE16F13BA45FE3B57B307E7EBBC0DC1685C032D600F4203F90BAEC10A
        7C53E7CF103C49A3783674644237C85F750D9EC2BD3A97F52C8EBA994E722DB4
        664292FBD33590CFC2A689D5235A8CB9FE9FC0150D7D23AF2C4827C2CFA05263
        72CCCA117F4F61D007759B50333933A7742ABEECFDBB999E3191E3BAE42E17DA
        73D02BD062C272518B8296D67EEC1E43CF9DE7B2272FB5C35C7007348634D542
        FD8950033579040DA65AFF8871CA24E88723CB35E59DEC49D81806F36039CAC4
        0FCAD48DAF02F7411DA80687E00378036629B2ABDAABD0C730934083740726D7
        D4DC8EBFA72A23066AE25E3A195919335093EBD0FF4FC53A1DD464D4404DE4B7
        5B03DECAAFF60FF9CFC91D70A0B6C30000000049454E44AE426082}
      ImageId = 27001
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
      UploadMime = 'image/*'
    end
    object btnAceitar: TFButton
      Left = 120
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Aceitar'
      Align = alLeft
      Caption = 'Aceitar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 2
      OnClick = btnAceitarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
        0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
        1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
        95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
        101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
        1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
        A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
        C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
        482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
        1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
        C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
        8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
      ImageId = 700088
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
      UploadMime = 'image/*'
    end
    object btnNovoCliente: TFButton
      Left = 180
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Cadastrar Novo Cliente'
      Align = alLeft
      Caption = 'Novo'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 3
      OnClick = btnNovoClienteClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
        E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
        B9B9B6418D210000000049454E44AE426082}
      ImageId = 0
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconClass = 'user-plus'
      IconReverseDirection = False
      UploadMime = 'image/*'
    end
    object btnAlterarCliente: TFButton
      Left = 240
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Alterar Cadastro Cliente'
      Align = alLeft
      Caption = 'Alterar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 4
      OnClick = btnAlterarClienteClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
        E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
        B9B9B6418D210000000049454E44AE426082}
      ImageId = 0
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconClass = 'address-card'
      IconReverseDirection = False
      UploadMime = 'image/*'
    end
    object btnExcluirCliente: TFButton
      Left = 300
      Top = 0
      Width = 60
      Height = 56
      Hint = 'Excluir cliente'
      Align = alLeft
      Caption = 'Excluir'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 5
      OnClick = btnExcluirClienteClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F80000017A4944415478DAB594394A444110867B1841345030D0710915B753B8
        45623046828118EB6DF400A2188A6220266E277003F508E2CC68EA98E85F4CD7
        A37C54F58258F0056F9AF77F35D5AFBBE2FEB92AA5E75EB00E8E4133336B08AC
        8003D0D604147E0A16C00B9803AF19E19760069C833AF8940219CE952A91E15C
        8584055B605779F9D14B1B46F8880F9F54D636C11E0BA88B6B30952109853F80
        79D0927B30E85F98555E288F4B1B8B0CA7869A720F7224DFA9E19A806AD80768
        E37A0755D0AFACDD834557FABC35018FE0C2F8275AA9E121418EC40C8F09785C
        CFC648A8DE7C03E6A98F0946C113E833D61B5E609D93A0A0E63A9B3D1D692278
        E22D416A7854A209682C57604259A399778101654D3DF165C1980F1F5702EE5C
        E76BA9BAF413FF4B9012DEF2CFC9D70A0BE8E2BA31C26FC19208E7AAF986AC0B
        9224C565B70D7632C253241B609F05DDE0082C07C6629536AE33B00ADA720FA4
        24355C9314E1720FB87AC01A38011F89E15242CD1D822FFE317655FCB97E0090
        F96719D2A81AD50000000049454E44AE426082}
      ImageId = 4600385
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
      UploadMime = 'image/*'
    end
    object btnAlterarFoneEmail: TFButton
      Left = 360
      Top = 0
      Width = 68
      Height = 56
      Hint = 'Alterar Fone/e-mail cliente'
      Align = alLeft
      Caption = 'Alterar Fone'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 6
      OnClick = btnAlterarFoneEmailClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
        E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
        B9B9B6418D210000000049454E44AE426082}
      ImageId = 0
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconClass = 'pencil-square-o'
      IconReverseDirection = False
      UploadMime = 'image/*'
    end
    object btnLimite: TFButton
      Left = 428
      Top = 0
      Width = 68
      Height = 56
      Hint = 'Consultar Limite Credito'
      Align = alLeft
      Caption = 'Limite'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 7
      OnClick = btnLimiteClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000002DA4944415478DAA5966B688E6118C7EF87D018739EC912B6A5163287
        7C91A825DF7C90B3291FD426145A8644F44AB1E683F0C92649F385454A0EB358
        24A70D45AC4D2D8C5713739A99DFE5BEDEDCEE9EE77D1DAEFAF5BC3DF7735FFF
        EBB90EF7F306DDDDDD462C0882815C66C14C980AC360080C864F10579EC135A8
        636F8349610164C066580BFD526DF0AC1ECA10AA4B26700C56FCA563D7BE401E
        22CFA3042E40E17F087C870204EE47098C860A98FF0FCE5B611BCE2B93A56813
        1C87E130CFFC2AF250E8E93DFFC138458673BA278EC89528817648836AA88226
        8DECABB10DE07651178C8051301B4A201B8A11381C25F0C6D876F42DAE422F60
        803ACD82CF7053DFE23A4871DF22F03A4A6003ECD3DFA96CBFB12D2D42D394FE
        70576940E89B2F20B9CF31B655E7C29810C7D28AABE12C1C80E5110134421122
        F75C8136635BB59C853B4C748E0A662A12C029E80B27B406099363E0A3F97D40
        3BA1145F156E913374F109346B5E5BF47A067AC30315736D2FD4C279EFBE08CF
        41A456042E1BDB1161D661EC9924D3BEC05B938E5B07EFA1180E7AEB12E88440
        0B26455E12526869DD9DF0D0BBFF0A46C262180F4F55D0B735E23006478CED7B
        E98A0298A24884E970D4DB7891D72FA45E92B67C136D55891A48914EC255782C
        E0A0ED679182405EBDC4DB280327DD26F3B1111619DB04BE3D12812613DE9AED
        FA463B6059C8BA7C0B2A34B0344DD320EF99D6408B2767519F10274B8D6DCBF2
        246990091E0BBB61BDB7562302799AFF528D385F231293E2D7187BB0F976C3D8
        A35A5A7712EC32F654706DBB08BC53077BC87B3D3997133417261ADB55320752
        CC71DEE6129E3FA475CAD5FA6539EB12F46471D0E114A8D9A1459139C956073D
        1C07324C0BE1961433A4C85B0920260295B032498E65BAA565CB608BB7F65283
        99E1DD978CC824778940BAE65FF217F5D13F6DEC20CAC745D29219F15CA7163B
        963855833FFCDB22F9BC64EC29DA0B56C1746D0A09F0B6A6AA1A7F8DAEE20F22
        66EB10E7E0C5240000000049454E44AE426082}
      ImageId = 4600436
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
      UploadMime = 'image/*'
    end
    object btnFlagsEspeciais: TFButton
      Left = 496
      Top = 0
      Width = 78
      Height = 56
      Hint = 
        'Ver flags de Venda do Cliente (Condi'#231#245'es Especiais)'#13#10#13#10'Acesso: K' +
        '0235'#13#10#13#10'No formul'#225'rio "Clientes" [Gerenciamento > Clientes], na ' +
        'guia "Especiais", o cliente dever'#225' atender, pelo '#13'menos, uma das' +
        ' seguintes condi'#231#245'es para ser considerado com Condi'#231#245'es Especiai' +
        's:'#13#10'01 - Exibi'#231#227'o de um c'#237'rculo verde nas seguintes colunas:'#13#10'01' +
        '.01 - Desconto'#13#10'01.02 - Condi'#231#227'o'#13#10'01.03 - Garantia'#13#10'01.04 - F'#225'br' +
        'ica'#13#10'01.05 - Reserva'#13#10'01.06 - Pr'#233'-Pedido'#13#10'02 - Na coluna "Tempo ' +
        'Reserva" exibir valor maior que "0 Horas"'#13#10'03 - Na coluna "Segme' +
        'nto" deve haver alguma informa'#231#227'o'
      Align = alLeft
      Caption = 'Flags Especiais'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 8
      Visible = False
      OnClick = btnFlagsEspeciaisClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
        E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
        B9B9B6418D210000000049454E44AE426082}
      ImageId = 0
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clWindowText
      Access = False
      IconClass = 'handshake-o'
      IconReverseDirection = False
      UploadMime = 'image/*'
    end
  end
  object HboxSeparadorBotoesPesquisa: TFHBox
    Left = 0
    Top = 61
    Width = 698
    Height = 9
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
  end
  object hBoxPesquisaCliente: TFHBox
    Left = 0
    Top = 70
    Width = 698
    Height = 31
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 3
    Padding.Right = 3
    Padding.Bottom = 0
    TabOrder = 2
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object edtPesquisarCliente: TFString
      Left = 0
      Top = 0
      Width = 631
      Height = 24
      Hint = 'Nome, telefone ou e-mail'
      HelpCaption = 'Nome, telefone ou e-mail'
      TabOrder = 0
      AccessLevel = 0
      Flex = True
      WOwner = FrInterno
      WOrigem = EhNone
      Required = False
      Prompt = 'Nome, telefone ou e-mail'
      Constraint.CheckWhen = cwImmediate
      Constraint.CheckType = ctExpression
      Constraint.FocusOnError = False
      Constraint.EnableUI = True
      Constraint.Enabled = False
      Constraint.FormCheck = True
      IconDirection = idLeft
      CharCase = ccNormal
      Pwd = False
      Maxlength = 0
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'Tahoma'
      Font.Style = []
      OnEnter = edtPesquisarClienteEnter
      SaveLiteralCharacter = False
      TextAlign = taLeft
    end
  end
  object vBoxGrid: TFVBox
    Left = 0
    Top = 101
    Width = 698
    Height = 503
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 3
    Padding.Left = 3
    Padding.Right = 3
    Padding.Bottom = 3
    TabOrder = 3
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hbCliente: TFHBox
      Left = 0
      Top = 0
      Width = 699
      Height = 196
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object hbGridClientes: TFVBox
        Left = 0
        Top = 0
        Width = 429
        Height = 173
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        ParentBackground = False
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object HboxExpacoGrid: TFHBox
          Left = 0
          Top = 0
          Width = 327
          Height = 21
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblFaturamento: TFLabel
            Left = 0
            Top = 0
            Width = 39
            Height = 13
            Caption = 'Cliente'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
        end
        object grdClientes: TFGrid
          Left = 0
          Top = 22
          Width = 404
          Height = 137
          Align = alClient
          TabOrder = 1
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'Tahoma'
          TitleFont.Style = []
          Table = tbLeadsConsultaClientes
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Paging.Enabled = True
          Paging.PageSize = 0
          Paging.DbPaging = False
          FrozenColumns = 0
          ShowFooter = False
          ShowHeader = True
          MultiSelection = False
          Grouping.Enabled = False
          Grouping.Expanded = False
          Grouping.ShowFooter = False
          Crosstab.Enabled = False
          Crosstab.GroupType = cgtConcat
          OnDoubleClick = btnAceitarClick
          EnablePopup = False
          WOwner = FrInterno
          WOrigem = EhNone
          EditionEnabled = False
          AuxColumnHeaders = <>
          NoBorder = False
          ActionButtons.BtnAccept = False
          ActionButtons.BtnView = False
          ActionButtons.BtnEdit = False
          ActionButtons.BtnDelete = False
          ActionButtons.BtnInLineEdit = False
          CustomActionButtons = <>
          ActionColumn.Title = 'A'#231#245'es'
          ActionColumn.Width = 100
          ActionColumn.TextAlign = taCenter
          ActionColumn.Visible = True
          Columns = <
            item
              Expanded = False
              FieldName = 'COD_CLIENTE_EXIBIR'
              Font = <>
              Title.Caption = 'C'#243'd. Cliente'
              Width = 120
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{B241036C-E21E-4F3A-A02F-3626D4739D73}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'CLIENTE_EXIBIR'
              Font = <>
              Title.Caption = 'Nome'
              Width = 200
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{2FD1FA66-AF5F-491B-AB0E-F6F79CBA5059}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'EMAIL_EXIBIR'
              Font = <>
              Title.Caption = 'E-mail'
              Width = 200
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{DB669278-B7C4-42F3-91AE-8D586AE7D801}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'EMAIL_NFE_EXIBIR'
              Font = <>
              Title.Caption = 'E-mail NF-e'
              Width = 200
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{52FA1822-B8F6-437D-8A20-16E444A6F456}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'EH_ATACADISTA'
              Font = <>
              Title.Caption = 'Atacadista'
              Width = 80
              Visible = True
              Precision = 0
              TextAlign = taCenter
              FieldType = ftCheckBox
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{24E25FE0-911A-4A84-9E76-8576DF9A5085}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'MIDIA_DESCRICAO_CODIGO'
              Font = <>
              Title.Caption = 'M'#237'dia'
              Width = 100
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{DC57F585-E97B-461F-ABF8-A75406FB0081}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end>
        end
      end
      object vboxTelefone: TFVBox
        Left = 429
        Top = 0
        Width = 255
        Height = 173
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 3
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object hBoxQualMelhorFone: TFHBox
          Left = 0
          Top = 0
          Width = 243
          Height = 20
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblQualMelhorFone: TFLabel
            Left = 0
            Top = 0
            Width = 233
            Height = 13
            Caption = 'Selecione o melhor telefone para contato'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object vBoxSeparadorSelecioneFone: TFVBox
            Left = 233
            Top = 0
            Width = 6
            Height = 17
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FIconClassEditarCliente: TFIconClass
            Left = 239
            Top = 0
            Width = 16
            Height = 16
            Hint = 'Alterar Fone/e-mail cliente'
            Picture.Data = {
              07544269746D617046050000424D460500000000000036000000280000001200
              0000120000000100200000000000100500000000000000000000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000000000000000000000000000000000000000000000000000000000000000
              0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
              0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
              000000000000000000000000000000000000000000000000000000000000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
              0000000000000000000000000000000000000000000000000000000000000000
              000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
              F000F0F0F000F0F0F000F0F0F000F0F0F000}
            Visible = False
            OnClick = FIconClassEditarClienteClick
            IconClass = 'pencil-square-o'
            WOwner = FrInterno
            WOrigem = EhNone
            Size = 18
            Color = clBlack
          end
        end
        object grdFoneCliente: TFGrid
          Left = 0
          Top = 21
          Width = 236
          Height = 145
          Align = alClient
          TabOrder = 1
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'Tahoma'
          TitleFont.Style = []
          Table = tbListFoneCliente
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Paging.Enabled = False
          Paging.PageSize = 0
          Paging.DbPaging = False
          FrozenColumns = 0
          ShowFooter = False
          ShowHeader = True
          MultiSelection = False
          Grouping.Enabled = False
          Grouping.Expanded = False
          Grouping.ShowFooter = False
          Crosstab.Enabled = False
          Crosstab.GroupType = cgtConcat
          EnablePopup = False
          WOwner = FrInterno
          WOrigem = EhNone
          EditionEnabled = False
          AuxColumnHeaders = <>
          NoBorder = False
          ActionButtons.BtnAccept = False
          ActionButtons.BtnView = False
          ActionButtons.BtnEdit = False
          ActionButtons.BtnDelete = False
          ActionButtons.BtnInLineEdit = False
          CustomActionButtons = <>
          ActionColumn.Title = 'A'#231#245'es'
          ActionColumn.Width = 100
          ActionColumn.TextAlign = taCenter
          ActionColumn.Visible = True
          Columns = <
            item
              Expanded = False
              FieldName = 'TIPO_FONE'
              Font = <>
              Title.Caption = 'Tipo'
              Width = 81
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{3ADD0070-C9B6-466A-9CDE-4BBEB710C112}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'FONE'
              Font = <>
              Title.Caption = 'Telefone'
              Width = 100
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = True
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{5A4AA125-9FA7-4CBC-9B9F-AF9522113B69}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end>
        end
      end
    end
    object vBoxGridEnderecoFaturamento: TFVBox
      Left = 0
      Top = 197
      Width = 672
      Height = 273
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object hBoxLabelEnderecoFaturamento: TFHBox
        Left = 0
        Top = 0
        Width = 207
        Height = 20
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblEnderecoFaturamento: TFLabel
          Left = 0
          Top = 0
          Width = 146
          Height = 13
          Caption = 'Endere'#231'o de Faturamento'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object vBoxFinalEnderecoFaturamento: TFVBox
          Left = 146
          Top = 0
          Width = 6
          Height = 17
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object iconClassEditarEndereco: TFIconClass
          Left = 152
          Top = 0
          Width = 16
          Height = 16
          Hint = 'Alterar Endere'#231'o Selecionado'
          Picture.Data = {
            07544269746D617046050000424D460500000000000036000000280000001200
            0000120000000100200000000000100500000000000000000000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
            0000000000000000000000000000000000000000000000000000000000000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000000000000000000000000000000000000000000000000000000000000000
            0000000000000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
            0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F0000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F0000000000000000000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F0000000000000000000000000000000
            000000000000000000000000000000000000000000000000000000000000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000000000000000
            0000000000000000000000000000000000000000000000000000000000000000
            000000000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
            F000F0F0F000F0F0F000F0F0F000F0F0F000}
          OnClick = iconClassEditarEnderecoClick
          IconClass = 'pencil-square-o'
          WOwner = FrInterno
          WOrigem = EhNone
          Size = 18
          Color = clBlack
        end
      end
      object grdEndereco: TFGrid
        Left = 0
        Top = 21
        Width = 657
        Height = 194
        TabOrder = 1
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Table = tbLeadsEnderecoCliente
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Paging.Enabled = False
        Paging.PageSize = 0
        Paging.DbPaging = False
        FrozenColumns = 0
        ShowFooter = False
        ShowHeader = True
        MultiSelection = False
        Grouping.Enabled = False
        Grouping.Expanded = False
        Grouping.ShowFooter = False
        Crosstab.Enabled = False
        Crosstab.GroupType = cgtConcat
        EnablePopup = False
        WOwner = FrInterno
        WOrigem = EhNone
        EditionEnabled = False
        AuxColumnHeaders = <>
        NoBorder = False
        ActionButtons.BtnAccept = False
        ActionButtons.BtnView = False
        ActionButtons.BtnEdit = False
        ActionButtons.BtnDelete = False
        ActionButtons.BtnInLineEdit = False
        CustomActionButtons = <>
        ActionColumn.Title = 'A'#231#245'es'
        ActionColumn.Width = 100
        ActionColumn.TextAlign = taCenter
        ActionColumn.Visible = True
        Columns = <
          item
            Expanded = False
            Font = <>
            Title.Caption = '#'
            Width = 35
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <
              item
                Expression = 'CHECKED = '#39'S'#39
                EvalType = etExpression
                GUID = '{DA72DA61-1A31-4F3A-B244-B335100D9771}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 7000110
                Color = clBlack
              end
              item
                Expression = 'CHECKED = '#39'N'#39
                EvalType = etExpression
                GUID = '{154A814D-47AD-47E2-8BD0-BB83C0750003}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 7000111
                Color = clBlack
              end>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{FE75BADB-FD9A-42E1-A42E-A9F377021DAF}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'TIPO_ENDERECO'
            Font = <>
            Title.Caption = 'Tipo'
            Width = 80
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{4791EFB1-**************-5DD63D2D59A6}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'UF'
            Font = <>
            Width = 40
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{A6EC9E80-A498-4D06-861D-EF1CBDD38F4D}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'CIDADE_EXIBIR'
            Font = <>
            Title.Caption = 'Cidade'
            Width = 100
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{FAB6B728-7384-4D7A-BBEB-C6A3EBEBEAF1}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'BAIRRO_EXIBIR'
            Font = <>
            Title.Caption = 'Bairro'
            Width = 100
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{DC8CC1D2-6D08-4E91-BDE6-352E044A4223}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'RUA_EXIBIR'
            Font = <>
            Title.Caption = 'Rua'
            Width = 100
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{E7A25E1C-D55A-4C9A-BF98-FA124F288ADF}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'NUMERO_EXIBIR'
            Font = <>
            Title.Caption = 'N'#250'mero'
            Width = 60
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{0EC8EC42-0291-4A4E-B3C5-745684C498DD}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'INSCRICAO_ESTADUAL_EXIBIR'
            Font = <>
            Title.Caption = 'Inscri'#231#227'o Estadual'
            Width = 120
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{DCBF4F3D-CC69-498D-AD5E-AAA5C24482E6}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'CEP_EXIBIR'
            Font = <>
            Title.Caption = 'CEP'
            Width = 80
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{2DBC0380-8118-481B-8B83-2BF675AD5F23}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end>
      end
    end
  end
  object FRadioGroup: TFRadioGroup
    WOwner = FrInterno
    WOrigem = EhNone
  end
  object tbLeadsConsultaClientes: TFTable
    FieldDefs = <
      item
        Name = 'CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente'
        GUID = '{BDA05A69-BA13-4FCA-86C5-58E0D379585B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{BBFD30C7-76DC-4BF1-8410-BF787D603CC6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel Res'
        GUID = '{177BA314-E279-4B7B-82B4-BA3ECCD91628}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel com'
        GUID = '{96E4467F-D1F6-4E34-91E6-6BEA6D0B2E58}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel Cel'
        GUID = '{7D9C0D5A-A64B-4747-91D5-DF3EB610B6D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        GUID = '{793CF4DE-D1D9-4BD9-B25F-3A6C3B563791}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email Nfe'
        GUID = '{2A981765-CC43-4822-BE7D-FE06C3F49781}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Exibir'
        GUID = '{AA6226D5-8A48-40A9-8B24-B6D88A86F62F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente Exibir'
        GUID = '{98903EB6-B751-40AF-8385-AA9473948359}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_RES_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel Res Exibir'
        GUID = '{40639E27-0FA2-4EF0-BC19-93BB1261687B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_COM_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel com Exibir'
        GUID = '{5E9ABB53-26F5-4F81-9E17-1A9AF67B44BB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEL_CEL_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tel Cel Exibir'
        GUID = '{3E294219-FAED-4750-BCF5-BDEEF436ED73}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email Exibir'
        GUID = '{1B87D097-E444-4264-9AC5-7196953B89BE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_NFE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email Nfe Exibir'
        GUID = '{04AF082B-A0E3-49F6-B9FF-3D530766B023}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe'
        GUID = '{B7F1BC71-AB14-416C-A6DD-8403BBD0A055}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sexo'
        GUID = '{9ADE63B1-3C8E-4ACF-A44F-C0C22DBC6806}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_ATACADISTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Atacadista'
        GUID = '{2830035D-0947-449D-B693-D8868E7C9916}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MIDIA_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Midia Descri'#231#227'o Codigo'
        GUID = '{3BEE7D7F-650F-479E-AF75-E4E4A4B969E7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_CONSULTA_CLIENTES'
    MaxRowCount = 1000
    OnAfterScroll = tbLeadsConsultaClientesAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;70001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsEnderecoCliente: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_ENDERECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Endere'#231'o'
        GUID = '{E3944481-1663-4397-9472-B18B3B21E258}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ENDERECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Endere'#231'o'
        GUID = '{504AC753-45B6-4EC9-9D89-14777A0E1F99}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{A1E6E573-F8AA-42F5-A770-6F8D95A4417B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        GUID = '{210013A4-E454-4D29-80B0-6D1B2C1D3821}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        GUID = '{A0AC1838-F67B-4D05-8BA0-7CD9B1069CA8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        GUID = '{D9E3B6E0-80A3-489E-870D-208B4D7F8DA1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHECKED'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'CHECKED'
        GUID = '{DA2BBE1A-86B4-4BE7-8380-6FC27CDED4B3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        GUID = '{7C186760-F434-4E91-9B91-16879CA6F40E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua'
        GUID = '{E61B0DD4-76AB-474B-9B4B-C1D8793FB586}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        GUID = '{172DE63E-3663-4DB1-AE47-836E5639FFAE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade Exibir'
        GUID = '{418A8FEE-590C-4A0E-8CF1-A1EA68A4477F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Exibir'
        GUID = '{22192435-ACD4-498D-A6F4-5646F3D2B114}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual Exibir'
        GUID = '{F814E378-F8DD-4F36-B387-069C9EAE6A05}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro Exibir'
        GUID = '{E86BD79A-8ED5-4417-880E-BA3A865F3843}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua Exibir'
        GUID = '{D07A00CB-DBE1-49F4-8B25-C42DD0565E44}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Exibir'
        GUID = '{DEC469BA-A099-4D3C-A71E-2B09359B4990}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_ENDERECO_CLIENTE'
    MaxRowCount = 200
    OnAfterScroll = tbLeadsEnderecoClienteAfterScroll
    OnBeforeScroll = tbLeadsEnderecoClienteBeforeScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;70002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCrmpartsExisteEvento: TFTable
    FieldDefs = <
      item
        Name = 'EXIST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Exist'
        GUID = '{C19CBF18-62AA-4AEF-9E3B-8E484AEAE77B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRMPARTS_EXISTE_EVENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;70004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListFoneCliente: TFTable
    FieldDefs = <
      item
        Name = 'TIPO_FONE'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'TIPO_FONE'
        GUID = '{EF021F17-B17A-49D4-B149-A6AA9B5FAE7B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'FONE'
        GUID = '{7DBEB63D-E11D-4B40-A52C-EED7577453C3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DUAL'
    Cursor = 'DUAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;70006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesDescontos: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{5209FC47-76B1-4194-B783-01B7C5A93231}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LETRA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Letra'
        GUID = '{2C255C68-BC2C-4E91-A885-0F38EF757BF8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_DESCONTOS'
    Cursor = 'CLIENTES_DESCONTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCrmpartsGridClienteEspecial: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{39EB6580-2EEE-41EE-A082-B88DF6060B75}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRMPARTS_GRID_CLIENTE_ESPECIAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000122;53002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
