object OficinaManutencaoAlterarServRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600264'
  Left = 44
  Top = 163
  Height = 299
  Width = 442
  object tbOficinaManutServicos: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{D58955DC-B9F8-4FB8-A221-FE9B4689F590}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{1A5F45B1-5B1C-42B4-8DA6-A9E6DCB9D4FC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo'
        GUID = '{1FC0CFDF-CB3D-4BCE-BCF8-2FA29927E307}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SUB_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Grupo'
        GUID = '{48C1C4E3-2FD3-44F8-B005-82258B556FAB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        GUID = '{5FC1547E-1173-4C7F-9F77-1E0EA4E54D68}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        GUID = '{908E8283-55CE-46C1-A05F-F2F8571499A6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TERCEIROS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Terceiros'
        GUID = '{0D2EA853-9CEB-4B14-9D3F-BD044E75D62B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGINAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Original'
        GUID = '{FCBE608A-1A7A-4CE8-8C15-35FBE9812CC9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LR_REPAIR_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Lr Repair Id.'
        GUID = '{A40B54FB-623B-4C77-8CB7-EBBC7D71458B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tmo'
        GUID = '{81627973-109B-47D7-846F-5D7BFD6099A4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        GUID = '{2A4EDD92-4F95-49AC-B032-40CD51E28E33}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AVISO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aviso'
        GUID = '{65FFBB10-59ED-4DDC-B640-75C6DCD59DEA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{FE983DDE-E6FB-4134-8BD6-09F23B24418E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM_TMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Origem Tmo'
        GUID = '{853093F7-BD3F-4010-B3F1-EF4F4ABC5E3C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TMO_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tmo Agenda'
        GUID = '{64AA6AC8-4D49-4FE4-ADBB-C9AEE56028DF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM_VALOR_HORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Origem Valor Hora'
        GUID = '{42B2833C-5460-477B-B04F-0CB96A1C548D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_HORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Hora'
        GUID = '{054434DC-9A20-4E7D-8623-97CA05C6CBE0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMO_COBRAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Como Cobrar'
        GUID = '{DFC10377-0DAD-4345-8F21-03AF3D5297BD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_CUSTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Custo'
        GUID = '{E262A772-76B9-4D8D-8D1E-4C4796004E30}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERV_LC11603'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o Lc11603'
        GUID = '{BE658A28-0BA2-4978-A327-9FE15382496D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OFICINA_MANUT_SERVICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600264;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServicos: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{88C85CB2-E0F7-49DF-BC02-B1EF8FFED2EC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{EAC7F6DE-3169-4941-8E91-C43F2F7FD7DD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{65D415FC-4A76-4DAC-A2F8-F7C5E2D8468D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{12D158D2-7639-4EAE-BD00-5DDF2098C09D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_SERVICOS'
    Cursor = 'OS_SERVICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600264;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTemposPadroes: TFTable
    FieldDefs = <
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{08E6E68B-D803-462F-AE5B-4A9EF36587C7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produto'
        GUID = '{FDF859CF-991D-4ED8-B998-B67DBFA7083A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo'
        GUID = '{55D58648-B033-4059-BD4E-0C5A4207FB1E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TEMPOS_PADROES'
    Cursor = 'TEMPOS_PADROES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600264;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServOrc: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{C953F328-CA94-4482-96B0-92FC0E94A8CB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{4242F838-12E9-4FFA-AC26-DC298BCD72D0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{E865714F-DD10-4223-8827-833623C3094A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{8109C69C-4B3A-4186-BBA3-B6BCBF067B12}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_SERV_ORC'
    Cursor = 'OS_SERV_ORC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600264;51601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
